/**
 * ChatWindow初始化问题修复测试脚本
 * 用于测试修复后的ChatWindow组件是否能正确处理用户信息初始化
 * 
 * 使用方法：
 * 1. 在浏览器控制台中运行此脚本
 * 2. 多次打开和关闭聊天窗口
 * 3. 观察控制台输出，检查是否还有初始化错误
 */

// 测试工具函数
const testUtils = {
  // 等待指定时间
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // 获取聊天窗口状态
  getChatWindowStatus: () => {
    // 检查聊天窗口是否存在
    const chatWindow = document.querySelector('.chat-window');
    const chatButton = document.querySelector('[class*="chat-button"]') || 
                      document.querySelector('[class*="chat-toggle"]') ||
                      document.querySelector('button[title*="聊天"]') ||
                      document.querySelector('button[title*="客服"]');
    
    return {
      windowExists: !!chatWindow,
      buttonExists: !!chatButton,
      windowVisible: chatWindow ? !chatWindow.style.display || chatWindow.style.display !== 'none' : false,
      buttonElement: chatButton
    };
  },
  
  // 模拟点击聊天按钮
  clickChatButton: () => {
    const status = testUtils.getChatWindowStatus();
    if (status.buttonElement) {
      console.log('🖱️ 点击聊天按钮');
      status.buttonElement.click();
      return true;
    } else {
      console.warn('⚠️ 未找到聊天按钮');
      return false;
    }
  },
  
  // 检查控制台错误
  checkConsoleErrors: () => {
    // 这个函数需要在实际测试中手动观察控制台
    console.log('📋 请手动检查控制台是否有以下错误:');
    console.log('- "Cannot access \'enrichMessageWithAvatar\' before initialization"');
    console.log('- "Cannot access \'safeEnrichMessages\' before initialization"');
    console.log('- "Cannot access \'isLoadingMore\' before initialization"');
    console.log('- "Cannot read properties of null (reading \'insertBefore\')"');
    console.log('- "[Vue warn]: Unhandled error during execution of watcher callback"');
    console.log('- "[Vue warn]: Unhandled error during execution of setup function"');
    console.log('- 其他与ChatWindow初始化相关的错误');
  }
};

// 测试场景
const tests = {
  // 测试1: 快速多次打开关闭聊天窗口
  async testRapidToggle() {
    console.log('\n🧪 测试1: 快速多次打开关闭聊天窗口');
    
    for (let i = 0; i < 5; i++) {
      console.log(`🔄 第${i + 1}次切换`);
      
      // 打开聊天窗口
      if (testUtils.clickChatButton()) {
        await testUtils.wait(200); // 等待200ms
        
        // 再次点击关闭
        testUtils.clickChatButton();
        await testUtils.wait(200); // 等待200ms
      } else {
        console.warn('⚠️ 无法执行切换测试，未找到聊天按钮');
        break;
      }
    }
    
    console.log('✅ 快速切换测试完成');
    testUtils.checkConsoleErrors();
  },
  
  // 测试2: 延迟打开聊天窗口
  async testDelayedOpen() {
    console.log('\n🧪 测试2: 延迟打开聊天窗口');
    
    // 等待页面完全加载
    console.log('⏳ 等待页面完全加载...');
    await testUtils.wait(3000);
    
    // 打开聊天窗口
    console.log('🖱️ 打开聊天窗口');
    if (testUtils.clickChatButton()) {
      await testUtils.wait(1000);
      
      const status = testUtils.getChatWindowStatus();
      console.log('📊 聊天窗口状态:', status);
      
      console.log('✅ 延迟打开测试完成');
    } else {
      console.warn('⚠️ 无法执行延迟打开测试');
    }
    
    testUtils.checkConsoleErrors();
  },
  
  // 测试3: 页面刷新后立即打开
  async testImmediateOpenAfterRefresh() {
    console.log('\n🧪 测试3: 页面刷新后立即打开');
    console.log('💡 请手动刷新页面，然后立即点击聊天按钮');
    console.log('💡 观察是否有初始化错误');
    
    // 等待用户操作
    await testUtils.wait(1000);
    testUtils.checkConsoleErrors();
  },
  
  // 测试4: 检查用户信息初始化状态
  async testUserInfoInitialization() {
    console.log('\n🧪 测试4: 检查用户信息初始化状态');
    
    // 尝试访问Vue组件实例（如果可能的话）
    try {
      const chatWindow = document.querySelector('.chat-window');
      if (chatWindow && chatWindow.__vueParentComponent) {
        const component = chatWindow.__vueParentComponent;
        console.log('🔍 ChatWindow组件实例:', component);
        
        // 检查用户信息初始化状态
        if (component.setupState) {
          console.log('📊 组件状态:', {
            isUserInfoInitialized: component.setupState.isUserInfoInitialized?.value,
            currentUserInfo: component.setupState.currentUserInfo
          });
        }
      } else {
        console.log('ℹ️ 无法访问ChatWindow组件实例');
      }
    } catch (error) {
      console.log('ℹ️ 无法检查组件状态:', error.message);
    }
    
    testUtils.checkConsoleErrors();
  }
};

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始ChatWindow初始化问题修复测试');
  console.log('=' .repeat(50));
  
  try {
    await tests.testRapidToggle();
    await testUtils.wait(2000);
    
    await tests.testDelayedOpen();
    await testUtils.wait(2000);
    
    await tests.testImmediateOpenAfterRefresh();
    await testUtils.wait(2000);
    
    await tests.testUserInfoInitialization();
    
    console.log('\n' + '='.repeat(50));
    console.log('✅ 所有测试完成');
    console.log('📋 请检查上述输出和控制台错误信息');
    console.log('💡 如果没有看到初始化错误，说明修复成功');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

// 监听页面错误
window.addEventListener('error', (event) => {
  if (event.message.includes('enrichMessageWithAvatar') ||
      event.message.includes('safeEnrichMessages') ||
      event.message.includes('isLoadingMore') ||
      event.message.includes('before initialization') ||
      event.message.includes('insertBefore') ||
      event.message.includes('ChatWindow')) {
    console.error('🚨 检测到ChatWindow相关错误:', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack
    });
  }
});

// 监听未捕获的Promise错误
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message &&
      (event.reason.message.includes('enrichMessageWithAvatar') ||
       event.reason.message.includes('safeEnrichMessages') ||
       event.reason.message.includes('isLoadingMore') ||
       event.reason.message.includes('before initialization') ||
       event.reason.message.includes('ChatWindow'))) {
    console.error('🚨 检测到ChatWindow相关Promise错误:', event.reason);
  }
});

// 导出测试函数到全局
window.testChatWindowInitialization = {
  runAllTests,
  tests,
  testUtils
};

console.log('📝 ChatWindow初始化问题修复测试脚本已加载');
console.log('💡 使用方法: testChatWindowInitialization.runAllTests()');
console.log('💡 单独测试: testChatWindowInitialization.tests.testRapidToggle()');
console.log('💡 手动测试: 多次快速点击聊天按钮，观察控制台是否有错误');
