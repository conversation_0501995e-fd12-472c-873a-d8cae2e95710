/**
 * WebSocket Token刷新重连功能测试脚本
 * 在浏览器控制台中运行此脚本来测试功能
 */

// 测试用户模块WebSocket重连
async function testUserWebSocketReconnect() {
  console.log('🧪 开始测试用户模块WebSocket重连功能');
  
  try {
    // 导入用户store
    const { useUserStore } = await import('./src/modules/user/stores/userStore.js');
    const userStore = useUserStore();
    
    // 检查用户是否已登录
    if (!userStore.isLoggedIn) {
      console.error('❌ 用户未登录，无法测试');
      return false;
    }
    
    console.log('✅ 用户已登录，开始测试token刷新');
    
    // 测试自动token刷新
    console.log('🔄 测试自动token刷新场景');
    await userStore.autoRefreshToken();

    // 等待一段时间
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 测试长期token登录
    console.log('🔄 测试长期token登录场景');
    await userStore.loginByLongTermTokenAction();

    console.log('✅ 用户模块WebSocket重连测试完成');
    return true;
  } catch (error) {
    console.error('❌ 用户模块WebSocket重连测试失败:', error);
    return false;
  }
}

// 测试商家模块WebSocket重连
async function testMerchantWebSocketReconnect() {
  console.log('🧪 开始测试商家模块WebSocket重连功能');
  
  try {
    // 导入商家store
    const { useMerchantStore } = await import('./src/modules/merchant/stores/merchantStore.js');
    const merchantStore = useMerchantStore();
    
    // 检查商家是否已登录
    if (!merchantStore.isLoggedIn) {
      console.error('❌ 商家未登录，无法测试');
      return false;
    }
    
    console.log('✅ 商家已登录，开始测试token刷新');
    
    // 测试自动token刷新
    console.log('🔄 测试自动token刷新场景');
    await merchantStore.autoRefreshToken();

    // 等待一段时间
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 测试长期token登录
    console.log('🔄 测试长期token登录场景');
    await merchantStore.loginByLongTermTokenAction();

    console.log('✅ 商家模块WebSocket重连测试完成');
    return true;
  } catch (error) {
    console.error('❌ 商家模块WebSocket重连测试失败:', error);
    return false;
  }
}

// 测试管理员模块WebSocket重连
async function testAdminWebSocketReconnect() {
  console.log('🧪 开始测试管理员模块WebSocket重连功能');
  
  try {
    // 导入管理员store
    const { useAdminStore } = await import('./src/modules/admin/stores/adminStore.js');
    const adminStore = useAdminStore();
    
    // 检查管理员是否已登录
    if (!adminStore.token) {
      console.error('❌ 管理员未登录，无法测试');
      return false;
    }
    
    console.log('✅ 管理员已登录，开始测试token刷新');
    
    // 手动触发token刷新
    await adminStore.loginByLongTermTokenAction();
    
    console.log('✅ 管理员模块WebSocket重连测试完成');
    return true;
  } catch (error) {
    console.error('❌ 管理员模块WebSocket重连测试失败:', error);
    return false;
  }
}

// 综合测试函数
async function testAllWebSocketReconnect() {
  console.log('🚀 开始综合测试所有模块的WebSocket重连功能');
  
  const results = {
    user: false,
    merchant: false,
    admin: false
  };
  
  // 根据当前页面路径决定测试哪个模块
  const currentPath = window.location.pathname;
  
  if (currentPath.startsWith('/user') || currentPath === '/') {
    results.user = await testUserWebSocketReconnect();
  } else if (currentPath.startsWith('/merchant')) {
    results.merchant = await testMerchantWebSocketReconnect();
  } else if (currentPath.startsWith('/admin')) {
    results.admin = await testAdminWebSocketReconnect();
  } else {
    console.warn('⚠️ 当前页面路径不匹配任何模块，无法进行测试');
  }
  
  console.log('📊 测试结果:', results);
  return results;
}

// 检查WebSocket连接状态
async function checkWebSocketStatus() {
  console.log('🔍 检查WebSocket连接状态');
  
  try {
    const { useChatStore } = await import('./src/modules/chat/stores/chat.js');
    const chatStore = useChatStore();
    
    console.log('📊 WebSocket状态:', {
      isConnected: chatStore.isConnected,
      clientStatus: chatStore.clientStatus,
      reconnectAttempts: chatStore.reconnectAttempts,
      lastConnectedAt: chatStore.lastConnectedAt ? new Date(chatStore.lastConnectedAt) : null,
      error: chatStore.error
    });
    
    return chatStore.isConnected;
  } catch (error) {
    console.error('❌ 检查WebSocket状态失败:', error);
    return false;
  }
}

// 暴露测试函数到全局作用域
if (typeof window !== 'undefined') {
  window.testUserWebSocketReconnect = testUserWebSocketReconnect;
  window.testMerchantWebSocketReconnect = testMerchantWebSocketReconnect;
  window.testAdminWebSocketReconnect = testAdminWebSocketReconnect;
  window.testAllWebSocketReconnect = testAllWebSocketReconnect;
  window.checkWebSocketStatus = checkWebSocketStatus;
  
  console.log('🎯 WebSocket重连测试函数已加载到全局作用域:');
  console.log('  - window.testUserWebSocketReconnect()');
  console.log('  - window.testMerchantWebSocketReconnect()');
  console.log('  - window.testAdminWebSocketReconnect()');
  console.log('  - window.testAllWebSocketReconnect()');
  console.log('  - window.checkWebSocketStatus()');
}

// 如果在Node.js环境中运行，导出函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testUserWebSocketReconnect,
    testMerchantWebSocketReconnect,
    testAdminWebSocketReconnect,
    testAllWebSocketReconnect,
    checkWebSocketStatus
  };
}
