@@ -356,7 +356,10 @@
         ElMessage.success('登录成功');
         
         // 登录成功后跳转到首页
-        router.push('/user/home');
+        // 使用优化的导航方式，解决用户登录后白屏问题
+        // router.push('/user/home');
+        await initRouteAndNavigate('/user/home');
+        
       } else {
         ElMessage.error('登录失败，请检查用户名和密码');
       }
@@ -426,10 +429,10 @@
         });
         
         if (loginResult) {
-          ElMessage.success('登录成功');
+          // 登录成功，使用优化的导航方式，避免白屏问题 
           
           // 登录成功后跳转到首页
-          router.push('/user/home');
+          await initRouteAndNavigate('/user/home');
         } else {
           ElMessage.error('登录失败，请检查手机号和验证码');
         }
