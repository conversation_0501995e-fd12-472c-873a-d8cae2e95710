# 到店自提订单功能修改总结

## 修改概述

根据后端新增的 `deliveryType` 字段（0-跑腿员配送，1-商家自配送，2-用户到店自提），对商家订单详情页面进行了适配，支持到店自提订单的完整流程管理。

## 修改的文件

### 1. 类型定义文件 (`src/modules/merchant/types/order.ts`)

**新增字段：**
- `DeliveryInfo` 接口新增：
  - `deliveryType: number` - 配送方式
  - `pickupCode?: string` - 取餐码
  - `pickupCodeUsed?: boolean` - 取餐码是否已使用
  - `pickupCodeUsedTime?: string | null` - 取餐码使用时间
  - `expectedPickupTime?: string` - 预计取餐时间
  - `startTime?: string` - 开始备餐时间
  - `endTime?: string` - 完成时间

- `OrderInfo` 接口新增：
  - `deliveryType: number` - 配送方式

### 2. API 接口文件 (`src/modules/merchant/api/order.ts`)

**新增API函数：**
- `startPickupPreparation(orderId: number)` - 开始备餐
- `completePickupPreparation(orderId: number)` - 完成备餐
- `completePickup(orderId: number, pickupCode: string)` - 确认自提

### 3. 订单详情页面 (`src/modules/merchant/views/order/OrderDetail.vue`)

#### 界面修改：
1. **条件显示收货信息**：非自提订单显示收货信息，自提订单显示自提信息
2. **自提信息区域**：显示配送方式、取餐码、取餐码状态、使用时间、预计取餐时间
3. **配送信息优化**：根据配送方式显示不同的配送信息
4. **自提状态区域**：显示自提相关的状态信息

#### 功能新增：
1. **状态映射函数**：
   - `getDeliveryTypeText()` - 获取配送方式文本
   - `getDeliveryTypeTagType()` - 获取配送方式标签类型
   - `getPickupStatusText()` - 获取自提状态文本
   - `getPickupStatusTagType()` - 获取自提状态标签类型

2. **操作判断函数**：
   - `canStartPickupPreparation()` - 是否可以开始备餐
   - `canCompletePickupPreparation()` - 是否可以完成备餐
   - `canCompletePickup()` - 是否可以确认自提

3. **操作处理函数**：
   - `handleStartPickupPreparation()` - 处理开始备餐
   - `handleCompletePickupPreparation()` - 处理完成备餐
   - `handleCompletePickup()` - 处理确认自提（包含取餐码验证）

#### 操作按钮：
- 根据 `deliveryType` 条件显示不同的操作按钮
- 自提订单显示：开始备餐、备餐完成、确认自提
- 配送订单显示：分配配送员、开始配送、完成配送

## 业务流程

### 到店自提订单流程：
1. **下单** → 用户下单，选择到店自提
2. **接单** → 商家接单，开始备餐准备
3. **备餐中** → 商家点击"开始备餐"
4. **备餐完成** → 商家点击"备餐完成"，等待用户自提
5. **等待自提** → 用户到店，提供取餐码
6. **自提完成** → 商家验证取餐码，确认自提完成
7. **待评价** → 订单完成，等待用户评价

### 商家操作流程：
1. **接单** → 点击"接单"按钮
2. **开始备餐** → 点击"开始备餐"按钮
3. **备餐完成** → 点击"备餐完成"按钮
4. **确认自提** → 用户到店后，输入取餐码确认自提

## 状态映射

### 配送状态到自提状态的映射：
- `WAITING (0)` → "等待接单"
- `PENDING (10)` → "备餐中"
- `ACCEPTED (20)` → "备餐中"
- `PICKING (30)` → "备餐中"
- `PICKED_UP (40)` → "等待自提"
- `DELIVERING (50)` → "等待自提"
- `COMPLETED (60)` → "自提完成"
- `CANCELLED (70)` → "已取消"

## 验证和测试

### 取餐码验证：
- 格式验证：必须为6位数字
- 非空验证：不能为空
- 后端验证：与订单绑定的取餐码匹配

### 错误处理：
- 网络错误提示
- 取餐码错误提示
- 操作失败回滚

## 兼容性

- 保持对原有配送订单的完全兼容
- 通过 `deliveryType` 字段区分不同类型订单
- 不影响现有的配送流程和界面

## 后续建议

1. 添加取餐码的自动生成和管理功能
2. 增加自提订单的统计和报表功能
3. 考虑添加自提订单的消息通知功能
4. 优化移动端的自提订单界面显示
