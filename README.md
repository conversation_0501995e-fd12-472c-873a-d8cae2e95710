# O_Mall 多商家电商平台前端项目

## 项目概述

O_Mall前端是基于Vue 3和Element Plus开发的现代电商平台界面，为多商家电商平台提供流畅、响应式的用户体验。项目采用模块化设计，结构清晰，便于维护和扩展，具有统一的设计风格和交互模式。

## 技术栈

*   核心框架：Vue 3 + Composition API
*   UI组件库：Element Plus
*   状态管理：Pinia
*   路由管理：Vue Router 4
*   HTTP客户端：Axios
*   CSS预处理器：SCSS
*   构建工具：Vite
*   代码规范：ESLint + Prettier
*   TypeScript：类型安全的JavaScript超集

**建议：**

*   可以考虑引入自动化测试工具，例如 Cypress 或 Playwright，以进行端到端测试，确保整体功能的稳定性。
*   对于大型项目，可以考虑使用 Monorepo 结构来管理多个模块，例如使用 pnpm 或 yarn workspaces。

## MCP服务

O_Mall项目集成了MCP（Model Context Protocol，模型上下文协议）服务，为前后端开发智能体提供项目常量、规则、开发指导等信息。

### MCP服务功能

* **统一规范定义**：集中定义并管理项目的规范、常量和配置信息
* **智能体接入支持**：提供结构化数据供前后端开发智能体调用和参考
* **开发指导**：提供代码风格、架构设计和最佳实践的指导
* **资源共享**：实现项目知识的共享和传递

### MCP资源类型

项目MCP服务包含以下几类资源：

1. **常量定义**：项目中使用的状态码、角色类型、主题颜色等常量
2. **规则定义**：代码风格、架构设计、API设计、UI设计等规则
3. **开发指导**：Vue 3 Composition API使用指南、Pinia状态管理指南、项目结构指南等
4. **组件规范**：通用组件的使用规范和示例
5. **API规范**：API接口的设计规范和示例
6. **工作流程**：订单处理、商家入驻、客服工单等业务流程
7. **架构设计**：前端整体架构、模块架构、动态路由架构等设计

## 项目结构

```bash
/
├── public/                 # 静态资源目录
├── src/                    # 源代码目录
│   ├── api/                # API接口定义
│   ├── assets/             # 资源文件(图片、字体等)
│   ├── components/         # 全局通用组件
│   ├── config/             # 项目配置文件
│   ├── directives/         # 自定义指令
│   ├── hooks/              # 可复用的组合式函数
│   ├── layouts/            # 布局组件
│   ├── mcp/                # MCP(模型上下文协议)服务
│   │   ├── resources/      # MCP资源定义
│   │   ├── types.ts        # MCP类型定义
│   │   ├── server.ts       # MCP服务实现
│   │   └── index.ts        # MCP服务入口
│   ├── modules/            # 功能模块（核心）
│   ├── router/             # 路由配置
│   ├── stores/             # Pinia状态管理
│   ├── styles/             # 全局样式和变量
│   ├── types/              # TypeScript类型定义
│   ├── utils/              # 工具函数
│   ├── views/              # 页面视图
│   ├── App.vue             # 根组件
│   ├── main.ts             # 入口文件
│   └── env.d.ts            # 环境变量类型定义
├── .eslintrc.js            # ESLint配置
├── .prettierrc.js          # Prettier配置
├── index.html              # HTML模板
├── package.json            # 项目依赖
├── tsconfig.json           # TypeScript配置
├── vite.config.ts          # Vite配置
└── README.md               # 项目说明文档
```

**建议：**

*   `config/` 目录可以细化，例如分为 `config/index.ts` (项目通用配置), `config/theme.ts` (主题配置), `config/theme.ts` (主题配置), `config/routes.ts` (路由配置) 等。
*   可以考虑将 `directives/` 和 `hooks/` 目录移动到 `src/composables/` 目录下，更符合 Vue 3 的 Composition API 的使用习惯。

## 模块化结构

系统采用模块化设计，每个功能模块都有自己的组件、状态和API定义：

```bash
modules/
├── user/                   # 用户模块
│   ├── components/         # 用户相关组件
│   ├── views/              # 用户相关页面
│   ├── stores/             # 用户状态管理
│   ├── api/                # 用户API接口
│   ├── constants/          # 用户模块常量
│   └── types/              # 用户模块类型定义
├── product/                # 商品模块
├── cart/                   # 购物车模块
├── order/                  # 订单模块
├── payment/                # 支付模块
├── delivery/               # 配送模块
├── merchant/               # 商家模块
├── runner/                 # 跑腿模块
├── admin/                  # 管理员模块
├── points/                 # 积分模块
└── giftcard/               # 礼品卡模块
```

**建议：**

*   模块化结构清晰，建议继续保持。
*   可以考虑为每个模块添加 `README.md` 文件，用于描述模块的功能和使用方法。

## 主要功能

### 用户系统

*   用户注册、登录、忘记密码
*   个人中心（信息管理、订单查看）
*   收货地址管理
*   账户安全设置
*   多角色切换（普通用户、商家、跑腿员）

### 商品浏览与购买

*   商品列表与详情展示
*   商品分类浏览
*   商品搜索（关键词、分类、筛选）
*   商品评价查看
*   相关商品推荐

### 购物车管理

*   添加、移除、修改商品
*   选择/取消选择商品
*   购物车统计
*   结算流程
*   跨设备购物车同步

### 订单系统

*   订单创建与确认
*   订单支付流程
*   订单状态追踪
*   订单取消与退款
*   订单评价

### 商家中心

*   商家入驻流程
*   商品管理（上架、编辑、下架）
*   订单管理与处理
*   商家数据统计
*   结算管理

### 跑腿系统

*   跑腿员注册与认证
*   跑腿订单接单与管理
*   配送路线与状态更新
*   收入统计与提现

### 管理后台

*   多级管理权限
*   用户管理
*   商家管理与审核
*   商品管理与审核
*   订单管理与处理
*   系统配置管理

**建议：**

*   功能模块完善，建议在每个模块的 `README.md` 文件中详细描述功能。
*   可以考虑引入权限管理系统，例如 RBAC (Role-Based Access Control)，以更精细地控制用户权限。

## 设计规范

### 基础设计变量

```scss
// src/styles/variables.scss

// 主要颜色
$primary-color: #2563eb;
$success-color: #10b981;
$warning-color: #f59e0b;
$danger-color: #ef4444;
$info-color: #6b7280;

// 文字颜色
$text-primary: #18181b;
$text-regular: #4b5563;
$text-secondary: #71717a;
$text-placeholder: #a1a1aa;

// 边框颜色
$border-color-base: #e4e4e7;
$border-color-light: #f4f4f5;
$border-color-extra-light: #fafafa;

// 背景颜色
$background-color-base: #f4f4f5;
$background-color-light: #f9fafb;

// 字体家族
$font-family: "Inter", "PingFang SC", "Microsoft YaHei", sans-serif;

// 字体大小
$font-size-base: 14px;
$font-size-small: 12px;
$font-size-large: 16px;
$font-size-extra-large: 18px;

// 圆角
$border-radius-small: 2px;
$border-radius-base: 4px;
$border-radius-large: 8px;
$border-radius-extra-large: 12px;
$border-radius-round: 20px;
$border-radius-circle: 50%;

// 阴影
$box-shadow-light: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$box-shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$box-shadow-dark: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
```

### 组件设计规范

所有自定义组件应遵循以下设计原则：

*   单一职责原则：一个组件只负责一个功能
*   可组合性：组件可以轻松组合以构建复杂界面
*   可重用性：组件设计应避免特定上下文依赖
*   可测试性：组件应易于单元测试
*   一致的命名：使用PascalCase命名组件，props使用camelCase

**建议：**

*   组件设计规范良好，建议继续保持。
*   可以考虑使用 Storybook 来管理和展示组件，方便团队成员查看和使用。

### UI组件库扩展

```bash
components/
├── base/                  # 基础组件
│   ├── AppButton.vue      # 按钮组件
│   ├── AppCard.vue        # 卡片组件
│   ├── AppDialog.vue      # 弹窗组件
│   ├── AppTable.vue       # 表格组件
│   └── AppTabs.vue        # 选项卡组件
├── form/                  # 表单组件
│   ├── AppForm.vue        # 表单组件
│   ├── AppInput.vue       # 输入框组件
│   ├── AppSelect.vue      # 选择器组件
│   └── AppUpload.vue      # 上传组件
├── layout/                # 布局组件
│   ├── AppHeader.vue      # 头部组件
│   ├── AppFooter.vue      # 底部组件
│   ├── AppSidebar.vue     # 侧边栏组件
│   └── AppBreadcrumb.vue  # 面包屑组件
└── common/                # 通用组件
    ├── AppEmpty.vue       # 空状态组件
    ├── AppLoading.vue     # 加载组件
    ├── AppPagination.vue  # 分页组件
    └── AppPrice.vue       # 价格组件
```

**建议：**

*   组件分类清晰，建议继续保持。
*   可以考虑为每个组件编写详细的文档，包括 props、events、slots 等。

## API接口管理

### 基础API配置

```typescript
// src/api/config.ts
import axios from 'axios';
import { useUserStore } from '@/stores/user';
import { ElMessage } from 'element-plus';

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
request.interceptors.request.use(
  config => {
    const userStore = useUserStore();
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data;
    if (res.code !== 200) {
      ElMessage.error(res.message || '操作失败');
      
      // 401: 未登录或Token过期
      if (res.code === 401) {
        const userStore = useUserStore();
        userStore.logout();
      }
      
      return Promise.reject(new Error(res.message || '操作失败'));
    }
    return res.data;
  },
  error => {
    const message = error.response?.data?.message || '网络错误，请稍后再试';
    ElMessage.error(message);
    return Promise.reject(error);
  }
);

export default request;
```

### 模块化API定义

```typescript
// src/modules/user/api/index.ts
import request from '@/utils/request';
import type { LoginParams, RegisterParams, UserInfo } from '../types';

export function login(data: LoginParams) {
  return request({
    url: '/api/v1/users/login',
    method: 'post',
    data
  });
}

export function register(data: RegisterParams) {
  return request({
    url: '/api/v1/users/register',
    method: 'post',
    data
  });
}

export function getUserInfo() {
  return request({
    url: '/api/v1/users/profile',
    method: 'get'
  });
}

export function updateUserInfo(data: Partial<UserInfo>) {
  return request({
    url: '/api/v1/users/profile',
    method: 'put',
    data
  });
}

export function logout() {
  return request({
    url: '/api/v1/users/logout',
    method: 'post'
  });
}
```

**建议：**

*   API 接口管理清晰，建议继续保持。
*   可以考虑使用 OpenAPI (Swagger) 来定义 API 接口，方便生成 API 文档和客户端代码。

## 状态管理

### Pinia Store设计

```typescript
// src/stores/user.ts
import { defineStore } from 'pinia';
import { login, logout, getUserInfo } from '@/modules/user/api';
import type { UserInfo, LoginParams } from '@/modules/user/types';
import { setToken, removeToken } from '@/utils/auth';

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: null as UserInfo | null,
    roles: [] as string[]
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    userName: (state) => state.userInfo?.nickname || state.userInfo?.username || '用户',
    avatar: (state) => state.userInfo?.avatar || '/images/default-avatar.png',
    // 判断是否有特定角色
    hasRole: (state) => (role: string) => state.roles.includes(role)
  },
  
  actions: {
    async login(loginParams: LoginParams) {
      try {
        const data = await login(loginParams);
        this.token = data.token;
        setToken(data.token);
        return await this.fetchUserInfo();
      } catch (error) {
        return Promise.reject(error);
      }
    },
    
    async fetchUserInfo() {
      try {
        const data = await getUserInfo();
        this.userInfo = data;
        this.roles = data.roles || [];
        return data;
      } catch (error) {
        return Promise.reject(error);
      }
    },
    
    async logout() {
      try {
        if (this.token) {
          await logout();
        }
      } catch (error) {
        console.error('Logout error', error);
      } finally {
        this.token = '';
        this.userInfo = null;
        this.roles = [];
        removeToken();
      }
    }
  }
});
```

**建议：**

*   Pinia Store 设计合理，建议继续保持。
*   可以考虑使用 Pinia 的插件，例如 `pinia-plugin-persist`，来实现状态持久化。

## 路由管理

### 路由配置

```typescript
// src/router/index.ts
import { createRouter, createWebHistory } from 'vue-router';
import { useUserStore } from '@/stores/user';
import Layout from '@/layouts/MainLayout.vue';
import userRoutes from '@/modules/user/router';
import productRoutes from '@/modules/product/router';
import cartRoutes from '@/modules/cart/router';
import orderRoutes from '@/modules/order/router';
import merchantRoutes from '@/modules/merchant/router';
import runnerRoutes from '@/modules/runner/router';
import adminRoutes from '@/modules/admin/router';

const routes = [
  {
    path: '/',
    component: Layout,
    redirect: '/home',
    children: [
      {
        path: 'home',
        name: 'Home',
        component: () => import('@/views/Home.vue'),
        meta: { title: '首页', requiresAuth: false }
      }
    ]
  },
  
  // 各模块路由
  ...userRoutes,
  ...productRoutes,
  ...cartRoutes,
  ...orderRoutes,
  ...merchantRoutes,
  ...runnerRoutes,
  
  // 管理后台路由
  {
    path: '/admin',
    component: () => import('@/layouts/AdminLayout.vue'),
    meta: { requiresAuth: true, roles: ['admin'] },
    children: adminRoutes
  },
  
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/404.vue')
  }
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior() {
    return { top: 0 };
  }
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = `${to.meta.title || '未知页面'} - O_Mall商城`;
  
  const userStore = useUserStore();
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  
  // 处理需要登录的页面
  if (requiresAuth) {
    if (!userStore.isLoggedIn) {
      next({ name: 'Login', query: { redirect: to.fullPath } });
      return;
    }
    
    // 如果需要特定角色
    if (to.meta.roles && to.meta.roles.length > 0) {
      const hasRole = to.meta.roles.some((role: string) => userStore.hasRole(role));
      if (!hasRole) {
        next({ name: 'Forbidden' });
        return;
      }
    }
  }
  
  next();
});

export default router;
```

### 模块化路由定义

```typescript
// src/modules/user/router/index.ts
import Layout from '@/layouts/MainLayout.vue';

export default [
  {
    path: '/user',
    component: Layout,
    redirect: '/user/profile',
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('../views/Login.vue'),
        meta: { title: '登录', requiresAuth: false }
      },
      {
        path: 'register',
        name: 'Register',
        component: () => import('../views/Register.vue'),
        meta: { title: '注册', requiresAuth: false }
      },
      {
        path: 'forgot-password',
        name: 'ForgotPassword',
        component: () => import('../views/ForgotPassword.vue'),
        meta: { title: '忘记密码', requiresAuth: false }
      },
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('../views/Profile.vue'),
        meta: { title: '个人中心', requiresAuth: true }
      },
      {
        path: 'addresses',
        name: 'UserAddresses',
        component: () => import('../views/Addresses.vue'),
        meta: { title: '地址管理', requiresAuth: true }
      },
      {
        path: 'settings',
        name: 'UserSettings',
        component: () => import('../views/Settings.vue'),
        meta: { title: '账户设置', requiresAuth: true }
      }
    ]
  }
];
```

**建议：**

*   路由管理清晰，建议继续保持。
*   可以考虑使用 Vue Router 的 `scrollBehavior` 选项，来实现页面滚动行为的定制。

## 组合式API的使用

### 可复用钩子函数示例

```typescript
// src/hooks/useCart.ts
import { computed, ref } from 'vue';
import { useCartStore } from '@/modules/cart/stores';
import { ElMessage } from 'element-plus';
import type { Product } from '@/modules/product/types';

export function useCart() {
  const cartStore = useCartStore();
  const loading = ref(false);
  
  const cartItems = computed(() => cartStore.items);
  const totalAmount = computed(() => cartStore.totalAmount);
  const totalPrice = computed(() => cartStore.totalPrice);
  
  async function addToCart(product: Product, quantity = 1) {
    loading.value = true;
    try {
      await cartStore.addItem(product, quantity);
      ElMessage.success('已加入购物车');
    } catch (error) {
      ElMessage.error('加入购物车失败');
    } finally {
      loading.value = false;
    }
  }
  
  async function updateQuantity(productId: number, quantity: number) {
    loading.value = true;
    try {
      await cartStore.updateItemQuantity(productId, quantity);
    } catch (error) {
      ElMessage.error('更新数量失败');
    } finally {
      loading.value = false;
    }
  }
  
  async function removeFromCart(productId: number) {
    loading.value = true;
    try {
      await cartStore.removeItem(productId);
      ElMessage.success('已从购物车移除');
    } catch (error) {
      ElMessage.error('移除失败');
    } finally {
      loading.value = false;
    }
  }
  
  return {
    cartItems,
    totalAmount,
    totalPrice,
    loading,
    addToCart,
    updateQuantity,
    removeFromCart
  };
}
```

### 组件中的使用示例

```vue
<!-- src/modules/product/components/ProductCard.vue -->
<template>
  <div class="product-card">
    <div class="product-image">
      <img :src="product.image" :alt="product.name" />
    </div>
    <div class="product-info">
      <h3 class="product-name">{{ product.name }}</h3>
      <div class="product-price">
        <AppPrice :value="product.price" />
      </div>
      <div class="product-actions">
        <AppButton 
          type="primary" 
          size="small" 
          :loading="loading" 
          @click="handleAddToCart"
        >
          加入购物车
        </AppButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { AppButton, AppPrice } from '@/components/base';
import { useCart } from '@/hooks/useCart';
import type { Product } from '@/modules/product/types';

const props = defineProps<{
  product: Product;
}>();

const { loading, addToCart } = useCart();

function handleAddToCart() {
  addToCart(props.product, 1);
}
</script>

<style lang="scss" scoped>
.product-card {
  border-radius: $border-radius-base;
  overflow: hidden;
  box-shadow: $box-shadow-light;
  transition: transform 0.3s, box-shadow 0.3s;
  background-color: #fff;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: $box-shadow-base;
  }
  
  .product-image {
    width: 100%;
    height: 0;
    padding-bottom: 100%;
    position: relative;
    overflow: hidden;
    
    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .product-info {
    padding: 12px;
  }
  
  .product-name {
    font-size: $font-size-base;
    margin: 0 0 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: $text-primary;
  }
  
  .product-price {
    color: $primary-color;
    font-weight: bold;
    margin-bottom: 8px;
  }
  
  .product-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
```

**建议：**

*   组合式 API 使用规范，建议继续保持。
*   可以考虑将常用的组合式 API 封装成插件，方便在项目中统一使用。

## 表单设计与验证

### 统一表单设计

```vue
<!-- src/modules/user/components/LoginForm.vue -->
<template>
  <AppForm 
    ref="formRef"
    :model="formData"
    :rules="rules"
    @submit="handleSubmit"
  >
    <AppFormItem prop="username" label="用户名">
      <AppInput 
        v-model="formData.username"
        placeholder="请输入用户名"
        prefix-icon="User"
      />
    </AppFormItem>
    
    <AppFormItem prop="password" label="密码">
      <AppInput 
        v-model="formData.password"
        placeholder="请输入密码"
        prefix-icon="Lock"
        type="password"
        show-password
      />
    </AppFormItem>
    
    <div class="form-actions">
      <AppButton 
        type="primary" 
        native-type="submit"
        :loading="loading"
        block
      >
        登录
      </AppButton>
    </div>
    
    <div class="form-links">
      <RouterLink to="/user/register">注册新账号</RouterLink>
      <RouterLink to="/user/forgot-password">忘记密码</RouterLink>
    </div>
  </AppForm>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { AppForm, AppFormItem, AppInput, AppButton } from '@/components/form';
import { useUserStore } from '@/stores/user';
import type { LoginParams } from '../types';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const formRef = ref();
const loading = ref(false);

const formData = reactive<LoginParams>({
  username: '',
  password: ''
});

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在3到20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在6到20个字符', trigger: 'blur' }
  ]
};

async function handleSubmit() {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;
    
    await userStore.login(formData);
    
    // 登录成功后跳转
    const redirectPath = route.query.redirect as string || '/';
    router.replace(redirectPath);
  } catch (error) {
    console.error('Login failed:', error);
  } finally {
    loading.value = false;
  }
}
</script>

<style lang="scss" scoped>
.form-actions {
  margin-top: 24px;
}

.form-links {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  font-size: $font-size-small;
  
  a {
    color: $primary-color;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
```

## 国际化支持

系统支持多语言切换，使用Vue I18n进行国际化配置：

```typescript
// src/i18n/index.ts
import { createI18n } from 'vue-i18n';
import zhCN from './locale/zh-CN';
import enUS from './locale/en-US';

const messages = {
  'zh-CN': zhCN,
  'en-US': enUS
};

const i18n = createI18n({
  legacy: false,
  locale: localStorage.getItem('language') || 'zh-CN',
  fallbackLocale: 'zh-CN',
  messages
});

export default i18n;
```

**建议:**

*   国际化支持完善，建议继续保持。
*   可以考虑使用 i18next 替代 Vue I18n，i18next 更加灵活和强大。

## 主题与样式定制

系统支持主题切换和样式定制：

```typescript
// src/hooks/useTheme.ts
import { ref, watchEffect } from 'vue';

export function useTheme() {
  const theme = ref(localStorage.getItem('theme') || 'light');
  
  watchEffect(() => {
    document.documentElement.setAttribute('data-theme', theme.value);
    localStorage.setItem('theme', theme.value);
  });
  
  function setTheme(newTheme: 'light' | 'dark') {
    theme.value = newTheme;
  }
  
  function toggleTheme() {
    theme.value = theme.value === 'light' ? 'dark' : 'light';
  }
  
  return {
    theme,
    setTheme,
    toggleTheme
  };
}
```

## 环境配置

### 开发环境配置

```ini
# .env.development
VITE_API_BASE_URL = 'http://localhost:8080/api'
VITE_APP_TITLE = 'O_Mall开发环境'
VITE_APP_ENV = 'development'
```

### 生产环境配置

```ini
# .env.production
VITE_API_BASE_URL = 'https://api.o-mall.com/api'
VITE_APP_TITLE = 'O_Mall'
VITE_APP_ENV = 'production'
```

**建议：**

*   环境配置清晰，建议继续保持。
*   可以考虑使用不同的配置文件来管理不同环境的配置，例如 `config/development.ts` 和 `config/production.ts`。

## 构建与部署

### 开发模式

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 生产构建

```bash
# 构建生产版本
npm run build

# 预览生产构建
npm run preview
```

### Docker部署

```bash
# 构建Docker镜像
docker build -t o-mall-frontend .

# 运行容器
docker run -d -p 80:80 --name o-mall-web o-mall-frontend
```

**建议：**

*   构建与部署流程清晰，建议继续保持。
*   可以考虑使用 CI/CD 工具，例如 Jenkins 或 GitHub Actions，来实现自动化构建和部署。

## 性能优化策略

*   代码分割：使用Vue Router的动态导入实现按需加载
*   缓存策略：使用Service Worker缓存静态资源
*   图片优化：使用webp格式和懒加载处理图片
*   状态管理优化：Pinia的细粒度状态订阅
*   虚拟列表：处理大数据列表展示
*   组件懒加载：非关键组件延迟加载
*   预请求关键数据：在路由导航前预加载数据

**建议：**

*   性能优化策略完善，建议继续保持。
*   可以考虑使用 Lighthouse 或 PageSpeed Insights 来评估网站的性能，并根据评估结果进行优化。

## 测试策略

项目使用Vitest和Vue Test Utils进行单元测试和组件测试：

```typescript
// src/modules/user/components/__tests__/LoginForm.spec.ts
import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import LoginForm from '../LoginForm.vue';
import { useUserStore } from '@/stores/user';

vi.mock('vue-router', () => ({
  useRouter: () => ({
    replace: vi.fn()
  }),
  useRoute: () => ({
    query: {}
  })
}));

describe('LoginForm', () => {
  it('should validate form fields', async () => {
    const wrapper = mount(LoginForm, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn
          })
        ],
        stubs: ['RouterLink']
      }
    });
    
    // 表单提交测试
    await wrapper.find('form').trigger('submit.prevent');
    
    // 验证错误信息显示
    expect(wrapper.text()).toContain('请输入用户名');
    expect(wrapper.text()).toContain('请输入密码');
  });
  
  it('should call login action when form is valid', async () => {
    const wrapper = mount(LoginForm, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn
          })
        ],
        stubs: ['RouterLink']
      }
    });
    
    const userStore = useUserStore();
    
    // 填充表单
    await wrapper.find('input[type="text"]').setValue('testuser');
    await wrapper.find('input[type="password"]').setValue('password123');
    
    // 提交表单
    await wrapper.find('form').trigger('submit.prevent');
    
    // 验证store action被调用
    expect(userStore.login).toHaveBeenCalledWith({
      username: 'testuser',
      password: 'password123'
    });
  });
});
```

**建议：**

*   测试策略完善，建议继续保持。
*   可以考虑使用 Codecov 或 SonarQube 来评估代码覆盖率和代码质量。

## 动态路由系统

本项目实现了基于后端数据的动态路由系统，支持根据用户权限动态生成菜单和路由。该系统解决了刷新动态路由页面时重定向到404页面的常见问题。

### 相关文件结构

```bash
src/
├── router/
│   └── index.ts                    # 主路由配置文件 - 负责路由初始化和动态路由注册
├── modules/
│   └── admin/
│       ├── router/
│       │   └── index.ts            # 管理员模块路由配置 - 包含动态路由生成逻辑
│       └── stores/
│           └── adminStore.ts       # 管理员状态管理 - 负责获取前端路径数据
├── utils/
│   └── routeGuard.ts               # 路由守卫工具 - 处理路由权限和路由加载状态
├── views/
│   └── DynamicRouteHandler.vue     # 动态路由处理中间件组件 - 解决刷新问题
└── layouts/
    └── AdminLayout.vue             # 管理员布局组件 - 负责动态菜单生成
```

### 动态路由实现原理

1. **数据来源**：
   - 动态路由配置从后端API获取，存储在`adminStore`中
   - 数据格式为模块化的路径配置，包含路径、标题、图标等信息
   - 数据通过`localforage`在本地持久化缓存，提高访问速度

2. **路由注册流程**：
   - 在`src/router/index.ts`中，首先注册基础静态路由
   - 通过`initializeRoutes`函数，从`localforage`恢复缓存的路径数据
   - 调用`generateDynamicRoutes`函数生成动态路由配置
   - 使用`router.addRoute`方法动态添加到Vue Router实例

3. **刷新页面处理机制**：
   - 在`src/router/index.ts`中，专门设置了动态路由处理器路由（优先级高于404）：
     ```javascript
     // 动态路由处理器（高优先级）
     router.addRoute({
       path: '/admin/:pathMatch(.*)*',
       name: 'AdminDynamicRouteHandler',
       component: () => import('@/views/DynamicRouteHandler.vue')
     });
     
     // 通用404页面（低优先级）
     router.addRoute({
       path: '/:pathMatch(.*)*',
       name: 'NotFound',
       component: () => import('@/views/404.vue')
     });
     ```
   - 当刷新动态路由页面时，会先匹配到`AdminDynamicRouteHandler`，而不是404页面
   - `DynamicRouteHandler.vue`组件会重新获取路径数据、注册动态路由，然后重定向到正确页面

4. **路由守卫**：
   - `src/utils/routeGuard.ts`提供路由守卫功能，处理权限控制和加载状态
   - 通过`beforeEach`钩子检测未匹配路由，并帮助处理导航

### 使用方法

#### 1. 添加新的动态路由

要添加新的动态路由，只需在后端数据库中添加新的路径配置，前端会自动获取并注册：

```javascript
// 路径数据示例
{
  "module": "admin",
  "paths": [
    {
      "path": "permissions",
      "title": "权限",
      "config_key": "admin_permissions_权限",
      "config_type": "page",
      "group": "permissions",
      "icon": "Aim",
      "id": 5
    },
    // 添加新的路径配置...
  ]
}
```

#### 2. 开发新的动态路由组件

1. 在`src/views/`中创建对应的Vue组件
2. 确保组件已在`main.ts`中预加载或使用适当的懒加载策略
3. 在管理员模块路由配置中，确保`getDynamicConfigComponent`函数能够正确加载组件

#### 3. 自定义动态路由行为

如需自定义动态路由的行为，可以修改以下文件：

- `src/views/DynamicRouteHandler.vue`: 修改动态路由处理逻辑
- `src/modules/admin/router/index.ts`: 修改动态路由生成逻辑
- `src/router/index.ts`: 修改路由注册逻辑
- `src/utils/routeGuard.ts`: 修改路由守卫和权限控制逻辑

### 注意事项与最佳实践

1. **路由名称约定**：
   - 动态路由名称遵循`Admin{PathName}`格式，首字母大写
   - 例如：路径为`/admin/users`，对应的路由名称为`AdminUsers`

2. **组件加载策略**：
   - 静态路由组件使用动态导入（懒加载）：`() => import('@/views/Home.vue')`
   - 动态路由组件使用预加载或特殊处理，确保刷新时可用

3. **刷新问题排查**：
   - 如遇刷新问题，检查浏览器控制台日志中的路由匹配情况
   - 确认动态路由处理器(`DynamicRouteHandler.vue`)是否正确触发
   - 验证路由名称是否符合命名规范

4. **性能优化**：
   - 使用`localforage`缓存路径数据，减少API请求
   - 适当延迟路由注册，确保组件加载完成
   - 登出时清除路由缓存，避免权限问题

### 技术实现亮点

1. **处理404重定向问题**：通过专门的中间件组件拦截未匹配路由请求，而不是依赖路由守卫
2. **路由名称智能匹配**：支持精确匹配和相似路由查找，提高路由恢复成功率
3. **路由优先级管理**：特殊路由（如动态路由处理器）优先级高于通用404路由
4. **详细日志**：关键操作点添加清晰的日志信息，便于调试和问题排查

# 网格管理模块重构

## 重构说明

为了提高代码的可维护性和组件间的数据共享能力，我们对网格管理模块进行了重构，将原有的服务层实现转换为 Pinia Store 实现。

## 设计理念

1. **状态集中管理**：使用 Pinia Store 集中管理网格管理相关的状态，方便不同组件间共享数据。
2. **功能模块化**：按照功能将状态和方法分组，使代码结构更清晰。
3. **类型安全**：通过 TypeScript 类型定义，确保数据操作的类型安全。
4. **易于维护**：统一的状态管理方式，使得代码更易于维护和扩展。

## 模块划分

### 1. 模块和分组管理
- 管理模块列表、选中的模块
- 管理分组列表、选中的分组
- 相关方法：`handleModuleChange`, `handleGroupChange`, `refreshCurrentModule`, `refreshAllModules`

### 2. API 和控制器管理
- 管理 API 文档、API 列表
- 管理控制器列表
- 相关方法：`fetchApiDoc`, `fetchControllers`, `filterApiDoc`

### 3. DTO 管理
- 管理 DTO 列表、选中的 DTO、DTO 源数据
- 相关方法：`selectDto`, `setDtoSource`, `updateDtoSourceJson`

### 4. 页面配置管理
- 管理页面配置数据、页面标题、前端路径等
- 相关方法：`generateConfig`, `saveConfig`, `loadConfigById`

### 5. 网格项管理
- 管理网格项列表、当前编辑的网格项
- 相关方法：`addGridItem`, `updateGridItem`, `removeGridItem`, `updateGridPositions`

### 6. UI 状态管理
- 管理各种对话框的显示状态、列表折叠状态

## 使用方法

1. 在组件中导入 Store：
```typescript
import { useGridManagementStore } from '../stores/gridManagementStore';
```

2. 在组件中使用 Store：
```typescript
const store = useGridManagementStore();
```

3. 访问 Store 中的状态：
```typescript
// 读取状态
const modules = store.modules;
const selectedModule = store.selectedModule;

// 修改状态
store.selectedModule = 'admin';
```

4. 调用 Store 中的方法：
```typescript
// 调用方法
await store.handleModuleChange();
await store.fetchApiDoc();
```

## 示例

### 模块和分组操作
```typescript
// 初始化模块数据
await store.handleModuleChange();

// 切换分组
await store.handleGroupChange('users');
```

### 页面配置操作
```typescript
// 加载配置
store.loadConfigById(config);

// 保存配置
await store.saveConfig();
```

### 网格项操作
```typescript
// 添加网格项
store.addGridItem(gridItem);

// 更新网格项
store.updateGridItem(id, updates);

// 删除网格项
store.removeGridItem(id);
```

## 注意事项

1. 所有与后端交互的方法都是异步的，需要使用 `await` 或 `.then()` 处理。
2. 在使用 Store 前，确保 Pinia 已经在应用程序中正确初始化。
3. 修改数据时，应该优先使用 Store 中提供的方法，而不是直接修改状态。
