/**
 * WebSocket连接管理修复测试脚本
 * 用于测试修复后的WebSocket连接管理是否正常工作
 * 
 * 使用方法：
 * 1. 在浏览器控制台中运行此脚本
 * 2. 观察控制台输出，检查是否还有重复连接问题
 * 3. 测试各种场景下的连接状态
 */

// 测试工具函数
const testUtils = {
  // 等待指定时间
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // 获取当前连接状态
  getConnectionStatus: () => {
    const chatStore = window.$pinia?.state?.value?.chat;
    if (!chatStore) {
      console.error('❌ 无法获取chatStore');
      return null;
    }
    
    return {
      isConnected: chatStore.isConnected,
      isInitializing: chatStore.isInitializing,
      isDisconnecting: chatStore.isDisconnecting,
      clientStatus: chatStore.clientStatus,
      hasClient: !!chatStore.chatClient,
      reconnectAttempts: chatStore.reconnectAttempts,
      lastConnectedAt: chatStore.lastConnectedAt
    };
  },
  
  // 打印连接状态
  logStatus: (label) => {
    const status = testUtils.getConnectionStatus();
    console.log(`📊 [${label}] 连接状态:`, status);
    return status;
  },
  
  // 获取用户store
  getUserStore: () => {
    return window.$pinia?.state?.value?.user;
  },
  
  // 获取商家store
  getMerchantStore: () => {
    return window.$pinia?.state?.value?.merchant;
  },
  
  // 获取管理员store
  getAdminStore: () => {
    return window.$pinia?.state?.value?.admin;
  }
};

// 测试场景
const tests = {
  // 测试1: 重复初始化保护
  async testDuplicateInitialization() {
    console.log('\n🧪 测试1: 重复初始化保护');
    
    const chatStore = window.$pinia?.state?.value?.chat;
    if (!chatStore) {
      console.error('❌ 无法获取chatStore');
      return;
    }
    
    testUtils.logStatus('初始状态');
    
    // 尝试多次快速初始化
    console.log('🔄 尝试多次快速初始化...');
    const promises = [];
    for (let i = 0; i < 3; i++) {
      promises.push(chatStore.initializeChat({
        userType: 'user',
        userId: 1
      }));
    }
    
    try {
      await Promise.all(promises);
      console.log('✅ 多次初始化完成');
    } catch (error) {
      console.log('⚠️ 初始化过程中出现错误:', error.message);
    }
    
    await testUtils.wait(1000);
    testUtils.logStatus('多次初始化后');
  },
  
  // 测试2: 断开连接保护
  async testDuplicateDisconnection() {
    console.log('\n🧪 测试2: 重复断开连接保护');
    
    const chatStore = window.$pinia?.state?.value?.chat;
    if (!chatStore) {
      console.error('❌ 无法获取chatStore');
      return;
    }
    
    testUtils.logStatus('断开前状态');
    
    // 尝试多次快速断开
    console.log('🔄 尝试多次快速断开...');
    const promises = [];
    for (let i = 0; i < 3; i++) {
      promises.push(chatStore.disconnectChat());
    }
    
    try {
      await Promise.all(promises);
      console.log('✅ 多次断开完成');
    } catch (error) {
      console.log('⚠️ 断开过程中出现错误:', error.message);
    }
    
    await testUtils.wait(1000);
    testUtils.logStatus('多次断开后');
  },
  
  // 测试3: Token刷新重连
  async testTokenRefreshReconnect() {
    console.log('\n🧪 测试3: Token刷新重连');
    
    const chatStore = window.$pinia?.state?.value?.chat;
    if (!chatStore) {
      console.error('❌ 无法获取chatStore');
      return;
    }
    
    testUtils.logStatus('Token刷新前');
    
    // 模拟token刷新重连
    console.log('🔄 模拟Token刷新重连...');
    try {
      await chatStore.reconnectAfterTokenRefresh('user', 1);
      console.log('✅ Token刷新重连完成');
    } catch (error) {
      console.log('⚠️ Token刷新重连出现错误:', error.message);
    }
    
    await testUtils.wait(1000);
    testUtils.logStatus('Token刷新重连后');
  },
  
  // 测试4: 连接状态锁测试
  async testConnectionLocks() {
    console.log('\n🧪 测试4: 连接状态锁测试');
    
    const chatStore = window.$pinia?.state?.value?.chat;
    if (!chatStore) {
      console.error('❌ 无法获取chatStore');
      return;
    }
    
    // 测试初始化锁
    console.log('🔒 测试初始化锁...');
    chatStore.isInitializing = true;
    
    try {
      await chatStore.initializeChat({ userType: 'user', userId: 1 });
      console.log('⚠️ 初始化锁未生效 - 这可能是个问题');
    } catch (error) {
      console.log('✅ 初始化锁生效，跳过了重复初始化');
    }
    
    chatStore.isInitializing = false;
    
    // 测试断开锁
    console.log('🔒 测试断开连接锁...');
    chatStore.isDisconnecting = true;
    
    try {
      await chatStore.disconnectChat();
      console.log('⚠️ 断开连接锁未生效 - 这可能是个问题');
    } catch (error) {
      console.log('✅ 断开连接锁生效，跳过了重复断开');
    }
    
    chatStore.isDisconnecting = false;
    testUtils.logStatus('锁测试完成');
  }
};

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始WebSocket连接管理修复测试');
  console.log('=' .repeat(50));
  
  try {
    await tests.testDuplicateInitialization();
    await testUtils.wait(2000);
    
    await tests.testDuplicateDisconnection();
    await testUtils.wait(2000);
    
    await tests.testTokenRefreshReconnect();
    await testUtils.wait(2000);
    
    await tests.testConnectionLocks();
    
    console.log('\n' + '='.repeat(50));
    console.log('✅ 所有测试完成');
    console.log('📋 请检查上述输出，确认没有重复连接或其他异常');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

// 导出测试函数到全局
window.testWebSocketConnectionManagement = {
  runAllTests,
  tests,
  testUtils
};

console.log('📝 WebSocket连接管理测试脚本已加载');
console.log('💡 使用方法: testWebSocketConnectionManagement.runAllTests()');
console.log('💡 单独测试: testWebSocketConnectionManagement.tests.testDuplicateInitialization()');
