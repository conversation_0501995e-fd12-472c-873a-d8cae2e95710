# 系统通知功能适配文档

## 背景

根据后端API的修改，系统通知不再支持session会话列表模式，而是直接通过消息列表API获取系统消息：
- API路径：`GET /api/v1/chat/messages?type=system`
- 查询条件：`session_id = 0` 且 `notification_type LIKE 'system_%'`

## 修改内容

### 1. 修改分类切换逻辑

在 `ChatWindow.vue` 的 `handleCategoryChange` 函数中，添加了对系统通知分类的特殊处理：

```javascript
// 🔧 系统通知分类使用消息列表模式，其他分类使用会话模式
if (category === MessageCategoryType.SYSTEM) {
  console.log(`📥 开始加载 ${category} 分类的消息列表（直接消息模式）`)
  await loadSystemMessages()
} else {
  console.log(`📥 开始加载 ${category} 分类的会话列表`)
  await loadCategorySessions(category)
  
  // 加载完成后，尝试恢复或自动选择会话
  await autoSelectSession(previousSessionId)
}
```

### 2. 新增系统消息加载函数

添加了 `loadSystemMessages` 函数，专门处理系统消息的加载：

```javascript
const loadSystemMessages = async () => {
  try {
    console.log('🔄 开始加载系统消息列表...')

    // 使用消息分类服务加载系统消息
    const response = await messageCategoryService.getCategoryMessages({
      type: MessageCategoryType.SYSTEM,
      page: 1,
      page_size: 20
    })

    console.log('📡 系统消息API响应:', response)

    // 处理响应数据
    const messageList = response.list || []
    console.log(`✅ 已加载 ${messageList.length} 条系统消息`)

    if (messageList.length > 0) {
      // 将系统消息存储到消息store中，使用特殊的session_id = 0
      const messageStore = useMessageStore()
      
      // 清空之前的系统消息
      messageStore.clearCategoryMessages('system')
      
      // 添加新的系统消息到store
      messageList.forEach((message: any) => {
        // 确保系统消息有正确的session_id
        message.session_id = 0
        messageStore.addMessage(message, 'system')
      })

      // 设置当前会话为系统消息会话（session_id = 0）
      sessionStore.currentSessionId = '0'
      
      console.log('✅ 系统消息加载完成')
    } else {
      console.log('ℹ️ 暂无系统消息')
      // 清空当前会话选择
      sessionStore.currentSessionId = null
    }

  } catch (error) {
    console.error('❌ 加载系统消息失败:', error)
  }
}
```

### 3. 修改UI显示逻辑

#### 3.1 隐藏系统通知的会话列表

修改会话列表的显示条件：

```html
<!-- 会话列表 - 系统通知分类不显示会话列表 -->
<div v-if="showSessionList && currentMessageCategory !== MessageCategoryType.SYSTEM" class="chat-window__sidebar">
```

#### 3.2 修改空状态提示

为系统通知分类提供专门的空状态提示：

```html
<h3 v-if="currentMessageCategory === MessageCategoryType.SYSTEM">系统通知</h3>
<h3 v-else>开始聊天</h3>
<p v-if="currentMessageCategory === MessageCategoryType.SYSTEM">暂无系统通知消息</p>
<p v-else>选择一个会话或创建新的会话开始聊天</p>
<button v-if="currentMessageCategory !== MessageCategoryType.SYSTEM" class="chat-window__start-btn" @click="handleSessionCreate">
  创建新会话
</button>
```

### 4. 修改消息加载逻辑

在 `loadCurrentSessionMessages` 函数中添加对系统消息的特殊处理：

```javascript
// 🔧 特殊处理系统消息（session_id = 0）
if (sessionId === '0' && loadStartCategory === MessageCategoryType.SYSTEM) {
  console.log('🔄 加载系统消息（直接从messageStore获取）')
  const messageStore = useMessageStore()
  const categoryMessages = messageStore.getCategoryMessages('system')
  const systemMessages = categoryMessages.get('0') || []
  
  result = {
    messages: systemMessages,
    total: systemMessages.length,
    has_more: false
  }
} else {
  // 普通会话消息加载
  const messageStore = useMessageStore()
  result = await messageStore.loadMessages(sessionId, loadStartCategory)
}
```

## API调用流程

### 系统通知分类切换流程

1. 用户点击"系统通知"分类
2. 调用 `handleCategoryChange(MessageCategoryType.SYSTEM)`
3. 检测到是系统通知分类，调用 `loadSystemMessages()`
4. 使用 `messageCategoryService.getCategoryMessages({ type: 'system' })` 调用API
5. API请求：`GET /api/v1/chat/messages?type=system`
6. 后端查询：`session_id = 0` 且 `notification_type LIKE 'system_%'`
7. 将返回的消息存储到messageStore，session_id设为0
8. 设置 `currentSessionId = '0'`
9. 界面显示系统消息，不显示会话列表

### 请求拦截器和响应拦截器

所有API请求都会经过聊天模块的请求拦截器和响应拦截器处理：

- **请求拦截器**：添加认证token、设置请求头等
- **响应拦截器**：统一处理响应格式、错误处理等

## 技术特点

1. **向后兼容**：其他分类（chat、order、service）仍使用原有的会话模式
2. **统一接口**：系统通知使用相同的消息显示组件和逻辑
3. **状态管理**：通过messageStore统一管理所有消息状态
4. **响应式更新**：系统消息的变化会自动反映到界面上
5. **错误处理**：完整的错误处理和日志记录

## 注意事项

1. 系统消息使用特殊的 `session_id = 0` 来标识
2. 系统通知分类不显示会话列表，直接显示消息内容
3. 系统消息不支持发送新消息功能
4. 所有API调用都经过统一的请求/响应拦截器处理
5. 保持与其他分类的一致性，使用相同的消息显示组件

## 测试建议

1. 切换到系统通知分类，验证是否正确调用API
2. 检查浏览器网络面板，确认API请求格式正确
3. 验证系统消息是否正确显示
4. 测试与其他分类的切换是否正常
5. 检查控制台日志，确认没有错误信息
