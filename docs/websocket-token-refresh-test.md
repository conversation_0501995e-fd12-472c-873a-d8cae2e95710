# WebSocket Token刷新重连功能测试指南

## 功能概述

本次更新为用户、商家、管理员三个模块添加了token刷新后的WebSocket自动重连功能。当access_token过期并成功刷新后，系统会自动断开旧的WebSocket连接并使用新token重新建立连接。

## 修改的文件

### 1. chatStore.ts
- 添加了 `reconnectAfterTokenRefresh()` 方法
- 改进了 `disconnectChat()` 方法，确保正确断开ChatClient连接

### 2. userStore.ts
- 在 `autoRefreshToken()` 方法的token刷新成功后添加WebSocket重连逻辑
- 在 `loginByLongTermTokenAction()` 方法的长期token登录成功后添加WebSocket重连逻辑
- 只在用户页面时才执行重连

### 3. merchantStore.ts
- 在 `autoRefreshToken()` 方法的token刷新成功后添加WebSocket重连逻辑
- 在 `setTokens()` 方法中添加WebSocket重连逻辑（覆盖长期token登录场景）
- 只在商家页面时才执行重连

### 4. adminStore.ts
- 在 `setTokens()` 和 `setlocacltoken()` 方法中添加WebSocket重连逻辑
- 只在管理员页面时才执行重连

## 测试步骤

### 用户模块测试

1. **登录用户账号**
   - 访问用户页面并登录
   - 确保WebSocket连接正常建立

2. **模拟token过期**
   ```javascript
   // 在浏览器控制台执行
   const { useUserStore } = await import('/src/modules/user/stores/userStore.js')
   const userStore = useUserStore()
   
   // 手动触发token刷新
   await userStore.autoRefreshToken()
   ```

3. **验证重连**
   - 检查控制台日志，应该看到：
     ```
     🔄 [UserStore] Token刷新成功，尝试重新连接WebSocket
     🔄 [ChatStore] Token更新后重新连接WebSocket
     ✅ [UserStore] WebSocket重连成功
     ```

### 商家模块测试

1. **登录商家账号**
   - 访问商家页面并登录
   - 确保WebSocket连接正常建立

2. **模拟token过期**
   ```javascript
   // 在浏览器控制台执行
   const { useMerchantStore } = await import('/src/modules/merchant/stores/merchantStore.js')
   const merchantStore = useMerchantStore()
   
   // 手动触发token刷新
   await merchantStore.autoRefreshToken()
   ```

3. **验证重连**
   - 检查控制台日志，应该看到：
     ```
     🔄 [MerchantStore] Token刷新成功，尝试重新连接WebSocket
     🔄 [ChatStore] Token更新后重新连接WebSocket
     ✅ [MerchantStore] WebSocket重连成功
     ```

### 管理员模块测试

1. **登录管理员账号**
   - 访问管理员页面并登录
   - 确保WebSocket连接正常建立

2. **模拟token过期**
   ```javascript
   // 在浏览器控制台执行
   const { useAdminStore } = await import('/src/modules/admin/stores/adminStore.js')
   const adminStore = useAdminStore()
   
   // 手动触发token刷新
   await adminStore.loginByLongTermTokenAction()
   ```

3. **验证重连**
   - 检查控制台日志，应该看到：
     ```
     🔄 [AdminStore] Token更新成功，尝试重新连接WebSocket
     🔄 [ChatStore] Token更新后重新连接WebSocket
     ✅ [AdminStore] WebSocket重连成功
     ```

## 预期行为

1. **Token刷新成功后**：
   - 旧的WebSocket连接被正确断开
   - 使用新token建立新的WebSocket连接
   - 聊天功能继续正常工作

2. **页面路由检查**：
   - 只有在对应的页面路径下才会执行WebSocket重连
   - 避免在错误的页面执行重连操作

3. **错误处理**：
   - WebSocket重连失败不会影响token刷新过程
   - 错误信息会记录到控制台但不会抛出异常

## 注意事项

1. **测试环境**：确保WebSocket服务器正常运行
2. **网络状态**：测试时保持网络连接稳定
3. **浏览器控制台**：关注控制台日志输出，确认各步骤正常执行
4. **聊天功能**：测试重连后聊天消息收发是否正常

## 故障排除

如果重连失败，检查：
1. WebSocket服务器是否正常运行
2. 新token是否有效
3. 页面路径是否正确匹配
4. 网络连接是否稳定

## 开发调试

在开发环境中，可以使用以下调试函数：
- `window.debugUserChatService()` - 用户聊天服务调试
- `window.debugAdminChatService()` - 管理员聊天服务调试  
- `window.reconnectUserChat()` - 手动重连用户聊天
- `window.reconnectAdminChat()` - 手动重连管理员聊天
