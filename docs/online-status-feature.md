# 在线状态指示器功能实现

## 功能概述

在聊天窗口的会话列表中，每个会话的用户头像右下角会显示一个小圆点，用于指示该用户的在线状态：
- 🟢 **绿色圆点**：用户在线
- ⚫ **灰色圆点**：用户离线

## 实现细节

### 1. HTML 结构

在 `ChatWindow.vue` 中，每个会话项的头像容器内添加了在线状态指示器：

```html
<div class="session-avatar">
  <img v-if="getSessionAvatar(session)" :src="getSessionAvatar(session)" :alt="session.title" />
  <div v-else class="avatar-placeholder">
    {{ getAvatarText(session.title || session.id.toString()) }}
  </div>
  <!-- 在线状态指示器 -->
  <div class="session-status-dot"
       :class="`session-status-dot--${getSessionOnlineStatus(session)}`">
  </div>
</div>
```

### 2. CSS 样式

```css
.session-status-dot {
  position: absolute;
  bottom: 0px;
  right: 0px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  z-index: 10;
}

.session-status-dot--online {
  background-color: #10b981; /* 绿色 */
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
}

.session-status-dot--offline {
  background-color: #6b7280; /* 灰色 */
}
```

### 3. JavaScript 逻辑

#### 获取在线状态函数

```javascript
const getSessionOnlineStatus = (session: any) => {
  if (!session) return 'offline'

  // 首先检查session自身的is_online属性
  if (session.is_online !== undefined) {
    return session.is_online ? 'online' : 'offline'
  }

  // 检查会话成员的在线状态
  const sessionId = session.id.toString()
  const membersStatus = sessionMembersOnlineStatus.value.get(sessionId)

  if (membersStatus && membersStatus.size > 0) {
    // 如果有任何成员在线，则显示为在线
    for (const memberInfo of membersStatus.values()) {
      if (memberInfo.online_status === 'active') {
        return 'online'
      }
    }
  }

  return 'offline'
}
```

#### 状态更新机制

在 `ChatStore` 中实现了 `updateSessionMembersOnlineStatus` 方法：

```javascript
updateSessionMembersOnlineStatus(data: any) {
  const { sessionId, onlineMembers } = data

  if (!sessionId || !onlineMembers) {
    console.warn('⚠️ [ChatStore] 会话成员在线状态数据不完整:', data)
    return
  }

  // 更新对应会话的在线状态
  const session = this.sessions.find(s => s.id.toString() === sessionId.toString())
  if (session) {
    // 检查是否有任何成员在线
    const hasOnlineMembers = onlineMembers.some((member: any) => member.online_status === 'active')
    // 使用类型断言来添加is_online属性
    ;(session as any).is_online = hasOnlineMembers

    console.log('✅ [ChatStore] 已更新会话在线状态:', {
      sessionId,
      hasOnlineMembers,
      sessionTitle: session.title,
      memberCount: onlineMembers.length
    })
  }

  // 更新在线用户状态
  onlineMembers.forEach((member: any) => {
    const userId = member.user_id || member.merchant_id
    if (userId) {
      const status = member.online_status === 'active' ? 'online' : 'offline'
      this.onlineUsers.set(userId.toString(), status as UserOnlineStatus)
    }
  })
}
```

### 4. WebSocket 事件监听

系统通过 WebSocket 监听 `sessionMembersOnline` 事件来实时更新在线状态：

```javascript
// 监听会话成员在线状态事件
chatClient.on('sessionMembersOnline', (data: any) => {
  console.log('🔧 [ChatStore] 收到会话成员在线状态:', data);
  this.updateSessionMembersOnlineStatus(data)
})
```

### 5. 响应式更新

使用 Vue 的 `watch` 监听会话在线状态变化：

```javascript
// 监听会话在线状态变化
watch(
  () => sessions.value.map(s => ({ id: s.id, is_online: (s as any).is_online })),
  (newStatuses, oldStatuses) => {
    if (oldStatuses && newStatuses.length === oldStatuses.length) {
      // 检查是否有在线状态变化
      const changes = newStatuses.filter((newStatus, index) => {
        const oldStatus = oldStatuses[index]
        return oldStatus && newStatus.is_online !== oldStatus.is_online
      })
      
      if (changes.length > 0) {
        console.log('🔄 检测到会话在线状态变化:', changes)
        // 触发界面重新渲染（Vue的响应式系统会自动处理）
      }
    }
  },
  { deep: true }
)
```

## 使用说明

1. **自动更新**：在线状态会通过 WebSocket 实时更新，无需手动刷新
2. **视觉反馈**：绿色圆点表示在线，灰色圆点表示离线
3. **位置**：指示器位于用户头像的右下角
4. **响应式**：状态变化会立即反映在界面上

## 技术特点

- ✅ 实时更新：通过 WebSocket 实现实时状态同步
- ✅ 响应式设计：使用 Vue 3 的响应式系统
- ✅ 视觉清晰：明确的颜色区分和位置设计
- ✅ 性能优化：只在状态变化时更新界面
- ✅ 类型安全：使用 TypeScript 确保类型安全

## 注意事项

1. 在线状态依赖于后端 WebSocket 服务的支持
2. 需要确保 `sessionMembersOnline` 事件的数据格式正确
3. 状态更新是异步的，可能存在短暂的延迟
4. 离线状态是默认状态，只有明确收到在线信息才会显示为在线
