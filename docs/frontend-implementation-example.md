# 前端实现示例代码

## 1. WebSocket服务扩展

### chatWebSocketService.ts 中添加客户状态处理

```typescript
// 在 handleWebSocketMessage 方法中添加
private handleWebSocketMessage(data: any): void {
  console.log('收到WebSocket消息:', data)
  
  switch (data.type) {
    case MessageType.CHAT:
      this.handleChatMessage(data)
      break
    case MessageType.MESSAGE:
      this.handleChatMessage(data)
      break
    case MessageType.SYSTEM:
      this.handleSystemMessage(data)
      break
    case MessageType.HEARTBEAT:
      this.handleHeartbeatMessage(data)
      break
    case MessageType.NOTIFICATION:
      this.handleNotificationMessage(data)
      break
    // 新增：处理客户状态通知
    case 'customer_status':
      this.handleCustomerStatusMessage(data)
      break
    default:
      console.log('未知消息类型:', data.type)
  }
}

// 新增：处理客户状态消息
private handleCustomerStatusMessage(data: any): void {
  console.log('收到客户状态消息:', data)
  
  // 触发客户状态更新事件
  this.emit('customer_status_update', {
    event: data.event,
    data: data.data,
    timestamp: data.timestamp
  })
}

// 在 handleNotificationMessage 方法中添加客户状态事件
private handleNotificationMessage(data: any): void {
  console.log('收到通知消息:', data)

  switch (data.event) {
    case 'connected':
      console.log('服务器确认连接已建立')
      if (this.status.value !== WebSocketStatus.CONNECTED) {
        this.updateStatus(WebSocketStatus.CONNECTED)
        this.emit('connected', { merchantId: this.getMerchantStore().merchantInfo?.id })
      }
      break
    case 'disconnected':
      console.log('服务器通知连接已断开')
      this.updateStatus(WebSocketStatus.DISCONNECTED)
      this.emit('disconnected', {})
      break
    case 'error':
      console.log('服务器通知连接错误')
      this.updateStatus(WebSocketStatus.ERROR)
      this.emit('error', data.data || {})
      break
    // 新增：客户状态相关事件
    case 'customer_online':
    case 'customer_offline':
    case 'customer_status_change':
      console.log('收到客户状态变更通知:', data.event, data.data)
      this.emit('customer_status_update', {
        event: data.event,
        data: data.data,
        timestamp: data.timestamp
      })
      break
    default:
      console.log('未知通知事件:', data.event)
      this.emit('notification', data)
  }
}
```

## 2. MerchantChat组件中的状态处理

### 在 MerchantChat.vue 中添加客户状态监听

```typescript
// 在 initChatUI 函数中添加客户状态监听
const initChatUI = () => {
  const service = getBackgroundService()
  if (!service) {
    console.warn('后台聊天服务不可用，等待服务启动')
    connectionStatus.value = 'disconnected'
    isUIInitialized = false
    return
  }

  if (isUIInitialized) {
    console.log('聊天UI已初始化，只更新连接状态')
    updateConnectionStatus()
    return
  }

  console.log('开始初始化聊天UI')

  // 先清理可能存在的旧监听器
  cleanupEventListeners()

  // 注册事件监听器
  console.log('注册事件监听器')
  service.on('message', handleMessage)
  service.on('notification', handleNotification)
  service.on('connected', handleConnected)
  service.on('disconnected', handleDisconnected)
  service.on('error', handleError)
  
  // 新增：注册客户状态更新监听器
  service.on('customer_status_update', handleCustomerStatusUpdate)

  console.log('事件监听器注册完成')

  // 更新连接状态
  updateConnectionStatus()

  // 启动状态同步定时器
  startStatusSync()

  // 标记为已初始化
  isUIInitialized = true

  console.log('聊天UI初始化完成，当前状态:', connectionStatus.value)
}

// 新增：处理客户状态更新
const handleCustomerStatusUpdate = (statusUpdate: any) => {
  console.log('处理客户状态更新:', statusUpdate)
  
  const { event, data, timestamp } = statusUpdate
  const { customer_id, session_ids, online_status } = data
  
  // 更新相关会话的客户在线状态
  if (session_ids && Array.isArray(session_ids)) {
    session_ids.forEach((sessionId: number) => {
      const session = sessions.value.find(s => s.id === sessionId)
      if (session) {
        // 更新客户信息中的在线状态
        if (session.customer_info) {
          const wasOnline = session.customer_info.is_online
          session.customer_info.is_online = online_status !== 'offline'
          session.customer_info.online_status = online_status
          session.customer_info.last_seen = timestamp
          
          console.log(`会话 ${sessionId} 客户状态更新: ${wasOnline ? '在线' : '离线'} -> ${session.customer_info.is_online ? '在线' : '离线'}`)
        }
        
        // 如果是当前会话，也更新当前会话的状态
        if (currentSession.value && currentSession.value.id === sessionId) {
          if (currentSession.value.customer_info) {
            currentSession.value.customer_info.is_online = online_status !== 'offline'
            currentSession.value.customer_info.online_status = online_status
            currentSession.value.customer_info.last_seen = timestamp
          }
        }
      }
    })
  }
  
  // 显示状态变更提示（可选）
  if (event === 'customer_online') {
    ElMessage({
      message: `客户 ${data.customer_name} 已上线`,
      type: 'info',
      duration: 2000
    })
  } else if (event === 'customer_offline') {
    ElMessage({
      message: `客户 ${data.customer_name} 已离线`,
      type: 'info', 
      duration: 2000
    })
  }
}

// 在 cleanupEventListeners 函数中添加清理
const cleanupEventListeners = () => {
  const service = getBackgroundService()
  if (service) {
    console.log('清理事件监听器')
    service.off('message', handleMessage)
    service.off('notification', handleNotification)
    service.off('connected', handleConnected)
    service.off('disconnected', handleDisconnected)
    service.off('error', handleError)
    // 新增：清理客户状态监听器
    service.off('customer_status_update', handleCustomerStatusUpdate)
  }
}
```

## 3. 状态显示优化

### 在模板中添加更详细的状态显示

```html
<!-- 聊天列表中的状态指示器 -->
<div class="session-avatar">
  <el-avatar 
    :src="session.target_avatar || session.customer_info?.avatar" 
    :size="40"
  >
    {{ (session.target_name || session.customer_info?.name || '客户').charAt(0) }}
  </el-avatar>
  
  <!-- 优化的在线状态指示器 -->
  <span 
    v-if="session.customer_info?.is_online" 
    class="online-indicator"
    :class="{
      'status-active': session.customer_info?.online_status === 'active',
      'status-idle': session.customer_info?.online_status === 'idle'
    }"
    :title="getOnlineStatusText(session.customer_info)"
  ></span>
</div>

<!-- 聊天详情页的状态显示 -->
<span class="customer-status">
  <span 
    class="status-dot" 
    :class="{ 
      'online': session.customer_info?.is_online,
      'active': session.customer_info?.online_status === 'active',
      'idle': session.customer_info?.online_status === 'idle'
    }"
  ></span>
  {{ getDetailedStatusText(currentSession.customer_info) }}
</span>
```

### 添加状态文本处理函数

```typescript
// 获取在线状态提示文本
const getOnlineStatusText = (customerInfo: any) => {
  if (!customerInfo?.is_online) return '离线'
  
  switch (customerInfo.online_status) {
    case 'active':
      return '在线 - 活跃'
    case 'idle':
      return '在线 - 空闲'
    default:
      return '在线'
  }
}

// 获取详细状态文本
const getDetailedStatusText = (customerInfo: any) => {
  if (!customerInfo?.is_online) {
    if (customerInfo?.last_seen) {
      const lastSeen = new Date(customerInfo.last_seen)
      const now = new Date()
      const diffMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60))
      
      if (diffMinutes < 60) {
        return `${diffMinutes}分钟前在线`
      } else if (diffMinutes < 1440) {
        return `${Math.floor(diffMinutes / 60)}小时前在线`
      } else {
        return '离线'
      }
    }
    return '离线'
  }
  
  switch (customerInfo.online_status) {
    case 'active':
      return '在线'
    case 'idle':
      return '在线 (空闲)'
    default:
      return '在线'
  }
}
```

## 4. CSS样式扩展

### 添加不同状态的样式

```scss
.online-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border: 2px solid white;
  border-radius: 50%;
  
  // 活跃状态 - 绿色
  &.status-active {
    background-color: #67c23a;
  }
  
  // 空闲状态 - 黄色
  &.status-idle {
    background-color: #e6a23c;
  }
  
  // 默认在线状态
  &:not(.status-active):not(.status-idle) {
    background-color: #67c23a;
  }
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #909399;
  
  &.online {
    &.active {
      background-color: #67c23a;
    }
    
    &.idle {
      background-color: #e6a23c;
    }
    
    &:not(.active):not(.idle) {
      background-color: #67c23a;
    }
  }
}
```

## 5. 调试和测试

### 添加调试日志

```typescript
// 在开发环境下添加详细日志
const debugCustomerStatus = (action: string, data: any) => {
  if (import.meta.env.DEV) {
    console.group(`🟢 客户状态调试 - ${action}`)
    console.log('时间:', new Date().toLocaleString())
    console.log('数据:', data)
    console.log('当前会话列表状态:', sessions.value.map(s => ({
      id: s.id,
      customer: s.customer_info?.name,
      online: s.customer_info?.is_online,
      status: s.customer_info?.online_status
    })))
    console.groupEnd()
  }
}

// 在状态更新函数中使用
const handleCustomerStatusUpdate = (statusUpdate: any) => {
  debugCustomerStatus('收到状态更新', statusUpdate)
  
  // ... 原有逻辑
  
  debugCustomerStatus('状态更新完成', {
    affected_sessions: statusUpdate.data.session_ids,
    new_status: statusUpdate.data.online_status
  })
}
```

这个实现示例展示了如何在前端处理客户在线状态的实时更新，包括WebSocket消息处理、状态同步、UI更新和调试功能。
