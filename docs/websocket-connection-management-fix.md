# WebSocket连接管理严重Bug修复

## 问题描述

WebSocket服务存在一个严重的连接管理bug：当WebSocket断开时，原来的进程会尝试再次连接，但是项目还会建立新的WebSocket连接进程并尝试连接。由于原来进程已经连接上，就会造成后来进程连接不上而报错，且聊天UI界面中显示"正在连接"和"已断开"状态。管理员、用户、商家三种layout中都存在此问题。

## 根本原因分析

1. **Layout组件中的多次初始化调用**
   - UserLayout, MerchantLayout, AdminLayout都有多个地方调用`initializeChatService()`
   - onMounted钩子和token变化监听器都会触发初始化
   - 缺乏连接状态检查，导致重复初始化

2. **Token变化监听器触发的重复连接**
   - Token变化时会触发重连，但没有检查当前连接状态
   - 可能在已连接状态下再次尝试连接

3. **自动重连机制与手动重连的冲突**
   - WebSocket服务内置自动重连机制
   - 手动重连逻辑与自动重连可能同时运行，造成竞态条件

4. **ChatStore状态管理不一致**
   - `initializeChat`方法缺乏连接状态锁定机制
   - 允许多个ChatClient实例同时存在

## 修复方案

### 1. 实现WebSocket连接状态锁机制

**文件**: `src/modules/chat/stores/chat.ts`

- 在`ChatState`接口中添加状态锁：
  ```typescript
  isInitializing: boolean // 连接初始化状态锁
  isDisconnecting: boolean // 断开连接状态锁
  ```

- 在`initializeChat`方法中添加锁检查：
  ```typescript
  // 连接状态锁检查 - 防止重复初始化
  if (this.isInitializing) {
    console.warn('⚠️ [ChatStore] 聊天服务正在初始化中，跳过重复调用');
    return;
  }
  
  if (this.isConnected && this.chatClient) {
    console.warn('⚠️ [ChatStore] 聊天服务已连接，跳过重复初始化');
    return;
  }
  ```

### 2. 改进WebSocket断开和清理逻辑

**文件**: `src/modules/chat/stores/chat.ts`

- 在`disconnectChat`方法中添加断开状态锁
- 确保彻底清理旧连接：
  - 停止所有定时器和重连机制
  - 清理事件监听器
  - 重置所有连接相关状态
  - 确保ChatClient实例完全销毁

### 3. 优化Layout组件中的WebSocket初始化逻辑

**文件**: 
- `src/layouts/UserLayout.vue`
- `src/layouts/MerchantLayout.vue` 
- `src/layouts/AdminLayout.vue`

**修复内容**:
- 避免重复调用`initializeChat`
- 改进token变化监听器的处理逻辑
- 添加连接状态检查
- 统一初始化时机

**UserLayout修复**:
```javascript
// 监听用户登录状态变化
watch(() => userStore.isLoggedIn, async (isLoggedIn, wasLoggedIn) => {
  if (isLoggedIn && !wasLoggedIn) {
    // 用户从未登录变为已登录，初始化聊天服务
    await initializeChatService();
  } else if (!isLoggedIn && wasLoggedIn) {
    // 用户从已登录变为未登录，断开聊天连接
    if (chatStore.isConnected) {
      await chatStore.disconnectChat();
    }
    closeChat();
  }
});

// onMounted中添加状态检查
if (userStore.isLoggedIn && !chatStore.isConnected && !chatStore.isInitializing) {
  await initializeChatService();
}
```

### 4. 修复自动重连与手动重连的冲突

**文件**: `src/modules/chat/stores/chat.ts`

- 改进`reconnectAfterTokenRefresh`方法
- 在手动重连前确保停止自动重连
- 添加重连状态检查，防止重复重连
- 增加等待时间确保连接完全断开

## 修复效果验证

### 测试脚本

创建了`test-websocket-connection-management.js`测试脚本，包含以下测试场景：

1. **重复初始化保护测试** - 验证多次快速初始化是否被正确阻止
2. **重复断开连接保护测试** - 验证多次快速断开是否被正确处理
3. **Token刷新重连测试** - 验证token更新后的重连逻辑
4. **连接状态锁测试** - 验证状态锁机制是否正常工作

### 使用方法

1. 在浏览器控制台中加载测试脚本
2. 运行 `testWebSocketConnectionManagement.runAllTests()`
3. 观察控制台输出，检查是否还有重复连接问题

## 预期效果

修复后应该实现：

1. ✅ **单一连接保证** - 同一时间只有一个WebSocket连接进程
2. ✅ **状态一致性** - 聊天UI状态与实际连接状态保持一致
3. ✅ **重连稳定性** - Token刷新后能稳定重连，无重复连接
4. ✅ **资源清理** - 断开连接时彻底清理所有相关资源
5. ✅ **竞态条件消除** - 消除自动重连与手动重连的冲突

## 注意事项

1. 修复涉及多个文件，需要确保所有相关组件都已更新
2. 建议在不同用户类型（用户、商家、管理员）下都进行测试
3. 特别关注token刷新场景下的连接稳定性
4. 监控控制台日志，确认没有重复连接警告

## 后续维护

1. 定期检查WebSocket连接日志，确保修复效果持续有效
2. 如果发现新的连接问题，可以参考本次修复的思路进行处理
3. 考虑添加更多的连接状态监控和告警机制
