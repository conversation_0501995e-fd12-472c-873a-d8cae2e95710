# 管理员Layout聊天模块接入文档

## 概述

参考用户layout和商家layout的实现，成功在管理员layout中接入了聊天模块，实现了统一的聊天功能。

## 实现内容

### 1. 模板修改

#### 1.1 Header区域添加聊天按钮

```html
<div class="header-right">
  <!-- 聊天按钮 -->
  <el-tooltip content="客服消息" placement="bottom">
    <el-button link @click="toggleChat" class="chat-button">
      <el-icon><ChatDotRound /></el-icon>
      消息
      <el-badge
        v-if="unreadCount > 0"
        :value="unreadCount"
        :max="99"
        class="chat-badge"
      />
    </el-button>
  </el-tooltip>

  <!-- 用户下拉菜单中也添加聊天选项 -->
  <el-dropdown trigger="click">
    <div class="avatar-container">
      <!-- ... -->
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item @click="navigateToProfile">个人信息</el-dropdown-item>
        <el-dropdown-item @click="openChat" class="chat-menu-item">
          <el-icon><ChatDotRound /></el-icon>
          <span>客服消息</span>
          <el-badge
            v-if="unreadCount > 0"
            :value="unreadCount"
            :max="99"
            class="menu-chat-badge"
          />
        </el-dropdown-item>
        <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</div>
```

#### 1.2 添加聊天窗口组件

```html
<!-- 聊天窗口 -->
<UnifiedChatWindow
  v-if="chatVisible"
  :visible="chatVisible"
  :minimized="chatMinimized"
  title="管理员消息"
  @close="closeChat"
  @minimize="minimizeChat"
  @restore="restoreChat"
  @unread-change="handleUnreadChange"
  @position-change="handlePositionChange"
  @size-change="handleSizeChange"
/>
```

### 2. Script部分修改

#### 2.1 导入聊天相关模块

```javascript
import { ref, computed, onMounted, watch, onBeforeUnmount, provide } from 'vue';
import { ChatDotRound } from '@element-plus/icons-vue';
import UnifiedChatWindow from '@/components/UnifiedChatWindow.vue';
import { useChatStore } from '@/modules/chat/stores';
import { getUnreadCount } from '@/modules/chat/api';
import { uploadConfigService } from '@/modules/chat/services/uploadConfig';
```

#### 2.2 初始化聊天相关状态

```javascript
const chatStore = useChatStore();

// 提供chatStore给子组件
provide('chatStore', chatStore);

// 聊天相关状态
const chatVisible = ref(false);
const chatMinimized = ref(false);

// 计算未读消息总数
const unreadCount = computed(() => {
  return chatStore.totalUnreadCount || 0;
});
```

#### 2.3 聊天相关方法

```javascript
/**
 * 聊天相关方法
 */
function toggleChat() {
  if (chatVisible.value) {
    if (chatMinimized.value) {
      restoreChat();
    } else {
      minimizeChat();
    }
  } else {
    openChat();
  }
}

function openChat() {
  chatVisible.value = true;
  chatMinimized.value = false;
}

function closeChat() {
  chatVisible.value = false;
  chatMinimized.value = false;
}

function minimizeChat() {
  chatMinimized.value = true;
}

function restoreChat() {
  chatMinimized.value = false;
}

function handleUnreadChange(count: number) {
  console.log('管理员聊天窗口未读消息数量变化:', count);
}

function handlePositionChange(x: number, y: number) {
  localStorage.setItem('adminChatWindowPosition', JSON.stringify({ x, y }));
}

function handleSizeChange(width: number, height: number) {
  localStorage.setItem('adminChatWindowSize', JSON.stringify({ width, height }));
}
```

#### 2.4 聊天服务初始化

```javascript
// 获取未读消息数量的API调用（带重试机制）
const callUnreadCountAPI = async (retries = 3) => {
  for (let i = 0; i < retries; i++) {
    try {
      const unreadData = await getUnreadCount();
      const totalUnread = typeof unreadData === 'number' ? unreadData : (unreadData?.total || 0);
      
      if (chatStore && typeof chatStore.setTotalUnreadCount === 'function') {
        chatStore.setTotalUnreadCount(totalUnread);
        console.log('✅ 管理员未读消息总数已更新:', totalUnread);
      }
      
      return totalUnread;
    } catch (error) {
      console.error(`❌ 第${i + 1}次API调用失败:`, error);
      if (i < retries - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }
  
  if (chatStore && typeof chatStore.setTotalUnreadCount === 'function') {
    chatStore.setTotalUnreadCount(0);
  }
  return 0;
};

// 初始化聊天服务
const initializeChatService = async () => {
  try {
    console.log('🚀 开始初始化管理员聊天服务');
    
    if (adminStore.token) {
      // 设置聊天上传配置为商家类型（管理员使用商家接口）
      uploadConfigService.setUserType('merchant');
      
      // 初始化聊天store
      await chatStore.initializeChat({
        userType: 'admin',
        userId: adminStore.currentAdmin?.id
      });
      
      // 获取未读消息数量
      await callUnreadCountAPI();
      
      console.log('✅ 管理员聊天服务初始化完成');
    }
  } catch (error) {
    console.error('❌ 管理员聊天服务初始化失败:', error);
  }
};
```

#### 2.5 生命周期钩子

```javascript
// 监听管理员登录状态变化
watch(() => adminStore.token, async (token) => {
  if (token) {
    await initializeChatService();
  } else {
    if (chatStore.isConnected) {
      await chatStore.disconnectChat();
    }
    closeChat();
  }
});

onMounted(async () => {
  // 设置聊天上传配置
  uploadConfigService.setUserType('merchant');
  
  // 其他初始化逻辑...
  
  // 如果管理员已登录，初始化聊天服务
  if (adminStore.token) {
    await initializeChatService();
  }
  
  // 开发环境调试函数
  if (import.meta.env.DEV) {
    (window as any).debugAdminChatService = () => {
      console.log('🔍 管理员聊天服务调试信息:', {
        hasToken: !!adminStore.token,
        adminInfo: adminStore.currentAdmin,
        chatStore: {
          isConnected: chatStore.isConnected,
          totalUnreadCount: chatStore.totalUnreadCount
        },
        chatVisible: chatVisible.value,
        unreadCount: unreadCount.value
      });
    };
  }
});

onBeforeUnmount(() => {
  console.log('🧹 AdminLayout卸载，保持WebSocket连接运行');
});
```

### 3. 样式修改

```css
/* 聊天按钮样式 */
.chat-button {
  position: relative;
  color: #3b82f6;
  font-weight: 500;
  margin-right: 16px;
}

.chat-button:hover {
  color: #2563eb;
  background-color: rgba(59, 130, 246, 0.1);
}

.chat-badge {
  position: absolute;
  top: -2px;
  right: -2px;
}

/* 菜单中的聊天项样式 */
.chat-menu-item {
  position: relative;
}

.menu-chat-badge {
  position: absolute;
  top: 8px;
  right: 16px;
  z-index: 10;
}

.chat-menu-item:hover {
  background-color: rgba(59, 130, 246, 0.1);
}
```

## 技术特点

### 1. 统一性
- ✅ 使用与用户端和商家端相同的`UnifiedChatWindow`组件
- ✅ 使用统一的`useChatStore`进行状态管理
- ✅ 使用相同的API接口和服务

### 2. 适配性
- ✅ 管理员使用`userType: 'admin'`进行聊天初始化
- ✅ 上传配置使用商家接口（`uploadConfigService.setUserType('merchant')`）
- ✅ 根据`adminStore.token`判断登录状态

### 3. 功能完整性
- ✅ 支持聊天窗口的打开、关闭、最小化、恢复
- ✅ 实时显示未读消息数量
- ✅ 支持窗口位置和大小的保存
- ✅ 完整的错误处理和重试机制

### 4. 开发友好
- ✅ 提供调试函数`window.debugAdminChatService()`
- ✅ 详细的控制台日志输出
- ✅ 完整的TypeScript类型支持

## 使用说明

1. **登录后自动初始化**：管理员登录后会自动初始化聊天服务
2. **多种打开方式**：可通过header按钮或下拉菜单打开聊天窗口
3. **实时未读提醒**：header按钮和菜单项都会显示未读消息数量
4. **窗口状态保存**：聊天窗口的位置和大小会自动保存到localStorage
5. **自动断开连接**：管理员登出时会自动断开聊天连接

## 注意事项

1. **上传接口**：管理员使用商家上传接口（`/v1/merchant/upload`）
2. **用户类型**：聊天初始化时使用`userType: 'admin'`
3. **登录状态判断**：使用`adminStore.token`而不是`adminStore.isLoggedIn`
4. **WebSocket连接**：只在登出时断开，页面切换时保持连接

## 调试方法

在浏览器控制台中使用以下命令进行调试：

```javascript
// 查看管理员聊天服务状态
window.debugAdminChatService()

// 手动重连聊天服务
window.reconnectAdminChat()
```

## 重要修复：WebSocket后台服务

### 问题描述
初始实现中，管理员的WebSocket连接不是作为后台服务运行的，而是在点击聊天按钮时才初始化连接。这与用户端和商家端的实现不一致。

### 根本原因
管理员的token是通过`localforage.getItem()`异步加载的，在`onMounted`执行时token可能还没有加载完成，导致聊天服务初始化被跳过。

### 修复方案

#### 1. 等待Token加载完成
```javascript
onMounted(async () => {
  // 等待token从localforage加载完成
  console.log('⏳ 等待管理员token从localforage加载...');
  let tokenCheckAttempts = 0;
  const maxTokenCheckAttempts = 10;

  const waitForToken = async (): Promise<boolean> => {
    while (tokenCheckAttempts < maxTokenCheckAttempts) {
      tokenCheckAttempts++;
      console.log(`🔍 第${tokenCheckAttempts}次检查管理员token:`, {
        hasToken: !!adminStore.token,
        tokenLength: adminStore.token?.length
      });

      if (adminStore.token) {
        console.log('✅ 管理员token已加载');
        return true;
      }

      // 等待100ms后再次检查
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('⚠️ 管理员token加载超时或未找到token');
    return false;
  };

  const hasToken = await waitForToken();

  // 如果管理员已登录，初始化聊天服务
  if (hasToken) {
    console.log('🚀 管理员已登录，开始初始化聊天服务...');
    await initializeChatService();
  } else {
    console.log('❌ 管理员未登录，跳过聊天服务初始化');
  }
});
```

#### 2. 增强Token变化监听
```javascript
// 监听管理员登录状态变化
watch(() => adminStore.token, async (token, oldToken) => {
  console.log('🔄 管理员token变化:', {
    oldToken: oldToken ? `${oldToken.substring(0, 10)}...` : 'null',
    newToken: token ? `${token.substring(0, 10)}...` : 'null',
    hasToken: !!token
  });

  if (token && !oldToken) {
    // 首次获取到token（从localforage加载完成）
    console.log('✅ 管理员token首次加载完成，初始化聊天服务');
    await initializeChatService();
  } else if (token && oldToken && token !== oldToken) {
    // token更新（重新登录）
    console.log('🔄 管理员token更新，重新初始化聊天服务');
    // 先断开旧连接
    if (chatStore.isConnected) {
      await chatStore.disconnectChat();
    }
    // 重新初始化
    await initializeChatService();
  } else if (!token && oldToken) {
    // 管理员登出
    console.log('👋 管理员登出，断开聊天连接');
    if (chatStore.isConnected) {
      await chatStore.disconnectChat();
    }
    // 关闭聊天窗口
    closeChat();
  }
});
```

#### 3. 延迟检查机制
```javascript
// 延迟检查：如果5秒后聊天服务仍未连接且有token，则尝试重新初始化
setTimeout(async () => {
  if (adminStore.token && !chatStore.isConnected) {
    console.log('⚠️ 延迟检查发现聊天服务未连接，尝试重新初始化');
    await initializeChatService();
  }
}, 5000);
```

#### 4. 增强调试功能
```javascript
(window as any).forceInitAdminChat = async () => {
  console.log('🚀 强制初始化管理员聊天服务');
  await initializeChatService();
};
```

### 修复效果
- ✅ **后台服务**：WebSocket连接现在作为后台服务在页面加载时自动初始化
- ✅ **异步兼容**：正确处理token的异步加载过程
- ✅ **状态同步**：实时监听token变化并相应地管理连接状态
- ✅ **容错机制**：包含延迟检查和重试机制
- ✅ **调试支持**：提供强制初始化功能用于调试

## 总结

管理员layout的聊天模块接入已完成，并修复了WebSocket后台服务的问题。现在管理员的聊天服务会在页面加载时自动初始化，与用户端和商家端保持一致的后台服务模式。管理员可以通过header按钮或下拉菜单访问聊天功能，支持实时消息通知和完整的聊天操作。
