# 客户在线状态功能测试指南

## 前端功能已完成

### 已实现的功能

1. **WebSocket消息处理**
   - ✅ 添加了`customer_status`消息类型处理
   - ✅ 在`handleNotificationMessage`中处理客户状态事件
   - ✅ 触发`customer_status_update`事件

2. **状态更新监听**
   - ✅ 注册`customer_status_update`事件监听器
   - ✅ 实现`handleCustomerStatusUpdate`函数
   - ✅ 更新会话列表和当前会话的客户状态

3. **UI状态显示**
   - ✅ 聊天列表中的在线指示器支持不同状态
   - ✅ 聊天详情页显示详细状态文本
   - ✅ 添加状态提示和最后在线时间

4. **调试功能**
   - ✅ 添加详细的调试日志
   - ✅ 开发环境下的状态变更追踪

## 测试方法

### 1. 前端测试准备

1. **打开浏览器开发者工具**
   - 访问 http://localhost:5174
   - 打开开发者工具的Console面板
   - 登录商家账户并打开聊天模块

2. **查看调试信息**
   - 在Console中可以看到详细的状态调试信息
   - 格式：`🟢 客户状态调试 - [操作]`

### 2. 后端WebSocket消息格式

前端现在可以处理以下格式的WebSocket消息：

#### 方式1：独立的customer_status消息
```json
{
  "type": "customer_status",
  "event": "customer_online",
  "timestamp": "2025-07-19T22:15:00+08:00",
  "data": {
    "customer_id": 3,
    "customer_name": "testuser",
    "session_ids": [13, 15],
    "online_status": "active"
  }
}
```

#### 方式2：notification消息中的客户状态事件
```json
{
  "type": "notification",
  "event": "customer_online",
  "timestamp": "2025-07-19T22:15:00+08:00",
  "data": {
    "customer_id": 3,
    "customer_name": "testuser", 
    "session_ids": [13, 15],
    "online_status": "active"
  }
}
```

### 3. 支持的状态事件

- `customer_online`: 客户上线
- `customer_offline`: 客户下线  
- `customer_status_change`: 客户状态变更（如从active变为idle）

### 4. 支持的状态值

- `active`: 活跃在线（绿色指示器）
- `idle`: 空闲在线（黄色指示器）
- `offline`: 离线（无指示器）

## 手动测试步骤

### 测试客户上线

1. 在浏览器Console中模拟发送WebSocket消息：
```javascript
// 获取WebSocket服务实例
const service = window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps[0]._instance.appContext.config.globalProperties.$chatService

// 模拟客户上线消息
service.emit('customer_status_update', {
  event: 'customer_online',
  data: {
    customer_id: 3,
    customer_name: 'testuser',
    session_ids: [13],
    online_status: 'active'
  },
  timestamp: new Date().toISOString()
})
```

### 测试客户下线

```javascript
service.emit('customer_status_update', {
  event: 'customer_offline',
  data: {
    customer_id: 3,
    customer_name: 'testuser',
    session_ids: [13],
    online_status: 'offline'
  },
  timestamp: new Date().toISOString()
})
```

### 测试状态变更

```javascript
service.emit('customer_status_update', {
  event: 'customer_status_change',
  data: {
    customer_id: 3,
    session_ids: [13],
    online_status: 'idle'
  },
  timestamp: new Date().toISOString()
})
```

## 预期效果

### 聊天列表中
- **绿色圆点**: 客户活跃在线
- **黄色圆点**: 客户空闲在线
- **无圆点**: 客户离线
- **鼠标悬停**: 显示详细状态提示

### 聊天详情页
- **在线**: 客户活跃状态
- **在线 (空闲)**: 客户空闲状态
- **离线**: 客户离线
- **X分钟前在线**: 显示最后在线时间

### 系统提示
- 客户上线时显示：`客户 [姓名] 已上线`
- 客户下线时显示：`客户 [姓名] 已离线`

## 调试信息说明

在开发环境下，Console会显示详细的调试信息：

```
🟢 客户状态调试 - 收到状态更新
  时间: 2025/7/19 22:15:30
  数据: {event: "customer_online", data: {...}, timestamp: "..."}
  当前会话列表状态: [
    {id: 13, customer: "testuser", online: true, status: "active"}
  ]

🟢 客户状态调试 - 状态更新完成
  时间: 2025/7/19 22:15:30
  数据: {affected_sessions: [13], new_status: "active", event: "customer_online"}
```

## 后端集成要点

1. **WebSocket连接管理**
   - 客户连接时记录session_ids映射
   - 商家连接时建立推送通道

2. **状态推送时机**
   - 客户WebSocket连接建立时推送上线消息
   - 客户WebSocket断开时推送下线消息
   - 客户活跃度变化时推送状态变更消息

3. **消息推送范围**
   - 根据session_ids找到相关的商家
   - 只向有会话关系的商家推送状态更新

## 验收标准

- ✅ 客户实际上线时，商家端实时显示在线状态
- ✅ 客户下线后，商家端实时显示离线状态
- ✅ 状态变更延迟不超过5秒
- ✅ 多个会话的状态同步更新
- ✅ 界面状态指示器正确显示不同状态
- ✅ 状态提示文本准确显示

前端功能已完全就绪，等待后端WebSocket推送功能完成即可进行完整测试。
