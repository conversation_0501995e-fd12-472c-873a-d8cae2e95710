# 管理员WebSocket连接Token修复文档

## 问题描述

管理员在建立WebSocket连接时使用的是商家的token而不是管理员的token，这导致认证失败或权限错误。

### 问题现象

从前端日志可以看到：
- Request拦截器正确使用了管理员token：`获取的admin token：存在`
- 但WebSocket连接可能使用了错误的token

### 根本原因

在`chatStore.initializeChat`方法中，token获取逻辑只处理了两种情况：
1. `userType === 'user'` → 获取用户token
2. 其他情况 → 默认获取商家token

当管理员传入`userType: 'admin'`时，代码走的是"其他情况"分支，错误地获取了商家token。

## 问题代码分析

### 1. initializeChat方法中的问题

```javascript
// 问题代码 ❌
if (userType === 'user') {
  // 获取用户token
  const { useUserStore } = await import('@/modules/user/stores/userStore')
  const userStore = useUserStore()
  token = userStore.token || ''
  userId = Number(userStore.userInfo?.id) || 0
} else {
  // 默认获取商家token - 管理员也会走这里！
  const merchantStore = (await import('@/modules/merchant/stores/merchantStore')).useMerchantStore()
  token = merchantStore.token || ''
  userId = Number(merchantStore.merchantInfo?.id) || 0
}
```

### 2. getChatClient方法中的相同问题

```javascript
// 问题代码 ❌
if (userType === 'user') {
  // 获取用户token
} else {
  // 默认获取商家token - 管理员也会走这里！
  const { useMerchantStore } = await import('@/modules/merchant/stores/merchantStore')
  const merchantStore = useMerchantStore()
  token = merchantStore.token || ''
  userId = Number(merchantStore.merchantInfo?.id) || 0
}
```

## 修复方案

### 1. 修复initializeChat方法

添加对`userType === 'admin'`的专门处理：

```javascript
// 修复后 ✅
if (userType === 'user') {
  console.log('🔧 [ChatStore] 开始获取用户token');
  const { useUserStore } = await import('@/modules/user/stores/userStore')
  const userStore = useUserStore()
  token = userStore.token || ''
  userId = Number(userStore.userInfo?.id) || 0
  console.log('🔧 [ChatStore] 用户token获取结果:', { hasToken: !!token, tokenLength: token?.length, userId });
  
  if (!token) {
    throw new Error('用户未登录，无法初始化聊天服务')
  }
} else if (userType === 'admin') {
  console.log('🔧 [ChatStore] 开始获取管理员token');
  // 直接从localforage获取管理员token
  const adminToken = await import('localforage').then(lf => lf.default.getItem('admin_access_token'))
  token = adminToken ? String(adminToken) : ''
  
  // 获取管理员信息
  const { useAdminStore } = await import('@/modules/admin/stores/adminStore')
  const adminStore = useAdminStore()
  userId = Number(adminStore.currentAdmin?.id) || 0
  console.log('🔧 [ChatStore] 管理员token获取结果:', { hasToken: !!token, tokenLength: token?.length, userId });
  
  if (!token) {
    throw new Error('管理员未登录，无法初始化聊天服务')
  }
} else {
  console.log('🔧 [ChatStore] 开始获取商家token');
  const merchantStore = (await import('@/modules/merchant/stores/merchantStore')).useMerchantStore()
  token = merchantStore.token || ''
  userId = Number(merchantStore.merchantInfo?.id) || 0
  console.log('🔧 [ChatStore] 商家token获取结果:', { hasToken: !!token, tokenLength: token?.length, userId });
  
  if (!token) {
    throw new Error('商家未登录，无法初始化聊天服务')
  }
}
```

### 2. 修复getChatClient方法

同样添加对管理员的专门处理：

```javascript
// 修复后 ✅
if (userType === 'user') {
  const { useUserStore } = await import('@/modules/user/stores/userStore')
  const userStore = useUserStore()
  token = userStore.token || ''
  userId = Number(userStore.userInfo?.id) || 0
} else if (userType === 'admin') {
  // 直接从localforage获取管理员token
  const adminToken = await import('localforage').then(lf => lf.default.getItem('admin_access_token'))
  token = adminToken ? String(adminToken) : ''
  
  // 获取管理员信息
  const { useAdminStore } = await import('@/modules/admin/stores/adminStore')
  const adminStore = useAdminStore()
  userId = Number(adminStore.currentAdmin?.id) || 0
} else {
  const { useMerchantStore } = await import('@/modules/merchant/stores/merchantStore')
  const merchantStore = useMerchantStore()
  token = merchantStore.token || ''
  userId = Number(merchantStore.merchantInfo?.id) || 0
}
```

### 3. 简化disconnectChat方法

原来的`disconnectChat`方法有问题，它创建了新的ChatClient实例。简化为只重置状态：

```javascript
// 修复后 ✅
async disconnectChat() {
  try {
    console.log('🔧 [ChatStore] 开始断开聊天连接');
    
    // 重置状态
    this.clientStatus = 'DISCONNECTED' as any
    this.isConnected = false
    this.reconnectAttempts = 0
    
    console.log('✅ [ChatStore] 聊天连接已断开');
    
  } catch (error) {
    console.error('❌ [ChatStore] 断开聊天连接失败:', error);
    this.error = error instanceof Error ? error.message : '断开连接失败'
    throw error
  }
}
```

## 技术要点

### 1. 为什么直接从localforage获取管理员token

管理员的token存储在localforage中，而adminStore.token的初始化是异步的：

```javascript
// adminStore中的异步初始化
localforage.getItem('admin_access_token').then((value) => {
  accessToken.value = value ? String(value) : '';
});
```

为了确保能获取到token，直接从localforage读取是最可靠的方式。

### 2. 用户类型处理逻辑

现在支持三种用户类型：
- `'user'` → 从userStore获取用户token
- `'admin'` → 从localforage获取管理员token
- 其他（包括`'merchant'`） → 从merchantStore获取商家token

### 3. 错误处理

每种用户类型都有对应的错误提示：
- 用户未登录，无法初始化聊天服务
- 管理员未登录，无法初始化聊天服务
- 商家未登录，无法初始化聊天服务

## 预期效果

修复后，管理员建立WebSocket连接时应该能看到类似这样的日志：

```
🔧 [ChatStore] 开始获取管理员token
🔧 [ChatStore] 管理员token获取结果: {hasToken: true, tokenLength: 1234, userId: 1}
🔧 [ChatStore] WebSocket URL构建完成: ws://localhost:8181/api/v1/chat/ws?token=eyJhbGciOi...
```

## 总结

这次修复解决了管理员WebSocket连接使用错误token的问题。通过在chatStore中添加对`userType === 'admin'`的专门处理，确保管理员使用正确的token建立WebSocket连接，从而实现正确的身份认证和权限控制。

修复涉及的文件：
- `src/modules/chat/stores/chat.ts` - 主要修复文件
- 修复了`initializeChat`、`getChatClient`和`disconnectChat`三个方法

现在管理员的聊天服务应该能够使用正确的管理员token建立WebSocket连接，与后端进行正确的身份认证。
