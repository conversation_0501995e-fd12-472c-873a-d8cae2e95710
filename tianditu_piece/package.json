{"name": "project", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 4173", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"axios": "^1.2.0", "dayjs": "^1.11.6", "echarts": "^5.4.0", "echarts-gl": "^2.0.9", "pinia": "^2.0.21", "tmap-wind": "^1.0.4", "vue": "^3.2.38", "vue-router": "^4.1.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.4", "@vitejs/plugin-vue": "^3.0.3", "@vue/eslint-config-prettier": "^7.0.0", "autoprefixer": "^10.4.13", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.3.0", "naive-ui": "^2.34.2", "postcss": "^8.4.19", "prettier": "^2.7.1", "tailwindcss": "^3.2.4", "vite": "^3.0.9"}}