const C=Object.prototype.hasOwnProperty,f=typeof Symbol<"u"?Symbol.toStringTag:void 0;function P(n){if(n===null)return n===void 0?"[object Undefined]":"[object Null]";if(!(f&&f in Object(n)))return toString.call(n);const t=C.call(n,f),i=n[f];let s=!1;try{n[f]=void 0,s=!0}catch{}const e=Object.prototype.toString.call(n);return s&&(t?n[f]=i:delete n[f]),e}function x(n){if(!M(n))return!1;const t=P(n);return t==="[object Function]"||t==="[object AsyncFunction]"||t==="[object GeneratorFunction]"||t==="[object Proxy]"}function M(n){const t=typeof n;return n!==null&&(t==="object"||t==="function")}function z(n){return n==null?!1:typeof n=="string"||n.constructor!==null&&n.constructor===String}function w(n){return Object.prototype.toString.call(n)==="[object Number]"&&!isNaN(n)}function I(n){return Array.isArray(n)}function g(n,...t){return Object.assign(n,...t)}function L(n,t){console.warn(`${t||"wind-layer"}: ${n}`)}const y={};function E(n,t){y[t]||(L(t,n),y[t]=!0)}function W(n,t){return n-t*Math.floor(n/t)}function p(n){return n!=null&&!isNaN(n)}function D(n,t={}){let i,s;if(n.forEach(function(r){switch(r.header.parameterCategory+","+r.header.parameterNumber){case"1,2":case"2,2":i=r;break;case"1,3":case"2,3":s=r;break}}),!s||!i)return;const e=i.header;return new S({xmin:e.lo1,ymin:e.la1,xmax:e.lo2,ymax:e.la2,deltaX:e.dx,deltaY:e.dy,cols:e.nx,rows:e.ny,us:i.data,vs:s.data,...t})}class v{constructor(t,i){this.u=t,this.v=i,this.m=this.magnitude()}magnitude(){return Math.sqrt(this.u**2+this.v**2)}directionTo(){let i=Math.atan2(this.u,this.v)*(180/Math.PI);return i<0&&(i+=360),i}directionFrom(){return(this.directionTo()+180)%360}}class S{constructor(t){this.grid=[],this.xmin=t.xmin,this.xmax=t.xmax,this.ymin=t.ymin,this.ymax=t.ymax,this.cols=t.cols,this.rows=t.rows,this.us=t.us,this.vs=t.vs,this.deltaX=t.deltaX,this.deltaY=t.deltaY,this.flipY=Boolean(t.flipY),this.ymin=Math.min(t.ymax,t.ymin),this.ymax=Math.max(t.ymax,t.ymin),this.deltaY<0&&this.ymin<this.ymax||(t.flipY===void 0&&(this.flipY=!0),console.warn("[wind-core]: The data is flipY")),this.isFields=!0;const i=Math.ceil((this.xmax-this.xmin)/t.deltaX),s=Math.ceil((this.ymax-this.ymin)/t.deltaY);(i!==this.cols||s!==this.rows)&&console.warn("[wind-core]: The data grid not equal"),this.isContinuous=Math.floor(this.cols*t.deltaX)>=360,this.translateX="translateX"in t?t.translateX:this.xmax>180,"wrappedX"in t&&E("[wind-core]: ","`wrappedX` namespace will deprecated please use `translateX` instead\uFF01"),this.wrapX=Boolean(t.wrapX),this.grid=this.buildGrid(),this.range=this.calculateRange()}buildGrid(){let t=[],i=0;const{rows:s,cols:e,us:o,vs:r}=this;for(let a=0;a<s;a++){const h=[];for(let l=0;l<e;l++,i++){let d=o[i],c=r[i],u=this.isValid(d)&&this.isValid(c);h[l]=u?new v(d,c):null}this.isContinuous&&h.push(h[0]),t[a]=h}return t}release(){this.grid=[]}extent(){return[this.xmin,this.ymin,this.xmax,this.ymax]}bilinearInterpolateVector(t,i,s,e,o,r){const a=1-t,h=1-i,l=a*h,d=t*h,c=a*i,u=t*i,m=s.u*l+e.u*d+o.u*c+r.u*u,j=s.v*l+e.v*d+o.v*c+r.v*u;return new v(m,j)}calculateRange(){if(!this.grid||!this.grid[0])return;const t=this.grid.length,i=this.grid[0].length;let s,e;for(let o=0;o<t;o++)for(let r=0;r<i;r++){const a=this.grid[o][r];if(a!==null){const h=a.m||a.magnitude();s===void 0?s=h:e===void 0?(e=h,s=Math.min(s,e),e=Math.max(s,e)):(s=Math.min(h,s),e=Math.max(h,e))}}return[s,e]}isValid(t){return t!=null}getWrappedLongitudes(){let t=this.xmin,i=this.xmax;return this.translateX&&(this.isContinuous?(t=-180,i=180):(i=this.xmax-360,t=this.xmin-360)),[t,i]}contains(t,i){const[s,e]=this.getWrappedLongitudes();let o=t>=s&&t<=e,r;return this.deltaY>=0?r=i>=this.ymin&&i<=this.ymax:r=i>=this.ymax&&i<=this.ymin,o&&r}getDecimalIndexes(t,i){const s=W(t-this.xmin,360)/this.deltaX;if(this.flipY){const e=(this.ymax-i)/this.deltaY;return[s,e]}else{const e=(this.ymin+i)/this.deltaY;return[s,e]}}valueAt(t,i){let s=!1;if((this.wrapX||this.contains(t,i))&&(s=!0),!s)return null;const e=this.getDecimalIndexes(t,i);let o=Math.floor(e[0]),r=Math.floor(e[1]);const a=this.clampColumnIndex(o),h=this.clampRowIndex(r);return this.valueAtIndexes(a,h)}interpolatedValueAt(t,i){let s=!1;if((this.wrapX||this.contains(t,i))&&(s=!0),!s)return null;let[e,o]=this.getDecimalIndexes(t,i);return this.interpolatePoint(e,o)}hasValueAt(t,i){return this.valueAt(t,i)!==null}interpolatePoint(t,i){const s=this.getFourSurroundingIndexes(t,i),[e,o,r,a]=s;let h=this.getFourSurroundingValues(e,o,r,a);if(h){const[l,d,c,u]=h;return this.bilinearInterpolateVector(t-e,i-r,l,d,c,u)}return null}clampColumnIndex(t){let i=t;t<0&&(i=0);let s=this.cols-1;return t>s&&(i=s),i}clampRowIndex(t){let i=t;t<0&&(i=0);let s=this.rows-1;return t>s&&(i=s),i}getFourSurroundingIndexes(t,i){let s=Math.floor(t),e=s+1;this.isContinuous&&e>=this.cols&&(e=0),e=this.clampColumnIndex(e);let o=this.clampRowIndex(Math.floor(i)),r=this.clampRowIndex(o+1);return[s,e,o,r]}getFourSurroundingValues(t,i,s,e){let o;if(o=this.grid[s]){const r=o[t],a=o[i];if(this.isValid(r)&&this.isValid(a)&&(o=this.grid[e])){const h=o[t],l=o[i];if(this.isValid(h)&&this.isValid(l))return[r,a,h,l]}}return null}valueAtIndexes(t,i){return this.grid[i][t]}lonLatAtIndexes(t,i){let s=this.longitudeAtX(t),e=this.latitudeAtY(i);return[s,e]}longitudeAtX(t){let i=this.deltaX/2,s=this.xmin+i+t*this.deltaX;return this.translateX&&(s=s>180?s-360:s),s}latitudeAtY(t){let i=this.deltaY/2;return this.ymax-i-t*this.deltaY}randomize(t={},i,s,e){let o=Math.random()*(i||this.cols)|0,r=Math.random()*(s||this.rows)|0;const a=e([o,r]);return a!==null?(t.x=a[0],t.y=a[1]):(t.x=this.longitudeAtX(o),t.y=this.latitudeAtY(r)),t}checkFields(){return this.isFields}}const A={globalAlpha:.9,lineWidth:1,colorScale:"#fff",velocityScale:1/25,maxAge:90,paths:800,frameRate:20,useCoordsDraw:!0,gpet:!0};function b(n,t,i,s){return Math.max(0,Math.min(s.length-1,Math.round((n-t)/(i-t)*(s.length-1))))}class O{constructor(t,i,s){if(this.particles=[],this.generated=!1,this.ctx=t,!this.ctx)throw new Error("ctx error");this.animate=this.animate.bind(this),this.setOptions(i),s&&this.updateData(s)}setOptions(t){this.options={...A,...t};const{width:i,height:s}=this.ctx.canvas;"particleAge"in t&&!("maxAge"in t)&&w(this.options.particleAge)&&(this.options.maxAge=this.options.particleAge),"particleMultiplier"in t&&!("paths"in t)&&w(this.options.particleMultiplier)&&(this.options.paths=Math.round(i*s*this.options.particleMultiplier)),this.prerender()}getOptions(){return this.options}updateData(t){this.field=t,this.generated&&(this.particles=this.prepareParticlePaths())}project(...t){throw new Error("project must be overriden")}unproject(...t){throw new Error("unproject must be overriden")}intersectsCoordinate(t){throw new Error("must be overriden")}clearCanvas(){this.stop(),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.forceStop=!1}start(){this.starting=!0,this.forceStop=!1,this.then=Date.now(),this.animate()}stop(){cancelAnimationFrame(this.animationLoop),this.starting=!1,this.forceStop=!0}animate(){this.animationLoop&&cancelAnimationFrame(this.animationLoop),this.animationLoop=requestAnimationFrame(this.animate);const t=Date.now(),i=t-this.then;i>this.options.frameRate&&(this.then=t-i%this.options.frameRate,this.render())}prerender(){this.generated=!1,this.field&&(this.particles=this.prepareParticlePaths(),this.generated=!0,!this.starting&&!this.forceStop&&(this.starting=!0,this.then=Date.now(),this.animate()))}render(){this.moveParticles(),this.drawParticles(),this.postrender()}postrender(){}moveParticles(){const{width:t,height:i}=this.ctx.canvas,s=this.particles,e=this.options.maxAge,o=x(this.options.velocityScale)?this.options.velocityScale():this.options.velocityScale;let r=0;const a=s.length;for(;r<a;r++){const h=s[r];h.age>e&&(h.age=0,this.field.randomize(h,t,i,this.unproject));const l=h.x,d=h.y,c=this.field.interpolatedValueAt(l,d);if(c===null)h.age=e;else{const u=l+c.u*o,m=d+c.v*o;this.field.hasValueAt(u,m)?(h.xt=u,h.yt=m,h.m=c.m):(h.x=u,h.y=m,h.age=e)}h.age++}}fadeIn(){const t=this.ctx.globalCompositeOperation;this.ctx.globalCompositeOperation="destination-in",this.ctx.fillRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.globalCompositeOperation=t}drawParticles(){const t=this.particles;this.fadeIn(),this.ctx.globalAlpha=this.options.globalAlpha,this.ctx.fillStyle=`rgba(0, 0, 0, ${this.options.globalAlpha})`,this.ctx.lineWidth=w(this.options.lineWidth)?this.options.lineWidth:1,this.ctx.strokeStyle=z(this.options.colorScale)?this.options.colorScale:"#fff";let i=0;const s=t.length;if(this.field&&s>0){let e,o;for(p(this.options.minVelocity)&&p(this.options.maxVelocity)?(e=this.options.minVelocity,o=this.options.maxVelocity):[e,o]=this.field.range;i<s;i++)this[this.options.useCoordsDraw?"drawCoordsParticle":"drawPixelParticle"](t[i],e,o)}}drawPixelParticle(t,i,s){const e=[t.x,t.y],o=[t.xt,t.yt];if(o&&e&&p(o[0])&&p(o[1])&&p(e[0])&&p(e[1])&&t.age<=this.options.maxAge){if(this.ctx.beginPath(),this.ctx.moveTo(e[0],e[1]),this.ctx.lineTo(o[0],o[1]),x(this.options.colorScale))this.ctx.strokeStyle=this.options.colorScale(t.m);else if(Array.isArray(this.options.colorScale)){const r=b(t.m,i,s,this.options.colorScale);this.ctx.strokeStyle=this.options.colorScale[r]}x(this.options.lineWidth)&&(this.ctx.lineWidth=this.options.lineWidth(t.m)),t.x=t.xt,t.y=t.yt,this.ctx.stroke()}}drawCoordsParticle(t,i,s){const e=[t.x,t.y],o=[t.xt,t.yt];if(o&&e&&p(o[0])&&p(o[1])&&p(e[0])&&p(e[1])&&this.intersectsCoordinate(o)&&t.age<=this.options.maxAge){const r=this.project(e),a=this.project(o);if(r&&a){if(this.ctx.beginPath(),this.ctx.moveTo(r[0],r[1]),this.ctx.lineTo(a[0],a[1]),t.x=t.xt,t.y=t.yt,x(this.options.colorScale))this.ctx.strokeStyle=this.options.colorScale(t.m);else if(Array.isArray(this.options.colorScale)){const h=b(t.m,i,s,this.options.colorScale);this.ctx.strokeStyle=this.options.colorScale[h]}x(this.options.lineWidth)&&(this.ctx.lineWidth=this.options.lineWidth(t.m)),this.ctx.stroke()}}}prepareParticlePaths(){const{width:t,height:i}=this.ctx.canvas,s=typeof this.options.paths=="function"?this.options.paths(this):this.options.paths,e=[];if(!this.field)return[];let o=0;for(;o<s;o++)e.push(this.field.randomize({age:this.randomize()},t,i,this.unproject));return e}randomize(){return Math.floor(Math.random()*this.options.maxAge)}}O.Field=S;if(!window.T)throw new Error("\u6CA1\u6709\u5F15\u5165\u5929\u5730\u56FEjs");const F=window.T.Overlay.extend({initialize:function(n,t={}){const i=g({},t);this.map=null,this.options=i,this.paneName="overlayPane",this.context="2d",this.zIndex=this.options.zIndex,this.mixBlendMode="normal",this.field=null,this.isDisableAutoConfig=i.isDisableAutoConfig,this.customStyle=i.customStyle,this.canvas=null,this.wind=null,this.adjustSize=()=>{const s=this.map.getSize(),e=this.canvas,o=window.devicePixelRatio||1;e!==null&&(e.width=s.x*o,e.height=s.y*o,this.context==="2d"&&e.getContext(this.context).scale(o,o),e.style.width=s.x+"px",e.style.height=s.y+"px")},this._draw=()=>{const s=this.map,e=s.getSize(),o=s.getCenter();if(o&&this.canvas){const r=s.lngLatToLayerPoint(o);this.canvas.style.left=r.x-e.x/2+"px",this.canvas.style.top=r.y-e.y/2+"px",this._render(this.canvas)}},this.getContext=()=>{if(this.canvas!==null)return this.canvas.getContext(this.context)},this.pickWindOptions=()=>{Object.keys(A).forEach(s=>{s in this.options&&(this.options.windOptions===void 0&&(this.options.windOptions={}),this.options.windOptions[s]=this.options[s])})},this.startAndDraw=()=>{this.start(),this._draw()},this.updateParams=s=>(this.setWindOptions(s),this),this.start=this.start.bind(this),this.stop=this.stop.bind(this),this.handleResize=this.handleResize.bind(this),this.pickWindOptions(),n&&this.setData(n,t.fieldOptions)},onAdd:function(n){this.map=n;const t=this.canvas=document.createElement("canvas");return t.setAttribute("id","_TmapWind"),t.style.cssText=this.customStyle||`position:absolute; left:0; top:0; z-index: ${this.zIndex} ;user-select:none;`,t.style.mixBlendMode=this.mixBlendMode,this.adjustSize(),n.getPanes()[this.paneName].appendChild(t),this.bindEvent(),this._draw(),this.canvas},_render:function(n){if(!this.getData()||!this.map)return this;const t=this.isDisableAutoConfig?this.getWindOptions():this.getOptimizeWindOptions();if(n&&!this.wind){const i=this.getData(),s=this.getContext();s&&(this.wind=new O(s,t,i),this.wind.project=this.project.bind(this),this.wind.unproject=this.unproject.bind(this),this.wind.intersectsCoordinate=this.intersectsCoordinate.bind(this),this.wind.postrender=()=>{})}return this.wind&&(this.wind.setOptions(this.options.windOptions),this.wind.prerender(),this.wind.render()),this},setWindOptions:function(n){const t=this.options.windOptions||{};this.options=g(this.options,{windOptions:g(t,n||{})}),this.wind&&(this.wind.setOptions(this.options.windOptions),this.wind.prerender())},bindEvent:function(){this.map.addEventListener("resize",this.handleResize),this.map.addEventListener("movestart",this.stop),this.map.addEventListener("moveend",this.startAndDraw),this.map.addEventListener("zoomstart",this.stop),this.map.addEventListener("zoomend",this.start)},unbindEvent:function(){this.map.removeEventListener("resize",this.handleResize),this.map.removeEventListener("movestart",this.stop),this.map.removeEventListener("moveend",this.startAndDraw),this.map.removeEventListener("zoomstart",this.stop),this.map.removeEventListener("zoomend",this.start)},getOptimizeWindOptions:function(){const n={0:.05,1:.05,2:.05,3:.03333333333333333,4:.025,5:.02,6:.016666666666666666,7:.003,8:.002,9:.001,10:5e-4,11:3e-4,12:15e-5,13:1e-4,14:5e-5,15:25e-6,16:1e-5,17:5e-6,18:2e-6},t=["rgb(36,104, 180)","rgb(60,157, 194)","rgb(128,205,193 )","rgb(151,218,168 )","rgb(198,231,181)","rgb(238,247,217)","rgb(255,238,159)","rgb(252,217,125)","rgb(255,182,100)","rgb(252,150,75)","rgb(250,112,52)","rgb(245,64,32)","rgb(237,45,28)","rgb(220,24,32)","rgb(180,0,35)"],i=this.options.windOptions||{},s=this.map.getZoom(),e={velocityScale:n[s]||1/200,paths:s>=8?3e3:5e3,frameRate:20,colorScale:t,lineWidth:2};return this.options=g(this.options,{windOptions:g(i,e||{})}),this.options.windOptions||{}},getWindOptions:function(){return this.options.windOptions||{}},onRemove:function(){this.unbindEvent(),this.canvas.parentNode.removeChild(this.canvas)},start:function(){this.wind&&this.wind.start()},stop:function(){this.wind&&this.wind.stop()},getElement:function(){return this.canvas},handleResize:function(){this.adjustSize(),this._draw()},setData:function(n,t={}){var i;return n&&n.checkFields&&n.checkFields()?this.field=n:I(n)?this.field=D(n,t):console.error("Illegal data"),this.map&&this.canvas&&this.field&&((i=this==null?void 0:this.wind)==null||i.updateData(this.field),this._render(this.canvas)),this},getData:function(){return this.field},project:function(n){const t=this.map.lngLatToContainerPoint(new window.T.LngLat(n[0],n[1]));return[t.x,t.y]},unproject:function(n){const t=this.map.containerPointToLngLat(new window.T.Point(n[0],n[1]));return[t.lng,t.lat]},intersectsCoordinate:function(n){return this.map.getBounds().contains(new window.T.LngLat(n[0],n[1]))},update:function(){this._draw()}}),X=F;export{X as WindLayer,F as default};
