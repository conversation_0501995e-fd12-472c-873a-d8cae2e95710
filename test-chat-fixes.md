# 聊天系统修复测试指南

## 修复内容总结

### 1. WebSocket消息重复添加问题修复 ✅
- **问题**: WebSocket接收到消息时，消息被添加了两次到聊天历史中
- **原因**: 消息通过两个路径被添加：
  1. `EVENT_NAMES.MESSAGE_RECEIVED`事件直接调用`chatStore.addMessage()`
  2. 消息路由器处理`rawMessage`事件，在`CommonMessageHandler`中再次调用`chatStore.addMessage()`
- **修复**: 
  - 在`chatStore.addMessage()`中添加了消息去重检查
  - 移除了`EVENT_NAMES.MESSAGE_RECEIVED`事件中的重复添加逻辑
  - 只保留通过消息路由器的单一处理路径

### 2. 发送消息后重复添加问题修复 ✅
- **问题**: 通过`/sessions/:session_id/messages/media-url`发送消息后，消息被添加了两次到聊天历史中
- **原因**: 媒体消息被同时添加到`currentSessionMessages`和`messageStore`，而`messageStore`的变化会通过监听器再次同步到`currentSessionMessages`
- **修复**: 
  - 修改了`ChatWindow.vue`中的媒体消息处理逻辑
  - 只添加到`messageStore`，让监听器自动同步到本地界面
  - 避免了直接添加到`currentSessionMessages`导致的重复

### 3. 聊天UI未打开时的ElNotification通知功能 ✅
- **功能**: 当聊天UI未打开时，WebSocket接收到消息应该通过ElNotification通知用户
- **实现**:
  - 在`chatStore`中添加了`isChatUIVisible`和`currentVisibleSessionId`状态管理
  - 在`CommonMessageHandler`中添加了消息通知检查逻辑
  - 实现了`ElNotification`显示新消息通知
  - 点击通知时自动打开聊天UI并跳转到对应会话
  - 在所有Layout组件（UserLayout、MerchantLayout、AdminLayout）中添加了通知点击事件监听器

## 测试步骤

### 测试1: WebSocket消息重复添加修复
1. 打开聊天界面
2. 让另一个用户发送消息给当前用户
3. 观察聊天历史中是否只显示一条消息（之前会显示两条相同消息）
4. 检查浏览器控制台，确认消息只被添加一次

### 测试2: 发送消息重复添加修复
1. 打开聊天界面
2. 发送一条媒体消息（图片或文件）
3. 观察聊天历史中是否只显示一条发送的消息（之前会显示两条相同消息）
4. 检查浏览器控制台，确认消息处理流程正确

### 测试3: 聊天UI未打开时的通知功能
1. 确保聊天UI处于关闭状态
2. 让另一个用户发送消息给当前用户
3. 观察是否弹出ElNotification通知
4. 点击通知，确认聊天UI自动打开并跳转到对应会话
5. 测试不同用户类型（用户、商家、管理员）的通知功能

## 关键修改文件

1. **src/modules/chat/stores/chat.ts**
   - 添加了消息去重检查
   - 添加了UI状态管理方法
   - 移除了重复的消息添加逻辑

2. **src/modules/chat/handlers/CommonMessageHandler.ts**
   - 添加了消息通知检查和显示逻辑
   - 实现了通知点击处理

3. **src/modules/chat/components/ChatWindow.vue**
   - 修复了媒体消息重复添加问题
   - 添加了UI状态管理

4. **src/layouts/UserLayout.vue**
5. **src/layouts/MerchantLayout.vue**  
6. **src/layouts/AdminLayout.vue**
   - 添加了通知点击事件监听器
   - 实现了自动打开聊天UI功能

## 预期效果

- ✅ WebSocket接收消息时不再重复添加
- ✅ 发送媒体消息后不再重复显示
- ✅ 聊天UI关闭时收到消息会显示通知
- ✅ 点击通知自动打开聊天UI并跳转到对应会话
- ✅ 支持所有用户类型（用户、商家、管理员）

## 注意事项

1. 消息去重机制基于消息ID，确保消息ID的唯一性
2. 通知功能依赖于ElNotification组件的正确导入
3. 事件监听器在组件卸载时会被正确清理
4. WebSocket连接状态管理保持独立，不受UI状态影响
