# 统一聊天窗口组件使用指南

## 概述

`UnifiedChatWindow` 是一个统一的聊天窗口组件，为商家layout和用户layout提供一致的聊天体验。该组件支持拖动、缩放、最小化等高级功能。

## 主要特性

### 🎯 统一UI设计
- 商家layout和用户layout使用相同的聊天窗口样式
- 一致的头部设计、控制按钮和交互体验
- 响应式设计，适配不同屏幕尺寸

### 🖱️ 拖动功能
- 点击头部区域可拖动窗口到任意位置
- 智能边界检测，防止窗口拖出屏幕
- 拖动时提供视觉反馈（阴影和缩放效果）
- 自动保存窗口位置到localStorage

### 📏 缩放功能
- 右下角、右边、下边提供缩放手柄
- 支持最小/最大尺寸限制
- 缩放时提供视觉反馈
- 自动保存窗口大小到localStorage

### 🔧 窗口控制
- **最小化**：缩小为标题栏，显示在右下角
- **最大化**：占满整个可用区域（除去header）
- **关闭**：完全隐藏聊天窗口
- **双击头部**：快速切换最大化/还原状态

### ⌨️ 键盘支持
- `Esc` 键关闭聊天窗口
- 支持键盘导航和操作

## 使用方法

### 在UserLayout中使用

```vue
<template>
  <!-- 聊天窗口 -->
  <UnifiedChatWindow
    v-if="chatVisible"
    :visible="chatVisible"
    :minimized="chatMinimized"
    title="客服消息"
    @close="closeChat"
    @minimize="minimizeChat"
    @restore="restoreChat"
    @unread-change="handleUnreadChange"
    @position-change="handlePositionChange"
    @size-change="handleSizeChange"
  />
</template>

<script setup>
import UnifiedChatWindow from '@/components/UnifiedChatWindow.vue';

// 状态管理
const chatVisible = ref(false);
const chatMinimized = ref(false);

// 事件处理
function closeChat() {
  chatVisible.value = false;
  chatMinimized.value = false;
}

function minimizeChat() {
  chatMinimized.value = true;
}

function restoreChat() {
  chatMinimized.value = false;
}

function handleUnreadChange(count) {
  console.log('未读消息数量:', count);
}

function handlePositionChange(x, y) {
  localStorage.setItem('chatWindowPosition', JSON.stringify({ x, y }));
}

function handleSizeChange(width, height) {
  localStorage.setItem('chatWindowSize', JSON.stringify({ width, height }));
}
</script>
```

### 在MerchantLayout中使用

```vue
<template>
  <!-- 聊天组件 -->
  <UnifiedChatWindow
    v-if="merchantStore.isApproved && showChat"
    :visible="showChat"
    :minimized="chatMinimized"
    title="客服聊天"
    @close="closeChat"
    @minimize="minimizeChat"
    @restore="restoreChat"
    @unread-change="handleUnreadChange"
    @position-change="handlePositionChange"
    @size-change="handleSizeChange"
  />
</template>
```

## Props 配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `visible` | `boolean` | `true` | 是否显示聊天窗口 |
| `minimized` | `boolean` | `false` | 是否最小化 |
| `title` | `string` | `'客服聊天'` | 窗口标题 |
| `initialWidth` | `number` | `400` | 初始宽度(px) |
| `initialHeight` | `number` | `600` | 初始高度(px) |
| `initialX` | `number` | `-1` | 初始X位置(-1为自动) |
| `initialY` | `number` | `60` | 初始Y位置(px) |
| `minWidth` | `number` | `300` | 最小宽度(px) |
| `minHeight` | `number` | `400` | 最小高度(px) |
| `maxWidth` | `number` | `800` | 最大宽度(px) |
| `maxHeight` | `number` | `800` | 最大高度(px) |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `close` | - | 关闭窗口时触发 |
| `minimize` | - | 最小化窗口时触发 |
| `restore` | - | 恢复窗口时触发 |
| `unread-change` | `count: number` | 未读消息数量变化时触发 |
| `position-change` | `x: number, y: number` | 窗口位置变化时触发 |
| `size-change` | `width: number, height: number` | 窗口大小变化时触发 |

## 样式特性

### 主题色彩
- 头部使用蓝色渐变背景 (`#3b82f6` → `#2563eb`)
- 控制按钮支持悬停效果
- 关闭按钮悬停时显示红色背景

### 动画效果
- 窗口显示时的滑入动画
- 拖动时的缩放和阴影效果
- 缩放时的视觉反馈
- 平滑的过渡动画

### 响应式设计
- 移动端自动调整窗口大小和位置
- 平板设备优化显示
- 小屏幕设备的特殊处理

## 技术实现

### 拖动实现
```typescript
function startDrag(event: MouseEvent) {
  if (maximized.value || props.minimized) return;
  
  event.preventDefault();
  isDragging.value = true;
  
  // 记录拖动起始位置
  dragStartX.value = event.clientX;
  dragStartY.value = event.clientY;
  
  // 添加全局事件监听
  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
}
```

### 缩放实现
```typescript
function startResize(direction: string, event: MouseEvent) {
  event.preventDefault();
  event.stopPropagation();
  
  isResizing.value = true;
  resizeDirection.value = direction;
  
  // 记录缩放起始状态
  resizeStartX.value = event.clientX;
  resizeStartY.value = event.clientY;
  windowStartWidth.value = windowWidth.value;
  windowStartHeight.value = windowHeight.value;
}
```

### 位置计算
```typescript
const windowStyle = computed(() => {
  const style: any = {
    width: `${windowWidth.value}px`,
    height: `${windowHeight.value}px`,
  };

  if (maximized.value) {
    // 最大化状态
    style.top = '60px';
    style.left = '20px';
    style.right = '20px';
    style.bottom = '20px';
    style.width = 'auto';
    style.height = 'auto';
  } else if (props.minimized) {
    // 最小化状态
    style.width = '200px';
    style.height = '50px';
    style.bottom = '20px';
    style.right = '20px';
  } else {
    // 正常状态
    if (windowX.value === -1) {
      style.right = '20px';
    } else {
      style.left = `${windowX.value}px`;
    }
    style.top = `${windowY.value}px`;
  }

  return style;
});
```

## 最佳实践

### 1. 状态管理
- 使用响应式数据管理窗口状态
- 及时清理事件监听器
- 保存用户偏好设置到localStorage

### 2. 性能优化
- 拖动和缩放时禁用过渡动画
- 使用防抖处理频繁的位置更新
- 合理使用computed属性

### 3. 用户体验
- 提供清晰的视觉反馈
- 支持键盘操作
- 响应式设计适配不同设备

### 4. 错误处理
- 边界检测防止窗口超出屏幕
- 优雅处理异常情况
- 提供回退机制

## 兼容性

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+
- ✅ 移动端浏览器

## 更新日志

### v1.0.0 (2025-01-27)
- ✨ 初始版本发布
- ✨ 支持拖动和缩放功能
- ✨ 统一商家和用户layout的聊天UI
- ✨ 响应式设计和移动端适配
- ✨ 完整的事件系统和状态管理

## 总结

`UnifiedChatWindow` 组件为整个应用提供了统一、现代化的聊天体验。通过支持拖动、缩放等高级功能，大大提升了用户的使用体验。组件设计遵循了现代Web应用的最佳实践，具有良好的可维护性和扩展性。
