<!-- 
  表单配置编辑器组件
  用于编辑表单配置，包括标签宽度、标签位置、表单项配置等
-->
<template>
  <div class="form-config-editor">
    <el-form :model="localConfig" label-width="120px">
      <el-form-item label="标签宽度">
        <el-input-number v-model="localConfig.labelWidth" :min="0" @change="handleConfigChange" />
      </el-form-item>
      <el-form-item label="标签位置">
        <el-select v-model="localConfig.labelPosition" @change="handleConfigChange">
          <el-option label="左侧" value="left" />
          <el-option label="顶部" value="top" />
          <el-option label="右侧" value="right" />
        </el-select>
      </el-form-item>
      <el-form-item label="表单尺寸">
        <el-select v-model="localConfig.size" @change="handleConfigChange">
          <el-option label="默认" value="default" />
          <el-option label="小" value="small" />
          <el-option label="大" value="large" />
        </el-select>
      </el-form-item>
      <el-form-item label="禁用表单">
        <el-switch v-model="localConfig.disabled" @change="handleConfigChange" />
      </el-form-item>
      <el-form-item label="栅格布局">
        <el-switch 
          :model-value="localConfig.useGrid" 
          @update:model-value="(val: boolean) => updateUseGrid(val)"
          @change="handleConfigChange" 
        />
      </el-form-item>
      
      <el-form-item label="校验规则">
        <el-button type="primary" @click="addFormRule">添加规则</el-button>
        <div v-if="Object.keys(localConfig.rules || {}).length > 0" class="rules-container">
          <div class="rules-grid-container">
            <div v-for="(rules, prop, index) in localConfig.rules" :key="prop" class="rule-item">
              <el-divider>{{ prop }}</el-divider>
              
              <el-form-item label="字段名">
                <el-input 
                  :model-value="prop" 
                  @update:model-value="(val: string) => updateRuleKey(prop, val)"
                  placeholder="输入字段名" 
                />
              </el-form-item>
              
              <div class="rule-items-grid">
                <div v-for="(rule, ruleIndex) in rules" :key="ruleIndex" class="rule-item-detail">
                  <el-divider>规则 {{ ruleIndex + 1 }}</el-divider>
                  
                  <!-- 必填校验（主要校验项） -->
                  <el-form-item label="是否必填" v-if="ruleIndex === 0">
                    <el-switch v-model="rule.required" @change="handleConfigChange" />
                  </el-form-item>
                  
                  <!-- 自定义校验器（附加校验项） -->
                  <el-form-item label="附加校验器" v-if="ruleIndex > 0">
                    <el-select 
                      :model-value="getValidatorType(rule)"
                      placeholder="选择校验器"
                      @change="(val: string) => handleValidatorChange(rule, val, ruleIndex, index)"
                      clearable
                    >
                      <el-option label="邮箱" value="checkEmail" />
                      <el-option label="手机号" value="checkPhone" />
                      <el-option label="URL地址" value="checkUrl" />
                      <el-option label="数字" value="checkNumber" />
                      <el-option label="整数" value="checkInteger" />
                      <el-option label="浮点数" value="checkFloat" />
                      <el-option label="身份证" value="checkIdCard" />
                      <el-option label="密码" value="checkPassword" />
                      <el-option label="用户名" value="checkUsername" />
                      <el-option label="IP地址" value="checkIpAddress" />
                      <el-option label="邮政编码" value="checkZipCode" />
                      <el-option label="大于阈值" value="checkGreaterThan" />
                    </el-select>
                  </el-form-item>
                  
                  <!-- 为checkGreaterThan添加阈值输入 -->
                  <el-form-item 
                    v-if="getValidatorType(rule) === 'checkGreaterThan'" 
                    label="阈值"
                  >
                    <el-input-number 
                      v-model="rule.threshold" 
                      :min="0" 
                      :precision="2"
                      @change="handleConfigChange" 
                    />
                  </el-form-item>
                  
                  <el-form-item label="错误消息">
                    <el-input v-model="rule.message" @input="handleConfigChange" />
                  </el-form-item>
                  
                  <el-form-item label="触发方式">
                    <el-select v-model="rule.trigger" @change="handleConfigChange">
                      <el-option label="变化时" value="change" />
                      <el-option label="失去焦点时" value="blur" />
                    </el-select>
                  </el-form-item>
                  
                  <el-button type="danger" @click="removeFormRule(prop, ruleIndex)">删除规则</el-button>
                </div>
              </div>
              
              <el-button type="primary" size="small" @click="addRuleItem(prop)">添加规则项</el-button>
            </div>
          </div>
        </div>
      </el-form-item>
      
      <el-form-item label="表单项">
        <div class="action-row">
          <el-button type="primary" @click="addFormItem">添加表单项</el-button>
          <!-- 添加关联排序开关 -->
          <LinkedSortingToggle />
        </div>
        <div class="draggable-container" style="width: 100%">
          <p v-if="localConfig.columns.length > 0" class="drag-tip">
            <el-icon><Rank /></el-icon> 拖拽排序
          </p>
          <draggable 
            v-model="localConfig.columns"
            :item-key="(item: FormItem) => item.prop || Math.random().toString()"
            handle=".drag-handle"
            ghost-class="ghost"
            @change="handleDragEnd"
            class="grid-container"
          >
            <template #item="{element, index}">
              <div class="form-item">
                <div class="drag-handle">
                  <el-icon><Rank /></el-icon>
                </div>
                <el-divider>表单项 {{ index + 1 }}</el-divider>
                <el-form-item label="标签">
                  <el-input v-model="element.label" @input="handleConfigChange" />
                </el-form-item>
                <el-form-item label="属性名">
                  <el-input v-model="element.prop" @input="handleConfigChange" />
                </el-form-item>
                <el-form-item label="类型">
                  <el-select v-model="element.valueType" @change="handleConfigChange">
                    <el-option label="文本输入" value="input" />
                    <el-option label="自动完成" value="autocomplete" />
                    <el-option label="级联选择" value="cascader" />
                    <el-option label="多选框" value="checkbox" />
                    <el-option label="颜色选择器" value="color-picker" />
                    <el-option label="日期选择器" value="date-picker" />
                    <el-option label="数字输入" value="input-number" />
                    <el-option label="单选框" value="radio" />
                    <el-option label="评分" value="rate" />
                    <el-option label="选择器" value="select" />
                    <el-option label="滑块" value="slider" />
                    <el-option label="开关" value="switch" />
                    <el-option label="时间选择器" value="time-picker" />
                    <el-option label="时间选择" value="time-select" />
                    <el-option label="文本域" value="textarea" />
                    <el-option label="纯文本" value="text" />
                    <el-option label="穿梭框" value="transfer" />
                    <el-option label="树形选择" value="tree-select" />
                    <el-option label="虚拟选择器" value="select-v2" />
                    <el-option label="Plus单选框" value="plus-radio" />
                    <el-option label="Plus日期选择器" value="plus-date-picker" />
                    <el-option label="Plus标签输入" value="plus-input-tag" />
                    <el-option label="上传" value="upload" />
                  </el-select>
                </el-form-item>
                <el-form-item label="占位提示">
                  <el-input v-model="element.placeholder" @input="handleConfigChange" />
                </el-form-item>
                <el-form-item label="默认值">
                  <el-input v-model="element.defaultValue" @input="handleConfigChange" />
                </el-form-item>
                <el-form-item label="禁用">
                  <el-switch v-model="element.disabled" @change="handleConfigChange" />
                </el-form-item>
                
                <!-- 栅格布局 -->
                <template v-if="localConfig.useGrid">
                  <el-divider>栅格布局</el-divider>
                  
                  <el-form-item label="栅格宽度">
                    <el-input-number 
                      :model-value="getColProp(element, 'span')" 
                      @update:model-value="(val: number) => updateColProps(element, 'span', val)"
                      :min="1" 
                      :max="24" 
                      @change="handleConfigChange" 
                    />
                  </el-form-item>
                  
                  <el-form-item label="左侧偏移">
                    <el-input-number 
                      :model-value="getColProp(element, 'offset')" 
                      @update:model-value="(val: number) => updateColProps(element, 'offset', val)"
                      :min="0" 
                      :max="24" 
                      @change="handleConfigChange" 
                    />
                  </el-form-item>
                </template>
                
                <!-- 日期选择器配置 -->
                <template v-if="element.valueType === 'date-picker'">
                  <el-divider>日期时间配置</el-divider>
                  
                  <el-form-item label="日期类型">
                    <el-select 
                      :model-value="getFieldProp(element, 'type')" 
                      @update:model-value="(val: string) => updateFieldProp(element, 'type', val)"
                      placeholder="选择日期类型"
                    >
                      <el-option label="日期" value="date" />
                      <el-option label="日期时间" value="datetime" />
                      <el-option label="年份" value="year" />
                      <el-option label="月份" value="month" />
                      <el-option label="周" value="week" />
                      <el-option label="日期范围" value="daterange" />
                      <el-option label="日期时间范围" value="datetimerange" />
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item label="显示格式">
                    <el-input 
                      :model-value="getFieldProp(element, 'format')" 
                      @update:model-value="(val: string) => updateFieldProp(element, 'format', val)"
                      placeholder="如: YYYY-MM-DD"
                    />
                  </el-form-item>
                  
                  <el-form-item label="值格式">
                    <el-input 
                      :model-value="getFieldProp(element, 'valueFormat')" 
                      @update:model-value="(val: string) => updateFieldProp(element, 'valueFormat', val)"
                      placeholder="如: YYYY-MM-DD"
                    />
                  </el-form-item>
                </template>
                
                <!-- 上传类型的专有配置 -->
                <template v-if="element.valueType === 'upload'">
                  <el-divider>上传配置</el-divider>
                  
                  <el-form-item label="上传样式">
                    <el-select 
                      :model-value="getFieldProp(element, 'listType')" 
                      @update:model-value="(val: string) => updateFieldProp(element, 'listType', val)"
                    >
                      <el-option label="文本列表" value="text" />
                      <el-option label="图片列表" value="picture" />
                      <el-option label="图片卡片" value="picture-card" />
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item label="上传URL">
                    <el-input 
                      :model-value="getFieldProp(element, 'action')" 
                      @update:model-value="(val: string) => updateFieldProp(element, 'action', val)"
                      placeholder="上传接口URL"
                    />
                  </el-form-item>
                  
                  <el-form-item label="多文件上传">
                    <el-switch 
                      :model-value="getFieldProp(element, 'multiple')" 
                      @update:model-value="(val: boolean) => updateFieldProp(element, 'multiple', val)"
                    />
                  </el-form-item>
                  
                  <el-form-item label="限制大小(MB)">
                    <el-input-number 
                      :model-value="getFieldProp(element, 'limitSize')" 
                      @update:model-value="(val: number) => updateFieldProp(element, 'limitSize', val)"
                      :min="0"
                    />
                  </el-form-item>
                </template>
                
                <!-- 选择器和单选框配置 -->
                <template v-if="element.valueType === 'select' || element.valueType === 'radio' || element.valueType === 'checkbox'">
                  <el-divider>选项配置</el-divider>
                  
                  <div class="options-section">
                    <div class="options-header">
                      <el-button size="small" type="primary" @click="addOptionToItem(element)">添加选项</el-button>
                      <el-button 
                        link 
                        size="small" 
                        @click="toggleOptionsExpanded(element.prop)"
                        class="toggle-options-btn"
                      >
                        {{ getOptionsExpanded(element.prop) ? '收起选项' : '展开选项' }}
                        <el-icon>
                          <component :is="getOptionsExpanded(element.prop) ? 'ArrowUp' : 'ArrowDown'" />
                        </el-icon>
                      </el-button>
                    </div>
                    
                    <div v-show="getOptionsExpanded(element.prop)" class="options-container">
                      <div class="options-grid">
                        <div v-for="(option, optIndex) in element.options || []" :key="optIndex" class="option-item">
                          <el-divider>选项 {{ optIndex + 1 }}</el-divider>
                          
                          <el-form-item label="标签">
                            <el-input v-model="option.label" @input="handleConfigChange" />
                          </el-form-item>
                          
                          <el-form-item label="值">
                            <el-input v-model="option.value" @input="handleConfigChange" />
                          </el-form-item>
                          
                          <el-button type="danger" size="small" @click="removeOptionFromItem(element, optIndex)">
                            删除选项
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
                
                <!-- 表单项属性 -->
                <el-form-item label="表单项属性">
                  <el-input 
                    type="textarea"
                    :value="getFieldPropsString(element)"
                    @input="(val: string) => updateFieldPropsFromInput(element, val)"
                    placeholder="表单项props，如 {min: 0, max: 100}"
                    :rows="3"
                  />
                </el-form-item>
                
                <el-button type="danger" @click="removeFormItem(index)">删除</el-button>
              </div>
            </template>
          </draggable>
        </div>
      </el-form-item>
    </el-form>
    
    <!-- JSON可视化预览区 -->
    <div class="json-preview-section">
      <el-divider>
        <div class="divider-actions">
          <el-button link size="small" @click="sourceVisible = !sourceVisible">
            {{ sourceVisible ? '隐藏源码' : '显示源码' }}
            <el-icon>
              <component :is="sourceVisible ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
          </el-button>
          <el-button type="warning" @click="handleFormatSource" v-if="sourceVisible">格式化</el-button>
        </div>
      </el-divider>
      
      <!-- JSON预览 -->
      <el-row :gutter="20">
        <!-- 源码编辑区 -->
        <el-col :span="sourceVisible ? 12 : 0" v-if="sourceVisible">
          <el-input
            v-model="sourceCode"
            type="textarea"
            :rows="10"
            @input="handleSourceChange"
            placeholder="请输入表单配置JSON"
          />
        </el-col>
        <!-- JSON预览区 -->
        <el-col :span="sourceVisible ? 12 : 24">
          <div class="json-viewer-container">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>JSON可视化预览</span>
                </div>
              </template>
              <JsonViewer 
                :value="parsedSourceJson" 
                :expandDepth="3" 
                copyable 
                sort 
                boxed 
                theme="light"
                class="text-left"
              />
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 添加规则类型选择对话框 -->
    <el-dialog
      v-model="ruleTypeDialogVisible"
      title="选择校验规则类型"
      width="500px"
    >
      <el-form label-width="100px">
        <el-form-item label="字段名">
          <el-select v-model="newRuleFieldProp" placeholder="选择字段" style="width: 100%">
            <el-option
              v-for="item in localConfig.columns"
              :key="item.prop"
              :label="`${item.label} (${item.prop})`"
              :value="item.prop"
            />
            <el-option label="自定义字段" value="custom" />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="newRuleFieldProp === 'custom'" label="自定义字段名">
          <el-input v-model="customRuleFieldProp" placeholder="输入字段名" />
        </el-form-item>
        
        <!-- <el-form-item label="是否必填">
          <el-switch v-model="newRuleRequired" />
        </el-form-item> -->
        
        <el-form-item label="附加校验器">
          <el-select v-model="newRuleValidator" placeholder="选择校验器" style="width: 100%" clearable>
            <el-option label="邮箱" value="checkEmail" />
            <el-option label="手机号" value="checkPhone" />
            <el-option label="URL地址" value="checkUrl" />
            <el-option label="数字" value="checkNumber" />
            <el-option label="整数" value="checkInteger" />
            <el-option label="浮点数" value="checkFloat" />
            <el-option label="身份证" value="checkIdCard" />
            <el-option label="密码" value="checkPassword" />
            <el-option label="用户名" value="checkUsername" />
            <el-option label="IP地址" value="checkIpAddress" />
            <el-option label="邮政编码" value="checkZipCode" />
            <el-option label="大于阈值" value="checkGreaterThan" />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="newRuleValidator === 'checkGreaterThan'" label="阈值">
          <el-input-number v-model="newRuleThreshold" :min="0" :precision="2" style="width: 100%" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ruleTypeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddFormRule">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { JsonViewer } from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/index.css';
import draggable from 'vuedraggable';
import LinkedSortingToggle from './LinkedSortingToggle.vue';
import { useLinkedSortingStore } from '../../stores/linkedSortingStore';

interface FormItemOption {
  label: string;
  value: string | number;
}

interface FormItem {
  label: string;
  prop: string;
  valueType: string;
  required?: boolean;
  placeholder?: string;
  defaultValue?: any;
  disabled?: boolean;
  options?: FormItemOption[];
  fieldProps?: Record<string, any>;
  colProps?: {
    span?: number;
    offset?: number;
  };
  [key: string]: any;
}

interface FormRule {
  required?: boolean;
  validator?: string | Function;
  threshold?: number;
  message: string;
  trigger: string;
  [key: string]: any;
}

interface FormConfig {
  labelWidth: number;
  labelPosition: string;
  size: string;
  disabled: boolean;
  useGrid: boolean;
  columns: FormItem[];
  rules?: Record<string, FormRule[]>;
  [key: string]: any;
}

const props = defineProps({
  modelValue: {
    type: Object as () => FormConfig,
    default: () => ({
      labelWidth: 120,
      labelPosition: 'right',
      size: 'default',
      disabled: false,
      useGrid: false,
      columns: []
    })
  }
});

const emit = defineEmits(['update:modelValue']);

// 本地配置数据
const localConfig = ref<FormConfig>({} as FormConfig);

// 源码显示状态，默认不显示
const sourceVisible = ref(false);
const sourceCode = ref('{}');

// JSON解析结果用于可视化显示
const parsedSourceJson = computed(() => {
  try {
    return JSON.parse(sourceCode.value);
  } catch (error) {
    return { error: '无效的JSON格式' };
  }
});

// 跟踪选项展开/折叠状态，使用Map存储每个表单项的状态
const optionsExpandedMap = ref<Map<string, boolean>>(new Map());

// 添加规则类型选择对话框
const ruleTypeDialogVisible = ref(false);
const newRuleFieldProp = ref('');
const customRuleFieldProp = ref('');
const newRuleRequired = ref(false);
const newRuleValidator = ref('');
const newRuleThreshold = ref(0);

// 获取关联排序状态管理
const linkedSortingStore = useLinkedSortingStore();

// 初始化本地配置
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    // 直接使用JSON.stringify进行深拷贝，避免引用问题
    localConfig.value = JSON.parse(JSON.stringify(newValue));
    
    // 再次确认columns数组存在
    if (!localConfig.value.columns) {
      localConfig.value.columns = [];
    }
    // 确保rules对象存在
    if (!localConfig.value.rules) {
      localConfig.value.rules = {};
    }
    updateSourceCode();
  }
}, { immediate: true, deep: true });

/**
 * 监听关联排序状态变化
 */
watch(() => linkedSortingStore.enabled, (enabled) => {
  if (enabled && localConfig.value.columns?.length) {
    // 开启关联排序时，应用排序顺序
    const sortedColumns = linkedSortingStore.sortItemsByMap(localConfig.value.columns);
    
    // 检查排序前后是否有变化，避免不必要的更新
    if (JSON.stringify(sortedColumns) !== JSON.stringify(localConfig.value.columns)) {
      localConfig.value.columns = sortedColumns;
      // 仅当有实际变化时才触发事件
      handleConfigChange();
    }
  }
});

/**
 * 监听排序更新时间戳变化，同步更新本地排序
 */
watch(() => linkedSortingStore.updateTimestamp, (timestamp) => {
  if (timestamp && linkedSortingStore.enabled && localConfig.value.columns?.length) {
    console.log('表单配置检测到排序更新:', timestamp);
    // 应用最新的排序顺序
    const sortedColumns = linkedSortingStore.sortItemsByMap(localConfig.value.columns);
    
    // 如果排序后的顺序与当前不同，则更新
    if (JSON.stringify(sortedColumns) !== JSON.stringify(localConfig.value.columns)) {
      localConfig.value.columns = sortedColumns;
      
      // 通知父组件值已更新
      emit('update:modelValue', JSON.parse(JSON.stringify(localConfig.value)));
    }
  }
});

/**
 * 配置变更处理
 */
function handleConfigChange() {
  // 在触发更新前先将新值保存为字符串，方便后续比较
  const newConfig = JSON.parse(JSON.stringify(localConfig.value));
  emit('update:modelValue', newConfig);
  updateSourceCode();
}

/**
 * 源码变化处理
 */
function handleSourceChange() {
  try {
    const parsed = JSON.parse(sourceCode.value);
    localConfig.value = parsed;
    emit('update:modelValue', parsed);
  } catch (error) {
    console.error('JSON格式错误:', error);
  }
}

/**
 * 格式化源码
 */
function handleFormatSource() {
  try {
    const parsed = JSON.parse(sourceCode.value);
    sourceCode.value = JSON.stringify(parsed, null, 2);
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化');
    console.error('JSON格式错误:', error);
  }
}

/**
 * 添加表单项
 */
function addFormItem() {
  if (!localConfig.value.columns) {
    localConfig.value.columns = [];
  }
  
  const newItem: FormItem = {
    label: '新表单项',
    prop: 'newField',
    valueType: 'input',
    required: false,
    placeholder: '请输入',
    defaultValue: '',
    disabled: false
  };
  
  if (localConfig.value.useGrid) {
    newItem.colProps = { span: 12 };
  }
  
  localConfig.value.columns.push(newItem);
  handleConfigChange();
}

/**
 * 删除表单项
 */
function removeFormItem(index: number) {
  if (localConfig.value.columns && index >= 0 && index < localConfig.value.columns.length) {
    localConfig.value.columns.splice(index, 1);
    handleConfigChange();
  }
}

/**
 * 添加表单校验规则
 */
function addFormRule() {
  ruleTypeDialogVisible.value = true;
}

/**
 * 为特定字段添加校验规则
 */
// function addRuleForField(prop: string) {
//   if (!localConfig.value.rules) {
//     localConfig.value.rules = {};
//   }
  
//   if (!localConfig.value.rules[prop]) {
//     localConfig.value.rules[prop] = [];
//   }
  
//   localConfig.value.rules[prop].push({
//     required: true,
//     message: '请输入' + prop,
//     trigger: 'blur'
//   });
  
//   handleConfigChange();
// }

/**
 * 为特定规则添加规则项
 */
function addRuleItem(prop: string) {
  // 记录当前操作的字段名
  newRuleFieldProp.value = prop;
  newRuleRequired.value = false;
  newRuleValidator.value = '';
  newRuleThreshold.value = 0;
  
  // 显示规则类型选择对话框
  ruleTypeDialogVisible.value = true;
}

/**
 * 移除表单校验规则
 */
function removeFormRule(prop: string, index: number) {
  if (localConfig.value.rules && 
      prop in localConfig.value.rules && 
      index >= 0 && 
      index < localConfig.value.rules[prop].length) {
    
    localConfig.value.rules[prop].splice(index, 1);
    
    // 如果没有规则了，删除整个prop
    if (localConfig.value.rules[prop].length === 0) {
      delete localConfig.value.rules[prop];
    }
    
    handleConfigChange();
  }
}

/**
 * 更新规则键名
 */
function updateRuleKey(oldKey: string, newKey: string) {
  if (oldKey === newKey || !localConfig.value.rules) return;
  
  const rules = { ...localConfig.value.rules };
  
  // 如果新键已存在，不进行操作
  if (newKey in rules) {
    ElMessage.warning(`字段名 ${newKey} 已存在规则，无法重命名`);
    return;
  }
  
  if (oldKey in rules) {
    rules[newKey] = rules[oldKey];
    delete rules[oldKey];
    localConfig.value.rules = rules;
    handleConfigChange();
  }
}

/**
 * 更新是否使用栅格布局
 */
function updateUseGrid(value: boolean) {
  localConfig.value.useGrid = value;
  
  // 如果启用栅格布局，为每个表单项添加colProps
  if (value && localConfig.value.columns) {
    localConfig.value.columns.forEach(item => {
      if (!item.colProps) {
        item.colProps = { span: 12 };
      }
    });
  }
  
  handleConfigChange();
}

/**
 * 获取colProps中的属性
 */
function getColProp(element: any, prop: string): number | undefined {
  if (!element.colProps) return undefined;
  return element.colProps[prop];
}

/**
 * 更新表单项的colProps
 */
function updateColProps(element: any, key: string, value: number) {
  if (!element.colProps) {
    element.colProps = {};
  }
  element.colProps[key] = value;
  handleConfigChange();
}

/**
 * 获取字段属性
 */
function getFieldProp(element: any, prop: string): any {
  if (!element.fieldProps) return undefined;
  return element.fieldProps[prop];
}

/**
 * 更新字段属性
 */
function updateFieldProp(element: any, prop: string, value: any) {
  if (!element.fieldProps) {
    element.fieldProps = {};
  }
  element.fieldProps[prop] = value;
  handleConfigChange();
}

/**
 * 获取fieldProps字符串表示
 */
function getFieldPropsString(element: any): string {
  if (!element.fieldProps) return '';
  return JSON.stringify(element.fieldProps, null, 2);
}

/**
 * 更新fieldProps
 */
function updateFieldPropsFromInput(element: any, val: string) {
  try {
    element.fieldProps = JSON.parse(val);
    handleConfigChange();
  } catch (error) {
    console.error('JSON格式错误:', error);
  }
}

/**
 * 添加选项到元素
 */
function addOptionToItem(element: any) {
  if (!element.options) {
    element.options = [];
  }
  
  element.options.push({
    label: '选项' + (element.options.length + 1),
    value: element.options.length + 1
  });
  
  handleConfigChange();
}

/**
 * 从元素中删除选项
 */
function removeOptionFromItem(element: any, index: number) {
  if (element.options && index >= 0 && index < element.options.length) {
    element.options.splice(index, 1);
    handleConfigChange();
  }
}

/**
 * 获取选项展开状态
 */
function getOptionsExpanded(elementProp: string): boolean {
  return optionsExpandedMap.value.get(elementProp) !== false; // 默认展开
}

/**
 * 切换选项展开状态
 */
function toggleOptionsExpanded(elementProp: string): void {
  const currentState = getOptionsExpanded(elementProp);
  optionsExpandedMap.value.set(elementProp, !currentState);
}

/**
 * 监听关联排序状态变化
 */
watch(() => linkedSortingStore.enabled, (enabled) => {
  if (enabled && localConfig.value.columns?.length) {
    // 开启关联排序时，应用排序顺序
    const sortedColumns = linkedSortingStore.sortItemsByMap(localConfig.value.columns);
    
    // 检查排序前后是否有变化，避免不必要的更新
    if (JSON.stringify(sortedColumns) !== JSON.stringify(localConfig.value.columns)) {
      localConfig.value.columns = sortedColumns;
      // 仅当有实际变化时才触发事件
      handleConfigChange();
    }
  }
});

/**
 * 拖拽排序后的处理
 */
function handleDragEnd(event: any) {
  console.log('表单项拖拽事件:', event);
  
  // 如果关联排序开启，更新排序映射
  if (linkedSortingStore.enabled && localConfig.value.columns?.length) {
    // 通知其他编辑器排序变化
    linkedSortingStore.updateSortOrder(localConfig.value.columns);
  }
  
  // 进行常规配置更新
  emit('update:modelValue', JSON.parse(JSON.stringify(localConfig.value)));
}

/**
 * 获取校验器类型
 * 从规则中提取校验器类型，处理字符串和函数两种情况
 */
function getValidatorType(rule: FormRule): string | undefined {
  if (!rule.validator) return undefined;
  
  // 如果validator是字符串，直接返回
  if (typeof rule.validator === 'string') {
    return rule.validator;
  }
  
  // 如果validator是函数，则根据函数名获取
  if (typeof rule.validator === 'function') {
    try {
      // 直接获取函数的name属性
      const validatorFn = rule.validator as Function;
      if (validatorFn.name) {
        return validatorFn.name;
      }
      
      // 如果没有name属性，尝试从函数字符串中提取
      const fnStr = Function.prototype.toString.call(validatorFn);
      
      // 检查常见校验器函数名
      const validatorTypes = [
        'checkEmail', 'checkPhone', 'checkUrl', 'checkNumber', 
        'checkInteger', 'checkFloat', 'checkIdCard', 'checkPassword', 
        'checkUsername', 'checkIpAddress', 'checkZipCode', 'checkGreaterThan'
      ];
      
      for (const type of validatorTypes) {
        if (fnStr.includes(type)) {
          return type;
        }
      }
    } catch (error) {
      console.error('解析校验器函数失败', error);
    }
  }
  
  return undefined;
}

/**
 * 处理校验器变化
 */
function handleValidatorChange(rule: FormRule, val: string, ruleIndex: number, index: number) {
  console.log('handleValidatorChange', localConfig.value.rules, rule.validator, val, ruleIndex, index);
  
  // 安全检查，确保数据存在
  if (!localConfig.value.rules) return;
  
  // 获取所有规则键
  const ruleKeys = Object.keys(localConfig.value.rules);
  if (index < 0 || index >= ruleKeys.length) return;
  
  // 获取当前字段名
  const fieldName = ruleKeys[index];
  const rules = localConfig.value.rules[fieldName];
  
  // 确保规则项存在
  if (!Array.isArray(rules) || ruleIndex < 0 || ruleIndex >= rules.length) return;
  
  // 更新校验器值 - 始终保存为字符串
  const currentRule = rules[ruleIndex];
  
  // 根据值更新
  if (val) {
    // 保存为字符串形式的校验器名称
    currentRule.validator = val;
    
    // 根据校验器类型设置默认值和清理旧参数
    if (val === 'checkGreaterThan') {
      // 为大于阈值校验器设置默认阈值
      if (currentRule.threshold === undefined) {
        currentRule.threshold = 0;
      }
    } else {
      // 清理不相关的参数
      if ('threshold' in currentRule) {
        delete currentRule.threshold;
      }
    }
    
    // 设置默认消息
    if (!currentRule.message) {
      const messageMap: Record<string, string> = {
        checkEmail: '请输入正确的邮箱地址',
        checkPhone: '请输入正确的手机号码',
        checkUrl: '请输入正确的URL地址',
        checkNumber: '请输入有效的数字',
        checkInteger: '请输入整数',
        checkFloat: '请输入有效的小数',
        checkIdCard: '请输入正确的身份证号码',
        checkPassword: '密码必须包含字母和数字，长度在6-20位之间',
        checkUsername: '用户名只能包含字母、数字和下划线，长度在3-20位之间',
        checkIpAddress: '请输入正确的IP地址',
        checkZipCode: '请输入正确的邮政编码',
        checkGreaterThan: '输入值必须大于阈值'
      };
      
      currentRule.message = messageMap[val] || '请输入正确的值';
    }
    
    // 设置默认触发方式为blur
    if (!currentRule.trigger) {
      currentRule.trigger = 'blur';
    }
  } else {
    // 如果值为空，清除校验器
    if ('validator' in currentRule) {
      delete currentRule.validator;
    }
    
    // 清理校验器相关参数
    if ('threshold' in currentRule) {
      delete currentRule.threshold;
    }
  }
  
  // 强制更新配置
  handleConfigChange();
}

/**
 * 确认添加表单规则
 */
function confirmAddFormRule() {
  if (!localConfig.value.rules) {
    localConfig.value.rules = {};
  }
  
  let fieldName = '';
  
  // 确定字段名
  if (newRuleFieldProp.value === 'custom') {
    if (!customRuleFieldProp.value) {
      ElMessage.warning('请输入自定义字段名');
      return;
    }
    fieldName = customRuleFieldProp.value;
  } else {
    if (!newRuleFieldProp.value) {
      ElMessage.warning('请选择字段');
      return;
    }
    fieldName = newRuleFieldProp.value;
  }
  
  // 检查字段是否已存在
  if (!localConfig.value.rules[fieldName]) {
    // 如果字段不存在，创建第一条规则为必填项
    localConfig.value.rules[fieldName] = [
      {
        required: true,
        message: `请输入${fieldName}`,
        trigger: 'blur'
      }
    ];
    
    // 如果用户选择了校验器，添加为第二条规则
    if (newRuleValidator.value) {
      // 创建新规则
      const newRule: FormRule = {
        validator: newRuleValidator.value,
        message: '',
        trigger: 'blur'
      };
      
      // 处理大于阈值校验器
      if (newRuleValidator.value === 'checkGreaterThan') {
        newRule.threshold = newRuleThreshold.value;
        newRule.message = `输入值必须大于 ${newRuleThreshold.value}`;
      } else {
        // 设置其他校验器的默认消息
        const messageMap: Record<string, string> = {
          checkEmail: '请输入正确的邮箱地址',
          checkPhone: '请输入正确的手机号码',
          checkUrl: '请输入正确的URL地址',
          checkNumber: '请输入有效的数字',
          checkInteger: '请输入整数',
          checkFloat: '请输入有效的小数',
          checkIdCard: '请输入正确的身份证号码',
          checkPassword: '密码必须包含字母和数字，长度在6-20位之间',
          checkUsername: '用户名只能包含字母、数字和下划线，长度在3-20位之间',
          checkIpAddress: '请输入正确的IP地址',
          checkZipCode: '请输入正确的邮政编码',
        };
        
        newRule.message = messageMap[newRuleValidator.value] || '请输入正确的值';
      }
      
      localConfig.value.rules[fieldName].push(newRule);
    }
  } else {
    // 如果字段已存在，添加新的校验规则
    if (!newRuleValidator.value) {
      ElMessage.warning('请选择一个校验器');
      return;
    }
    
    // 创建新规则
    const newRule: FormRule = {
      validator: newRuleValidator.value,
      message: '',
      trigger: 'blur'
    };
    
    // 处理大于阈值校验器
    if (newRuleValidator.value === 'checkGreaterThan') {
      newRule.threshold = newRuleThreshold.value;
      newRule.message = `输入值必须大于 ${newRuleThreshold.value}`;
    } else {
      // 设置其他校验器的默认消息
      const messageMap: Record<string, string> = {
        checkEmail: '请输入正确的邮箱地址',
        checkPhone: '请输入正确的手机号码',
        checkUrl: '请输入正确的URL地址',
        checkNumber: '请输入有效的数字',
        checkInteger: '请输入整数',
        checkFloat: '请输入有效的小数',
        checkIdCard: '请输入正确的身份证号码',
        checkPassword: '密码必须包含字母和数字，长度在6-20位之间',
        checkUsername: '用户名只能包含字母、数字和下划线，长度在3-20位之间',
        checkIpAddress: '请输入正确的IP地址',
        checkZipCode: '请输入正确的邮政编码',
      };
      
      newRule.message = messageMap[newRuleValidator.value] || '请输入正确的值';
    }
    
    localConfig.value.rules[fieldName].push(newRule);
  }
  
  // 重置对话框状态
  newRuleFieldProp.value = '';
  customRuleFieldProp.value = '';
  newRuleRequired.value = false;
  newRuleValidator.value = '';
  newRuleThreshold.value = 0;
  ruleTypeDialogVisible.value = false;
  
  handleConfigChange();
}

/**
 * 更新源码表示
 */
function updateSourceCode() {
  sourceCode.value = JSON.stringify(localConfig.value, null, 2);
}
</script>

<style scoped>
.form-config-editor {
  width: 100%;
}

.json-viewer-container {
  height: 100%;
  overflow: auto;
  max-height: 500px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  width: 100%;
}

.text-left :deep(.vjs-tree),
.text-left :deep(.vjs-tree *) {
  text-align: left !important;
}

.divider-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.draggable-container {
  margin-top: 10px;
  border: 1px dashed #ccc;
  padding: 10px;
  border-radius: 4px;
}

.drag-tip {
  color: #909399;
  font-size: 14px;
  margin-bottom: 10px;
}

/* 修改表单项容器的布局，使用CSS Grid实现响应式布局 */
.grid-container {
  display: grid !important; /* 确保grid显示不被覆盖 */
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important; /* 确保列设置不被覆盖 */
  gap: 15px;
  width: 100%;
  margin: 0;
  padding: 0;
}

.form-item {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  width: 100%; /* 确保宽度为100% */
  box-sizing: border-box; /* 确保padding不会增加宽度 */
  min-width: 0; /* 防止内容溢出 */
}

.form-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.option-item {
  margin: 10px 0;
  padding: 10px;
  background: #f0f9eb;
  border-radius: 4px;
}

.drag-handle {
  width: 100%;
  cursor: move;
  padding: 5px;
  background: #ecf5ff;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 5px;
}

.source-code-section {
  margin-top: 20px;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.rules-container {
  margin-top: 10px;
  border: 1px dashed #e6a23c;
  padding: 10px;
  border-radius: 4px;
  width: 100%;
}

.rules-grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  width: 100%;
}

.rule-item {
  margin-bottom: 15px;
  background: #fdf6ec;
  padding: 10px;
  border-radius: 4px;
  width: 100%;
  box-sizing: border-box;
}

.rule-items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  width: 100%;
}

.rule-item-detail {
  margin: 10px 0;
  padding: 10px;
  background: #f8f8f8;
  border-radius: 4px;
  box-sizing: border-box;
}

.options-section {
  margin-top: 10px;
  padding: 10px;
  border: 0px dashed #e6a23c;
  border-radius: 4px;
  width: 100%;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.toggle-options-btn {
  margin-left: 10px;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  width: 100%;
}

.action-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.drag-tip {
  display: none;
}
</style>
