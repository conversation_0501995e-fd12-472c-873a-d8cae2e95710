<!--
  关联排序开关组件
  用于在配置编辑器之间开启/关闭关联排序功能
-->
<template>
  <div class="linked-sorting-toggle">
    <el-tooltip 
      :content="enabled ? '关联排序已开启，排序一个编辑器中的项目将会同步排序其他编辑器中的项目' : '开启关联排序，将同步排序表单、表格、搜索和详情编辑器中的项目'"
      placement="top"
    >
      <el-switch
        v-model="enabled"
        inline-prompt
        active-text="关联排序"
        inactive-text="独立排序"
        class="linked-sorting-switch"
      />
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useLinkedSortingStore } from '../../stores/linkedSortingStore';

const linkedSortingStore = useLinkedSortingStore();

// 计算属性，用于获取和设置关联排序状态
const enabled = computed({
  get: () => linkedSortingStore.enabled,
  set: (value) => linkedSortingStore.setEnabled(value)
});
</script>

<style scoped>
.linked-sorting-toggle {
  margin: 10px 0;
  display: flex;
  justify-content: flex-end;
}

.linked-sorting-switch {
  margin-left: auto;
}
</style>
