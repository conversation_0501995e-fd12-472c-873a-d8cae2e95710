<!-- 
  服务配置编辑器组件
  用于编辑服务配置，包括基础URL、标题、消息配置以及自定义操作等
  2025-04-15 修复：统一数据源为 form，所有操作、渲染、源码编辑全部基于 form，彻底消灭 undefined 渲染报错。
  2025-04-15 修复：draggable 相关逻辑，保证不再报错，兼容空和未定义场景。
  2025-04-23 更新：使用 store 方式直接绑定数据，移除 form 变量，简化数据流。
  ServiceConfigEditor.vue
  服务配置编辑器组件，支持自定义操作的增删改查及拖拽排序
  该文件包含自定义操作(customActions)的相关逻辑，确保 customActions 始终为对象，避免未定义错误
  2025-04-27 更新：引入独立的RulesConfigEditor组件处理表单校验规则配置
-->
<template>
  <div class="service-config-editor">
    <el-form label-width="120px">
      <el-form-item v-if="!store.gridLayoutStore.structureData.config_content.viewDialogConfig">
        <el-button type="primary" @click="initviewDialogConfig">初始化FormDialog配置</el-button>
      </el-form-item>
      <div v-else>
        <el-form-item label="弹窗类型">
          <el-select v-model="store.gridLayoutStore.structureData.config_content.viewDialogConfig.dialogType" placeholder="选择弹窗类型" default-value="dialog">
            <el-option label="对话框" value="dialog" />
            <el-option label="抽屉" value="drawer" />
          </el-select>
        </el-form-item>
        <el-form-item label="弹窗宽度">
          <el-input v-model="store.gridLayoutStore.structureData.config_content.viewDialogConfig.width" />
        </el-form-item>
        <el-form-item label="每行列数">
          <el-select v-model="store.gridLayoutStore.structureData.config_content.viewDialogConfig.column" placeholder="请选择列数">
            <el-option v-for="item in [1,2,3,4]" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="校验规则">
          <RulesConfigEditor
            v-model="store.gridLayoutStore.structureData.config_content.viewDialogConfig.rules"
            :columns="store.gridLayoutStore.structureData.config_content.viewDialogConfig.columns"
            @change="handleConfigChange"
          />
        </el-form-item>
        <el-form-item label="列配置">
          <!-- <div class="action-row">
            <el-button type="primary" @click="addFormColumn">添加列</el-button>
          </div> -->
          <ColumnConfigEditor
            v-model="store.gridLayoutStore.structureData.config_content.viewDialogConfig.columns"
            mode="transfer"
            type="form"
            :availableFields="availableFields"
            :parent="columnParent"
            @change="handleConfigChange"
          />
        </el-form-item>
      </div>
    </el-form>
    <!-- 底部操作按钮区域 -->
    <div class="footer-actions" style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 32px;">
      <el-button type="primary" @click="onSaveConfig">保存</el-button>
      <el-button type="primary" @click="handleRefreshPage">刷新页面</el-button>
    </div>
    <!-- 列高级配置弹窗 -->
    <el-dialog
      v-model="columnDialogVisible"
      title="列高级配置"
      width="800px"
    >
      <div v-if="currentEditColumn">
        <el-form label-width="120px">
          <el-form-item label="标签">
            <el-input v-model="currentEditColumn.label" @input="handleConfigChange" />
          </el-form-item>
          <el-form-item label="属性">
            <el-input v-model="currentEditColumn.prop" @input="handleConfigChange" />
          </el-form-item>
          <el-form-item label="宽度">
            <el-input-number v-model="currentEditColumn.width" :min="0" @change="handleConfigChange" />
          </el-form-item>
          <el-form-item label="类型">
            <el-select v-model="currentEditColumn.valueType" @change="handleConfigChange">
              <el-option label="文本" value="text" />
              <el-option label="标签" value="tag" />
              <el-option label="图片" value="img" />
              <el-option label="日期时间" value="date-picker" />
              <el-option label="单选框" value="radio" />
              <el-option label="选择器" value="select" />
              <el-option label="多选框" value="checkbox" />
              <el-option label="密码框" value="password" />
              <el-option label="文本域" value="textarea" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="提示文本">
            <el-input 
              v-model="currentEditColumn.tooltip" 
              @input="handleConfigChange"
              placeholder="鼠标悬停时显示的提示文本"
            />
          </el-form-item>
          
          <el-form-item label="占位符">
            <el-input 
              v-model="currentEditColumn.placeholder" 
              @input="handleConfigChange"
              placeholder="输入框的占位符文本"
            />
          </el-form-item>
          
          <el-form-item label="必填">
            <el-switch 
              v-model="currentEditColumn.required" 
              @change="handleColumnRequiredChange"
            />
          </el-form-item>
          
          <!-- 日期时间格式配置 -->
          <template v-if="currentEditColumn.valueType === 'date-picker'">
            <el-divider>日期时间配置</el-divider>
            
            <el-form-item label="日期类型">
              <el-select 
                v-model="currentEditColumn.type" 
                placeholder="选择日期类型"
              >
                <el-option label="日期" value="date" />
                <el-option label="日期时间" value="datetime" />
                <el-option label="年份" value="year" />
                <el-option label="月份" value="month" />
                <el-option label="周" value="week" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="显示格式">
              <el-input 
                v-model="currentEditColumn.format"
                placeholder="如: YYYY-MM-DD"
              >
                <template #append>
                  <el-tooltip content="例如：YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item label="值格式">
              <el-input 
                v-model="currentEditColumn.valueFormat"
                placeholder="如: YYYY-MM-DD"
              >
                <template #append>
                  <el-tooltip content="例如：YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
          </template>
          
          <!-- 选择器和单选框配置 -->
          <template v-if="currentEditColumn.valueType === 'select' || currentEditColumn.valueType === 'radio' || currentEditColumn.valueType === 'tag' || currentEditColumn.valueType === 'checkbox'">
            <el-divider>选项配置</el-divider>
            
            <div class="options-section">
              <div class="options-header">
                <el-button type="primary" size="small" @click="addColumnOption">添加选项</el-button>
              </div>
              
              <div class="options-container">
                <div class="options-grid">
                  <div v-for="(option, optIndex) in currentEditColumn.options || []" :key="optIndex" class="option-item">
                    <el-divider>选项 {{ optIndex + 1 }}</el-divider>
                    
                    <el-form-item label="标签">
                      <el-input v-model="option.label" @input="handleConfigChange" />
                    </el-form-item>
                    
                    <el-form-item label="值">
                      <el-input v-model="option.value" @input="handleConfigChange" />
                    </el-form-item>
                    
                    <el-form-item label="颜色">
                      <el-select v-model="option.color" @change="handleConfigChange">
                        <el-option label="默认" value="" />
                        <el-option label="红色" value="red" />
                        <el-option label="蓝色" value="blue" />
                        <el-option label="绿色" value="green" />
                        <el-option label="黄色" value="yellow" />
                        <el-option label="灰色" value="gray" />
                        <el-option label="主要" value="primary" />
                        <el-option label="成功" value="success" />
                        <el-option label="警告" value="warning" />
                        <el-option label="危险" value="danger" />
                        <el-option label="信息" value="info" />
                      </el-select>
                    </el-form-item>
                    
                    <el-button type="danger" size="small" @click="removeColumnOption(optIndex)">
                      删除选项
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </template>
          
          <!-- 文本域配置 -->
          <template v-if="currentEditColumn.valueType === 'textarea'">
            <el-divider>文本域配置</el-divider>
            
            <el-form-item label="行数">
              <el-input-number 
                v-model="currentEditColumn.rows"
                :min="2"
                :max="10"
              />
            </el-form-item>
            
            <el-form-item label="自动高度">
              <el-switch 
                v-model="currentEditColumn.autosize"
              />
            </el-form-item>
          </template>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="columnDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveColumnConfig">保存</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- JSON可视化预览区 -->
    <div class="json-preview-section">
      <el-divider>
        <div class="divider-actions">
          <el-button link size="small" @click="sourceVisible = !sourceVisible">
            {{ sourceVisible ? '隐藏源码' : '显示源码' }}
            <el-icon>
              <component :is="sourceVisible ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
          </el-button>
          <el-button type="warning" @click="handleFormatSource" v-if="sourceVisible">格式化</el-button>
        </div>
      </el-divider>
      
      <!-- JSON预览 -->
      <el-row :gutter="20">
        <!-- 源码编辑区 -->
        <el-col :span="sourceVisible ? 12 : 0" v-if="sourceVisible">
          <el-input
            v-model="sourceCode"
            type="textarea"
            :rows="10"
            @input="handleSourceChange"
            placeholder="请输入查看对话框配置JSON"
          />
        </el-col>
        <!-- JSON预览区 -->
        <el-col :span="sourceVisible ? 12 : 24">
          <div class="json-viewer-container">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>JSON可视化预览</span>
                </div>
              </template>
              <JsonViewer 
                :value="parsedSourceJson" 
                :expandDepth="3" 
                copyable 
                sort 
                boxed 
                theme="light"
                class="text-left"
              />
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- 添加规则类型选择弹窗 -->
    <el-dialog
      v-model="ruleTypeDialogVisible"
      title="添加校验规则"
      width="500px"
    >
      <el-form label-width="120px">
        <el-form-item label="字段选择">
          <el-select v-model="newRuleFieldProp" placeholder="选择要校验的字段">
            <el-option label="自定义字段" value="custom" />
            <el-option 
              v-for="field in availableFields" 
              :key="field.prop" 
              :label="field.label" 
              :value="field.prop" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="自定义字段名" v-if="newRuleFieldProp === 'custom'">
          <el-input v-model="customRuleFieldProp" placeholder="输入自定义字段名" />
        </el-form-item>
        <el-form-item label="是否必填">
          <el-switch v-model="newRuleRequired" />
        </el-form-item>
        <el-form-item label="校验器">
          <el-select v-model="newRuleValidator" placeholder="选择校验器" clearable>
            <el-option 
              v-for="option in validatorOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="阈值" v-if="newRuleValidator === 'checkGreaterThan'">
          <el-input-number v-model="newRuleThreshold" :min="0" :precision="2" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ruleTypeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="ruleTypeDialogVisible = false">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
//import { ArrowUp, ArrowDown } from '@element-plus/icons-vue';
import { JsonViewer } from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/index.css';
import ColumnConfigEditor from './ColumnConfigEditor.vue';
import RulesConfigEditor from './RulesConfigEditor.vue';
import { validatorOptions } from '@/components/page/validator';

interface FormColumn {
  label: string;
  prop: string;
  valueType?: string;
  width?: number;
  required?: boolean;
  placeholder?: string;
  tooltip?: string;
  options?: Array<{
    label: string;
    value: string;
    color?: string;
  }>;
  [key: string]: any;
}

interface viewDialogConfig {
  dialogType?: string;
  title?: string;
  width?: string | number;
  rules?: Record<string, any[]>;
  columns?: FormColumn[];
  [key: string]: any;
}

const props = defineProps({
  modelValue: {
    type: Object as () => viewDialogConfig,
    default: () => ({})
  },
  store: {
    type: Object,
    required: true
  }
});

const ruleTypeDialogVisible = ref(false);
const newRuleFieldProp = ref('');
const customRuleFieldProp = ref('');
const newRuleRequired = ref(true);
const newRuleValidator = ref('');
const newRuleThreshold = ref(0);

const viewDialogConfig = computed(() => {
  return props.store.gridLayoutStore.structureData.config_content.viewDialogConfig || {};
});

// 监启viewDialogConfig变化，更新源码
watch(viewDialogConfig, () => {
  updateSourceCode();
}, { deep: true });

// 组件挂载时初始化源码
onMounted(() => {
  updateSourceCode();
});

// const localConfig = computed(() => {
//   return {
//     columns: props.store.gridLayoutStore.structureData.config_content.viewDialogConfig?.columns || []
//   };
// });

const emit = defineEmits(['update:modelValue', 'refresh']);

// const defaultConfig: viewDialogConfig = {
//   dialogType: 'dialog',
//   title: '表单弹窗',
//   width: '500px',
//   rules: {},
//   columns: []
// };

function ensureConfigIntegrity() {
  if (!viewDialogConfig.value.rules) {
    viewDialogConfig.value.rules = {};
  }
  if (!viewDialogConfig.value.columns) {
    viewDialogConfig.value.columns = [];
  }
}

ensureConfigIntegrity();

function onSaveConfig() {
  props.store.saveConfig();
  props.store.saveLastConfig();
  console.log('保存配置:', viewDialogConfig.value);
  ElMessage.success('配置已保存');
}

// 源码显示状态，默认不显示
const sourceVisible = ref(false);
const sourceCode = ref('{}');

// 解析后的JSON对象，用于预览
const parsedSourceJson = computed(() => {
  try {
    return JSON.parse(sourceCode.value);
  } catch (e) {
    return {};
  }
});

function updateSourceCode() {
  // 确保存在数据再更新
  if (viewDialogConfig.value) {
    sourceCode.value = JSON.stringify(viewDialogConfig.value, null, 2);
  }
}

function handleSourceChange() {
  try {
    const parsed = JSON.parse(sourceCode.value);
    props.store.gridLayoutStore.structureData.config_content.viewDialogConfig = parsed;
    ElMessage.success('配置已更新');
  } catch (error) {
    ElMessage.error('JSON格式错误，请检查');
  }
}

function handleFormatSource() {
  try {
    const parsed = JSON.parse(sourceCode.value);
    sourceCode.value = JSON.stringify(parsed, null, 2);
    ElMessage.success('格式化成功');
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化');
  }
}

function handleRefreshPage() {
  window.location.reload();
}

function handleConfigChange(): void {
  updateSourceCode();
}

function handleColumnRequiredChange() {
  if (!currentEditColumn.value) return;
  
  if (!viewDialogConfig.value.rules) {
    viewDialogConfig.value.rules = {};
  }
  
  const prop = currentEditColumn.value.prop;
  
  if (currentEditColumn.value.required) {
    if (!viewDialogConfig.value.rules[prop]) {
      viewDialogConfig.value.rules[prop] = [];
    }
    
    const existingRequiredRule = viewDialogConfig.value.rules[prop].find((rule: any) => rule.required === true);
    
    if (!existingRequiredRule) {
      viewDialogConfig.value.rules[prop].unshift({
        required: true,
        message: `请输入${currentEditColumn.value.label || prop}`,
        trigger: 'blur'
      });
    }
  } else {
    if (viewDialogConfig.value.rules[prop]) {
      viewDialogConfig.value.rules[prop] = viewDialogConfig.value.rules[prop].filter((rule: any) => rule.required !== true);
      
      if (viewDialogConfig.value.rules[prop].length === 0) {
        delete viewDialogConfig.value.rules[prop];
      }
    }
  }
  
  handleConfigChange();
}

function saveColumnConfig() {
  if (!currentEditColumn.value) return;
  
  const index = viewDialogConfig.value.columns.findIndex((col: any) => col.prop === currentEditColumn.value?.prop);
  
  if (index !== -1) {
    viewDialogConfig.value.columns[index] = { ...currentEditColumn.value };
  } else {
    viewDialogConfig.value.columns.push({ ...currentEditColumn.value });
  }
  
  columnDialogVisible.value = false;
  handleConfigChange();
  ElMessage.success('列配置已保存');
}

function addColumnOption() {
  if (!currentEditColumn.value) return;
  
  if (!currentEditColumn.value.options) {
    currentEditColumn.value.options = [];
  }
  
  currentEditColumn.value.options.push({
    label: `选项${currentEditColumn.value.options.length + 1}`,
    value: `option${currentEditColumn.value.options.length + 1}`
  });
}

function removeColumnOption(index: number) {
  if (!currentEditColumn.value || !currentEditColumn.value.options) return;
  
  currentEditColumn.value.options.splice(index, 1);
}

function initviewDialogConfig() {
  props.store.gridLayoutStore.structureData.config_content.viewDialogConfig = {
    dialogType: 'dialog',
    createTitle: '创建',
    editTitle: '编辑',
    viewTitle: '查看',
    width: '800px',
    column: 2, // 默认每行列数为2
    columns: []
  };
}

const columnParent = reactive({
  addRule(prop: string, required: boolean) {
    if (!viewDialogConfig.value.rules) {
      viewDialogConfig.value.rules = {};
    }
    
    if (!viewDialogConfig.value.rules[prop]) {
      viewDialogConfig.value.rules[prop] = [];
    }
    
    const existingRequiredRule = viewDialogConfig.value.rules[prop].find((rule: any) => rule.required === true);
    
    if (!existingRequiredRule && required) {
      viewDialogConfig.value.rules[prop].unshift({
        required: true,
        message: `请输入${prop}`,
        trigger: 'blur'
      });
    }
  },
  removeRule(prop: string) {
    if (!viewDialogConfig.value.rules) {
      return;
    }
    
    if (viewDialogConfig.value.rules[prop]) {
      delete viewDialogConfig.value.rules[prop];
    }
  }
});

const columnDialogVisible = ref(false);
const currentEditColumn = ref<FormColumn | null>(null);

const availableFields = computed(() => {
  const fields = props.store.processedDtoFields || [];
  
  return fields.map((field: any) => ({
    prop: field.prop,  // 修改key为prop，与ColumnConfigEditor组件的期望一致
    label: field.label || field.prop,
    type: field.type
  }));
});

// function addFormColumn() {
//   if (!viewDialogConfig.value.columns) {
//     viewDialogConfig.value.columns = [];
//   }
  
//   viewDialogConfig.value.columns.push({
//     label: '新列',
//     prop: 'newColumn' + Date.now(),
//     valueType: 'text',
//     required: false,
//     placeholder: '',
//     tooltip: ''
//   });
  
//   handleConfigChange();
// }
</script>

<style scoped>
.json-viewer-container {
  margin-bottom: 20px;
  text-align: left;
}
.service-config-editor {
  width: 100%;
}

.draggable-container {
  margin-top: 10px;
  border: 1px dashed #ccc;
  padding: 10px;
  border-radius: 4px;
}

.drag-tip {
  color: #909399;
  font-size: 14px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.custom-action-item {
  margin-bottom: 15px;
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
  min-width: 0;
}

.custom-action-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.column-item {
  margin-bottom: 15px;
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
  min-width: 0;
}

.column-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.grid-container {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important;
  gap: 15px;
  width: 100%;
  margin: 0;
  padding: 0;
}

.drag-handle {
  width: 100%;
  cursor: move;
  padding: 5px;
  background: #ecf5ff;
  border-radius: 4px;
  display: inline-block;
}

.source-code-section {
  margin-top: 20px;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.rules-container {
  margin-top: 10px;
  width: 100%;
}

.rules-grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  width: 100%;
}

.rule-item {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 10px;
}

.rule-items-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.rule-item-detail {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  margin-bottom: 10px;
}

.options-section {
  margin-top: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.options-container {
  margin-top: 10px;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
}

.option-item {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.action-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.footer-actions {
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
  margin-top: 20px;
}
</style>
