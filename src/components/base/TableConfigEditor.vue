<!-- 
  表格配置编辑器组件
  用于编辑表格配置，包括显示选项、分页、列配置以及操作按钮等
-->
<template>
  <div class="table-config-editor">
    <el-form :model="localConfig" label-width="120px">
      <el-form-item label="显示选择">
        <el-switch v-model="localConfig.showSelection" @change="handleConfigChange" />
      </el-form-item>
      <el-form-item label="显示序号">
        <el-switch v-model="localConfig.showIndex" @change="handleConfigChange" />
      </el-form-item>
      <el-form-item label="显示操作">
        <el-switch v-model="localConfig.showActions" @change="handleConfigChange" />
      </el-form-item>
      <el-form-item label="行键">
        <el-input v-model="localConfig.rowKey" @input="handleConfigChange" />
      </el-form-item>
      <el-form-item label="显示分页">
        <el-switch v-model="localConfig.showPagination" @change="handleConfigChange" />
      </el-form-item>
      <el-form-item label="分页配置">
        <el-form-item label="每页条数">
          <el-input-number 
            :model-value="localConfig.pagination?.pageSize" 
            @update:model-value="(val: number) => updatePagination('pageSize', val)"
            :min="1" 
            @change="handleConfigChange" 
          />
        </el-form-item>
        <el-form-item label="可选条数">
          <el-select 
            :model-value="localConfig.pagination?.pageSizes" 
            @update:model-value="(val: number[]) => updatePagination('pageSizes', val)"
            multiple 
            @change="handleConfigChange"
          >
            <el-option
              v-for="size in [10, 20, 30, 50, 100]"
              :key="size"
              :label="size"
              :value="size"
            />
          </el-select>
        </el-form-item>
      </el-form-item>
      
      <!-- 新增 action-bar 配置 -->
      <el-form-item label="操作栏配置">
        <el-button type="primary" @click="addActionBarButton">添加操作栏按钮</el-button>
        <el-divider>基础配置</el-divider>
        <el-form-item label="按钮类型">
          <el-select 
            :model-value="getActionBarProp('type')" 
            @update:model-value="(val: string) => updateActionBarProp('type', val)"
            placeholder="选择按钮类型"
          >
            <el-option label="链接" value="link" />
            <el-option label="图标" value="icon" />
            <el-option label="按钮" value="button" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="确认类型">
          <el-select 
            :model-value="getActionBarProp('confirmType')" 
            @update:model-value="(val: string) => updateActionBarProp('confirmType', val)"
            placeholder="选择确认类型"
          >
            <el-option label="气泡确认框" value="popconfirm" />
            <el-option label="消息确认框" value="messageBox" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="列宽度">
          <el-input-number 
            :model-value="getActionBarProp('width')" 
            @update:model-value="(val: number) => updateActionBarProp('width', val)"
            :min="50"
            :step="10"
            @change="handleConfigChange"
          />
        </el-form-item>
        
        <el-divider>按钮列表</el-divider>
        <div class="draggable-container" style="width: 100%">
          <p v-if="(localConfig.actionBar?.buttons || []).length > 0" class="drag-tip">
            <el-icon><Rank /></el-icon> 拖拽排序
          </p>
          <draggable 
            :list="localConfig.actionBar?.buttons || []"
            :item-key="(item: any) => item.code || Math.random().toString()"
            handle=".drag-handle"
            ghost-class="ghost"
            @change="handleDragEnd"
            class="grid-container"
          >
            <template #item="{element, index}">
              <div class="button-item">
                <div class="drag-handle">
                  <el-icon><Rank /></el-icon>
                </div>
                <el-divider>操作栏按钮 {{ index + 1 }}</el-divider>
                
                <el-form-item label="文本">
                  <el-input v-model="element.text" @input="handleConfigChange" />
                </el-form-item>
                
                <el-form-item label="代码">
                  <el-input v-model="element.code" @input="handleConfigChange" />
                </el-form-item>
                
                <el-form-item label="图标">
                  <el-input v-model="element.icon" @input="handleConfigChange" placeholder="Element Plus图标名称" />
                </el-form-item>
                
                <el-form-item label="按钮类型">
                  <el-select v-model="element.props.type" @change="handleConfigChange">
                    <el-option label="主要" value="primary" />
                    <el-option label="成功" value="success" />
                    <el-option label="警告" value="warning" />
                    <el-option label="危险" value="danger" />
                    <el-option label="信息" value="info" />
                    <el-option label="文本" value="text" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="需要确认">
                  <el-switch 
                    :model-value="!!element.confirm" 
                    @update:model-value="(val: boolean) => handleConfirmChange(element, val)" 
                  />
                </el-form-item>
                
                <el-form-item label="确认信息" v-if="element.confirm">
                  <el-input 
                    :model-value="getConfirmMessage(element)" 
                    @update:model-value="(val: string) => updateConfirmMessage(element, val)"
                    placeholder="输入确认提示信息，如: 确定删除此条数据吗？"
                  />
                </el-form-item>
                
                <el-form-item label="条件显示">
                  <el-input 
                    v-model="element.condition" 
                    @input="handleConfigChange"
                    placeholder="输入条件表达式，如: row.status === '1'"
                  />
                </el-form-item>
                
                <el-button type="danger" @click="removeActionBarButton(index)">删除</el-button>
              </div>
            </template>
          </draggable>
        </div>
      </el-form-item>
      
      <el-form-item label="列配置">
        <ColumnConfigEditor 
          v-model="localConfig.columns"
          mode="drag"
          :type="props.type"
          :parent="columnParent"
          @change="handleConfigChange"
        >
          <template #sorting-toggle>
            <LinkedSortingToggle />
          </template>
        </ColumnConfigEditor>
      </el-form-item>
    </el-form>
    
    <!-- JSON可视化预览区 -->
    <div class="json-preview-section">
      <el-divider>
        <div class="divider-actions">
          <el-button link size="small" @click="sourceVisible = !sourceVisible">
            {{ sourceVisible ? '隐藏源码' : '显示源码' }}
            <el-icon>
              <component :is="sourceVisible ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
          </el-button>
          <el-button type="warning" @click="handleFormatSource" v-if="sourceVisible">格式化</el-button>
        </div>
      </el-divider>
      
      <!-- JSON预览 -->
      <el-row :gutter="20">
        <!-- 源码编辑区 -->
        <el-col :span="sourceVisible ? 12 : 0" v-if="sourceVisible">
          <el-input
            v-model="sourceCode"
            type="textarea"
            :rows="10"
            @input="handleSourceChange"
            placeholder="请输入表格配置JSON"
          />
        </el-col>
        <!-- JSON预览区 -->
        <el-col :span="sourceVisible ? 12 : 24">
          <div class="json-viewer-container">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>JSON可视化预览</span>
                </div>
              </template>
              <JsonViewer 
                :value="parsedSourceJson" 
                :expandDepth="3" 
                copyable 
                sort 
                boxed 
                theme="light"
                class="text-left"
              />
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { JsonViewer } from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/index.css';
import draggable from 'vuedraggable';
import LinkedSortingToggle from './LinkedSortingToggle.vue';
import { useLinkedSortingStore } from '../../stores/linkedSortingStore';
import { Rank } from '@element-plus/icons-vue';
import ColumnConfigEditor from './ColumnConfigEditor.vue'; // 引入ColumnConfigEditor组件

interface TableColumn {
  label: string;
  prop: string;
  valueType?: string;
  width?: number;
  editable?: boolean;
  options?: any[];
  fieldProps?: Record<string, any>;
  [key: string]: any;
}

interface Pagination {
  pageSize: number;
  pageSizes: number[];
}

// 定义操作栏按钮类型接口
interface ActionBarButtonProps {
  type?: string;
  [key: string]: any;
}

interface ActionBarButtonConfirm {
  message: string;
  [key: string]: any;
}

interface ActionBarButton {
  text: string;
  code: string;
  icon?: string;
  type?: string;
  props?: ActionBarButtonProps;
  confirm?: boolean | ActionBarButtonConfirm;
  condition?: string;
  [key: string]: any;
}

interface TableConfig {
  showSelection: boolean;
  showIndex: boolean;
  showActions: boolean;
  rowKey: string;
  showPagination: boolean;
  pagination?: Pagination;
  columns: TableColumn[];
  actionBar?: {
    type?: string;
    confirmType?: string;
    width?: number;
    buttons?: ActionBarButton[];
  };
  [key: string]: any;
}

const props = defineProps({
  modelValue: {
    type: Object as () => TableConfig,
    default: () => ({
      showSelection: true,
      showIndex: true,
      showActions: true,
      rowKey: 'id',
      showPagination: true,
      pagination: {
        pageSize: 10,
        pageSizes: [10, 20, 50, 100]
      },
      columns: [],
      actionBar: {
        type: 'link', 
        confirmType: 'popconfirm', 
        width: 200, 
        buttons: [] 
      }
    })
  },
  // 表格类型，用于传递给ColumnConfigEditor以配置不同类型的列
  type: {
    type: String as () => 'table' | 'form' | 'page' | undefined,
    default: 'table',
    validator: (value: string) => ['table', 'form', 'page', undefined].includes(value as any)
  }
});

const emit = defineEmits(['update:modelValue']);

// 本地配置数据
const localConfig = ref<TableConfig>({} as TableConfig);

// 源码显示状态，默认不显示
const sourceVisible = ref(false);
const sourceCode = ref('{}');

// JSON解析结果用于可视化显示
const parsedSourceJson = computed(() => {
  try {
    return JSON.parse(sourceCode.value);
  } catch (error) {
    return { error: '无效的JSON格式' };
  }
});

// 列配置父组件实例，用于传递给ColumnConfigEditor
const columnParent = reactive({
  // 添加规则（在Columns组件中不需要实现）
  addRule: (prop: string, required: boolean) => {
    console.log('添加规则', prop, required);
  },
  // 删除规则（在Columns组件中不需要实现）
  removeRule: (prop: string) => {
    console.log('删除规则', prop);
  }
});

// 获取关联排序状态管理
const linkedSortingStore = useLinkedSortingStore();

// 监听关联排序状态变化
watch(() => linkedSortingStore.enabled, (enabled) => {
  if (enabled && localConfig.value.columns?.length) {
    // 开启关联排序时，应用排序顺序
    const sortedColumns = linkedSortingStore.sortItemsByMap(localConfig.value.columns);
    if (JSON.stringify(sortedColumns) !== JSON.stringify(localConfig.value.columns)) {
      localConfig.value.columns = sortedColumns;
      // 通知父组件值已更新
      emit('update:modelValue', JSON.parse(JSON.stringify(localConfig.value)));
    }
  }
});

// 监听排序更新时间戳变化，同步更新本地排序
watch(() => linkedSortingStore.updateTimestamp, (timestamp) => {
  if (timestamp && linkedSortingStore.enabled && localConfig.value.columns?.length) {
    console.log('表格配置检测到排序更新:', timestamp);
    // 应用最新的排序顺序
    const sortedColumns = linkedSortingStore.sortItemsByMap(localConfig.value.columns);
    
    // 如果排序后的顺序与当前不同，则更新
    if (JSON.stringify(sortedColumns) !== JSON.stringify(localConfig.value.columns)) {
      localConfig.value.columns = sortedColumns;
      
      // 通知父组件值已更新
      emit('update:modelValue', JSON.parse(JSON.stringify(localConfig.value)));
    }
  }
});

// 在script标签前的代码已移到接口定义区域

// 初始化本地配置
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    // 直接使用JSON.stringify进行深拷贝，避免引用问题
    localConfig.value = JSON.parse(JSON.stringify(newValue));
    
    // 迁移处理：确保所有按钮都有props.type属性
    if (localConfig.value.actionBar?.buttons?.length) {
      localConfig.value.actionBar.buttons.forEach((button: ActionBarButton) => {
        if (!button.props) {
          button.props = { type: button.type || 'primary' };
        } else if (!button.props.type && button.type) {
          button.props.type = button.type;
        }
      });
    }
    
    // 再次确认columns数组存在
    if (!localConfig.value.columns) {
      localConfig.value.columns = [];
    }
    
    // 确保actionBar配置存在
    if (!localConfig.value.actionBar) {
      localConfig.value.actionBar = { 
        type: 'link', 
        confirmType: 'popconfirm', 
        width: 200, 
        buttons: [] 
      };
    } else if (!localConfig.value.actionBar.buttons) {
      localConfig.value.actionBar.buttons = [];
    }
    
    updateSourceCode();
  }
}, { immediate: true, deep: true });

/**
 * 配置变更处理
 */
function handleConfigChange() {
  // 在触发更新前先将新值保存为字符串，方便后续比较
  const newConfig = JSON.parse(JSON.stringify(localConfig.value));
  emit('update:modelValue', newConfig);
  updateSourceCode();
}

/**
 * 源码变化处理
 */
function handleSourceChange() {
  try {
    const parsed = JSON.parse(sourceCode.value);
    localConfig.value = parsed;
    emit('update:modelValue', parsed);
  } catch (error) {
    console.error('JSON格式错误:', error);
  }
}

/**
 * 格式化源码
 */
function handleFormatSource() {
  try {
    const parsed = JSON.parse(sourceCode.value);
    sourceCode.value = JSON.stringify(parsed, null, 2);
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化');
    console.error('JSON格式错误:', error);
  }
}

/**
 * 添加操作栏按钮
 */
function addActionBarButton() {
  if (!localConfig.value.actionBar) {
    localConfig.value.actionBar = {
      type: 'button',
      confirmType: 'popconfirm',
      width: 100,
      buttons: []
    };
  }
  
  if (!localConfig.value.actionBar.buttons) {
    localConfig.value.actionBar.buttons = [];
  }
  
  const newButton: ActionBarButton = {
    text: '操作',
    code: 'action' + (localConfig.value.actionBar.buttons.length + 1),
    icon: '',
    type: 'primary', // 保留原属性以便兼容旧代码
    props: {
      type: 'primary'
    },
    confirm: false, // 默认关闭确认
    condition: ''
  };
  
  localConfig.value.actionBar.buttons.push(newButton);
  handleConfigChange();
}

/**
 * 删除操作栏按钮
 */
function removeActionBarButton(index: number) {
  if (localConfig.value.actionBar?.buttons && index >= 0 && index < localConfig.value.actionBar.buttons.length) {
    localConfig.value.actionBar.buttons.splice(index, 1);
    handleConfigChange();
  }
}

/**
 * 更新分页配置
 */
 function updatePagination(key: 'pageSize', value: number): void;
function updatePagination(key: 'pageSizes', value: number[]): void;
function updatePagination(key: 'pageSize' | 'pageSizes', value: number | number[]): void {
  if (!localConfig.value.pagination) {
    localConfig.value.pagination = {
      pageSize: 10,
      pageSizes: [10, 20, 50, 100]
    };
  }
  
  // 添加类型断言确保类型安全
  (localConfig.value.pagination[key] as any) = value;
  handleConfigChange();
}

/**
 * 获取操作栏属性
 */
function getActionBarProp(prop: string): any {
  if (!localConfig.value.actionBar) return undefined;
  return (localConfig.value.actionBar as Record<string, any>)[prop];
}

/**
 * 更新操作栏属性
 */
function updateActionBarProp(prop: string, value: any) {
  if (!localConfig.value.actionBar) {
    localConfig.value.actionBar = {};
  }
  (localConfig.value.actionBar as Record<string, any>)[prop] = value;
  handleConfigChange();
}

/**
 * 获取确认消息
 */
function getConfirmMessage(element: any): string {
  if (typeof element.confirm === 'object' && element.confirm.message) {
    return element.confirm.message;
  }
  return '确定执行此操作吗？';
}

/**
 * 更新确认消息
 */
function updateConfirmMessage(element: any, val: string): void {
  if (typeof element.confirm !== 'object') {
    element.confirm = { message: val };
  } else {
    element.confirm.message = val;
  }
  handleConfigChange();
}

/**
 * 处理确认开关状态变化
 */
function handleConfirmChange(element: any, val: boolean): void {
  // 如果开启确认，将confirm转换为对象格式
  if (val) {
    element.confirm = {
      message: '确定执行此操作吗？'
    };
  } else {
    // 如果关闭确认，将confirm设置为false
    element.confirm = false;
  }
  
  handleConfigChange();
}

/**
 * 拖拽排序后的处理
 */
function handleDragEnd(event: any) {
  console.log('表格列拖拽事件:', event);
  
  // 如果关联排序开启，更新排序映射
  if (linkedSortingStore.enabled && localConfig.value.columns?.length) {
    // 通知其他编辑器排序变化
    linkedSortingStore.updateSortOrder(localConfig.value.columns);
  }
  
  // 进行常规配置更新
  emit('update:modelValue', JSON.parse(JSON.stringify(localConfig.value)));
}

/**
 * 更新源码表示
 */
function updateSourceCode() {
  sourceCode.value = JSON.stringify(localConfig.value, null, 2);
}
</script>

<style scoped>
.table-config-editor {
  width: 100%;
}

.json-viewer-container {
  height: 100%;
  overflow: auto;
  max-height: 500px;
  text-align: left;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  width: 100%;
}

.text-left :deep(.vjs-tree),
.text-left :deep(.vjs-tree *) {
  text-align: left !important;
}

.divider-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.draggable-container {
  margin-top: 10px;
  border: 1px dashed #ccc;
  padding: 10px;
  border-radius: 4px;
}

.drag-tip {
  display: none;
}

.button-item, .column-item {
  margin-bottom: 15px;
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  width: 100%; /* 确保宽度为100% */
  box-sizing: border-box; /* 确保padding不会增加宽度 */
  min-width: 0; /* 防止内容溢出 */
}

.button-item:hover, .column-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.option-item {
  margin: 10px 0;
  padding: 10px;
  background: #f0f9eb;
  border-radius: 4px;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.options-section {
  margin-top: 10px;
  padding: 10px;
  border: 0px dashed #e6a23c;
  border-radius: 4px;
  width: 100%;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  width: 100%;
}

.toggle-options-btn {
  margin-left: 10px;
}

.grid-container {
  display: grid !important; /* 确保grid显示不被覆盖 */
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important; /* 实现每行动态显示个数 */
  gap: 15px;
  width: 100%;
  margin: 0;
  padding: 0;
}

.source-code-section {
  margin-top: 20px;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.action-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
</style>
