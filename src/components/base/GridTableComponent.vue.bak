<!--
  @description GridTableComponent - 网格系统中的表格组件
  <AUTHOR> AI
  @date 2025-04-11
-->
<template>
  <div class="grid-table-component">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    <div v-else>
      <el-table
        :data="tableData"
        border
        stripe
        size="small"
        v-loading="loading"
        :height="tableHeight"
        :row-key="config.rowKey || 'id'"
        @selection-change="handleSelectionChange"
      >
        <!-- 选择列 -->
        <el-table-column
          v-if="config.showSelection"
          type="selection"
          width="55"
          align="center"
          fixed="left"
        />
        
        <!-- 序号列 -->
        <el-table-column
          v-if="config.showIndex"
          type="index"
          width="60"
          label="序号"
          align="center"
          fixed="left"
        />
        
        <!-- 动态列 -->
        <template v-for="column in config.columns" :key="column.prop">
          <el-table-column
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :fixed="column.fixed"
            :align="column.align || 'left'"
            :sortable="(column as any).sortable ? true : false"
          >
            <template #default="scope">
              <template v-if="column.valueType === 'select' || column.valueType === 'radio'">
                <el-tag
                  :type="getTagType(column, scope.row[column.prop])"
                  size="small"
                >
                  {{ getDisplayText(column, scope.row[column.prop]) }}
                </el-tag>
              </template>
              
              <template v-else-if="column.valueType === 'img'">
                <el-image
                  style="width: 50px; height: 50px"
                  :src="scope.row[column.prop]"
                  :preview-src-list="[scope.row[column.prop]]"
                  fit="cover"
                />
              </template>
              
              <template v-else-if="column.valueType === 'date'">
                {{ formatDate(scope.row[column.prop]) }}
              </template>
              
              <template v-else-if="column.valueType === 'datetime'">
                {{ formatDatetime(scope.row[column.prop]) }}
              </template>
              
              <template v-else-if="column.valueType === 'price'">
                {{ formatPrice(scope.row[column.prop]) }}
              </template>
              
              <template v-else>
                {{ scope.row[column.prop] }}
              </template>
            </template>
          </el-table-column>
        </template>
        
        <!-- 操作列 -->
        <el-table-column
          v-if="config.showActions && config.actionBar"
          :label="config.actionBar.title || '操作'"
          :width="config.actionBar.width || 200"
          fixed="right"
          align="center"
        >
          <template #default="scope">
            <div class="action-buttons">
              <template v-for="button in config.actionBar.buttons" :key="button.action">
                <el-button
                  :type="button.type || 'primary'"
                  :size="button.size || 'small'"
                  @click="handleAction(button.action || '', scope.row)"
                >
                  <el-icon v-if="button.icon">
                    <component :is="button.icon"></component>
                  </el-icon>
                  {{ button.text }}
                </el-button>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div v-if="config.showPagination" class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="config.pagination?.pageSizes || [10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * GridTableComponent 是一个用于在网格中展示表格数据的组件
 * 支持表格列动态配置、分页、操作按钮等功能
 */
import { ref, onMounted, watch, inject } from 'vue';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import type { TableConfig, TableColumn } from '@/types/grid';
import type GridPageStore from '@/components/page/GridPageStore';

// 扩展TableColumn接口，添加sortable属性
// interface ExtendedTableColumn extends TableColumn {
//   sortable?: boolean;
// }

// 组件事件
const emit = defineEmits(['refresh', 'action']);

// 组件属性
const props = defineProps({
  /**
   * 表格配置
   */
  config: {
    type: Object as () => TableConfig,
    required: true
  }
});

// 获取GridPageStore实例（通过依赖注入）
const gridPageStore = inject<GridPageStore>('gridPageStore');

// 内部状态
const loading = ref(false);
const tableData = ref<any[]>([]);
const selectedRows = ref<any[]>([]);
const currentPage = ref(1);
const pageSize = ref(props.config.pagination?.pageSize || 10);
const total = ref(0);
const tableHeight = ref('auto');

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
};

// 处理操作按钮点击
const handleAction = (action: string, row: any) => {
  emit('action', { action, row });
};

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  loadData();
};

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadData();
};

// 格式化日期
const formatDate = (date: string | number) => {
  if (!date) return '';
  return dayjs(date).format('YYYY-MM-DD');
};

// 格式化日期时间
const formatDatetime = (datetime: string | number) => {
  if (!datetime) return '';
  return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
};

// 格式化价格
const formatPrice = (price: number) => {
  if (price === undefined || price === null) return '';
  return `¥ ${price.toFixed(2)}`;
};

// 获取标签类型
const getTagType = (column: TableColumn, value: any): string => {
  if (!column.valueEnum || value === undefined || value === null) return '';
  
  const valueStr = String(value);
  const enumItem = column.valueEnum[valueStr];
  
  if (!enumItem) return '';
  
  // 将status映射到element-plus的type
  const statusMap: Record<string, string> = {
    success: 'success',
    info: 'info',
    warning: 'warning',
    danger: 'danger'
  };
  
  return statusMap[enumItem.status || ''] || '';
};

// 获取显示文本
const getDisplayText = (column: TableColumn, value: any): string => {
  if (value === undefined || value === null) return '';
  
  const valueStr = String(value);
  
  // 从valueEnum中获取显示文本
  if (column.valueEnum && column.valueEnum[valueStr]) {
    return column.valueEnum[valueStr].text || valueStr;
  }
  
  // 从options中获取显示文本
  if (column.options) {
    const option = column.options.find(opt => String(opt.value) === valueStr);
    if (option) {
      return option.label || valueStr;
    }
  }
  
  return valueStr;
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  
  try {
    // 判断数据来源
    if (props.config.dataSource === 'static' && props.config.staticData) {
      // 静态数据
      const data = [...(props.config.staticData || [])];
      
      // 处理分页
      if (props.config.showPagination) {
        const start = (currentPage.value - 1) * pageSize.value;
        const end = start + pageSize.value;
        
        total.value = data.length;
        tableData.value = data.slice(start, end);
      } else {
        tableData.value = data;
        total.value = data.length;
      }
    } else if (props.config.dataSource === 'api' && props.config.api) {
      // 从API获取数据
      // 在实际项目中，这里应该调用API服务获取数据
      // 示例代码，实际项目中应替换为真实的API调用
      ElMessage.info('API数据源功能需要实现');
      tableData.value = [];
      total.value = 0;
    } else {
      // URL为空或不存在，尝试从GridPageStore获取数据
      if (gridPageStore) {
        console.log('从GridPageStore获取表格数据');
        const gridData = gridPageStore.getService().data.value;
        const gridTotal = gridPageStore.getService().total.value;
        
        if (gridData && gridData.length > 0) {
          // 使用store中的数据
          if (props.config.showPagination) {
            const start = (currentPage.value - 1) * pageSize.value;
            const end = start + pageSize.value;
            
            // 如果有分页，则需要处理分页逻辑
            total.value = gridTotal || gridData.length;
            // 检查是否需要本地分页，或者服务端已经分页
            if (gridData.length > pageSize.value) {
              // 如果数据量大于页大小，可能需要本地分页
              tableData.value = gridData.slice(start, end);
            } else {
              // 否则直接使用服务端返回的已分页数据
              tableData.value = gridData;
            }
          } else {
            // 不分页直接使用全部数据
            tableData.value = gridData;
            total.value = gridTotal || gridData.length;
          }
          return;
        }
      }
      
      // 如果store中没有数据或store不存在，显示空数据
      console.warn('GridPageStore未提供数据或store不存在');
      tableData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('加载表格数据失败:', error);
    ElMessage.error('加载数据失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 监听配置变化，重新加载数据
watch(() => props.config, () => {
  console.log('表格配置变化:', props.config);
  // 重置分页
  currentPage.value = 1;
  pageSize.value = props.config.pagination?.pageSize || 10;
  
  // 重新加载数据
  loadData();
}, { immediate: true, deep: true });

// 监听GridPageStore中的数据变化，当数据变化时更新表格
watch(() => gridPageStore?.getService().data.value, (newData) => {
  if (newData && (!props.config.dataSource || !props.config.api)) {
    // 如果没有指定数据源或API，且store有数据更新，则刷新表格
    loadData();
  }
}, { deep: true });

// 组件挂载时加载数据
onMounted(() => {
  console.log('表格组件挂载，配置:', props.config);
  loadData();
});

// 向父组件暴露方法
defineExpose({
  refresh: loadData,
  getSelectedRows: () => selectedRows.value,
  clearSelection: () => selectedRows.value = []
});
</script>

<style scoped>
.grid-table-component {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading-container {
  padding: 20px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>
