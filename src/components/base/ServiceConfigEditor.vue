<!-- 
  服务配置编辑器组件
  用于编辑服务配置，包括基础URL、标题、消息配置以及自定义操作等
  2025-04-15 修复：统一数据源为 form，所有操作、渲染、源码编辑全部基于 form，彻底消灭 undefined 渲染报错。
  2025-04-15 修复：draggable 相关逻辑，保证不再报错，兼容空和未定义场景。
  2025-04-23 更新：使用 store 方式直接绑定数据，移除 form 变量，简化数据流。
  ServiceConfigEditor.vue
  服务配置编辑器组件，支持自定义操作的增删改查及拖拽排序
  该文件包含自定义操作(customActions)的相关逻辑，确保 customActions 始终为对象，避免未定义错误
-->
<template>
  <div class="service-config-editor">
    <el-form label-width="120px">
      <el-form-item label="基础URL">
        <el-input v-model="store.gridLayoutStore.structureData.config_content.serviceConfig.baseUrl" />
      </el-form-item>
      <el-form-item label="添加标题">
        <el-input v-model="store.gridLayoutStore.structureData.config_content.serviceConfig.addTitle" />
      </el-form-item>
      <el-form-item label="编辑标题">
        <el-input v-model="store.gridLayoutStore.structureData.config_content.serviceConfig.editTitle" />
      </el-form-item>
      <el-form-item label="查看标题">
        <el-input v-model="store.gridLayoutStore.structureData.config_content.serviceConfig.viewTitle" />
      </el-form-item>
      <el-form-item label="自定义操作">
        <el-button type="primary" @click="addCustomAction">添加操作</el-button>
        <div class="draggable-container" style="width: 100%;">
          <p v-if="customActionKeys.length > 0" class="drag-tip">
            <el-icon><Rank /></el-icon> 拖拽排序
          </p>
          <draggable 
            :list="customActionKeys"
            :item-key="(key: string) => key"
            handle=".drag-handle"
            ghost-class="ghost"
            @change="handleDragEnd"
            class="grid-container"
          >
            <template #item="{element: key}">
              <div v-if="serviceConfig.customActions && serviceConfig.customActions[key]">
                <div class="custom-action-item">
                  <div class="drag-handle">
                    <el-icon><Rank /></el-icon>
                  </div>
                  <el-divider>{{ key }}</el-divider>
                  <el-form-item label="URL">
                    <el-input v-model="serviceConfig.customActions[key].url" />
                  </el-form-item>
                  <el-form-item label="方法">
                    <el-select v-model="serviceConfig.customActions[key].method">
                      <el-option label="GET" value="get" />
                      <el-option label="POST" value="post" />
                      <el-option label="PUT" value="put" />
                      <el-option label="DELETE" value="delete" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="确认消息">
                    <el-input v-model="serviceConfig.customActions[key].confirmMessage" />
                  </el-form-item>
                  <el-form-item label="成功消息">
                    <el-input v-model="serviceConfig.customActions[key].successMessage" />
                  </el-form-item>
                  <el-button type="danger" @click="removeCustomAction(key)">删除</el-button>
                </div>
              </div>
            </template>
          </draggable>
        </div>
      </el-form-item>
      <el-form-item label="网格配置">
        <el-form-item label="列数" v-if="serviceConfig.gridOptions">
          <el-input-number 
            v-model="serviceConfig.gridOptions.column" 
          />
        </el-form-item>
        <el-form-item label="行高" v-if="serviceConfig.gridOptions">
          <el-input-number 
            v-model="serviceConfig.gridOptions.cellHeight" 
          />
        </el-form-item>
        <el-form-item label="间距" v-if="serviceConfig.gridOptions">
          <el-input-number 
            v-model="serviceConfig.gridOptions.margin" 
          />
        </el-form-item>
        <el-form-item label="启用拖拽">
          <el-switch
            v-model="serviceConfig.draggable"
          />
        </el-form-item>
        <el-form-item label="启用调整大小">
          <el-switch
            v-model="serviceConfig.resizable"
          />
        </el-form-item>
      </el-form-item>
      <el-form-item label="消息配置">
        <el-form-item label="添加成功" v-if="serviceConfig.messages">
          <el-input 
            v-model="serviceConfig.messages.addSuccess" 
          />
        </el-form-item>
        <el-form-item label="更新成功" v-if="serviceConfig.messages">
          <el-input 
            v-model="serviceConfig.messages.updateSuccess" 
          />
        </el-form-item>
        <el-form-item label="删除确认" v-if="serviceConfig.messages">
          <el-input 
            v-model="serviceConfig.messages.deleteConfirm" 
          />
        </el-form-item>
        <el-form-item label="删除成功" v-if="serviceConfig.messages">
          <el-input 
            v-model="serviceConfig.messages.deleteSuccess" 
          />
        </el-form-item>
      </el-form-item>
    </el-form>
    <!-- 底部操作按钮区域 -->
    <div class="footer-actions" style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 32px;">
      <el-button type="primary" @click="onSaveConfig">保存</el-button>
      <el-button type="primary" @click="handleRefreshPage">刷新页面</el-button>
    </div>
    
    <!-- JSON可视化预览区 -->
    <div class="json-preview-section">
      <el-divider>
        <div class="divider-actions">
          <el-button link size="small" @click="sourceVisible = !sourceVisible">
            {{ sourceVisible ? '隐藏源码' : '显示源码' }}
            <el-icon>
              <component :is="sourceVisible ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
          </el-button>
          <el-button type="warning" @click="handleFormatSource" v-if="sourceVisible">格式化</el-button>
        </div>
      </el-divider>
      
      <!-- JSON预览 -->
      <el-row :gutter="20">
        <!-- 源码编辑区 -->
        <el-col :span="sourceVisible ? 12 : 0" v-if="sourceVisible">
          <el-input
            v-model="sourceCode"
            type="textarea"
            :rows="10"
            @input="handleSourceChange"
            placeholder="请输入Service配置JSON"
          />
        </el-col>
        <!-- JSON预览区 -->
        <el-col :span="sourceVisible ? 12 : 24">
          <div class="json-viewer-container">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>JSON可视化预览</span>
                </div>
              </template>
              <JsonViewer 
                :value="parsedSourceJson" 
                :expandDepth="3" 
                copyable 
                sort 
                boxed 
                theme="light"
                class="text-left"
              />
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
// ServiceConfigEditor.vue
// 该组件用于编辑服务配置，支持基础信息、消息、表格参数等配置项，参数变更时会合并默认值并保证结构完整。
import { JsonViewer } from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/index.css';
// 2025-04-15 修复：统一数据源为 form，所有操作、渲染、源码编辑全部基于 form，彻底消灭 undefined 渲染报错。
// 2025-04-15 修复：draggable 相关逻辑，保证不再报错，兼容空和未定义场景。
// 2025-04-23 更新：使用 store 方式直接绑定数据，移除 form 变量，简化数据流。
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import draggable from 'vuedraggable';
import { Rank } from '@element-plus/icons-vue';

interface Message {
  addSuccess?: string;
  updateSuccess?: string;
  deleteConfirm?: string;
  deleteSuccess?: string;
}

interface CustomAction {
  url: string;
  method: string;
  confirmMessage?: string;
  successMessage?: string;
}

interface ServiceConfig {
  baseUrl: string;
  addTitle?: string;
  editTitle?: string;
  viewTitle?: string;
  messages?: Message;
  gridOptions?: {
    column: number;
    cellHeight: number;
    margin: number;
  };
  customActions?: Record<string, CustomAction>;
  draggable?: boolean;
  resizable?: boolean;
  [key: string]: any;
}

const props = defineProps({
  modelValue: {
    type: Object as () => ServiceConfig,
    default: () => ({})
  },
  store: {
    type: Object,
    required: true
  }
});

const store = props.store;

// 便捷访问 serviceConfig
const serviceConfig = computed(() => {
  return store.gridLayoutStore.structureData.config_content.serviceConfig || {};
});

const emit = defineEmits(['update:modelValue', 'refresh']);

// 默认值配置，用于初始化结构
const defaultConfig: ServiceConfig = {
  baseUrl: '',
  addTitle: '',
  editTitle: '',
  messages: {
    addSuccess: '添加成功',
    updateSuccess: '更新成功',
    deleteConfirm: '确认删除吗？',
    deleteSuccess: '删除成功'
  },
  gridOptions: {
    column: 12,
    cellHeight: 80,
    margin: 1
  },
  customActions: {},
  draggable: false,
  resizable: false
};

/**
 * 确保配置完整性，初始化时检查并补充默认结构
 */
function ensureConfigIntegrity() {
  // 确保 messages 存在
  if (!serviceConfig.value.messages) {
    serviceConfig.value.messages = { ...defaultConfig.messages };
  }
  
  // 确保 gridOptions 存在
  if (!serviceConfig.value.gridOptions) {
    serviceConfig.value.gridOptions = { ...defaultConfig.gridOptions };
  }
  
  // 确保 customActions 存在
  if (!serviceConfig.value.customActions) {
    serviceConfig.value.customActions = {};
  }
}

// 组件加载时确保配置完整性
ensureConfigIntegrity();

/**
 * 点击保存按钮时同步配置
 */
function onSaveConfig() {
  // 同步数据已经自动完成（通过直接绑定到store）
  store.saveConfig();
  ElMessage.success('配置已保存');
}

// 源码显示状态，默认不显示
const sourceVisible = ref(false);
const sourceCode = ref('{}');

// JSON解析结果用于可视化显示
const parsedSourceJson = computed(() => {
  try {
    return JSON.parse(sourceCode.value);
  } catch (error) {
    return { error: '无效的JSON格式' };
  }
});

// 初始化源码编辑区内容并监听变化
watch(() => serviceConfig.value, (newValue) => {
  if (newValue) {
    updateSourceCode();
  }
}, { immediate: true, deep: true });

// 更新源码表示
function updateSourceCode() {
  sourceCode.value = JSON.stringify(serviceConfig.value, null, 2);
}

// 源码变化处理
function handleSourceChange() {
  try {
    const parsed = JSON.parse(sourceCode.value);
    // 更新 serviceConfig
    Object.assign(serviceConfig.value, parsed);
    // 确保结构完整
    ensureConfigIntegrity();
  } catch (error) {
    console.error('JSON格式错误:', error);
  }
}

// 格式化源码
function handleFormatSource() {
  try {
    const parsed = JSON.parse(sourceCode.value);
    sourceCode.value = JSON.stringify(parsed, null, 2);
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化');
    console.error('JSON格式错误:', error);
  }
}

// 刷新页面
function handleRefreshPage() {
  emit('refresh');
}

/**
 * 添加自定义操作
 */
function addCustomAction() {
  if (!serviceConfig.value.customActions || typeof serviceConfig.value.customActions !== 'object') {
    serviceConfig.value.customActions = {};
  }
  const actionKey = `action${Object.keys(serviceConfig.value.customActions).length + 1}`;
  serviceConfig.value.customActions[actionKey] = {
    url: '',
    method: 'get',
    confirmMessage: '',
    successMessage: ''
  };
}

/**
 * 删除自定义操作
 */
function removeCustomAction(key: string) {
  if (!serviceConfig.value.customActions || typeof serviceConfig.value.customActions !== 'object') {
    serviceConfig.value.customActions = {};
  }
  if (key in serviceConfig.value.customActions) {
    const actions = { ...serviceConfig.value.customActions };
    delete actions[key];
    serviceConfig.value.customActions = actions;
  }
}

/**
 * 拖拽排序后的处理
 * 这里只对 key 顺序做重排，保持响应式
 */
function handleDragEnd(_evt: any) {
  if (!serviceConfig.value.customActions || typeof serviceConfig.value.customActions !== 'object') {
    serviceConfig.value.customActions = {};
  }
  if (!serviceConfig.value.customActions) return;
  const newKeys = [...customActionKeys.value];
  const newActions: Record<string, CustomAction> = {};
  newKeys.forEach(key => {
    if (!serviceConfig.value.customActions) return;
    newActions[key] = serviceConfig.value.customActions[key];
  });
  serviceConfig.value.customActions = newActions;
}

// 计算 customActions 的 key 列表，保证响应式
const customActionKeys = computed(() => {
  if (!serviceConfig.value.customActions || typeof serviceConfig.value.customActions !== 'object') return [];
  return Object.keys(serviceConfig.value.customActions);
});
</script>

<style scoped>
.service-config-editor {
  width: 100%;
}

.draggable-container {
  margin-top: 10px;
  border: 1px dashed #ccc;
  padding: 10px;
  border-radius: 4px;
}

.drag-tip {
  display: none;
  color: #909399;
  font-size: 14px;
  margin-bottom: 10px;
}

.custom-action-item {
  margin-bottom: 15px;
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
  min-width: 0;
}

.custom-action-item:hover {
  background-color: #f0f9ff;
}

.grid-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.drag-handle {
  cursor: move;
  position: absolute;
  top: 10px;
  right: 10px;
}

.drag-tip {
  color: #909399;
  margin-top: 0;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.json-viewer-container {
  height: 100%;
  overflow: auto;
  max-height: 500px;
  text-align: left;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  width: 100%;
}

.text-left :deep(.vjs-tree),
.text-left :deep(.vjs-tree *) {
  text-align: left !important;
}

.divider-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}
</style>
