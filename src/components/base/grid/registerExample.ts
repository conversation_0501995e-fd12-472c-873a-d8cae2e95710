// src/components/base/grid/registerExample.ts
/**
 * @description 组件注册示例
 * <AUTHOR> AI
 * @date 2025-04-24
 * @功能说明：
 *   1. 展示如何注册自定义组件到GridItemComponent系统
 *   2. 说明如何处理组件配置和事件
 */

import { defineAsyncComponent } from 'vue';
import { registerCustomComponent } from './helpers';

/**
 * 注册自定义组件示例
 * 此函数展示如何向系统注册新的组件类型
 */
export function registerCustomComponents() {
  // 注册自定义图表组件
  registerCustomComponent(
    // 组件类型名称
    'custom-chart',
    // 异步加载组件
    defineAsyncComponent(() => import('@/components/business/CustomChartComponent.vue')),
    // 配置预处理器
    (config) => {
      const processed = { ...config };
      // 设置默认主题和尺寸
      processed.theme = processed.theme || 'light';
      processed.height = processed.height || 300;
      return processed;
    },
    // 事件处理器
    {
      'chart-click': (params: any) => {
        console.log('图表点击事件', params);
        return params;
      },
      'legend-select-changed': (params: any) => {
        console.log('图例选择变化', params);
        return params;
      }
    }
  );

  // 注册自定义数据看板组件
  registerCustomComponent(
    // 组件类型名称
    'dashboard',
    // 异步加载组件
    defineAsyncComponent(() => import('@/components/business/DashboardComponent.vue')),
    // 配置预处理器
    (config) => {
      const processed = { ...config };
      // 设置默认刷新间隔
      processed.refreshInterval = processed.refreshInterval || 30000;
      return processed;
    },
    // 事件处理器
    {
      'refresh': () => {
        console.log('看板刷新');
      },
      'data-updated': (data: any) => {
        console.log('看板数据更新', data);
        return data;
      }
    }
  );

  console.log('自定义组件注册完成');
}

/**
 * 使用示例：
 * 
 * 在应用入口文件（如main.ts）中引入并调用：
 * 
 * ```typescript
 * import { registerCustomComponents } from '@/components/base/grid/registerExample';
 * 
 * // 注册自定义组件
 * registerCustomComponents();
 * ```
 * 
 * 然后在GridItemComponent中使用：
 * 
 * ```html
 * <GridItemComponent
 *   title="自定义图表"
 *   :content="{
 *     type: 'custom-chart',
 *     data: chartData,
 *     options: chartOptions
 *   }"
 * />
 * ```
 */
