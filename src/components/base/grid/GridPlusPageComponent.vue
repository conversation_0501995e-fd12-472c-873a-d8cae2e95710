<!--
  @description GridPlusPageComponent - 基于PlusPage的网格页面组件
  <AUTHOR>
  @date 2025-04-25
  @功能说明：
    1. 基于plus-pro-components的PlusPage组件，提供易用的表格页面功能
    2. 与gridLayoutStore紧密集成，支持从store获取结构数据
    3. 提供完整的表格页面功能，包括搜索、分页、操作按钮等
    4. 支持表格数据管理、表单弹窗、批量操作等
    5. 适配网格布局系统
  @使用方法：
    通过向组件传入config配置来定义表格的行为和外观
    也可以通过store属性传递gridLayoutStore实例
-->
<template>
  <div class="grid-plus-page-component">
    <PlusPage
      ref="plusPageRef"
      :key="componentStore.tableDataVersion.value+'page'"
      :request="processedRequest"
      :columns="processedColumns"
      :params="mergedParams"
      :search="processedSearch"
      :table="processedTable"
      :pagination="pagination"
      :isCard="config.isCard !== false"
      :defaultPageInfo="defaultPageInfo"
      :postData="config.postData"
      @search="handleSearchEvent"
      @reset="handleResetEvent"
      @requestError="handleRequestError"
      @requestComplete="handleRequestComplete"
      @paginationChange="handlePaginationChange"
      @selection-change="handleSelectionChange"
    >
      <template #table-title>
        <el-row class="button-row">
          <el-button type="primary" :icon="Plus" @click="handleCreate"> 添加 </el-button>
          <el-button v-if="config.batchDelete" :disabled="selectedRows.length === 0" :icon="Delete" type="danger" @click="handleBatchDelete"> 批量删除 </el-button>
        </el-row>
      </template>
      <!-- 透传所有插槽 -->
      <template v-for="(_, name) in $slots" v-slot:[name]="slotData">
        <slot :name="name" v-bind="slotData"></slot>
      </template>
    </PlusPage>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, inject } from 'vue';
import { PlusPage } from 'plus-pro-components';
import type { GridPlusPageConfig } from './types';
import GridLayoutService from '@/components/page/gridLayoutService';
import { getGridComponentStore } from '@/components/page/gridComponentStore';
import type GridLayoutStoreType from '@/components/page/gridLayoutStore';
import { Plus, Delete } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// 组件属性
const props = defineProps<{
  config: GridPlusPageConfig;
  store?: any; // 接收来自 GridItemComponent 传递的 store
}>();

const emit = defineEmits(['search', 'reset', 'requestError', 'requestComplete', 'paginationChange', 'settings', 'action']);

// 注入页面服务
// 提供默认值null，避免注入失败时报错
const pageService = inject<GridLayoutService>('pageService');

// 注入 gridLayoutStore，并指定类型
const gridLayoutStore = inject<GridLayoutStoreType>('gridLayoutStore');
// 获取全局单例
const componentStore = getGridComponentStore();
// PlusPage 组件实例引用
const plusPageRef = ref();

// 选中行数据
const selectedRows = ref<any[]>([]);

/**
 * 处理请求函数
 * 优先使用config中的request，如果没有则根据store和dataSource构造
 */
const processedRequest = computed(() => {
  console.log('[page api] GridPlusPageComponent] processedRequest', props.config.api, props.config.dataSource);

  // 优先使用 config 中的 request
  if (props.config.api) {
    console.log('[page api] GridPlusPageComponent] processedRequest', props.config.api);
    if (!gridLayoutStore) {
      console.error('未找到 gridLayoutStore，请检查注入');
      // 返回一个默认的请求函数而不是undefined
      return async (_params: any) => ({ data: [], total: 0, success: false });
    }
    return async (params: any) => {
      try {
        const response = await componentStore.fetchDataByApi(params);
        return response;
      } catch (error) {
        console.error('请求失败:', error);
        return {
          data: [],
          total: 0,
          success: false
        };
      }
    };
  }

  // 如果有 store 且 dataSource 为 'api'，则使用 store 的 service.getList
  if (props.store) {
    return async (params: any) => {
      try {
        // 优先使用 props.store
        let service;
        
        if (props.store.getService) {
          // 如果是 GridLayoutStore 实例，使用 getService() 方法获取 service
          service = props.store.getService();
        } else if (props.store.service) {
          // 如果直接暴露了 service 属性
          service = props.store.service;
        }
        
        // 如果 props.store 没有 service，则尝试使用注入的 pageService
        if (!service && pageService) {
          service = pageService;
        }
        
        // 如果 props.store 和 pageService 都没有，则尝试使用注入的 gridLayoutStore
        if (!service && gridLayoutStore) {
          service = gridLayoutStore.getService();
        }
        
        if (!service) {
          console.error('未找到可用的 service，请检查 store 或 注入');
          return { data: [], total: 0, success: false };
        }
        
        const response = await service.fetchList(params);
        // 适配 PlusPage 期望的响应格式
        return {
          data: response.list || [],
          total: response.total || 0,
          success: response.code === 0
        };
      } catch (error) {
        console.error('请求失败:', error);
        return {
          data: [],
          total: 0,
          success: false
        };
      }
    };
  }

  // 如果是静态数据源
  if (props.config.dataSource === 'static' && props.config.staticData) {
    return async (params: any) => {
      const { current = 1, pageSize = 10, ...filters } = params;
      
      // 筛选数据
      let filteredData = [...props.config.staticData || []];
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
          filteredData = filteredData.filter(item => 
            String(item[key]).toLowerCase().includes(String(filters[key]).toLowerCase())
          );
        }
      });
      
      // 分页
      const start = (current - 1) * pageSize;
      const end = start + pageSize;
      const paginatedData = filteredData.slice(start, end);
      
      return {
        data: paginatedData,
        total: filteredData.length,
        success: true
      };
    };
  }

  // 默认返回空函数
  return async (_params: any) => ({ data: [], total: 0, success: true });
});

/**
 * 处理列配置
 * 优先使用config中的columns，如没有则从store中获取
 */
const processedColumns = computed(() => {
  // 优先使用 config 中的 columns
  if (props.config.columns && props.config.columns.length > 0) {
    return props.config.columns;
  }

  // 如果有 props.store，则尝试从 props.store 中获取
  if (props.store && props.store.columns && props.store.columns.length > 0) {
    return props.store.columns;
  }
  
  // GridLayoutStore 不直接暴露 columns，所以这里不再尝试从 gridLayoutStore 获取
  // 如果需要，可以从 structureData 中获取，但需要确定具体路径

  return [];
});

/**
 * 合并参数
 * 包括config中的params和store中的相关参数
 */
const mergedParams = computed(() => {
  const baseParams = props.config.params || {};
  
  // 优先使用 props.store 中的参数
  if (props.store && props.store.structureData) {
    const structureData = props.store.structureData;
    // 根据 structureData 的类型进行不同处理
    if (structureData.value && structureData.value.config_content) {
      // 如果是 ref 类型
      const serviceConfig = structureData.value.config_content.serviceConfig || {};
      if (serviceConfig.defaultParams) {
        return { ...serviceConfig.defaultParams, ...baseParams };
      }
    } else if (structureData.config_content) {
      // 如果是普通对象
      const serviceConfig = structureData.config_content.serviceConfig || {};
      if (serviceConfig.defaultParams) {
        return { ...serviceConfig.defaultParams, ...baseParams };
      }
    }
  }
  
  // 如果 props.store 中没有参数，尝试从注入的 gridLayoutStore 中获取
  if (gridLayoutStore && gridLayoutStore.structureData && gridLayoutStore.structureData.value) {
    const serviceConfig = gridLayoutStore.structureData.value.config_content?.serviceConfig || {};
    if (serviceConfig.defaultParams) {
      return { ...serviceConfig.defaultParams, ...baseParams };
    }
  }
  
  return baseParams;
});

/**
 * 处理搜索配置
 */
const processedSearch = computed(() => {
  return { 
    labelWidth: '100px', 
    colProps: { span: 8 },
    ...props.config.search
  };
});

/**
 * 处理表格配置
 * 包括表格属性和操作按钮
 */
const processedTable = computed(() => {
  console.log('[GridPlusPageComponent] processedTable', props.config);
  const tableConfig: any = {
    isSelection: props.config.table?.isSelection !== false,
    ...props.config.table
  };

  // 处理操作按钮
  if (props.config.actionBar) {
    tableConfig.actionBar = props.config.actionBar;
    
    // 添加点击操作按钮的事件处理
    if (!tableConfig.onClickAction) {
      tableConfig.onClickAction = (params: any) => {
        handleClickAction(params);
      };
    }
  }

  if (props.config.batchDelete) {
    tableConfig.isSelection = true;
    tableConfig.onSelectionChange = handleSelectionChange;
  }
  
  // 如果store中有表格配置，合并它们
  if (props.store && props.store.pageStructure && props.store.pageStructure.table) {
    // 低优先级store配置不覆盖高优先级config配置
    return { ...props.store.pageStructure.table, ...tableConfig };
  }

  return tableConfig;
});

/**
 * 处理按钮点击事件
 * @param data 包含行数据、索引和按钮代码的对象
 */
const handleClickAction = (data: any) => {
  console.log('[GridPlusPageComponent] 按钮点击:', data);
  // 直接调用componentStore的行按钮点击方法
  componentStore.handleRowButtonClick(data);
};

/**
 * 处理表格选择变化事件
 * @param selection 选中的行数据数组
 */
const handleSelectionChange = (selection: any[]) => {
  console.log('[GridPlusPageComponent] 选择变化', selection);
  selectedRows.value = selection;
};

/**
 * 处理创建按钮点击事件
 * 显示创建模态框
 */
const handleCreate = () => {
  console.log('[GridPlusPageComponent] 添加');
  componentStore.showModal('create');
};

/**
 * 处理批量删除按钮点击事件
 * 确认后批量删除选中项
 */
const handleBatchDelete = () => {
  if (!selectedRows.value || selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的项');
    return;
  }

  ElMessageBox.confirm('确认要批量删除选中的项吗？', '批量删除', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 获取所有选中行的ID
      const ids = selectedRows.value.map(row => row.id);
      
      // 获取服务实例
      let service;
      if (props.store && props.store.getService) {
        service = props.store.getService();
      } else if (props.store && props.store.service) {
        service = props.store.service;
      } else if (pageService) {
        service = pageService;
      } else if (gridLayoutStore) {
        service = gridLayoutStore.getService();
      }
      
      if (!service) {
        ElMessage.error('未找到可用的服务实例');
        return;
      }
      
      // 调用批量删除方法
      await service.batchDeleteItems(ids);
      ElMessage.success('批量删除成功');
      
      // 刷新表格数据
      plusPageRef.value?.getList();
      
      // 清空选中项
      selectedRows.value = [];
    } catch (error) {
      console.error('批量删除失败:', error);
      ElMessage.error(`批量删除失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }).catch(() => {
    // 用户取消删除操作
  });
};

/**
 * 处理分页配置
 */
const pagination = computed(() => {
  if (props.config.pagination === false) return false;
  
  // 优先使用 config 的分页配置
  if (typeof props.config.pagination === 'object') {
    return props.config.pagination;
  }
  
  // 尝试从 store 获取分页配置
  if (props.store && props.store.pageStructure && props.store.pageStructure.pagination) {
    return props.store.pageStructure.pagination;
  }
  
  // 默认启用分页
  return true;
});

/**
 * 默认分页信息
 */
const defaultPageInfo = computed(() => {
  // 优先使用 config 中的默认分页信息
  if (props.config.defaultPageInfo) {
    return props.config.defaultPageInfo;
  }
  
  // 尝试从 store 获取默认分页信息
  if (props.store && 
      props.store.pageStructure && 
      props.store.pageStructure.pagination && 
      props.store.pageStructure.pagination.defaultPageInfo) {
    return props.store.pageStructure.pagination.defaultPageInfo;
  }
  
  // 默认值
  return { page: 1, pageSize: 10 };
});

/**
 * 事件处理函数
 */
const handleSearchEvent = (values: any) => {
  emit('search', values);
};

const handleResetEvent = () => {
  emit('reset');
};

const handleRequestError = (error: any) => {
  emit('requestError', error);
};

const handleRequestComplete = (data: any) => {
  emit('requestComplete', data);
};

const handlePaginationChange = (pagination: any) => {
  emit('paginationChange', pagination);
};

// 对外暴露方法
defineExpose({
  // 转发 PlusPage 的方法
  setSearchFieldsValue: (values: Record<string, any>) => {
    plusPageRef.value?.setSearchFieldsValue(values);
  },
  getSearchFieldsValue: (key?: string) => {
    return plusPageRef.value?.getSearchFieldsValue(key);
  },
  clearSearchFieldsValue: () => {
    plusPageRef.value?.clearSearchFieldsValue();
  },
  getList: () => {
    plusPageRef.value?.getList();
  },
  // 为了向后兼容，保留原有方法别名
  refresh: () => {
    plusPageRef.value?.getList();
  },
  handleSearch: () => {
    plusPageRef.value?.getSearchFieldsValue && 
    plusPageRef.value?.setSearchFieldsValue(plusPageRef.value?.getSearchFieldsValue());
    plusPageRef.value?.getList();
  },
  handleReset: () => {
    plusPageRef.value?.clearSearchFieldsValue();
    plusPageRef.value?.getList();
  },
  // 获取内部PlusPage实例
  getPlusPageInstance: () => plusPageRef.value
});

// 监听配置变化
watch(() => componentStore.tableDataVersion.value, () => {
  console.log('[GridPlusPageComponent] tableDataVersion changed');
}, { deep: true });

// 监听store变化
// watch(() => props.store, () => {
//   plusPageRef.value?.getList();
// }, { deep: true });

// 组件挂载后初始化
onMounted(() => {
  if (props.config.immediate !== false) {
    plusPageRef.value?.getList();
  }
});
</script>

<style scoped>
.grid-plus-page-component {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
