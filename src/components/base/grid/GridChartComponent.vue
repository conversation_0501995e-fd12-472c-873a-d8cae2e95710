<!--
  @description GridChartComponent - 网格系统中的图表组件
  <AUTHOR> AI
  @date 2025-04-11
-->
<template>
  <div
    class="grid-chart-component"
    :class="{ 'is-loading': loading, 'is-error': error }"
    :style="{ height: '100%'}"
  >
    <div 
      class="chart-container" 
      ref="chartRef"
    ></div>
    <div v-if="loading" class="chart-loading">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      <span>加载中...</span>
    </div>
    <div v-if="error" class="chart-error">
      <el-icon>
        <Warning />
      </el-icon>
      <span>{{ errorMessage || '图表加载失败' }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * GridChartComponent 是一个用于在网格中展示图表的组件
 * 支持多种图表类型，可以通过配置自定义图表样式和数据
 */
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue';
import { ElMessage } from 'element-plus';
import type { ChartConfig } from '@/types/grid';
import * as echarts from 'echarts/core';
import {
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  RadarChart,
  GaugeChart
} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent
} from 'echarts/components';
import { LabelLayout, UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import { Warning, Loading } from '@element-plus/icons-vue';

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  RadarChart,
  GaugeChart
]);

// 组件事件
const emit = defineEmits(['refresh']);

// 组件属性
const props = defineProps({
  /**
   * 图表配置
   */
  config: {
    type: Object as () => ChartConfig,
    required: true
  }
});

// 内部状态
const loading = ref(false);
const error = ref(false);
const errorMessage = ref('');
const chartRef = ref<HTMLElement | null>(null);

// 图表实例
let chartInstance: echarts.ECharts | null = null;

// 重试相关
let retryCount = 0;
const MAX_RETRIES = 5;

// 可见性检查相关
let visibilityCheckCount = 0;
const MAX_VISIBILITY_CHECKS = 3;

// 观察器相关
let resizeObserver: ResizeObserver | null = null;
let mutationObserver: MutationObserver | null = null;
let autoRefreshTimer: ReturnType<typeof setInterval> | null = null;
let observerTimeout: ReturnType<typeof setTimeout> | null = null;

// 监听组件挂载
onMounted(() => {
  console.log('组件挂载完成');
  // 延迟初始化，确保DOM已完全渲染
  setTimeout(() => {
    initChart();
  }, 200);
});

// 监听组件销毁
onBeforeUnmount(() => {
  console.log('组件销毁前清理资源');
  cleanup();
});

// 清理资源
const cleanup = () => {
  // 停止所有观察器
  stopAllObservers();
  
  // 清除定时器
  if (autoRefreshTimer !== null) {
    clearInterval(autoRefreshTimer);
    autoRefreshTimer = null;
  }
  
  // 移除窗口事件监听
  window.removeEventListener('resize', handleResize);
  
  // 销毁图表实例
  if (chartInstance) {
    try {
      chartInstance.dispose();
      chartInstance = null;
    } catch (e) {
      console.error('销毁图表实例出错:', e);
    }
  }
};

// 初始化图表
const initChart = async () => {
  error.value = false;
  errorMessage.value = '';
  
  // 确保DOM已渲染完成
  await nextTick();
  
  // 停止所有观察器
  stopAllObservers();
  
  if (!chartRef.value) {
    error.value = true;
    errorMessage.value = '图表容器元素不存在';
    console.error('图表容器元素不存在');
    return;
  }
  
  try {
    // 检查容器尺寸
    const { clientWidth, clientHeight } = chartRef.value;
    console.log('图表容器尺寸:', clientWidth, clientHeight);
    
    // 如果已有实例，先销毁
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
    
    // 使用延迟等待DOM真正渲染
    setTimeout(async () => {
      if (!chartRef.value) {
        error.value = true;
        errorMessage.value = '图表容器不可用';
        return;
      }
      
      try {
        console.log('准备创建图表实例...');
        // 创建图表实例
        chartInstance = echarts.init(chartRef.value, null, { renderer: 'canvas' });
        console.log('ECharts实例已创建');
        
        // 为图表实例添加唯一标识，方便调试
        (chartInstance as any)._chartId = 'chart-' + Date.now();
        console.log('图表ID:', (chartInstance as any)._chartId);
        
        // 延迟加载数据，确保DOM完全就绪
        loadData();
        
        // 设置自动刷新
        setupAutoRefresh();
        
        // 监听窗口大小变化，调整图表大小
        window.addEventListener('resize', handleResize);
        
        // 监听Grid大小变化
        setupGridSizeObserver();
      } catch (initError: any) {
        error.value = true;
        errorMessage.value = '图表初始化失败';
        console.error('创建图表实例失败:', initError);
      }
    }, 300);
  } catch (err: any) {
    console.error('初始化图表失败:', err);
    errorMessage.value = '初始化图表失败';
    error.value = true;
  }
};

// 加载数据
const loadData = async () => {
  if (!chartInstance) {
    console.error('加载数据失败：图表实例不存在');
    return;
  }
  
  if (!chartRef.value) {
    console.error('加载数据失败：图表容器不存在');
    return;
  }
  
  // 检查容器可见性
  if (chartRef.value.offsetParent === null) {
    // 避免无限递归
    if (visibilityCheckCount >= MAX_VISIBILITY_CHECKS) {
      console.warn(`已达到最大可见性检查次数 ${MAX_VISIBILITY_CHECKS}，将继续尝试渲染图表`);
      // 即使不可见也强制继续
      visibilityCheckCount = 0;
    } else {
      visibilityCheckCount++;
      console.warn(`图表容器当前不可见，尝试强制显示 (尝试 ${visibilityCheckCount}/${MAX_VISIBILITY_CHECKS})`);
      
      // 尝试强制显示
      chartRef.value.style.visibility = 'visible';
      chartRef.value.style.display = 'block';
      chartRef.value.style.width = '100%';
      chartRef.value.style.height = '100%';
      
      // 如果父元素存在，也尝试显示
      if (chartRef.value.parentElement) {
        chartRef.value.parentElement.style.visibility = 'visible';
        chartRef.value.parentElement.style.display = 'block';
        chartRef.value.parentElement.style.width = '100%';
        chartRef.value.parentElement.style.height = '100%';
      }
      
      // 等待DOM更新
      await nextTick();
      
      // 重新检查可见性
      if (chartRef.value.offsetParent === null) {
        console.warn(`无法使图表容器可见，可能是由父级元素控制的 (尝试 ${visibilityCheckCount}/${MAX_VISIBILITY_CHECKS})`);
        
        // 仅在未达到最大检查次数时重试
        if (visibilityCheckCount < MAX_VISIBILITY_CHECKS) {
          setTimeout(() => {
            if (chartInstance && chartRef.value) {
              loadData();
            }
          }, 500);
          return;
        }
      }
    }
  }
  
  // 重置可见性检查计数
  visibilityCheckCount = 0;
  loading.value = true;
  error.value = false;
  
  try {
    let chartData: any = {};
    
    // 调试输出配置信息
    console.log('图表配置:', JSON.stringify(props.config));
    
    // 判断数据来源
    if (props.config.dataSource === 'static' && props.config.data) {
      // 使用静态数据
      chartData = props.config.data;
      console.log('使用静态数据:', JSON.stringify(chartData));
    } else if (props.config.dataSource === 'api' && props.config.api) {
      // 从API获取数据
      // 在实际项目中，这里应该调用API服务获取数据
      // 示例代码，实际项目中应替换为真实的API调用
      console.log('API数据源需要实现');
      ElMessage.info('API数据源功能需要实现');
    } else {
      // 使用默认的示例数据
      console.log('使用默认数据，图表类型:', props.config.chartType || 'line');
      chartData = getDefaultChartData();
      console.log('生成的默认数据:', JSON.stringify(chartData));
    }
    
    // 生成图表选项
    const options = generateChartOptions(chartData);
    console.log('生成的图表选项:', JSON.stringify(options));
    
    // 更新图表
    await nextTick();
    
    // 再次检查图表实例和容器
    if (!chartInstance) {
      console.error('设置选项失败：图表实例不存在');
      return;
    }
    
    if (!chartRef.value) {
      console.error('设置选项失败：图表容器不存在');
      return;
    }
    
    try {
      // 再次验证图表ID，确保是同一个实例
      console.log('正在设置选项的图表ID:', (chartInstance as any)._chartId);
      
      // 清除之前的图表
      chartInstance.clear();
      
      // 设置新的选项
      chartInstance.setOption(options, true);
      console.log('图表选项已设置');
      
      // 强制执行一次重绘，并适应容器大小
      updateChartSize();
      console.log('图表已重绘');
      
      // 成功显示图表
      error.value = false;
      errorMessage.value = '';
    } catch (renderError: any) {
      console.error('设置图表选项失败:', renderError);
      error.value = true;
      errorMessage.value = '图表渲染失败';
      
      // 尝试最后的修复方案：完全重新创建图表实例
      if (chartRef.value) {
        console.log('尝试重新创建图表实例');
        if (chartInstance) {
          chartInstance.dispose();
        }
        
        // 创建新图表实例
        chartInstance = echarts.init(chartRef.value, null, { renderer: 'canvas' });
        chartInstance.setOption(options);
        updateChartSize();
        error.value = false;
        errorMessage.value = '';
      }
    }
  } catch (err: any) {
    console.error('加载图表数据失败:', err);
    ElMessage.error('加载图表数据失败');
    error.value = true;
    errorMessage.value = '加载图表数据失败';
  } finally {
    loading.value = false;
  }
};

// 处理重试
const handleRetry = () => {
  // 检查是否达到最大重试次数
  if (retryCount >= MAX_RETRIES) {
    console.error(`达到最大重试次数 ${MAX_RETRIES}，停止尝试初始化图表`);
    return;
  }
  
  // 增加重试计数
  retryCount++;
  console.log(`正在进行第 ${retryCount} 次重试...`);
  
  // 重新初始化图表
  initChart();
};

// 更新图表尺寸以适应容器
const updateChartSize = () => {
  if (!chartInstance || !chartRef.value) return;
  
  // 获取父容器尺寸
  const container = chartRef.value.parentElement?.parentElement;
  if (container) {
    // 获取Grid容器的实际尺寸
    const gridItem = container.closest('.grid-stack-item');
    if (gridItem) {
      // 获取网格项的内容区域
      const gridContent = gridItem.querySelector('.grid-stack-item-content');
      if (gridContent) {
        const { width, height } = gridContent.getBoundingClientRect();
        console.log('Grid容器尺寸:', width, height);
        
        // 设置图表容器样式
        if (chartRef.value) {
          chartRef.value.style.width = '100%';
          chartRef.value.style.height = '100%';
        }
        
        // 重置图表尺寸
        chartInstance.resize({
          width: 'auto',
          height: 'auto'
        });
      }
    } else {
      // 如果找不到grid-stack-item，使用父容器尺寸
      const { width, height } = container.getBoundingClientRect();
      console.log('父容器尺寸:', width, height);
      
      // 重置图表尺寸
      chartInstance.resize();
    }
  } else {
    // 默认调整尺寸
    chartInstance.resize();
  }
};

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    updateChartSize();
  }
};

// 设置Grid大小变化观察器
const setupGridSizeObserver = () => {
  // 找到Grid容器
  const container = chartRef.value?.parentElement?.parentElement;
  if (!container) return;
  
  // 尝试找到网格项
  const gridItem = container.closest('.grid-stack-item');
  if (gridItem) {
    console.log('找到Grid容器，设置大小监听');
    
    // 监听网格项尺寸变化
    const gridResizeObserver = new ResizeObserver(() => {
      console.log('Grid容器尺寸变化');
      if (chartInstance) {
        updateChartSize();
      }
    });
    
    // 开始观察网格项
    gridResizeObserver.observe(gridItem);
    
    // 监听网格项的内容区域变化
    const gridContent = gridItem.querySelector('.grid-stack-item-content');
    if (gridContent) {
      gridResizeObserver.observe(gridContent);
    }
  }
};

// 停止所有观察器
const stopAllObservers = () => {
  if (observerTimeout !== null) {
    clearTimeout(observerTimeout);
    observerTimeout = null;
  }
  
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
  
  if (mutationObserver) {
    mutationObserver.disconnect();
    mutationObserver = null;
  }
};

// 设置自动刷新
const setupAutoRefresh = () => {
  // 清除现有的定时器
  if (autoRefreshTimer !== null) {
    clearInterval(autoRefreshTimer);
    autoRefreshTimer = null;
  }
  
  // 如果配置了自动刷新间隔，则设置定时器
  if (props.config.refreshInterval && props.config.refreshInterval > 0) {
    autoRefreshTimer = setInterval(() => {
      loadData();
    }, props.config.refreshInterval * 1000);
  }
};

// 根据图表类型生成对应的图表选项
const generateChartOptions = (data: any) => {
  // 如果图表容器尺寸为0，可能会导致渲染问题
  if (chartRef.value && (chartRef.value.clientWidth === 0 || chartRef.value.clientHeight === 0) && !chartInstance) {
    // 图表容器尺寸无效，设置延迟重试（仅在图表未初始化时）
    setTimeout(handleRetry, 500);
    return {};
  }
  
  // 根据图表类型调用不同的选项生成函数
  console.log('生成图表选项，类型:', props.config.chartType);
  switch (props.config.chartType) {
    case 'bar':
      return generateBarChartOptions(data);
    case 'line':
      return generateLineChartOptions(data);
    case 'pie':
      return generatePieChartOptions(data);
    case 'scatter':
      return generateScatterChartOptions(data);
    case 'radar':
      return generateRadarChartOptions(data);
    case 'gauge':
      return generateGaugeChartOptions(data);
    default:
      // 默认返回柱状图
      return generateBarChartOptions(data);
  }
};

// 生成折线图选项
const generateLineChartOptions = (data: any) => {
  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: data.series?.map((item: any) => item.name) || []
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.xAxis?.data || []
    },
    yAxis: {
      type: 'value'
    },
    series: data.series || []
  };
};

// 生成柱状图选项
const generateBarChartOptions = (data: any) => {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: data.series?.map((item: any) => item.name) || []
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.xAxis?.data || []
    },
    yAxis: {
      type: 'value'
    },
    series: data.series || []
  };
};

// 生成饼图选项
const generatePieChartOptions = (data: any) => {
  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: data.series?.[0]?.data?.map((item: any) => item.name) || []
    },
    series: [
      {
        name: data.series?.[0]?.name || '数据',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '15',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data.series?.[0]?.data || []
      }
    ]
  };
};

// 生成散点图选项
const generateScatterChartOptions = (data: any) => {
  return {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      data: data.series?.map((item: any) => item.name) || []
    },
    xAxis: {},
    yAxis: {},
    series: data.series?.map((series: any) => ({
      ...series,
      type: 'scatter'
    })) || []
  };
};

// 生成雷达图选项
const generateRadarChartOptions = (data: any) => {
  return {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      data: data.series?.map((item: any) => item.name) || []
    },
    radar: {
      indicator: data.radar?.indicator || []
    },
    series: data.series?.map((series: any) => ({
      ...series,
      type: 'radar'
    })) || []
  };
};

// 生成仪表盘选项
const generateGaugeChartOptions = (data: any) => {
  return {
    tooltip: {
      formatter: '{a} <br/>{b} : {c}%'
    },
    series: [
      {
        name: data.series?.[0]?.name || '数据',
        type: 'gauge',
        progress: {
          show: true
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}%'
        },
        data: data.series?.[0]?.data || [{ value: 50, name: '完成率' }]
      }
    ]
  };
};

// 获取默认的图表数据
const getDefaultChartData = () => {
  // 根据图表类型返回不同的默认数据
  switch (props.config.chartType) {
    case 'line':
      return {
        xAxis: {
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        series: [
          {
            name: '访问量',
            type: 'line',
            data: [120, 132, 101, 134, 90, 230, 210]
          },
          {
            name: '销售额',
            type: 'line',
            data: [220, 182, 191, 234, 290, 330, 310]
          }
        ]
      };
    case 'bar':
      return {
        xAxis: {
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        series: [
          {
            name: '访问量',
            type: 'bar',
            data: [120, 132, 101, 134, 90, 230, 210]
          },
          {
            name: '销售额',
            type: 'bar',
            data: [220, 182, 191, 234, 290, 330, 310]
          }
        ]
      };
    case 'pie':
      return {
        series: [
          {
            name: '销售额',
            data: [
              { value: 1048, name: '搜索引擎' },
              { value: 735, name: '直接访问' },
              { value: 580, name: '邮件营销' },
              { value: 484, name: '联盟广告' },
              { value: 300, name: '视频广告' }
            ]
          }
        ]
      };
    case 'scatter':
      return {
        series: [
          {
            name: '男性',
            type: 'scatter',
            data: [
              [161.2, 51.6], [167.5, 59.0], [159.5, 49.2], [157.0, 63.0], [155.8, 53.6],
              [170.0, 59.0], [159.1, 47.6], [166.0, 69.8], [176.2, 66.8], [160.2, 75.2]
            ]
          },
          {
            name: '女性',
            type: 'scatter',
            data: [
              [174.0, 65.6], [175.3, 71.8], [193.5, 80.7], [186.5, 72.6], [187.2, 78.8],
              [181.5, 74.8], [184.0, 86.4], [184.5, 78.4], [175.0, 62.0], [184.0, 81.6]
            ]
          }
        ]
      };
    case 'radar':
      return {
        radar: {
          indicator: [
            { name: '销售', max: 6500 },
            { name: '管理', max: 16000 },
            { name: '信息技术', max: 30000 },
            { name: '客服', max: 38000 },
            { name: '研发', max: 52000 },
            { name: '市场', max: 25000 }
          ]
        },
        series: [
          {
            type: 'radar',
            name: '预算 vs 开销',
            data: [
              {
                value: [4200, 3000, 20000, 35000, 50000, 18000],
                name: '预算'
              },
              {
                value: [5000, 14000, 28000, 26000, 42000, 21000],
                name: '开销'
              }
            ]
          }
        ]
      };
    case 'gauge':
      return {
        series: [
          {
            name: '业务指标',
            data: [{ value: 75, name: '完成率' }]
          }
        ]
      };
    default:
      return {};
  }
};

// 监听配置变化
watch(() => props.config, () => {
  // 清理原有资源
  cleanup();
  
  // 重新初始化图表
  nextTick(() => {
    initChart();
  });
}, { deep: true });

// 向父组件暴露方法
defineExpose({
  refresh: loadData
});
</script>

<style scoped>
.grid-chart-component {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: white;
}

.chart-container {
  flex: 1;
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-loading,
.chart-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 20;
}

.chart-loading {
  color: #409eff;
}

.chart-error {
  color: #f56c6c;
}

::v-deep .grid-stack-item .grid-stack-item-content {
  overflow-y: hidden!important;
}
</style>
