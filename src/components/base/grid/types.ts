// src/components/base/grid/types.ts
// 本文件定义了Grid组件内容类型及组件映射，自动同步类型和值

import { defineAsyncComponent } from 'vue';
import type { Component } from 'vue';

/**
 * 组件映射常量，key即为所有可用类型
 * 使用异步组件加载方式提高性能
 */
export const gridComponentMap: Record<string, Component> = {
  table: defineAsyncComponent(() => import('./GridPlusTableComponent.vue')),
  info: defineAsyncComponent(() => import('./GridInfoComponent.vue')),
  chart: defineAsyncComponent(() => import('./GridChartComponent.vue')),
  custom: defineAsyncComponent(() => import('./GridCustomComponent.vue')),
  plustable: defineAsyncComponent(() => import('./GridPlusTableComponent.vue')),
  form: defineAsyncComponent(() => import('./GridPlusFormComponent.vue')),
  search: defineAsyncComponent(() => import('./GridPlusSearchComponent.vue')),
  page: defineAsyncComponent(() => import('./GridPlusPageComponent.vue')),
};

/**
 * Grid组件内容类型，自动从映射中推导
 */
export type GridComponentType = keyof typeof gridComponentMap | 'GridItemComponent';

/**
 * 组件注册机制，用于扩展新的组件类型
 * @param type 组件类型名称
 * @param component 组件定义
 */
export function registerGridComponent(type: string, component: Component): void {
  gridComponentMap[type] = component;
}

/**
 * 验证组件类型是否有效
 * @param type 组件类型
 * @returns 是否为有效的组件类型
 */
export function isValidComponentType(type: string): boolean {
  return type in gridComponentMap;
}

/**
 * 通用网格内容配置接口
 */
export interface GridContentConfig {
  type: GridComponentType;
  config?: Record<string, any>;
  [key: string]: any;
}

/**
 * 按钮配置接口
 */
export interface ActionButton {
  text: string;
  code: string;
  confirm?: boolean;
  props?: Record<string, any>;
  icon?: any;
}

/**
 * 列配置接口
 * 兼容 PlusProComponents 的 PlusColumn 类型
 */
export interface GridPlusColumn {
  // 基础属性
  label: string;
  prop: string;
  valueType?: string;
  
  // 表格样式
  width?: string | number;
  minWidth?: string | number;
  fixed?: boolean | 'left' | 'right';
  align?: 'left' | 'center' | 'right';
  
  // 选项配置(适用于select、tag等)
  options?: {
    label: string;
    value: string | number;
    type?: string;
    color?: string;
  }[];
  
  // 显示控制
  hideInSearch?: boolean;
  hideInTable?: boolean;
  hideInForm?: boolean;
  hideInDetail?: boolean;
  
  // 表单相关
  span?: number;
  tableColumnProps?: Record<string, any>;
  formProps?: Record<string, any>;
  
  // 其他属性
  order?: number;
  [key: string]: any;
}

/**
 * GridPlusPage 组件配置接口
 */
export interface GridPlusPageConfig {
  // 数据源配置
  dataSource: 'static' | 'api';
  staticData?: any[];
  request?: Function;
  api?: string;
  params?: Record<string, any>;
  
  // 列配置
  columns?: GridPlusColumn[];
  rowKey?: string;
  batchDelete?: boolean;
  
  // 搜索配置
  search?: {
    labelWidth?: string;
    colProps?: {
      span?: number;
    };
    buttonColSpan?: number;
    defaultValues?: Record<string, any>;
  };
  showSearch?: boolean;
  
  // 表格配置
  table?: {
    isSelection?: boolean;
    showActions?: boolean;
    actionLabel?: string;
    actionWidth?: number;
    actionFixed?: boolean | 'left' | 'right';
    onClickAction?: (actionType: string, record: any) => void;
    onSelectionChange?: (selection: any[]) => void;
  };
  showToolbar?: boolean;
  
  // 分页配置
  pagination?: {
    pageSizes?: number[];
    layout?: string;
  } | boolean;
  showPagination?: boolean;
  defaultPageInfo?: {
    page: number;
    pageSize: number;
  };
  
  // 操作权限
  allowCreate?: boolean;
  allowUpdate?: boolean;
  allowDelete?: boolean;
  allowBatchDelete?: boolean;
  createButtonText?: string;
  batchDeleteButtonText?: string;
  
  // 表单配置
  form?: {
    labelPosition?: 'top' | 'left' | 'right';
    labelWidth?: string;
    rules?: Record<string, any>;
  };
  dialog?: {
    width?: string;
  };
  
  // 自定义操作按钮
  actionBar?: ActionButton[];
  
  // 卡片模式
  isCard?: boolean;
  
  // 参数处理函数
  postData?: (data: any) => any;
  beforeSearchSubmit?: (params: Record<string, any>) => Record<string, any>;
  
  // 是否立即加载数据
  immediate?: boolean;
}
