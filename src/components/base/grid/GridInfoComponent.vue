<!--
  @description GridInfoComponent - 网格系统中的信息统计卡片组件
  <AUTHOR> AI
  @date 2025-04-11
-->
<template>
  <div class="grid-info-component">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>
    <div v-else class="info-cards">
      <div 
        v-for="(item, index) in config.items" 
        :key="index"
        class="info-card"
      >
        <div class="info-card-content">
          <div class="info-icon" v-if="item.icon">
            <el-icon :class="['icon', item.color]">
              <component :is="item.icon"></component>
            </el-icon>
          </div>
          <div class="info-data">
            <div class="info-value" :class="item.color">
              {{ item.value }}
              <span v-if="item.unit" class="info-unit">{{ item.unit }}</span>
            </div>
            <div class="info-label">{{ item.label }}</div>
            <div v-if="item.trend && item.trend !== 'none'" class="info-trend">
              <el-icon :class="['trend-icon', item.trend === 'up' ? 'up' : 'down']">
                <component :is="item.trend === 'up' ? 'ArrowUp' : 'ArrowDown'"></component>
              </el-icon>
              <span class="trend-value" :class="item.trend === 'up' ? 'up' : 'down'">
                {{ item.percent }}%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * GridInfoComponent 是一个用于在网格中展示信息统计卡片的组件
 * 支持显示多个统计指标，包括数值、趋势等
 */
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
//import { ArrowUp, ArrowDown } from '@element-plus/icons-vue';
import type { InfoConfig } from '@/types/grid';

// 组件事件
const emit = defineEmits(['refresh']);

// 组件属性
const props = defineProps({
  /**
   * 信息卡片配置
   */
  config: {
    type: Object as () => InfoConfig,
    required: true
  }
});

// 内部状态
const loading = ref(false);

// 加载数据
const loadData = async () => {
  loading.value = true;
  
  try {
    // 判断数据来源
    if (props.config.dataSource === 'api' && props.config.api) {
      // 从API获取数据
      // 在实际项目中，这里应该调用API服务获取数据
      // 示例代码，实际项目中应替换为真实的API调用
      ElMessage.info('API数据源功能需要实现');
    }
    // 对于静态数据，不需要额外处理
  } catch (error) {
    console.error('加载信息卡片数据失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 监听配置变化，重新加载数据
watch(() => props.config, () => {
  // 重新加载数据
  loadData();
}, { deep: true });

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});

// 向父组件暴露方法
defineExpose({
  refresh: loadData
});
</script>

<style scoped>
.grid-info-component {
  height: 100%;
}

.loading-container {
  padding: 20px;
}

.info-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.info-card {
  flex: 1;
  min-width: 200px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  padding: 15px;
  box-shadow: var(--el-box-shadow-light);
  border: 1px solid var(--el-border-color-light);
}

.info-card-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background-color: var(--el-color-primary-light-9);
}

.info-icon .icon {
  font-size: 24px;
  color: var(--el-color-primary);
}

.info-data {
  flex: 1;
}

.info-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  line-height: 1.2;
}

.info-unit {
  font-size: 14px;
  margin-left: 5px;
  color: var(--el-text-color-secondary);
}

.info-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-top: 5px;
}

.info-trend {
  display: flex;
  align-items: center;
  margin-top: 5px;
  font-size: 12px;
}

.trend-icon {
  margin-right: 3px;
}

.trend-icon.up {
  color: var(--el-color-success);
}

.trend-icon.down {
  color: var(--el-color-danger);
}

.trend-value.up {
  color: var(--el-color-success);
}

.trend-value.down {
  color: var(--el-color-danger);
}

/* 颜色样式 */
.green {
  color: var(--el-color-success);
}

.red {
  color: var(--el-color-danger);
}

.blue {
  color: var(--el-color-primary);
}

.orange {
  color: var(--el-color-warning);
}

.gray {
  color: var(--el-text-color-secondary);
}
</style>
