# GridPlusTable 操作按钮配置说明

## 概述

GridPlusTableComponent现已支持通过配置`actionBar`属性来添加表格行操作按钮，与PlusTable组件的action-bar功能完全兼容。

## 配置结构

在TableConfig中添加actionBar配置，示例如下：

```typescript
interface TableConfig {
  // 其他属性...
  
  // action-bar配置，用于PlusTable操作栏
  actionBar?: {
    // 操作按钮列表
    buttons: TableActionBarButton[];
    // 按钮类型：link（链接）, icon（图标）, button（按钮）
    type?: 'link' | 'icon' | 'button';
    // 确认类型：popconfirm（气泡确认框）, messageBox（消息确认框）
    confirmType?: 'popconfirm' | 'messageBox';
    // 列宽度
    width?: number;
  };
}
```

## 按钮配置

每个按钮的配置项：

```typescript
interface TableActionBarButton {
  // 按钮文本
  text: string;
  
  // 按钮代码，用于标识操作类型
  code: string;
  
  // 按钮操作类型，与code二选一
  action?: string;
  
  // 按钮类型（Element Plus的按钮类型）
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text';
  
  // 按钮图标，支持Element Plus图标组件
  icon?: any;
  
  // 按钮属性，可以是对象或函数
  props?: Record<string, any> | ((row: any) => Record<string, any>);
  
  // 是否需要确认
  confirm?: boolean;
  
  // 确认配置
  confirmConfig?: {
    // 确认框标题
    title?: string;
    // 确认框消息
    message?: string | ((params: any) => string);
    // 确认框属性
    popconfirmProps?: Record<string, any>;
  };
  
  // 条件显示，可以是字符串表达式或函数
  condition?: string | ((row: any) => boolean);
  
  // 显示函数，用于控制按钮是否显示
  show?: (row: any) => boolean;
}
```

## 使用示例

### 基本配置

```javascript
const tableConfig = {
  columns: [
    // 列配置...
  ],
  // 操作按钮配置
  actionBar: {
    buttons: [
      {
        text: '查看',
        code: 'view',
        icon: 'View', // Element Plus 图标
        type: 'info'
      },
      {
        text: '编辑',
        code: 'edit',
        icon: 'Edit',
        type: 'primary'
      },
      {
        text: '删除',
        code: 'delete',
        icon: 'Delete',
        type: 'danger',
        confirm: true
      }
    ],
    type: 'link',  // 可选值：link, icon, button
    width: 200     // 操作列宽度
  }
}
```

### 带确认的操作按钮

```javascript
const tableConfig = {
  columns: [
    // 列配置...
  ],
  actionBar: {
    buttons: [
      {
        text: '删除',
        code: 'delete',
        icon: 'Delete',
        type: 'danger',
        confirm: true,
        confirmConfig: {
          title: '确认删除',
          message: (params) => `确定删除ID为${params.row.id}的记录吗？`,
          popconfirmProps: { width: 200 }
        }
      }
    ],
    confirmType: 'popconfirm', // 可选值：popconfirm, messageBox
    type: 'button'
  }
}
```

### 条件显示按钮

```javascript
const tableConfig = {
  columns: [
    // 列配置...
  ],
  actionBar: {
    buttons: [
      {
        text: '审核',
        code: 'audit',
        type: 'warning',
        // 使用字符串表达式（在表达式中可以访问row对象）
        condition: "row.status === '0'", 
      },
      {
        text: '发布',
        code: 'publish',
        type: 'success',
        // 使用函数
        show: (row) => row.status === '1'
      }
    ],
    type: 'link'
  }
}
```

### 动态按钮属性

```javascript
const tableConfig = {
  columns: [
    // 列配置...
  ],
  actionBar: {
    buttons: [
      {
        text: '状态',
        code: 'status',
        // 动态属性，根据行数据变化
        props: (row) => ({
          type: row.status === '1' ? 'success' : 'danger',
          disabled: row.locked
        })
      }
    ]
  }
}
```

## 事件处理

组件中可以通过`@clickAction`事件监听按钮点击：

```vue
<template>
  <GridPlusTableComponent 
    :config="tableConfig" 
    @clickAction="handleClickAction"
  />
</template>

<script setup>
const handleClickAction = (data) => {
  // data结构：{ row, index, code }
  const { row, index, code } = data;
  
  switch(code) {
    case 'view':
      // 处理查看逻辑
      break;
    case 'edit':
      // 处理编辑逻辑
      break;
    case 'delete':
      // 处理删除逻辑
      break;
    // ...
  }
};
</script>
```

## 注意事项

1. 在导入Element Plus图标时，确保正确引入：
   ```javascript
   import { Edit, View, Delete } from '@element-plus/icons-vue';
   ```

2. 使用confirm功能时，可以根据需要选择`popconfirm`或`messageBox`两种确认方式。

3. 条件显示按钮时，可以使用字符串表达式或函数两种方式，字符串表达式通过Function构造函数执行，有一定安全风险，谨慎使用。
