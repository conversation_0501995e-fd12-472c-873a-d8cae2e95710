<!--
  @description GridPlusTableComponent - 用于在网格中渲染PlusTable组件
  <AUTHOR>
  @date 2025-04-11
  @ todo: 
  1. 接入GridComponentStore，实现数据绑定
  2. 分析获取数据应该在GridPlusTableComponent还是GridComponentStore中？并实现数据获取逻辑
-->
<template>
  <div class="grid-plus-table-component">
    <div class="plus-table-wrapper">
      <PlusTable 
        v-if="tableColumns.length > 0" 
        :columns="tableColumns" 
        :table-data="componentStore.tableData.value"
        v-bind="tableProps"
        :action-bar="actionBarConfig" 
        @clickAction="handleClickAction"
      />
      <div v-else class="empty-data">
        <el-empty description="暂无数据配置" />
      </div>
    </div>
    <div v-if="showPagination" class="plus-table-pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="config.pagination?.pageSizes || [10, 20, 50, 100]"
        :layout="paginationLayout"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * GridPlusTableComponent用于在网格中渲染PlusTable组件
 * 支持静态数据和API数据，以及分页和筛选等功能
 * 此组件用于渲染表格，支持注入父组件（祖先）提供的store
 */
import { ref, computed, onMounted, watch, inject } from 'vue';
import type { TableConfig } from '@/types/grid';
import { ElMessage, ElEmpty } from 'element-plus';
import { useTable } from 'plus-pro-components';
import type { PlusColumn, TableValueType } from 'plus-pro-components';
import GridLayoutService from '@/components/page/gridLayoutService';
import { getGridComponentStore } from '@/components/page/gridComponentStore';
import type GridLayoutStoreType from '@/components/page/gridLayoutStore';

// 组件属性
const props = defineProps<{
  config: TableConfig;
}>();

// 组件事件
const emit = defineEmits(['refresh', 'config', 'updated', 'clickAction']);

// 注入页面服务
// 提供默认值null，避免注入失败时报错
const pageService = inject<GridLayoutService>('pageService');

// 注入 gridLayoutStore，并指定类型
const gridLayoutStore = inject<GridLayoutStoreType>('gridLayoutStore')!;
// 获取全局单例
const componentStore = getGridComponentStore();
// 表格数据
const { tableData, buttons } = useTable<any[]>();

// 分页相关状态
const currentPage = ref(0);
const pageSize = ref(props.config.pagination?.pageSize || 10);
const total = ref(0);
const loading = ref(false);

// 是否显示分页
const showPagination = computed(() => {
  return props.config.showPagination !== false;
});

// 分页布局
const paginationLayout = computed(() => {
  return 'total, sizes, prev, pager, next, jumper';
});

// 表格列配置
const tableColumns = computed<PlusColumn[]>(() => {
  if (!props.config.columns) return [];
  
  return props.config.columns.map(column => {
    // 将GridTable的列配置转换为PlusTable的列配置
    const plusColumn: PlusColumn = {
      label: column.label,
      prop: column.prop,
      valueType: column.valueType as TableValueType | undefined,
      width: column.width,
      fixed: column.fixed,
      align: column.align || 'left'
    };

    // 处理选项配置
    if (column.options && column.options.length > 0) {
      plusColumn.options = column.options.map(option => ({
        label: option.label,
        value: option.value,
        type: option.color ? undefined : (option.value === '0' ? 'primary' : 
                option.value === '1' ? 'success' : 
                option.value === '2' ? 'info' : 
                option.value === '3' ? 'danger' : 'warning'),
        color: option.color
      }));
    }
    
    // 值枚举配置
    if (column.valueEnum) {
      plusColumn.valueEnum = column.valueEnum;
    }

    return plusColumn;
  });
});

// 操作栏配置
const actionBarConfig = computed(() => {
  if (!props.config.actionBar) return undefined;
  
  // 如果配置了actionBar，则初始化按钮配置
  if (props.config.actionBar.buttons && props.config.actionBar.buttons.length > 0) {
    buttons.value = props.config.actionBar.buttons.map(button => ({
      text: button.text,
      code: button.code || button.action,
      icon: button.icon,
      props: button.props || { type: button.type || 'primary' },
      show: button.condition ? (row: any) => evaluateCondition(row, button.condition as string) : undefined,
      confirm: button.confirm ? {
        popconfirmProps: { width: 200 },
        message: (params) => `确定${button.text}id为${params.row?.id || ''}的数据吗？`
      } : undefined
    }));
  } else {
    return undefined;
  }

  return {
    buttons: buttons.value,
    type: props.config.actionBar.type || 'link',
    confirmType: props.config.actionBar.confirmType || 'popconfirm',
    width: props.config.actionBar.width || 200
  };
});

/**
 * 评估条件表达式
 * @param row 行数据
 * @param condition 条件表达式
 * @returns 条件计算结果
 */
const evaluateCondition = (row: any, condition: string): boolean => {
  try {
    // 使用Function构造函数创建一个可执行的函数
    // eslint-disable-next-line no-new-func
    const func = new Function('row', `return ${condition}`);
    return func(row);
  } catch (e) {
    console.error('条件表达式计算错误:', condition, e);
    return true; // 表达式错误默认显示
  }
};

// 表格附加属性
const tableProps = computed(() => {
  return {
    stripe: true,
    border: true,
    'highlight-current-row': true,
    'row-key': props.config.rowKey || 'id',
    'empty-text': '暂无数据',
  };
});

/**
 * 处理页面大小变化
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  loadData();
};

/**
 * 处理页码变化
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadData();
};

/**
 * 处理按钮点击事件
 * @param data 包含行数据、索引和按钮代码的对象
 */
const handleClickAction = (data: any) => {
  console.log('[GridPlusTableComponent] 按钮点击:', data);
  
  // 直接调用 componentStore 中的行按钮点击处理方法
  componentStore.handleRowButtonClick(data);
  
  // 触发组件事件，允许父组件进行自定义处理
  emit('clickAction', data);
};

/**
 * 加载表格数据
 */
const loadData = async () => {
  if (props.config.dataSource === 'static' && props.config.staticData) {
    // 使用静态数据（支持分页）
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    tableData.value = props.config.staticData.slice(start, end);
    total.value = props.config.staticData.length;
    return;
  }
  
  if (props.config.dataSource === 'api') {
    if (!pageService) {
      console.warn('GridPlusTableComponent: PageService 未注入，请确保父组件提供了pageService');
      // 不显示错误消息，避免用户体验受影响
      tableData.value = [];
      total.value = 0;
      loading.value = false;
      return;
    }
    // 从API加载数据
    loading.value = true;
    try {
      const params = {
        ...props.config.params,
        page: currentPage.value,
        pageSize: pageSize.value
      };
      
      // 调用 service 的 fetchData 方法
      const response = await componentStore.fetchDataByApi(props.config.api?props.config.api:gridLayoutStore.getServiceConfig().baseUrl, params);
      console.log('[GridPlusTableComponent] fetchDataByApi response:', response);
      // 假设 service 返回数据格式为 { data: [], total: number }
      // 或直接返回数组，Service 内部的 transformResponseData 可以用来统一格式
      if (response && typeof response === 'object') {
          if (Array.isArray(response.data)) {
              tableData.value = response.data;
              total.value = response.total ?? response.data.length; // 优先使用total
          } else if (Array.isArray(response.list)) { // 兼容list格式
              tableData.value = response.list;
              total.value = response.total ?? response.list.length;
          } else if (Array.isArray(response)) { // 兼容直接返回数组
              tableData.value = response;
              total.value = response.length; // 需要后端配合返回总数，或前端计算
          } else {
              console.error('GridPlusTableComponent: Service 返回数据格式不符合预期:', response);
              ElMessage.error('数据格式错误');
              tableData.value = [];
              total.value = 0;
          }
      } else if (Array.isArray(response)) { // 兼容直接返回数组的情况
         tableData.value = response;
         total.value = response.length; // 同样，需要后端配合返回总数
      } else {
        console.error('GridPlusTableComponent: Service 返回数据格式不符合预期:', response);
        ElMessage.error('数据格式错误');
        tableData.value = [];
        total.value = 0;
      }

    } catch (error) {
      // 错误已在 service 中处理，这里可以不再弹窗
      console.error('加载表格数据出错 (组件层):', error);
      tableData.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  } else {
    console.log('[GridPlusTableComponent] config', props.config);
    //console.error('GridPlusTableComponent: servicece ata!',gridLayoutStore.serviceConfig);
    const data = await componentStore.fetchDataByApi(props.config.api?props.config.api:gridLayoutStore.getServiceConfig().baseUrl, {});
    //console.error('GridPlusTableComponent: data!',data);
    componentStore.tableData.value = data.list;
    
  }
};

/**
 * 刷新表格数据
 */
const refreshData = () => {
  currentPage.value = 1;
  loadData();
  emit('refresh');
};

// 监听配置变化
watch(() => props.config, () => {
  console.log('[GridPlusTableComponent] 配置变化:', props.config);
  refreshData();
}, { deep: true });

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});

// 向父组件暴露刷新方法
defineExpose({
  refresh: refreshData
});
</script>

<style scoped>
.grid-plus-table-component {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.plus-table-wrapper {
  flex: 1;
  overflow: auto;
  padding: 4px;
}

.plus-table-pagination {
  padding: 10px 0;
  display: flex;
  justify-content: flex-end;
}

.empty-data {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
