<!--
  @description GridPlusFormComponent - 用于在网格中渲染PlusForm组件
  <AUTHOR>
  @date 2025-04-11
-->
<template>
  <div class="grid-plus-form-component">
    <div class="plus-form-wrapper">
      <PlusForm
        v-model="formState"
        :columns="formColumns"
        :rules="formRules"
        :layout="config.layout || 'horizontal'"
        :label-width="config.labelWidth || '120px'"
        :submit-text="config.submitText || '提交'"
        :reset-text="config.resetText || '重置'"
        :submit-button="showSubmitButton"
        :reset-button="showResetButton"
        @change="handleFormChange"
        @submit="handleFormSubmit"
        @submit-error="handleFormSubmitError"
        @reset="handleFormReset"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * GridPlusFormComponent用于在网格中渲染PlusForm组件
 * 支持动态表单配置、表单验证和表单提交等功能
 */
import { ref, computed, onMounted, watch, inject } from 'vue';
import type { FormConfig } from '@/types/grid';
import { ElMessage } from 'element-plus';
import type { PlusColumn, FieldValues } from 'plus-pro-components';
import GridLayoutService from '@/components/page/gridLayoutService';

// 组件属性
const props = defineProps<{
  config: FormConfig;
}>();

// 组件事件
const emit = defineEmits(['refresh', 'config', 'updated', 'submit', 'reset']);

// 注入页面服务
const pageService = inject<GridLayoutService>('pageService');

// 表单状态
const formState = ref<FieldValues>(props.config.initialValues || {});

// 是否显示提交按钮
const showSubmitButton = computed(() => {
  return props.config.showSubmitButton !== false;
});

// 是否显示重置按钮
const showResetButton = computed(() => {
  return props.config.showResetButton !== false;
});

// 表单列配置
const formColumns = computed<PlusColumn[]>(() => {
  if (!props.config.columns) return [];
  
  return props.config.columns;
});

// 表单验证规则
const formRules = computed(() => {
  return props.config.rules || {};
});

/**
 * 处理表单字段值变化
 */
const handleFormChange = (values: FieldValues, prop: PlusColumn) => {
  emit('updated', { values, field: prop.prop });
  
  // 如果配置了onChange回调，尝试执行
  if (props.config.onChange && typeof props.config.onChange === 'function') {
    try {
      // 这里需要注意：实际环境中，不应该直接执行字符串函数
      // 这里只是示例，实际实现应该更安全
      props.config.onChange(values, prop);
    } catch (error) {
      console.error('执行onChange回调出错:', error);
    }
  }
};

/**
 * 处理表单提交
 */
const handleFormSubmit = async (values: FieldValues) => {
  emit('submit', values);
  
  // 如果配置了onSubmit回调，优先执行回调
  if (props.config.onSubmit && typeof props.config.onSubmit === 'function') {
    try {
      await props.config.onSubmit(values);
      // 可以在这里添加成功提示，或者由回调函数自己处理
    } catch (error) {
      console.error('执行onSubmit回调出错:', error);
      // 回调出错通常也需要提示用户
      ElMessage.error('表单提交处理失败');
    }
    return; // 执行完回调后不再执行默认的API提交
  }
  
  // 如果配置了 submitApi (这里可以理解为需要调用 service 进行 CRUD)
  if (props.config.submitApi) { 
    if (!pageService) {
      console.error('GridPlusFormComponent: PageService 未注入!');
      ElMessage.error('服务配置错误，无法提交表单');
      return;
    }
    
    try {
      let response;
      // 判断是创建还是更新 (通常根据表单数据中是否包含id来判断)
      const idKey = props.config.idKey || 'id';
      const idValue = values[idKey];
      const id = (typeof idValue === 'string' || typeof idValue === 'number') ? idValue : undefined;
      
      if (id !== undefined) {
        // 更新操作
        response = await pageService.updateItem(id, values);
        ElMessage.success('表单更新成功');
      } else {
        // 创建操作
        response = await pageService.createItem(values);
        ElMessage.success('表单创建成功');
      }
      console.log('Service 提交响应:', response);
      // 可以在这里根据需要处理响应，例如关闭弹窗、刷新表格等
      // emit('refresh'); // 示例：提交成功后通知父组件刷新
      
    } catch (error) {
      // 错误已在 service 中处理，这里可以不再弹窗
      console.error('表单提交出错 (组件层):', error);
      // 但仍然可以在这里做一些额外的错误处理，例如不清空表单
    }
  } else {
    // 如果没有配置 onSubmit 回调和 submitApi，给出默认提示
    console.log('表单已提交 (无API配置):', values);
    ElMessage.success('表单提交成功 (本地)');
  }
};

/**
 * 处理表单提交错误
 */
const handleFormSubmitError = (error: any) => {
  console.error('表单验证错误:', error);
  
  if (props.config.onSubmitError && typeof props.config.onSubmitError === 'function') {
    try {
      props.config.onSubmitError(error);
    } catch (err) {
      console.error('执行onSubmitError回调出错:', err);
    }
  }
};

/**
 * 处理表单重置
 */
const handleFormReset = () => {
  emit('reset');
  
  // 重置表单状态到初始值
  formState.value = { ...(props.config.initialValues || {}) };
  
  if (props.config.onReset && typeof props.config.onReset === 'function') {
    try {
      props.config.onReset();
    } catch (error) {
      console.error('执行onReset回调出错:', error);
    }
  }
  
  ElMessage.info('表单已重置');
};

// 监听配置变化
watch(() => props.config, (newConfig) => {
  console.log('watch config', newConfig);
  // 当配置变化时，重置表单状态
  if (newConfig.initialValues) {
    formState.value = { ...newConfig.initialValues };
  }
}, { deep: true });

// 组件挂载时的操作
onMounted(() => {
  // 初始化表单状态
  formState.value = { ...(props.config.initialValues || {}) };
});

// 向父组件暴露方法
defineExpose({
  // 重置表单
  reset: handleFormReset,
  // 提交表单（可以从外部触发）
  submit: () => {
    // 实际场景中，应该调用PlusForm组件的submit方法
  },
  // 获取表单当前值
  getValues: () => formState.value
});
</script>

<style scoped>
.grid-plus-form-component {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  padding: 10px;
}

.empty-content {
  padding: 0;
}
.plus-form-wrapper {
  flex: 1;
  overflow: auto;
}
</style>
