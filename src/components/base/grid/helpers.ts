// src/components/base/grid/helpers.ts
/**
 * @description 网格组件辅助工具函数
 * <AUTHOR> AI
 * @date 2025-04-24
 */

import { registerGridComponent } from './types';
import type { Component } from 'vue';

/**
 * 组件预处理器映射表
 * 用于对不同类型组件的配置进行预处理
 */
export const configProcessors: Record<string, (config: Record<string, any>) => Record<string, any>> = {
  
  // 表单组件预处理器
  form: (config: Record<string, any>) => {
    // 深拷贝避免修改原始对象
    const processed = JSON.parse(JSON.stringify(config));
    // 设置默认值
    if (!processed.formProps) processed.formProps = { labelWidth: '120px' };
    return processed;
  },
  
  // 表格组件预处理器
  table: (config: Record<string, any>) => {
    console.log('table configProcessors', config);
    // 深拷贝避免修改原始对象
    const processed = JSON.parse(JSON.stringify(config));
    // 设置默认值
    if (!processed.pagination) processed.pagination = { current: 1, pageSize: 10 };
    return processed;
  },
  
  // 表格组件别名 - 使用相同的预处理器
  plustable: (config: Record<string, any>) => configProcessors.table(config),
  
  // 图表组件预处理器
  chart: (config: Record<string, any>) => {
    // 深拷贝避免修改原始对象
    const processed = JSON.parse(JSON.stringify(config));
    // 设置默认主题
    if (!processed.theme) processed.theme = 'default';
    return processed;
  },
  
  // 页面组件预处理器
  page: (config: Record<string, any>) => {
    // 深拷贝避免修改原始对象
    const processed = JSON.parse(JSON.stringify(config));
    
    // 确保必要的配置结构存在
    if (!processed.columns) processed.columns = [];
    if (!processed.pagination) processed.pagination = { pageSize: 10, pageSizes: [10, 20, 50, 100] };
    if (!processed.table) processed.table = { isSelection: true };
    
    return processed;
  },
  
  // 自定义组件预处理器
  custom: (config: Record<string, any>) => {
    // 深拷贝避免修改原始对象
    const processed = JSON.parse(JSON.stringify(config));
    // 确保config对象存在
    if (!processed.config) processed.config = {};
    return processed;
  }
};

/**
 * 组件事件处理器映射表
 * 用于为不同类型组件提供默认事件处理
 */
export const eventHandlers: Record<string, Record<string, Function>> = {
  // 表单组件事件
  form: {
    submit: (values: any) => {
      console.log('表单提交', values);
      return values;
    },
    reset: () => {
      console.log('表单重置');
    }
  },
  
  // 表格组件事件
  table: {
    'row-click': (row: any) => {
      console.log('表格行点击', row);
      return row;
    },
    'selection-change': (selection: any[]) => {
      console.log('表格选择变化', selection);
      return selection;
    }
  },
  
  // 表格组件别名 - 使用相同的事件处理器
  plustable: {
    'row-click': (row: any) => eventHandlers.table['row-click'](row),
    'selection-change': (selection: any[]) => eventHandlers.table['selection-change'](selection)
  }
};

/**
 * 处理组件配置，对特定类型组件进行预处理
 * @param type 组件类型
 * @param config 原始配置
 * @returns 预处理后的配置
 */
export function processComponentConfig(type: string, config: Record<string, any>): Record<string, any> {
  // 使用对应类型的预处理器，如果没有则返回原配置
  const processor = configProcessors[type];
  return processor ? processor(config) : { ...config };
}

/**
 * 获取组件的事件处理器
 * @param type 组件类型
 * @returns 事件处理器映射表
 */
export function getComponentEventHandlers(type: string): Record<string, Function> {
  return eventHandlers[type] || {};
}

/**
 * 注册新的组件类型，同时可以注册对应的配置预处理器和事件处理器
 * @param type 组件类型名称
 * @param component 组件定义
 * @param processor 可选的配置预处理器
 * @param handlers 可选的事件处理器
 */
export function registerCustomComponent(
  type: string, 
  component: Component, 
  processor?: (config: Record<string, any>) => Record<string, any>,
  handlers?: Record<string, Function>
): void {
  // 注册组件
  registerGridComponent(type, component);
  
  // 注册配置预处理器
  if (processor) {
    configProcessors[type] = processor;
  }
  
  // 注册事件处理器
  if (handlers) {
    eventHandlers[type] = handlers;
  }
}
