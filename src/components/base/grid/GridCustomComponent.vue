<!--
  @description GridCustomComponent - 网格系统中的自定义内容组件
  <AUTHOR> AI
  @date 2025-04-11
-->
<template>
  <div class="grid-custom-component">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>
    <template v-else>
      <!-- 自定义HTML内容 -->
      <div v-if="config.html" v-html="config.html"></div>
      
      <!-- 动态组件 -->
      <component 
        v-else-if="config.component"
        :is="resolveComponent(config.component)"
        v-bind="config.props || {}"
      />
      
      <!-- 默认内容 -->
      <div v-else class="empty-content">
        <el-empty description="未配置内容" />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
/**
 * GridCustomComponent 是一个用于在网格中展示自定义内容的组件
 * 支持HTML内容、动态组件等
 */
import { ref, onMounted, defineAsyncComponent, markRaw } from 'vue';
import type { CustomConfig } from '@/types/grid';

// 组件事件
const emit = defineEmits(['refresh']);

// 组件属性
defineProps({
  /**
   * 自定义内容配置
   */
  config: {
    type: Object as () => CustomConfig,
    required: true
  }
});

// 内部状态
const loading = ref(false);

// 解析组件名称为实际组件
const resolveComponent = (componentName: string) => {
  // 预定义的组件映射
  const componentMap: Record<string, any> = {
    // 可以在这里添加更多预定义组件
    'ElButton': markRaw(defineAsyncComponent(() => import('element-plus').then(m => m.ElButton))),
    'ElCard': markRaw(defineAsyncComponent(() => import('element-plus').then(m => m.ElCard))),
    'ElAlert': markRaw(defineAsyncComponent(() => import('element-plus').then(m => m.ElAlert)))
  };
  
  // 返回预定义组件或尝试异步加载
  return componentMap[componentName] || markRaw(defineAsyncComponent(() => {
    // 这里可以根据实际项目结构调整组件导入路径
    try {
      return import(`@/components/${componentName}.vue`);
    } catch (error) {
      console.error(`无法加载组件 ${componentName}:`, error);
      return import('element-plus').then(m => m.ElEmpty);
    }
  }));
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  
  try {
    // 延迟一小段时间模拟加载
    await new Promise(resolve => setTimeout(resolve, 500));
  } catch (error) {
    console.error('加载自定义内容失败:', error);
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});

// 向父组件暴露方法
defineExpose({
  refresh: loadData
});
</script>

<style scoped>
.grid-custom-component {
  height: 100%;
  width: 100%;
  overflow: auto;
  color: var(--el-text-color-primary);
}

.loading-container {
  padding: 20px;
}

.empty-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0px;
}
</style>
