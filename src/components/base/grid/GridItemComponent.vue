<!--
  @description GridItemComponent - 用于在gridstack网格中创建可自定义的网格项
  <AUTHOR> AI
  @date 2025-04-11
  @功能说明：
    1. 支持通用结构数据与专用结构数据分层解析。
    2. 通用结构数据：每个组件都需要，如位置参数、type、title、icon、可拖拽、可缩放、整体样式等。
    3. 专用结构数据：仅传递给子组件（如GridPlusFormComponent、GridPlusTableComponent）的专用渲染参数。
    4. 通过content.type自动动态渲染对应业务组件，并将content.config作为专用props传递。
    5. 适配PlusProComponents体系，便于高扩展。
    6. 操作按钮全部并排合并至主题切换按钮区域，无下拉菜单。
  @变更记录：
    2025-04-11：移除原有的下拉菜单和按钮分散布局，将移动、刷新、设置、关闭、主题切换五个按钮全部并排显示于同一区域。
-->
<!-- 本文件为GridItemComponent，负责根据内容类型渲染对应的子组件 -->
<template>
  <div 
    class="grid-item-component" 
    :class="{ 'theme-dark': isDarkMode }"
    :style="componentStyle"
    ref="containerRef"
  >
    <!-- 操作按钮区域 - 全部并排显示在主题切换按钮旁边 -->
    <!-- 所有操作按钮合并区域 -->
    <div class="grid-item-actions-floating actions-merged">
      <div class="grid-item-drag-btn"> 
        <el-icon circle size="small"><Rank /></el-icon>
      </div>
      <el-button v-if="configurable" circle size="small" @click="handleItemClick">
        <el-icon><Setting /></el-icon>
      </el-button>
      <div v-if="themeModeSwitchable">
        <el-tooltip :content="isDarkMode ? '切换到日间模式' : '切换到夜间模式'">
          <el-button circle size="small" @click="toggleThemeMode">
            <el-icon>
              <component :is="isDarkMode ? 'Sunny' : 'Moon'"></component>
            </el-icon>
          </el-button>
        </el-tooltip>
        <el-button v-if="closable" circle size="small" @click="handleClose">
        <el-icon><Close /></el-icon>
      </el-button>
      </div>
    </div>

    <!-- 标题区域，可配置是否显示 -->
    <div class="grid-item-header" v-if="showHeader && showTitle !== false && content.showTitle !== false" :style="titleStyle">
      <div class="grid-item-title">
        <el-icon v-if="icon" class="title-icon">
          <component :is="icon"></component>
        </el-icon>
        {{ title }}
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="grid-item-content" :class="{ 'no-header': !showHeader || !showTitle }">
      <!-- 根据content.type渲染不同的内容组件，并将content.config作为专用props传递 -->
      <component
        v-if="contentType && isValidComponentType(contentType)"
        :is="getComponentByType(contentType)"
        :store="gridLayoutStore"
        :config="processedConfig.config || processedConfig"
        v-on="componentEvents"
        @refresh="handleRefresh"
        @config="handleConfig"
        @close="handleClose"
      />
      <!-- 添加错误处理和降级显示 -->
      <div v-else-if="contentType && !isValidComponentType(contentType)" class="error-placeholder">
        <el-alert
          title="组件加载失败"
          type="error"
          :description="`无法加载组件类型: ${contentType}`"
          show-icon
        />
      </div>
      <div v-else-if="htmlContent" v-html="htmlContent"></div>
      <slot v-else></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
// GridItemComponent.vue
// 此组件用于渲染单个网格项，支持注入父组件提供的store
import { inject, ref, computed, onMounted, onUnmounted } from 'vue';
import { Setting, Close, Rank } from '@element-plus/icons-vue';
import { ElButton, ElAlert } from 'element-plus';
import { gridComponentMap, isValidComponentType } from '@/components/base/grid/types';
import type { GridComponentType } from '@/components/base/grid/types';
import { processComponentConfig, getComponentEventHandlers } from '@/components/base/grid/helpers';

// 注入 gridLayoutStore，供本组件和子组件使用
const gridLayoutStore = inject('gridLayoutStore');

// 组件事件
const emit = defineEmits(['refresh', 'config', 'close', 'remove', 'edit', 'themeChange', 'item-click']);

// 组件属性
const props = defineProps({
  // 通用结构数据
  title: { type: String, default: '' },
  icon: { type: String, default: '' },
  showHeader: { type: Boolean, default: true },
  showTitle: { type: Boolean, default: true },
  refreshable: { type: Boolean, default: false },
  configurable: { type: Boolean, default: false },
  closable: { type: Boolean, default: false },
  editable: { type: Boolean, default: false },
  customStyle: { type: Object, default: () => ({}) },
  themeMode: { type: String, default: 'light' },
  htmlContent: { type: String, default: '' },
  content: { type: Object, default: () => ({}) },
  themeModeSwitchable: { type: Boolean, default: true },
  // 新增字段，确保storeLayout.props所有字段都声明
  config: { type: Object, default: () => ({}) }, // 业务配置
  id: { type: [String, Number], default: '' },
  name: { type: String, default: '' },
  remark: { type: String, default: '' },
  type: { type: String, default: '' },
});

// 内部状态
const containerRef = ref<HTMLElement | null>(null);
const componentWidth = ref(0);
// const isNarrowWidth = computed(() => {
//   // 计算可用空间 - 如果以下按钮都显示至少需要150px空间，否则折叠
//   const requiredSpace = [
//     props.refreshable, 
//     props.configurable, 
//     props.closable
//   ].filter(Boolean).length * 40 + 20; // 每个按钮约40px，加上边距
  
//   return componentWidth.value < Math.min(requiredSpace, 150);
// });
const themeMode = ref(props.content.themeMode || 'light');
const isDarkMode = computed(() => themeMode.value === 'dark' || 
  (themeMode.value === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches));

// 检查是否有任何操作按钮可用
// const hasActions = computed(() => {
//   return props.refreshable || props.configurable || props.closable;
// });

// 解析type和config
const contentType = computed(() => props.content?.type || '');
const contentConfig = computed(() => props.content? props.content : {});

/**
 * 处理组件配置，针对不同组件类型进行预处理
 * @returns 预处理后的配置对象
 */
const processedConfig = computed(() => {
  //console.log('[processedConfig] contentType:', contentType.value, 'contentConfig:', contentConfig.value, 'processedConfig:', processComponentConfig(contentType.value, contentConfig.value));
  if (!contentType.value || !contentConfig.value) return {};
  return processComponentConfig(contentType.value, contentConfig.value);
});

/**
 * 根据组件类型生成对应的事件处理器
 */
const componentEvents = computed(() => {
  if (!contentType.value) return {};
  
  // 获取预定义的事件处理器
  const defaultHandlers = getComponentEventHandlers(contentType.value);
  
  // 合并自定义事件处理逻辑
  const customHandlers: Record<string, Function> = {};
  
  // 自定义处理某些事件
  if (contentType.value === 'form') {
    customHandlers.submit = (values: any) => {
      // 先执行默认处理器
      const result = defaultHandlers.submit ? defaultHandlers.submit(values) : values;
      // 然后执行自定义逻辑
      emit('config', { type: 'form-submit', data: result });
    };
  }
  
  // 合并默认处理器和自定义处理器，自定义处理器优先
  return { ...defaultHandlers, ...customHandlers };
});

// 处理刷新操作
const handleRefresh = () => {
  emit('refresh');
};

// 处理配置操作（合并了配置和编辑操作）
const handleConfig = () => {
  emit('config');
  // 为了兼容性，同时触发edit事件
  emit('edit');
};

// 处理关闭操作
/**
 * 处理关闭操作
 * 当点击关闭按钮时，向父组件emit 'close'事件
 */
const handleClose = () => {
  console.log('handleClose', Number(props.id));
  emit('close', Number(props.id)); // 传递唯一标识，便于父组件识别
};

// 切换主题模式
const toggleThemeMode = () => {
  themeMode.value = isDarkMode.value ? 'light' : 'dark';
  emit('themeChange', themeMode.value);
};

/**
 * 处理整个网格项的点击（不包括操作按钮区域）
 * @param e 鼠标事件
 * @description 避免操作按钮重复触发itemClick
 */
function handleItemClick() {
  emit('item-click', props.id);
}

// 计算组件样式
const componentStyle = computed(() => {
  const customStyle: Record<string, string> = {};
  
  // 应用背景色
  if (props.content.backgroundColor) {
    customStyle.backgroundColor = props.content.backgroundColor;
  }
  
  // 应用文本颜色
  if (props.content.textColor) {
    customStyle.color = props.content.textColor;
  }
  
  // 合并自定义样式
  if (props.content.customStyle) {
    Object.assign(customStyle, props.content.customStyle);
  }
  if(props.content.customStyle && props.content.customStyle.opacity) {
    if(customStyle.opacity && parseFloat(customStyle.opacity) > 1) {
      customStyle.opacity = (parseFloat(customStyle.opacity) / 100).toString();
    }
  }
  //console.log('[GridItemComponent] customStyle:', customStyle);
  
  return customStyle;
});

// 计算标题样式
const titleStyle = computed(() => {
  const style: Record<string, string> = {};
  
  // 应用标题背景色
  if (props.content.titleBackgroundColor) {
    style.backgroundColor = props.content.titleBackgroundColor;
  }
  
  return style;
});

// 测量容器宽度
const updateComponentWidth = () => {
  if (containerRef.value) {
    componentWidth.value = containerRef.value.offsetWidth;
  }
};

// 监听窗口大小变化
onMounted(() => {
  updateComponentWidth();
  window.addEventListener('resize', updateComponentWidth);
  //console.log('GridItemComponent props', props, props.content);
  // ResizeObserver用于监听容器大小变化
  const resizeObserver = new ResizeObserver(() => {
    updateComponentWidth();
  });
  
  if (containerRef.value) {
    resizeObserver.observe(containerRef.value);
  }
  
  // 初始化主题模式
  if (props.content.themeMode === 'auto') {
    const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleMediaQueryChange = (e: MediaQueryListEvent) => {
      if (themeMode.value === 'auto') {
        emit('themeChange', e.matches ? 'dark' : 'light');
      }
    };
    darkModeMediaQuery.addEventListener('change', handleMediaQueryChange);
  }
  
  // 清理函数
  onUnmounted(() => {
    window.removeEventListener('resize', updateComponentWidth);
    if (containerRef.value) {
      resizeObserver.unobserve(containerRef.value);
    }
  });
});

// 根据内容类型获取对应的组件
const getComponentByType = (type: GridComponentType) => {
  if (!isValidComponentType(type)) {
    console.error(`无效的组件类型: ${type}`);
    return null;
  }
  return gridComponentMap[type as keyof typeof gridComponentMap];
};
</script>

<style scoped>
/* 拖动手柄样式强化 */
.grid-item-drag-handle {
  cursor: grab;
  display: inline-flex;
  align-items: center;
  margin-right: 0px;
}
.grid-item-actions-floating {
  display: flex;
  align-items: center;
  position: absolute;
  left: 10px;
  bottom: 10px;
  z-index: 20;
  background: rgba(255,255,255,0.95);
  border-radius: 24px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.12);
  padding: 4px 10px;
  transition: box-shadow 0.2s;
}
.grid-item-actions-floating.actions-merged > * {
  margin-left: 4px;
}
.grid-item-actions-floating:hover {
  box-shadow: 0 4px 24px rgba(0,0,0,0.20);
}
.theme-mode-switcher {
  display: inline-flex;
  align-items: center;
}
.theme-dark .grid-item-actions-floating {
  background: rgba(0,0,0,0.7);
}

.grid-item-component {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
  width: 100%;
  background-color: var(--el-bg-color);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s;
}

.grid-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: var(--el-color-primary-light-9);
  border-bottom: 1px solid var(--el-border-color-light);
}

.theme-dark .grid-item-header {
  background-color: var(--el-color-primary-dark-9);
  border-bottom-color: var(--el-border-color-darker);
}

.grid-item-title {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 14px;
  color: var(--el-text-color-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.theme-dark .grid-item-title {
  color: var(--el-color-white);
}

.title-icon {
  margin-right: 5px;
  font-size: 16px;
}

.grid-item-content {
  flex: 1;
  overflow: auto;
  /*padding: 8px;
}
.grid-item-content.no-header {
  /*padding-top: 30px; /* 为悬浮操作按钮留出空间 */
}

.el-button {
  margin-left: 2px;
}

.theme-mode-switcher {
  position: absolute;
  bottom: 5px;
  right: 5px;
  z-index: 10;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.grid-item-component:hover .theme-mode-switcher {
  opacity: 1;
}
</style>

<style scoped>
/* grid-item-drag-btn：仿 el-button circle small 的圆形按钮样式 */
.grid-item-drag-btn {
  width: 23px;
  height: 23px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--el-button-bg-color, #fff);
  border: 1px solid var(--el-border-color, #dcdfe6);
  cursor: grab;
  transition: background 0.2s, box-shadow 0.2s, border-color 0.2s, color 0.2s;
  box-sizing: border-box;
  color: var(--el-button-text-color, #606266);
}
.grid-item-drag-btn:hover {
  background: var(--el-button-hover-bg-color, #f5f7fa);
  border-color: var(--el-color-primary, #409EFF);
  color: var(--el-color-primary, #409EFF);
}
.grid-item-drag-btn:active {
  background: var(--el-button-active-bg-color, #ecf5ff);
  border-color: var(--el-color-primary, #409EFF);
}
</style>
