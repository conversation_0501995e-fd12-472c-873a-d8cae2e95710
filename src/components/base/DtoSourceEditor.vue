<!-- 
  DTO源编辑器组件
  用于编辑DTO源对象，内部维护JSON字符串，支持格式化与生成字段等功能
-->
<template>
  <el-form>
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="action-row">
            <el-button type="primary" @click="handleFormatDto">格式化</el-button>
            <el-button type="default" @click="showEditor = !showEditor">
              {{ showEditor ? '隐藏编辑器' : '显示编辑器' }}
              <el-icon>
                <component :is="showEditor ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
          </el-button>
        </div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="showEditor ? 12 : 24" v-if="showEditor">
          <el-input
            :value="dtoSourceJsonStr"
            @input="onInput"
            type="textarea"
            :rows="20"
            placeholder="请输入DTO源JSON"
          />
        </el-col>
        <el-col :span="showEditor ? 12 : 24">
          <div class="json-viewer-container">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>JSON可视化预览</span>
                </div>
              </template>
              <JsonViewer 
                :value="parsedJson" 
                :expandDepth="3" 
                copyable 
                sort 
                boxed 
                theme="light"
                class="text-left"
              />
            </el-card>
          </div>
        </el-col>
      </el-row>
    <el-form-item style="margin-top: 20px;">
      <el-button type="primary" @click="handleSaveDtoSource">保存DTO源</el-button>
      <el-button type="success" @click="handleGenerateFromDto">从DTO源生成字段</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { JsonViewer } from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/index.css';

/**
 * props:
 *   modelValue: DTO对象
 * emits:
 *   update:modelValue: DTO对象
 *   save
 *   generate
 */
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['update:modelValue', 'save', 'generate']);

// 显示编辑器状态，默认不显示
const showEditor = ref(false);

// 本地DTO源字符串
const dtoSourceJsonStr = ref(JSON.stringify(props.modelValue, null, 2));

// 监听props变化，外部对象变化时同步到字符串
watch(() => props.modelValue, (newValue) => {
  try {
    const newStr = JSON.stringify(newValue, null, 2);
    if (newStr !== dtoSourceJsonStr.value) {
      dtoSourceJsonStr.value = newStr;
    }
  } catch (e) {
    // 忽略
  }
}, { deep: true });

// JSON解析结果用于可视化显示
const parsedJson = computed(() => {
  try {
    return JSON.parse(dtoSourceJsonStr.value);
  } catch (error) {
    return { error: '无效的JSON格式' };
  }
});

// 输入框内容变化时，尝试解析为对象并emit
function onInput(val: string) {
  dtoSourceJsonStr.value = val;
  try {
    const obj = JSON.parse(val);
    emit('update:modelValue', obj);
  } catch (e) {
    // 不弹窗，避免编辑时频繁报错
  }
}

/**
 * 格式化DTO源
 */
function handleFormatDto() {
  try {
    const parsed = JSON.parse(dtoSourceJsonStr.value);
    dtoSourceJsonStr.value = JSON.stringify(parsed, null, 2);
    emit('update:modelValue', parsed);
  } catch (error) {
    ElMessage.error('JSON格式错误，请检查输入内容');
  }
}

/**
 * 保存DTO源
 */
function handleSaveDtoSource() {
  try {
    const obj = JSON.parse(dtoSourceJsonStr.value);
    emit('update:modelValue', obj);
    emit('save', obj);
  } catch (error) {
    ElMessage.error('保存失败：JSON格式错误');
  }
}

/**
 * 从DTO源生成字段
 */
function handleGenerateFromDto() {
  try {
    const obj = JSON.parse(dtoSourceJsonStr.value);
    emit('update:modelValue', obj);
    emit('generate', obj);
  } catch (error) {
    ElMessage.error('生成失败：JSON格式错误');
  }
}
</script>

<style scoped>
/* 可以添加适当的样式 */
.json-viewer-container {
  height: 100%;
  width: 100%;
  overflow: auto;
  max-height: 500px;
  text-align: left;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  width: 100%;
}

.action-row {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}
</style>
