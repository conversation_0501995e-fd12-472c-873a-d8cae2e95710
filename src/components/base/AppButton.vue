<!-- src/components/base/AppButton.vue -->
<template>
    <el-button
      :type="type"
      :size="size"
      :loading="loading"
      :disabled="disabled"
      @click="handleClick"
    >
      <slot></slot>
    </el-button>
  </template>
  
  <script setup lang="ts">
  // defineProps和defineEmits是Vue 3的编译器宏，不需要导入
  
  defineProps({
    type: {
      type: String,
      default: 'default'
    },
    size: {
      type: String,
      default: 'default'
    },
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  });
  
  const emits = defineEmits(['click']);
  
  const handleClick = (event: MouseEvent) => {
    emits('click', event);
  };
  </script>
  
  <style scoped lang="scss">
  .el-button {
    border-radius: 4px;
    transition: all 0.3s;
  
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }
  }
  </style>