<!-- 
  列配置编辑器组件
  用于编辑表格和表单的列配置，支持拖拽排序、穿梭框选择、高级配置等功能
  该组件被设计为可复用组件，可被TableConfigEditor、FormDialogConfigEditor等组件调用
-->
<template>
  <div class="column-config-editor">
    <!-- 主要编辑区域 -->
    <div v-if="isTransferMode" class="transfer-mode">
      <!-- 穿梭框模式 -->
      <div class="action-row">
        <el-button type="primary" @click="addColumn">添加列</el-button>
      </div>
      <div class="transfer-control">
        <el-button 
          v-if="transferModel.length > 0" 
          type="primary" 
          @click="showAdvancedConfig"
        >
          高级配置
        </el-button>
      </div>
      
      <el-transfer
        v-model="transferModel"
        :data="availableFieldsData"
        :titles="['可用字段', '已选字段']"
        :props="{
          key: 'prop',
          label: 'label'
        }"
        filterable
      >
        <template #right-footer>
          <el-button link @click="showAdvancedConfig">高级配置</el-button>
        </template>
      </el-transfer>
    </div>
    
    <!-- 拖拽模式 -->
    <div v-else>
      <el-input 
        v-model="searchText" 
        placeholder="搜索字段" 
        prefix-icon="Search" 
        clearable 
        class="mb-2" 
      />
      
      <div class="drag-columns">
        <draggable 
          v-model="columns"
          :item-key="(item: ColumnType) => item.prop || Math.random().toString()"
          handle=".drag-handle"
          ghost-class="ghost"
          @change="handleDragEnd"
          class="grid-items-wrapper"
        >
          <template #item="{element}">
            <div 
              v-if="!searchText || 
                element.label?.toLowerCase().includes(searchText.toLowerCase()) || 
                element.prop?.toLowerCase().includes(searchText.toLowerCase())"
              class="column-item"
            >
              <div class="drag-handle">
                <el-icon><Rank /></el-icon>
              </div>
              <el-divider>列 {{ columns.indexOf(element) + 1 }}</el-divider>
              <el-form-item label="标签">
                <el-input v-model="element.label" @input="handleConfigChange" />
              </el-form-item>
              <el-form-item label="属性">
                <el-input v-model="element.prop" @input="handleConfigChange" />
              </el-form-item>
              <el-form-item label="宽度">
                <el-input-number v-model="element.width" :min="0" @change="handleConfigChange" />
              </el-form-item>
              <el-form-item label="类型">
                <el-select v-model="element.valueType" @change="handleConfigChange">
                  <el-option label="文本" value="text" />
                  <el-option label="标签" value="tag" />
                  <el-option label="图片" value="img" />
                  <el-option label="日期时间" value="date-picker" />
                  <el-option label="单选框" value="radio" />
                  <el-option label="选择器" value="select" />
                  <el-option label="多选框" value="checkbox" />
                  <el-option label="密码框" value="password" />
                  <el-option label="文本域" value="textarea" />
                  <el-option label="上传" value="upload" />
                  <el-option label="开关" value="switch" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="提示文本">
                <el-input 
                  v-model="element.tooltip" 
                  @input="handleConfigChange"
                  placeholder="鼠标悬停时显示的提示文本"
                />
              </el-form-item>
              
              <el-form-item label="占位符">
                <el-input 
                  v-model="element.placeholder" 
                  @input="handleConfigChange"
                  placeholder="输入框的占位符文本"
                />
              </el-form-item>
              
              <el-form-item label="必填">
                <el-switch 
                  v-model="element.required" 
                  @change="(val: boolean) => handleRequiredChange(element, val)"
                />
              </el-form-item>
              
              <!-- 可编辑 (仅表格模式下显示) -->
              <el-form-item v-if="type === 'table'" label="可编辑">
                <el-switch 
                  v-model="element.editable" 
                  @change="handleConfigChange"
                />
              </el-form-item>
              
              <!-- page类型特有配置 -->
              <template v-if="type === 'page'">
                <el-divider>页面特有配置</el-divider>
                
                <el-form-item label="隐藏在表格">
                  <el-switch 
                    v-model="element.hideInTable" 
                    @change="handleConfigChange"
                    :active-text="'在PlusPage组件中隐藏'"
                  />
                </el-form-item>
                
                <el-form-item label="隐藏在搜索">
                  <el-switch 
                    v-model="element.hideInSearch" 
                    @change="handleConfigChange"
                    :active-text="'在PlusSearch组件中隐藏'"
                  />
                </el-form-item>
              </template>
              
              <!-- 日期时间格式配置 -->
              <template v-if="element.valueType === 'date-picker'">
                <el-divider>日期时间配置</el-divider>
                
                <el-form-item label="日期类型">
                  <el-select 
                    :model-value="getFieldProp(element, 'type')" 
                    @update:model-value="(val: string) => updateFieldProp(element, 'type', val)"
                    placeholder="选择日期类型"
                  >
                    <el-option label="日期" value="date" />
                    <el-option label="日期时间" value="datetime" />
                    <el-option label="年份" value="year" />
                    <el-option label="月份" value="month" />
                    <el-option label="周" value="week" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="显示格式">
                  <el-input 
                    :model-value="getFieldProp(element, 'format')" 
                    @update:model-value="(val: string) => updateFieldProp(element, 'format', val)"
                    placeholder="如: YYYY-MM-DD"
                  >
                    <template #append>
                      <el-tooltip content="例如：YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss">
                        <el-icon><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </template>
                  </el-input>
                </el-form-item>
                
                <el-form-item label="值格式">
                  <el-input 
                    :model-value="getFieldProp(element, 'valueFormat')" 
                    @update:model-value="(val: string) => updateFieldProp(element, 'valueFormat', val)"
                    placeholder="如: YYYY-MM-DD"
                  >
                    <template #append>
                      <el-tooltip content="例如：YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss">
                        <el-icon><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </template>
                  </el-input>
                </el-form-item>
              </template>
              
              <!-- 选择器和单选框配置 -->
              <template v-if="element.valueType === 'select' || element.valueType === 'radio' || element.valueType === 'tag' || element.valueType === 'checkbox'">
                <el-divider>选项配置</el-divider>
                
                <div class="options-section">
                  <div class="options-header">
                    <div>
                      <el-button size="small" type="primary" @click="addOptionToItem(element)">添加选项</el-button>
                      <el-button size="small" type="success" @click="importValueEnum(element)">导入ValueEnum</el-button>
                    </div>
                    <el-button 
                      link
                      size="small" 
                      @click="toggleOptionsExpanded(element.prop)"
                      class="toggle-options-btn"
                    >
                      {{ getOptionsExpanded(element.prop) ? '收起选项' : '展开选项' }}
                      <el-icon>
                        <component :is="getOptionsExpanded(element.prop) ? ArrowUp : ArrowDown" />
                      </el-icon>
                    </el-button>
                  </div>
                  
                  <div v-show="getOptionsExpanded(element.prop)" class="options-container">
                    <div class="options-grid">
                      <div v-for="(option, optIndex) in element.options || []" :key="optIndex" class="option-item">
                        <el-divider>选项 {{ optIndex + 1 }}</el-divider>
                        
                        <el-form-item label="标签">
                          <el-input v-model="option.label" @input="handleConfigChange" />
                        </el-form-item>
                        
                        <el-form-item label="值">
                          <el-input v-model="option.value" @input="handleConfigChange" />
                        </el-form-item>
                        
                        <el-form-item label="颜色">
                          <el-select v-model="option.color" @change="handleConfigChange">
                            <el-option label="默认" value="" />
                            <el-option label="红色" value="red" />
                            <el-option label="蓝色" value="blue" />
                            <el-option label="绿色" value="green" />
                            <el-option label="黄色" value="yellow" />
                            <el-option label="灰色" value="gray" />
                            <el-option label="主要" value="primary" />
                            <el-option label="成功" value="success" />
                            <el-option label="警告" value="warning" />
                            <el-option label="危险" value="danger" />
                            <el-option label="信息" value="info" />
                          </el-select>
                        </el-form-item>
                        
                        <el-button type="danger" size="small" @click="removeOptionFromItem(element, optIndex)">
                          删除选项
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              
              <!-- 文本域配置 -->
              <template v-if="element.valueType === 'textarea'">
                <el-divider>文本域配置</el-divider>
                
                <el-form-item label="行数">
                  <el-input-number 
                    :model-value="getFieldProp(element, 'rows')" 
                    @update:model-value="(val: number) => updateFieldProp(element, 'rows', val)"
                    :min="2"
                    :max="10"
                  />
                </el-form-item>
                
                <el-form-item label="自动高度">
                  <el-switch 
                    :model-value="getFieldProp(element, 'autosize')" 
                    @update:model-value="(val: boolean) => updateFieldProp(element, 'autosize', val)"
                  />
                </el-form-item>
              </template>
              
              <!-- 文件上传配置 -->
              <template v-if="element.valueType === 'upload'">
                <el-divider>文件上传配置</el-divider>
                
                <el-form-item label="上传地址">
                  <el-input 
                    :model-value="getUploadProp(element, 'action')" 
                    @update:model-value="(val: string) => updateUploadProp(element, 'action', val)"
                    placeholder="/v1/admin/upload"
                  />
                </el-form-item>
                
                <el-form-item label="允许多选">
                  <el-switch 
                    :model-value="getUploadProp(element, 'multiple')" 
                    @update:model-value="(val: boolean) => updateUploadProp(element, 'multiple', val)"
                  />
                </el-form-item>
                
                <el-form-item label="接受类型">
                  <el-input 
                    :model-value="getUploadProp(element, 'accept')" 
                    @update:model-value="(val: string) => updateUploadProp(element, 'accept', val)"
                    placeholder="如: .jpg,.png,.pdf"
                  />
                </el-form-item>
                
                <el-form-item label="文件限制">
                  <el-input-number 
                    :model-value="getUploadProp(element, 'fileLimit')" 
                    @update:model-value="(val: number) => updateUploadProp(element, 'fileLimit', val)"
                    :min="1" 
                    :max="10"
                  />
                </el-form-item>
                
                <el-form-item label="大小限制">
                  <el-input-number 
                    style="width: 100%;"
                    :model-value="getSizeLimitInKB(element)" 
                    @update:model-value="(val: number) => updateUploadProp(element, 'sizeLimit', val * 1024)"
                    :min="1" 
                    :step="1"
                  >
                    <template #suffix><span>KB</span></template>
                  </el-input-number>
                </el-form-item>
                
                <el-form-item label="文件用途">
                  <el-input 
                    :model-value="getUploadProp(element, 'fileUsage')" 
                    @update:model-value="(val: string) => updateUploadProp(element, 'fileUsage', val)"
                    placeholder="默认与字段名相同"
                  />
                </el-form-item>
                
                <el-form-item label="上传样式">
                  <el-select 
                    :model-value="getUploadProp(element, 'style')" 
                    @update:model-value="(val: string) => updateUploadProp(element, 'style', val)"
                  >
                    <el-option label="默认拖拽区域" value="default" />
                    <el-option label="按钮" value="button" />
                    <el-option label="链接" value="link" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="显示文件列表">
                  <el-switch 
                    :model-value="getUploadProp(element, 'showFileList')" 
                    @update:model-value="(val: boolean) => updateUploadProp(element, 'showFileList', val)"
                  />
                </el-form-item>
                
                <el-form-item label="提示文字">
                  <el-input 
                    :model-value="getUploadProp(element, 'tip')" 
                    @update:model-value="(val: string) => updateUploadProp(element, 'tip', val)"
                    placeholder="点击或拖拽文件到此区域上传"
                  />
                </el-form-item>
              </template>
              
              <el-button type="danger" @click="removeColumn(columns.indexOf(element))">删除</el-button>
            </div>
          </template>
        </draggable>
      </div>
    </div>
    
    <!-- 高级配置对话框 -->
    <el-dialog
      v-model="advancedConfigVisible"
      title="高级列配置"
      width="80%"
      append-to-body
    >
      <!-- 穿梭框模式的高级配置界面，显示所有选中的列 -->
      <div v-if="advancedColumnsList.length > 0">
        <div class="search-bar">
          <el-input 
            v-model="advancedSearchText" 
            placeholder="搜索字段" 
            prefix-icon="Search" 
            clearable 
            class="mb-2" 
          />
        </div>
        
        <draggable 
          v-model="advancedColumnsList"
          :item-key="(item: ColumnType) => item.prop || Math.random().toString()"
          handle=".drag-handle"
          ghost-class="ghost"
          @change="handleAdvancedDragEnd"
          class="grid-items-wrapper"
        >
          <template #item="{element}">
            <div 
              v-if="!advancedSearchText || 
                element.label?.toLowerCase().includes(advancedSearchText.toLowerCase()) || 
                element.prop?.toLowerCase().includes(advancedSearchText.toLowerCase())"
              class="column-item"
            >
              <div class="column-header">
                <div class="drag-handle">
                  <el-icon><Rank /></el-icon>
                </div>
                <h3>{{ element.label || '未命名列' }}</h3>
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="editColumn(element)"
                >
                  编辑
                </el-button>
              </div>
              
              <el-descriptions :column="1" border>
                <el-descriptions-item label="标签">{{ element.label }}</el-descriptions-item>
                <el-descriptions-item label="属性">{{ element.prop }}</el-descriptions-item>
                <el-descriptions-item label="类型">{{ element.valueType }}</el-descriptions-item>
                <el-descriptions-item label="宽度">{{ element.width }}</el-descriptions-item>
                <el-descriptions-item v-if="type === 'form'" label="必填">
                  <el-tag :type="element.required ? 'danger' : 'info'">
                    {{ element.required ? '是' : '否' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item v-if="type === 'table'" label="可编辑">
                  <el-tag :type="element.editable ? 'success' : 'info'">
                    {{ element.editable ? '是' : '否' }}
                  </el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </template>
        </draggable>
      </div>
      <el-empty v-else description="请先选择要配置的列" />
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="advancedConfigVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 列高级配置弹窗 -->
    <el-dialog 
      v-model="columnDialogVisible" 
      title="高级字段配置" 
      width="600px"
      append-to-body
      :before-close="() => closeColumnDialog(false)"
    >
      <el-form 
        label-width="80px" 
        size="default"
        v-if="currentEditColumn"
      >
        <!-- 高级配置表单 -->
        <el-form-item label="标签">
          <el-input v-model="currentEditColumn.label" />
        </el-form-item>
        
        <el-form-item label="属性">
          <el-input v-model="currentEditColumn.prop" />
        </el-form-item>
        
        <el-form-item label="宽度">
          <el-input-number v-model="currentEditColumn.width" :min="0" />
        </el-form-item>
        
        <el-form-item label="类型">
          <el-select v-model="currentEditColumn.valueType" placeholder="选择字段类型">
            <el-option label="文本" value="text" />
            <el-option label="数字" value="number" />
            <el-option label="输入框" value="input" />
            <el-option label="标签" value="tag" />
            <el-option label="图片" value="img" />
            <el-option label="日期时间" value="date-picker" />
            <el-option label="单选框" value="radio" />
            <el-option label="选择器" value="select" />
            <el-option label="多选框" value="checkbox" />
            <el-option label="密码框" value="password" />
            <el-option label="文本域" value="textarea" />
            <el-option label="上传" value="upload" />
            <el-option label="开关" value="switch" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="提示文本">
          <el-input 
            v-model="currentEditColumn.tooltip" 
            placeholder="鼠标悬停时显示的提示文本"
          />
        </el-form-item>
        
        <el-form-item label="占位符" v-if="type === 'form'">
          <el-input 
            v-model="currentEditColumn.placeholder" 
            placeholder="输入框的占位符文本"
          />
        </el-form-item>
        
        <el-form-item label="必填" v-if="type === 'form'">
          <el-switch v-model="currentEditColumn.required" />
        </el-form-item>
        
        <el-form-item label="可编辑" v-if="type === 'table'">
          <el-switch v-model="currentEditColumn.editable" />
        </el-form-item>
        
        <!-- page类型特有配置 -->
        <template v-if="type === 'page'">
          <el-divider>页面特有配置</el-divider>
          
          <el-form-item label="隐藏在表格">
            <el-switch 
              v-model="currentEditColumn.hideInTable" 
              :active-text="'在PlusPage组件中隐藏'"
            />
          </el-form-item>
          
          <el-form-item label="隐藏在搜索">
            <el-switch 
              v-model="currentEditColumn.hideInSearch" 
              :active-text="'在PlusSearch组件中隐藏'"
            />
          </el-form-item>
        </template>
        
        <!-- 日期时间格式配置 -->
        <template v-if="currentEditColumn.valueType === 'date-picker'">
          <el-divider>日期时间配置</el-divider>
          
          <el-form-item label="日期类型">
            <el-select 
              v-model="currentEditColumn.type" 
              placeholder="选择日期类型"
            >
              <el-option label="日期" value="date" />
              <el-option label="日期时间" value="datetime" />
              <el-option label="年份" value="year" />
              <el-option label="月份" value="month" />
              <el-option label="周" value="week" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="显示格式">
            <el-input 
              v-model="currentEditColumn.format"
              placeholder="如: YYYY-MM-DD"
            >
              <template #append>
                <el-tooltip content="例如：YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="值格式">
            <el-input 
              v-model="currentEditColumn.valueFormat"
              placeholder="如: YYYY-MM-DD"
            >
              <template #append>
                <el-tooltip content="例如：YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </template>
            </el-input>
          </el-form-item>
        </template>
        
        <!-- 选择器和单选框配置 -->
        <template v-if="currentEditColumn.valueType === 'select' || currentEditColumn.valueType === 'radio' || currentEditColumn.valueType === 'tag' || currentEditColumn.valueType === 'checkbox'">
          <el-divider>选项配置</el-divider>
          
          <div class="options-section">
            <div class="options-header">
              <el-button type="primary" size="small" @click="addOption">添加选项</el-button>
            </div>
            
            <div class="options-container">
              <div class="options-grid">
                <div v-for="(option, optIndex) in currentEditColumn.options || []" :key="optIndex" class="option-item">
                  <el-divider>选项 {{ optIndex + 1 }}</el-divider>
                  
                  <el-form-item label="标签">
                    <el-input v-model="option.label" />
                  </el-form-item>
                  
                  <el-form-item label="值">
                    <el-input v-model="option.value" />
                  </el-form-item>
                  
                  <el-form-item label="颜色">
                    <el-select v-model="option.color">
                      <el-option label="默认" value="" />
                      <el-option label="红色" value="red" />
                      <el-option label="蓝色" value="blue" />
                      <el-option label="绿色" value="green" />
                      <el-option label="黄色" value="yellow" />
                      <el-option label="灰色" value="gray" />
                      <el-option label="主要" value="primary" />
                      <el-option label="成功" value="success" />
                      <el-option label="警告" value="warning" />
                      <el-option label="危险" value="danger" />
                      <el-option label="信息" value="info" />
                    </el-select>
                  </el-form-item>
                  
                  <el-button type="danger" size="small" @click="removeOption(optIndex)">
                    删除选项
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </template>
        
        <!-- 文本域配置 -->
        <template v-if="currentEditColumn.valueType === 'textarea'">
          <el-divider>文本域配置</el-divider>
          
          <el-form-item label="行数">
            <el-input-number 
              v-model="currentEditColumn.rows"
              :min="2"
              :max="10"
            />
          </el-form-item>
          
          <el-form-item label="自动高度">
            <el-switch 
              v-model="currentEditColumn.autosize"
            />
          </el-form-item>
        </template>
        
        <!-- 文件上传配置 -->
        <template v-if="currentEditColumn.valueType === 'upload'">
          <el-divider>文件上传配置</el-divider>
          
          <el-form-item label="上传地址">
            <el-input 
              v-model="uploadProps.action" 
              placeholder="/v1/admin/upload"
            />
          </el-form-item>
          
          <el-form-item label="允许多选">
            <el-switch v-model="uploadProps.multiple" />
          </el-form-item>
          
          <el-form-item label="接受类型">
            <el-input 
              v-model="uploadProps.accept" 
              placeholder="如: .jpg,.png,.pdf"
            />
          </el-form-item>
          
          <el-form-item label="数量限制">
            <el-input-number 
              v-model="uploadProps.fileLimit" 
              :min="1" 
              :max="10"
              placeholder="1"
            />
          </el-form-item>
          
          <el-form-item label="大小限制">
            <el-input-number 
              v-model="uploadProps.sizeLimit" 
              :min="1" 
              :step="1"
              placeholder="5120"
            >
              <template #suffix> <span>KB</span></template>
            </el-input-number>
          </el-form-item>
          
          <el-form-item label="文件用途">
            <el-input 
              v-model="uploadProps.fileUsage" 
              placeholder="默认与字段名相同"
            />
          </el-form-item>
          
          <el-form-item label="上传样式">
            <el-select v-model="uploadProps.style">
              <el-option label="默认拖拽区域" value="default" />
              <el-option label="按钮" value="button" />
              <el-option label="链接" value="link" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="显示文件列表">
            <el-switch v-model="uploadProps.showFileList" />
          </el-form-item>
          
          <el-form-item label="提示文字">
            <el-input 
              v-model="uploadProps.tip" 
              placeholder="点击或拖拽文件到此区域上传"
            />
          </el-form-item>
        </template>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeColumnDialog(false)">取消</el-button>
          <el-button type="primary" @click="saveAdvancedConfig">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
// ColumnConfigEditor.vue
// 公共列配置编辑器组件，支持表格和表单的列配置，可在不同组件间复用
// 支持拖拽排序和穿梭框两种模式，并提供高级配置功能
import { ref, computed, watch, onMounted, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import draggable from 'vuedraggable';
import { Rank, ArrowUp, ArrowDown, QuestionFilled } from '@element-plus/icons-vue';

// 定义接口
interface FieldOption {
  label: string;
  value: string | number;
  color?: string;
}

interface ColumnType {
  label: string;
  prop: string;
  valueType?: string;
  width?: number;
  editable?: boolean;
  required?: boolean;
  placeholder?: string;
  tooltip?: string;
  options?: FieldOption[];
  fieldProps?: Record<string, any>;
  [key: string]: any;
}

interface TableParent {
  addRule?: (prop: string, required: boolean) => void;
  removeRule?: (prop: string) => void;
  [key: string]: any;
}

interface FormParent {
  addRule?: (prop: string, required: boolean) => void;
  removeRule?: (prop: string) => void;
  [key: string]: any;
}

// 定义组件接受的props
const props = withDefaults(defineProps<{
  // 配置项
  modelValue: ColumnType[];
  // 所有可选列（穿梭框模式需要）
  allColumns?: ColumnType[];
  // 可用字段列表，兼容availableFields名称
  availableFields?: ColumnType[];
  // 模式：drag(拖拽模式)，transfer(穿梭框模式)
  mode?: 'drag' | 'transfer';
  // 类型：table(表格)，form(表单)
  type?: 'table' | 'form' | 'page' | 'view' | 'search';
  // 所属父组件（用于关联表单规则）
  parent?: TableParent | FormParent;
  // 是否深度监听
  deepWatch?: boolean;
}>(), {
  mode: 'drag',
  type: 'table',
  deepWatch: false,
  allColumns: () => [],
  availableFields: () => []
});

// 定义emit
const emit = defineEmits<{
  (e: 'update:modelValue', value: ColumnType[]): void;
  (e: 'change', value: ColumnType[]): void;
  (e: 'update:allColumns', value: ColumnType[]): void;
}>();

// 当前所处模式
const isTransferMode = computed(() => props.mode === 'transfer');
const isTableMode = computed(() => props.type === 'table');

// 本地管理状态
const expandedFields = reactive<Record<string, boolean>>({});
const columnDialogVisible = ref(false);
const currentEditColumn = ref<ColumnType | null>(null);
const searchText = ref('');
const tempAddedColumn = ref<ColumnType | null>(null);
const editIndex = ref(-1);

// 上传属性代理对象
const uploadProps = computed(() => {
  if (!currentEditColumn.value) return {};
  if (!currentEditColumn.value.uploadProps) {
    currentEditColumn.value.uploadProps = {};
  }
  return currentEditColumn.value.uploadProps;
});

// 高级配置相关
const advancedSearchText = ref('');
const advancedConfigVisible = ref(false);
const advancedColumnsList = ref<ColumnType[]>([]);

// 穿梭框模式下直接使用columns数组
const columns = computed({
  get: () => props.modelValue,
  set: (val: ColumnType[]) => {
    emit('update:modelValue', val);
    emit('change', val);
  }
});

// 使用allColumns或availableFields作为数据源
const availableFieldsData = computed(() => {
  let result = props.allColumns && props.allColumns.length > 0 
    ? props.allColumns 
    : props.availableFields || [];
  
  // 确保所有已选择的字段都在availableFieldsData中
  if (isTransferMode.value && props.modelValue.length > 0) {
    const existingProps = result.map(item => item.prop);
    const missingColumns = props.modelValue.filter(col => 
      col.prop && !existingProps.includes(col.prop)
    );
    
    if (missingColumns.length > 0) {
      console.log('有选中的字段不在availableFieldsData中，添加它们:', missingColumns);
      result = [...result, ...missingColumns];
    }
  }
  
  console.log('Available fields data:', result);
  return result;
});

// 穿梭框模式下，使用已选择的字段作为value
const transferModel = computed({
  get: () => {
    // 构建已选择的字段的prop数组
    const selectedProps = props.modelValue.map((col) => col.prop || '');
    
    // 检查每个prop是否在availableFieldsData中存在
    // 如果不存在，可能是父组件没有正确处理update:allColumns事件
    const missingProps = selectedProps.filter(prop => 
      prop && !availableFieldsData.value.some(field => field.prop === prop)
    );
    
    if (missingProps.length > 0) {
      console.warn('有选中的字段在可用字段列表中不存在:', missingProps);
      console.warn('这可能是因为父组件没有正确处理update:allColumns事件');
      
      // 尝试在运行时添加缺失的字段到availableFieldsData
      const updatedAvailableFields = [...availableFieldsData.value];
      props.modelValue
        .filter(col => missingProps.includes(col.prop || ''))
        .forEach(col => {
          if (!updatedAvailableFields.some(field => field.prop === col.prop)) {
            updatedAvailableFields.push(col);
          }
        });
      
      emit('update:allColumns', updatedAvailableFields);
    }
    
    console.log('TransferModel get - 选中的字段:', selectedProps);
    console.log('TransferModel get - 可用字段:', availableFieldsData.value.map(f => f.prop));
    
    return selectedProps;
  },
  set: (val: string[]) => {
    console.log('TransferModel set:', val);
    handleTransferChange(val);
  }
});

// 监听变更
watch(() => props.modelValue, (newVal) => {
  if (props.deepWatch) {
    // 深度监听变化，确保UI与父组件数据同步
    console.log('Columns deep watched:', newVal);
  }
}, { deep: props.deepWatch });

// 初始化
onMounted(() => {
  console.log('ColumnConfigEditor mounted with mode:', props.mode);
  console.log('ColumnConfigEditor type:', props.type);
  console.log('Initial modelValue:', props.modelValue);
  console.log('Initial availableFieldsData:', availableFieldsData.value);
  
  // 初始化：确保所有已有字段都在allColumns中
  if (isTransferMode.value) {
    const availableFields = [...availableFieldsData.value];
    const missingFields: ColumnType[] = [];
    
    // 检查每个modelValue中的字段是否在availableFields中
    props.modelValue.forEach(column => {
      if (column.prop && !availableFields.some(field => field.prop === column.prop)) {
        missingFields.push(column);
      }
    });
    
    // 如果有缺失的字段，添加到availableFields
    if (missingFields.length > 0) {
      console.log('添加缺失的字段到availableFields:', missingFields);
      // 注意：这里由于props是只读的，我们需要通过emit一个事件来让父组件更新allColumns
      // 如果父组件没有提供更新allColumns的方法，那么这段代码不会有效果
      emit('update:allColumns', [...availableFields, ...missingFields]);
    }
  }
});

// 处理列表拖拽结束
function handleDragEnd() {
  emit('update:modelValue', columns.value);
  emit('change', columns.value);
}

// 处理高级配置中的拖拽结束
function handleAdvancedDragEnd() {
  // 更新父组件的数据，确保拖曳排序能被保存
  // 由于拖曳只影响顺序，我们需要将所有列重新排序并传回去
  const newOrder = advancedColumnsList.value;
  
  // 对于穿梭框模式，需要根据当前的筛选规则重新排序所有数据
  if (isTransferMode.value) {
    // 创建新的排序映射表
    const orderMap = new Map<string, number>();
    newOrder.forEach((col, index) => {
      if (col.prop) {
        orderMap.set(col.prop, index);
      }
    });
    
    // 对所有列按照新顺序排序
    const updatedColumns = [...props.modelValue].sort((a, b) => {
      const aIndex = a.prop ? orderMap.get(a.prop) ?? 999 : 999;
      const bIndex = b.prop ? orderMap.get(b.prop) ?? 999 : 999;
      return aIndex - bIndex;
    });
    
    // 使用深拷贝确保数据类型一致性
    const clonedColumns = updatedColumns.map(col => {
      // 确保返回的对象结构与原对象一致，不引入数组类型
      return JSON.parse(JSON.stringify(col));
    });
    
    // 更新到父组件
    emit('update:modelValue', clonedColumns);
    emit('change', clonedColumns);
  } else {
    // 拖拽模式直接更新所有列，同样使用深拷贝
    const clonedColumns = newOrder.map(col => {
      return JSON.parse(JSON.stringify(col));
    });
    emit('update:modelValue', clonedColumns);
    emit('change', clonedColumns);
  }
}

// 处理表单验证规则
function handleRequiredChange(element: ColumnType, required: boolean) {
  if (element && element.prop && props.parent) {
    // 处理表单验证规则
    if (required && props.parent.addRule) {
      props.parent.addRule(element.prop, required);
    } else if (props.parent.removeRule) {
      props.parent.removeRule(element.prop);
    }
    
    // 更新配置
    handleConfigChange();
  }
}

// 删除列
function removeColumn(index: number) {
  const columnArray = [...columns.value] as ColumnType[];
  const removedColumn = columnArray[index];
  
  // 如果有表单验证规则，需要先移除
  if (props.type === 'form' && removedColumn.required && props.parent && props.parent.removeRule) {
    props.parent.removeRule(removedColumn.prop);
  }
  
  // 删除列
  columnArray.splice(index, 1);
  
  // 更新绑定值
  emit('update:modelValue', columnArray);
  emit('change', columnArray);
}

// // 编辑标签
// function onEditLabel(value: string) {
// }

// // 编辑属性
// function onEditProp(value: string) {
// }

// 显示高级配置
function showAdvancedConfig() {
  if (isTransferMode.value) {
    // 穿梭框模式下，需要有选中的列
    if (transferModel.value.length === 0) {
      ElMessage.warning('请先选择至少一个字段');
      return;
    }
    
    // 初始化高级配置列表 - 穿梭框模式下，根据选中的列进行过滤
    advancedColumnsList.value = props.modelValue.filter(col => 
      transferModel.value.includes(col.prop || '')
    );
  } else {
    // 拖拽模式下，可直接编辑列配置
    if (columns.value.length === 0) {
      ElMessage.warning('没有可配置的列');
      return;
    }
    
    // 拖拽模式下，使用所有列
    advancedColumnsList.value = [...props.modelValue];
  }
  
  // 显示高级配置对话框
  advancedConfigVisible.value = true;
}

// 编辑列配置
function editColumn(column: ColumnType) {
  currentEditColumn.value = JSON.parse(JSON.stringify(column));
  columnDialogVisible.value = true;
  
  // 设置当前编辑的列索引
  const index = props.modelValue.findIndex(col => col.prop === column.prop);
  editIndex.value = index;
}

// 保存高级配置
function saveAdvancedConfig() {
  if (columnDialogVisible.value && currentEditColumn.value) {
    if (editIndex.value > -1) {
      // 编辑现有列
      if (props.modelValue[editIndex.value]) {
        const updatedColumns = [...props.modelValue];
        updatedColumns[editIndex.value] = { ...currentEditColumn.value };
        emit('update:modelValue', updatedColumns);
        emit('change', updatedColumns);
      }
    } else if (isTransferMode.value) {
      // 穿梭框模式下，更新所有列
      const updatedColumns = props.modelValue.map(col => {
        if (col.prop === currentEditColumn.value?.prop) {
          return { ...currentEditColumn.value };
        }
        return col;
      });
      emit('update:modelValue', updatedColumns);
      emit('change', updatedColumns);
    }
    
    columnDialogVisible.value = false;
    currentEditColumn.value = null;
    editIndex.value = -1;
  } else if (advancedConfigVisible.value) {
    // 从高级配置对话框保存
    emit('update:modelValue', advancedColumnsList.value);
    emit('change', advancedColumnsList.value);
    advancedConfigVisible.value = false;
  }
}

// 添加选项
function addOption() {
  if (currentEditColumn.value) {
    if (!currentEditColumn.value.options) {
      currentEditColumn.value.options = [];
    }
    
    currentEditColumn.value.options.push({
      label: '新选项',
      value: `option_${currentEditColumn.value.options.length + 1}`,
      color: ''
    });
  }
}

// 删除选项
function removeOption(index: number) {
  if (currentEditColumn.value && currentEditColumn.value.options) {
    currentEditColumn.value.options.splice(index, 1);
  }
}

// 添加列
function addColumn() {
  if (isTransferMode.value) {
    // 在穿梭框模式下添加新列
    const timestamp = Date.now();
    const newProp = `column_${timestamp}`;
    const newColumn: ColumnType = isTableMode.value 
      ? {
          label: `新列${(props.modelValue as ColumnType[]).length + 1}`,
          prop: newProp,
          valueType: 'text'
        } as ColumnType
      : {
          label: `新列${(props.modelValue as ColumnType[]).length + 1}`,
          prop: newProp,
          valueType: 'text',
          placeholder: '',
          required: false,
          tooltip: ''
        } as ColumnType;
    
    console.log('添加新列:', newColumn);
    
    // 保存临时列，用于取消时删除
    tempAddedColumn.value = newColumn;
    
    // 1. 先确保新列添加到可用字段列表中（availableFieldsData）
    // 创建一个新的allColumns数组
    const updatedAvailableFields = [...availableFieldsData.value];
    if (!updatedAvailableFields.some(field => field.prop === newProp)) {
      console.log('将新列添加到availableFieldsData:', newColumn);
      updatedAvailableFields.push(newColumn);
      emit('update:allColumns', updatedAvailableFields);
    } else {
      console.log('新列已存在于availableFieldsData中');
    }
    
    // 2. 然后添加到模型数据中
    const newColumns = [...(props.modelValue as ColumnType[]), newColumn];
    console.log('更新后的columns:', newColumns);
    emit('update:modelValue', newColumns);
    emit('change', newColumns);
    
    // 3. 打开编辑对话框
    currentEditColumn.value = reactive({ ...newColumn });
    columnDialogVisible.value = true;
  } else {
    // 拖拽模式下添加新列的代码保持不变
    const newColumn: ColumnType = isTableMode.value
      ? {
          label: `新列${columns.value.length + 1}`,
          prop: `column_${Date.now()}`,
          valueType: 'text',
          width: 120
        } as ColumnType
      : {
          label: `新列${columns.value.length + 1}`,
          prop: `column_${Date.now()}`,
          valueType: 'text',
          placeholder: '',
          required: false,
          tooltip: ''
        } as ColumnType;
    
    const newColumns = [...columns.value, newColumn];
    emit('update:modelValue', newColumns);
    emit('change', newColumns);
  }
}

// 配置变更处理函数
function handleConfigChange() {
  emit('change', props.modelValue);
}

// 获取字段属性
function getFieldProp(element: ColumnType, prop: string): any {
  if (element && element.fieldProps) return element.fieldProps[prop];
  return undefined;
}

// 更新字段属性
function updateFieldProp(element: ColumnType, prop: string, value: string | number | boolean) {
  if (element) {
    if (!element.fieldProps) {
      element.fieldProps = {};
    }
    element.fieldProps[prop] = value;
    handleConfigChange();
  }
}

/**
 * 获取上传配置属性值
 * @param element 列元素
 * @param prop 属性名
 * @returns 属性值
 */
function getUploadProp(element: ColumnType, prop: string): any {
  if (!element.uploadProps) {
    element.uploadProps = {};
  }
  return element.uploadProps[prop];
}

/**
 * 更新上传配置属性
 * @param element 列元素
 * @param prop 属性名
 * @param value 属性值
 */
function updateUploadProp(element: ColumnType, prop: string, value: any) {
  if (!element.uploadProps) {
    element.uploadProps = {};
  }
  element.uploadProps[prop] = value;
  handleConfigChange();
}

/**
 * 获取文件大小限制(KB)
 * @param element 列元素
 * @returns 文件大小限制(KB)
 */
function getSizeLimitInKB(element: ColumnType): number {
  const byteSize = getUploadProp(element, 'sizeLimit');
  if (!byteSize) return 5120; // 默认5MB = 5120KB
  return Math.floor(byteSize / 1024);
}

// 添加选项到元素
function addOptionToItem(element: ColumnType) {
  if (element) {
    if (!element.options) {
      element.options = [];
    }
    
    element.options.push({
      label: '选项' + (element.options.length + 1),
      value: element.options.length + 1,
      color: ''
    });
    
    handleConfigChange();
  }
}

// 从元素中删除选项
function removeOptionFromItem(element: ColumnType, index: number) {
  if (element && element.options && index >= 0 && index < element.options.length) {
    element.options.splice(index, 1);
    handleConfigChange();
  }
}

// 导入值枚举到元素
function importValueEnum(element: ColumnType) {
  // 这里可以实现导入逻辑，例如打开一个窗口让用户粘贴JSON格式的enum
  ElMessage.info('该功能将在未来版本中实现');
  
  // 如果没有选项数组，则创建一个
  if (element) {
    if (!element.options) {
      element.options = [];
    }
    
    // 添加示例选项
    element.options.push(
      { label: '选项A', value: 'A', color: 'success' },
      { label: '选项B', value: 'B', color: 'warning' },
      { label: '选项C', value: 'C', color: 'danger' }
    );
    
    handleConfigChange();
  }
}

// 获取选项展开状态
function getOptionsExpanded(elementProp: string): boolean {
  return expandedFields[elementProp] !== false; // 默认展开
}

// 切换选项展开状态
function toggleOptionsExpanded(elementProp: string): void {
  const currentState = getOptionsExpanded(elementProp);
  expandedFields[elementProp] = !currentState;
}

// 关闭列编辑对话框，如果是取消操作，则移除刚添加的列
function closeColumnDialog(save: boolean = false) {
  console.log('closeColumnDialog - 保存:', save, '临时列:', tempAddedColumn.value);
  
  if (!save && tempAddedColumn.value && isTransferMode.value) {
    // 如果是取消操作，并且有临时添加的列，则移除它
    const removedProp = tempAddedColumn.value.prop;
    console.log('移除临时添加的列:', removedProp);
    
    // 从modelValue中移除
    const newColumns = props.modelValue.filter(col => col.prop !== removedProp);
    console.log('移除后的列数组:', newColumns);
    emit('update:modelValue', newColumns);
    emit('change', newColumns);
  }
  
  // 清空临时变量
  tempAddedColumn.value = null;
  currentEditColumn.value = null;
  columnDialogVisible.value = false;
}

// 处理穿梭框数据变化
function handleTransferChange(value: string[]) {
  const currentColumns = props.modelValue;
  const newColumns: ColumnType[] = [];
  
  // 添加所有已选择的列
  for (const key of value) {
    // 查找是否已存在该列
    const existingColumn = currentColumns.find((col) => col.prop === key);
    
    if (existingColumn) {
      // 如果存在，使用现有的列配置
      newColumns.push(existingColumn);
    } else {
      // 如果不存在，创建一个新的列配置
      const field = availableFieldsData.value.find((f) => f.prop === key);
      
      let newColumn: ColumnType;
      if (props.type === 'table') {
        newColumn = {
          label: field ? field.label : key,
          prop: key,
          valueType: 'text'
        };
      } else {
        newColumn = {
          label: field ? field.label : key,
          prop: key,
          valueType: 'text',
          placeholder: '',
          required: false,
          tooltip: ''
        };
      }
      
      newColumns.push(newColumn);
    }
  }
  
  // 更新列配置
  emit('update:modelValue', newColumns);
  emit('change', newColumns);
}
</script>

<style scoped>
.column-config-editor {
  width: 100%;
}

.action-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.draggable-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #f9f9f9;
}

.drag-tip {
  color: #909399;
  font-size: 14px;
  margin-bottom: 8px;
}

.column-item {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 0;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.drag-handle {
  cursor: move;
  position: absolute;
  top: 8px;
  right: 8px;
  color: #909399;
  font-size: 20px;
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
}

.columns-list {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.options-section {
  margin-top: 10px;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.options-container {
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.option-item {
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 10px;
  border: 1px solid #ebeef5;
}

.grid-items-wrapper {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
  width: 100%;
  margin: 0;
  padding: 0;
  margin-top: 16px;
}

.toggle-options-btn {
  display: flex;
  align-items: center;
  gap: 4px;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.action-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.options-section {
  width: 100%;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 16px;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.options-container {
  width: 100%;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.option-item {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 12px;
  background-color: #f9f9f9;
}

.draggable-container {
  margin-top: 8px;
}

.drag-tip {
  color: #909399;
  font-size: 14px;
  margin-bottom: 8px;
}

.column-item {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 0;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.drag-handle {
  cursor: move;
  position: absolute;
  top: 8px;
  right: 8px;
  color: #909399;
  font-size: 20px;
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
}

.transfer-control {
  margin-bottom: 16px;
}
</style>
