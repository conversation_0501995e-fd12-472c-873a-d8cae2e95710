<!-- 
  工具栏配置编辑器组件
  用于编辑工具栏配置，包括是否显示添加按钮、是否启用批量操作以及自定义按钮等
-->
<template>
  <div class="toolbar-config-editor">
    <el-form :model="localConfig" label-width="120px">
      <el-form-item label="显示添加按钮">
        <el-switch v-model="localConfig.showAddButton" @change="handleConfigChange" />
      </el-form-item>
      <el-form-item label="添加按钮文本">
        <el-input v-model="localConfig.addButtonText" @input="handleConfigChange" />
      </el-form-item>
      <el-form-item label="启用批量操作">
        <el-switch v-model="localConfig.enableBatchActions" @change="handleConfigChange" />
      </el-form-item>
      <el-form-item label="自定义按钮">
        <el-button type="primary" @click="addToolbarButton">添加按钮</el-button>
        <div class="draggable-container" style="width: 100%;">
          <p v-if="localConfig.buttons && localConfig.buttons.length > 0" class="drag-tip">
            <el-icon><Rank /></el-icon> 拖拽排序
          </p>
          <draggable 
            v-model="localConfig.buttons"
            :item-key="(item: any) => item.action || Math.random().toString()"
            handle=".drag-handle"
            ghost-class="ghost"
            @change="handleDragEnd"
            class="grid-container"
          >
            <template #item="{element, index}">
              <div class="button-item">
                <div class="drag-handle">
                  <el-icon><Rank /></el-icon>
                </div>
                <el-divider>按钮 {{ index + 1 }}</el-divider>
                <el-form-item label="文本">
                  <el-input v-model="element.text" @input="handleConfigChange" />
                </el-form-item>
                <el-form-item label="类型">
                  <el-select v-model="element.type" @change="handleConfigChange">
                    <el-option label="主要" value="primary" />
                    <el-option label="成功" value="success" />
                    <el-option label="警告" value="warning" />
                    <el-option label="危险" value="danger" />
                    <el-option label="信息" value="info" />
                    <el-option label="默认" value="default" />
                  </el-select>
                </el-form-item>
                <el-form-item label="操作">
                  <el-input v-model="element.action" @input="handleConfigChange" />
                </el-form-item>
                <el-form-item label="图标">
                  <el-input v-model="element.icon" @input="handleConfigChange" />
                </el-form-item>
                <el-form-item label="权限">
                  <el-input v-model="element.permission" @input="handleConfigChange" placeholder="权限标识" />
                </el-form-item>
                <el-form-item label="确认消息">
                  <el-input v-model="element.confirmMessage" @input="handleConfigChange" placeholder="操作前确认消息" />
                </el-form-item>
                <el-button type="danger" @click="removeToolbarButton(index)">删除</el-button>
              </div>
            </template>
          </draggable>
        </div>
      </el-form-item>
    </el-form>
    
    <!-- 工具栏配置源码编辑区 -->
    <!-- JSON可视化预览区 -->
    <div class="json-preview-section">
      <el-divider>
        <div class="divider-actions">
          <el-button link size="small" @click="sourceVisible = !sourceVisible">
            {{ sourceVisible ? '隐藏源码' : '显示源码' }}
            <el-icon>
              <component :is="sourceVisible ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
          </el-button>
          <el-button type="warning" @click="handleFormatSource" v-if="sourceVisible">格式化</el-button>
        </div>
      </el-divider>
      
      <!-- JSON预览 -->
      <el-row :gutter="20">
        <!-- 源码编辑区 -->
        <el-col :span="sourceVisible ? 12 : 0" v-if="sourceVisible">
          <el-input
            v-model="sourceCode"
            type="textarea"
            :rows="10"
            @input="handleSourceChange"
            placeholder="请输入工具栏配置JSON"
          />
        </el-col>
        <!-- JSON预览区 -->
        <el-col :span="sourceVisible ? 12 : 24">
          <div class="json-viewer-container">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>JSON可视化预览</span>
                </div>
              </template>
              <JsonViewer 
                :value="parsedSourceJson" 
                :expandDepth="3" 
                copyable 
                sort 
                boxed 
                theme="light"
                class="text-left"
              />
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { JsonViewer } from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/index.css';
import draggable from 'vuedraggable';

interface ToolbarButton {
  text: string;
  type: string;
  action: string;
  icon?: string;
  permission?: string;
  confirmMessage?: string;
  [key: string]: any;
}

interface ToolbarConfig {
  showAddButton: boolean;
  addButtonText: string;
  enableBatchActions: boolean;
  buttons: ToolbarButton[];
  [key: string]: any;
}

const props = defineProps({
  modelValue: {
    type: Object as () => ToolbarConfig,
    default: () => ({
      showAddButton: true,
      addButtonText: '添加',
      enableBatchActions: false,
      buttons: []
    })
  }
});

const emit = defineEmits(['update:modelValue']);

// 本地配置数据
const localConfig = ref<ToolbarConfig>({} as ToolbarConfig);

// 源码显示状态和源码文本
const sourceVisible = ref(false);
const sourceCode = ref('{}');

// JSON解析结果用于可视化显示
const parsedSourceJson = computed(() => {
  try {
    return JSON.parse(sourceCode.value);
  } catch (error) {
    return { error: '无效的JSON格式' };
  }
});

// 初始化本地配置
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    localConfig.value = JSON.parse(JSON.stringify(newValue));
    // 确保buttons数组存在
    if (!localConfig.value.buttons) {
      localConfig.value.buttons = [];
    }
    updateSourceCode();
  }
}, { immediate: true, deep: true });

/**
 * 更新源码表示
 */
function updateSourceCode() {
  sourceCode.value = JSON.stringify(localConfig.value, null, 2);
}

/**
 * 配置变更处理
 */
function handleConfigChange() {
  updateSourceCode();
  emit('update:modelValue', localConfig.value);
}

/**
 * 源码变化处理
 */
function handleSourceChange() {
  try {
    const parsed = JSON.parse(sourceCode.value);
    localConfig.value = parsed;
    emit('update:modelValue', parsed);
  } catch (error) {
    console.error('JSON格式错误:', error);
  }
}

/**
 * 格式化源码
 */
function handleFormatSource() {
  try {
    const parsed = JSON.parse(sourceCode.value);
    sourceCode.value = JSON.stringify(parsed, null, 2);
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化');
    console.error('JSON格式错误:', error);
  }
}

/**
 * 添加工具栏按钮
 */
function addToolbarButton() {
  if (!localConfig.value.buttons) {
    localConfig.value.buttons = [];
  }
  
  localConfig.value.buttons.push({
    text: '新按钮',
    type: 'primary',
    action: 'action' + (localConfig.value.buttons.length + 1),
    icon: '',
    permission: '',
    confirmMessage: ''
  });
  
  handleConfigChange();
}

/**
 * 删除工具栏按钮
 */
function removeToolbarButton(index: number) {
  if (localConfig.value.buttons && index >= 0 && index < localConfig.value.buttons.length) {
    localConfig.value.buttons.splice(index, 1);
    handleConfigChange();
  }
}

/**
 * 拖拽排序后的处理
 */
function handleDragEnd() {
  handleConfigChange();
}
</script>

<style scoped>
.toolbar-config-editor {
  width: 100%;
}

.json-viewer-container {
  height: 100%;
  overflow: auto;
  max-height: 500px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  width: 100%;
}

.draggable-container {
  margin-top: 10px;
  border: 1px dashed #ccc;
  padding: 10px;
  border-radius: 4px;
}

.drag-tip {
  color: #909399;
  font-size: 14px;
  margin-bottom: 10px;
}

/* 修改为响应式布局 */
.grid-container {
  display: grid !important; /* 确保grid显示不被覆盖 */
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important; /* 确保列设置不被覆盖 */
  gap: 15px;
  width: 100%;
  margin: 0;
  padding: 0;
}

.button-item {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  width: 100%; /* 确保宽度为100% */
  box-sizing: border-box; /* 确保padding不会增加宽度 */
  min-width: 0; /* 防止内容溢出 */
}

.button-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.drag-handle {
  width: 100%;
  cursor: move;
  padding: 5px;
  background: #ecf5ff;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 5px;
}

.source-code-section {
  margin-top: 20px;
}

.box-card {
  width: 100%;
}

.text-left :deep(.vjs-tree),
.text-left :deep(.vjs-tree *) {
  text-align: left !important;
}

.divider-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
</style>
