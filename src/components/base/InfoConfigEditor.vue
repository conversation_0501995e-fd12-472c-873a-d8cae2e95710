<!-- 
  详情配置编辑器组件
  用于编辑详情配置，包括边框、列数、尺寸、方向、详情项等
-->
<template>
  <div class="info-config-editor">
    <el-form :model="localConfig" label-width="120px">
      <el-form-item label="边框">
        <el-switch 
          :model-value="localConfig.border" 
          @update:model-value="(val: boolean) => updateInfoConfig('border', val)"
          @change="handleConfigChange"
        />
      </el-form-item>
      <el-form-item label="列数">
        <el-input-number 
          :model-value="localConfig.column" 
          @update:model-value="(val: number) => updateInfoConfig('column', val)"
          :min="1" 
          :max="6" 
          @change="handleConfigChange"
        />
      </el-form-item>
      <el-form-item label="尺寸">
        <el-select 
          :model-value="localConfig.size" 
          @update:model-value="(val: string) => updateInfoConfig('size', val)"
          @change="handleConfigChange"
        >
          <el-option label="默认" value="default" />
          <el-option label="小" value="small" />
          <el-option label="大" value="large" />
        </el-select>
      </el-form-item>
      <el-form-item label="方向">
        <el-select 
          :model-value="localConfig.direction" 
          @update:model-value="(val: string) => updateInfoConfig('direction', val)"
          @change="handleConfigChange"
        >
          <el-option label="水平" value="horizontal" />
          <el-option label="垂直" value="vertical" />
        </el-select>
      </el-form-item>
      <el-form-item label="标签宽度">
        <el-input-number 
          :model-value="localConfig.width" 
          @update:model-value="(val: number) => updateInfoConfig('width', val)"
          :min="0" 
          @change="handleConfigChange"
        />
      </el-form-item>

    </el-form>
          <el-form-item label="详情项">
        <div class="action-row">
          <el-button type="primary" @click="addInfoItem">添加详情项</el-button>
          <!-- 添加关联排序开关 -->
          <LinkedSortingToggle />
        </div>
        <div class="draggable-container" style="width: 100%;">
          <p v-if="(localConfig.columns || []).length > 0" class="drag-tip">
            <el-icon><Rank /></el-icon> 拖拽排序
          </p>
          <draggable 
            v-model="localConfig.columns"
            :item-key="(item: InfoItem) => item.prop || Math.random().toString()"
            handle=".drag-handle"
            ghost-class="ghost"
            @change="handleDragEnd"
            class="grid-container"
          >
            <template #item="{element, index}">
              <div class="info-item">
                <div class="drag-handle">
                  <el-icon><Rank /></el-icon>
                </div>
                <el-divider>详情项 {{ index + 1 }}</el-divider>
                <el-form-item label="标签">
                  <el-input v-model="element.label" @input="handleConfigChange" />
                </el-form-item>
                <el-form-item label="属性">
                  <el-input v-model="element.prop" @input="handleConfigChange" />
                </el-form-item>
                <el-form-item label="跨行">
                  <el-input-number v-model="element.span" :min="1" :max="24" @change="handleConfigChange" />
                </el-form-item>
                <el-form-item label="类型">
                  <el-select v-model="element.valueType" @change="handleConfigChange">
                    <el-option label="文本" value="text" />
                    <el-option label="标签" value="tag" />
                    <el-option label="图片" value="img" />
                    <el-option label="日期时间" value="date" />
                    <el-option label="自定义" value="custom" />
                  </el-select>
                </el-form-item>
                
                <!-- 标签类型配置 -->
                <template v-if="element.valueType === 'tag'">
                  <el-divider>标签配置</el-divider>
                  
                  <el-form-item label="选项配置">
                    <el-button size="small" type="primary" @click="addOptionToItem(element)">添加选项</el-button>
                    
                    <div v-for="(option, optIndex) in element.options || []" :key="optIndex" class="option-item">
                      <el-divider>选项 {{ optIndex + 1 }}</el-divider>
                      
                      <el-form-item label="标签">
                        <el-input v-model="option.label" @input="handleConfigChange" />
                      </el-form-item>
                      
                      <el-form-item label="值">
                        <el-input v-model="option.value" @input="handleConfigChange" />
                      </el-form-item>
                      
                      <el-form-item label="颜色">
                        <el-select v-model="option.color" @change="handleConfigChange">
                          <el-option label="默认" value="" />
                          <el-option label="主要" value="primary" />
                          <el-option label="成功" value="success" />
                          <el-option label="警告" value="warning" />
                          <el-option label="危险" value="danger" />
                          <el-option label="信息" value="info" />
                        </el-select>
                      </el-form-item>
                      
                      <el-button type="danger" size="small" @click="removeOptionFromItem(element, optIndex)">
                        删除选项
                      </el-button>
                    </div>
                  </el-form-item>
                </template>
                
                <!-- 自定义渲染类型 -->
                <template v-if="element.valueType === 'custom'">
                  <el-form-item label="渲染类型">
                    <el-select 
                      :model-value="getRenderType(element)"
                      @update:model-value="(val: string) => updateRenderType(element, val)"
                      @change="handleConfigChange"
                    >
                      <el-option label="富文本" value="html" />
                      <el-option label="JSON" value="json" />
                      <el-option label="状态展示" value="status" />
                      <el-option label="进度条" value="progress" />
                      <el-option label="评分" value="rate" />
                    </el-select>
                  </el-form-item>
                  
                  <template v-if="getRenderType(element) === 'status'">
                    <el-form-item label="状态配置">
                      <el-button size="small" type="primary" @click="addStatusOption(element)">添加状态</el-button>
                      
                      <div v-for="(option, optIndex) in getStatusOptions(element)" :key="optIndex" class="option-item">
                        <el-divider>状态 {{ optIndex + 1 }}</el-divider>
                        
                        <el-form-item label="状态值">
                          <el-input v-model="option.value" @input="handleConfigChange" />
                        </el-form-item>
                        
                        <el-form-item label="状态文本">
                          <el-input v-model="option.text" @input="handleConfigChange" />
                        </el-form-item>
                        
                        <el-form-item label="状态类型">
                          <el-select v-model="option.status" @change="handleConfigChange">
                            <el-option label="成功" value="success" />
                            <el-option label="警告" value="warning" />
                            <el-option label="危险" value="error" />
                            <el-option label="信息" value="info" />
                          </el-select>
                        </el-form-item>
                        
                        <el-button type="danger" size="small" @click="removeStatusOption(element, optIndex)">
                          删除状态
                        </el-button>
                      </div>
                    </el-form-item>
                  </template>
                </template>
                
                <el-button type="danger" @click="removeInfoItem(index)">删除</el-button>
              </div>
            </template>
          </draggable>
        </div>
      </el-form-item>
    <!-- JSON可视化预览区 -->
    <div class="json-preview-section">
      <el-divider>
        <div class="divider-actions">
          <el-button link size="small" @click="sourceVisible = !sourceVisible">
            {{ sourceVisible ? '隐藏源码' : '显示源码' }}
            <el-icon>
              <component :is="sourceVisible ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
          </el-button>
          <el-button type="warning" @click="handleFormatSource" v-if="sourceVisible">格式化</el-button>
        </div>
      </el-divider>
      
      <!-- JSON预览 -->
      <el-row :gutter="20">
        <!-- 源码编辑区 -->
        <el-col :span="sourceVisible ? 12 : 0" v-if="sourceVisible">
          <el-input
            v-model="sourceCode"
            type="textarea"
            :rows="10"
            @input="handleSourceChange"
            placeholder="请输入详情配置JSON"
          />
        </el-col>
        <!-- JSON预览区 -->
        <el-col :span="sourceVisible ? 12 : 24">
          <div class="json-viewer-container">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>JSON可视化预览</span>
                </div>
              </template>
              <JsonViewer 
                :value="parsedSourceJson" 
                :expandDepth="3" 
                copyable 
                sort 
                boxed 
                theme="light"
                class="text-left"
              />
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { JsonViewer } from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/index.css';
import draggable from 'vuedraggable';
import LinkedSortingToggle from './LinkedSortingToggle.vue';
import { useLinkedSortingStore } from '../../stores/linkedSortingStore';

interface InfoOption {
  label: string;
  value: string | number;
  color?: string;
}

interface StatusOption {
  value: string | number;
  text: string;
  status: string;
}

interface CustomRenderOption {
  type: string;
  options?: StatusOption[];
  [key: string]: any;
}

interface InfoItem {
  label: string;
  prop: string;
  span?: number;
  valueType: string;
  options?: InfoOption[];
  render?: CustomRenderOption;
  [key: string]: any;
}

interface InfoConfig {
  border: boolean;
  column: number;
  size: string;
  direction: string;
  width: number;
  columns: InfoItem[];
  [key: string]: any;
}

const props = defineProps({
  modelValue: {
    type: Object as () => InfoConfig,
    default: () => ({
      border: true,
      column: 3,
      size: 'default',
      direction: 'horizontal',
      width: 100,
      columns: []
    })
  }
});

const emit = defineEmits(['update:modelValue']);

// 本地配置数据
const localConfig = ref<InfoConfig>({} as InfoConfig);

// 源码显示状态，默认不显示
const sourceVisible = ref(false);
const sourceCode = ref('{}');

// JSON解析结果用于可视化显示
const parsedSourceJson = computed(() => {
  try {
    return JSON.parse(sourceCode.value);
  } catch (error) {
    return { error: '无效的JSON格式' };
  }
});

// 获取关联排序状态管理
const linkedSortingStore = useLinkedSortingStore();

// 初始化本地配置
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    // 直接使用JSON.stringify进行深拷贝，避免引用问题
    localConfig.value = JSON.parse(JSON.stringify(newValue));
    
    // 再次确认columns数组存在
    if (!localConfig.value.columns) {
      localConfig.value.columns = [];
    }
    updateSourceCode();
  }
}, { immediate: true, deep: true });

/**
 * 更新源码表示
 */
function updateSourceCode() {
  sourceCode.value = JSON.stringify(localConfig.value, null, 2);
}

/**
 * 配置变更处理
 */
function handleConfigChange() {
  // 在触发更新前先将新值保存为字符串，方便后续比较
  const newConfig = JSON.parse(JSON.stringify(localConfig.value));
  emit('update:modelValue', newConfig);
  updateSourceCode();
}

/**
 * 源码变化处理
 */
function handleSourceChange() {
  try {
    const parsed = JSON.parse(sourceCode.value);
    localConfig.value = parsed;
    emit('update:modelValue', parsed);
  } catch (error) {
    console.error('JSON格式错误:', error);
  }
}

/**
 * 格式化源码
 */
function handleFormatSource() {
  try {
    const parsed = JSON.parse(sourceCode.value);
    sourceCode.value = JSON.stringify(parsed, null, 2);
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化');
    console.error('JSON格式错误:', error);
  }
}

/**
 * 添加详情项
 */
function addInfoItem() {
  if (!localConfig.value.columns) {
    localConfig.value.columns = [];
  }
  
  localConfig.value.columns.push({
    label: '新详情项',
    prop: 'newInfo',
    span: 1,
    valueType: 'text'
  });
  
  handleConfigChange();
}

/**
 * 删除详情项
 */
function removeInfoItem(index: number) {
  if (localConfig.value.columns && index >= 0 && index < localConfig.value.columns.length) {
    localConfig.value.columns.splice(index, 1);
    handleConfigChange();
  }
}

/**
 * 更新详情配置
 */
function updateInfoConfig(key: 'border' | 'column' | 'size' | 'direction' | 'width', value: boolean | number | string) {
  if (key === 'border') {
    localConfig.value.border = value as boolean;
  } else if (key === 'column') {
    localConfig.value.column = value as number;
  } else if (key === 'size') {
    localConfig.value.size = value as string;
  } else if (key === 'direction') {
    localConfig.value.direction = value as string;
  } else if (key === 'width') {
    localConfig.value.width = value as number;
  }
  
  handleConfigChange();
}

/**
 * 获取渲染类型
 */
function getRenderType(element: any): string {
  if (!element.render) return 'html';
  return element.render.type || 'html';
}

/**
 * 更新渲染类型
 */
function updateRenderType(element: any, type: string) {
  if (!element.render) {
    element.render = { type };
  } else {
    element.render.type = type;
  }
  
  // 如果是状态类型且没有选项，添加默认选项
  if (type === 'status' && (!element.render.options || element.render.options.length === 0)) {
    element.render.options = [{
      value: '1',
      text: '正常',
      status: 'success'
    }];
  }
  
  handleConfigChange();
}

/**
 * 获取状态选项
 */
function getStatusOptions(element: any): StatusOption[] {
  if (!element.render || !element.render.options) return [];
  return element.render.options;
}

/**
 * 添加状态选项
 */
function addStatusOption(element: any) {
  if (!element.render) {
    element.render = { type: 'status', options: [] };
  }
  
  if (!element.render.options) {
    element.render.options = [];
  }
  
  element.render.options.push({
    value: element.render.options.length + 1,
    text: '状态' + (element.render.options.length + 1),
    status: 'info'
  });
  
  handleConfigChange();
}

/**
 * 删除状态选项
 */
function removeStatusOption(element: any, index: number) {
  if (element.render && element.render.options && 
      index >= 0 && index < element.render.options.length) {
    element.render.options.splice(index, 1);
    handleConfigChange();
  }
}

/**
 * 添加选项到元素
 */
function addOptionToItem(element: any) {
  if (!element.options) {
    element.options = [];
  }
  
  element.options.push({
    label: '选项' + (element.options.length + 1),
    value: element.options.length + 1,
    color: ''
  });
  
  handleConfigChange();
}

/**
 * 从元素中删除选项
 */
function removeOptionFromItem(element: any, index: number) {
  if (element.options && index >= 0 && index < element.options.length) {
    element.options.splice(index, 1);
    handleConfigChange();
  }
}

/**
 * 拖拽排序后的处理
 */
function handleDragEnd(event: any) {
  console.log('详情项拖拽事件:', event);
  
  // 如果关联排序开启，更新排序映射
  if (linkedSortingStore.enabled && localConfig.value.columns?.length) {
    // 通知其他编辑器排序变化
    linkedSortingStore.updateSortOrder(localConfig.value.columns);
  }
  
  // 进行常规配置更新
  emit('update:modelValue', JSON.parse(JSON.stringify(localConfig.value)));
}

// 监听关联排序状态变化
watch(() => linkedSortingStore.enabled, (enabled) => {
  if (enabled && localConfig.value.columns?.length) {
    // 开启关联排序时，应用排序顺序
    const sortedColumns = linkedSortingStore.sortItemsByMap(localConfig.value.columns);
    
    // 检查排序前后是否有变化，避免不必要的更新
    if (JSON.stringify(sortedColumns) !== JSON.stringify(localConfig.value.columns)) {
      localConfig.value.columns = sortedColumns;
      // 仅当有实际变化时才触发事件
      handleConfigChange();
    }
  }
});

// 监听排序更新时间戳变化，同步更新本地排序
watch(() => linkedSortingStore.updateTimestamp, (timestamp) => {
  if (timestamp && linkedSortingStore.enabled && localConfig.value.columns?.length) {
    console.log('详情配置检测到排序更新:', timestamp);
    // 应用最新的排序顺序
    const sortedColumns = linkedSortingStore.sortItemsByMap(localConfig.value.columns);
    
    // 如果排序后的顺序与当前不同，则更新
    if (JSON.stringify(sortedColumns) !== JSON.stringify(localConfig.value.columns)) {
      localConfig.value.columns = sortedColumns;
      
      // 通知父组件值已更新
      emit('update:modelValue', JSON.parse(JSON.stringify(localConfig.value)));
    }
  }
});
</script>

<style scoped>
.info-config-editor {
  width: 100%;
}

.json-viewer-container {
  height: 100%;
  overflow: auto;
  max-height: 500px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  width: 100%;
}

.text-left :deep(.vjs-tree),
.text-left :deep(.vjs-tree *) {
  text-align: left !important;
}

.divider-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.draggable-container {
  margin-top: 10px;
  border: 1px dashed #ccc;
  padding: 10px;
  border-radius: 4px;
}

.drag-tip {
  color: #909399;
  font-size: 14px;
  margin-bottom: 10px;
}

/* 修改为响应式布局 */
.grid-container {
  display: grid !important; /* 确保grid显示不被覆盖 */
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important; /* 确保列设置不被覆盖 */
  gap: 15px;
  width: 100%;
  margin: 0;
  padding: 0;
}

.info-item {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  width: 100%; /* 确保宽度为100% */
  box-sizing: border-box; /* 确保padding不会增加宽度 */
  min-width: 0; /* 防止内容溢出 */
}

.info-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.option-item {
  margin: 10px 0;
  padding: 10px;
  background: #f0f9eb;
  border-radius: 4px;
}

.drag-handle {
  width: 100%;
  cursor: move;
  padding: 5px;
  background: #ecf5ff;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 5px;
}

.source-code-section {
  margin-top: 20px;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.action-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
</style>
