<!--
  @description GridConfigEditor - 网格项内容配置编辑器
  <AUTHOR> AI
  @date 2025-04-11
-->
<template>
  <div class="grid-config-editor">
    <el-tabs v-model="activeTab" class="config-tabs">
      <el-tab-pane label="基础配置" name="basic">
        <div class="config-section">
          <el-form :model="basicConfig" label-width="100px">
            <el-form-item label="标题">
              <el-input v-model="basicConfig.title" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item label="图标">
              <el-input v-model="basicConfig.icon" placeholder="请输入图标名称" />
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="basicConfig.type" placeholder="请选择内容类型" disabled>
                <el-option
                  v-for="item in contentTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="可刷新">
              <el-switch v-model="basicConfig.refreshable" />
            </el-form-item>
            <el-form-item label="可配置">
              <el-switch v-model="basicConfig.configurable" />
            </el-form-item>
            <el-form-item label="可编辑">
              <el-switch v-model="basicConfig.editable" />
            </el-form-item>
            <el-form-item label="可关闭">
              <el-switch v-model="basicConfig.closable" />
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="内容配置" name="content">
        <div class="config-section content-config-section">
          <!-- 信息卡片配置 -->
          <div v-if="isInfoType" class="info-card-config">
            <h3>信息卡片配置</h3>
            <div class="info-items-container">
              <div 
                v-for="(_item, index) in infoItems" 
                :key="index" 
                class="info-item-config"
              >
                <el-card>
                  <template #header>
                    <div class="info-item-header">
                      <span>信息项 #{{ index + 1 }}</span>
                      <el-button 
                        type="danger" 
                        size="small" 
                        circle 
                        @click="removeInfoItem(index)"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </template>
                  <el-form label-width="80px">
                    <el-form-item label="标签">
                      <el-input v-model="infoItems[index].label" placeholder="标签" />
                    </el-form-item>
                    <el-form-item label="数值">
                      <el-input v-model="infoItems[index].value" placeholder="数值" />
                    </el-form-item>
                    <el-form-item label="颜色">
                      <el-color-picker v-model="infoItems[index].color" />
                    </el-form-item>
                  </el-form>
                </el-card>
              </div>
              <div class="add-info-item">
                <el-button type="primary" @click="addInfoItem">
                  <el-icon><Plus /></el-icon> 添加信息项
                </el-button>
              </div>
            </div>
          </div>
          
          <!-- HTML内容配置 -->
          <div v-else-if="isHtmlType" class="html-content-config">
            <h3>HTML内容配置</h3>
            <el-form>
              <el-form-item>
                <el-input
                  v-model="htmlContent"
                  type="textarea"
                  :rows="15"
                  placeholder="请输入HTML内容"
                  @change="updateHtmlContent"
                />
              </el-form-item>
            </el-form>
          </div>
          
          <!-- 表格配置 -->
          <div v-else-if="isTableType" class="table-config">
            <h3>表格配置</h3>
            <table-config-editor
              v-model="tableConfig"
              @update:modelValue="updateTableConfig"
            />
          </div>
          
          <!-- 表单配置 -->
          <div v-else-if="isFormType" class="form-config">
            <h3>表单配置</h3>
            <form-config-editor
              v-model="formConfig"
              @update:modelValue="updateFormConfig"
            />
          </div>
          
          <!-- 图表配置 -->
          <div v-else-if="isChartType" class="chart-config">
            <h3>图表配置</h3>
            <el-form :model="chartConfig" label-width="120px">
              <el-form-item label="图表类型">
                <el-select v-model="chartConfig.chartType">
                  <el-option label="折线图" value="line" />
                  <el-option label="柱状图" value="bar" />
                  <el-option label="饼图" value="pie" />
                  <el-option label="散点图" value="scatter" />
                </el-select>
              </el-form-item>
              <el-form-item label="数据源类型">
                <el-select v-model="chartConfig.dataSource">
                  <el-option label="静态数据" value="static" />
                  <el-option label="API接口" value="api" />
                </el-select>
              </el-form-item>
              <el-form-item v-if="chartConfig.dataSource === 'api'" label="API地址">
                <el-input v-model="chartConfig.apiUrl" placeholder="请输入API地址" />
              </el-form-item>
              <el-form-item v-if="chartConfig.dataSource === 'static'" label="静态数据">
                <el-input
                  v-model="chartDataText"
                  type="textarea"
                  :rows="8"
                  placeholder="请输入JSON格式的数据"
                  @change="updateChartData"
                />
              </el-form-item>
            </el-form>
          </div>
          
          <!-- 自定义组件配置 -->
          <div v-else-if="isCustomType" class="custom-config">
            <h3>自定义组件配置</h3>
            <el-form>
              <el-form-item label="组件名称">
                <el-input v-model="customConfig.componentName" placeholder="请输入组件名称" />
              </el-form-item>
              <el-form-item label="组件属性">
                <el-input
                  v-model="customPropsText"
                  type="textarea"
                  :rows="8"
                  placeholder="请输入JSON格式的组件属性"
                  @change="updateCustomProps"
                />
              </el-form-item>
            </el-form>
          </div>
          
          <div v-else class="no-config">
            <el-empty description="该类型暂不支持内容配置" />
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="高级配置" name="advanced">
        <div class="config-section">
          <el-form>
            <el-form-item label="原始JSON配置">
              <el-input
                v-model="jsonConfig"
                type="textarea"
                :rows="20"
                placeholder="原始JSON配置"
                @change="updateFromJsonConfig"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
/**
 * GridConfigEditor - 网格项内容配置编辑器
 * 用于编辑网格项的内容配置，支持不同类型的可视化编辑
 */
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, Delete } from '@element-plus/icons-vue';
import TableConfigEditor from './TableConfigEditor.vue';
import FormConfigEditor from './FormConfigEditor.vue';

// 定义组件属性
const props = defineProps<{
  modelValue: any
}>();

// 定义组件事件
const emit = defineEmits<{
  'update:modelValue': [value: any]
}>();

// 内容类型选项
const contentTypes = [
  { label: '信息卡片', value: 'info' },
  { label: 'HTML内容', value: 'html' },
  { label: '表格', value: 'table' },
  { label: '表单', value: 'form' },
  { label: '图表', value: 'chart' },
  { label: '自定义组件', value: 'custom' }
];

// 活动标签页
const activeTab = ref('basic');

// 本地配置副本
const localConfig = ref<any>(null);

// 基础配置
const basicConfig = ref({
  title: '',
  icon: '',
  type: 'info',
  refreshable: true,
  configurable: false,
  editable: true,
  closable: true
});

// JSON配置
const jsonConfig = ref('');

// 判断内容类型
const isInfoType = computed(() => basicConfig.value.type === 'info');
const isHtmlType = computed(() => basicConfig.value.type === 'html');
const isTableType = computed(() => basicConfig.value.type === 'table');
const isFormType = computed(() => basicConfig.value.type === 'form');
const isChartType = computed(() => basicConfig.value.type === 'chart');
const isCustomType = computed(() => basicConfig.value.type === 'custom');

// 信息卡片配置
const infoItems = ref<any[]>([]);

// HTML内容配置
const htmlContent = ref('');

// 表格配置
const tableConfig = ref<any>({
  columns: [],
  dataSource: 'static',
  apiUrl: '',
  staticData: []
});

// 表单配置
const formConfig = ref<any>({
  labelWidth: '100px',
  labelPosition: 'right',
  fields: [],
  buttons: [
    { text: '提交', type: 'primary', action: 'submit' },
    { text: '重置', type: 'default', action: 'reset' }
  ]
});

// 图表配置
const chartConfig = ref<any>({
  chartType: 'bar',
  dataSource: 'static',
  apiUrl: '',
  data: []
});
const chartDataText = ref('');

// 自定义组件配置
const customConfig = ref<any>({
  componentName: '',
  props: {}
});
const customPropsText = ref('');

/**
 * 初始化配置数据
 */
const initConfig = () => {
  if (!props.modelValue) {
    return;
  }
  
  try {
    // 深拷贝接收到的配置
    localConfig.value = JSON.parse(JSON.stringify(props.modelValue));
    
    // 设置基础配置
    basicConfig.value = {
      title: localConfig.value.title || '',
      icon: localConfig.value.icon || '',
      type: localConfig.value.type || 'info',
      refreshable: localConfig.value.refreshable !== false,
      configurable: localConfig.value.configurable === true,
      editable: localConfig.value.editable === true,
      closable: localConfig.value.closable !== false
    };
    
    // 根据类型设置内容配置
    if (isInfoType.value && localConfig.value.config && localConfig.value.config.items) {
      infoItems.value = [...localConfig.value.config.items];
    } else if (isHtmlType.value && localConfig.value.config && localConfig.value.config.html) {
      htmlContent.value = localConfig.value.config.html;
    } else if (isTableType.value && localConfig.value.config) {
      tableConfig.value = { ...localConfig.value.config };
    } else if (isFormType.value && localConfig.value.config) {
      formConfig.value = { ...localConfig.value.config };
    } else if (isChartType.value && localConfig.value.config) {
      chartConfig.value = { ...localConfig.value.config };
      if (chartConfig.value.data) {
        chartDataText.value = JSON.stringify(chartConfig.value.data, null, 2);
      }
    } else if (isCustomType.value && localConfig.value.config) {
      customConfig.value = { ...localConfig.value.config };
      if (customConfig.value.props) {
        customPropsText.value = JSON.stringify(customConfig.value.props, null, 2);
      }
    }
    
    // 更新JSON配置
    jsonConfig.value = JSON.stringify(localConfig.value, null, 2);
  } catch (error) {
    console.error('初始化配置出错:', error);
    ElMessage.error('初始化配置失败');
  }
};

/**
 * 更新整体配置
 */
const updateConfig = () => {
  try {
    // 基础配置
    localConfig.value = {
      ...localConfig.value,
      title: basicConfig.value.title,
      icon: basicConfig.value.icon,
      type: basicConfig.value.type,
      refreshable: basicConfig.value.refreshable,
      configurable: basicConfig.value.configurable,
      editable: basicConfig.value.editable,
      closable: basicConfig.value.closable
    };
    
    // 根据类型更新内容配置
    if (isInfoType.value) {
      localConfig.value.config = {
        ...(localConfig.value.config || {}),
        items: [...infoItems.value]
      };
    } else if (isHtmlType.value) {
      localConfig.value.config = {
        ...(localConfig.value.config || {}),
        html: htmlContent.value
      };
    } else if (isTableType.value) {
      localConfig.value.config = { ...tableConfig.value };
    } else if (isFormType.value) {
      localConfig.value.config = { ...formConfig.value };
    } else if (isChartType.value) {
      localConfig.value.config = { ...chartConfig.value };
    } else if (isCustomType.value) {
      localConfig.value.config = { ...customConfig.value };
    }
    
    // 更新JSON配置
    jsonConfig.value = JSON.stringify(localConfig.value, null, 2);
    
    // 触发更新事件
    emit('update:modelValue', localConfig.value);
  } catch (error) {
    console.error('更新配置出错:', error);
    ElMessage.error('更新配置失败');
  }
};

/**
 * 添加信息项
 */
const addInfoItem = () => {
  infoItems.value.push({
    label: '新信息项',
    value: '0',
    color: '#409EFF'
  });
  updateConfig();
};

/**
 * 移除信息项
 * @param index 信息项索引
 */
const removeInfoItem = (index: number) => {
  infoItems.value.splice(index, 1);
  updateConfig();
};

/**
 * 更新HTML内容
 */
const updateHtmlContent = () => {
  updateConfig();
};

/**
 * 更新表格配置
 */
const updateTableConfig = () => {
  updateConfig();
};

/**
 * 更新表单配置
 */
const updateFormConfig = () => {
  updateConfig();
};

/**
 * 更新图表数据
 */
const updateChartData = () => {
  try {
    chartConfig.value.data = JSON.parse(chartDataText.value);
    updateConfig();
  } catch (error) {
    ElMessage.error('图表数据格式错误，请检查JSON格式');
  }
};

/**
 * 更新自定义组件属性
 */
const updateCustomProps = () => {
  try {
    customConfig.value.props = JSON.parse(customPropsText.value);
    updateConfig();
  } catch (error) {
    ElMessage.error('组件属性格式错误，请检查JSON格式');
  }
};

/**
 * 获取当前配置，供外部组件使用
 * @returns 当前配置
 */
const getConfig = () => {
  updateConfig();
  return JSON.parse(JSON.stringify(localConfig.value));
};

// 向外部暴露方法
defineExpose({
  getConfig
});

/**
 * 从JSON配置更新
 */
const updateFromJsonConfig = () => {
  try {
    const config = JSON.parse(jsonConfig.value);
    localConfig.value = config;
    initConfig();
    emit('update:modelValue', localConfig.value);
  } catch (error) {
    ElMessage.error('JSON格式错误，无法解析');
  }
};

// 监听配置变化
watch(() => props.modelValue, () => {
  initConfig();
}, { deep: true });

// 监听基础配置变化
watch(basicConfig, () => {
  updateConfig();
}, { deep: true });

// 监听信息项变化
watch(infoItems, () => {
  updateConfig();
}, { deep: true });

// 组件挂载时初始化
onMounted(() => {
  initConfig();
});
</script>

<style scoped>
.grid-config-editor {
  height: 100%;
  overflow: auto;
}

.config-tabs {
  height: 100%;
}

.config-section {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.content-config-section {
  min-height: 400px;
}

.info-items-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 16px;
}

.info-item-config {
  width: calc(50% - 8px);
  min-width: 300px;
}

.info-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-info-item {
  margin-top: 16px;
  width: 100%;
  display: flex;
  justify-content: center;
}

.html-content-config,
.table-config,
.form-config,
.chart-config,
.custom-config {
  margin-top: 16px;
}

h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  color: var(--el-text-color-primary);
}

:deep(.el-tabs__content) {
  height: calc(100% - 55px);
  overflow: auto;
}
</style>
