<!-- 
  校验规则编辑器组件
  用于编辑表单的校验规则配置，支持必填校验和自定义校验器
  该组件被设计为可复用组件，可被FormDialogConfigEditor等组件调用
-->
<template>
  <div class="rules-config-editor">
    <el-button type="primary" @click="addFormRule">添加规则</el-button>
    <div v-if="Object.keys(modelValue || {}).length > 0" class="rules-container">
      <div class="rules-grid-container">
        <div v-for="(rules, prop) in modelValue" :key="prop" class="rule-item">
          <el-divider>{{ prop }}</el-divider>
          
          <el-form-item label="字段名">
            <el-select 
              :model-value="prop" 
              @update:model-value="(val: string) => updateRuleKey(prop.toString(), val)"
              placeholder="选择字段名" 
              filterable
              allow-create
              default-first-option
            >
              <!-- 自定义字段选项 -->
              <el-option value="custom" label="自定义字段">
                <div style="display: flex; align-items: center;">
                  <el-icon><Edit /></el-icon>
                  <span style="margin-left: 8px;">自定义字段</span>
                </div>
              </el-option>
              <!-- 从columns中获取的选项 -->
              <el-option 
                v-for="column in columns" 
                :key="column.prop" 
                :label="column.label || column.prop" 
                :value="column.prop"
                :disabled="modelValue && modelValue[column.prop] && column.prop !== prop"
              >
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <span>{{ column.label || column.prop }}</span>
                  <el-tag v-if="modelValue && modelValue[column.prop] && column.prop !== prop" size="small" type="info">已配置</el-tag>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          
          <div class="rule-items-grid">
            <div v-for="(rule, ruleIndex) in rules" :key="ruleIndex" class="rule-item-detail">
              <el-divider>规则 {{ ruleIndex + 1 }}</el-divider>
              
              <!-- 必填校验（主要校验项） -->
              <el-form-item label="是否必填" v-if="ruleIndex === 0">
                <el-switch v-model="rule.required" @change="handleConfigChange" />
              </el-form-item>
              
              <!-- 自定义校验器（附加校验项） -->
              <el-form-item label="附加校验器" v-if="ruleIndex > 0">
                <el-select 
                  :model-value="getValidatorType(rule)"
                  placeholder="选择校验器"
                  @change="(val: string) => handleValidatorChange(rule, val)"
                  clearable
                >
                  <el-option 
                    v-for="option in validatorOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
              
              <!-- 为checkGreaterThan添加阈值输入 -->
              <el-form-item 
                v-if="getValidatorType(rule) === 'checkGreaterThan'" 
                label="阈值"
              >
                <el-input-number 
                  v-model="rule.threshold" 
                  :min="0" 
                  :precision="2"
                  @change="handleConfigChange" 
                />
              </el-form-item>
              
              <el-form-item label="错误消息">
                <el-input v-model="rule.message" @input="handleConfigChange" />
              </el-form-item>
              
              <el-form-item label="触发方式">
                <el-select v-model="rule.trigger" @change="handleConfigChange">
                  <el-option label="变化时" value="change" />
                  <el-option label="失去焦点时" value="blur" />
                </el-select>
              </el-form-item>
              
              <el-button type="danger" @click="removeFormRule(prop.toString(), ruleIndex)">删除规则</el-button>
            </div>
          </div>
          
          <el-button type="primary" size="small" @click="addRuleItem(prop.toString())">添加规则项</el-button>
        </div>
      </div>
    </div>
    
    <!-- 添加规则弹窗 -->
    <el-dialog
      v-model="addRuleDialogVisible"
      title="添加表单校验规则"
      width="500px"
    >
      <el-form label-width="120px">
        <el-form-item label="字段名">
          <el-select 
            v-model="newRuleProp" 
            placeholder="选择字段名" 
            filterable
            allow-create
            default-first-option
          >
            <!-- 自定义字段选项 -->
            <el-option value="custom" label="自定义字段">
              <div style="display: flex; align-items: center;">
                <el-icon><Edit /></el-icon>
                <span style="margin-left: 8px;">自定义字段</span>
              </div>
            </el-option>
            <!-- 从columns中获取的选项 -->
            <el-option 
              v-for="column in columns" 
              :key="column.prop" 
              :label="column.label || column.prop" 
              :value="column.prop"
              :disabled="modelValue && modelValue[column.prop]"
            >
              <div style="display: flex; align-items: center; justify-content: space-between;">
                <span>{{ column.label || column.prop }}</span>
                <el-tag v-if="modelValue && modelValue[column.prop]" size="small" type="info">已配置</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否必填">
          <el-switch v-model="newRuleRequired" />
        </el-form-item>
        <el-form-item label="错误消息">
          <el-input 
            v-model="newRuleMessage" 
            placeholder="输入错误提示信息"
          />
        </el-form-item>
        <el-form-item label="触发方式">
          <el-select v-model="newRuleTrigger" placeholder="选择触发方式">
            <el-option label="变化时" value="change" />
            <el-option label="失去焦点时" value="blur" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addRuleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddFormRule">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
// RulesConfigEditor.vue
// 校验规则编辑器组件，用于编辑表单项的校验规则，支持必填和多种自定义校验器

import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { Edit } from '@element-plus/icons-vue';

// 类型定义
interface FormRule {
  required?: boolean;
  message?: string;
  trigger?: string;
  validator?: any;
  threshold?: number;
}

interface FormRuleSet {
  [key: string]: FormRule[];
}

interface Column {
  label?: string;
  prop: string;
  [key: string]: any;
}

// 定义校验器选项
const validatorOptions = [
  { label: '邮箱', value: 'checkEmail' },
  { label: '手机号', value: 'checkPhone' },
  { label: 'URL', value: 'checkUrl' },
  { label: '数字', value: 'checkNumber' },
  { label: '整数', value: 'checkInteger' },
  { label: '浮点数', value: 'checkFloat' },
  { label: '身份证', value: 'checkIdCard' },
  { label: '密码', value: 'checkPassword' },
  { label: '用户名', value: 'checkUsername' },
  { label: 'IP地址', value: 'checkIpAddress' },
  { label: '邮政编码', value: 'checkZipCode' },
  { label: '大于某值', value: 'checkGreaterThan' },
];

// 定义组件接受的props
const props = defineProps<{
  modelValue: FormRuleSet;
  columns?: Column[];
}>();

// 定义emit
const emit = defineEmits<{
  (e: 'update:modelValue', value: FormRuleSet): void;
  (e: 'change'): void;
}>();

// 新增规则相关变量
const addRuleDialogVisible = ref(false);
const newRuleProp = ref('');
const newRuleRequired = ref(true);
const newRuleMessage = ref('');
const newRuleTrigger = ref('blur');

// 添加表单规则
const addFormRule = () => {
  addRuleDialogVisible.value = true;
  newRuleProp.value = '';
  newRuleRequired.value = true;
  newRuleMessage.value = '该字段不能为空';
  newRuleTrigger.value = 'blur';
};

// 确认添加表单规则
const confirmAddFormRule = () => {
  if (!newRuleProp.value) {
    ElMessage.warning('请输入字段名');
    return;
  }
  
  // 检查是否已存在该字段的规则
  if (props.modelValue && props.modelValue[newRuleProp.value]) {
    ElMessage.warning(`字段 "${newRuleProp.value}" 的规则已存在`);
    return;
  }
  
  // 创建新规则
  const rules: FormRule[] = [{
    required: newRuleRequired.value,
    message: newRuleMessage.value || '该字段不能为空',
    trigger: newRuleTrigger.value || 'blur'
  }];
  
  // 更新规则集
  const updatedRules = { 
    ...(props.modelValue || {}),
    [newRuleProp.value]: rules 
  };
  
  emit('update:modelValue', updatedRules);
  emit('change');
  addRuleDialogVisible.value = false;
  
  ElMessage.success(`已成功添加字段 "${newRuleProp.value}" 的校验规则`);
};

// 添加规则项到已有字段
const addRuleItem = (prop: string) => {
  if (!props.modelValue || !props.modelValue[prop]) {
    return;
  }
  
  const updatedRules = { ...props.modelValue };
  updatedRules[prop] = [
    ...updatedRules[prop],
    {
      message: '请输入有效的内容',
      trigger: 'blur'
    }
  ];
  
  emit('update:modelValue', updatedRules);
  emit('change');
  
  ElMessage.success(`已成功添加字段 "${prop}" 的附加校验规则`);
};

// 删除表单规则
const removeFormRule = (prop: string, ruleIndex: number) => {
  if (!props.modelValue || !props.modelValue[prop]) {
    return;
  }
  
  const updatedRules = { ...props.modelValue };
  
  // 如果是最后一个规则，则删除整个字段的规则
  if (updatedRules[prop].length === 1) {
    delete updatedRules[prop];
    emit('update:modelValue', updatedRules);
    emit('change');
    ElMessage.success(`已删除字段 "${prop}" 的所有校验规则`);
    return;
  }
  
  // 删除指定索引的规则
  updatedRules[prop] = updatedRules[prop].filter((_, index) => index !== ruleIndex);
  emit('update:modelValue', updatedRules);
  emit('change');
  
  ElMessage.success(`已删除字段 "${prop}" 的第 ${ruleIndex + 1} 个校验规则`);
};

// 更新规则字段名
const updateRuleKey = (oldKey: string, newKey: string) => {
  if (oldKey === newKey || !newKey) return;
  
  if (props.modelValue[newKey]) {
    ElMessage.warning(`字段 "${newKey}" 的规则已存在`);
    return;
  }
  
  const updatedRules = { ...props.modelValue };
  
  // 创建新键并保留原值
  updatedRules[newKey] = [...updatedRules[oldKey]];
  
  // 删除旧键
  delete updatedRules[oldKey];
  
  emit('update:modelValue', updatedRules);
  emit('change');
  
  ElMessage.success(`已将字段名由 "${oldKey}" 修改为 "${newKey}"`);
};

// 获取默认验证器错误消息
const getDefaultValidatorMessage = (validatorType: string): string => {
  switch (validatorType) {
    case 'checkEmail': return '请输入正确的邮箱地址';
    case 'checkPhone': return '请输入正确的手机号码';
    case 'checkUrl': return '请输入正确的URL地址';
    case 'checkNumber': return '请输入有效的数字';
    case 'checkInteger': return '请输入整数';
    case 'checkFloat': return '请输入有效的小数';
    case 'checkIdCard': return '请输入正确的身份证号码';
    case 'checkPassword': return '密码必须包含字母和数字，长度在6-20位之间';
    case 'checkUsername': return '用户名只能包含字母、数字和下划线，长度在3-20位之间';
    case 'checkIpAddress': return '请输入正确的IP地址';
    case 'checkZipCode': return '请输入正确的邮政编码';
    case 'checkGreaterThan': return '输入值必须大于指定阈值';
    default: return '请输入有效的内容';
  }
};

// 获取校验器类型
const getValidatorType = (rule: any): string => {
  if (!rule.validator) return '';
  
  // 对象形式的验证器
  if (typeof rule.validator === 'object' && rule.validator.name) {
    return rule.validator.name;
  }
  
  // 字符串形式的验证器
  if (typeof rule.validator === 'string') {
    return rule.validator;
  }
  
  // 函数形式的验证器
  if (typeof rule.validator === 'function') {
    const funcStr = rule.validator.toString();
    for (const option of validatorOptions) {
      if (funcStr.includes(option.value)) {
        return option.value;
      }
    }
  }
  
  return '';
};

// 处理校验器变更
const handleValidatorChange = (rule: any, validatorType: string) => {
  if (!validatorType) {
    // 清除验证器
    rule.validator = undefined;
    delete rule.threshold;
    handleConfigChange();
    return;
  }
  
  rule.validator = validatorType;
  
  // 设置默认错误消息
  if (!rule.message || rule.message === '请输入有效的内容') {
    rule.message = getDefaultValidatorMessage(validatorType);
  }
  
  // 为 checkGreaterThan 添加默认阈值
  if (validatorType === 'checkGreaterThan' && rule.threshold === undefined) {
    rule.threshold = 0;
  }
  
  handleConfigChange();
};

// 处理配置变更
const handleConfigChange = () => {
  emit('change');
};
</script>

<style scoped>
.rules-config-editor {
  width: 100%;
}

.rules-container {
  margin-top: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
  background-color: #f5f7fa;
}

.rules-grid-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.rule-item {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  background-color: #fff;
}

.rule-items-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.rule-item-detail {
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

@media (min-width: 768px) {
  .rules-grid-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1200px) {
  .rules-grid-container {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
