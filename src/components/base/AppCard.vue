<!-- src/components/base/AppCard.vue -->
<template>
    <div :class="['app-card', { 'app-card--shadow': shadow }]">
      <!-- 插槽内容 -->
      <slot></slot>
    </div>
  </template>
  
  <script setup lang="ts">
  // defineProps是Vue 3的编译器宏，不需要导入
  
  defineProps({
    shadow: {
      type: Boolean,
      default: true
    }
  });
  </script>
  
  <style scoped lang="scss">
  .app-card {
    border-radius: 4px;
    background-color: #fff;
    padding: 20px;
  
    &--shadow {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
  }
  </style>