<!-- 
  搜索配置编辑器组件
  用于编辑搜索配置，包括搜索项配置、布局和高级搜索等
-->
<template>
  <div class="search-config-editor">
    <el-form :model="localConfig" label-width="120px">
      <el-form-item label="搜索项">
        <div class="action-row">
          <el-button type="primary" @click="addSearchItem">添加搜索项</el-button>
          <!-- 添加关联排序开关 -->
          <LinkedSortingToggle />
        </div>
        <div class="draggable-container" style="width: 100%;">
          <p v-if="localConfig.columns && localConfig.columns.length > 0" class="drag-tip">
            <el-icon><Rank /></el-icon> 拖拽排序
          </p>
          <draggable 
            v-model="localConfig.columns"
            :item-key="(item: SearchItem) => item.prop || Math.random().toString()"
            handle=".drag-handle"
            ghost-class="ghost"
            @change="handleDragEnd"
            class="grid-container"
          >
            <template #item="{element, index}">
              <div class="search-item">
                <div class="drag-handle">
                  <el-icon><Rank /></el-icon>
                </div>
                <el-divider>搜索项 {{ index + 1 }}</el-divider>
                <el-form-item label="标签">
                  <el-input v-model="element.label" @input="handleConfigChange" />
                </el-form-item>
                <el-form-item label="属性">
                  <el-input v-model="element.prop" @input="handleConfigChange" />
                </el-form-item>
                <el-form-item label="类型">
                  <el-select v-model="element.valueType" @change="handleConfigChange">
                    <el-option label="文本输入" value="input" />
                    <el-option label="数字输入" value="number" />
                    <el-option label="选择器" value="select" />
                    <el-option label="日期选择器" value="date-picker" />
                    <el-option label="日期范围" value="daterange" />
                  </el-select>
                </el-form-item>
                <el-form-item label="占位提示">
                  <el-input v-model="element.placeholder" @input="handleConfigChange" />
                </el-form-item>
                
                <!-- 日期选择器配置 -->
                <template v-if="element.valueType === 'date-picker' || element.valueType === 'daterange'">
                  <el-divider>日期时间配置</el-divider>
                  
                  <el-form-item label="日期类型">
                    <el-select 
                      :model-value="getFieldProp(element, 'type')" 
                      @update:model-value="(val: string) => updateFieldProp(element, 'type', val)"
                      placeholder="选择日期类型"
                    >
                      <el-option label="日期" value="date" />
                      <el-option label="日期时间" value="datetime" />
                      <el-option label="年份" value="year" />
                      <el-option label="月份" value="month" />
                      <el-option label="周" value="week" />
                      <el-option label="日期范围" value="daterange" />
                      <el-option label="日期时间范围" value="datetimerange" />
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item label="显示格式">
                    <el-input 
                      :model-value="getFieldProp(element, 'format')" 
                      @update:model-value="(val: string) => updateFieldProp(element, 'format', val)"
                      placeholder="如: YYYY-MM-DD"
                    />
                  </el-form-item>
                  
                  <el-form-item label="值格式">
                    <el-input 
                      :model-value="getFieldProp(element, 'valueFormat')" 
                      @update:model-value="(val: string) => updateFieldProp(element, 'valueFormat', val)"
                      placeholder="如: YYYY-MM-DD"
                    />
                  </el-form-item>
                </template>
                
                <!-- 选择器配置 -->
                <template v-if="element.valueType === 'select'">
                  <el-divider>选项配置</el-divider>
                  
                  <el-form-item label="选项">
                    <el-button size="small" type="primary" @click="addOptionToItem(element)">添加选项</el-button>
                    <div v-for="(option, optIndex) in element.options || []" :key="optIndex" class="option-item">
                      <el-divider>选项 {{ optIndex + 1 }}</el-divider>
                      
                      <el-form-item label="标签">
                        <el-input v-model="option.label" @input="handleConfigChange" />
                      </el-form-item>
                      
                      <el-form-item label="值">
                        <el-input v-model="option.value" @input="handleConfigChange" />
                      </el-form-item>
                      
                      <el-button type="danger" size="small" @click="removeOptionFromItem(element, optIndex)">
                        删除选项
                      </el-button>
                    </div>
                  </el-form-item>
                </template>
                
                <el-button type="danger" @click="removeSearchItem(index)">删除</el-button>
              </div>
            </template>
          </draggable>
        </div>
      </el-form-item>
    </el-form>
    
    <!-- JSON可视化预览区 -->
    <div class="json-preview-section">
      <el-divider>
        <div class="divider-actions">
          <el-button link size="small" @click="sourceVisible = !sourceVisible">
            {{ sourceVisible ? '隐藏源码' : '显示源码' }}
            <el-icon>
              <component :is="sourceVisible ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
          </el-button>
          <el-button type="warning" @click="handleFormatSource" v-if="sourceVisible">格式化</el-button>
        </div>
      </el-divider>
      
      <!-- JSON预览 -->
      <el-row :gutter="20">
        <!-- 源码编辑区 -->
        <el-col :span="sourceVisible ? 12 : 0" v-if="sourceVisible">
          <el-input
            v-model="sourceCode"
            type="textarea"
            :rows="10"
            @input="handleSourceChange"
            placeholder="请输入搜索配置JSON"
          />
        </el-col>
        <!-- JSON预览区 -->
        <el-col :span="sourceVisible ? 12 : 24">
          <div class="json-viewer-container">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>JSON可视化预览</span>
                </div>
              </template>
              <JsonViewer 
                :value="parsedSourceJson" 
                :expandDepth="3" 
                copyable 
                sort 
                boxed 
                theme="light"
                class="text-left"
              />
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { JsonViewer } from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/index.css';
import draggable from 'vuedraggable';
import LinkedSortingToggle from './LinkedSortingToggle.vue';
import { useLinkedSortingStore } from '../../stores/linkedSortingStore';

interface SearchItemOption {
  label: string;
  value: string | number;
}

interface SearchItem {
  label: string;
  prop: string;
  valueType: string;
  placeholder?: string;
  options?: SearchItemOption[];
  fieldProps?: Record<string, any>;
  [key: string]: any;
}

interface SearchConfig {
  columns: SearchItem[];
  [key: string]: any;
}

const props = defineProps({
  type: {
    type: String,
    default: 'search'
  },
  modelValue: {
    type: Object as () => SearchConfig,
    default: () => ({
      columns: []
    })
  }
});

const emit = defineEmits(['update:modelValue']);

// 本地配置数据
const localConfig = ref<SearchConfig>({} as SearchConfig);

// 源码显示状态，默认不显示
const sourceVisible = ref(false);
const sourceCode = ref('{}');

// JSON解析结果用于可视化显示
const parsedSourceJson = computed(() => {
  try {
    return JSON.parse(sourceCode.value);
  } catch (error) {
    return { error: '无效的JSON格式' };
  }
});

// 获取关联排序状态管理
const linkedSortingStore = useLinkedSortingStore();

// 初始化本地配置
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    // 直接使用JSON.stringify进行深拷贝，避免引用问题
    localConfig.value = JSON.parse(JSON.stringify(newValue));
    
    // 再次确认columns数组存在
    if (!localConfig.value.columns) {
      localConfig.value.columns = [];
    }
  }
  
  // 更新源码表示
  updateSourceCode();
}, { immediate: true });

// 监听关联排序状态变化
watch(() => linkedSortingStore.enabled, (enabled) => {
  if (enabled && localConfig.value.columns?.length) {
    // 开启关联排序时，应用排序顺序
    const sortedColumns = linkedSortingStore.sortItemsByMap(localConfig.value.columns);
    localConfig.value.columns = sortedColumns;
    
    // 通知父组件值已更新
    emit('update:modelValue', JSON.parse(JSON.stringify(localConfig.value)));
  }
});

// 监听排序更新时间戳变化，同步更新本地排序
watch(() => linkedSortingStore.updateTimestamp, (timestamp) => {
  if (timestamp && linkedSortingStore.enabled && localConfig.value.columns?.length) {
    console.log('搜索配置检测到排序更新:', timestamp);
    // 应用最新的排序顺序
    const sortedColumns = linkedSortingStore.sortItemsByMap(localConfig.value.columns);
    
    // 如果排序后的顺序与当前不同，则更新
    if (JSON.stringify(sortedColumns) !== JSON.stringify(localConfig.value.columns)) {
      localConfig.value.columns = sortedColumns;
      
      // 通知父组件值已更新
      emit('update:modelValue', JSON.parse(JSON.stringify(localConfig.value)));
    }
  }
});

/**
 * 更新源码表示
 */
function updateSourceCode() {
  sourceCode.value = JSON.stringify(localConfig.value, null, 2);
}

/**
 * 配置变更处理
 */
function handleConfigChange() {
  // 在触发更新前先将新值保存为字符串，方便后续比较
  const newConfig = JSON.parse(JSON.stringify(localConfig.value));
  emit('update:modelValue', newConfig);
  updateSourceCode();
}

/**
 * 源码变化处理
 */
function handleSourceChange() {
  try {
    const parsed = JSON.parse(sourceCode.value);
    localConfig.value = parsed;
    emit('update:modelValue', parsed);
  } catch (error) {
    console.error('JSON格式错误:', error);
  }
}

/**
 * 格式化源码
 */
function handleFormatSource() {
  try {
    const parsed = JSON.parse(sourceCode.value);
    sourceCode.value = JSON.stringify(parsed, null, 2);
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化');
    console.error('JSON格式错误:', error);
  }
}

/**
 * 添加搜索项
 */
function addSearchItem() {
  if (!localConfig.value.columns) {
    localConfig.value.columns = [];
  }
  
  localConfig.value.columns.push({
    label: '新搜索项',
    prop: 'newSearch',
    valueType: 'input',
    placeholder: '请输入'
  });
  
  handleConfigChange();
}

/**
 * 删除搜索项
 */
function removeSearchItem(index: number) {
  if (localConfig.value.columns && index >= 0 && index < localConfig.value.columns.length) {
    localConfig.value.columns.splice(index, 1);
    handleConfigChange();
  }
}

/**
 * 获取字段属性
 */
function getFieldProp(element: any, prop: string): any {
  if (!element.fieldProps) return undefined;
  return element.fieldProps[prop];
}

/**
 * 更新字段属性
 */
function updateFieldProp(element: any, prop: string, value: any) {
  if (!element.fieldProps) {
    element.fieldProps = {};
  }
  element.fieldProps[prop] = value;
  handleConfigChange();
}

/**
 * 添加选项到元素
 */
function addOptionToItem(element: any) {
  if (!element.options) {
    element.options = [];
  }
  
  element.options.push({
    label: '选项' + (element.options.length + 1),
    value: element.options.length + 1
  });
  
  handleConfigChange();
}

/**
 * 从元素中删除选项
 */
function removeOptionFromItem(element: any, index: number) {
  if (element.options && index >= 0 && index < element.options.length) {
    element.options.splice(index, 1);
    handleConfigChange();
  }
}

/**
 * 拖拽排序后的处理
 */
function handleDragEnd(event: any) {
  console.log('搜索项拖拽事件:', event);
  
  // 如果关联排序开启，更新排序映射
  if (linkedSortingStore.enabled && localConfig.value.columns?.length) {
    // 通知其他编辑器排序变化
    linkedSortingStore.updateSortOrder(localConfig.value.columns);
  }
  
  // 进行常规配置更新
  emit('update:modelValue', JSON.parse(JSON.stringify(localConfig.value)));
}
</script>

<style scoped>
.search-config-editor {
  width: 100%;
}

.json-viewer-container {
  height: 100%;
  overflow: auto;
  max-height: 500px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  width: 100%;
}


.divider-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.draggable-container {
  margin-top: 10px;
  border: 1px dashed #ccc;
  padding: 10px;
  border-radius: 4px;
}

.drag-tip {
  color: #909399;
  font-size: 14px;
  margin-bottom: 10px;
}

/* 修改为响应式布局 */
.grid-container {
  display: grid !important; /* 确保grid显示不被覆盖 */
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important; /* 确保列设置不被覆盖 */
  gap: 15px;
  width: 100%;
  margin: 0;
  padding: 0;
}

.search-item {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  width: 100%; /* 确保宽度为100% */
  box-sizing: border-box; /* 确保padding不会增加宽度 */
  min-width: 0; /* 防止内容溢出 */
}

.search-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.option-item {
  margin: 10px 0;
  padding: 10px;
  background: #f0f9eb;
  border-radius: 4px;
}

.drag-handle {
  width: 100%;
  cursor: move;
  padding: 5px;
  background: #ecf5ff;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 5px;
}

.source-code-section {
  margin-top: 20px;
}

.action-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
</style>
