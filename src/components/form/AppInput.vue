<!-- src/components/form/AppInput.vue -->
<template>
    <el-input
      v-model="modelValue"
      :placeholder="placeholder"
      :type="type"
      :prefix-icon="prefixIcon"
      :show-password="showPassword"
    />
  </template>
  
  <script setup lang="ts">
  import { computed } from 'vue';
  
  const props = defineProps({
    modelValue: {
      type: [String, Number],
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'text'
    },
    prefixIcon: {
      type: String,
      default: ''
    },
    showPassword: {
      type: Boolean,
      default: false
    }
  });
  
  const emits = defineEmits(['update:modelValue']);
  
  const modelValue = computed({
    get: () => props.modelValue,
    set: (value) => emits('update:modelValue', value)
  });
  </script>