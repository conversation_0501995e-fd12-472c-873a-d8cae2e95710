<!-- src/components/form/AppForm.vue -->
<template>
    <el-form
      :model="model"
      :rules="rules"
      ref="formRef"
      :label-position="labelPosition"
      @submit.prevent
    >
      <slot></slot>
    </el-form>
  </template>
  
  <script setup lang="ts">
  import {  ref, provide } from 'vue';
  import type { PropType } from 'vue';
  import { ElForm } from 'element-plus';
  
  defineProps({
    model: {
      type: Object,
      required: true
    },
    rules: {
      type: Object,
      default: () => ({})
    },
    labelPosition: {
      type: String as PropType<'right' | 'top' | 'left'>,
      default: 'top'
    }
  });
  
  const formRef = ref<InstanceType<typeof ElForm>>();
  
  const emit = defineEmits(['submit']);
  
  provide('formRef', formRef);
  
  // 暴露validate方法给父组件
  defineExpose({
    validate: async (callback: (valid: boolean) => void) => {
      if (!formRef.value) return;
      return formRef.value.validate(callback);
    }
  });
  </script>