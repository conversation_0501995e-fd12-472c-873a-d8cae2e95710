<!-- src/components/form/AppFormItem.vue -->
<template>
  <el-form-item :label="label" :prop="prop" :label-width="width" label-position="left">
    <slot></slot>
  </el-form-item>
</template>

<script setup lang="ts">
// import { defineProps } from 'vue';

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  prop: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '80px' // 设置默认值
  }
});

// 调试信息
console.log('Label Width:', props.width);
</script>

<style scoped lang="scss">
.el-form-item {
  display: flex;
  align-items: center; /* 确保 label 和 input 在同一行 */
  margin-bottom: 15px; /* 调整间距 */
}

.el-form-item__label {
  flex: 0 0 v-bind(width); /* 固定 label 的宽度 */
  white-space: nowrap; /* 防止 label 换行 */
  text-align: right; /* 文字右对齐 */
  padding-right: 10px; /* 右侧内边距 */
}

.el-form-item__content {
  flex: 1 1 auto; /* 使 input 占据剩余空间 */
}
</style>