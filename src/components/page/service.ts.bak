/**
 * 页面组件服务层
 * 用于处理CRUD操作和数据转换
 */
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { get, post, put, del } from '@/utils/request';
import dayjs from 'dayjs';
import type { 
  PageServiceOptions, 
  PageDataParams, 
  PageFormData, 
  RequestConfig, 
  ResponseData,
  CustomPlusColumn,
  SearchConfig,
  TableConfig,
  FormConfig
} from './types';

/**
 * 根据valueType格式化数据值
 * @param value 原始值
 * @param valueType 值类型
 * @returns 格式化后的值
 */
export function formatValueByType(value: any, valueType?: string): any {
  if (value === null || value === undefined || value === '') {
    return '';
  }

  try {
    switch (valueType) {
      case 'date-picker':
      case 'date':
        // 日期格式化为 YYYY-MM-DD
        return value ? dayjs(value).format('YYYY-MM-DD') : '';
      
      case 'datetime-picker':
      case 'dateTime':
        // 日期时间格式化为 YYYY-MM-DD HH:mm:ss
        return value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '';
      
      case 'time-picker':
      case 'time':
        // 时间格式化为 HH:mm:ss
        return value ? dayjs(value).format('HH:mm:ss') : '';
      
      case 'money':
      case 'price':
        // 金额格式化，保留2位小数
        return typeof value === 'number' ? value.toFixed(2) : value;
      
      case 'percent':
        // 百分比格式化
        return typeof value === 'number' ? `${(value * 100).toFixed(2)}%` : value;
      
      case 'img':
      case 'image':
        // 图片无需处理，直接返回URL
        return value;
      // case 'tag':
      // case 'select':
      // case 'radio':
      //   return String(value);

      
      default:
        return value;
    }
  } catch (error) {
    console.error('格式化值错误:', error);
    return value;
  }
}

/**
 * 解析columns配置，生成对应的组件配置
 * @param columns 列配置
 * @returns 组件配置对象
 */
export function parseColumnsToConfig(columns: CustomPlusColumn[]): {
  searchConfig: SearchConfig,
  tableConfig: TableConfig,
  formConfig: FormConfig
} {
  if (!columns || !Array.isArray(columns) || columns.length === 0) {
    return {
      searchConfig: { columns: [] },
      tableConfig: { columns: [] },
      formConfig: { columns: [] }
    };
  }

  // 搜索配置的列 - 过滤掉hideInSearch为true的列
  const searchColumns = columns.filter(col => !col.hideInSearch);
  
  // 表格配置的列 - 过滤掉hideInTable为true的列
  const tableColumns = columns.filter(col => !col.hideInTable);
  
  // 表单配置的列 - 过滤掉hideInForm为true的列
  const formColumns = columns.filter(col => !col.hideInForm);

  // valueType映射处理 - 表单和表格的valueType有时不一致
  const mappedFormColumns = formColumns.map(col => {
    const newCol = { ...col };
    
    // 处理valueType映射
    if (col.valueType) {
      // 图片处理：表格中为img，表单中为image/upload
      if (col.valueType === 'img') {
        newCol.valueType = 'upload';
      }
      
      // 处理其他可能的映射...
    }
    
    return newCol;
  });

  return {
    searchConfig: { 
      columns: searchColumns,
      span: 8, // 默认每行显示3项
      labelWidth: '100px' // 默认标签宽度
    },
    tableConfig: { 
      columns: tableColumns,
      showIndex: true, // 默认显示序号
      showSelection: false // 默认不显示选择框
    },
    formConfig: { 
      columns: mappedFormColumns,
      labelWidth: '100px', // 默认标签宽度
      labelPosition: 'right' // 默认标签位置
    }
  };
}

/**
 * 基于columns对数据进行格式化处理
 * @param data 原始数据
 * @param columns 列配置
 * @returns 格式化后的数据
 */
export function formatDataByColumns(
  data: any[],
  columns: CustomPlusColumn[]
): any[] {
  if (!data || !Array.isArray(data) || data.length === 0 || !columns || columns.length === 0) {
    return data;
  }

  return data.map(item => {
    // 运行时检查是否为对象
    if (typeof item !== 'object' || item === null) {
      return item;
    }

    const formatted = { ...item };
    
    columns.forEach(column => {
      if (column.prop && column.prop in item && column.valueType) {
        formatted[column.prop] = formatValueByType(item[column.prop], column.valueType);
      }
    });
    
    return formatted;
  });
}

/**
 * 创建页面服务
 * @param options 服务配置项
 * @returns 页面服务对象
 */
export function createPageService<T = any>(options: PageServiceOptions<T>) {
  // 状态和引用
  const loading = ref(false);
  const data = ref<T[]>([]);
  const total = ref(0);
  const currentRow = ref<T | null>(null);
  const formVisible = ref(false);
  const formTitle = ref('');
  const formMode = ref<'add' | 'edit'>('add');
  const formData = reactive<PageFormData>({});
  
  // 详情查看状态
  const infoVisible = ref(false);
  const infoTitle = ref('查看详情');
  const infoData = ref<T | null>(null);
  
  // 存储columns配置，用于数据格式化
  const columnsConfig = ref<CustomPlusColumn[]>(options.columns || []);
  
  // 基础API路径
  const baseApiPath = options.baseUrl || '';
  
  // 设置默认API URLs函数
  const defaultUrls = () => {
    if (baseApiPath && !options.urls) {
      options.urls = {
        // RESTful API路径
        list: baseApiPath,                    // GET /users - 获取列表
        create: baseApiPath,                  // POST /users - 创建
        detail: `${baseApiPath}/:id`,         // GET /users/:id - 获取详情
        update: `${baseApiPath}/:id`,         // PUT /users/:id - 更新
        delete: `${baseApiPath}/:id`          // DELETE /users/:id - 删除
      };
    }
  }
  
  // 初始化时设置默认URLs
  defaultUrls();
  
  // 默认参数
  const defaultParams = reactive<PageDataParams>({
    page: 1,
    pageSize: 10,
    ...options.defaultParams
  });
  
  // 当前查询参数
  const currentParams = reactive<PageDataParams>({
    ...defaultParams
  });

  /**
   * 格式化请求参数，将前端格式转换为后端所需格式
   * @param params 请求参数
   * @returns 格式化后的请求参数
   */
  const formatRequestParams = (params: Record<string, any>): Record<string, any> => {
    if (options.formatRequest) {
      return options.formatRequest(params);
    }
    // 默认格式化逻辑
    const formatted: Record<string, any> = {};
    for (const key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key) && params[key] !== undefined && params[key] !== '') {
        formatted[key] = params[key];
      }
    }
    return formatted;
  };

  /**
   * 格式化响应数据，将后端数据转换为前端所需格式
   * @param responseData 响应数据
   * @returns 格式化后的响应数据
   */
  const formatResponseData = (responseData: ResponseData<T>): { data: T[], total: number } => {
    if (options.formatResponse) {
      console.log('formatResponseData格式化前的数据:', responseData);
      return options.formatResponse(responseData);
    }
    const responseList = Array.isArray(responseData.list) ? responseData.list : responseData.data || responseData.items || [];
    
    return {
      data: responseList,
      total: responseData.total || responseList.length
    };
  };

  /**
   * 执行API请求
   * @param config 请求配置
   * @returns 响应数据
   */
  const request= async <R = any>(config: RequestConfig): Promise<R> => {
    // 检查URL是否存在
    if (!config.url) {
      throw new Error('请提供URL');
    }
    
    // 格式化请求参数
    const params = formatRequestParams(config.params || {});
    
    try {
      loading.value = true;
      
      // 打印请求信息方便调试
      console.log('请求URL:', config.url);
      console.log('请求方法:', config.method);
      console.log('请求参数:', params);
      
      let response;
      
      // 根据方法直接调用对应的HTTP方法
      switch (config.method?.toLowerCase()) {
        case 'get':
          response = await get(config.url, params);
          break;
        case 'post':
          response = await post(config.url, params);
          break;
        case 'put':
          response = await put(config.url, params);
          break;
        case 'delete':
          response = await del(config.url);
          break;
        default:
          // 如果没有指定方法或不支持的方法，回退到自定义api
          if (!config.api && !options.api) {
            throw new Error('未指定请求方法，且未提供API方法');
          }
          const api = config.api || options.api;
          if (!api) {
            throw new Error('API方法未定义');
          }
          response = await api(params, config.method, config.url);
      }
      
      return response as R;
    } catch (error) {
      console.error('请求出错:', error);
      ElMessage.error('操作失败，请重试');
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取数据列表
   * @param params 查询参数
   */
  const getList = async (params?: Record<string, any>) => {
    try {
      loading.value = true;
  
      const queryParams = {
        ...currentParams,
        ...params
      };
  
      Object.assign(currentParams, queryParams);
  
      const response = await request<ResponseData<any>>({
        params: queryParams,
        url: options.urls?.list,
        method: 'get'
      });
  
      console.log('列表响应:', response);
  
      const formatted = formatResponseData(response);
  
      let formattedList: any[] = formatted.data;
  
      if (Array.isArray(formatted.data) && columnsConfig.value.length > 0) {
        formattedList = formatDataByColumns(formatted.data as any, columnsConfig.value as any) as any;
      } else {
        console.warn('未应用格式化逻辑，原始数据:', formatted.data);
        formattedList = formatted.data;
      }
  
      if (!Array.isArray(formattedList)) {
        console.warn('格式化后的数据不是数组:', formattedList);
        formattedList = [];
      }
  
      // 将结果转为T类型赋值给data
      data.value = formattedList as T[];
      total.value = formatted.total;
  
      if (options.onListSuccess) {
        options.onListSuccess(formatted.data, formatted.total);
      }
  
      return formatted;
    } catch (error) {
      console.error('获取列表数据失败:', error);
      if (options.onListError) {
        options.onListError(error);
      }
      return { data: [], total: 0 };
    } finally {
      loading.value = false;
    }
  };

  /**
   * 重置查询参数并重新获取列表
   */
  const resetAndGetList = () => {
    // 重置为默认参数
    Object.assign(currentParams, defaultParams);
    // 重新获取列表
    getList();
  };

  /**
   * 打开添加表单
   */
  const openAddForm = () => {
    formMode.value = 'add';
    formTitle.value = options.addTitle || '添加';
    
    // 清空表单数据
    Object.keys(formData).forEach(key => {
      delete formData[key];
    });
    
    // 获取默认表单数据
    let defaultData = {};
    
    // 1. 优先使用options.defaultFormData
    if (options.defaultFormData) {
      defaultData = { ...options.defaultFormData };
    } 
    // 2. 如果没有defaultFormData但有表单列配置，则从列配置生成
    else if (options.formColumns) {
      defaultData = generateDefaultFormData(options.formColumns);
    }
    
    // 3. 应用默认数据
    if (Object.keys(defaultData).length > 0) {
      console.log('应用默认表单数据:', defaultData);
      Object.assign(formData, defaultData);
    }
    
    // 显示表单
    formVisible.value = true;
  };

  /**
   * 打开编辑表单
   * @param row 当前行数据
   */
  const openEditForm = async (row: any) => {
    formMode.value = 'edit';
    formTitle.value = options.editTitle || '编辑';
    
    try {
      // 清空表单数据
      Object.keys(formData).forEach(key => {
        delete formData[key];
      });
      
      // 获取主键字段
      const idField = options.idField || 'id';
      const recordId = row[idField];
      
      if (!recordId) {
        throw new Error('缺少记录ID');
      }

      // 显示加载状态
      loading.value = true;

      // 获取最新详情
      let detailData = null;
      
      // 如果配置了detail URL，则优先通过API获取详情
      if (options.urls?.detail) {
        detailData = await getDetail(recordId);
        console.log('获取详情:', detailData);
        
        if (!detailData) {
          // 如果获取失败但有行数据，则使用行数据
          detailData = { ...row };
        }
      } else {
        // 没有detail URL，直接使用行数据
        detailData = { ...row };
      }
      
      // 处理表单数据
      // 将详情数据赋值给表单
      Object.assign(formData, detailData);
      
      // 如果有表单数据格式化方法
      if (options.formatFormData) {
        const formatted = options.formatFormData(formData, 'edit');
        // 清空并重新赋值，确保没有旧数据残留
        Object.keys(formData).forEach(key => {
          delete formData[key];
        });
        Object.assign(formData, formatted);
      }
      
      console.log('编辑表单数据:', formData);
      
      // 显示表单
      formVisible.value = true;
    } catch (error) {
      console.error('获取详情失败:', error);
      ElMessage.error({
        message: `加载失败: ${error instanceof Error ? error.message : '未知错误'}`,
        duration: 3000
      });
    } finally {
      // 关闭加载状态
      loading.value = false;
    }
  };

  /**
   * 保存表单数据
   * @param data 表单数据
   * @param mode 表单模式，add或edit
   */
  const saveForm = async (data?: Record<string, any>, mode?: 'add' | 'edit') => {
    const saveData = data || formData;
    
    // 如果传入了mode参数，则覆盖内部的formMode
    if (mode) {
      formMode.value = mode;
    }
    
    // 格式化表单数据
    let submitData = { ...saveData };
    if (options.formatFormData) {
      submitData = options.formatFormData(saveData, formMode.value);
    }
    
    try {
      loading.value = true;
      console.log('saveForm模式:', formMode.value);
      console.log('提交数据:', submitData);
      
      let apiUrl = '';
      let apiMethod: 'get' | 'post' | 'put' | 'delete' = 'post'; // 根据实际赋值调整初始值
      
      if (formMode.value === 'add') {
        // 添加操作 - 使用create URL
        apiUrl = options.urls?.create || '';
        apiMethod = 'post';
        console.log('添加操作URL:', apiUrl);

        // 添加操作
        const response = await request({
          params: submitData,
          url: apiUrl,
          method: apiMethod
        });
        
        console.log('添加成功响应:', response);
        ElMessage.success(options.messages?.addSuccess || '添加成功');
        
        // 触发回调
        if (options.onAddSuccess) {
          options.onAddSuccess(response);
        }
      } else {
        // 编辑操作 - 使用update URL
        apiUrl = options.urls?.update || '';
        apiMethod = 'put';
        
        // 处理URL中的:id参数替换
        if (apiUrl && apiUrl.includes(':id')) {
          // 获取主键
          const idField = options.idField || 'id';
          const id = submitData[idField];
          
          if (id) {
            // 替换URL中的:id为实际ID
            apiUrl = apiUrl.replace(':id', id.toString());
          } else {
            console.error('编辑操作缺少ID字段:', idField);
            throw new Error(`编辑操作需要${idField}字段`);
          }
        }
        
        console.log('编辑操作URL:', apiUrl);
        
        // 编辑操作
        const response = await request({
          params: submitData,
          url: apiUrl,
          method: apiMethod
        });
        
        console.log('编辑成功响应:', response);
        ElMessage.success(options.messages?.editSuccess || '更新成功');
        
        // 触发回调
        if (options.onUpdateSuccess) {
          options.onUpdateSuccess(response);
        }
      }
      
      // 操作成功后关闭表单
      formVisible.value = false;
      
      // 刷新列表数据
      await getList();
      
      return true;
    } catch (error) {
      console.error('保存表单失败:', error);
      
      // 触发错误回调
      if (formMode.value === 'add' && options.onAddError) {
        options.onAddError(error);
      } else if (formMode.value === 'edit' && options.onUpdateError) {
        options.onUpdateError(error);
      }
      
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 删除一条记录
   * @param id 记录ID
   */
  const deleteItem = async (id: string | number) => {
    try {
      // 确保存在delete URL
      if (!options.urls?.delete) {
        console.error('未配置删除API地址');
        ElMessage.error('删除失败：系统配置错误');
        return false;
      }
      
      // 构造删除URL（替换:id占位符）
      let deleteUrl = options.urls.delete;
      if (deleteUrl.includes(':id')) {
        deleteUrl = deleteUrl.replace(':id', id.toString());
      }
      
      console.log('删除操作URL:', deleteUrl);
      
      // 执行删除请求
      loading.value = true;
      const response = await request({
        url: deleteUrl,
        method: 'delete'
      });
      
      console.log('删除成功响应:', response);
      ElMessage.success(options.messages?.deleteSuccess || '删除成功');
      
      // 触发回调
      if (options.onDeleteSuccess) {
        options.onDeleteSuccess(response);
      }
      
      // 刷新列表
      await getList();
      
      return true;
    } catch (error) {
      console.error('删除失败:', error);
      
      // 触发错误回调
      if (options.onDeleteError) {
        options.onDeleteError(error);
      }
      
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取单条记录详情
   * @param id 记录ID
   * @returns 记录详情
   */
  const getDetail = async (id: string | number): Promise<T | null> => {
    try {
      // 确保存在detail URL
      if (!options.urls?.detail) {
        console.error('未配置详情API地址');
        ElMessage.error('获取详情失败：系统配置错误');
        return null;
      }
      
      // 构造详情URL（替换:id占位符）
      let detailUrl = options.urls.detail;
      if (detailUrl.includes(':id')) {
        detailUrl = detailUrl.replace(':id', id.toString());
      }
      
      console.log('获取详情URL:', detailUrl);
      
      // 执行请求
      loading.value = true;
      const response = await request({
        url: detailUrl,
        method: 'get'
      });
      
      console.log('获取详情响应:', response);
      
      // 格式化响应数据
      let detail: T | null = null;
      
      if (options.formatDetailResponse) {
        detail = options.formatDetailResponse(response);
      } else {
        // 默认取response.data作为详情数据
        detail = response || null;
      }
      
      // 触发回调
      if (options.onDetailSuccess) {
        options.onDetailSuccess(detail);
      }
      
      return detail;
    } catch (error) {
      console.error('获取详情失败:', error);
      
      // 触发错误回调
      if (options.onDetailError) {
        options.onDetailError(error);
      }
      
      return null;
    } finally {
      // 关闭加载状态
      loading.value = false;
    }
  };

  /**
   * 根据字段类型生成默认值
   * @param valueType 字段类型
   * @returns 默认值
   */
  const getDefaultValueByType = (valueType?: string): any => {
    if (!valueType) return '';
    
    switch (valueType.toLowerCase()) {
      case 'input-number':
      case 'number':
      case 'rate':
      case 'slider':
        return 0;
      case 'switch':
      case 'checkbox':
        return false;
      case 'radio':
        return null;
      case 'select':
      case 'cascader':
      case 'tree-select':
        return undefined;
      case 'date-picker':
      case 'date':
      case 'datetime':
      case 'time-picker':
      case 'time':
        return new Date();
      case 'range-picker':
      case 'daterange':
      case 'timerange':
        return [new Date(), new Date()];
      case 'array':
      case 'list':
        return [];
      case 'object':
        return {};
      default:
        return '';
    }
  };

  /**
   * 从列配置中生成默认表单数据
   * @param columns 列配置
   * @returns 默认表单数据
   */
  const generateDefaultFormData = (columns?: CustomPlusColumn[]): Record<string, any> => {
    if (!columns || columns.length === 0) {
      return {};
    }
    
    const defaultData: Record<string, any> = {};
    
    // 遍历列配置
    columns.forEach(column => {
      // 跳过没有prop属性或hidden为true的列
      if (!column.prop || column.hidden) {
        return;
      }
      
      // 如果配置了默认值，则使用配置的默认值
      if (column.defaultValue !== undefined) {
        defaultData[column.prop] = column.defaultValue;
        return;
      } else {
        // 如果没有配置默认值，根据valueType生成默认值
        defaultData[column.prop] = getDefaultValueByType(column.valueType);
      }
    });
    
    console.log('从列配置生成的默认表单数据:', defaultData);
    return defaultData;
  };

  /**
   * 自定义操作方法
   * @param action 操作名称
   * @param row 当前行数据
   * @param extraParams 额外参数
   */
  const handleCustomAction = async (action: string, row?: T, extraParams?: Record<string, any>) => {
    if (!options.customActions || !options.customActions[action]) {
      console.warn(`未定义的自定义操作: ${action}`);
      return;
    }
    
    const actionConfig = options.customActions[action];
    
    try {
      // 显示确认对话框
      if (typeof actionConfig === 'object' && actionConfig.confirmMessage) {
        await ElMessageBox.confirm(
          actionConfig.confirmMessage,
          (actionConfig as any).confirmTitle || '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: (actionConfig as any).confirmType || 'warning'
          }
        );
      }
      
      loading.value = true;
      
      // 准备参数
      let params: Record<string, any> = {};
      if (row) {
        // 获取主键
        const idField = options.idField || 'id';
        params.id = (row as any)[idField];
        
        // 添加额外参数
        if (typeof actionConfig === 'object' && (actionConfig as any).includeRow) {
          params = { ...row, ...params };
        }
      }
      
      // 添加额外参数
      if (extraParams) {
        params = { ...params, ...extraParams };
      }
      
      // 添加固定参数
      if (typeof actionConfig === 'object' && (actionConfig as any).fixedParams) {
        params = { ...params, ...(actionConfig as any).fixedParams };
      }
      
      // 格式化参数
      if (typeof actionConfig === 'object' && actionConfig.formatParams) {
        params = actionConfig.formatParams(params);
      }
      
      // 执行请求
      const response = await request({
        params,
        url: (actionConfig as any).url,
        method: (actionConfig as any).method || 'post',
        api: (actionConfig as any).api
      });
      
      // 显示成功消息
      if (typeof actionConfig === 'object' && actionConfig.successMessage) {
        ElMessage.success(actionConfig.successMessage);
      }
      
      // 触发回调
      if (typeof actionConfig === 'object' && (actionConfig as any).onSuccess) {
        (actionConfig as any).onSuccess(response, row);
      }
      
      // 刷新列表
      if (typeof actionConfig === 'object' && (actionConfig as any).refreshAfterSuccess !== false) {
        getList();
      }
      
      return response;
    } catch (error) {
      if (error !== 'cancel') {
        console.error(`自定义操作 ${action} 失败:`, error);
        
        // 显示错误消息
        if (typeof actionConfig === 'object' && (actionConfig as any).errorMessage) {
          ElMessage.error((actionConfig as any).errorMessage);
        }
        
        // 触发回调
        if (typeof actionConfig === 'object' && (actionConfig as any).onError) {
          (actionConfig as any).onError(error, row);
        }
        
        throw error;
      }
    } finally {
      loading.value = false;
    }
  };

  /**
   * 打开查看描述列表弹窗
   * @param row 要查看的行数据
   * @param title 自定义标题，默认为'查看详情'
   */
  const openInfoDescriptions = async (row: any, title?: string) => {
    try {
      // 设置标题
      infoTitle.value = title || options.viewTitle || '查看详情';
      
      // 获取主键字段
      const idField = options.idField || 'id';
      const recordId = row[idField];
      
      if (!recordId) {
        console.warn('记录ID不存在，使用当前行数据展示');
        infoData.value = row;
        infoVisible.value = true;
        return;
      }

      // 显示加载状态
      loading.value = true;

      // 获取最新详情
      let detailData = null;
      
      // 如果配置了detail URL，则优先通过API获取详情
      if (options.urls?.detail) {
        detailData = await getDetail(recordId);
        console.log('获取详情:', detailData);
        
        if (!detailData) {
          // 如果获取失败但有行数据，则使用行数据
          detailData = { ...row };
        }
      } else {
        // 没有detail URL，直接使用行数据
        detailData = { ...row };
      }
      
      // 更新详情数据
      infoData.value = detailData;
      
      // 显示弹窗
      infoVisible.value = true;

    } catch (error) {
      console.error('获取详情失败:', error);
      ElMessage.error({
        message: `加载失败: ${error instanceof Error ? error.message : '未知错误'}`,
        duration: 3000
      });
    } finally {
      // 关闭加载状态
      loading.value = false;
    }
  };

  // 初始化
  if (options.immediate !== false) {
    getList();
  }

  return {
    // 状态
    loading,
    data,
    total,
    currentRow,
    formVisible,
    formTitle,
    formMode,
    formData,
    infoVisible,
    infoTitle,
    infoData,
    currentParams,
    
    // 方法
    defaultUrls,
    getList,
    resetAndGetList,
    openAddForm,
    openEditForm,
    saveForm,
    deleteItem,
    getDetail,
    executeAction: handleCustomAction, // 添加自定义操作方法的别名
    
    // 内部方法别名
    request,
    formatRequestParams,
    formatResponseData,
    getDefaultValueByType,
    generateDefaultFormData,
    
    // 解析columns配置的方法
    parseColumnsToConfig, // 新增：导出解析columns的方法
    formatDataByColumns,   // 新增：导出数据格式化方法
    openInfoDescriptions,
  };
}
