# GridLayout 页面分层示例说明文档

## 1. 设计目标
本示例演示如何基于 grid-layout-plus 实现灵活的页面布局，并采用"结构数据+对象数据"分层思想，便于高扩展性与低耦合。

- **结构数据**：页面结构、布局、方法、接口配置等，由父组件通过 store 传递，用于描述页面结构和行为。
- **对象数据**：页面实际业务数据（如商家列表），通过结构数据中定义的接口动态获取。

## 2. 目录结构
```
src/components/page/
  GridLayout.vue           // 页面主视图，负责渲染布局和业务组件
  gridLayoutStore.ts       // store 层，管理结构数据和对象数据
  gridLayoutService.ts     // service 层，处理接口请求与数据转换
  README.gridlayout.md     // 说明文档
src/services/
  structureDataCacheService.ts // 结构数据缓存服务
```

## 3. 架构设计

### 3.1 三层架构
模块采用清晰的三层架构设计：

1. **视图层(GridLayout.vue)**：
   - 负责渲染布局和业务组件
   - 监听用户交互事件（拖拽、缩放、点击等）
   - 维护视图状态（布局、样式等）

2. **状态管理层(gridLayoutStore.ts)**：
   - 管理结构数据和对象数据
   - 提供响应式状态
   - 处理业务逻辑
   - 管理step属性控制多步骤页面
   - 实现结构数据的本地持久化

3. **服务层(gridLayoutService.ts)**：
   - 处理API请求
   - 数据格式转换
   - 提供完整的CRUD操作
   - 错误处理与日志记录

### 3.2 设计模式

- **工厂模式**：通过createGridLayoutStore工厂函数创建GridLayoutStore实例，便于统一配置和初始化
- **单例模式**：通过props传递store实例，确保整个页面使用同一个store实例，避免状态不一致
- **依赖注入**：使用Vue3的provide/inject机制在组件树中共享store和service，使子组件能够直接访问
- **观察者模式**：通过Vue的响应式系统实现数据变化自动触发视图更新

## 4. 工作流程

### 4.1 初始化流程
```mermaid
flowchart TD
    A[父组件创建store] --> B[传递store给GridLayout]
    B --> C[GridLayout.initLayout]
    C --> D[parseGridItemsFromBackend]
    D --> E[过滤step显示]
    E --> F[渲染布局]
```

1. 父组件使用createGridLayoutStore工厂函数创建GridLayoutStore实例，传入serviceConfig和初始gridItems
2. 将store实例通过props传递给GridLayout组件
3. GridLayout组件挂载后，调用initLayout()方法初始化布局
4. 通过parseGridItemsFromBackend将后端数据转换为前端布局项
5. 根据当前step值过滤布局项（默认step值为1）
6. 渲染网格布局和业务组件

### 4.2 数据流转过程

1. **结构数据流**：
   - 从服务端获取页面配置和网格项配置
   - 通过store管理结构数据，并提供给GridLayout组件
   - GridLayout依据结构数据渲染布局和组件

2. **对象数据流**：
   - 通过服务层请求后端API获取具体业务数据
   - 存储在store的objectData中
   - 通过props传递给对应的业务组件

3. **事件流**：
   - 用户操作触发GridLayout组件事件
   - 事件通过$emit传递给父组件（item-click、item-delete、items-updated等）
   - 父组件执行相应的业务逻辑
   - 业务逻辑可能触发store更新
   - store更新导致视图刷新

### 4.3 多步骤页面控制流程

1. 父组件设置store.step值（默认值为1）
2. GridLayout监听step变化，调用refreshLayout()
3. refreshLayout()调用filterItemsByStep()过滤布局项
4. 显示规则：
   - 无step属性或step为0的项始终显示
   - step值等于当前store.step值的项显示
   - step为数组时，包含当前step值的项显示
5. 根据过滤结果更新layout
6. 布局自动更新，仅显示符合当前step条件的网格项

### 4.4 结构数据本地持久化流程

1. **初始化流程**：
   - 页面加载时，调用store.loadStructureData(frontendPath)
   - 首先尝试从本地缓存(localforage)获取结构数据
   - 如果有缓存且版本匹配，直接使用缓存数据
   - 如果无缓存或版本不匹配，从后端获取并更新缓存

2. **布局变更时自动持久化**：
   - GridLayout监听layout-change事件
   - 当布局变更时，自动调用store.saveLayoutToCache保存到本地

3. **版本控制机制**：
   - 利用systemInfo.configVersion作为全局配置版本
   - 利用PageData.version和PageData.version_hash作为页面配置版本
   - 当版本变化时自动更新缓存

4. **手动刷新机制**：
   - 提供store.refreshPageConfig方法，强制从后端重新获取数据

## 5. 结构数据示例
```ts
const structureData = {
  layout: [
    { x: 0, y: 0, w: 6, h: 4, i: 'merchantTable', component: 'GridItemComponent', props: {type: 'table'}, step: 0 }
  ],
  apiConfig: {
    fetchList: '/api/merchant/list'
  }
}
```
- `layout`：描述页面布局及每个组件的类型、位置、大小、props 等。
- `apiConfig`：定义接口地址，供对象数据获取使用。
- `step`：(可选)步骤属性，用于控制组件在多步骤页面中的显示逻辑（详见第12节）。

## 6. 对象数据示例
```ts
const objectData = {
  datalist: [] // 商家列表
}
```

## 7. 数据流说明
- 父组件通过 store 传递结构数据，GridLayout.vue 负责渲染布局。
- 页面挂载后，store 根据结构数据中的接口配置自动拉取对象数据。
- 业务组件（如 MerchantTable）通过 props 获取对象数据进行渲染。

## 8. 主要代码说明
- **GridLayout.vue**：
  - 渲染 grid-layout-plus 布局，根据结构数据动态渲染业务组件。
  - 仅注册了GridItemComponent一个组件，通过不同的type属性值渲染不同的内容组件。
  - GridItemComponent实现了通用功能，包括整体样式、标题显示、工具栏、刷新按钮等UI元素。
  - 支持透明的props传递，布局变更事件处理，布局持久化。
  - 页面挂载后自动调用 store.fetchObjectData()。
  - 支持扩展多种grid-layout-plus事件（resize/drag/move等）。
  - 支持根据step值过滤显示网格项，实现多步骤页面功能。
  - 在布局变更时自动保存到缓存。
- **gridLayoutStore.ts**：
  - 管理 structureData（结构数据）、objectData（对象数据）。
  - 提供createGridLayoutStore工厂函数，统一创建store实例。
  - fetchObjectData() 根据结构数据配置的接口获取对象数据。
  - 管理step属性，控制多步骤页面显示逻辑（默认值为1）。
  - 提供结构数据缓存管理功能：loadStructureData、saveLayoutToCache、refreshPageConfig等。
  - 包含parseGridItemsFromBackend和parseStructureDataFromBackend函数，处理后端数据转换。
- **gridLayoutService.ts**：
  - 统一处理接口请求与数据转换，无状态设计。
  - 提供完整的CRUD接口。
  - 支持自定义请求参数和响应处理。
  - 灵活的API URL构建机制，支持函数式URL和模板字符串替换。
- **structureDataCacheService.ts**：
  - 使用localforage实现结构数据的本地持久化存储
  - 提供版本控制和差异化更新机制
  - 支持多页面配置管理

## 9. 技术要点

### 9.1 动态组件渲染
通过Vue3的动态组件功能，根据网格项配置动态渲染GridItemComponent组件：
```html
<component 
  :is="components[item.component as keyof typeof components]" 
  v-bind="item.props"
/>
```

GridItemComponent作为统一入口组件，根据type属性值决定渲染的内容组件：
```html
<!-- 简化示意代码 -->
<div class="grid-item-wrapper">
  <!-- 通用标题栏 -->
  <div v-if="props.showTitle" class="grid-item-header">{{ props.title }}</div>
  
  <!-- 根据type动态渲染内容 -->
  <component v-if="props.type" :is="getComponentByType(props.type)" v-bind="props" />
  
  <!-- 通用工具栏 -->
  <div v-if="props.showToolbar" class="grid-item-toolbar">
    <button v-if="props.refreshable" @click="refresh">刷新</button>
    <button v-if="props.closable" @click="close">关闭</button>
  </div>
</div>
```

### 9.2 单例模式与依赖注入
```typescript
// 父组件创建store实例
const store = createGridLayoutStore(serviceConfig, gridItems, pageConfig);

// 使用props接收store实例，实现单例模式
const props = defineProps({
  store: {
    type: Object,
    required: true
  }
});

// 使用provide/inject共享store和service
provide('gridLayoutStore', store);
provide('pageService', store.service);
```

### 9.3 多步骤页面控制
```typescript
// 根据当前step值过滤布局项
function filterItemsByStep(items: LayoutItem[]): LayoutItem[] {
  return items.filter(item => {
    // 无step属性或step为0的项始终显示
    if (item.step === undefined || item.step === 0) return true;
    
    // step值等于当前步骤的项显示
    if (typeof item.step === 'number' && item.step === store.step.value) return true;
    
    // step为数组时，包含当前步骤的项显示
    if (Array.isArray(item.step) && item.step.includes(store.step.value)) return true;
    
    return false;
  });
}
```

## 10. 组件通信

### 10.1 父子组件通信
使用Vue3标准通信机制：
- Props 向下传递：父组件通过props向GridLayout传递store
- 事件向上传递：GridLayout通过$emit向父组件传递事件（item-click, item-delete等）
- 依赖注入：使用provide/inject在组件树共享gridLayoutStore和pageService

### 10.2 业务组件与Store交互
业务组件可以通过注入的方式获取store和service：
```typescript
const store = inject('gridLayoutStore');
const service = inject('pageService');
```

## 11. 响应式实现
基于Vue3的Composition API，充分利用ref和reactive：
- 使用ref定义值类型的响应式状态
- 使用reactive定义对象类型的响应式状态
- 使用watch监听状态变化
- 使用computed计算派生状态

## 12. 多步骤页面实现

多步骤页面功能允许根据页面当前步骤显示不同的组件，是实现向导式界面的核心。

### 12.1 基本原理
- store.step控制当前页面的步骤（默认值为1）
- 每个gridItem可以设置step属性，定义在哪个步骤显示
- step=0表示在所有步骤中都显示
- filterItemsByStep函数过滤当前应显示的网格项

### 12.2 实现示例
```typescript
// 在父组件中控制步骤
function nextStep() {
  store.step.value++;
}

function prevStep() {
  if (store.step.value > 1) {
    store.step.value--;
  }
}
```

### 12.3 gridItem的step配置
```typescript
// 单个步骤显示
{ step: 1 } // 仅在步骤1显示

// 多个步骤显示
{ step: [1, 3] } // 在步骤1和3显示

// 所有步骤显示
{ step: 0 } // 在所有步骤显示
```

## 13. 规范与最佳实践

- 推荐使用工厂函数创建store实例，便于标准化配置。
- 使用props传递store实例，确保单例模式正确实现。
- GridItemComponent应实现通用的UI功能，内容组件负责特定业务逻辑。
- 对重要逻辑添加单元测试，提高代码质量和可靠性。
- 定期清理过期缓存，避免缓存数据过多占用客户端存储空间。

## 14. 常见问题

### 14.1 数据缓存相关问题
1. **问题**：缓存数据与后端不同步
   **解决**：使用版本控制机制，当检测到版本变化时自动更新缓存

2. **问题**：缓存数据过多占用空间
   **解决**：定期清理长时间未使用的缓存，或设置缓存大小限制

### 14.2 布局相关问题
1. **问题**：网格项重叠或布局异常
   **解决**：使用grid-layout-plus的preventCollision属性，或在保存布局前进行冲突检测

2. **问题**：响应式布局适配不同屏幕尺寸
   **解决**：为不同断点配置不同布局，或使用响应式布局参数

### 14.3 性能相关问题
1. **问题**：大量网格项导致性能下降
   **解决**：实现虚拟滚动，仅渲染可视区域内的网格项

2. **问题**：频繁布局变更导致性能问题
   **解决**：使用防抖处理布局变更事件，或批量处理布局更新

### 14.4 组件通信问题
1. **问题**：跨组件通信困难
   **解决**：使用provide/inject共享store，或实现简单的事件总线

## 15. 页面布局示例

```typescript
// 多样化布局组合示例
const structureData = {
  layout: [
    {
      x: 0, y: 0, w: 12, h: 2, i: 'header',
      component: 'GridItemComponent',
      props: {
        type: 'header',
        title: '页面标题',
        content: { 
          type: 'header',
          config: { showLogo: true } 
        }
      },
      step: 0 // 在所有步骤显示
    },
    {
      x: 0, y: 2, w: 6, h: 4, i: 'form',
      component: 'GridItemComponent',
      props: {
        type: 'form',
        title: '表单',
        content: { 
          type: 'form',
          config: { fields: [...] } 
        }
      },
      step: 1 // 仅在步骤1显示
    },
    {
      x: 6, y: 2, w: 6, h: 4, i: 'table',
      component: 'GridItemComponent',
      props: {
        type: 'table',
        title: '数据表格',
        content: {
          type: 'table',
          config: { columns: [...], dataApi: '/api/list' }
        }
      },
      step: [1, 3] // 在步骤1和3显示
    },
    {
      x: 0, y: 6, w: 12, h: 3, i: 'chart',
      component: 'GridItemComponent',
      props: {
        type: 'chart',
        title: '统计图表',
        content: {
          type: 'chart',
          config: { chartType: 'bar', dataApi: '/api/chart/data' }
        }
      },
      step: 2 // 仅在步骤2中显示
    }
  ],
  apiConfig: {
    fetchList: '/api/xxx/list'
  }
}
```

> 说明：
> - layout 数组中的每一项都包含通用结构数据（如x, y, w, h, i, type, title, draggable, resizable, style等），以及 content 字段（type, config）作为专用结构数据。
> - 每个网格项的component值均为'GridItemComponent'，但通过props.type区分不同类型。
> - GridItemComponent负责处理通用UI功能（如边框、标题栏、透明度），内容由type决定。
> - 通过灵活配置 layout，可实现表单、表格、图表等多类型混合页面。
> - step属性定义了布局项在多步骤页面中的显示逻辑，step=0表示在所有步骤中都显示。

## 16. 数据库结构与前端数据处理说明

在实际业务中，结构数据（页面布局与组件配置）是保存在数据库中的。数据库表结构设计如下：

- 页面数据表：存储页面的基础信息（如页面ID、名称、路由等）。
- grid组件数据表：存储每个grid组件的配置（如类型、参数、位置等）。
- 两者之间为多对多关系（即一个页面可包含多个grid组件，一个grid组件也可被多个页面复用）。

### 前端数据处理流程
1. 前端获取页面数据时，会自动追加 `griditems` 数组到页面数据对象中。
2. `griditems` 数组中的每个元素为一个 grid 组件的配置对象，数据库原始数据没有 `i` 字段，只有 `id` 字段。
3. 前端在处理 `griditems` 时，会将每个 griditem 的 `id` 字段转化为 `i` 字段，通过parseGridItemsFromBackend函数实现。
4. 转换规则为：如果position.i存在且为数字，则使用该值；否则使用id作为i值。

> 示例：
> 数据库中的 griditem： `{ id: 123, ... }`
> 转换后的 layout item： `{ i: 123, ... }`

## 17. 未来优化方向

### 17.1 性能优化
- 实现虚拟滚动，优化大量网格项的渲染性能
- 利用Vue3的suspense特性，改进数据加载体验
- 引入Web Worker处理复杂计算，避免阻塞主线程

### 17.2 功能扩展
- 支持更多类型的网格项组件
- 实现拖放式页面编辑器
- 增强多设备适配能力，支持不同断点的布局方案

### 17.3 开发体验优化
- 提供更完善的TypeScript类型定义
- 开发调试工具，可视化组件状态和数据流
- 自动化测试套件，确保组件质量
