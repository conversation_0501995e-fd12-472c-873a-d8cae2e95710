/**
 * 页面组件类型定义
 * 提供TypeScript类型支持
 * 注意：大部分类型已迁移到共享类型定义中，此文件保留组件特有类型
 * <AUTHOR>
 * @date 2025-04-27
 */
import type { 
  TableValueType, 
  FormItemValueType, 
  PageData, 
  DTOInfoDTO,
  AdminGridInfoDTO,
  UiPageConfig,
  DialogConfig,
  FormRule,
  PlusColumn,
  OptionsRow, 
  LayoutGridItem,
  GridItemContent,
  ServiceConfig,
  UIState,
  PageInfoMap
} from '@/types/shared';

/**
 * 单个表单值的类型
 */
export type FieldValueType =
  | string
  | number
  | boolean
  | Date
  | (string | number)[]
  | null
  | undefined;

// 重导出共享类型，以保持兼容性
export type {
  TableValueType, 
  FormItemValueType, 
  PageData, 
  DTOInfoDTO,
  AdminGridInfoDTO,
  UiPageConfig,
  DialogConfig,
  FormRule,
  PlusColumn,
  OptionsRow, 
  LayoutGridItem,
  GridItemContent,
  ServiceConfig,
  UIState,
  PageInfoMap
};

/**
 * 组件类型定义
 */
export type ComponentType = 'search' | 'table' | 'form' | 'chart' | 'card' | 'description' | 'custom';

/**
 * 搜索字段配置接口
 */
export interface SearchField {
  label: string;
  name: string;
  component: string;
  componentProps?: Record<string, any>;
  defaultValue?: any;
  rules?: any[];
}

/**
 * 分页配置接口
 */
export interface Pagination {
  current: number;
  pageSize: number;
  total: number;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  pageSizeOptions?: string[];
  onChange?: (page: number, pageSize: number) => void;
  onShowSizeChange?: (current: number, size: number) => void;
}

/**
 * PlusProComponents分页接口
 */
export interface PageInfo {
  page: number;
  pageSize: number;
}

/**
 * 基础组件配置接口
 */
export interface ComponentConfig {
  type: ComponentType;
  id?: string;
  api?: string;
  title?: string;
  description?: string;
}

/**
 * 表格组件配置接口
 */
export interface TableConfig extends ComponentConfig {
  type: 'table';
  columns: PlusColumn[];
  rowKey?: string;
  showPagination?: boolean;
  rowSelection?: {
    type: 'checkbox' | 'radio';
    [key: string]: any;
  };
  scroll?: {
    x?: number | string | boolean;
    y?: number | string | boolean;
  };
}

/**
 * 搜索组件配置接口
 */
export interface SearchConfig extends ComponentConfig {
  type: 'search';
  fields: SearchField[];
  layout?: 'horizontal' | 'vertical' | 'inline';
  submitText?: string;
  resetText?: string;
  collapsed?: boolean;
  defaultCollapsed?: boolean;
  defaultValues?: Record<string, any>;
}

/**
 * 表单组件配置接口
 */
export interface FormConfig extends ComponentConfig {
  type: 'form';
  fields: SearchField[];
  layout?: 'horizontal' | 'vertical' | 'inline';
  submitText?: string;
  resetText?: string;
  labelWidth?: number | string;
}

// 注：UIState 类型已移至共享类型定义中，从 @/types/shared 导入

/**
 * 排序信息接口
 */
export interface SorterInfo {
  field: string;
  order: 'ascend' | 'descend' | undefined;
}

/**
 * 字段信息接口
 */
export interface FieldInfo {
  prop: string;
  type: string;
  validation: string;
  defaultValue: unknown;
  label: string;
  options?: { value: number | string; label: string }[];
}
