/**
 * gridLayoutStore.ts
 * 管理 GridLayout 的 Store，包括布局结构数据和网格项配置
 * @update 2025-04-24 添加结构数据本地持久化支持
 * @update 2025-04-25 拆分Store职责，专注于布局和结构数据管理，支持与gridComponentStore协作
 *  此组件主要用于拖拽式布局
 * 本文件用于管理 grid 布局相关的响应式状态，包括 serviceConfig、gridLoading 等。
 * 提供安全的 setter 方法，确保关键字段始终保持响应式。
 */
import { ref, watch, type Ref } from 'vue';
import { ElMessage } from 'element-plus';
import type { GridContentConfig } from '@/types/grid';
import GridLayoutService from './gridLayoutService';
import type GridComponentStore from './gridComponentStore';
import type { PageData, AdminGridInfoDTO } from '@/modules/admin/types';
import type { ServiceConfig } from '@/types/shared';
import type { LayoutGridItem } from '@/modules/admin/types';
import type { GridComponentType } from '@/components/base/grid/types';
//import type { PageData } from '@/modules/admin/types';
// 导入缓存服务
import { saveStructureData, getStructureData, checkPageVersionUpdate, clearStructureDataCache } from '@/services/structureDataCacheService';

/**
 * 结构数据类型定义（页面结构、布局、接口、方法等）
 */
export interface StructureData extends PageData {
  layout: Array<LayoutGridItem>; // grid-layout-plus 布局数组
}

/**
 * 扩展的网格项类型
 */
export interface ExtendedGridItem extends LayoutGridItem {
  component?: GridComponentType; // 组件名称
  props?: any; // 组件属性
  _originalData?: Record<string, any>; // 原始数据
  content?: GridContentConfig; // 内容配置
  [key: string]: any; // 其他属性
}

/**
 * 页面配置类型，包含页面元信息和服务配置
 */
export type GridLayoutConfig = {
  version?: string;
  title?: string;
  moduleName?: string;
  frontendPath?: string;
  icon?: string;
  serviceConfig: ServiceConfig;
  [key: string]: any;
};

/**
 * 解析后端 grid_items 数据为前端可用布局项
 * @param gridItems 后端返回的 grid_items 数组
 * @returns 前端 layout 数组，供 grid-layout-plus 使用
 */
export function parseGridItemsFromBackend(gridItems: AdminGridInfoDTO[]): ExtendedGridItem[] {
  if (!Array.isArray(gridItems)) return [];
  return gridItems.map((item) => {
    const dragAllowFrom = '.grid-item-drag-btn';
    const x = item.position?.x ?? 0;
    const y = item.position?.y ?? 0;
    const w = item.position?.w ?? 4;
    const h = item.position?.h ?? 3;
    const i = Number(item.position?.i) > 0 ? Number(item.position.i) : item.id;
    const component = 'GridItemComponent';
    const type = item.content.type || 'custom';
    const props = { ...item.content, name: item.name, id: item.id, remark: item.remark, type, config: item.content || {}, content: item.content };
    //console.log('【调试】props', x, y, w, h, i, component, props, type, item.content);
    // 创建扩展的网格项
    return {
      x, y, w, h, i, component, props, dragAllowFrom,
      _originalData: item,
      type,
      step: item.step, // 保留step字段，用于筛选显示
      content: {
        type,
        title: item.name || '未命名项',
        icon: item.content.icon,
        showTitle: item.content.showTitle !== false,
        //showToolbar: item.content.showToolbar !== false,
        refreshable: item.content.refreshable !== false,
        configurable: item.content.configurable !== false,
        editable: item.content.editable !== false,
        closable: item.content.closable !== false,
        config: item.content.config || {},
        content: item.content
      }
    };
  });
}

/**
 * 解析后端页面配置对象为前端 structureData
 * @param pageData 后端页面配置对象（含 config_content, grid_items 等）
 * @returns StructureData
 */
export function parseStructureDataFromBackend(pageData: PageData): StructureData {
  console.log('parseStructureDataFromBackend', pageData);
  // 确保grid_items存在并正确解析为layout数组
  const layout = parseGridItemsFromBackend(pageData.grid_items || []);
  console.log('【调试】layout', layout);
  return {
    layout,
    ...pageData
  };
}

/**
 * GridLayoutStore 类
 * 负责管理GridLayout的布局和结构数据，包括页面结构、网格项配置和多步骤控制
 * @param serviceConfig 页面服务配置
 * @param gridItems 网格项数据（可选）
 * @param pageConfig 页面配置信息（包含元信息和serviceConfig，可选）
 */
class GridLayoutStore {
  /**
   * 当前步骤，用于控制网格项的显示逻辑
   * step为0时显示所有step为0的网格项
   * step为其他值时，显示对应step值的网格项和所有step为0的网格项
   */
  public step = ref<number>(1);
  
  /**
   * 页面布局配置，初始为默认值，加载后可被覆盖
   */
  public pageConfig = ref<PageData>({
    id: 0,
    config_key: '',
    config_type: '',
    created_at: '',
    updated_at: '',
    draggable: true,
    resizable: true,
    status: 1,
    title: '默认页面',
    version: '1.0.0',
    version_hash: '',
    remark: '',
    module: '',
    group: '',
    icon: '',
    frontend_path: '',
    config_content: { // 页面配置信息
      frontendPath: '',
      icon: '',
      moduleName: '',
      serviceConfig: {
        addTitle: '',
        apiPrefix: '',
        baseUrl: '',
        customActions: {},
        gridOptions: {
          cellHeight: 50,
          column: 12,
          margin: 10
        },
        messages: {
          addSuccess: '',
          deleteConfirm: '',
          deleteSuccess: '',
          editSuccess: ''
        },
        resizable: true,
        draggable: true,
        viewTitle: '',
        title: '',
        version: ''
      }
    },
    dto: { // 数据模型字段信息
      id: 0,
      module: '',
      name: '',
      description: '',
      type: '',
      structure: [], // 数据模型字段结构
      created_at: '',
      updated_at: ''
    },
    grid_items: [] // 网格项信息
  });
  private service: GridLayoutService;
  public gridLoading: Ref<boolean> = ref(false);
  public gridError: Ref<boolean> = ref(false);
  public errorMessage: Ref<string> = ref('');
  public gridItems = ref<ExtendedGridItem[]>([]);
  public structureData = ref<StructureData>();
  public serviceConfig = ref<ServiceConfig>({
    baseUrl: '',
    addTitle: '',
    apiPrefix: '',
    customActions: {},
    gridOptions: {
      cellHeight: 50,
      column: 12,
      margin: 10
    },
    messages: {
      addSuccess: '',
      deleteConfirm: '',
      deleteSuccess: '',
      editSuccess: ''
    },
    resizable: true,
    draggable: true,
    viewTitle: '',
    title: '',
    version: ''
  });

  // 引用关联的gridComponentStore，实现共享和联动
  private componentStore: GridComponentStore | null = null;

  /**
   * 构造函数，初始化 serviceConfig、service、gridItems 等
   * @param serviceConfig ServiceConfig 配置
   * @param gridItems 可选，初始 gridItems
   * @param pageConfig 可选，页面配置
   */
  constructor(serviceConfig: ServiceConfig, gridItems?: ExtendedGridItem[], pageConfig?: PageData) {
    console.log('[store构造] serviceConfig:', serviceConfig);
    // 保证 serviceConfig 是全新对象，避免 Proxy target 问题
    this.setServiceConfig(serviceConfig);
    this.service = new GridLayoutService(this.serviceConfig.value);
    console.log('[store构造] 新建 service:', this.service, 'config:', this.service.config);
    // 监听 serviceConfig 变化，自动重建 service 实例
    watch(this.serviceConfig, (newConfig) => {
      console.log('[watch] serviceConfig 变化:', newConfig, '当前this.serviceConfig.value:', this.serviceConfig.value);
      this.service = new GridLayoutService(newConfig);
      console.log('[watch] 重新 new service:', this.service, 'config:', this.service.config);
      
      // 如果已关联componentStore，同步更新其服务配置
      if (this.componentStore) {
        this.updateComponentStoreConfig();
      }
    }, { deep: true });
    
    // 如果提供了pageConfig，则使用它
    if (pageConfig) {
      this.pageConfig.value = pageConfig;
    }
    // gridItems 类型防御
    if (Array.isArray(gridItems)) {
      this.gridItems = ref(gridItems);
    } else {
      this.gridItems = ref([]);
    }
  }

  /**
   * 关联GridComponentStore，建立两个store之间的联系
   * @param componentStore GridComponentStore实例
   */
  connectComponentStore(componentStore: GridComponentStore): void {
    this.componentStore = componentStore;
    
    // 初始化时同步service和配置
    componentStore.updateService(this.service);
    
    // 只有在structureData存在时才更新配置
    if (this.structureData.value) {
      console.log('[GridLayoutStore] structureData存在，立即更新ComponentStore配置');
      this.updateComponentStoreConfig();
    } else {
      console.log('[GridLayoutStore] structureData不存在，跳过更新ComponentStore配置');
    }
    
    console.log('[GridLayoutStore] 已关联GridComponentStore');
  }

  /**
   * 更新关联的ComponentStore配置
   */
  private updateComponentStoreConfig(): void {
    //console.error('[GridLayoutStore] updateComponentStoreConfig', this.componentStore, this.structureData.value);
    if (!this.componentStore || !this.structureData.value) {
      console.error('[GridLayoutStore] 无法更新ComponentStore配置：componentStore或structureData不存在');
      return;
    }
    
    // 从structureData中获取apiConfig等信息
    const adminConfig = this.structureData.value.config_content;
    if (!adminConfig) {
      console.error('[GridLayoutStore] 无法更新ComponentStore配置：adminConfig不存在');
      return;
    }
    
    // 创建类型适配器，将admin模块的UiPageConfig转换为page组件需要的UiPageConfig
    const adaptedConfig = this.adaptConfigToPageComponent(adminConfig);
    
    // 更新componentStore的配置
    this.componentStore.updateConfig(adaptedConfig);
    
    console.log('[调试 GridLayoutStore] 已更新ComponentStore配置:', adaptedConfig);
  }

  /**
   * 适配器函数 - 将admin模块的UiPageConfig转换为page组件需要的格式
   * @param adminConfig admin模块的UiPageConfig配置
   * @returns 适配后的UiPageConfig配置
   */
  private adaptConfigToPageComponent(adminConfig: any): import('./types').UiPageConfig {
    // 深拷贝配置对象
    const adaptedConfig = JSON.parse(JSON.stringify(adminConfig)) as import('./types').UiPageConfig;
    
    // 特别处理serviceConfig.formColumns中的render函数
    if (adaptedConfig.serviceConfig?.formColumns) {
      adaptedConfig.serviceConfig.formColumns = adaptedConfig.serviceConfig.formColumns.map((column: any) => {
        // 如果column有render函数，需要进行适配
        if (column.render) {
          const originalRender = column.render;
          // 创建适配后的render函数，匹配page组件需要的签名
          column.render = (_value: any, data: { row: any; column: any; index: number }) => {
            // 调用原始render函数，并传递正确的参数
            if (typeof originalRender === 'function') {
              // 确保参数符合原始函数的期望
              try {
                return originalRender(data.row, data.column);
              } catch (error) {
                console.error('[adaptConfigToPageComponent] 调用render函数出错:', error);
                return null;
              }
            }
            return null;
          };
        }
        return column;
      });
    }
    
    return adaptedConfig;
  }

  /**
   * 获取服务实例
   */
  getService(): GridLayoutService {
    console.log('[getService] 当前 service:', this.service, 'config:', this.service?.config);
    return this.service;
  }

  /**
   * 获取 serviceConfig 配置的统一方法
   * 无论 serviceConfig 是什么类型（ref 或普通对象），都返回正确的 ServiceConfig 对象
   * @returns ServiceConfig 对象
   */
  public getServiceConfig(): ServiceConfig {
    return typeof this.serviceConfig === 'object' && 'value' in this.serviceConfig 
      ? this.serviceConfig.value 
      : this.serviceConfig as unknown as ServiceConfig;
  }

  /**
   * 设置后端 grid_items 并格式化为前端 layout
   * @param backendGridItems 后端 grid_items
   */
  setGridItems(backendGridItems: any[]) {
    this.gridItems.value = parseGridItemsFromBackend(backendGridItems);
  }

  /**
   * 设置后端页面配置对象，自动解析 config_content 和 grid_items
   * @param pageData 后端页面配置对象（含 config_content, grid_items 等）
   * @param componentStore 可选，如果提供则使用该componentStore
   */
  setStructureDataFromBackend(pageData: PageData, componentStore?: GridComponentStore) {
    console.log('【调试】setStructureDataFromBackend', pageData);
    
    // 如果提供了componentStore参数，则设置它
    if (componentStore && !this.componentStore) {
      console.log('【调试】使用传入的componentStore');
      this.connectComponentStore(componentStore);
    }
    
    // 先解析并设置structureData
    this.structureData.value = parseStructureDataFromBackend(pageData);
    console.log('【调试】structureData已设置:', this.componentStore, this.structureData.value);
    
    // 确保有关联的componentStore且数据有效
    if (this.componentStore && pageData.config_content && pageData.config_content.serviceConfig) {
      // 确保在设置structureData后再更新componentStore
      setTimeout(() => {
        console.log('【调试】延迟执行updateComponentStoreConfig');
        this.updateComponentStoreConfig();
        
        console.log('【调试】生成colunms！', pageData.dto?.structure);
        // 从dto的structure生成columns
        if (this.componentStore && pageData.dto && pageData.dto.structure) {
          this.componentStore.generateColumnsFromPageData(pageData);
        }
      }, 1);
    } else {
      console.warn('【警告】无法更新componentStore配置，原因：', {
        componentStoreExists: !!this.componentStore,
        configContentExists: !!pageData.config_content,
        serviceConfigExists: !!(pageData.config_content && pageData.config_content.serviceConfig)
      });
    }
  }

  /**
   * 加载结构数据，优先使用缓存
   * @param frontendPath 前端路径
   * @param forceUpdate 是否强制从后端更新
   * @returns Promise<void>
   */
  async loadStructureData(frontendPath: string, forceUpdate = false): Promise<void> {
    if (!frontendPath) {
      console.error('加载结构数据失败：前端路径为空');
      return;
    }
    
    // 如果不强制更新，尝试从缓存获取
    if (!forceUpdate) {
      const cachedData = await getStructureData(frontendPath);
      if (cachedData) {
        this.structureData.value = parseStructureDataFromBackend(cachedData);
        console.log(`从缓存加载路径 ${frontendPath} 的结构数据成功，版本: ${cachedData.version}`);
        
        // 如果存在关联的componentStore，同步更新配置
        if (this.componentStore && cachedData.config_content && cachedData.config_content.serviceConfig) {
          this.updateComponentStoreConfig();
        }
        
        return;
      }
    }
    
    // 缓存不存在或需要强制更新，从后端获取
    await this.fetchPageDataFromBackend(frontendPath);
  }
  
  /**
   * 从后端获取页面数据并更新缓存
   * @param frontendPath 前端路径
   * @returns Promise<void>
   */
  async fetchPageDataFromBackend(frontendPath: string): Promise<void> {
    try {
      this.setGridLoading(true);
      console.log(`正在从后端获取路径 ${frontendPath} 的页面配置...`);
      const response = await this.service.getPageConfig();
      this.setGridLoading(false);
      
      if (response && response.data) {
        const pageData = response.data as PageData;
        
        // 检查是否需要更新缓存
        const needUpdate = await checkPageVersionUpdate(frontendPath, pageData);
        
        if (needUpdate) {
          // 确保structureData是ref对象
          if (this.structureData === undefined || this.structureData === null) {
            this.structureData = ref<StructureData>();
          }
          // 更新store中的数据
          if (typeof this.structureData === 'object' && 'value' in this.structureData) {
            this.structureData.value = parseStructureDataFromBackend(pageData);
            // 保存到缓存
            await saveStructureData(frontendPath, pageData);
            console.log(`从后端加载路径 ${frontendPath} 的结构数据成功并更新缓存，版本: ${pageData.version}`);
            
            // 如果存在关联的componentStore，同步更新配置
            if (this.componentStore && pageData.config_content && pageData.config_content.serviceConfig) {
              this.updateComponentStoreConfig();
            }
          } else {
            console.error('structureData不是响应式对象:', this.structureData);
            // 重新初始化structureData
            this.structureData = ref<StructureData>(parseStructureDataFromBackend(pageData));
            await saveStructureData(frontendPath, pageData);
            
            // 如果存在关联的componentStore，同步更新配置
            if (this.componentStore && pageData.config_content && pageData.config_content.serviceConfig) {
              this.updateComponentStoreConfig();
            }
          }
        } else {
          console.log(`后端页面配置版本与缓存一致，无需更新，版本: ${pageData.version}`);
          // 使用缓存数据
          const cachedData = await getStructureData(frontendPath);
          if (cachedData) {
            // 同样确保structureData是ref对象
            if (this.structureData === undefined || this.structureData === null) {
              this.structureData = ref<StructureData>();
            }
            if (typeof this.structureData === 'object' && 'value' in this.structureData) {
              this.structureData.value = parseStructureDataFromBackend(cachedData);
              
              // 如果存在关联的componentStore，同步更新配置
              if (this.componentStore && cachedData.config_content && cachedData.config_content.serviceConfig) {
                this.updateComponentStoreConfig();
              }
            } else {
              this.structureData = ref<StructureData>(parseStructureDataFromBackend(cachedData));
              
              // 如果存在关联的componentStore，同步更新配置
              if (this.componentStore && cachedData.config_content && cachedData.config_content.serviceConfig) {
                this.updateComponentStoreConfig();
              }
            }
          }
        }
      }
    } catch (error) {
      this.setGridLoading(false);
      console.error(`从后端加载路径 ${frontendPath} 的结构数据失败:`, error);
      throw new Error(`加载页面配置失败: ${error}`);
    }
  }
  
  /**
   * 保存布局更改到缓存
   * @param frontendPath 前端路径
   * @returns Promise<void>
   */
  async saveLayoutToCache(frontendPath: string): Promise<void> {
    // 确保修改不会影响版本信息
    if (this.structureData.value && this.structureData.value.version && this.structureData.value.version_hash) {
      await saveStructureData(frontendPath, this.structureData.value);
      console.log(`已保存路径 ${frontendPath} 的布局更改到缓存`);
    } else {
      console.warn(`保存布局到缓存失败: ${frontendPath} 的结构数据不完整`, this.structureData.value);
    }
  }
  
  /**
   * 手动刷新页面配置
   * @param frontendPath 前端路径
   * @returns Promise<void>
   */
  async refreshPageConfig(frontendPath: string): Promise<void> {
    try {
      this.setGridLoading(true);
      console.log('开始刷新页面配置，清除缓存:', frontendPath);
      // 清除当前路径的缓存
      await clearStructureDataCache(frontendPath);
      
      console.log('从后端获取最新配置'); // 之下开始从后端获取数据，仅仅更新当前页面的数据即可
      // 强制从后端重新加载
      await this.fetchPageDataFromBackend(frontendPath);
      
      console.log('刷新页面配置完成');
      ElMessage.success('页面配置已刷新');
    } catch (error) {
      this.setGridLoading(false);
      console.error('刷新页面配置失败:', error);
      ElMessage.error(`刷新失败: ${error}`);
    } finally {
      this.setGridLoading(false);
    }
  }

  /**
   * 更新 serviceConfig，并自动刷新 service 实例
   * @param newConfig 新的 serviceConfig
   * @returns Promise<void>，当 service 实例已更新后 resolve
   */
  updateServiceConfig(newConfig: ServiceConfig): Promise<void> {
    console.log('[updateServiceConfig] 新 config:', newConfig);
    return new Promise((resolve) => {
      try {
        // 使用JSON序列化和反序列化确保完全脱离引用关系
        // 这比深度克隆更彻底，可以完全避免Vue响应式系统的引用问题
        const plainConfig = JSON.parse(JSON.stringify(newConfig));
        console.log('[updateServiceConfig] 序列化后的 config:', plainConfig);
        
        // 1. 先创建新的service实例
        const newService = new GridLayoutService(plainConfig);
        console.log('[updateServiceConfig] 已创建新service实例:', newService, 'config:', newService.config);
        
        // 2. 更新service引用
        this.service = newService;
        
        // 3. 最后更新serviceConfig的值
        // 检查this.serviceConfig.value是否存在，避免Object.keys报错
        if (this.serviceConfig && this.serviceConfig.value) {
          // 直接替换整个对象，而不是先清空再赋值
          this.setServiceConfig(plainConfig);
        } else {
          // 如果serviceConfig.value不存在，则创建一个新的引用
          this.setServiceConfig(plainConfig);
        }
        
        // 如果存在关联的componentStore，同步更新配置和service
        if (this.componentStore) {
          this.componentStore.updateService(this.service);
          //this.componentStore.updateConfig(plainConfig);
        }
        
        console.log('[updateServiceConfig] 更新后的serviceConfig:', this.serviceConfig);
        
        // 立即解析Promise
        resolve();
      } catch (error) {
        console.error('[updateServiceConfig] 错误:', error);
        // 即使出错也解析Promise，避免阻塞
        resolve();
      }
    });
  }

  /**
   * 安全设置 serviceConfig 的方法
   * 确保 serviceConfig 始终为响应式 ref 对象
   * @param value 要设置的 ServiceConfig 对象
   */
  private setServiceConfig(value: ServiceConfig): void {
    if (typeof this.serviceConfig !== 'object' || !('value' in this.serviceConfig)) {
      this.serviceConfig = ref(value);
    } else {
      this.serviceConfig.value = value;
    }
  }

  /**
   * 设置gridLoading状态的安全方法
   * 确保gridLoading始终是响应式对象
   * @param value 要设置的布尔值
   */
  private setGridLoading(value: boolean): void {
    if (typeof this.gridLoading !== 'object' || !('value' in this.gridLoading)) {
      this.gridLoading = ref(value);
    } else {
      this.gridLoading.value = value;
    }
  }

  /**
   * 设置gridError状态的安全方法
   * 确保gridError始终是响应式对象
   * @param value 要设置的布尔值
   * @param message 可选的错误消息
   */
  setGridError(value: boolean, message?: string): void {
    if (typeof this.gridError !== 'object' || !('value' in this.gridError)) {
      this.gridError = ref(value);
    } else {
      this.gridError.value = value;
    }
    
    if (message !== undefined) {
      if (typeof this.errorMessage !== 'object' || !('value' in this.errorMessage)) {
        this.errorMessage = ref(message);
      } else {
        this.errorMessage.value = message;
      }
    }
  }
}

// 创建一个工厂函数，用于创建GridLayoutStore实例
export function createGridLayoutStore(serviceConfig: ServiceConfig, gridItems?: ExtendedGridItem[], pageConfig?: PageData): GridLayoutStore {
  return new GridLayoutStore(serviceConfig, gridItems, pageConfig);
}

export default GridLayoutStore;
