<!-- 
  页面配置编辑器组件
  用于可视化编辑页面配置，包括service和基础配置
  组件配置项将在GridItemEditor中设置
  修改历史：
  2025-04-15 将props参数方式改为store方式获取数据
-->
<!-- GridPageConfigEditor.vue -->
<!-- 本文件用于编辑和管理页面的Grid配置，包括配置的生成、编辑等功能 -->
<template>
  <el-card class="box-card">
    <template #header>
      <div class="card-header">
        <span>配置编辑</span>
      </div>
    </template>
    
    <el-tabs v-model="activeConfigTab" type="border-card">
      <!-- DTO源标签页 -->
      <el-tab-pane label="DTO源" name="dto">
        <dto-source-editor
          :modelValue="store.gridLayoutStore?.structureData?.dto || {}"
          @update:modelValue="updateDtoSourceJson"
          @save="handleSaveDtoSource"
          @generate="handleGenerateFromDto"
        />
      </el-tab-pane>

      <!-- Service配置 -->
      <el-tab-pane label="Service配置" name="service">
        <service-config-editor
          v-if="store.gridLayoutStore.structureData"
          :modelValue="store.gridLayoutStore.structureData.config_content.serviceConfig"
          :store="store"
          @update:modelValue="updateServiceConfig"
          @refresh="handleRefreshPage"
        />
      </el-tab-pane>
      
      <!-- FormDialog配置 -->
      <el-tab-pane label="FormDialog配置" name="formDialog">
        <form-dialog-config-editor
          v-if="store.gridLayoutStore.structureData"
          :modelValue="store.gridLayoutStore.structureData.config_content.formDialogConfig"
          :store="store" 
          @update:modelValue="updateFormDialogConfig"
          @refresh="handleRefreshPage"
        />
      </el-tab-pane>

      <!-- ViewDialog配置 -->
      <el-tab-pane label="ViewDialog配置" name="viewDialog">
        <view-dialog-config-editor
          v-if="store.gridLayoutStore.structureData"
          :modelValue="store.gridLayoutStore.structureData.config_content.viewDialogConfig"
          :store="store" 
          @update:modelValue="updateViewDialogConfig"
          @refresh="handleRefreshPage"
        />
      </el-tab-pane>
      
      <!-- 源码编辑 -->
      <el-tab-pane label="源码编辑" name="source">
        <div class="source-editor">
          <div class="button-group">
            <el-button type="primary" @click="handleSaveSource">保存配置</el-button>
            <el-button type="warning" @click="handleFormatSource">格式化</el-button>
            <el-button type="success" @click="handleExportConfig">导出配置</el-button>
            <el-button type="info" @click="handleImportConfig">导入配置</el-button>
            <el-button link size="small" @click="sourceVisible = !sourceVisible" class="toggle-btn">
              {{ sourceVisible ? '隐藏源码' : '显示源码' }}
              <el-icon>
                <component :is="sourceVisible ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </div>
          
          <!-- JSON预览和源码编辑区 -->
          <el-row :gutter="20">
            <!-- 源码编辑区域 -->
            <el-col :span="sourceVisible ? 12 : 0" v-if="sourceVisible">
              <el-input
                v-model="sourceValue"
                type="textarea"
                :rows="20"
                @input="handleSourceChange"
                placeholder="请输入JSON配置"
              />
            </el-col>
            <!-- JSON预览区域 -->
            <el-col :span="sourceVisible ? 12 : 24">
              <div class="json-viewer-container">
                <el-card class="json-preview-card">
                  <template #header>
                    <div class="card-header">
                      <span>JSON可视化预览</span>
                    </div>
                  </template>
                  <JsonViewer 
                    :value="parsedSourceJson" 
                    :expandDepth="3" 
                    copyable 
                    sort 
                    boxed 
                    theme="light"
                    class="text-left"
                  />
                </el-card>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
//import { ArrowUp, ArrowDown } from '@element-plus/icons-vue';
import { JsonViewer } from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/index.css';
// 修复导入路径，使用正确的模块路径
import { useGridManagementStore } from '@/modules/admin/views/gridManagement/gridManagementStore';
import DtoSourceEditor from '@/components/base/DtoSourceEditor.vue';
import ServiceConfigEditor from '@/components/base/ServiceConfigEditor.vue';
import FormDialogConfigEditor from '@/components/base/FormDialogConfigEditor.vue';
import ViewDialogConfigEditor from '@/components/base/ViewDialogConfigEditor.vue';
// 确保TableConfigEditor在模板中使用，或者移除未使用的导入
//import TableConfigEditor from '@/components/base/TableConfigEditor.vue';
import type { DtoStructure } from '@/modules/admin/views/gridManagement/GridManagementService';

// 通用配置类型
interface ServiceConfig {
  baseUrl: string;
  [key: string]: any;
}

// 表单对话框配置类型
interface FormDialogConfig {
  columns: Array<any>;
  [key: string]: any;
}

// ViewDialog配置类型
interface ViewDialogConfig {
  columns: Array<any>;
  [key: string]: any;
}

// 页面对象中的各种配置
interface PageConfig {
  serviceConfig?: ServiceConfig;
  formDialogConfig?: FormDialogConfig;
  tableConfig?: any;
  [key: string]: any;
}

// 页面结构数据
interface StorePageStructure {
  dto?: DtoStructure;
  config_content: PageConfig;
  [key: string]: any;
}

// 定义store类型
interface Store {
  gridLayoutStore: {
    structureData?: StorePageStructure;
    [key: string]: any;
  };
  pageConfig?: any;
  setDtoSource: () => void;
  [key: string]: any;
}

// 使用store代替props
const store = useGridManagementStore() as Store;

// 激活的配置标签页
const activeConfigTab = ref('dto');
// 源码编辑器的值 - 只关注config_content部分
const sourceValue = ref(JSON.stringify(store.gridLayoutStore?.structureData?.config_content, null, 2));
// 源码编辑区域显示状态，默认不显示
const sourceVisible = ref(false);
// 解析后的JSON对象，用于预览
const parsedSourceJson = computed(() => {
  try {
    return JSON.parse(sourceValue.value);
  } catch (e) {
    return {};
  }
});

// 监听store.pageConfig的config_content变化，自动同步到sourceValue
watch(() => store.gridLayoutStore?.structureData?.config_content, (val) => {
  if (val) {
    sourceValue.value = JSON.stringify(val, null, 2);
  }
}, { deep: true });

/**
 * 更新DTO源对象
 * @param val 新的DTO值
 */
function updateDtoSourceJson(val: DtoStructure) {
  if (store.gridLayoutStore.structureData) {
    store.gridLayoutStore.structureData.dto = val;
    // 保留原有功能调用
    store.setDtoSource();
  }
}

/**
 * 更新Service配置
 * @param val 新的配置值
 */
function updateServiceConfig(val: ServiceConfig) {
  if (store.gridLayoutStore.structureData) {
    store.gridLayoutStore.structureData.config_content.serviceConfig = val;
    handleRefreshPage();
  }
}

/**
 * 更新FormDialog配置
 * @param val 新的配置值
 */
function updateFormDialogConfig(val: FormDialogConfig) {
  if (store.gridLayoutStore.structureData) {
    // 使用明确定义的FormDialogConfig类型
    store.gridLayoutStore.structureData.config_content.formDialogConfig = val;
    handleRefreshPage();
  }
}

/**
 * 更新ViewDialog配置
 * @param val 新的配置值
 */
function updateViewDialogConfig(val: ViewDialogConfig) {
  if (store.gridLayoutStore.structureData) {
    // 使用明确定义的ViewDialogConfig类型
    store.gridLayoutStore.structureData.config_content.viewDialogConfig = val;
    handleRefreshPage();
  }
}

// 处理DTO源保存
function handleSaveDtoSource(_parsedDto: any) {
  store.setDtoSource();
}

/**
 * 根据DTO生成页面配置所需字段
 * @param parsedDto 后端传来的页面配置DTO
 */
function handleGenerateFromDto(parsedDto: any) {
  console.log('【GridManagementStore】handleGenerateFromDto', parsedDto);
  store.processDtoStructure(parsedDto);
}

// 处理页面刷新
function handleRefreshPage() {
  store.refreshBasePage();
}

// 处理源码保存
function handleSaveSource() {
  try {
    const parsed = JSON.parse(sourceValue.value);
    if (store.gridLayoutStore?.structureData) {
      // 正确保存到config_content部分
      store.gridLayoutStore.structureData.config_content = parsed;
      ElMessage.success('配置已保存');
    } else {
      ElMessage.warning('未找到目标结构数据');
    }
  } catch (e) {
    ElMessage.error('JSON格式错误');
  }
}

// 处理源码格式化
function handleFormatSource() {
  try {
    sourceValue.value = JSON.stringify(JSON.parse(sourceValue.value), null, 2);
    ElMessage.success('格式化成功');
  } catch (e) {
    ElMessage.error('JSON格式错误');
  }
}

// 处理源码变化
function handleSourceChange() {
  // 尝试实时解析源码显示预览，但不保存回配置
  try {
    // parsedSourceJson 是响应式的，依赖于 sourceValue，会自动更新
    // 我们可以在这里添加额外验证逻辑
    JSON.parse(sourceValue.value);
  } catch (e) {
    // 可以选择在这里显示错误，但为了不干扰用户输入，我们选择不显示
    // console.error('JSON格式错误', e);
  }
}

// 处理导出配置
function handleExportConfig() {
  const blob = new Blob([sourceValue.value], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'pageConfig.json';
  a.click();
  URL.revokeObjectURL(url);
}

// 处理导入配置
function handleImportConfig() {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json,application/json';
  input.onchange = (e: any) => {
    const file = e.target.files[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = (event: any) => {
      try {
        const parsed = JSON.parse(event.target.result);
        if (store.gridLayoutStore?.structureData?.config_content) {
          Object.assign(store.gridLayoutStore.structureData.config_content, parsed || {});
        } else if (store.gridLayoutStore?.structureData) {
          store.gridLayoutStore.structureData.config_content = parsed || {};
        }
        sourceValue.value = JSON.stringify(parsed, null, 2);
        ElMessage.success('导入成功');
      } catch (err) {
        ElMessage.error('导入失败：JSON格式错误');
      }
    };
    reader.readAsText(file);
  };
  input.click();
}
</script>

<style scoped>
.box-card {
  width: 100%;
  margin-bottom: 20px;
}
.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}
.button-group {
  margin-bottom: 10px;
}
.source-editor {
  margin-top: 10px;
}

.json-preview-card {
  width: 98%;
}

.toggle-btn {
  margin-left: 10px;
}

.json-viewer-container {
  margin-bottom: 20px;
  text-align: left;
}
</style>
