/**
 * @description GridPage Store - 网格页面状态管理
 * <AUTHOR> AI
 * @date 2025-04-12
 * @note 本文件为GridPage页面的状态管理核心，负责管理所有页面配置信息、状态及与服务层的交互
 */
import { reactive, ref } from 'vue';
//import type { GridStackNode } from 'gridstack';
import GridPageService from './GridPageService';
import type { GridPageServiceConfig } from './GridPageService';
import type { GridContentConfig } from '@/types/grid';

// 页面配置类型，包含页面元信息和服务配置
type GridPageConfig = {
  version?: string;
  title?: string;
  moduleName?: string;
  frontendPath?: string;
  icon?: string;
  serviceConfig: GridPageServiceConfig;
  [key: string]: any;
};

// 扩展 GridStackNode 类型，添加 _originalData 属性
// interface ExtendedGridStackNode extends GridStackNode {
//   _originalData?: Record<string, any>;
// }

/**
 * GridPageStore 类
 * 负责管理GridPage的状态、数据交互以及与服务层的通信
 * @param serviceConfig 页面服务配置
 * @param gridItems 网格项数据（可选）
 * @param pageConfig 页面配置信息（包含元信息和serviceConfig，可选）
 */
class GridPageStore {
  public pageConfig: GridPageConfig | undefined;
  private service: GridPageService;
  public loading = ref(false);
  public error = ref(false);
  public errorMessage = ref('');
  public gridItems = ref<ExtendedGridStackNode[]>([]);
  public tableData = ref<any[]>([]);
  public formVisible = ref(false);
  public formTitle = ref('');
  public formMode = ref<'add' | 'edit'>('add');
  public formData = reactive<Record<string, any>>({});
  public infoVisible = ref(false);
  public infoTitle = ref('查看详情');
  public infoData = ref<any | null>(null);
  public currentGridItem = ref<ExtendedGridStackNode | null>(null);
  public currentParams = reactive<Record<string, any>>({});
  /**
   * gridOptions 表格配置项
   * @description 用于存储表格的显示和交互配置，例如列定义、分页、排序等
   */
  public gridOptions: any = {};

  /**
   * 构造函数
   * @param serviceConfig 页面服务配置
   * @param gridItems 网格项数据（可选）
   * @param pageConfig 页面配置信息（可选）
   */
  constructor(serviceConfig: GridPageServiceConfig, gridItems?: ExtendedGridStackNode[], pageConfig?: GridPageConfig) {
    this.service = new GridPageService(serviceConfig);
    this.pageConfig = pageConfig;
    // gridItems 类型防御
    if (Array.isArray(gridItems)) {
      this.gridItems = ref(gridItems);
    } else {
      this.gridItems = ref([]);
    }
    // 设置默认参数
    const defaultParams = { page: 1, pageSize: 20, ...serviceConfig.defaultParams };
    Object.assign(this.currentParams, defaultParams);
  }

  /**
   * 获取服务实例
   */
  getService(): GridPageService {
    return this.service;
  }

  /**
   * 加载网格数据
   * @param params 查询参数
   */
  async loadGridData(params?: Record<string, any>): Promise<void> {
    // 防御：确保 this.gridItems 一定为 ref
    if (!this.gridItems || typeof this.gridItems !== 'object' || !('value' in this.gridItems)) {
      this.gridItems = ref([]);
      console.log('防御启动！ gridItems:', this.gridItems.value);
    }
    try {
      this.loading.value = true;
      // 合并查询参数
      const queryParams = { ...this.currentParams, ...params };
      Object.assign(this.currentParams, queryParams);
      // 调用服务加载数据
      const result = await this.service.fetchData(queryParams);
      // 将返回的数据转换为网格项格式
      if (result.list && Array.isArray(result.list)) {
        console.log('result.list', result.list);
        this.tableData.value = result.list;
        //this.gridItems.value = this.transformDataToGridItems(result.list);
      }
      this.error.value = false;
      this.errorMessage.value = '';
    } catch (error: any) {
      console.error('加载网格数据失败:', error);
      this.error.value = true;
      this.errorMessage.value = error.message || '加载数据失败';
    } finally {
      this.loading.value = false;
    }
  }


  /**
   * 为数据项创建默认内容配置
   * @param item 数据项
   * @returns 内容配置
   */
  private createDefaultContent(item: any): GridContentConfig {
    // 根据数据项属性判断合适的展示方式
    const contentType = this.determineContentType(item);
    
    return {
      title: item.title || item.name || '未命名项',
      type: contentType,
      config: this.createContentConfig(item, contentType),
      refreshable: true,
      editable: true,
      closable: true,
      // 将原始数据保存到content中，方便子组件使用
      originalData: item
    } as GridContentConfig;
  }

  /**
   * 根据数据项确定内容类型
   * @param item 数据项
   * @returns 内容类型
   */
  private determineContentType(item: any): string {
    // 如果含有图片URL，优先展示为图片
    if (item.imageUrl || item.imgUrl || item.coverUrl || item.thumbUrl) {
      return 'image';
    }
    
    // 如果含有图表数据，展示为图表
    if (item.chartData || item.dataPoints || item.series) {
      return 'chart';
    }
    
    // 如果含有表格数据，展示为表格
    if (item.tableData || item.rows || item.columns) {
      return 'table';
    }
    
    // 如果含有HTML内容，展示为HTML
    if (item.html || item.htmlContent) {
      return 'html';
    }
    
    // 默认展示为文本卡片
    return 'card';
  }

  /**
   * 创建内容配置
   * @param item 数据项
   * @param contentType 内容类型
   * @returns 内容配置
   */
  private createContentConfig(item: any, contentType: string): any {
    switch (contentType) {
      case 'image':
        return {
          src: item.imageUrl || item.imgUrl || item.coverUrl || item.thumbUrl,
          alt: item.title || item.name || '图片'
        };
      
      case 'chart':
        return {
          // 图表配置
          type: 'line', // 默认为折线图
          data: item.chartData || item.dataPoints || item.series || [],
          options: item.chartOptions || {}
        };
      
      case 'table':
        return {
          // 表格配置
          data: item.tableData || item.rows || [],
          columns: item.columns || []
        };
      
      case 'html':
        return {
          html: item.html || item.htmlContent || `<div>${item.description || item.content || ''}</div>`
        };
      
      case 'card':
      default:
        return {
          // 卡片配置
          title: item.title || item.name || '未命名项',
          subtitle: item.subtitle || '',
          description: item.description || item.content || '',
          footer: item.footer || ''
        };
    }
  }

  /**
   * 保存网格布局
   * @param gridData 网格数据
   */
  async saveGridLayout(gridData: ExtendedGridStackNode[]): Promise<boolean> {
    try {
      this.loading.value = true;
      
      // 获取原始数据并合并网格布局信息
      const layoutData = gridData.map(node => {
        const originalData = node._originalData || {};
        // 保留网格布局信息
        return {
          ...originalData,
          id: originalData.id || node.id,
          x: node.x,
          y: node.y,
          w: node.w,
          h: node.h,
          content: node.content
        };
      });
      
      // 调用服务保存布局
      await this.service.handleAction('saveLayout', { layout: layoutData });
      
      return true;
    } catch (error) {
      console.error('保存网格布局失败:', error);
      return false;
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * 打开添加表单
   */
  openAddForm(): void {
    this.formMode.value = 'add';
    this.formTitle.value = '添加项目';
    
    // 清空表单数据
    Object.keys(this.formData).forEach(key => {
      delete this.formData[key];
    });
    
    // 获取服务实例的配置
    const serviceConfig = (this.service as any).config || {};
    
    // 应用默认表单数据
    if (serviceConfig.defaultFormData) {
      Object.assign(this.formData, serviceConfig.defaultFormData);
    }
    
    // 显示表单
    this.formVisible.value = true;
  }

  /**
   * 打开编辑表单
   * @param gridItem 网格项
   */
  async openEditForm(gridItem: ExtendedGridStackNode): Promise<void> {
    try {
      this.formMode.value = 'edit';
      this.formTitle.value = '编辑项目';
      this.currentGridItem.value = gridItem;
      
      // 清空表单数据
      Object.keys(this.formData).forEach(key => {
        delete this.formData[key];
      });
      
      const originalData = gridItem._originalData || {};
      
      // 获取服务实例的配置
      const serviceConfig = (this.service as any).config || {};
      
      // 如果存在 ID，尝试获取详情数据
      const idField = serviceConfig.idField || 'id';
      const recordId = originalData[idField];
      
      if (recordId) {
        this.loading.value = true;
        try {
          // 获取详情数据
          const detailData = await this.service.getDetail(recordId);
          if (detailData) {
            // 合并网格布局信息
            const formData = {
              ...detailData,
              x: gridItem.x,
              y: gridItem.y,
              w: gridItem.w,
              h: gridItem.h
            };
            Object.assign(this.formData, formData);
          } else {
            // 如果获取详情失败，直接使用原始数据
            Object.assign(this.formData, originalData);
          }
        } catch (error) {
          console.error('获取详情失败:', error);
          // 使用原始数据
          Object.assign(this.formData, originalData);
        } finally {
          this.loading.value = false;
        }
      } else {
        // 没有 ID，直接使用原始数据
        Object.assign(this.formData, originalData);
      }
      
      // 显示表单
      this.formVisible.value = true;
    } catch (error) {
      console.error('打开编辑表单失败:', error);
    }
  }

  /**
   * 保存表单数据
   */
  async saveForm(): Promise<boolean> {
    try {
      this.loading.value = true;
      
      // 获取服务实例的配置
      const serviceConfig = (this.service as any).config || {};
      
      if (this.formMode.value === 'add') {
        // 创建新项目
        const result = await this.service.createItem(this.formData);
        if (result) {
          // 刷新网格数据
          await this.loadGridData();
          this.formVisible.value = false;
          return true;
        }
      } else {
        // 编辑现有项目
        const idField = serviceConfig.idField || 'id';
        const id = this.formData[idField];
        
        if (!id) {
          throw new Error(`编辑操作需要${idField}字段`);
        }
        
        const result = await this.service.updateItem(id, this.formData);
        if (result) {
          // 刷新网格数据
          await this.loadGridData();
          this.formVisible.value = false;
          return true;
        }
      }
      
      return false;
    } catch (error) {
      console.error('保存表单失败:', error);
      return false;
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * 删除网格项
   * @param gridItem 网格项
   */
  // async deleteGridItem(gridItem: ExtendedGridStackNode): Promise<boolean> {
  //   try {
  //     // 获取服务实例的配置
  //     const serviceConfig = (this.service as any).config || {};
      
  //     // 获取主键
  //     const idField = serviceConfig.idField || 'id';
  //     const originalData = gridItem._originalData || {};
  //     const recordId = originalData[idField];
      
  //     if (!recordId) {
  //       console.error('删除失败: 缺少记录ID');
  //       return false;
  //     }
      
  //     // 调用服务删除
  //     const result = await this.service.deleteItem(recordId);
      
  //     if (result) {
  //       // 从本地数据中移除
  //       this.gridItems.value = this.gridItems.value.filter(item => {
  //         const itemOriginalData = item._originalData || {};
  //         return itemOriginalData[idField] !== recordId;
  //       });
  //       return true;
  //     }
      
  //     return false;
  //   } catch (error) {
  //     console.error('删除网格项失败:', error);
  //     return false;
  //   }
  // }

  /**
   * 打开详情查看
   * @param gridItem 网格项
   */
  async openInfoView(gridItem: ExtendedGridStackNode): Promise<void> {
    try {
      this.infoTitle.value = '查看详情';
      this.currentGridItem.value = gridItem;
      
      const originalData = gridItem._originalData || {};
      
      // 获取服务实例的配置
      const serviceConfig = (this.service as any).config || {};
      
      // 获取主键
      const idField = serviceConfig.idField || 'id';
      const recordId = originalData[idField];
      
      if (recordId) {
        this.loading.value = true;
        try {
          // 获取详情数据
          const detailData = await this.service.getDetail(recordId);
          if (detailData) {
            this.infoData.value = detailData;
          } else {
            // 如果获取详情失败，直接使用原始数据
            this.infoData.value = originalData;
          }
        } catch (error) {
          console.error('获取详情失败:', error);
          // 使用原始数据
          this.infoData.value = originalData;
        } finally {
          this.loading.value = false;
        }
      } else {
        // 没有 ID，直接使用原始数据
        this.infoData.value = originalData;
      }
      
      // 显示详情弹窗
      this.infoVisible.value = true;
    } catch (error) {
      console.error('打开详情查看失败:', error);
    }
  }

  /**
   * 重置查询参数并重新加载数据
   */
  resetAndReload(): void {
    // 获取服务实例的配置
    const serviceConfig = (this.service as any).config || {};
    
    // 重置为默认参数
    const defaultParams = { page: 1, pageSize: 20, ...serviceConfig.defaultParams };
    Object.assign(this.currentParams, defaultParams);
    
    // 重新加载数据
    this.loadGridData();
  }

  /**
   * 处理自定义操作
   * @param action 操作名称
   * @param payload 操作数据
   */
  async handleAction(action: string, payload: any): Promise<any> {
    try {
      this.loading.value = true;
      const result = await this.service.handleAction(action, payload);
      return result;
    } catch (error) {
      console.error(`执行操作 ${action} 失败:`, error);
      throw error;
    } finally {
      this.loading.value = false;
    }
  }
}

export type { ExtendedGridStackNode, GridPageConfig };
export default GridPageStore;