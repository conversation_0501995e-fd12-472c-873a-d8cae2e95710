/**
 * 页面组件模块导出
 */
import BasePage from './BasePage.vue';
import GridPage from './GridPage.vue';
import { createPageService } from './service';
import type {
  TableValueType, 
  FormItemValueType, 
  PageData, 
  DTOInfoDTO,
  AdminGridInfoDTO,
  UiPageConfig,
  DialogConfig,
  FormRule,
  PlusColumn,
  OptionsRow, 
  LayoutGridItem,
  GridItemContent,
  ServiceConfig,
  UIState,
  PageInfoMap
} from './types';

// 导出组件
export { BasePage, GridPage, createPageService };

// 导出类型
export type {
  TableValueType, 
  FormItemValueType, 
  PageData, 
  DTOInfoDTO,
  AdminGridInfoDTO,
  UiPageConfig,
  DialogConfig,
  FormRule,
  PlusColumn,
  OptionsRow, 
  LayoutGridItem,
  GridItemContent,
  ServiceConfig,
  UIState,
  PageInfoMap
};

// 默认导出
export default BasePage;
