# BasePage 组件使用文档

## 组件简介

BasePage 是一个基于 plus-pro-components 和 Element Plus 构建的高级页面组件，用于快速构建管理后台的 CRUD（增删改查）页面。通过简单的 JSON 配置即可实现复杂的数据交互功能，极大地提高了开发效率。

### 主要特点

- **配置驱动**：通过 JSON 配置即可生成完整的 CRUD 页面
- **服务层设计**：内置服务层处理数据转换和 API 交互
- **表单校验**：提供丰富的表单校验规则，支持通过字符串引用方式简化配置
- **集成上传组件**：无缝集成 FileUploader 组件，支持文件上传
- **丰富的事件**：提供多种事件回调，方便业务扩展
- **类型支持**：完善的 TypeScript 类型定义
- **智能配置**：支持仅传入 `columns` 配置，自动生成搜索、表格和表单配置

## 安装依赖

组件依赖以下库：

```bash
# 安装 plus-pro-components
npm install plus-pro-components

# 安装其他依赖
npm install element-plus dayjs
```

## 目录结构

```
src/components/page/
├── BasePage.vue      # 核心组件
├── service.ts        # 服务层实现
├── validator.ts      # 表单校验规则
├── types.ts          # 类型定义
├── index.ts          # 模块导出
└── README.md         # 使用文档
```

## 快速开始

### 基本用法

```vue
<template>
  <BasePage
    title="用户管理"
    :service-config="serviceConfig"
    :search-config="searchConfig"
    :table-config="tableConfig"
    :form-config="formConfig"
    :toolbar-config="toolbarConfig"
    @toolbar-action="handleToolbarAction"
    @row-action="handleRowAction"
  />
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { BasePage } from '@/components/page';

// 服务配置
const serviceConfig = reactive({
  api: (params, method) => {
    // 调用API接口
  },
  // 其他配置...
});

// 搜索配置
const searchConfig = reactive({
  columns: [
    { label: '关键词', prop: 'keyword' }
    // 其他搜索字段...
  ]
});

// 表格配置
const tableConfig = reactive({
  columns: [
    { label: 'ID', prop: 'id' },
    { label: '名称', prop: 'name' }
    // 其他表格列...
  ]
});

// 表单配置
const formConfig = reactive({
  columns: [
    { label: '名称', prop: 'name', required: true }
    // 其他表单字段...
  ]
});

// 工具栏配置
const toolbarConfig = reactive({
  buttons: [
    { text: '新增', action: 'add' }
    // 其他按钮...
  ]
});

// 处理工具栏动作
const handleToolbarAction = (action) => {
  console.log('工具栏动作:', action);
};

// 处理行动作
const handleRowAction = (action, row, index) => {
  console.log('行动作:', action, row, index);
};
</script>
```

### 简化用法 (仅传入 columns)

新增功能：只需传入 `columns` 配置，即可自动生成搜索、表格和表单配置，极大简化了配置过程。

```vue
<template>
  <BasePage
    :columns="columns"
    :service-config="serviceConfig"
    @toolbar-action="handleToolbarAction"
    @row-action="handleRowAction"
  />
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { BasePage } from '@/components/page';

// 列配置
const columns = reactive([
  { 
    label: 'ID', 
    prop: 'id',
    hideInSearch: true,  // 在搜索表单中隐藏
    hideInForm: true     // 在表单中隐藏
  },
  { 
    label: '用户名', 
    prop: 'username' 
    // 默认在搜索、表格和表单中都显示
  },
  { 
    label: '创建时间', 
    prop: 'createTime',
    valueType: 'date-picker', // 日期类型，表格中会自动格式化
    hideInSearch: false,      // 在搜索中显示
    hideInForm: true          // 在表单中隐藏
  },
  { 
    label: '头像', 
    prop: 'avatar',
    valueType: 'img',         // 表格中显示为图片
    hideInSearch: true        // 在搜索中隐藏
  }
]);

// 服务配置
const serviceConfig = reactive({
  baseUrl: '/api/users',
  // 其他配置...
});

// 处理工具栏动作
const handleToolbarAction = (action) => {
  console.log('工具栏动作:', action);
};

// 处理行动作
const handleRowAction = (action, row, index) => {
  console.log('行动作:', action, row, index);
};
</script>
```

## 组件属性

### 基础属性

| 属性           | 类型            | 默认值   | 说明                          |
|---------------|----------------|---------|------------------------------|
| title         | string         | ''      | 页面标题                       |
| baseUrl       | string         | ''      | API基础URL                    |
| apiPrefix     | string         | '/api'  | API前缀                       |
| initialData   | array          | []      | 初始表格数据                    |
| columns       | array          | []      | 列配置，可自动生成搜索/表格/表单配置 |
| searchConfig  | object         | -       | 搜索配置                       |
| tableConfig   | object         | -       | 表格配置                       |
| formConfig    | object         | -       | 表单配置                       |
| toolbarConfig | object         | -       | 工具栏配置                      |
| serviceConfig | object         | -       | 服务配置                       |

### columns 配置

`columns` 是一个核心配置项，可以用于自动生成搜索、表格和表单配置。每个列配置项支持以下属性：

| 属性          | 类型                 | 默认值   | 说明                           |
|--------------|---------------------|---------|-------------------------------|
| label        | string              | -       | 列标签                          |
| prop         | string              | -       | 列属性名                        |
| valueType    | string              | -       | 值类型，用于格式化和控制组件类型     |
| hideInSearch | boolean             | false   | 是否在搜索表单中隐藏               |
| hideInTable  | boolean             | false   | 是否在表格中隐藏                  |
| hideInForm   | boolean             | false   | 是否在表单中隐藏                  |
| width        | number/string       | -       | 列宽度                          |
| ...          | -                   | -       | 其他 plus-pro-components 属性   |

#### valueType 支持的值类型

不同的 valueType 会影响数据展示格式和表单控件类型：

| valueType        | 表格中的展示           | 表单中的控件类型       | 说明                   |
|-----------------|---------------------|---------------------|------------------------|
| text            | 文本                 | 输入框                | 默认类型                |
| textarea        | 文本                 | 文本域                | 多行文本                |
| select          | 文本                 | 下拉选择框             | 选择类型                |
| date-picker     | 格式化日期            | 日期选择器             | 日期类型 (YYYY-MM-DD)   |
| datetime-picker | 格式化日期时间         | 日期时间选择器          | 日期时间 (YYYY-MM-DD HH:mm:ss) |
| time-picker     | 格式化时间            | 时间选择器             | 时间类型 (HH:mm:ss)     |
| switch          | 开关状态              | 开关                  | 布尔类型                |
| img/image       | 图片                 | 上传组件               | 图片上传/显示           |
| upload          | 文件链接              | 上传组件               | 文件上传/显示           |
| money/price     | 格式化金额            | 数字输入框             | 金额类型                |
| percent         | 百分比                | 数字输入框             | 百分比类型              |

## 表单校验

/**
 * 表单校验
 */

### 校验规则

BasePage 组件内置了丰富的表单校验规则，支持通过字符串引用的方式简化配置。目前支持以下校验规则：

| 校验规则名称      | 说明                                        |
|------------------|---------------------------------------------|
| checkEmail       | 校验邮箱地址格式                             |
| checkPhone       | 校验手机号码格式（中国大陆手机号）           |
| checkUrl         | 校验URL地址格式                              |
| checkNumber      | 校验是否为有效数字                           |
| checkInteger     | 校验是否为整数                               |
| checkFloat       | 校验是否为浮点数                             |
| checkIdCard      | 校验身份证号码格式                           |
| checkPassword    | 校验密码强度（必须包含字母和数字，长度6-20位）|
| checkUsername    | 校验用户名格式（字母、数字、下划线，长度3-20位）|
| checkIpAddress   | 校验IP地址格式                               |
| checkZipCode     | 校验邮政编码格式                             |
| checkGreaterThan | 校验值是否大于指定阈值                       |

### 使用方式

在表单配置中使用校验规则有两种方式：

#### 1. 字符串引用方式

```javascript
// 表单配置
const formConfig = {
  // 其他配置...
  rules: {
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { validator: 'checkEmail', trigger: 'blur' }
    ],
    phone: [
      { validator: 'checkPhone', trigger: 'blur' }
    ],
    url: [
      { validator: 'checkUrl', trigger: 'blur' }
    ],
    // 大于阈值校验
    score: [
      { validator: 'checkGreaterThan', threshold: 60, message: '分数必须大于60分', trigger: 'blur' }
    ],
    amount: [
      { required: true, message: '请输入金额', trigger: 'blur' },
      { validator: 'checkGreaterThan', threshold: 100, message: '金额必须大于100元', trigger: 'blur' }
    ]
  }
};
```

#### 2. 直接导入使用

```javascript
import { validators, ruleCreators } from '@/components/page/validator';

// 表单配置
const formConfig = {
  // 其他配置...
  rules: {
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { validator: validators.checkEmail, trigger: 'blur' }
    ],
    phone: [
      { validator: validators.checkPhone, trigger: 'blur' }
    ],
    // 使用大于阈值校验
    age: [
      { validator: (rule, value, callback) => validators.checkGreaterThan(rule, value, callback, 18, '年龄必须大于18岁'), trigger: 'blur' }
    ]
  }
};
```

### 创建自定义校验规则

如果内置的校验规则不满足需求，可以通过以下方式创建自定义校验规则：

```javascript
import { ruleCreators } from '@/components/page/validator';

// 使用创建器快速创建校验规则
const myRules = {
  // 必填的邮箱校验
  email: ruleCreators.createEmailRule(true, '请输入有效的邮箱地址'),
  
  // 数字字段校验
  amount: ruleCreators.createNumberRule(true, '请输入有效的金额'),
  
  // 大于阈值校验
  score: ruleCreators.createGreaterThanRule(60, true, '分数必须大于60分')
};

// 表单配置
const formConfig = {
  // 其他配置...
  rules: myRules
};
```

### 示例

```vue
<template>
  <BasePage
    title="用户注册"
    :form-config="formConfig"
    @form-submit="handleFormSubmit"
  />
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { BasePage } from '@/components/page';

// 表单配置
const formConfig = reactive({
  columns: [
    { label: '用户名', prop: 'username', required: true },
    { label: '邮箱', prop: 'email', required: true },
    { label: '手机号', prop: 'phone', required: true },
    { label: '密码', prop: 'password', required: true, valueType: 'password' }
  ],
  rules: {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { validator: 'checkUsername', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { validator: 'checkEmail', trigger: 'blur' }
    ],
    phone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { validator: 'checkPhone', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { validator: 'checkPassword', trigger: 'blur' }
    ]
  }
});

// 处理表单提交
const handleFormSubmit = (formData) => {
  console.log('表单数据:', formData);
};
</script>
```

## 高级用法

{{ ... }}
