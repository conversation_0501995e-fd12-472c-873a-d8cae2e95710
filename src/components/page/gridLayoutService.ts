/**
 * @description gridLayoutService - 页面布局服务层
 * <AUTHOR>
 * @date 2025-04-16
 * @update 2025-04-18 完善API请求方法，增加布局保存、页面配置获取等功能
 * @update 2025-04-20 重构为通用服务类，类比GridPageService实现
 * @update 2025-04-22 彻底剥离所有状态变量，仅保留服务方法，变量全部由store统一管理
 * @note service 层，处理接口请求与数据转换，提供完整的CRUD操作，不再持有任何响应式状态
 */
import { get, post, put, del } from '@/utils/request';
import type { AxiosResponse } from 'axios';
import { getUiConfigById as apiGetUiConfigById } from '@/modules/admin/api/uiConfig';

/**
 * 服务配置接口
 */
export interface serviceConfig {
  baseUrl: string; // 资源的基础URL
  apiPrefix?: string; // 全局API前缀
  listApi?: string | ((params: any) => string);
  createApi?: string;
  updateApi?: string | ((id: string | number) => string);
  deleteApi?: string | ((id: string | number) => string);
  saveLayoutApi?: string;
  getPageConfigApi?: string;
  transformRequestParams?: (params: any) => any;
  transformResponseData?: (response: AxiosResponse) => any;
  idField?: string;
  formatDetailResponse?: (response: any) => any;
  defaultParams?: Record<string, any>;
  defaultFormData?: Record<string, any>;
  customActions?: Record<string, any>;
  gridOptions?: {
    column: number;
    cellHeight: number;
    margin: number;
  };
  messages?: {
    addSuccess?: string;
    editSuccess?: string;
    deleteSuccess?: string;
    deleteConfirm?: string;
    saveLayoutSuccess?: string;
  };
}

/**
 * @description GridLayout 通用服务类
 * 负责处理与后端API的数据交互，包括网格布局的CRUD操作
 * 所有状态变量全部由store统一管理，本类仅提供服务方法
 */
class GridLayoutService {
  public config: serviceConfig;

  /**
   * 构造函数
   * @param config 页面配置对象
   */
  constructor(config: serviceConfig) {
    console.log('[GridLayoutService构造] config:', config);
    this.config = config;
  }

  /**
 * 获取特定操作的API URL
 * @param type 操作类型 ('list', 'create', 'update', 'delete', 'detail', 'saveLayout', 'getPageConfig')
 * @param id 资源ID (用于 update, delete, detail)
 * @param params 请求参数 (可用于 list 的函数式API)
 * @returns 完整的API URL
 */
private getApiUrl(
  type: 'list' | 'create' | 'update' | 'delete' | 'detail' | 'saveLayout' | 'getPageConfig' | 'batchDelete',
  id?: string | number,
  params?: any
): string {
  const {
    baseUrl,
    listApi,
    createApi,
    updateApi,
    deleteApi,
    saveLayoutApi,
    getPageConfigApi,
    apiPrefix
  } = this.config;

  let url = '';

  // 拼接前缀
  const prefix = apiPrefix ? '' : '';
  const base = baseUrl ? baseUrl.replace(/\/$/, '') : '';

  switch (type) {
    case 'list':
      if (typeof listApi === 'function') {
        url = listApi(params);
      } else if (typeof listApi === 'string') {
        url = listApi;
      } else {
        url = `${base}`;
      }
      break;
    case 'create':
      url = createApi || `${base}`;
      break;
    case 'update':
      if (typeof updateApi === 'function') {
        url = updateApi(id!);
      } else if (typeof updateApi === 'string') {
        url = updateApi.replace(/:id|\{id\}/g, String(id));
      } else {
        url = `${base}/${id}`;
      }
      break;
    case 'delete':
      if (typeof deleteApi === 'function') {
        url = deleteApi(id!);
      } else if (typeof deleteApi === 'string') {
        url = deleteApi.replace(/:id|\{id\}/g, String(id));
      } else {
        url = `${base}/${id}`;
      }
      break;
    case 'detail':
      url = `${base}/${id}`;
      break;
    case 'saveLayout':
      url = saveLayoutApi || `${base}/saveLayout`;
      break;
    case 'getPageConfig':
      url = getPageConfigApi || `${base}/pageConfig`;
      break;
    case 'batchDelete':
      url = `${base}/batchDelete`;
      break;
    default:
      url = base;
  }

  // 补充前缀
  if (prefix && !url.startsWith(prefix)) {
    url = `${prefix}${url.startsWith('/') ? '' : '/'}${url}`;
  }

  return url;
}

  /**
   * 获取列表数据
   * @param params 查询参数，由store传入
   * @returns Promise<any>
   */
  async fetchList(params: any): Promise<any> {
    const url = this.getApiUrl('list', undefined, params);
    //console.log('【调试】GridLayoutService，fetchList', url, params, this.config);
    if (!url || url === '/api/grid') {
      throw new Error('获取列表数据失败，API URL 为空');
    }
    const queryParams = this.config.transformRequestParams ? this.config.transformRequestParams(params) : params;
    return await get(url, queryParams);
  }

  /**
   * 创建资源
   * @param data 新建数据，由store传入
   * @returns Promise<any>
   */
  async createItem(data: any): Promise<any> {
    const url = this.getApiUrl('create');
    return await post(url, data);
  }

  /**
   * 获取资源详情
   * @param id 主键
   * @returns Promise<any>
   */
  async getItemDetail(id: string | number): Promise<any> {
    const url = this.getApiUrl('detail', id);
    return await get(url);
  }

  /**
   * 更新资源
   * @param id 主键
   * @param data 更新数据
   * @returns Promise<any>
   */
  async updateItem(id: string | number, data: any): Promise<any> {
    const url = this.getApiUrl('update', id);
    return await put(url, data);
  }

  /**
   * 删除资源
   * @param id 主键
   * @returns Promise<any>
   */
  async deleteItem(id: string | number): Promise<any> {
    const url = this.getApiUrl('delete', id);
    return await del(url);
  }

  /**
   * 批量删除资源
   * @param ids 主键数组
   * @returns Promise<any>
   */
  async batchDeleteItems(ids: (string | number)[]): Promise<any> {
    const url = this.getApiUrl('batchDelete');
    return await post(url, ids);
  }

  /**
   * 保存布局
   * @param layoutData 布局数据
   * @returns Promise<any>
   */
  async saveLayout(layoutData: any): Promise<any> {
    const url = this.getApiUrl('saveLayout');
    return await post(url, layoutData);
  }

  /**
   * 获取页面配置
   * @returns Promise<any>
   */
  async getPageConfig(): Promise<any> {
    const url = this.getApiUrl('getPageConfig');
    return await get(url);
  }

  /**
   * 处理自定义操作
   * @param action 操作名
   * @param payload 操作数据
   * @returns Promise<any>
   */
  async handleCustomAction(action: string, payload: any): Promise<any> {
    if (typeof this.config.customActions?.[action] === 'function') {
      return await this.config.customActions[action](payload);
    }
    throw new Error(`未实现的自定义操作: ${action}`);
  }

  /**
   * 获取单条UI配置（直接对接后端 /v1/ui-config/ui-configs/:id）
   * @param id 配置ID
   * @returns Promise<any>
   */
  async getUiConfigById(id: number | string): Promise<any> {
    return await apiGetUiConfigById(id);
  }
}

export default GridLayoutService;
