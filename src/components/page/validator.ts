/**
 * 表单校验函数集合
 * 提供通用的表单字段校验规则
 * 支持在BasePage和其他组件中复用
 */
import type { FormRules } from 'element-plus';

// 导出FormRules类型供其他文件使用
export type { FormRules };

// 类型定义
type ValidatorCallback = (error?: string | Error | undefined) => void;
type CustomRuleItem = {
  required?: boolean;
  message?: string;
  trigger?: string | string[];
  validator?: (rule: any, value: any, callback: ValidatorCallback) => void;
};

// 校验错误提示信息
const messages = {
  required: '该字段不能为空',
  email: '请输入正确的邮箱地址',
  phone: '请输入正确的手机号码',
  url: '请输入正确的URL地址',
  number: '请输入有效的数字',
  integer: '请输入整数',
  float: '请输入有效的小数',
  idCard: '请输入正确的身份证号码',
  password: '密码必须包含字母和数字，长度在6-20位之间',
  username: '用户名只能包含字母、数字和下划线，长度在3-20位之间',
  ipAddress: '请输入正确的IP地址',
  zipCode: '请输入正确的邮政编码',
};

/**
 * 校验邮箱
 * @param _rule 校验规则
 * @param value 字段值
 * @param callback 回调函数
 */
export const checkEmail = (_rule: any, value: string, callback: ValidatorCallback) => {
  if (!value) {
    callback();
    return;
  }
  
  const emailRegex = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  if (!emailRegex.test(value)) {
    callback(new Error(messages.email));
  } else {
    callback();
  }
};

/**
 * 校验手机号码
 * @param _rule 校验规则
 * @param value 字段值
 * @param callback 回调函数
 */
export const checkPhone = (_rule: any, value: string, callback: ValidatorCallback) => {
  if (!value) {
    callback();
    return;
  }
  
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(value)) {
    callback(new Error(messages.phone));
  } else {
    callback();
  }
};

/**
 * 校验URL地址
 * @param _rule 校验规则
 * @param value 字段值
 * @param callback 回调函数
 */
export const checkUrl = (_rule: any, value: string, callback: ValidatorCallback) => {
  if (!value) {
    callback();
    return;
  }
  
  try {
    // 使用URL构造函数验证
    new URL(value);
    callback();
  } catch (error) {
    callback(new Error(messages.url));
  }
};

/**
 * 校验数字
 * @param _rule 校验规则
 * @param value 字段值
 * @param callback 回调函数
 */
export const checkNumber = (_rule: any, value: any, callback: ValidatorCallback) => {
  if (value === '' || value === null || value === undefined) {
    callback();
    return;
  }
  
  if (isNaN(Number(value))) {
    callback(new Error(messages.number));
  } else {
    callback();
  }
};

/**
 * 校验整数
 * @param _rule 校验规则
 * @param value 字段值
 * @param callback 回调函数
 */
export const checkInteger = (_rule: any, value: any, callback: ValidatorCallback) => {
  if (value === '' || value === null || value === undefined) {
    callback();
    return;
  }
  
  const intRegex = /^-?\d+$/;
  if (!intRegex.test(String(value))) {
    callback(new Error(messages.integer));
  } else {
    callback();
  }
};

/**
 * 校验浮点数
 * @param _rule 校验规则
 * @param value 字段值
 * @param callback 回调函数
 */
export const checkFloat = (_rule: any, value: any, callback: ValidatorCallback) => {
  if (value === '' || value === null || value === undefined) {
    callback();
    return;
  }
  
  const floatRegex = /^-?\d+(\.\d+)?$/;
  if (!floatRegex.test(String(value))) {
    callback(new Error(messages.float));
  } else {
    callback();
  }
};

/**
 * 校验身份证号码
 * @param _rule 校验规则
 * @param value 字段值
 * @param callback 回调函数
 */
export const checkIdCard = (_rule: any, value: string, callback: ValidatorCallback) => {
  if (!value) {
    callback();
    return;
  }
  
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  if (!idCardRegex.test(value)) {
    callback(new Error(messages.idCard));
  } else {
    callback();
  }
};

/**
 * 校验密码
 * @param _rule 校验规则
 * @param value 字段值
 * @param callback 回调函数
 */
export const checkPassword = (_rule: any, value: string, callback: ValidatorCallback) => {
  if (!value) {
    callback();
    return;
  }
  
  // 密码必须包含字母和数字，长度在6-20位之间
  const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d).{6,20}$/;
  if (!passwordRegex.test(value)) {
    callback(new Error(messages.password));
  } else {
    callback();
  }
};

/**
 * 校验用户名
 * @param _rule 校验规则
 * @param value 字段值
 * @param callback 回调函数
 */
export const checkUsername = (_rule: any, value: string, callback: ValidatorCallback) => {
  if (!value) {
    callback();
    return;
  }
  
  // 用户名只能包含字母、数字和下划线，长度在3-20位之间
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  if (!usernameRegex.test(value)) {
    callback(new Error(messages.username));
  } else {
    callback();
  }
};

/**
 * 校验IP地址
 * @param _rule 校验规则
 * @param value 字段值
 * @param callback 回调函数
 */
export const checkIpAddress = (_rule: any, value: string, callback: ValidatorCallback) => {
  if (!value) {
    callback();
    return;
  }
  
  const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
  if (!ipRegex.test(value)) {
    callback(new Error(messages.ipAddress));
    return;
  }
  
  const parts = value.split('.');
  for (const part of parts) {
    const num = parseInt(part, 10);
    if (num < 0 || num > 255) {
      callback(new Error(messages.ipAddress));
      return;
    }
  }
  
  callback();
};

/**
 * 校验邮政编码
 * @param _rule 校验规则
 * @param value 字段值
 * @param callback 回调函数
 */
export const checkZipCode = (_rule: any, value: string, callback: ValidatorCallback) => {
  if (!value) {
    callback();
    return;
  }
  
  const zipCodeRegex = /^\d{6}$/;
  if (!zipCodeRegex.test(value)) {
    callback(new Error(messages.zipCode));
  } else {
    callback();
  }
};

/**
 * 校验值是否大于指定阈值
 * @param _rule 校验规则
 * @param value 字段值
 * @param callback 回调函数
 * @param threshold 阈值
 * @param message 自定义错误消息
 */
export const checkGreaterThan = (
  _rule: any, 
  value: number | string, 
  callback: ValidatorCallback, 
  threshold: number,
  message?: string
) => {
  if (value === '' || value === null || value === undefined) {
    callback();
    return;
  }
  
  const numValue = Number(value);
  
  if (isNaN(numValue)) {
    callback(new Error(messages.number));
    return;
  }
  
  if (numValue <= threshold) {
    callback(new Error(message || `输入值必须大于 ${threshold}`));
  } else {
    callback();
  }
};

/**
 * 创建必填验证规则
 * @param message 自定义错误消息
 * @returns 必填验证规则
 */
export const createRequiredRule = (message?: string): CustomRuleItem => {
  return {
    required: true,
    message: message || messages.required,
    trigger: ['blur', 'change']
  };
};

/**
 * 创建邮箱验证规则
 * @param required 是否必填
 * @param message 自定义错误消息
 * @returns 邮箱验证规则
 */
export const createEmailRule = (required: boolean = false, message?: string): CustomRuleItem[] => {
  const rules: CustomRuleItem[] = [
    { validator: checkEmail, trigger: 'blur' }
  ];
  
  if (required) {
    rules.unshift(createRequiredRule(message));
  }
  
  return rules;
};

/**
 * 创建手机号验证规则
 * @param required 是否必填
 * @param message 自定义错误消息
 * @returns 手机号验证规则
 */
export const createPhoneRule = (required: boolean = false, message?: string): CustomRuleItem[] => {
  const rules: CustomRuleItem[] = [
    { validator: checkPhone, trigger: 'blur' }
  ];
  
  if (required) {
    rules.unshift(createRequiredRule(message));
  }
  
  return rules;
};

/**
 * 创建URL验证规则
 * @param required 是否必填
 * @param message 自定义错误消息
 * @returns URL验证规则
 */
export const createUrlRule = (required: boolean = false, message?: string): CustomRuleItem[] => {
  const rules: CustomRuleItem[] = [
    { validator: checkUrl, trigger: 'blur' }
  ];
  
  if (required) {
    rules.unshift(createRequiredRule(message));
  }
  
  return rules;
};

/**
 * 创建数字验证规则
 * @param required 是否必填
 * @param message 自定义错误消息
 * @returns 数字验证规则
 */
export const createNumberRule = (required: boolean = false, message?: string): CustomRuleItem[] => {
  const rules: CustomRuleItem[] = [
    { validator: checkNumber, trigger: 'blur' }
  ];
  
  if (required) {
    rules.unshift(createRequiredRule(message));
  }
  
  return rules;
};

/**
 * 创建整数验证规则
 * @param required 是否必填
 * @param message 自定义错误消息
 * @returns 整数验证规则
 */
export const createIntegerRule = (required: boolean = false, message?: string): CustomRuleItem[] => {
  const rules: CustomRuleItem[] = [
    { validator: checkInteger, trigger: 'blur' }
  ];
  
  if (required) {
    rules.unshift(createRequiredRule(message));
  }
  
  return rules;
};

/**
 * 创建大于阈值验证规则
 * @param threshold 阈值
 * @param required 是否必填
 * @param message 自定义错误消息
 * @returns 大于阈值验证规则
 */
export const createGreaterThanRule = (
  threshold: number, 
  required: boolean = false, 
  message?: string
): CustomRuleItem[] => {
  const rules: CustomRuleItem[] = [
    { 
      validator: (rule: any, value: any, callback: ValidatorCallback) => 
        checkGreaterThan(rule, value, callback, threshold, message), 
      trigger: 'blur' 
    }
  ];
  
  if (required) {
    rules.unshift(createRequiredRule());
  }
  
  return rules;
};

// 新增类型定义
type ValidatorName = keyof typeof validatorFunctions;
type ValidatorParams = Record<string, any>;

/**
 * 解析校验器
 * 将字符串校验器名称转换为实际的校验函数
 * @param validator 校验器名称或函数
 * @param params 校验器参数
 * @returns 校验函数
 */
export const parseValidator = (validator: string | Function, params?: ValidatorParams): Function => {
  if (typeof validator === 'function') {
    return validator;
  }
  
  // 获取校验器函数
  const validatorFn = validatorFunctions[validator as ValidatorName];
  
  if (!validatorFn) {
    console.error(`校验器 "${validator}" 不存在`);
    return (_rule: any, _value: any, callback: ValidatorCallback) => callback();
  }
  
  // 根据校验器名称和参数返回不同的校验函数
  if (validator === 'checkGreaterThan') {
    const { threshold, message } = params || {};
    if (threshold === undefined) {
      console.error(`校验器 "${validator}" 缺少必要参数 "threshold"`);
      return (_rule: any, _value: any, callback: ValidatorCallback) => callback();
    }
    
    return (rule: any, value: any, callback: ValidatorCallback) => 
      checkGreaterThan(rule, value, callback, threshold, message);
  }
  
  // 其他校验器直接返回
  return validatorFn;
};

/**
 * 校验器函数映射表
 * 用于将字符串校验器名称映射到实际的校验函数
 */
export const validatorFunctions = {
  checkEmail,
  checkPhone,
  checkUrl,
  checkNumber,
  checkInteger,
  checkFloat,
  checkIdCard,
  checkPassword,
  checkUsername,
  checkIpAddress,
  checkZipCode,
  checkGreaterThan
};

/**
 * 导出所有校验器列表，方便使用时引用
 */
export const validators = {
  checkEmail,
  checkPhone,
  checkUrl,
  checkNumber,
  checkInteger,
  checkFloat,
  checkIdCard,
  checkPassword,
  checkUsername,
  checkIpAddress,
  checkZipCode,
  checkGreaterThan,
  parseValidator
};

/**
 * 导出所有规则生成函数列表，方便使用时引用
 */
export const ruleCreators = {
  createRequiredRule,
  createEmailRule,
  createPhoneRule,
  createUrlRule,
  createNumberRule,
  createIntegerRule,
  createGreaterThanRule
};

/**
 * 校验器选项列表，供下拉选择使用
 * 集中在一处维护，避免重复定义
 */
export const validatorOptions = [
  { label: '邮箱', value: 'validators.checkEmail' },
  { label: '手机号', value: 'validators.checkPhone' },
  { label: 'URL地址', value: 'validators.checkUrl' },
  { label: '数字', value: 'validators.checkNumber' },
  { label: '整数', value: 'validators.checkInteger' },
  { label: '浮点数', value: 'validators.checkFloat' },
  { label: '身份证', value: 'validators.checkIdCard' },
  { label: '密码', value: 'validators.checkPassword' },
  { label: '用户名', value: 'validators.checkUsername' },
  { label: 'IP地址', value: 'validators.checkIpAddress' },
  { label: '邮政编码', value: 'validators.checkZipCode' },
  { label: '大于阈值', value: 'validators.checkGreaterThan' }
];

export default {
  validators,
  ruleCreators,
  validatorOptions
};
