<!-- 
  页面配置编辑器组件
  用于可视化编辑页面配置，包括service、table、form、toolbar、search等配置项
  支持实时预览和配置保存
-->
<template>
  <el-card class="box-card">
    <template #header>
      <div class="card-header">
        <span>配置编辑</span>
      </div>
    </template>
    
    <el-tabs v-model="activeConfigTab" type="border-card">
      <!-- DTO源标签页 -->
      <el-tab-pane label="DTO源" name="dto">
        <el-form label-width="120px">
          <el-form-item label="DTO结构">
            <el-button type="primary" @click="handleFormatDto">格式化</el-button>
            <el-input
              v-model="dtoSourceJson"
              type="textarea"
              :rows="20"
              placeholder="请输入DTO源JSON"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSaveDtoSource">保存DTO源</el-button>
            <el-button type="success" @click="handleGenerateFromDto">从DTO源生成字段</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- Service配置 -->
      <el-tab-pane label="Service配置" name="service">
        <el-form :model="localConfig.serviceConfig" label-width="120px">
          <el-form-item label="基础URL">
            <el-input v-model="localConfig.serviceConfig.baseUrl" @input="handleConfigChange" />
          </el-form-item>
          <el-form-item label="添加标题">
            <el-input v-model="localConfig.serviceConfig.addTitle" @input="handleConfigChange" />
          </el-form-item>
          <el-form-item label="编辑标题">
            <el-input v-model="localConfig.serviceConfig.editTitle" @input="handleConfigChange" />
          </el-form-item>
          <el-form-item label="刷新页面">
            <el-button type="primary" @click="handleRefreshPage">刷新页面</el-button>
          </el-form-item>
          <el-form-item label="自定义操作">
            <el-button type="primary" @click="addCustomAction">添加操作</el-button>
            <div class="draggable-container">
              <p v-if="Object.keys(localConfig.serviceConfig.customActions || {}).length > 0" class="drag-tip">
                <el-icon><Rank /></el-icon> 拖拽排序
              </p>
              <draggable 
                :list="Object.entries(localConfig.serviceConfig.customActions || {})"
                :item-key="(item: any) => item[0]"
                handle=".drag-handle"
                ghost-class="ghost"
                @change="handleDragEnd"
                class="grid-container"
              >
                <template #item="{element, index}">
                  <div class="custom-action-item">
                    <div class="drag-handle">
                      <el-icon><Rank /></el-icon>
                    </div>
                    <el-divider>{{ element[0] }}</el-divider>
                    <el-form-item label="URL">
                      <el-input v-model="element[1].url" @input="handleConfigChange" />
                    </el-form-item>
                    <el-form-item label="方法">
                      <el-select v-model="element[1].method" @change="handleConfigChange">
                        <el-option label="GET" value="get" />
                        <el-option label="POST" value="post" />
                        <el-option label="PUT" value="put" />
                        <el-option label="DELETE" value="delete" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="确认消息">
                      <el-input v-model="element[1].confirmMessage" @input="handleConfigChange" />
                    </el-form-item>
                    <el-form-item label="成功消息">
                      <el-input v-model="element[1].successMessage" @input="handleConfigChange" />
                    </el-form-item>
                    <el-button type="danger" @click="removeCustomAction(element[0])">删除</el-button>
                  </div>
                </template>
              </draggable>
            </div>
          </el-form-item>
          <el-form-item label="消息配置">
            <el-form-item label="添加成功">
              <el-input 
                :model-value="localConfig.serviceConfig.messages?.addSuccess" 
                @update:model-value="(val: string) => updateMessage('addSuccess', val)"
                @input="handleConfigChange" 
              />
            </el-form-item>
            <el-form-item label="更新成功">
              <el-input 
                :model-value="localConfig.serviceConfig.messages?.updateSuccess" 
                @update:model-value="(val: string) => updateMessage('updateSuccess', val)"
                @input="handleConfigChange" 
              />
            </el-form-item>
            <el-form-item label="删除确认">
              <el-input 
                :model-value="localConfig.serviceConfig.messages?.deleteConfirm" 
                @update:model-value="(val: string) => updateMessage('deleteConfirm', val)"
                @input="handleConfigChange" 
              />
            </el-form-item>
            <el-form-item label="删除成功">
              <el-input 
                :model-value="localConfig.serviceConfig.messages?.deleteSuccess" 
                @update:model-value="(val: string) => updateMessage('deleteSuccess', val)"
                @input="handleConfigChange" 
              />
            </el-form-item>
          </el-form-item>
        </el-form>
        
        <!-- Service配置源码编辑区 -->
        <div class="source-code-section">
          <el-divider>
            <el-button type="text" size="small" @click="serviceSourceVisible = !serviceSourceVisible">
              {{ serviceSourceVisible ? '收起源码' : '展开源码' }}
              <el-icon>
                <component :is="serviceSourceVisible ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </el-divider>
          
          <div v-show="serviceSourceVisible">
            <el-button type="warning" @click="handleFormatSpecificSource('serviceConfig')">格式化</el-button>
            <el-input
              v-model="serviceSourceCode"
              type="textarea"
              :rows="10"
              @input="handleSpecificSourceChange('serviceConfig')"
              placeholder="请输入Service配置JSON"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 表格配置 -->
      <el-tab-pane label="表格配置" name="table">
        <el-form :model="localConfig.tableConfig" label-width="120px">
          <el-form-item label="显示选择">
            <el-switch v-model="localConfig.tableConfig.showSelection" @change="handleConfigChange" />
          </el-form-item>
          <el-form-item label="显示序号">
            <el-switch v-model="localConfig.tableConfig.showIndex" @change="handleConfigChange" />
          </el-form-item>
          <el-form-item label="显示操作">
            <el-switch v-model="localConfig.tableConfig.showActions" @change="handleConfigChange" />
          </el-form-item>
          <el-form-item label="行键">
            <el-input v-model="localConfig.tableConfig.rowKey" @input="handleConfigChange" />
          </el-form-item>
          <el-form-item label="显示分页">
            <el-switch v-model="localConfig.tableConfig.showPagination" @change="handleConfigChange" />
          </el-form-item>
          <el-form-item label="分页配置">
            <el-form-item label="每页条数">
              <el-input-number 
                :model-value="localConfig.tableConfig.pagination?.pageSize" 
                @update:model-value="(val: number) => updatePagination('pageSize', val)"
                :min="1" 
                @change="handleConfigChange" 
              />
            </el-form-item>
            <el-form-item label="可选条数">
              <el-select 
                :model-value="localConfig.tableConfig.pagination?.pageSizes" 
                @update:model-value="(val: number[]) => updatePagination('pageSizes', val)"
                multiple 
                @change="handleConfigChange"
              >
                <el-option
                  v-for="size in [10, 20, 30, 50, 100]"
                  :key="size"
                  :label="size"
                  :value="size"
                />
              </el-select>
            </el-form-item>
          </el-form-item>
          <el-form-item label="操作按钮">
            <el-button type="primary" @click="addTableActionButton">添加按钮</el-button>
            <div class="draggable-container">
              <p v-if="(localConfig.tableConfig.actions?.buttons || []).length > 0" class="drag-tip">
                <el-icon><Rank /></el-icon> 拖拽排序
              </p>
              <draggable 
                :list="localConfig.tableConfig.actions?.buttons || []"
                :item-key="(item: any) => item.action || Math.random().toString()"
                handle=".drag-handle"
                ghost-class="ghost"
                @change="handleDragEnd"
                class="grid-container"
              >
                <template #item="{element, index}">
                  <div class="button-item">
                    <div class="drag-handle">
                      <el-icon><Rank /></el-icon>
                    </div>
                    <el-divider>按钮 {{ index + 1 }}</el-divider>
                    <el-form-item label="文本">
                      <el-input v-model="element.text" @input="handleConfigChange" />
                    </el-form-item>
                    <el-form-item label="类型">
                      <el-select v-model="element.type" @change="handleConfigChange">
                        <el-option label="主要" value="primary" />
                        <el-option label="成功" value="success" />
                        <el-option label="警告" value="warning" />
                        <el-option label="危险" value="danger" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="操作">
                      <el-input v-model="element.action" @input="handleConfigChange" />
                    </el-form-item>
                    <el-form-item label="图标">
                      <el-input v-model="element.icon" @input="handleConfigChange" />
                    </el-form-item>
                    <el-button type="danger" @click="removeTableActionButton(index)">删除</el-button>
                  </div>
                </template>
              </draggable>
            </div>
          </el-form-item>
          <el-form-item label="列配置">
            <el-button type="primary" @click="addTableColumn">添加列</el-button>
            <div class="draggable-container">
              <p v-if="localConfig.tableConfig.columns.length > 0" class="drag-tip">
                <el-icon><Rank /></el-icon> 拖拽排序
              </p>
              <draggable 
                v-model="localConfig.tableConfig.columns"
                :item-key="(item: any) => item.prop || Math.random().toString()"
                handle=".drag-handle"
                ghost-class="ghost"
                @change="handleDragEnd"
                class="grid-container"
              >
                <template #item="{element, index}">
                  <div class="column-item">
                    <div class="drag-handle">
                      <el-icon><Rank /></el-icon>
                    </div>
                    <el-divider>列 {{ index + 1 }}</el-divider>
                    <el-form-item label="标签">
                      <el-input v-model="element.label" @input="handleConfigChange" />
                    </el-form-item>
                    <el-form-item label="属性">
                      <el-input v-model="element.prop" @input="handleConfigChange" />
                    </el-form-item>
                    <el-form-item label="宽度">
                      <el-input-number v-model="element.width" :min="0" @change="handleConfigChange" />
                    </el-form-item>
                    <el-form-item label="类型">
                      <el-select v-model="element.valueType" @change="handleConfigChange">
                        <el-option label="文本" value="text" />
                        <el-option label="标签" value="tag" />
                        <el-option label="图片" value="img" />
                        <el-option label="日期时间" value="date-picker" />
                        <el-option label="单选框" value="radio" />
                        <el-option label="选择器" value="select" />
                      </el-select>
                    </el-form-item>
                    
                    <!-- 日期时间格式配置 -->
                    <template v-if="element.valueType === 'date-picker'">
                      <el-divider>日期时间配置</el-divider>
                      
                      <el-form-item label="日期类型">
                        <el-select 
                          :model-value="getFieldProp(element, 'type')" 
                          @update:model-value="(val: string) => updateFieldProp(element, 'type', val)"
                          placeholder="选择日期类型"
                        >
                          <el-option label="日期" value="date" />
                          <el-option label="日期时间" value="datetime" />
                          <el-option label="年份" value="year" />
                          <el-option label="月份" value="month" />
                          <el-option label="周" value="week" />
                        </el-select>
                      </el-form-item>
                      
                      <el-form-item label="显示格式">
                        <el-input 
                          :model-value="getFieldProp(element, 'format')" 
                          @update:model-value="(val: string) => updateFieldProp(element, 'format', val)"
                          placeholder="如: YYYY-MM-DD"
                        >
                          <template #append>
                            <el-tooltip content="例如：YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss">
                              <el-icon><QuestionFilled /></el-icon>
                            </el-tooltip>
                          </template>
                        </el-input>
                      </el-form-item>
                      
                      <el-form-item label="值格式">
                        <el-input 
                          :model-value="getFieldProp(element, 'valueFormat')" 
                          @update:model-value="(val: string) => updateFieldProp(element, 'valueFormat', val)"
                          placeholder="如: YYYY-MM-DD"
                        >
                          <template #append>
                            <el-tooltip content="例如：YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss">
                              <el-icon><QuestionFilled /></el-icon>
                            </el-tooltip>
                          </template>
                        </el-input>
                      </el-form-item>
                      
                      <el-form-item label="提示文本">
                        <el-input 
                          :model-value="getFieldProp(element, 'placeholder')" 
                          @update:model-value="(val: string) => updateFieldProp(element, 'placeholder', val)"
                          placeholder="如: 请选择日期"
                        />
                      </el-form-item>
                    </template>
                    
                    <!-- 选择器和单选框配置 -->
                    <template v-if="element.valueType === 'select' || element.valueType === 'radio'">
                      <el-divider>选项配置</el-divider>
                      
                      <el-form-item label="选项值">
                        <el-button size="small" type="primary" @click="addOptionToItem(element)">添加选项</el-button>
                        <el-button size="small" type="success" @click="importValueEnum(element)">导入ValueEnum</el-button>
                        
                        <div v-for="(option, optIndex) in element.options || []" :key="optIndex" class="option-item">
                          <el-divider>选项 {{ optIndex + 1 }}</el-divider>
                          
                          <el-form-item label="标签">
                            <el-input v-model="option.label" @input="handleConfigChange" />
                          </el-form-item>
                          
                          <el-form-item label="值">
                            <el-input v-model="option.value" @input="handleConfigChange" />
                          </el-form-item>
                          
                          <el-form-item label="颜色">
                            <el-select v-model="option.color" @change="handleConfigChange">
                              <el-option label="默认" value="" />
                              <el-option label="红色" value="red" />
                              <el-option label="蓝色" value="blue" />
                              <el-option label="绿色" value="green" />
                              <el-option label="黄色" value="yellow" />
                              <el-option label="灰色" value="gray" />
                              <el-option label="主要" value="primary" />
                              <el-option label="成功" value="success" />
                              <el-option label="警告" value="warning" />
                              <el-option label="危险" value="danger" />
                              <el-option label="信息" value="info" />
                            </el-select>
                          </el-form-item>
                          
                          <el-button type="danger" size="small" @click="removeOptionFromItem(element, optIndex)">
                            删除选项
                          </el-button>
                        </div>
                      </el-form-item>
                      
                      <el-form-item label="提示文本">
                        <el-input 
                          :model-value="getFieldProp(element, 'placeholder')" 
                          @update:model-value="(val: string) => updateFieldProp(element, 'placeholder', val)"
                          :placeholder="element.valueType === 'select' ? '请选择' : '请选择一项'"
                        />
                      </el-form-item>
                    </template>
                    
                    <el-button type="danger" @click="removeTableColumn(index)">删除</el-button>
                  </div>
                </template>
              </draggable>
            </div>
          </el-form-item>
        </el-form>
        
        <!-- 表格配置源码编辑区 -->
        <div class="source-code-section">
          <el-divider>
            <el-button type="text" size="small" @click="tableSourceVisible = !tableSourceVisible">
              {{ tableSourceVisible ? '收起源码' : '展开源码' }}
              <el-icon>
                <component :is="tableSourceVisible ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </el-divider>
          
          <div v-show="tableSourceVisible">
            <el-button type="warning" @click="handleFormatSpecificSource('tableConfig')">格式化</el-button>
            <el-input
              v-model="tableSourceCode"
              type="textarea"
              :rows="10"
              @input="handleSpecificSourceChange('tableConfig')"
              placeholder="请输入表格配置JSON"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 表单配置 -->
      <el-tab-pane label="表单配置" name="form">
        <el-form :model="localConfig.formConfig" label-width="120px">
          <el-form-item label="标签宽度">
            <el-input-number v-model="localConfig.formConfig.labelWidth" :min="0" @change="handleConfigChange" />
          </el-form-item>
          <el-form-item label="标签位置">
            <el-select v-model="localConfig.formConfig.labelPosition" @change="handleConfigChange">
              <el-option label="左对齐" value="left" />
              <el-option label="右对齐" value="right" />
              <el-option label="顶部对齐" value="top" />
            </el-select>
          </el-form-item>
          
          <!-- 新增：表单宽度配置 -->
          <el-form-item label="弹窗宽度">
            <el-input v-model="localConfig.formConfig.width" @input="handleConfigChange" placeholder="如: 600px 或 50%">
              <template #append>
                <el-tooltip content="支持像素(px)或百分比(%)，默认500px">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </template>
            </el-input>
          </el-form-item>
          
          <!-- 新增：表单校验规则配置 -->
          <el-form-item label="表单校验规则">
            <el-button type="primary" @click="addFormRule">添加校验规则</el-button>
            <div class="draggable-container">
              <p v-if="Object.keys(localConfig.formConfig.rules || {}).length > 0" class="drag-tip">
                <el-icon><Warning /></el-icon> 表单校验规则
              </p>
              
              <div v-for="(rules, prop) in localConfig.formConfig.rules || {}" :key="prop" class="rules-item">
                <el-divider>{{ prop }}字段规则</el-divider>
                <el-form-item label="字段名">
                  <el-input v-model="rulesKeyMap[prop]" @blur="updateRuleKey(prop)" placeholder="字段名" />
                </el-form-item>
                
                <div v-for="(rule, index) in rules" :key="index" class="rule-item">
                  <el-divider>规则 {{ index + 1 }}</el-divider>
                  <el-form-item label="是否必填">
                    <el-switch v-model="rule.required" @change="handleConfigChange" />
                  </el-form-item>
                  <el-form-item label="错误提示">
                    <el-input v-model="rule.message" @input="handleConfigChange" placeholder="请输入错误提示" />
                  </el-form-item>
                  <el-form-item label="最小长度">
                    <el-input-number v-model="rule.min" :min="0" @change="handleConfigChange" />
                  </el-form-item>
                  <el-form-item label="最大长度">
                    <el-input-number v-model="rule.max" :min="0" @change="handleConfigChange" />
                  </el-form-item>
                  <el-form-item label="验证模式">
                    <el-select v-model="rule.trigger" @change="handleConfigChange">
                      <el-option label="触发时" value="change" />
                      <el-option label="失去焦点时" value="blur" />
                      <el-option label="两者都是" value="both" />
                    </el-select>
                  </el-form-item>
                  <el-button type="danger" @click="removeFormRule(prop, index)">删除规则</el-button>
                  <el-button type="primary" @click="addRuleItem(prop)">添加规则</el-button>
                </div>
              </div>
            </div>
          </el-form-item>
          
          <el-form-item label="表单项">
            <el-button type="primary" @click="addFormItem">添加表单项</el-button>
            <div class="draggable-container">
              <p v-if="localConfig.formConfig.columns.length > 0" class="drag-tip">
                <el-icon><Rank /></el-icon> 拖拽排序
              </p>
              <draggable 
                v-model="localConfig.formConfig.columns"
                :item-key="(item: any) => item.prop || Math.random().toString()"
                handle=".drag-handle"
                ghost-class="ghost"
                @change="handleDragEnd"
                class="grid-container"
              >
                <template #item="{element, index}">
                  <div class="form-item">
                    <div class="drag-handle">
                      <el-icon><Rank /></el-icon>
                    </div>
                    <el-divider>表单项 {{ index + 1 }}</el-divider>
                    <el-form-item label="标签">
                      <el-input v-model="element.label" @input="handleConfigChange" />
                    </el-form-item>
                    <el-form-item label="属性">
                      <el-input v-model="element.prop" @input="handleConfigChange" />
                    </el-form-item>
                    <el-form-item label="类型">
                      <el-select v-model="element.valueType" @change="(val: string) => handleFormItemTypeChange(element, val)">
                        <el-option label="文本" value="text" />
                        <el-option label="密码" value="password" />
                        <el-option label="单选框" value="radio" />
                        <el-option label="多选框" value="checkbox" />
                        <el-option label="选择器" value="select" />
                        <el-option label="日期选择器" value="date-picker" />
                        <el-option label="时间选择器" value="time-picker" />
                        <el-option label="数字输入框" value="input-number" />
                        <el-option label="开关" value="switch" />
                        <el-option label="评分" value="rate" />
                        <el-option label="级联选择器" value="cascader" />
                        <el-option label="上传" value="upload" />
                        <el-option label="图片" value="img" />
                        <el-option label="可复制文本" value="copy" />
                        <el-option label="多行文本" value="textarea" />
                      </el-select>
                    </el-form-item>
                    
                    <!-- 新增：日期选择器配置 -->
                    <template v-if="element.valueType === 'date-picker'">
                      <el-divider>日期选择器配置</el-divider>
                      
                      <el-form-item label="日期类型">
                        <el-select 
                          :model-value="getFieldProp(element, 'type')" 
                          @update:model-value="(val: string) => updateFieldProp(element, 'type', val)"
                          placeholder="选择日期类型"
                        >
                          <el-option label="日期" value="date" />
                          <el-option label="日期时间" value="datetime" />
                          <el-option label="年份" value="year" />
                          <el-option label="月份" value="month" />
                          <el-option label="周" value="week" />
                        </el-select>
                      </el-form-item>
                      
                      <el-form-item label="显示格式">
                        <el-input 
                          :model-value="getFieldProp(element, 'format')" 
                          @update:model-value="(val: string) => updateFieldProp(element, 'format', val)"
                          placeholder="如: YYYY-MM-DD"
                        >
                          <template #append>
                            <el-tooltip content="例如：YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss">
                              <el-icon><QuestionFilled /></el-icon>
                            </el-tooltip>
                          </template>
                        </el-input>
                      </el-form-item>
                      
                      <el-form-item label="值格式">
                        <el-input 
                          :model-value="getFieldProp(element, 'valueFormat')" 
                          @update:model-value="(val: string) => updateFieldProp(element, 'valueFormat', val)"
                          placeholder="如: YYYY-MM-DD"
                        >
                          <template #append>
                            <el-tooltip content="例如：YYYY-MM-DD 或 timestamp">
                              <el-icon><QuestionFilled /></el-icon>
                            </el-tooltip>
                          </template>
                        </el-input>
                      </el-form-item>
                      
                      <el-form-item label="提示文本">
                        <el-input 
                          :model-value="getFieldProp(element, 'placeholder')" 
                          @update:model-value="(val: string) => updateFieldProp(element, 'placeholder', val)"
                          placeholder="如: 请选择日期"
                        />
                      </el-form-item>
                    </template>
                    
                    <!-- 上传类型的专有配置 -->
                    <template v-if="element.valueType === 'upload'">
                      <el-divider>上传配置</el-divider>
                      
                      <el-form-item label="上传样式">
                        <el-select 
                          :model-value="getUploadProp(element, 'style')" 
                          @update:model-value="(val: string) => updateUploadProp(element, 'style', val)"
                          placeholder="上传区域样式"
                        >
                          <el-option label="默认（拖拽区域）" value="default" />
                          <el-option label="按钮" value="button" />
                          <el-option label="链接" value="link" />
                        </el-select>
                      </el-form-item>
                      
                      <el-form-item label="文件类型">
                        <el-input 
                          :model-value="getUploadProp(element, 'accept')" 
                          @update:model-value="(val: string) => updateUploadProp(element, 'accept', val)"
                          placeholder="如: .jpg,.png,.pdf"
                        />
                      </el-form-item>
                      
                      <el-form-item label="文件用途">
                        <el-input 
                          :model-value="getUploadProp(element, 'fileUsage')" 
                          @update:model-value="(val: string) => updateUploadProp(element, 'fileUsage', val)"
                          placeholder="如: avatar, product_img"
                        />
                      </el-form-item>
                      
                      <el-form-item label="大小限制">
                        <el-input-number 
                          :model-value="getUploadProp(element, 'sizeLimit') ? getUploadProp(element, 'sizeLimit') / (1024 * 1024) : 5" 
                          @update:model-value="(val: number) => updateUploadProp(element, 'sizeLimit', val ? val * 1024 * 1024 : 5 * 1024 * 1024)"
                          :min="0"
                          :step="1"
                          placeholder="文件大小限制(MB)"
                        />
                        <span class="ml-2 text-gray-500">MB</span>
                      </el-form-item>
                      
                      <el-form-item label="文件数量">
                        <el-input-number 
                          :model-value="getUploadProp(element, 'fileLimit')" 
                          @update:model-value="(val: number) => updateUploadProp(element, 'fileLimit', val || 1)"
                          :min="1"
                          placeholder="最大文件数量"
                        />
                      </el-form-item>
                      
                      <el-form-item label="提示文本">
                        <el-input 
                          :model-value="getUploadProp(element, 'tip')" 
                          @update:model-value="(val: string) => updateUploadProp(element, 'tip', val)"
                          placeholder="上传区域提示文本"
                        />
                      </el-form-item>
                    </template>
                    
                    <el-form-item label="必填">
                      <el-switch v-model="element.required" @change="handleConfigChange" />
                    </el-form-item>
                    
                    <!-- 新增：表单项占位宽度配置 -->
                    <el-form-item label="占位宽度">
                      <el-select 
                        :model-value="element.colProps && element.colProps.span || 24" 
                        @update:model-value="(val: number) => updateColProps(element, 'span', val)"
                      >
                        <el-option label="占满(24/24)" :value="24" />
                        <el-option label="一半(12/24)" :value="12" />
                        <el-option label="三分之一(8/24)" :value="8" />
                        <el-option label="四分之一(6/24)" :value="6" />
                      </el-select>
                    </el-form-item>
                    
                    <!-- 新增：提示项配置 -->
                    <el-form-item label="提示信息">
                      <el-input v-model="element.tooltip" @input="handleConfigChange" placeholder="悬停显示的提示信息" />
                    </el-form-item>
                    
                    <!-- 新增：表单项属性配置 -->
                    <el-form-item label="表单项属性">
                      <el-input 
                        type="textarea"
                        :value="getFieldPropsString(element)"
                        @input="(val: string) => updateFieldPropsFromInput(element, val)"
                        placeholder="JSON格式的表单项属性，如：{ maxlength: 10, showWordLimit: true }"
                        :rows="3"
                      />
                    </el-form-item>
                    
                    <!-- 新增：默认值配置 -->
                    <el-form-item label="默认值">
                      <el-input v-model="element.defaultValue" @input="handleConfigChange" />
                    </el-form-item>
                    
                    <el-button type="danger" @click="removeFormItem(index)">删除</el-button>
                    
                    <!-- 新增：添加到校验规则按钮 -->
                    <el-button type="primary" @click="addRuleForField(element.prop)" v-if="element.prop">添加校验规则</el-button>
                  </div>
                </template>
              </draggable>
            </div>
          </el-form-item>
        </el-form>
        
        <!-- 表单配置源码编辑区 -->
        <div class="source-code-section">
          <el-divider>
            <el-button type="text" size="small" @click="formSourceVisible = !formSourceVisible">
              {{ formSourceVisible ? '收起源码' : '展开源码' }}
              <el-icon>
                <component :is="formSourceVisible ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </el-divider>
          
          <div v-show="formSourceVisible">
            <el-button type="warning" @click="handleFormatSpecificSource('formConfig')">格式化</el-button>
            <el-input
              v-model="formSourceCode"
              type="textarea"
              :rows="10"
              @input="handleSpecificSourceChange('formConfig')"
              placeholder="请输入表单配置JSON"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 详情配置 -->
      <el-tab-pane label="详情配置" name="info">
        <el-form :model="localConfig.infoConfig" label-width="120px">
          <el-form-item label="边框">
            <el-switch 
              :model-value="localConfig.infoConfig?.border" 
              @update:model-value="(val: boolean) => updateInfoConfig('border', val)"
              @change="handleConfigChange" 
            />
          </el-form-item>
          <el-form-item label="列数">
            <el-input-number 
              :model-value="localConfig.infoConfig?.column" 
              @update:model-value="(val: number) => updateInfoConfig('column', val)"
              :min="1" 
              :max="4" 
              @change="handleConfigChange" 
            />
          </el-form-item>
          <el-form-item label="尺寸">
            <el-select 
              :model-value="localConfig.infoConfig?.size" 
              @update:model-value="(val: string) => updateInfoConfig('size', val)"
              @change="handleConfigChange"
            >
              <el-option label="大" value="large" />
              <el-option label="默认" value="default" />
              <el-option label="小" value="small" />
            </el-select>
          </el-form-item>
          <el-form-item label="方向">
            <el-select 
              :model-value="localConfig.infoConfig?.direction" 
              @update:model-value="(val: string) => updateInfoConfig('direction', val)"
              @change="handleConfigChange"
            >
              <el-option label="水平" value="horizontal" />
              <el-option label="垂直" value="vertical" />
            </el-select>
          </el-form-item>
          
          <!-- 新增：详情弹窗宽度配置 -->
          <el-form-item label="弹窗宽度">
            <el-input
              :model-value="localConfig.infoConfig?.width" 
              @update:model-value="(val: string) => updateInfoConfig('width', val)"
              @input="handleConfigChange"
              placeholder="如: 700px 或 60%"
            >
              <template #append>
                <el-tooltip content="支持像素(px)或百分比(%)，默认700px">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="详情项">
            <el-button type="primary" @click="addInfoItem">添加详情项</el-button>
            <div class="draggable-container">
              <p v-if="(localConfig.infoConfig?.columns || []).length > 0" class="drag-tip">
                <el-icon><Rank /></el-icon> 拖拽排序
              </p>
              <draggable 
                :list="localConfig.infoConfig?.columns || []"
                :item-key="(item: any) => item.prop || Math.random().toString()"
                handle=".drag-handle"
                ghost-class="ghost"
                @change="handleDragEnd"
                class="grid-container"
              >
                <template #item="{element, index}">
                  <div class="info-item">
                    <div class="drag-handle">
                      <el-icon><Rank /></el-icon>
                    </div>
                    <el-divider>详情项 {{ index + 1 }}</el-divider>
                    <el-form-item label="标签">
                      <el-input v-model="element.label" @input="handleConfigChange" />
                    </el-form-item>
                    <el-form-item label="属性">
                      <el-input v-model="element.prop" @input="handleConfigChange" />
                    </el-form-item>

                    <!-- 新增：详情项显示类型 -->
                    <el-form-item label="显示类型">
                      <el-select v-model="element.valueType" @change="handleConfigChange">
                        <el-option label="文本" value="text" />
                        <el-option label="标签" value="tag" />
                        <el-option label="图片" value="img" />
                        <el-option label="日期时间" value="date-picker" />
                        <el-option label="单选框" value="radio" />
                        <el-option label="选择器" value="select" />
                        <el-option label="自定义渲染" value="custom" />
                      </el-select>
                    </el-form-item>

                    <!-- 新增：日期时间格式配置（详情页） -->
                    <template v-if="element.valueType === 'date-picker'">
                      <el-divider>日期时间格式</el-divider>
                      
                      <el-form-item label="日期格式">
                        <el-input 
                          :model-value="getFieldProp(element, 'format')" 
                          @update:model-value="(val: string) => updateFieldProp(element, 'format', val)"
                          placeholder="如: YYYY-MM-DD"
                        >
                          <template #append>
                            <el-tooltip content="例如：YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss">
                              <el-icon><QuestionFilled /></el-icon>
                            </el-tooltip>
                          </template>
                        </el-input>
                      </el-form-item>
                    </template>

                    <!-- 新增：选项配置(当类型为select时) -->
                    <template v-if="element.valueType === 'select' || element.valueType === 'radio'">
                      <el-divider>选项配置</el-divider>
                      
                      <el-form-item label="选项值">
                        <el-button size="small" type="primary" @click="addOptionToItem(element)">添加选项</el-button>
                        <el-button size="small" type="success" @click="importValueEnum(element)">导入ValueEnum</el-button>
                        
                        <div v-for="(option, optIndex) in element.options || []" :key="optIndex" class="option-item">
                          <el-divider>选项 {{ optIndex + 1 }}</el-divider>
                          
                          <el-form-item label="标签">
                            <el-input v-model="option.label" @input="handleConfigChange" />
                          </el-form-item>
                          
                          <el-form-item label="值">
                            <el-input v-model="option.value" @input="handleConfigChange" />
                          </el-form-item>
                          
                          <el-form-item label="颜色">
                            <el-select v-model="option.color" @change="handleConfigChange">
                              <el-option label="红色" value="red" />
                              <el-option label="蓝色" value="blue" />
                              <el-option label="绿色" value="green" />
                              <el-option label="黄色" value="yellow" />
                              <el-option label="灰色" value="gray" />
                            </el-select>
                          </el-form-item>
                          
                          <el-button type="danger" size="small" @click="removeOptionFromItem(element, optIndex)">
                            删除选项
                          </el-button>
                        </div>
                      </el-form-item>
                    </template>

                    <!-- 新增：自定义渲染函数配置 -->
                    <template v-if="element.valueType === 'custom'">
                      <el-form-item label="渲染类型">
                        <el-select 
                          :model-value="getRenderType(element)"
                          @update:model-value="(val: string) => updateRenderType(element, val)"
                        >
                          <el-option label="描述组件" value="descriptions" />
                          <el-option label="直接渲染" value="render" />
                          <el-option label="HTML渲染" value="html" />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="渲染代码模板">
                        <el-select 
                          :model-value="getRenderTemplate(element)"
                          @update:model-value="(val: string) => updateRenderTemplate(element, val)"
                          placeholder="选择渲染模板"
                        >
                          <el-option label="图片" value="image" />
                          <el-option label="标签" value="tag" />
                          <el-option label="视频" value="video" />
                          <el-option label="按钮" value="button" />
                          <el-option label="多标签" value="tags" />
                          <el-option label="自定义HTML" value="html" />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="渲染代码">
                        <el-input
                          type="textarea"
                          :model-value="getRenderCode(element)"
                          @update:model-value="(val: string) => updateRenderCode(element, val)"
                          placeholder="渲染函数代码，如: ({ value }) => h(ElTag, { type: 'success' }, () => value)"
                          :rows="3"
                        />
                      </el-form-item>
                    </template>

                    <el-button type="danger" @click="removeInfoItem(index)">删除</el-button>
                  </div>
                </template>
              </draggable>
            </div>
          </el-form-item>
        </el-form>
        
        <!-- 详情配置源码编辑区 -->
        <div class="source-code-section">
          <el-divider>
            <el-button type="text" size="small" @click="infoSourceVisible = !infoSourceVisible">
              {{ infoSourceVisible ? '收起源码' : '展开源码' }}
              <el-icon>
                <component :is="infoSourceVisible ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </el-divider>
          
          <div v-show="infoSourceVisible">
            <el-button type="warning" @click="handleFormatSpecificSource('infoConfig')">格式化</el-button>
            <el-input
              v-model="infoSourceCode"
              type="textarea"
              :rows="10"
              @input="handleSpecificSourceChange('infoConfig')"
              placeholder="请输入详情配置JSON"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 工具栏配置 -->
      <el-tab-pane label="工具栏配置" name="toolbar">
        <el-form :model="localConfig.toolbarConfig" label-width="120px">
          <el-form-item label="按钮">
            <el-button type="primary" @click="addToolbarButton">添加按钮</el-button>
            <div class="draggable-container">
              <p v-if="localConfig.toolbarConfig.buttons.length > 0" class="drag-tip">
                <el-icon><Rank /></el-icon> 拖拽排序
              </p>
              <draggable 
                v-model="localConfig.toolbarConfig.buttons"
                :item-key="(item: any) => item.action || Math.random().toString()"
                handle=".drag-handle"
                ghost-class="ghost"
                @change="handleDragEnd"
                class="grid-container"
              >
                <template #item="{element, index}">
                  <div class="button-item">
                    <div class="drag-handle">
                      <el-icon><Rank /></el-icon>
                    </div>
                    <el-divider>按钮 {{ index + 1 }}</el-divider>
                    <el-form-item label="文本">
                      <el-input v-model="element.text" @input="handleConfigChange" />
                    </el-form-item>
                    <el-form-item label="类型">
                      <el-select v-model="element.type" @change="handleConfigChange">
                        <el-option label="主要" value="primary" />
                        <el-option label="成功" value="success" />
                        <el-option label="警告" value="warning" />
                        <el-option label="危险" value="danger" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="图标">
                      <el-input v-model="element.icon" @input="handleConfigChange" />
                    </el-form-item>
                    <el-form-item label="操作">
                      <el-input v-model="element.action" @input="handleConfigChange" />
                    </el-form-item>
                    <el-button type="danger" @click="removeToolbarButton(index)">删除</el-button>
                  </div>
                </template>
              </draggable>
            </div>
          </el-form-item>
        </el-form>
        
        <!-- 工具栏配置源码编辑区 -->
        <div class="source-code-section">
          <el-divider>
            <el-button type="text" size="small" @click="toolbarSourceVisible = !toolbarSourceVisible">
              {{ toolbarSourceVisible ? '收起源码' : '展开源码' }}
              <el-icon>
                <component :is="toolbarSourceVisible ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </el-divider>
          
          <div v-show="toolbarSourceVisible">
            <el-button type="warning" @click="handleFormatSpecificSource('toolbarConfig')">格式化</el-button>
            <el-input
              v-model="toolbarSourceCode"
              type="textarea"
              :rows="10"
              @input="handleSpecificSourceChange('toolbarConfig')"
              placeholder="请输入工具栏配置JSON"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 搜索配置 -->
      <el-tab-pane label="搜索配置" name="search">
        <el-form :model="localConfig.searchConfig" label-width="120px">
          <el-form-item label="搜索项">
            <el-button type="primary" @click="addSearchItem">添加搜索项</el-button>
            <div class="draggable-container">
              <p v-if="localConfig.searchConfig.columns.length > 0" class="drag-tip">
                <el-icon><Rank /></el-icon> 拖拽排序
              </p>
              <draggable 
                v-model="localConfig.searchConfig.columns"
                :item-key="(item: any) => item.prop || Math.random().toString()"
                handle=".drag-handle"
                ghost-class="ghost"
                @change="handleDragEnd"
                class="grid-container"
              >
                <template #item="{element, index}">
                  <div class="search-item">
                    <div class="drag-handle">
                      <el-icon><Rank /></el-icon>
                    </div>
                    <el-divider>搜索项 {{ index + 1 }}</el-divider>
                    <el-form-item label="标签">
                      <el-input v-model="element.label" @input="handleConfigChange" />
                    </el-form-item>
                    <el-form-item label="属性">
                      <el-input v-model="element.prop" @input="handleConfigChange" />
                    </el-form-item>
                    <el-button type="danger" @click="removeSearchItem(index)">删除</el-button>
                  </div>
                </template>
              </draggable>
            </div>
          </el-form-item>
        </el-form>
        
        <!-- 搜索配置源码编辑区 -->
        <div class="source-code-section">
          <el-divider>
            <el-button type="text" size="small" @click="searchSourceVisible = !searchSourceVisible">
              {{ searchSourceVisible ? '收起源码' : '展开源码' }}
              <el-icon>
                <component :is="searchSourceVisible ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </el-divider>
          
          <div v-show="searchSourceVisible">
            <el-button type="warning" @click="handleFormatSpecificSource('searchConfig')">格式化</el-button>
            <el-input
              v-model="searchSourceCode"
              type="textarea"
              :rows="10"
              @input="handleSpecificSourceChange('searchConfig')"
              placeholder="请输入搜索配置JSON"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 源码编辑 -->
      <el-tab-pane label="源码编辑" name="source">
        <div class="source-editor">
          <el-button type="primary" @click="handleSaveSource">保存配置</el-button>
          <el-button type="warning" @click="handleFormatSource">格式化</el-button>
          <el-input
            v-model="sourceCode"
            type="textarea"
            :rows="20"
            @input="handleSourceChange"
            placeholder="请输入JSON配置"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, computed } from 'vue';
import type { PageConfig } from '@/types/page';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Rank, Warning, QuestionFilled } from '@element-plus/icons-vue';
// 使用带类型定义的导入方式
import draggable from 'vuedraggable';

// 定义拖拽项的通用接口
// interface DraggableItem {
//   [key: string]: any;
// }

// 定义拖拽变更事件类型
// interface DraggableChangeEvent {
//   added?: { element: DraggableItem; newIndex: number };
//   removed?: { element: DraggableItem; oldIndex: number };
//   moved?: { element: DraggableItem; newIndex: number; oldIndex: number };
// }

const props = defineProps<{
  modelValue: PageConfig;
  dtoSourceJson?: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: PageConfig): void;
  (e: 'update:dtoSourceJson', value: string): void;
  (e: 'refresh-page'): void;
  (e: 'generate-from-dto'): void;
}>();

const activeConfigTab = ref('service');

// DTO源相关 - 使用计算属性实现双向绑定
const dtoSourceJson = computed({
  get: () => props.dtoSourceJson || '{}',
  set: (val) => emit('update:dtoSourceJson', val)
});

// 监听dtoSourceJson变化
watch(() => props.dtoSourceJson, (newValue) => {
  if (newValue && newValue !== '{}') {
    // 尝试解析DTO源
    try {
      // 直接解析JSON，无需保存到变量
      JSON.parse(newValue);
      console.log('DTO源解析成功');
    } catch (error) {
      console.error('解析DTO源JSON失败:', error);
    }
  }
}, { immediate: true });

// 创建本地配置副本
const localConfig = ref<PageConfig>({
  version: '1.0',
  title: '',
  serviceConfig: {
    baseUrl: '',
    messages: {
      addSuccess: '',
      updateSuccess: '',
      deleteConfirm: '',
      deleteSuccess: ''
    },
    customActions: {},
    addTitle: '',
    editTitle: '',
    viewTitle: ''
  },
  tableConfig: {
    columns: [],
    actions: {
      title: '操作',
      width: 150,
      buttons: []
    },
    showIndex: true,
    showSelection: false,
    pagination: {
      pageSize: 10,
      pageSizes: [10, 20, 30, 50]
    }
  },
  formConfig: {
    columns: [],
    labelWidth: 100,
    labelPosition: 'right',
    width: '500px',
    rules: {} // 新增表单校验规则字段
  },
  infoConfig: { // 确保 infoConfig 始终存在并包含默认属性
    columns: [],
    border: false, // 默认值改为false
    column: 1,    // 默认值改为1
    size: 'default',
    direction: 'horizontal'
  },
  toolbarConfig: {
    buttons: []
  },
  searchConfig: {
    columns: []
  }
});

// 新增：用于表单规则字段名临时存储的映射
const rulesKeyMap = ref<Record<string, string>>({});

// 源码编辑相关
const sourceCode = ref(JSON.stringify(props.modelValue, null, 2));

// 各部分源码编辑的可见性控制
const serviceSourceVisible = ref(false);
const tableSourceVisible = ref(false);
const formSourceVisible = ref(false);
const infoSourceVisible = ref(false);
const toolbarSourceVisible = ref(false);
const searchSourceVisible = ref(false);

// 各部分源码
const serviceSourceCode = ref(JSON.stringify(localConfig.value.serviceConfig, null, 2));
const tableSourceCode = ref(JSON.stringify(localConfig.value.tableConfig, null, 2));
const formSourceCode = ref(JSON.stringify(localConfig.value.formConfig, null, 2));
const infoSourceCode = ref(JSON.stringify(localConfig.value.infoConfig, null, 2));
const toolbarSourceCode = ref(JSON.stringify(localConfig.value.toolbarConfig, null, 2));
const searchSourceCode = ref(JSON.stringify(localConfig.value.searchConfig, null, 2));

// 监听localConfig变化，更新各部分源码
watch(() => localConfig.value.serviceConfig, (val) => {
  serviceSourceCode.value = JSON.stringify(val, null, 2);
}, { deep: true });

watch(() => localConfig.value.tableConfig, (val) => {
  tableSourceCode.value = JSON.stringify(val, null, 2);
}, { deep: true });

watch(() => localConfig.value.formConfig, (val) => {
  formSourceCode.value = JSON.stringify(val, null, 2);
}, { deep: true });

watch(() => localConfig.value.infoConfig, (val) => {
  infoSourceCode.value = JSON.stringify(val, null, 2);
}, { deep: true });

watch(() => localConfig.value.toolbarConfig, (val) => {
  toolbarSourceCode.value = JSON.stringify(val, null, 2);
}, { deep: true });

watch(() => localConfig.value.searchConfig, (val) => {
  searchSourceCode.value = JSON.stringify(val, null, 2);
}, { deep: true });

// 处理特定区域源码变化
const handleSpecificSourceChange = (configKey: keyof PageConfig) => {
  try {
    const sourceMap: Record<string, string> = {
      'serviceConfig': serviceSourceCode.value,
      'tableConfig': tableSourceCode.value,
      'formConfig': formSourceCode.value,
      'infoConfig': infoSourceCode.value,
      'toolbarConfig': toolbarSourceCode.value,
      'searchConfig': searchSourceCode.value
    };
    
    const newConfig = JSON.parse(sourceMap[configKey]);
    localConfig.value[configKey] = newConfig;
    handleConfigChange();
  } catch (error) {
    console.error(`${configKey} JSON解析错误:`, error);
  }
};

// 格式化特定区域源码
const handleFormatSpecificSource = (configKey: keyof PageConfig) => {
  try {
    const sourceMap: Record<string, any> = {
      'serviceConfig': serviceSourceCode,
      'tableConfig': tableSourceCode,
      'formConfig': formSourceCode,
      'infoConfig': infoSourceCode,
      'toolbarConfig': toolbarSourceCode,
      'searchConfig': searchSourceCode
    };
    
    const parsed = JSON.parse(sourceMap[configKey].value);
    sourceMap[configKey].value = JSON.stringify(parsed, null, 2);
    ElMessage.success('格式化成功');
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化');
  }
};

// 防抖处理配置更新
let updateTimeout: number | null = null;
const handleConfigChange = () => {
  if (updateTimeout) {
    clearTimeout(updateTimeout);
  }
  updateTimeout = window.setTimeout(() => {
    // 更新源码
    sourceCode.value = JSON.stringify(localConfig.value, null, 2);
    // 触发父组件更新
    emit('update:modelValue', { ...localConfig.value });
  }, 300);
};

// 处理源码变化
const handleSourceChange = () => {
  try {
    const newConfig = JSON.parse(sourceCode.value);
    localConfig.value = newConfig;
    handleConfigChange();
  } catch (error) {
    // JSON 解析错误时不更新配置
    console.error('JSON解析错误:', error);
  }
};

// 格式化源码
const handleFormatSource = () => {
  try {
    const parsed = JSON.parse(sourceCode.value);
    sourceCode.value = JSON.stringify(parsed, null, 2);
    ElMessage.success('格式化成功');
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化');
  }
};

// 保存源码配置
const handleSaveSource = () => {
  try {
    const newConfig = JSON.parse(sourceCode.value);
    localConfig.value = newConfig;
    handleConfigChange();
    ElMessage.success('配置已保存');
  } catch (error) {
    ElMessage.error('JSON格式错误，无法保存');
  }
};

// 处理页面刷新
const handleRefreshPage = () => {
  emit('refresh-page');
};

// 监听props变化，更新本地配置和源码
watch(() => props.modelValue, (newValue) => {
  if (JSON.stringify(newValue) !== JSON.stringify(localConfig.value)) {
    // 使用深拷贝确保完全复制对象，避免引用问题
    localConfig.value = JSON.parse(JSON.stringify(newValue));
    sourceCode.value = JSON.stringify(newValue, null, 2);
    
    // 确保infoConfig存在并包含必要属性
    if (!localConfig.value.infoConfig) {
      localConfig.value.infoConfig = {
        columns: [],
        border: false,
        column: 1,
        size: 'default',
        direction: 'horizontal'
      };
    }
    
    // 确保所有必要的配置对象都存在
    if (!localConfig.value.serviceConfig.messages) {
      localConfig.value.serviceConfig.messages = {
        addSuccess: '添加成功',
        updateSuccess: '更新成功',
        deleteConfirm: '确认删除？',
        deleteSuccess: '删除成功'
      };
    }
    
    // 确保tableConfig的pagination存在
    if (!localConfig.value.tableConfig.pagination) {
      localConfig.value.tableConfig.pagination = {
        pageSize: 10,
        pageSizes: [10, 20, 30, 50]
      };
    }
    
    // 强制所有表单项更新完毕
    nextTick(() => {
      handleConfigChange();
    });
  }
}, { deep: true, immediate: true });

// 添加自定义操作
function addCustomAction() {
  const key = `action_${Object.keys(localConfig.value.serviceConfig.customActions).length + 1}`;
  localConfig.value.serviceConfig.customActions[key] = {
    url: '',
    method: 'get',
    confirmMessage: '',
    successMessage: ''
  };
  handleConfigChange();
}

// 删除自定义操作
function removeCustomAction(key: string) {
  delete localConfig.value.serviceConfig.customActions[key];
  handleConfigChange();
}

// 添加表格列
function addTableColumn() {
  localConfig.value.tableConfig.columns.push({
    label: '',
    prop: '',
    width: 120,
    valueType: 'text'
  });
  handleConfigChange();
}

// 删除表格列
function removeTableColumn(index: number) {
  localConfig.value.tableConfig.columns.splice(index, 1);
  handleConfigChange();
}

// 添加表单项
function addFormItem() {
  localConfig.value.formConfig.columns.push({
    label: '',
    prop: '',
    valueType: 'text',
    required: false,
    colProps: { // 添加默认占位宽度
      span: 24
    }
  });
  handleConfigChange();
}

// 删除表单项
function removeFormItem(index: number) {
  localConfig.value.formConfig.columns.splice(index, 1);
  handleConfigChange();
}

// 添加工具栏按钮
function addToolbarButton() {
  localConfig.value.toolbarConfig.buttons.push({
    text: '',
    type: 'primary',
    icon: '',
    action: ''
  });
  handleConfigChange();
}

// 删除工具栏按钮
function removeToolbarButton(index: number) {
  localConfig.value.toolbarConfig.buttons.splice(index, 1);
  handleConfigChange();
}

// 添加搜索项
function addSearchItem() {
  localConfig.value.searchConfig.columns.push({
    label: '',
    prop: ''
  });
  handleConfigChange();
}

// 删除搜索项
function removeSearchItem(index: number) {
  localConfig.value.searchConfig.columns.splice(index, 1);
  handleConfigChange();
}

// 添加表格操作按钮
function addTableActionButton() {
  if (!localConfig.value.tableConfig.actions) {
    localConfig.value.tableConfig.actions = {
      title: '操作',
      width: 200,
      buttons: []
    };
  }
  localConfig.value.tableConfig.actions.buttons.push({
    text: '',
    type: 'primary',
    action: '',
    icon: ''
  });
  handleConfigChange();
}

// 删除表格操作按钮
function removeTableActionButton(index: number) {
  localConfig.value.tableConfig.actions?.buttons?.splice(index, 1);
  handleConfigChange();
}

// 添加详情项
function addInfoItem() {
  if (!localConfig.value.infoConfig) {
    localConfig.value.infoConfig = {
      columns: [],
      border: true,
      column: 2,
      size: 'default',
      direction: 'horizontal'
    };
  }
  
  localConfig.value.infoConfig.columns.push({
    label: '新字段',
    prop: 'newField'
  });
  handleConfigChange();
};

// 删除详情项
function removeInfoItem(index: number) {
  if (localConfig.value.infoConfig?.columns) {
    localConfig.value.infoConfig.columns.splice(index, 1);
    handleConfigChange();
  }
};

// 在localConfig初始化时确保infoConfig存在
watch(localConfig, (newValue) => {
  if (!newValue.infoConfig && newValue.tableConfig) {
    // 如果没有infoConfig但有tableConfig，从tableConfig复制列配置
    const infoColumns = newValue.tableConfig.columns.map(col => ({
      label: col.label,
      prop: col.prop
    }));
    
    newValue.infoConfig = {
      columns: infoColumns,
      border: true,
      column: 2,
      size: 'default',
      direction: 'horizontal'
    };
  }
}, { immediate: true, deep: true });

// 更新消息配置
function updateMessage(key: 'addSuccess' | 'updateSuccess' | 'deleteConfirm' | 'deleteSuccess', value: string) {
  if (!localConfig.value.serviceConfig.messages) {
    localConfig.value.serviceConfig.messages = {};
  }
  localConfig.value.serviceConfig.messages[key] = value;
  handleConfigChange();
}

// 更新分页配置
function updatePagination(key: 'pageSize' | 'pageSizes', value: number | number[]) {
  if (!localConfig.value.tableConfig.pagination) {
    localConfig.value.tableConfig.pagination = {
      pageSize: 15,
      pageSizes: [10, 20, 30, 50]
    };
  }
  if (key === 'pageSize' && typeof value === 'number') {
    localConfig.value.tableConfig.pagination.pageSize = value;
  } else if (key === 'pageSizes' && Array.isArray(value)) {
    localConfig.value.tableConfig.pagination.pageSizes = value;
  }
  handleConfigChange();
}

// 更新详情配置
function updateInfoConfig(key: 'border' | 'column' | 'size' | 'direction' | 'width', value: boolean | number | string) {
  if (!localConfig.value.infoConfig) {
    localConfig.value.infoConfig = {
      columns: [],
      border: true,
      column: 2,
      size: 'default',
      direction: 'horizontal'
    };
  }
  
  // 根据key的类型进行类型断言
  switch (key) {
    case 'border':
      localConfig.value.infoConfig.border = value as boolean;
      break;
    case 'column':
      localConfig.value.infoConfig.column = value as number;
      break;
    case 'size':
      localConfig.value.infoConfig.size = value as 'large' | 'default' | 'small';
      break;
    case 'direction':
      localConfig.value.infoConfig.direction = value as 'horizontal' | 'vertical';
      break;
    case 'width':
      localConfig.value.infoConfig.width = value as string;
      break;
  }
  
  handleConfigChange();
}

// 格式化DTO源
const handleFormatDto = () => {
  try {
    const parsed = JSON.parse(dtoSourceJson.value);
    emit('update:dtoSourceJson', JSON.stringify(parsed, null, 2));
    ElMessage.success('DTO源格式化成功');
    
    // 触发配置更新
    nextTick(() => {
      handleConfigChange();
    });
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化');
  }
};

// 保存DTO源
const handleSaveDtoSource = () => {
  try {
    const parsedDto = JSON.parse(dtoSourceJson.value);
    emit('update:dtoSourceJson', JSON.stringify(parsedDto, null, 2));
    ElMessage.success('DTO源保存成功');
    
    // 触发配置更新
    nextTick(() => {
      handleConfigChange();
    });
  } catch (error) {
    ElMessage.error('JSON格式错误，无法保存DTO源');
  }
};

// 从DTO源生成字段 - 调用父组件方法处理
const handleGenerateFromDto = () => {
  try {
    // 先验证JSON格式是否正确
    JSON.parse(dtoSourceJson.value);
    // 发送事件给父组件处理
    emit('generate-from-dto');
  } catch (error) {
    ElMessage.error('DTO源JSON格式错误');
  }
};

// 拖拽排序后的处理函数
const handleDragEnd = () => {
  handleConfigChange();
};

// 新增：添加表单校验规则
function addFormRule() {
  // 创建一个唯一的键名
  const key = `field_${Object.keys(localConfig.value.formConfig.rules || {}).length + 1}`;
  
  // 确保rules对象存在
  if (!localConfig.value.formConfig.rules) {
    localConfig.value.formConfig.rules = {};
  }
  
  // 添加新规则
  localConfig.value.formConfig.rules[key] = [
    {
      required: true,
      message: '请输入必填字段',
      trigger: 'blur'
    }
  ];
  
  // 更新映射
  rulesKeyMap.value[key] = key;
  
  handleConfigChange();
}

// 新增：为特定字段添加校验规则
function addRuleForField(prop: string) {
  if (!prop) return;
  
  // 确保rules对象存在
  if (!localConfig.value.formConfig.rules) {
    localConfig.value.formConfig.rules = {};
  }
  
  // 如果该字段已经有规则，不重复添加
  if (localConfig.value.formConfig.rules[prop]) {
    ElMessage.info(`字段 ${prop} 已有校验规则，请直接编辑`);
    activeConfigTab.value = 'form'; // 切换到表单配置标签页
    return;
  }
  
  // 添加新规则
  localConfig.value.formConfig.rules[prop] = [
    {
      required: true,
      message: `请输入${prop}`,
      trigger: 'blur'
    }
  ];
  
  // 更新映射
  rulesKeyMap.value[prop] = prop;
  
  handleConfigChange();
  ElMessage.success(`已为字段 ${prop} 添加校验规则`);
}

// 新增：为特定规则添加规则项
function addRuleItem(prop: string) {
  if (!localConfig.value.formConfig.rules || !localConfig.value.formConfig.rules[prop]) return;
  
  localConfig.value.formConfig.rules[prop].push({
    required: false,
    message: '请输入有效内容',
    trigger: 'blur'
  });
  
  handleConfigChange();
}

// 新增：移除表单校验规则
function removeFormRule(prop: string, index: number) {
  if (!localConfig.value.formConfig.rules || !localConfig.value.formConfig.rules[prop]) return;
  
  if (localConfig.value.formConfig.rules[prop].length > 1) {
    // 如果有多个规则项，只删除特定项
    localConfig.value.formConfig.rules[prop].splice(index, 1);
  } else {
    // 如果只有一个规则项，删除整个规则
    delete localConfig.value.formConfig.rules[prop];
    delete rulesKeyMap.value[prop];
  }
  
  handleConfigChange();
}

// 新增：更新规则键名
function updateRuleKey(oldKey: string) {
  if (!localConfig.value.formConfig.rules || !localConfig.value.formConfig.rules[oldKey]) return;
  
  const newKey = rulesKeyMap.value[oldKey];
  if (newKey && newKey !== oldKey) {
    // 创建新键
    localConfig.value.formConfig.rules[newKey] = [...localConfig.value.formConfig.rules[oldKey]];
    // 删除旧键
    delete localConfig.value.formConfig.rules[oldKey];
    // 更新映射
    delete rulesKeyMap.value[oldKey];
    rulesKeyMap.value[newKey] = newKey;
    
    handleConfigChange();
  }
}

// 新增：更新表单项的colProps
function updateColProps(element: any, key: string, value: number) {
  if (!element.colProps) {
    element.colProps = {};
  }
  element.colProps[key] = value;
  handleConfigChange();
}

// 新增：获取fieldProps字符串表示
function getFieldPropsString(element: any): string {
  if (!element.fieldProps) return '';
  return JSON.stringify(element.fieldProps, null, 2);
}

// 新增：更新fieldProps
function updateFieldPropsFromInput(element: any, val: string) {
  try {
    if (val) {
      element.fieldProps = JSON.parse(val);
    } else {
      element.fieldProps = undefined;
    }
    handleConfigChange();
  } catch (error) {
    ElMessage.error('JSON格式错误，无法解析表单项属性');
  }
}

// 新增：获取字段属性
function getFieldProp(element: any, prop: string): any {
  if (!element.fieldProps) return '';
  return element.fieldProps[prop];
}

// 新增：更新字段属性
function updateFieldProp(element: any, prop: string, value: any) {
  if (!element.fieldProps) {
    element.fieldProps = {};
  }
  element.fieldProps[prop] = value;
  handleConfigChange();
}

// 获取上传配置属性
function getUploadProp(element: any, prop: string): any {
  if (!element.uploadProps) return '';
  return element.uploadProps[prop];
}

// 更新上传配置属性
function updateUploadProp(element: any, prop: string, value: any) {
  if (!element.uploadProps) {
    element.uploadProps = {};
  }
  element.uploadProps[prop] = value;
  handleConfigChange();
}

// 新增：为详情项添加选项
function addOptionToItem(element: any) {
  if (!element.options) {
    element.options = [];
  }
  
  element.options.push({
    label: '选项' + (element.options.length + 1),
    value: 'option' + (element.options.length + 1),
    color: 'blue'
  });
  
  handleConfigChange();
}

// 新增：从详情项中移除选项
function removeOptionFromItem(element: any, index: number) {
  if (!element.options) return;
  
  element.options.splice(index, 1);
  handleConfigChange();
}

// 新增：获取渲染类型
function getRenderType(element: any): string {
  if (element.renderDescriptionsItem) return 'descriptions';
  if (element.render) return 'render';
  if (element.renderHTML) return 'html';
  return 'descriptions'; // 默认类型
}

// 新增：更新渲染类型
function updateRenderType(element: any, type: string) {
  // 清除现有的渲染函数属性
  delete element.renderDescriptionsItem;
  delete element.render;
  delete element.renderHTML;
  
  // 根据选择的类型设置对应的默认渲染函数
  if (type === 'descriptions') {
    element.renderDescriptionsItem = getRenderTemplateCode('tag', type);
  } else if (type === 'render') {
    element.render = getRenderTemplateCode('tag', type);
  } else if (type === 'html') {
    element.renderHTML = getRenderTemplateCode('html', type);
  }
  
  handleConfigChange();
}

// 新增：获取渲染模板类型
function getRenderTemplate(element: any): string {
  const renderCode = getRenderCode(element);
  
  if (renderCode.includes('ElImage') || renderCode.includes('src:')) return 'image';
  if (renderCode.includes('ElTag')) return 'tag';
  if (renderCode.includes('video')) return 'video';
  if (renderCode.includes('ElButton')) return 'button';
  if (renderCode.includes('map') && renderCode.includes('ElTag')) return 'tags';
  if (renderCode.includes('as string')) return 'html';
  
  return ''; // 无法识别的模板
}

// 新增：获取渲染代码
function getRenderCode(element: any): string {
  if (element.renderDescriptionsItem) {
    return element.renderDescriptionsItem.toString();
  }
  if (element.render) {
    return element.render.toString();
  }
  if (element.renderHTML) {
    return element.renderHTML.toString();
  }
  return '';
}

// 新增：更新渲染代码模板
function updateRenderTemplate(element: any, template: string) {
  const renderType = getRenderType(element);
  const templateCode = getRenderTemplateCode(template, renderType);
  
  if (renderType === 'descriptions') {
    element.renderDescriptionsItem = templateCode;
  } else if (renderType === 'render') {
    element.render = templateCode;
  } else if (renderType === 'html') {
    element.renderHTML = templateCode;
  }
  
  handleConfigChange();
}

// 新增：获取渲染模板代码
function getRenderTemplateCode(template: string, renderType: string): string {
  // 为了简化,这里返回字符串形式的代码而不是实际函数
  // 实际使用时这个字符串会被转换为函数
  switch (template) {
    case 'image':
      return renderType === 'descriptions' 
        ? '({ value }) => h(ElImage, { src: value, style: { width: "140px" } })' 
        : 'value => h(ElImage, { src: value, style: { width: "140px" } })';
    case 'tag':
      return renderType === 'descriptions'
        ? '({ value }) => h(ElTag, { type: "success" }, () => value)'
        : 'value => h(ElTag, { type: "success" }, () => value)';
    case 'video':
      return renderType === 'descriptions'
        ? '({ value }) => h("video", { src: value, controls: true, style: { width: "200px" } })'
        : 'value => h("video", { src: value, controls: true, style: { width: "200px" } })';
    case 'button':
      return renderType === 'descriptions'
        ? '({ value }) => h(ElButton, { type: "primary" }, () => value)'
        : 'value => h(ElButton, { type: "primary" }, () => value)';
    case 'tags':
      return renderType === 'descriptions'
        ? '({ value }) => value?.map(item => h(ElTag, { type: item, style: { marginLeft: "5px" } }, () => item))'
        : 'value => value?.map(item => h(ElTag, { type: item, style: { marginLeft: "5px" } }, () => item))';
    case 'html':
      return renderType === 'html'
        ? 'value => value as string'
        : 'value => value';
    default:
      return renderType === 'descriptions'
        ? '({ value }) => value'
        : 'value => value';
  }
}

// 新增：更新渲染代码
function updateRenderCode(element: any, code: string) {
  const renderType = getRenderType(element);
  
  try {
    if (renderType === 'descriptions') {
      // 简化处理：直接保存代码字符串，不做实际转换
      element.renderDescriptionsItem = code;
    } else if (renderType === 'render') {
      element.render = code;
    } else if (renderType === 'html') {
      element.renderHTML = code;
    }
    
    handleConfigChange();
  } catch (error) {
    console.error('渲染代码格式错误:', error);
    ElMessage.error('渲染代码格式错误');
  }
}

// 新增：将 valueEnum 转换为 options
const convertValueEnumToOptions = (valueEnum: Record<string, any> | undefined): any[] => {
  if (!valueEnum) return [];
  
  return Object.entries(valueEnum).map(([value, item]) => ({
    value,
    label: item.text,
    color: item.status
  }));
};

// 新增：将 options 转换为 valueEnum
// const convertOptionsToValueEnum = (options: any[] | undefined): Record<string, any> | undefined => {
//   if (!options || !options.length) return undefined;
  
//   const valueEnum: Record<string, any> = {};
//   options.forEach(option => {
//     valueEnum[option.value] = {
//       text: option.label,
//       status: option.color
//     };
//   });
  
//   return valueEnum;
// };

// 新增：导入ValueEnum
function importValueEnum(element: any) {
  ElMessageBox.prompt('请输入ValueEnum JSON', '导入ValueEnum', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPlaceholder: `示例：
{
  "0": {
    "text": "未知",
    "status": "info"
  },
  "1": {
    "text": "男",
    "status": "success"
  },
  "2": {
    "text": "女",
    "status": "warning"
  }
}`,
  })
    .then(({ value }) => {
      try {
        const valueEnum = JSON.parse(value);
        // 使用转换函数将valueEnum转换为options
        const options = convertValueEnumToOptions(valueEnum);
        
        // 清空原有选项
        if (!element.options) {
          element.options = [];
        } else {
          element.options.length = 0;
        }
        
        // 添加新选项
        options.forEach(option => {
          element.options.push(option);
        });
        
        // 触发配置变更
        handleConfigChange();
        
        ElMessage({
          type: 'success',
          message: '成功导入ValueEnum并转换为选项'
        });
      } catch (error) {
        ElMessage.error('ValueEnum格式错误，请检查JSON格式');
        console.error('导入ValueEnum错误:', error);
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消导入'
      });
    });
}

// 新增：处理表单项类型变更，自动设置默认值
function handleFormItemTypeChange(element: any, type: string) {
  // 根据类型设置默认值
  switch(type) {
    case 'text':
    case 'textarea':
    case 'password':
      element.defaultValue = '';
      break;
    case 'number':
    case 'slider':
    case 'rate':
      element.defaultValue = 0;
      break;
    case 'switch':
      element.defaultValue = false;
      break;
    case 'checkbox':
      element.defaultValue = [];
      break;
    case 'date':
    case 'dateTime':
    case 'time':
      element.defaultValue = '';
      break;
    case 'select':
    case 'radio':
    case 'cascader':
    case 'treeSelect':
      // 如果有选项，取第一个选项的值作为默认值
      if (element.options && element.options.length > 0) {
        element.defaultValue = element.options[0].value;
      }
      break;
    case 'upload':
      element.defaultValue = [];
      break;
    case 'color':
      element.defaultValue = '#409EFF';
      break;
    default:
      element.defaultValue = '';
  }
  
  handleConfigChange();
}
</script>

<style scoped>
.custom-action-item,
.column-item,
.form-item,
.button-item,
.search-item,
.info-item {
  margin: 10px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  position: relative;
  background-color: #fff;
  transition: all 0.3s;
  flex: 1;
  min-width: 300px; /* 设置最小宽度 */
}

.drag-handle {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: move;
  color: #909399;
  padding: 5px;
  border-radius: 4px;
}

.drag-handle:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.draggable-container {
  padding: 10px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  margin-top: 10px;
  min-height: 50px;
}

/* 添加网格布局样式 */
.grid-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
}

.drag-tip {
  color: #909399;
  font-size: 12px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
  width: 100%; /* 提示信息占满宽度 */
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.el-divider {
  margin: 15px 0;
}

.source-editor {
  padding: 20px;
}

.source-editor .el-button {
  margin-bottom: 10px;
  margin-right: 10px;
}

.rules-item {
  margin: 10px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  position: relative;
  background-color: #fff;
  transition: all 0.3s;
}

.rule-item {
  margin: 10px 0;
  padding: 10px;
  border: 1px dashed #ebeef5;
  border-radius: 4px;
  background-color: #f8f8f8;
}

/* 添加源码编辑区样式 */
.source-code-section {
  margin-top: 20px;
  padding: 10px;
  border-radius: 4px;
}

.source-code-section .el-divider {
  margin: 12px 0;
}
</style>
