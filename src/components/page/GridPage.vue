<!--
  @description GridPage - 基于gridstack.js的响应式布局网格页面组件
  <AUTHOR> AI
  @date 2025-04-12
  @update 2025-04-22 适配GridLayoutStore新类/工厂函数
  @note 本文件为GridPage组件，所有数据均通过store传递
-->
<template>
  <div class="grid-page">
    <div class="grid-container">
      <div ref="gridRef" class="grid-stack"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * GridPage是一个基于gridstack.js库的响应式布局网格页面组件
 * 它提供了创建可拖拽、可调整大小的网格系统，用于构建仪表盘、控制面板等页面
 * @update 2025-04-22 适配GridLayoutStore新类/工厂函数
 */
// import { ref, onMounted, onBeforeUnmount, watch, h, createApp, provide, nextTick } from 'vue';
// import 'gridstack/dist/gridstack.min.css';
// import GridItemComponent from '@/components/base/GridItemComponent.vue';
// import type { GridContentConfig } from '@/types/grid';
// import type GridLayoutStore from './gridLayoutStore';

// // 组件属性定义
// const props = defineProps({
//   /**
//    * store实例，类型为GridLayoutStore类实例
//    */
//   store: {
//     type: Object as () => InstanceType<typeof GridLayoutStore>,
//     required: true
//   }
// });

// // 事件
// const emit = defineEmits([
//   'change',
//   'added', 
//   'removed', 
//   'resized', 
//   'dragged'
// ]);

// // 使用传入的唯一store实例
// const store = props.store;

// // 提供store实例给子组件
// provide('gridPageStore', store);

// // 网格引用
// const gridRef = ref<HTMLElement | null>(null);
// // 网格实例
// let grid: GridStack | null = null;

// // 允许传递给GridStack的字段白名单
// const GRIDSTACK_OPTION_KEYS = [
//   'column', 'cellHeight', 'margin', 'float', 'animate',
//   'disableResize', 'disableDrag', 'handle', 'resizable',
//   'disableOneColumnMode', 'alwaysShowResizeHandle',
//   'minWidth', 'oneColumnSize'
// ];

// // 默认网格配置
// /*
//  * 默认网格配置
//  * 关键：handle: '.grid-item-drag-handle' 只允许拖动按钮拖动
//  */
// const defaultOptions = {
//   column: 12, // 默认列数
//   cellHeight: 50,
//   margin: 10,
//   float: true,
//   animate: true,
//   // 拖拽和调整大小控制
//   disableResize: !store.pageConfig?.serviceConfig?.resizable,
//   disableDrag: !store.pageConfig?.serviceConfig?.draggable, // 关键：允许拖动
//   handle: '.grid-item-drag-handle', // 只允许拖动按钮拖动
//   // 调整大小配置
//   resizable: {
//     handles: 'e, se' // 只能从右侧和右下角调整大小
//   },
//   // 响应式配置
//   disableOneColumnMode: false, 
//   alwaysShowResizeHandle: 'mobile' as const,
//   // 核心响应式配置
//   minWidth: 768, // 触发移动模式的宽度
//   // 单列模式配置
//   oneColumnSize: 768, // <= 768px时切换到单列模式
// };

// /**
//  * 初始化网格（确保所有内容渲染后再初始化GridStack）
//  */
// const initGrid = async () => {
//   if (!gridRef.value) return;

//   // 通过store获取配置和数据
//   const serviceConfig = props.store.pageConfig?.serviceConfig || {};
//   const gridItems = props.store.gridItems || [];
//   gridItems.value.forEach((item: any) => {
//     if(typeof item.content === 'string') {
//       item.content = JSON.parse(item.content);
//     }
//   });

//   // 确保配置项类型正确 - 修复类型转换问题
//   const safeOptions = { ...serviceConfig };
//   if (safeOptions && safeOptions.column !== undefined) {
//     safeOptions.column = Number(safeOptions.column);
//     if (isNaN(safeOptions.column)) safeOptions.column = 12;
//   }
//   if (safeOptions && safeOptions.cellHeight !== undefined) {
//     safeOptions.cellHeight = Number(safeOptions.cellHeight);
//     if (isNaN(safeOptions.cellHeight)) safeOptions.cellHeight = 50;
//   }
//   if (safeOptions && safeOptions.margin !== undefined) {
//     safeOptions.margin = Number(safeOptions.margin);
//     if (isNaN(safeOptions.margin)) safeOptions.margin = 10;
//   }

//   // 合并并过滤options，只保留GridStack支持的字段
//   const mergedOptions = { ...defaultOptions, ...safeOptions };
//   const options: Record<string, any> = {};
//   for (const key of GRIDSTACK_OPTION_KEYS) {
//     if (mergedOptions[key] !== undefined) options[key] = mergedOptions[key];
//   }
//   options.handle = '.grid-item-drag-handle'; // 强制保证handle正确
//   console.log('最终传递给GridStack的options:', options);

//   await nextTick();
//   setTimeout(() => {
//     if (grid) {
//       grid.destroy(false);
//       grid = null;
//     }
//     if (gridRef.value) {
//       grid = GridStack.init(options, gridRef.value);
//       // 初始化后强制加载gridItems，确保页面有内容
//       if (grid && gridItems.value && gridItems.value.length > 0) {
//         grid.load(gridItems.value);
//         console.log('已调用grid.load:', gridItems.value);
//         // 关键：遍历所有节点，渲染自定义内容
//         const nodes = grid.engine.nodes || [];
//         nodes.forEach(node => {
//           if (node.el && node.content) {
//             renderGridItemContent(node);
//           }
//         });
//       }
//       // ...（保留原有事件绑定逻辑）
//     }
//   }, 50);
// };

// /**
//  * 加载网格项并渲染
//  * 优先级：store.gridItems > API加载
//  */
// const loadGridItems = async () => {
//   // 通过store获取配置和数据
//   const serviceConfig = props.store.pageConfig?.serviceConfig || {};
//   const gridItems = props.store.gridItems.value || [];
  
//   console.log('尝试从store加载项目:', gridItems, serviceConfig);
//   if (!grid) return;
  
//   // 如果store已有数据，使用store数据
//   if (gridItems && gridItems.length > 0) {
//     console.log('使用store数据加载网格项:', gridItems);
//     grid.load(gridItems);
    
//     // 手动渲染内容
//     setTimeout(() => {
//       if (!grid) return;
//       const nodes = grid.engine.nodes || [];
//       nodes.forEach(node => {
//         if (node.el && node.content) {
//           renderGridItemContent(node);
//         }
//       });
//     }, 100);
//     return;
//   }
  
//   // 如果没有现有数据，通过store加载对象数据
//   await props.store.loadGridData();
//   // 加载后自动渲染（如有需要可加类似上面 grid.load 的逻辑）
// };

// /**
//  * 刷新数据
//  * 从API重新获取数据并更新网格
//  */
// const refreshData = async () => {
//   try {
//     // 清空网格
//     // if (grid) {
//     //   grid.removeAll();
//     // }
    
//     // 重新从API获取数据
//     await props.store.loadGridData();
//   } catch (error) {
//     console.error('刷新数据失败:', error);
//   }
// };

// /**
//  * 处理网格项编辑事件
//  */
// const handleGridItemEdit = (event: Event) => {
//   const customEvent = event as CustomEvent;
//   const { node } = customEvent.detail;
//   if (node) {
//     console.log('编辑网格项:', node.id);
//     props.store.openEditForm(node as unknown as GridStackNode);
//   }
// };

// /**
//  * 处理网格项配置事件
//  */
// const handleGridItemConfig = (event: Event) => {
//   const customEvent = event as CustomEvent;
//   const { node } = customEvent.detail;
//   console.log('配置网格项:', node.id);
// };

// /**
//  * 处理网格项关闭事件
//  */
// const handleGridItemClose = (event: Event) => {
//   const customEvent = event as CustomEvent;
//   const { node } = customEvent.detail;
//   if (node && store.pageConfig?.serviceConfig?.resizable) {
//     console.log('关闭网格项:', node.id);
//     // 如果需要移除网格项
//     if (grid && node.id) {
//       if (store.pageConfig?.serviceConfig?.resizable) {
//         // 如果设置了removable，则调用后端API删除
//         props.store.deleteGridItem(node as unknown as GridStackNode).then((success: boolean) => {
//           if (success) {
//             removeGridItem(`#${node.id}`);
//           }
//         });
//       } else {
//         // 否则只从UI中移除
//         removeGridItem(`#${node.id}`);
//         emit('removed', grid.save());
//       }
//     }
//   }
// };

// /**
//  * 渲染网格项内容
//  * @param node 网格节点
//  */
// const renderGridItemContent = (node: GridStackNode) => {
//   if (!node.el || !node.content) return;
  
//   const contentEl = node.el.querySelector('.grid-stack-item-content');
//   if (!contentEl) return;
  
//   // 检查内容类型
//   if (typeof node.content === 'string') {
//     // 如果是HTML字符串，直接设置innerHTML
//     contentEl.innerHTML = node.content;
//   } else if (typeof node.content === 'object') {
//     // 如果是对象配置，使用Vue组件渲染
//     try {
//       // 清空原有内容
//       contentEl.innerHTML = '';
      
//       // 存储应用实例的属性名称
//       const appKey = '_vueApp';
      
//       // 检查是否已经挂载了应用，如果有则先卸载
//       if ((contentEl as any)[appKey]) {
//         try {
//           (contentEl as any)[appKey].unmount();
//           (contentEl as any)[appKey] = null;
//         } catch (unmountError) {
//           console.error('卸载原有应用失败:', unmountError);
//         }
//       }
      
//       // 创建Vue组件并挂载
//       const app = createApp({
//         render() {
//           const content = node.content as unknown as GridContentConfig;
//           return h(GridItemComponent, {
//             title: content.title || '',
//             icon: content.icon || '',
//             showHeader: true,
//             refreshable: content.refreshable !== false,
//             configurable: content.configurable || false,
//             editable: content.editable || false,
//             closable: content.closable || false,
//             contentType: content.type,
//             showTitle: content.showTitle || true,
//             contentConfig: content.config || {},
//             htmlContent: content.config?.html,
//             content: content, // 直接传递完整的content对象，确保所有属性都能正确传递
//             onRefresh: () => {
//               console.log('网格项刷新:', node.id);
//               // 触发刷新逻辑，重新加载数据
//               if (node.id) {
//                 const itemId = (node as any)._originalData?.id || node.id;
//                 if (itemId) {
//                   props.store.getService().getDetail(itemId).then((data: any) => {
//                     // 更新网格项内容
//                     if (data && grid) {
//                       const updatedContent = (props.store as any).createDefaultContent 
//                         ? (props.store as any).createDefaultContent(data) 
//                         : node.content;
//                       grid.update(`#${node.id}`, { content: updatedContent as any });
//                       setTimeout(() => {
//                         renderGridItemContent(node);
//                       }, 50);
//                     }
//                   });
//                 }
//               }
//             },
//             onEdit: () => {
//               console.log('网格项编辑:', node.id);
//               // 触发编辑事件，可通过事件委托传递到父组件
//               const event = new CustomEvent('grid-item-edit', { 
//                 detail: { nodeId: node.id, node: node } 
//               });
//               document.dispatchEvent(event);
//             },
//             onConfig: () => {
//               console.log('网格项配置:', node.id);
//               // 触发配置事件
//               const event = new CustomEvent('grid-item-config', { 
//                 detail: { nodeId: node.id, node: node } 
//               });
//               document.dispatchEvent(event);
//             },
//             onClose: () => {
//               console.log('网格项关闭:', node.id);
//               // 触发关闭事件
//               const event = new CustomEvent('grid-item-close', { 
//                 detail: { nodeId: node.id, node: node } 
//               });
//               document.dispatchEvent(event);
//             }
//           });
//         }
//       });

//       // 提供store给应用
//       app.provide('gridPageStore', props.store);
      
//       // 导入 Element Plus 组件
//       import('element-plus').then(ElementPlus => {
//         // 注册 Element Plus
//         app.use(ElementPlus.default);
        
//         // 导入并注册图标
//         import('@element-plus/icons-vue').then(IconsModule => {
//           // 注册所有图标组件
//           for (const [key, component] of Object.entries(IconsModule)) {
//             app.component(key, component as any);
//           }
          
//           // 保存应用实例到元素上，以便后续卸载
//           (contentEl as any)[appKey] = app;
          
//           // 挂载组件
//           app.mount(contentEl);
//         }).catch(iconError => {
//           console.error('导入Element Plus图标失败:', iconError);
//           contentEl.innerHTML = `<div class="error-content">加载图标组件失败</div>`;
//         });
//       }).catch(importError => {
//         console.error('导入Element Plus失败:', importError);
//         contentEl.innerHTML = `<div class="error-content">加载UI组件库失败</div>`;
//       });
//     } catch (error) {
//       console.error('渲染网格项内容失败:', error);
//       contentEl.innerHTML = `<div class="error-content">内容渲染失败</div>`;
//     }
//   }
// };

// /**
//  * 获取当前网格数据
//  */
// const getGridData = () => {
//   if (!grid) return [];
  
//   try {
//     // 确保获取所有网格项，包括位置和尺寸信息
//     const nodes = grid.engine.nodes || [];
//     const serializedData = nodes.map(node => {
//       // 提取必要的网格项数据
//       return {
//         id: node.id,
//         x: node.x,
//         y: node.y,
//         w: node.w,
//         h: node.h,
//         content: node.content,
//         // 其他可能需要的属性
//         minW: node.minW,
//         maxW: node.maxW,
//         minH: node.minH,
//         maxH: node.maxH,
//         locked: node.locked,
//         noResize: node.noResize,
//         noMove: node.noMove,
//         autoPosition: node.autoPosition
//       };
//     });
//     console.log('gridpage 获取网格数据:', serializedData);
//     return serializedData as unknown as GridStackNode[];
//   } catch (error) {
//     console.error('获取网格数据出错:', error);
//     // 如果上面的方法失败，回退到标准方法
//     return grid.save();
//   }
// };

// /**
//  * 添加网格项
//  * @param item 网格项数据
//  */
// const addGridItem = (item: GridStackNode) => {
//   if (!grid) return;
  
//   // 添加网格项
//   const node = grid.addWidget(item);
  
//   // 手动渲染内容
//   if (node && item.content) {
//     setTimeout(() => {
//       renderGridItemContent(item);
//     }, 50);
//   }
  
//   return node;
// };

// /**
//  * 移除网格项
//  * @param el 网格项元素或选择器
//  */
// const removeGridItem = (el: GridItemHTMLElement | string) => {
//   if (!grid) return;
//   grid.removeWidget(el);
// };

// /**
//  * 更新网格项
//  * @param el 网格项元素或ID
//  * @param options 更新选项
//  */
// const updateGridItem = (el: GridItemHTMLElement | string, options: GridStackNode) => {
//   if (!grid) return;
//   grid.update(el, options);
// };

// /**
//  * 保存当前布局
//  */
// const saveLayout = async () => {
//   if (!grid) return false;
  
//   const gridData = getGridData();
//   return await props.store.saveGridLayout(gridData as unknown as GridStackNode[]);
// };

// /**
//  * 销毁网格
//  */
// const destroyGrid = () => {
//   if (grid) {
//     try {
//       grid.destroy();
//     } catch (error) {
//       console.error('销毁网格失败:', error);
//     }
//     grid = null;
//   }
  
//   // 移除事件监听
//   document.removeEventListener('grid-item-edit', handleGridItemEdit);
//   document.removeEventListener('grid-item-config', handleGridItemConfig);
//   document.removeEventListener('grid-item-close', handleGridItemClose);
// };

// // 监听store配置变化，自动刷新数据
// watch(() => props.store.pageConfig?.serviceConfig, (newVal) => {
//   if (newVal && newVal.baseUrl) {
//     refreshData();
//   }
// }, { immediate: true });

// // 监听store.gridItems变化，自动渲染
// watch(() => props.store.gridItems, (newItems) => {
//   loadGridItems();
// }, { immediate: true });

// // 监听可拖拽和可调整大小属性
// watch(() => store.pageConfig?.serviceConfig?.draggable, (newVal) => {
//   if (!grid) return;
//   grid.enableMove(newVal);

// });

// watch(() => store.pageConfig?.serviceConfig?.resizable, (newVal) => {
//   if (!grid) return;
//   grid.enableResize(newVal);
  
// });

// // 挂载时初始化
// onMounted(async () => {
//   await props.store.loadGridData();
//   loadGridItems();
//   console.log('props.store.pageConfig', props.store.pageConfig, 'props.store.gridItems', props.store.gridItems);
//   console.log('挂载时初始化');
//   // 初始化服务和网格
//   initGrid();
  
//   // 确保即使已有数据也尝试更新一次
//   // 这样可以保证组件挂载后总是有最新数据
//   // fetchDataFromAPI();
// });

// // 组件销毁前清理
// onBeforeUnmount(() => {
//   destroyGrid();
// });

// // 修改暴露的方法，添加刷新数据的方法
// defineExpose({
//   store: props.store,
//   getGridData,
//   addGridItem,
//   removeGridItem,
//   updateGridItem,
//   grid: () => grid,
//   saveLayout,
//   refresh: refreshData,
//   reload: refreshData,
//   getService: () => props.store.getService()
// });
</script>

<style scoped>
.grid-page {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.grid-container {
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
  overflow-y: auto; /* 添加垂直方向滚动条 */
  overflow-x: hidden; /* 隐藏水平方向滚动条 */
}

.grid-stack {
  min-height: 100%;
}

:deep(.grid-stack-item-content) {
  background-color: var(--el-bg-color);
  border-radius: 4px;
  overflow: hidden;
  overflow-y: hidden !important;
  box-shadow: var(--el-box-shadow-light);
  transition: box-shadow 0.3s ease, background-color 0.3s ease;
}
:deep(.grid-stack-item-content:hover) {
  background-color: var(--el-color-primary-light-7);
  box-shadow: var(--el-box-shadow-lighter);
}

/* 拖动手柄样式强化，保证手柄可见且易于操作 */
:deep(.grid-item-drag-handle) {
  cursor: grab;
  display: flex;
  align-items: center;
  margin-right: 4px;
  user-select: none;
}
:deep(.grid-item-drag-handle:active) {
  cursor: grabbing;
}
</style>
