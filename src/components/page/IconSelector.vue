<template>
    <div class="icon-selector">
      <el-popover
        :width="600"
        trigger="click"
        placement="bottom"
        popper-class="icon-selector-popper"
        :show-arrow="false"
      >
        <template #reference>
              <ElButton>
                <el-icon v-if="modelValue" class="icon-display">
                  <component :is="modelValue"></component>
                </el-icon>
              </ElButton>
        </template>
  
        <div class="icon-selector-container">
          <div class="icon-selector-search">
            <el-input
              v-model="searchText"
              placeholder="搜索图标"
              clearable
              @input="filterIcons"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <div class="icon-list">
            <div
              v-for="icon in filteredIcons"
              :key="icon"
              class="icon-item"
              @click="selectIcon(icon)"
            >
              <el-icon>
                <component :is="icon"></component>
              </el-icon>
              <div class="icon-name">{{ icon }}</div>
            </div>
          </div>
        </div>
      </el-popover>
    </div>
  </template>
  
  <script setup lang="ts">
  /**
   * 图标选择器组件
   * 用于选择Element Plus图标库中的图标
   */
  import { ref, computed, onMounted } from 'vue'
  import { ElButton, ElIcon, ElInput, ElPopover } from 'element-plus'
  import * as ElementPlusIcons from '@element-plus/icons-vue'
  import { Search } from '@element-plus/icons-vue'
  
  defineProps({
    modelValue: {
      type: String,
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    }
  })
  
  const emit = defineEmits(['update:modelValue'])
  
  const searchText = ref('')
  const iconList = ref<string[]>([])
  
  onMounted(() => {
    // 获取所有Element Plus图标
    iconList.value = Object.keys(ElementPlusIcons).filter(
      (icon) => icon !== 'default'
    )
  })
  
  const filteredIcons = computed(() => {
    if (!searchText.value) return iconList.value
    return iconList.value.filter((icon) =>
      icon.toLowerCase().includes(searchText.value.toLowerCase())
    )
  })
  
  const filterIcons = () => {
    // 搜索图标实现
  }
  
  const selectIcon = (iconName: string) => {
    emit('update:modelValue', iconName)
    searchText.value = ''
  }
  </script>
  
  <style scoped>
  .icon-selector {
    width: 100%;
  }
  
  .icon-selector-container {
    padding: 10px;
  }
  
  .icon-selector-search {
    margin-bottom: 15px;
  }
  
  .icon-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 12px;
    max-height: 320px;
    overflow-y: auto;
  }
  
  .icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .icon-item:hover {
    background-color: var(--el-color-primary-light-9);
  }
  
  .icon-item .el-icon {
    font-size: 24px;
    margin-bottom: 5px;
  }
  
  .icon-name {
    font-size: 12px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
  }
  
  .icon-display {
    font-size: 16px;
  }
  </style>
  
  <style>
  /* 全局样式，确保 popover 宽度适配 */
  .icon-selector-popper {
    max-width: 90vw !important;
    min-width: 320px !important;
  }
  
  .icon-selector-popper .el-popover__title {
    word-break: break-all;
  }
  </style>