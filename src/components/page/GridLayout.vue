<!--
  @description GridLayout.vue 页面布局示例，采用 grid-layout-plus 作为布局组件。
  <AUTHOR>
  @date 2025-04-12
  @update 2025-04-22 适配新版GridLayoutStore类/工厂函数
  @update 2025-04-25 修改为通过props接收store实例，实现单例模式
  @update 2025-04-26 添加step功能，根据step过滤网格项的显示
  @update 2025-04-26 添加gridComponentStore单例，与子组件共享
  @note 支持通过结构数据渲染自定义业务组件，结构数据由store统一管理。
  @note 事件 item-click、item-delete、items-updated 通过 $emit 透传给父组件，由父组件监听处理。
  文件：GridLayout.vue
  说明：本组件用于实现可拖拽、可缩放的网格布局，并监控组件的拖动和缩放等事件，实时记录事件日志。
-->
<!--
 * GridLayout.vue
 * 网格布局组件，支持动态添加、删除网格项
 * <AUTHOR>
 * @date 2025-04-18
 * @description 提供网格项的增删、拖拽、布局等功能，并通过事件与父组件通信
-->
<!--
  GridLayout.vue
  该文件为网格布局组件，负责布局渲染及相关操作。此处暴露 handleTest 方法供父组件调用。
-->
<template>
  <div class="grid-layout-demo">
    <!-- 渲染页面结构 -->
    <GridLayout
      v-if="store.structureData.config_content.serviceConfig"
      :layout="layout"
      :col-num="store.structureData.config_content.serviceConfig.gridOptions?.column || 12"
      :row-height="store.structureData.config_content.serviceConfig.gridOptions?.cellHeight || 50  "
      :margin="typeof store.structureData.config_content.serviceConfig.gridOptions?.margin === 'number' ? [store.structureData.config_content.serviceConfig.gridOptions.margin, store.structureData.config_content.serviceConfig.gridOptions.margin] : [10, 10]"
      :is-draggable="true"
      :is-resizable="true"
      @layout-change="onLayoutChange"
      @resize="onResize"
      @drag="onDrag"
      @layout-before-mount="layoutBeforeMountEvent"
      @layout-mounted="layoutMountedEvent"
      @layout-ready="layoutReadyEvent"
      @layout-updated="layoutUpdatedEvent"
    >
      <GridItem
        v-for="item in layout"
        :key="item.i"
        v-bind="item"
        :id="item.i"
        @resize="resizeEvent"
        @move="moveEvent"
        @resized="resizedEvent"
        @container-resized="containerResizedEvent"
        @moved="movedEvent"
        @item-click="onItemClick(item)"
      >
        <!-- 动态渲染业务组件，支持多组件注册及props透传 -->
        <component :is="components[item.component as keyof typeof components]" v-bind="item.props"
          @delete.native="onItemDelete(item.i)"
          @close="onItemClose(item.i)"
          @item-click="onItemClick(item)"
        />
      </GridItem>
    </GridLayout>
    <el-dialog
      v-if="store.structureData.config_content.formDialogConfig?.dialogType === 'dialog'"
      v-model="componentStore.uiState.formModalVisible"
      :width="(() => {
        const width = store.structureData.config_content.formDialogConfig?.width;
        if (!width) return '500px'; // 默认宽度
        if (typeof width === 'string') {
          // 如果已经包含单位（px或%），直接返回
          if (width.includes('px') || width.includes('%')) return width;
          // 如果是纯数字字符串，添加px单位
          if (/^\d+$/.test(width)) return `${width}px`;
          return width; // 其他字符串格式直接返回
        }
        // 如果是数字，添加px单位
        if (typeof width === 'number') return `${width}px`;
        return '500px'; // 其他情况使用默认值
      })()"
      :title="componentStore.formDialogTitle.value || '查看'"
    >
      <PlusForm
        :model-value="componentStore.currentRecord.value"
        :columns="store.structureData.config_content.formDialogConfig?.columns || []"
        :rules="processRules(store.structureData.config_content.formDialogConfig?.rules || {})"
        :labelWidth="store.structureData.config_content.formDialogConfig?.labelWidth || 180"
        @submit="handleSubmit"
        @cancel="handleDialogCancel"
      >
      <!-- 透传表单相关插槽 -->
      <template
        v-for="name in Object.keys($slots)"
        :key="name"
        #[name]="slotData"
        v-if="name.startsWith('table-')"
      >
        <slot :name="name" v-bind="slotData || {}" />
      </template>

      <!-- 自定义上传组件处理 -->
      <template
        v-for="column in uploadColumns"
        :key="column.prop"
        #[`plus-field-${column.prop}`]="slotProps"
      >
        <!-- 显示当前文件 -->
        <div v-if="getFileValue(slotProps, column)" class="upload-current-file">
          <div v-if="isImageUrl(getFileValue(slotProps, column))" class="image-preview">
            <el-image
              :src="getFileValue(slotProps, column)"
              :preview-src-list="[getFileValue(slotProps, column)]"
              fit="contain"
              class="preview-image"
              style="max-width: 50%; max-height: 50%;"
            />
            <!-- <div class="preview-url">{{ getFileValue(slotProps, column) }}</div> -->
          </div>
          <div v-else class="file-preview">
            <el-icon><Document /></el-icon>
            <a :href="getFileValue(slotProps, column)" target="_blank" class="file-link">
              {{ getFileNameFromUrl(getFileValue(slotProps, column)) }}
            </a>
            <div class="preview-url">{{ getFileValue(slotProps, column) }}</div>
          </div>
        </div>
        
        <FileUploader
          ref="fileUploaderRef"
          :action="column.uploadProps?.action || '/v1/admin/upload'"
          :headers="column.uploadProps?.headers"
          :multiple="column.uploadProps?.multiple"
          :accept="column.uploadProps?.accept"
          :file-limit="column.uploadProps?.fileLimit || 1"
          :size-limit="column.uploadProps?.sizeLimit || 5120"
          :file-usage="column.uploadProps?.fileUsage || column.prop"
          :upload-style="column.uploadProps?.style || 'default'"
          :show-file-list="column.uploadProps?.showFileList !== false"
          :initial-files="getInitialFiles(getFileValue(slotProps, column))"
          @success="(res: any) => {
            handleUploadSuccess(res, column, slotProps);
            // 直接修改formModel值，确保双向绑定生效
            if (componentStore.currentRecord.value && column.prop) {
              let fileUrl = '';
              if (res && res.data && res.data.file_url) fileUrl = res.data.file_url;
              else if (res && res.file_url) fileUrl = res.file_url;
              else if (res && res.url) fileUrl = res.url;
              else if (typeof res === 'string') fileUrl = res;
              
              if (fileUrl) {
                console.log('直接设置formModel值:', column.prop, fileUrl);
                componentStore.currentRecord.value[column.prop] = fileUrl;
              }
            }
          }"
          @error="(err: any) => handleUploadError(err, column)"
        >
          <template #tip>
            <slot :name="`upload-tip-${column.prop}`">
              <p>
                {{ column.uploadProps?.tip || "点击或拖拽文件到此区域上传" }}
              </p>
              <p v-if="column.uploadProps?.sizeLimit" class="upload-tip">
                文件大小不超过
                {{ formatFileSize(column.uploadProps.sizeLimit) }}
              </p>
            </slot>
          </template>
        </FileUploader>
      </template>
      </PlusForm>
    </el-dialog>
    <el-drawer
      v-else
      v-model="componentStore.uiState.formModalVisible"
      :title="componentStore.formDialogTitle.value || '编辑'"
      :direction="'rtl'"
      :size="(() => {
        const width = store.structureData.config_content.formDialogConfig?.width;
        if (!width) return '500px'; // 默认宽度
        if (typeof width === 'string') {
          // 如果已经包含单位（px或%），直接返回
          if (width.includes('px') || width.includes('%')) return width;
          // 如果是纯数字字符串，添加px单位
          if (/^\d+$/.test(width)) return `${width}px`;
          return width; // 其他字符串格式直接返回
        }
        // 如果是数字，添加px单位
        if (typeof width === 'number') return `${width}px`;
        return '500px'; // 其他情况使用默认值
      })()"
    >
      <PlusForm
        :model-value="componentStore.currentRecord.value"
        :columns="store.structureData.config_content.formDialogConfig?.columns || []"
        :rules="processRules(store.structureData.config_content.formDialogConfig?.rules || {})"
        :labelWidth="store.structureData.config_content.formDialogConfig?.labelWidth || 180"
        @submit="handleSubmit"
        @cancel="handleDialogCancel"
      >
      <!-- 透传表单相关插槽 -->
      <template
        v-for="name in Object.keys($slots)"
        :key="name"
        #[name]="slotData"
        v-if="name.startsWith('table-')"
      >
        <slot :name="name" v-bind="slotData || {}" />
      </template>

      <!-- 自定义上传组件处理 -->
      <template
        v-for="column in uploadColumns"
        :key="column.prop"
        #[`plus-field-${column.prop}`]="slotProps"
      >
        <!-- 显示当前文件 -->
        <div v-if="getFileValue(slotProps, column)" class="upload-current-file">
          <div v-if="isImageUrl(getFileValue(slotProps, column))" class="image-preview">
            <el-image
              :src="getFileValue(slotProps, column)"
              :preview-src-list="[getFileValue(slotProps, column)]"
              fit="contain"
              class="preview-image"
              style="max-width: 50%; max-height: 50%;"
            />
            <!-- <div class="preview-url">{{ getFileValue(slotProps, column) }}</div> -->
          </div>
          <div v-else class="file-preview">
            <el-icon><Document /></el-icon>
            <a :href="getFileValue(slotProps, column)" target="_blank" class="file-link">
              {{ getFileNameFromUrl(getFileValue(slotProps, column)) }}
            </a>
            <div class="preview-url">{{ getFileValue(slotProps, column) }}</div>
          </div>
        </div>
        
        <FileUploader
          ref="fileUploaderRef"
          :action="column.uploadProps?.action || '/v1/admin/upload'"
          :headers="column.uploadProps?.headers"
          :multiple="column.uploadProps?.multiple"
          :accept="column.uploadProps?.accept"
          :file-limit="column.uploadProps?.fileLimit || 1"
          :size-limit="column.uploadProps?.sizeLimit || 5120"
          :file-usage="column.uploadProps?.fileUsage || column.prop"
          :upload-style="column.uploadProps?.style || 'default'"
          :show-file-list="column.uploadProps?.showFileList !== false"
          :initial-files="getInitialFiles(getFileValue(slotProps, column))"
          @success="(res: any) => {
            handleUploadSuccess(res, column, slotProps);
            // 直接修改formModel值，确保双向绑定生效
            if (componentStore.currentRecord.value && column.prop) {
              let fileUrl = '';
              if (res && res.data && res.data.file_url) fileUrl = res.data.file_url;
              else if (res && res.file_url) fileUrl = res.file_url;
              else if (res && res.url) fileUrl = res.url;
              else if (typeof res === 'string') fileUrl = res;
              
              if (fileUrl) {
                console.log('直接设置formModel值:', column.prop, fileUrl);
                componentStore.currentRecord.value[column.prop] = fileUrl;
              }
            }
          }"
          @error="(err: any) => handleUploadError(err, column)"
        >
          <template #tip>
            <slot :name="`upload-tip-${column.prop}`">
              <p>
                {{ column.uploadProps?.tip || "点击或拖拽文件到此区域上传" }}
              </p>
              <p v-if="column.uploadProps?.sizeLimit" class="upload-tip">
                文件大小不超过
                {{ formatFileSize(column.uploadProps.sizeLimit) }}
              </p>
            </slot>
          </template>
        </FileUploader>
      </template>
      </PlusForm>
    </el-drawer>
    <el-dialog
      v-if="store.structureData.config_content.viewDialogConfig?.dialogType === 'dialog'"
      v-model="componentStore.uiState.viewModalVisible"
      :title="componentStore.formDialogTitle.value || '查看'"
    >
      <PlusDescriptions :column="store.structureData.config_content.viewDialogConfig?.column" :columns="store.structureData.config_content.viewDialogConfig?.columns" :data="componentStore.currentRecord.value" />
    </el-dialog>
    <el-drawer
      v-else
      v-model="componentStore.uiState.viewModalVisible"
      :title="componentStore.formDialogTitle.value || '查看'">
      <PlusDescriptions :column="store.structureData.config_content.viewDialogConfig?.column" :columns="store.structureData.config_content.viewDialogConfig?.columns" :data="componentStore.currentRecord.value" />
    </el-drawer>

    </div>
</template>
l.
<script setup lang="ts">
/**
 * @description 页面布局组件，适配新版GridLayoutStore类/工厂函数。
 * <AUTHOR>
 * @update 2025-04-22
 * @update 2025-04-25 修改为通过props接收store实例
 * @update 2025-04-26 添加step功能，根据step过滤网格项的显示
 * @update 2025-04-26 添加gridComponentStore单例，与子组件共享
 */
import { GridLayout, GridItem } from 'grid-layout-plus';
import { parseValidator } from '@/components/page/validator';
import { onMounted, ref, reactive, provide, watch, nextTick, computed } from 'vue';
import GridItemComponent from '@/components/base/grid/GridItemComponent.vue';
import { parseGridItemsFromBackend } from './gridLayoutStore';
import type { GridComponentType } from '@/components/base/grid/types';
import type { AdminGridInfoDTO, CustomPlusColumn } from '@/modules/admin/types';
// 导入gridComponentStore相关内容
import { createGridComponentStore } from './gridComponentStore';
//import type GridComponentStore from './gridComponentStore';
//import { config } from 'localforage';
//import service from '@/modules/admin/service';
//import { PlusDrawerForm } from 'plus-pro-components';
import { FileUploader } from "@/components/common";
import type { PlusColumn } from "plus-pro-components";
// 声明全局变量类型，解决window.$gridComponentStore的TypeScript报错
declare global {
  interface Window {
    $gridComponentStore: any;
  }
}
const dialogFormRef = ref();
const drawerFormRef = ref();

// 定义props，接收外部传入的store实例
const props = defineProps({
  store: {
    type: Object,
    required: true
  }
});

// 定义emit事件
const emit = defineEmits(['item-click', 'item-delete', 'items-updated']);

// 组件注册表
const components = {
  GridItemComponent
};

// 明确 layout item 的类型，确保与结构数据一致
interface LayoutItem {
  x: number;
  y: number;
  w: number;
  h: number;
  i: string | number;
  dragAllowFrom?: string; // 拖拽允许的元素选择器
  component?: GridComponentType;
  props?: Record<string, any>;
  step?: number | number[]; // 步骤属性，用于控制显示逻辑
}
//const key = ref(0);
//const showGrid = ref(true);
// 使用props传入的store实例
const store = props.store;

// 创建GridComponentStore单例
const componentStore = createGridComponentStore(store.getService(), store.serviceConfig.value);

// 连接两个store，确保配置同步
store.connectComponentStore(componentStore);

// 直接取属性，无需 storeToRefs
//const structureData = store.structureData;
const allDone = ref(false);

// 提供两个store实例给子组件
provide('gridLayoutStore', store);
provide('gridComponentStore', componentStore); // 提供组件store给子组件
// 提供页面服务给子组件，解决GridPlusTableComponent的注入问题
provide('pageService', store.service);
// 支持布局持久化（如本地存储）
const layout = ref<LayoutItem[]>([]);

// 事件日志
const eventLogs = reactive<string[]>([])
//const eventsDiv = ref<HTMLElement>()

// 监听store的step变化，更新布局
watch(
  () => store.step,
  (newStep) => {
    console.log('[GridLayout] step变化:', newStep);
    refreshLayout();
  }
);

// 日志区自动滚动
// watch(
//   () => eventLogs.length,
//   () => {
//     requestAnimationFrame(() => {
//       if (eventsDiv.value) {
//         eventsDiv.value.scrollTop = eventsDiv.value.scrollHeight
//       }
//     })
//   }
// )

/**
 * @description 透传事件到父组件：item-click、item-delete、items-updated
 */

// 透传点击事件到父组件
function onItemClick(item: LayoutItem) {
  // @description 触发 item-click 事件，供父组件监听
  // @param item 当前被点击的网格项对象
  // @event item-click
  // @emit
  // 用于父组件监听：@item-click="editGridItem"
  // 2025-04-25 by cascade
  console.log('[GridLayout] item-click', item);
  emit('item-click', item);
}

/**
 * 处理关闭事件，通知父组件删除网格项（不直接操作layout，由父组件/Store处理数据和视图同步）
 * @param id 网格项唯一标识
 * @event item-delete
 * @description 只负责向父组件发出删除请求，实际数据移除由父组件/Store完成
 */
function onItemClose(id: LayoutItem['i']) {
  console.log('onItemClose', id);
  // 只发出删除事件，不直接修改layout
  emit('item-delete', id);
}

/**
 * 处理删除事件，通知父组件删除网格项（不直接操作layout，由父组件/Store处理数据和视图同步）
 * @param item 网格项对象
 * @event item-delete
 * @description 只负责向父组件发出删除请求，实际数据移除由父组件/Store完成
 */
function onItemDelete(item: LayoutItem['i']) {
  // @description 触发 item-delete 事件，供父组件监听
  // @param item 当前被删除的网格项对象
  // @event item-delete
  // @emit
  // 用于父组件监听：@item-delete="removeGridItem"
  // 2025-04-25 by cascade
  emit('item-delete', item);
}

// 透传 items-updated 事件到父组件（如布局变更、拖动、缩放等均可触发）
// function emitItemsUpdated() {
//   // @description 触发 items-updated 事件，供父组件监听
//   // @event items-updated
//   // @emit
//   // 用于父组件监听：@items-updated="store.updateGridItemsFromNodes"
//   // 2025-04-25 by cascade
//   emit('items-updated', layout);
// }

/**
 * 拖动中事件
 * @param i 元素id
 * @param newX 新的x坐标
 * @param newY 新的y坐标
 */
function moveEvent(i: string, newX: number, newY: number) {
  const eventLog = `移动中: i = ${i}, x: ${newX}, y: ${newY}`
  eventLogs.push(eventLog)
  // console.log(eventLog)
}

/**
 * 拖动结束事件
 * @param i 元素id
 * @param newX 新的x坐标
 * @param newY 新的y坐标
 */
function movedEvent(i: string, newX: number, newY: number) {
  const eventLog = `移动结束: i = ${i}, x: ${newX}, y: ${newY}`
  eventLogs.push(eventLog)
  console.log('movedEvent', i, newX, newY)
  // 仅将当前被拖动的 item 传递给父组件
  const item = layout.value.find(item => item.i === i)
  if (item) {
    emit('items-updated', layout.value)
  }
}

/**
 * 缩放中事件
 * @param i 元素id
 * @param newH 新高度
 * @param newW 新宽度
 * @param newHPx 新高度(px)
 * @param newWPx 新宽度(px)
 */
function resizeEvent(i: string, newH: number, newW: number, newHPx: number, newWPx: number) {
  const eventLog = `缩放中: i = ${i}, h: ${newH}, w: ${newW}, h(px): ${newHPx}, w(px): ${newWPx}`
  eventLogs.push(eventLog)
  
}

/**
 * 缩放结束事件
 * @param i 元素id
 * @param newX 新x坐标
 * @param newY 新y坐标
 * @param newHPx 新高度(px)
 * @param newWPx 新宽度(px)
 */
function resizedEvent(i: string, newX: number, newY: number, newHPx: number, newWPx: number) {
  const eventLog = `缩放结束: i = ${i}, x: ${newX}, y: ${newY}, h(px): ${newHPx}, w(px): ${newWPx}`
  eventLogs.push(eventLog)
  console.log('resizedEvent', i, newX, newY, newHPx, newWPx)
  // 仅将当前被缩放的 item 传递给父组件
  const item = layout.value.find(item => item.i === i)
  if (item) {
    emit('items-updated', layout.value)
  }
}

// 计算上传字段列
const uploadColumns = computed(() => {
  if (!store.structureData.config_content.formDialogConfig?.columns) {
    return [];
  }

  return store.structureData.config_content.formDialogConfig?.columns?.filter((column: any) => {
    return column.valueType === "upload";
  });
});

/**
 * 获取文件值，尝试多个来源
 */
 function getFileValue(slotProps: any, column: CustomPlusColumn): string {
  console.log("getFileValue被调用, slotProps:", slotProps, "column:", column);
  
  // 尝试从多个可能的来源获取文件值
  const sources = [
    // 从slotProps.value获取
    slotProps?.value,
    // 从slotProps.model获取
    slotProps?.model?.[column.prop],
    // 从表单模型获取
    componentStore.currentRecord.value?.[column.prop]
  ];
  
  // 返回第一个非空值
  for (const source of sources) {
    if (source) {
      console.log("找到文件值:", source);
      return source;
    }
  }
  
  console.log("没有找到任何文件值");
  return '';
};

// 方法 - 判断是否为图片URL
const isImageUrl = (url: string) => {
  if (!url) {
    return false;
  }

  // 检查URL是否以图片扩展名结尾
  const extensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"];
  const lowercasedUrl = url.toLowerCase();

  // 检查扩展名
  return extensions.some((ext) => lowercasedUrl.endsWith(ext));
};

// 从URL中提取文件名
const getFileNameFromUrl = (url: string): string => {
  if (!url) return '';
  
  try {
    // 尝试从URL中提取文件名
    const pathParts = new URL(url).pathname.split('/');
    const fileName = pathParts[pathParts.length - 1];
    
    // 解码URL编码的文件名
    return decodeURIComponent(fileName);
  } catch (error) {
    // 如果URL解析失败，直接返回最后一部分
    const parts = url.split('/');
    return parts[parts.length - 1];
  }
};

// 处理初始文件
const getInitialFiles = (value: string | undefined) => {
  console.log("getInitialFiles被调用，value=", value);
  
  if (!value) {
    console.log("没有初始文件值");
    return [];
  }
  
  try {
    // 创建一个包含当前文件URL的初始文件数组
    const initialFile = {
      id: 'initial-file',
      name: getFileNameFromUrl(value),
      size: 0,
      status: 'success' as const,
      raw: new File([], getFileNameFromUrl(value)),
      url: value,
      mime_type: isImageUrl(value) ? 'image/jpeg' : 'application/octet-stream'
    };
    
    console.log("创建的初始文件对象:", initialFile);
    return [initialFile];
  } catch (error) {
    console.error("创建初始文件对象失败:", error);
    return [];
  }
};

// 方法 - 处理上传成功
const handleUploadSuccess = (
  response: any,
  column: PlusColumn,
  slotProps: any
) => {
  console.log("上传成功:", response);
  console.log("slotProps完整内容:", slotProps);
  console.log("slotProps.model:", slotProps.model);
  console.log("column信息:", column);

  // 提取文件URL
  let fileUrl = "";
  if (response && response.data && response.data.file_url) {
    fileUrl = response.data.file_url;
  } else if (response && response.file_url) {
    fileUrl = response.file_url;
  } else if (response && response.url) {
    fileUrl = response.url;
  } else if (typeof response === "string") {
    fileUrl = response;
  }

  if (fileUrl) {
    console.log("获取到的文件URL:", fileUrl);
    
    // 更新表单字段值 - 尝试多种方式
    // 方法1: 使用onChange如果存在
    if (slotProps && typeof slotProps.onChange === "function") {
      console.log("使用onChange更新值");
      slotProps.onChange(fileUrl);
    } 
    
    // 方法2: 直接更新model
    if (slotProps && slotProps.model && column.prop) {
      console.log("尝试更新model:", column.prop);
      slotProps.model[column.prop] = fileUrl;
    }
    
    // 方法3: 更新formModel
    if (componentStore.currentRecord.value && column.prop) {
      console.log("更新formModel:", column.prop);
      componentStore.currentRecord.value[column.prop] = fileUrl;
    }

    // 强制更新DOM
    nextTick(() => {
      console.log("文件上传成功后的值:", {
        "formModel": componentStore.currentRecord.value ? componentStore.currentRecord.value[column.prop] : undefined,
        "slotProps.model": slotProps && slotProps.model ? slotProps.model[column.prop] : undefined,
        "slotProps.value": slotProps ? slotProps.value : undefined
      });
    });

    // 触发事件
    //emit("upload-success", fileUrl, column.prop, response);
  }
};

// 方法 - 处理上传错误
const handleUploadError = (error: any, column: PlusColumn) => {
  console.error("上传失败:", error, column.prop);

  // 触发事件
  //emit("upload-error", error, column.prop);

  // 显示错误消息
  //ElMessage.error("上传失败");
};

// 方法 - 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + " B";
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + " KB";
  } else if (size < 1024 * 1024 * 1024) {
    return (size / 1024 / 1024).toFixed(2) + " MB";
  } else {
    return (size / 1024 / 1024 / 1024).toFixed(2) + " GB";
  }
};

/**
 * 容器缩放事件
 */
function containerResizedEvent() {
  const eventLog = "容器缩放"
  eventLogs.push(eventLog)
  // 透传事件给父组件
  //emitItemsUpdated();
}

/**
 * 布局挂载前事件
 * @param newLayout 新布局
 */
function layoutBeforeMountEvent(newLayout: LayoutItem[]) {
  const eventLog = `布局挂载前: ${newLayout.length} 项`
  eventLogs.push(eventLog)
}

/**
 * 布局挂载后事件
 * @param newLayout 新布局
 */
function layoutMountedEvent(newLayout: LayoutItem[]) {
  const eventLog = `布局挂载后: ${newLayout.length} 项`
  eventLogs.push(eventLog)
}

/**
 * 布局就绪事件
 * @param newLayout 新布局
 */
function layoutReadyEvent(newLayout: LayoutItem[]) {
  const eventLog = `布局就绪: ${newLayout.length} 项`
  eventLogs.push(eventLog)
  // 透传事件给父组件
  //emitItemsUpdated();
}

/**
 * 布局更新事件
 * @param newLayout 新布局
 */
 function layoutUpdatedEvent(newLayout: any) {
  eventLogs.push('Updated layout')
  console.info('Updated layout: ', newLayout)
  // 此处不再 emit('items-updated', newLayout)
}

// 初始化布局
function initLayout() {
  console.log('[initLayout] 开始初始化布局');
  if(!store || !store.structureData) {
    console.error('GridLayoutStore未传入或为undefined');
    return;
  }
  
  console.log('[initLayout] 检查structureData:', 
    '有grid_items?', !!store.structureData.grid_items, 
    '长度:', store.structureData.grid_items?.length || 0);
  
  if(!store.structureData.grid_items || store.structureData.grid_items.length === 0) {
    console.log('[initLayout] grid_items为空，设置空布局');
    layout.value = [];
    return;
  }
  
  console.log('[initLayout] 开始生成前端布局结构...', store.structureData.config_content);
  // 从后端数据生成前端可用布局项
  const parsedItems = parseGridItemsFromBackend(store.structureData.grid_items || []);
  
  // 根据当前step过滤显示的项目
  const filteredItems = filterItemsByStep(parsedItems);
  
  console.log('[initLayout] 过滤后的布局项:', filteredItems.length, '总布局项:', parsedItems.length);
  
  // 更新layout
  layout.value = filteredItems;
  
  // 更新store中对应的layout（如有需要）
  if(store.structureData && 'layout' in store.structureData) {
    store.structureData.layout = parsedItems;
  }
  
  // 强制刷新
  nextTick(() => {
    allDone.value = true;
    console.log('[initLayout] nextTick后布局已更新, layout:', layout.value);
  });
}

// 监听 structureData 变化自动更新布局
watch(
  () => store.structureData.grid_items,
  async (val: AdminGridInfoDTO[]) => {
    if(val.length == 0){
      console.log('[watch] grid_items 变化，数组为空');
      layout.value = [];
      return;
    }
    console.log('[watch] grid_items 变化:', val.length, '当前 allDone:', allDone.value);
    
    // allDone 是否已完成初始化的标志
    if (allDone.value) {
      console.log('[watch] 开始生成前端布局结构 (watch)...');
      // 从后端数据生成前端可用布局项
      const parsedItems = parseGridItemsFromBackend(val);
      
      // 根据当前step过滤显示的项目
      const filteredItems = filterItemsByStep(parsedItems);
      
      console.log('[watch] 过滤后的布局项:', filteredItems.length, '总布局项:', parsedItems.length);
      
      // 更新 layout
      layout.value = filteredItems;
    }
  },
  { immediate: true }
);

/**
 * 监听serviceConfig变化，同时对比新旧grid_items内容是否一致
 */
 watch(
  () => store.structureData.config_content.serviceConfig,
  async (newConfig, oldConfig) => {
    console.log('[watch] serviceConfig 变化:', newConfig, oldConfig);
    const newGridItemsStr = JSON.stringify(newConfig);
    const oldGridItemsStr = JSON.stringify(oldConfig);
    if(newGridItemsStr !== oldGridItemsStr) {
      await store.updateServiceConfig(newConfig);
    }
  },
  { deep: true, immediate: true }
);

// 根据当前step值过滤布局项
function filterItemsByStep(items: LayoutItem[]): LayoutItem[] {
  try {
    // 兼容 step 为单值或数组
    const rawStep = store.step;
    // 处理 Proxy(Array) 的情况，转为普通数组
    const currentSteps = Array.isArray(rawStep) ? Array.from(rawStep) : [rawStep ?? 0];
    //console.log('[GridLayout] 过滤step:', currentSteps, '(原始值:', rawStep, ')', '项目总数:', items.length);

    if (!items || !Array.isArray(items)) {
      console.error('[GridLayout] filterItemsByStep: items不是有效数组');
      return [];
    }

    return items.filter((item: LayoutItem) => {
      if (!item) return false;
      // item.step 可能为数组、数字、undefined

      let itemStep: number[] = [];
      if (Array.isArray(item.step)) {
        itemStep = Array.from(item.step);
      } else if (item.step !== undefined) {
        itemStep = [item.step];
      } else if (item.props && item.props.step !== undefined && item.props.step !== null) {
        // 兼容props.step
        if (Array.isArray(item.props.step)) {
          itemStep = Array.from(item.props.step);
        } else {
          itemStep = [item.props.step];
        }
      } else {
        itemStep = [0];
      }
      // 日志优化，避免打印Proxy
      //console.log('[GridLayout] 项目step值:', itemStep, '项目:', item.i);
      // 判断是否有交集
      const hasIntersection = itemStep.some(stepVal => currentSteps.includes(stepVal));
      // 规则：step为0时只显示step为0的项，step非0时显示step为0的和与当前step有交集的项
      if (currentSteps.length === 1 && currentSteps[0] === 0) {
        return itemStep.includes(0);
      } else {
        return itemStep.includes(0) || hasIntersection;
      }
    });
  } catch (error) {
    console.error('[GridLayout] filterItemsByStep出错:', error);
    return []; // 出错时返回空数组
  }
}

// 刷新布局
function refreshLayout() {
  console.log('[refreshLayout] 开始, store.structureData:', store.structureData);
  
  if (!store || !store.structureData || !store.structureData.grid_items || !store.structureData.config_content) {
    console.error('GridLayoutStore未传入或为undefined');
    return;
  }
  
  console.log('[refreshLayout] 开始生成前端布局结构...');
  // 重新从后端数据生成前端可用布局项
  const parsedItems = parseGridItemsFromBackend(store.structureData.grid_items || []);
  
  // 根据当前step过滤显示的项目
  const filteredItems = filterItemsByStep(parsedItems);
  
  console.log('[refreshLayout] 过滤后的布局项:', filteredItems.length, '总布局项:', parsedItems.length);
  // 如果没有任何布局项，新增一个提示布局项
  if (filteredItems.length === 0) {
    filteredItems.push({
      x: 0,
      y: 0,
      w: 6,
      h: 2,
      i: 'no-layout',
      dragAllowFrom: '.grid-item-drag-btn',
      component: 'GridItemComponent',
      props: { title: '错误', icon: 'info', htmlContent: '<div style="text-align: center;">没有找到layout</div>', type:'custom'},
    });
  }
  // 更新layout
  layout.value = filteredItems;
  
  // 强制刷新
  nextTick(() => {
    allDone.value = true;
    console.log('[refreshLayout] nextTick后布局已更新, layout:', layout.value);
  });
}

// 布局变更事件，支持持久化
function onLayoutChange(newLayout: LayoutItem[]) {
  // 更新本地布局数据
  layout.value = newLayout;
  console.log('onLayoutChange', newLayout);
  
  // 发出更新事件
  emit('items-updated', newLayout);
  
  // 如果有前端路径，保存到缓存
  if (store.structureData?.frontend_path) {
    // 保存到缓存
    store.saveLayoutToCache(store.structureData.frontend_path);
    console.log(`布局已自动保存到缓存: ${store.structureData.frontend_path}`);
  }
}

// 其它事件（可扩展）
function onResize(_args: any[]) {
  //console.log('onResize', ...args);
}
function onDrag(_args: any[]) {
  //console.log('onDrag', ...args);
}

// watch(() => componentStore.uiState.viewModalVisible, (newVal) => {
//   console.log('[GridLayout] viewmodalVisible变化:', newVal, store.structureData.config_content.viewDialogConfig);
// })
watch(() => componentStore.currentRecord.value, (newVal) => {
  console.log('[GridLayout] currentRecord变化:', newVal, store.structureData.config_content.formDialogConfig);
  // 如果newVal为null，则初始化它：具体方法为根据store.structureData.config_content.formDialogConfig.columns生成，如果columns中valueType为number，则初始化为0，如果valueType为string，则初始化为空字符串
  if(newVal === null) {
    const columns = store.structureData.config_content.formDialogConfig.columns;
    const initObj: any = {};
    columns.forEach((col: any) => {
      if(col.valueType === 'number') {
        initObj[col.prop] = 0;
      } else if(col.valueType === 'switch') {
        initObj[col.prop] = false;
      } else {
        initObj[col.prop] = '';
      }
    });
    componentStore.currentRecord.value = initObj;                
  }
})

// 监听抽屉表单可见性变化，处理表单校验信息清除
watch(() => componentStore.uiState.formModalVisible, (visible) => {
  // 当表单关闭时，如果是抽屉表单，清除校验信息
  if (!visible && store.structureData.config_content.formDialogConfig?.dialogType !== 'dialog') {
    console.log('[GridLayout] 抽屉关闭，清除校验信息');
    // 延时确保DOM已更新
    nextTick(() => {
      if (drawerFormRef.value) {
        drawerFormRef.value.formInstance.clearValidate();
      }
    });
  }
})

// 页面挂载后获取对象数据
onMounted(async () => {
  initLayout();
  //store.loading = ref(false);
  
  // 提供公共组件store的接口函数给组件
  window.$gridComponentStore = componentStore;
  console.log('[GridLayout] componentStore已初始化并注入全局：', componentStore);
});

// 向外部暴露componentStore的实例和refreshLayout方法，方便父组件或其他组件访问
defineExpose({
  layoutStore: store,
  componentStore,
  refreshLayout
});

/**
 * refreshLayout
 * 父组件可通过ref调用此方法，重新生成layout并更新视图
 * 会根据当前step值筛选显示的网格项
 */
// defineExpose({ refreshLayout }) // 已合并到上方

function handleDialogCancel() {
  console.log('[GridLayout] handleDialogCancel');
  // 根据当前打开的表单类型，清除对应的校验信息
  if (store.structureData.config_content.formDialogConfig?.dialogType === 'dialog') {
    // 对话框表单
    dialogFormRef.value.formInstance?.clearValidate();
  } else {
    // 抽屉表单
    drawerFormRef.value.formInstance?.clearValidate();
  }
  componentStore.handleDialogCancel();
}

function handleSubmit() {
  console.log('[GridLayout] handleSubmit', componentStore.currentRecord.value);
  //dialogFormRef.value.formInstance?.clearValidate();
  componentStore.submitForm(componentStore.currentRecord.value);
}

/**
 * 处理表单校验规则，将字符串形式的校验器转换为实际的校验函数
 * @param rules 原始校验规则配置
 * @returns 处理后的校验规则配置
 */
function processRules(rules: Record<string, any[]>) {
  const processedRules: Record<string, any[]> = {};
  
  // 遍历所有字段的规则
  Object.keys(rules).forEach(field => {
    // 确保当前字段的规则是数组
    if (Array.isArray(rules[field])) {
      // 处理每个规则项
      processedRules[field] = rules[field].map(rule => {
        // 如果规则中包含validator且是字符串
        if (rule.validator && typeof rule.validator === 'string') {
          const newRule = { ...rule };
          
          // 处理"validators.xxx"形式的字符串
          if (rule.validator.startsWith('validators.')) {
            const validatorName = rule.validator.replace('validators.', '');
            const params = { 
              threshold: rule.threshold, 
              message: rule.message 
            };
            
            // 转换字符串校验器为实际函数
            newRule.validator = parseValidator(validatorName, params);
            
            // 已使用params中的threshold，从规则中删除
            if (rule.threshold !== undefined) {
              delete newRule.threshold;
            }
          } else {
            // 直接尝试解析其他形式的字符串校验器
            newRule.validator = parseValidator(rule.validator, {
              threshold: rule.threshold,
              message: rule.message
            });
            
            // 已使用params中的threshold，从规则中删除
            if (rule.threshold !== undefined) {
              delete newRule.threshold;
            }
          }
          
          return newRule;
        }
        
        // 不需要处理的规则直接返回
        return rule;
      });
    } else {
      // 非数组类型的规则直接原样保留
      processedRules[field] = rules[field];
    }
  });
  
  return processedRules;
}
</script>

<style scoped>
.grid-layout-demo {
  width: 100%;
  min-height: 600px;
  background: #f7f8fa;
  padding: 16px;
  box-sizing: border-box;
}

.event-logs {
  height: 100px;
  padding: 10px;
  margin-top: 10px;
  overflow-y: scroll;
  background-color: #ddd;
  border: 1px solid black;
}
</style>
