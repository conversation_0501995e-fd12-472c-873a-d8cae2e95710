/**
 * @description gridComponentStore - 业务组件数据和配置管理
 * <AUTHOR>
 * @date 2025-04-25
 * @note 专注于管理PlusProComponents组件的配置和数据共享，支持表格、搜索等组件间的数据关联
 */
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { 
  UiPageConfig, 
  TableValueType, 
  FormItemValueType, 
  //ComponentType, 
  OptionsRow, 
  PlusColumn, 
  //SearchField, 
  Pagination, 
  PageInfo, 
  PageInfoMap, 
  ComponentConfig, 
  TableConfig, 
  //SearchConfig, 
  //FormConfig, 
  ServiceConfig, 
  UIState, 
  SorterInfo, 
  //FieldInfo,
  //DTOInfoDTO,
  PageData
} from './types';
import GridLayoutService from './gridLayoutService';
import cloneDeep from 'lodash/cloneDeep';

/**
 * 网格组件Store类
 * 负责管理业务组件数据、配置和状态
 */
export class GridComponentStore {
  /** 加载状态 */
  public loading = ref(false);
  /** 错误信息 */
  public error = ref<Error | null>(null);
  /** 表单对话框标题 */
  public formDialogTitle = ref('abc');
  /** 编辑表单对话框标题 */
  public editFormDialogTitle = ref('编辑');
  /** 查看表单对话框标题 */
  public viewFormDialogTitle = ref('查看');
  /** 创建表单对话框标题 */
  public createFormDialogTitle = ref('创建');
  /** 表格列定义映射 - 根据组件ID存储不同的列配置 */
  public columnsMap = reactive<Record<string, PlusColumn[]>>({});
  /** 默认表格列定义（向后兼容） */
  public columns = ref<PlusColumn[]>([]);
  /** 组件配置映射 */
  public componentConfigs = reactive<Record<string, ComponentConfig>>({});
  /** 查询参数 */
  public queryParams = reactive<Record<string, any>>({});
  /** 分页信息 */
  public pagination = reactive<Pagination>({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100']
  });
  /** 默认分页信息 */
  public defaultPageInfo: PageInfo = {
    page: 1,
    pageSize: 10
  };
  /** 默认分页大小选项 */
  public defaultPageSizeList = [10, 20, 30, 50, 100];
  /** 分页参数映射 */
  public pageInfoMap: PageInfoMap = {
    current: 'page',
    page: 'page',
    pageSize: 'pageSize',
    pageSizeList: 'pageSizeList'
  };
  /** 表格数据版本 用以刷新数据表格 */
  public tableDataVersion = ref(0);
  /** 表格数据 */
  public tableData = ref<any[]>([]);
  /** 表单数据 */
  public formData = ref<any>({});
  /** 对话框可见性 */
  public dialogVisible = ref(false);
  /** 当前记录 */
  public currentRecord = ref<any>({});
  /** 当前操作的原始记录（用于取消编辑时恢复） */
  public originalRecord = ref<any>(null);
  /** 选中行数据 */
  public selectedRows = ref<any[]>([]);
  /** 选中行键值 */
  public selectedRowKeys = ref<(string | number)[]>([]);
  /** UI 状态 */
  public uiState = reactive<UIState>({
    formModalVisible: false,
    viewModalVisible:false,
    modalType: '',
    confirmLoading: false,
    formMode: '',
    searchCollapsed: false
  });
  /** 排序信息 */
  public sorter = ref<SorterInfo | null>(null);
  /** 筛选参数 */
  public filters = reactive<Record<string, any>>({});
  /** 布局服务 */
  private service: GridLayoutService;
  /** 服务配置 */
  public serviceConfig = ref<ServiceConfig>({
    baseUrl: '/api'
  });

  /**
   * 构造函数
   * @param service GridLayoutService实例
   * @param serviceConfig ServiceConfig配置
   */
  constructor(service?: GridLayoutService, serviceConfig?: ServiceConfig) {
    // 服务初始化
    if (service) {
      this.service = service;
    } else if (serviceConfig) {
      this.service = new GridLayoutService(serviceConfig);
    } else {
      this.service = new GridLayoutService({ baseUrl: '/api' });
    }
    
    if (serviceConfig) {
      this.serviceConfig.value = { ...serviceConfig };
      
      // 如果有分页映射配置，则使用它
      if (serviceConfig.pageInfoMap) {
        this.pageInfoMap = serviceConfig.pageInfoMap;
      }
    }
    
    // 监听查询参数变化，自动刷新数据
    watch(this.queryParams, () => {
      // 重置到第一页
      this.pagination.current = 1;
      this.refresh();
    }, { deep: true });
  }

  /**
   * 请求数据方法（适配PlusProComponents）
   * @param params 请求参数
   * @returns 格式化后的数据
   */
  request = async (params: any) => {
    try {
      // 处理分页参数
      const { current, pageSize, ...rest } = params;
      // 更新查询参数（不含分页信息）
      this.updateSearchParams(rest);
      // 更新分页信息
      this.setPagination({
        current,
        pageSize
      });
      // 构建查询参数
      const requestParams = {
        ...rest,
        [this.pageInfoMap.page || this.pageInfoMap.current]: current,
        [this.pageInfoMap.pageSize]: pageSize
      };
      // 发起请求，result 已为 data 内容
      const result = await this.service.fetchList(requestParams);
      // 格式化返回结果
      let list = [];
      let total = 0;
      if (Array.isArray(result)) {
        list = result;
      } else if (Array.isArray(result.list)) {
        list = result.list;
      } else if (Array.isArray(result.data)) {
        list = result.data;
      } else {
        list = [];
      }
      if (result.total !== undefined) {
        total = result.total;
      } else if (result.data && result.data.total !== undefined) {
        total = result.data.total;
      }
      // 保存数据到 store
      this.tableData.value = list;
      this.pagination.total = total;
      // 返回符合 PlusPage 要求的格式
      return {
        data: list,
        success: true,
        total
      };
    } catch (error) {
      console.error('[GridComponentStore] 请求数据失败:', error);
      this.handleRequestError(error);
      return {
        data: [],
        success: false,
        total: 0
      };
    }
  };

  /**
   * 处理搜索参数前的转换
   * @param params 原始参数
   * @returns 处理后的参数
   */
  beforeSearchSubmit = (params: Record<string, any>) => {
    // 在这里可以对参数进行转换，比如日期转换、参数名映射等
    const processedParams: Record<string, any> = { ...params };
    
    // 例如对日期范围进行处理
    if (params.dateRange && Array.isArray(params.dateRange)) {
      const [startDate, endDate] = params.dateRange;
      processedParams.startDate = startDate;
      processedParams.endDate = endDate;
      delete processedParams.dateRange;
    }
    
    console.log('[GridComponentStore] 处理搜索参数:', processedParams);
    return processedParams;
  };

  /**
   * 处理返回数据
   * @param data 原始数据
   * @returns 处理后的数据
   */
  postData = (data: any[]) => {
    // 处理返回的数据，例如格式化日期、状态等
    return data.map(item => ({
      ...item,
      // 可以在这里添加字段处理逻辑
    }));
  };

  /**
   * 设置搜索字段值
   * @param values 字段值
   */
  setSearchFieldsValue = (values: Record<string, any>) => {
    this.updateSearchParams(values);
  };

  /**
   * 获取搜索字段值
   * @returns 搜索参数
   */
  getSearchFieldsValue = () => {
    return { ...this.queryParams };
  };

  /**
   * 清除搜索字段值
   */
  clearSearchFieldsValue = () => {
    this.resetSearchParams();
  };

  /**
   * 处理分页变化
   * @param page 页码
   * @param pageSize 每页条数
   */
  handlePaginationChange = (page: number, pageSize: number) => {
    this.setPagination({
      current: page,
      pageSize
    });
  };

  /**
   * 处理搜索
   * @param values 搜索值
   */
  handleSearch = (values: Record<string, any>) => {
    this.updateSearchParams(values);
    this.setPagination({ current: 1 });
  };

  /**
   * 处理重置
   */
  handleReset = () => {
    this.resetSearchParams();
  };

  /**
   * 处理表格变化
   * @param pagination 分页信息
   * @param filters 筛选条件
   * @param sorter 排序条件
   */
  handleTableChange = (pagination: any, filters: Record<string, any>, sorter: any) => {
    // 更新分页
    if (pagination) {
      this.setPagination({
        current: pagination.current,
        pageSize: pagination.pageSize
      });
    }
    
    // 更新筛选
    if (filters) {
      this.filters = filters;
    }
    
    // 更新排序
    if (sorter && sorter.field) {
      this.sorter.value = {
        field: sorter.field,
        order: sorter.order
      };
    } else {
      this.sorter.value = null;
    }
    
    this.refresh();
  };

  /**
   * 处理请求错误
   * @param error 错误对象
   */
  handleRequestError = (error: any) => {
    this.error.value = error instanceof Error ? error : new Error(String(error));
    ElMessage.error(`请求失败: ${error instanceof Error ? error.message : String(error)}`);
  };

  /**
   * 请求完成处理
   * @param result 请求结果
   */
  handleRequestComplete = (result: any) => {
    console.log('[GridComponentStore] 请求完成:', result);
  };

  /**
   * 显示模态框
   * @param type 模态框类型
   * @param record 记录对象
   */
  showModal = (type: 'create' | 'edit' | 'view' | 'delete', record?: any) => {
    this.formDialogTitle.value = type === 'edit' ? this.editFormDialogTitle.value : type === 'view' ? this.viewFormDialogTitle.value : type === 'create' ? this.createFormDialogTitle.value : '';
    this.uiState.modalType = type;
    
    // 根据类型设置不同的模态框可见性
    if (type === 'view') {
      this.uiState.viewModalVisible = true;
      this.uiState.formModalVisible = false;
    } else {
      this.uiState.formModalVisible = true;
      this.uiState.viewModalVisible = false;
    }
    
    if (type !== 'create' && record) {
      this.currentRecord.value = record;
      this.originalRecord.value = cloneDeep(record); // 复制原始记录
    } else {
      this.currentRecord.value = null;
      this.originalRecord.value = null;
    }
    
    this.uiState.formMode = type === 'view' ? 'view' : (type === 'edit' ? 'edit' : 'create');
    
    console.log(`[GridComponentStore] 显示${type}模态框`);
  };

  /**
   * 隐藏模态框
   */
  hideModal = () => {
    this.uiState.formModalVisible = false;
    this.uiState.viewModalVisible = false;
    console.log('[GridComponentStore] 隐藏模态框');
  };

  /**
   * 提交表单
   * @param values 表单值
   * @returns 处理结果
   */
  submitForm = async (values: Record<string, any>) => {
    console.log('[GridComponentStore] submitForm', values);
    try {
      this.loading.value = true;
      
      if (this.uiState.formMode === 'edit' && this.currentRecord.value?.id) {
        // 编辑模式
        await this.updateData(this.currentRecord.value.id, values);
      } else {
        // 新建模式
        await this.createData(values);
      }
      
      this.hideModal();
      this.refresh();
      return true;
    } catch (error) {
      console.error('[GridComponentStore] 提交表单失败:', error);
      return false;
    } finally {
      this.loading.value = false;
    }
  };

  /**
   * 处理对话框取消操作
   * 当用户取消编辑时，恢复表格中对应行的原始数据
   */
  handleDialogCancel = () => {
    // 隐藏对话框
    this.hideModal();
    
    // 如果是编辑模式且有原始记录，恢复数据
    if (this.uiState.formMode === 'edit' && this.originalRecord.value && this.currentRecord.value?.id) {
      const id = this.currentRecord.value.id;
      // 在表格数据中查找匹配的记录并更新
      const index = this.tableData.value.findIndex((item: any) => item.id === id);
      if (index !== -1) {
        console.log('[GridComponentStore] 取消编辑，恢复原始数据:', this.originalRecord.value);
        // 使用原始数据更新表格数据
        this.tableData.value[index] = cloneDeep(this.originalRecord.value);
      }
    }
    
    // 清空当前记录和原始记录
    this.currentRecord.value = {};
    this.originalRecord.value = null;
  };

  /**
   * 更新服务配置
   * @param config 新的服务配置
   */
  updateConfig(config: UiPageConfig): void {
    if (config.serviceConfig) {
      this.serviceConfig.value = { ...config.serviceConfig };
      // 更新服务实例
      this.service = new GridLayoutService(this.serviceConfig.value);
    }
  }

  /**
   * 更新服务实例
   * @param service 新的GridLayoutService实例
   */
  updateService(service: GridLayoutService): void {
    this.service = service;
  }

  /**
   * 注册组件配置
   * @param id 组件ID
   * @param config 组件配置
   */
  registerComponent(id: string, config: ComponentConfig): void {
    this.componentConfigs[id] = config;
  }

  /**
   * 获取组件配置
   * @param id 组件ID
   * @returns 组件配置
   */
  getComponentConfig<T extends ComponentConfig>(id: string): T | undefined {
    return this.componentConfigs[id] as T | undefined;
  }

  /**
   * 更新搜索参数
   * @param params 搜索参数
   */
  updateSearchParams(params: Record<string, any>): void {
    // 合并而不是替换，保留未在params中的字段
    for (const key in params) {
      this.queryParams[key] = params[key];
    }
  }

  /**
   * 重置搜索参数
   */
  resetSearchParams(): void {
    // 清空查询参数对象
    for (const key in this.queryParams) {
      delete this.queryParams[key];
    }
  }

  /**
   * 设置分页信息
   * @param pagination 分页信息
   */
  setPagination(pagination: Partial<Pagination>): void {
    for (const key in pagination) {
      // @ts-ignore - 动态键访问
      this.pagination[key] = pagination[key];
    }
  }

  /**
   * 设置表格选中行
   * @param selectedRowKeys 选中行的key数组
   * @param selectedRows 选中行数据数组
   */
  setSelectedRows(selectedRowKeys: (string | number)[], selectedRows: any[]): void {
    this.selectedRowKeys.value = selectedRowKeys;
    this.selectedRows.value = selectedRows;
  }

  /**
   * 切换搜索组件折叠状态
   */
  toggleSearchCollapse(): void {
    this.uiState.searchCollapsed = !this.uiState.searchCollapsed;
  }

  /**
   * 刷新数据
   * @returns Promise
   */
  async refresh(): Promise<void> {
    try {
      this.loading.value = true;
      // 构建查询参数
      const params = {
        ...this.queryParams,
        [this.pageInfoMap.page || this.pageInfoMap.current]: this.pagination.current,
        [this.pageInfoMap.pageSize]: this.pagination.pageSize
      };
      
      // 添加排序参数
      if (this.sorter.value) {
        params.sortField = this.sorter.value.field;
        params.sortOrder = this.sorter.value.order;
      }
      
      // 添加筛选参数
      for (const key in this.filters) {
        if (this.filters[key]) {
          params[key] = this.filters[key];
        }
      }
      
      // 发起请求
      const result = await this.service.fetchList(params);
      
      // 处理返回结果
      let list = [];
      let total = 0;
      if (Array.isArray(result)) {
        list = result;
      } else if (result.list) {
        list = result.list;
        total = result.total || 0;
      } else if (result.data) {
        if (Array.isArray(result.data)) {
          list = result.data;
        } else if (result.data.list) {
          list = result.data.list;
          total = result.data.total || 0;
        }
      }
      
      // 更新数据
      this.tableData.value = list;
      this.pagination.total = total;
      this.handleRequestComplete(result);
      this.tableDataVersion.value++; // 更新数据版本
    } catch (error) {
      this.handleRequestError(error);
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * 获取详情数据
   * @param id 记录ID
   * @returns 详情数据
   */
  async fetchDetail(id: string | number): Promise<any> {
    try {
      this.loading.value = true;
      const result = await this.service.getItemDetail(id);
      return result;
    } catch (error) {
      this.handleRequestError(error);
      throw error;
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * 创建数据
   * @param data 数据对象
   * @returns 创建结果
   */
  async createData(data: any): Promise<any> {
    try {
      this.loading.value = true;
      const result = await this.service.createItem(data);
      ElMessage.success('创建成功');
      return result;
    } catch (error) {
      this.handleRequestError(error);
      throw error;
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * 更新数据
   * @param id 记录ID
   * @param data 数据对象
   * @returns 更新结果
   */
  async updateData(id: string | number, data: any): Promise<any> {
    try {
      this.loading.value = true;
      const result = await this.service.updateItem(id, data);
      ElMessage.success('更新成功');
      return result;
    } catch (error) {
      this.handleRequestError(error);
      throw error;
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * 删除数据
   * @param id 记录ID
   * @returns 删除结果
   */
  async deleteData(id: string | number): Promise<any> {
    try {
      this.loading.value = true;
      const result = await this.service.deleteItem(id);
      ElMessage.success('删除成功');
      return result;
    } catch (error) {
      this.handleRequestError(error);
      throw error;
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * 批量删除数据
   * @param ids 记录ID数组
   * @returns 删除结果
   */
  async batchDeleteData(ids: (string | number)[]): Promise<any> {
    try {
      this.loading.value = true;
      const result = await this.service.batchDeleteItems(ids);
      ElMessage.success('批量删除成功');
      return result;
    } catch (error) {
      this.handleRequestError(error);
      throw error;
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * 获取指定API的数据(独立组件可以使用)
   * @param api API地址
   * @param params 查询参数
   * @returns 数据
   */
  async fetchDataByApi(api: string, params?: any): Promise<any> {
    try {
      this.loading.value = true;
      console.log('[调试 GridComponentStore] fetchDataByApi', api, params);
      const result = await this.service.fetchList({ ...params });
      return result;
    } catch (error) {
      this.handleRequestError(error);
      throw error;
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * 处理行按钮点击事件
   * @param data 包含行数据、按钮信息和索引的对象
   * @returns 处理结果
   */
  handleRowButtonClick(data: any): void {
    if (!data) return;
    
    // 获取行数据和按钮信息
    const { row, buttonRow, index } = data;
    // 从buttonRow中获取code
    const code = buttonRow?.code;
    
    console.log('[GridComponentStore] 行按钮点击:', row, buttonRow, code, index);
    
    if (!row || !code) {
      console.warn('[GridComponentStore] 行操作缺少必要数据:', row, code);
      return;
    }
    
    console.log(`[GridComponentStore] 执行操作: ${code}`, row, index);
    
    // 处理预定义操作
    switch (code) {
      case "edit":
        // 编辑模式
        if (row.id !== undefined) {
          // 先获取最新数据，再显示编辑表单
          this.fetchDetail(row.id).then(latestData => {
            // 使用深拷贝，避免直接修改原始数据
            const clonedData = cloneDeep(latestData);
            this.showModal('edit', clonedData);
          }).catch(() => {
            // 获取详情失败，回退使用当前行数据
            const clonedRow = cloneDeep(row);
            this.showModal('edit', clonedRow);
          });
        } else {
          const clonedRow = cloneDeep(row);
          this.showModal('edit', clonedRow);
        }
        break;
      case "delete":
        // 删除操作
        if (row.id !== undefined) {
          this.deleteData(row.id);
        }
        break;
      case "view":
        // 查看操作
        if (row.id !== undefined) {
          // 先获取最新数据，再显示查看表单
          this.fetchDetail(row.id).then(latestData => {
            // 使用深拷贝，保持一致性处理
            const clonedData = cloneDeep(latestData);
            this.showModal('view', clonedData);
          }).catch(() => {
            // 获取详情失败，回退使用当前行数据
            const clonedRow = cloneDeep(row);
            this.showModal('view', clonedRow);
          });
        } else {
          const clonedRow = cloneDeep(row);
          this.showModal('view', clonedRow);
        }
        break;
      default:
        // 处理自定义操作
        this.handleCustomAction(code, row);
        break;
    }
    // 可根据行数据和按钮信息进行操作
  }

  /**
   * 处理自定义行操作
   * @param action 操作代码
   * @param row 行数据
   * @returns 执行结果
   */
  async handleCustomAction(action: string, row: any): Promise<void> {
    console.log(`[GridComponentStore] 执行自定义操作 ${action}`, row);
    // 可根据action和row数据进行特定操作
  }

  /**
   * 从DTO结构生成表格列定义
   * @param structure DTO的结构对象
   * @param componentId 可选的组件ID，默认为'default'
   * @returns 处理后的列定义数组
   */
  generateColumns(structure: Record<string, any>, componentId?: string): PlusColumn[] {
    console.log('[GridComponentStore] generateColumns', structure, componentId);
    const columns: PlusColumn[] = [];
    
    Object.entries(structure).forEach(([key, field]) => {
      // 对field类型进行检查，确保它是一个对象
      if (typeof field === 'object' && field !== null) {
        const fieldObj = field as Record<string, unknown>;
        const fieldType = (fieldObj.type as string) || 'string';
        const description = (fieldObj.description as string) || '';
        
        // 处理description字段，提取label和options
        let label = description;
        let options: OptionsRow[] | undefined = undefined;
        
        // 检查description是否包含冒号和逗号分隔的选项
        if (description && description.includes('：') && description.includes(',')) {
          const colonIndex = description.indexOf('：');
          if (colonIndex !== -1) {
            // 提取冒号前的部分作为label
            label = description.substring(0, colonIndex);
            
            // 提取冒号后的部分，检查是否有选项格式
            const optionsText = description.substring(colonIndex + 1);
            const optionParts = optionsText.split(',');
            
            // 检查选项格式是否合适（例如：0-普通,1-银牌等）
            if (optionParts.length > 0 && optionParts[0].includes('-')) {
              options = optionParts.map(item => {
                const parts = item.trim().split('-');
                if (parts.length === 2) {
                  // 尝试将value转换为数字，如果失败则保留为字符串
                  const value = isNaN(Number(parts[0])) ? parts[0] : Number(parts[0]);
                  return {
                    value,
                    label: parts[1]
                  };
                }
                return { value: item, label: item };
              });
            }
          }
        }
        
        // 根据字段类型确定valueType
        let valueType: TableValueType | FormItemValueType = 'input';
        switch (fieldType) {
          case 'number':
            valueType = 'input-number';
            break;
          case 'boolean':
            valueType = 'switch';
            break;
          case 'date':
          case 'datetime':
            valueType = 'date-picker';
            break;
          case 'string':
          default:
            valueType = options ? 'select' : 'input';
            break;
        }
        
        const column: PlusColumn = {
          prop: key,
          label: label || key,
          valueType: valueType,
          sortable: true
        };
        
        // 如果存在选项，则添加到列定义中
        if (options && options.length > 0) {
          column.options = options;
        }
        columns.push(column);
      } else {
        // 如果field不是对象，创建一个基本的列定义
        columns.push({
          prop: key,
          label: key,
          valueType: 'input',
          sortable: true
        });
      }
    });
    console.log('[调试 GridComponentStore] 生成的列定义:', columns);
    
    // 如果提供了组件ID，直接设置到对应组件的列配置
    if (componentId) {
      this.setComponentColumns(componentId, columns);
    }
    
    return columns;
  }

  /**
   * 从PageData生成并设置表格列配置
   * @param pageData 页面数据对象
   * @param componentId 可选的组件ID，默认为'default'
   */
  generateColumnsFromPageData(pageData: PageData, componentId: string = 'default'): void {
    if (pageData.dto && pageData.dto.structure) {
      const columns = this.generateColumns(pageData.dto.structure, componentId);
      
      // 设置到指定组件的列配置
      this.setComponentColumns(componentId, columns);
      
      // 如果存在表格组件配置，更新其列定义
      if (this.componentConfigs[componentId] && this.componentConfigs[componentId].type === 'table') {
        (this.componentConfigs[componentId] as TableConfig).columns = columns;
      }
      
      console.log(`[GridComponentStore] 从DTO生成的列定义(${componentId}):`, columns);
    } else {
      console.warn('[GridComponentStore] 无法生成列定义：pageData.dto.structure 为空');
    }
  }

  /**
   * 设置指定组件的列定义
   * @param componentId 组件ID
   * @param columns 列定义数组
   */
  setComponentColumns(componentId: string, columns: PlusColumn[]): void {
    this.columnsMap[componentId] = columns;
    
    // 如果是默认组件，同时更新 columns ref (向后兼容)
    if (componentId === 'default') {
      this.columns.value = columns;
    }
  }

  /**
   * 获取指定组件的列定义
   * @param componentId 组件ID
   * @returns 列定义数组
   */
  getComponentColumns(componentId: string): PlusColumn[] {
    // 如果指定组件ID的列配置不存在，返回默认列配置
    return this.columnsMap[componentId] || this.columns.value || [];
  }
}

/**
 * 全局单例实例
 * 所有导入此模块的组件都将共享此实例
 */
let globalGridComponentStore: GridComponentStore | null = null;

/**
 * 创建或获取GridComponentStore单例实例
 * @param service GridLayoutService实例
 * @param uiConfig 服务配置
 * @returns GridComponentStore实例
 */
export function createGridComponentStore(service?: GridLayoutService, uiConfig?: UiPageConfig): GridComponentStore {
  if (!globalGridComponentStore) {
    // 根据传入的service或uiConfig创建store
    if (service) {
      globalGridComponentStore = new GridComponentStore(service);
    } else if (uiConfig) {
      globalGridComponentStore = new GridComponentStore(undefined, uiConfig.serviceConfig);
      globalGridComponentStore.updateConfig(uiConfig);
    } else {
      globalGridComponentStore = new GridComponentStore();
    }
  } else if (service || uiConfig) {
    // 更新现有实例的配置
    if (service) {
      globalGridComponentStore.updateService(service);
    }
    if (uiConfig) {
      globalGridComponentStore.updateConfig(uiConfig);
    }
  }
  
  return globalGridComponentStore;
}

/**
 * 获取当前的GridComponentStore实例
 * 如果实例不存在，会创建一个默认实例
 * @returns GridComponentStore实例
 */
export function getGridComponentStore(): GridComponentStore {
  if (!globalGridComponentStore) {
    globalGridComponentStore = new GridComponentStore();
  }
  return globalGridComponentStore;
}

/**
 * 重置GridComponentStore单例实例
 * 主要用于测试或需要完全重置状态的场景
 */
export function resetGridComponentStore(): void {
  globalGridComponentStore = null;
}

export default GridComponentStore;
