import { get, post, put, del } from '@/utils/request';
import type { AxiosResponse } from 'axios';
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

/**
 * @description GridPage 页面配置接口
 */
interface PageConfig {
  baseUrl: string; // 资源的基础URL，例如 '/users', '/orders'
  apiPrefix?: string; // 全局API前缀，例如 '/api/v1'
  // 可选：为特定操作指定不同的API路径或方法
  listApi?: string | ((params: any) => string); // 获取列表数据的API
  createApi?: string; // 创建资源的API
  updateApi?: string | ((id: string | number) => string); // 更新资源的API
  deleteApi?: string | ((id: string | number) => string); // 删除资源的API
  // 可选：数据转换钩子
  transformRequestParams?: (params: any) => any; // 发送请求前转换参数
  transformResponseData?: (response: AxiosResponse) => any; // 接收响应后转换数据
  // 扩展配置
  idField?: string; // 主键字段名，默认为'id'
  formatDetailResponse?: (response: any) => any; // 格式化详情响应
  defaultParams?: Record<string, any>; // 默认查询参数
  defaultFormData?: Record<string, any>; // 默认表单数据
  customActions?: Record<string, any>; // 自定义操作配置
  gridOptions?: {
    column: number;
    cellHeight: number;
    margin: number;
  }; // 网格选项
  messages?: {
    addSuccess?: string;
    editSuccess?: string;
    deleteSuccess?: string;
    deleteConfirm?: string;
  }; // 自定义消息
}

/**
 * @description GridPage 通用服务类t
 * 负责处理与后端API的数据交互，包括CRUD操作
 */
class GridPageService {
  public config: PageConfig; // 页面配置
  public loading = ref(false); // 加载状态
  public data = ref<any[]>([]); // 数据列表
  public total = ref(0); // 总记录数
  public currentRow = ref<any | null>(null); // 当前选中行
  public formVisible = ref(false); // 表单可见性
  public formTitle = ref(''); // 表单标题
  public formMode = ref<'add' | 'edit'>('add'); // 表单模式
  public formData = reactive<Record<string, any>>({}); // 表单数据
  // 详情查看状态
  public infoVisible = ref(false); // 详情可见性
  public infoTitle = ref('查看详情'); // 详情标题
  public infoData = ref<any | null>(null); // 详情数据
  // 当前查询参数
  public currentParams = reactive<Record<string, any>>({});

  /**
   * @description 构造函数
   * @param config 页面配置对象
   */
  constructor(config: PageConfig) {
    this.config = config;

    // 初始化默认参数
    const defaultParams = {
      page: 1,
      pageSize: 10,
      ...config.defaultParams
    };
    
    // 设置当前参数
    Object.assign(this.currentParams, defaultParams);
  }

  /**
   * @description 获取特定操作的API URL
   * @param type 操作类型 ('list', 'create', 'update', 'delete')
   * @param id 资源ID (用于 update, delete)
   * @param params 请求参数 (可用于 list 的函数式API)
   * @returns 完整的API URL
   */
  private getApiUrl(type: 'list' | 'create' | 'update' | 'delete' | 'detail', id?: string | number, params?: any): string {
    let url = '';
    const { baseUrl, listApi, createApi, updateApi, deleteApi, apiPrefix } = this.config;
    
    console.log('配置信息:', { 
      baseUrl, 
      listApi, 
      apiPrefix,
      type
    });
    
    // 根据操作类型和配置决定最终URL
    switch (type) {
      case 'list':
        if (typeof listApi === 'function') url = listApi(params); // 如果是函数，调用它生成URL
        else url = listApi || baseUrl; // 否则使用配置的listApi或baseUrl
        break;
      case 'create':
        url = createApi || baseUrl; // 使用配置的createApi或baseUrl
        break;
      case 'update':
        if (typeof updateApi === 'function') url = updateApi(id!); 
        else url = updateApi || `${baseUrl}/${id}`; // 使用配置的updateApi或RESTful风格的路径
        break;
      case 'delete':
        if (typeof deleteApi === 'function') url = deleteApi(id!); 
        else url = deleteApi || `${baseUrl}/${id}`; // 使用配置的deleteApi或RESTful风格的路径
        break;
      case 'detail':
        // 使用与update相同的URL结构，但使用GET方法
        if (typeof updateApi === 'function') url = updateApi(id!);
        else url = `${baseUrl}/${id}`;
        break;
    }
    
    console.log('原始构造的URL:', url);
    
    // 重要：检查URL是否已经包含api前缀，避免重复添加
    // 假设@/utils/request已经添加了'/api'前缀，这里我们要避免重复添加
    let finalUrl = url;
    
    // 1. 如果URL已经是完整的HTTP路径，不做任何修改
    if (url.startsWith('http')) {
      finalUrl = url;
    }
    // 2. 如果URL已经以'/'开头，直接返回，不添加前缀
    else if (url.startsWith('/')) {
      finalUrl = url;
    }
    // 3. 如果URL包含'api/'前缀，也直接返回，但需要确保以'/'开头
    else if (url.includes('api/')) {
      finalUrl = url.startsWith('/') ? url : `/${url}`;
    }
    // 4. 其他情况，不处理，让request工具处理
    
    console.log(`最终URL (${type}): ${finalUrl}`);
    return finalUrl;
  }

  /**
   * @description 获取列表数据 (Retrieve)
   * @param params 查询参数，例如分页、筛选条件等
   * @returns 列表数据 Promise
   */
  async fetchData(params: any = {}): Promise<any> {
    console.log('fetchData baseurl', this.config.baseUrl);
    try {
      this.loading.value = true;
      
      // 合并当前参数和新参数
      const queryParams = {
        ...this.currentParams,
        ...params
      };
      
      // 更新当前参数
      Object.assign(this.currentParams, queryParams);

      // 转换请求参数
      const transformedParams = this.config.transformRequestParams
        ? this.config.transformRequestParams(queryParams)
        : queryParams;
        
      const url = this.getApiUrl('list', undefined, transformedParams);
      console.log('请求列表数据:', url, transformedParams);
      
      // 确保我们了解@/utils/request中的baseURL设置
      // 假设它添加了'/api'前缀，我们这里不再添加
      const response = await get(url, transformedParams);
      
      // 转换响应数据
      const formattedResponse = this.config.transformResponseData
        ? this.config.transformResponseData(response as unknown as AxiosResponse)
        : response;
      
      // 提取数据和总数
      let responseList: any[] = [];
      let total = 0;
      
      if (formattedResponse && formattedResponse.list !== undefined) {
        responseList = formattedResponse.list;
        total = formattedResponse.total || responseList.length;
      } else if (formattedResponse && formattedResponse.data !== undefined) {
        responseList = formattedResponse.data;
        total = formattedResponse.total || responseList.length;
      } else if (Array.isArray(formattedResponse)) {
        responseList = formattedResponse;
        total = formattedResponse.length;
      } else {
        console.warn('无法解析响应数据格式:', formattedResponse);
      }
      
      // 更新组件状态
      this.data.value = responseList;
      this.total.value = total;
      
      return formattedResponse;
    } catch (error) {
      console.error('获取列表数据失败:', error);
      return { list: [], total: 0 };
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * @description 创建新项目 (Create)
   * @param data 要创建的数据
   * @returns 创建结果 Promise
   */
  async createItem(data: any): Promise<any> {
    try {
      this.loading.value = true;
      const url = this.getApiUrl('create');
      console.log('创建项目:', url, data);
      
      const response = await post(url, data);
      
      // 显示成功消息
      ElMessage.success(this.config.messages?.addSuccess || '添加成功');
      
      return response;
    } catch (error) {
      console.error('创建失败:', error);
      throw error;
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * @description 更新现有项目 (Update)
   * @param id 要更新的资源ID
   * @param data 更新的数据
   * @returns 更新结果 Promise
   */
  async updateItem(id: string | number, data: any): Promise<any> {
    try {
      this.loading.value = true;
      const url = this.getApiUrl('update', id);
      console.log('更新项目:', url, data);
      
      const response = await put(url, data);
      
      // 显示成功消息
      ElMessage.success(this.config.messages?.editSuccess || '更新成功');
      
      return response;
    } catch (error) {
      console.error('更新失败:', error);
      throw error;
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * @description 删除项目 (Delete)
   * @param id 要删除的资源ID
   * @returns 删除结果 Promise
   */
  async deleteItem(id: string | number): Promise<any> {
    try {
      // 确认删除
      await ElMessageBox.confirm(
        this.config.messages?.deleteConfirm || '确定要删除吗？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );
      
      this.loading.value = true;
      const url = this.getApiUrl('delete', id);
      console.log('删除项目:', url);
      
      const response = await del(url);
      
      // 显示成功消息
      ElMessage.success(this.config.messages?.deleteSuccess || '删除成功');
      
      return response;
    } catch (error) {
      if (error === 'cancel') {
        return false;
      }
      console.error('删除失败:', error);
      throw error;
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * @description 获取单条记录详情
   * @param id 记录ID
   * @returns 记录详情
   */
  async getDetail(id: string | number): Promise<any> {
    try {
      this.loading.value = true;
      const url = this.getApiUrl('detail', id);
      console.log('获取详情:', url);
      
      const response = await get(url);
      
      // 格式化响应数据
      const formattedResponse = this.config.formatDetailResponse 
        ? this.config.formatDetailResponse(response)
        : response;
        
      return formattedResponse;
    } catch (error) {
      console.error('获取详情失败:', error);
      return null;
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * @description 打开添加表单
   */
  openAddForm() {
    this.formMode.value = 'add';
    this.formTitle.value = '添加';
    
    // 清空表单数据
    Object.keys(this.formData).forEach(key => {
      delete this.formData[key];
    });
    
    // 使用默认表单数据
    if (this.config.defaultFormData) {
      Object.assign(this.formData, this.config.defaultFormData);
    }
    
    // 显示表单
    this.formVisible.value = true;
  }

  /**
   * @description 打开编辑表单
   * @param row 当前行数据
   */
  async openEditForm(row: any) {
    this.formMode.value = 'edit';
    this.formTitle.value = '编辑';
    
    try {
      // 清空表单数据
      Object.keys(this.formData).forEach(key => {
        delete this.formData[key];
      });
      
      // 获取主键字段
      const idField = this.config.idField || 'id';
      const recordId = row[idField];
      
      if (!recordId) {
        throw new Error('缺少记录ID');
      }

      // 显示加载状态
      this.loading.value = true;

      // 获取最新详情
      let detailData = null;
      
      // 尝试获取详情
      detailData = await this.getDetail(recordId);
      
      if (!detailData) {
        // 如果获取失败但有行数据，则使用行数据
        detailData = { ...row };
      }
      
      // 将详情数据赋值给表单
      Object.assign(this.formData, detailData);
      
      console.log('编辑表单数据:', this.formData);
      
      // 显示表单
      this.formVisible.value = true;
    } catch (error) {
      console.error('获取详情失败:', error);
      ElMessage.error('加载失败，请重试');
    } finally {
      // 关闭加载状态
      this.loading.value = false;
    }
  }

  /**
   * @description 保存表单数据
   * @param data 表单数据
   * @returns 保存结果
   */
  async saveForm(data?: any): Promise<boolean> {
    const saveData = data || this.formData;
    
    try {
      this.loading.value = true;
      console.log('保存表单模式:', this.formMode.value);
      console.log('提交数据:', saveData);
      
      if (this.formMode.value === 'add') {
        // 创建操作
        await this.createItem(saveData);
      } else {
        // 编辑操作 - 需要获取ID
        const idField = this.config.idField || 'id';
        const id = saveData[idField];
        
        if (!id) {
          throw new Error(`编辑操作需要${idField}字段`);
        }
        
        await this.updateItem(id, saveData);
      }
      
      // 操作成功后关闭表单
      this.formVisible.value = false;
      
      // 刷新列表数据
      await this.fetchData();
      
      return true;
    } catch (error) {
      console.error('保存表单失败:', error);
      return false;
    } finally {
      this.loading.value = false;
    }
  }

  /**
   * @description 打开查看详情
   * @param row 当前行数据
   * @param title 标题
   */
  async openInfoDescriptions(row: any, title?: string) {
    try {
      // 设置标题
      this.infoTitle.value = title || '查看详情';
      
      // 获取主键字段
      const idField = this.config.idField || 'id';
      const recordId = row[idField];
      
      if (!recordId) {
        console.warn('记录ID不存在，使用当前行数据展示');
        this.infoData.value = row;
        this.infoVisible.value = true;
        return;
      }

      // 显示加载状态
      this.loading.value = true;

      // 获取最新详情
      let detailData = null;
      
      // 尝试获取详情
      detailData = await this.getDetail(recordId);
      
      if (!detailData) {
        // 如果获取失败但有行数据，则使用行数据
        detailData = { ...row };
      }
      
      // 更新详情数据
      this.infoData.value = detailData;
      
      // 显示弹窗
      this.infoVisible.value = true;

    } catch (error) {
      console.error('获取详情失败:', error);
      ElMessage.error('加载失败，请重试');
    } finally {
      // 关闭加载状态
      this.loading.value = false;
    }
  }

  /**
   * @description 重置查询参数并重新获取列表
   */
  resetAndGetList() {
    // 重置为默认参数
    Object.assign(this.currentParams, this.config.defaultParams || {
      page: 1,
      pageSize: 10
    });
    // 重新获取列表
    this.fetchData();
  }

  /**
   * @description 处理自定义操作 (可选)
   * 可以用于处理非标准CRUD的操作
   * @param action 操作名称
   * @param payload 操作所需数据
   * @returns 操作结果 Promise
   */
  async handleAction(action: string, payload: any): Promise<any> {
    try {
      // 检查是否有自定义操作配置
      if (this.config.customActions && this.config.customActions[action]) {
        const actionConfig = this.config.customActions[action];
        let url = '';
        let method = 'post';
        
        // 根据配置确定URL和方法
        if (typeof actionConfig === 'string') {
          url = actionConfig;
        } else if (typeof actionConfig === 'object') {
          url = actionConfig.url;
          method = actionConfig.method || 'post';
        }
        
        if (!url) {
          url = `${this.config.baseUrl}/actions/${action}`;
        }
        
        console.log(`处理自定义操作 (${action}):`, url, payload);
        
        // 执行请求
        if (method.toLowerCase() === 'get') {
          return await get(url, payload);
        } else if (method.toLowerCase() === 'put') {
          return await put(url, payload);
        } else if (method.toLowerCase() === 'delete') {
          return await del(url);
        } else {
          return await post(url, payload);
        }
      }
      
      // 默认路径
      const url = `${this.config.baseUrl}/actions/${action}`;
      console.log(`处理自定义操作 (${action}):`, url, payload);
      return post(url, payload);
    } catch (error) {
      console.error(`执行操作 ${action} 失败:`, error);
      throw error;
    }
  }
}

export default GridPageService;
export type { PageConfig as GridPageServiceConfig }; // 导出配置类型，方便外部使用 