<!-- 
  基础页面组件
  通过JSON配置和API实现CRUD功能
-->
<template>
  <div class="base-page">
    <!-- 页面标题 -->
    <el-card>
      <PlusSearch
        v-model="searchParams"
        :columns="resolvedSearchConfig?.columns || []"
        @search="handleSearch"
        @reset="handleSearchReset"
      />
    </el-card>
    <el-card style="margin-top: 20px">
      <PlusTable
        :columns="resolvedTableConfig?.columns || []"
        :showIndex="resolvedTableConfig?.showIndex !== false"
        :isSelection="resolvedTableConfig?.showSelection"
        :rowKey="resolvedTableConfig?.rowKey || 'id'"
        :key="tableKey + 'table'"
        :data="tableData"
        :total="total"
        :search="resolvedSearchConfig || { span: 8 }"
        :action-bar="
          resolvedTableConfig?.actions
            ? {
                ...resolvedTableConfig.actions,
                buttons: resolvedTableConfig.actions.buttons?.map((btn) => ({
                  ...btn,
                  text: btn.text || '',
                  icon: btn.icon ? resolveIcon(btn.icon) : undefined, // 关键修复点
                })),
              }
            : undefined
        "
        :pagination="
          resolvedTableConfig?.showPagination !== false
            ? {
                key: tableKey + 'pagination',
                total: total,
                modelValue: pageInfo,
                pageSizeList: resolvedTableConfig?.pagination?.pageSizes || [
                  10, 20, 30, 50,
                ],
                layout:
                  resolvedTableConfig?.pagination?.layout ||
                  'total, sizes, prev, pager, next, jumper',
              }
            : undefined
        "
        @search="handleSearch"
        @reset="handleSearchReset"
        @click-action="handleRowAction"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        @paginationChange="handlePaginationChange"
      >
        <template v-if="toolbarConfig?.buttons?.length" #title>
          <el-button
            v-for="(button, index) in toolbarConfig.buttons"
            :key="index"
            :type="button.type"
            :icon="button.icon ? resolveIcon(button.icon) : undefined"
            :disabled="button.disabled"
            @click="handleToolbarAction(button.action as any)"
          >
            {{ button.text }}
          </el-button>
        </template>
        <!-- 透传表格相关插槽 -->
        <template
          v-for="name in Object.keys($slots)"
          :key="name"
          #[name]="slotData"
          v-if="name.startsWith('table-')"
        >
          <slot :name="name" v-bind="slotData || {}" />
        </template>

        <!-- 自定义单元格渲染 -->
        <template
          v-for="column in resolvedTableConfig?.columns"
          :key="column.prop"
          #[`cell-${column.prop}`]="{ row }"
        >
          <!-- 上传字段特殊处理 -->
          <template v-if="column.valueType === ('upload' as any)">
            <div v-if="row[column.prop]" class="upload-preview">
              <el-image
                v-if="isImageUrl(row[column.prop])"
                :src="row[column.prop]"
                :preview-src-list="[row[column.prop]]"
                fit="cover"
                class="upload-image"
              />
              <div v-else class="upload-file">
                <el-icon><Document /></el-icon>
                <a :href="row[column.prop]" target="_blank">查看文件</a>
              </div>
            </div>
          </template>

          <!-- 图片类型处理 -->
          <template v-else-if="column.valueType === 'img' && row[column.prop]">
            <el-image
              :src="row[column.prop]"
              :preview-src-list="[row[column.prop]]"
              :style="{ width: '140px' }"
              fit="cover"
            />
          </template>
          <template v-else-if="column.valueType === 'text' && !row[column.prop]">
            <el-input v-model="row[column.prop]" />
          </template>
          <!-- 日期时间类型处理 -->
          <!-- <template v-else-if="column.valueType === 'date-picker' && row[column.prop]">
            {{ formatDateTime(row[column.prop], getFieldProp(column, 'format')) }}
          </template> -->
          
          <!-- 标签类型处理 -->
          <template v-else-if="column.valueType === 'tag' && row[column.prop]">
            <el-tag
              :type="getTagType(row[column.prop], column.options)"
              :effect="column.effect || 'light'"
            >
              {{ getTagLabel(row[column.prop], column.options) || row[column.prop] }}
            </el-tag>
          </template>
          
          <!-- HTML渲染类型处理 -->
          <template v-else-if="column.renderHTML">
            <div v-html="(column.renderHTML as any)(row[column.prop], row)"></div>
          </template>
          
          <!-- 默认插槽透传 -->
          <slot v-else :name="`info-cell-${column.prop}`" :value="row[column.prop]" :row="row">
            {{ row[column.prop] }}
          </slot>
        </template>
      </PlusTable>
    </el-card>

    <!-- 表单弹窗 -->
    <PlusDialogForm
      v-model:visible="formVisible"
      v-model="formModel"
      :form="{
        columns: resolvedFormConfig?.columns || [],
        labelWidth: resolvedFormConfig?.labelWidth || 80,
        labelPosition: resolvedFormConfig?.labelPosition || 'right',
        rules: resolvedFormConfig?.rules,
        hasLabel: resolvedFormConfig?.hasLabel !== false,
        span: resolvedFormConfig?.span,
        gutter: resolvedFormConfig?.gutter,
      }"
      :dialog="{
        title: pageService.formTitle.value,
        width: typeof resolvedFormConfig?.width === 'number' ? resolvedFormConfig.width + 'px' : resolvedFormConfig?.width || '500px',
        destroyOnClose: true,
      }"
      @confirm="handleDialogConfirm"
      @cancel="handleDialogCancel"
      @closed="handleFormClosed"
    >
      <!-- 透传表单相关插槽 -->
      <template
        v-for="name in Object.keys($slots)"
        :key="name"
        #[name]="slotData"
        v-if="name.startsWith('table-')"
      >
        <slot :name="name" v-bind="slotData || {}" />
      </template>

      <!-- 自定义上传组件处理 -->
      <template
        v-for="column in uploadColumns"
        :key="column.prop"
        #[`plus-field-${column.prop}`]="slotProps"
      >
        <!-- 显示当前文件 -->
        <div v-if="getFileValue(slotProps, column)" class="upload-current-file">
          <div v-if="isImageUrl(getFileValue(slotProps, column))" class="image-preview">
            <el-image
              :src="getFileValue(slotProps, column)"
              :preview-src-list="[getFileValue(slotProps, column)]"
              fit="cover"
              class="preview-image"
            />
            <div class="preview-url">{{ getFileValue(slotProps, column) }}</div>
          </div>
          <div v-else class="file-preview">
            <el-icon><Document /></el-icon>
            <a :href="getFileValue(slotProps, column)" target="_blank" class="file-link">
              {{ getFileNameFromUrl(getFileValue(slotProps, column)) }}
            </a>
            <div class="preview-url">{{ getFileValue(slotProps, column) }}</div>
          </div>
        </div>
        
        <FileUploader
          ref="fileUploaderRef"
          :action="column.uploadProps?.action || '/v1/admin/upload'"
          :headers="column.uploadProps?.headers"
          :multiple="column.uploadProps?.multiple"
          :accept="column.uploadProps?.accept"
          :file-limit="column.uploadProps?.fileLimit || 1"
          :size-limit="column.uploadProps?.sizeLimit || 5 * 1024 * 1024"
          :file-usage="column.uploadProps?.fileUsage || column.prop"
          :upload-style="column.uploadProps?.style || 'default'"
          :show-file-list="column.uploadProps?.showFileList !== false"
          :initial-files="getInitialFiles(getFileValue(slotProps, column))"
          @success="(res) => {
            handleUploadSuccess(res, column, slotProps);
            // 直接修改formModel值，确保双向绑定生效
            if (formModel.value && column.prop) {
              let fileUrl = '';
              if (res && res.data && res.data.file_url) fileUrl = res.data.file_url;
              else if (res && res.file_url) fileUrl = res.file_url;
              else if (res && res.url) fileUrl = res.url;
              else if (typeof res === 'string') fileUrl = res;
              
              if (fileUrl) {
                console.log('直接设置formModel值:', column.prop, fileUrl);
                formModel.value[column.prop] = fileUrl;
              }
            }
          }"
          @error="(err) => handleUploadError(err, column)"
        >
          <template #tip>
            <slot :name="`upload-tip-${column.prop}`">
              <p>
                {{ column.uploadProps?.tip || "点击或拖拽文件到此区域上传" }}
              </p>
              <p v-if="column.uploadProps?.sizeLimit" class="upload-tip">
                文件大小不超过
                {{ formatFileSize(column.uploadProps.sizeLimit) }}
              </p>
            </slot>
          </template>
        </FileUploader>
      </template>
      
    </PlusDialogForm>

    <!-- 查看详情描述列表弹出框 -->
    <el-dialog
      v-model="infoVisible"
      :title="infoTitle"
      :width="typeof resolvedInfoConfig?.width === 'number' ? resolvedInfoConfig.width + 'px' : resolvedInfoConfig?.width || '700px'"
      destroy-on-close
      @close="handleInfoClosed"
    >
      <PlusDescriptions
        v-if="infoModel && resolvedInfoConfig"
        :columns="resolvedInfoConfig.columns || []"
        :data="infoModel"
        :border="resolvedInfoConfig?.border !== false"
        :column="resolvedInfoConfig?.column || 2"
        :size="resolvedInfoConfig?.size || 'default'"
        :direction="resolvedInfoConfig?.direction || 'horizontal'"
      >
        <!-- 自定义详情项渲染 -->
        <template 
          v-for="column in resolvedInfoConfig?.columns || []" 
          :key="column.prop"
          #[`cell-${column.prop}`]="{ row }"
        >
          <!-- 自定义渲染函数处理 -->
          <template v-if="column.renderDescriptionsItem">
            <div v-if="typeof column.renderDescriptionsItem === 'function'">
              {{ column.renderDescriptionsItem({ value: row[column.prop], row, column }) }}
            </div>
            <component
              v-else
              :is="column.renderDescriptionsItem"
              :value="row[column.prop]"
              :row="row"
            />
          </template>
          
          <!-- 图片类型处理 -->
          <template v-else-if="column.valueType === 'img' && row[column.prop]">
            <el-image
              :src="row[column.prop]"
              :preview-src-list="[row[column.prop]]"
              :style="{ width: '140px' }"
              fit="cover"
            />
          </template>
          
          <!-- 日期时间类型处理 -->
          <template v-else-if="column.valueType === 'date-picker' && row[column.prop]">
            {{ formatDateTime(row[column.prop], getFieldProp(column, 'format')) }}
          </template>
          
          <!-- 标签类型处理 -->
          <template v-else-if="column.valueType === 'tag' && row[column.prop]">
            <el-tag
              :type="getTagType(row[column.prop], column.options)"
              :effect="column.effect || 'light'"
            >
              {{ getTagLabel(row[column.prop], column.options) || row[column.prop] }}
            </el-tag>
          </template>
          
          <!-- HTML渲染类型处理 -->
          <template v-else-if="column.renderHTML">
            <div v-html="(column.renderHTML as any)(row[column.prop], row)"></div>
          </template>
          
          <!-- 默认插槽透传 -->
          <slot v-else :name="`info-cell-${column.prop}`" :value="row[column.prop]" :row="row">
            {{ row[column.prop] }}
          </slot>
        </template>
      </PlusDescriptions>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from "vue";
import {
  PlusSearch,
  PlusTable,
  useTable,
  PlusDialogForm,
  PlusDescriptions,
} from "plus-pro-components";
import type { PlusColumn } from "plus-pro-components";
import { ElMessageBox, ElMessage } from "element-plus";
import { Document, Edit, Delete, View } from "@element-plus/icons-vue";
//import type { FormInstance } from 'element-plus';

import dayjs from "dayjs";
import {
  createPageService,
  formatValueByType,
  parseColumnsToConfig,
  formatDataByColumns,
} from "./service";
import { validators } from "./validator"; // 只引入校验函数对象

import { FileUploader } from "@/components/common";
import type {
  SearchConfig,
  TableConfig,
  FormConfig,
  ServiceConfig,
  CustomPlusColumn,
} from "./types";

const { tableData, total, pageInfo } = useTable<any>();

/**
 * 定义参数类型
 */
interface Props {
  // 基础配置
  title?: string;
  baseUrl?: string;
  apiPrefix?: string;

  // 组件配置
  searchConfig?: SearchConfig;
  tableConfig?: TableConfig;
  formConfig?: FormConfig;
  toolbarConfig?: {
    buttons: Array<{
      type?: string;
      text: string;
      icon?: string;
      action: string;
      disabled?: boolean;
    }>;
  };
  serviceConfig?: ServiceConfig;
  infoConfig?: {
    column?: number;
    size?: string;
    direction?: string;
    columns: CustomPlusColumn[];
    border?: boolean;
    width?: string | number;
  };

  // 初始数据
  initialData?: any[];

  // 列配置 - 可以仅传入此配置，自动生成其他配置
  columns?: CustomPlusColumn[];
}

const tableKey = ref(0);
// 定义事件
interface Emits {
  (e: "search", params: Record<string, any>): void;
  (e: "refresh"): void;
  (e: "toolbar-action", action: string): void;
  (e: "row-action", action: string, row: any, index: number): void;
  (e: "selection-change", rows: any[]): void;
  (e: "row-click", row: any, column: any, event: Event): void;
  (e: "sort-change", params: { prop: string; order: string | null }): void;
  (e: "form-submit", formData: any, mode: "add" | "edit"): void;
  (e: "form-cancel"): void;
  (e: "upload-success", url: string, field: string, response: any): void;
  (e: "upload-error", error: any, field: string): void;
}

// 接收属性
const props = withDefaults(defineProps<Props>(), {
  title: "",
  baseUrl: "",
  apiPrefix: "/api",
  initialData: () => [],
});

// 定义事件
const emit = defineEmits<Emits>();

// 获取组件实例引用
const plusPageRef = ref();
const plusFormRef = ref();
const fileUploaderRef = ref();

// 从columns生成配置
const generatedConfigs = computed(() => {
  if (props.columns && props.columns.length > 0) {
    // 使用service.ts中提供的方法解析columns配置
    return parseColumnsToConfig(props.columns);
  }
  return {
    searchConfig: { columns: [] },
    tableConfig: { columns: [] },
    formConfig: { columns: [] },
  };
});

// 合并配置 - 优先使用传入的配置，如果没有则使用自动生成的配置
const resolvedSearchConfig = computed(() => {
  console.log(
    "searchConfig",
    props.searchConfig,
    generatedConfigs.value.searchConfig
  );
  return props.searchConfig || generatedConfigs.value.searchConfig;
});

const resolvedTableConfig = computed(() => {
  return props.tableConfig || generatedConfigs.value.tableConfig;
});

const resolvedFormConfig = computed(() => {
  const formConfig = props.formConfig || generatedConfigs.value.formConfig;
  if (!formConfig) {
    return {
      columns: [],
    };
  }

  // 处理表单规则，注入自定义校验函数
  if (formConfig.rules) {
    // 检查每个规则是否包含validator字段，如果包含且值为字符串，则查找对应的校验函数
    Object.entries(formConfig.rules).forEach(([field, fieldRules]) => {
      if (Array.isArray(fieldRules)) {
        fieldRules.forEach((rule: any) => {
          if (rule.validator && typeof rule.validator === 'string') {
            // 从validators中获取对应名称的校验函数
            const validatorName = rule.validator as string;
            if (validators[validatorName as keyof typeof validators]) {
              rule.validator = validators[validatorName as keyof typeof validators];
            }
          }
        });
      } else if (fieldRules && typeof fieldRules === 'object') {
        const rule = fieldRules as any;
        if (rule.validator && typeof rule.validator === 'string') {
          const validatorName = rule.validator as string;
          if (validators[validatorName as keyof typeof validators]) {
            rule.validator = validators[validatorName as keyof typeof validators];
          }
        }
      }
    });
  }

  return formConfig;
});

// 详情查看配置
const resolvedInfoConfig = computed(() => {
  return props.infoConfig;
});

// 服务实例 - 传入columns配置
const pageService = createPageService({
  baseUrl: props.baseUrl,
  apiPrefix: props.apiPrefix,
  columns: props.columns ? props.columns : props.tableConfig?.columns, // 将columns传递给服务层，用于数据格式化

  ...(props.serviceConfig || {}),
});

// 表格数据

//const currentPage = ref(1);
//const pageSize = ref(10);
const searchParams = ref<Record<string, any>>({});
const selectedRows = ref<any[]>([]);

// 表单数据
const formVisible = ref(false);
const formMode = computed(() => pageService.formMode.value);
const formModel = ref<Record<string, any>>({});
const formTitle = computed(() => pageService.formTitle.value);

// 查看详情数据
const infoVisible = ref(false);
const infoTitle = ref("查看详情");
const infoModel = ref<Record<string, any>>({});

// 计算上传字段列
const uploadColumns = computed(() => {
  if (!resolvedFormConfig.value) {
    return [];
  }

  return resolvedFormConfig.value?.columns?.filter((column: any) => {
    return column.valueType === "upload";
  });
});

// 处理详情关闭
const handleInfoClosed = () => {
  infoVisible.value = false;
  pageService.infoVisible.value = false;
};
// 图标解析
const resolveIcon = (iconName: string) => {
  const icons: Record<string, any> = {
    Document,
    Edit,
    Delete,
    View,
    // 添加更多图标映射...
  };
  return icons[iconName] || undefined;
};

// 获取标签类型
const getTagType = (value: any, options?: any) => {
  // 检查options是否为数组或类似数组的对象
  if (!options || typeof options.find !== 'function') return '';
  
  const option = options.find((opt: any) => opt.value === value);
  return option?.color || '';
};

// 获取标签文本
const getTagLabel = (value: any, options?: any) => {
  // 检查options是否为数组或类似数组的对象
  if (!options || typeof options.find !== 'function') return value;
  
  const option = options.find((opt: any) => opt.value === value);
  return option?.label || value;
};

// 处理请求
const handleRequest = async (params: any) => {
  if (!params) params = {};

  // 合并查询参数
  const mergedParams = {
    ...searchParams.value,
    ...params,
  };

  try {
    console.log("请求参数:", mergedParams);
    // 注意：getList方法没有返回值，它内部更新data和total
    await pageService.getList(mergedParams);

    // 直接使用响应中的数据（当页面初始化或搜索时调用）
    console.log("tableData.value", tableData.value, total.value);
    return {
      data: tableData.value,
      total: total.value,
    };
  } catch (error) {
    console.error("加载数据失败:", error);
    tableData.value = [];
    total.value = 0;
    return {
      data: [],
      total: 0,
    };
  }
};

// 方法 - 刷新表格
const refreshTable = () => {
  // 直接调用我们自己的请求处理方法，避免依赖PlusPage组件的方法
  handleRequest({});
  tableKey.value++;

  // 触发事件
  emit("refresh");
};

// 方法 - 处理搜索
const handleSearch = (values: Record<string, any>) => {
  console.log("搜索参数:", values);
  searchParams.value = values;
  //currentPage.value = 1;
  pageInfo.value.page = 1;

  // 触发事件
  emit("search", values);

  // 刷新表格
  refreshTable();
};

// 方法 - 处理搜索重置
const handleSearchReset = () => {
  searchParams.value = {};
  //currentPage.value = 1;
  pageInfo.value.page = 1;
  // 刷新表格
  refreshTable();
};

// 方法 - 处理排序变化
const handleSortChange = (params: { prop: string; order: string | null }) => {
  console.log("排序变化:", params);

  // 添加排序参数
  searchParams.value.sortField = params.prop;
  searchParams.value.sortOrder = params.order;

  // 触发事件
  emit("sort-change", params);

  // 刷新表格
  refreshTable();
};

// 方法 - 处理选择变化
const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows;

  // 触发事件
  emit("selection-change", rows);
};

// 方法 - 处理工具栏按钮点击
const handleToolbarAction = (action: string) => {
  console.log("工具栏操作:", action);

  // 触发事件
  emit("toolbar-action", action);

  // 处理预定义操作
  switch (action) {
    case "add":
      openAddForm();
      break;
    case "refresh":
      refreshTable();
      break;
    // 其他操作由外部处理
  }
};

// 方法 - 处理行操作按钮点击
const handleRowAction = ({
  row,
  buttonRow,
  index,
}: {
  row: any;
  buttonRow: any;
  index: number;
}) => {
  if (!row || !buttonRow) return;

  const action = buttonRow.code || buttonRow.action;
  console.log("行操作:", action, row, index);

  // 触发事件
  emit("row-action", action, row, index);

  // 处理预定义操作
  switch (action) {
    case "edit":
      openEditForm(row);
      break;
    case "delete":
      deleteRow(row);
      break;
    case "view":
      viewRow(row);
      break;
    default:
      // 处理自定义操作
      if (
        props.serviceConfig?.customActions &&
        props.serviceConfig.customActions[action]
      ) {
        handleCustomAction(action, row);
      }
      break;
  }
};

// 处理自定义操作
const handleCustomAction = async (action: string, row: any) => {
  const actionConfig = props.serviceConfig?.customActions?.[action];
  if (!actionConfig) return;

  // 确认提示
  // if (typeof actionConfig === "object" && actionConfig.confirmMessage) {
  //   try {
  //     await ElMessageBox.confirm(actionConfig.confirmMessage, "提示", {
  //       confirmButtonText: "确定",
  //       cancelButtonText: "取消",
  //       type: "warning",
  //     });
  //   } catch (error) {
  //     return; // 用户取消操作
  //   }
  // }

  try {
    // 执行操作
    await pageService.executeAction(action, row);

    // 显示成功消息
    if (typeof actionConfig === "object" && actionConfig.successMessage) {
      ElMessage.success(actionConfig.successMessage);
    }
    console.log("执行操作成功", action, row);
    // 刷新表格
    refreshTable();
  } catch (error) {
    if (error !== "cancel") {
      console.error(`执行${action}操作失败:`, error);
      ElMessage.error(`操作失败: ${error}`);
    }
  }
};

// 方法 - 打开添加表单
const openAddForm = () => {
  pageService.formMode.value = "add";
  formModel.value = {};

  try {
    // 获取默认表单数据
    // 1. 从service中获取默认表单数据
    if (typeof pageService.generateDefaultFormData === "function") {
      // 如果提供了formConfig.columns，则使用它来生成默认值
      const columns = resolvedFormConfig.value?.columns || [];

      // 创建默认表单数据
      const defaultData = pageService.generateDefaultFormData(columns);
      console.log("从列配置生成的默认表单数据:", defaultData);

      // 合并到formModel
      formModel.value = { ...defaultData };
    }

    // 2. 使用serviceConfig中的defaultFormData
    if (props.serviceConfig?.defaultFormData) {
      formModel.value = {
        ...formModel.value,
        ...props.serviceConfig.defaultFormData,
      };
      console.log(
        "使用配置的默认表单数据:",
        props.serviceConfig.defaultFormData
      );
    }

    // 显示表单
    formVisible.value = true;
  } catch (error) {
    console.error("打开添加表单错误:", error);
    ElMessage.error("初始化表单失败");
  }
};

// 方法 - 打开编辑表单
const openEditForm = async (row: any) => {
  try {
    // 如果service有自己的openEditForm方法，优先使用
    if (typeof pageService.openEditForm === "function") {
      // 调用service中的openEditForm方法
      await pageService.openEditForm(row);

      // 将service的formData同步到本地的formModel
      formModel.value = { ...pageService.formData };
      console.log("formModel:", formModel.value);
      //formVisible.value = true;
    } else {
      console.log("使用旧的实现方式");
      // 旧的实现方式（备用）
      pageService.formMode.value = "edit";

      // 如果有获取详情方法，则先获取详情
      if (typeof pageService.getDetail === "function") {
        const detail = await pageService.getDetail(row.id || row._id);
        formModel.value = detail || { ...row };
      } else {
        formModel.value = { ...row };
      }
    }
    console.log("formModel", formModel.value);
    // 最后打开表单对话框
    formVisible.value = true;
  } catch (error) {
    console.error("打开编辑表单失败:", error);
    ElMessage.error("获取详情失败");
  }
};

// 方法 - 删除行
const deleteRow = async (row: any) => {
  try {
    await ElMessageBox.confirm("确认删除此项?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    await pageService.deleteItem(row.id || row._id);
    ElMessage.success("删除成功");
    refreshTable();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
      ElMessage.error("删除失败");
    }
  }
};

// 方法 - 查看行
const viewRow = (row: any) => {
  // 如果有配置查看详情，则打开查看详情弹窗
  if (resolvedInfoConfig.value) {
    // 调用服务层方法处理
    pageService.openInfoDescriptions(row);
  } else {
    // 查看操作，根据需要实现
    console.log("查看行:", row);
  }
};

// 方法 - 处理表单确认
const handleDialogConfirm = async () => {
  console.log("handleDialogConfirm");
  try {
    // 表单验证
    const isValid = await plusFormRef.value?.validate();
    console.log("isValid", isValid, "formModel:", formModel.value);
    //if (!isValid) return;

    // 根据模式执行不同操作
    if (pageService.formMode.value === "add") {
      console.log("添加");
      await pageService.saveForm(formModel.value, "add");
      ElMessage.success(
        props.serviceConfig?.messages?.addSuccess || "添加成功"
      );
    } else {
      console.log("编辑");
      await pageService.saveForm(formModel.value, "edit");
      ElMessage.success(
        props.serviceConfig?.messages?.editSuccess || "编辑成功"
      );
    }

    // 关闭表单并刷新数据
    formVisible.value = false;
    refreshTable();

    // 触发表单提交事件
    emit("form-submit", formModel.value, pageService.formMode.value);
  } catch (error) {
    console.error("表单提交失败:", error);
    ElMessage.error("操作失败，请重试");
  }
};

// 方法 - 处理表单取消
const handleDialogCancel = () => {
  formVisible.value = false;

  // 触发事件
  emit("form-cancel");
};

// 方法 - 处理表单关闭
const handleFormClosed = () => {
  formModel.value = {};
};

// 方法 - 处理上传成功
const handleUploadSuccess = (
  response: any,
  column: PlusColumn,
  slotProps: any
) => {
  console.log("上传成功:", response);
  console.log("slotProps完整内容:", slotProps);
  console.log("slotProps.model:", slotProps.model);
  console.log("column信息:", column);

  // 提取文件URL
  let fileUrl = "";
  if (response && response.data && response.data.file_url) {
    fileUrl = response.data.file_url;
  } else if (response && response.file_url) {
    fileUrl = response.file_url;
  } else if (response && response.url) {
    fileUrl = response.url;
  } else if (typeof response === "string") {
    fileUrl = response;
  }

  if (fileUrl) {
    console.log("获取到的文件URL:", fileUrl);
    
    // 更新表单字段值 - 尝试多种方式
    // 方法1: 使用onChange如果存在
    if (slotProps && typeof slotProps.onChange === "function") {
      console.log("使用onChange更新值");
      slotProps.onChange(fileUrl);
    } 
    
    // 方法2: 直接更新model
    if (slotProps && slotProps.model && column.prop) {
      console.log("尝试更新model:", column.prop);
      slotProps.model[column.prop] = fileUrl;
    }
    
    // 方法3: 更新formModel
    if (formModel.value && column.prop) {
      console.log("更新formModel:", column.prop);
      formModel.value[column.prop] = fileUrl;
    }

    // 强制更新DOM
    nextTick(() => {
      console.log("文件上传成功后的值:", {
        "formModel": formModel.value ? formModel.value[column.prop] : undefined,
        "slotProps.model": slotProps && slotProps.model ? slotProps.model[column.prop] : undefined,
        "slotProps.value": slotProps ? slotProps.value : undefined
      });
    });

    // 触发事件
    emit("upload-success", fileUrl, column.prop, response);
  }
};

// 方法 - 处理上传错误
const handleUploadError = (error: any, column: PlusColumn) => {
  console.error("上传失败:", error);

  // 触发事件
  emit("upload-error", error, column.prop);

  // 显示错误消息
  ElMessage.error("上传失败");
};

// 方法 - 格式化日期
const formatDate = (value: any, withTime = false) => {
  if (!value) {
    return "-";
  }

  try {
    return dayjs(value).format(withTime ? "YYYY-MM-DD HH:mm:ss" : "YYYY-MM-DD");
  } catch (error) {
    return value;
  }
};

// 方法 - 格式化金额
const formatMoney = (value: any, format?: string) => {
  if (value === undefined || value === null || value === "") {
    return "-";
  }

  const numValue = parseFloat(value);
  if (isNaN(numValue)) {
    return value;
  }

  // 默认保留两位小数
  return `¥ ${numValue.toFixed(2)}`;
};

// 方法 - 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + " B";
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + " KB";
  } else if (size < 1024 * 1024 * 1024) {
    return (size / 1024 / 1024).toFixed(2) + " MB";
  } else {
    return (size / 1024 / 1024 / 1024).toFixed(2) + " GB";
  }
};

// 方法 - 判断是否为图片URL
const isImageUrl = (url: string) => {
  if (!url) {
    return false;
  }

  // 检查URL是否以图片扩展名结尾
  const extensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"];
  const lowercasedUrl = url.toLowerCase();

  // 检查扩展名
  return extensions.some((ext) => lowercasedUrl.endsWith(ext));
};

// 从URL中提取文件名
const getFileNameFromUrl = (url: string): string => {
  if (!url) return '';
  
  try {
    // 尝试从URL中提取文件名
    const pathParts = new URL(url).pathname.split('/');
    const fileName = pathParts[pathParts.length - 1];
    
    // 解码URL编码的文件名
    return decodeURIComponent(fileName);
  } catch (error) {
    // 如果URL解析失败，直接返回最后一部分
    const parts = url.split('/');
    return parts[parts.length - 1];
  }
};

// 处理初始文件
const getInitialFiles = (value: string | undefined) => {
  console.log("getInitialFiles被调用，value=", value);
  
  if (!value) {
    console.log("没有初始文件值");
    return [];
  }
  
  try {
    // 创建一个包含当前文件URL的初始文件数组
    const initialFile = {
      id: 'initial-file',
      name: getFileNameFromUrl(value),
      size: 0,
      status: 'success' as const,
      raw: new File([], getFileNameFromUrl(value)),
      url: value,
      mime_type: isImageUrl(value) ? 'image/jpeg' : 'application/octet-stream'
    };
    
    console.log("创建的初始文件对象:", initialFile);
    return [initialFile];
  } catch (error) {
    console.error("创建初始文件对象失败:", error);
    return [];
  }
};

// 监听数据变化
watch(
  () => pageService.data.value,
  (newData) => {
    tableData.value = newData;
  }
);

watch(
  () => pageService.total.value,
  (newTotal) => {
    total.value = newTotal;
  }
);

// 监听表单可见性变化
watch(
  () => pageService.formVisible.value,
  (visible) => {
    formVisible.value = visible;
  }
);

// 监听表单数据变化
watch(
  () => pageService.formData,
  (data) => {
    formModel.value = { ...data };
  },
  { deep: true }
);

// 监听详情可见性变化
watch(
  () => pageService.infoVisible.value,
  (visible) => {
    console.log("infoVisible", visible);
    infoVisible.value = visible;
  }
);

// 监听详情标题变化
watch(
  () => pageService.infoTitle.value,
  (title) => {
    infoTitle.value = title;
  }
);

// 监听详情数据变化
watch(
  () => pageService.infoData.value,
  (data) => {
    if (data) {
      infoModel.value = { ...data };
    }
  }
);

// 监听分页变化
const handlePaginationChange = (getpageInfo: any) => {
  pageInfo.value = getpageInfo;
  console.log("handlePaginationChange", getpageInfo);
  searchParams.value.page = getpageInfo.page;
  searchParams.value.pageSize = getpageInfo.pageSize;
  refreshTable();
};

// 监听表单模型变化，实时更新预览
watch(formModel, (newVal) => {
  if (newVal) {
    console.log("表单模型变化:", newVal);
    // 强制刷新上传组件视图
    nextTick(() => {
      const columns = uploadColumns.value || [];
      if (columns.length) {
        columns.forEach(column => {
          const prop = column.prop;
          if (newVal[prop]) {
            console.log(`字段 ${prop} 的值变化为:`, newVal[prop]);
          }
        });
      }
    });
  }
}, { deep: true });

// 页面加载完成后初始化
onMounted(async () => {
  if (props.tableConfig?.pagination?.pageSize) {
    console.log("pageSize", props.tableConfig?.pagination?.pageSize);
    pageInfo.value.pageSize = props.tableConfig?.pagination?.pageSize;
  }
  try {
    await handleRequest({});
  } catch (error) {
    console.error("初始加载失败:", error);
  }
});

// 暴露方法
defineExpose({
  refreshTable,
  handleToolbarAction,
  handleRowAction,
  openAddForm,
  openEditForm,
  deleteRow,
  formModel,
  selectedRows,
  searchParams,
});

/**
 * 获取文件值，尝试多个来源
 */
function getFileValue(slotProps: any, column: CustomPlusColumn): string {
  console.log("getFileValue被调用, slotProps:", slotProps, "column:", column);
  
  // 尝试从多个可能的来源获取文件值
  const sources = [
    // 从slotProps.value获取
    slotProps?.value,
    // 从slotProps.model获取
    slotProps?.model?.[column.prop],
    // 从表单模型获取
    formModel.value?.[column.prop]
  ];
  
  // 返回第一个非空值
  for (const source of sources) {
    if (source) {
      console.log("找到文件值:", source);
      return source;
    }
  }
  
  console.log("没有找到任何文件值");
  return '';
};

// 方法 - 格式化日期时间
const formatDateTime = (value: any, format?: string) => {
  if (!value) {
    return "-";
  }

  try {
    return dayjs(value).format(format || "YYYY-MM-DD HH:mm:ss");
  } catch (error) {
    return value;
  }
};

// 方法 - 获取字段属性
const getFieldProp = (column: any, prop: string) => {
  if (!column) return undefined;
  
  // 尝试从column中获取属性
  if (column[prop]) {
    return column[prop];
  }
  
  // 尝试从column.fieldProps中获取属性
  if (column.fieldProps && column.fieldProps[prop]) {
    return column.fieldProps[prop];
  }
  
  return undefined;
};
</script>

<style lang="scss" scoped>
.base-page {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .page-title {
      font-size: 18px;
      font-weight: 500;
      margin: 0;
    }

    .page-toolbar {
      display: flex;
      gap: 8px;
    }
  }

  .table-container {
    margin-top: 16px;
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }

  .upload-preview {
    display: flex;
    align-items: center;

    .upload-image {
      width: 40px;
      height: 40px;
      border-radius: 4px;
      object-fit: cover;
    }

    .upload-file {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 40px;
      border: 1px dashed #dcdfe6;
      border-radius: 4px;

      .el-icon {
        font-size: 20px;
        color: #909399;
      }

      a {
        font-size: 12px;
        color: #409eff;
        margin-top: 4px;
      }
    }
  }

  .upload-preview-form {
    margin-top: 8px;

    .upload-image {
      width: 60px;
      height: 60px;
      border-radius: 4px;
      object-fit: cover;
    }
  }

  .upload-tip {
    font-size: 12px;
    color: #909399;
  }
  
  /* 新增：上传文件预览样式 */
  .upload-current-file {
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background-color: #f8f8f8;
    position: relative;
  }
  
  .image-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 100px; /* 确保有最小高度 */
    
    .preview-image {
      max-width: 100%;
      max-height: 120px;
      border-radius: 4px;
      object-fit: contain;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }
  
  .file-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    
    .el-icon {
      font-size: 24px;
      color: #909399;
      margin-right: 8px;
    }
    
    .file-link {
      color: #409eff;
      text-decoration: none;
      font-weight: bold;
      padding: 5px 0;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
  
  .preview-url {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
    word-break: break-all;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block; /* 修改为显示 */
  }
}
</style>
