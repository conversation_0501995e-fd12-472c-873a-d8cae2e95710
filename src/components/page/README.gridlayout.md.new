# GridLayout 页面分层示例说明文档

## 1. 设计目标
本示例演示如何基于 grid-layout-plus 实现灵活的页面布局，并采用"结构数据+对象数据"分层思想，便于高扩展性与低耦合。

- **结构数据**：页面结构、布局、方法、接口配置等，由父组件通过 store 传递，用于描述页面结构和行为。
- **对象数据**：页面实际业务数据（如商家列表），通过结构数据中定义的接口动态获取。

## 2. 目录结构
```
src/components/page/
  GridLayout.vue           // 页面主视图，负责渲染布局和业务组件
  gridLayoutStore.ts       // store 层，管理结构数据和对象数据
  gridLayoutService.ts     // service 层，处理接口请求与数据转换
  README.gridlayout.md     // 说明文档
src/services/
  structureDataCacheService.ts // 结构数据缓存服务
```

## 3. 架构设计

### 3.1 三层架构
模块采用清晰的三层架构设计：

1. **视图层(GridLayout.vue)**：
   - 负责渲染布局和业务组件
   - 监听用户交互事件（拖拽、缩放、点击等）
   - 维护视图状态（布局、样式等）

2. **状态管理层(gridLayoutStore.ts)**：
   - 管理结构数据和对象数据
   - 提供响应式状态
   - 处理业务逻辑
   - 管理step属性控制多步骤页面
   - 实现结构数据的本地持久化

3. **服务层(gridLayoutService.ts)**：
   - 处理API请求
   - 数据格式转换
   - 提供完整的CRUD操作
   - 错误处理与日志记录

### 3.2 设计模式

- **工厂模式**：通过createGridLayoutStore工厂函数创建GridLayoutStore实例
- **单例模式**：通过props传递store实例，确保整个页面使用同一个store
- **依赖注入**：使用Vue3的provide/inject机制在组件树中共享store和service
- **观察者模式**：通过Vue的响应式系统实现数据变化自动触发视图更新

## 4. 工作流程

### 4.1 初始化流程
```mermaid
flowchart TD
    A[父组件创建store] --> B[传递store给GridLayout]
    B --> C[GridLayout.initLayout]
    C --> D[parseGridItemsFromBackend]
    D --> E[过滤step显示]
    E --> F[渲染布局]
```

1. 父组件创建GridLayoutStore实例，传入serviceConfig和初始gridItems
2. 将store实例通过props传递给GridLayout组件
3. GridLayout组件挂载后，调用initLayout()方法初始化布局
4. 通过parseGridItemsFromBackend将后端数据转换为前端布局项
5. 根据当前step值过滤布局项
6. 渲染网格布局和业务组件

### 4.2 数据流转过程

1. **结构数据流**：
   - 从服务端获取页面配置和网格项配置
   - 通过store管理结构数据，并提供给GridLayout组件
   - GridLayout依据结构数据渲染布局和组件

2. **对象数据流**：
   - 通过服务层请求后端API获取具体业务数据
   - 存储在store的objectData中
   - 通过props传递给对应的业务组件

3. **事件流**：
   - 用户操作触发GridLayout组件事件
   - 事件通过$emit传递给父组件
   - 父组件执行相应的业务逻辑
   - 业务逻辑可能触发store更新
   - store更新导致视图刷新

### 4.3 多步骤页面控制流程

1. 父组件设置store.step值
2. GridLayout监听step变化，调用refreshLayout()
3. refreshLayout()调用filterItemsByStep()过滤布局项
4. 根据过滤结果更新layout
5. 布局自动更新，仅显示符合当前step条件的网格项

### 4.4 结构数据本地持久化流程

1. **初始化流程**：
   - 页面加载时，调用store.loadStructureData(frontendPath)
   - 首先尝试从本地缓存(localforage)获取结构数据
   - 如果有缓存且版本匹配，直接使用缓存数据
   - 如果无缓存或版本不匹配，从后端获取并更新缓存

2. **布局变更时自动持久化**：
   - GridLayout监听layout-change事件
   - 当布局变更时，自动调用store.saveLayoutToCache保存到本地

3. **版本控制机制**：
   - 利用systemInfo.configVersion作为全局配置版本
   - 利用PageData.version和PageData.version_hash作为页面配置版本
   - 当版本变化时自动更新缓存

4. **手动刷新机制**：
   - 提供store.refreshPageConfig方法，强制从后端重新获取数据

## 5. 结构数据示例
```ts
const structureData = {
  layout: [
    { x: 0, y: 0, w: 6, h: 4, i: 'merchantTable', component: 'MerchantTable', props: {}, step: 0 }
  ],
  apiConfig: {
    fetchList: '/api/merchant/list'
  }
}
```
- `layout`：描述页面布局及每个组件的类型、位置、大小、props 等。
- `apiConfig`：定义接口地址，供对象数据获取使用。
- `step`：(可选)步骤属性，用于控制组件在多步骤页面中的显示逻辑（详见第12节）。

## 6. 对象数据示例
```ts
const objectData = {
  merchants: [] // 商家列表
}
```

## 7. 数据流说明
- 父组件通过 store 传递结构数据，GridLayout.vue 负责渲染布局。
- 页面挂载后，store 根据结构数据中的接口配置自动拉取对象数据。
- 业务组件（如 MerchantTable）通过 props 获取对象数据进行渲染。

## 8. 主要代码说明
- **GridLayout.vue**：
  - 渲染 grid-layout-plus 布局，根据结构数据动态渲染业务组件。
  - 支持多组件注册，props 透传，布局变更事件，布局持久化（localStorage）。
  - 页面挂载后自动调用 store.fetchObjectData()。
  - 支持扩展更多 grid-layout-plus 事件（resize/drag等）。
  - 支持根据step值过滤显示网格项，实现多步骤页面功能。
  - 在布局变更时自动保存到缓存。
- **gridLayoutStore.ts**：
  - 管理 structureData（结构数据）、objectData（对象数据）。
  - fetchObjectData() 根据结构数据配置的接口获取对象数据。
  - 管理step属性，控制多步骤页面显示逻辑。
  - 提供结构数据缓存管理功能：loadStructureData、saveLayoutToCache、refreshPageConfig等。
- **gridLayoutService.ts**：
  - 统一处理接口请求与数据转换。
  - 提供完整的CRUD接口。
  - 支持自定义请求参数和响应处理。
- **structureDataCacheService.ts**：
  - 使用localforage实现结构数据的本地持久化存储
  - 提供版本控制和差异化更新机制
  - 支持多页面配置管理

## 9. 技术要点

### 9.1 动态组件渲染
通过Vue3的动态组件功能，根据网格项配置动态渲染不同的业务组件：
```html
<component 
  :is="components[item.component as keyof typeof components]" 
  v-bind="item.props"
/>
```

### 9.2 单例模式与依赖注入
```typescript
// 使用props接收store实例，实现单例模式
const props = defineProps({
  store: {
    type: Object,
    required: true
  }
});

// 使用provide/inject共享store和service
provide('gridLayoutStore', store);
provide('pageService', store.service);
```

### 9.3 多步骤页面控制
```typescript
// 根据当前step值过滤布局项
function filterItemsByStep(items: LayoutItem[]): LayoutItem[] {
  // 显示规则：
  // 1. 无step属性或step为0的项始终显示
  // 2. step为当前步骤的项显示
  // 3. step为数组时，包含当前步骤的项显示
  // ...
}
```

### 9.4 结构数据本地持久化
```typescript
// 加载结构数据，优先使用缓存
async loadStructureData(frontendPath: string, forceUpdate = false): Promise<void> {
  // 如果不强制更新，尝试从缓存获取
  if (!forceUpdate) {
    const cachedData = await getStructureData(frontendPath);
    if (cachedData) {
      this.structureData.value = parseStructureDataFromBackend(cachedData);
      return;
    }
  }
  
  // 缓存不存在或需要强制更新，从后端获取
  await this.fetchPageDataFromBackend(frontendPath);
}

// 保存布局更改到缓存
async saveLayoutToCache(frontendPath: string): Promise<void> {
  if (this.structureData.value) {
    await saveStructureData(frontendPath, this.structureData.value);
  }
}
```

## 10. 优化建议

### 10.1 性能优化
1. **懒加载业务组件**：使用`defineAsyncComponent`实现按需加载
   ```typescript
   const components = {
     GridItemComponent: defineAsyncComponent(() => import('./GridItemComponent.vue')),
     // ...
   };
   ```

2. **虚拟列表**：对于大量网格项，考虑使用虚拟滚动
3. **响应式优化**：对大型数据结构使用`shallowRef`/`shallowReactive`
4. **渲染优化**：使用`v-memo`减少不必要的重渲染
5. **缓存策略优化**：使用节流/防抖减少频繁缓存操作

### 10.2 代码优化
1. **统一事件处理**：封装事件处理逻辑，减少重复代码
2. **增强错误处理**：完善Service层的错误处理和重试机制
3. **类型定义完善**：补充TS类型定义，提高代码健壮性

### 10.3 功能增强
1. **布局持久化增强**：
   - 支持更精细的差异化缓存更新
   - 添加缓存清理和恢复默认布局功能
   - 实现缓存版本迁移机制
2. **拖拽体验优化**：添加辅助线和吸附功能
3. **组件通信增强**：使用事件总线实现跨组件通信
4. **状态历史记录**：支持操作历史和撤销/重做

## 11. 扩展建议
- 可通过扩展 layout、apiConfig、props 等字段，实现更复杂的页面结构与业务逻辑。
- 支持自定义组件、方法、事件等。
- 支持布局持久化、还原、重置等高级功能。
- 支持自定义样式、class、slot 等 grid-layout-plus 能力。
- 可以利用step属性实现分步表单、向导式操作界面等复杂交互。

## 12. 维护建议
- 每个文件均有详细中文注释，便于团队协作与后续维护。
- 建议结构数据与对象数据分离，保持高内聚低耦合。
- 推荐所有业务组件均注册到 components 注册表，便于统一管理和动态渲染。
- 对重要逻辑添加单元测试，提高代码质量和可靠性。
- 定期清理过期缓存，避免缓存数据过多占用客户端存储空间。

## 13. 常见问题
- 如遇"找不到模块 grid-layout-plus"报错，请先安装依赖：
  ```sh
  npm install grid-layout-plus
  ```
- 组件注册表未包含的业务组件不会被渲染，请补充注册。
- 布局持久化现使用localforage存储，支持IndexedDB/WebSQL/localStorage多种存储方式。
- 如需使用多步骤功能，确保正确设置每个网格项的step属性。
- 如果缓存数据与预期不符，可以使用store.refreshPageConfig强制刷新。

## 14. 结构数据配置示例

### 示例1：表单和表格混合布局
```js
const structureData = {
  layout: [
    {
      x: 0, y: 0, w: 6, h: 4, i: 'form1',
      type: 'plusform', // 通用结构数据
      title: '商家信息录入',
      content: {
        type: 'plusform', // 专用类型
        config: {
          formItems: [
            { label: '商家名称', prop: 'name', type: 'input', required: true },
            { label: '联系电话', prop: 'phone', type: 'input' },
            { label: '类型', prop: 'type', type: 'select', options: [ {label: '直营', value: 1}, {label: '加盟', value: 2} ] }
          ],
          submitApi: '/api/merchant/add',
          layout: 'horizontal',
          showReset: true
        }
      },
      draggable: true, // 通用结构数据
      resizable: true, // 通用结构数据
      style: { minHeight: '220px' }, // 通用结构数据
      step: 0 // 在所有步骤中都显示，默认为0
    },
    {
      x: 6, y: 0, w: 6, h: 8, i: 'table1',
      type: 'plustable',
      title: '商家列表',
      content: {
        type: 'plustable',
        config: {
          columns: [
            { label: '商家名称', prop: 'name' },
            { label: '联系电话', prop: 'phone' },
            { label: '类型', prop: 'type', formatter: (row) => row.type === 1 ? '直营' : '加盟' }
          ],
          dataApi: '/api/merchant/list',
          pagination: true,
          rowKey: 'id'
        }
      },
      draggable: true,
      resizable: true,
      style: { minHeight: '300px' },
      step: 1 // 仅在步骤1中显示
    }
  ],
  apiConfig: {
    fetchList: '/api/merchant/list'
  }
}
```

### 示例2：多类型混合布局
```js
const structureData = {
  layout: [
    {
      x: 0, y: 0, w: 4, h: 4, i: 'form2', static?: false,
      type: 'plusform',
      title: '基础信息',
      content: {
        type: 'plusform',
        config: { /* ...表单配置... */ }
      },
      style: { minHeight: '220px' },
      step: 0 // 在所有步骤中都显示
    },
    {
      x: 4, y: 0, w: 4, h: 4, i: 'table2',
      type: 'plustable',
      title: '数据表格',
      content: {
        type: 'plustable',
        config: { /* ...表格配置... */ }
      },
      step: 1 // 仅在步骤1中显示
    },
    {
      x: 8, y: 0, w: 4, h: 4, i: 'chart1',
      type: 'chart',
      title: '统计图表',
      content: {
        type: 'chart',
        config: { chartType: 'bar', dataApi: '/api/chart/data' }
      },
      step: 2 // 仅在步骤2中显示
    }
  ],
  apiConfig: {
    fetchList: '/api/xxx/list'
  }
}
```

> 说明：
> - layout 数组中的每一项都包含通用结构数据（如x, y, w, h, i, type, title, draggable, resizable, style等），以及 content 字段（type, config）作为专用结构数据。
> - content.type 决定渲染哪个业务组件，content.config 作为专用props传递给对应组件。
> - 通过灵活配置 layout，可实现表单、表格、图表等多类型混合页面。
> - step属性定义了布局项在多步骤页面中的显示逻辑，step=0表示在所有步骤中都显示。

## 15. 数据库结构与前端数据处理说明

在实际业务中，结构数据（页面布局与组件配置）是保存在数据库中的。数据库表结构设计如下：

- 页面数据表：存储页面的基础信息（如页面ID、名称、路由等）。
- grid组件数据表：存储每个grid组件的配置（如类型、参数、位置等）。
- 两者之间为多对多关系（即一个页面可包含多个grid组件，一个grid组件也可被多个页面复用）。

### 前端数据处理流程
1. 前端获取页面数据时，会自动追加 `griditems` 数组到页面数据对象中。
2. `griditems` 数组中的每个元素为一个 grid 组件的配置对象，数据库原始数据没有 `i` 字段，只有 `id` 字段。
3. 前端在处理 `griditems` 时，会将每个 griditem 的 `id` 字段转化为 `i` 字段，转换规则为：`i = 'grid-item-' + id`。
4. 这样，最终传递给 grid-layout-plus 组件的布局数据中，每个 griditem 都有唯一的 `i` 字段，满足组件库的唯一标识要求。

> 示例：
> 数据库中的 griditem： `{ id: 123, ... }`
> 转换后的 layout item： `{ i: 'grid-item-123', ... }`

## 16. 多步骤页面功能说明

GridLayout 模块支持多步骤页面功能，通过 `step` 属性控制网格项在不同步骤中的显示与隐藏。

### 核心实现

1. 每个布局项（gridItem）可以设置 `step` 属性，表示该项所属的步骤。
2. GridLayoutStore 提供 `step` 响应式变量，控制当前显示的步骤。
3. GridLayout 组件根据 store 中的 step 值过滤显示对应的布局项。

### 显示规则

- **step 为 0 或不设置**：在所有步骤中都显示（全局组件）
- **step 为具体数值**：仅在对应步骤显示
- **step 为数组**：在数组包含的任意步骤中显示

### 使用方法

1. 设置布局项的 step 属性：
   ```js
   { x: 0, y: 0, w: 4, h: 4, i: 'form1', step: 1 } // 仅在步骤1显示
   { x: 4, y: 0, w: 4, h: 4, i: 'chart1', step: [1, 3] } // 在步骤1和3显示
   { x: 8, y: 0, w: 4, h: 4, i: 'info1', step: 0 } // 在所有步骤显示
   ```

2. 通过控制 store.step 值切换当前步骤：
   ```js
   // 父组件中切换步骤
   function nextStep() {
     store.step.value++;
   }
   
   function prevStep() {
     store.step.value--;
   }
   
   function goToStep(step: number) {
     store.step.value = step;
   }
   ```

### 应用场景

- 分步表单
- 向导式操作界面
- 流程图展示
- 多阶段数据展示

## 17. 本地持久化功能说明

GridLayout模块现已支持结构数据的本地持久化功能，使用localforage实现。这大幅减少了对后端API的请求，提高了前端性能和用户体验。

### 核心功能

1. **自动缓存机制**：
   - 首次访问页面时，获取并缓存结构数据
   - 布局变更时，自动保存到缓存
   - 刷新页面时，优先使用缓存数据

2. **版本控制**：
   - 利用全局configVersion进行系统级控制
   - 利用页面级version和version_hash进行精确版本控制
   - 自动检测版本变化，实现智能缓存更新

3. **手动控制**：
   - 提供refreshPageConfig方法，支持手动强制刷新
   - 支持前端清除缓存操作

### 使用方法

```javascript
// 初始化时加载数据（优先使用缓存）
await store.loadStructureData(frontendPath);

// 布局变更时自动保存
// 在GridLayout.vue的onLayoutChange方法中已自动处理

// 手动强制刷新数据
await store.refreshPageConfig(frontendPath);
```

### 存储策略

- 使用localforage作为存储引擎，支持多种本地存储方式
- 按前端路径(frontendPath)存储不同页面的配置
- 存储内容包含完整的结构数据和版本信息
- 当版本变化时自动失效并更新缓存

---
如需进一步自定义或集成，请参阅 grid-layout-plus 官方文档：https://grid-layout-plus.netlify.app/
