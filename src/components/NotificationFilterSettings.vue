<template>
  <div class="notification-filter-settings">
    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <span>通知过滤设置</span>
          <el-button type="primary" size="small" @click="resetToDefaults">
            重置默认
          </el-button>
        </div>
      </template>

      <div class="settings-content">
        <el-alert
          title="说明"
          type="info"
          :closable="false"
          show-icon
          class="settings-alert"
        >
          <p>您可以选择哪些类型的消息会弹出通知。关闭的消息类型仍会在聊天窗口中显示，但不会弹出ElNotification通知。</p>
        </el-alert>

        <el-tabs v-model="activeTab" class="filter-tabs">
          <el-tab-pane label="系统通知" name="system">
            <div class="filter-category">
              <div
                v-for="filter in systemFilters"
                :key="filter.messageType + (filter.eventType || '')"
                class="filter-item"
              >
                <el-switch
                  v-model="filter.enabled"
                  @change="updateFilter(filter)"
                  class="filter-switch"
                />
                <div class="filter-info">
                  <div class="filter-title">{{ filter.description }}</div>
                  <div class="filter-detail">
                    {{ filter.messageType }}{{ filter.eventType ? `:${filter.eventType}` : '' }}
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="业务通知" name="business">
            <div class="filter-category">
              <div
                v-for="filter in businessFilters"
                :key="filter.messageType + (filter.eventType || '')"
                class="filter-item"
              >
                <el-switch
                  v-model="filter.enabled"
                  @change="updateFilter(filter)"
                  class="filter-switch"
                />
                <div class="filter-info">
                  <div class="filter-title">{{ filter.description }}</div>
                  <div class="filter-detail">
                    {{ filter.messageType }}{{ filter.eventType ? `:${filter.eventType}` : '' }}
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="社交通知" name="social">
            <div class="filter-category">
              <div
                v-for="filter in socialFilters"
                :key="filter.messageType + (filter.eventType || '')"
                class="filter-item"
              >
                <el-switch
                  v-model="filter.enabled"
                  @change="updateFilter(filter)"
                  class="filter-switch"
                />
                <div class="filter-info">
                  <div class="filter-title">{{ filter.description }}</div>
                  <div class="filter-detail">
                    {{ filter.messageType }}{{ filter.eventType ? `:${filter.eventType}` : '' }}
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="安全通知" name="security">
            <div class="filter-category">
              <div
                v-for="filter in securityFilters"
                :key="filter.messageType + (filter.eventType || '')"
                class="filter-item"
              >
                <el-switch
                  v-model="filter.enabled"
                  @change="updateFilter(filter)"
                  class="filter-switch"
                />
                <div class="filter-info">
                  <div class="filter-title">{{ filter.description }}</div>
                  <div class="filter-detail">
                    {{ filter.messageType }}{{ filter.eventType ? `:${filter.eventType}` : '' }}
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getNotificationFilter, type NotificationFilterRule } from '@/modules/chat/config/notificationFilter'

interface Props {
  userType: 'user' | 'merchant' | 'admin'
}

const props = defineProps<Props>()

const activeTab = ref('system')
const filters = ref<NotificationFilterRule[]>([])

// 按类别分组的过滤器
const systemFilters = computed(() => 
  filters.value.filter(f => f.category === 'system')
)

const businessFilters = computed(() => 
  filters.value.filter(f => f.category === 'business')
)

const socialFilters = computed(() => 
  filters.value.filter(f => f.category === 'social')
)

const securityFilters = computed(() => 
  filters.value.filter(f => f.category === 'security')
)

/**
 * 加载过滤器设置
 */
function loadFilters() {
  try {
    const notificationFilter = getNotificationFilter(props.userType)
    filters.value = notificationFilter.getAllFilters()
  } catch (error) {
    console.error('Failed to load notification filters:', error)
    ElMessage.error('加载通知设置失败')
  }
}

/**
 * 更新过滤器设置
 */
function updateFilter(filter: NotificationFilterRule) {
  try {
    const notificationFilter = getNotificationFilter(props.userType)
    notificationFilter.updateFilter(filter.messageType, filter.eventType, filter.enabled)
    ElMessage.success('设置已保存')
  } catch (error) {
    console.error('Failed to update notification filter:', error)
    ElMessage.error('保存设置失败')
  }
}

/**
 * 重置为默认设置
 */
function resetToDefaults() {
  try {
    const notificationFilter = getNotificationFilter(props.userType)
    notificationFilter.resetToDefaults()
    loadFilters()
    ElMessage.success('已重置为默认设置')
  } catch (error) {
    console.error('Failed to reset notification filters:', error)
    ElMessage.error('重置设置失败')
  }
}

onMounted(() => {
  loadFilters()
})
</script>

<style scoped>
.notification-filter-settings {
  max-width: 800px;
  margin: 0 auto;
}

.settings-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.settings-content {
  padding: 0;
}

.settings-alert {
  margin-bottom: 20px;
}

.filter-tabs {
  margin-top: 20px;
}

.filter-category {
  padding: 10px 0;
}

.filter-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.filter-item:last-child {
  border-bottom: none;
}

.filter-switch {
  margin-right: 16px;
  flex-shrink: 0;
}

.filter-info {
  flex: 1;
}

.filter-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.filter-detail {
  font-size: 12px;
  color: #909399;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

:deep(.el-tabs__content) {
  padding-top: 20px;
}

:deep(.el-alert__content) {
  padding-right: 0;
}
</style>
