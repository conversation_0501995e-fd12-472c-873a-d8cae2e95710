<!--
  用户聊天窗口组件
  为用户layout提供浮动的聊天窗口功能
-->
<template>
  <div 
    v-if="visible" 
    class="user-chat-window"
    :class="{
      'minimized': minimized,
      'maximized': maximized
    }"
  >
    <!-- 聊天窗口头部 -->
    <div class="chat-header" @dblclick="toggleMaximize">
      <div class="chat-title">
        <el-icon class="chat-icon"><ChatDotRound /></el-icon>
        <span>客服聊天</span>
        <el-badge 
          v-if="unreadCount > 0" 
          :value="unreadCount" 
          :max="99" 
          class="header-badge"
        />
      </div>
      
      <div class="chat-controls">
        <el-tooltip content="最小化" placement="top">
          <el-button 
            link 
            size="small" 
            @click="handleMinimize"
            class="control-btn"
          >
            <el-icon><Minus /></el-icon>
          </el-button>
        </el-tooltip>
        
        <el-tooltip :content="maximized ? '还原' : '最大化'" placement="top">
          <el-button 
            link 
            size="small" 
            @click="toggleMaximize"
            class="control-btn"
          >
            <el-icon><FullScreen v-if="!maximized" /><Aim v-else /></el-icon>
          </el-button>
        </el-tooltip>
        
        <el-tooltip content="关闭" placement="top">
          <el-button 
            link 
            size="small" 
            @click="handleClose"
            class="control-btn close-btn"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>
    
    <!-- 聊天窗口内容 -->
    <div v-if="!minimized" class="chat-content">
      <ChatWindow
        :key="chatKey"
        @unread-change="handleUnreadChange"
        class="embedded-chat"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue';
import { ChatDotRound, Minus, FullScreen, Aim, Close } from '@element-plus/icons-vue';
import { ChatWindow } from '@/modules/chat/components';

// Props
interface Props {
  visible: boolean;
  minimized?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  minimized: false
});

// Emits
const emit = defineEmits<{
  close: [];
  minimize: [];
  restore: [];
  'unread-change': [count: number];
}>();

// 状态
const maximized = ref(false);
const unreadCount = ref(0);
const chatKey = ref(0);

// 计算属性 - 已移除未使用的windowClass

// 方法
function handleClose() {
  emit('close');
}

function handleMinimize() {
  emit('minimize');
}

function handleRestore() {
  emit('restore');
}

function toggleMaximize() {
  if (props.minimized) {
    handleRestore();
  } else {
    maximized.value = !maximized.value;
  }
}

function handleUnreadChange(count: number) {
  unreadCount.value = count;
  emit('unread-change', count);
}

// 监听visible变化，重新渲染聊天组件
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    chatKey.value++;
  }
});

// 键盘事件处理
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Escape' && props.visible && !props.minimized) {
    handleClose();
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<style scoped>
.user-chat-window {
  position: fixed;
  top: 60px;
  right: 20px;
  width: 400px;
  height: 600px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  overflow: hidden;
}

.user-chat-window.minimized {
  height: 50px;
  width: 200px;
  bottom: 20px;
  top: auto;
}

.user-chat-window.maximized {
  top: 60px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  width: auto;
  height: auto;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  cursor: move;
  user-select: none;
  border-radius: 12px 12px 0 0;
  min-height: 26px;
}

.chat-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
}

.chat-icon {
  font-size: 16px;
}

.header-badge {
  margin-left: 4px;
}

.chat-controls {
  display: flex;
  gap: 4px;
}

.control-btn {
  color: rgba(255, 255, 255, 0.8);
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.control-btn:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.close-btn:hover {
  background: #ef4444;
  color: white;
}

.chat-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.embedded-chat {
  flex: 1;
  border: none;
  border-radius: 0;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-chat-window {
    left: 10px;
    right: 10px;
    bottom: 10px;
    width: auto;
    height: 500px;
  }
  
  .user-chat-window.maximized {
    top: 70px;
    left: 10px;
    right: 10px;
    bottom: 10px;
  }
  
  .user-chat-window.minimized {
    width: 150px;
    right: 10px;
  }
}

@media (max-width: 480px) {
  .user-chat-window {
    height: 450px;
  }
  
  .user-chat-window.maximized {
    top: 60px;
  }
}

/* 动画效果 */
.user-chat-window {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 最小化状态下的样式 */
.user-chat-window.minimized .chat-header {
  border-radius: 12px;
}

.user-chat-window.minimized .chat-title {
  font-size: 13px;
}

.user-chat-window.minimized .control-btn {
  padding: 2px;
}
</style>
