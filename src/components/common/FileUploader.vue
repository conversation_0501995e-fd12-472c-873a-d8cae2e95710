<template>
  <div class="file-uploader">
    <!-- 上传区域 -->
    <div 
      v-if="uploadStyle === 'default'"
      class="upload-area"
      :class="{ 'is-dragover': isDragover, 'is-disabled': disabled }"
      @dragover.prevent="handleDragover"
      @dragleave.prevent="handleDragleave"
      @drop.prevent="handleDrop"
      @click="handleClick"
    >
      <input 
        ref="fileInput"
        type="file"
        :accept="accept"
        :multiple="multiple"
        class="file-input"
        @change="handleFileChange"
      >
      
      <div class="upload-content">
        <el-icon class="upload-icon"><Upload /></el-icon>
        <div class="upload-text">
          <slot name="tip">
            <p>点击或拖拽文件到此区域上传</p>
            <p v-if="fileLimit" class="upload-limit">支持上传 {{ fileLimit }} 个文件</p>
            <p v-if="sizeLimit" class="upload-limit">文件大小不超过 {{ formatFileSize(sizeLimit) }}</p>
            <p v-if="accept" class="upload-limit">支持的文件类型: {{ formatAccept(accept) }}</p>
          </slot>
        </div>
      </div>
    </div>

    <!-- 按钮样式上传区域 -->
    <div v-else-if="uploadStyle === 'button'" class="upload-button-container">
      <input 
        ref="fileInput"
        type="file"
        :accept="accept"
        :multiple="multiple"
        class="file-input"
        @change="handleFileChange"
      >
      <el-button 
        :type="buttonType" 
        :size="buttonSize" 
        :disabled="disabled"
        @click="handleClick"
      >
        <el-icon v-if="buttonIcon" class="upload-button-icon"><component :is="buttonIcon" /></el-icon>
        <span>{{ buttonText }}</span>
      </el-button>
    </div>

    <!-- 链接样式上传区域 -->
    <div v-else-if="uploadStyle === 'link'" class="upload-link-container">
      <input 
        ref="fileInput"
        type="file"
        :accept="accept"
        :multiple="multiple"
        class="file-input"
        @change="handleFileChange"
      >
      <el-link 
        :type="linkType" 
        :disabled="disabled"
        @click="handleClick"
      >
        <el-icon v-if="linkIcon" class="upload-link-icon"><component :is="linkIcon" /></el-icon>
        <span>{{ linkText }}</span>
      </el-link>
    </div>

    <!-- 文件列表 -->
    <div v-if="showFileList && fileList.length > 0" class="file-list">
      <div 
        v-for="(file, index) in fileList" 
        :key="index"
        class="file-item"
        :class="{ 'is-error': file.status === 'error' }"
      >
        <div class="file-info">
          <el-icon class="file-icon"><Document /></el-icon>
          <div class="file-name" :title="file.name">{{ file.name }}</div>
          <div class="file-size">{{ formatFileSize(file.size) }}</div>
        </div>
        
        <div class="file-status">
          <!-- 上传中 -->
          <template v-if="file.status === 'uploading'">
            <el-progress 
              :percentage="file.percentage" 
              :stroke-width="4" 
              class="file-progress"
            />
          </template>
          
          <!-- 上传成功 -->
          <template v-else-if="file.status === 'success'">
            <el-icon class="success-icon"><CircleCheck /></el-icon>
            <el-button 
              v-if="showPreview && isPreviewable(file.raw)"
              link
              @click="handlePreview(file)"
            >
              预览
            </el-button>
          </template>
          
          <!-- 上传失败 -->
          <template v-else-if="file.status === 'error'">
            <el-tooltip :content="file.message || '上传失败'" placement="top">
              <el-icon class="error-icon"><CircleClose /></el-icon>
            </el-tooltip>
            <el-button 
              link
              @click="handleRetry(file, index)"
            >
              重试
            </el-button>
          </template>
        </div>
        
        <!-- 删除按钮 -->
        <el-button 
          v-if="!disabled && file.status !== 'uploading'"
          link
          class="delete-btn"
          @click="handleRemove(file, index)"
        >
          <el-icon><Delete /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="文件预览"
      width="50%"
      destroy-on-close
    >
      <div class="preview-container">
        <img v-if="previewFile && isImage(previewFile.raw)" :src="previewUrl" class="preview-image">
        <div v-else class="preview-fallback">
          <el-icon class="preview-icon"><Document /></el-icon>
          <p>{{ previewFile?.name }}</p>
          <el-button type="primary" @click="downloadFile(previewFile)">下载文件</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Upload, Document, CircleCheck, CircleClose, Delete } from '@element-plus/icons-vue';
import { post } from '@/utils/request';

// 定义文件状态类型
type FileStatus = 'ready' | 'uploading' | 'success' | 'error';

// 定义上传文件类型
interface UploadFile {
  id?: number | string;
  name: string;
  size: number;
  percentage?: number;
  status: FileStatus;
  raw: File;
  url?: string;
  message?: string;
  file_usage?: string;
  mime_type?: string;
}

// 定义组件属性
const props = defineProps({
  // 文件上传地址
  action: {
    type: String,
    default: '/v1/admin/upload'
  },
  // 是否支持多文件上传
  multiple: {
    type: Boolean,
    default: false
  },
  // 接受的文件类型
  accept: {
    type: String,
    default: ''
  },
  // 文件数量限制
  fileLimit: {
    type: Number,
    default: 0
  },
  // 文件大小限制（KB）
  sizeLimit: {
    type: Number,
    default: 0
  },
  // 是否显示文件列表
  showFileList: {
    type: Boolean,
    default: true
  },
  // 是否显示预览按钮
  showPreview: {
    type: Boolean,
    default: true
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 文件用途
  fileUsage: {
    type: String,
    default: ''
  },
  // 是否匿名上传
  isAnonymous: {
    type: Boolean,
    default: false
  },
  // 自动上传
  autoUpload: {
    type: Boolean,
    default: true
  },
  // 初始文件列表
  initialFiles: {
    type: Array,
    default: () => []
  },
  // 上传区域样式类型：default(默认拖拽区域)、button(按钮)、link(链接)
  uploadStyle: {
    type: String,
    default: 'default',
    validator: (value: string) => ['default', 'button', 'link'].includes(value)
  },
  // 按钮类型 (当uploadStyle为button时有效)
  buttonType: {
    type: String,
    default: 'primary',
    validator: (value: string) => ['primary', 'success', 'warning', 'danger', 'info', 'text'].includes(value)
  },
  // 按钮大小 (当uploadStyle为button时有效)
  buttonSize: {
    type: String,
    default: 'default',
    validator: (value: string) => ['large', 'default', 'small'].includes(value)
  },
  // 按钮文本 (当uploadStyle为button时有效)
  buttonText: {
    type: String,
    default: '上传文件'
  },
  // 按钮图标 (当uploadStyle为button时有效)
  buttonIcon: {
    type: String,
    default: 'Upload'
  },
  // 链接类型 (当uploadStyle为link时有效)
  linkType: {
    type: String,
    default: 'primary',
    validator: (value: string) => ['primary', 'success', 'warning', 'danger', 'info', 'default'].includes(value)
  },
  // 链接文本 (当uploadStyle为link时有效)
  linkText: {
    type: String,
    default: '点击上传文件'
  },
  // 链接图标 (当uploadStyle为link时有效)
  linkIcon: {
    type: String,
    default: 'Upload'
  },
  // 请求头
  headers: {
    type: Object,
    default: () => ({})
  },
  // 额外参数
  data: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
const emit = defineEmits([
  'update:fileList',
  'change',
  'success',
  'error',
  'remove',
  'exceed',
  'preview'
]);

// 内部状态
const fileInput = ref<HTMLInputElement | null>(null);
const isDragover = ref(false);
const fileList = ref<UploadFile[]>([]);
const previewVisible = ref(false);
const previewFile = ref<UploadFile | null>(null);
const previewUrl = ref('');

// 初始化文件列表
onMounted(() => {
  if (props.initialFiles && props.initialFiles.length > 0) {
    fileList.value = props.initialFiles.map((file: any) => ({
      id: file.id,
      name: file.filename || file.name,
      size: file.size || 0,
      status: 'success' as FileStatus,
      raw: new File([], file.filename || file.name, { type: file.mime_type }),
      url: file.url,
      mime_type: file.mime_type,
      file_usage: file.file_usage
    }));
  }
});

// 监听文件列表变化
watch(fileList, (newVal) => {
  emit('update:fileList', newVal);
  emit('change', newVal);
}, { deep: true });

// 处理点击上传区域
const handleClick = () => {
  if (props.disabled) return;
  fileInput.value?.click();
};

// 处理拖拽进入
const handleDragover = () => {
  if (props.disabled) return;
  isDragover.value = true;
};

// 处理拖拽离开
const handleDragleave = () => {
  isDragover.value = false;
};

// 处理文件拖放
const handleDrop = (e: DragEvent) => {
  if (props.disabled) return;
  isDragover.value = false;
  const files = e.dataTransfer?.files;
  if (!files) return;
  handleFiles(Array.from(files));
};

// 处理文件选择
const handleFileChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  const files = target.files;
  if (!files) return;
  handleFiles(Array.from(files));
  // 重置input，以便能够选择相同的文件
  target.value = '';
};

// 处理文件
const handleFiles = (files: File[]) => {
  // 检查文件数量限制
  if (props.fileLimit > 0 && fileList.value.length + files.length > props.fileLimit) {
    ElMessage.warning(`最多只能上传 ${props.fileLimit} 个文件`);
    emit('exceed', files, fileList.value);
    return;
  }

  // 处理每个文件
  files.forEach(file => {
    // 检查文件大小 (sizeLimit是KB，需要转换为字节)
    if (props.sizeLimit > 0 && file.size > props.sizeLimit * 1024) {
      ElMessage.warning(`文件 ${file.name} 大小超过限制 ${formatFileSize(props.sizeLimit * 1024)}`);
      return;
    }

    // 添加到文件列表
    const uploadFile: UploadFile = {
      name: file.name,
      size: file.size,
      status: 'ready',
      raw: file,
      percentage: 0
    };

    fileList.value.push(uploadFile);

    // 自动上传
    if (props.autoUpload) {
      uploadFile.status = 'uploading';
      uploadFile.percentage = 0;
      uploadFile.message = '';
      uploadFile.url = '';
      uploadFile.id = undefined;
      uploadFile.mime_type = file.type;
      uploadFile.file_usage = props.fileUsage;

      handleUploadFile(uploadFile, fileList.value.length - 1);
    }
  });
};

// 上传文件
const handleUploadFile = async (file: UploadFile, index: number) => {
  const formData = new FormData();
  formData.append('file', file.raw);
  
  if (props.fileUsage) {
    formData.append('file_usage', props.fileUsage);
  }
  
  formData.append('is_anonymous', props.isAnonymous ? 'true' : 'false');
  
  // 添加额外参数
  Object.keys(props.data).forEach(key => {
    formData.append(key, props.data[key]);
  });

  try {
    // 使用axios上传文件
    const response: any = await post(props.action, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        ...props.headers
      },
      onUploadProgress: (progressEvent: any) => {
        if (progressEvent.total) {
          const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          fileList.value[index].percentage = percentage;
        }
      }
    });

    // 更新文件状态
    fileList.value[index].status = 'success';
    fileList.value[index].id = response.id;
    fileList.value[index].url = response.url;
    fileList.value[index].mime_type = response.mime_type;

    emit('success', response, fileList.value[index], fileList.value);
  } catch (error: any) {
    // 处理上传失败
    fileList.value[index].status = 'error';
    fileList.value[index].message = error.message || '上传失败';

    emit('error', error, fileList.value[index], fileList.value);
  }
};

// 处理重试上传
const handleRetry = (file: UploadFile, index: number) => {
  file.status = 'uploading';
  file.percentage = 0;
  file.message = '';
  handleUploadFile(file, index);
};

// 处理移除文件
const handleRemove = (file: UploadFile, index: number) => {
  fileList.value.splice(index, 1);
  emit('remove', file, fileList.value);
};

// 处理预览文件
const handlePreview = (file: UploadFile) => {
  previewFile.value = file;
  if (file.url) {
    previewUrl.value = file.url;
  } else if (file.raw && isImage(file.raw)) {
    previewUrl.value = URL.createObjectURL(file.raw);
  }
  previewVisible.value = true;
  emit('preview', file);
};

// 下载文件
const downloadFile = (file: UploadFile | null) => {
  if (!file || !file.url) return;
  
  const link = document.createElement('a');
  link.href = file.url;
  link.download = file.name;
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 检查文件是否可预览
const isPreviewable = (file: File | null) => {
  if (!file) return false;
  return isImage(file);
};

// 检查是否为图片
const isImage = (file: File | null) => {
  if (!file) return false;
  return file.type.startsWith('image/');
};

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }
};

// 格式化accept属性
const formatAccept = (accept: string) => {
  if (!accept) return '';
  return accept.split(',').map(type => {
    type = type.trim();
    if (type.startsWith('.')) {
      return type.substring(1).toUpperCase();
    } else if (type.includes('/')) {
      const parts = type.split('/');
      if (parts[1] === '*') {
        return parts[0].toUpperCase();
      } else {
        return parts[1].toUpperCase();
      }
    }
    return type.toUpperCase();
  }).join(', ');
};

// 暴露方法
defineExpose({
  fileList,
  submit: () => {
    fileList.value.forEach((file, index) => {
      if (file.status === 'ready') {
        file.status = 'uploading';
        handleUploadFile(file, index);
      }
    });
  },
  clearFiles: () => {
    fileList.value = [];
  }
});
</script>

<style scoped lang="scss">
.file-uploader {
  width: 100%;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  padding: 10px;
  transition: all 0.3s;
  
  &:hover {
    border-color: $primary-color;
  }
  
  &.is-dragover {
    background-color: rgba(37, 99, 235, 0.05);
    border-color: $primary-color;
  }
  
  &.is-disabled {
    cursor: not-allowed;
    background-color: #f5f5f5;
    border-color: #d9d9d9;
  }
}

.upload-button-container {
  display: inline-block;
  position: relative;
  margin-bottom: 8px;
}

.upload-link-container {
  display: inline-block;
  position: relative;
  margin-bottom: 8px;
}

.upload-button-icon,
.upload-link-icon {
  margin-right: 4px;
  vertical-align: middle;
}

.file-input {
  display: none;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 28px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.upload-text {
  text-align: center;
  color: #606266;
  
  p {
    margin: 4px 0;
  }
  
  .upload-limit {
    font-size: 12px;
    color: #909399;
  }
}

.file-list {
  margin-top: 16px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: #f8f8f8;
  transition: all 0.3s;
  
  &:hover {
    background-color: #f0f0f0;
  }
  
  &.is-error {
    background-color: #fff0f0;
  }
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.file-icon {
  font-size: 20px;
  color: #909399;
  margin-right: 8px;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.file-size {
  font-size: 12px;
  color: #909399;
  margin-right: 16px;
}

.file-status {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.file-progress {
  width: 100px;
}

.success-icon {
  color: #67c23a;
  font-size: 16px;
  margin-right: 8px;
}

.error-icon {
  color: #f56c6c;
  font-size: 16px;
  margin-right: 8px;
}

.delete-btn {
  padding: 2px;
}

.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 400px;
}

.preview-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.preview-icon {
  font-size: 48px;
  color: #909399;
  margin-bottom: 16px;
}
</style>