# 文件上传组件使用说明

## 组件介绍

`FileUploader` 是一个功能完整的文件上传组件，基于Element Plus UI框架开发，支持单文件和多文件上传、拖拽上传、文件预览、上传进度显示等功能。组件与后端API完全对接，提供了完整的文件上传、管理解决方案。

## 安装和引入

组件已集成到项目中，无需额外安装。您可以直接从组件库中引入：

```vue
<script setup>
import { FileUploader } from '@/components/common';
</script>
```

## 基本用法

### 单文件上传

```vue
<template>
  <FileUploader
    action="/v1/admin/upload"
    :file-limit="1"
    :size-limit="5 * 1024 * 1024"
    accept=".jpg,.jpeg,.png,.gif"
    file-usage="avatar"
    @success="handleUploadSuccess"
  />
</template>

<script setup>
import { FileUploader } from '@/components/common';
import { ref } from 'vue';

const handleUploadSuccess = (response, file, fileList) => {
  console.log('上传成功:', response);
  // 可以在这里获取文件URL等信息
  const fileUrl = response.url;
};
</script>
```

### 多文件上传

```vue
<template>
  <FileUploader
    action="/v1/admin/upload"
    :multiple="true"
    :file-limit="5"
    :size-limit="10 * 1024 * 1024"
    accept=".pdf,.doc,.docx,.xls,.xlsx"
    file-usage="document"
    @success="handleUploadSuccess"
  />
</template>
```

### 不同样式的上传组件

组件支持三种不同的样式：默认样式（拖拽上传区域）、按钮样式和链接样式。

#### 默认样式（拖拽上传区域）

```vue
<template>
  <FileUploader
    action="/v1/admin/upload"
    upload-style="default"
    @success="handleUploadSuccess"
  />
</template>
```

#### 按钮样式

```vue
<template>
  <FileUploader
    action="/v1/admin/upload"
    upload-style="button"
    button-type="primary"
    button-text="上传文件"
    button-icon="Upload"
    button-size="default"
    @success="handleUploadSuccess"
  />
</template>
```

#### 链接样式

```vue
<template>
  <FileUploader
    action="/v1/admin/upload"
    upload-style="link"
    link-type="primary"
    link-text="点击上传文件"
    link-icon="Upload"
    @success="handleUploadSuccess"
  />
</template>
```

### 自定义上传提示

```vue
<template>
  <FileUploader action="/v1/admin/upload">
    <template #tip>
      <p>点击或拖拽图片到此处上传</p>
      <p>仅支持JPG、PNG格式，文件大小不超过2MB</p>
    </template>
  </FileUploader>
</template>
```

### 手动上传模式

```vue
<template>
  <FileUploader
    ref="uploaderRef"
    action="/v1/admin/upload"
    :auto-upload="false"
  />
  <el-button type="primary" @click="submitUpload">开始上传</el-button>
  <el-button @click="clearFiles">清空文件列表</el-button>
</template>

<script setup>
import { ref } from 'vue';
import { FileUploader } from '@/components/common';

const uploaderRef = ref(null);

const submitUpload = () => {
  uploaderRef.value.submit();
};

const clearFiles = () => {
  uploaderRef.value.clearFiles();
};
</script>
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
| ----- | ---- | ----- | ---- |
| action | String | '/v1/admin/upload' | 上传的接口地址 |
| multiple | Boolean | false | 是否支持多文件上传 |
| accept | String | '' | 接受的文件类型，例如 '.jpg,.png' |
| fileLimit | Number | 0 | 文件数量限制，0表示不限制 |
| sizeLimit | Number | 0 | 文件大小限制（字节），0表示不限制 |
| showFileList | Boolean | true | 是否显示已上传文件列表 |
| showPreview | Boolean | true | 是否显示预览按钮 |
| disabled | Boolean | false | 是否禁用上传功能 |
| fileUsage | String | '' | 文件用途，对应后端 file_usage 参数 |
| isAnonymous | Boolean | false | 是否匿名上传 |
| autoUpload | Boolean | true | 是否自动上传 |
| initialFiles | Array | [] | 初始文件列表 |
| uploadStyle | String | 'default' | 上传区域样式类型：default(默认拖拽区域)、button(按钮)、link(链接) |
| buttonType | String | 'primary' | 按钮类型，当uploadStyle为button时有效，可选值：primary、success、warning、danger、info、text |
| buttonSize | String | 'default' | 按钮大小，当uploadStyle为button时有效，可选值：large、default、small |
| buttonText | String | '上传文件' | 按钮文本，当uploadStyle为button时有效 |
| buttonIcon | String | 'Upload' | 按钮图标，当uploadStyle为button时有效，使用Element Plus图标名称 |
| linkType | String | 'primary' | 链接类型，当uploadStyle为link时有效，可选值：primary、success、warning、danger、info、default |
| linkText | String | '点击上传文件' | 链接文本，当uploadStyle为link时有效 |
| linkIcon | String | 'Upload' | 链接图标，当uploadStyle为link时有效，使用Element Plus图标名称 |
| headers | Object | {} | 上传请求头 |
| data | Object | {} | 上传时附带的额外参数 |

## 组件事件

| 事件名 | 说明 | 回调参数 |
| ----- | ---- | -------- |
| change | 文件列表变化时触发 | (fileList: UploadFile[]) |
| success | 文件上传成功时触发 | (response: any, file: UploadFile, fileList: UploadFile[]) |
| error | 文件上传失败时触发 | (error: Error, file: UploadFile, fileList: UploadFile[]) |
| remove | 文件被移除时触发 | (file: UploadFile, fileList: UploadFile[]) |
| exceed | 文件数量超出限制时触发 | (files: File[], fileList: UploadFile[]) |
| preview | 点击文件预览时触发 | (file: UploadFile) |

## 组件插槽

| 插槽名 | 说明 |
| ----- | ---- |
| tip | 自定义上传区域的提示文本 |

## 组件方法

| 方法名 | 说明 | 参数 |
| ----- | ---- | ---- |
| submit | 手动上传文件列表中的文件 | 无 |
| clearFiles | 清空文件列表 | 无 |

## 类型定义

```typescript
// 文件状态类型
type FileStatus = 'ready' | 'uploading' | 'success' | 'error';

// 上传文件类型
interface UploadFile {
  id?: number | string;
  name: string;
  size: number;
  percentage?: number;
  status: FileStatus;
  raw: File;
  url?: string;
  message?: string;
  file_usage?: string;
  mime_type?: string;
}
```

## 注意事项

1. 文件上传大小和类型限制需要与后端配置保持一致
2. 匿名上传模式下，不需要用户登录即可上传文件
3. 文件用途(fileUsage)参数用于后端区分不同用途的文件，如头像、商品图片等
4. 初始文件列表(initialFiles)可用于编辑场景，显示已上传的文件

## 与后端API对接说明

组件已完全对接后端API，主要使用以下接口：

- 文件上传：`/v1/admin/upload`
- 文件删除：通过API模块中的`deleteFile`方法调用

更多API详情请参考`src/modules/admin/api/file.ts`文件。