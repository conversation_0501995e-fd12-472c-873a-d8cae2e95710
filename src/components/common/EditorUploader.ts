/**
 * @description EditorUploader - 富文本编辑器文件上传工具
 * <AUTHOR> AI
 * @date 2025-04-23
 */
import { ElMessage } from 'element-plus';
import { post } from '@/utils/request';

// 图片插入函数类型
export type InsertImageFnType = (url: string, alt?: string, href?: string) => void;

// 视频插入函数类型
export type InsertVideoFnType = (url: string, poster?: string) => void;

/**
 * 富文本编辑器上传工具类
 * 专门用于 wangEditor 的文件上传功能
 */
export class EditorUploader {
  /**
   * 上传图片
   * @param file 图片文件
   * @param insertFn 编辑器图片插入函数
   */
  static async uploadImage(file: File, insertFn: InsertImageFnType) {
    try {
      // 调试日志
      console.log('EditorUploader - 上传图片:', file.name, file.size, file.type);
      
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      formData.append('file_usage', 'editor_image');
      
      // 发送上传请求，使用 post 函数而不是 axios
      const response: any = await post('/v1/admin/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      console.log('EditorUploader - 图片上传响应:', response);
      
      // 处理响应 - post 直接返回 data
      if (response) {
        const url = response.file_url || '';
        // 插入图片到编辑器
        insertFn(url, file.name, url);
        ElMessage.success('图片上传成功');
      } else {
        // 上传失败
        ElMessage.error('图片上传失败: 未获取到有效的响应');
      }
    } catch (error: any) {
      console.error('EditorUploader - 图片上传出错:', error);
      ElMessage.error('图片上传出错: ' + (error.message || '未知错误'));
    }
  }
  
  /**
   * 上传视频
   * @param file 视频文件
   * @param insertFn 编辑器视频插入函数
   */
  static async uploadVideo(file: File, insertFn: InsertVideoFnType) {
    try {
      // 调试日志
      console.log('EditorUploader - 上传视频:', file.name, file.size, file.type);
      
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      formData.append('file_usage', 'editor_video');
      
      // 发送上传请求，使用 post 函数而不是 axios
      const response: any = await post('/v1/admin/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      console.log('EditorUploader - 视频上传响应:', response);
      
      // 处理响应 - post 直接返回 data
      if (response) {
        const url = response.file_url || '';
        // 插入视频到编辑器
        insertFn(url);
        ElMessage.success('视频上传成功');
      } else {
        // 上传失败
        ElMessage.error('视频上传失败: 未获取到有效的响应');
      }
    } catch (error: any) {
      console.error('EditorUploader - 视频上传出错:', error);
      ElMessage.error('视频上传出错: ' + (error.message || '未知错误'));
    }
  }
}

export default EditorUploader;
