<!--
 * 天地图组件使用示例
 * 作者: AI Assistant
 * 日期: 2025-01-27
 * 版本: 1.0.0
 * 描述: 展示天地图组件的各种使用方法和功能
-->

<template>
  <div class="tianditu-example">
    <div class="example-header">
      <h2>天地图组件示例</h2>
      <p>展示天地图组件的各种功能和使用方法</p>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="control-group">
        <label>API密钥:</label>
        <input 
          v-model="apiKey" 
          type="text" 
          placeholder="请输入天地图API密钥"
          class="api-key-input"
        />
      </div>
      
      <div class="control-group">
        <label>地图类型:</label>
        <select v-model="mapType" class="map-type-select">
          <option value="normal">普通地图</option>
          <option value="satellite">卫星地图</option>
          <option value="terrain">地形图</option>
        </select>
      </div>
      
      <div class="control-group">
        <label>缩放级别:</label>
        <input 
          v-model.number="zoom" 
          type="range" 
          min="1" 
          max="18" 
          class="zoom-slider"
        />
        <span class="zoom-value">{{ zoom }}</span>
      </div>
      
      <div class="control-group">
        <button @click="addRandomMarker" class="btn btn-primary">
          添加随机标注
        </button>
        <button @click="clearAllMarkers" class="btn btn-secondary">
          清除标注
        </button>
        <button @click="fitToMarkers" class="btn btn-info">
          适应标注
        </button>
      </div>
    </div>

    <!-- 地图容器 -->
    <div class="map-container">
      <TiandituMap
        ref="mapRef"
        :api-key="apiKey"
        :longitude="center.lng"
        :latitude="center.lat"
        :zoom="zoom"
        :map-type="mapType"
        :markers="markers"
        :show-controls="true"
        width="100%"
        height="500px"
        @map-ready="onMapReady"
        @map-click="onMapClick"
        @marker-click="onMarkerClick"
      />
    </div>

    <!-- 信息面板 -->
    <div class="info-panel">
      <div class="info-section">
        <h3>地图信息</h3>
        <p><strong>中心点:</strong> {{ formatCoordinate(center.lng, center.lat) }}</p>
        <p><strong>缩放级别:</strong> {{ zoom }}</p>
        <p><strong>地图类型:</strong> {{ mapTypeNames[mapType] }}</p>
        <p><strong>标注数量:</strong> {{ markers.length }}</p>
      </div>
      
      <div class="info-section">
        <h3>点击信息</h3>
        <p v-if="clickInfo"><strong>点击位置:</strong> {{ clickInfo }}</p>
        <p v-else class="placeholder">点击地图查看坐标信息</p>
      </div>
      
      <div class="info-section">
        <h3>标注列表</h3>
        <div v-if="markers.length > 0" class="marker-list">
          <div 
            v-for="(marker, index) in markers" 
            :key="marker.id || index"
            class="marker-item"
            @click="focusMarker(marker)"
          >
            <div class="marker-title">{{ marker.title || `标注 ${index + 1}` }}</div>
            <div class="marker-coord">{{ formatCoordinate(marker.lng, marker.lat) }}</div>
            <button @click.stop="removeMarker(index)" class="remove-btn">删除</button>
          </div>
        </div>
        <p v-else class="placeholder">暂无标注点</p>
      </div>
    </div>

    <!-- 预设位置 -->
    <div class="preset-locations">
      <h3>预设位置</h3>
      <div class="location-buttons">
        <button 
          v-for="location in presetLocations" 
          :key="location.name"
          @click="goToLocation(location)"
          class="btn btn-outline"
        >
          {{ location.name }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import TiandituMap from './TiandituMap.vue'
import { formatCoordinate, calculateCenter, calculateBounds } from './utils'
import type { MarkerData, LngLat, MapType, MapInstance } from './types'

// 响应式数据
const mapRef = ref<MapInstance>()
const apiKey = ref('')
const mapType = ref<MapType>('normal')
const zoom = ref(12)
const center = reactive<LngLat>({
  lng: 116.40969,
  lat: 39.89945
})
const markers = ref<MarkerData[]>([])
const clickInfo = ref('')

// 地图类型名称映射
const mapTypeNames = {
  normal: '普通地图',
  satellite: '卫星地图',
  terrain: '地形图'
}

// 预设位置
const presetLocations = [
  { name: '北京', lng: 116.40969, lat: 39.89945 },
  { name: '上海', lng: 121.48941, lat: 31.40527 },
  { name: '广州', lng: 113.27324, lat: 23.15792 },
  { name: '深圳', lng: 114.05956, lat: 22.54497 },
  { name: '杭州', lng: 120.15507, lat: 30.27408 },
  { name: '成都', lng: 104.07573, lat: 30.65946 }
]

/**
 * 地图准备就绪事件
 */
const onMapReady = (map: any) => {
  console.log('地图加载完成', map)
  
  // 添加一些示例标注
  addSampleMarkers()
}

/**
 * 地图点击事件
 */
const onMapClick = (event: any) => {
  if (event && event.lnglat) {
    const lng = event.lnglat.lng
    const lat = event.lnglat.lat
    clickInfo.value = formatCoordinate(lng, lat)
    
    // 可选：在点击位置添加标注
    // addMarkerAtPosition(lng, lat)
  }
}

/**
 * 标注点击事件
 */
const onMarkerClick = (marker: any, data: MarkerData) => {
  console.log('标注点击', marker, data)
  alert(`点击了标注: ${data.title || '未命名标注'}\n位置: ${formatCoordinate(data.lng, data.lat)}`)
}

/**
 * 添加示例标注
 */
const addSampleMarkers = () => {
  const sampleMarkers: MarkerData[] = [
    {
      id: 1,
      lng: 116.40969,
      lat: 39.89945,
      title: '天安门广场',
      content: '中华人民共和国的象征，位于北京市中心'
    },
    {
      id: 2,
      lng: 116.41969,
      lat: 39.90945,
      title: '故宫博物院',
      content: '明清两朝的皇家宫殿，现为世界文化遗产'
    },
    {
      id: 3,
      lng: 116.39969,
      lat: 39.88945,
      title: '前门大街',
      content: '北京著名的商业街，历史悠久'
    }
  ]
  
  markers.value = sampleMarkers
}

/**
 * 添加随机标注
 */
const addRandomMarker = () => {
  const randomLng = center.lng + (Math.random() - 0.5) * 0.02
  const randomLat = center.lat + (Math.random() - 0.5) * 0.02
  
  const newMarker: MarkerData = {
    id: Date.now(),
    lng: randomLng,
    lat: randomLat,
    title: `随机标注 ${markers.value.length + 1}`,
    content: `这是一个随机生成的标注点\n坐标: ${formatCoordinate(randomLng, randomLat)}`
  }
  
  markers.value.push(newMarker)
}

/**
 * 在指定位置添加标注
 */
// const addMarkerAtPosition = (lng: number, lat: number) => {
//   const newMarker: MarkerData = {
//     id: Date.now(),
//     lng,
//     lat,
//     title: '点击标注',
//     content: `点击位置: ${formatCoordinate(lng, lat)}`
//   }
  
//   markers.value.push(newMarker)
// }

/**
 * 清除所有标注
 */
const clearAllMarkers = () => {
  markers.value = []
  mapRef.value?.clearMarkers()
}

/**
 * 删除指定标注
 */
const removeMarker = (index: number) => {
  markers.value.splice(index, 1)
}

/**
 * 聚焦到指定标注
 */
const focusMarker = (marker: MarkerData) => {
  center.lng = marker.lng
  center.lat = marker.lat
  zoom.value = Math.max(zoom.value, 15)
  mapRef.value?.setCenter(marker.lng, marker.lat, zoom.value)
}

/**
 * 适应所有标注
 */
const fitToMarkers = () => {
  if (markers.value.length === 0) return
  
  const bounds = calculateBounds(markers.value)
  if (bounds) {
    const centerPoint = calculateCenter(markers.value)
    if (centerPoint) {
      center.lng = centerPoint.lng
      center.lat = centerPoint.lat
      mapRef.value?.setCenter(centerPoint.lng, centerPoint.lat, 12)
    }
  }
}

/**
 * 前往指定位置
 */
const goToLocation = (location: LngLat & { name: string }) => {
  center.lng = location.lng
  center.lat = location.lat
  zoom.value = 12
  mapRef.value?.setCenter(location.lng, location.lat, 12)
}
</script>

<style scoped>
.tianditu-example {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.example-header {
  text-align: center;
  margin-bottom: 30px;
}

.example-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.example-header p {
  color: #666;
  font-size: 16px;
}

.control-panel {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-group label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.api-key-input {
  width: 300px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.map-type-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.zoom-slider {
  width: 120px;
}

.zoom-value {
  font-weight: 500;
  color: #333;
  min-width: 20px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #117a8b;
}

.btn-outline {
  background: white;
  color: #007bff;
  border: 1px solid #007bff;
}

.btn-outline:hover {
  background: #007bff;
  color: white;
}

.map-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.info-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.info-section {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.info-section h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 18px;
}

.info-section p {
  margin: 8px 0;
  color: #666;
}

.placeholder {
  color: #999;
  font-style: italic;
}

.marker-list {
  max-height: 200px;
  overflow-y: auto;
}

.marker-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.marker-item:hover {
  background-color: #f8f9fa;
}

.marker-title {
  font-weight: 500;
  color: #333;
}

.marker-coord {
  font-size: 12px;
  color: #666;
}

.remove-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
}

.remove-btn:hover {
  background: #c82333;
}

.preset-locations {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.preset-locations h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.location-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

@media (max-width: 768px) {
  .control-panel {
    flex-direction: column;
    align-items: stretch;
  }
  
  .control-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .api-key-input {
    width: 100%;
  }
  
  .info-panel {
    grid-template-columns: 1fr;
  }
}
</style>