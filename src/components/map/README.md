# 天地图组件

基于天地图JavaScript API 4.0开发的Vue 3组件，提供地图展示、标注、定位等功能。

## 功能特性

- 🗺️ 支持多种地图类型（普通地图、卫星地图、地形图）
- 📍 支持标注点添加和管理
- 🎛️ 支持地图控件（缩放、比例尺、地图类型切换）
- 📱 响应式设计，支持移动端
- 🔧 TypeScript支持
- ⚡ 性能优化（防抖、节流）

## 安装使用

### 1. 获取天地图API密钥

访问 [天地图开放平台](http://lbs.tianditu.gov.cn/) 注册账号并申请API密钥。

### 2. 基础使用

```vue
<template>
  <TiandituMap
    :api-key="your-api-key"
    :longitude="116.40969"
    :latitude="39.89945"
    :zoom="12"
    width="100%"
    height="400px"
    @map-ready="onMapReady"
    @map-click="onMapClick"
  />
</template>

<script setup>
import { TiandituMap } from '@/components/map'

const onMapReady = (map) => {
  console.log('地图加载完成', map)
}

const onMapClick = (event) => {
  console.log('地图点击事件', event)
}
</script>
```

### 3. 添加标注点

```vue
<template>
  <TiandituMap
    :api-key="your-api-key"
    :markers="markers"
    @marker-click="onMarkerClick"
  />
</template>

<script setup>
import { ref } from 'vue'
import { TiandituMap } from '@/components/map'
import type { MarkerData } from '@/components/map'

const markers = ref<MarkerData[]>([
  {
    lng: 116.40969,
    lat: 39.89945,
    title: '北京天安门',
    content: '中华人民共和国的象征'
  },
  {
    lng: 116.41969,
    lat: 39.90945,
    title: '故宫博物院',
    content: '明清两朝的皇家宫殿'
  }
])

const onMarkerClick = (marker, data) => {
  console.log('标注点击', marker, data)
}
</script>
```

### 4. 使用组件引用

```vue
<template>
  <TiandituMap
    ref="mapRef"
    :api-key="your-api-key"
  />
  <button @click="changeCenter">改变中心点</button>
  <button @click="addMarker">添加标注</button>
</template>

<script setup>
import { ref } from 'vue'
import { TiandituMap } from '@/components/map'
import type { MapInstance, MarkerData } from '@/components/map'

const mapRef = ref<MapInstance>()

const changeCenter = () => {
  mapRef.value?.setCenter(116.41969, 39.90945, 15)
}

const addMarker = () => {
  const newMarkers: MarkerData[] = [
    {
      lng: 116.42969,
      lat: 39.91945,
      title: '新标注点',
      content: '这是一个新添加的标注点'
    }
  ]
  mapRef.value?.addMarkers(newMarkers)
}
</script>
```

## API 文档

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| width | string | '100%' | 地图容器宽度 |
| height | string | '400px' | 地图容器高度 |
| longitude | number | 116.40969 | 地图中心点经度 |
| latitude | number | 39.89945 | 地图中心点纬度 |
| zoom | number | 12 | 地图缩放级别 (1-18) |
| apiKey | string | '' | 天地图API密钥 |
| mapType | 'normal' \| 'satellite' \| 'terrain' | 'normal' | 地图类型 |
| showControls | boolean | true | 是否显示地图控件 |
| markers | MarkerData[] | [] | 标注点数据数组 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| map-ready | (map: any) | 地图加载完成事件 |
| map-click | (event: any) | 地图点击事件 |
| marker-click | (marker: any, data: MarkerData) | 标注点击事件 |

### Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| setCenter | (lng: number, lat: number, zoom?: number) | void | 设置地图中心点 |
| setZoom | (zoom: number) | void | 设置地图缩放级别 |
| addMarkers | (markers: MarkerData[]) | void | 添加标注点 |
| clearMarkers | () | void | 清除所有标注点 |
| getMapInstance | () | any | 获取原生地图实例 |

### 类型定义

#### MarkerData

```typescript
interface MarkerData {
  lng: number          // 经度
  lat: number          // 纬度
  title?: string       // 标题
  content?: string     // 内容描述
  icon?: string        // 自定义图标URL
  id?: string | number // 标注点ID
  data?: any          // 扩展数据
}
```

#### LngLat

```typescript
interface LngLat {
  lng: number  // 经度
  lat: number  // 纬度
}
```

## 工具函数

组件还提供了一系列工具函数，可以单独导入使用：

```typescript
import {
  isValidCoordinate,
  calculateDistance,
  calculateCenter,
  formatCoordinate
} from '@/components/map'

// 验证坐标
const isValid = isValidCoordinate(116.40969, 39.89945)

// 计算两点距离
const distance = calculateDistance(
  { lng: 116.40969, lat: 39.89945 },
  { lng: 116.41969, lat: 39.90945 }
)

// 计算中心点
const center = calculateCenter([
  { lng: 116.40969, lat: 39.89945 },
  { lng: 116.41969, lat: 39.90945 }
])

// 格式化坐标显示
const formatted = formatCoordinate(116.40969, 39.89945)
```

## 注意事项

1. **API密钥**: 使用前必须申请天地图API密钥
2. **网络环境**: 需要能够访问天地图服务
3. **坐标系统**: 天地图使用CGCS2000坐标系，与WGS84基本一致
4. **性能优化**: 大量标注点时建议使用聚合功能
5. **移动端**: 在移动设备上使用时注意触摸事件处理

## 常见问题

### Q: 地图无法显示？
A: 请检查：
- API密钥是否正确
- 网络是否能访问天地图服务
- 控制台是否有错误信息

### Q: 标注点不显示？
A: 请检查：
- 坐标数据是否正确
- 坐标是否在有效范围内（经度-180~180，纬度-90~90）
- markers数组是否为空

### Q: 如何自定义标注点样式？
A: 可以通过MarkerData的icon属性指定自定义图标URL

## 更新日志

### v1.0.0 (2025-01-27)
- 初始版本发布
- 支持基础地图显示
- 支持标注点添加
- 支持地图控件
- 提供TypeScript类型支持