<!--
 * 简化版天地图组件
 * 作者: AI Assistant
 * 日期: 2025-01-27
 * 版本: 1.0.0
 * 描述: 参考tianditu_piece项目的简化实现，确保稳定性
-->

<template>
  <div
    :id="mapId"
    class="tianditu-map"
    :style="{ width: width, height: height }"
  ></div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useTiandituStore } from '@/stores/tianditu'
import { loadJs, randomCreateMapType } from './utils'
import { tianditu_token, tianditu_tokens } from '@/config/tianditu'
import { generateUniqueId } from '@/utils/format'

// 定义组件名称
defineOptions({
  name: 'SimpleTiandituMap'
})

// 组件属性定义
interface Props {
  width?: string
  height?: string
  longitude?: number
  latitude?: number
  zoom?: number
  apiKey?: string
  useRandomToken?: boolean
}

// 设置默认属性值
const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '400px',
  longitude: 116.40769,
  latitude: 39.89945,
  zoom: 12,
  apiKey: tianditu_token,
  useRandomToken: false
})

// 组件事件定义
interface Emits {
  (e: 'mapReady', map: any): void
  (e: 'mapClick', event: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const mapId = ref(`tianditu-map-${generateUniqueId()}`)
const pinia_useTiandituStore = useTiandituStore()

/**
 * 随机分发地图加载的token，避免资源耗尽过快
 */
const randomCreateUrl = (): string => {
  const index = Math.floor(Math.random() * tianditu_tokens.length)
  window.localStorage.setItem('demo_tokens_index', index.toString())
  return `https://api.tianditu.gov.cn/api?v=4.0&tk=${tianditu_tokens[index]}`
}

/**
 * 获取API URL
 */
const getApiUrl = (): string => {
  if (props.useRandomToken) {
    return randomCreateUrl()
  }
  return `https://api.tianditu.gov.cn/api?v=4.0&tk=${props.apiKey}`
}

/**
 * 初始化地图
 */
const initMap = () => {
  const apiUrl = getApiUrl()
  
  loadJs(apiUrl).then(() => {
    // 创建地图视图，初始化
    const map = new window.T.Map(mapId.value)
    
    // 在window注册map，用于支持标绘控件
    window.map = map
    
    // 设置中心点和缩放级别
    map.centerAndZoom(new window.T.LngLat(props.longitude, props.latitude), props.zoom)
    
    // 初始化store
    pinia_useTiandituStore.initTmap(map)
    
    // 随机设置一个图层（可选）
    if (props.useRandomToken) {
      map.setMapType(randomCreateMapType())
    }
    
    // 添加地图事件监听
    map.addEventListener('click', (event: any) => {
      emit('mapClick', event)
    })
    
    emit('mapReady', map)
  }).catch((error) => {
    console.error('天地图API加载失败:', error)
  })
}

// 计算样式
// const customWidth = computed(() => {
//   return props.width
// })

// 组件挂载时初始化地图
onMounted(() => {
  initMap()
})
</script>

<style scoped>
.tianditu-map {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
}
</style>