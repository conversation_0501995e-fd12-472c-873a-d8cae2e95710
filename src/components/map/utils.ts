/**
 * 天地图工具函数
 * 作者: AI Assistant
 * 日期: 2025-06-08
 * 版本: 1.1.0
 * 描述: 天地图相关的工具函数和辅助方法
 */

import type { LngLat, MapBounds, MarkerData } from './types'

// 坐标系类型定义
export type CoordinateSystem = 'WGS84' | 'GCJ02' | 'BD09'

// 坐标转换常量
const PI = Math.PI
const X_PI = PI * 3000.0 / 180.0
const A = 6378245.0
const EE = 0.00669342162296594323

/**
 * 坐标系转换函数
 */

/**
 * 判断坐标是否在中国境内
 * @param lng 经度
 * @param lat 纬度
 * @returns {boolean} 是否在中国境内
 */
const isInChina = (lng: number, lat: number): boolean => {
  return lng >= 72.004 && lng <= 137.8347 && lat >= 0.8293 && lat <= 55.8271
}

/**
 * WGS84转GCJ02
 * @param lng 经度
 * @param lat 纬度
 * @returns {LngLat} 转换后的坐标
 */
export const wgs84ToGcj02 = (lng: number, lat: number): LngLat => {
  if (!isInChina(lng, lat)) {
    return { lng, lat }
  }
  
  let dlat = transformLat(lng - 105.0, lat - 35.0)
  let dlng = transformLng(lng - 105.0, lat - 35.0)
  const radlat = lat / 180.0 * PI
  let magic = Math.sin(radlat)
  magic = 1 - EE * magic * magic
  const sqrtmagic = Math.sqrt(magic)
  dlat = (dlat * 180.0) / ((A * (1 - EE)) / (magic * sqrtmagic) * PI)
  dlng = (dlng * 180.0) / (A / sqrtmagic * Math.cos(radlat) * PI)
  
  return {
    lng: lng + dlng,
    lat: lat + dlat
  }
}

/**
 * GCJ02转WGS84
 * @param lng 经度
 * @param lat 纬度
 * @returns {LngLat} 转换后的坐标
 */
export const gcj02ToWgs84 = (lng: number, lat: number): LngLat => {
  if (!isInChina(lng, lat)) {
    return { lng, lat }
  }
  
  let dlat = transformLat(lng - 105.0, lat - 35.0)
  let dlng = transformLng(lng - 105.0, lat - 35.0)
  const radlat = lat / 180.0 * PI
  let magic = Math.sin(radlat)
  magic = 1 - EE * magic * magic
  const sqrtmagic = Math.sqrt(magic)
  dlat = (dlat * 180.0) / ((A * (1 - EE)) / (magic * sqrtmagic) * PI)
  dlng = (dlng * 180.0) / (A / sqrtmagic * Math.cos(radlat) * PI)
  
  return {
    lng: lng - dlng,
    lat: lat - dlat
  }
}

/**
 * GCJ02转BD09
 * @param lng 经度
 * @param lat 纬度
 * @returns {LngLat} 转换后的坐标
 */
export const gcj02ToBd09 = (lng: number, lat: number): LngLat => {
  const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * X_PI)
  const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * X_PI)
  
  return {
    lng: z * Math.cos(theta) + 0.0065,
    lat: z * Math.sin(theta) + 0.006
  }
}

/**
 * BD09转GCJ02
 * @param lng 经度
 * @param lat 纬度
 * @returns {LngLat} 转换后的坐标
 */
export const bd09ToGcj02 = (lng: number, lat: number): LngLat => {
  const x = lng - 0.0065
  const y = lat - 0.006
  const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * X_PI)
  const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI)
  
  return {
    lng: z * Math.cos(theta),
    lat: z * Math.sin(theta)
  }
}

/**
 * WGS84转BD09
 * @param lng 经度
 * @param lat 纬度
 * @returns {LngLat} 转换后的坐标
 */
export const wgs84ToBd09 = (lng: number, lat: number): LngLat => {
  const gcj02 = wgs84ToGcj02(lng, lat)
  return gcj02ToBd09(gcj02.lng, gcj02.lat)
}

/**
 * BD09转WGS84
 * @param lng 经度
 * @param lat 纬度
 * @returns {LngLat} 转换后的坐标
 */
export const bd09ToWgs84 = (lng: number, lat: number): LngLat => {
  const gcj02 = bd09ToGcj02(lng, lat)
  return gcj02ToWgs84(gcj02.lng, gcj02.lat)
}

/**
 * 坐标转换辅助函数 - 纬度转换
 */
const transformLat = (lng: number, lat: number): number => {
  let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng))
  ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0
  ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0
  ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0
  return ret
}

/**
 * 坐标转换辅助函数 - 经度转换
 */
const transformLng = (lng: number, lat: number): number => {
  let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng))
  ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0
  ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0
  ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0
  return ret
}

/**
 * 通用坐标转换函数
 * @param lng 经度
 * @param lat 纬度
 * @param from 源坐标系
 * @param to 目标坐标系
 * @returns {LngLat} 转换后的坐标
 */
export const convertCoordinate = (
  lng: number,
  lat: number,
  from: CoordinateSystem,
  to: CoordinateSystem
): LngLat => {
  if (from === to) {
    return { lng, lat }
  }
  
  // 转换映射表
  const converters: Record<string, (lng: number, lat: number) => LngLat> = {
    'WGS84_GCJ02': wgs84ToGcj02,
    'GCJ02_WGS84': gcj02ToWgs84,
    'GCJ02_BD09': gcj02ToBd09,
    'BD09_GCJ02': bd09ToGcj02,
    'WGS84_BD09': wgs84ToBd09,
    'BD09_WGS84': bd09ToWgs84
  }
  
  const key = `${from}_${to}`
  const converter = converters[key]
  
  if (converter) {
    return converter(lng, lat)
  }
  
  // 如果没有直接转换函数，通过中间坐标系转换
  if (from === 'WGS84' && to === 'BD09') {
    return wgs84ToBd09(lng, lat)
  }
  if (from === 'BD09' && to === 'WGS84') {
    return bd09ToWgs84(lng, lat)
  }
  
  console.warn(`不支持的坐标转换: ${from} -> ${to}`)
  return { lng, lat }
}

/**
 * 获取当前配置的坐标系
 * @returns {CoordinateSystem} 当前坐标系
 */
export const getCurrentCoordinateSystem = (): CoordinateSystem => {
  const system = import.meta.env.VITE_TIANDITU_COORDINATE_SYSTEM as CoordinateSystem
  return system || 'WGS84'
}

/**
 * 将输入坐标转换为天地图使用的坐标系（WGS84）
 * @param lng 经度
 * @param lat 纬度
 * @returns {LngLat} 转换后的坐标
 */
export const convertToMapCoordinate = (lng: number, lat: number): LngLat => {
  const currentSystem = getCurrentCoordinateSystem()
  return convertCoordinate(lng, lat, currentSystem, 'WGS84')
}

/**
 * 将天地图坐标（WGS84）转换为当前配置的坐标系
 * @param lng 经度
 * @param lat 纬度
 * @returns {LngLat} 转换后的坐标
 */
export const convertFromMapCoordinate = (lng: number, lat: number): LngLat => {
  const currentSystem = getCurrentCoordinateSystem()
  return convertCoordinate(lng, lat, 'WGS84', currentSystem)
}

/**
 * 检查天地图API是否已加载
 * @returns {boolean} 是否已加载
 */
export const isTiandituLoaded = (): boolean => {
  return typeof window !== 'undefined' && !!window.T
}

/**
 * 验证经纬度坐标是否有效
 * @param lng 经度
 * @param lat 纬度
 * @returns {boolean} 坐标是否有效
 */
export const isValidCoordinate = (lng: number, lat: number): boolean => {
  return (
    typeof lng === 'number' &&
    typeof lat === 'number' &&
    lng >= -180 &&
    lng <= 180 &&
    lat >= -90 &&
    lat <= 90 &&
    !isNaN(lng) &&
    !isNaN(lat)
  )
}

/**
 * 计算两点之间的距离（单位：米）
 * 使用Haversine公式
 * @param point1 第一个点
 * @param point2 第二个点
 * @returns {number} 距离（米）
 */
export const calculateDistance = (point1: LngLat, point2: LngLat): number => {
  const R = 6371000 // 地球半径（米）
  const lat1Rad = (point1.lat * Math.PI) / 180
  const lat2Rad = (point2.lat * Math.PI) / 180
  const deltaLatRad = ((point2.lat - point1.lat) * Math.PI) / 180
  const deltaLngRad = ((point2.lng - point1.lng) * Math.PI) / 180

  const a =
    Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
    Math.cos(lat1Rad) *
      Math.cos(lat2Rad) *
      Math.sin(deltaLngRad / 2) *
      Math.sin(deltaLngRad / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

  return R * c
}

/**
 * 计算多个点的中心点
 * @param points 坐标点数组
 * @returns {LngLat | null} 中心点坐标
 */
export const calculateCenter = (points: LngLat[]): LngLat | null => {
  if (!points || points.length === 0) return null

  const validPoints = points.filter(point => 
    isValidCoordinate(point.lng, point.lat)
  )

  if (validPoints.length === 0) return null

  const sum = validPoints.reduce(
    (acc, point) => ({
      lng: acc.lng + point.lng,
      lat: acc.lat + point.lat
    }),
    { lng: 0, lat: 0 }
  )

  return {
    lng: sum.lng / validPoints.length,
    lat: sum.lat / validPoints.length
  }
}

/**
 * 计算包含所有点的边界
 * @param points 坐标点数组
 * @returns {MapBounds | null} 地图边界
 */
export const calculateBounds = (points: LngLat[]): MapBounds | null => {
  if (!points || points.length === 0) return null

  const validPoints = points.filter(point => 
    isValidCoordinate(point.lng, point.lat)
  )

  if (validPoints.length === 0) return null

  let minLng = validPoints[0].lng
  let maxLng = validPoints[0].lng
  let minLat = validPoints[0].lat
  let maxLat = validPoints[0].lat

  validPoints.forEach(point => {
    minLng = Math.min(minLng, point.lng)
    maxLng = Math.max(maxLng, point.lng)
    minLat = Math.min(minLat, point.lat)
    maxLat = Math.max(maxLat, point.lat)
  })

  return {
    southwest: { lng: minLng, lat: minLat },
    northeast: { lng: maxLng, lat: maxLat }
  }
}

/**
 * 根据边界计算合适的缩放级别
 * @param bounds 地图边界
 * @returns {number} 缩放级别
 */
export const calculateZoomLevel = (
  bounds: MapBounds
): number => {
  const lngDiff = bounds.northeast.lng - bounds.southwest.lng
  const latDiff = bounds.northeast.lat - bounds.southwest.lat

  // 基于经纬度差值和地图容器大小计算缩放级别
  const lngZoom = Math.floor(Math.log2(360 / lngDiff))
  const latZoom = Math.floor(Math.log2(180 / latDiff))

  // 取较小值，确保所有点都在视野内
  let zoom = Math.min(lngZoom, latZoom)

  // 限制缩放级别范围
  zoom = Math.max(1, Math.min(18, zoom))

  return zoom
}

/**
 * 格式化坐标显示
 * @param lng 经度
 * @param lat 纬度
 * @param precision 精度（小数位数）
 * @returns {string} 格式化后的坐标字符串
 */
export const formatCoordinate = (
  lng: number,
  lat: number,
  precision: number = 6
): string => {
  if (!isValidCoordinate(lng, lat)) {
    return '无效坐标'
  }

  const lngStr = lng.toFixed(precision)
  const latStr = lat.toFixed(precision)
  const lngDir = lng >= 0 ? 'E' : 'W'
  const latDir = lat >= 0 ? 'N' : 'S'

  return `${Math.abs(parseFloat(latStr))}°${latDir}, ${Math.abs(parseFloat(lngStr))}°${lngDir}`
}

/**
 * 生成默认的标注点图标URL
 * @param color 图标颜色
 * @returns {string} 图标URL
 */
export const generateMarkerIcon = (
  color: string = 'red'
): string => {
  // 这里可以根据需要返回自定义图标URL
  // 或者使用天地图提供的默认图标
  return `https://api.tianditu.gov.cn/img/map/markerA${color}.png`
}

/**
 * 验证标注点数据
 * @param marker 标注点数据
 * @returns {boolean} 数据是否有效
 */
export const validateMarkerData = (marker: MarkerData): boolean => {
  return (
    marker &&
    typeof marker === 'object' &&
    isValidCoordinate(marker.lng, marker.lat)
  )
}

/**
 * 过滤有效的标注点数据
 * @param markers 标注点数组
 * @returns {MarkerData[]} 有效的标注点数组
 */
export const filterValidMarkers = (markers: MarkerData[]): MarkerData[] => {
  if (!Array.isArray(markers)) return []
  return markers.filter(validateMarkerData)
}

/**
 * 转换地址为坐标（需要配合地理编码服务）
 * @returns {Promise<LngLat | null>} 坐标结果
 */
export const geocodeAddress = async (): Promise<LngLat | null> => {
  // 这里可以集成天地图的地理编码服务
  // 目前返回null，需要根据实际API实现
  console.warn('地理编码功能需要集成天地图地理编码服务')
  return null
}

/**
 * 转换坐标为地址（需要配合逆地理编码服务）
 * @returns {Promise<string | null>} 地址结果
 */
export const reverseGeocode = async (): Promise<string | null> => {
  // 这里可以集成天地图的逆地理编码服务
  // 目前返回null，需要根据实际API实现
  console.warn('逆地理编码功能需要集成天地图逆地理编码服务')
  return null
}

/**
 * 动态加载js
 * @param src 脚本地址
 * @returns Promise
 */
export const loadJs = (src: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    let script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = src
    document.body.appendChild(script)

    script.onload = () => {
      resolve('success')
    }
    script.onerror = () => {
      reject('error')
    }
  })
}

/**
 * 随机分发地图加载的token，避免资源耗尽过快
 * @param tokens token数组
 * @returns 随机选择的API URL
 */
export const randomCreateUrl = (tokens: string[]): string => {
  const index = Math.floor(Math.random() * tokens.length)
  // 存储一下
  window.localStorage.setItem('demo_tokens_index', index.toString())
  return `https://api.tianditu.gov.cn/api?v=4.0&tk=${tokens[index]}`
}

/**
 * 随机设置地图加载的类型，避免同一地图资源耗尽过快
 * @returns 随机地图类型常量
 */
export const randomCreateMapType = () => {
  const types = [
    // 此地图类型展示普通街道视图。
    'TMAP_NORMAL_MAP',
    // 此地图类型展示卫星视图。
    'TMAP_SATELLITE_MAP',
    // 此地图类型展示卫星和路网的混合视图。
    'TMAP_HYBRID_MAP',
    // 此地图类型展示地形视图。
    'TMAP_TERRAIN_MAP',
    // 此地图类型展示地形和路网的混合视图。
    'TMAP_TERRAIN_HYBRID_MAP',
  ]
  const index = Math.floor(Math.random() * types.length)
  window.localStorage.setItem('demo_maptype_index', index.toString())

  return (window as any)[types[index]]
}

/**
 * 加载天地图API脚本
 * @param apiKey 天地图API密钥
 * @returns Promise
 */
export const loadTiandituAPI = (apiKey: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    if (isTiandituLoaded()) {
      resolve()
      return
    }

    const apiUrl = `https://api.tianditu.gov.cn/api?v=4.0&tk=${apiKey}`
    
    loadJs(apiUrl).then(() => {
      console.log('天地图API加载成功')
      resolve()
    }).catch(() => {
      console.error('天地图API加载失败')
      reject(new Error('天地图API加载失败'))
    })
  })
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout> | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 时间限制（毫秒）
 * @returns {Function} 节流后的函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}