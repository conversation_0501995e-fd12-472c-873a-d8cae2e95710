/**
 * 天地图组件类型定义
 * 作者: AI Assistant
 * 日期: 2025-01-27
 * 版本: 1.0.0
 * 描述: 天地图相关的TypeScript类型定义
 */

// 天地图API全局对象类型声明
declare global {
  interface Window {
    T: any
  }
}

/**
 * 地图类型枚举
 */
export type MapType = 'normal' | 'satellite' | 'terrain'

/**
 * 标注点图标配置接口
 */
export interface MarkerIcon {
  /** 图标URL */
  url: string
  /** 图标尺寸 [宽度, 高度] */
  size: [number, number]
  /** 图标锚点 [x, y] */
  anchor: [number, number]
}

/**
 * 标注点数据接口
 */
export interface MarkerData {
  /** 经度 */
  lng: number
  /** 纬度 */
  lat: number
  /** 标题 */
  title?: string
  /** 内容描述 */
  content?: string
  /** 自定义图标，可以是URL字符串或图标配置对象 */
  icon?: string | MarkerIcon
  /** 标注点ID */
  id?: string | number
  /** 是否可拖拽 */
  draggable?: boolean
  /** 扩展数据 */
  data?: any
}

/**
 * 地图配置接口
 */
export interface MapConfig {
  /** 地图容器宽度 */
  width?: string
  /** 地图容器高度 */
  height?: string
  /** 地图中心点经度 */
  longitude?: number
  /** 地图中心点纬度 */
  latitude?: number
  /** 地图缩放级别 */
  zoom?: number
  /** 天地图API密钥 */
  apiKey?: string
  /** 地图类型 */
  mapType?: MapType
  /** 是否显示地图控件 */
  showControls?: boolean
  /** 标注点数据 */
  markers?: MarkerData[]
}

/**
 * 地图事件接口
 */
export interface MapEvents {
  /** 地图准备就绪事件 */
  onMapReady?: (map: any) => void
  /** 地图点击事件 */
  onMapClick?: (event: any) => void
  /** 标注点击事件 */
  onMarkerClick?: (marker: any, data: MarkerData) => void
  /** 标注点拖拽开始事件 */
  onMarkerDragStart?: (marker: any, data: MarkerData) => void
  /** 标注点拖拽中事件 */
  onMarkerDragging?: (marker: any, data: MarkerData, lnglat: LngLat) => void
  /** 标注点拖拽结束事件 */
  onMarkerDragEnd?: (marker: any, data: MarkerData, lnglat: LngLat) => void
}

/**
 * 地图实例方法接口
 */
export interface MapInstance {
  /** 设置地图中心点 */
  setCenter: (lng: number, lat: number, zoom?: number) => void
  /** 设置地图缩放级别 */
  setZoom: (zoom: number) => void
  /** 添加标注点 */
  addMarkers: (markers: MarkerData[]) => void
  /** 清除所有标注点 */
  clearMarkers: () => void
  /** 获取地图实例 */
  getMapInstance: () => any
}

/**
 * 地理坐标点接口
 */
export interface LngLat {
  /** 经度 */
  lng: number
  /** 纬度 */
  lat: number
}

/**
 * 地图边界接口
 */
export interface MapBounds {
  /** 西南角坐标 */
  southwest: LngLat
  /** 东北角坐标 */
  northeast: LngLat
}

/**
 * 地图控件配置接口
 */
export interface ControlConfig {
  /** 是否显示地图类型控件 */
  showMapType?: boolean
  /** 是否显示缩放控件 */
  showZoom?: boolean
  /** 是否显示比例尺控件 */
  showScale?: boolean
  /** 是否显示导航控件 */
  showNavigation?: boolean
}

/**
 * 信息窗口配置接口
 */
export interface InfoWindowConfig {
  /** 窗口标题 */
  title?: string
  /** 窗口内容 */
  content: string
  /** 窗口宽度 */
  width?: number
  /** 窗口高度 */
  height?: number
  /** 是否自动调整大小 */
  autoPan?: boolean
}

export default {}