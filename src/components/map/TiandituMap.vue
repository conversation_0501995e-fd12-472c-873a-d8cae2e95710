<!--
 * 天地图组件
 * 作者: AI Assistant
 * 日期: 2025-01-27
 * 版本: 1.0.0
 * 描述: 基于天地图JavaScript API 4.0的Vue组件，提供地图展示、标注、定位等功能
-->

<template>
  <div class="tianditu-map-container">
    <div :id="mapId" class="tianditu-map" :style="{ width: width, height: height }"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { generateUniqueId } from '@/utils/format'
import { loadTiandituAPI, convertToMapCoordinate, convertFromMapCoordinate } from './utils'
import { tianditu_token, defaultMapConfig } from '@/config/tianditu'
import type { MarkerData } from './types'

// 定义组件名称
defineOptions({
  name: 'TiandituMap'
})

// 组件属性定义
interface Props {
  // 地图容器宽度
  width?: string
  // 地图容器高度
  height?: string
  // 地图中心点经度
  longitude?: number
  // 地图中心点纬度
  latitude?: number
  // 地图缩放级别
  zoom?: number
  // 天地图API密钥
  apiKey?: string
  // 地图类型 'normal' | 'satellite' | 'terrain'
  mapType?: 'normal' | 'satellite' | 'terrain'
  // 是否显示地图控件
  showControls?: boolean
  // 标注点数据
  markers?: MarkerData[]
  // 单个可拖拽标记点（用于编辑模式）
  editableMarker?: MarkerData | null
  // 是否启用编辑模式
  editMode?: boolean
  // 是否启用添加标记点模式（点击地图添加新标记点）
  addMarkerMode?: boolean
}

// 组件事件定义
interface Emits {
  (e: 'mapReady', map: any): void
  (e: 'mapClick', event: any): void
  (e: 'markerClick', marker: any, data: any): void
  (e: 'markerDragStart', marker: any, data: MarkerData): void
  (e: 'markerDragging', marker: any, data: MarkerData, lnglat: { lng: number, lat: number }): void
  (e: 'markerDragEnd', marker: any, data: MarkerData, lnglat: { lng: number, lat: number }): void
  (e: 'editableMarkerUpdate', lnglat: { lng: number, lat: number }): void
  (e: 'markerAdded', marker: any, data: MarkerData): void
}

// 设置默认属性值
const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '400px',
  longitude: defaultMapConfig.center[0],
  latitude: defaultMapConfig.center[1],
  zoom: defaultMapConfig.zoom,
  apiKey: tianditu_token,
  mapType: 'normal',
  showControls: true,
  markers: () => [],
  editableMarker: null,
  editMode: false,
  addMarkerMode: false
})

// 定义事件
const emit = defineEmits<Emits>()

// 响应式数据
const mapId = ref(`tianditu-map-${generateUniqueId()}`)
const mapInstance = ref<any>(null)
const isMapReady = ref(false)
const markerInstances = ref<any[]>([])
const editableMarkerInstance = ref<any>(null)



/**
 * 初始化地图
 */
const initMap = async () => {
  try {
    if (!props.apiKey) {
      console.warn('请提供天地图API密钥')
      return
    }

    // 加载天地图API
    await loadTiandituAPI(props.apiKey)
    
    // 等待DOM更新
    await nextTick()
    
    // 创建地图实例
    mapInstance.value = new window.T.Map(mapId.value)
    
    // 在window注册map，用于支持标绘控件
    window.map = mapInstance.value
    
    // 设置地图中心点和缩放级别
    // 将输入坐标转换为天地图使用的WGS84坐标系
    const convertedCenter = convertToMapCoordinate(props.longitude, props.latitude)
    const centerPoint = new window.T.LngLat(convertedCenter.lng, convertedCenter.lat)
    mapInstance.value.centerAndZoom(centerPoint, props.zoom)
    
    // 设置地图类型
    setMapType(props.mapType)
    
    // 添加地图控件
    if (props.showControls) {
      addMapControls()
    }
    
    // 添加地图事件监听
    addMapEventListeners()
    
    // 添加标注点
    if (props.markers.length > 0) {
      addMarkers(props.markers)
    }
    
    // 添加可编辑标记点
    if (props.editableMarker) {
      addEditableMarker(props.editableMarker)
    }
    
    isMapReady.value = true
    emit('mapReady', mapInstance.value)
    
  } catch (error) {
    console.error('地图初始化失败:', error)
  }
}

/**
 * 设置地图类型
 */
const setMapType = (type: string) => {
  if (!mapInstance.value) return
  
  let mapTypeConstant
  
  switch (type) {
    case 'satellite':
      mapTypeConstant = window.TMAP_SATELLITE_MAP
      break
    case 'terrain':
      mapTypeConstant = window.TMAP_TERRAIN_MAP
      break
    case 'normal':
    default:
      mapTypeConstant = window.TMAP_NORMAL_MAP
      break
  }
  
  if (mapTypeConstant) {
    mapInstance.value.setMapType(mapTypeConstant)
  }
}

/**
 * 添加地图控件
 */
const addMapControls = () => {
  if (!mapInstance.value) return
  
  // 添加地图类型控件
  const mapTypeControl = new window.T.Control.MapType()
  mapInstance.value.addControl(mapTypeControl)
  
  // 添加缩放控件
  const zoomControl = new window.T.Control.Zoom()
  mapInstance.value.addControl(zoomControl)
  
  // 添加比例尺控件
  const scaleControl = new window.T.Control.Scale()
  mapInstance.value.addControl(scaleControl)
}

/**
 * 添加地图事件监听
 */
const addMapEventListeners = () => {
  if (!mapInstance.value) return
  
  // 地图点击事件
  mapInstance.value.addEventListener('click', (event: any) => {
    // 将地图坐标转换为当前配置的坐标系
    if (event.lnglat) {
      const convertedCoord = convertFromMapCoordinate(event.lnglat.lng, event.lnglat.lat)
      event.convertedLnglat = convertedCoord
    }
    
    emit('mapClick', event)
    
    // 如果启用了添加标记点模式，在点击位置添加新的可编辑标记点
    if (props.addMarkerMode && event.lnglat) {
      addNewMarkerAtPosition(event.lnglat.lng, event.lnglat.lat)
    }
  })
}

/**
 * 添加标注点
 */
const addMarkers = (markers: MarkerData[]) => {
  if (!mapInstance.value) return
  
  // 清除现有标注
  clearMarkers()
  
  markers.forEach((markerData, _index) => {
    // 将标记点坐标转换为天地图使用的WGS84坐标系
    const convertedCoord = convertToMapCoordinate(markerData.lng, markerData.lat)
    const point = new window.T.LngLat(convertedCoord.lng, convertedCoord.lat)
    
    // 创建标注配置
    const markerOptions: any = {}
    
    // 设置拖拽属性
    if (markerData.draggable) {
      markerOptions.draggable = true
    }
    
    // 创建标注
    let marker: any
    
    // 处理自定义图标
    if (markerData.icon) {
      if (typeof markerData.icon === 'string') {
        // 字符串类型的图标URL
        const icon = new window.T.Icon({
          iconUrl: markerData.icon,
          iconSize: new window.T.Point(32, 32),
          iconAnchor: new window.T.Point(16, 32)
        })
        markerOptions.icon = icon
      } else {
        // 对象类型的图标配置
        const icon = new window.T.Icon({
          iconUrl: markerData.icon.url,
          iconSize: new window.T.Point(markerData.icon.size[0], markerData.icon.size[1]),
          iconAnchor: new window.T.Point(markerData.icon.anchor[0], markerData.icon.anchor[1])
        })
        markerOptions.icon = icon
      }
    }
    
    marker = new window.T.Marker(point, markerOptions)
    
    // 添加拖拽事件监听
    if (markerData.draggable) {
      marker.addEventListener('dragstart', () => {
        emit('markerDragStart', marker, markerData)
      })
      
      marker.addEventListener('dragging', (event: any) => {
        // 将地图坐标转换为当前配置的坐标系
        const convertedCoord = convertFromMapCoordinate(event.lnglat.lng, event.lnglat.lat)
        const lnglat = { lng: convertedCoord.lng, lat: convertedCoord.lat }
        emit('markerDragging', marker, markerData, lnglat)
      })
      
      marker.addEventListener('dragend', (event: any) => {
        // 将地图坐标转换为当前配置的坐标系
        const convertedCoord = convertFromMapCoordinate(event.lnglat.lng, event.lnglat.lat)
        const lnglat = { lng: convertedCoord.lng, lat: convertedCoord.lat }
        emit('markerDragEnd', marker, markerData, lnglat)
      })
    }
    
    // 添加信息窗口
    if (markerData.title || markerData.content) {
      const infoWindow = new window.T.InfoWindow()
      const content = `
        <div>
          ${markerData.title ? `<h4>${markerData.title}</h4>` : ''}
          ${markerData.content ? `<p>${markerData.content}</p>` : ''}
        </div>
      `
      infoWindow.setContent(content)
      
      marker.addEventListener('click', () => {
        marker.openInfoWindow(infoWindow)
        emit('markerClick', marker, markerData)
      })
    }
    
    // 添加到地图
    mapInstance.value.addOverLay(marker)
    markerInstances.value.push(marker)
  })
}

/**
 * 清除所有标注点
 */
const clearMarkers = () => {
  markerInstances.value.forEach(marker => {
    mapInstance.value.removeOverLay(marker)
  })
  markerInstances.value = []
}

/**
 * 添加可编辑标记点
 */
const addEditableMarker = (markerData: MarkerData) => {
  if (!mapInstance.value) return
  
  // 清除现有可编辑标记点
  clearEditableMarker()
  
  // 将标记点坐标转换为天地图使用的WGS84坐标系
  const convertedCoord = convertToMapCoordinate(markerData.lng, markerData.lat)
  const point = new window.T.LngLat(convertedCoord.lng, convertedCoord.lat)
  
  // 创建标注配置，强制启用拖拽
  const markerOptions: any = {
    draggable: true
  }
  
  // 处理自定义图标
  if (markerData.icon) {
    if (typeof markerData.icon === 'string') {
      const icon = new window.T.Icon({
        iconUrl: markerData.icon,
        iconSize: new window.T.Point(32, 32),
        iconAnchor: new window.T.Point(16, 32)
      })
      markerOptions.icon = icon
    } else {
      const icon = new window.T.Icon({
        iconUrl: markerData.icon.url,
        iconSize: new window.T.Point(markerData.icon.size[0], markerData.icon.size[1]),
        iconAnchor: new window.T.Point(markerData.icon.anchor[0], markerData.icon.anchor[1])
      })
      markerOptions.icon = icon
    }
  }
  
  const marker = new window.T.Marker(point, markerOptions)
  
  // 添加拖拽事件监听
  marker.addEventListener('dragstart', () => {
    emit('markerDragStart', marker, markerData)
  })
  
  marker.addEventListener('dragging', (event: any) => {
    // 将地图坐标转换为当前配置的坐标系
    const convertedCoord = convertFromMapCoordinate(event.lnglat.lng, event.lnglat.lat)
    const lnglat = { lng: convertedCoord.lng, lat: convertedCoord.lat }
    emit('markerDragging', marker, markerData, lnglat)
  })
  
  marker.addEventListener('dragend', (event: any) => {
    // 将地图坐标转换为当前配置的坐标系
    const convertedCoord = convertFromMapCoordinate(event.lnglat.lng, event.lnglat.lat)
    const lnglat = { lng: convertedCoord.lng, lat: convertedCoord.lat }
    emit('markerDragEnd', marker, markerData, lnglat)
    emit('editableMarkerUpdate', lnglat)
  })
  
  // 添加信息窗口
  if (markerData.title || markerData.content) {
    const infoWindow = new window.T.InfoWindow()
    const content = `
      <div>
        ${markerData.title ? `<h4>${markerData.title}</h4>` : ''}
        ${markerData.content ? `<p>${markerData.content}</p>` : ''}
        <p><small>可拖拽移动位置</small></p>
      </div>
    `
    infoWindow.setContent(content)
    
    marker.addEventListener('click', () => {
      marker.openInfoWindow(infoWindow)
      emit('markerClick', marker, markerData)
    })
  }
  
  // 添加到地图
  mapInstance.value.addOverLay(marker)
  editableMarkerInstance.value = marker
}

/**
 * 清除可编辑标记点
 */
const clearEditableMarker = () => {
  if (editableMarkerInstance.value && mapInstance.value) {
    mapInstance.value.removeOverLay(editableMarkerInstance.value)
    editableMarkerInstance.value = null
  }
}

/**
 * 更新可编辑标记点位置
 */
const updateEditableMarkerPosition = (lng: number, lat: number) => {
  if (editableMarkerInstance.value) {
    // 将输入坐标转换为天地图使用的WGS84坐标系
    const convertedCoord = convertToMapCoordinate(lng, lat)
    const point = new window.T.LngLat(convertedCoord.lng, convertedCoord.lat)
    editableMarkerInstance.value.setLngLat(point)
  }
}

/**
 * 在指定位置添加新的可编辑标记点
 */
const addNewMarkerAtPosition = (lng: number, lat: number) => {
  if (!mapInstance.value) return
  
  // 将地图坐标转换为当前配置的坐标系
  const convertedCoord = convertFromMapCoordinate(lng, lat)
  
  // 创建新的标记点数据（使用转换后的坐标）
  const newMarkerData: MarkerData = {
    id: Date.now(),
    lng: convertedCoord.lng,
    lat: convertedCoord.lat,
    title: '新标记点',
    content: `位置: ${convertedCoord.lng.toFixed(6)}, ${convertedCoord.lat.toFixed(6)}`,
    draggable: true
  }
  
  // 添加为可编辑标记点
  addEditableMarker(newMarkerData)
  
  // 触发标记点添加事件
  emit('markerAdded', editableMarkerInstance.value, newMarkerData)
}

/**
 * 设置地图中心点
 */
const setCenter = (lng: number, lat: number, zoom?: number) => {
  if (!mapInstance.value) return
  
  // 将输入坐标转换为天地图使用的WGS84坐标系
  const convertedCoord = convertToMapCoordinate(lng, lat)
  const point = new window.T.LngLat(convertedCoord.lng, convertedCoord.lat)
  if (zoom !== undefined) {
    mapInstance.value.centerAndZoom(point, zoom)
  } else {
    mapInstance.value.setCenter(point)
  }
}

/**
 * 设置地图缩放级别
 */
const setZoom = (zoom: number) => {
  if (!mapInstance.value) return
  mapInstance.value.setZoom(zoom)
}

/**
 * 获取地图实例
 */
const getMapInstance = () => {
  return mapInstance.value
}

// 监听标注点变化
watch(() => props.markers, (newMarkers) => {
  if (isMapReady.value) {
    addMarkers(newMarkers)
  }
}, { deep: true })

// 监听地图类型变化
watch(() => props.mapType, (newType) => {
  if (isMapReady.value) {
    setMapType(newType)
  }
})

// 监听可编辑标记点变化
watch(() => props.editableMarker, (newMarker) => {
  if (isMapReady.value) {
    if (newMarker) {
      addEditableMarker(newMarker)
    } else {
      clearEditableMarker()
    }
  }
}, { deep: true })

// 暴露方法给父组件
defineExpose({
  setCenter,
  setZoom,
  addMarkers,
  clearMarkers,
  addEditableMarker,
  clearEditableMarker,
  updateEditableMarkerPosition,
  getMapInstance
})

// 组件挂载时初始化地图
onMounted(() => {
  initMap()
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (mapInstance.value) {
    mapInstance.value.clearOverLays()
    mapInstance.value = null
  }
})
</script>

<style scoped>
.tianditu-map-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.tianditu-map {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
}
</style>

<style>
/* 天地图信息窗口样式 */
.T_infowin {
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.T_infowin .T_shadow {
  border-radius: 8px !important;
}

.T_infowin .T_content {
  padding: 12px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.T_infowin h4 {
  margin: 0 0 8px 0 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #333 !important;
}

.T_infowin p {
  margin: 0 !important;
  color: #666 !important;
}
</style>