/**
 * 天地图组件模块入口文件
 * 作者: AI Assistant
 * 日期: 2025-01-27
 * 版本: 1.0.0
 * 描述: 统一导出天地图相关组件、类型和工具函数
 */

// 导入组件
import TiandituMapComponent from './TiandituMap.vue'
import SimpleTiandituMapComponent from './SimpleTiandituMap.vue'

// 导出组件
export { TiandituMapComponent as TiandituMap }
export { SimpleTiandituMapComponent as SimpleTiandituMap }

// 导出类型定义
export type {
  MapType,
  MarkerData,
  MapConfig,
  MapEvents,
  MapInstance,
  LngLat,
  MapBounds,
  ControlConfig,
  InfoWindowConfig
} from './types'

// 导出工具函数
export {
  isTiandituLoaded,
  isValidCoordinate,
  calculateDistance,
  calculateCenter,
  calculateBounds,
  calculateZoomLevel,
  formatCoordinate,
  generateMarkerIcon,
  validateMarkerData,
  filterValidMarkers,
  geocodeAddress,
  reverseGeocode,
  debounce,
  throttle
} from './utils'

// 默认导出主组件
export default TiandituMapComponent