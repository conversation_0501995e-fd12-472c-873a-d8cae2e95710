# 天地图组件重构说明

## 重构概述

本次重构参考了 `tianditu_piece` 项目的成功实现，解决了原有天地图组件的工作异常问题。

## 主要问题和解决方案

### 1. API加载问题
**问题**: 原组件使用 `document.head.appendChild` 和复杂的配置方式加载API
**解决**: 参考 `tianditu_piece` 项目，使用 `document.body.appendChild` 和简化的加载方式

### 2. 地图初始化问题
**问题**: 原组件使用了复杂的投影配置 `projection: 'EPSG:4326'`
**解决**: 简化地图初始化，直接使用 `new window.T.Map(mapId.value)`

### 3. 地图类型设置问题
**问题**: 原组件使用自定义图层URL方式设置地图类型
**解决**: 使用天地图标准的地图类型常量 `TMAP_NORMAL_MAP` 等

### 4. 配置管理问题
**问题**: 缺乏统一的配置管理
**解决**: 新增 `src/config/tianditu.ts` 配置文件，统一管理API密钥和默认配置

## 新增文件

### 1. 配置文件
- `src/config/tianditu.ts` - 天地图配置管理

### 2. 组件文件
- `src/components/map/SimpleTiandituMap.vue` - 简化版天地图组件
- `src/views/MapTest.vue` - 测试页面

### 3. 文档文件
- `src/components/map/REFACTOR_README.md` - 本文档

## 修改的文件

### 1. 核心组件
- `src/components/map/TiandituMap.vue` - 修复API加载和地图初始化逻辑
- `src/components/map/utils.ts` - 新增工具函数
- `src/components/map/index.ts` - 导出新组件
- `src/components/map/globalRegister.ts` - 注册新组件

### 2. 路由配置
- `src/router/index.ts` - 添加测试页面路由

## 使用方法

### 1. 原版组件（修复后）
```vue
<template>
  <TiandituMap
    :api-key="your-api-key"
    :longitude="116.40769"
    :latitude="39.89945"
    :zoom="12"
    :height="'400px'"
    :markers="markers"
    @map-ready="onMapReady"
  />
</template>
```

### 2. 简化版组件（推荐）
```vue
<template>
  <SimpleTiandituMap
    :api-key="your-api-key"
    :longitude="116.40769"
    :latitude="39.89945"
    :zoom="12"
    :height="'400px'"
    :use-random-token="true"
    @map-ready="onMapReady"
  />
</template>
```

### 3. 配置API密钥
在 `.env` 文件中配置：
```env
VITE_TIANDITU_API_KEY=your-tianditu-api-key
VITE_TIANDITU_API_KEY_2=your-second-api-key
VITE_TIANDITU_API_KEY_3=your-third-api-key
```

## 测试方法

1. 启动项目：`npm run dev`
2. 访问测试页面：`http://localhost:5173/map-test`
3. 在测试页面中验证两个组件的功能

## 核心改进

### 1. 稳定性提升
- 参考成功项目的实现方式
- 简化复杂的配置和初始化逻辑
- 统一错误处理机制

### 2. 可维护性提升
- 统一配置管理
- 模块化工具函数
- 清晰的组件结构

### 3. 功能完整性
- 保持原有功能不变
- 新增简化版组件选择
- 提供完整的测试环境

## 注意事项

1. **API密钥**: 确保在环境变量中正确配置天地图API密钥
2. **网络环境**: 确保能够访问天地图服务
3. **浏览器兼容性**: 建议使用现代浏览器测试
4. **组件选择**: 推荐使用 `SimpleTiandituMap` 组件，更稳定可靠

## 后续优化建议

1. 添加更多地图控件支持
2. 完善标注点的自定义样式
3. 添加地图绘制功能
4. 优化移动端适配
5. 添加单元测试