# 天地图组件坐标系兼容功能

## 概述

天地图组件现已支持多种坐标系，包括：
- **WGS84**: 世界大地坐标系（GPS坐标系）
- **GCJ02**: 火星坐标系（中国国家测绘局坐标系）
- **BD09**: 百度坐标系

## 配置方式

### 环境变量配置

在 `.env.development` 或 `.env.production` 文件中配置坐标系：

```bash
# 坐标系配置 (WGS84/GCJ02/BD09)
VITE_TIANDITU_COORDINATE_SYSTEM = 'GCJ02'
```

支持的坐标系值：
- `'WGS84'` - GPS坐标系（默认）
- `'GCJ02'` - 火星坐标系
- `'BD09'` - 百度坐标系

## 功能特性

### 自动坐标转换

1. **输入坐标转换**: 传入组件的坐标会自动从配置的坐标系转换为天地图使用的WGS84坐标系
2. **输出坐标转换**: 组件返回的坐标（如点击事件、拖拽事件）会自动转换为配置的坐标系
3. **透明转换**: 用户无需手动处理坐标转换，组件内部自动处理

### 支持的功能

- ✅ 地图中心点坐标转换
- ✅ 标记点坐标转换
- ✅ 地图点击事件坐标转换
- ✅ 标记点拖拽事件坐标转换
- ✅ 可编辑标记点坐标转换
- ✅ 动态添加标记点坐标转换

## 使用示例

### 基础使用

```vue
<template>
  <TiandituMap
    :longitude="116.404"
    :latitude="39.915"
    :zoom="15"
    :markers="markers"
    @map-click="onMapClick"
  />
</template>

<script setup>
import { ref } from 'vue'
import { TiandituMap } from '@/components/map'

// 如果配置为GCJ02坐标系，这里的坐标应该是GCJ02格式
const markers = ref([
  {
    lng: 116.404, // GCJ02经度
    lat: 39.915,  // GCJ02纬度
    title: '测试标记',
    content: '这是一个测试标记点'
  }
])

const onMapClick = (event) => {
  // event.convertedLnglat 包含转换后的坐标（当前配置的坐标系）
  // event.lnglat 包含原始的WGS84坐标
  console.log('当前坐标系坐标:', event.convertedLnglat)
  console.log('WGS84坐标:', event.lnglat)
}
</script>
```

### 坐标系工具函数

```javascript
import { 
  getCurrentCoordinateSystem,
  convertCoordinate,
  convertToMapCoordinate,
  convertFromMapCoordinate
} from '@/components/map/utils'

// 获取当前配置的坐标系
const currentSystem = getCurrentCoordinateSystem() // 'GCJ02'

// 手动坐标转换
const wgs84Coord = convertCoordinate(116.404, 39.915, 'GCJ02', 'WGS84')
const bd09Coord = convertCoordinate(116.404, 39.915, 'GCJ02', 'BD09')

// 转换为地图坐标（WGS84）
const mapCoord = convertToMapCoordinate(116.404, 39.915)

// 从地图坐标转换为当前坐标系
const userCoord = convertFromMapCoordinate(116.397, 39.909)
```

## 坐标系说明

### WGS84 (World Geodetic System 1984)
- 全球定位系统使用的坐标系
- GPS设备直接输出的坐标
- 国际标准坐标系

### GCJ02 (火星坐标系)
- 中国国家测绘局制定的坐标系
- 在WGS84基础上进行了加密偏移
- 高德地图、腾讯地图等使用此坐标系

### BD09 (百度坐标系)
- 百度公司制定的坐标系
- 在GCJ02基础上进行了二次加密
- 百度地图使用此坐标系

## 注意事项

1. **坐标精度**: 坐标转换可能会有微小的精度损失，通常在米级别
2. **中国境外**: 对于中国境外的坐标，GCJ02和WGS84转换可能不会生效
3. **性能考虑**: 坐标转换会有轻微的性能开销，但对于一般应用可以忽略
4. **配置一致性**: 确保前端配置的坐标系与后端API返回的坐标系一致

## 常见问题

### Q: 如何知道我的坐标是什么坐标系？
A: 
- GPS设备: WGS84
- 高德地图API: GCJ02
- 百度地图API: BD09
- 谷歌地图: WGS84

### Q: 坐标显示位置不准确怎么办？
A: 检查配置的坐标系是否与实际使用的坐标系一致，如果不一致请修改环境变量配置。

### Q: 可以动态切换坐标系吗？
A: 目前坐标系配置是通过环境变量设置的，需要重新构建应用才能生效。如需动态切换，可以使用工具函数手动转换坐标。

## 更新日志

- **v1.1.0**: 新增坐标系兼容功能，支持WGS84、GCJ02、BD09坐标系
- **v1.0.0**: 基础天地图组件功能