/**
 * 天地图组件全局注册文件
 * 作者: AI Assistant
 * 日期: 2025-06-08
 * 描述: 将天地图组件注册为全局组件，确保在动态路由和懒加载环境中可用
 */

// import type { App } from 'vue'
import TiandituMap from './TiandituMap.vue'
import SimpleTiandituMap from './SimpleTiandituMap.vue'

// 导出天地图组件
export { TiandituMap, SimpleTiandituMap }

// 全局注册函数
export default {
  install(app: any) {
    app.component('TiandituMap', TiandituMap)
    app.component('SimpleTiandituMap', SimpleTiandituMap)
    console.log('✅ 天地图组件已全局注册')
  }
}
