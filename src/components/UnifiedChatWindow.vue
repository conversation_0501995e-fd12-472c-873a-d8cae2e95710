<!--
  统一聊天窗口组件
  支持拖动、缩放、最小化等功能
  适用于商家layout和用户layout
-->
<template>
  <div 
    v-if="visible" 
    ref="chatWindowRef"
    class="unified-chat-window"
    :class="{
      'minimized': minimized,
      'maximized': maximized,
      'dragging': isDragging,
      'resizing': isResizing
    }"
    :style="windowStyle"
  >
    <!-- 聊天窗口头部 -->
    <div 
      ref="headerRef"
      class="chat-header" 
      @mousedown="startDrag"
      @dblclick="toggleMaximize"
    >
      <div class="chat-title">
        <el-icon class="chat-icon"><ChatDotRound /></el-icon>
        <span>{{ title }}</span>
        <el-badge 
          v-if="unreadCount > 0" 
          :value="unreadCount" 
          :max="99" 
          class="header-badge"
        />
      </div>
      
      <div class="chat-controls">
        <el-tooltip content="最小化" placement="top">
          <el-button 
            link 
            size="small" 
            @click="handleMinimize"
            class="control-btn"
          >
            <el-icon><Minus /></el-icon>
          </el-button>
        </el-tooltip>
        
        <el-tooltip :content="maximized ? '还原' : '最大化'" placement="top">
          <el-button 
            link 
            size="small" 
            @click="toggleMaximize"
            class="control-btn"
          >
            <el-icon><FullScreen v-if="!maximized" /><Aim v-else /></el-icon>
          </el-button>
        </el-tooltip>
        
        <el-tooltip content="关闭" placement="top">
          <el-button 
            link 
            size="small" 
            @click="handleClose"
            class="control-btn close-btn"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>
    
    <!-- 聊天窗口内容 -->
    <div v-if="!minimized" class="chat-content">
      <ChatWindow
        ref="chatWindowComponentRef"
        :key="chatKey"
        :show-session-list="true"
        @unread-change="handleUnreadChange"
        class="embedded-chat"
      />
    </div>
    
    <!-- 缩放手柄 -->
    <div 
      v-if="!minimized && !maximized"
      class="resize-handles"
    >
      <!-- 右下角缩放手柄 -->
      <div 
        class="resize-handle resize-se"
        @mousedown="startResize('se', $event)"
      ></div>
      
      <!-- 右边缩放手柄 -->
      <div 
        class="resize-handle resize-e"
        @mousedown="startResize('e', $event)"
      ></div>
      
      <!-- 下边缩放手柄 -->
      <div 
        class="resize-handle resize-s"
        @mousedown="startResize('s', $event)"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ChatDotRound, Minus, FullScreen, Aim, Close } from '@element-plus/icons-vue';
import { ChatWindow } from '@/modules/chat/components';

// Props
interface Props {
  visible: boolean;
  minimized?: boolean;
  title?: string;
  initialWidth?: number;
  initialHeight?: number;
  initialX?: number;
  initialY?: number;
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
}

const props = withDefaults(defineProps<Props>(), {
  minimized: false,
  title: '客服聊天',
  initialWidth: 1000,
  initialHeight: 650,
  initialX: -1, // -1 表示自动计算
  initialY: 60,
  minWidth: 800,
  minHeight: 500,
  maxWidth: 1200,
  maxHeight: 900
});

// Emits
const emit = defineEmits<{
  close: [];
  minimize: [];
  restore: [];
  'unread-change': [count: number];
  'position-change': [x: number, y: number];
  'size-change': [width: number, height: number];
}>();

// 状态
const chatWindowRef = ref<HTMLElement>();
const chatWindowComponentRef = ref<any>(); // ChatWindow组件的引用
const headerRef = ref<HTMLElement>();
const maximized = ref(false);
const unreadCount = ref(0);
const chatKey = ref(0);

// 拖动相关状态
const isDragging = ref(false);
const dragStartX = ref(0);
const dragStartY = ref(0);
const windowStartX = ref(0);
const windowStartY = ref(0);

// 缩放相关状态
const isResizing = ref(false);
const resizeDirection = ref('');
const resizeStartX = ref(0);
const resizeStartY = ref(0);
const windowStartWidth = ref(0);
const windowStartHeight = ref(0);

// 窗口位置和尺寸
const windowX = ref(props.initialX);
const windowY = ref(props.initialY);
const windowWidth = ref(props.initialWidth);
const windowHeight = ref(props.initialHeight);

// 计算窗口样式
const windowStyle = computed(() => {
  const style: any = {
    width: `${windowWidth.value}px`,
    height: `${windowHeight.value}px`,
  };

  if (maximized.value) {
    style.top = '60px';
    style.left = '20px';
    style.right = '20px';
    style.bottom = '20px';
    style.width = 'auto';
    style.height = 'auto';
  } else if (props.minimized) {
    style.width = '200px';
    style.height = '50px';
    style.bottom = '20px';
    style.right = '20px';
    style.top = 'auto';
  } else {
    // 计算初始位置
    if (windowX.value === -1) {
      style.right = '20px';
    } else {
      style.left = `${windowX.value}px`;
    }
    style.top = `${windowY.value}px`;
  }

  return style;
});

// 方法
function handleClose() {
  emit('close');
}

function handleMinimize() {
  emit('minimize');
}

function handleRestore() {
  emit('restore');
}

function toggleMaximize() {
  if (props.minimized) {
    handleRestore();
  } else {
    maximized.value = !maximized.value;
  }
}

function handleUnreadChange(count: number) {
  unreadCount.value = count;
  emit('unread-change', count);
}

// 拖动功能
function startDrag(event: MouseEvent) {
  if (maximized.value || props.minimized) return;
  
  event.preventDefault();
  isDragging.value = true;
  
  dragStartX.value = event.clientX;
  dragStartY.value = event.clientY;
  
  const rect = chatWindowRef.value!.getBoundingClientRect();
  windowStartX.value = rect.left;
  windowStartY.value = rect.top;
  
  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
  document.body.style.userSelect = 'none';
}

function handleDrag(event: MouseEvent) {
  if (!isDragging.value) return;
  
  const deltaX = event.clientX - dragStartX.value;
  const deltaY = event.clientY - dragStartY.value;
  
  const newX = Math.max(0, Math.min(
    window.innerWidth - windowWidth.value,
    windowStartX.value + deltaX
  ));
  const newY = Math.max(0, Math.min(
    window.innerHeight - windowHeight.value,
    windowStartY.value + deltaY
  ));
  
  windowX.value = newX;
  windowY.value = newY;
  
  emit('position-change', newX, newY);
}

function stopDrag() {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.body.style.userSelect = '';
}

// 缩放功能
function startResize(direction: string, event: MouseEvent) {
  event.preventDefault();
  event.stopPropagation();
  
  isResizing.value = true;
  resizeDirection.value = direction;
  
  resizeStartX.value = event.clientX;
  resizeStartY.value = event.clientY;
  windowStartWidth.value = windowWidth.value;
  windowStartHeight.value = windowHeight.value;
  
  document.addEventListener('mousemove', handleResize);
  document.addEventListener('mouseup', stopResize);
  document.body.style.userSelect = 'none';
}

function handleResize(event: MouseEvent) {
  if (!isResizing.value) return;
  
  const deltaX = event.clientX - resizeStartX.value;
  const deltaY = event.clientY - resizeStartY.value;
  
  let newWidth = windowStartWidth.value;
  let newHeight = windowStartHeight.value;
  
  if (resizeDirection.value.includes('e')) {
    newWidth = Math.max(props.minWidth, Math.min(props.maxWidth, windowStartWidth.value + deltaX));
  }
  
  if (resizeDirection.value.includes('s')) {
    newHeight = Math.max(props.minHeight, Math.min(props.maxHeight, windowStartHeight.value + deltaY));
  }
  
  windowWidth.value = newWidth;
  windowHeight.value = newHeight;
  
  emit('size-change', newWidth, newHeight);
}

function stopResize() {
  isResizing.value = false;
  resizeDirection.value = '';
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
  document.body.style.userSelect = '';
}

// 键盘事件处理
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Escape' && props.visible && !props.minimized) {
    handleClose();
  }
}

// 监听visible变化，但避免不必要的重新渲染
watch(() => props.visible, (newVisible, oldVisible) => {
  if (newVisible) {
    // 只在首次显示时重新渲染，避免每次打开都重新挂载组件
    if (chatKey.value === 0) {
      chatKey.value = 1;
    }

    nextTick(() => {
      // 确保窗口在屏幕范围内
      if (windowX.value === -1) {
        windowX.value = window.innerWidth - windowWidth.value - 20;
      }

      // 如果是重新显示（不是首次），刷新连接状态
      if (oldVisible === false && chatWindowComponentRef.value?.refreshConnectionStatus) {
        console.log('🔄 UnifiedChatWindow: 重新显示，刷新连接状态')
        setTimeout(() => {
          chatWindowComponentRef.value.refreshConnectionStatus()
        }, 100) // 稍微延迟以确保组件完全渲染
      }
    });
  }
});

onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
  // 清理拖动和缩放事件监听器
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
});
</script>

<style scoped>
.unified-chat-window {
  position: fixed;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  overflow: hidden;
  user-select: none;
}

.unified-chat-window.dragging {
  transition: none;
  cursor: move;
}

.unified-chat-window.resizing {
  transition: none;
}

.unified-chat-window.minimized {
  height: 50px !important;
  width: 200px !important;
}

.unified-chat-window.maximized {
  border-radius: 0;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  cursor: move;
  user-select: none;
  border-radius: 8px 8px 0 0;
  min-height: 26px;
  flex-shrink: 0;
}

.unified-chat-window.minimized .chat-header {
  border-radius: 8px;
  padding: 8px 12px;
}

.unified-chat-window.maximized .chat-header {
  border-radius: 0;
}

.chat-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
  flex: 1;
}

.unified-chat-window.minimized .chat-title {
  font-size: 13px;
}

.chat-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.header-badge {
  margin-left: 4px;
}

.chat-controls {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
  position: relative;
  z-index: 30;
}

.control-btn {
  color: rgba(255, 255, 255, 0.8);
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
  min-width: 24px;
  height: 24px;
  position: relative;
  z-index: 40;
  pointer-events: auto;
  cursor: pointer;
}

.unified-chat-window.minimized .control-btn {
  padding: 2px;
  min-width: 20px;
  height: 20px;
}

.control-btn:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.close-btn:hover {
  background: #ef4444;
  color: white;
}

.chat-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.embedded-chat {
  flex: 1;
  border: none;
  border-radius: 0;
  box-shadow: none;
  min-height: 0;
}

/* 缩放手柄 */
.resize-handles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  pointer-events: all;
  background: transparent;
}

.resize-handle.resize-se {
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  cursor: se-resize;
}

.resize-handle.resize-e {
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  cursor: e-resize;
}

.resize-handle.resize-s {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  cursor: s-resize;
}

/* 右下角缩放指示器 */
.resize-handle.resize-se::after {
  content: '';
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  background: linear-gradient(
    -45deg,
    transparent 0%,
    transparent 40%,
    #ccc 40%,
    #ccc 60%,
    transparent 60%,
    transparent 100%
  );
  background-size: 3px 3px;
  opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .unified-chat-window {
    width: 90vw !important;
    height: 80vh !important;
    max-width: 90vw !important;
    max-height: 80vh !important;
  }
}

@media (max-width: 768px) {
  .unified-chat-window {
    left: 10px !important;
    right: 10px !important;
    width: auto !important;
    max-width: none !important;
  }

  .unified-chat-window.maximized {
    top: 60px !important;
    left: 10px !important;
    right: 10px !important;
    bottom: 10px !important;
  }

  .unified-chat-window.minimized {
    width: 150px !important;
    right: 10px !important;
  }
}

@media (max-width: 480px) {
  .unified-chat-window {
    left: 5px !important;
    right: 5px !important;
  }

  .unified-chat-window.maximized {
    top: 50px !important;
    left: 5px !important;
    right: 5px !important;
    bottom: 5px !important;
  }
}

/* 动画效果 */
.unified-chat-window {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 拖动时的视觉反馈 */
.unified-chat-window.dragging {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  transform: scale(1.02);
}

/* 缩放时的视觉反馈 */
.unified-chat-window.resizing {
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.2);
}
</style>
