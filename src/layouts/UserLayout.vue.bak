<template>
  <div class="user-layout">
    <!-- 头部区域 -->
    <header class="user-header">
      <div class="logo-container">
        <img src="/images/logo.png" alt="O_Mall" class="logo" />
        <h1 class="title">用户中心</h1>
      </div>
      
      <div class="header-right">
        <!-- 购物车图标 -->
        <el-badge :value="cartCount" :max="99" class="cart-badge">
          <el-button :icon="ShoppingCart" circle @click="goToCart" />
        </el-badge>
        
        <!-- 用户菜单 -->
        <el-dropdown trigger="click" @command="handleCommand">
          <div class="user-info">
            <el-avatar :src="userStore.avatar" :size="32" />
            <span class="user-name">{{ userStore.username }}</span>
            <el-icon><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人资料</el-dropdown-item>
              <el-dropdown-item command="orders">我的订单</el-dropdown-item>
              <el-dropdown-item command="favorites">我的收藏</el-dropdown-item>
              <el-dropdown-item command="addresses">收货地址</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>

    <!-- 内容区域 -->
    <main class="user-content">
      <!-- 侧边栏 -->
      <aside class="user-sidebar" v-if="showSidebar">
        <el-menu
          :default-active="activeMenu"
          class="el-menu-vertical"
          :router="true"
          background-color="#ffffff"
          text-color="#303133"
          active-text-color="#409EFF"
        >
          <el-menu-item index="/user/profile">
            <el-icon><User /></el-icon>
            <span>个人资料</span>
          </el-menu-item>
          <el-menu-item index="/user/orders">
            <el-icon><List /></el-icon>
            <span>我的订单</span>
          </el-menu-item>
          <el-menu-item index="/user/favorites">
            <el-icon><Star /></el-icon>
            <span>我的收藏</span>
          </el-menu-item>
          <el-menu-item index="/user/addresses">
            <el-icon><Location /></el-icon>
            <span>收货地址</span>
          </el-menu-item>
          <el-menu-item index="/user/reviews">
            <el-icon><ChatDotRound /></el-icon>
            <span>我的评价</span>
          </el-menu-item>
          <el-menu-item index="/user/security">
            <el-icon><Lock /></el-icon>
            <span>账号安全</span>
          </el-menu-item>
        </el-menu>
      </aside>

      <!-- 主内容 -->
      <div class="main-content">
        <router-view />
      </div>
    </main>

    <!-- 底部区域 -->
    <footer class="user-footer">
      <p>© 2024 O_Mall 多商家电商平台. All rights reserved.</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/modules/user/stores/index';
import { ArrowDown, User, List, Star, Location, ChatDotRound, Lock, ShoppingCart } from '@element-plus/icons-vue';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 购物车商品数量
const cartCount = ref(0);

// 当前激活的菜单项
const activeMenu = computed(() => {
  return route.path;
});

// 是否显示侧边栏（登录和注册页面不显示）
const showSidebar = computed(() => {
  return ![
    '/user/login', 
    '/user/register', 
    '/user/forgot-password'
  ].includes(route.path);
});

// 下拉菜单命令处理
const handleCommand = (command: string) => {
  if (command === 'logout') {
    userStore.logout();
    router.push('/user/login');
  } else {
    router.push(`/user/${command}`);
  }
};

// 跳转到购物车
const goToCart = () => {
  router.push('/user/cart');
};
</script>

<style scoped lang="scss">
html, body {
  overflow: hidden; /* 禁止滚动条 */
}
.user-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 64px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  height: 40px;
  margin-right: 10px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-name {
  margin: 0 8px;
  font-size: 14px;
}

.user-content {
  display: flex;
  flex: 1;
  background-color: #f5f7fa;
}

.user-sidebar {
  width: 220px;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
}

.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.user-footer {
  text-align: center;
  padding: 20px;
  background-color: #fff;
  border-top: 1px solid #e6e6e6;
  color: #909399;
  font-size: 14px;
}

.el-menu-vertical {
  border-right: none;
}

.cart-badge {
  margin-right: 10px;
}
</style>