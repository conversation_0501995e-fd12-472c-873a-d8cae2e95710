<!--
  用户中心布局组件
  为用户模块提供统一的页面布局和导航结构
-->
<template>
  <div class="user-layout">
    <!-- 头部导航栏 -->
    <header class="header">
      <div class="header-container">
        <div class="logo-container">
          <router-link to="/">
            <img v-if="systemInfo?.siteLogo" :src="adjustLinkProtocol(systemInfo?.siteLogo)" alt="siteLogo" class="logo" />
          </router-link>
          <h1 class="site-title">用户中心</h1>
        </div>
        
        <div class="header-actions">
          <el-tooltip content="返回商城首页" placement="bottom">
            <el-button link @click="navigateTo('/')">
              <el-icon><House /></el-icon>
              首页
            </el-button>
          </el-tooltip>

          <!-- 聊天按钮 -->
          <el-tooltip content="客服消息" placement="bottom">
            <el-button link @click="toggleChat" class="chat-button">
              <el-icon><ChatDotRound /></el-icon>
              消息
              <el-badge
                v-if="unreadCount > 0"
                :value="unreadCount"
                :max="99"
                class="chat-badge"
              />
            </el-button>
          </el-tooltip>

          <el-dropdown trigger="click" @command="handleCommand">
            <div class="user-dropdown-link">
              <el-avatar :size="32" :src="userStore.avatar" class="user-avatar" />
              <span class="user-name">{{ userStore.nickname }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  账户设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>
    
    <!-- 主内容区域 -->
    <main class="main-content">
      <div class="main-container">
        <!-- 侧边栏导航 -->
        <div class="sidebar">
          <el-menu
            router
            :default-active="activeMenu"
            class="menu"
          >
            <el-menu-item index="/user/home">
              <el-icon><House /></el-icon>
              <span>用户首页</span>
            </el-menu-item>
            
            <el-sub-menu index="/user/takeout">
              <template #title>
                <el-icon><Goods /></el-icon>
                <span>外卖服务</span>
              </template>
              <el-menu-item index="/user/takeout">
                <el-icon><Shop /></el-icon>
                <span>外卖商家</span>
              </el-menu-item>
              <el-menu-item index="/user/takeout/cart">
                <el-icon><ShoppingCart /></el-icon>
                <span>购物车</span>
              </el-menu-item>
            </el-sub-menu>
            
            <el-menu-item index="/user/orders">
              <el-icon><List /></el-icon>
              <span>我的订单</span>
            </el-menu-item>
            
            <el-menu-item index="/user/addresses">
              <el-icon><Location /></el-icon>
              <span>收货地址</span>
            </el-menu-item>
            
            <el-menu-item index="/user/coupons">
              <el-icon><Ticket /></el-icon>
              <span>优惠券中心</span>
            </el-menu-item>
            
            <el-menu-item index="/user/profile">
              <el-icon><User /></el-icon>
              <span>个人资料</span>
            </el-menu-item>
            
            <el-menu-item index="/user/settings">
              <el-icon><Setting /></el-icon>
              <span>账户设置</span>
            </el-menu-item>

            <el-menu-item @click="openChat" class="chat-menu-item">
              <el-icon><ChatDotRound /></el-icon>
              <span>客服消息</span>
              <el-badge
                v-if="unreadCount > 0"
                :value="unreadCount"
                :max="99"
                class="menu-chat-badge"
              />
            </el-menu-item>

            <el-menu-item index="/user/referral/invite">
              <el-icon><Share /></el-icon>
              <span>邀请好友</span>
            </el-menu-item>

            <!-- Runner Menu Start -->
            <el-sub-menu v-if="runnerMenuItems.length > 0" index="user-runner-submenu"> <!-- Unique index for the submenu -->
              <template #title>
                <el-icon><component :is="runnerMenus[0].icon || 'Menu'" /></el-icon>
                <span>{{ runnerMenus[0].title }}</span>
              </template>
              <el-menu-item
                v-for="item in runnerMenuItems"
                :key="item.path"
                :index="item.path"
              >
                <el-icon>
                  <component :is="item.icon || 'Document'" />
                </el-icon>
                <span>{{ item.title }}</span>
              </el-menu-item>
            </el-sub-menu>
            <!-- Runner Menu End -->
            
            <!-- 动态菜单项 -->
            <!-- <template v-for="menuItem in dynamicMenuItems" :key="menuItem.path">
              <el-menu-item :index="menuItem.path">
                <el-icon>
                  <component :is="menuItem.icon || 'Document'" />
                </el-icon>
                <span>{{ menuItem.title }}</span>
              </el-menu-item>
            </template> -->
          </el-menu>
        </div>
        
        <!-- 内容区域 -->
        <div class="content">
          <!-- 页面头部 -->
          <div class="page-header">
            <h2 class="page-title">{{ pageTitle }}</h2>
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/user/home' }">用户中心</el-breadcrumb-item>
              <el-breadcrumb-item>{{ pageTitle }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <!-- 路由视图 -->
          <div class="page-content">
            <router-view v-slot="{ Component }">
              <transition name="fade" mode="out-in">
                <keep-alive :include="cachedViews">
                  <component :is="Component" />
                </keep-alive>
              </transition>
            </router-view>
          </div>
        </div>
      </div>
    </main>
    
    <!-- 底部版权信息 -->
    <footer class="footer">
      <div class="footer-container">
        <p>&copy; 2025 O_Mall 购物中心. 保留所有权利.</p>
      </div>
    </footer>
    
    <!-- 登出确认对话框 -->
    <el-dialog
      v-model="logoutDialogVisible"
      title="退出确认"
      width="380px"
    >
      <span>确定要退出登录吗？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="logoutDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmLogout" :loading="loggingOut">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 聊天窗口 -->
    <UnifiedChatWindow
      v-if="chatVisible"
      :visible="chatVisible"
      :minimized="chatMinimized"
      title="客服消息"
      @close="closeChat"
      @minimize="minimizeChat"
      @restore="restoreChat"
      @unread-change="handleUnreadChange"
      @position-change="handlePositionChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onBeforeUnmount, provide } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/modules/user/stores/userStore';
import { ElMessage } from 'element-plus';
import { Ticket, ChatDotRound } from '@element-plus/icons-vue';
import { runnerMenus } from '@/modules/user/router';
import { adjustLinkProtocol } from '@/utils/format';
import UnifiedChatWindow from '@/components/UnifiedChatWindow.vue';

import { useSystemStore } from '@/stores/systemStore';
// 移除用户WebSocket服务导入，统一使用chatStore
import { /* useSessionStore, */ useChatStore } from '@/modules/chat/stores';
import { getUnreadCount } from '@/modules/chat/api';
import { uploadConfigService } from '@/modules/chat/services/uploadConfig';
import { setupMessageHandlers, cleanupMessageHandlers } from '@/modules/chat/handlers';

const systemStore = useSystemStore();
const systemInfo = computed(() => systemStore.systemInfo);

// 聊天相关stores和服务
// const sessionStore = useSessionStore(); // 未使用，注释掉
const chatStore = useChatStore();

// 提供chatStore给子组件
provide('chatStore', chatStore);

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 登出对话框
const logoutDialogVisible = ref(false);
const loggingOut = ref(false);

// 聊天相关状态
const chatVisible = ref(false);
const chatMinimized = ref(false);

// 🔧 通知点击事件处理器
const handleOpenChatUI = (event: CustomEvent) => {
  const { sessionId } = event.detail
  console.log('🔔 [UserLayout] 收到打开聊天UI事件:', sessionId)

  // 打开聊天窗口
  if (!chatVisible.value) {
    openChat()
  }

  // 切换到指定会话
  if (sessionId) {
    chatStore.currentSessionId = sessionId
  }
}

// 计算未读消息总数
const unreadCount = computed(() => {
  // 优先使用chatStore中的totalUnreadCount
  return chatStore.totalUnreadCount || 0;
});



// 动态菜单项
const dynamicMenuItems = ref([]);

// 缓存的视图组件
const cachedViews = ref(['UserHome', 'UserProfile', 'UserOrders', 'UserAddresses', 'UserSettings']);

// 计算当前激活的菜单项
const activeMenu = computed(() => {
  return route.path;
});

// 计算当前页面标题
const pageTitle = computed(() => {
  return route.meta.title || '用户中心';
});

// Computed property for runner menu items
const runnerMenuItems = computed(() => {
  if (runnerMenus && runnerMenus.length > 0 && runnerMenus[0].children) {
    // Here you could also filter items based on userStore.isRunner and item.requiresRunner if needed
    return runnerMenus[0].children;
  }
  return [];
});

/**
 * 处理下拉菜单命令
 * @param command 命令
 */
function handleCommand(command: string) {
  switch (command) {
    case 'profile':
      navigateTo('/user/profile');
      break;
    case 'settings':
      navigateTo('/user/settings');
      break;
    case 'logout':
      showLogoutConfirm();
      break;
    default:
      break;
  }
}

/**
 * 导航到指定路径
 * @param path 路径
 */
function navigateTo(path: string) {
  router.push(path);
}

/**
 * 显示登出确认对话框
 */
function showLogoutConfirm() {
  logoutDialogVisible.value = true;
}

/**
 * 确认登出
 */
async function confirmLogout() {
  try {
    loggingOut.value = true;
    await userStore.userLogout();
    ElMessage.success('已安全退出登录');
    router.push('/user/login');
  } catch (error) {
    console.error('登出失败:', error);
    ElMessage.error('登出失败，请稍后重试');
  } finally {
    loggingOut.value = false;
    logoutDialogVisible.value = false;
  }
}

/**
 * 聊天相关方法
 */
function toggleChat() {
  if (chatVisible.value) {
    if (chatMinimized.value) {
      restoreChat();
    } else {
      minimizeChat();
    }
  } else {
    openChat();
  }
}

function openChat() {
  chatVisible.value = true;
  chatMinimized.value = false;
  // 同步聊天UI状态到chatStore
  chatStore.setChatUIVisible(true);
}

function closeChat() {
  chatVisible.value = false;
  chatMinimized.value = false;
  // 同步聊天UI状态到chatStore
  chatStore.setChatUIVisible(false);
}

function minimizeChat() {
  chatMinimized.value = true;
}

function restoreChat() {
  chatMinimized.value = false;
}

function handleUnreadChange(count: number) {
  console.log('用户聊天窗口未读消息数量变化:', count);
}

function handlePositionChange(x: number, y: number) {
  // console.log('聊天窗口位置变化:', { x, y });
  // 可以在这里保存窗口位置到localStorage
  localStorage.setItem('chatWindowPosition', JSON.stringify({ x, y }));
}

function handleSizeChange(width: number, height: number) {
  // console.log('聊天窗口大小变化:', { width, height });
  // 可以在这里保存窗口大小到localStorage
  localStorage.setItem('chatWindowSize', JSON.stringify({ width, height }));
}

/**
 * 获取动态菜单项
 */
async function fetchDynamicMenuItems() {
  try {
    // 这里应该从API获取动态菜单数据
    // 实际项目中，可能需要从后端获取用户可访问的模块和页面
    // 以下为模拟数据
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 这只是示例数据，实际项目中应该从API获取
    dynamicMenuItems.value = [
      // 以下是动态菜单示例，实际项目中应该从API获取
      // {
      //   path: '/user/favorites',
      //   title: '我的收藏',
      //   icon: 'Star'
      // }
    ];
  } catch (error) {
    console.error('获取动态菜单失败:', error);
  }
}

// 检查用户登录状态
async function checkLoginStatus() {
  console.log('🔍 开始检查用户登录状态');

  // 先尝试从存储中恢复用户状态
  try {
    const restored = await userStore.loadUserFromStorage();
    console.log('📦 用户状态恢复结果:', restored);

    if (restored && userStore.isLoggedIn) {
      console.log('✅ 用户已登录，用户信息:', {
        isLoggedIn: userStore.isLoggedIn,
        userId: userStore.userInfo?.id,
        username: userStore.userInfo?.username,
        hasToken: !!userStore.token,
        tokenExpiryTime: userStore.tokenExpiryTime
      });
      console.log(userStore.token, userStore.tokenExpiryTime)
      // 确保token刷新定时器正常启动
      if (userStore.token && userStore.tokenExpiryTime) {
        const now = Date.now();
        const remainingSeconds = Math.floor((userStore.tokenExpiryTime - now) / 1000);
        if (remainingSeconds > 0) {
          console.log('🔄 启动token刷新定时器，剩余时间:', remainingSeconds, '秒');
          userStore.startTokenRefreshTimer(remainingSeconds);
        } else {
          console.log('⚠️ Token已过期，立即刷新');
          userStore.autoRefreshToken();
        }
      }
      
      return true;
    }
  } catch (error) {
    console.error('恢复用户状态失败:', error);
  }

  // 如果恢复失败或用户未登录，尝试使用长期token登录
  try {
    console.log('🔄 尝试使用长期token登录');
    const longTermLoginSuccess = await userStore.loginByLongTermTokenAction();

    if (longTermLoginSuccess && userStore.isLoggedIn) {
      console.log('✅ 长期token登录成功');
      
      // 长期token登录成功后也要确保启动定时器
      if (userStore.token && userStore.tokenExpiryTime) {
        const now = Date.now();
        const remainingSeconds = Math.floor((userStore.tokenExpiryTime - now) / 1000);
        if (remainingSeconds > 0) {
          console.log('🔄 长期token登录后启动token刷新定时器，剩余时间:', remainingSeconds, '秒');
          userStore.startTokenRefreshTimer(remainingSeconds);
        }
      }
      
      return true;
    }
  } catch (error) {
    console.error('长期token登录失败:', error);
  }

  // 如果所有尝试都失败，重定向到登录页面
  console.log('❌ 用户未登录，重定向到登录页面');
  ElMessage.warning('请先登录');
  router.push('/user/login');
  return false;
}

// 通用的未读消息API调用函数，支持重试
const callUnreadCountAPI = async (retries = 3, delay = 1000) => {
  for (let i = 0; i < retries; i++) {
    try {
      console.log(`📊 第${i + 1}次尝试调用getUnreadCount API...`);
      const startTime = Date.now();
      const unreadData = await getUnreadCount();
      const endTime = Date.now();
      
      console.log('📊 getUnreadCount API调用成功:', {
        duration: `${endTime - startTime}ms`,
        response: unreadData,
        responseType: typeof unreadData,
        attempt: i + 1
      });
      
      const totalUnread = typeof unreadData === 'number' ? unreadData : (unreadData?.total || 0);
      
      if (chatStore && typeof chatStore.setTotalUnreadCount === 'function') {
        chatStore.setTotalUnreadCount(totalUnread);
        console.log('✅ 用户未读消息总数已更新:', totalUnread);
      } else {
        console.warn('⚠️ chatStore.setTotalUnreadCount方法不存在');
      }
      
      return totalUnread;
    } catch (error: any) {
      console.error(`❌ 第${i + 1}次API调用失败:`, {
        error: error?.message || error,
        stack: error?.stack
      });
      
      if (i < retries - 1) {
        console.log(`⏳ ${delay}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  console.error('❌ 所有重试都失败，设置默认未读数量为0');
  if (chatStore && typeof chatStore.setTotalUnreadCount === 'function') {
    chatStore.setTotalUnreadCount(0);
  }
  return 0;
};

// 初始化聊天服务
const initializeChatService = async () => {
  try {
    console.log('🚀 开始初始化用户聊天服务');
    console.log('🔍 用户登录状态检查:', {
      isLoggedIn: userStore.isLoggedIn,
      hasUserInfo: !!userStore.userInfo,
      hasToken: !!userStore.token,
      userId: userStore.userInfo?.id
    });

    // 修改条件：只要用户已登录且有token就尝试初始化
    if (!userStore.isLoggedIn || !userStore.token) {
      console.warn('⚠️ 用户聊天服务初始化条件不满足:', {
        isLoggedIn: userStore.isLoggedIn,
        hasToken: !!userStore.token,
        reason: !userStore.isLoggedIn ? '用户未登录' : '缺少认证token'
      });
      return;
    }

    // 如果用户信息不完整，尝试获取（但不阻止初始化）
    if (!userStore.userInfo || !userStore.userInfo.id) {
      console.log('🔄 用户信息不完整，尝试获取用户信息...');
      try {
        await userStore.fetchUserInfo();
        console.log('✅ 用户信息获取完成');
      } catch (error) {
        console.warn('⚠️ 获取用户信息失败，但继续初始化聊天服务:', error);
      }
    }

    console.log('🔗 开始初始化统一聊天服务');

    // 初始化消息处理器
    try {
      console.log('🔧 初始化用户消息处理器...');
      const messageRouter = await setupMessageHandlers('user', {
        debug: import.meta.env.DEV,
        timeout: 10000
      });

      // 将消息路由器集成到聊天store中
      if (typeof chatStore.setMessageRouter === 'function') {
        chatStore.setMessageRouter(messageRouter);
      } else {
        console.warn('⚠️ chatStore.setMessageRouter方法不存在，消息路由器将在后续版本中集成');
      }

      console.log('✅ 用户消息处理器初始化完成');
    } catch (handlerError) {
      console.error('❌ 消息处理器初始化失败:', handlerError);
    }

    // 初始化聊天store
    try {
      const initStartTime = Date.now();
      await chatStore.initializeChat({
        userType: 'user',
        userId: userStore.userInfo?.id
      });
      const initEndTime = Date.now();
      
      console.log('✅ 用户聊天store初始化完成，耗时:', `${initEndTime - initStartTime}ms`);
      console.log('📊 初始化后的聊天状态:', {
        isConnected: chatStore.isConnected,
        totalUnreadCount: chatStore.totalUnreadCount,
        error: chatStore.error
      });
    } catch (initError) {
      console.error('❌ 用户聊天store初始化失败:', initError);
      // 即使初始化失败，也继续尝试获取未读消息
    }
    
    // 获取未读消息数量（使用重试机制）
    await callUnreadCountAPI();
    
    console.log('✅ 用户聊天服务初始化完成');
  } catch (error) {
    console.error('💥 初始化用户聊天服务失败:', error);
  }
};

// 监听用户登录状态变化
watch(() => userStore.isLoggedIn, async (isLoggedIn, wasLoggedIn) => {
  console.log('🔄 [UserLayout] 用户登录状态变化:', { isLoggedIn, wasLoggedIn, isConnected: chatStore.isConnected });

  if (isLoggedIn && !wasLoggedIn) {
    // 用户从未登录变为已登录，初始化聊天服务
    console.log('✅ [UserLayout] 用户登录，初始化聊天服务');
    await initializeChatService();
  } else if (!isLoggedIn && wasLoggedIn) {
    // 用户从已登录变为未登录，断开聊天连接
    console.log('👋 [UserLayout] 用户登出，断开聊天连接');
    if (chatStore.isConnected) {
      await chatStore.disconnectChat();
    }
    // 关闭聊天窗口
    closeChat();
  }
  // 如果都是true或都是false，说明是重复触发，不处理
});

onMounted(async () => {
  console.log('UserLayout mounted', systemInfo.value);

  // 设置聊天上传配置为用户类型
  uploadConfigService.setUserType('user');
  console.log('🔧 UserLayout: 聊天上传配置设置为用户类型');

  // 🔧 添加通知点击事件监听器
  window.addEventListener('openChatUI', handleOpenChatUI as EventListener)
  console.log('🔧 [UserLayout] 已添加通知点击事件监听器')

  // 检查登录状态
  checkLoginStatus();

  // 获取动态菜单
  fetchDynamicMenuItems();

  // 如果用户已登录且聊天服务未连接，初始化聊天服务
  if (userStore.isLoggedIn && !chatStore.isConnected && !chatStore.isInitializing) {
    console.log('🚀 [UserLayout] 组件挂载时用户已登录，初始化聊天服务');
    await initializeChatService();
  } else {
    console.log('ℹ️ [UserLayout] 跳过聊天服务初始化:', {
      isLoggedIn: userStore.isLoggedIn,
      isConnected: chatStore.isConnected,
      isInitializing: chatStore.isInitializing
    });
  }

  // 开发环境下暴露调试函数
  if (import.meta.env.DEV) {
    (window as any).debugUserChatService = () => {
      console.log('🔍 用户聊天服务调试信息:', {
        isLoggedIn: userStore.isLoggedIn,
        userInfo: userStore.userInfo,
        hasToken: !!userStore.token,
        tokenLength: userStore.token?.length,
        chatStore: {
          isConnected: chatStore.isConnected,
          totalUnreadCount: chatStore.totalUnreadCount
        },
        chatVisible: chatVisible.value,
        unreadCount: unreadCount.value
      });
    };

    (window as any).reconnectUserChat = async () => {
      console.log('🔄 手动重连用户聊天服务');
      await chatStore.disconnectChat();
      await new Promise(resolve => setTimeout(resolve, 1000));
      await chatStore.initializeChat({
        userType: 'user',
        userId: userStore.userInfo?.id
      });
    };

    console.log('💡 用户聊天调试函数已暴露:');
    console.log('  - window.debugUserChatService()');
    console.log('  - window.reconnectUserChat()');
  }
});

// 组件卸载前清理资源
onBeforeUnmount(() => {
  // 🔧 清理通知点击事件监听器
  window.removeEventListener('openChatUI', handleOpenChatUI as EventListener)
  console.log('🔧 [UserLayout] 已清理通知点击事件监听器')

  // 清理消息处理器
  try {
    cleanupMessageHandlers();
    console.log('🧹 用户消息处理器已清理');
  } catch (error) {
    console.error('❌ 清理消息处理器失败:', error);
  }

  // 注意：不要在这里断开WebSocket连接！
  // WebSocket连接应该作为后台服务持续运行，只有在用户登出时才断开
  // 用户可能只是导航到其他页面，聊天服务应该继续运行以接收消息
  console.log('🧹 UserLayout卸载，保持WebSocket连接运行');
});
</script>

<style scoped>
.user-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header {
  background-color: #ffffff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  height: 60px;
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.site-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-dropdown-link {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown-link:hover {
  background-color: #f5f7fa;
}

.user-avatar {
  margin-right: 8px;
}

.user-name {
  margin-right: 5px;
  font-size: 14px;
}

.main-content {
  flex: 1;
  margin-top: 60px;
}

.main-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  gap: 20px;
}

.sidebar {
  width: 220px;
  position: sticky;
  top: 80px;
  height: fit-content;
}

.menu {
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.content {
  flex: 1;
  overflow: hidden;
}

.page-header {
  background-color: #ffffff;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.page-content {
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  padding: 20px;
  min-height: 500px;
}

.footer {
  background-color: #ffffff;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);
  margin-top: 30px;
  padding: 20px 0;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
  color: #909399;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 聊天按钮样式 */
.chat-button {
  position: relative;
  color: #3b82f6;
  font-weight: 500;
}

.chat-button:hover {
  color: #2563eb;
  background-color: rgba(59, 130, 246, 0.1);
}

.chat-badge {
  position: absolute;
  top: -2px;
  right: -2px;
}

/* 菜单中的聊天项样式 */
.chat-menu-item {
  position: relative;
}

.menu-chat-badge {
  position: absolute;
  top: 8px;
  right: 16px;
  z-index: 10;
}

.chat-menu-item:hover {
  background-color: rgba(59, 130, 246, 0.1);
}
</style>
