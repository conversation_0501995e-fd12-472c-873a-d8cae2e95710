<template>
  <div class="admin-layout">
    <el-container class="layout-container">
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '220px'" class="aside">
        <div class="logo">
          <img v-if="systemInfo?.siteLogo" :src="adjustLinkProtocol(systemInfo?.siteLogo)" alt="Logo" class="logo-img" />
          <h2 class="logo-text" v-if="!isCollapse">{{ systemInfo?.siteName || 'O_Mall 管理后台' }}</h2>
        </div>
        <el-scrollbar>
          <el-menu
            :default-active="activeMenu"
            class="el-menu-vertical"
            :router="true"
            :collapse="isCollapse"
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
          >
            <template v-for="item in menuItems" :key="item.key">
              <!-- 有子菜单的情况 -->
              <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.path">
                <template #title>
                  <el-icon v-if="item.icon">
                    <component :is="item.icon" />
                  </el-icon>
                  <span>{{ item.label }}</span>
                </template>
                <el-menu-item 
                  v-for="child in item.children" 
                  :key="child.key" 
                  :index="child.path"
                >
                  <span>{{ child.label }}</span>
                </el-menu-item>
              </el-sub-menu>
              
              <!-- 没有子菜单的情况 -->
              <el-menu-item v-else :index="item.path">
                <el-icon v-if="item.icon">
                  <component :is="item.icon" />
                </el-icon>
                <template #title>{{ item.label }}</template>
              </el-menu-item>
            </template>
          </el-menu>
        </el-scrollbar>
      </el-aside>
      
      <el-container class="main-container">
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-icon class="toggle-icon" @click="toggleSidebar">
              <Fold v-if="!isCollapse" />
              <Expand v-else />
            </el-icon>
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/admin/dashboard' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item v-if="currentRoute.meta.title">
                {{ currentRoute.meta.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <!-- 聊天按钮 -->
            <el-tooltip content="客服消息" placement="bottom">
              <el-button link @click="toggleChat" class="chat-button">
                <el-icon><ChatDotRound /></el-icon>
                消息
                <el-badge
                  v-if="unreadCount > 0"
                  :value="unreadCount"
                  :max="99"
                  class="chat-badge"
                />
              </el-button>
            </el-tooltip>

            <el-dropdown trigger="click">
              <div class="avatar-container">
                <el-avatar :size="32" :src="adminInfo.avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="username">{{ adminInfo.nickname }}</span>
                <el-icon><CaretBottom /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="navigateToProfile">个人信息</el-dropdown-item>
                  <el-dropdown-item @click="openChat" class="chat-menu-item">
                    <el-icon><ChatDotRound /></el-icon>
                    <span>客服消息</span>
                    <el-badge
                      v-if="unreadCount > 0"
                      :value="unreadCount"
                      :max="99"
                      class="menu-chat-badge"
                    />
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 内容区域 -->
        <el-main class="main">
          <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>

    <!-- 聊天窗口 -->
    <UnifiedChatWindow
      v-if="chatVisible"
      :visible="chatVisible"
      :minimized="chatMinimized"
      title="管理员消息"
      @close="closeChat"
      @minimize="minimizeChat"
      @restore="restoreChat"
      @unread-change="handleUnreadChange"
      @position-change="handlePositionChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onBeforeUnmount, provide } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import { Fold, Expand, User, CaretBottom, ChatDotRound } from '@element-plus/icons-vue';
import { ADMIN_MENU_ITEMS } from '../modules/admin/constants';
import { useAdminStore } from '../modules/admin/stores/adminStore';
import { useSystemStore } from '@/stores/systemStore';
import { adjustLinkProtocol } from '@/utils/format';
import UnifiedChatWindow from '@/components/UnifiedChatWindow.vue';
import { useChatStore } from '@/modules/chat/stores';
import { getUnreadCount } from '@/modules/chat/api';
import { uploadConfigService } from '@/modules/chat/services/uploadConfig';
import { setupMessageHandlers, cleanupMessageHandlers } from '@/modules/chat/handlers';
import localforage from 'localforage';

// 定义菜单项和路径数据的接口
interface MenuItem {
  key: string;
  label: string;
  icon?: string;
  path: string;
  children?: MenuItem[];
  [key: string]: any; // 允许添加额外属性如dynamic
}

interface PathItem {
  icon?: string;
  path: string;
  title: string;
  count: number;
  [key: string]: any;
}

interface ModuleData {
  module: string;
  paths: PathItem[];
}

const systemStore = useSystemStore();
const systemInfo = computed(() => systemStore.systemInfo);
const route = useRoute();
const router = useRouter();
const adminStore = useAdminStore();
const chatStore = useChatStore();

// 提供chatStore给子组件
provide('chatStore', chatStore);

const isCollapse = ref(false);
const staticMenuItems = ref<MenuItem[]>(ADMIN_MENU_ITEMS);
const menuItems = ref<MenuItem[]>([...ADMIN_MENU_ITEMS]);

// 聊天相关状态
const chatVisible = ref(false);
const chatMinimized = ref(false);

// 🔧 通知点击事件处理器
const handleOpenChatUI = (event: CustomEvent) => {
  const { sessionId } = event.detail
  console.log('🔔 [AdminLayout] 收到打开聊天UI事件:', sessionId)

  // 打开聊天窗口
  if (!chatVisible.value) {
    openChat()
  }

  // 切换到指定会话
  if (sessionId) {
    chatStore.currentSessionId = sessionId
  }
}

// 计算未读消息总数
const unreadCount = computed(() => {
  // 优先使用chatStore中的totalUnreadCount
  return chatStore.totalUnreadCount || 0;
});

// 处理前端路径数据，生成动态菜单项
const generateDynamicMenuItems = (frontendPaths: ModuleData[]) => {
  if (!frontendPaths || !Array.isArray(frontendPaths) || frontendPaths.length === 0) {
    console.log('前端路径数据为空，使用静态菜单');
    return staticMenuItems.value;
  }

  const newMenuItems: MenuItem[] = [...staticMenuItems.value];
  
  // 处理前端路径数据
  frontendPaths.forEach(moduleData => {
    if (!moduleData.module || !moduleData.paths || !Array.isArray(moduleData.paths)) {
      return;
    }
    
    // 根据模块生成子菜单
    const moduleName = moduleData.module;
    const modulePaths = moduleData.paths;
    
    // 跳过空路径
    if (modulePaths.length === 0) return;
    
    // 根据模块名称创建不同的菜单结构
    switch(moduleName) {
      case 'admin':
        // admin模块的路径直接添加到顶级菜单
        modulePaths.forEach((pathItem: PathItem) => {
          console.log('处理admin模块路径:', pathItem);
          // 检查是否已存在相同路径的菜单项（避免重复）
          const existingItem = newMenuItems.find(item => item.path === `/admin/${pathItem.path}`);
          if (!existingItem && pathItem.path && pathItem.title) {
            newMenuItems.push({
              key: pathItem.path,
              label: pathItem.title,
              icon: pathItem.icon || 'Menu', // 使用路径中的图标或默认图标
              path: `/admin/${pathItem.path}`,
              dynamic: true // 标记为动态生成的
            });
          }
        });
        break;
        
      case 'ui_config':
        // UI配置模块添加到系统设置子菜单下
        const systemMenu = newMenuItems.find(item => item.key === 'system');
        if (systemMenu && systemMenu.children) {
          modulePaths.forEach((pathItem: PathItem) => {
            // 检查是否已存在相同路径的菜单项
            const existingItem = systemMenu.children?.find(child => 
              child.path === `/admin/system/${pathItem.path}`
            );
            
            if (!existingItem && pathItem.path && pathItem.title) {
              systemMenu.children?.push({
                key: pathItem.path,
                label: pathItem.title,
                icon: pathItem.icon || undefined, // 使用路径中的图标或不设置（继承父级）
                path: `/admin/system/${pathItem.path}`,
                dynamic: true // 标记为动态生成的
              });
            }
          });
        }
        break;
        
      default:
        // 其他模块创建新的顶级菜单
        const moduleMenu: MenuItem = {
          key: moduleName,
          label: moduleName.charAt(0).toUpperCase() + moduleName.slice(1), // 首字母大写
          icon: 'Menu',
          path: `/admin/${moduleName}`,
          children: modulePaths.map((pathItem: PathItem) => ({
            key: pathItem.path,
            label: pathItem.title,
            icon: pathItem.icon || undefined, // 使用路径中的图标或不设置（继承父级）
            path: `/admin/${moduleName}/${pathItem.path}`,
            dynamic: true
          }))
        };
        
        // 检查是否已存在相同模块名的菜单
        const existingModule = newMenuItems.find(item => item.key === moduleName);
        if (!existingModule) {
          newMenuItems.push(moduleMenu);
        }
        break;
    }
  });
  
  console.log('生成的动态菜单：', newMenuItems);
  return newMenuItems;
};

// 监听前端路径数据变化，更新菜单
watch(() => adminStore.frontendPaths, (newPaths) => {
  console.log('监听到frontendPaths变化:', newPaths);
  if (newPaths && newPaths.length > 0) {
    console.log('前端路径数据已更新，重新生成菜单', newPaths);
    menuItems.value = generateDynamicMenuItems(newPaths);
  }
}, { deep: true, immediate: true });

// 监听路由变化，确保菜单状态同步
watch(() => route.path, (newPath, oldPath) => {
  console.log(`🔄 AdminLayout 路由变化: ${oldPath} -> ${newPath}`);
  
  // 如果是跨模块路由切换，重新获取菜单数据
  const oldModule = oldPath?.split('/')[2];
  const newModule = newPath?.split('/')[2];
  
  if (oldModule !== newModule) {
    console.log(`🔄 跨模块路由切换: ${oldModule} -> ${newModule}`);
    
    // 延迟一点时间确保路由完全切换
    setTimeout(async () => {
      try {
        // 重新获取前端路径数据
        const paths = await adminStore.fetchFrontendPaths(false);
        if (paths && paths.length > 0) {
          menuItems.value = generateDynamicMenuItems(paths);
          console.log('✅ 菜单数据已同步');
        }
      } catch (error) {
        console.error('❌ 同步菜单数据失败:', error);
      }
    }, 100);
  }
}, { immediate: false });

// 当前路由信息
const currentRoute = computed(() => route);

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path;
});

// 管理员信息
const adminInfo = computed(() => {
  return adminStore.currentAdmin || {
    nickname: '管理员',
    avatar: '',
  };
});

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value;
};

// 处理退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      adminStore.logout();
      router.push('/admin/login');
    })
    .catch(() => {});
};

// 跳转到个人信息页面
const navigateToProfile = () => {
  router.push('/admin/profile');
};

/**
 * 聊天相关方法
 */
function toggleChat() {
  if (chatVisible.value) {
    if (chatMinimized.value) {
      restoreChat();
    } else {
      minimizeChat();
    }
  } else {
    openChat();
  }
}

function openChat() {
  chatVisible.value = true;
  chatMinimized.value = false;
  // 同步聊天UI状态到chatStore
  chatStore.setChatUIVisible(true);
}

function closeChat() {
  chatVisible.value = false;
  chatMinimized.value = false;
  // 同步聊天UI状态到chatStore
  chatStore.setChatUIVisible(false);
}

function minimizeChat() {
  chatMinimized.value = true;
}

function restoreChat() {
  chatMinimized.value = false;
}

function handleUnreadChange(count: number) {
  console.log('管理员聊天窗口未读消息数量变化:', count);
}

function handlePositionChange(x: number, y: number) {
  // console.log('聊天窗口位置变化:', { x, y });
  // 可以在这里保存窗口位置到localStorage
  localStorage.setItem('adminChatWindowPosition', JSON.stringify({ x, y }));
}

function handleSizeChange(width: number, height: number) {
  // console.log('聊天窗口大小变化:', { width, height });
  // 可以在这里保存窗口大小到localStorage
  localStorage.setItem('adminChatWindowSize', JSON.stringify({ width, height }));
}

// 刷新前端路径数据
// const refreshMenuData = async () => {
//   try {
//     // 清除缓存并重新获取
//     await adminStore.clearFrontendPathsCache();
//     const paths = await adminStore.fetchFrontendPaths();
//     console.log('刷新获取的前端路径数据:', paths);
//     if (paths && paths.length > 0) {
//       menuItems.value = generateDynamicMenuItems(paths);
//     } else {
//       console.warn('获取的前端路径数据为空');
//     }
//   } catch (error) {
//     console.error('刷新菜单数据失败:', error);
//   }
// };

// 获取未读消息数量的API调用（带重试机制）
const callUnreadCountAPI = async (retries = 3) => {
  for (let i = 0; i < retries; i++) {
    try {
      console.log(`🔧 [callUnreadCountAPI] 第${i + 1}次尝试获取管理员未读消息数量`);
      const unreadData = await getUnreadCount();
      console.log('🔧 [callUnreadCountAPI] API返回的原始数据:', {
        unreadData,
        type: typeof unreadData,
        attempt: i + 1
      });

      const totalUnread = typeof unreadData === 'number' ? unreadData : (unreadData?.total || 0);

      if (chatStore && typeof chatStore.setTotalUnreadCount === 'function') {
        chatStore.setTotalUnreadCount(totalUnread);
        console.log('✅ 管理员未读消息总数已更新:', totalUnread);
      } else {
        console.warn('⚠️ chatStore.setTotalUnreadCount方法不存在');
      }

      return totalUnread;
    } catch (error: any) {
      console.error(`❌ 第${i + 1}次API调用失败:`, {
        error: error?.message || error,
        stack: error?.stack
      });

      if (i < retries - 1) {
        console.log(`⏳ 等待1秒后进行第${i + 2}次重试...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  console.error('❌ 所有重试都失败，设置默认未读数量为0');
  if (chatStore && typeof chatStore.setTotalUnreadCount === 'function') {
    chatStore.setTotalUnreadCount(0);
  }
  return 0;
};

// 初始化聊天服务
const initializeChatService = async () => {
  try {
    console.log('🚀 开始初始化管理员聊天服务');

    // 直接从localforage检查token状态
    const tokenFromStorage = await localforage.getItem('admin_access_token');
    const hasStorageToken = !!(tokenFromStorage && String(tokenFromStorage).trim());

    console.log('🔍 管理员登录状态检查:', {
      hasAdminInfo: !!adminStore.currentAdmin,
      hasTokenFromStorage: hasStorageToken,
      hasAdminStoreToken: !!adminStore.token,
      adminId: adminStore.currentAdmin?.id,
      tokenLength: tokenFromStorage ? String(tokenFromStorage).length : 0
    });

    // 修改条件：只要localforage中有token就尝试初始化
    if (hasStorageToken) {
      console.log('✅ 管理员登录状态验证通过，开始初始化聊天服务');

      // 设置聊天上传配置为商家类型（管理员使用商家接口）
      uploadConfigService.setUserType('merchant');
      console.log('🔧 AdminLayout: 聊天上传配置设置为商家类型（管理员使用商家接口）');

      console.log('🔗 开始初始化统一聊天服务');

      // 初始化消息处理器
      try {
        console.log('🔧 初始化管理员消息处理器...');
        const messageRouter = await setupMessageHandlers('admin', {
          debug: import.meta.env.DEV,
          timeout: 10000
        });

        // 将消息路由器集成到聊天store中
        // 注意：这里需要在chatStore中添加setMessageRouter方法
        // 或者通过其他方式将消息路由器传递给WebSocket服务
        if (typeof chatStore.setMessageRouter === 'function') {
          chatStore.setMessageRouter(messageRouter);
        } else {
          console.warn('⚠️ chatStore.setMessageRouter方法不存在，消息路由器将在后续版本中集成');
        }

        console.log('✅ 管理员消息处理器初始化完成');
      } catch (handlerError) {
        console.error('❌ 消息处理器初始化失败:', handlerError);
      }

      // 初始化聊天store
      try {
        const initStartTime = Date.now();
        await chatStore.initializeChat({
          userType: 'admin',
          userId: adminStore.currentAdmin?.id
        });
        const initEndTime = Date.now();

        console.log('✅ 管理员聊天store初始化完成，耗时:', `${initEndTime - initStartTime}ms`);
        console.log('📊 初始化后的聊天状态:', {
          isConnected: chatStore.isConnected,
          totalUnreadCount: chatStore.totalUnreadCount,
          error: chatStore.error
        });
      } catch (initError) {
        console.error('❌ 管理员聊天store初始化失败:', initError);
        // 即使初始化失败，也继续尝试获取未读消息
      }

      // 获取未读消息数量（使用重试机制）
      await callUnreadCountAPI();

      console.log('✅ 管理员聊天服务初始化完成');
    } else {
      console.warn('⚠️ 管理员localforage中无token，跳过聊天服务初始化');
    }
  } catch (error) {
    console.error('❌ 管理员聊天服务初始化失败:', error);
  }
};

// 监听管理员登录状态变化
watch(() => adminStore.token, async (token, oldToken) => {
  console.log('🔄 [AdminLayout] 管理员token变化:', {
    oldToken: oldToken ? `${oldToken.substring(0, 10)}...` : 'null',
    newToken: token ? `${token.substring(0, 10)}...` : 'null',
    hasToken: !!token,
    isConnected: chatStore.isConnected,
    isInitializing: chatStore.isInitializing
  });

  if (token && !oldToken && !chatStore.isConnected && !chatStore.isInitializing) {
    // 首次获取到token（从localforage加载完成）且聊天服务未连接
    console.log('✅ [AdminLayout] 管理员token首次加载完成，初始化聊天服务');
    await initializeChatService();
  } else if (token && oldToken && token !== oldToken) {
    // token更新（重新登录）
    console.log('🔄 [AdminLayout] 管理员token更新，重新初始化聊天服务');
    // 先断开旧连接
    if (chatStore.isConnected) {
      await chatStore.disconnectChat();
    }
    // 等待断开完成后重新初始化
    await new Promise(resolve => setTimeout(resolve, 500));
    await initializeChatService();
  } else if (!token && oldToken) {
    // 管理员登出
    console.log('👋 [AdminLayout] 管理员登出，断开聊天连接');
    if (chatStore.isConnected) {
      await chatStore.disconnectChat();
    }
    // 关闭聊天窗口
    closeChat();
  } else {
    console.log('ℹ️ [AdminLayout] 跳过token变化处理:', {
      reason: !token ? '无token' :
              chatStore.isConnected ? '已连接' :
              chatStore.isInitializing ? '正在初始化' : '其他'
    });
  }
});

onMounted(async () => {
  console.log('AdminLayout mounted', systemInfo.value);

  // 设置聊天上传配置为商家类型（管理员使用商家接口）
  uploadConfigService.setUserType('merchant');
  console.log('🔧 AdminLayout: 聊天上传配置设置为商家类型（管理员使用商家接口）');

  // 🔧 添加通知点击事件监听器
  window.addEventListener('openChatUI', handleOpenChatUI as EventListener)
  console.log('🔧 [AdminLayout] 已添加通知点击事件监听器')

  // 直接从localforage检查token（避免依赖异步初始化的adminStore.token）
  console.log('🔍 直接从localforage检查管理员token...');
  const tokenFromStorage = await localforage.getItem('admin_access_token');
  const hasToken = !!(tokenFromStorage && String(tokenFromStorage).trim());

  console.log('� 管理员token检查结果:', {
    hasTokenFromStorage: !!tokenFromStorage,
    tokenLength: tokenFromStorage ? String(tokenFromStorage).length : 0,
    adminStoreToken: !!adminStore.token,
    adminStoreTokenLength: adminStore.token?.length || 0
  });

  // 如果有token但没有管理员信息，则获取管理员信息
  if (hasToken && !adminStore.currentAdmin) {
    console.log('🔄 获取管理员信息...');
    await adminStore.fetchCurrentAdmin();
  }

  // 获取前端路径并生成菜单
  try {
    // 直接强制重新获取前端路径数据，避免使用缓存导致的问题
    console.log('组件挂载时重新获取前端路径数据');
    // 先清除缓存，确保获取最新数据
    await adminStore.clearFrontendPathsCache();
    const paths = await adminStore.fetchFrontendPaths();
    console.log('获取到最新前端路径数据:', paths);

    if (paths && paths.length > 0) {
      menuItems.value = generateDynamicMenuItems(paths);
    } else {
      console.warn('获取的前端路径数据为空，尝试重新获取...');
      // 延迟再试一次
      setTimeout(async () => {
        const retryPaths = await adminStore.fetchFrontendPaths();
        if (retryPaths && retryPaths.length > 0) {
          menuItems.value = generateDynamicMenuItems(retryPaths);
        }
      }, 1000);
    }
  } catch (error) {
    console.error('获取前端路径失败', error);
  }

  // 如果管理员已登录且聊天服务未连接，初始化聊天服务
  if (hasToken && !chatStore.isConnected && !chatStore.isInitializing) {
    console.log('🚀 [AdminLayout] 组件挂载时管理员已登录，初始化聊天服务');
    await initializeChatService();
  } else {
    console.log('ℹ️ [AdminLayout] 跳过聊天服务初始化:', {
      hasToken,
      isConnected: chatStore.isConnected,
      isInitializing: chatStore.isInitializing
    });
  }

  // 开发环境下暴露调试函数
  if (import.meta.env.DEV) {
    (window as any).debugAdminChatService = () => {
      console.log('🔍 管理员聊天服务调试信息:', {
        hasToken: !!adminStore.token,
        adminInfo: adminStore.currentAdmin,
        tokenLength: adminStore.token?.length,
        chatStore: {
          isConnected: chatStore.isConnected,
          totalUnreadCount: chatStore.totalUnreadCount
        },
        chatVisible: chatVisible.value,
        unreadCount: unreadCount.value
      });
    };

    (window as any).reconnectAdminChat = async () => {
      console.log('🔄 手动重连管理员聊天服务');
      await chatStore.disconnectChat();
      await new Promise(resolve => setTimeout(resolve, 1000));
      await chatStore.initializeChat({
        userType: 'admin',
        userId: adminStore.currentAdmin?.id
      });
    };

    (window as any).forceInitAdminChat = async () => {
      console.log('� 强制初始化管理员聊天服务');
      await initializeChatService();
    };

    console.log('�💡 管理员聊天调试函数已暴露:');
    console.log('  - window.debugAdminChatService()');
    console.log('  - window.reconnectAdminChat()');
    console.log('  - window.forceInitAdminChat()');
  }

  // 延迟检查：如果5秒后聊天服务仍未连接且有token，则尝试重新初始化
  setTimeout(async () => {
    const tokenFromStorage = await localforage.getItem('admin_access_token');
    const hasStorageToken = !!(tokenFromStorage && String(tokenFromStorage).trim());

    if (hasStorageToken && !chatStore.isConnected && !chatStore.isInitializing) {
      console.log('⚠️ [AdminLayout] 延迟检查发现聊天服务未连接，尝试重新初始化');
      await initializeChatService();
    } else {
      console.log('ℹ️ [AdminLayout] 延迟检查跳过重新初始化:', {
        hasStorageToken,
        isConnected: chatStore.isConnected,
        isInitializing: chatStore.isInitializing
      });
    }
  }, 5000);
});

// 组件卸载前清理资源
onBeforeUnmount(() => {
  // 🔧 清理通知点击事件监听器
  window.removeEventListener('openChatUI', handleOpenChatUI as EventListener)
  console.log('🔧 [AdminLayout] 已清理通知点击事件监听器')

  // 清理消息处理器
  try {
    cleanupMessageHandlers();
    console.log('🧹 管理员消息处理器已清理');
  } catch (error) {
    console.error('❌ 清理消息处理器失败:', error);
  }

  // 注意：不要在这里断开WebSocket连接！
  // WebSocket连接应该作为后台服务持续运行，只有在管理员登出时才断开
  // 管理员可能只是导航到其他页面，聊天服务应该继续运行以接收消息
  console.log('🧹 AdminLayout卸载，保持WebSocket连接运行');
});
</script>

<style scoped>
html, body {
  overflow: hidden; /* 禁止滚动条 */
}
.admin-layout {
  height: 100vh;
  width: 100%;
  margin: 0; /* 移除外边距 */
  padding: 0; /* 移除外边距 */
}

.layout-container {
  height: 100%;
  margin: 0; /* 移除外边距 */
  padding: 0; /* 移除外边距 */
}

.aside {
  background-color: #304156;
  transition: width 0.3s;
  overflow: hidden;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  background-color: #2b3649;
}

.logo-img {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

.logo-text {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
}

.el-menu-vertical {
  border-right: none;
}

.header {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.toggle-icon {
  font-size: 20px;
  cursor: pointer;
  margin-right: 20px;
}

.header-right {
  display: flex;
  align-items: center;
}

.avatar-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin: 0 5px;
  color: #606266;
}

.main {
  background-color: #f0f2f5;
  padding: 20px; /* 根据需要调整内边距 */
}

/* 路由切换动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

/* 聊天按钮样式 */
.chat-button {
  position: relative;
  color: #3b82f6;
  font-weight: 500;
  margin-right: 16px;
}

.chat-button:hover {
  color: #2563eb;
  background-color: rgba(59, 130, 246, 0.1);
}

.chat-badge {
  position: absolute;
  top: -2px;
  right: -2px;
}

/* 菜单中的聊天项样式 */
.chat-menu-item {
  position: relative;
}

.menu-chat-badge {
  position: absolute;
  top: 8px;
  right: 16px;
  z-index: 10;
}

.chat-menu-item:hover {
  background-color: rgba(59, 130, 246, 0.1);
}
</style>