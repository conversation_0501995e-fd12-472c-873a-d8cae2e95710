<!--
  动态路由处理器组件
  用于处理动态路由刷新问题
  作为中间件，当检测到动态路由刷新时，负责重新加载路由配置并导航到正确页面
-->
<template>
  <div class="dynamic-route-handler">
    <div v-if="routerLoading" class="loading-container">
      <div class="loader"></div>
      <p>正在加载动态路由...</p>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAdminStore } from '@/modules/admin/stores/adminStore';
import { registerDynamicRoutes } from '@/router';

export default defineComponent({
  name: 'DynamicRouteHandler',
  
  setup() {
    const routerLoading = ref(true);
    const router = useRouter();
    const route = useRoute();
    const adminStore = useAdminStore();
    
    // 处理动态路由加载
    const handleDynamicRoute = async () => {
      try {
        console.log('🔄 动态路由处理器: 开始处理路径', route.fullPath);
        
        // 验证token有效性
        const hasToken = await adminStore.retoken();
        if (!hasToken && route.path.startsWith('/admin/') && route.path !== '/admin/login') {
          console.log('🔒 Token无效，重定向到登录页');
          router.replace('/admin/login');
          return;
        }
        
        // 获取前端路径数据
        console.log('📡 获取前端路径数据');
        const paths = await adminStore.fetchFrontendPaths(true);
        
        if (paths && paths.length > 0) {
          // 注册动态路由
          console.log('🔧 注册动态路由');
          await registerDynamicRoutes(paths);
          
          // 确保路由注册完成
          await new Promise(resolve => setTimeout(resolve, 200));
          
          // 分析当前路径
          const pathSegments = route.path.split('/').filter(Boolean);
          if (pathSegments.length < 2) {
            console.warn('⚠️ 路径结构异常');
            router.replace('/admin/dashboard');
            return;
          }
          
          // 获取最后的路径段，用于构建路由名称
          const segment = pathSegments[pathSegments.length - 1];
          const possibleName = `Admin${segment.charAt(0).toUpperCase() + segment.slice(1)}`;
          
          console.log(`🔍 尝试通过名称导航: ${possibleName}`);
          
          // 检查是否有匹配的路由名称
          if (router.hasRoute(possibleName)) {
            console.log(`✅ 找到匹配的路由名称: ${possibleName}`);
            router.replace({ name: possibleName, query: route.query, params: route.params });
          } else {
            // 查找相似路由
            console.log('🔍 搜索相似路由');
            const similarRoutes = router.getRoutes().filter(r => 
              r.path.includes(segment) || 
              (r.name && String(r.name).toLowerCase().includes(segment.toLowerCase()))
            );
            
            if (similarRoutes.length > 0) {
              // 使用第一个相似路由
              console.log(`➡️ 使用相似路由: ${similarRoutes[0].path}`);
              router.replace({ path: similarRoutes[0].path });
            } else {
              // 无法找到匹配的路由，回到首页
              console.warn('❌ 未找到匹配路由，回到首页');
              router.replace('/admin/dashboard');
            }
          }
        } else {
          console.warn('❌ 未获取到前端路径数据');
          router.replace('/admin/dashboard');
        }
      } catch (error) {
        console.error('❌ 动态路由处理失败:', error);
        router.replace('/admin/dashboard');
      } finally {
        routerLoading.value = false;
      }
    };
    
    onMounted(() => {
      handleDynamicRoute();
    });
    
    return {
      routerLoading
    };
  }
});
</script>

<style scoped>
.dynamic-route-handler {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.loading-container {
  text-align: center;
}

.loader {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
