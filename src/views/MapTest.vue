<!--
 * 天地图组件测试页面
 * 作者: AI Assistant
 * 日期: 2025-01-27
 * 版本: 1.0.0
 * 描述: 测试重构后的天地图组件功能
-->

<template>
  <div class="map-test-container">
    <h1>天地图组件测试</h1>
    
    <div class="test-section">
      <h2>原版天地图组件</h2>
      <div class="map-wrapper">
        <TiandituMap
          ref="originalMapRef"
          :api-key="apiKey"
          :longitude="116.40769"
          :latitude="39.89945"
          :zoom="12"
          :height="'400px'"
          :markers="markers"
          :editable-marker="editableMarker"
          :edit-mode="editMode"
          :add-marker-mode="addMarkerMode"
          @map-ready="onOriginalMapReady"
          @map-click="onMapClick"
          @marker-click="onMarkerClick"
          @marker-drag-start="onMarkerDragStart"
          @marker-dragging="onMarkerDragging"
          @marker-drag-end="onMarkerDragEnd"
          @editable-marker-update="onEditableMarkerUpdate"
          @marker-added="onMarkerAdded"
        />
      </div>
    </div>
    
    <div class="test-section">
      <h2>简化版天地图组件</h2>
      <div class="map-wrapper">
        <SimpleTiandituMap
          ref="simpleMapRef"
          :api-key="apiKey"
          :longitude="116.40769"
          :latitude="39.89945"
          :zoom="12"
          :height="'400px'"
          :use-random-token="true"
          @map-ready="onSimpleMapReady"
          @map-click="onMapClick"
        />
      </div>
    </div>
    
    <div class="controls">
      <h3>控制面板</h3>
      <div class="control-group">
        <label>API密钥:</label>
        <input v-model="apiKey" placeholder="请输入天地图API密钥" />
      </div>
      
      <div class="control-group">
        <button @click="addTestMarker">添加测试标记</button>
        <button @click="clearMarkers">清除标记</button>
        <button @click="changeMapCenter">切换到北京</button>
      </div>
      
      <div class="control-group">
        <label>编辑模式:</label>
        <input type="checkbox" v-model="editMode" />
        <button @click="addEditableMarker" :disabled="!editMode">添加可编辑标记</button>
        <button @click="clearEditableMarker" :disabled="!editMode">清除可编辑标记</button>
      </div>
      
      <div class="control-group" v-if="editMode && editableMarker">
        <label>经度:</label>
        <input type="number" v-model.number="editableMarker.lng" step="0.000001" @change="updateEditableMarkerPosition" />
        <label>纬度:</label>
        <input type="number" v-model.number="editableMarker.lat" step="0.000001" @change="updateEditableMarkerPosition" />
      </div>
      
      <div class="control-group">
        <label>添加标记点模式:</label>
        <input type="checkbox" v-model="addMarkerMode" />
        <span v-if="addMarkerMode" style="color: #007bff; font-size: 12px;">点击地图添加新标记点</span>
      </div>
      
      <div class="status">
          <h4>状态信息:</h4>
          <p>当前坐标系: {{ currentCoordinateSystem }}</p>
          <p>原版地图状态: {{ originalMapStatus }}</p>
          <p>简化版地图状态: {{ simpleMapStatus }}</p>
          <p>最后点击位置: {{ lastClickPosition }}</p>
          <p>编辑模式: {{ editMode ? '开启' : '关闭' }}</p>
          <p v-if="editableMarker">可编辑标记位置: 经度 {{ editableMarker.lng.toFixed(6) }}, 纬度 {{ editableMarker.lat.toFixed(6) }}</p>
          <p>拖拽状态: {{ dragStatus }}</p>
          <p>添加标记点模式: {{ addMarkerMode ? '开启' : '关闭' }}</p>
        </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { TiandituMap, SimpleTiandituMap } from '@/components/map'
import { getCurrentCoordinateSystem } from '@/components/map/utils'
import type { MarkerData } from '@/components/map/types'

// 响应式数据
const originalMapRef = ref()
const simpleMapRef = ref()
const apiKey = ref(import.meta.env.VITE_TIANDITU_API_KEY || '')
const originalMapStatus = ref('未初始化')
const simpleMapStatus = ref('未初始化')
const lastClickPosition = ref('无')
const editMode = ref(false)
const dragStatus = ref('无拖拽')
const addMarkerMode = ref(false)
const currentCoordinateSystem = ref(getCurrentCoordinateSystem())

// 可编辑标记点数据
const editableMarker = ref<MarkerData | null>(null)

// 标记点数据
const markers = ref<MarkerData[]>([
  {
    lng: 116.40769,
    lat: 39.89945,
    title: '北京天安门',
    content: '中华人民共和国首都北京的象征性建筑'
  },
  {
    lng: 116.41769,
    lat: 39.90945,
    title: '测试点2',
    content: '这是一个测试标记点'
  }
])

// 事件处理函数
const onOriginalMapReady = (map: any) => {
  console.log('原版地图初始化完成:', map)
  originalMapStatus.value = '已初始化'
}

const onSimpleMapReady = (map: any) => {
  console.log('简化版地图初始化完成:', map)
  simpleMapStatus.value = '已初始化'
}

const onMapClick = (event: any) => {
  console.log('地图点击事件:', event)
  if (event.lnglat) {
    const wgs84Coord = `WGS84 - 经度: ${event.lnglat.lng.toFixed(6)}, 纬度: ${event.lnglat.lat.toFixed(6)}`
    const currentSystemCoord = event.convertedLnglat ? 
      `${currentCoordinateSystem.value} - 经度: ${event.convertedLnglat.lng.toFixed(6)}, 纬度: ${event.convertedLnglat.lat.toFixed(6)}` : 
      '坐标转换失败'
    lastClickPosition.value = `${currentSystemCoord} | ${wgs84Coord}`
  }
}

const onMarkerClick = (marker: any, data: any) => {
  console.log('标记点击事件:', marker, data)
  alert(`点击了标记: ${data.title}`)
}

// 控制函数
const addTestMarker = () => {
  const newMarker: MarkerData = {
    lng: 116.40769 + Math.random() * 0.01,
    lat: 39.89945 + Math.random() * 0.01,
    title: `测试标记 ${markers.value.length + 1}`,
    content: `这是第 ${markers.value.length + 1} 个测试标记`
  }
  markers.value.push(newMarker)
}

const clearMarkers = () => {
  markers.value = []
}

const changeMapCenter = () => {
  if (originalMapRef.value) {
    originalMapRef.value.setCenter(116.40769, 39.89945, 15)
  }
}

// 可编辑标记点事件处理
const onMarkerDragStart = (marker: any, data: any) => {
  console.log('标记开始拖拽:', marker, data)
  dragStatus.value = '开始拖拽'
}

const onMarkerDragging = (marker: any, data: any, lnglat: any) => {
  console.log('标记拖拽中:', marker, data, lnglat)
  dragStatus.value = `拖拽中: ${lnglat.lng.toFixed(6)}, ${lnglat.lat.toFixed(6)}`
}

const onMarkerDragEnd = (marker: any, data: any, lnglat: any) => {
  console.log('标记拖拽结束:', marker, data, lnglat)
  dragStatus.value = `拖拽结束: ${lnglat.lng.toFixed(6)}, ${lnglat.lat.toFixed(6)}`
}

const onEditableMarkerUpdate = (lnglat: any) => {
  console.log('可编辑标记位置更新:', lnglat)
  if (editableMarker.value) {
    editableMarker.value.lng = lnglat.lng
    editableMarker.value.lat = lnglat.lat
  }
  dragStatus.value = `位置已更新: ${lnglat.lng.toFixed(6)}, ${lnglat.lat.toFixed(6)}`
}

const onMarkerAdded = (markerData: MarkerData) => {
  console.log('新标记点已添加:', markerData)
  // 自动开启编辑模式以便编辑新添加的标记点
  editMode.value = true
  dragStatus.value = `新标记点已添加: ${markerData.lng.toFixed(6)}, ${markerData.lat.toFixed(6)}`
}

// 可编辑标记点控制函数
const addEditableMarker = () => {
  editableMarker.value = {
    lng: 116.40769,
    lat: 39.89945,
    title: '可编辑标记',
    content: '这是一个可以拖拽的标记点',
    draggable: true
  }
  dragStatus.value = '已添加可编辑标记'
}

const clearEditableMarker = () => {
  editableMarker.value = null
  dragStatus.value = '已清除可编辑标记'
}

const updateEditableMarkerPosition = () => {
  if (editableMarker.value && originalMapRef.value) {
    originalMapRef.value.updateEditableMarkerPosition(editableMarker.value.lng, editableMarker.value.lat)
    dragStatus.value = `手动更新位置: ${editableMarker.value.lng.toFixed(6)}, ${editableMarker.value.lat.toFixed(6)}`
  }
}
</script>

<style scoped>
.map-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
}

.map-wrapper {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.controls {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
}

.control-group {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-group label {
  min-width: 80px;
  font-weight: bold;
}

.control-group input[type="text"],
.control-group input[type="number"] {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.control-group input[type="checkbox"] {
  margin: 0 5px;
}

.control-group input[type="number"] {
  max-width: 150px;
  margin-right: 10px;
}

.control-group button {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.control-group button:hover {
  background: #66b1ff;
}

.control-group button:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}

.control-group button:disabled:hover {
  background: #c0c4cc;
}

.status {
  background: white;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
}

.status h4 {
  margin-top: 0;
  color: #333;
}

.status p {
  margin: 5px 0;
  color: #666;
}

h1, h2, h3 {
  color: #333;
}

h1 {
  text-align: center;
  margin-bottom: 30px;
}

h2 {
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}
</style>