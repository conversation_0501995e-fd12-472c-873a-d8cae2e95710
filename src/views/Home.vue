<template>
  <div class="home-container">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="logo-container">
        <h1 class="title">{{ systemInfo?.siteName || 'O_Mall 多商家电商平台' }}</h1>
      </div>
      <div class="nav-links">
        <a href="#about">关于我们</a>
        <a href="#features">平台特色</a>
        <a href="#contact">联系方式</a>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 欢迎区域 -->
      <section class="hero-section">
        <div class="hero-content">
          <h1>欢迎来到 {{ systemInfo?.siteName || 'O_Mall 多商家电商平台' }}</h1>
          <p>一站式购物体验，多商家入驻，丰富商品选择</p>
          
          <!-- 三个入口按钮 -->
          <div class="entry-buttons">
            <el-button 
              type="primary" 
              size="large" 
              @click="goToLogin('admin')"
              :icon="Management"
              class="entry-button admin-button"
            >
              管理员入口
            </el-button>
            
            <el-button 
              type="success" 
              size="large" 
              @click="goToLogin('merchant')"
              :icon="Shop"
              class="entry-button merchant-button"
            >
              商家入口
            </el-button>
            
            <el-button 
              type="warning" 
              size="large" 
              @click="goToLogin('user')"
              :icon="User"
              class="entry-button user-button"
            >
              用户入口
            </el-button>
          </div>
        </div>
      </section>

      <!-- 关于我们 -->
      <section id="about" class="about-section">
        <h2>关于我们</h2>
        <div class="section-content">
          <p>O_Mall 是一个现代化的多商家电商平台，致力于为用户提供便捷、安全、多样化的购物体验。我们连接消费者与各类商家，打造一个繁荣的电商生态系统。</p>
          <p>平台支持多种商家入驻，从个体商户到大型企业，为消费者提供丰富多样的商品选择。我们注重用户体验，提供直观的界面设计和流畅的购物流程。</p>
        </div>
      </section>

      <!-- 平台特色 -->
      <section id="features" class="features-section">
        <h2>平台特色</h2>
        <div class="features-grid">
          <div class="feature-card">
            <el-icon size="40"><ShoppingCart /></el-icon>
            <h3>多样化商品</h3>
            <p>汇集各类商家，提供丰富多样的商品选择，满足不同消费者需求。</p>
          </div>
          
          <div class="feature-card">
            <el-icon size="40"><Lock /></el-icon>
            <h3>安全交易</h3>
            <p>采用先进的安全技术，保障用户信息和交易安全，让购物无忧。</p>
          </div>
          
          <div class="feature-card">
            <el-icon size="40"><TrendCharts /></el-icon>
            <h3>商家分析</h3>
            <p>为商家提供详细的销售分析和数据报表，助力商业决策。</p>
          </div>
          
          <div class="feature-card">
            <el-icon size="40"><Service /></el-icon>
            <h3>优质服务</h3>
            <p>提供全方位的客户服务，及时解决用户问题，提升购物体验。</p>
          </div>
        </div>
      </section>

      <!-- 联系方式 -->
      <section id="contact" class="contact-section">
        <h2>联系我们</h2>
        <div class="contact-info">
          <div class="contact-item">
            <el-icon><Location /></el-icon>
            <span>地址：{{ systemInfo?.address || '中国北京市海淀区中关村大街1号' }}</span>
          </div>
          <div class="contact-item">
            <el-icon><Phone /></el-icon>
            <span>电话：{{ systemInfo?.phone || '************' }}</span>
          </div>
          <div class="contact-item">
            <el-icon><Message /></el-icon>
            <span>邮箱：{{ systemInfo?.email || '<EMAIL>' }}</span>
          </div>
        </div>
      </section>
    </main>

    <!-- 底部区域 -->
    <footer class="footer">
      <p v-if="systemInfo?.copyright">{{ systemInfo.copyright }}</p>
      <p v-else>&copy; {{ systemInfo?.siteName || 'O_Mall' }} All rights reserved.</p>
      <div class="footer-links">
        <a href="#">隐私政策</a>
        <a href="#">使用条款</a>
        <a href="#">帮助中心</a>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useSystemStore } from '@/stores/systemStore';
import { 
  User, 
  Shop, 
  Management, 
  ShoppingCart, 
  Lock, 
  TrendCharts, 
  Service, 
  Location, 
  Phone, 
  Message 
} from '@element-plus/icons-vue';

const router = useRouter();
const systemStore = useSystemStore();
const systemInfo = computed(() => systemStore.systemInfo);

/**
 * 初始化页面，确保系统信息已加载
 */
onMounted(async () => {
  if (!systemInfo.value) {
    await systemStore.initSystemInfo();
  }
});

/**
 * 跳转到不同角色的登录页面
 * @param role 角色类型：admin-管理员，merchant-商家，user-用户
 */
const goToLogin = (role: string) => {
  switch (role) {
    case 'admin':
      router.push('/admin/login');
      break;
    case 'merchant':
      router.push('/merchant/login');
      break;
    case 'user':
      router.push('/user/login');
      break;
    default:
      break;
  }
};
</script>

<style scoped lang="scss">
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: auto;
}
.home-container {
  min-height: 100vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px;
  height: 60px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logo-container {
  display: flex;
  align-items: center;
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.nav-links {
  display: flex;
  gap: 24px;
}

.nav-links a {
  color: #666;
  text-decoration: none;
  font-size: 16px;
}

.nav-links a:hover {
  color: #409eff;
}

.main-content {
  flex: 1;
  padding: 40px;
}

.hero-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
  padding: 40px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.hero-content {
  text-align: center;
}

.hero-content h1 {
  font-size: 32px;
  margin-bottom: 16px;
  color: #333;
}

.hero-content p {
  font-size: 18px;
  color: #666;
  margin-bottom: 24px;
}

.entry-buttons {
  display: flex;
  justify-content: center;
  gap: 24px;
}

.entry-button {
  min-width: 150px;
}

.about-section,
.features-section,
.contact-section {
  margin-bottom: 40px;
  padding: 40px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.about-section h2,
.features-section h2,
.contact-section h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 24px;
}

.section-content p {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.feature-card {
  padding: 24px;
  border-radius: 8px;
  background-color: #f9f9f9;
  text-align: center;
}

.feature-card h3 {
  margin: 16px 0;
  color: #333;
}

.feature-card p {
  color: #666;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
}

.footer {
  background-color: #333;
  color: #fff;
  padding: 24px;
  text-align: center;
}

.footer p {
  margin-bottom: 8px;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 24px;
}

.footer-links a {
  color: #fff;
  text-decoration: none;
}

.footer-links a:hover {
  color: #409eff;
}
</style>