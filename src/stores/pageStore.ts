/**
 * 页面数据管理Store
 * 提供通用的CRUD操作和数据管理功能
 */
import { defineStore } from 'pinia';
import axios from 'axios';

// 定义类型接口
interface PageState {
  currentConfig: any | null;
  searchParams: Record<string, any>;
  tableData: any[];
  currentItem: any | null;
}

export const usePageStore = defineStore('page', {
  state: (): PageState => ({
    currentConfig: null,
    searchParams: {},
    tableData: [],
    currentItem: null
  }),
  actions: {
    /**
     * 设置当前页面配置
     * @param config 页面配置对象
     */
    setConfig(config: any) {
      this.currentConfig = config;
    },
    /**
     * 获取列表数据
     */
    async fetchData() {
      const baseUrl = this.currentConfig.baseUrl;
      const listUrl = `${baseUrl}${this.currentConfig.crud.list}`;
      try {
        const response = await axios.get(listUrl, { params: this.searchParams });
        this.tableData = response.data;
      } catch (error) {
        console.error('Failed to load data:', error);
      }
    },
    /**
     * 创建数据
     * @param data 要创建的数据对象
     */
    async createData(data: any) {
      const baseUrl = this.currentConfig.baseUrl;
      const createUrl = `${baseUrl}${this.currentConfig.crud.create}`;
      try {
        await axios.post(createUrl, data);
        this.fetchData(); // 刷新列表
      } catch (error) {
        console.error('Failed to create data:', error);
      }
    },
    /**
     * 更新数据
     * @param id 要更新的数据ID
     * @param data 更新的数据内容
     */
    async updateData(id: string | number, data: any) {
      const baseUrl = this.currentConfig.baseUrl;
      const updateUrl = `${baseUrl}${this.currentConfig.crud.update.replace('{id}', id)}`;
      try {
        await axios.put(updateUrl, data);
        this.fetchData(); // 刷新列表
      } catch (error) {
        console.error('Failed to update data:', error);
      }
    },
    /**
     * 删除数据
     * @param id 要删除的数据ID
     */
    async deleteData(id: string | number) {
      const baseUrl = this.currentConfig.baseUrl;
      const deleteUrl = `${baseUrl}${this.currentConfig.crud.delete.replace('{id}', id)}`;
      try {
        await axios.delete(deleteUrl);
        this.fetchData(); // 刷新列表
      } catch (error) {
        console.error('Failed to delete data:', error);
      }
    },
    /**
     * 查看单条数据详情
     * @param id 要查看的数据ID
     */
    async viewData(id: string | number) {
      const baseUrl = this.currentConfig.baseUrl;
      const viewUrl = `${baseUrl}${this.currentConfig.crud.view.replace('{id}', id)}`;
      try {
        const response = await axios.get(viewUrl);
        this.currentItem = response.data;
      } catch (error) {
        console.error('Failed to view data:', error);
      }
    },
    exportData(params: { format: string }) {
      const format = params.format;
      // 实现导出逻辑，例如生成 CSV 文件
      console.log(`Exporting data in ${format} format`);
    }
  }
});