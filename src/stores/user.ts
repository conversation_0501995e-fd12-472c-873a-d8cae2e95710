/**
 * 用户状态管理模块
 * 作者: 系统
 * 日期: 2024-01-01
 * 版本: 1.0.0
 * 描述: 管理用户登录状态、用户信息和相关操作
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { logout as logoutApi } from '@/modules/user/api/auth'
// import { generateDeviceInfo } from '@/utils/deviceInfo' // 已移至用户模块的userStore中
import localforage from 'localforage'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref('')
  const userInfo = ref({
    id: '',
    username: '',
    nickname: '',
    avatar: '',
    email: '',
    phone: '',
    role: '',
    status: ''
  })
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const isAdmin = computed(() => userInfo.value.role === 'admin')
  const isMerchant = computed(() => userInfo.value.role === 'merchant')
  const isNormalUser = computed(() => ['normal', 'vip'].includes(userInfo.value.role))
  
  // 方法
  function setToken(newToken: string) {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }
  
  function setUserInfo(info: any) {
    userInfo.value = info
  }
  
  /**
   * 用户登出方法
   * 调用登出API并清除本地存储的用户信息
   */
  async function logout() {
    try {
      // 获取当前设备信息
      const deviceInfoStr = localStorage.getItem('user_current_device_info')
      
      if (deviceInfoStr) {
        const deviceInfo = JSON.parse(deviceInfoStr)
        const deviceId = deviceInfo.device_id
        
        if (deviceId) {
          // 调用登出API
          await logoutApi(deviceId)
          console.log('用户登出API调用成功')
        } else {
          console.warn('设备信息中缺少device_id，跳过服务器登出')
        }
      } else {
        console.warn('未找到设备信息，跳过服务器登出')
      }
    } catch (error) {
      console.error('用户登出API调用失败:', error)
      // 即使API调用失败，也要清除本地状态
    } finally {
      // 清除本地状态
      token.value = ''
      userInfo.value = {
        id: '',
        username: '',
        nickname: '',
        avatar: '',
        email: '',
        phone: '',
        role: '',
        status: ''
      }
      
      // 清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('user_current_device_info')
      await localforage.removeItem('user_access_token')
      await localforage.removeItem('user_refresh_token')
      await localforage.removeItem('user_token_expiry')
    }
  }
  
  // 初始化 - 从本地存储恢复token
  function init() {
    const savedToken = localStorage.getItem('token')
    if (savedToken) {
      token.value = savedToken
    }
  }
  
  // 导出所有状态和方法
  return {
    token,
    userInfo,
    isLoggedIn,
    isAdmin,
    isMerchant,
    isNormalUser,
    setToken,
    setUserInfo,
    logout,
    init
  }
  })