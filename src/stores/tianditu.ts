/**
 * 天地图状态管理
 * 作者: AI Assistant
 * 日期: 2025-06-08
 * 版本: 1.0.0
 * 描述: 管理天地图实例、控件和覆盖物
 */

import { defineStore } from 'pinia'

// 判断是否显示错误信息
const isShowErrorMessage = true

// 天地图状态管理
export const useTiandituStore = defineStore('tianditu', {
  state: () => ({
    // 全局天地图组件视图对象
    Tmap: null as any,
    // 存储地图中所有添加的控件对象，方便后续的操作与管理
    mapControl: {} as Record<string, any>,
    /**
     * 存储地图中所有手动操作的覆盖物对象
     * 单一覆盖物，存储形式如：'marker1': overlay
     * 多个覆盖物，存储形式如：'_ARRAY_markers': [overlay,overlay, .....]
     */
    mapOverLay: {} as Record<string, any>,
    // 存储地图上叠加自定义的地图图块层
    mapTileLayer: {} as Record<string, any>,
    // 天地图配置
    config: {
      center: [116.40769, 39.89945], // 默认中心点
      zoom: 12, // 默认缩放级别
      apiKey: '' // API密钥
    }
  }),
  
  actions: {
    /**
     * 初始化天地图
     * @param Tmap 天地图实例
     */
    initTmap(Tmap: any) {
      if (this.Tmap) {
        console.log('天地图已经完成初始化，跳过初始化')
        return
      }
      
      if (!Tmap) {
        if (isShowErrorMessage) {
          console.error('Tmap传递为空')
        }
        throw new Error('Tmap传递为空')
      }
      
      this.Tmap = Tmap
      console.log('天地图初始化成功')
      
      // 设置中心点和缩放级别
      const [lng, lat] = this.config.center
      this.Tmap.centerAndZoom(new window.T.LngLat(lng, lat), this.config.zoom)
    },
    
    /**
     * 设置天地图API密钥
     * @param apiKey API密钥
     */
    setApiKey(apiKey: string) {
      this.config.apiKey = apiKey
    },
    
    /**
     * 设置地图中心点和缩放级别
     * @param lng 经度
     * @param lat 纬度
     * @param zoom 缩放级别
     */
    setCenter(lng: number, lat: number, zoom?: number) {
      if (!this.Tmap) return
      
      this.config.center = [lng, lat]
      const centerPoint = new window.T.LngLat(lng, lat)
      
      if (zoom !== undefined) {
        this.config.zoom = zoom
        this.Tmap.centerAndZoom(centerPoint, zoom)
      } else {
        this.Tmap.setCenter(centerPoint)
      }
    },
    
    /**
     * 添加控件
     * @param id 控件唯一ID
     * @param control 控件实例
     */
    addControl(id: string, control: any) {
      if (!id || !control) {
        if (isShowErrorMessage) {
          console.error('addControl 参数不完整')
        }
        throw new Error('addControl 参数不完整')
      }
      
      // 检查是否已经存在
      if (this.mapControl[id]) {
        if (isShowErrorMessage) {
          console.error(`当前存储的控件已经存在:${id}`)
        }
        throw new Error(`当前存储的控件已经存在:${id}`)
      }
      
      this.mapControl[id] = control
      this.Tmap.addControl(control)
    },
    
    /**
     * 移除控件
     * @param id 控件唯一ID
     */
    removeControl(id: string) {
      if (!id) {
        if (isShowErrorMessage) {
          console.error('removeControl 参数不完整')
        }
        throw new Error('removeControl 参数不完整')
      }
      
      // 检查是否不存在
      if (!this.mapControl[id]) {
        if (isShowErrorMessage) {
          console.error(`当前销毁的控件不存在:${id}`)
        }
        throw new Error(`当前销毁的控件不存在:${id}`)
      }
      
      this.Tmap.removeControl(this.mapControl[id])
      delete this.mapControl[id]
    },
    
    /**
     * 添加覆盖物
     * @param id 覆盖物唯一ID
     * @param overlay 覆盖物实例
     */
    addOverLay(id: string, overlay: any) {
      if (!id || !overlay) {
        if (isShowErrorMessage) {
          console.error('addOverLay 参数不完整')
        }
        throw new Error('addOverLay 参数不完整')
      }
      
      // 检查是否已经存在
      if (this.mapOverLay[id]) {
        if (isShowErrorMessage) {
          console.error(`当前存储的覆盖物已经存在:${id}`)
        }
        throw new Error(`当前存储的覆盖物已经存在:${id}`)
      }
      
      this.mapOverLay[id] = overlay
      this.Tmap.addOverLay(overlay)
    },
    
    /**
     * 更新覆盖物
     * @param id 覆盖物唯一ID
     * @param overlay 覆盖物实例
     * @param isRemoveOld 是否移除旧的覆盖物
     */
    updateOverLay(id: string, overlay: any, isRemoveOld = true) {
      if (!id || !overlay) {
        if (isShowErrorMessage) {
          console.error('updateOverLay 参数不完整')
        }
        throw new Error('updateOverLay 参数不完整')
      }
      
      // 检查是否不存在
      if (!this.mapOverLay[id]) {
        if (isShowErrorMessage) {
          console.error(`当前更新的覆盖物不存在:${id}`)
        }
        throw new Error(`当前更新的覆盖物不存在:${id}`)
      }
      
      // 默认移除旧的视图
      if (isRemoveOld) {
        this.removeOverLay(id)
      }
      
      this.mapOverLay[id] = overlay
      this.Tmap.addOverLay(overlay)
    },
    
    /**
     * 移除覆盖物
     * @param id 覆盖物唯一ID
     */
    removeOverLay(id: string) {
      if (!id) {
        if (isShowErrorMessage) {
          console.error('removeOverLay 参数不完整')
        }
        throw new Error('removeOverLay 参数不完整')
      }
      
      // 检查是否不存在
      if (!this.mapOverLay[id]) {
        if (isShowErrorMessage) {
          console.error(`当前销毁的覆盖物不存在:${id}`)
        }
        throw new Error(`当前销毁的覆盖物不存在:${id}`)
      }
      
      this.Tmap.removeOverLay(this.mapOverLay[id])
      delete this.mapOverLay[id]
    },
    
    /**
     * 查询覆盖物
     * @param id 覆盖物唯一ID
     * @returns 覆盖物实例
     */
    searchOverLay(id: string) {
      return this.mapOverLay[id]
    },
    
    /**
     * 添加某一种类的多个覆盖物
     * @param id 覆盖物类型ID
     * @param overlays 覆盖物实例或数组
     */
    addOverLayForType(id: string, overlays: any | any[]) {
      if (!id || !overlays) {
        if (isShowErrorMessage) {
          console.error('addOverLayForType 参数不完整')
        }
        throw new Error('addOverLayForType 参数不完整')
      }
      
      // 固定规则命名，用于区分
      const ID = `_ARRAY_${id}`
      let lays: any[] = []
      
      // 判断overlays是不是一个数组
      Array.isArray(overlays) ? (lays = overlays) : (lays = [overlays])
      
      lays.forEach((item) => {
        this.Tmap.addOverLay(item)
      })
      
      this.mapOverLay[ID]
        ? (this.mapOverLay[ID] = this.mapOverLay[ID].concat(lays))
        : (this.mapOverLay[ID] = lays)
    },
    
    /**
     * 移除某一种类的多个覆盖物
     * @param id 覆盖物类型ID
     */
    removeOverLayForType(id: string) {
      if (!id) {
        if (isShowErrorMessage) {
          console.error('removeOverLayForType 参数不完整')
        }
        throw new Error('removeOverLayForType 参数不完整')
      }
      
      // 固定规则命名，用于区分
      const ID = `_ARRAY_${id}`
      
      // 检查是否不存在
      if (!this.mapOverLay[ID]) {
        if (isShowErrorMessage) {
          console.error(`当前销毁的覆盖物类不存在:${ID}`)
        }
        throw new Error(`当前销毁的覆盖物类不存在:${ID}`)
      }
      
      this.mapOverLay[ID].forEach((item: any) => {
        this.Tmap.removeOverLay(item)
      })
      
      delete this.mapOverLay[ID]
    },
    
    /**
     * 添加图层
     * @param id 图层唯一ID
     * @param layer 图层实例
     */
    addTileLayer(id: string, layer: any) {
      if (!id || !layer) {
        if (isShowErrorMessage) {
          console.error('addTileLayer 参数不完整')
        }
        throw new Error('addTileLayer 参数不完整')
      }
      
      // 检查是否已经存在
      if (this.mapTileLayer[id]) {
        if (isShowErrorMessage) {
          console.error(`当前地图图层已经存在:${id}`)
        }
        throw new Error(`当前地图图层已经存在:${id}`)
      }
      
      this.mapTileLayer[id] = layer
      this.Tmap.addLayer(layer)
    },
    
    /**
     * 移除图层
     * @param id 图层唯一ID
     */
    removeTileLayer(id: string) {
      if (!id) {
        if (isShowErrorMessage) {
          console.error('removeTileLayer 参数不完整')
        }
        throw new Error('removeTileLayer 参数不完整')
      }
      
      // 检查是否不存在
      if (!this.mapTileLayer[id]) {
        if (isShowErrorMessage) {
          console.error(`当前销毁的地图图层不存在:${id}`)
        }
        throw new Error(`当前销毁的地图图层不存在:${id}`)
      }
      
      this.Tmap.removeLayer(this.mapTileLayer[id])
      delete this.mapTileLayer[id]
    },
    
    /**
     * 清空所有覆盖物
     */
    clearAllOverLays() {
      if (!this.Tmap) return
      
      this.Tmap.clearOverLays()
      this.mapOverLay = {}
    }
  }
})
