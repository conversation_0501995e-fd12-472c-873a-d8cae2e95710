/**
 * 关联排序状态管理
 * 用于管理配置编辑器之间的关联排序状态
 */
import { defineStore } from 'pinia';

interface LinkedSortingState {
  enabled: boolean;        // 是否启用关联排序
  lastSortedMap: Map<string, number>; // 字段名和排序顺序的映射
  updateTimestamp: number;  // 更新时间戳，用于通知其他组件排序已更新
}

export const useLinkedSortingStore = defineStore('linkedSorting', {
  state: (): LinkedSortingState => ({
    enabled: false,
    lastSortedMap: new Map(),
    updateTimestamp: 0
  }),
  
  actions: {
    /**
     * 设置关联排序状态
     * @param enabled 是否启用
     */
    setEnabled(enabled: boolean) {
      this.enabled = enabled;
      // 如果禁用关联排序，清除排序映射
      if (!enabled) {
        this.clearSortOrder();
      }
    },
    
    /**
     * 更新排序映射
     * @param items 排序后的项目数组
     */
    updateSortOrder(items: any[]) {
      if (!this.enabled || !items || items.length === 0) return;
      
      // 清除旧的排序顺序
      this.lastSortedMap.clear();
      
      // 建立新的排序映射
      items.forEach((item, index) => {
        if (item && item.prop) {
          this.lastSortedMap.set(item.prop, index);
        }
      });
      
      // 更新时间戳，通知其他组件排序已更新
      this.updateTimestamp = Date.now();
    },
    
    /**
     * 获取项目的排序顺序
     * @param prop 属性名
     */
    getSortOrder(prop: string): number | undefined {
      return this.lastSortedMap.get(prop);
    },
    
    /**
     * 根据排序映射对数组进行排序
     * @param items 需要排序的数组
     * @returns 排序后的数组
     */
    sortItemsByMap(items: any[]): any[] {
      if (!this.enabled || !items || items.length === 0) return items;
      
      // 创建items的浅拷贝以避免修改原始数据
      const sortedItems = [...items];
      
      // 对数组进行排序
      sortedItems.sort((a, b) => {
        const orderA = this.lastSortedMap.get(a.prop);
        const orderB = this.lastSortedMap.get(b.prop);
        
        // 如果两个项都有排序顺序，按顺序排序
        if (orderA !== undefined && orderB !== undefined) {
          return orderA - orderB;
        }
        
        // 如果只有A有排序顺序，A排在前面
        if (orderA !== undefined) return -1;
        
        // 如果只有B有排序顺序，B排在前面
        if (orderB !== undefined) return 1;
        
        // 如果都没有排序顺序，保持原来的顺序
        return 0;
      });
      
      return sortedItems;
    },
    
    /**
     * 清除排序映射
     */
    clearSortOrder() {
      this.lastSortedMap.clear();
      // 更新时间戳，通知其他组件排序已更新
      this.updateTimestamp = Date.now();
    }
  }
});
