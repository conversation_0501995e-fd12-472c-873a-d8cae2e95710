/**
 * 系统配置信息store
 * 管理系统配置信息和路由状态，并将配置存储在sessionStorage中
 */
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { getSysInfo } from '@/api/system';

// 定义系统信息接口
export interface SystemInfo {
  siteName: string;
  siteVersion: string;
  apiVersion: string;
  siteLogo: string;
  siteFavicon: string;
  copyright: string;
  configVersion: string;
  address?: string;
  phone?: string;
  email?: string;
  region?: string;
}

// 注意：原API响应接口结构已由请求拦截器处理，接口直接返回data部分
// 原结构：{ code: number; message: string; data: T }


// 定义存储在sessionStorage中的键名
const STORAGE_KEY = 'o_mall_system_info';

export const useSystemStore = defineStore('system', () => {
  // 系统信息状态
  const systemInfo = ref<SystemInfo | null>(null);
  // 加载状态
  const sysLoading = ref(false);
  // 是否已初始化
  const initialized = ref(false);

  /**
   * 从sessionStorage初始化系统信息
   * 如果sessionStorage不存在则向后端获取
   */
  async function initSystemInfo() {
    if (initialized.value) return systemInfo.value;
    
    sysLoading.value = true;
    try {
      // 尝试从sessionStorage获取系统信息
      const cachedInfo = sessionStorage.getItem(STORAGE_KEY);
      
      if (cachedInfo && cachedInfo !== 'undefined') {
        // 解析缓存的系统信息
        systemInfo.value = JSON.parse(cachedInfo);
        console.log('从sessionStorage加载系统信息成功');
        
        // 更新全局访问点
        updateGlobalSystemInfo();
      } else {
        // 如果sessionStorage不存在，则从后端获取
        await fetchSystemInfo();
        
      }
      
      initialized.value = true;
    } catch (error) {
      console.error('初始化系统信息失败:', error);
    } finally {
      sysLoading.value = false;
    }
    
    return systemInfo.value;
  }
  
  /**
   * 从后端获取系统信息
   * 注意：API响应已经经过request响应拦截器处理，直接返回有效数据部分
   */
  async function fetchSystemInfo() {
    sysLoading.value = true;
    try {
      // 获取系统信息 - 响应拦截器已处理，直接返回SystemInfo对象
      const response = await getSysInfo();
      console.log('systemStore fetchSystemInfo API返回数据:', response);
      
      // 响应已经是SystemInfo对象
      const sysInfo = response as unknown as SystemInfo;
      
      // 更新系统信息
      systemInfo.value = sysInfo;
        
      // 保存到sessionStorage
      sessionStorage.setItem(STORAGE_KEY, JSON.stringify(sysInfo));
      
      console.log('从后端获取系统信息成功');
      
      // 更新全局访问点
      updateGlobalSystemInfo();
    } catch (error) {
      console.error('获取系统信息失败:', error);
    } finally {
      sysLoading.value = false;
    }
    
    return systemInfo.value;
  }
  
  /**
   * 更新全局访问点，用于非响应式环境访问系统信息
   * 如在缓存服务中避免循环依赖
   */
  function updateGlobalSystemInfo() {
    if (typeof window !== 'undefined' && systemInfo.value) {
      // @ts-ignore
      window.$systemInfo = systemInfo.value;
    }
  }
  
  /**
   * 清除缓存的系统信息
   */
  function clearSystemInfo() {
    systemInfo.value = null;
    sessionStorage.removeItem(STORAGE_KEY);
    initialized.value = false;
    console.log('系统信息缓存已清除');
  }

  /**
   * 更新页面标题和图标
   */
  function updateDocumentMeta() {
    if (!systemInfo.value) return;
    
    // 更新页面标题
    document.title = systemInfo.value.siteName;
    
    // 更新网站图标
    const faviconLink = document.querySelector('link[rel="icon"]') || document.createElement('link');
    faviconLink.setAttribute('rel', 'icon');
    faviconLink.setAttribute('href', systemInfo.value.siteFavicon);
    
    if (!document.querySelector('link[rel="icon"]')) {
      document.head.appendChild(faviconLink);
    }
  }

  // 暴露状态和方法
  return {
    systemInfo,
    sysLoading,
    initialized,
    initSystemInfo,
    fetchSystemInfo,
    clearSystemInfo,
    updateDocumentMeta
  };
});
