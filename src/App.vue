<script setup lang="ts">
import { routeStatus } from './utils/routeGuard';

// 使用来自routeGuard的统一状态
const isRouteLoading = routeStatus.isLoading;
</script>

<template>
  <div class="app-container">
    <!-- 路由加载状态指示器 -->
    <div v-if="isRouteLoading" class="route-loading-overlay">
      <div class="loading-spinner"></div>
      <p>页面加载中...</p>
    </div>
    
    <!-- 添加路由过渡效果 -->
    <router-view v-slot="{ Component, route }">
      <!-- 特殊路由不使用过渡效果 -->
      <component 
        v-if="route.path === '/merchant/apply' || route.path.startsWith('/user/')" 
        :is="Component" 
      />
      <!-- 普通路由使用过渡效果 -->
      <transition v-else name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
  </div>
</template>

<style>
/* 重置 html 和 body 的默认样式 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  overflow: auto; /* 禁止滚动条 */
}

/* 确保 app 占据整个视口 */
#app, .app-container {
  height: 100%;
  width: 100%;
}

/* 确保 router-view 占据整个视口 */
router-view {
  height: 100%;
  width: 100%;
}

/* 路由加载覆盖层样式 */
.route-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* 加载旋转器样式 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409EFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 路由过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>