<template>
  <div class="notification-test-page">
    <el-card class="test-card">
      <template #header>
        <h3>WebSocket通知测试页面</h3>
      </template>

      <div class="test-content">
        <el-alert
          title="测试说明"
          type="info"
          :closable="false"
          show-icon
          class="test-alert"
        >
          <p>此页面用于测试WebSocket通知功能。请确保聊天UI处于关闭状态，然后点击下面的按钮模拟不同类型的通知消息。</p>
        </el-alert>

        <div class="test-section">
          <h4>聊天UI状态控制</h4>
          <div class="status-info">
            <span>当前聊天UI状态: </span>
            <el-tag :type="chatUIVisible ? 'success' : 'danger'">
              {{ chatUIVisible ? '已打开' : '已关闭' }}
            </el-tag>
            <el-button 
              type="primary" 
              size="small" 
              @click="toggleChatUI"
              style="margin-left: 10px;"
            >
              {{ chatUIVisible ? '关闭聊天UI' : '打开聊天UI' }}
            </el-button>
          </div>
        </div>

        <div class="test-section">
          <h4>用户通知测试</h4>
          <div class="button-group">
            <el-button @click="testUserOnlineNotification">
              测试用户上线通知
            </el-button>
            <el-button @click="testUserRefundNotification">
              测试退款结果通知
            </el-button>
            <el-button @click="testUserOrderNotification">
              测试订单通知
            </el-button>
            <el-button @click="testGenericEventNotification">
              测试通用事件通知
            </el-button>
          </div>
        </div>

        <div class="test-section">
          <h4>商家通知测试</h4>
          <div class="button-group">
            <el-button @click="testMerchantRefundRequest">
              测试商家退款申请
            </el-button>
            <el-button @click="testMerchantGenericNotification">
              测试商家通用通知
            </el-button>
          </div>
        </div>

        <div class="test-section">
          <h4>管理员通知测试</h4>
          <div class="button-group">
            <el-button @click="testAdminNotification">
              测试管理员通知
            </el-button>
          </div>
        </div>

        <div class="test-section">
          <h4>通知过滤器设置</h4>
          <NotificationFilterSettings :user-type="currentUserType" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useChatStore } from '@/modules/chat/stores/chat'
import NotificationFilterSettings from '@/components/NotificationFilterSettings.vue'

const chatStore = useChatStore()
const currentUserType = ref<'user' | 'merchant' | 'admin'>('user')

const chatUIVisible = computed(() => chatStore.isChatUIVisible)

/**
 * 切换聊天UI状态
 */
function toggleChatUI() {
  chatStore.setChatUIVisible(!chatUIVisible.value)
  ElMessage.info(`聊天UI已${chatUIVisible.value ? '打开' : '关闭'}`)
}

/**
 * 模拟WebSocket消息处理
 */
function simulateWebSocketMessage(message: any) {
  // 这里应该调用实际的消息处理器
  // 为了测试，我们直接触发相应的处理逻辑
  console.log('Simulating WebSocket message:', message)
  
  // 根据消息类型调用相应的处理器
  // 这里需要根据实际的消息路由逻辑来实现
  ElMessage.success('已发送测试消息，请查看是否弹出通知')
}

/**
 * 测试用户上线通知
 */
function testUserOnlineNotification() {
  const message = {
    type: 'notification',
    event: 'user_online',
    session_id: 0,
    data: {
      user_id: 123,
      username: 'testuser',
      message: '用户 testuser 已上线'
    }
  }
  simulateWebSocketMessage(message)
}

/**
 * 测试用户退款结果通知
 */
function testUserRefundNotification() {
  const message = {
    type: 'notification',
    event: 'user_refund_result',
    session_id: 0,
    data: {
      refund_id: 'RF123456',
      order_id: 'ORD789',
      status: 'approved',
      amount: 99.99,
      message: '您的退款申请已通过审核'
    }
  }
  simulateWebSocketMessage(message)
}

/**
 * 测试用户订单通知
 */
function testUserOrderNotification() {
  const message = {
    type: 'notification',
    event: 'order_user_update',
    session_id: 0,
    data: {
      order_id: 'ORD789',
      status: 'shipped',
      message: '您的订单已发货'
    }
  }
  simulateWebSocketMessage(message)
}

/**
 * 测试通用事件通知
 */
function testGenericEventNotification() {
  const message = {
    type: 'notification',
    event: 'system_maintenance',
    session_id: 0,
    data: {
      message: '系统将于今晚进行维护，请提前保存您的工作'
    }
  }
  simulateWebSocketMessage(message)
}

/**
 * 测试商家退款申请
 */
function testMerchantRefundRequest() {
  const message = {
    type: 'notification',
    event: 'merchant_refund_request',
    session_id: 0,
    data: {
      refund_id: 'RF123456',
      order_id: 'ORD789',
      user_id: 123,
      amount: 99.99,
      reason: '商品质量问题',
      message: '您收到一个新的退款申请'
    }
  }
  simulateWebSocketMessage(message)
}

/**
 * 测试商家通用通知
 */
function testMerchantGenericNotification() {
  const message = {
    type: 'notification',
    event: 'merchant_new_order',
    session_id: 0,
    data: {
      order_id: 'ORD789',
      user_id: 123,
      total_amount: 199.99,
      message: '您收到一个新订单'
    }
  }
  simulateWebSocketMessage(message)
}

/**
 * 测试管理员通知
 */
function testAdminNotification() {
  const message = {
    type: 'notification',
    event: 'admin_system_alert',
    session_id: 0,
    data: {
      alert_type: 'security',
      message: '检测到异常登录活动',
      priority: 3
    }
  }
  simulateWebSocketMessage(message)
}

onMounted(() => {
  // 根据当前用户角色设置用户类型
  // 这里应该从用户store或路由中获取
  currentUserType.value = 'user'
})
</script>

<style scoped>
.notification-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.test-content {
  padding: 0;
}

.test-alert {
  margin-bottom: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.test-section:last-child {
  border-bottom: none;
}

.test-section h4 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.status-info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.button-group .el-button {
  margin: 0;
}

:deep(.el-alert__content) {
  padding-right: 0;
}
</style>
