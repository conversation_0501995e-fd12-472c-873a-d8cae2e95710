/**
 * 路由主配置文件
 * 负责导入各个模块的路由配置并合并
 * 提供动态路由注册功能
 */
import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';
import localforage from 'localforage';
import type { ModuleData } from '@/modules/admin/router/index';
// 导入各模块路由
import adminRoutes, { generateDynamicRoutes } from '@/modules/admin/router/index';
import userRoutes from '@/modules/user/router/index';
import merchantRoutes from '@/modules/merchant/router/index';

// 创建路由实例 - 修改路由创建方式，将404页面单独管理
const mainRoutes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue')
  },
  {
    path: '/map-test',
    name: 'MapTest',
    component: () => import('@/views/MapTest.vue')
  },
  // 合并各模块路由
  ...adminRoutes,
  ...userRoutes,
  ...merchantRoutes,
  // 其他模块路由可以在这里添加
];

// 创建路由实例，不再包含通配符路由作为404
const router = createRouter({
  history: createWebHistory(),
  routes: mainRoutes,
  // 添加滚动行为
  scrollBehavior(_to, _from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
  // 设置更宽松的路径匹配策略
  strict: false,
  sensitive: false
});

// 存储已注册的路由路径，用于避免重复注册
const registeredRoutePaths = new Set<string>();

// 标记路由是否已经初始化
let routesInitialized = false;

/**
 * 添加动态路由
 * @param routes 路由配置数组
 */
function addDynamicRoutes(routes: RouteRecordRaw[]) {
  if (!routes || !Array.isArray(routes) || routes.length === 0) {
    console.warn('没有动态路由需要添加');
    return;
  }
  
  console.log('准备添加动态路由，数量:', routes.length);
  
  // 异步加载组件，返回Promise以等待加载完成
  const asyncLoadComponent = async (childRoute: RouteRecordRaw) => {
    if (childRoute.component) return;
    
    try {
      // 根据路径模式添加组件引用
      if (childRoute.meta?.dynamic || 
          childRoute.path === 'page/:configId' || 
          childRoute.path.startsWith('system/') || 
          childRoute.path === 'content/articles' ||
          ['permissions', 'roles', 'users'].includes(childRoute.path)) {
          
        // 动态导入动态配置页面组件
        const module = await import('@/modules/admin/router/index');
        childRoute.component = module.getDynamicConfigComponent;
        console.log(`为子路由 ${childRoute.path} 成功导入动态配置页面组件`);
      } else {
        // 根据路径模式导入不同的组件
        const viewName = childRoute.path.split('/').pop();
        if (viewName) {
          const capitalized = viewName.charAt(0).toUpperCase() + viewName.slice(1);
          try {
            // 尝试使用动态导入加载组件
            const modulePromise = import(`@/modules/admin/views/${capitalized}View.vue`);
            childRoute.component = () => modulePromise;
            console.log(`为子路由 ${childRoute.path} 成功导入组件: ${capitalized}View.vue`);
          } catch (err) {
            console.error(`无法导入组件: ${capitalized}View.vue`, err);
            // 导入失败时，使用404页面
            childRoute.component = () => import('@/views/404.vue');
          }
        }
      }
    } catch (err) {
      console.error(`为路由 ${childRoute.path} 导入组件失败:`, err);
      // 失败时，确保有一个备用组件
      childRoute.component = () => import('@/views/404.vue');
    }
  };
  
  // 遍历静态路由，确保父级路由已添加
  routes.forEach(route => {
    try {
      // 验证路由对象有效性
      if (!route || typeof route !== 'object') {
        console.warn('发现无效的路由配置项，跳过');
        return;
      }
      
      // 验证路由必须有path属性
      if (!route.path || typeof route.path !== 'string') {
        console.warn('路由缺少有效的path属性，跳过:', JSON.stringify(route));
        return;
      }
      
      // 验证路由必须有name属性
      if (!route.name) {
        console.warn(`路由缺少name属性 (路径: ${route.path})，跳过:`, JSON.stringify(route));
        return;
      }

      // 如果是/admin/login路由，需要从模块导入组件
      if (route.path === '/admin/login' && !route.component) {
        console.log('为/admin/login路由动态导入组件');
        route.component = () => import('@/modules/admin/views/Login.vue');
      }
      
      const routeName = String(route.name);
      const routePath = route.path;
      
      // 检查是否是父级路由
      if ((routePath === '/admin' || routePath === '/merchant') && route.children) {
        // 确保children是有效数组
        if (!Array.isArray(route.children) || route.children.length === 0) {
          console.warn('路由的children属性不是有效数组，跳过子路由处理:', routePath);
          return;
        }
        
        console.log('处理/admin父级路由, 子路由数量:', route.children.length);
        
        // 如果父级路由缺少component属性，动态导入
        if (!route.component) {
          console.log('为/admin父级路由动态导入Layout组件');
          route.component = () => import('@/layouts/AdminLayout.vue');
        }
        
        // 对于父级路由，我们先确保父级路由被添加，然后依次添加子路由
        const existingParent = router.hasRoute(routeName);
        
        if (!existingParent && !registeredRoutePaths.has(routePath)) {
          console.log('添加父级路由:', routePath);
          // 克隆路由对象，暂时去掉children以避免嵌套问题
          const parentRoute = { ...route, children: [] };
          router.addRoute(parentRoute);
          registeredRoutePaths.add(routePath);
        }
        
        // 然后依次添加子路由
        route.children.forEach(async (childRoute, index) => {
          // 检查子路由是否有效
          if (!childRoute || typeof childRoute !== 'object') {
            console.warn(`发现无效的子路由配置项 (索引: ${index})，跳过`);
            return;
          }
          
          // 确保子路由有name属性
          if (!childRoute.name) {
            console.warn(`子路由缺少name属性 (索引: ${index})，跳过:`, JSON.stringify(childRoute));
            return;
          }
          
          const childName = String(childRoute.name);
          
          // 确保子路由有有效的path属性
          if (!childRoute.path || typeof childRoute.path !== 'string') {
            console.warn(`子路由缺少有效的path属性 (索引: ${index}, 名称: ${childName})，跳过:`, JSON.stringify(childRoute));
            return;
          }
          
          // 加载组件
          if (!childRoute.component) {
            await asyncLoadComponent(childRoute);
          }
          
          const fullPath = `${routePath}/${childRoute.path}`.replace(/\/+/g, '/');
          
          if (!router.hasRoute(childName) && !registeredRoutePaths.has(fullPath)) {
            //console.log('添加子路由:', fullPath, '名称:', childName, '组件:', childRoute.component ? '已加载' : '未加载');
            try {
              // 将子路由添加到父路由下
              router.addRoute(routeName, childRoute);
              registeredRoutePaths.add(fullPath);
            } catch (err) {
              console.error(`添加子路由失败 (路径: ${fullPath}, 名称: ${childName}):`, err);
            }
          } else {
            //console.log('子路由已存在，跳过:', fullPath);
          }
        });
      } else {
        // 确保路由有component属性
        if (!route.component) {
          console.warn(`路由缺少component属性 (路径: ${routePath}, 名称: ${routeName})，跳过:`, JSON.stringify(route));
          return;
        }
        
        const existingRoute = router.hasRoute(routeName);
        
        if (!existingRoute && !registeredRoutePaths.has(routePath)) {
          console.log('添加单独路由:', routePath);
          try {
            router.addRoute(route);
            registeredRoutePaths.add(routePath);
          } catch (err) {
            console.error(`添加路由失败 (路径: ${routePath}, 名称: ${routeName}):`, err);
          }
        } else {
          console.log('路由已存在，跳过:', routePath);
        }
      }
    } catch (error) {
      console.error(`添加路由失败:`, route, error);
    }
  });
  
  console.log('当前已注册的路由路径:', Array.from(registeredRoutePaths));
  // 输出当前路由器中的所有路由
  console.log('路由器中的所有路由:', router.getRoutes().map(r => r.path));
}

/**
 * 根据前端路径数据注册动态路由
 * @param frontendPaths 前端路径数据
 */
export async function registerDynamicRoutes(frontendPaths: any[]) {
  try {
    console.log('开始注册动态路由', frontendPaths);
    
    // 验证前端路径数据
    if (!frontendPaths || !Array.isArray(frontendPaths) || frontendPaths.length === 0) {
      console.warn('前端路径数据无效，跳过路由注册');
      return false;
    }
    
    // 打印frontendPaths中的模块名称
    const moduleNames = frontendPaths.map(m => m.module);
    console.log('检测到的模块:', moduleNames.join(', '));
    
    // 确保数据可渲染、可序列化
    const serializablePaths = frontendPaths.map(module => {
      return {
        module: module.module,
        paths: Array.isArray(module.paths) ? module.paths.map((path: any) => ({
          path: path.path,
          title: path.title,
          count: path.count || 0,
          config_key: path.config_key || '',
          config_type: path.config_type || '',
          group: path.group || '',
          icon: path.icon || '',
          id: path.id || '',
          version_hash: path.version_hash || ''
        })) : []
      };
    });
    
    // 检测模块类型并生成相应的动态路由
    const hasMerchantModule = moduleNames.includes('merchant');
    const hasAdminModule = moduleNames.includes('admin');
    
    console.log(`模块检测: 管理员=${hasAdminModule}, 商家=${hasMerchantModule}`);
    
    // 根据检测到的模块分别处理
    if (hasAdminModule) {
      console.log('开始处理管理员动态路由');
      // 遍历找出管理员模块数据
      const adminModuleData = serializablePaths.filter(m => m.module === 'admin');
      // 生成管理员动态路由配置
      if (adminModuleData.length > 0) {
        const adminDynamicRoutes = generateDynamicRoutes(adminModuleData as ModuleData[]);
        console.log('管理员动态路由:', adminDynamicRoutes.length);
        // 添加到路由实例
        addDynamicRoutes(adminDynamicRoutes);
        
        // 保存前端路径数据到localforage
        try {
          await localforage.setItem('admin_frontend_paths', adminModuleData);
          console.log('管理员前端路径数据已保存到localforage');
        } catch (storageError) {
          console.error('保存管理员前端路径数据失败:', storageError);
        }
      }
    }
    
    if (hasMerchantModule) {
      console.log('开始处理商家动态路由');
      // 遍历找出商家模块数据
      const merchantModuleData = serializablePaths.filter(m => m.module === 'merchant');
      // 引入商家模块相关函数
      if (merchantModuleData.length > 0) {
        try {
          // 动态导入商家模块的路由生成函数
          const merchantModule = await import('@/modules/merchant/router/index');
          if (merchantModule && typeof merchantModule.generateDynamicRoutes === 'function') {
            const merchantDynamicRoutes = merchantModule.generateDynamicRoutes(merchantModuleData);
            console.log('商家动态路由:', merchantDynamicRoutes.length);
            // 添加到路由实例
            addDynamicRoutes(merchantDynamicRoutes);
            
            // 保存商家前端路径数据到localforage
            try {
              await localforage.setItem('merchant_frontend_paths', merchantModuleData);
              console.log('商家前端路径数据已保存到localforage');
            } catch (storageError) {
              console.error('保存商家前端路径数据失败:', storageError);
            }
          } else {
            console.error('商家模块缺少generateDynamicRoutes函数');
          }
        } catch (merchantError) {
          console.error('加载商家模块路由函数失败:', merchantError);
        }
      }
    }
    
    console.log('动态路由注册完成');
    routesInitialized = true;
    return true;
  } catch (error) {
    console.error('注册动态路由失败:', error);
    return false;
  }
}

/**
 * 初始化路由 - 在应用启动时从localforage恢复动态路由
 */
export async function initializeRoutes() {
  if (routesInitialized) {
    console.log('路由已初始化，跳过');
    return true;
  }
  
  // 记录是否有模块初始化成功
  let atLeastOneModuleInitialized = false;
  
  try {
    
    // 先尝试获取商家模块路径数据
    const merchantPaths = await localforage.getItem('merchant_frontend_paths');
    if (merchantPaths && Array.isArray(merchantPaths) && merchantPaths.length > 0) {
      console.log('从localforage恢复商家前端路径数据', merchantPaths);
      
      try {
        // 动态导入商家模块的路由生成函数
        const merchantModule = await import('@/modules/merchant/router/index');
        if (merchantModule && typeof merchantModule.generateDynamicRoutes === 'function') {
          // 使用模块提供的方法清除缓存
          if (typeof merchantModule.clearDynamicRoutesCache === 'function') {
            merchantModule.clearDynamicRoutesCache();
          }
          
          // 生成商家模块路由
          const merchantDynamicRoutes = merchantModule.generateDynamicRoutes(merchantPaths);
          console.log('从缓存恢复商家动态路由:', merchantDynamicRoutes.length);
          
          // 添加到路由实例
          addDynamicRoutes(merchantDynamicRoutes);
          atLeastOneModuleInitialized = true;
          console.log('商家模块路由恢复完成');
        }
      } catch (err) {
        console.error('恢复商家模块路由失败:', err);
        await localforage.removeItem('merchant_frontend_paths');
      }
    }
    
    // 然后尝试从localforage获取管理员前端路径数据
    const frontendPaths = await localforage.getItem('admin_frontend_paths');
    
    if (frontendPaths && Array.isArray(frontendPaths) && frontendPaths.length > 0) {
      console.log('从localforage恢复管理员前端路径数据', frontendPaths);
      
      // 验证数据结构的有效性
      const validPaths = frontendPaths.every((module: any) => {
        return module && 
               typeof module === 'object' &&
               typeof module.module === 'string' && 
               Array.isArray(module.paths) &&
               module.paths.every((path: any) => {
                 return path && 
                        typeof path === 'object' &&
                        typeof path.path === 'string' && 
                        typeof path.title === 'string';
               });
      });
      
      if (!validPaths) {
        console.warn('localforage中的前端路径数据格式无效，将在登录后重新获取');
        await localforage.removeItem('admin_frontend_paths');
        return false;
      }
      
      try {
        // 重要修改：不要使用缓存的路由配置，而是每次从前端路径数据重新生成
        // 这样可以确保component属性不会丢失
        
        // 从前端模块导入动态配置页面组件
        const adminModule = await import('@/modules/admin/router/index');
        
        // 使用保存的前端路径数据生成动态路由
        const dynamicRoutes = adminModule.generateDynamicRoutes(frontendPaths);
        console.log('dynamicRoutes:', dynamicRoutes);
        
        // 添加到路由实例
        addDynamicRoutes(dynamicRoutes);
        
        // 确保路由初始化完成后，刷新路由器的内部匹配表
        router.getRoutes();
        
        console.log('已从localforage恢复管理员动态路由配置');
        atLeastOneModuleInitialized = true;
        routesInitialized = true;
        return true;
      } catch (routeError) {
        console.error('生成动态路由失败:', routeError);
        // 如果生成路由失败，清除可能损坏的数据
        await localforage.removeItem('admin_frontend_paths');
        return false;
      }
    } else {
      console.log('localforage中不存在管理员前端路径数据，将在登录后获取');
    }
    
    // 即使没有从缓存恢复路由，也设置初始化状态为完成
    // 只要至少有一个模块初始化成功，就返回true
    routesInitialized = true;
    console.log('路由初始化完成，至少有一个模块初始化成功: ' + atLeastOneModuleInitialized);
    return atLeastOneModuleInitialized;
  } catch (error) {
    console.error('初始化路由失败:', error);
    // 即使出错也设置初始化状态为完成，避免重复尝试
    routesInitialized = true;
    return false;
  }
}

// 在创建路由实例后立即初始化路由
initializeRoutes();

// 动态路由和404路由处理逻辑
// 为了解决刷新问题，将动态路由和404路由分别处理
// 针对admin路径的处理使用特殊的动态路由处理器组件
router.addRoute({
  path: '/admin/:pathMatch(.*)*',
  name: 'AdminDynamicRouteHandler',
  component: () => import('@/views/DynamicRouteHandler.vue')
});

// 针对merchant路径也添加动态路由处理器，解决商家模块刷新到404的问题
router.addRoute({
  path: '/merchant/:pathMatch(.*)*',
  name: 'MerchantDynamicRouteHandler',
  component: () => import('@/views/DynamicRouteHandler.vue')
});

// 针对user路径添加动态路由处理器，解决用户模块刷新到404的问题
router.addRoute({
  path: '/user/:pathMatch(.*)*',
  name: 'UserDynamicRouteHandler',
  component: () => import('@/views/DynamicRouteHandler.vue')
});

// 其他所有未匹配路径显示404页面
router.addRoute({
  path: '/:pathMatch(.*)*',
  name: 'NotFound',
  component: () => import('@/views/404.vue')
});

// 路由器的默认导出
export default router;
