/**
 * qrcode模块的类型声明文件
 * 为qrcode库提供TypeScript类型支持
 */
declare module 'qrcode' {
  export interface QRCodeOptions {
    /**
     * 纠错级别
     */
    errorCorrectionLevel?: 'low' | 'medium' | 'quartile' | 'high' | 'L' | 'M' | 'Q' | 'H';
    
    /**
     * 类型
     */
    type?: 'image/png' | 'image/jpeg' | 'image/webp';
    
    /**
     * 质量
     */
    quality?: number;
    
    /**
     * 边距
     */
    margin?: number;
    
    /**
     * 颜色
     */
    color?: {
      /**
       * 暗模块颜色
       */
      dark?: string;
      
      /**
       * 亮模块颜色
       */
      light?: string;
    };
    
    /**
     * 宽度
     */
    width?: number;
    
    /**
     * 缩放因子
     */
    scale?: number;
    
    /**
     * 输出小写的URL编码字符
     */
    small?: boolean;
  }
  
  /**
   * 将文本转为二维码Canvas
   */
  export function toCanvas(
    canvas: HTMLCanvasElement,
    text: string,
    options?: QRCodeOptions
  ): Promise<HTMLCanvasElement>;
  
  /**
   * 将文本转为二维码DataURL
   */
  export function toDataURL(
    text: string,
    options?: QRCodeOptions
  ): Promise<string>;
  
  /**
   * 将文本转为二维码Buffer
   */
  export function toBuffer(
    text: string,
    options?: QRCodeOptions
  ): Promise<Buffer>;
  
  /**
   * 将文本转为二维码字符串
   */
  export function toString(
    text: string,
    options?: QRCodeOptions
  ): Promise<string>;
}
