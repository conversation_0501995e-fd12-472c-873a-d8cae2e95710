/**
 * vue3-json-viewer 类型声明文件
 */
declare module 'vue3-json-viewer' {
  import { Component } from 'vue'
  
  export interface JsonViewerProps {
    value: any
    expandDepth?: number
    copyable?: boolean
    sort?: boolean
    boxed?: boolean
    theme?: 'light' | 'dark'
    previewMode?: boolean
    showLength?: boolean
    showLine?: boolean
    showDoubleQuotes?: boolean
    showIcon?: boolean
  }
  
  export const JsonViewer: Component<JsonViewerProps>
}
