/**
 * 网格系统类型定义
 * @description 用于定义网格系统中使用的各种类型
 */

// 网格内容配置类型
export interface GridContentConfig {
  // 内容类型：table, chart, info, custom等
  type: string;
  // 标题
  title: string;
  // 图标
  icon?: string;
  // 是否显示标题
  showTitle?: boolean;
  // 是否显示工具栏
  showToolbar?: boolean;
  // 是否可刷新
  refreshable?: boolean;
  // 是否可设置
  configurable?: boolean;
  // 是否可编辑
  editable?: boolean;
  // 是否可关闭
  closable?: boolean;
  // 主题模式：light, dark, auto
  themeMode?: 'light' | 'dark' | 'auto';
  // 背景颜色
  backgroundColor?: string;
  // 标题背景颜色
  titleBackgroundColor?: string;
  // 文本颜色
  textColor?: string;
  // 自定义样式对象
  customStyle?: Record<string, string>;
  // 根据内容类型的具体配置
  config: TableConfig | ChartConfig | InfoConfig | CustomConfig | FormConfig | any;
}

// 表格配置类型
export interface TableConfig {
  // 表格列配置
  columns: TableColumn[];
  // 操作列配置
  // actions?: {
  //   title?: string;
  //   width?: number;
  //   buttons: TableActionButton[];
  // };
  // action-bar配置，用于PlusTable操作栏
  actionBar?: {
    // 操作按钮列表
    buttons: TableActionBarButton[];
    // 按钮类型：link, icon, button
    type?: 'link' | 'icon' | 'button';
    // 按钮标题
    title?: string;
    // 确认类型：popconfirm, messageBox
    confirmType?: 'popconfirm' | 'messageBox';
    // 宽度
    width?: number;
  };
  // 分页配置
  pagination?: {
    pageSize: number;
    pageSizes: number[];
  };
  // 是否显示选择列
  showSelection?: boolean;
  // 是否显示序号列
  showIndex?: boolean;
  // 是否显示操作列
  showActions?: boolean;
  // 行键名
  rowKey?: string;
  // 是否显示分页
  showPagination?: boolean;
  // 数据源
  dataSource?: 'api' | 'static';
  // 静态数据
  staticData?: any[];
  // API地址
  api?: string;
  // 请求方法
  method?: string;
  // 请求参数
  params?: Record<string, any>;
}

// 表格列配置
export interface TableColumn {
  // 列标题
  label: string;
  // 字段名
  prop: string;
  // 值类型：text, select, img, date, price等
  valueType?: string;
  // 值枚举，用于select等类型
  valueEnum?: Record<string, {
    text: string;
    status?: string;
  }>;
  // 选项，用于select等类型
  options?: Array<{
    label: string;
    value: any;
    color?: string;
  }>;
  // 是否可编辑
  editable?: boolean;
  // 列宽
  width?: number;
  // 固定列
  fixed?: 'left' | 'right';
  // 对齐方式
  align?: 'left' | 'center' | 'right';
  // 自定义格式化
  formatter?: string;
  // 自定义渲染
  render?: string;
}

// 表格操作按钮
export interface TableActionButton {
  // 按钮文本
  text: string;
  // 按钮类型
  type?: string;
  // 按钮图标
  icon?: string;
  // 操作类型
  action: string;
  // 按钮大小
  size?: string;
  // 权限
  permission?: string;
  // 条件显示
  condition?: string;
}

// 表格操作栏按钮配置（用于PlusTable的action-bar）
export interface TableActionBarButton {
  // 按钮文本
  text: string;
  // 按钮代码，用于标识操作类型
  code: string;
  // 按钮操作类型，与code二选一
  action?: string;
  // 按钮大小
  size?: string;
  // 按钮类型
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text';
  // 按钮图标，支持Element Plus图标
  icon?: any;
  // 按钮属性，可以是对象或函数
  props?: Record<string, any> | ((row: any) => Record<string, any>);
  // 是否需要确认
  confirm?: boolean;
  // 确认配置
  confirmConfig?: {
    // 确认框标题
    title?: string;
    // 确认框消息
    message?: string | ((data: any) => string);
    // 确认框属性
    popconfirmProps?: Record<string, any>;
  };
  // 条件显示，可以是字符串表达式或函数
  condition?: string | ((row: any) => boolean);
  // 显示函数，用于控制按钮是否显示
  show?: (row: any) => boolean;
}

// 图表配置
export interface ChartConfig {
  // 图表类型：line, bar, pie等
  chartType: string;
  // 图表选项
  options?: any;
  // 图表数据
  data?: any;
  // 数据源
  dataSource?: 'api' | 'static';
  // API地址
  api?: string;
  // 请求参数
  params?: Record<string, any>;
  // 自动刷新间隔（秒）
  refreshInterval?: number;
}

// 信息卡片配置
export interface InfoConfig {
  // 显示的信息项
  items: Array<{
    label: string;
    value: any;
    unit?: string;
    icon?: string;
    color?: string;
    trend?: 'up' | 'down' | 'none';
    percent?: number;
  }>;
  // 数据源
  dataSource?: 'api' | 'static';
  // API地址
  api?: string;
  // 请求参数
  params?: Record<string, any>;
}

// 自定义内容配置
export interface CustomConfig {
  // HTML内容
  html?: string;
  // 渲染函数
  render?: string;
  // 组件名称
  component?: string;
  // 组件属性
  props?: Record<string, any>;
}

// 表单配置
export interface FormConfig {
  // 表单列定义
  columns: any[];
  // 表单初始值
  initialValues?: Record<string, any>;
  // 表单验证规则
  rules?: Record<string, any[]>;
  // 表单布局
  layout?: 'horizontal' | 'vertical' | 'inline';
  // 标签宽度
  labelWidth?: string;
  // 表单提交回调
  onSubmit?: Function;
  // 表单提交错误回调
  onSubmitError?: Function;
  // 表单重置回调
  onReset?: Function;
  // 表单字段变化回调
  onChange?: Function;
  // 是否显示提交按钮
  showSubmitButton?: boolean;
  // 是否显示重置按钮
  showResetButton?: boolean;
  // 提交按钮文本
  submitText?: string;
  // 重置按钮文本
  resetText?: string;
  // 提交API
  submitApi?: string;
  // 提交方法
  submitMethod?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  // 其他配置
  [key: string]: any;
}
