/**
 * 共享类型定义文件
 * 提供所有模块共用的TypeScript类型定义
 * <AUTHOR>
 * @date 2025-04-27
 */
import type { ComputedRef } from 'vue';

/**
 * 所有表格列显示的类型 默认是 `undefined`
 */
export type TableValueType =
  | 'img'
  | 'link'
  | 'money'
  | 'tag'
  | 'progress'
  | 'copy'
  | 'code'
  // v0.1.0 新增
  | 'divider'
  // v0.1.0 新增
  | 'avatar'
  | ''

/**
 * 所有表单的类型 默认是 `input` (`undefined`)
 */
export type FormItemValueType =
| 'autocomplete'
| 'cascader'
| 'checkbox'
| 'color-picker'
| 'date-picker'
| 'input-number'
| 'radio'
| 'rate'
| 'select'
| 'slider'
| 'switch'
| 'time-picker'
| 'time-select'
| 'textarea'
| 'input'
| 'text'
| 'plus-radio'
| 'plus-date-picker'
| 'plus-input-tag'
/**
 * @version 0.1.1 新增
 */
| 'transfer'
/**
 * @version 0.1.1 新增
 */
| 'tree-select'
/**
 * @version 0.1.21 新增
 */
| 'select-v2'
| undefined

/**
 * 选项类型 (适配 PlusProComponents)
 */
export interface OptionsRow {
  label: string;
  value: string | number;
  type?: string;
  disabled?: boolean;
}

/**
 * 表格列配置接口 (适配 PlusProComponents)
 */
export interface PlusColumn {
  prop: string;
  label?: string | ComputedRef<string>;
  width?: string | number;
  minWidth?: string | number;
  valueType?: TableValueType | FormItemValueType | string;
  editable?: boolean;
  disabledHeaderFilter?: boolean;
  headerIsChecked?: boolean;
  tableColumnProps?: Record<string, any>;
  preview?: boolean;
  linkText?: string;
  descriptionsItemProps?: Record<string, any>;
  renderDescriptionsItem?: (...args: any[]) => any;
  renderDescriptionsLabel?: (...args: any[]) => any;
  options?: any;
  optionsMap?: Record<string, string>;
  customGetStatus?: (...args: any[]) => OptionsRow;
  tooltip?: string | ComputedRef<string> | Record<string, any> | ComputedRef<Record<string, any>>;
  render?: (...args: any[]) => any;
  renderHTML?: (...args: any[]) => string;
  renderHeader?: (...args: any[]) => any;
  formatter?: (...args: any[]) => any;
  formProps?: Record<string, any> | ComputedRef<Record<string, any>> | ((...args: any[]) => Record<string, any>);
  formItemProps?: Record<string, any> | ((...args: any[]) => Record<string, any> | Promise<Record<string, any>>);
  fieldProps?: Record<string, any> | ((...args: any[]) => Record<string, any> | Promise<Record<string, any>>);
  fieldSlots?: Record<string, (...args: any[]) => any>;
  fieldChildrenSlot?: (...args: any[]) => any;
  renderField?: (...args: any[]) => any;
  renderLabel?: (...args: any[]) => any;
  hasLabel?: boolean;
  renderExtra?: (...args: any[]) => any;
  colProps?: Record<string, any>;
  order?: number;
  hideInDescriptions?: boolean;
  hideInForm?: boolean;
  hideInTable?: boolean;
  hideInSearch?: boolean;
  sortable?: boolean | string;
  fixed?: boolean | string;
  [key: string]: any; // 允许添加其他属性
}

/**
 * 表单规则接口
 */
export interface FormRule {
  required?: boolean;
  validator?: string | Function;
  threshold?: number;
  message: string;
  trigger: string;
}

/**
 * 对话框配置接口
 */
export interface DialogConfig {
  dialogType: string;
  title: string;
  width: string | number;
  rules: Record<string, FormRule[]>;
  columns: PlusColumn[];
}

/**
 * UI状态接口
 */
export interface UIState {
  formModalVisible: boolean;
  viewModalVisible: boolean;
  modalType: 'create' | 'edit' | 'view' | 'delete' | '';
  formMode: 'create' | 'edit' | 'view' | '';
  searchCollapsed: boolean;
  confirmLoading?: boolean;
  [key: string]: any; // 允许添加其他状态属性
}

/**
 * 网格选项配置接口
 * @description 用于配置grid-layout相关参数
 */
export interface GridOptions {
  column: number;
  cellHeight: number;
  margin: number;
  maxRows?: number;
  [key: string]: any;
}

/**
 * 服务配置接口
 */
export interface ServiceConfig {
  baseUrl: string;
  apiPrefix?: string;
  headers?: Record<string, string>;
  timeout?: number;
  withCredentials?: boolean;
  transformRequest?: (data: any) => any;
  transformResponse?: (data: any) => any;
  pageInfoMap?: PageInfoMap;
  api?: (params: any, method?: string, url?: string) => Promise<any>;
  defaultParams?: PageDataParams;
  gridOptions?: GridOptions;
  urls?: {
    /** 列表接口 */
    list?: string;
    /** 详情接口 */
    detail?: string;
    /** 添加接口 */
    create?: string;
    /** 更新接口 */
    update?: string;
    /** 删除接口 */
    delete?: string;
    /** 其他自定义接口 */
    [key: string]: string | undefined;
  };
  formatRequest?: (params: Record<string, any>) => Record<string, any>;
  formatResponse?: (response: any) => { data: any[]; total: number };
  formatDetailResponse?: (response: any) => any;
  onListSuccess?: (data: any[], total: number) => void;
  onListError?: (error: any) => void;
  onDetailSuccess?: (data: any) => void;
  onDetailError?: (error: any) => void;
  idField?: string;
  addTitle?: string;
  editTitle?: string;
  viewTitle?: string;
  messages?: {
    /** 添加成功 */
    addSuccess?: string;
    /** 编辑成功 */
    editSuccess?: string;
    /** 删除确认 */
    deleteConfirm?: string;
    /** 删除成功 */
    deleteSuccess?: string;
  };
  immediate?: boolean;
  formatFormData?: (data: Record<string, any>, mode: 'add' | 'edit') => Record<string, any>;
  onAddSuccess?: (response: any) => void;
  onAddError?: (error: any) => void;
  onUpdateSuccess?: (response: any) => void;
  onUpdateError?: (error: any) => void;
  onDeleteSuccess?: (response: any) => void;
  onDeleteError?: (error: any) => void;
  customActions?: Record<string, any | ((params: any) => Promise<any>)>;
  defaultFormData?: Record<string, any>;
  formColumns?: PlusColumn[] | any[];
  sortField?: string;
  sortOrder?: string;
  [key: string]: any; // 允许添加其他配置属性
}

/**
 * 页面数据参数
 */
export interface PageDataParams {
  page?: number;
  pageSize?: number;
  sortField?: string;
  sortOrder?: 'ascend' | 'descend';
}

/**
 * 分页映射配置接口
 */
export interface PageInfoMap {
  total?: string;
  current: string;
  page?: string;
  pageSize: string;
  pageSizeList: string | [number];
}

/**
 * DTOInfoDTO 数据传输对象
 * @description 用于描述后端DTO结构定义
 * <AUTHOR>
 * @date 2025-04-19
 */
export interface DTOInfoDTO {
  /** 主键ID */
  id: number;
  /** 模块名称 */
  module: string;
  /** DTO名称 */
  name: string;
  /** DTO描述 */
  description: string;
  /** DTO类型 (request-请求对象, response-响应对象, common-通用对象) */
  type: string;
  /** DTO结构定义 (JSON格式) */
  structure: Record<string, any>;
  /** 创建时间 */
  created_at: string;
  /** 更新时间 */
  updated_at: string;
}

/**
 * GridItem 网格项布局配置
 * @description 用于描述动态layout中每个网格项的属性
 * <AUTHOR>
 * @date 2025-04-19
 */
export interface LayoutGridItem {
  /** 唯一标识 */
  i: number | string;
  /** 初始横向位置（第几列） */
  x: number;
  /** 初始纵向位置（第几行） */
  y: number;
  /** 初始宽度（占多少列） */
  w: number;
  /** 初始高度（占多少行） */
  h: number;
  /** 最小宽度，默认1 */
  minW?: number;
  /** 最小高度，默认1 */
  minH?: number;
  /** 最大宽度，默认Infinity */
  maxW?: number;
  /** 最大高度，默认Infinity */
  maxH?: number;
  /** 是否可拖拽，null表示继承父级 */
  isDraggable?: boolean | null;
  /** 是否可缩放，null表示继承父级 */
  isResizable?: boolean | null;
  /** 拖拽是否受限于容器，null表示继承父级 */
  isBounded?: boolean | null;
  /** 是否静态（不可拖拽、缩放、移动） */
  static?: boolean;
  /** 拖拽忽略元素，css选择器 */
  dragIgnoreFrom?: string;
  /** 拖拽允许元素，css选择器 */
  dragAllowFrom?: string;
  /** 缩放忽略元素，css选择器 */
  resizeIgnoreFrom?: string;
  /** 是否保持宽高比 */
  preserveAspectRatio?: boolean;
  /** 拖拽配置 */
  dragOption?: Record<string, any>;
  /** 缩放配置 */
  resizeOption?: Record<string, any>;
}

/**
 * 扩展GridStackNode的内容类型，以支持前端需要的内容格式
 */
export interface GridItemContent {
  type: string;
  title: string;
  icon: string;
  showTitle: boolean;
  refreshable: boolean;
  configurable: boolean;
  editable: boolean;
  closable: boolean;
  themeMode?: string;
  config: any;
  customStyle?: Record<string, string>;
}

/**
 * AdminGridInfoDTO 管理员模块使用的网格布局数据传输对象
 */
export interface AdminGridInfoDTO {
  id: number;
  uiConfigId?: number;
  ui_config_id?: number;
  name: string;
  step?: number[];
  permission?: any[];
  content: GridItemContent;
  position: LayoutGridItem;
  api: string;
  dto?: DTOInfoDTO;
  remark: string;
  status: number;
  created_at: string;
  updated_at: string;
}

/**
 * UI页面配置接口
 */
export interface UiPageConfig {
  frontendPath: string;
  icon: string;
  moduleName: string;
  serviceConfig: ServiceConfig;
  formDialogConfig?: DialogConfig;
  viewDialogConfig?: DialogConfig;
}

/**
 * PageData 页面配置对象
 * @description 用于描述完整的页面配置，包括基础属性、内容配置、DTO、网格项等
 * <AUTHOR>
 * @date 2025-04-19
 */
export interface PageData {
  id: number;                       // 配置ID
  config_key: string;               // 配置唯一标识
  config_type: string;              // 配置类型
  created_at: string;               // 创建时间
  updated_at: string;               // 更新时间
  draggable: boolean;               // 是否可拖拽
  resizable: boolean;               // 是否可调整大小
  status: number;                   // 状态
  title: string;                    // 页面标题
  version: string;                  // 配置版本
  version_hash: string;             // 版本识别号
  remark: string;                   // 备注
  module: string;                   // 模块名称
  group: string;                    // 分组
  icon: string;                     // 图标
  frontend_path: string;            // 前端路径
  config_content: UiPageConfig;     // 页面内容配置（参见 UiPageConfig 接口）
  dto?: DTOInfoDTO;                 // DTO数据（参见 DTOInfoDTO 接口）
  grid_items: AdminGridInfoDTO[];   // 网格项数组（参见 AdminGridInfoDTO 接口）
}
