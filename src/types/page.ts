/**
 * 页面配置类型定义
 */

// 表格值类型
export type TableValueType = 'text' | 'tag' | 'img' | 'dateTime';

// 表单值类型
export type FormItemValueType = 'text' | 'password' | 'radio' | 'upload' | 'checkbox' | 'select' | 
  'date-picker' | 'time-picker' | 'input-number' | 'switch' | 'rate' | 'cascader' | 'img' | 'copy' | 'textarea';

// 按钮类型
export type ButtonType = 'primary' | 'success' | 'warning' | 'danger';

// 标签位置
export type LabelPosition = 'left' | 'right' | 'top';

// 自定义操作配置
export interface CustomAction {
  url: string;
  method: 'get' | 'post' | 'put' | 'delete';
  confirmMessage: string;
  successMessage: string;
}

// 服务配置
export interface ServiceConfig {
  baseUrl: string;
  formatResponse?: (response: any) => any;
  customActions: Record<string, CustomAction>;
  messages?: Record<string, string>;
  addTitle?: string;
  editTitle?: string;
  viewTitle?: string; 
}

// 表格列配置
export interface TableColumn {
  label: string;
  prop: string;
  width?: number;
  valueType?: TableValueType;
  hideInSearch?: boolean;
  render?: (value: any, row?: any) => any;
  renderHTML?: (value: any, row?: any) => string;
  options?: Array<{label: string; value: any; color?: string}>;
}

// 新增详情列接口，扩展TableColumn
export interface InfoColumn extends TableColumn {
  renderDescriptionsItem?: (params: { value: any; row?: any }) => any;
}

// 表格配置
export interface TableConfig {
  columns: TableColumn[];
  actions?: Record<string, any>;
  showSelection?: boolean;
  showIndex?: boolean;
  showActions?: boolean;
  rowKey?: string;
  showPagination?: boolean;
  pagination?: {
    pageSize?: number;
    pageSizes?: number[];
  };
}

// 表单校验规则项
export interface FormRule {
  required?: boolean;
  message?: string;
  min?: number;
  max?: number;
  trigger?: 'blur' | 'change' | 'both';
  pattern?: string;
  validator?: Function;
}

// 栅格列参数
export interface ColProps {
  span?: number;
  offset?: number;
  pull?: number;
  push?: number;
  xs?: number | object;
  sm?: number | object;
  md?: number | object;
  lg?: number | object;
  xl?: number | object;
}

// 上传配置类型
export interface UploadProps {
  action?: string;
  headers?: Record<string, string>;
  multiple?: boolean;
  accept?: string;
  fileLimit?: number;
  sizeLimit?: number;
  fileUsage?: string;
  style?: 'default' | 'button' | 'link';
  showFileList?: boolean;
  tip?: string;
}

// 表单项配置
export interface FormItem {
  label: string;
  prop: string;
  valueType?: FormItemValueType;
  required?: boolean;
  rules?: any[];
  fieldProps?: Record<string, any>;
  colProps?: ColProps; // 新增栅格布局属性
  tooltip?: string; // 新增提示文本
  options?: any[]; // 新增选项值
  uploadProps?: UploadProps; // 新增上传配置属性
}

// 表单配置
export interface FormConfig {
  columns: FormItem[];
  labelWidth?: number | string;
  labelPosition?: LabelPosition;
  width?: string; // 添加 width 属性
  rules?: Record<string, FormRule[]>; // 新增表单校验规则
}

// 工具栏按钮配置
export interface ToolbarButton {
  text: string;
  type: ButtonType;
  icon?: string;
  action: string;
}

// 工具栏配置
export interface ToolbarConfig {
  buttons: ToolbarButton[];
}

// 搜索项配置
export interface SearchItem {
  label: string;
  prop: string;
}

// 搜索配置
export interface SearchConfig {
  columns: SearchItem[];
}

// 详情配置
export interface InfoConfig {
  columns: InfoColumn[];
  border?: boolean;
  column?: number;
  size?: 'large' | 'default' | 'small';
  direction?: 'horizontal' | 'vertical';
  width?: string | number; // 添加width属性
}

// 页面配置
export interface PageConfig {
  version: string;
  title: string;
  serviceConfig: ServiceConfig;
  tableConfig: TableConfig;
  formConfig: FormConfig;
  infoConfig?: InfoConfig;
  toolbarConfig: ToolbarConfig;
  searchConfig: SearchConfig;
}
