/**
 * 商家模块请求工具
 * 专门为商家模块处理HTTP请求，包含请求拦截器、响应拦截器和错误处理
 */
import { nextTick } from 'vue';
import axios from 'axios';
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios';
import { ElMessage } from 'element-plus';
import router from '@/router';
import type { BaseResponse } from '@/modules/admin/constants/apiTypes';
import { getApiBaseUrl } from '@/utils/apiConfig';
import localforage from 'localforage';

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// token过期前的提前刷新时间（这里设置为5分钟）
const TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5分钟，单位毫秒

// 标志位，用于控制是否正在刷新token
let isRefreshingToken = false;
// 上次刷新token的时间
let lastTokenRefreshTime = 0;
// 刷新token的最小间隔时间（10秒）
const TOKEN_REFRESH_INTERVAL = 10 * 1000;

// URL 白名单，不需要权限认证的请求路径
const URL_WHITE_LIST = [
  '/v1/merchant/public/login',
  '/v1/merchant/refresh-token',
  '/v1/merchant/resetpassword'
];

// 模块命名空间
const NAMESPACE = 'merchant';

/**
 * 检查URL是否在白名单中
 * @param url 请求URL
 * @returns 是否在白名单中
 */
function isUrlInWhiteList(url: string | undefined): boolean {
  if (!url) return false;
  return URL_WHITE_LIST.some(whiteUrl => url.includes(whiteUrl));
}

// 检查token是否需要刷新
async function checkAndRefreshToken(config: InternalAxiosRequestConfig): Promise<string | null> {
  const tokenKey = `${NAMESPACE}_access_token`;
  const tokenExpiryKey = `${NAMESPACE}_token_expiry`;
  
  // 如果是刷新token的请求或白名单内的请求，直接返回当前token（如果有）
  if (config.url && isUrlInWhiteList(config.url)) {
    const token = await localforage.getItem<string>(tokenKey);
    return token || null;
  }
  
  const token = await localforage.getItem<string>(tokenKey);
  const tokenExpireTime = await localforage.getItem<string>(tokenExpiryKey);
  
  if (!token) {
    return null;
  }
  
  // 如果token存在但过期时间不存在，设置一个默认过期时间（24小时）
  const now = Date.now();
  let expireTime = 0;
  if (!tokenExpireTime) {
    console.warn(`Token expiry time not found for merchant module, using default expiry time`);
    // 设置一个默认的过期时间（当前时间+24小时）
    expireTime = now + 24 * 60 * 60 * 1000;
    // 保存默认过期时间以便后续使用
    await localforage.setItem(tokenExpiryKey, expireTime.toString());
  } else {
    expireTime = parseInt(tokenExpireTime);
  }
  
  // 如果token已经过期，强制刷新
  if (now >= expireTime) {
    console.warn(`Token has expired for merchant module, must refresh before proceeding`);
    return await refreshTokenProcess(token);
  }
  
  // 如果token即将过期（阈值内）
  if (now >= expireTime - TOKEN_REFRESH_THRESHOLD) {
    console.log(`Token will expire soon for merchant module, refreshing...`);
    // 立即刷新token但不阻塞当前请求
    refreshTokenProcess(token).catch(err => {
      console.error(`Background token refresh failed for merchant module`, err);
    });
  }

  return token;
}

// 提取token刷新逻辑到独立函数
async function refreshTokenProcess(currentToken: string): Promise<string> {
  // 检查是否正在刷新token或者刚刚刷新过
  const now = Date.now();
  if (isRefreshingToken || (now - lastTokenRefreshTime < TOKEN_REFRESH_INTERVAL)) {
    console.log(`Token refresh in progress or recently refreshed for merchant module. Using current token.`);
    return currentToken;
  }
  
  isRefreshingToken = true;
  try {
    // 导入merchant store
    const { useMerchantStore } = await import('@/modules/merchant/stores/merchantStore');
    const merchantStore = useMerchantStore();
    const success = await merchantStore.loginByLongTermTokenAction();
    
    // 记录刷新时间
    lastTokenRefreshTime = Date.now();
    isRefreshingToken = false;
    
    if (success) {
      await nextTick();
      console.log(`Token refreshed successfully for merchant module`);
      // 返回新的token
      const newToken = await localforage.getItem<string>(`${NAMESPACE}_access_token`);
      return newToken || currentToken; // 如果获取不到新token，则返回当前token
    } else {
      console.log(`Token refresh failed for merchant module`);
      // 清除长期token
      await localforage.removeItem(`${NAMESPACE}_refresh_token`);
      return currentToken;
    }
  } catch (error) {
    console.error(`Token refresh failed for merchant module:`, error);
    // 清除长期token
    await localforage.removeItem(`${NAMESPACE}_refresh_token`);
    isRefreshingToken = false;
    return currentToken; // 返回旧token，让请求继续，可能会在响应中处理401错误
  }
}

// 请求拦截器
service.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    console.log('Merchant request interceptor config url is:', config.url);
    
    // 添加请求数据日志
    if (config.url?.includes('/grid-infos/batch/position')) {
      console.log('批量更新网格位置请求数据:', config.data);
      if (typeof config.data === 'string') {
        try {
          const parsedData = JSON.parse(config.data);
          console.log('解析后的批量更新网格位置请求数据:', parsedData);
        } catch (e) {
          console.error('解析请求数据失败:', e);
        }
      }
    }

    // 检查并刷新token
    const token = await checkAndRefreshToken(config);
    console.log(`获取的merchant token：${token ? '存在' : '不存在'}, URL: ${config.url}`);
    
    // 如果是白名单中的请求，则不添加token（除非是刷新token的请求需要带上旧token）
    const isRefreshTokenRequest = config.url?.includes(`/v1/merchant/refresh-token`);
    const isInWhiteList = isUrlInWhiteList(config.url);
    console.log(`是否是刷新token请求：${isRefreshTokenRequest}, 是否在白名单中：${isInWhiteList}`);
    
    // 只有在token存在的情况下才设置Authorization头部
    if (token && ((token && !isInWhiteList) || isRefreshTokenRequest)) {
      // 使用类型断言确保能够正确设置headers
      config.headers = config.headers || {};
      config.headers['Authorization'] = `Bearer ${token}`;
      console.log(`设置Authorization头部：Bearer ${token?.substring(0, 10)}...`);
    } else {
      console.log(`不设置Authorization头部，token状态: ${token ? '存在' : '不存在'}, 是否在白名单: ${isInWhiteList}`);
      // 确保移除可能存在的Authorization头部
      if (config.headers && config.headers['Authorization']) {
        delete config.headers['Authorization'];
        console.log('已移除Authorization头部');
      }
    }
    
    return config;
  },
  (error) => {
    console.error('Merchant request error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  async (response: AxiosResponse<BaseResponse<any>>) => {
    console.log('=== Merchant响应拦截器开始 ===');
    console.log('响应URL:', response.config.url);
    console.log('响应状态码:', response.status);
    console.log('响应数据:', response.data);
    
    const { code, message, data } = response.data || {};
    console.log('=== Merchant响应拦截器处理非200状态码 ===');
    console.log('状态码:', code, '消息:', message);
    
    // 请求成功
    if (code === 200) {
      console.log('=== Merchant响应拦截器处理成功 ===');
      if(data) {
        return data;
      } else {
        return {message: 'success'};
      }
    }

    // 处理特定错误码
    switch (code) {
      case 401:
        // 防止重复执行401处理逻辑
        if (isRefreshingToken) {
          console.log('Already handling 401 error for merchant module, skipping duplicate handling');
          break;
        }
        
        // 未登录或token过期，清除merchant模块的token
        await localforage.removeItem(`${NAMESPACE}_access_token`);
        
        // 清除sessionStorage和localStorage中的token
        sessionStorage.removeItem(`${NAMESPACE}_access_token`);
        
        // 如果请求不是刷新token的请求，才尝试用长期token登录
        const isRefreshTokenRequest = response.config.url?.includes('/v1/merchant/refresh-token');
        
        if (!isRefreshTokenRequest) {
          isRefreshingToken = true;
          try {
            // 导入merchant store
            const { useMerchantStore } = await import('@/modules/merchant/stores/merchantStore');
            const merchantStore = useMerchantStore();
            const longTermLoginSuccess = await merchantStore.loginByLongTermTokenAction();
            
            isRefreshingToken = false;
            lastTokenRefreshTime = Date.now();
            
            if (longTermLoginSuccess) {
              ElMessage.success('长期Token登录成功');
              break;
            }
          } catch (error) {
            console.error(`Merchant long term token login failed:`, error);
            isRefreshingToken = false;
          }
        }
        
        // 如果长期token登录失败或者是刷新token请求本身失败，则跳转到登录页
        console.log('Merchant拦截器跳转！');
        nextTick(() => {
          router.push('/merchant/login');
        });
        ElMessage.error('登录已过期，请重新登录');
        break;
      case 403:
        ElMessage.error('没有权限访问该资源');
        break;
      case 404:
        ElMessage.error('请求的资源不存在');
        break;
      case 500:
        ElMessage.error('服务器错误，请稍后重试');
        break;
      default:
        ElMessage.error(message || '请求失败');
    }

    return Promise.reject(new Error(message || '请求失败'));
  },
  (error) => {
    console.error('Merchant response error:', error);
    const message = error.response?.data?.message || '网络错误，请稍后重试';
    ElMessage.error(message);
    return Promise.reject(error);
  }
);

// 封装GET请求
export async function get<T>(url: string, params?: any): Promise<T> {
  const response = await service.get<BaseResponse<T>>(url, { params });
  return response as any;
}

// 封装POST请求
export async function post<T>(url: string, data?: any, config?: any): Promise<T> {
  const response = await service.post<BaseResponse<T>>(url, data, config);
  console.log('merchant post请求响应', response);
  return response as any;
}

// 封装PUT请求
export async function put<T>(url: string, data?: any): Promise<T> {
  const response = await service.put<BaseResponse<T>>(url, data);
  return response as any;
}

// 封装DELETE请求
export async function del<T>(url: string, data?: any): Promise<T> {
  const response = await service.delete<BaseResponse<T>>(url, { data });
  return response as any;
}

// 封装PATCH请求
export async function patch<T>(url: string, data?: any): Promise<T> {
  const response = await service.patch<BaseResponse<T>>(url, data);
  return response as any;
}

export default service;