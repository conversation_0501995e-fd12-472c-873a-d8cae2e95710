/**
 * 订单相关类型定义 - 驼峰命名版
 * 此文件包含与后端API返回数据结构一致的驼峰命名法类型定义
 */

// 配送状态枚举 - 与后端配送状态定义保持一致
// 配送状态流转：待配送(0) → 待接单(10) → 已接单(20) → 取餐中(30) → 已取餐(40) → 配送中(50) → 已送达(60)
// 任意阶段可转为已取消(70)状态
export enum DeliveryStatus {
  WAITING = 0,               // 待配送（初始状态）
  PENDING = 10,              // 待接单
  ACCEPTED = 20,             // 已接单
  PICKING = 30,              // 取餐中
  PICKED_UP = 40,            // 已取餐
  DELIVERING = 50,           // 配送中
  COMPLETED = 60,            // 已送达
  CANCELLED = 70,            // 已取消
  
  // 保持向后兼容的别名（这些值已不再使用，但保留以避免代码错误）
  // PAID = 20,                 // 已支付 (旧值，不再使用)
  // DELIVERY_EXCEPTION = 61,   // 配送失败 (兼容旧代码)
}

/**
 * 配送状态文本映射 (中文) - 与后端配送状态定义保持一致
 * 配送状态流转：待配送(0) → 待接单(10) → 已接单(20) → 取餐中(30) → 已取餐(40) → 配送中(50) → 已送达(60)
 * 任意阶段可转为已取消(70)状态
 */
export const DeliveryStatusText: Record<DeliveryStatus, string> = {
  // 主要配送状态（与后端配送状态定义一致）
  [DeliveryStatus.WAITING]: '待配送',
  [DeliveryStatus.PENDING]: '待接单',
  [DeliveryStatus.ACCEPTED]: '已接单',
  [DeliveryStatus.PICKING]: '取餐中',
  [DeliveryStatus.PICKED_UP]: '已取餐',
  [DeliveryStatus.DELIVERING]: '配送中',
  [DeliveryStatus.COMPLETED]: '已送达',
  [DeliveryStatus.CANCELLED]: '已取消',
};

/**
 * 配送信息接口 - 驼峰命名版
 */
export interface DeliveryInfo {
  deliveryStaffId: number;          // 配送员ID
  deliveryStaffName: string;        // 配送员姓名
  deliveryStaffPhone: string;       // 配送员电话
  deliveryStatus: DeliveryStatus;   // 配送状态
  deliveryType: number;             // 配送方式：0-跑腿员配送，1-商家自配送，2-用户到店自提
  estimatedArrivalTime: string | null; // 预计到达时间
  deliveryStartedAt: string | null;    // 开始配送时间
  deliveryCompletedAt: string | null;  // 配送完成时间

  // 增加OrderDetail.vue中使用的属性
  deliveryAddress?: string;         // 配送地址
  deliveryDistance?: number;        // 配送距离（单位：km）
  expectedTime?: string;            // 预计送达时间
  receiverName?: string;           // 收货人姓名
  receiverPhone?: string;          // 收货人电话

  // 到店自提相关字段
  pickupCode?: string;             // 取餐码
  pickupCodeUsed?: boolean;        // 取餐码是否已使用
  pickupCodeUsedTime?: string | null; // 取餐码使用时间
  expectedPickupTime?: string;     // 预计取餐时间
  startTime?: string;              // 开始备餐时间
  endTime?: string;                // 完成时间
}

/**
 * 套餐选项接口 - 驼峰命名版
 */
export interface ComboOption {
  optionName: string;   // 选项名称
  quantity: number;     // 数量
  extraPrice: number;   // 额外价格
}

/**
 * 套餐选择接口 - 驼峰命名版
 */
export interface ComboSelection {
  comboName: string;    // 套餐名称
  options: ComboOption[]; // 选项
}

/**
 * 订单商品项接口 - 驼峰命名版
 */
export interface OrderItem {
  id: number;
  foodId: number;           // 商品ID
  foodName: string;         // 商品名称
  foodImage: string;        // 商品图片
  variantName: string;      // 规格名称
  quantity: number;         // 数量
  price: number;            // 价格
  subtotal: number;         // 小计
  comboSelections: ComboSelection[]; // 套餐选择
}

/**
 * 收货地址接口 - 驼峰命名版
 */
export interface DeliveryAddress {
  id: number;
  receiver: string;         // 收货人
  phone: string;            // 联系电话
  province: string;         // 省份
  city: string;             // 城市
  district: string;         // 区/县
  detail: string;           // 详细地址
  isDefault: boolean;       // 是否默认地址
}

/**
 * 订单信息接口 - 驼峰命名版
 */
export interface OrderInfo {
  id: number;               // 订单ID
  orderID: number;          // 后端返回的订单ID（与id相同，用于兼容）
  orderNo: string;          // 订单号（与原order_number对应）
  userId: number;           // 用户ID
  userID: number;           // 用户ID（大驼峰命名，用于页面显示）
  userName: string;         // 用户名
  userPhone: string;        // 用户电话
  merchantId: number;       // 商家ID
  merchantID: number;       // 商家ID（大驼峰命名，用于页面显示）
  merchantName: string;     // 商家名称
  orderStatus: number;      // 订单状态（与原status对应）
  statusText: string;       // 订单状态文本
  totalAmount: number;      // 总金额
  itemsCount: number;       // 商品项数量
  deliveryFee: number;      // 配送费
  packagingFee: number;     // 包装费
  discountAmount: number;   // 优惠金额
  deliveryType: number;     // 配送方式：0-跑腿员配送，1-商家自配送，2-用户到店自提
  items: OrderItem[];       // 订单商品项（与原order_items对应）
  remark: string;           // 订单备注
  createTime: string;       // 创建时间（与原created_at对应）
  paidAt: string;           // 支付时间
  payTime?: string;         // 兼容字段：支付时间
  acceptedAt: string | null; // 接单时间
  completedAt: string | null; // 完成时间
  cancelledAt: string | null; // 取消时间
  refundingAt: string | null; // 退款开始时间
  refundedAt: string | null;  // 退款完成时间
  updatedAt: string;        // 更新时间
  
  // 退款相关信息（支持后端新字段格式）
  hasRefund?: boolean;      // 是否有退款
  refundNo?: string;        // 退款单号
  refundStatus?: number;    // 退款状态（直接字段）
  refundAmount?: number;    // 退款金额（直接字段）
  refundReason?: string;    // 退款原因（直接字段）
  refundTime?: string;      // 退款时间（直接字段）
  
  // 兼容原有的嵌套结构
  refundInfo?: {
    refundId: number;         // 退款ID
    refundAmount: number;     // 退款金额
    refundReason: string;     // 退款原因
    refundStatus: number;     // 退款状态
    applyTime: string;        // 申请时间
    processTime?: string;     // 处理时间
    processRemark?: string;   // 处理备注
    refundNo?: string;        // 退款单号
    refundTime?: string;      // 退款时间
  };
  
  // 配送相关信息
  deliveryInfo: DeliveryInfo; // 配送基本信息
  deliveryAddress?: DeliveryAddress; // 配送地址
}

/**
 * 订单列表查询参数接口 - 驼峰命名版
 */
export interface OrderQueryParams {
  page?: number;            // 页码
  limit?: number;           // 每页数量
  orderStatus?: number;     // 订单状态
  orderNo?: string;         // 订单号
  startDate?: string;       // 开始日期
  endDate?: string;         // 结束日期
  userId?: number;          // 用户ID
}

/**
 * 订单列表响应接口 - 驼峰命名版
 */
export interface OrderListResponse {
  total: number;            // 总数
  items: OrderInfo[];       // 订单列表
}
