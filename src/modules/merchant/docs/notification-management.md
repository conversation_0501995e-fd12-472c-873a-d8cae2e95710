# 商家聊天模块通知管理功能

## 功能概述

为商家聊天模块增加了完整的通知管理功能，支持接收和处理各种类型的通知，特别是退款申请通知。

## 主要功能

### 1. 通知接收与处理
- 自动接收WebSocket推送的通知消息
- 支持多种通知类型（退款申请、一般通知等）
- 智能通知分类和处理

### 2. 通知显示
- **Element Plus Notification**: 页面右上角弹出通知
- **桌面通知**: 浏览器原生桌面通知
- **声音提醒**: 播放提示音
- **通知中心**: 聊天窗口内的通知管理面板

### 3. 通知管理
- 通知列表展示（最近100条）
- 未读通知计数和标记
- 一键标记全部已读
- 清空所有通知
- 点击通知跳转到相关页面

## 通知类型

### 退款申请通知 (merchant_refund_request)
```javascript
{
  type: 'notification',
  event: 'merchant_refund_request',
  session_id: 0,
  data: {
    title: "收到退款申请",
    content: "订单 202507251442251419 收到退款申请，退款金额 8 元，退款原因：不想要了/拍错了",
    data: {
      action_type: 'refund_process',
      action_url: '/merchant/refund/detail/10',
      apply_time: 1753426322,
      merchant_id: 1,
      message: '用户申请退款，订单号：202507251442251419，退款金额：8.00元，退款原因：不想要了/拍错了'
    },
    expire_time: 1753685522,
    persistent: true,
    priority: 2,
    type: "merchant_refund_request"
  },
  timestamp: 1753426322
}
```

**特点：**
- 显示时长：8秒
- 通知类型：warning（警告）
- 图标：Money（金钱图标，红色）
- 点击可跳转到退款详情页

### 新订单通知 (merchant_new_order)
```javascript
{
  type: 'notification',
  event: 'merchant_new_order',
  session_id: 0,
  data: {
    content: "您收到新订单，请及时处理",
    data: {
      delivery_addr: '配送地址',
      merchant_id: 1,
      merchant_name: '商家',
      order_id: 85,
      order_no: '202507251442251420',
      customer_name: '张三',
      total_amount: 128.50
    },
    expire_time: 1753514740,
    order_id: 0,
    order_no: "",
    persistent: true,
    priority: 2,
    title: "收到新订单",
    type: "merchant_new_order"
  },
  timestamp: 1753428340
}
```

**特点：**
- 显示时长：6秒
- 通知类型：success（成功）
- 图标：ShoppingBag（购物袋图标，绿色）
- 点击可跳转到订单详情页

### 一般通知
- 显示时长：5秒
- 通知类型：info（信息）
- 图标：Bell（铃铛图标）

## 通知设置

```javascript
const notificationSettings = {
  enabled: true,    // 是否启用通知
  sound: true,      // 是否播放声音
  desktop: true,    // 是否显示桌面通知
  refund: true,     // 是否显示退款通知
  newOrder: true,   // 是否显示新订单通知
  position: 'top-right' // 通知位置
}
```

## UI组件

### 通知按钮
- 位置：聊天窗口头部右侧
- 显示未读通知数量徽章
- 有未读通知时会有脉冲动画效果

### 通知面板
- 触发：点击通知按钮
- 显示最近10条通知
- 支持标记已读、清空等操作
- 区分已读/未读状态

## 样式特性

### 通知项样式
- 未读通知：蓝色背景，左侧蓝色边框
- 已读通知：默认背景
- 悬停效果：浅灰色背景
- 图标区分：退款通知（红色金钱图标）、新订单通知（绿色购物袋图标）、一般通知（蓝色铃铛图标）

### 动画效果
- 通知按钮脉冲动画（有未读通知时）
- 通知弹出动画
- 状态指示器动画

## 使用方法

### 1. 基本使用
通知管理功能已集成到 `MerchantChat.vue` 组件中，无需额外配置即可使用。

### 2. 测试通知
可以使用 `NotificationTest.vue` 组件来测试通知功能：

```vue
<template>
  <NotificationTest />
</template>

<script setup>
import NotificationTest from '@/modules/merchant/components/NotificationTest.vue'
</script>
```

### 3. 自定义通知设置
可以通过修改 `notificationSettings` 来自定义通知行为：

```javascript
// 禁用声音提醒
notificationSettings.value.sound = false

// 更改通知位置
notificationSettings.value.position = 'bottom-right'

// 禁用退款通知
notificationSettings.value.refund = false

// 禁用新订单通知
notificationSettings.value.newOrder = false
```

## 技术实现

### 核心方法
- `handleNotification()`: 主通知处理器
- `handleRefundNotification()`: 退款通知处理
- `handleNewOrderNotification()`: 新订单通知处理
- `handleGeneralNotification()`: 一般通知处理
- `showDesktopNotification()`: 桌面通知显示
- `addNotificationToList()`: 添加通知到列表

### 状态管理
- `notifications`: 通知列表
- `notificationSettings`: 通知设置
- `unreadNotificationCount`: 未读通知计数

### 权限要求
- 桌面通知需要用户授权浏览器通知权限
- 组件会自动请求权限

## 注意事项

1. **浏览器兼容性**: 桌面通知功能需要现代浏览器支持
2. **权限管理**: 首次使用会请求通知权限
3. **性能优化**: 通知列表限制为100条，自动清理旧通知
4. **响应式设计**: 通知面板适配不同屏幕尺寸

## 扩展功能

### 添加新的通知类型
1. 在 `handleNotification()` 中添加新的事件类型判断
2. 创建对应的处理方法
3. 添加相应的图标和样式

### 自定义通知样式
1. 修改 CSS 中的通知相关样式
2. 调整颜色、动画、布局等

### 集成其他通知渠道
1. 邮件通知
2. 短信通知
3. 第三方推送服务
