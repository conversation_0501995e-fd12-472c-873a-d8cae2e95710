通过分析Dashboard.vue代码，我发现商家仪表盘页面在加载时需要后端提供以下API接口和数据：

1. 商家信息与登录状态接口
1.1 商家信息获取/token校验接口
CopyInsert
接口功能：验证商家登录状态并获取基本信息
调用时机：页面初始化时
调用方法：merchantStore.retoken()
返回数据字段：
{
  success: boolean,           // 是否成功
  merchantInfo: {
    id: number,               // 商家ID
    merchantName: string,     // 商家名称
    isOperating: boolean      // 是否营业中
    // 其他商家基础信息
  }
}
2. 统计数据接口
2.1 商家统计数据接口
CopyInsert
接口功能：获取仪表盘显示的统计数据
调用时机：页面初始化和手动刷新时
调用方法：merchantStore.fetchStatistics()
返回数据字段：
{
  todaySales: number,         // 今日销售额
  todayOrders: number,        // 今日订单数
  pendingOrders: number,      // 待处理订单数
  lowStockProducts: number,   // 库存不足商品数
  salesTrend: number,         // 销售趋势百分比(与昨日对比)
  ordersTrend: number         // 订单趋势百分比(与昨日对比)
}
3. 营业状态管理接口
3.1 切换营业状态接口
CopyInsert
接口功能：切换商家营业/暂停状态
调用时机：点击"开始营业"/"暂停营业"按钮时
调用方法：merchantStore.toggleOperationStatus()
请求参数：
{
  status: boolean             // true表示营业，false表示暂停
}
返回数据字段：
{
  success: boolean,           // 是否成功
  isOperating: boolean,       // 当前营业状态
  message: string             // 提示信息
}
4. 订单相关接口
4.1 获取最近订单列表
CopyInsert
接口功能：获取最近订单数据
调用时机：页面初始化时
调用方法：getOrderList()
请求参数：
{
  page: number,               // 页码，默认1
  pageSize: number            // 每页条数，默认5
}
返回数据字段：
{
  list: [
    {
      id: number,             // 订单ID
      orderNo: string,        // 订单号
      userName: string,       // 客户名称
      status: string,         // 订单状态(pending_payment, paid, pending_shipment, shipped, completed, canceled, closed)
      totalAmount: number,    // 订单金额
      createTime: string      // 下单时间
    }
  ],
  total: number,              // 总订单数
  page: number,               // 当前页码
  pageSize: number            // 每页条数
}
5. 图表数据接口
5.1 销售趋势图表数据
CopyInsert
接口功能：获取销售趋势图表数据
调用时机：页面初始化和切换图表周期时
调用方法：未直接体现在代码中，可能是getMerchantChartData('sales', period)
请求参数：
{
  type: string,               // 'sales'表示销售额数据
  period: string              // 'week'表示本周，'month'表示本月
}
返回数据字段：
{
  labels: string[],           // 时间标签数组(如日期)
  values: number[],           // 对应的销售额数组
  totalAmount: number,        // 总销售额
  averageAmount: number       // 平均销售额
}
5.2 订单分布图表数据
CopyInsert
接口功能：获取订单分布图表数据
调用时机：页面初始化和刷新图表时
调用方法：未直接体现在代码中，可能是getMerchantChartData('orders', period)
请求参数：
{
  type: string                // 'orders'表示订单数据
}
返回数据字段：
{
  categories: [               // 订单分类统计
    {
      name: string,           // 分类名称(如"待支付"、"已完成"等)
      value: number,          // 订单数量
      percentage: number      // 占比百分比
    }
  ],
  totalOrders: number         // 总订单数
}
5.3 订单数据导出接口
CopyInsert
接口功能：导出订单数据
调用时机：点击"导出数据"按钮时
调用方法：未直接体现在代码中
请求参数：
{
  period: string,             // 导出周期，如"week"、"month"等
  format: string              // 导出格式，如"csv"、"excel"等
}
返回数据：
- 二进制文件流或下载链接
接口汇总
根据Dashboard.vue页面的需求，后端需要提供以下核心API接口：

商家登录状态验证及信息获取接口
商家统计数据获取接口
商家营业状态切换接口
最近订单列表获取接口
销售趋势图表数据接口
订单分布图表数据接口
订单数据导出接口