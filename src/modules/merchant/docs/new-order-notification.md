# 新订单通知功能实现

## 功能概述

为商家聊天模块新增了新订单通知功能，当用户下单并支付成功后，商家会收到实时的新订单提醒。

## 通知数据格式

后端通过WebSocket推送的新订单通知数据格式：

```javascript
{
  type: 'notification',
  event: 'merchant_new_order',
  session_id: 0,
  data: {
    content: "您收到新订单，请及时处理",
    data: {
      delivery_addr: '配送地址',
      merchant_id: 1,
      merchant_name: '商家',
      order_id: 85,
      order_no: '202507251442251420',
      customer_name: '张三',
      total_amount: 128.50
    },
    expire_time: 1753514740,
    order_id: 0,
    order_no: "",
    persistent: true,
    priority: 2,
    title: "收到新订单",
    type: "merchant_new_order"
  },
  timestamp: 1753428340
}
```

## 实现特性

### 1. 通知显示
- **Element Plus Notification**: 成功类型（绿色），显示6秒
- **桌面通知**: 系统原生通知，显示6秒
- **声音提醒**: 播放提示音
- **通知中心**: 绿色购物袋图标标识

### 2. 交互功能
- 点击通知可跳转到订单详情页面
- 支持通知设置开关控制
- 自动添加到通知历史列表

### 3. 视觉设计
- 图标：绿色购物袋图标 (ShoppingBag)
- 颜色：成功绿色主题
- 动画：通知按钮脉冲效果（有未读通知时）

## 代码实现

### 1. 通知处理逻辑

```javascript
// 在 handleNotification 中添加新订单事件处理
if (notification.event === 'merchant_new_order') {
  handleNewOrderNotification(notification)
}

// 新订单通知处理方法
const handleNewOrderNotification = (notification: any) => {
  if (!notificationSettings.value.enabled || !notificationSettings.value.newOrder) {
    return
  }
  
  const data = notification.data
  
  // 显示Element Plus通知
  ElNotification({
    title: data.title || '收到新订单',
    message: data.content || '您有新的订单需要处理',
    type: 'success',
    position: notificationSettings.value.position,
    duration: 6000,
    showClose: true,
    onClick: () => {
      // 跳转到订单详情页
      if (data.data?.order_id) {
        window.open(`/merchant/order/detail/${data.data.order_id}`, '_blank')
      }
    }
  })
  
  // 播放提示音和显示桌面通知
  // ...
}
```

### 2. UI组件更新

```vue
<!-- 通知图标显示 -->
<div class="notification-icon">
  <el-icon v-if="notification.type === 'merchant_refund_request'" color="#f56c6c">
    <Money />
  </el-icon>
  <el-icon v-else-if="notification.type === 'merchant_new_order'" color="#67c23a">
    <ShoppingBag />
  </el-icon>
  <el-icon v-else color="#409eff">
    <Bell />
  </el-icon>
</div>
```

### 3. 通知设置

```javascript
const notificationSettings = ref({
  enabled: true,    // 是否启用通知
  sound: true,      // 是否播放声音
  desktop: true,    // 是否显示桌面通知
  refund: true,     // 是否显示退款通知
  newOrder: true,   // 是否显示新订单通知 ← 新增
  position: 'top-right' as const
})
```

## 测试功能

### 1. 测试组件
使用 `NotificationTest.vue` 组件可以测试新订单通知：

```vue
<el-button @click="testNewOrderNotification" type="success">
  测试新订单通知
</el-button>
```

### 2. 演示页面
在 `NotificationDemo.vue` 页面中可以体验完整的通知功能。

## 使用方式

### 1. 自动工作
新订单通知功能已集成到现有的 `MerchantChat.vue` 组件中，当接收到 `merchant_new_order` 事件时会自动触发。

### 2. 自定义设置
可以通过修改通知设置来控制新订单通知的行为：

```javascript
// 禁用新订单通知
notificationSettings.value.newOrder = false

// 只在特定时间段启用
if (isBusinessHours()) {
  notificationSettings.value.newOrder = true
}
```

## 扩展功能

### 1. 订单信息展示
可以在通知内容中显示更多订单信息：
- 订单金额
- 客户姓名
- 配送地址
- 商品信息

### 2. 快速操作
可以在通知中添加快速操作按钮：
- 接受订单
- 查看详情
- 联系客户

### 3. 统计功能
可以添加新订单通知的统计功能：
- 今日新订单数量
- 通知响应时间
- 处理效率统计

## 注意事项

1. **权限要求**: 桌面通知需要用户授权浏览器通知权限
2. **性能优化**: 通知列表自动限制为100条，避免内存占用过多
3. **响应式设计**: 通知面板适配不同屏幕尺寸
4. **错误处理**: 对无效的通知数据进行容错处理

## 相关文件

- `src/modules/merchant/components/MerchantChat.vue` - 主要实现文件
- `src/modules/merchant/components/NotificationTest.vue` - 测试组件
- `src/modules/merchant/views/NotificationDemo.vue` - 演示页面
- `src/modules/merchant/docs/notification-management.md` - 完整文档
