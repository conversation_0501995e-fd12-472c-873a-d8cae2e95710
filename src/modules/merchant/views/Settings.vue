<!--
 * @author: <PERSON>rae <PERSON>
 * @date: 2023-06-15
 * @version: 1.0
 * @description: 商家账户设置页面，包含基本信息和安全设置两个标签页
-->
<template>
  <div class="settings-page">
    <div class="page-header">
    </div>
    
    <el-card class="settings-card">
      <el-tabs v-model="activeTab">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-form :model="basicForm" :rules="basicRules" ref="basicFormRef" label-width="100px">
            <el-form-item label="店铺名称" prop="merchantName">
              <el-input v-model="basicForm.merchantName" placeholder="请输入店铺名称" />
            </el-form-item>
            
            <el-form-item label="店铺LOGO">
              <el-upload
                class="avatar-uploader"
                action="/v1/merchant/upload"
                :show-file-list="false"
                :on-success="handleLogoSuccess"
              >
                <img v-if="basicForm.logo" :src="basicForm.logo" class="avatar" />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </el-form-item>
            
            <el-form-item label="联系人姓名" prop="contactName">
              <el-input v-model="basicForm.contactName" placeholder="请输入联系人姓名" />
            </el-form-item>
            
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="basicForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
            
            <el-form-item label="电子邮箱" prop="email">
              <el-input v-model="basicForm.email" placeholder="请输入电子邮箱" />
            </el-form-item>
            
            <el-form-item label="店铺简介">
              <el-input 
                v-model="basicForm.description" 
                type="textarea" 
                :rows="4" 
                placeholder="请输入店铺简介"
              />
            </el-form-item>
            
            <el-form-item label="地址" prop="address">
              <el-input v-model="basicForm.address" placeholder="请输入商家地址" />
            </el-form-item>
            
            <el-form-item label="支持自取">
              <el-switch 
                v-model="basicForm.supportPickup" 
                :active-value="1" 
                :inactive-value="0"
                active-text="支持" 
                inactive-text="不支持"
              />
            </el-form-item>
            
            <el-form-item label="商家配送">
              <el-switch 
                v-model="basicForm.supportSelfDelivery" 
                :active-value="1" 
                :inactive-value="0"
                active-text="支持" 
                inactive-text="不支持"
              />
            </el-form-item>
            
            <el-form-item label="位置选择" style="width: 100%;">
              <div class="location-selector" style="width: 100%;">
                <TiandituMap
                  :width="'100%'"
                  :height="'300px'"
                  :longitude="(basicForm.longitude && basicForm.latitude) ? basicForm.longitude : defaultMapConfig.center[0]"
                  :latitude="(basicForm.longitude && basicForm.latitude) ? basicForm.latitude : defaultMapConfig.center[1]"
                  :zoom="(basicForm.longitude && basicForm.latitude) ? 16 : defaultMapConfig.zoom"
                  :editable-marker="editableMarkerData"
                  :edit-mode="true"
                  :add-marker-mode="!basicForm.longitude || !basicForm.latitude || basicForm.longitude === 0 || basicForm.latitude === 0"
                  @map-click="handleMapClick"
                  @editable-marker-update="handleMarkerUpdate"
                />
                <div class="coordinate-info">
                  <span>经度: {{ basicForm.longitude || '未设置' }}</span>
                  <span>纬度: {{ basicForm.latitude || '未设置' }}</span>
                </div>
              </div>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveBasicInfo" :loading="saving">保存</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 安全设置 -->
        <el-tab-pane label="安全设置" name="security">
          <el-form :model="securityForm" :rules="securityRules" ref="securityFormRef" label-width="100px">
            <el-form-item label="原密码" prop="oldPassword">
              <el-input 
                v-model="securityForm.oldPassword" 
                type="password" 
                placeholder="请输入原密码" 
                show-password
              />
            </el-form-item>
            
            <el-form-item label="新密码" prop="newPassword">
              <el-input 
                v-model="securityForm.newPassword" 
                type="password" 
                placeholder="请输入新密码" 
                show-password
              />
            </el-form-item>
            
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input 
                v-model="securityForm.confirmPassword" 
                type="password" 
                placeholder="请再次输入新密码" 
                show-password
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="changePassword" :loading="changing">修改密码</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useMerchantStore } from '@/modules/merchant/stores/merchantStore'
import TiandituMap from '@/components/map/TiandituMap.vue'
import type { FormInstance } from 'element-plus'

// 商家store
const merchantStore = useMerchantStore()

// 当前激活的标签页
const activeTab = ref('basic')

// 表单引用
const basicFormRef = ref<FormInstance | null>(null)
const securityFormRef = ref<FormInstance | null>(null)

// 加载状态
const saving = ref(false)
const changing = ref(false)

// 基本信息表单
const basicForm = reactive({
  merchantName: '',
  logo: '',
  contactName: '',
  phone: '',
  email: '',
  description: '',
  longitude: 0,
  latitude: 0,
  address: '',
  supportPickup: 0, // 是否支持自取，0不支持，1支持
  supportSelfDelivery: 0 // 是否支持商家自己派送，0不支持，1支持
})

// 安全设置表单
const securityForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 基本信息验证规则
const basicRules = {
  merchantName: [
    { required: true, message: '请输入店铺名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  contactName: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入商家地址', trigger: 'blur' }
  ]
}

// 安全设置验证规则
const securityRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (_rule: any, value: string, callback: (error?: Error) => void) => {
        if (value !== securityForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 处理LOGO上传成功
interface UploadResponse {
  url: string
  [key: string]: any
}

function handleLogoSuccess(response: UploadResponse) {
  basicForm.logo = response.url
}

// 地图默认配置
const defaultMapConfig = {
  center: [116.397428, 39.90923], // 默认位置北京天安门
  zoom: 12
};

// 可编辑标记点数据
const editableMarkerData = computed(() => {
  if (basicForm.longitude && basicForm.latitude && 
      basicForm.longitude !== 0 && basicForm.latitude !== 0) {
    return {
      lng: basicForm.longitude,
      lat: basicForm.latitude,
      title: basicForm.merchantName || '商家位置'
    };
  }
  return null;
});

/**
 * 处理地图点击事件
 * @param event 点击事件
 */
function handleMapClick(event: any) {
  // 当商家经纬度为0时，第一次点击地图创建可编辑标记点
  if (!basicForm.longitude || !basicForm.latitude || 
      basicForm.longitude === 0 || basicForm.latitude === 0) {
    if (event && event.lnglat) {
      basicForm.longitude = event.lnglat.lng;
      basicForm.latitude = event.lnglat.lat;
    }
  }
}

/**
 * 处理标记点更新事件
 * @param lnglat 更新的经纬度
 */
function handleMarkerUpdate(lnglat: { lng: number, lat: number }) {
  // 当用户拖拽标记点时更新坐标数据
  if (lnglat && lnglat.lng && lnglat.lat) {
    // 保留6位小数
    basicForm.longitude = parseFloat(lnglat.lng.toFixed(6));
    basicForm.latitude = parseFloat(lnglat.lat.toFixed(6));
  }
}

// 保存基本信息
async function saveBasicInfo() {
  if (!basicFormRef.value) return
  
  await basicFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      saving.value = true
      try {
        // 这里应该有API请求保存商家信息
        // 模拟保存
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // 更新本地商家信息
        merchantStore.updateInfo({
          name: basicForm.merchantName,
          logo: basicForm.logo,
          contact_name: basicForm.contactName,
          contact_mobile: basicForm.phone,
          contact_email: basicForm.email,
          description: basicForm.description,
          longitude: basicForm.longitude,
          latitude: basicForm.latitude,
          address: basicForm.address,
          support_pickup: basicForm.supportPickup,
          support_self_delivery: basicForm.supportSelfDelivery
        })
        
        ElMessage.success('保存成功')
      } catch (error) {
        ElMessage.error('保存失败，请重试')
        console.error(error)
      } finally {
        saving.value = false
      }
    }
  })
}

// 修改密码
async function changePassword() {
  if (!securityFormRef.value) return
  
  await securityFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      changing.value = true
      try {
        // 这里应该有API请求修改密码
        // 模拟修改
        await new Promise(resolve => setTimeout(resolve, 500))
        
        ElMessage.success('密码修改成功')
        // 清空表单
        securityForm.oldPassword = ''
        securityForm.newPassword = ''
        securityForm.confirmPassword = ''
        securityFormRef.value?.resetFields()
      } catch (error) {
        ElMessage.error('密码修改失败，请重试')
        console.error(error)
      } finally {
        changing.value = false
      }
    }
  })
}

// 商家信息接口
interface MerchantInfo {
  name?: string
  logo?: string
  contact_name?: string
  contact_mobile?: string
  contact_email?: string
  description?: string
  longitude?: number
  latitude?: number
  address?: string
  support_pickup?: number
  support_self_delivery?: number
  [key: string]: any
}

// 初始化
onMounted(() => {
  // 加载商家信息
  if (merchantStore.merchantInfo) {
    const info = merchantStore.merchantInfo as MerchantInfo
    basicForm.merchantName = info.name || ''
    basicForm.logo = info.logo || ''
    basicForm.contactName = info.contact_name || ''
    basicForm.phone = info.contact_mobile || ''
    basicForm.email = info.contact_email || ''
    basicForm.description = info.description || ''
    basicForm.longitude = Number(info.longitude) || 0
    basicForm.latitude = Number(info.latitude) || 0
    basicForm.address = info.address || ''
    basicForm.supportPickup = Number(info.support_pickup) || 0
    basicForm.supportSelfDelivery = Number(info.support_self_delivery) || 0
  }
})
</script>

<style scoped>
.settings-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.settings-card {
  margin-bottom: 20px;
}

.avatar-uploader {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.avatar-uploader:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}

.location-selector {
  .coordinate-info {
    margin-top: 10px;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-size: 14px;
    color: #606266;
    display: flex;
    justify-content: space-between;
  }
}
</style>