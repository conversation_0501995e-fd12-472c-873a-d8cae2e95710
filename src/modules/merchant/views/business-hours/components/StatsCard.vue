<template>
  <el-card class="stats-card">
    <template #header>
      <div class="card-header">
        <span>营业时长统计</span>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="dateShortcuts"
          value-format="YYYY-MM-DD"
          @change="handleDateChange"
          size="small"
          style="width: 280px"
        />
      </div>
    </template>
    
    <div class="stats-overview">
      <div class="stat-item">
        <div class="stat-value">{{ formatDuration(stats.total_duration) }}</div>
        <div class="stat-label">总营业时长</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ formatDuration(stats.average_duration) }}</div>
        <div class="stat-label">平均每次营业</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ stats.total_open_times || 0 }}次</div>
        <div class="stat-label">总营业次数</div>
      </div>
    </div>
    
    <div class="chart-container">
      <div ref="chartRef" style="width: 100%; height: 300px;"></div>
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';
import * as echarts from 'echarts';
import dayjs from 'dayjs';

// 扩展ECharts类型声明
declare module 'echarts' {
  interface ECharts {
    resizeTimer?: number | null;
  }
}

const props = defineProps({
  stats: {
    type: Object,
    required: true,
    default: () => ({
      total_duration: 0,          // 总营业时长(小时)
      average_duration: 0,        // 平均每次营业时长(小时)
      total_open_times: 0,        // 总营业次数
      complete_open_times: 0,     // 完整营业次数
      incomplete_open_times: 0,   // 不完整营业次数
      average_open_time_per_day: 0, // 平均每天营业时长(小时)
      daily_stats: [],             // 每日统计数据
      days_count: 0,              // 统计天数
      period: ''                  // 统计周期
    })
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['date-change']);

const chartRef = ref<HTMLElement | null>(null);
let chart: (echarts.ECharts & { resizeTimer?: number | null }) | null = null;

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近7天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 6);
      return [start, end];
    }
  },
  {
    text: '最近30天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 29);
      return [start, end];
    }
  },
  {
    text: '本月',
    value: () => {
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth(), 1);
      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      return [start, end];
    }
  }
];

// 日期范围
const dateRange = ref([
  dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]);

// 格式化时长
const formatDuration = (hours: number) => {
  if (hours === undefined || hours === null) return '--';
  
  const h = Math.floor(hours);
  const m = Math.round((hours - h) * 60);
  
  if (h > 0 && m > 0) {
    return `${h}小时${m}分钟`;
  } else if (h > 0) {
    return `${h}小时`;
  } else if (m > 0) {
    return `${m}分钟`;
  } else {
    return '0分钟';
  }
};

// 处理日期变化
const handleDateChange = () => {
  if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
    emit('date-change', {
      startDate: dateRange.value[0],
      endDate: dateRange.value[1]
    });
  }
};

// 存储resizeObserver以便在组件卸载时清理
let resizeObserver: ResizeObserver | null = null;

// 组件卸载时清理资源
onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
  if (chart) {
    chart.dispose();
    chart = null;
  }
});

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  
  // 确保容器可见
  if (chartRef.value.offsetWidth === 0) {
    // 如果容器不可见，延迟初始化
    setTimeout(initChart, 100);
    return;
  }
  
  // 如果已存在图表实例，先销毁
  if (chart) {
    chart.dispose();
  }
  
  chart = echarts.init(chartRef.value);
  updateChart();
  
  // 如果已存在观察器，先断开
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
  
  // 创建新的resize观察器
  resizeObserver = new ResizeObserver(() => {
    if (chart) {
      chart.resize();
    }
  });
  
  resizeObserver.observe(chartRef.value);
};

// 更新图表数据
const updateChart = () => {
  if (!chart || !props.stats || !props.stats.daily_stats) {
    console.log('统计数据不完整', props.stats);
    return;
  }
  
  // 准备数据
  const dates = props.stats.daily_stats.map((item: any) => item.date);
  const durations = props.stats.daily_stats.map((item: any) => item.duration || 0);
  const openTimes = props.stats.daily_stats.map((item: any) => item.open_times || 0);
  
  console.log('图表数据', {
    dates,
    durations,
    openTimes,
    daily_stats: props.stats.daily_stats
  });
  
  // 设置图表选项
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any[]) {
        const date = params[0].axisValue;
        let result = `<div>${date}</div>`;
        
        params.forEach(param => {
          if (param.seriesName === '营业时长') {
            result += `<div>营业时长: ${formatDuration(param.value)}</div>`;
          } else {
            result += `<div>营业次数: ${param.value}次</div>`;
          }
        });
        
        return result;
      }
    },
    legend: {
      data: ['营业时长', '营业次数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 45,
        formatter: (value: string) => {
          return value.substring(5); // 只显示月-日
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '营业时长(小时)',
        position: 'left',
        axisLabel: {
          formatter: (value: number) => value.toFixed(1)
        }
      },
      {
        type: 'value',
        name: '营业次数',
        position: 'right',
        axisLabel: {
          formatter: (value: number) => value
        }
      }
    ],
    series: [
      {
        name: '营业时长',
        type: 'bar',
        data: durations,
        itemStyle: {
          color: '#409EFF',
          borderRadius: [4, 4, 0, 0]
        },
        barWidth: '40%',
        yAxisIndex: 0,
        tooltip: {
          valueFormatter: (value: number) => formatDuration(value)
        }
      },
      {
        name: '营业次数',
        type: 'line',
        data: openTimes,
        yAxisIndex: 1,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#67C23A'
        },
        lineStyle: {
          color: '#67C23A',
          width: 3
        }
      }
    ]
  };
  
  chart.setOption(option);
};

// 处理窗口大小变化
const handleResize = () => {
  if (chart) {
    // 添加防抖，避免频繁触发resize
    if (chart.resizeTimer) {
      clearTimeout(chart.resizeTimer);
    }
    chart.resizeTimer = window.setTimeout(() => {
      if (chart) {  // 再次检查chart是否存在
        chart.resize({
          animation: {
            duration: 300
          }
        });
      }
    }, 100) as unknown as number;  // 类型转换，因为setTimeout在浏览器中返回number
  }
};

// 监听数据变化
watch(() => props.stats, () => {
  updateChart();
}, { deep: true });

// 组件挂载时初始化图表
onMounted(async () => {
  // 等待DOM更新完成
  await nextTick();
  initChart();
  // 初始渲染完成后强制调整大小
  setTimeout(() => {
    if (chart) {
      chart.resize();
    }
  }, 100);
  window.addEventListener('resize', handleResize);
});

// 组件卸载前清理
onBeforeUnmount(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.stats-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-overview {
  display: flex;
  justify-content: space-around;
  margin-bottom: 24px;
  text-align: center;
}

.stat-item {
  flex: 1;
  padding: 0 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.chart-container {
  margin-top: 24px;
}

@media (max-width: 768px) {
  .stats-overview {
    flex-direction: column;
    gap: 16px;
  }
  
  .stat-item {
    padding: 0;
  }
}
</style>