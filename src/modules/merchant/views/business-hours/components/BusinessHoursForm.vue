<!--
 * 商家营业时间表单组件
 * 提供添加和编辑营业时间的功能，支持多时间段设置
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="formData.id ? '编辑营业时间' : '添加营业时间'"
    width="600px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="星期" prop="weekdays">
        <el-checkbox-group v-model="formData.weekdays">
          <el-checkbox v-for="day in weekdays" :key="day.value" :label="day.value">
            {{ day.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="营业时间" prop="timeRanges">
        <div v-for="(time, index) in formData.timeRanges" :key="index" class="time-range-item">
          <el-time-picker
            v-model="time.range"
            is-range
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="HH:mm"
            value-format="HH:mm"
          />
          <el-button
            v-if="formData.timeRanges.length > 1"
            type="danger"
            :icon="Delete"
            circle
            @click="removeTimeRange(index)"
          />
        </div>
        <el-button type="primary" :icon="Plus" @click="addTimeRange">
          添加时间段
        </el-button>
      </el-form-item>

      <el-form-item label="重复方式" prop="repeatWeekly">
        <el-radio-group v-model="formData.repeatWeekly">
          <el-radio :label="true">每周重复</el-radio>
          <el-radio :label="false">仅一次</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import { Plus, Delete } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';

interface TimeRange {
  range: [string, string];
  disabled?: boolean;
}

interface FormData {
  id: number | null;
  weekdays: number[];
  timeRanges: TimeRange[];
  repeatWeekly: boolean;
}

interface WeekdayOption {
  label: string;
  value: number;
}

const props = withDefaults(defineProps<{
  visible: boolean;
  formData: Partial<FormData>;
}>(), {
  visible: false,
  formData: () => ({
    id: null,
    weekdays: [],
    timeRanges: [{ range: ['09:00', '18:00'], disabled: false }],
    repeatWeekly: true
  })
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'submit', data: FormData): void;
}>();

const formRef = ref<FormInstance>();
const dialogVisible = ref(props.visible);

const weekdays: WeekdayOption[] = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 0 },
];

const formData = reactive<FormData>({
  id: null,
  weekdays: [],
  timeRanges: [{ range: ['09:00', '18:00'], disabled: false }],
  repeatWeekly: true
});

// Initialize form data from props
Object.assign(formData, props.formData);

const rules = reactive<FormRules>({
  weekdays: [
    { 
      type: 'array', 
      required: true, 
      message: '请至少选择一天', 
      trigger: 'change',
      validator: (_: unknown, value: unknown, callback: (error?: Error) => void) => {
        if (Array.isArray(value) && value.length > 0) {
          callback();
        } else {
          callback(new Error('请至少选择一天'));
        }
      }
    }
  ]
});

const addTimeRange = (): void => {
  formData.timeRanges.push({ range: ['09:00', '18:00'], disabled: false });
};

const removeTimeRange = (index: number): void => {
  if (formData.timeRanges.length > 1) {
    formData.timeRanges.splice(index, 1);
  } else {
    ElMessage.warning('至少需要保留一个时间段');
  }
};

const validateTimeRanges = (): boolean => {
  for (const time of formData.timeRanges) {
    if (!time.range || !time.range[0] || !time.range[1]) {
      ElMessage.warning('请填写完整的营业时间');
      return false;
    }
    if (time.range[0] >= time.range[1]) {
      ElMessage.warning('结束时间必须晚于开始时间');
      return false;
    }
  }
  return true;
};

const handleSubmit = async (): Promise<void> => {
  if (!formRef.value) return;
  
  try {
    const valid = await formRef.value.validate();
    if (!valid || !validateTimeRanges()) return;
    
    emit('submit', { 
      ...formData,
      weekdays: [...formData.weekdays].sort((a, b) => a - b)
    });
    dialogVisible.value = false;
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const handleClose = (): void => {
  formRef.value?.resetFields();
  emit('update:visible', false);
};

// Watch for visible prop changes
watch(() => props.visible, (val: boolean) => {
  dialogVisible.value = val;
  if (val) {
    Object.assign(formData, props.formData);
  }
});

// Sync dialog visibility to parent
watch(dialogVisible, (val: boolean) => {
  emit('update:visible', val);
});
</script>

<style scoped>
.time-range-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  margin-bottom: 16px;
}
.time-range-item :deep(.el-time-picker) {
  margin-right: 8px;
}
</style>
