<!--
 * 营业状态卡片组件
 * 展示当前营业状态和营业时间概览
-->
<template>
  <el-card class="operation-status-card">
    <template #header>
      <div class="card-header">
        <span>营业状态</span>
        <el-button 
          v-if="editable" 
          type="primary" 
          link
          :loading="statusLoading"
          @click="toggleOperationStatus"
        >
          {{ isOpen ? '暂停营业' : '开始营业' }}
        </el-button>
      </div>
    </template>
    
    <div class="status-content">
      <div class="status-indicator" :class="{ 'status-open': isOpen, 'status-closed': !isOpen }">
        <span class="status-dot"></span>
        <span class="status-text">{{ isOpen ? '营业中' : '已打烊' }}</span>
      </div>
      
      <div v-if="nextChange" class="next-change">
        <span v-if="isOpen">
          将于 {{ nextChange.time }} 自动打烊
        </span>
        <span v-else>
          将于 {{ nextChange.time }} 自动开始营业
        </span>
        <el-tag size="small" type="info">
          {{ nextChange.date }}
        </el-tag>
      </div>
      
      <div class="today-hours" v-if="todayHours.length > 0">
        <div class="section-title">今日营业时间</div>
        <div v-for="(time, index) in todayHours" :key="index" class="time-range">
          {{ time.startTime }} - {{ time.endTime }}
        </div>
      </div>
      
      <div class="action-buttons" v-if="editable">
        <el-button type="primary" @click="handleAddHours">
          <el-icon><Plus /></el-icon>
          添加营业时间
        </el-button>
        <!-- <el-button @click="handleBatchSet">
          批量设置
        </el-button> -->
      </div>
    </div>
  </el-card>
  
  <!-- 修改状态对话框 -->
  <el-dialog
    v-model="statusDialogVisible"
    title="修改营业状态"
    width="500px"
  >
    <el-form :model="statusForm" label-width="100px">
      <el-form-item label="营业状态">
        <el-radio-group v-model="statusForm.status">
          <el-radio :value="1">营业中</el-radio>
          <el-radio :value="0">休息中</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="自动恢复" prop="autoRestore">
        <el-switch v-model="statusForm.autoRestore" />
      </el-form-item>
      
      <el-form-item 
        v-if="statusForm.autoRestore" 
        label="恢复时间" 
        prop="restoreTime"
      >
        <el-date-picker
          v-model="statusForm.restoreTime"
          type="datetime"
          placeholder="选择恢复时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          :disabled-date="disabledDate"
          :disabled-hours="disabledHours"
          :disabled-minutes="disabledMinutes"
        />
      </el-form-item>
      
      <el-form-item label="备注说明" prop="remarks">
        <el-input
          v-model="statusForm.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入修改原因或备注信息"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="statusDialogVisible = false">取消</el-button>
        <!-- <el-button type="primary" @click="handleStatusSubmit">
          确认修改
        </el-button> -->
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { useMerchantStore } from '@/modules/merchant/stores/merchantStore';

const merchantStore = useMerchantStore();
// 所以这里不需要额外的响应类型定义

// 下次营业时间变更类型
export interface NextChangeInfo {
  time: string;
  date: string;
  // 其他可能存在的属性
  [key: string]: any;
}

// 营业时间段类型
export interface BusinessHour {
  startTime: string;
  endTime: string;
  id?: string | number;
  // 其他可能存在的属性
  [key: string]: any;
}

// 状态表单类型
interface StatusForm {
  status: number; // 1-营业中, 0-休息中
  autoRestore: boolean;
  restoreTime: string;
  remarks: string;
}

const props = withDefaults(defineProps<{
  // 是否营业中
  isOpen: boolean;
  // 营业状态文本
  statusText?: string;
  // 今日营业时间段
  todayHours: BusinessHour[];
  // 下次变更信息
  nextChange: NextChangeInfo | null;
  // 是否可编辑
  editable?: boolean;
}>(), {
  isOpen: false,
  statusText: '',
  todayHours: () => [],
  nextChange: null,
  editable: true
});

const emit = defineEmits<{
  // 添加营业时间事件
  (e: 'add-hours'): void;
  // 批量设置事件
  (e: 'batch-set'): void;
  // 编辑状态事件
  (e: 'edit-status', data: { status: number; overrideAutoSchedule?: boolean }): void;
  // 状态变更事件
  (e: 'status-change', data: { status: number }): void;
}>();

const statusDialogVisible = ref(false);

const statusLoading = ref(false);

/**
 * 切换营业状态
 */
async function toggleOperationStatus() {
  try {
    statusLoading.value = true;
    const newStatus = await merchantStore.toggleOperationStatus();
    // 显示成功消息
    ElMessage.success(newStatus === 1 ? '状态已更新为营业中' : '状态已更新为休息中');
    // 触发状态变更事件
    emit('status-change', { status: newStatus });
  } catch (error) {
    console.error('切换营业状态失败:', error);
  } finally {
    statusLoading.value = false;
  }
}

const statusForm = reactive<StatusForm>({
  status: props.isOpen ? 1 : 0,
  autoRestore: false,
  restoreTime: '',
  remarks: ''
});



// const handleStatusSubmit = () => {
//   // 发送状态变更请求
//   emit('status-change', statusForm.status);
  
//   // 关闭对话框
//   statusDialogVisible.value = false;
  
//   // 显示成功消息
//   ElMessage.success('营业状态已更新');
// };

const handleAddHours = () => {
  emit('add-hours');
};

// const handleBatchSet = () => {
//   emit('batch-set');
// };

// 禁用今天之前的时间
const disabledDate = (time: Date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return time.getTime() < today.getTime();
};

// 禁用当前时间之前的小时
const disabledHours = () => {
  const hours = [];
  const now = new Date();
  
  if (new Date().toDateString() === new Date(statusForm.restoreTime).toDateString()) {
    for (let i = 0; i < now.getHours(); i++) {
      hours.push(i);
    }
  }
  
  return hours;
};

// 禁用当前时间之前的分钟
const disabledMinutes = (hour: number) => {
  const minutes = [];
  const now = new Date();
  
  if (new Date().toDateString() === new Date(statusForm.restoreTime).toDateString() && 
      hour === now.getHours()) {
    for (let i = 0; i < now.getMinutes(); i++) {
      minutes.push(i);
    }
  }
  
  return minutes;
};
</script>

<style scoped>
.operation-status-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-content {
  text-align: center;
  padding: 20px 0;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 24px;
  border-radius: 20px;
  background-color: #f0f9eb;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-open .status-dot {
  background-color: #67c23a;
  box-shadow: 0 0 6px #67c23a;
}

.status-closed .status-dot {
  background-color: #909399;
  box-shadow: 0 0 6px #909399;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.next-change {
  margin: 12px 0;
  color: #606266;
  font-size: 14px;
}

.next-change .el-tag {
  margin-left: 8px;
}

.today-hours {
  margin: 20px 0;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.section-title {
  margin-bottom: 8px;
  color: #606266;
  font-size: 14px;
}

.time-range {
  margin: 4px 0;
  color: #303133;
}

.action-buttons {
  margin-top: 24px;
}
</style>
