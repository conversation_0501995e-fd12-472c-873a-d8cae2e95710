<!--
 * 营业时间表格组件
 * 展示商家的营业时间安排，支持编辑和删除操作
-->
<template>
  <div class="business-hours-table">
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="weekday" label="星期" width="100">
        <template #default="{ row }">
          {{ formatWeekday(row.weekday) }}
        </template>
      </el-table-column>
      <el-table-column prop="timeRanges" label="营业时间">
        <template #default="{ row }">
          <div v-for="(time, index) in row.timeRanges" :key="index" class="time-range">
            {{ time.startTime }} - {{ time.endTime }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="row.status === 'normal' ? 'success' : 'warning'">
            {{ row.status === 'normal' ? '正常' : '已关闭' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" v-if="editable">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="danger" link @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  editable: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['edit', 'delete']);

const weekdayMap = {
  0: '周日',
  1: '周一',
  2: '周二',
  3: '周三',
  4: '周四',
  5: '周五',
  6: '周六'
};

const formatWeekday = (weekday: number) => {
  return weekdayMap[weekday as keyof typeof weekdayMap] || '';
};

const handleEdit = (row: any) => {
  console.log(row)
  emit('edit', row);
};

const handleDelete = (row: any) => {
  emit('delete', row);
};

// 处理数据，将同一天的时间段合并
const tableData = computed(() => {
  const daysMap = new Map();
  
  props.data.forEach((item: any) => {
    if (!daysMap.has(item.weekday)) {
      daysMap.set(item.weekday, {
        //id: item.id,
        key: item.key,
        weekday: item.weekday,
        timeRanges: [],
        status: item.status || 'normal'
      });
    }
    
    daysMap.get(item.weekday).timeRanges.push({
      startTime: item.startTime,
      endTime: item.endTime
    });
  });
  
  // 按星期排序
  return Array.from(daysMap.values()).sort((a, b) => a.weekday - b.weekday);
});
</script>

<style scoped>
.business-hours-table {
  margin-top: 20px;
}
.time-range {
  margin: 4px 0;
}
</style>
