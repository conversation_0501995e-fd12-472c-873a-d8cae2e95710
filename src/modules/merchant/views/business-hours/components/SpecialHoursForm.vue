<!--
 * 特殊营业时间表单组件
 * 用于设置节假日的特殊营业时间
-->
<template>
  <el-dialog
    :title="formData.id ? '编辑特殊营业时间' : '添加特殊营业时间'"
    v-model="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @closed="handleClose"
  >
    <el-form 
      :model="formData" 
      :rules="rules" 
      ref="formRef" 
      label-width="100px"
      label-position="top"
    >
      <el-form-item label="日期范围" required>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="营业时间" required>
        <div class="time-range-item" v-for="(time, index) in formData.timeRanges" :key="index">
          <el-time-picker
            v-model="time.range"
            is-range
            format="HH:mm"
            value-format="HH:mm"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
          <el-button 
            v-if="index > 0" 
            type="danger" 
            text 
            :icon="Delete" 
            @click="removeTimeRange(index)"
          />
        </div>
        <el-button type="primary" text @click="addTimeRange">
          <el-icon><Plus /></el-icon> 添加时间段
        </el-button>
      </el-form-item>
      
      <el-form-item label="优先级" prop="priority">
        <el-radio-group v-model="formData.priority">
          <el-radio-button label="high">高</el-radio-button>
          <el-radio-button label="normal">中</el-radio-button>
          <el-radio-button label="low">低</el-radio-button>
        </el-radio-group>
        <div class="el-form-item-msg">
          高优先级会覆盖低优先级的设置，同优先级下最新设置生效
        </div>
      </el-form-item>
      
      <el-form-item label="备注说明" prop="remarks">
        <el-input
          v-model="formData.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入特殊营业时间的说明，如：国庆节期间延长营业时间"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import { Plus, Delete } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({
      id: null,
      startDate: '',
      endDate: '',
      timeRanges: [{ range: ['09:00', '22:00'] }],
      priority: 'normal',
      remarks: ''
    })
  }
});

const emit = defineEmits(['update:visible', 'submit']);

const dialogVisible = ref(props.visible);
const formRef = ref();
const dateRange = ref([props.formData.startDate, props.formData.endDate]);

const rules = {
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
};

const addTimeRange = () => {
  formData.timeRanges.push({ range: ['09:00', '22:00'] });
};

const removeTimeRange = (index: number) => {
  if (formData.timeRanges.length > 1) {
    formData.timeRanges.splice(index, 1);
  } else {
    ElMessage.warning('至少保留一个时间段');
  }
};

const handleSubmit = async () => {
  try {
    if (!dateRange.value || dateRange.value.length !== 2) {
      ElMessage.warning('请选择日期范围');
      return;
    }
    
    // 验证时间范围
    for (const time of formData.timeRanges) {
      if (!time.range || !time.range[0] || !time.range[1]) {
        ElMessage.warning('请填写完整的营业时间');
        return;
      }
      if (time.range[0] >= time.range[1]) {
        ElMessage.warning('结束时间必须晚于开始时间');
        return;
      }
    }
    
    const submitData = {
      ...formData,
      startDate: dateRange.value[0],
      endDate: dateRange.value[1]
    };
    
    emit('submit', submitData);
    dialogVisible.value = false;
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const handleClose = () => {
  formRef.value?.resetFields();
  emit('update:visible', false);
};

// 禁用今天之前的日期
const disabledDate = (time: Date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return time.getTime() < today.getTime() - 24 * 60 * 60 * 1000; // 包含今天
};

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val;
  if (val) {
    dateRange.value = [props.formData.startDate, props.formData.endDate];
  }
});

// 监听formData变化
const formData = reactive({ ...props.formData });
watch(() => props.formData, (val) => {
  Object.assign(formData, val);
  dateRange.value = [val.startDate, val.endDate];
}, { deep: true });
</script>

<style scoped>
.time-range-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.time-range-item :deep(.el-time-picker) {
  flex: 1;
  margin-right: 8px;
}

.el-form-item-msg {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  margin-top: 4px;
}
</style>
