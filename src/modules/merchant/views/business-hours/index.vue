<!--
 * @author: <PERSON>rae <PERSON>
 * @date: 2023-06-15
 * @version: 1.0
 * @description: 商家经营时段管理页面，用于查看、添加、编辑和删除营业时间
-->
<template>
  <div class="business-hours-page">
    <div class="page-header">
      <!-- <h2>营业时间管理</h2> -->
    </div>
    
    <div class="page-content">
      <!-- 操作选项卡 -->
      <el-tabs v-model="activeTab" class="business-hours-tabs">
        <el-tab-pane label="常规营业时间" name="regular">
          <!-- 营业状态卡片 -->
          <OperationStatusCard 
            :is-open="operationStatus === 1" 
            :next-change="nextChangeInfo" 
            :today-hours="todayBusinessHours" 
            :editable="true"
            @add-hours="showAddHoursForm"
            @batch-set="showBatchSetForm"
            @status-change="handleEditStatus"
          />
          
          <!-- 营业时间表格 -->
          <el-card class="business-hours-card">
            <template #header>
              <div class="card-header">
                <span>每周营业时间</span>
                <div class="header-actions">
                  <el-button type="primary" @click="showAddHoursForm">
                    <el-icon><Plus /></el-icon>
                    添加营业时间段
                  </el-button>
                </div>
              </div>
            </template>
            
            <BusinessHoursTable 
              :data="businessHours" 
              :editable="true"
              @edit="handleEditHours"
              @delete="handleDeleteHours"
            />
          </el-card>
        </el-tab-pane>

        <el-tab-pane label="营业统计" name="stats">
          <StatsCard :stats="statisticsData" />
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <!-- 添加/编辑营业时间表单 -->
    <BusinessHoursForm 
      v-model:visible="hoursFormVisible" 
      :form-data="currentFormData"
      @submit="handleHoursSubmit"
    />
    
    <!-- 添加/编辑特殊营业时间表单 -->
    <SpecialHoursForm 
      v-model:visible="specialHoursFormVisible" 
      :form-data="currentSpecialFormData"
      @submit="handleSpecialHoursSubmit"
    />
    
  </div>
</template>

<script setup lang="ts">
/**
 * 商家经营时段管理
 * @module merchant/views/business-hours/index
 * @description 商家经营时段管理页面，包含常规营业时间、特殊营业时间设置和营业统计功能
 */

import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
//import type { FormInstance, FormRules } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useMerchantStore } from '@/modules/merchant/stores/merchantStore'
import { 
  getBusinessHours, 
  addBusinessHours, 
  //batchSetBusinessHours, 
  updateBusinessHours, 
  deleteBusinessHours, 
  setSpecialBusinessHours, 
  getOperationStats 
} from '@/modules/merchant/api/business-hours'
import type { SpecialBusinessHour } from '@/modules/merchant/api/business-hours'
import OperationStatusCard from './components/OperationStatusCard.vue'
import BusinessHoursTable from './components/BusinessHoursTable.vue'
import BusinessHoursForm from './components/BusinessHoursForm.vue'
import SpecialHoursForm from './components/SpecialHoursForm.vue'
import StatsCard from './components/StatsCard.vue'

/**
 * 营业时间API返回的数据类型
 */
interface BusinessHoursApiData {
  /** 常规营业时间列表 */
  business_hours: Array<{
    key: string;
    weekday: number;
    startTime: string;
    endTime: string;
  }>;
  
  /** 特殊营业时间列表 */
  special_hours: Array<{
    id: number;
    start_date: string;
    business_hours: Array<{
      startTime: string;
      endTime: string;
    }>;
    remarks?: string;
  }>;
  
  /** 营业状态：0-休息中，1-营业中 */
  operation_status: number;
  
  /** 自动开关店设置 */
  auto_schedule: boolean | { enabled: boolean };
  
  /** 状态相关数据 */
  status_data?: {
    auto_restore: boolean;
    last_updated: string;
    remarks: string;
    restore_time: string;
  };
}

/**
 * 时间范围类型
 */
interface TimeRange {
  /** 时间范围，格式为 [开始时间, 结束时间] */
  range: [string, string];
}

/**
 * 营业时间项
 */
interface BusinessHour {
  /** 唯一标识 */
  //id: number;
  /** 时间段的唯一键，格式："{weekday}:{startTime}:{endTime}" */
  key?: string;
  /** 星期几，0-6 分别表示周日到周六 */
  weekday: number;
  /** 开始时间，格式：HH:mm */
  startTime: string;
  /** 结束时间，格式：HH:mm */
  endTime: string;
  /** 营业状态 */
  status: 'normal' | 'closed' | 'special';
}

/**
 * 下一次状态变更信息
 */
interface NextChangeInfo {
  /** 变更时间 */
  time: string;
  /** 变更日期 */
  date: string;
  /** 变更后的状态：0-休息, 1-营业 */
  status: number;
  /** 剩余时间 */
  remainingTime: string;
  /** 是否是今天 */
  isToday: boolean;
}

/**
 * 特殊营业时间
 */
interface SpecialHour {
  /** 唯一标识 */
  id: number;
  /** 日期，格式：YYYY-MM-DD */
  date: string;
  /** 是否营业 */
  isOpen: boolean;
  /** 营业时间段 */
  timeRanges: TimeRange[];
  /** 说明 */
  description: string;
}

// 营业状态 (0-休息中, 1-营业中)
const operationStatus = ref(1)

// 自动开关店
const autoSchedule = ref(true)

// 营业时间数据
const businessHours = ref<BusinessHour[]>([])

// 加载状态
const loading = ref(false)
const submitting = ref(false)



// 表单可见性控制
const hoursFormVisible = ref(false)
const batchSetVisible = ref(false)

interface BusinessHoursFormData {
  key: string | null;
  weekdays: number[];
  timeRanges: { range: [string, string] }[];
  repeatWeekly: boolean;
}

// 当前编辑的表单数据
const currentFormData = ref<BusinessHoursFormData>({
  key: null,
  weekdays: [],
  timeRanges: [{ range: ['09:00', '18:00'] }],
  repeatWeekly: true
})

// 计算今日营业时间
const todayBusinessHours = computed(() => {
  const today = new Date().getDay() // 0-周日, 1-周一, ..., 6-周六
  return businessHours.value
    .filter(item => item.weekday === today)
    .map(item => ({
      startTime: item.startTime,
      endTime: item.endTime
    }))
})

// 计算剩余时间
function calculateRemainingTime(endTime: string, currentTime: string): string {
  const [endHour, endMinute] = endTime.split(':').map(Number);
  const [currentHour, currentMinute] = currentTime.split(':').map(Number);
  
  let minutesRemaining = (endHour * 60 + endMinute) - (currentHour * 60 + currentMinute);
  
  if (minutesRemaining <= 0) {
    return '即将结束';
  }
  
  const hours = Math.floor(minutesRemaining / 60);
  const minutes = minutesRemaining % 60;
  
  if (hours > 0) {
    return `剩余 ${hours}小时${minutes}分钟`;
  } else {
    return `剩余 ${minutes}分钟`;
  }
}

// 计算下一次状态变更信息
const nextChangeInfo = computed<NextChangeInfo>(() => {
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  const currentTime = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
  const today = now.getDay();
  const todayStr = now.toISOString().split('T')[0];
  
  // 获取今天的营业时间
  const todayHours = businessHours.value.filter(h => h.weekday === today);
  
  // 如果当前是营业状态，计算下一次关店时间
  if (operationStatus.value === 1) {
    // 找到当前所在的时间段
    const currentPeriod = todayHours.find(h => h.startTime <= currentTime && h.endTime > currentTime);
    
    if (currentPeriod) {
      return {
        time: currentPeriod.endTime,
        date: todayStr,
        status: 0, // 下一次将关店
        remainingTime: calculateRemainingTime(currentPeriod.endTime, currentTime),
        isToday: true
      };
    }
    
    // 如果没有找到，使用默认时间
    return {
      time: '22:00',
      date: todayStr,
      status: 0,
      remainingTime: calculateRemainingTime('22:00', currentTime),
      isToday: true
    };
  } else {
    // 如果当前是休息状态，计算下一次开店时间
    // 找到今天还没到的时间段
    const nextPeriodToday = todayHours.find(h => h.startTime > currentTime);
    
    if (nextPeriodToday) {
      return {
        time: nextPeriodToday.startTime,
        date: todayStr,
        status: 1, // 下一次将开店
        remainingTime: calculateRemainingTime(nextPeriodToday.startTime, currentTime),
        isToday: true
      };
    }
    
    // 如果今天没有更多时间段，返回明天第一个时间段
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    const tomorrowDay = tomorrow.getDay();
    const tomorrowHours = businessHours.value.filter(h => h.weekday === tomorrowDay);
    const nextOpenTime = tomorrowHours.length > 0 ? tomorrowHours[0].startTime : '09:00';
    
    return {
      time: nextOpenTime,
      date: tomorrowStr,
      status: 1, // 下一次将开店
      remainingTime: calculateRemainingTime(nextOpenTime, '00:00'), // 从午夜开始计算
      isToday: false
    };
  }
})

// 特殊营业时间数据
const specialHours = ref<SpecialHour[]>([])

// 添加/编辑特殊营业时间表单可见性
const specialHoursFormVisible = ref(false)

// 当前编辑的特殊营业时间表单数据
const currentSpecialFormData = ref<SpecialHoursFormData>({
  id: null,
  date: '',
  isOpen: true,
  timeRanges: [{ range: ['09:00', '18:00'] as [string, string] }],
  description: ''
})

interface StatisticsData {
  total_duration: number;          // 总营业时长(小时)
  average_duration: number;        // 平均每次营业时长(小时)
  total_open_times: number;        // 总营业次数
  complete_open_times: number;     // 完整营业次数
  incomplete_open_times: number;   // 不完整营业次数
  average_open_time_per_day: number; // 平均每天营业时长(小时)
  daily_stats: Array<{             // 每日统计数据
    date: string;                  // 日期
    duration: number;              // 当日营业时长(小时)
    open_times: number;            // 当日营业次数
  }>;
  days_count: number;              // 统计天数
  period: string;                  // 统计周期
}

// 统计数据
const statisticsData = ref<StatisticsData>({
  total_duration: 0,
  average_duration: 0,
  total_open_times: 0,
  complete_open_times: 0,
  incomplete_open_times: 0,
  average_open_time_per_day: 0,
  daily_stats: [],
  days_count: 0,
  period: '7d'
})

// 标签页控制
const activeTab = ref('regular')

// 获取营业时间数据
async function fetchBusinessHours() {
  loading.value = true
  try {
    // 调用真实API获取营业时间数据
    // 注意：由于响应拦截器已将data部分提取，直接得到原始数据
    const res = await getBusinessHours() as BusinessHoursApiData;
    console.log('获取营业时间数据', res)
    
    // 处理常规营业时间数据
    // 根据实际返回的数据结构进行适配
    if (res && Array.isArray(res.business_hours)) {
      businessHours.value = res.business_hours.map((item: any) => ({
        //key: 0, // 使用0作为默认id，因为新结构中没有id字段
        weekday: item.weekday,
        startTime: item.startTime,
        endTime: item.endTime,
        status: 'normal' as const,
        key: item.key // 保留key字段，可能在其他地方使用
      }));
    } else {
      // 如果没有数据或格式不对，设置为空数组
      businessHours.value = [];
      console.warn('API返回的营业时间数据格式不正确');
    }
    
    // 处理特殊营业时间数据
    if (res && Array.isArray(res.special_hours)) {
      specialHours.value = res.special_hours.map((item: any) => ({
        id: item.id || 0, // 确保id不为undefined
        date: item.start_date,
        isOpen: Array.isArray(item.business_hours) && item.business_hours.length > 0,
        timeRanges: Array.isArray(item.business_hours) ? item.business_hours.map((hour: any) => ({
          range: [
            hour.startTime || '00:00',
            hour.endTime || '23:59'
          ] as [string, string]
        })) : [],
        description: item.remarks || ''
      }));
    } else {
      specialHours.value = [];
    }
    
    // 更新营业状态
    if (res && typeof res.operation_status === 'number') {
      operationStatus.value = res.operation_status;
      
      // 更新商家 store 中的经营状态
      const merchantStore = useMerchantStore();
      if (merchantStore.merchantInfo) {
        // 使用 store 的 action 更新状态，确保状态管理的一致性
        merchantStore.setMerchantInfo({
          ...merchantStore.merchantInfo,
          operationStatus: res.operation_status,
          operation_status: res.operation_status // 保持向后兼容
        });
      }
      
      // 更新状态相关数据
      if (res.status_data) {
        // 这里可以添加更多状态相关数据的处理
        console.log('状态数据:', res.status_data);
      }
    }
    
    // 更新自动开关店设置
    if (res && typeof res.auto_schedule === 'object' && res.auto_schedule !== null) {
      autoSchedule.value = res.auto_schedule.enabled || false;
      // 可能还需要更新其他相关设置
    } else if (res && typeof res.auto_schedule === 'boolean') {
      // 直接处理boolean类型的auto_schedule
      autoSchedule.value = res.auto_schedule;
    }
    
    // 更新统计数据
    updateStatistics();
  } catch (error) {
    ElMessage.error('获取营业时间数据失败');
    console.error('获取营业时间数据失败:', error);
  } finally {
    loading.value = false;
  }
}

// 显示添加营业时间表单
function showAddHoursForm() {
  currentFormData.value = {
    key: null,
    weekdays: [],
    timeRanges: [{ range: ['09:00', '18:00'] as [string, string] }],
    repeatWeekly: true
  }
  hoursFormVisible.value = true
}

// 显示批量设置表单
function showBatchSetForm() {
  batchSetVisible.value = true
}

// 处理编辑营业时间
function handleEditHours(row: BusinessHour & { timeRanges?: Array<{startTime: string, endTime: string}> }) {
  // 将行数据转换为表单数据格式
  currentFormData.value = {
    key: row.key || null,
    weekdays: [row.weekday],
    timeRanges: (row.timeRanges || [row]).map(time => ({
      range: [time.startTime, time.endTime] as [string, string]
    })),
    repeatWeekly: true
  }
  hoursFormVisible.value = true
}

// 处理删除营业时间
function handleDeleteHours(row: BusinessHour) {
  console.log(row)
  ElMessageBox.confirm(
    '确定要删除该营业时间段吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    submitting.value = true;
    try {
      // 生成或使用现有的key
      const key = row.key || `${row.weekday}:${row.startTime}:${row.endTime}`;
      
      // 调用真实API删除营业时间
      await deleteBusinessHours({
        keys: [key]
      });
      
      // 刷新数据
      await fetchBusinessHours();
      
      ElMessage.success('删除成功');
    } catch (error) {
      ElMessage.error('删除失败，请重试');
      console.error('删除营业时间失败:', error);
    } finally {
      submitting.value = false;
    }
  }).catch(() => {
    // 取消删除
  });
}

/**
 * 处理营业状态变更
 * @param data 状态数据
 * @param data.status 新的营业状态
 */
async function handleEditStatus(data: { status: number }) {
  // 更新本地状态
  operationStatus.value = data.status;
  console.log('营业状态已更新:', data.status === 1 ? '营业中' : '休息中');
  
  submitting.value = true;
  try {
    // 同步最新的营业时间数据
    await fetchBusinessHours();
    // 更新统计数据
    updateStatistics();
  } catch (error) {
    console.error('获取最新营业时间失败:', error);
  } finally {
    submitting.value = false;
  }
}

// 处理营业时间表单提交
async function handleHoursSubmit(data: any) {
  submitting.value = true
  try {
    // 转换数据格式为API所需的格式
    const apiBusinessHours = data.weekdays.flatMap((weekday: number) => {
      return data.timeRanges.map((time: { range: [string, string] }) => ({
        id: data.id || undefined, // 如果是新增，不传id
        weekday,
        startTime: time.range[0],
        endTime: time.range[1]
      }))
    })
    
    // 调用真实API保存营业时间
    if (data.id) {
      // 更新现有营业时间
      await updateBusinessHours(apiBusinessHours)
    } else {
      // 添加新的营业时间
      await addBusinessHours(apiBusinessHours)
    }
    
    // 重新获取最新数据
    await fetchBusinessHours()
    
    ElMessage.success(data.id ? '编辑成功' : '添加成功')
    hoursFormVisible.value = false
  } catch (error) {
    ElMessage.error(data.id ? '编辑失败，请重试' : '添加失败，请重试')
    console.error('提交营业时间失败:', error)
  } finally {
    submitting.value = false
  }
}


// 更新统计数据
async function updateStatistics() {
  try {
    // 调用真实API获取营业统计信息
    const res:any = await getOperationStats('7d');
    
    console.log('获取营业统计信息', res);
    
    if (res) {
      // 直接使用后端返回的数据结构
      statisticsData.value = {
        ...res,
        // 确保所有必要的字段都有值
        daily_stats: res.daily_stats || [],
        days_count: res.days_count || 0,
        period: res.period || '7d'
      };
    }
  } catch (error) {
    console.error('获取统计数据失败', error);
    // 使用空数据避免UI错误
    statisticsData.value = {
      total_duration: 0,
      average_duration: 0,
      total_open_times: 0,
      complete_open_times: 0,
      incomplete_open_times: 0,
      average_open_time_per_day: 0,
      daily_stats: [],
      days_count: 0,
      period: '7d'
    };
  }
}

// 处理特殊营业时间表单提交
interface SpecialHoursFormData {
  id: number | null;
  date: string;
  isOpen: boolean;
  timeRanges: TimeRange[];
  description: string;
}

async function handleSpecialHoursSubmit(data: SpecialHoursFormData) {
  submitting.value = true
  try {
    // 构建特殊营业时间数据
    const specialHoursData: Omit<SpecialBusinessHour, 'id'> = {
      start_date: data.date,
      end_date: data.date, // 如果要设置范围，这里可以改成结束日期
      business_hours: data.isOpen ? data.timeRanges.map(tr => ({
        startTime: tr.range[0],
        endTime: tr.range[1]
      })) : [],
      priority: 'high', // 默认使用高优先级
      remarks: data.description
    }
    
    // 调用真实API保存特殊营业时间
    await setSpecialBusinessHours(specialHoursData)
    
    // 重新获取最新数据
    await fetchBusinessHours()
    
    ElMessage.success(data.id ? '编辑成功' : '添加成功')
    specialHoursFormVisible.value = false
  } catch (error) {
    ElMessage.error(data.id ? '编辑失败，请重试' : '添加失败，请重试')
    console.error('提交特殊营业时间失败:', error)
  } finally {
    submitting.value = false
  }
}


// 初始化
onMounted(() => {
  fetchBusinessHours()
})
</script>

<style scoped>
.business-hours-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.business-hours-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.time-settings {
  margin-top: 10px;
  padding-left: 20px;
}

.time-period {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>