<!--
 * 商家促销活动列表页面
 * 提供促销活动的列表展示、搜索、分页和状态管理等功能
-->
<template>
  <div class="promotion-list-container">
    <el-card class="list-card">
      <template #header>
        <div class="card-header">
          <h2>促销活动管理</h2>
          <div>
            <el-dropdown @command="handleAddDropdown" split-button type="primary" @click="goToAdd(PROMOTION_TYPES.PRODUCT_DISCOUNT)">
              <el-icon class="el-icon--left"><Plus /></el-icon>
              新建促销活动
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="PROMOTION_TYPES.PRODUCT_DISCOUNT">商品折扣活动</el-dropdown-item>
                  <el-dropdown-item :command="PROMOTION_TYPES.FULL_REDUCTION">满减优惠活动</el-dropdown-item>
                  <el-dropdown-item :command="PROMOTION_TYPES.COUPON">优惠券发放活动</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="活动名称:">
            <el-input v-model="searchForm.keyword" placeholder="输入关键词搜索" clearable />
          </el-form-item>
          <el-form-item label="活动类型:">
            <el-select v-model="searchForm.type" placeholder="全部类型" clearable>
              <el-option label="商品折扣" :value="PROMOTION_TYPES.PRODUCT_DISCOUNT" />
              <el-option label="满减优惠" :value="PROMOTION_TYPES.FULL_REDUCTION" />
              <el-option label="优惠券发放" :value="PROMOTION_TYPES.COUPON" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态:">
            <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
              <el-option label="待发布" :value="PROMOTION_STATUS.PENDING" />
              <el-option label="进行中" :value="PROMOTION_STATUS.ACTIVE" />
              <el-option label="已结束" :value="PROMOTION_STATUS.ENDED" />
              <el-option label="已取消" :value="PROMOTION_STATUS.CANCELED" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
            <el-button @click="resetSearch" :icon="Refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="promotionList" style="width: 100%" border stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="活动名称" min-width="140" show-overflow-tooltip />
        <el-table-column prop="description" label="活动描述" min-width="180" show-overflow-tooltip />
        <el-table-column label="活动类型" width="120">
          <template #default="scope">
            <el-tag v-if="scope.row.type === PROMOTION_TYPES.PRODUCT_DISCOUNT" type="success">商品折扣</el-tag>
            <el-tag v-else-if="scope.row.type === PROMOTION_TYPES.FULL_REDUCTION" type="warning">满减优惠</el-tag>
            <el-tag v-else-if="scope.row.type === PROMOTION_TYPES.COUPON" type="danger">优惠券发放</el-tag>
            <el-tag v-else>其他类型</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="活动规则" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            <div v-if="scope.row.type === PROMOTION_TYPES.PRODUCT_DISCOUNT">
              {{ getDiscountRule(scope.row) }}
            </div>
            <div v-else-if="scope.row.type === PROMOTION_TYPES.FULL_REDUCTION">
              {{ getFullReductionRule(scope.row) }}
            </div>
            <div v-else-if="scope.row.type === PROMOTION_TYPES.COUPON">
              {{ getCouponRule(scope.row) }}
            </div>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <el-table-column label="使用次数/限制" width="120">
          <template #default="scope">
            <span>{{ scope.row.usage_count || 0 }}/{{ scope.row.max_usage_count || '不限' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="活动期限" width="240">
          <template #default="scope">
            <div>{{ formatDate(scope.row.start_time) }}</div>
            <div>{{ formatDate(scope.row.end_time) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.status === PROMOTION_STATUS.PENDING" type="warning">待发布</el-tag>
            <el-tag v-else-if="scope.row.status === PROMOTION_STATUS.ACTIVE" type="success">进行中</el-tag>
            <el-tag v-else-if="scope.row.status === PROMOTION_STATUS.ENDED" type="info">已结束</el-tag>
            <el-tag v-else-if="scope.row.status === PROMOTION_STATUS.CANCELED" type="danger">已取消</el-tag>
            <el-tag v-else-if="isExpired(scope.row)" type="info">已过期</el-tag>
            <el-tag v-else-if="isFuture(scope.row)" type="warning">未开始</el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="230" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-button size="small" @click="goToEdit(scope.row.id)" type="primary" :icon="Edit">编辑</el-button>
              <el-button 
                v-if="scope.row.status === PROMOTION_STATUS.PENDING" 
                size="small" 
                type="success" 
                @click="handlePublish(scope.row)" 
                :icon="Upload"
              >发布</el-button>
              <el-button 
                v-if="scope.row.status === PROMOTION_STATUS.ACTIVE" 
                size="small" 
                type="warning" 
                @click="handleCancel(scope.row)" 
                :icon="Close"
              >取消</el-button>
              <el-button size="small" type="danger" @click="handleDelete(scope.row)" :icon="Delete">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/**
 * 商家促销活动列表页面
 * 提供促销活动的列表展示、搜索、分页和状态管理等功能
 */
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Search, Refresh, Edit, Delete, Close, Plus, Upload } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDateTime } from '@/utils/dateUtils'
import { 
  getPromotionList, 
  disablePromotion, 
  deletePromotion,
  publishPromotion
} from '../api/promotion'
import { 
  PROMOTION_TYPES, 
  PROMOTION_STATUS, 
  DISCOUNT_TYPES, 
  COUPON_TYPES 
} from '../constants/promotion'

const router = useRouter()
const loading = ref(false)
const promotionList = ref<any[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索表单
const searchForm = ref({
  keyword: '',
  type: null as number | null,
  status: null as number | null,
})

/**
 * 获取促销活动数据
 */
const fetchPromotions = async () => {
  loading.value = true
  try {
    const data: any = await getPromotionList({
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchForm.value.keyword,
      type: searchForm.value.type || undefined,
      status: searchForm.value.status || undefined
    })
    // 响应拦截器已处理成功响应，直接使用返回的data
    promotionList.value = data.list || []
    total.value = data.total || 0
  } catch (error: any) {
    // 响应拦截器已处理错误响应并显示错误消息
    console.error('获取促销活动列表失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 日期格式化
 */
const formatDate = (date: string) => {
  return date ? formatDateTime(new Date(date)) : '--'
}

/**
 * 检查促销活动是否过期
 */
const isExpired = (promotion: any) => {
  return new Date(promotion.end_time) < new Date()
}

/**
 * 检查促销活动是否未开始
 */
const isFuture = (promotion: any) => {
  return new Date(promotion.start_time) > new Date()
}

/**
 * 获取折扣规则展示文本
 */
const getDiscountRule = (promotion: any) => {
  try {
    const rules = typeof promotion.rules === 'string' ? JSON.parse(promotion.rules) : promotion.rules
    if (!rules) return '--'
    
    if (rules.discount_type === DISCOUNT_TYPES.PERCENTAGE) {
      return `享${rules.discount_value}折优惠`
    } else if (rules.discount_type === DISCOUNT_TYPES.FIXED_AMOUNT) {
      return `直降${rules.discount_value}元`
    }
    return '--'
  } catch (e) {
    return '--'
  }
}

/**
 * 获取满减规则展示文本
 */
const getFullReductionRule = (promotion: any) => {
  try {
    const rules = typeof promotion.rules === 'string' ? JSON.parse(promotion.rules) : promotion.rules
    if (!rules || !rules.conditions || !rules.conditions.length) return '--'
    
    const conditionTexts = rules.conditions.map((c: any) => `满${c.threshold}减${c.discount}`)
    return conditionTexts.join('，')
  } catch (e) {
    return '--'
  }
}

/**
 * 获取优惠券规则展示文本
 */
const getCouponRule = (promotion: any) => {
  try {
    const rules = typeof promotion.rules === 'string' ? JSON.parse(promotion.rules) : promotion.rules
    if (!rules || !rules.coupon) return '--'
    
    const coupon = rules.coupon
    if (coupon.type === COUPON_TYPES.AMOUNT) {
      return `满${coupon.min_order_amount}元减${coupon.amount}元`
    } else if (coupon.type === COUPON_TYPES.DISCOUNT) {
      return `满${coupon.min_order_amount}元享${coupon.amount}折`
    }
    return '--'
  } catch (e) {
    return '--'
  }
}

/**
 * 新建促销活动处理
 */
const handleAddDropdown = (command: number) => {
  goToAdd(command)
}

/**
 * 跳转到添加页面
 */
const goToAdd = (type: number) => {
  router.push(`/merchant/promotion/add?type=${type}`)
}

/**
 * 跳转到编辑页面
 */
const goToEdit = (id: number) => {
  router.push(`/merchant/promotion/edit/${id}`)
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  currentPage.value = 1
  fetchPromotions()
}

/**
 * 重置搜索
 */
const resetSearch = () => {
  searchForm.value = {
    keyword: '',
    type: null,
    status: null,
  }
  currentPage.value = 1
  fetchPromotions()
}

/**
 * 页码变化处理
 */
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchPromotions()
}

/**
 * 当前页变化处理
 */
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchPromotions()
}

/**
 * 处理取消促销活动
 */
const handleCancel = async (promotion: any) => {
  try {
    await disablePromotion(promotion.id)
    // 响应拦截器已处理成功响应
    ElMessage.success('促销活动已取消')
    fetchPromotions()
  } catch (error: any) {
    // 响应拦截器已处理错误响应并显示错误消息
    console.error('取消促销活动失败:', error)
  }
}

/**
 * 处理发布促销活动
 */
const handlePublish = async (promotion: any) => {
  try {
    await publishPromotion(promotion.id)
    // 响应拦截器已处理成功响应
    ElMessage.success('促销活动已发布')
    fetchPromotions()
  } catch (error: any) {
    // 响应拦截器已处理错误响应并显示错误消息
    console.error('发布促销活动失败:', error)
    ElMessage.error(`发布促销活动失败: ${error || '未知错误'}`)
  }
}

/**
 * 处理删除促销活动
 */
const handleDelete = (promotion: any) => {
  ElMessageBox.confirm(
    `确定要删除促销活动 "${promotion.name}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确认删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await deletePromotion(promotion.id)
      // 响应拦截器已处理成功响应
      ElMessage.success('促销活动已删除')
      fetchPromotions()
    } catch (error: any) {
      // 响应拦截器已处理错误响应并显示错误消息
      console.error('删除促销活动失败:', error)
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// u9875u9762u521du59cbu5316u65f6u83b7u53d6u6570u636e
onMounted(() => {
  fetchPromotions()
})
</script>

<style scoped>
.promotion-list-container {
  padding: 20px;
}

.list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
