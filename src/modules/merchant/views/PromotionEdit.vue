<!--
 * u5546u5bb6u4fc3u9500u6d3bu52a8u7f16u8f91u9875u9762
 * u63d0u4f9bu4fc3u9500u6d3bu52a8u7684u65b0u589eu548cu7f16u8f91u529fu80fd
-->
<template>
  <div class="promotion-edit-container">
    <el-card class="edit-card">
      <template #header>
        <div class="card-header">
          <h2>{{ isEdit ? '编辑促销活动' : '新建促销活动' }}</h2>
          <div>
            <el-button @click="goBack" :icon="Back">返回列表</el-button>
          </div>
        </div>
      </template>
      
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="rules" 
        label-width="100px" 
        label-position="right"
        v-loading="loading"
      >
        <!-- 基本信息部分 -->
        <el-divider content-position="left">活动基本信息</el-divider>
        
        <el-form-item label="活动名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入活动名称" />
        </el-form-item>

        <el-form-item label="活动描述" prop="description">
          <el-input v-model="formData.description" type="textarea" rows="3" placeholder="请输入活动描述" />
        </el-form-item>

        <el-form-item label="活动类型" prop="type">
          <el-select 
            v-model="formData.type" 
            placeholder="请选择活动类型"
            :disabled="isEdit"
          >
            <el-option label="商品折扣活动" :value="PROMOTION_TYPES.PRODUCT_DISCOUNT" />
            <el-option label="满减优惠活动" :value="PROMOTION_TYPES.FULL_REDUCTION" />
            <el-option label="优惠券发放活动" :value="PROMOTION_TYPES.COUPON" />
          </el-select>
        </el-form-item>

        <el-form-item label="活动时间">
          <el-col :span="11">
            <el-form-item prop="start_time">
              <el-date-picker 
                v-model="formData.start_time" 
                type="datetime" 
                placeholder="开始时间"
                value-format="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="text-center">
            <span class="text-gray-500">至</span>
          </el-col>
          <el-col :span="11">
            <el-form-item prop="end_time">
              <el-date-picker 
                v-model="formData.end_time" 
                type="datetime" 
                placeholder="结束时间"
                value-format="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-form-item>

        <el-form-item label="使用限制" prop="max_usage_count">
          <el-input-number 
            v-model="formData.max_usage_count" 
            :min="0" 
            :precision="0"
            placeholder="最大使用次数，0表示不限制"
            style="width: 220px;"
          />
          <div class="el-form-item-tip">最大使用次数，0表示不限制</div>
        </el-form-item>

        <!-- 根据活动类型切换不同的规则表单 -->
        <div v-if="formData.type === PROMOTION_TYPES.PRODUCT_DISCOUNT">
          <product-discount-form v-model:form-data="formData" />
        </div>
        <div v-else-if="formData.type === PROMOTION_TYPES.FULL_REDUCTION">
          <full-reduction-form v-model:form-data="formData" />
        </div>
        <div v-else-if="formData.type === PROMOTION_TYPES.COUPON">
          <coupon-form v-model:form-data="formData" />
        </div>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">保存</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="success" @click="previewPromotion" :disabled="!formValid">预览</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 预览弹窗 -->
    <el-dialog v-model="previewVisible" title="促销活动预览" width="50%">
      <div class="preview-content">
        <div class="preview-header">
          <div class="preview-title">{{ formData.name }}</div>
          <div class="preview-desc">{{ formData.description }}</div>
          <div class="preview-time">有效期：{{ formatDateTime(formData.start_time) }} 至 {{ formatDateTime(formData.end_time) }}</div>
        </div>

        <div class="preview-body">
          <div v-if="formData.type === PROMOTION_TYPES.PRODUCT_DISCOUNT" class="preview-section">
            <h4>折扣详情：</h4>
            <div class="preview-rules">
              <div v-if="getRules().discount_type === DISCOUNT_TYPES.PERCENTAGE">
                商品享受 {{ getRules().discount_value }} 折优惠
              </div>
              <div v-else-if="getRules().discount_type === DISCOUNT_TYPES.FIXED_AMOUNT">
                商品直降 {{ getRules().discount_value }} 元
              </div>
            </div>
          </div>

          <div v-if="formData.type === PROMOTION_TYPES.FULL_REDUCTION" class="preview-section">
            <h4>满减规则：</h4>
            <div class="preview-rules">
              <div v-for="(condition, index) in getRules().conditions" :key="index">
                满 {{ condition.threshold }} 元减 {{ condition.discount }} 元
              </div>
            </div>
          </div>

          <div v-if="formData.type === PROMOTION_TYPES.COUPON" class="preview-section">
            <h4>优惠券详情：</h4>
            <div class="preview-rules">
              <div>优惠券名称：{{ getRules().coupon?.name }}</div>
              <div>优惠类型：{{ getCouponTypeText(getRules().coupon?.type) }}</div>
              <div>优惠金额：{{ getRules().coupon?.amount }} {{ getRules().coupon?.type === COUPON_TYPES.DISCOUNT ? '折' : '元' }}</div>
              <div>最低消费：{{ getRules().coupon?.min_order_amount }} 元</div>
              <div>每人限领：{{ getRules().coupon?.per_user_limit }} 张</div>
              <div>有效期：{{ getRules().coupon?.valid_days }} 天</div>
            </div>
          </div>

          <div class="preview-section">
            <h4>适用商品：</h4>
            <div>
              {{ formData.food_ids && formData.food_ids.length > 0 ? '指定' + formData.food_ids.length + '个商品' : '全部商品' }}
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="previewVisible = false">关闭</el-button>
          <el-button type="primary" @click="submitForm">确认发布</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 商家促销活动编辑页面
 * 提供促销活动的新增和编辑功能
 */
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/dateUtils'
import { getPromotionDetail, createPromotion, updatePromotion } from '../api/promotion'
import { PROMOTION_TYPES, PROMOTION_STATUS, DISCOUNT_TYPES, COUPON_TYPES } from '../constants/promotion'

// 导入各类促销活动表单组件
import ProductDiscountForm from '../components/promotion/ProductDiscountForm.vue'
import FullReductionForm from '../components/promotion/FullReductionForm.vue'
import CouponForm from '../components/promotion/CouponForm.vue'

const router = useRouter()
const route = useRoute()
const formRef = ref(null)
const loading = ref(false)
const submitting = ref(false)
const previewVisible = ref(false)
const formValid = ref(false)

// 判断是否为编辑模式
const isEdit = computed(() => !!route.params.id)

// 定义ruleData类型
interface RuleData {
  discount_type: number;  // 必填项
  discount_value: number;
  conditions: Array<{threshold: number; discount: number}>;
  coupon?: {
    name: string;
    type: number;
    amount: number;
    min_order_amount: number;
    per_user_limit: number;
    valid_days: number;
  };
}

// 表单数据
const formData = reactive({
  id: undefined as number | undefined,
  name: '',
  description: '',
  type: Number(route.query.type) || PROMOTION_TYPES.PRODUCT_DISCOUNT,
  start_time: '',
  end_time: '',
  max_usage_count: 0,
  status: PROMOTION_STATUS.PENDING,
  rules: '',
  food_ids: [] as number[],
  ruleData: {
    discount_type: 1,  // 默认值：1-固定金额折扣
    discount_value: 0,  // 默认折扣值
    conditions: []      // 默认空数组
  } as RuleData // 用于存储不同类型促销活动的规则数据
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入活动名称', trigger: 'blur' },
    { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入活动描述', trigger: 'blur' },
    { min: 5, max: 200, message: '长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择活动类型', trigger: 'change' }
  ],
  start_time: [
    { required: true, message: '请选择开始时间', trigger: 'change' },
    {
      validator: (_: any, value: string, callback: Function) => {
        if (value) {
          const selectedTime = new Date(value).getTime()
          const minTime = new Date().getTime() + 5 * 60 * 1000 // 当前时间推后5分钟
          if (selectedTime < minTime) {
            callback(new Error('开始时间不得早于当前时间推后5分钟'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  end_time: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    {
      validator: (_: any, value: string, callback: Function) => {
        if (value && formData.start_time && new Date(value) <= new Date(formData.start_time)) {
          callback(new Error('结束时间必须晚于开始时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

/**
 * 返回列表页
 */
const goBack = () => {
  router.push('/merchant/promotion/list')
}

/**
 * 获取促销活动详情
 */
const fetchPromotionDetail = async () => {
  if (!route.params.id) return
  
  loading.value = true
  try {
    const detail: any = await getPromotionDetail(Number(route.params.id))
    // 响应拦截器已处理成功响应，直接使用返回的data
    formData.id = detail.id
    formData.name = detail.name
    formData.description = detail.description
    formData.type = detail.type
    formData.start_time = detail.start_time
    formData.end_time = detail.end_time
    formData.max_usage_count = detail.max_usage_count
    formData.status = detail.status
    formData.food_ids = detail.food_ids || []
    
    // 解析规则数据
    if (detail.rules) {
      try {
        const rulesObj = typeof detail.rules === 'string' ? JSON.parse(detail.rules) : detail.rules
        formData.ruleData = rulesObj
      } catch (e) {
        console.error('解析规则数据失败:', e)
      }
    }
  } catch (error: any) {
    // 响应拦截器已处理错误响应并显示错误消息
    ElMessage.error(`获取促销活动详情失败: ${error.message || '未知错误'}`)
    console.error('获取促销活动详情失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 准备提交的数据
 */
const prepareSubmitData = () => {
  const data: any = {
    name: formData.name,
    description: formData.description,
    type: formData.type,
    start_time: formData.start_time,
    end_time: formData.end_time,
    max_usage_count: formData.max_usage_count,
    status: formData.status
  }
  
  // 根据活动类型，准备规则数据
  data.rules = JSON.stringify(formData.ruleData)
  
  // 设置适用商品
  if (formData.food_ids && formData.food_ids.length > 0) {
    data.food_ids = formData.food_ids
  }
  
  return data
}

/**
 * 提交表单
 */
const submitForm = async () => {
  if (!formRef.value) return
  
  const form = formRef.value as any
  await form.validate(async (valid: boolean) => {
    if (!valid) {
      ElMessage.warning('请完善表单信息')
      return
    }
    
    submitting.value = true
    try {
      const submitData = prepareSubmitData()
      // let res: any

      if (isEdit.value) {
        // 编辑模式
        await updatePromotion(Number(route.params.id), submitData)
      } else {
        // 新增模式
        await createPromotion(submitData)
      }

      // 响应拦截器已处理成功响应
      ElMessage.success(isEdit.value ? '促销活动更新成功' : '促销活动创建成功')
      previewVisible.value = false
      router.push('/merchant/promotion/list')
    } catch (error: any) {
      // 响应拦截器已处理错误响应并显示错误消息
      ElMessage.error(error.message || '操作失败')
      console.error(isEdit.value ? '更新促销活动失败:' : '创建促销活动失败:', error)
    } finally {
      submitting.value = false
    }
  })
}

/**
 * 重置表单
 */
const resetForm = () => {
  if (!formRef.value) return
  
  const form = formRef.value as any
  form.resetFields()
  
  // 如果是编辑模式，重新获取详情
  if (isEdit.value) {
    fetchPromotionDetail()
  }
}

/**
 * 预览促销活动
 */
const previewPromotion = async () => {
  if (!formRef.value) return
  
  const form = formRef.value as any
  await form.validate(async (valid: boolean) => {
    if (!valid) {
      ElMessage.warning('请完善表单信息')
      return
    }
    
    try {
      // 显示预览弹窗
      previewVisible.value = true
      
      // 注意：如果需要从后端获取预览数据，可以在此处添加相关代码
      // 目前使用客户端预览，无需调用后端API

    } catch (error: any) {
      ElMessage.error(error.message || '预览失败')
    }
  })
}

/**
 * 获取规则数据
 */
const getRules = (): RuleData => {
  return formData.ruleData || { 
    discount_type: undefined,
    discount_value: 0,
    conditions: [],
    coupon: {
      name: '',
      type: COUPON_TYPES.AMOUNT,
      amount: 0,
      min_order_amount: 0,
      per_user_limit: 0,
      valid_days: 0
    }
  }
}

/**
 * 获取优惠券类型文本
 */
const getCouponTypeText = (type: number | undefined) => {
  if (type === undefined) return '未知类型'
  switch (type) {
    case COUPON_TYPES.AMOUNT:
      return '满减券'
    case COUPON_TYPES.DISCOUNT:
      return '折扣券'
    case COUPON_TYPES.EXCHANGE:
      return '兑换券'
    default:
      return '未知类型'
  }
}

// 监听表单数据变化，更新表单验证状态
const updateFormValidStatus = async () => {
  if (!formRef.value) return
  
  try {
    await (formRef.value as any).validate((_: any, valid: boolean) => {
      formValid.value = valid
    })
  } catch (e) {
    formValid.value = false
  }
}

// 页面加载时，如果是编辑模式则获取详情
onMounted(() => {
  if (isEdit.value) {
    fetchPromotionDetail()
  }
  
  // 初始化后延迟执行一次表单验证
  setTimeout(() => {
    updateFormValidStatus()
  }, 500)
})
</script>

<style scoped>
.promotion-edit-container {
  padding: 20px;
}

.edit-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
}

.text-center {
  text-align: center;
}

.el-form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 预览样式 */
.preview-content {
  padding: 20px;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.preview-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.preview-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
}

.preview-desc {
  color: #606266;
  margin-bottom: 8px;
}

.preview-time {
  color: #909399;
  font-size: 13px;
}

.preview-body {
  padding: 15px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.preview-section {
  margin-bottom: 20px;
}

.preview-section h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #303133;
}

.preview-rules {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.preview-rules div {
  margin-bottom: 5px;
}
</style>
