<!-- 
 * 商家外卖商品编辑页面
 * 包含商品的新增和编辑功能
 * 支持设置商品基本信息、规格和图片
 -->
 <template>
  <div class="food-edit-container">
    <el-card class="header-card">
      <div class="page-header">
        <h2>{{ foodId ? '编辑外卖商品' : '新增外卖商品' }}</h2>
        <div class="header-actions">
          <el-button @click="goBack">返回</el-button>
          
          <!-- 草稿状态下的按钮 -->
          <template v-if="foodData && foodData.status === FoodStatus.DRAFT">
            <el-button type="primary" @click="saveAsDraft" :loading="saving">保存草稿</el-button>
            <el-button type="success" @click="submitFood" :loading="submitting" v-if="foodData.audit_status !== AuditStatus.PENDING">提交审核</el-button>
          </template>
          
          <!-- 已审核通过商品的上下架按钮 -->
          <template v-if="foodData && foodData.audit_status === AuditStatus.APPROVED">
            <el-button 
              v-if="foodData.status === FoodStatus.OFF_SALE" 
              type="success" 
              @click="publishFood" 
              :loading="publishing"
            >上架</el-button>
            <el-button 
              v-if="foodData.status === FoodStatus.ON_SALE" 
              type="warning" 
              @click="offlineFood" 
              :loading="offlining"
            >下架</el-button>
          </template>
          
          <!-- 新建商品时的按钮 -->
          <template v-if="!foodId">
            <el-button type="primary" @click="saveAsDraft" :loading="saving">保存草稿</el-button>
            <el-button type="success" @click="submitFood" :loading="submitting">提交审核</el-button>
          </template>
        </div>
      </div>
    </el-card>

    <el-steps :active="activeStep" finish-status="success" simple style="margin: 20px 0">
      <el-step title="基本信息" icon="Edit" />
      <el-step title="规格设置" icon="List" />
      <el-step title="提交审核" icon="Finished" />
    </el-steps>

    <!-- 第一步：基本信息 -->
    <el-card v-show="activeStep === 0" class="form-card">
      <template #header>
        <div class="card-header">
          <h3>商品基本信息</h3>
        </div>
      </template>
      
      <el-form ref="basicFormRef" :model="basicForm" :rules="basicRules" label-position="top">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="商品主图" prop="image">
              <div class="image-uploader-container">
                <!-- 图片预览区域 -->
                <div class="image-preview" v-if="basicForm.image">
                  <el-image
                    :src="basicForm.image"
                    fit="cover"
                    style="width: 200px; height: 200px"
                    :preview-src-list="[basicForm.image]"
                  />
                  <div class="image-actions">
                    <el-button type="danger" @click="removeImage" size="small">
                      <el-icon><Delete /></el-icon> 删除图片
                    </el-button>
                  </div>
                </div>

                <!-- 文件上传 -->
                <div v-if="!basicForm.image">
                  <FileUploader
                    action="/v1/merchant/upload"
                    :file-limit="1"
                    :size-limit="2 * 1024 * 1024"
                    accept=".jpg,.jpeg,.png,.gif"
                    file-usage="takeout_food"
                    @success="handleUploadSuccess"
                    upload-style="default"
                  >
                    <template #tip>
                      <p>点击或拖拽图片到此处上传</p>
                      <p>仅支持JPG、PNG格式，文件大小不超过2MB</p>
                    </template>
                  </FileUploader>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品名称" prop="name">
              <el-input v-model="basicForm.name" placeholder="请输入商品名称" maxlength="50" show-word-limit></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单分类" prop="category_id">
              <el-select v-model="basicForm.category_id" placeholder="请选择菜单分类" class="full-width">
                <el-option 
                  v-for="category in categories" 
                  :key="category.id" 
                  :label="category.name" 
                  :value="category.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="全局分类" prop="global_category_id">
              <el-cascader
                v-model="basicForm.global_category_id"
                :options="globalCategories"
                :props="{
                  expandTrigger: 'hover',
                  checkStrictly: false,
                  label: 'name',
                  value: 'id',
                  children: 'children'
                }"
                clearable
                filterable
                placeholder="请选择全局分类"
                class="full-width"
                style="width: 100%"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="商品简介" prop="brief">
          <el-input 
            v-model="basicForm.brief" 
            type="textarea" 
            :rows="2" 
            placeholder="请输入商品简介"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item label="商品描述" prop="description">
          <el-input 
            v-model="basicForm.description" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入商品详细描述"
            maxlength="500"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="商品价格(元)" prop="price">
              <el-input-number 
                v-model="basicForm.price" 
                :precision="2" 
                :step="0.1" 
                :min="0" 
                controls-position="right"
                class="full-width"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="原价(元)" prop="original_price">
              <el-input-number 
                v-model="basicForm.original_price" 
                :precision="2" 
                :step="0.1" 
                :min="0" 
                controls-position="right"
                class="full-width"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="包装费(元)" prop="packaging_fee">
              <el-input-number 
                v-model="basicForm.packaging_fee" 
                :precision="2" 
                :step="0.5" 
                :min="0" 
                controls-position="right"
                class="full-width"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="备餐时间(分钟)" prop="preparation_time">
              <el-input-number 
                v-model="basicForm.preparation_time" 
                :min="0" 
                :step="5" 
                controls-position="right"
                class="full-width"
                placeholder="请输入备餐时间"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="每日限量" prop="daily_limit">
              <el-input-number 
                v-model="basicForm.daily_limit" 
                :min="0" 
                :step="1" 
                controls-position="right"
                class="full-width"
                placeholder="0表示不限制"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="商品标签">
              <el-checkbox-group v-model="basicForm.tags">
                <el-checkbox label="热销">热销</el-checkbox>
                <el-checkbox label="新品">新品</el-checkbox>
                <el-checkbox label="推荐">推荐</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="其他选项">
              <el-checkbox v-model="basicForm.is_spicy">辣味</el-checkbox>
              <el-checkbox v-model="basicForm.is_recommend">推荐到首页</el-checkbox>
            </el-form-item>
          </el-col> -->
        </el-row>
        
        <el-form-item label="关键词" prop="keywords">
          <el-input 
            v-model="basicForm.keywords" 
            placeholder="请输入关键词，多个关键词用逗号分隔"
          ></el-input>
        </el-form-item>

        <el-form-item label="商品排序" prop="sort_order">
          <el-input-number 
            v-model="basicForm.sort_order" 
            :min="0" 
            :step="1" 
            controls-position="right"
            class="sort-input"
          ></el-input-number>
          <div class="form-tip">数值越小排序越靠前</div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="nextStep">下一步</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 第二步：规格设置 -->
    <el-card v-show="activeStep === 1" class="form-card">
      <template #header>
        <div class="card-header">
          <h3>商品规格设置</h3>
        </div>
      </template>
      
      <div class="spec-setting-container">
        <FoodSpecSettings
          ref="specSettingsRef"
          v-model:enableSpecs="enableSpecs"
          v-model:specGroups="specGroups"
          :foodId="foodId"
          :canEditSpecs="canEditSpecs"
          :basePrice="basicForm.price"
          @specCombinationsChange="handleSpecCombinationsChange"
          @addSpecOption="handleAddSpecOption"
          @removeSpecOption="handleRemoveSpecOption"
          @updateSpecOption="handleUpdateSpecOption"
        />
        
        <el-form-item>
          <el-button type="primary" @click="saveSpecsAndNext">下一步</el-button>
        </el-form-item>
      </div>
    </el-card>

    <!-- 第三步：商品图片 -->
    <!-- <el-card v-show="activeStep === 2" class="form-card">
      <template #header>
        <div class="card-header">
          <h3>商品图片</h3>
        </div>
      </template>
      
      <div class="image-upload">
        <FoodImageUpload
          ref="imageUploaderRef"
          v-model:mainImage="mainImage"
          v-model:detailImages="detailImages"
          :uploadingImages="uploadingImages"
          @fileChange="handleFileChange"
        />
        
        <el-form>
          <el-form-item>
            <el-button type="primary" @click="saveImagesAndNext" :loading="uploadingImages">下一步</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card> -->

    <!-- 第四步：提交审核 -->
    <el-card v-show="activeStep === 2" class="form-card">
      <template #header>
        <div class="card-header">
          <h3>确认提交</h3>
        </div>
      </template>
      
      <div class="submit-review">
        <el-result icon="success" title="商品信息已保存">
          <template #extra>
            <el-button type="primary" @click="submitFoodForReview" :loading="submitting">提交审核</el-button>
          </template>
        </el-result>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Delete } from '@element-plus/icons-vue';
import FoodSpecSettings from '../components/FoodSpecSettings.vue';
import { FileUploader } from '@/components/common';
import { TakeoutFoodStatus, TakeoutFoodAuditStatus } from '../types';
import type { TakeoutFood, TakeoutFoodCategory, CategoryItem } from '../types';
import { 
  createFood, 
  updateFood, 
  getFoodCategories, 
  getFoodSpecs, 
  getFood, 
  createFoodVariant, 
  updateFoodVariant, 
  deleteFoodVariant,
  getCategoryTree
} from '../api/takeout';

// 定义分类接口
interface FoodCategory {
  id: number;
  name: string;
  sort_order: number;
  children?: FoodCategory[];
}

/**
 * 初始化分类数据
 * 从API获取外卖分类列表并更新本地状态
 */
const _initCategories = async () => {
  try {
    const res: any = await getFoodCategories();
    console.log('分类列表:', res);
    if (res && res.list) {
      categories.value = res.list.map((item: FoodCategory) => ({
        id: item.id,
        name: item.name,
        sort_order: item.sort_order,
        children: item.children || []
      }));
    }
  } catch (error) {
    console.error('获取分类列表失败:', error);
    ElMessage.error('获取分类列表失败');
  }
};

const _initGlobalCategories = async () => {
  try {
    const res: any = await getCategoryTree();
    console.log('全局分类列表:', res);
    
    // 添加详细结构分析
    console.log('分类数据结构分析:', {
      isArray: Array.isArray(res),
      firstItemKeys: res[0] ? Object.keys(res[0]) : null,
      hasNameProperty: res[0]?.name !== undefined
    });
    
    if (res && Array.isArray(res)) {
      globalCategories.value = res;
      console.log('分类数据已加载，条目数:', res.length);
    } else {
      console.warn('获取的全局分类数据格式非预期:', res);
      globalCategories.value = [];
    }
  } catch (error) {
    console.error('获取全局分类列表失败:', error);
    ElMessage.error('获取全局分类列表失败');
  }
};

// 使用枚举作为常量
const FoodStatus = TakeoutFoodStatus;
const AuditStatus = TakeoutFoodAuditStatus;

// 扩展TakeoutFood接口，添加图片相关属性
interface ExtendedTakeoutFood extends TakeoutFood {
  main_image?: string;
  detail_images?: string[];
  image?: string;
}

// 外卖商品变体类型
interface TakeoutFoodVariant {
  id?: number;
  food_id: number;
  name: string;
  description?: string;
  image?: string;
  price: number;
  original_price?: number;
  stock: number;
  is_default?: boolean;
  sort_order?: number;
  created_at?: string;
  updated_at?: string;
}

// 规格选项接口 - 已由 SpecOptionItem 替代

// 规格组合接口
interface SpecCombination {
  id?: number;
  name: string;
  price: number;
  stock: number;
  original_price: number;
  image?: string;
}

// 商品规格响应类型
interface FoodVariantsResponse {
  list: TakeoutFoodVariant[];
  total: number;
  page: number;
  pageSize: number;
}

// 路由和路由参数
const route = useRoute();
const router = useRouter();
// 从路由参数获取ID，如果在编辑模式下，否则为undefined
const routeId = computed(() => {
  const id = route.params.id;
  return id ? Number(id) : undefined;
});
// 使用ref创建可变的foodId
const foodId = ref(routeId.value);

// 页面状态
const activeStep = ref(0);
const saving = ref(false);
const submitting = ref(false);
const publishing = ref(false); // 上架状态
const offlining = ref(false); // 下架状态

// 当前编辑的食品数据
const foodData = ref<TakeoutFood | null>(null);

const categories = ref<TakeoutFoodCategory[]>([]);
const globalCategories = ref<CategoryItem[]>([]);

// 基本信息表单
const basicFormRef = ref<any>(null);
const basicForm = reactive<{
  name: string;
  category_id: number;
  global_category_id: number[];
  image: string;
  description: string;
  brief: string;
  price: number;
  original_price: number;
  packaging_fee: number;
  preparation_time: number;
  daily_limit: number;
  is_spicy: boolean;
  is_recommend: boolean;
  tags: string[];
  keywords: string; // 在表单中始终使用字符串格式存储，使用逗号分隔多个关键词
  sort_order: number;
  status: TakeoutFoodStatus;
}>({
  name: '',
  category_id: 0,
  global_category_id: [],
  image: '',
  description: '',
  brief: '',
  price: 0,
  original_price: 0,
  packaging_fee: 0,
  preparation_time: 15,
  daily_limit: 0,
  is_spicy: false,
  is_recommend: false,
  tags: [],
  keywords: '',
  sort_order: 10,
  status: TakeoutFoodStatus.DRAFT
});

// 定义规格项接口
interface SpecOptionItem {
  name: string;
  price_adjustment: number;
  stock: number; // 库存数量，-1表示不限制
  is_default: boolean;
  enabled: boolean;
  // 保存原始变体ID，用于后续更新
  variantId?: number;
}

// 定义规格组接口
interface SpecGroup {
  name: string;
  defaultOption: number;
  options: SpecOptionItem[];
}

// 规格设置相关状态
const enableSpecs = ref(false);
const canEditSpecs = ref(true);

// 初始化规格组
const initSpecGroups = () => {
  if (specGroups.value.length === 0) {
    specGroups.value.push({
      name: '规格',
      options: [
        {
          name: '标准',
          price_adjustment: 0,
          stock: -1, // 默认不限制库存
          is_default: true,
          enabled: true
        }
      ],
      defaultOption: 0
    });
  }
};

// 规格组数据
const specGroups = ref<SpecGroup[]>([]);

// 规格组合结果
const specCombinations = ref<SpecCombination[]>([]);

// 图片上传相关状态
const mainImage = ref('');
const detailImages = ref<string[]>([]);

// 表单验证规则
const basicRules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 50, message: '商品名称长度应为2-50个字符', trigger: 'blur' }
  ],
  category_id: [
    { type: 'number', min: 1, message: '请选择有效的商品分类', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入商品价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '商品价格不能小于0', trigger: 'blur' }
  ],
  image: [
    { required: true, message: '请上传商品主图', trigger: 'change' }
  ]
};

// 处理规格组合变化
const handleSpecCombinationsChange = (combinations: any[]) => {
  specCombinations.value = combinations;
};

// 处理添加规格选项
const handleAddSpecOption = async (groupIndex: number, option: any) => {
  if (!foodId.value || !canEditSpecs.value) return;
  
  try {
    const newVariant = {
      name: option.name || '新规格',
      price: basicForm.price + (option.price_adjustment || 0),
      stock: 0, // 默认值
      is_default: false,
      original_price: 0,
      food_id: foodId.value
    };
    
    const response: any = await createFoodVariant(foodId.value, newVariant);
    
    // 更新本地规格选项的ID
    if (response && response.id) {
      const optionIndex = specGroups.value[groupIndex].options.length - 1;
      specGroups.value[groupIndex].options[optionIndex].variantId = response.id;
      
      ElMessage.success('规格选项添加成功');
    }
  } catch (error: any) {
    console.error('添加规格选项失败:', error);
    ElMessage.error(error?.message || '添加规格选项失败');
  }
};

// 处理更新规格选项
const handleUpdateSpecOption = async (groupIndex: number, optionIndex: number, option: any) => {
  if (!foodId.value || !canEditSpecs.value) return;
  
  const variantId = option.variantId;
  if (!variantId) {
    // 如果还没有保存到后端，先创建
    return handleAddSpecOption(groupIndex, option);
  }
  
  // 计算实际价格
  const actualPrice = basicForm.price + (option.price_adjustment || 0);
  
  // 检查是否为默认规格
  const isDefault = specGroups.value[groupIndex].defaultOption === optionIndex;
  
  try {
    await updateFoodVariant(variantId, {
      food_id: foodId.value,
      name: option.name,
      price: actualPrice,
      is_default: isDefault,
      stock: 0,  // 默认值
      original_price: 0
    });
    
    ElMessage.success('规格选项更新成功');
  } catch (error: any) {
    console.error('更新规格选项失败:', error);
    ElMessage.error(error?.message || '更新规格选项失败');
  }
};

// 处理删除规格选项
const handleRemoveSpecOption = async (_groupIndex: number, _optionIndex: number, variantId?: number) => {
  if (!foodId.value || !canEditSpecs.value || !variantId) return;
  
  try {
    await deleteFoodVariant(variantId);
    ElMessage.success('规格选项删除成功');
  } catch (error: any) {
    console.error('删除规格选项失败:', error);
    ElMessage.error(error?.message || '删除规格选项失败');
  }
};

// 处理上传成功事件
const handleUploadSuccess = (response: any) => {
  console.log('上传成功:', response)
  basicForm.image = response.file_url;
  ElMessage.success('图片上传成功');
};

// 移除图片
const removeImage = () => {
  basicForm.image = '';
};

// 加载商品数据
const loadFoodData = async (id: number) => {
  try {
    // 1. 加载基本信息
    const food = await getFood(id) as ExtendedTakeoutFood;
    
    // 保存商品完整数据
    foodData.value = food;
    
    // 填充基本表单数据
    basicForm.name = food.name;
    basicForm.category_id = food.category_id;
    // 如果有全局分类编号，则需要将其转换为数组路径
    if (food.global_category_id) {
      // 如果有全局分类，则通过递归查找其路径
      const findCategoryPath = (categories: CategoryItem[], targetId: number, currentPath: number[] = []): number[] | null => {
        for (const category of categories) {
          // 检查当前分类
          if (category.id && category.id === targetId) {
            return [...currentPath, category.id as number];
          }
          // 检查子分类
          if (category.children && category.children.length > 0) {
            // 使用类型断言解决可能的undefined问题
            const path = findCategoryPath(category.children, targetId, [...currentPath, (category.id || 0) as number]);
            if (path) return path;
          }
        }
        return null;
      };
      
      // 查找分类路径
      const path = findCategoryPath(globalCategories.value, food.global_category_id);
      if (path) {
        basicForm.global_category_id = path;
      } else {
        // 如果找不到路径，可能是数据还没加载或者分类不存在
        basicForm.global_category_id = [food.global_category_id];
      }
    } else {
      basicForm.global_category_id = [];
    }
    basicForm.description = food.description || '';
    basicForm.brief = food.brief || '';
    basicForm.price = food.price;
    basicForm.original_price = food.original_price || 0;
    basicForm.packaging_fee = food.packaging_fee || 0;
    basicForm.preparation_time = food.preparation_time || 15;
    basicForm.daily_limit = food.daily_limit || 0;
    basicForm.is_spicy = food.is_spicy || false;
    basicForm.is_recommend = food.is_recommend || false;
    basicForm.tags = Array.isArray(food.tags) ? food.tags : [];
    // 处理keywords字段，确保存储为字符串
    basicForm.keywords = Array.isArray(food.keywords) ? food.keywords.join(', ') : (food.keywords || '');
    basicForm.sort_order = food.sort_order || 10;
    basicForm.status = food.status || TakeoutFoodStatus.DRAFT;
    basicForm.image = food.image || '';
    
    // 2. 加载规格信息
    loadFoodSpecs(id);
    
    // 3. 加载图片信息
    if (food.main_image) {
      mainImage.value = food.main_image;
    }
    
    if (food.detail_images && food.detail_images.length > 0) {
      detailImages.value = food.detail_images;
    }
    
    ElMessage.success('商品信息加载成功');
  } catch (error: any) {
    console.error('加载商品信息失败:', error);
    ElMessage.error(error?.message || '加载商品信息失败');
  }
};

/**
 * 加载商品规格
 * 从服务器获取商品的规格变体信息并处理成前端需要的格式
 * @param id 商品ID
 */
const loadFoodSpecs = async (id: number) => {
  try {
    const response = await getFoodSpecs(id) as FoodVariantsResponse;
    console.log('商品规格数据:', response);
    
    // 处理规格数据
    if (response?.list?.length > 0) {
      enableSpecs.value = true;
      
      // 直接使用变体列表作为单一规格组的选项
      // 根据返回的数据结构调整处理逻辑
      specGroups.value = [{
        name: '规格',  // 创建一个默认规格组名称
        options: response.list.map((variant: any) => ({
          name: variant.name,
          price_adjustment: variant.price ? variant.price - basicForm.price : 0,
          stock: variant.stock !== undefined ? variant.stock : -1,
          is_default: variant.is_default || false,
          enabled: true,
          // 保存原始变体ID，用于后续更新
          variantId: variant.id
        })),
        defaultOption: response.list.findIndex((variant: any) => variant.is_default) || 0
      }];
      
      // 设置变体数据
      const variants = response.list.map((variant: any) => ({
        id: variant.id,
        name: variant.name,
        price: variant.price || basicForm.price,
        stock: variant.stock,
        original_price: variant.original_price || 0,
        image: variant.image || '',
        is_default: variant.is_default || false
      }));
      
      // 设置默认选中的规格
      const defaultVariant = variants.find((v: any) => v.is_default);
      if (defaultVariant) {
        const defaultIndex = variants.findIndex((v: any) => v.id === defaultVariant.id);
        if (defaultIndex !== -1 && specGroups.value.length > 0) {
          specGroups.value[0].defaultOption = defaultIndex;
        }
      }
    }
  } catch (error: any) {
    console.error('加载商品规格失败:', error);
    ElMessage.error(error?.message || '加载商品规格失败');
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 下一步
const nextStep = async () => {
  if (activeStep.value === 0) {
    // 验证基本信息表单
    try {
      await basicFormRef.value.validate();
      
      // 保存基本信息
      await saveAsDraft();
      
      // 验证通过后进入下一步
      activeStep.value = activeStep.value + 1;
    } catch (error) {
      // 表单验证失败
      return;
    }
  } else {
    // 其他步骤直接跳到下一步
    const nextStep = Math.min(activeStep.value + 1, 2);
    activeStep.value = nextStep;
  }
};

// 保存为草稿
const saveAsDraft = async () => {
  saving.value = true;
  try {
    if (foodId.value) {
      // 更新现有商品
      // 处理keywords字段，确保是字符串数组格式
      const processKeywords = (keywords: string): string[] => {
        if (!keywords) return [];
        return keywords.split(',')
          .map(k => k.trim())
          .filter(Boolean);
      };

      // 使用类型断言来解决类型错误
      // 创建包含所有数据的完整对象
      const updateData: any = {
        name: basicForm.name,
        category_id: basicForm.category_id,
        global_category_id: basicForm.global_category_id.length > 0 ? basicForm.global_category_id[basicForm.global_category_id.length - 1] : null,
        brief: basicForm.brief,
        description: basicForm.description,
        price: basicForm.price,
        original_price: basicForm.original_price,
        packaging_fee: basicForm.packaging_fee,
        preparation_time: basicForm.preparation_time,
        daily_limit: basicForm.daily_limit,
        tags: basicForm.tags,
        is_spicy: basicForm.is_spicy,
        keywords: processKeywords(basicForm.keywords),
        sort_order: basicForm.sort_order,
        image: basicForm.image,
        status: FoodStatus.DRAFT
      };
      
      await updateFood(foodId.value, updateData);
      
      ElMessage.success('商品草稿更新成功');
    } else {
      // 创建新商品
      // 处理keywords字段，确保是字符串数组格式
      const processKeywords = (keywords: string): string[] => {
        if (!keywords) return [];
        return keywords.split(',')
          .map(k => k.trim())
          .filter(Boolean);
      };

      // 使用类型断言来解决类型错误
      // 创建包含所有数据的完整对象
      const createData: any = {
        name: basicForm.name,
        category_id: basicForm.category_id,
        global_category_id: basicForm.global_category_id.length > 0 ? basicForm.global_category_id[basicForm.global_category_id.length - 1] : null,
        brief: basicForm.brief,
        description: basicForm.description,
        price: basicForm.price,
        original_price: basicForm.original_price,
        packaging_fee: basicForm.packaging_fee,
        preparation_time: basicForm.preparation_time,
        daily_limit: basicForm.daily_limit,
        tags: basicForm.tags,
        is_spicy: basicForm.is_spicy,
        keywords: processKeywords(basicForm.keywords),
        sort_order: basicForm.sort_order,
        image: basicForm.image,
        status: FoodStatus.DRAFT
      };
      
      const data = await createFood(createData);
      
      if (data?.id) {
        foodId.value = data.id;
        ElMessage.success('商品草稿保存成功');
      }
    }
  } catch (error: any) {
    console.error('保存商品失败:', error);
    ElMessage.error(error?.message || '保存失败');
  } finally {
    saving.value = false;
  }
};

// 保存规格设置并进入下一步
const saveSpecsAndNext = async () => {
  if (enableSpecs.value) {
    // 验证规格设置是否完整
    const hasEmptyFields = specGroups.value.some(group => 
      group.name.trim() === '' || 
      group.options.some(opt => opt.enabled && opt.name.trim() === '')
    );
    
    if (hasEmptyFields) {
      ElMessage.warning('请填写完整的规格信息');
      return;
    }
    
    // 直接进入下一步，因为所有规格操作已在即时修改时完成
    ElMessage.success('规格设置已即时保存');
    activeStep.value = activeStep.value + 1;
  } else {
    // 未启用规格，直接进入下一步
    activeStep.value = activeStep.value + 1;
  }
};

// 提交商品审核
const submitFood = async () => {
  // 验证所有输入
  await submitFoodForReview();
};

// 初始化数据
const initialize = () => {
  _initCategories();
  _initGlobalCategories();
  // 初始化规格组
  initSpecGroups();
  
  if (foodId.value) {
    loadFoodData(foodId.value);
  }
};

// 页面加载后执行初始化
setTimeout(() => {
  initialize();
}, 0);

/**
 * 提交商品进行平台审核
 */
const submitFoodForReview = async () => {
  if (!foodId.value) {
    ElMessage.warning('请先保存或选择要提交的商品');
    return;
  }
  
  submitting.value = true;
  try {
    // 调用提交审核API - 与FoodList.vue中保持一致的调用方式
    await updateFood(foodId.value, { status: FoodStatus.OFF_SALE });
    
    ElMessage.success('商品提交审核成功');
    // 转到商品列表页
    setTimeout(() => {
      router.push('/merchant/takeout/food/list');
    }, 1500);
  } catch (error: any) {
    console.error('提交审核失败:', error);
    ElMessage.error(error?.message || '提交审核失败');
  } finally {
    submitting.value = false;
  }
};

/**
 * 上架商品
 */
const publishFood = async () => {
  if (!foodId.value) {
    ElMessage.warning('无法上架，商品ID不存在');
    return;
  }
  
  publishing.value = true;
  try {
    await updateFood(foodId.value, { status: FoodStatus.ON_SALE });
    
    // 更新本地数据
    if (foodData.value) {
      foodData.value.status = FoodStatus.ON_SALE;
    }
    
    ElMessage.success('商品已上架');
  } catch (error: any) {
    console.error('上架商品失败:', error);
    ElMessage.error(error?.message || '上架商品失败');
  } finally {
    publishing.value = false;
  }
};

/**
 * 下架商品
 */
const offlineFood = async () => {
  if (!foodId.value) {
    ElMessage.warning('无法下架，商品ID不存在');
    return;
  }
  
  offlining.value = true;
  try {
    await updateFood(foodId.value, { status: FoodStatus.OFF_SALE });
    
    // 更新本地数据
    if (foodData.value) {
      foodData.value.status = FoodStatus.OFF_SALE;
    }
    
    ElMessage.success('商品已下架');
  } catch (error: any) {
    console.error('下架商品失败:', error);
    ElMessage.error(error?.message || '下架商品失败');
  } finally {
    offlining.value = false;
  }
};

// 注：删除了本地模拟的submitFoodForAudit函数，改为使用导入的updateFood API
</script>

<style scoped lang="scss">
.food-edit-container {
  padding: 20px;

.image-uploader-container {
  margin-bottom: 20px;

  .image-preview {
    position: relative;
    width: 200px;
    height: 200px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
    
    .image-actions {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.6);
      opacity: 0.8;
      transition: opacity 0.3s;
    }
    
    &:hover .image-actions {
      opacity: 1;
    }
  }
}

  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
}

.header-card {
  margin-bottom: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.form-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
}

.full-width {
  width: 100%;
}

.sort-input {
  width: 180px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
