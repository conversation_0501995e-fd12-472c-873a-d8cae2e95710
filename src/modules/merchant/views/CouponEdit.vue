<!--
/**
 * 优惠券编辑页面
 * <AUTHOR>
 * @date 2025-01-20
 * @version 1.0.0
 * @description 商家优惠券的创建和编辑功能页面，支持满减券、折扣券、兑换券等类型，
 *              包含商品选择、时间设置、使用规则配置等功能
 */
-->
<template>
  <div class="coupon-edit-container">
    <el-card class="edit-card">
      <template #header>
        <div class="card-header">
          <h2>{{ isEdit ? '编辑优惠券' : '新建优惠券' }}</h2>
          <el-button @click="goBack" :icon="Back">返回列表</el-button>
        </div>
      </template>

      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="rules" 
        label-width="120px" 
        class="coupon-form"
        v-loading="loading"
      >
        <el-divider content-position="left">活动基本信息</el-divider>
        
        <el-form-item label="活动名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入活动名称，如：新客专享优惠券" />
        </el-form-item>
        
        <el-form-item label="活动描述" prop="description">
          <el-input 
            v-model="formData.description" 
            type="textarea" 
            :rows="2" 
            placeholder="请输入活动描述，如：新用户注册即可领取，满100元减15元" 
          />
        </el-form-item>
        
        <el-form-item label="活动类型" prop="activity_type">
          <el-select v-model="formData.activity_type" placeholder="请选择活动类型" disabled>
            <el-option label="优惠券活动" :value="4" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="活动时间" required>
          <el-col :span="11">
            <el-form-item prop="start_time">
              <el-date-picker
                v-model="formData.start_time"
                type="datetime"
                placeholder="开始时间"
                style="width: 100%"
                :disabled="isEdit && isActive"
              />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="text-center">
            <span class="text-gray-500">至</span>
          </el-col>
          <el-col :span="11">
            <el-form-item prop="end_time">
              <el-date-picker
                v-model="formData.end_time"
                type="datetime"
                placeholder="结束时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-form-item>
        
        <el-form-item label="最大发放数量" prop="max_usage_count">
          <el-input-number 
            v-model="formData.max_usage_count" 
            :min="0" 
            :precision="0" 
            :step="100"
            placeholder="0表示不限制"
          />
          <span class="ml-2 text-gray-500">(0表示不限制)</span>
        </el-form-item>
        
        <el-divider content-position="left">优惠券设置</el-divider>
        
        <el-form-item label="优惠券名称" prop="coupon_name">
          <el-input v-model="formData.coupon_name" placeholder="请输入优惠券名称，如：新客满减券" />
        </el-form-item>
        
        <el-form-item label="优惠券类型" prop="type">
          <el-radio-group v-model="formData.type">
            <el-radio :label="COUPON_TYPES.AMOUNT">满减券</el-radio>
            <el-radio :label="COUPON_TYPES.DISCOUNT">折扣券</el-radio>
            <el-radio :label="COUPON_TYPES.EXCHANGE">兑换券</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="优惠金额" prop="amount" v-if="formData.type !== COUPON_TYPES.EXCHANGE">
          <el-input-number 
            v-model="formData.amount" 
            :min="0" 
            :precision="formData.type === COUPON_TYPES.AMOUNT ? 0 : 1" 
            :step="formData.type === COUPON_TYPES.AMOUNT ? 5 : 0.1"
          />
          <span class="ml-2">{{ formData.type === COUPON_TYPES.AMOUNT ? '元' : '折' }}</span>
          <div v-if="formData.type === COUPON_TYPES.DISCOUNT" class="text-gray-500 mt-1">
            注：8.5表示8.5折，即85%的折扣
          </div>
        </el-form-item>
        
        <el-form-item label="使用门槛" prop="min_order_amount">
          <el-input-number 
            v-model="formData.min_order_amount" 
            :min="0" 
            :precision="0" 
            :step="10"
          />
          <span class="ml-2">元</span>
          <div class="text-gray-500 mt-1">
            订单满多少金额后可使用此优惠券，0表示无门槛
          </div>
        </el-form-item>
        
        <el-form-item label="每人限领" prop="per_user_limit">
          <el-input-number 
            v-model="formData.per_user_limit" 
            :min="0" 
            :precision="0" 
            :step="1"
          />
          <span class="ml-2">张</span>
          <span class="ml-2 text-gray-500">(0表示不限制)</span>
        </el-form-item>
        
        <el-form-item label="有效期" prop="valid_days">
          <el-input-number 
            v-model="formData.valid_days" 
            :min="1" 
            :precision="0" 
            :step="1"
          />
          <span class="ml-2">天</span>
          <span class="ml-2 text-gray-500">(自领取日起计算)</span>
        </el-form-item>
        
        <el-form-item label="适用范围" prop="apply_to_all">
          <el-radio-group v-model="formData.apply_to_all">
            <el-radio :label="true">所有商品</el-radio>
            <el-radio :label="false">指定商品</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item v-if="!formData.apply_to_all" label="选择商品" prop="food_ids">
          <div class="selected-foods-display" v-if="selectedFoods.length > 0">
            <div class="selected-foods-header">
              <span>已选择 {{ selectedFoods.length }} 个商品</span>
              <el-button type="primary" size="small" @click="showFoodSelector">重新选择</el-button>
            </div>
            <div class="selected-foods-list">
              <el-tag
                v-for="food in selectedFoods"
                :key="food.id"
                closable
                @close="removeFoodFromSelection(food.id)"
                class="food-tag"
              >
                {{ food.name }} (¥{{ food.price }})
              </el-tag>
            </div>
          </div>
          <el-button v-else type="primary" @click="showFoodSelector">选择商品</el-button>
          <div class="text-gray-500 mt-1">
            请选择适用的商品，不选则默认为所有商品
          </div>
        </el-form-item>
        
        <el-form-item label="领取渠道" prop="channels">
          <el-checkbox-group v-model="formData.channels">
            <el-checkbox label="register">注册自动发放</el-checkbox>
            <el-checkbox label="popup">首页弹窗</el-checkbox>
            <el-checkbox label="center">优惠券中心</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="发布状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="0">保存为草稿</el-radio>
            <el-radio :label="1">立即发布</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">{{ isEdit ? '保存修改' : '创建优惠券' }}</el-button>
          <el-button @click="previewCoupon" :disabled="submitting">预览效果</el-button>
          <el-button @click="resetForm" :disabled="submitting">重置表单</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 预览弹窗 -->
    <el-dialog
      v-model="previewVisible"
      title="优惠券预览"
      width="500px"
    >
      <div class="coupon-preview">
        <div class="preview-header">
          <div class="preview-image">
            <el-icon size="48"><Discount /></el-icon>
          </div>
          <div class="preview-title">
            <h3>{{ formData.name }}</h3>
            <p>{{ formData.description }}</p>
          </div>
        </div>
        
        <div class="preview-info">
          <p>
            <strong>有效期：</strong> 
            {{ formatDateTime(formData.start_time) }} 至 {{ formatDateTime(formData.end_time) }}
          </p>
          
          <div class="preview-details">
            <h4>优惠券详情：</h4>
            <ul>
              <li v-if="formData.min_order_amount > 0">
                满{{ formData.min_order_amount }}元可用
              </li>
              <li v-else>无使用门槛</li>
              <li v-if="formData.type === COUPON_TYPES.AMOUNT">
                优惠金额{{ formData.amount }}元
              </li>
              <li v-else-if="formData.type === COUPON_TYPES.DISCOUNT">
                打{{ formData.amount }}折
              </li>
              <li v-else-if="formData.type === COUPON_TYPES.EXCHANGE">
                商品兑换券
              </li>
              <li>{{ formData.apply_to_all ? '适用于所有商品' : '适用于指定商品' }}</li>
              <li v-if="formData.per_user_limit > 0">
                每人限领{{ formData.per_user_limit }}张
              </li>
              <li v-else>每人不限领取次数</li>
              <li>领取后{{ formData.valid_days }}天内有效</li>
            </ul>
          </div>
          
          <div class="user-view">
            <h4>用户领取展示效果：</h4>
            <div class="coupon-card">
              <div class="coupon-info">
                <div class="coupon-name">{{ formData.coupon_name }}</div>
                <div class="coupon-desc">
                  {{ formData.type === COUPON_TYPES.AMOUNT ? 
                    `满${formData.min_order_amount}减${formData.amount}` : 
                    formData.type === COUPON_TYPES.DISCOUNT ? 
                    `满${formData.min_order_amount}打${formData.amount}折` :
                    '商品兑换券' }}
                </div>
              </div>
              <div class="coupon-action">
                <el-button type="danger" size="small">立即领取</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="previewVisible = false">返回编辑</el-button>
          <el-button type="primary" @click="submitForm">确认发布</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 商品选择器弹窗 -->
    <el-dialog
      v-model="foodSelectorVisible"
      title="选择商品"
      width="80%"
      :before-close="handleFoodSelectorClose"
    >
      <FoodSelector
        :selected-ids="formData.food_ids"
        @confirm="handleFoodSelection"
        @cancel="foodSelectorVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 商家优惠券编辑页面
 * 提供优惠券的创建和编辑功能
 */
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back, Discount } from '@element-plus/icons-vue'
import { getCouponDetail, createCoupon, updateCoupon } from '../api/coupon'
import { getFoodList } from '../api/takeout'
import { formatDateTime } from '../../../utils/dateUtils'
import { COUPON_TYPES } from '../constants/promotion'
import FoodSelector from '../components/FoodSelector.vue'



const route = useRoute()
const router = useRouter()
const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const previewVisible = ref(false)
const foodSelectorVisible = ref(false)
const selectedFoods = ref<any[]>([])

// 判断是否为编辑模式
const isEdit = computed(() => !!route.params.id)

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  activity_type: 4, // 优惠券活动类型
  start_time: new Date(),
  end_time: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 默认30天后
  max_usage_count: 1000,
  status: 0, // 默认为草稿状态（待发布）
  coupon_name: '',
  type: COUPON_TYPES.AMOUNT, // 优惠券类型：1-满减券, 2-折扣券, 3-兑换券
  amount: 15,
  min_order_amount: 100,
  per_user_limit: 1,
  valid_days: 30,
  apply_to_all: true,
  food_ids: [] as number[],
  channels: ['register', 'popup', 'center']
})

// 判断优惠券是否处于活动中
const isActive = computed(() => {
  const now = new Date()
  return now >= formData.start_time && now <= formData.end_time
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入活动名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度应为2-50个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入活动描述', trigger: 'blur' },
    { min: 5, max: 200, message: '长度应为5-200个字符', trigger: 'blur' }
  ],
  activity_type: [
    { required: true, message: '请选择活动类型', trigger: 'change' }
  ],
  start_time: [
    { required: true, message: '请选择开始时间', trigger: 'change' },
    {
      validator: (_: any, value: Date, callback: Function) => {
        if (value) {
          const selectedTime = new Date(value).getTime()
          const minTime = new Date().getTime() + 5 * 60 * 1000 // 当前时间推后5分钟
          if (selectedTime < minTime) {
            callback(new Error('开始时间不得早于当前时间推后5分钟'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  end_time: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  coupon_name: [
    { required: true, message: '请输入优惠券名称', trigger: 'blur' },
    { min: 2, max: 30, message: '长度应为2-30个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择优惠券类型', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入优惠金额', trigger: 'blur' }
  ],
  channels: [
    { type: 'array', required: true, message: '请至少选择一个领取渠道', trigger: 'change' }
  ]
}

/**
 * 显示商品选择器弹窗
 */
const showFoodSelector = () => {
  foodSelectorVisible.value = true
}

/**
 * 处理商品选择确认
 * @param foods 选中的商品列表
 */
const handleFoodSelection = (foods: any[]) => {
  selectedFoods.value = foods
  formData.food_ids = foods.map(food => food.id)
  foodSelectorVisible.value = false
}

/**
 * 从选择中移除指定商品
 * @param foodId 要移除的商品ID
 */
const removeFoodFromSelection = (foodId: number) => {
  selectedFoods.value = selectedFoods.value.filter(food => food.id !== foodId)
  formData.food_ids = formData.food_ids.filter(id => id !== foodId)
}

/**
 * 处理商品选择器弹窗关闭
 */
const handleFoodSelectorClose = () => {
  foodSelectorVisible.value = false
}

/**
 * 获取优惠券详情
 * @param id 优惠券ID
 */
const fetchCouponDetail = async (id: string | number) => {
  loading.value = true
  try {
    const couponData = await getCouponDetail(Number(id))
    // 响应拦截器已处理成功响应，直接使用返回的数据
    // 移除调试日志
    
    // 使用类型断言解决unknown类型问题
    const typedCouponData = couponData as any;
    
    // 设置表单数据 - 直接从后端返回的数据中获取
    formData.name = typedCouponData.name || ''
    formData.description = typedCouponData.description || ''
    formData.activity_type = 4 // 固定为优惠券活动类型
    formData.start_time = typedCouponData.start_time ? new Date(typedCouponData.start_time) : new Date()
    formData.end_time = typedCouponData.end_time ? new Date(typedCouponData.end_time) : new Date()
    formData.max_usage_count = typedCouponData.total_limit || 0
    formData.status = typedCouponData.status
    
    // 设置优惠券相关数据 - 直接从couponData获取
    formData.coupon_name = typedCouponData.name || ''
    formData.type = typedCouponData.type || COUPON_TYPES.AMOUNT
    formData.amount = typedCouponData.amount || 0
    formData.min_order_amount = typedCouponData.min_order_amount || 0
    formData.per_user_limit = typedCouponData.per_user_limit || 1
    formData.valid_days = 30 // 默认30天，后端暂无此字段
    formData.apply_to_all = typedCouponData.apply_to_all || (typedCouponData.apply_to_foods === '' || !typedCouponData.apply_to_foods)
    
    // 设置领取渠道 - 默认值，后端暂无此字段
    formData.channels = ['register', 'popup', 'center']
    
    // 设置商品ID - 从apply_to_foods字段解析
    if (typedCouponData.apply_to_foods && typedCouponData.apply_to_foods !== '') {
      formData.food_ids = typedCouponData.apply_to_foods.split(',').map((id: string) => Number(id.trim())).filter((id: number) => !isNaN(id))
      formData.apply_to_all = false
    } else {
      formData.food_ids = []
      formData.apply_to_all = true
    }
    
    if (formData.food_ids.length > 0 && !formData.apply_to_all) {
      // 获取已选商品信息
      fetchSelectedFoods()
    }

  } catch (error: any) {
    ElMessage.error(error.message || '获取优惠券详情失败')
  } finally {
    loading.value = false
  }
}

/**
 * 获取已选商品的详细信息
 * 用于编辑模式下回显已选商品
 */
const fetchSelectedFoods = async () => {
  if (formData.food_ids.length === 0) return
  
  try {
    // 这里简化处理，实际可能需要批量查询API
    const promises = formData.food_ids.map(id => 
      getFoodList({ page: 1, pageSize: 1, keyword: `id:${Number(id)}` })
    )
    
    const results = await Promise.all(promises)
    const foods: any[] = []
    
    results.forEach(res => {
      // 响应拦截器已处理成功响应，直接使用返回的数据
      // 添加类型断言解决res类型为unknown的问题
      const typedRes = res as any;
      if (typedRes.list && typedRes.list.length > 0) {
        foods.push(typedRes.list[0])
      }
    })
    
    selectedFoods.value = foods
  } catch (error) {
    console.error('获取已选商品失败:', error)
  }
}

/**
 * 预览优惠券效果
 * 验证表单后显示预览弹窗
 */
const previewCoupon = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      previewVisible.value = true
    } else {
      ElMessage.warning('请先完成必填信息')
    }
  })
}

/**
 * 提交表单数据
 * 创建或更新优惠券
 */
const submitForm = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      ElMessage.warning('请完成必填信息')
      return
    }
    
    // 确保结束时间大于开始时间
    if (formData.end_time <= formData.start_time) {
      ElMessage.error('结束时间必须大于开始时间')
      return
    }
    
    submitting.value = true
    
    // 将food_ids数组转换为数字类型
    const numericFoodIds = formData.food_ids.map(id => Number(id))
    
    try {
      // 构建API请求数据
      const requestData = {
        name: formData.name,
        description: formData.description,
        type: formData.type,
        start_time: formatDateTime(formData.start_time),
        end_time: formatDateTime(formData.end_time),
        max_usage_count: formData.max_usage_count,
        per_user_limit: formData.per_user_limit,
        daily_limit: 1000,
        total_limit: formData.max_usage_count,
        amount: formData.amount,
        min_order_amount: formData.min_order_amount,
        max_discount_amount: formData.type === COUPON_TYPES.DISCOUNT ? formData.amount * 100 : 0,
        valid_days: formData.valid_days,
        apply_to_all: formData.apply_to_all,
        apply_to_categories: '',
        apply_to_foods: formData.apply_to_all ? '' : numericFoodIds.join(','),
        exclude_foods: '',
        user_level_limit: 0,
        status: formData.status
      }
      
      if (isEdit.value) {
        // 编辑模式
        await updateCoupon(Number(route.params.id), requestData)
      } else {
        // 创建模式
        await createCoupon(requestData)
      }
      
      // 响应拦截器已处理成功响应
      ElMessage.success(isEdit.value ? '优惠券更新成功' : '优惠券创建成功')
      // 关闭预览弹窗
      previewVisible.value = false
      // 返回列表页
      goBack()
    } catch (error: any) {
      ElMessage.error(error.message || (isEdit.value ? '更新优惠券失败' : '创建优惠券失败'))
    } finally {
      submitting.value = false
    }
  })
}

/**
 * 重置表单数据
 * 编辑模式下重新加载数据，新建模式下恢复默认值
 */
const resetForm = () => {
  if (isEdit.value) {
    // 编辑模式下重新加载数据
    fetchCouponDetail(Number(route.params.id))
  } else {
    // 新建模式下重置为默认值
    formRef.value.resetFields()
  }
}

/**
 * 返回优惠券列表页
 */
const goBack = () => {
  router.push('/merchant/coupon/list')
}

// 页面初始化
onMounted(() => {
  if (isEdit.value) {
    // 编辑模式，获取优惠券详情
    fetchCouponDetail(Number(route.params.id))
  }
})
</script>

<style scoped>
.coupon-edit-container {
  padding: 20px;
}

.edit-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
}

.text-center {
  text-align: center;
}

.coupon-form {
  max-width: 900px;
  margin: 0 auto;
}

.text-gray-500 {
  color: #909399;
  font-size: 13px;
}

.ml-2 {
  margin-left: 8px;
}

.mt-1 {
  margin-top: 4px;
}

.selected-foods-display {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background-color: #f8f9fa;
}

.selected-foods-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
}

.selected-foods-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.food-tag {
  margin: 0;
}

/* 预览样式 */
.coupon-preview {
  padding: 10px;
}

.preview-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #dcdfe6;
}

.preview-image {
  background-color: #f56c6c;
  color: white;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.preview-title h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
}

.preview-title p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.preview-info {
  font-size: 14px;
}

.preview-details h4 {
  margin: 15px 0 10px;
  font-size: 16px;
}

.preview-details ul {
  margin: 10px 0;
  padding-left: 20px;
}

.preview-details li {
  margin-bottom: 8px;
}

.user-view {
  margin-top: 20px;
}

.user-view h4 {
  margin: 15px 0 10px;
  font-size: 16px;
}

.coupon-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff8f8;
  border: 1px solid #ffcdd2;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
}

.coupon-info {
  flex: 1;
}

.coupon-name {
  font-weight: bold;
  color: #f56c6c;
  font-size: 16px;
  margin-bottom: 5px;
}

.coupon-desc {
  color: #606266;
  font-size: 14px;
}

.coupon-action {
  margin-left: 20px;
}
</style>
