<template>
  <div class="notification-demo">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>商家通知管理演示</span>
        </div>
      </template>
      
      <div class="demo-content">
        <el-alert
          title="功能说明"
          type="info"
          :closable="false"
          show-icon
        >
          <p>此页面演示商家聊天模块的通知管理功能。点击下方按钮可以测试不同类型的通知。</p>
          <p>通知会在页面右上角弹出，同时会显示在聊天窗口的通知中心。</p>
        </el-alert>
        
        <div class="demo-buttons">
          <el-button
            type="warning"
            size="large"
            @click="simulateRefundNotification"
            :icon="Money"
          >
            模拟退款申请通知
          </el-button>

          <el-button
            type="success"
            size="large"
            @click="simulateNewOrderNotification"
            :icon="ShoppingBag"
          >
            模拟新订单通知
          </el-button>

          <el-button
            type="info"
            size="large"
            @click="simulateGeneralNotification"
            :icon="Bell"
          >
            模拟一般通知
          </el-button>
          
          <el-button 
            type="success" 
            size="large"
            @click="simulateMultipleNotifications"
            :icon="ChatDotRound"
          >
            模拟多条通知
          </el-button>
        </div>
        
        <div class="demo-info">
          <h4>通知特性：</h4>
          <ul>
            <li><strong>退款通知</strong>：显示8秒，警告类型，红色金钱图标，可点击跳转</li>
            <li><strong>新订单通知</strong>：显示6秒，成功类型，绿色购物袋图标，可点击跳转</li>
            <li><strong>一般通知</strong>：显示5秒，信息类型，蓝色铃铛图标</li>
            <li><strong>桌面通知</strong>：需要浏览器权限，会在系统通知栏显示</li>
            <li><strong>声音提醒</strong>：播放提示音（需要音频文件）</li>
            <li><strong>通知中心</strong>：在聊天窗口查看所有通知</li>
          </ul>
        </div>
      </div>
    </el-card>
    
    <!-- 商家聊天组件 -->
    <div class="chat-container">
      <MerchantChat 
        :merchant-id="1" 
        :merchant-token="'demo-token'"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElNotification } from 'element-plus'
import { Money, Bell, ChatDotRound, ShoppingBag } from '@element-plus/icons-vue'
import MerchantChat from '../components/MerchantChat.vue'

// 模拟退款申请通知
const simulateRefundNotification = () => {
  ElNotification({
    title: '收到退款申请',
    message: '订单 202507251442251419 收到退款申请，退款金额 8 元，退款原因：不想要了/拍错了',
    type: 'warning',
    position: 'top-right',
    duration: 8000,
    showClose: true,
    onClick: () => {
      ElNotification.success({
        title: '跳转成功',
        message: '已跳转到退款详情页面',
        duration: 3000
      })
    }
  })
  
  // 同时显示桌面通知
  if ('Notification' in window && Notification.permission === 'granted') {
    const notification = new Notification('收到退款申请', {
      body: '订单 202507251442251419 收到退款申请，退款金额 8 元',
      icon: '/icons/refund-notification.png',
      tag: 'refund_demo'
    })
    
    notification.onclick = () => {
      window.focus()
      notification.close()
    }
    
    setTimeout(() => notification.close(), 8000)
  }
}

// 模拟新订单通知
const simulateNewOrderNotification = () => {
  ElNotification({
    title: '收到新订单',
    message: '您收到新订单，请及时处理',
    type: 'success',
    position: 'top-right',
    duration: 6000,
    showClose: true,
    onClick: () => {
      ElNotification.success({
        title: '跳转成功',
        message: '已跳转到订单详情页面',
        duration: 3000
      })
    }
  })

  // 同时显示桌面通知
  if ('Notification' in window && Notification.permission === 'granted') {
    const notification = new Notification('收到新订单', {
      body: '您收到新订单，请及时处理',
      icon: '/icons/order-notification.png',
      tag: 'new_order_demo'
    })

    notification.onclick = () => {
      window.focus()
      notification.close()
    }

    setTimeout(() => notification.close(), 6000)
  }
}

// 模拟一般通知
const simulateGeneralNotification = () => {
  ElNotification({
    title: '系统通知',
    message: '您有新的订单需要处理，请及时查看',
    type: 'info',
    position: 'top-right',
    duration: 5000,
    showClose: true
  })
}

// 模拟多条通知
const simulateMultipleNotifications = () => {
  const notifications = [
    { title: '收到新订单', message: '您收到一个新订单，请及时处理', type: 'success' },
    { title: '收到退款申请', message: '订单 202507251442251419 收到退款申请', type: 'warning' },
    { title: '库存预警', message: '商品库存不足，请及时补货', type: 'warning' },
    { title: '客户咨询', message: '有客户在线咨询，请及时回复', type: 'info' }
  ]
  
  notifications.forEach((notif, index) => {
    setTimeout(() => {
      ElNotification({
        title: notif.title,
        message: notif.message,
        type: notif.type as any,
        position: 'top-right',
        duration: 4000,
        showClose: true
      })
    }, index * 1000)
  })
}

// 请求通知权限
if ('Notification' in window && Notification.permission === 'default') {
  Notification.requestPermission()
}
</script>

<style scoped>
.notification-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.demo-content {
  .el-alert {
    margin-bottom: 24px;
  }
  
  .demo-buttons {
    display: flex;
    gap: 16px;
    margin: 24px 0;
    flex-wrap: wrap;
    
    .el-button {
      min-width: 180px;
    }
  }
  
  .demo-info {
    margin-top: 32px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    
    h4 {
      margin-bottom: 12px;
      color: #303133;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        line-height: 1.5;
        
        strong {
          color: #409eff;
        }
      }
    }
  }
}

.chat-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

@media (max-width: 768px) {
  .demo-buttons {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
  
  .chat-container {
    bottom: 10px;
    right: 10px;
  }
}
</style>
