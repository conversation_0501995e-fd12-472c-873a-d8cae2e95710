<template>
  <div class="reviews-page">
    <div class="page-header">
      <h2>评价管理</h2>
    </div>
    
    <!-- 评价筛选区域 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="商品名称">
          <el-input v-model="filterForm.productName" placeholder="商品名称" clearable />
        </el-form-item>
        <el-form-item label="评分">
          <el-select v-model="filterForm.rating" placeholder="评分" clearable>
            <el-option label="全部" value="" />
            <el-option label="5星" value="5" />
            <el-option label="4星" value="4" />
            <el-option label="3星" value="3" />
            <el-option label="2星" value="2" />
            <el-option label="1星" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="回复状态">
          <el-select v-model="filterForm.replied" placeholder="回复状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="已回复" value="true" />
            <el-option label="未回复" value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="评价时间">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchReviews">搜索</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 评价列表 -->
    <el-card class="review-list-card">
      <div class="table-operation">
        <div class="batch-actions">
          <el-button 
            type="primary" 
            :disabled="selectedReviews.length === 0" 
            @click="batchReply"
          >
            批量回复
          </el-button>
          <el-button 
            type="danger" 
            :disabled="selectedReviews.length === 0"
            @click="batchDelete"
          >
            批量删除
          </el-button>
        </div>
        <el-button type="success" @click="exportReviews">
          <el-icon><Download /></el-icon> 导出评价
        </el-button>
      </div>
      
      <el-table
        :data="reviewList"
        style="width: 100%"
        v-loading="loading"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="商品信息" min-width="250">
          <template #default="{row}">
            <div class="product-info">
              <el-image :src="row.productImage" class="product-image" fit="cover" />
              <div class="product-details">
                <div class="product-name">{{ row.productName }}</div>
                <div class="product-price">¥{{ row.productPrice.toFixed(2) }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="评分" width="120">
          <template #default="{row}">
            <el-rate
              v-model="row.rating"
              disabled
              show-score
              text-color="#ff9900"
              score-template="{value}"
            />
          </template>
        </el-table-column>
        <el-table-column label="评价内容" min-width="250">
          <template #default="{row}">
            <div class="review-content">
              <div class="review-text">{{ row.content }}</div>
              <div class="review-images" v-if="row.images && row.images.length > 0">
                <el-image 
                  v-for="(img, index) in row.images" 
                  :key="index" 
                  :src="img" 
                  class="review-image" 
                  fit="cover"
                  :preview-src-list="row.images"
                />
              </div>
              <div class="review-reply" v-if="row.reply">
                <div class="reply-title">商家回复：</div>
                <div class="reply-content">{{ row.reply }}</div>
                <div class="reply-time">{{ formatTime(row.replyTime) }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="买家" width="120" />
        <el-table-column prop="createTime" label="评价时间" width="180">
          <template #default="{row}">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{row}">
            <el-button 
              size="small" 
              type="primary" 
              @click="replyReview(row)"
              v-if="!row.reply"
            >
              回复
            </el-button>
            <el-button 
              size="small" 
              @click="editReply(row)"
              v-else
            >
              修改回复
            </el-button>
            <el-button 
              size="small" 
              type="info"
              @click="viewReviewDetail(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 回复对话框 -->
    <el-dialog v-model="replyDialogVisible" :title="isEdit ? '修改回复' : '回复评价'" width="500px">
      <div class="review-info">
        <div class="review-rating">
          <span class="label">买家评分：</span>
          <el-rate v-model="currentReview.rating" disabled show-score text-color="#ff9900" score-template="{value}" />
        </div>
        <div class="review-detail">
          <span class="label">评价内容：</span>
          <div class="content">{{ currentReview.content }}</div>
        </div>
      </div>
      
      <el-form :model="replyForm">
        <el-form-item label="回复内容">
          <el-input 
            v-model="replyForm.content" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入回复内容"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="replyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReply" :loading="submitting">确认</el-button>
        </span>
      </template>
    </el-dialog>
  
  <!-- 评价详情对话框 -->
  <el-dialog v-model="detailDialogVisible" title="评价详情" width="600px">
    <div class="review-info">
      <div class="review-rating">
        <span class="label">买家评分：</span>
        <el-rate v-model="currentReview.rating" disabled show-score text-color="#ff9900" score-template="{value}" />
      </div>
      <div class="review-detail">
        <span class="label">评价内容：</span>
        <div class="content">{{ currentReview.content }}</div>
      </div>
      <div v-if="currentReview.images && currentReview.images.length > 0" class="review-images-container">
        <span class="label">评价图片：</span>
        <div class="detail-images">
          <el-image 
            v-for="(img, index) in currentReview.images" 
            :key="index" 
            :src="img" 
            class="detail-image" 
            fit="cover"
            :preview-src-list="currentReview.images"
          />
        </div>
      </div>
      <div class="review-reply" v-if="currentReview.reply">
        <span class="label">商家回复：</span>
        <div class="content">{{ currentReview.reply }}</div>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="replyReview(currentReview)" v-if="!currentReview.reply">回复评价</el-button>
      </span>
    </template>
  </el-dialog>
  
  <!-- 批量回复对话框 -->
  <el-dialog v-model="batchReplyDialogVisible" title="批量回复评价" width="600px">
    <div class="batch-info">
      <p>已选择 <span class="highlight">{{ selectedReviews.length }}</span> 条评价</p>
    </div>
    
    <div class="template-container">
      <div class="template-title">快速回复模板：</div>
      <div class="template-buttons">
        <el-button 
          v-for="(template, index) in replyTemplates" 
          :key="index"
          size="small"
          @click="useReplyTemplate(template.value)"
          class="template-button"
        >
          {{ template.label }}
        </el-button>
      </div>
    </div>
    
    <el-form :model="batchReplyForm">
      <el-form-item label="回复内容">
        <el-input 
          v-model="batchReplyForm.content" 
          type="textarea" 
          :rows="4" 
          placeholder="请输入批量回复内容"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="batchReplyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBatchReply" :loading="batchSubmitting">确认回复</el-button>
      </span>
    </template>
  </el-dialog>
</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import { formatTime } from '@/utils/format'

// 筛选表单
const filterForm = reactive({
  productName: '',
  rating: '',
  replied: '',
  dateRange: []
})

// 类型定义
interface Review {
  id: string
  productId: string
  productName: string
  productImage: string
  productPrice: number
  rating: number
  content: string
  images: string[]
  customerName: string
  customerId: string
  createTime: Date
  reply?: string
  replyTime?: Date
}

// 评价列表数据
const loading = ref(false)
const reviewList = ref<Review[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const selectedReviews = ref<string[]>([])

// 回复对话框
const replyDialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const currentReview = reactive({
  id: '',
  productId: '',
  rating: 0,
  content: '',
  reply: '',
  images: [] as string[]
})
const replyForm = reactive({
  content: ''
})

// 评价详情对话框
const detailDialogVisible = ref(false)

// 批量回复对话框
const batchReplyDialogVisible = ref(false)
const batchSubmitting = ref(false)
const batchReplyForm = reactive({
  content: ''
})

// 获取评价列表
async function getReviewList() {
  loading.value = true
  selectedReviews.value = []
  try {
    // 构建查询参数
    // 实际项目中会使用以下参数调用API
    /* 
    const queryParams = {
      page: currentPage.value,
      pageSize: pageSize.value,
      productName: filterForm.productName || undefined,
      rating: filterForm.rating || undefined,
      replied: filterForm.replied || undefined,
      startDate: filterForm.dateRange?.[0] ? new Date(filterForm.dateRange[0]).toISOString() : undefined,
      endDate: filterForm.dateRange?.[1] ? new Date(filterForm.dateRange[1]).toISOString() : undefined
    }
    
    // 调用API获取评价列表
    // const { data } = await api.merchant.getReviews(queryParams)
    // reviewList.value = data.items
    // total.value = data.total
    */


    // 临时使用模拟数据，实际项目中替换为上面的API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    reviewList.value = [
      {
        id: '1',
        productId: 'p001',
        productName: '高品质无线蓝牙耳机',
        productImage: 'https://placeholder.pics/svg/80',
        productPrice: 299.00,
        rating: 5,
        content: '音质非常好，续航也很长，很满意的一次购物体验！',
        images: [
          'https://placeholder.pics/svg/100',
          'https://placeholder.pics/svg/100'
        ],
        customerName: '张三',
        customerId: 'u001',
        createTime: new Date('2023-06-16 14:30:00'),
        reply: '感谢您的支持和认可，我们会继续提供优质的产品和服务！',
        replyTime: new Date('2023-06-16 16:45:00')
      },
      {
        id: '2',
        productId: 'p002',
        productName: '智能手表',
        productImage: 'https://placeholder.pics/svg/80',
        productPrice: 599.00,
        rating: 4,
        content: '功能很全面，但是电池续航一般，希望后续能改进。',
        images: [],
        customerName: '李四',
        customerId: 'u002',
        createTime: new Date('2023-06-15 10:20:00'),
        reply: '',
        replyTime: undefined
      },
      {
        id: '3',
        productId: 'p003',
        productName: '便携式移动电源',
        productImage: 'https://placeholder.pics/svg/80',
        productPrice: 129.00,
        rating: 5,
        content: '充电速度快，容量大，外观也很漂亮，非常满意！',
        images: [
          'https://placeholder.pics/svg/100'
        ],
        customerName: '王五',
        customerId: 'u003',
        createTime: new Date('2023-06-14 09:15:00'),
        reply: '谢谢您的好评，欢迎下次再来选购我们的产品！',
        replyTime: new Date('2023-06-14 11:30:00')
      }
    ]
    
    total.value = 3
  } catch (error) {
    ElMessage.error('获取评价列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 搜索评价
function searchReviews() {
  currentPage.value = 1
  getReviewList()
}

// 重置筛选条件
function resetFilter() {
  filterForm.productName = ''
  filterForm.rating = ''
  filterForm.replied = ''
  filterForm.dateRange = []
  searchReviews()
}

// 分页处理
function handleSizeChange() {
  getReviewList()
}

function handleCurrentChange() {
  getReviewList()
}

// 回复评价
function replyReview(row: any) {
  isEdit.value = false
  currentReview.id = row.id
  currentReview.rating = row.rating
  currentReview.content = row.content
  replyForm.content = ''
  replyDialogVisible.value = true
}

// 修改回复
function editReply(row: any) {
  isEdit.value = true
  currentReview.id = row.id
  currentReview.rating = row.rating
  currentReview.content = row.content
  replyForm.content = row.reply
  replyDialogVisible.value = true
}

// 提交回复
async function submitReply() {
  if (!replyForm.content.trim()) {
    ElMessage.warning('请输入回复内容')
    return
  }
  
  submitting.value = true
  try {
    // 准备请求参数
    // 实际项目中会使用以下参数调用API
    /*
    const replyParams = {
      reviewId: currentReview.id,
      content: replyForm.content.trim()
    }

    // 调用API提交回复
    // const { data } = await api.merchant.replyReview(replyParams)
    */
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 更新本地数据
    const index = reviewList.value.findIndex(item => item.id === currentReview.id)
    if (index !== -1) {
      reviewList.value[index].reply = replyForm.content
      reviewList.value[index].replyTime = new Date()
    }
    
    ElMessage.success(isEdit.value ? '修改回复成功' : '回复成功')
    replyDialogVisible.value = false
  } catch (error) {
    ElMessage.error('操作失败，请重试')
    console.error(error)
  } finally {
    submitting.value = false
  }
}

// 查看评价详情
function viewReviewDetail(row: Review) {
  currentReview.id = row.id
  currentReview.rating = row.rating
  currentReview.content = row.content
  currentReview.reply = row.reply || ''
  currentReview.images = row.images || []
  detailDialogVisible.value = true
}

// 批量回复模板
const replyTemplates = [
  { label: '感谢您的购买和好评，我们会继续提供优质的产品和服务！', value: '感谢您的购买和好评，我们会继续提供优质的产品和服务！' },
  { label: '感谢您的反馈，我们会认真考虑您的建议并不断改进产品。', value: '感谢您的反馈，我们会认真考虑您的建议并不断改进产品。' },
  { label: '非常抱歉给您带来不好的体验，我们会尽快解决这个问题。', value: '非常抱歉给您带来不好的体验，我们会尽快解决这个问题。' }
]

// 使用模板回复
function useReplyTemplate(template: string) {
  if (batchReplyDialogVisible.value) {
    batchReplyForm.content = template
  } else {
    replyForm.content = template
  }
}

// 批量回复
function batchReply() {
  if (selectedReviews.value.length === 0) {
    ElMessage.warning('请先选择要回复的评价')
    return
  }
  batchReplyForm.content = ''
  batchReplyDialogVisible.value = true
}

// 提交批量回复
async function submitBatchReply() {
  if (!batchReplyForm.content.trim()) {
    ElMessage.warning('请输入回复内容')
    return
  }
  
  batchSubmitting.value = true
  try {
    // 准备请求参数
    // 实际项目中会使用以下参数调用API
    /*
    const batchParams = {
      reviewIds: selectedReviews.value,
      content: batchReplyForm.content.trim()
    }

    // 调用API提交批量回复
    // await api.merchant.batchReplyReviews(batchParams)
    */
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 更新本地数据
    const now = new Date()
    reviewList.value.forEach(item => {
      if (selectedReviews.value.includes(item.id)) {
        item.reply = batchReplyForm.content
        item.replyTime = now
      }
    })
    
    ElMessage.success('批量回复成功')
    batchReplyDialogVisible.value = false
    selectedReviews.value = []
  } catch (error) {
    ElMessage.error('操作失败，请重试')
    console.error(error)
  } finally {
    batchSubmitting.value = false
  }
}

// 批量删除
function batchDelete() {
  if (selectedReviews.value.length === 0) {
    ElMessage.warning('请先选择要删除的评价')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedReviews.value.length} 条评价吗？此操作不可逆`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 调用API删除评价
      // await api.merchant.batchDeleteReviews({ reviewIds: selectedReviews.value })
      
      // 模拟删除
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 更新本地数据
      reviewList.value = reviewList.value.filter(item => !selectedReviews.value.includes(item.id))
      total.value -= selectedReviews.value.length
      selectedReviews.value = []
      
      ElMessage.success('删除成功')
    } catch (error) {
      ElMessage.error('删除失败，请重试')
      console.error(error)
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 导出评价
async function exportReviews() {
  try {
    ElMessage.info('正在准备导出数据...')
    
    // 构建查询参数，与当前筛选条件相同
    // 实际项目中会使用以下参数调用API
    /*
    const exportParams = {
      productName: filterForm.productName || undefined,
      rating: filterForm.rating || undefined,
      replied: filterForm.replied || undefined,
      startDate: filterForm.dateRange?.[0] ? new Date(filterForm.dateRange[0]).toISOString() : undefined,
      endDate: filterForm.dateRange?.[1] ? new Date(filterForm.dateRange[1]).toISOString() : undefined
    }
    
    // 实际项目中，这里应该调用API请求获取导出文件
    // const response = await api.merchant.exportReviews(exportParams)
    */
    // 然后使用浏览器下载文件
    // const blob = new Blob([response.data])
    // const link = document.createElement('a')
    // link.href = URL.createObjectURL(blob)
    // link.download = `评价数据_${new Date().toISOString().split('T')[0]}.xlsx`
    // link.click()
    
    // 模拟导出过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('评价数据导出成功')
  } catch (error) {
    ElMessage.error('导出失败，请重试')
    console.error(error)
  }
}

// 处理表格选择变化
function handleSelectionChange(selection: Review[]) {
  selectedReviews.value = selection.map(item => item.id)
}

// 初始化
onMounted(() => {
  getReviewList()
})
</script>

<style scoped>
.reviews-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-card {
  margin-bottom: 20px;
}

.review-list-card {
  margin-bottom: 20px;
}

.product-info {
  display: flex;
  align-items: center;
}

.product-image {
  width: 60px;
  height: 60px;
  margin-right: 10px;
  border-radius: 4px;
  object-fit: cover;
  border: 1px solid #eee;
}

.product-details {
  flex: 1;
}

.product-name {
  font-weight: bold;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price {
  color: #f56c6c;
  font-size: 13px;
}

.review-content {
  padding: 5px 0;
}

.review-text {
  margin-bottom: 8px;
  line-height: 1.5;
}

.review-images {
  display: flex;
  flex-wrap: wrap;
  margin: 8px 0;
}

.review-image {
  width: 60px;
  height: 60px;
  margin-right: 5px;
  margin-bottom: 5px;
  border-radius: 4px;
  object-fit: cover;
  cursor: pointer;
  border: 1px solid #eee;
}

.review-reply {
  background-color: #f8f8f8;
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 8px;
}

.reply-title {
  font-weight: bold;
  color: #67c23a;
  margin-bottom: 4px;
}

.reply-content {
  margin-bottom: 4px;
  line-height: 1.5;
}

.reply-time {
  font-size: 12px;
  color: #999;
  text-align: right;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.review-info {
  margin-bottom: 20px;
}

.review-rating,
.review-detail {
  margin-bottom: 10px;
}

.label {
  font-weight: bold;
  margin-right: 5px;
}

.content {
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.template-container {
  margin-bottom: 15px;
}

.template-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #606266;
}

.template-buttons {
  display: flex;
  flex-wrap: wrap;
}

.template-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.table-operation {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-actions {
  display: flex;
  gap: 10px;
}

.detail-images {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.detail-image {
  width: 100px;
  height: 100px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  object-fit: cover;
  cursor: pointer;
  border: 1px solid #eee;
}

.review-images-container {
  margin-top: 10px;
  margin-bottom: 10px;
}

.highlight {
  color: #409eff;
  font-weight: bold;
}

.batch-info {
  margin-bottom: 15px;
  font-size: 14px;
  color: #606266;
}

/* 媒体查询适配移动端 */
@media (max-width: 768px) {
  .filter-form {
    display: flex;
    flex-direction: column;
  }
  
  .filter-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }
}
</style>