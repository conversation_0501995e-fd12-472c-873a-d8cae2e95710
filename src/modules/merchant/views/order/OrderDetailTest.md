# 订单详情页面到店自提功能测试指南

## 功能概述

本次修改为订单详情页面添加了到店自提功能的支持，包括：

1. **配送方式识别**：根据 `deliveryType` 字段区分不同配送方式
   - 0: 跑腿员配送
   - 1: 商家自配送  
   - 2: 用户到店自提

2. **到店自提订单流程**：
   - 下单 → 接单（备餐中）→ 等待自提 → 自提完成（待评价）→ 评价完成（订单结束）

3. **商家操作流程**：
   - 接单 → 开始备餐 → 备餐完成 → 用户自提（验证取餐码）

## 新增功能

### 1. 界面显示优化

- **自提信息区域**：显示取餐码、取餐码状态、预计取餐时间等
- **配送方式标识**：清晰标识订单的配送方式
- **自提状态显示**：将配送状态转换为自提相关的状态文本

### 2. 操作按钮

- **开始备餐**：商家接单后开始备餐
- **备餐完成**：备餐完成，等待用户自提
- **确认自提**：用户到店后，验证取餐码并确认自提完成

### 3. 状态映射

配送状态到自提状态的映射：
- `WAITING` → "等待接单"
- `PENDING/ACCEPTED/PICKING` → "备餐中"
- `PICKED_UP/DELIVERING` → "等待自提"
- `COMPLETED` → "自提完成"
- `CANCELLED` → "已取消"

## 测试步骤

### 1. 准备测试数据

确保后端返回的订单数据包含以下字段：
```json
{
  "deliveryType": 2,
  "deliveryInfo": {
    "deliveryType": 2,
    "pickupCode": "854101",
    "pickupCodeUsed": false,
    "pickupCodeUsedTime": null,
    "expectedPickupTime": "2025-07-31 21:33:19",
    "startTime": "2025-07-31T21:03:19+08:00",
    "endTime": "2025-07-31T21:33:19+08:00"
  }
}
```

### 2. 界面测试

1. **访问订单详情页面**
   - 导航到商家后台 → 订单管理 → 订单列表
   - 点击到店自提订单的"查看详情"按钮

2. **验证界面显示**
   - 确认显示"自提信息"区域而非"收货信息"
   - 验证取餐码、取餐码状态等信息正确显示
   - 确认配送方式显示为"到店自提"

3. **验证操作按钮**
   - 根据订单状态，确认显示正确的操作按钮
   - 验证按钮的启用/禁用状态

### 3. 功能测试

1. **开始备餐**
   - 点击"开始备餐"按钮
   - 确认弹出确认对话框
   - 确认操作成功后状态更新

2. **备餐完成**
   - 点击"备餐完成"按钮
   - 确认状态变更为"等待自提"

3. **确认自提**
   - 点击"确认自提"按钮
   - 输入正确的取餐码（6位数字）
   - 确认自提完成，订单状态更新

### 4. 错误处理测试

1. **取餐码验证**
   - 输入错误格式的取餐码
   - 输入错误的取餐码
   - 验证错误提示信息

2. **网络错误处理**
   - 模拟网络错误
   - 验证错误提示和状态恢复

## API 接口

新增的API接口：

1. `POST /v1/merchant/takeout/orders/pickup/start-preparation` - 开始备餐
2. `POST /v1/merchant/takeout/orders/pickup/complete-preparation` - 完成备餐  
3. `POST /v1/merchant/takeout/orders/pickup/complete` - 确认自提

## 注意事项

1. 确保后端API已实现相应的接口
2. 验证取餐码的格式和有效性
3. 注意状态流转的逻辑正确性
4. 测试不同订单状态下的按钮显示逻辑
