<template>
  <div class="order-list-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>外卖订单管理</span>
        </div>
      </template>

      <!-- 筛选区域 -->
      <el-form :inline="true" @submit.prevent class="filter-form">
        <el-form-item label="订单状态">
          <el-select v-model="filterStatus" placeholder="全部状态" clearable @change="handleFilterChange" style="width: 240px">
            <el-option label="全部状态" :value="undefined"></el-option>
            <el-option v-for="option in statusOptions" :key="option.value" :label="option.label" :value="option.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <!-- 订单列表 -->
      <el-table :data="orders" v-loading="loading" style="width: 100%">
        <el-table-column prop="order_number" label="订单号" min-width="200"></el-table-column>
        <el-table-column prop="user_name" label="用户名称" width="150"></el-table-column>
        <el-table-column label="订单状态" width="120">
          <template #default="scope">
            <span>{{ OrderStatusText[scope.row.status as OrderStatus] || '未知状态' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pay_amount" label="支付金额" width="100">
          <template #default="scope">
            <span>¥{{ scope.row.pay_amount.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_amount" label="总金额" width="100">
          <template #default="scope">
            <span>¥{{ scope.row.total_amount.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="下单时间" width="180">
           <template #default="scope">
            <span>{{ formatTime(scope.row.created_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewOrderDetail(scope.row.id)">查看详情</el-button>
            <!-- 后续根据订单状态添加更多操作按钮 -->
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="pagination.total > 0"
        background
        layout="prev, pager, next, jumper, ->, total"
        :total="pagination.total"
        :current-page.sync="pagination.currentPage"
        :page-size.sync="pagination.pageSize"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        style="margin-top: 20px; text-align: right;"
      ></el-pagination>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { getOrderList } from '@/modules/merchant/api/order';
import type { OrderInfo, OrderStatus } from '@/modules/merchant/types';
import { OrderStatusText } from '@/modules/merchant/types';
import { formatTime } from '@/utils/format'; // 假设有时间格式化工具

const router = useRouter();

const orders = ref<OrderInfo[]>([]);
const loading = ref(false);
const filterStatus = ref<number | undefined>(undefined);

const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 订单状态筛选选项
const statusOptions = computed(() => {
  return Object.entries(OrderStatusText)
    .map(([value, label]) => ({
      value: parseInt(value, 10),
      label: label || '未知',
    }))
    .filter(option => !isNaN(option.value)); //确保value是数字
});

// 获取订单列表
const fetchOrders = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.value.currentPage,
      page_size: pagination.value.pageSize,
      status: filterStatus.value,
    };
    const response: any = await getOrderList(params);
    // getOrderList 现在返回 Promise<{ list: OrderInfo[]; total_count: number; }>
    // 或者如果你的 get 工具函数返回的是 AxiosResponse<T>, 则需要 response.data.data
    // 假设 get 工具函数返回的是 { data: T } 结构，其中 T 是我们指定的泛型
    if (response && response.list) { // 直接访问泛型中定义的属性
      orders.value = response.list;
      pagination.value.total = response.total_count;
    } else {
      // 处理API未按预期结构返回数据的情况，或者response本身为null/undefined
      console.warn('获取订单列表API响应格式不符合预期:', response);
      orders.value = [];
      pagination.value.total = 0;
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    ElMessage.error('获取订单列表失败');
    orders.value = [];
    pagination.value.total = 0;
  } finally {
    loading.value = false;
  }
};

// 处理筛选条件变化
const handleFilterChange = () => {
  pagination.value.currentPage = 1; // 筛选条件改变时，回到第一页
  fetchOrders();
};

// 处理页码变化
const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page;
  fetchOrders();
};

// 处理每页显示数量变化
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size;
  pagination.value.currentPage = 1; // 更改每页大小时，回到第一页
  fetchOrders();
};

// 查看订单详情
const viewOrderDetail = (orderId: number) => {
  router.push({ name: 'MerchantOrderDetail', params: { id: orderId.toString() } });
};

// 组件挂载时获取数据
onMounted(() => {
  fetchOrders();
});



</script>

<style scoped lang="scss">
.order-list-page {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.filter-form {
  margin-bottom: 20px;
}
</style>
