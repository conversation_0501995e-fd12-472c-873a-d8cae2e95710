<template>
  <div class="order-statistics-page" v-loading="loading">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>订单统计</span>
        </div>
      </template>

      <div v-if="stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card shadow="hover">
              <el-statistic title="今日订单总数" :value="stats.today_orders"></el-statistic>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <el-statistic title="今日订单总金额" :value="stats.today_amount" prefix="¥"></el-statistic>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <el-statistic title="处理中订单" :value="stats.processing_count"></el-statistic>
            </el-card>
          </el-col>
          <!-- 配送中订单暂时没有直接字段，如果需要可以从 processing_count 细分或API增加 -->
        </el-row>

        <el-row :gutter="20" class="section-margin">
          <el-col :span="6">
            <el-card shadow="hover">
              <el-statistic title="本月订单总数" :value="stats.month_orders"></el-statistic>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <el-statistic title="本月订单总金额" :value="stats.month_amount" prefix="¥"></el-statistic>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <el-statistic title="已完成订单总数" :value="stats.completed_count"></el-statistic>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <el-statistic title="已取消订单总数" :value="stats.cancelled_count"></el-statistic>
            </el-card>
          </el-col>
        </el-row>

        <!-- 可以在这里添加图表，例如订单状态分布 -->
        <!-- <div id="orderStatusChart" style="width: 100%; height: 400px; margin-top: 30px;"></div> -->

      </div>
      <el-empty v-else-if="!loading" description="暂无统计数据或加载失败"></el-empty>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getOrderStatistics } from '@/modules/merchant/api/order';
import type { OrderStatistics } from '@/modules/merchant/types';
// import * as echarts from 'echarts'; // 如果使用ECharts，取消注释

const stats = ref<OrderStatistics | null>(null);
const loading = ref(false);
// let chartInstance: echarts.ECharts | null = null; // 如果使用ECharts

const fetchOrderStats = async () => {
  loading.value = true;
  try {
    const response = await getOrderStatistics();
    // 假设 getOrderStatistics 返回的是 OrderStatistics 对象
    // 如果返回 Promise<OrderStatistics>
    stats.value = response;
    // 如果返回 Promise<{ data: OrderStatistics }>
    // stats.value = response.data;

    // 如果使用ECharts，可以在这里初始化图表
    // if (stats.value) {
    //   initOrderStatusChart(stats.value);
    // }
  } catch (error) {
    console.error('获取订单统计失败:', error);
    ElMessage.error('获取订单统计失败');
    stats.value = null;
  } finally {
    loading.value = false;
  }
};

/* // 如果使用ECharts，取消注释以下代码
const initOrderStatusChart = (data: OrderStatistics) => {
  const chartDom = document.getElementById('orderStatusChart');
  if (chartDom) {
    if (chartInstance) {
      chartInstance.dispose(); // 销毁旧实例，防止重复渲染
    }
    chartInstance = echarts.init(chartDom);
    const option = {
      title: {
        text: '订单状态分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '订单状态',
          type: 'pie',
          radius: '50%',
          data: [
            { value: data.processing_count, name: '处理中/待配送' }, // processing_count 可能包含待处理和待配送
            // { value: data.delivering_order_count, name: '配送中' }, // API暂无此细分字段，如有需要可添加
            { value: data.completed_count, name: '已完成' },
            { value: data.cancelled_count, name: '已取消' },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    chartInstance.setOption(option);
  }
};
*/

onMounted(() => {
  fetchOrderStats();
});

// 如果使用了ECharts，在组件卸载时销毁图表实例
// import { onBeforeUnmount } from 'vue';
// onBeforeUnmount(() => {
//   if (chartInstance) {
//     chartInstance.dispose();
//   }
// });
</script>

<style scoped lang="scss">
.order-statistics-page {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.section-margin {
  margin-top: 20px;
}
.el-statistic {
  text-align: center;
}
</style>
