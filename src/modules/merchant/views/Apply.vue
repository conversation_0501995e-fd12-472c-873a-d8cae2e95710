<template>
  <div class="merchant-apply">
    <div class="apply-container">
      <div class="apply-header">
        <img src="/images/logo.png" alt="O_Mall" class="logo" />
        <h1 class="title">商家入驻申请</h1>
      </div>

      <el-card class="apply-card">
        <el-steps :active="activeStep" finish-status="success" class="apply-steps" align-center>
          <el-step title="阅读协议" description="商家入驻协议" />
          <el-step title="填写资料" description="基本信息和资质" />
          <el-step title="等待审核" description="提交成功等待审核" />
        </el-steps>

        <!-- 步骤1：阅读协议 -->
        <div v-if="activeStep === 0" class="step-content">
          <div class="agreement-content">
            <h3>O_Mall商家入驻协议</h3>
            <div class="agreement-text" v-if="agreementLoading">
              <el-skeleton :rows="15" animated />
            </div>
            <div class="agreement-text" v-else-if="agreementError">
              <p>加载协议内容失败，请刷新页面重试。</p>
            </div>
            <div class="agreement-text" v-else v-html="agreementContent"></div>
          </div>
          
          <div class="agreement-confirm">
            <el-checkbox v-model="agreementAccepted">我已仔细阅读并同意以上协议内容</el-checkbox>
          </div>
          
          <div class="step-actions">
            <el-button type="primary" :disabled="!agreementAccepted" @click="nextStep">下一步</el-button>
            <el-button @click="cancelApply">取消</el-button>
          </div>
        </div>

        <!-- 步骤2：填写资料 -->
        <div v-if="activeStep === 1" class="step-content">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-position="left"
            label-width="180px"
            :disabled="submitting"
            class="beauty-form"
          >
            <!-- 店铺基本信息 -->
            <div class="form-section-card">
              <h3 class="form-section-title">店铺基本信息</h3>
              <el-form-item label="店铺名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入店铺名称" maxlength="30" show-word-limit />
              </el-form-item>
              <el-form-item label="店铺简介" prop="description">
                <el-input v-model="formData.description" placeholder="请简述店铺特色和优势" type="textarea" :rows="3" maxlength="500" show-word-limit />
              </el-form-item>
              <el-form-item label="店铺地址" prop="address">
                <el-input v-model="formData.address" placeholder="请输入店铺详细地址" />
              </el-form-item>
              <el-form-item label="商家分类" prop="category_id">
                <el-select v-model="formData.category_id" placeholder="请选择商家分类" style="width: 100%" :loading="categoryLoading">
                  <el-option 
                    v-for="item in categoryList" 
                    :key="item.id" 
                    :label="item.name" 
                    :value="item.id" 
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="经营范围" prop="businessScope">
                <el-input v-model="formData.businessScope" placeholder="请简述店铺主要经营的商品类型" type="textarea" :rows="2" maxlength="200" show-word-limit />
              </el-form-item>
            </div>
            
            <!-- 登录信息 -->
            <div class="form-section-card">
              <h3 class="form-section-title">登录信息</h3>
              <el-form-item label="登录用户名" prop="username">
                <el-input v-model="formData.username" placeholder="请设置登录用户名" maxlength="20" show-word-limit />
              </el-form-item>
              <el-form-item label="登录密码" prop="password">
                <el-input v-model="formData.password" placeholder="请设置登录密码" type="password" show-password />
              </el-form-item>
            </div>
            <!-- 联系人信息 -->
            <div class="form-section-card">
              <h3 class="form-section-title">联系人信息</h3>
              <el-form-item label="联系人姓名" prop="contact_name">
                <el-input v-model="formData.contact_name" placeholder="请输入联系人姓名" />
              </el-form-item>
              <el-form-item label="联系电话" prop="contact_mobile">
                <el-input v-model="formData.contact_mobile" placeholder="请输入联系人手机号" />
              </el-form-item>
              <el-form-item label="电子邮箱" prop="contact_email">
                <el-input v-model="formData.contact_email" placeholder="请输入电子邮箱" />
              </el-form-item>
            </div>
            <!-- 资质信息 -->
            <div class="form-section-card">
              <h3 class="form-section-title">资质信息</h3>
              <el-form-item label="营业执照" prop="business_license">
                <div v-if="formData.business_license" class="preview-image-container">
                  <img :src="formData.business_license" class="uploaded-image" />
                  <el-button type="danger" size="small" class="remove-btn" @click="formData.business_license = ''">删除</el-button>
                </div>
                <FileUploader
                  v-else
                  :action="uploadUrl"
                  :headers="uploadHeaders"
                  :data="{type: 'license'}"
                  accept=".jpg,.jpeg,.png"
                  :file-limit="1"
                  file-usage="license"
                  @success="(res) => handleUploadSuccess(res, 'business_license')"
                  @error="handleUploadError"
                >
                  <template #tip>
                    <p>上传营业执照</p>
                    <p class="upload-tip">请上传清晰的营业执照电子版，支持JPG、PNG格式</p>
                  </template>
                </FileUploader>
              </el-form-item>
              <el-form-item label="身份证正面照" prop="idCardFront">
                <div v-if="formData.idCardFront" class="preview-image-container">
                  <img :src="formData.idCardFront" class="uploaded-image" />
                  <el-button type="danger" size="small" class="remove-btn" @click="formData.idCardFront = ''">删除</el-button>
                </div>
                <FileUploader
                  v-else
                  :action="uploadUrl"
                  :headers="uploadHeaders"
                  :data="{type: 'idcard'}"
                  accept=".jpg,.jpeg,.png"
                  :file-limit="1"
                  file-usage="idcard"
                  @success="(res) => handleUploadSuccess(res, 'idCardFront')"
                  @error="handleUploadError"
                >
                  <template #tip>
                    <p>上传身份证正面照</p>
                    <p class="upload-tip">请上传联系人身份证正面照片，需清晰可见</p>
                  </template>
                </FileUploader>
              </el-form-item>
              <el-form-item label="身份证背面照" prop="idCardBack">
                <div v-if="formData.idCardBack" class="preview-image-container">
                  <img :src="formData.idCardBack" class="uploaded-image" />
                  <el-button type="danger" size="small" class="remove-btn" @click="formData.idCardBack = ''">删除</el-button>
                </div>
                <FileUploader
                  v-else
                  :action="uploadUrl"
                  :headers="uploadHeaders"
                  :data="{type: 'idcard'}"
                  accept=".jpg,.jpeg,.png"
                  :file-limit="1"
                  file-usage="idcard"
                  @success="(res) => handleUploadSuccess(res, 'idCardBack')"
                  @error="handleUploadError"
                >
                  <template #tip>
                    <p>上传身份证背面照</p>
                    <p class="upload-tip">请上传联系人身份证背面照片，需清晰可见</p>
                  </template>
                </FileUploader>
              </el-form-item>
            </div>
            <!-- 结算信息 -->
            <div class="form-section-card">
              <h3 class="form-section-title">结算信息</h3>
              <el-form-item label="银行名称" prop="bankName">
                <el-select v-model="formData.bankName" placeholder="请选择开户银行" style="width: 100%">
                  <el-option label="工商银行" value="工商银行" />
                  <el-option label="农业银行" value="农业银行" />
                  <el-option label="建设银行" value="建设银行" />
                  <el-option label="中国银行" value="中国银行" />
                  <el-option label="交通银行" value="交通银行" />
                  <el-option label="招商银行" value="招商银行" />
                  <el-option label="浦发银行" value="浦发银行" />
                  <el-option label="民生银行" value="民生银行" />
                  <el-option label="中信银行" value="中信银行" />
                  <el-option label="兴业银行" value="兴业银行" />
                  <el-option label="广发银行" value="广发银行" />
                  <el-option label="光大银行" value="光大银行" />
                  <el-option label="邮政储蓄银行" value="邮政储蓄银行" />
                </el-select>
              </el-form-item>
              <el-form-item label="开户名" prop="bankAccountName">
                <el-input v-model="formData.bankAccountName" placeholder="请输入银行卡开户名" />
              </el-form-item>
              <el-form-item label="银行账号" prop="bankAccount">
                <el-input v-model="formData.bankAccount" placeholder="请输入银行卡账号" />
              </el-form-item>
            </div>
          </el-form>
          <div class="step-actions beauty-actions">
            <el-button type="primary" @click="submitForm" :loading="submitting">提交申请</el-button>
            <el-button @click="prevStep">上一步</el-button>
            <el-button @click="cancelApply">取消</el-button>
          </div>
        </div>

        <!-- 步骤3：提交成功 -->
        <div v-if="activeStep === 2" class="step-content step-success">
          <el-result
            icon="success"
            title="申请提交成功"
            sub-title="您的商家入驻申请已提交成功，我们将在1-3个工作日内完成审核。审核结果将通过短信和邮件通知您，请耐心等待。"
          >
            <template #extra>
              <el-button type="primary" @click="goToStatus">查看申请状态</el-button>
              <el-button @click="goToHome">返回首页</el-button>
            </template>
          </el-result>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { FileUploader } from '@/components/common';
import { register, merchantCategory } from '../api/auth';
import { getMerchantServiceAgreement } from '../api/system';

// 商家分类项类型
interface MerchantCategoryItem {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  sort_order?: number;
  is_show?: boolean;
  created_at?: string;
  updated_at?: string;
}

// API 返回的商家分类列表类型
interface MerchantCategoryResponse {
  list: MerchantCategoryItem[];
  total: number;
  page: number;
  pageSize: number;
}
//import { useMerchantStore } from '../stores/merchantStore';
import type { MerchantApplication } from '../types';

// 路由和商家store
const router = useRouter();
//const merchantStore = useMerchantStore();

// 表单引用
const formRef = ref();

// 当前步骤
const activeStep = ref(0);

// 是否同意协议
const agreementAccepted = ref(false);

// 协议内容
const agreementContent = ref('');
const agreementLoading = ref(true);
const agreementError = ref(false);

// 商家分类列表
const categoryList = ref<{id: number; name: string}[]>([]);
const categoryLoading = ref(false);

// 获取商家入驻协议
const fetchAgreement = async () => {
  agreementLoading.value = true;
  agreementError.value = false;
  
  try {
    const data = await getMerchantServiceAgreement();
    if (data && data.content) {
      agreementContent.value = data.content;
    } else {
      throw new Error('协议内容格式不正确');
    }
  } catch (error) {
    console.error('获取商家入驻协议失败:', error);
    agreementError.value = true;
    ElMessage.error('获取协议内容失败，请刷新页面重试');
  } finally {
    agreementLoading.value = false;
  }
};

// 获取商家分类列表
const fetchCategories = async () => {
  categoryLoading.value = true;
  
  try {
    const response = await merchantCategory() as MerchantCategoryResponse;
    console.log('商家分类列表:', response);
    if (response && Array.isArray(response.list)) {
      // 只提取需要的字段，避免不必要的响应数据
      // 确保 list 存在且是数组
      const list = Array.isArray(response?.list) ? response.list : [];
      categoryList.value = list.map((item: MerchantCategoryItem) => ({
        id: item.id,
        name: item.name
      }));
      console.log('处理后的分类列表:', categoryList.value);
    } else {
      throw new Error('获取商家分类失败：返回数据格式不正确');
    }
  } catch (error) {
    console.error('获取商家分类列表失败:', error);
    ElMessage.error('获取商家分类列表失败，请刷新页面重试');
  } finally {
    categoryLoading.value = false;
  }
};

// 组件挂载时获取协议内容和分类列表
onMounted(() => {
  fetchAgreement();
  fetchCategories();
});

// 提交状态
const submitting = ref(false);

// 表单数据
const formData = reactive<MerchantApplication>({
  name: '', // 商家名称
  logo: '', // 商家Logo
  description: '', // 商家描述
  username: '', // 登录用户名
  password: '', // 登录密码
  contact_name: '', // 联系人姓名
  contact_mobile: '', // 联系人手机号
  contact_email: '', // 联系人电子邮箱
  business_license: '', // 营业执照
  address: '', // 商家地址
  category_id: undefined, // 商家分类ID
  
  // 前端使用字段（不提交给后端）
  idCardFront: '',
  idCardBack: '',
  bankName: '',
  bankAccount: '',
  bankAccountName: '',
  businessScope: '',
  
  // 兼容旧字段名
  businessLicense: ''
});

// 上传文件相关
const uploadUrl = import.meta.env.VITE_API_BASE_URL + '/v1/merchant/upload';
const uploadHeaders = computed(() => {
  return {
    Authorization: `Bearer ${localStorage.getItem('token') || ''}`
  };
});

// 上传成功处理函数
interface UploadFileResponse {
  id: number;
  file_name: string;
  file_path: string;
  file_url: string;
  file_size: number;
  file_type: string;
  file_usage: string;
  file_ext: string;
  is_anonymous: boolean;
  storage: string;
  created_at: string;
  [key: string]: any;
}

const handleUploadSuccess = (res: UploadFileResponse, field: keyof MerchantApplication) => {
  if (res && res.file_url) {
    // 使用类型断言确保TypeScript理解这是合法操作
    (formData as any)[field] = res.file_url;
    ElMessage.success('上传成功');
  } else {
    ElMessage.error('上传失败：无效的响应数据');
  }
};

// 上传失败处理函数
const handleUploadError = (error: any) => {
  console.error('上传失败:', error);
  ElMessage.error('文件上传失败，请重试');
};

// 定义必要的功能函数
const nextStep = () => {
  if (activeStep.value === 0 && !agreementAccepted.value) {
    ElMessage.warning('请先阅读并同意协议');
    return;
  }
  activeStep.value++;
};

const prevStep = () => {
  if (activeStep.value > 0) {
    activeStep.value--;
  }
};

const cancelApply = () => {
  router.push('/');
};

const submitForm = async () => {
  if (!formRef.value) return;
  
  try {
    // 表单验证
    await formRef.value.validate();
    
    submitting.value = true;
    
    // 提交申请
    await register(formData);
    
    // 提交成功，进入下一步
    ElMessage.success('申请提交成功，请等待审核');
    activeStep.value = 2;
  } catch (error: any) {
    if (error?.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error('提交失败，请检查表单信息');
    }
  } finally {
    submitting.value = false;
  }
};

const goToStatus = () => {
  router.push('/merchant/status');
};

const goToHome = () => {
  router.push('/');
};

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入店铺名称', trigger: 'blur' },
    { min: 2, max: 30, message: '长度在2到30个字符', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择商家分类', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入店铺简介', trigger: 'blur' },
    { max: 500, message: '最多500个字符', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请设置登录用户名', trigger: 'blur' },
    { min: 4, max: 20, message: '用户名长度在4-20个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请设置登录密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在6-20个字符之间', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入店铺地址', trigger: 'blur' }
  ],
  contact_name: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  contact_mobile: [
    { required: true, message: '请输入联系人手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  contact_email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  business_license: [
    { required: true, message: '请上传营业执照', trigger: 'change' }
  ]
};

// ...其他代码
</script>

<style lang="scss" scoped>
/**
 * Apply.vue - 商家入驻申请表单页面
 * 
 * <AUTHOR>
 * @date 2023-05-20
 * @version 1.0
 * @description 商家入驻申请表单页面，包含协议阅读、信息填写和提交成功三个步骤
 */

.merchant-apply {
  padding: 30px 0;
  background-color: #f5f7fa;
  min-height: 100vh;
  
  .apply-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 20px;
    
    .apply-header {
      text-align: center;
      margin-bottom: 30px;
      
      .logo {
        height: 60px;
        margin-bottom: 15px;
      }
      
      .title {
        font-size: 24px;
        color: var(--el-color-primary);
        margin: 0;
        font-weight: 600;
      }
    }
    
    .apply-card {
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      margin-bottom: 40px;
      
      .apply-steps {
        margin: 20px 0 40px;
        width: 100%;
      }
      
      .step-content {
        margin-top: 20px;
        
        &.step-success {
          text-align: center;
          padding: 30px 0;
        }
      }
    }
  }
}

// 协议相关样式
.agreement-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 20px;
  background-color: #fafafa;
  
  h3 {
    text-align: center;
    margin-top: 0;
    color: var(--el-color-primary);
    font-weight: 600;
  }
  
  .agreement-text {
    color: #606266;
    line-height: 1.6;
    
    h4 {
      margin: 15px 0 10px;
      font-weight: 500;
    }
    
    p {
      margin: 5px 0;
    }
  }
}

.agreement-confirm {
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 步骤操作按钮
.step-actions {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  gap: 20px;
  
  &.beauty-actions {
    margin-top: 40px;
  }
}

// 表单美化
.beauty-form {
  :deep(.el-form-item) {
    margin-bottom: 22px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #333;
      line-height: 1.5;
      padding-right: 20px;
    }
    
    .el-form-item__content {
      .el-input__wrapper, 
      .el-textarea__wrapper,
      .el-select {
        box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        transition: all 0.3s;
        
        &:hover, &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary);
        }
      }
    }
  }
  
  .form-section-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    
    .form-section-title {
      margin: 0 0 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
      color: var(--el-color-primary);
      font-weight: 600;
      font-size: 18px;
    }
  }
}

// 上传区域美化
.preview-image-container {
  position: relative;
  display: inline-block;
  margin-bottom: 10px;
  width: 100%;
  max-width: 300px;
  
  .uploaded-image {
    width: 100%;
    height: auto;
    max-height: 200px;
    border-radius: 4px;
    object-fit: cover;
    border: 1px solid #e0e0e0;
  }
  
  .remove-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    opacity: 0.8;
    
    &:hover {
      opacity: 1;
    }
  }
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

// 响应式调整
@media screen and (max-width: 768px) {
  .beauty-form {
    :deep(.el-form-item) {
      .el-form-item__label {
        text-align: left;
        float: none;
        display: block;
        padding: 0 0 8px;
      }
    }
  }
  
  .step-actions {
    flex-direction: column;
    align-items: center;
    
    .el-button {
      width: 100%;
      max-width: 300px;
      margin: 5px 0;
    }
  }
}
</style>