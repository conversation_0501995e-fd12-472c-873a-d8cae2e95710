<template>
  <div class="category-list-container">
    <!-- 顶部操作区 -->
    <div class="top-bar">
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入分类名称搜索"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
      <div class="operations">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon> 新增分类
        </el-button>
      </div>
    </div>

    <!-- 主体内容区 -->
    <div class="main-content">
      <!-- 左侧分类树 -->
      <div class="category-tree" :style="{ width: treeWidth + 'px' }">
        <h3>分类结构</h3>
        <el-scrollbar height="calc(100vh - 250px)">
          <el-tree
            ref="treeRef"
            :data="categoryTreeData"
            :props="{ label: 'name', children: 'children' }"
            highlight-current
            node-key="id"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <span>{{ node.label }}</span>
                <span class="node-count">({{ data.food_count || 0 }})</span>
              </div>
            </template>
          </el-tree>
        </el-scrollbar>
      </div>

      <!-- 拖拽分割线 -->
      <div 
        class="resize-handler"
        @mousedown="handleMouseDown"
        :style="{ cursor: isDragging ? 'grabbing' : 'col-resize' }"
      >
        <div class="resize-line"></div>
        <div class="resize-icon">
          <el-icon><DArrowLeft /></el-icon>
          <el-icon><DArrowRight /></el-icon>
        </div>
      </div>

      <!-- 右侧分类表格 -->
      <div class="category-table">
        <h3>{{ currentParentName ? `${currentParentName}的子分类` : '所有分类' }}</h3>
        <el-scrollbar>
          <el-table
            v-loading="loading"
            :data="categoryList"
            style="width: 100%"
            border
            row-key="id"
            table-layout="auto"
          >
          <el-table-column label="ID" prop="id" min-width="80" width="80" />
          <el-table-column label="分类名称" prop="name" min-width="120" />
          <el-table-column label="图片" min-width="100" width="100">
            <template #default="{ row }">
              <el-image
                v-if="row.image"
                :src="row.image"
                fit="cover"
                style="width: 60px; height: 60px"
                :preview-src-list="[row.image]"
              />
              <span v-else>无图片</span>
            </template>
          </el-table-column>
          <el-table-column label="描述" prop="description" min-width="200">
            <template #default="{ row }">
              <span>{{ row.description || '无描述' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="排序" prop="sort_order" min-width="80" width="80" />
          <el-table-column label="商品数量" prop="food_count" min-width="100" width="100" />
          <el-table-column label="是否可见" min-width="100" width="100">
            <template #default="{ row }">
              <el-tag :type="row.is_visible ? 'success' : 'info'">
                {{ row.is_visible ? '可见' : '隐藏' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="200" width="200" fixed="right">
            <template #default="{ row }">
              <el-button 
                type="primary" 
                link 
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button 
                type="primary" 
                link 
                @click="handleAddSub(row)"
              >
                添加子分类
              </el-button>
              <el-popconfirm
                title="确定删除此分类吗？"
                @confirm="handleDelete(row)"
              >
                <template #reference>
                  <el-button 
                    type="danger" 
                    link
                  >
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        </el-scrollbar>

        <!-- 分页控件 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 商家外卖分类列表页面
 * 实现分类的树形展示、列表查询、添加、编辑和删除功能
 */
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { getCategoryList, deleteCategory } from '../api/takeoutCategory';

// 分类数据类型
interface Category {
  id: number;
  name: string;
  description?: string;
  image?: string;
  parent_id: number;
  sort_order: number;
  is_visible: boolean;
  children?: Category[];
}

// 分类列表响应类型
interface CategoryListResponse {
  list: Category[];
  total: number;
  page: number;
  pageSize: number;
}

// 定义API响应类型
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 路由实例
const router = useRouter();

// 分类数据相关
const loading = ref(false);
const categoryList = ref<Category[]>([]);
const categoryTreeData = ref<Category[]>([]);
const currentParentId = ref<number | null>(null);
const currentParentName = ref('');
const searchKeyword = ref('');
const treeRef = ref();

// 拖拽分割线相关
const treeWidth = ref(280); // 初始宽度
const isDragging = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

/**
 * 加载分类列表数据
 */
const loadCategoryList = async () => {
  loading.value = true;
  try {
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value
    };

    if (searchKeyword.value) {
      params.keyword = searchKeyword.value;
    }

    if (currentParentId.value !== null) {
      params.parent_id = currentParentId.value;
    }

    const res = await getCategoryList(params) as CategoryListResponse;
    console.log('获取分类列表数据:', res);
    // 直接使用返回的数据，因为请求已经过拦截器处理
    categoryList.value = res?.list || [];
    total.value = res?.total || 0;
  } catch (error) {
    console.error('加载分类列表失败:', error);
    ElMessage.error('加载分类列表失败');
  } finally {
    loading.value = false;
  }
};

/**
 * 加载分类树数据
 */
const loadCategoryTree = async () => {
  try {
    const res = await getCategoryList({
      include_children: true
    }) as CategoryListResponse;
    console.log('获取分类树数据:', res);
    // 直接使用返回的数据，因为请求已经过拦截器处理
    categoryTreeData.value = res?.list || [];
  } catch (error) {
    console.error('加载分类树失败:', error);
    ElMessage.error('加载分类树失败');
  }
};

/**
 * 处理分类树节点点击事件
 */
const handleNodeClick = (data: Category) => {
  currentParentId.value = data.id;
  currentParentName.value = data.name;
  currentPage.value = 1; // 重置页码
  loadCategoryList();
};

/**
 * 处理搜索按钮点击事件
 */
const handleSearch = () => {
  currentPage.value = 1; // 重置页码
  loadCategoryList();
};

/**
 * 处理分页大小变化
 */
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  loadCategoryList();
};

/**
 * 处理页码变化
 */
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  loadCategoryList();
};

/**
 * 处理新增分类按钮点击事件
 */
const handleAdd = () => {
  router.push('/merchant/takeout/category/add');
};

/**
 * 处理编辑分类按钮点击事件
 */
const handleEdit = (row: Category) => {
  router.push(`/merchant/takeout/category/edit/${row.id}`);
};

/**
 * 处理添加子分类按钮点击事件
 */
const handleAddSub = (row: Category) => {
  router.push(`/merchant/takeout/category/add?parent_id=${row.id}&parent_name=${encodeURIComponent(row.name)}`);
};

/**
 * 处理删除分类按钮点击事件
 */
const handleDelete = async (row: Category) => {
  try {
    const res = await deleteCategory(row.id) as ApiResponse;
    if (res.code === 0) {
      ElMessage.success('删除成功');
      // 重新加载数据
      loadCategoryList();
      loadCategoryTree();
    } else {
      ElMessage.error(res.message || '删除失败');
    }
  } catch (error) {
    console.error('删除分类失败:', error);
    ElMessage.error('删除分类失败');
  }
};

/**
 * 处理鼠标按下事件，开始拖拽
 */
const handleMouseDown = (event: MouseEvent) => {
  isDragging.value = true;
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
  // 防止选中文本
  event.preventDefault();
};

/**
 * 处理鼠标移动事件，调整宽度
 */
const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value) return;
  
  // 获取容器左边缘的位置
  const containerRect = document.querySelector('.main-content')?.getBoundingClientRect();
  if (!containerRect) return;
  
  const minWidth = 180; // 最小宽度
  const maxWidth = window.innerWidth * 0.6; // 最大宽度
  
  // 计算新宽度 (鼠标位置减去容器左边缘位置)
  let newWidth = event.clientX - containerRect.left;
  
  // 限制宽度范围
  if (newWidth < minWidth) newWidth = minWidth;
  if (newWidth > maxWidth) newWidth = maxWidth;
  
  treeWidth.value = newWidth;
};

/**
 * 处理鼠标释放事件，结束拖拽
 */
const handleMouseUp = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
};

// 初始化
onMounted(() => {
  loadCategoryList();
  loadCategoryTree();
});

// 组件销毁时移除事件监听
onBeforeUnmount(() => {
  if (isDragging.value) {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }
});
</script>

<style scoped>
.category-list-container {
  padding: 20px;
}

.top-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-box {
  width: 300px;
}

/* main-content 样式已移至下方与其他相关样式放在一起 */

.main-content {
  position: relative;
  display: flex;
  gap: 0; /* 移除间隙，让分割线紧贴两边 */
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-height: 500px; /* 确保有足够的高度 */
  overflow: hidden;
}

.category-tree {
  width: 280px; /* 初始宽度，被动态重写 */
  min-width: 150px;
  padding-right: 15px;
  transition: width 0.05s ease;
  overflow: hidden;
  border-right: none; /* 移除右边框，由分割线提供视觉分隔 */
  flex-shrink: 0;
}

.resize-handler {
  width: 20px; /* 增加宽度显福分割线 */
  background-color: #f5f7fa;
  border-left: 1px solid #e0e0e0; 
  border-right: 1px solid #e0e0e0;
  cursor: col-resize;
  height: 100%;
  position: relative;
  transition: background-color 0.2s;
  z-index: 10;
  margin: 0;
  flex-shrink: 0; /* 防止被压缩 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.resize-line {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #ffffff;
  transform: translateX(-50%);
}

.resize-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  border-radius: 4px;
  padding: 0px;
  display: flex;
  flex-direction: column;
  color: rgb(0, 0, 0);
}

.resize-icon .el-icon {
  margin: 2px;
}

.resize-handler:hover,
.resize-handler:active {
  background-color: #1890ff;
  border-color: #1890ff;
}

.category-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.node-count {
  color: #909399;
  font-size: 12px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
