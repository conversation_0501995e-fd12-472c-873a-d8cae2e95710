<template>
  <!-- 添加全局加载状态 -->
  <div v-if="pageLoading" class="dashboard-loading">
    <el-icon class="loading-icon"><Loading /></el-icon>
    <p>加载中...</p>
  </div>
  
  <div v-else class="merchant-dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-left">
        <h2>欢迎回来，{{ merchantStore.merchantName }}</h2>
        <p class="shop-status">
          <el-tag :type="merchantStore.isOperating ? 'success' : 'warning'" effect="dark">{{ shopStatusText }}</el-tag>
          <span class="update-time">最后更新时间：{{ formatTime(new Date(), 'HH:mm:ss') }}</span>
        </p>
      </div>
      <div class="welcome-right">
        <el-button 
          :type="merchantStore.isOperating ? 'warning' : 'success'"
          @click="toggleShopStatus"
          :loading="statusToggling"
        >
          <el-icon>
            <component :is="merchantStore.isOperating ? 'TurnOff' : 'Operation'" />
          </el-icon>
          {{ merchantStore.isOperating ? '暂停营业' : '开始营业' }}
        </el-button>
        <el-button type="primary" @click="refreshData" :loading="refreshing">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="openShopSettings">
          <el-icon><Setting /></el-icon>
          店铺设置
        </el-button>
      </div>
    </div>

    <!-- 数据概览区域 -->
    <el-row :gutter="20" class="dashboard-stat-cards">
      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="stat-card today-sales">
          <div class="stat-card-content">
            <div class="stat-card-value">
              <count-to
                :start-val="0"
                :end-val="todaySales"
                :duration="2000"
                :decimals="2"
                separator=","
                prefix="¥"
              />
            </div>
            <div class="stat-card-title">今日销售额</div>
            <div class="stat-card-footer">
              <span class="trend-text">
                <span class="trend-arrow" :class="salesTrend >= 0 ? 'up' : 'down'">
                  <el-icon><component :is="salesTrend >= 0 ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                </span>
                {{ Math.abs(salesTrend) }}%
              </span>
              <span class="trend-desc">较昨日</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="stat-card today-orders">
          <div class="stat-card-content">
            <div class="stat-card-value">
              <count-to
                :start-val="0"
                :end-val="todayOrders"
                :duration="2000"
                separator=","
              />
            </div>
            <div class="stat-card-title">今日订单数</div>
            <div class="stat-card-footer">
              <span class="trend-text">
                <span class="trend-arrow" :class="ordersTrend >= 0 ? 'up' : 'down'">
                  <el-icon><component :is="ordersTrend >= 0 ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                </span>
                {{ Math.abs(ordersTrend) }}%
              </span>
              <span class="trend-desc">较昨日</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="stat-card pending-orders">
          <div class="stat-card-content">
            <div class="stat-card-value">
              <count-to
                :start-val="0"
                :end-val="pendingOrders"
                :duration="2000"
                separator=","
              />
            </div>
            <div class="stat-card-title">待处理订单</div>
            <div class="stat-card-footer">
              <el-button type="primary" link @click="toOrders">立即处理</el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="stat-card low-stock">
          <div class="stat-card-content">
            <div class="stat-card-value">
              <count-to
                :start-val="0"
                :end-val="lowStockProducts"
                :duration="2000"
                separator=","
              />
            </div>
            <div class="stat-card-title">库存不足商品</div>
            <div class="stat-card-footer">
              <el-button type="warning" link @click="toLowStockProducts">查看商品</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="dashboard-charts">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">
              <div class="chart-title">销售趋势</div>
              <div class="chart-actions">
                <el-radio-group v-model="salesChartPeriod" size="small" @change="changeSalesChartPeriod">
                  <el-radio-button label="week">本周</el-radio-button>
                  <el-radio-button label="month">本月</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <div ref="salesChartRef" class="chart"></div>
            <div v-if="loadingCharts" class="chart-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>加载中...</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">
              <div class="chart-title">订单分布</div>
              <div class="chart-actions">
                <el-button-group size="small">
                  <el-button @click="exportOrderData" :loading="exporting">
                    <el-icon><Download /></el-icon>
                    导出数据
                  </el-button>
                  <el-button @click="refreshOrderChart">
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <div ref="orderChartRef" class="chart"></div>
            <div v-if="loadingCharts" class="chart-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>加载中...</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作和订单列表 -->
    <el-row :gutter="20" class="dashboard-bottom">
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <el-card shadow="hover" class="quick-actions-card">
          <template #header>
            <div class="card-header">
              <span>快速操作</span>
            </div>
          </template>
          <div class="quick-actions-grid">
            <div class="quick-action-item" @click="toProductAdd">
              <el-icon class="quick-action-icon"><Plus /></el-icon>
              <span class="quick-action-text">添加商品</span>
            </div>
            <div class="quick-action-item" @click="toOrders">
              <el-icon class="quick-action-icon"><List /></el-icon>
              <span class="quick-action-text">订单管理</span>
            </div>
            <div class="quick-action-item" @click="toProducts">
              <el-icon class="quick-action-icon"><Goods /></el-icon>
              <span class="quick-action-text">商品管理</span>
            </div>
            <div class="quick-action-item" @click="toStatistics">
              <el-icon class="quick-action-icon"><DataAnalysis /></el-icon>
              <span class="quick-action-text">数据分析</span>
            </div>
            <div class="quick-action-item" @click="toSettlements">
              <el-icon class="quick-action-icon"><Money /></el-icon>
              <span class="quick-action-text">结算记录</span>
            </div>
            <div class="quick-action-item" @click="toReviews">
              <el-icon class="quick-action-icon"><ChatDotRound /></el-icon>
              <span class="quick-action-text">评价管理</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <el-card shadow="hover" class="recent-orders-card">
          <template #header>
            <div class="card-header">
              <span>最近订单</span>
              <el-button type="primary" link @click="toOrders">查看全部</el-button>
            </div>
          </template>
          <div v-if="loadingRecentOrders" class="loading-orders">
            <el-skeleton :rows="5" animated />
          </div>
          <div v-else-if="recentOrders.length === 0" class="empty-orders">
            <el-empty description="暂无订单数据" />
          </div>
          <el-table v-else :data="recentOrders" style="width: 100%" size="large">
            <el-table-column prop="orderNo" label="订单号" width="180" />
            <el-table-column prop="userName" label="客户" width="120" />
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag :type="getOrderStatusType(scope.row.status)">
                  {{ getOrderStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="totalAmount" label="金额" width="150">
              <template #default="scope">
                ¥{{ formatAmount(scope.row.totalAmount) }}
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="下单时间" width="180">
              <template #default="scope">
                {{ formatTime(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
              <template #default="scope">
                <el-button type="primary" link size="small" @click="viewOrder(scope.row.id)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, defineComponent } from 'vue';
import { useRouter } from 'vue-router';
//import localforage from 'localforage';
import { ElMessage } from 'element-plus';
import { 
  Refresh, 
  Setting, 
  //ArrowUp, 
  //ArrowDown, 
  Loading, 
  Download, 
  Plus, 
  List, 
  Goods, 
  DataAnalysis, 
  Money, 
  ChatDotRound, 
  //Key,
  //Operation,
  //TurnOff 
} from '@element-plus/icons-vue';
import { useMerchantStore } from '../stores/merchantStore';
import { getOrderList } from '../api';
import { formatTime, formatAmount } from '@/utils/format';
import { ORDER_STATUS } from '../constants';
import type { MerchantOrder } from '../types';


// CountTo 组件 (模拟) - 使用组合式API重写
// 在实际项目中，您可能需要安装第三方库如 vue-count-to 或自己实现
const CountTo = defineComponent({
  props: {
    startVal: { type: Number, default: 0 },
    endVal: { type: Number, default: 100 },
    duration: { type: Number, default: 3000 },
    decimals: { type: Number, default: 0 },
    separator: { type: String, default: ',' },
    prefix: { type: String, default: '' },
    suffix: { type: String, default: '' },
  },
  setup(props) {
    // 明确定义props的类型
    type CountToProps = {
      startVal: number;
      endVal: number;
      duration: number;
      decimals: number;
      separator: string;
      prefix: string;
      suffix: string;
    };
    
    const typedProps = props as CountToProps;
    const displayValue = ref(typedProps.startVal);
    const formattedValue = ref('');
    
    // 格式化值的函数
    const formatValue = () => {
      formattedValue.value = typedProps.prefix + 
        displayValue.value.toFixed(typedProps.decimals).replace(/\d(?=(\d{3})+\.)/g, '$&,') + 
        typedProps.suffix;
    };
    
    // 监听endVal变化
    watch(() => typedProps.endVal, (newVal: number) => {
      displayValue.value = newVal;
      formatValue();
    }, { immediate: true });
    
    return {
      formattedValue
    };
  },
  template: `<span>{{ formattedValue }}</span>`
});

// 路由和商家store
const router = useRouter();
const merchantStore = useMerchantStore();

// 图表引用
const salesChartRef = ref(null);
const orderChartRef = ref(null);

// 加载状态
const pageLoading = ref(true);
const refreshing = ref(false);
const exporting = ref(false);
const loadingCharts = ref(true);
const loadingRecentOrders = ref(true);
//const tokenRefreshing = ref(false);
const statusToggling = ref(false); // 切换经营状态的加载状态

// 图表周期
const salesChartPeriod = ref('week');

// 最近订单
const recentOrders = ref<MerchantOrder[]>([]);

// 定时刷新
let refreshTimer: any = null;

// 统计数据计算属性
const todaySales = computed(() => merchantStore.statistics?.todaySales || 0);
const todayOrders = computed(() => merchantStore.statistics?.todayOrders || 0);
const pendingOrders = computed(() => merchantStore.statistics?.pendingOrders || 0);
const lowStockProducts = computed(() => merchantStore.statistics?.lowStockProducts || 0);

// 趋势数据（模拟）
const salesTrend = ref(12.5);  // 模拟数据：较昨日上涨12.5%
const ordersTrend = ref(-5.2); // 模拟数据：较昨日下降5.2%

// 商铺状态
const shopStatusText = computed(() => {
  if (!merchantStore.merchantInfo) return '加载中...';
  return merchantStore.isOperating ? '营业中' : '暂停营业';
});

/**
 * 组件挂载时执行
 */

onMounted(async () => {
  pageLoading.value = true;
  try {
    console.log('Dashboard mounted, 开始加载数据...');
    
    // 确保merchantStore已经初始化
    if (!merchantStore.isLoggedIn) {
      console.log('商家未登录或token已失效，尝试恢复状态...');
      const success = await merchantStore.retoken();
      if (!success) {
        console.warn('无法恢复商家登录状态');
      }
    }
    
    // 并行加载各项数据
    await Promise.all([
      refreshData().catch(err => console.error('获取统计数据失败:', err)),
      fetchRecentOrders().catch(err => console.error('获取最近订单失败:', err))
    ]);
    
    // 初始化图表
    initCharts();
    
    // 设置自动刷新（每5分钟）
    refreshTimer = setInterval(() => {
      refreshData(false);
    }, 5 * 60 * 1000);
    
    console.log('Dashboard数据加载完成');
  } catch (error) {
    console.error('Dashboard初始化失败:', error);
    ElMessage.error('加载数据失败，请刷新页面重试');
  } finally {
    pageLoading.value = false;
  }
});

/**
 * 组件卸载前清除定时器
 */
onBeforeUnmount(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
});

/**
 * 测试长期token登录
 */
// const testLongTermLogin = async () => {
//   tokenRefreshing.value = true;
//   try {
//     // 检查是否记住登录
//     const remember = await localforage.getItem('merchant_remember');
//     console.log('记住登录状态:', remember);
    
//     // 检查长期token
//     const longTermToken = await localforage.getItem('merchant_long_term_token');
//     console.log('当前长期token:', longTermToken);
    
//     // 执行长期token登录
//     const result = await merchantStore.loginByLongTermTokenAction();
    
//     if (result) {
//       ElMessage.success('长期token登录成功');
//     } else {
//       ElMessage.error(merchantStore.error || '长期token登录失败');
//     }
//   } catch (error: any) {
//     console.error('测试长期token登录出错:', error);
//     ElMessage.error(error.message || '测试过程出错');
//   } finally {
//     tokenRefreshing.value = false;
//   }
// };

/**
 * 切换店铺营业状态
 */
async function toggleShopStatus() {
  try {
    statusToggling.value = true;
    await merchantStore.toggleOperationStatus();
    ElMessage.success(`已${merchantStore.isOperating ? '开始' : '暂停'}营业`);
  } catch (error) {
    console.error('切换经营状态失败:', error);
    ElMessage.error('切换经营状态失败，请稍后再试');
  } finally {
    statusToggling.value = false;
  }
}

/**
 * 刷新数据
 */
const refreshData = async (showMessage = true) => {
  refreshing.value = true;
  try {
    console.log('获取商家统计数据...');
    await merchantStore.fetchStatistics();
    console.log('商家统计数据获取成功');
    if (showMessage) {
      ElMessage.success('数据已更新');
    }
    return true;
  } catch (error: any) {
    console.error('获取统计数据失败:', error);
    if (showMessage) {
      ElMessage.error(error.message || '获取数据失败');
    }
    throw error; // 向上传播错误，让调用者处理
  } finally {
    refreshing.value = false;
  }
};

/**
 * 获取最近订单数据
 */
const fetchRecentOrders = async () => {
  loadingRecentOrders.value = true;
  try {
    console.log('获取最近订单数据...');
    const response: any = await getOrderList({ page: 1, page_size: 5 });
    recentOrders.value = response.list || [];
    console.log('最近订单数据获取成功，数量:', recentOrders.value.length);
    return response;
  } catch (error: any) {
    console.error('获取订单数据失败:', error);
    ElMessage.error(error.message || '获取订单数据失败');
    throw error; // 向上传播错误，让调用者处理
  } finally {
    loadingRecentOrders.value = false;
  }
};

/**
 * 获取图表数据
 */
const fetchChartData = async () => {
  loadingCharts.value = true;
  try {
    // 这里会根据实际API调用获取图表数据
    // const salesData = await getMerchantChartData('sales', salesChartPeriod.value);
    // const orderData = await getMerchantChartData('orders', 'week');
    
    // 更新图表
    updateSalesChart();
    updateOrderChart();
  } catch (error: any) {
    ElMessage.error(error.message || '获取图表数据失败');
  } finally {
    loadingCharts.value = false;
  }
};

/**
 * 初始化图表
 */
const initCharts = () => {
  console.log('初始化仪表盘图表...');
  try {
    // 在实际项目中，这里会使用echarts或其他图表库初始化图表
    // 例如：
    // const salesChart = echarts.init(salesChartRef.value);
    // const orderChart = echarts.init(orderChartRef.value);
    
    // 为演示目的，我们假设已初始化图表
    fetchChartData().catch(err => console.error('获取图表数据失败:', err));
    console.log('图表初始化完成');
  } catch (error) {
    console.error('图表初始化失败:', error);
  }
};

/**
 * 更新销售趋势图表
 */
const updateSalesChart = () => {
  // 在实际项目中，这里会使用echarts或其他图表库更新图表
  // 例如：
  // salesChart.setOption({...});
};

/**
 * 更新订单分布图表
 */
const updateOrderChart = () => {
  // 在实际项目中，这里会使用echarts或其他图表库更新图表
  // 例如：
  // orderChart.setOption({...});
};

/**
 * 切换销售图表周期
 */
const changeSalesChartPeriod = () => {
  fetchChartData();
};

/**
 * 刷新订单图表
 */
const refreshOrderChart = () => {
  fetchChartData();
};

/**
 * 导出订单数据
 */
const exportOrderData = () => {
  exporting.value = true;
  
  setTimeout(() => {
    ElMessage.success('数据导出成功');
    exporting.value = false;
  }, 1500);
};

/**
 * 打开店铺设置
 */
const openShopSettings = () => {
  router.push('/merchant/shop');
};

/**
 * 跳转到商品管理
 */
const toProducts = () => {
  router.push('/merchant/takeout/food/list');
};

/**
 * 跳转到添加商品
 */
const toProductAdd = () => {
  router.push('/merchant/takeout/food/add');
};

/**
 * 跳转到订单管理
 */
const toOrders = () => {
  router.push('/merchant/order/list');
};

/**
 * 跳转到库存不足商品
 */
const toLowStockProducts = () => {
  router.push('/merchant/takeout/food/list?filter=low_stock');
};

/**
 * 跳转到统计分析
 */
const toStatistics = () => {
  router.push('/merchant/statistics');
};

/**
 * 跳转到结算记录
 */
const toSettlements = () => {
  router.push('/merchant/settlements');
};

/**
 * 跳转到评价管理
 */
const toReviews = () => {
  router.push('/merchant/reviews');
};

/**
 * 查看订单详情
 */
const viewOrder = (orderId: number) => {
  router.push(`/merchant/order/detail/${orderId}`);
};

/**
 * 获取订单状态样式类型
 */
const getOrderStatusType = (status: number) => {
  const statusMap: Record<number, string> = {
    10: 'warning',
    20: 'success',
    30: 'primary',
    40: 'info',
    50: 'success',
    60: 'danger',
    70: 'info'
  };
  return statusMap[status] || 'info';
};

/**
 * 获取订单状态文本
 */
const getOrderStatusText = (status: string | number) => {
  const statusValue = typeof status === 'string' ? parseInt(status) : status;
  const statusItem = Object.values(ORDER_STATUS).find(item => item.value === statusValue);
  return statusItem ? statusItem.label : status.toString();
};
</script>

<style lang="scss" scoped>
/* 添加全局加载样式 */
.dashboard-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 9999;
  
  .loading-icon {
    font-size: 48px;
    color: #409EFF;
    margin-bottom: 16px;
    animation: rotate 2s linear infinite;
  }
  
  p {
    font-size: 16px;
    color: #606266;
  }
  
  @keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
}

.merchant-dashboard {
  .welcome-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    
    .welcome-left {
      h2 {
        margin: 0 0 10px;
        font-size: 20px;
        color: #303133;
      }
      
      .shop-status {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #606266;
        
        .update-time {
          margin-left: 15px;
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    .welcome-right {
      display: flex;
      gap: 10px;
    }
  }
  
  .dashboard-stat-cards {
    margin-bottom: 20px;
    
    .stat-card {
      height: 140px;
      
      &.today-sales {
        .stat-card-value {
          color: #67c23a;
        }
      }
      
      &.today-orders {
        .stat-card-value {
          color: #409eff;
        }
      }
      
      &.pending-orders {
        .stat-card-value {
          color: #e6a23c;
        }
      }
      
      &.low-stock {
        .stat-card-value {
          color: #f56c6c;
        }
      }
      
      .stat-card-content {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        
        .stat-card-value {
          font-size: 28px;
          font-weight: bold;
          margin-bottom: 10px;
        }
        
        .stat-card-title {
          font-size: 14px;
          color: #606266;
          margin-bottom: 10px;
        }
        
        .stat-card-footer {
          font-size: 12px;
          color: #909399;
          
          .trend-text {
            margin-right: 5px;
            
            .trend-arrow {
              display: inline-flex;
              align-items: center;
              
              &.up {
                color: #67c23a;
              }
              
              &.down {
                color: #f56c6c;
              }
              
              .el-icon {
                margin-right: 2px;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
  
  .dashboard-charts {
    margin-bottom: 20px;
    
    .chart-card {
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .chart-title {
          font-size: 16px;
          font-weight: bold;
        }
      }
      
      .chart-container {
        height: 340px;
        position: relative;
        
        .chart {
          width: 100%;
          height: 100%;
        }
        
        .chart-loading {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(255, 255, 255, 0.7);
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          
          .el-icon {
            font-size: 24px;
            margin-bottom: 10px;
          }
        }
      }
    }
  }
  
  .dashboard-bottom {
    .quick-actions-card {
      height: 100%;
      
      .quick-actions-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
        
        .quick-action-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 15px;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s;
          
          &:hover {
            background-color: #f5f7fa;
            transform: translateY(-3px);
          }
          
          .quick-action-icon {
            font-size: 24px;
            color: $primary-color;
            margin-bottom: 10px;
          }
          
          .quick-action-text {
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
    
    .recent-orders-card {
      height: 100%;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .loading-orders,
      .empty-orders {
        padding: 30px 0;
      }
    }
  }
}
</style>