<template>
  <div class="category-edit-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2>{{ isEdit ? '编辑分类' : '新增分类' }}</h2>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        :disabled="loading"
      >
        <!-- 基本信息 -->
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入分类名称" />
        </el-form-item>

        <el-form-item label="分类描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>

        <el-form-item label="父级分类" prop="parent_id">
          <el-select
            v-model="formData.parent_id"
            placeholder="请选择父级分类"
            clearable
            filterable
          >
            <el-option label="无父级分类(一级分类)" :value="0" />
            <el-option
              v-for="item in categoryOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="排序权重" prop="sort_order">
          <el-input-number
            v-model="formData.sort_order"
            :min="0"
            :max="9999"
            placeholder="请设置排序权重"
          />
          <div class="input-tip">数值越小越靠前显示</div>
        </el-form-item>

        <el-form-item label="是否可见" prop="is_visible">
          <el-switch v-model="formData.is_visible" />
        </el-form-item>

        <!-- 分类图片上传 -->
        <el-form-item label="分类图片" prop="image">
          <div class="image-uploader-container">
            <!-- 图片预览区域 -->
            <div class="image-preview" v-if="formData.image">
              <el-image
                :src="formData.image"
                fit="cover"
                style="width: 200px; height: 200px"
                :preview-src-list="[formData.image]"
              />
              <div class="image-actions">
                <el-button type="danger" @click="removeImage" size="small">
                  <el-icon><Delete /></el-icon> 删除图片
                </el-button>
              </div>
            </div>

            <!-- 文件上传 -->
            <div v-if="!formData.image">
              <FileUploader
                action="/v1/merchant/upload"
                :file-limit="1"
                :size-limit="2 * 1024 * 1024"
                accept=".jpg,.jpeg,.png,.gif"
                file-usage="takeout_category"
                @success="handleUploadSuccess"
                upload-style="default"
              >
                <template #tip>
                  <p>点击或拖拽图片到此处上传</p>
                  <p>仅支持JPG、PNG格式，文件大小不超过2MB</p>
                </template>
              </FileUploader>
            </div>
          </div>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="loading">保存</el-button>
          <el-button @click="goBack">取消</el-button>
          <el-button 
            v-if="isEdit" 
            type="danger" 
            @click="handleDelete"
            :loading="deleteLoading"
          >删除</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/**
 * 商家外卖分类编辑页面
 * 实现新增和编辑分类的功能，包含图片上传
 */
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Delete } from '@element-plus/icons-vue';
import { FileUploader } from '@/components/common';
import { getCategoryList, getCategoryDetail, createCategory, updateCategory, deleteCategory } from '../api/takeoutCategory';

// 分类数据类型
interface Category {
  id: number;
  name: string;
  description?: string;
  image?: string;
  parent_id: number;
  sort_order: number;
  is_visible: boolean;
  children?: Category[];
}

// 分类列表响应类型
interface CategoryListResponse {
  list: Category[];
  total: number;
  page: number;
  pageSize: number;
}

// 定义API响应类型
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 路由相关
const route = useRoute();
const router = useRouter();

// 表单数据
const formRef = ref();
const loading = ref(false);
const deleteLoading = ref(false);
const categoryOptions = ref<any[]>([]);

// 默认表单数据
const defaultFormData = {
  name: '',
  description: '',
  image: '',
  parent_id: undefined,
  sort_order: 0,
  is_visible: true
};

// 表单数据
interface FormData {
  name: string;
  description: string;
  image: string;
  parent_id: number | undefined;
  sort_order: number;
  is_visible: boolean;
  [key: string]: any;
}

const formData = reactive<FormData>({ ...defaultFormData });

// 编辑模式判断
const isEdit = computed(() => route.params.id !== undefined);

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 20, message: '分类名称长度不能超过20个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '分类描述长度不能超过200个字符', trigger: 'blur' }
  ],
  sort_order: [
    { type: 'number', message: '排序权重必须为数字', trigger: 'blur' }
  ]
};

/**
 * 加载分类选项
 */
const loadCategoryOptions = async () => {
  try {
    const res = await getCategoryList() as CategoryListResponse;
    // 直接使用返回的数据，因为请求已经过拦截器处理
    categoryOptions.value = res?.list || [];
  } catch (error) {
    console.error('加载分类选项失败:', error);
    ElMessage.error('加载分类选项失败');
  }
};

/**
 * 进入编辑状态时加载详情
 */
const loadCategoryDetail = async () => {
  if (!isEdit.value) return;
  
  loading.value = true;
  try {
    const data = await getCategoryDetail(Number(route.params.id)) as Category;
    // 直接使用返回的数据，因为请求已经过拦截器处理
    Object.keys(defaultFormData).forEach(key => {
      if (data[key as keyof Category] !== undefined) {
        formData[key as keyof FormData] = data[key as keyof Category] as any;
      }
    });
  } catch (error) {
    console.error('加载分类详情失败:', error);
    ElMessage.error('加载分类详情失败');
    goBack();
  } finally {
    loading.value = false;
  }
};

/**
 * 根据URL参数设置父级分类
 */
const setParentFromQuery = () => {
  if (!isEdit.value && route.query.parent_id) {
    formData.parent_id = Number(route.query.parent_id);
  }
};

/**
 * 处理图片上传成功事件
 */
const handleUploadSuccess = (response: any) => {
  console.log('上传成功:', response)
  if (response && response.file_url) {
    formData.image = response.file_url;
    ElMessage.success('上传成功');
  }
};

/**
 * 移除已上传的图片
 */
const removeImage = () => {
  formData.image = '';
};

/**
 * 提交表单
 */
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid: boolean) => {
    if (!valid) return;
    
    loading.value = true;
    try {
      // 准备提交数据
      const submitData: any = {};
      Object.keys(formData).forEach(key => {
        // 如果是parent_id且值为0或undefined，设置为0
        if (key === 'parent_id') {
          submitData[key] = formData[key as keyof FormData] === undefined ? 0 : formData[key as keyof FormData];
        } else if (formData[key as keyof FormData] !== undefined) {
          submitData[key] = formData[key as keyof FormData];
        }
      });
      
      // 根据是否编辑模式调用不同接口
      if (isEdit.value) {
        await updateCategory(Number(route.params.id), submitData);
      } else {
        await createCategory(submitData);
      }
      
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功');
      goBack();
    } catch (error: any) {
      console.error(isEdit.value ? '更新分类失败:' : '创建分类失败:', error);
      ElMessage.error(error?.message || (isEdit.value ? '更新分类失败' : '创建分类失败'));
    } finally {
      loading.value = false;
    }
  });
};

/**
 * 处理删除分类
 */
const handleDelete = async () => {
  if (!isEdit.value) return;
  
  try {
    await ElMessageBox.confirm('确定要删除该分类吗？删除后不可恢复。', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    deleteLoading.value = true;
    const res = await deleteCategory(Number(route.params.id)) as ApiResponse;
    if (res.code === 0) {
      ElMessage.success('删除成功');
      goBack();
    } else {
      ElMessage.error(res.message || '删除失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error);
      ElMessage.error('删除分类失败');
    }
  } finally {
    deleteLoading.value = false;
  }
};

/**
 * 返回列表页
 */
const goBack = () => {
  router.push('/merchant/takeout/category/list');
};

// 初始化
onMounted(() => {
  loadCategoryOptions();
  loadCategoryDetail();
  setParentFromQuery();
});
</script>

<style scoped>
.category-edit-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-uploader-container {
  width: 100%;
  max-width: 500px;
}

.image-preview {
  margin-bottom: 20px;
}

.image-actions {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.input-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.2;
}
</style>
