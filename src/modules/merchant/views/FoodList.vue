<!-- 
 * 商家外卖商品列表页面
 * 显示所有外卖商品，支持分页、搜索和状态筛选
 * 提供新增、编辑和删除功能
 -->
<template>
  <div class="food-list-container">
    <el-card class="header-card">
      <div class="page-header">
        <h2>外卖商品管理</h2>
        <div class="header-actions">
          <el-button type="primary" icon="Plus" @click="addFood">新增商品</el-button>
          <el-button type="primary" icon="Refresh" @click="clearFoodCache">清除缓存</el-button>
        </div>
      </div>
    </el-card>
    
    <!-- 搜索和筛选区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="商品名称">
          <el-input v-model="searchForm.keyword" placeholder="请输入商品名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="商品分类">
          <el-select v-model="searchForm.category_id" placeholder="全部分类" clearable>
            <el-option 
              v-for="category in categories" 
              :key="category.id" 
              :label="category.name" 
              :value="category.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
            <el-option label="草稿" :value="FoodStatus.DRAFT"></el-option>
            <el-option label="上架销售中" :value="FoodStatus.ON_SALE"></el-option>
            <el-option label="已下架" :value="FoodStatus.OFF_SALE"></el-option>
            <el-option label="已售罄" :value="FoodStatus.SOLD_OUT"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="searchForm.audit_status" placeholder="全部审核状态" clearable>
            <el-option label="未审核" :value="AuditStatus.PENDING"></el-option>
            <el-option label="审核通过" :value="AuditStatus.APPROVED"></el-option>
            <el-option label="审核拒绝" :value="AuditStatus.REJECTED"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchFoods">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 商品列表 -->
    <el-card class="list-card">
      <el-table
        v-loading="loading"
        :data="foodList"
        border
        style="width: 100%"
        max-height="500"
      >
        <el-table-column label="商品图片" width="100">
          <template #default="scope">
            <el-image 
              :src="scope.row.image || '/images/default-food.png'" 
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 4px;"
            ></el-image>
          </template>
        </el-table-column>
        
        <el-table-column prop="name" label="商品名称" min-width="200" show-overflow-tooltip></el-table-column>
        
        <el-table-column prop="category_name" label="商品分类" width="120"></el-table-column>
        
        <el-table-column prop="price" label="价格" width="100">
          <template #default="scope">
            <span>{{ formatPrice(scope.row.price) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="daily_limit" label="每日限购" width="120"></el-table-column>
        
        <!-- <el-table-column prop="has_specs" label="规格" width="80">
          <template #default="scope">
            <el-tag size="small" v-if="scope.row.has_specs" type="success">有规格</el-tag>
            <el-tag size="small" v-else type="info">无规格</el-tag>
          </template>
        </el-table-column> -->
        
        <el-table-column prop="status" label="商品状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="audit_status" label="审核状态" width="100">
          <template #default="scope">
            <el-tag :type="getAuditStatusTagType(scope.row.audit_status)" size="small">
              {{ getAuditStatusText(scope.row.audit_status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="scope">
            <el-button 
              link 
              type="primary" 
              size="small" 
              @click="editFood(scope.row.id)"
              :disabled="scope.row.status === FoodStatus.ON_SALE"
            >编辑</el-button>
            
            <el-button 
              v-if="scope.row.status === FoodStatus.DRAFT && scope.row.audit_status === AuditStatus.PENDING" 
              link 
              type="success" 
              size="small" 
              @click="submitFood(scope.row.id)"
            >提交审核</el-button>
            
            <el-button 
              v-if="scope.row.status === FoodStatus.ON_SALE && scope.row.audit_status === AuditStatus.APPROVED" 
              link 
              type="warning" 
              size="small" 
              @click="offlineFood(scope.row.id)"
            >下架</el-button>
            
            <el-button 
              v-if="(scope.row.status === FoodStatus.OFF_SALE || scope.row.status === FoodStatus.DRAFT) && scope.row.audit_status === AuditStatus.REJECTED" 
              link 
              type="success" 
              size="small" 
              @click="submitFood(scope.row.id)"
            >重新提交</el-button>
            
            <el-button 
              v-if="scope.row.status === FoodStatus.OFF_SALE && scope.row.audit_status === AuditStatus.APPROVED" 
              link 
              type="success" 
              size="small" 
              @click="publishFood(scope.row.id)"
            >上架</el-button>
            
            <el-button 
              v-if="scope.row.audit_status !== AuditStatus.PENDING && scope.row.status !== FoodStatus.ON_SALE" 
              link 
              type="danger" 
              size="small" 
              @click="confirmDelete(scope.row.id)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessageBox, ElMessage } from 'element-plus';
import { TakeoutFoodStatus, TakeoutFoodAuditStatus } from '../types';
import type { TakeoutFood, TakeoutFoodCategory } from '../types';
import { getFoodList, updateFood, deleteFood, getFoodCategories, enableFood, disableFood, clearFoodCache } from '../api/takeout';

// 商品列表接口
interface FoodListResponse {
  list: TakeoutFood[];
  total: number;
  page: number;
  pageSize: number;
}

// 使用枚举作为常量
const FoodStatus = TakeoutFoodStatus;
const AuditStatus = TakeoutFoodAuditStatus;

// 路由实例
const router = useRouter();

// 页面状态
const loading = ref(false);
const foodList = ref<TakeoutFood[]>([]);
const categories = ref<TakeoutFoodCategory[]>([]);

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category_id: undefined as string | undefined,
  status: undefined as string | undefined,
  audit_status: undefined as number | undefined,
});

// 分页参数
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 加载商品分类
const loadCategories = async () => {
  try {
    const data = await getFoodCategories() as TakeoutFoodCategory[];
    console.log('分类数据:', data);
    // 直接使用返回的数据，因为请求已经过拦截器处理
    categories.value = data || [];
  } catch (error) {
    console.error('加载分类失败:', error);
    ElMessage.error('加载分类失败');
  }
};

// 加载商品列表
const loadFoodList = async () => {
  loading.value = true;
  try {
    // 处理搜索参数，转换类型以匹配 API 期望的格式
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize, // 注意：使用驼峰命名法
      keyword: searchForm.keyword,
      // 将 string 类型转换为 number 类型（如果存在）
      category_id: searchForm.category_id ? Number(searchForm.category_id) : undefined,
      status: searchForm.status,
      audit_status: searchForm.audit_status
    };
    
    // 直接使用返回的数据，因为请求已经过拦截器处理
    const response = await getFoodList(params) as FoodListResponse;
    console.log('商品列表数据:', response);
    
    foodList.value = response?.list || [];
    pagination.total = response?.total || 0;
  } catch (error: any) {
    console.error('加载商品列表失败:', error);
    ElMessage.error(error?.message || '加载商品列表失败');
  } finally {
    loading.value = false;
  }
};

// 查询商品
const searchFoods = () => {
  pagination.page = 1;
  loadFoodList();
};

// 重置查询条件
const resetSearch = () => {
  searchForm.keyword = '';
  searchForm.category_id = undefined;
  searchForm.status = undefined;
  pagination.page = 1;
  loadFoodList();
};

// 格式化价格
const formatPrice = (price: number) => {
  return `¥${price.toFixed(2)}`;
};

// 格式化日期时间
const formatDateTime = (timestamp: string | number) => {
  if (!timestamp) return '-';
  
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取商品状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case FoodStatus.DRAFT: return '草稿';
    case FoodStatus.ON_SALE: return '上架销售中';
    case FoodStatus.OFF_SALE: return '已下架';
    case FoodStatus.SOLD_OUT: return '已售罄';
    default: return '未知';
  }
};

// 获取审核状态文本
const getAuditStatusText = (status: number) => {
  switch (status) {
    case AuditStatus.PENDING: return '未审核';
    case AuditStatus.APPROVED: return '审核通过';
    case AuditStatus.REJECTED: return '审核拒绝';
    default: return '未知';
  }
};

// 获取商品状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case FoodStatus.DRAFT: return 'info';
    case FoodStatus.ON_SALE: return 'success';
    case FoodStatus.OFF_SALE: return 'info';
    case FoodStatus.SOLD_OUT: return 'danger';
    default: return 'info';
  }
};

// 获取审核状态标签类型
const getAuditStatusTagType = (status: number) => {
  switch (status) {
    case AuditStatus.PENDING: return 'warning';
    case AuditStatus.APPROVED: return 'success';
    case AuditStatus.REJECTED: return 'danger';
    default: return 'info';
  }
};

// 新增商品
const addFood = () => {
  router.push('/merchant/takeout/food/add');
};

// 编辑商品
const editFood = (id: number) => {
  router.push(`/merchant/takeout/food/edit/${id}`);
};

// 提交审核
const submitFood = async (id: number) => {
  try {
    await updateFood(id, { status: FoodStatus.OFF_SALE });
    ElMessage.success('商品提交审核成功');
    loadFoodList();
  } catch (error: any) {
    console.error('提交审核失败:', error);
    ElMessage.error(error?.message || '提交审核失败');
  }
};

// 下架商品
const offlineFood = async (id: number) => {
  try {
    await disableFood(id);
    ElMessage.success('商品已下架');
    loadFoodList();
  } catch (error: any) {
    console.error('下架商品失败:', error);
    ElMessage.error(error?.message || '下架商品失败');
  }
};

// 上架商品
const publishFood = async (id: number) => {
  try {
    await enableFood(id);
    ElMessage.success('商品已上架');
    loadFoodList();
  } catch (error: any) {
    console.error('上架商品失败:', error);
    ElMessage.error(error?.message || '上架商品失败');
  }
};

// 确认删除
const confirmDelete = (id: number) => {
  ElMessageBox.confirm(
    '确定要删除这个商品吗？删除后不可恢复。',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await deleteFood(id);
        ElMessage.success('商品已删除');
        loadFoodList(); // 重新加载列表
      } catch (error: any) {
        console.error('删除商品失败:', error);
        ElMessage.error(error?.message || '删除商品失败');
      }
    })
    .catch(() => {
      // 用户取消删除操作
    });
};

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  loadFoodList();
};

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  loadFoodList();
};

// 页面加载时初始化
onMounted(() => {
  loadCategories();
  loadFoodList();
});
</script>

<style scoped lang="scss">
.food-list-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .header-card,
  .search-card,
  .list-card {
    margin-bottom: 20px;
  }
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
  }
  
  .search-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
