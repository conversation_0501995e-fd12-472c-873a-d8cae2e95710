<template>
  <div class="merchant-status">
    <div class="status-container">
      <div class="status-header">
        <img src="/images/logo.png" alt="O_Mall" class="logo" />
        <h1 class="title">商家入驻状态</h1>
      </div>

      <el-card class="status-card">
        <!-- 加载中 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton style="width: 100%" animated>
            <template #template>
              <div style="padding: 20px 0">
                <el-skeleton-item variant="image" style="width: 100%; height: 120px" />
                <div style="padding: 14px">
                  <el-skeleton-item variant="h3" style="width: 50%" />
                  <div style="display: flex; justify-content: space-between; margin-top: 20px">
                    <el-skeleton-item variant="text" style="margin-right: 16px" />
                    <el-skeleton-item variant="text" style="width: 30%" />
                  </div>
                  <div style="margin-top: 16px">
                    <el-skeleton-item variant="text" style="width: 100%" />
                    <el-skeleton-item variant="text" style="width: 100%" />
                    <el-skeleton-item variant="text" style="width: 60%" />
                  </div>
                </div>
              </div>
            </template>
          </el-skeleton>
        </div>

        <!-- 待审核 -->
        <div v-else-if="merchantStatus === MerchantStatus.PENDING" class="status-content">
          <el-result
            icon="info"
            title="申请审核中"
            sub-title="您的商家入驻申请已提交成功，正在等待平台审核，请耐心等待。"
          >
            <template #extra>
              <el-button type="primary" @click="goToHome">返回首页</el-button>
              <el-button @click="contactSupport">联系客服</el-button>
            </template>
          </el-result>
          
          <div class="application-info">
            <h3>申请信息</h3>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="申请店铺名称">{{ merchantInfo?.name || '未知' }}</el-descriptions-item>
              <el-descriptions-item label="申请时间">{{ formatTime(merchantInfo?.applyTime || '') }}</el-descriptions-item>
              <el-descriptions-item label="预计审核完成时间">{{ getExpectedReviewTime(merchantInfo?.applyTime || '') }}</el-descriptions-item>
              <el-descriptions-item label="申请状态">
                <el-tag type="warning">待审核</el-tag>
              </el-descriptions-item>
            </el-descriptions>
            
            <div class="audit-process">
              <h4>审核流程说明</h4>
              <el-steps :active="1" finish-status="success" simple>
                <el-step title="提交申请" description="商家信息提交成功"></el-step>
                <el-step title="资料审核" description="平台正在审核您的资料(1-3个工作日)"></el-step>
                <el-step title="审核结果" description="审核通过后即可开始运营"></el-step>
              </el-steps>
            </div>
            
            <div class="operation-guide">
              <h4>审核期间建议</h4>
              <ul>
                <li>请确保您提供的联系方式正确且畅通，平台可能会与您核实信息</li>
                <li>审核结果将通过短信和邮件通知您</li>
                <li>您可以提前准备商品信息，审核通过后即可上架</li>
                <li>如有疑问，可随时联系客服咨询审核进度</li>
              </ul>
            </div>
            
            <div class="contact-support">
              <p>如有疑问，请联系客服：</p>
              <p><i class="el-icon-phone"></i> ************</p>
              <p><i class="el-icon-message"></i> <EMAIL></p>
            </div>
          </div>
        </div>

        <!-- 已拒绝 -->
        <div v-else-if="merchantStatus === MerchantStatus.REJECTED" class="status-content">
          <el-result
            icon="error"
            title="申请未通过"
            sub-title="很遗憾，您的商家入驻申请未能通过审核。您可以根据拒绝原因修改信息后重新提交申请。"
          >
            <template #extra>
              <el-button type="primary" @click="reapply">重新申请</el-button>
              <el-button @click="goToHome">返回首页</el-button>
            </template>
          </el-result>
          
          <div class="application-info">
            <h3>申请信息</h3>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="申请店铺名称">{{ merchantInfo?.name || '未知' }}</el-descriptions-item>
              <el-descriptions-item label="申请时间">{{ formatTime(merchantInfo?.applyTime || '') }}</el-descriptions-item>
              <el-descriptions-item label="拒绝时间">{{ formatTime(merchantInfo?.updateTime || '') }}</el-descriptions-item>
              <el-descriptions-item label="申请状态">
                <el-tag type="danger">已拒绝</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="拒绝原因">
                <div class="reject-reason">{{ merchantInfo?.rejectReason || '未提供拒绝原因' }}</div>
              </el-descriptions-item>
            </el-descriptions>
            
            <div class="contact-support">
              <p>如有疑问，请联系客服：</p>
              <p><i class="el-icon-phone"></i> ************</p>
              <p><i class="el-icon-message"></i> <EMAIL></p>
            </div>
          </div>
        </div>

        <!-- 已暂停 -->
        <div v-else-if="merchantStatus === MerchantStatus.SUSPENDED" class="status-content">
          <el-result
            icon="warning"
            title="店铺已暂停"
            sub-title="您的店铺已被平台暂停服务，请联系平台客服了解详细情况。"
          >
            <template #extra>
              <el-button type="primary" @click="contactSupport">联系客服</el-button>
              <el-button @click="goToHome">返回首页</el-button>
            </template>
          </el-result>
          
          <div class="application-info">
            <h3>店铺信息</h3>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="店铺名称">{{ merchantInfo?.name || '未知' }}</el-descriptions-item>
              <el-descriptions-item label="暂停时间">{{ formatTime(merchantInfo?.updateTime || '') }}</el-descriptions-item>
              <el-descriptions-item label="店铺状态">
                <el-tag type="danger">已暂停</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="暂停原因">
                <div class="reject-reason">{{ merchantInfo?.rejectReason || '未提供暂停原因' }}</div>
              </el-descriptions-item>
            </el-descriptions>
            
            <div class="contact-support">
              <p>如需恢复店铺，请联系客服：</p>
              <p><i class="el-icon-phone"></i> ************</p>
              <p><i class="el-icon-message"></i> <EMAIL></p>
            </div>
          </div>
        </div>

        <!-- 无申请记录 -->
        <div v-else-if="!merchantInfo" class="status-content">
          <el-result
            icon="warning"
            title="未找到申请记录"
            sub-title="您尚未提交商家入驻申请或申请记录已被删除。"
          >
            <template #extra>
              <el-button type="primary" @click="goToApply">立即申请入驻</el-button>
              <el-button @click="goToHome">返回首页</el-button>
            </template>
          </el-result>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useMerchantStore } from '../stores/merchantStore';
import { formatTime, addDays } from '@/utils/format';
import { MerchantStatus } from '../types';

// 定义商家信息接口
// interface MerchantInfo {
//   name?: string;
//   applyTime?: string | number | Date;
//   updateTime?: string | number | Date;
//   rejectReason?: string;
//   status?: number;
//   audit_status?: number;
// }

// 路由和商家store
const router = useRouter();
const merchantStore = useMerchantStore();

// 加载状态
const loading = ref(true);

// 商家信息
const merchantInfo = computed(() => merchantStore.merchantInfo);

// 商家状态
const merchantStatus = computed(() => merchantStore.merchantStatus);

// 加载商家信息
onMounted(async () => {
  try {
    // 如果已经有商家信息，则不需要再次获取
    if (!merchantStore.merchantInfo) {
      await merchantStore.fetchMerchantInfo();
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取商家信息失败');
  } finally {
    loading.value = false;
  }
});

/**
 * 前往首页
 */
const goToHome = () => {
  router.push('/');
};

/**
 * 前往申请页面
 */
const goToApply = () => {
  router.push('/merchant/apply');
};

/**
 * 重新申请
 */
const reapply = () => {
  router.push('/merchant/apply');
};

/**
 * 联系客服
 */
const contactSupport = () => {
  // 这里可以实现联系客服的逻辑，比如打开聊天窗口
  ElMessage.info('正在连接客服，请稍候...');
};

/**
 * 计算预计审核完成时间（申请时间后的1-3个工作日）
 * @param applyTime 申请时间
 * @returns 格式化的预计审核完成时间
 */
const getExpectedReviewTime = (applyTime: string | number | Date) => {
  if (!applyTime) return '未知';
  // 预计审核结束时间为申请时间+3个工作日
  const reviewEndDate = addDays(applyTime, 3);
  return formatTime(reviewEndDate) + ' 前';
};
</script>

<style lang="scss" scoped>
.merchant-status {
  padding: 30px 0;
  background-color: #f5f7fa;
  min-height: 100vh;
  
  .status-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
    
    .status-header {
      text-align: center;
      margin-bottom: 30px;
      
      .logo {
        height: 60px;
        margin-bottom: 15px;
      }
      
      .title {
        font-size: 24px;
        color: $primary-color;
        margin: 0;
      }
    }
    
    .status-card {
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      
      .loading-container {
        padding: 40px 20px;
      }
      
      .status-content {
        padding: 20px 0;
        
        .application-info {
          margin-top: 40px;
          padding: 0 40px;
          
          h3 {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            color: #333;
          }
          
          h4 {
            margin: 20px 0 15px;
            color: #409EFF;
            font-weight: 600;
          }
          
          .reject-reason {
            color: #f56c6c;
            line-height: 1.6;
          }
          
          .audit-process {
            margin-top: 25px;
            padding: 15px;
            background-color: #ecf5ff;
            border-radius: 4px;
            border-left: 4px solid #409EFF;
          }
          
          .operation-guide {
            margin-top: 25px;
            padding: 15px;
            background-color: #f0f9eb;
            border-radius: 4px;
            border-left: 4px solid #67c23a;
            
            ul {
              padding-left: 20px;
              margin: 10px 0;
              
              li {
                line-height: 1.8;
                color: #606266;
                margin-bottom: 8px;
              }
            }
          }
          
          .contact-support {
            margin-top: 30px;
            padding: 15px;
            background-color: #f5f7fa;
            border-radius: 4px;
            
            p {
              margin: 5px 0;
              color: #606266;
            }
          }
        }
      }
    }
  }
}
</style>