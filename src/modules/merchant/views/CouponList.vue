<template>
  <div class="coupon-list-container">
    <el-card class="list-card">
      <template #header>
        <div class="card-header">
          <h2>优惠券管理</h2>
          <div>
            <el-dropdown @command="handleAddDropdown" split-button type="primary" @click="goToAdd(COUPON_TYPES.AMOUNT)">
              <el-icon class="el-icon--left"><Plus /></el-icon>
              新建优惠券
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="COUPON_TYPES.AMOUNT">满减券</el-dropdown-item>
                  <el-dropdown-item :command="COUPON_TYPES.DISCOUNT">折扣券</el-dropdown-item>
                  <el-dropdown-item :command="COUPON_TYPES.EXCHANGE">兑换券</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="优惠券名称:">
            <el-input v-model="searchForm.keyword" placeholder="输入关键词搜索" clearable />
          </el-form-item>
          <el-form-item label="优惠券类型:">
            <el-select v-model="searchForm.type" placeholder="全部类型" clearable>
              <el-option label="满减券" :value="COUPON_TYPES.AMOUNT" />
              <el-option label="折扣券" :value="COUPON_TYPES.DISCOUNT" />
              <el-option label="兑换券" :value="COUPON_TYPES.EXCHANGE" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态:">
            <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
              <el-option label="待发布" :value="COUPON_STATUS.PENDING" />
              <el-option label="进行中" :value="COUPON_STATUS.ACTIVE" />
              <el-option label="已结束" :value="COUPON_STATUS.ENDED" />
              <el-option label="已暂停" :value="COUPON_STATUS.PAUSED" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
            <el-button @click="resetSearch" :icon="Refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="couponList" style="width: 100%" border stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="优惠券名称" min-width="140" show-overflow-tooltip />
        <el-table-column prop="description" label="优惠券描述" min-width="180" show-overflow-tooltip />
        <el-table-column label="优惠券类型" width="120">
          <template #default="scope">
            <el-tag v-if="scope.row.type === COUPON_TYPES.AMOUNT" type="danger">满减券</el-tag>
            <el-tag v-else-if="scope.row.type === COUPON_TYPES.DISCOUNT" type="success">折扣券</el-tag>
            <el-tag v-else-if="scope.row.type === COUPON_TYPES.EXCHANGE" type="warning">兑换券</el-tag>
            <el-tag v-else>其他类型</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="优惠金额" width="120">
          <template #default="scope">
            <span v-if="scope.row.type === COUPON_TYPES.AMOUNT">{{ scope.row.amount }}元</span>
            <span v-else-if="scope.row.type === COUPON_TYPES.DISCOUNT">{{ scope.row.amount }}折</span>
            <span v-else-if="scope.row.type === COUPON_TYPES.EXCHANGE">兑换券</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="使用门槛" width="120">
          <template #default="scope">
            <span v-if="scope.row.min_order_amount > 0">满{{ scope.row.min_order_amount }}元</span>
            <span v-else>无门槛</span>
          </template>
        </el-table-column>
        <el-table-column label="已发放/总数" width="120">
          <template #default="scope">
            <span>{{ scope.row.issued_count || 0 }}/{{ scope.row.total_limit || '不限' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已使用/每人限制" width="140">
          <template #default="scope">
            <span>{{ scope.row.used_count || 0 }}/{{ scope.row.per_user_limit || '不限' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="240">
          <template #default="scope">
            <div>{{ formatDate(scope.row.start_time) }}</div>
            <div>{{ formatDate(scope.row.end_time) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.status === COUPON_STATUS.PENDING" type="warning">待发布</el-tag>
            <el-tag v-else-if="scope.row.status === COUPON_STATUS.ACTIVE" type="success">已发布</el-tag>
            <el-tag v-else-if="scope.row.status === COUPON_STATUS.ENDED" type="info">已结束</el-tag>
            <el-tag v-else-if="scope.row.status === COUPON_STATUS.PAUSED" type="danger">已暂停</el-tag>
            <el-tag v-else-if="isExpired(scope.row)" type="info">已过期</el-tag>
            <el-tag v-else-if="isFuture(scope.row)" type="warning">未开始</el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-button size="small" @click="goToEdit(scope.row.id)" type="primary" :icon="Edit">编辑</el-button>
              <el-button
                v-if="scope.row.status === COUPON_STATUS.PENDING"
                size="small"
                type="success"
                @click="handlePublish(scope.row)"
                :icon="VideoPlay"
              >发布</el-button>
              <el-button
                v-else-if="scope.row.status === COUPON_STATUS.ACTIVE"
                size="small"
                type="warning"
                @click="handleDisable(scope.row)"
                :icon="VideoPause"
              >暂停</el-button>
              <el-button
                v-else-if="scope.row.status === COUPON_STATUS.PAUSED && !isExpired(scope.row)"
                size="small"
                type="success"
                @click="handleEnable(scope.row)"
                :icon="VideoPlay"
              >启用</el-button>
              <el-button
                size="small"
                type="info"
                @click="handleIssue(scope.row)"
                :icon="Share"
                :disabled="scope.row.status !== COUPON_STATUS.ACTIVE"
              >发放</el-button>
              <el-button size="small" type="danger" @click="handleDelete(scope.row)" :icon="Delete">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/**
 * 商家优惠券列表页面
 * 提供优惠券的列表展示、搜索、分页和状态管理等功能
 */
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Search, Refresh, VideoPause, VideoPlay, Share } from '@element-plus/icons-vue'
import { getCouponList, enableCoupon, disableCoupon, deleteCoupon, publishCoupon } from '../api/coupon'
import { formatDateTime } from '@/utils/dateUtils'
import { COUPON_TYPES } from '../constants/promotion'

// 优惠券状态常量（根据后端实际返回值调整）
const COUPON_STATUS = {
  ACTIVE: 1,     // 已发布/进行中（后端返回status=1表示已发布）
  PENDING: 0,    // 待发布（草稿状态）
  ENDED: 3,      // 已结束
  PAUSED: 4      // 已暂停
}

const router = useRouter()
const loading = ref(false)
const couponList = ref<any[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索表单
const searchForm = ref({
  keyword: '',
  type: null as number | null,
  status: null as number | null,
})

// 获取优惠券数据
const fetchCoupons = async () => {
  loading.value = true
  try {
    const data: any = await getCouponList({
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchForm.value.keyword || undefined,
      type: searchForm.value.type || undefined,
      status: searchForm.value.status || undefined
    })
    // 响应拦截器已处理成功响应，直接使用返回的data
    couponList.value = data.list || []
    total.value = data.total || 0
  } catch (error: any) {
    // 响应拦截器已处理错误响应并显示错误消息
    console.error('获取优惠券列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 日期格式化
const formatDate = (date: string) => {
  return date ? formatDateTime(new Date(date)) : '--'
}

// 检查优惠券是否过期
const isExpired = (coupon: any) => {
  return new Date(coupon.end_time) < new Date()
}

// 检查优惠券是否未开始
const isFuture = (coupon: any) => {
  return new Date(coupon.start_time) > new Date()
}

/**
 * 新建优惠券处理
 */
const handleAddDropdown = (command: number) => {
  goToAdd(command)
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchCoupons()
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    keyword: '',
    type: null,
    status: null,
  }
  currentPage.value = 1
  fetchCoupons()
}

// 页码变化处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchCoupons()
}

// 当前页变化处理
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchCoupons()
}

// 跳转到添加页面
const goToAdd = (type: number) => {
  router.push(`/merchant/coupon/add?type=${type}`)
}

// 跳转到编辑页面
const goToEdit = (id: number) => {
  router.push(`/merchant/coupon/edit/${id}`)
}

// 处理发布优惠券
const handlePublish = async (coupon: any) => {
  try {
    await publishCoupon(coupon.id)
    // 响应拦截器已处理成功响应
    ElMessage.success('优惠券已发布')
    fetchCoupons()
  } catch (error: any) {
    // 响应拦截器已处理错误响应并显示错误消息
    console.error('发布优惠券失败:', error)
  }
}

// 处理启用优惠券
const handleEnable = async (coupon: any) => {
  try {
    await enableCoupon(coupon.id)
    // 响应拦截器已处理成功响应
    ElMessage.success('优惠券已启用')
    fetchCoupons()
  } catch (error: any) {
    // 响应拦截器已处理错误响应并显示错误消息
    console.error('启用优惠券失败:', error)
  }
}

// 处理暂停优惠券
const handleDisable = async (coupon: any) => {
  try {
    await disableCoupon(coupon.id)
    // 响应拦截器已处理成功响应
    ElMessage.success('优惠券已暂停')
    fetchCoupons()
  } catch (error: any) {
    // 响应拦截器已处理错误响应并显示错误消息
    console.error('暂停优惠券失败:', error)
  }
}

// 处理发放优惠券
const handleIssue = (coupon: any) => {
  router.push(`/merchant/coupon/issue/${coupon.id}`)
}

// 处理删除优惠券
const handleDelete = (coupon: any) => {
  ElMessageBox.confirm(
    `确定要删除优惠券 "${coupon.name}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确认删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await deleteCoupon(coupon.id)
      // 响应拦截器已处理成功响应
      ElMessage.success('优惠券已删除')
      fetchCoupons()
    } catch (error: any) {
      // 响应拦截器已处理错误响应并显示错误消息
      console.error('删除优惠券失败:', error)
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 页面初始化时获取数据
onMounted(() => {
  fetchCoupons()
  // 调试状态映射
  debugStatusMapping()
})

// 调试函数：验证状态映射是否正确
const debugStatusMapping = () => {
  console.log('=== 优惠券状态映射调试 ===')
  console.log('COUPON_STATUS.PENDING:', COUPON_STATUS.PENDING) // 应该是 0
  console.log('COUPON_STATUS.ACTIVE:', COUPON_STATUS.ACTIVE)   // 应该是 1
  console.log('COUPON_STATUS.ENDED:', COUPON_STATUS.ENDED)     // 应该是 3
  console.log('COUPON_STATUS.PAUSED:', COUPON_STATUS.PAUSED)   // 应该是 4

  // 模拟后端返回的数据
  const mockCoupon = { status: 1, status_text: "已发布" }
  console.log('后端返回status=1时，前端判断为:',
    mockCoupon.status === COUPON_STATUS.ACTIVE ? '已发布' :
    mockCoupon.status === COUPON_STATUS.PENDING ? '待发布' : '其他状态')
}
</script>

<style scoped>
.coupon-list-container {
  padding: 20px;
}

.list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
