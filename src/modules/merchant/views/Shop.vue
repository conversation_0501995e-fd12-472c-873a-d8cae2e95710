<template>
  <div class="shop-management">
    <el-card class="shop-info-card">
      <template #header>
        <div class="card-header">
          <h2>店铺信息</h2>
          <el-button type="primary" size="small" @click="editShopInfo">编辑信息</el-button>
        </div>
      </template>
      <div class="shop-info">
        <div class="shop-logo">
          <el-avatar :size="100" :src="shopInfo.logo" />
        </div>
        <div class="shop-details">
          <h3>{{ shopInfo.name }}</h3>
          <p><strong>店铺类型：</strong>{{ shopInfo.type }}</p>
          <p><strong>经营类目：</strong>{{ shopInfo.category }}</p>
          <p><strong>创建时间：</strong>{{ formatTime(shopInfo.createdAt) }}</p>
          <p><strong>店铺评分：</strong>
            <el-rate v-model="shopInfo.rating" disabled text-color="#ff9900" />
            <span class="rating-value">{{ shopInfo.rating.toFixed(1) }}</span>
          </p>
        </div>
      </div>
    </el-card>

    <el-card class="shop-stats-card">
      <template #header>
        <div class="card-header">
          <h2>店铺数据</h2>
          <el-select v-model="timeRange" placeholder="选择时间范围" size="small">
            <el-option label="今日" value="today" />
            <el-option label="本周" value="week" />
            <el-option label="本月" value="month" />
            <el-option label="全部" value="all" />
          </el-select>
        </div>
      </template>
      <div class="stats-grid">
        <div class="stat-item">
          <h4>总订单数</h4>
          <div class="stat-value">{{ shopStats.totalOrders }}</div>
        </div>
        <div class="stat-item">
          <h4>总销售额</h4>
          <div class="stat-value">¥{{ shopStats.totalSales.toFixed(2) }}</div>
        </div>
        <div class="stat-item">
          <h4>商品数量</h4>
          <div class="stat-value">{{ shopStats.productCount }}</div>
        </div>
        <div class="stat-item">
          <h4>访客数</h4>
          <div class="stat-value">{{ shopStats.visitorCount }}</div>
        </div>
      </div>
    </el-card>

    <el-dialog v-model="editDialogVisible" title="编辑店铺信息" width="500px">
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="店铺名称">
          <el-input v-model="editForm.name" />
        </el-form-item>
        <el-form-item label="店铺LOGO">
          <el-upload
            class="avatar-uploader"
            action="/api/merchant/upload"
            :show-file-list="false"
            :on-success="handleLogoSuccess"
          >
            <img v-if="editForm.logo" :src="editForm.logo" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="店铺描述">
          <el-input v-model="editForm.description" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="editForm.phone" />
        </el-form-item>
        <el-form-item label="经营类目">
          <el-select v-model="editForm.category" placeholder="选择经营类目">
            <el-option label="服装" value="clothing" />
            <el-option label="电子产品" value="electronics" />
            <el-option label="食品" value="food" />
            <el-option label="家居" value="home" />
            <el-option label="美妆" value="beauty" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveShopInfo">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { formatTime } from '@/utils/format'

// 店铺信息
const shopInfo = reactive({
  name: '优品商城',
  logo: 'https://placeholder.pics/svg/100',
  type: '个人店铺',
  category: '电子产品',
  createdAt: new Date('2023-01-15'),
  rating: 4.8
})

// 店铺统计数据
const shopStats = reactive({
  totalOrders: 256,
  totalSales: 25680.50,
  productCount: 48,
  visitorCount: 1256
})

// 时间范围选择
const timeRange = ref('month')

// 编辑店铺信息
const editDialogVisible = ref(false)
const editForm = reactive({
  name: '',
  logo: '',
  description: '',
  phone: '',
  category: ''
})

function editShopInfo() {
  // 复制当前店铺信息到表单
  editForm.name = shopInfo.name
  editForm.logo = shopInfo.logo
  editForm.description = '专注于高品质电子产品销售'
  editForm.phone = '13800138000'
  editForm.category = shopInfo.category
  
  editDialogVisible.value = true
}

function handleLogoSuccess(res: any) {
  editForm.logo = res.url
}

function saveShopInfo() {
  // 这里应该有API请求保存数据
  // 模拟保存成功
  shopInfo.name = editForm.name
  shopInfo.logo = editForm.logo
  shopInfo.category = editForm.category
  
  ElMessage.success('店铺信息更新成功')
  editDialogVisible.value = false
}

onMounted(() => {
  // 这里应该有API请求获取店铺信息和统计数据
  console.log('Shop component mounted')
})
</script>

<style scoped lang="scss">
.shop-management {
  padding: 20px;
  
  .shop-info-card, .shop-stats-card {
    margin-bottom: 20px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h2 {
      margin: 0;
      font-size: 18px;
    }
  }
  
  .shop-info {
    display: flex;
    gap: 20px;
    
    .shop-details {
      h3 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 20px;
      }
      
      p {
        margin: 8px 0;
      }
      
      .rating-value {
        margin-left: 8px;
        color: #ff9900;
      }
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    
    .stat-item {
      text-align: center;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 4px;
      
      h4 {
        margin: 0 0 10px 0;
        color: #606266;
        font-size: 14px;
      }
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #409EFF;
      }
    }
  }
  
  .avatar-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
      
      &:hover {
        border-color: var(--el-color-primary);
      }
    }
    
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 100px;
      height: 100px;
      text-align: center;
      line-height: 100px;
    }
    
    .avatar {
      width: 100px;
      height: 100px;
      display: block;
    }
  }
}

@media (max-width: 768px) {
  .shop-management {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .shop-info {
      flex-direction: column;
    }
  }
}
</style>