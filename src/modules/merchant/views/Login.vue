<template>
  <div class="admin-login">
    <div class="login-container">
      <div class="login-header">
        <img v-if="systemInfo?.siteLogo" :src="adjustLinkProtocol(systemInfo?.siteLogo)" alt="siteLogo" class="logo" />
        <h1 class="title">{{ systemInfo?.siteName || 'O_Mall 商家中心' }}</h1>
      </div>

      <AppCard class="login-form-card">
        <h2 class="form-title">商家登录</h2>
        
        <!-- 登录方式选项卡 -->
        <el-tabs v-model="activeTab" class="login-tabs">

          <!-- 账号密码登录选项卡 -->
          <el-tab-pane label="账号密码登录" name="password">
            <AppForm
              ref="passwordFormRef"
              :model="passwordForm"
              :rules="passwordRules"
              label-position="top"
              @keyup.enter="handlePasswordSubmit"
            >
              <AppFormItem label="用户名" prop="username">
                <AppInput
                  v-model="passwordForm.username"
                  placeholder="请输入用户名/手机号"
                  prefix-icon="User"
                />
              </AppFormItem>

              <AppFormItem label="密码" prop="password">
                <AppInput
                  v-model="passwordForm.password"
                  type="password"
                  placeholder="请输入密码"
                  prefix-icon="Lock"
                  show-password
                />
              </AppFormItem>

              <AppFormItem>
                <el-checkbox v-model="rememberUsername">记住用户名</el-checkbox>
              </AppFormItem>
              <AppFormItem>
                <el-checkbox v-model="rememberMe">30天内免登录</el-checkbox>
              </AppFormItem>

              <AppFormItem>
                <el-checkbox v-model="agreePrivacyPolicy" :disabled="loading">
                  我已阅读并同意
                  <el-button link type="primary" @click.stop="showPrivacyPolicy">《隐私协议》</el-button>
                </el-checkbox>
              </AppFormItem>

              <div class="form-actions">
                <AppButton
                  type="primary"
                  :loading="loading"
                  class="submit-btn"
                  :disabled="!agreePrivacyPolicy"
                  @click="handlePasswordSubmit"
                >
                  登录
                </AppButton>
              </div>
            </AppForm>
          </el-tab-pane>
          
          <!-- 手机验证码登录选项卡 -->
          <el-tab-pane label="手机验证码登录" name="sms">
            <AppForm
              ref="smsFormRef"
              :model="smsForm"
              :rules="smsRules"
              label-position="top"
              @keyup.enter="handleSmsSubmit"
            >
              <AppFormItem label="手机号" prop="phone">
                <AppInput
                  v-model="smsForm.phone"
                  placeholder="请输入手机号"
                  prefix-icon="Iphone"
                />
              </AppFormItem>

              <AppFormItem label="验证码" prop="code">
                <div class="verification-code-input">
                  <AppInput
                    v-model="smsForm.code"
                    placeholder="请输入验证码"
                    prefix-icon="Key"
                  />
                  <AppButton
                    type="primary"
                    class="send-code-btn"
                    :disabled="!canSendCode || smsLoading"
                    @click.prevent="sendVerificationCode"
                  >
                    {{ sendCodeText }}
                  </AppButton>
                </div>
              </AppFormItem>

              <AppFormItem>
                <el-checkbox v-model="rememberMe">30天内免登录</el-checkbox>
              </AppFormItem>

              <AppFormItem>
                <el-checkbox v-model="agreePrivacyPolicy" :disabled="smsLoading">
                  我已阅读并同意
                  <el-button link type="primary" @click.stop="showPrivacyPolicy">《隐私协议》</el-button>
                </el-checkbox>
              </AppFormItem>

              <div class="form-actions">
                <AppButton
                  type="primary"
                  :loading="smsLoading"
                  class="submit-btn"
                  :disabled="!agreePrivacyPolicy"
                  @click="handleSmsSubmit"
                >
                  登录
                </AppButton>
              </div>
            </AppForm>
          </el-tab-pane>
        </el-tabs>

        <div class="login-footer">
          <p>还没有商家账号？</p>
          <AppButton
            type="default"
            class="register-btn"
            @click="toApply"
            text
          >
            立即入驻 <el-icon><ArrowRight /></el-icon>
          </AppButton>
        </div>
      </AppCard>
    </div>
  </div>

  <!-- 隐私协议弹窗 -->
  <el-dialog
    v-model="privacyDialogVisible"
    title="隐私协议"
    width="60%"
    :close-on-click-modal="false"
  >
    <div v-if="privacyLoading" class="privacy-loading">
      <el-skeleton :rows="10" animated />
    </div>
    <div v-else-if="privacyError" class="privacy-error">
      <el-empty description="加载隐私协议失败，请稍后再试" />
    </div>
    <div v-else class="privacy-content">
      <h3>{{ privacyPolicy.version }} ({{ privacyPolicy.last_updated }})</h3>
      <div v-html="privacyPolicy.content"></div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="privacyDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="agreeToPrivacyPolicy">
          同意并继续
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useMerchantStore } from '../stores/merchantStore';
import { ElMessage } from 'element-plus';
import { getPrivacyPolicy } from '../api/system';
import type { PrivacyPolicyInfo } from '../api/system';
import { MerchantStatus } from '../types';
import AppCard from '@/components/base/AppCard.vue';
import AppForm from '@/components/form/AppForm.vue';
import AppFormItem from '@/components/form/AppFormItem.vue';
import AppInput from '@/components/form/AppInput.vue';
import AppButton from '@/components/base/AppButton.vue';
import { useSystemStore } from '@/stores/systemStore';
import { adjustLinkProtocol } from '@/utils/format';

const systemStore = useSystemStore();
const systemInfo = computed(() => systemStore.systemInfo);

const router = useRouter();
const passwordFormRef = ref();
const smsFormRef = ref();
const loading = ref(false);
const smsLoading = ref(false);
const rememberMe = ref(false);
const rememberUsername = ref(false);
const agreePrivacyPolicy = ref(false);
const activeTab = ref('password'); // 默认使用密码登录方式

// 验证码发送相关
const countdown = ref(0);
const canSendCode = computed(() => {
  // 检查手机号格式是否正确
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(smsForm.phone) && countdown.value === 0;
});
const sendCodeText = computed(() => {
  return countdown.value > 0 ? `重新发送(${countdown.value}s)` : '发送验证码';
});

// 隐私协议相关
const privacyDialogVisible = ref(false);
const privacyLoading = ref(false);
const privacyError = ref(false);
const privacyPolicy = reactive<PrivacyPolicyInfo>({
  content: '',
  last_updated: '',
  version: ''
});
const merchantStore = useMerchantStore();

// 跳转到商家入驻申请页面
const toApply = () => {
  router.push('/merchant/apply');
};
// 密码登录表单
const passwordForm = reactive({
  username: '',
  password: 'admin123'
});

// 验证码登录表单
const smsForm = reactive({
  phone: '',
  code: ''
});

// 密码登录表单校验规则
const passwordRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在3到20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在6到20个字符', trigger: 'blur' }
  ]
};

// 验证码登录表单校验规则
const smsRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, max: 6, message: '验证码长度不正确', trigger: 'blur' }
  ]
};

// 显示隐私协议弹窗
const showPrivacyPolicy = async (e: Event) => {
  e.preventDefault();
  privacyDialogVisible.value = true;
  
  if (!privacyPolicy.content) {
    await fetchPrivacyPolicy();
  }
};

// 获取隐私协议内容
const fetchPrivacyPolicy = async () => {
  privacyLoading.value = true;
  privacyError.value = false;
  
  try {
    const response = await getPrivacyPolicy();
    // 处理隐私协议数据 - 直接使用返回的数据，无需获取.data
    Object.assign(privacyPolicy, response);
  } catch (error) {
    console.error('获取隐私协议失败:', error);
    privacyError.value = true;
    ElMessage.error('获取隐私协议失败，请稍后再试');
  } finally {
    privacyLoading.value = false;
  }
};

// 同意隐私协议
const agreeToPrivacyPolicy = () => {
  agreePrivacyPolicy.value = true;
  privacyDialogVisible.value = false;
};

// 初始化路由并根据商家状态跳转到对应页面
const initRouteAndNavigate = async () => {
  try {
    console.log('开始初始化路由并跳转...');
    
    // 动态导入路由初始化函数
    const { initializeRoutes } = await import('@/router');
    
    // 初始化路由确保路由表完整
    await initializeRoutes();
    
    // 显示成功消息
    ElMessage.success('登录成功，正在跳转...');
    
    // 根据商家状态决定跳转目标
    const targetPath = determineRedirectPath();
    
    // 使用延迟跳转，确保路由系统有足够时间准备
    setTimeout(() => {
      // 使用原生导航而不是Vue Router，避免触发路由守卫
      window.location.href = window.location.origin + targetPath;
      console.log(`跳转到${targetPath}完成`);
    }, 800);
    
  } catch (error) {
    console.error('路由初始化失败:', error);
    ElMessage.error('系统错误，请重试');
  }
};

// 根据商家状态决定跳转路径
const determineRedirectPath = () => {
  // 检查商家token是否存在
  if (!merchantStore.token) {
    console.log('未获取到token信息，跳转到状态页面');
    return '/merchant/status';
  }
  
  // 检查商家状态
  const status = merchantStore.merchantStatus;
  const audit_status = merchantStore.merchantAuditStatus;
  
  console.log('商家状态:', status, '审核状态:', audit_status);
  
  // 根据状态决定跳转路径
  if (status === MerchantStatus.APPROVED || audit_status === 1) {
    // 已审核通过，跳转到仪表盘
    return '/merchant/dashboard';
  } else {
    // 其他状态（待审核、已拒绝、已暂停等）跳转到状态页面
    return '/merchant/status';
  }
};

// 密码登录提交
const handlePasswordSubmit = async () => {
  if (!passwordFormRef.value) return;
  
  if (!agreePrivacyPolicy.value) {
    ElMessage.warning('请先阅读并同意隐私协议');
    return;
  }

  await passwordFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true;
      try {
        // 登录逻辑
        const res = await merchantStore.login(passwordForm);
        console.log('res', res);
        
        // 处理登录响应
        handleLoginResponse(res);

        if (rememberUsername.value) {
          localStorage.setItem('rememberMerchantUsername', passwordForm.username);
        } else {
          localStorage.removeItem('rememberMerchantUsername');
        }
        localStorage.setItem('rememberMe', rememberMe.value.toString());
        console.log('passwordForm', passwordForm);
        
        // 初始化路由并跳转
        await initRouteAndNavigate();
      } catch (error: any) {
        ElMessage.error(error.message || '登录失败，请检查用户名和密码');
      } finally {
        loading.value = false;
      }
    }
  });
};

// 发送短信验证码
const sendVerificationCode = async () => {
  if (!canSendCode.value) return;
  
  try {
    // 调用Store中的发送验证码方法
    await merchantStore.sendVerificationCode(smsForm.phone);
    
    // 显示成功消息
    ElMessage.success('验证码发送成功，60秒内有效');
    
    // 开始倒计时，固定60秒
    startCountdown(60);
  } catch (error: any) {
    ElMessage.error(error.message || '验证码发送失败，请稍后再试');
  }
};

// 倒计时
const startCountdown = (expireTime = 60) => {
  countdown.value = expireTime; // 使用服务器返回的过期时间，默认60秒
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

// 验证码登录提交
const handleSmsSubmit = async () => {
  if (!smsFormRef.value) return;
  
  if (!agreePrivacyPolicy.value) {
    ElMessage.warning('请先阅读并同意隐私协议');
    return;
  }

  await smsFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      smsLoading.value = true;
      try {
        // 调用Store中的短信验证码登录方法
        const res = await merchantStore.smsLogin({
          phone: smsForm.phone,
          code: smsForm.code
        }, rememberMe.value);
        
        // 处理登录响应
        handleLoginResponse(res);
        
        // 登录成功提示
        ElMessage.success('登录成功，正在跳转...');
        
        // 保存状态
        localStorage.setItem('rememberMe', rememberMe.value.toString());
        
        // 初始化路由并跳转
        await initRouteAndNavigate();
      } catch (error: any) {
        ElMessage.error(error.message || '登录失败，请检查手机号和验证码');
      } finally {
        smsLoading.value = false;
      }
    }
  });
};

// 处理登录响应
const handleLoginResponse = (loginResult: any) => {
  if (loginResult && loginResult.data) {
    const responseData = loginResult.data;
    
    // 处理设备ID
    if (responseData.device_id) {
      localStorage.setItem('merchant_device_id', responseData.device_id);
      console.log('设备ID已保存:', responseData.device_id);
    }
    
    // 处理新设备提示
    if (responseData.is_new_device) {
      ElMessage.info('检测到新设备登录，请注意账户安全');
    }
    
    // 处理风险等级
    if (responseData.risk_level) {
      console.log('登录风险等级:', responseData.risk_level);
      if (responseData.risk_level === 'high') {
        ElMessage.warning('检测到高风险登录，请注意账户安全');
      }
    }
  }
};

onMounted(() => {
  // 初始化记住用户名和自动登录
  const savedUsername = localStorage.getItem('rememberMerchantUsername');
  if (savedUsername) {
    passwordForm.username = savedUsername;
    rememberUsername.value = true;
  }
  const savedRememberMe = localStorage.getItem('rememberMe');
  if (savedRememberMe === 'true') {
    rememberMe.value = true;
  }
});
</script>

<style scoped lang="scss">
.admin-login {
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;

  .login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .login-header {
    text-align: center;
    margin-bottom: 20px;

    .logo {
      height: 60px;
      margin-bottom: 15px;
    }

    .title {
      font-size: 24px;
      color: $primary-color;
      margin: 0;
    }
  }

  .login-form-card {
    padding: 20px;

    .form-title {
      text-align: center;
      margin-bottom: 20px;
      font-size: 20px;
      color: $text-primary;
      font-weight: 500;
    }
    
    .login-tabs {
      width: 100%;
      margin-bottom: 15px;
    }

    .form-actions {
      margin-top: 20px;
    }

    .submit-btn {
      width: 100%;
      padding: 12px 0;
      font-size: 16px;
      transition: all 0.3s;
      border-radius: 4px;
    }

    .submit-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    }
    
    .verification-code-input {
      display: flex;
      align-items: center;
      gap: 10px;
      
      .el-input {
        flex: 1;
      }
      
      .send-code-btn {
        min-width: 110px;
        white-space: nowrap;
      }
    }

    .login-footer {
      margin-top: 20px;
      text-align: center;

      p {
        color: $text-secondary;
        font-size: 12px;
      }

      .register-btn {
        font-size: 16px;
      }
    }
  }

  // 隐私协议弹窗样式
  .privacy-content {
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
    font-size: 14px;
    line-height: 1.6;
    color: $text-primary;
    
    h3 {
      margin-bottom: 15px;
      color: $primary-color;
      font-weight: 500;
    }
  }

  .privacy-loading {
    padding: 20px 0;
  }

  .privacy-error {
    padding: 40px 0;
  }
}
</style>