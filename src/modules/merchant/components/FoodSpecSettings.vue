<!-- 
 * 商品规格设置组件
 * 用于设置商品的规格组和规格选项
 */
-->
<template>
  <div class="spec-setting">
    <el-form label-position="top">
      <el-form-item label="是否启用多规格">
        <el-switch v-model="localEnableSpecs" :disabled="!!foodId && !canEditSpecs" @change="handleEnableSpecsChange" />
        <div v-if="!!foodId && !canEditSpecs" class="warning-tip">
          已上架的商品不能修改规格设置，如需修改请先下架商品
        </div>
      </el-form-item>
    </el-form>
    
    <div v-if="localEnableSpecs" class="spec-groups">
      <div v-for="(group, groupIndex) in localSpecGroups" :key="groupIndex" class="spec-group">
        <div class="spec-group-header">
          <el-input 
            v-model="group.name" 
            placeholder="规格组名称，如：大小、口味等" 
            maxlength="20"
            @input="handleSpecGroupsChange"
          ></el-input>
          
          <el-button 
            type="danger" 
            @click="removeSpecGroup(groupIndex)" 
            :disabled="localSpecGroups.length <= 1"
            circle plain
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
        
        <div class="spec-options">
          <div v-for="(option, optionIndex) in group.options" :key="optionIndex" class="spec-option">
            <el-checkbox v-model="option.enabled" :disabled="group.options.filter(o => o.enabled).length <= 1 && option.enabled" @change="handleSpecGroupsChange">
              <el-input 
                v-model="option.name" 
                placeholder="规格选项名称，如：大杯、中杯等" 
                maxlength="20"
                :disabled="!option.enabled"
                @input="() => {
                  handleSpecGroupsChange();
                  handleSpecOptionChange(groupIndex, optionIndex);
                }"
              ></el-input>
            </el-checkbox>
            
            <el-radio 
              v-model="group.defaultOption" 
              :label="optionIndex"
              :disabled="!option.enabled"
              @change="() => {
                handleSpecGroupsChange();
                handleSpecOptionChange(groupIndex, optionIndex);
              }"
            >默认</el-radio>
            
            <el-input-number 
              v-model="option.price_adjustment" 
              :precision="2" 
              :step="0.5" 
              :min="-1000" 
              :max="1000"
              controls-position="right"
              placeholder="价格调整"
              :disabled="!option.enabled"
              @change="() => {
                handleSpecGroupsChange();
                handleSpecOptionChange(groupIndex, optionIndex);
              }"
            ></el-input-number>
            
            <el-input-number 
              v-model="option.stock" 
              :min="-1"
              :max="999999"
              controls-position="right"
              placeholder="库存(-1不限)"
              :disabled="!option.enabled"
              @change="() => {
                handleSpecGroupsChange();
                handleSpecOptionChange(groupIndex, optionIndex);
              }"
            >
              <template #append>
                <el-tooltip content="-1表示不限制库存" placement="top">
                  <el-icon><InfoFilled /></el-icon>
                </el-tooltip>
              </template>
            </el-input-number>
            
            <el-button 
              type="danger" 
              @click="removeSpecOption(groupIndex, optionIndex)" 
              :disabled="group.options.length <= 1"
              circle plain size="small"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          
          <el-button type="primary" plain @click="addSpecOption(groupIndex)" size="small">
            <el-icon><Plus /></el-icon>添加选项
          </el-button>
        </div>
      </div>
      
      <el-button type="primary" plain @click="addSpecGroup">
        <el-icon><Plus /></el-icon>添加规格组
      </el-button>
      
      <!-- 规格组合预览 -->
      <div v-if="specCombinations.length > 0" class="spec-combinations">
        <h4>规格组合预览</h4>
        <el-table :data="specCombinations" border>
          <el-table-column label="组合">
            <template #default="{ row }">
              <div v-for="(spec, index) in row.specs" :key="index">
                {{ spec.groupName }}: {{ spec.optionName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="priceAdjustment" label="价格调整" width="100">
            <template #default="{ row }">
              <span :class="{ 'price-up': row.priceAdjustment > 0, 'price-down': row.priceAdjustment < 0 }">
                {{ row.priceAdjustment > 0 ? '+' : '' }}{{ row.priceAdjustment }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="最终价格" width="120">
            <template #default="{ row }">
              <div>{{ (basePrice + row.priceAdjustment).toFixed(2) }}</div>
              <div v-if="row.stock !== undefined" class="stock-info">
                库存: {{ row.stock === -1 ? '不限' : row.stock }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { Delete, Plus, InfoFilled } from '@element-plus/icons-vue';

// 规格选项接口
interface SpecOption {
  groupName: string;
  optionName: string;
  priceAdjustment: number;
  stock?: number; // 库存数量，-1表示不限制
}

// 本地规格选项接口
interface LocalSpecOption {
  name: string;
  price_adjustment: number;
  stock: number; // 库存数量，-1表示不限制
  is_default: boolean;
  enabled: boolean;
  variantId?: number; // 变体ID，从后端返回
}

// 规格组合接口
interface SpecCombination {
  specs: SpecOption[];
  priceAdjustment: number;
  stock?: number; // 库存数量，-1表示不限制
}

// 接收父组件的属性
const props = defineProps<{
  enableSpecs: boolean;
  specGroups: Array<{
    name: string;
    defaultOption: number;
    options: Array<{
      name: string;
      price_adjustment: number;
      is_default: boolean;
      enabled: boolean;
      stock: number; // 库存数量，-1表示不限制
    }>;
  }>;
  foodId?: number;
  canEditSpecs: boolean;
  basePrice: number;
}>();

// 向父组件发送事件
const emit = defineEmits<{
  'update:enableSpecs': [value: boolean];
  'update:specGroups': [value: typeof props.specGroups];
  'specCombinationsChange': [value: SpecCombination[]];
  'addSpecOption': [groupIndex: number, option: any];
  'removeSpecOption': [groupIndex: number, optionIndex: number, optionId?: number];
  'updateSpecOption': [groupIndex: number, optionIndex: number, option: any];
}>();

// 本地状态，用于双向绑定
const localEnableSpecs = ref(props.enableSpecs);
const localSpecGroups = ref<typeof props.specGroups>([...props.specGroups]);

// 监听父组件传入的属性
watch(() => props.enableSpecs, (newVal) => {
  localEnableSpecs.value = newVal;
});

watch(() => props.specGroups, (newVal) => {
  // 深拷贝避免直接修改父组件的数据
  localSpecGroups.value = newVal.map(group => {
    const options = group.options.map(option => ({
      ...option,
      // 确保每个选项都有stock属性，默认为-1（不限制）
      stock: 'stock' in option ? option.stock : -1
    }));
    
    return {
      ...group,
      options
    };
  });
}, { deep: true, immediate: true });

// 处理启用规格的变化
const handleEnableSpecsChange = () => {
  emit('update:enableSpecs', localEnableSpecs.value);
  // 如果启用规格，确保有默认规格组
  if (localEnableSpecs.value && localSpecGroups.value.length === 0) {
    initDefaultSpecGroup();
  }
  // 重新计算规格组合
  calculateSpecCombinations();
};

// 处理规格组变化
const handleSpecGroupsChange = () => {
  emit('update:specGroups', localSpecGroups.value);
  calculateSpecCombinations();
};

// 处理规格选项变化
const handleSpecOptionChange = (groupIndex: number, optionIndex: number) => {
  const option = localSpecGroups.value[groupIndex].options[optionIndex];
  // 向父组件发送更新规格选项的事件
  emit('updateSpecOption', groupIndex, optionIndex, option);
};

// 初始化默认规格组
const initDefaultSpecGroup = () => {
  if (localSpecGroups.value.length === 0) {
    localSpecGroups.value.push({
      name: '规格',
      options: [
        {
          name: '标准',
          price_adjustment: 0,
          stock: -1, // 默认不限制库存
          is_default: true,
          enabled: true
        }
      ],
      defaultOption: 0
    });
    
    handleSpecGroupsChange();
  }
};

// 添加规格组
const addSpecGroup = () => {
  localSpecGroups.value.push({
    name: '',
    options: [{ 
      name: '', 
      price_adjustment: 0, 
      stock: -1, // 默认不限制库存
      is_default: true, 
      enabled: true 
    }],
    defaultOption: 0
  });
  
  handleSpecGroupsChange();
};

// 移除规格组
const removeSpecGroup = (index: number) => {
  localSpecGroups.value.splice(index, 1);
  if (localSpecGroups.value.length === 0) {
    initDefaultSpecGroup();
  } else {
    handleSpecGroupsChange();
  }
};

// 添加规格选项
const addSpecOption = (groupIndex: number) => {
  const newOption = {
    name: '',
    price_adjustment: 0,
    stock: -1, // 默认不限制库存
    is_default: false,
    enabled: true
  } as const;
  
  localSpecGroups.value[groupIndex].options.push({
    ...newOption
  });
  handleSpecGroupsChange();
  
  // 向父组件发送添加规格选项的事件
  emit('addSpecOption', groupIndex, newOption);
};

// 移除规格选项
const removeSpecOption = (groupIndex: number, optionIndex: number) => {
  // 保存要删除选项的ID，如果有的话
  const optionToRemove = localSpecGroups.value[groupIndex].options[optionIndex] as LocalSpecOption;
  const variantId = optionToRemove.variantId;
  
  // 从本地数组中移除
  localSpecGroups.value[groupIndex].options.splice(optionIndex, 1);
  
  // 如果删除的是默认选项，重置默认选项到第一个
  if (localSpecGroups.value[groupIndex].defaultOption === optionIndex) {
    localSpecGroups.value[groupIndex].defaultOption = 0;
  } else if (localSpecGroups.value[groupIndex].defaultOption > optionIndex) {
    // 如果删除的是默认选项之前的项，调整默认选项索引
    localSpecGroups.value[groupIndex].defaultOption--;
  }
  
  handleSpecGroupsChange();
  
  // 向父组件发送移除规格选项的事件
  emit('removeSpecOption', groupIndex, optionIndex, variantId);
};

// 规格组合结果
const specCombinations = ref<SpecCombination[]>([]);

// 计算所有规格组合
const calculateSpecCombinations = () => {
  // 清空现有组合
  specCombinations.value = [];
  
  // 如果没有启用规格，直接返回
  if (!localEnableSpecs.value) return;
  
  // 获取所有有效的规格组和选项
  const validGroups = localSpecGroups.value
    .filter(group => group.name.trim() !== '')
    .map(group => ({
      name: group.name,
      options: group.options
        .filter(option => option.enabled && option.name.trim() !== '')
        .map(option => ({
          name: option.name,
          price_adjustment: option.price_adjustment,
          stock: option.stock // 包含库存信息
        }))
    }))
    .filter(group => group.options.length > 0);
  
  if (validGroups.length === 0) return;
  
  // 创建递归函数生成所有组合
  const generateCombinations = (
    currentIndex: number,
    currentSpecs: SpecOption[] = []
  ) => {
    // 如果已经处理完所有规格组，添加当前组合到结果中
    if (currentIndex >= validGroups.length) {
      const totalAdjustment = currentSpecs.reduce((sum, spec) => sum + spec.priceAdjustment, 0);
      // 找出所有规格中的最小库存，如果不为-1的话
      const stock = currentSpecs.reduce((minStock, spec) => {
        if (spec.stock === undefined) return minStock;
        if (minStock === -1) return spec.stock;
        if (spec.stock === -1) return minStock;
        return Math.min(minStock, spec.stock);
      }, -1);
      
      specCombinations.value.push({
        specs: currentSpecs,
        priceAdjustment: totalAdjustment,
        stock: stock
      });
      return;
    }
    
    // 获取当前规格组
    const currentGroup = validGroups[currentIndex];
    
    // 遍历当前规格组的所有选项
    currentGroup.options.forEach(option => {
      // 创建一个新的组合
      const newSpecs = [...currentSpecs, {
        groupName: currentGroup.name,
        optionName: option.name,
        priceAdjustment: option.price_adjustment,
        stock: option.stock || -1 // 包含库存信息，默认为-1（不限制）
      }];
      
      // 递归处理下一个规格组
      generateCombinations(currentIndex + 1, newSpecs);
    });
  };
  
  // 开始递归生成组合
  generateCombinations(0);
  
  // 通知父组件规格组合变化
  emit('specCombinationsChange', specCombinations.value);
};

// 组件挂载时初始化数据
onMounted(() => {
  if (localSpecGroups.value.length === 0) {
    initDefaultSpecGroup();
  }
  calculateSpecCombinations();
});
</script>

<style scoped lang="scss">
.spec-setting {
  margin-bottom: 20px;
}

.warning-tip {
  color: #E6A23C;
  font-size: 12px;
  margin-top: 5px;
}

.spec-groups {
  margin-top: 20px;
}

.spec-group {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #FAFAFA;
}

.spec-group-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 10px;
}

.spec-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.spec-option {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px dashed #EBEEF5;
  
  .el-input-number {
    width: 120px;
  }
  
  .stock-input {
    .el-input-number {
      width: 140px;
    }
  }
}

.stock-info {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.spec-combinations {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  background-color: #fff;
  
  h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-weight: 500;
  }
}

.price-up {
  color: #F56C6C;
}

.price-down {
  color: #67C23A;
}
</style>
