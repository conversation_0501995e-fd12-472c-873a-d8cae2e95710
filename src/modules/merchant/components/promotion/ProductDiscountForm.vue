<!--
 * 商品折扣表单组件
 * 用于在促销活动编辑页面中设置商品折扣规则
-->
<template>
  <div class="product-discount-form">
    <el-divider content-position="left">折扣设置</el-divider>
    
    <!-- 折扣类型 -->
    <el-form-item label="折扣类型">
      <el-radio-group v-model="ruleData.discount_type">
        <el-radio :label="DISCOUNT_TYPES.PERCENTAGE">百分比折扣</el-radio>
        <el-radio :label="DISCOUNT_TYPES.FIXED_AMOUNT">固定金额折扣</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <!-- 折扣值 -->
    <el-form-item label="折扣值">
      <div class="flex items-center">
        <el-input-number
          v-model="ruleData.discount_value"
          :min="0"
          :precision="ruleData.discount_type === DISCOUNT_TYPES.PERCENTAGE ? 1 : 2"
          :max="ruleData.discount_type === DISCOUNT_TYPES.PERCENTAGE ? 10 : 100000"
          :step="ruleData.discount_type === DISCOUNT_TYPES.PERCENTAGE ? 0.1 : 1"
          style="width: 180px;"
        />
        <span class="ml-2">{{ ruleData.discount_type === DISCOUNT_TYPES.PERCENTAGE ? '折' : '元' }}</span>
      </div>
    </el-form-item>
    <div class="el-form-item-tip mb-4">
      {{ ruleData.discount_type === DISCOUNT_TYPES.PERCENTAGE ? '输入范围：0.1-10折' : '输入范围：0-100000元' }}
    </div>
    
    <!-- 适用商品 -->
    <el-form-item label="适用商品">
      <div class="flex items-center">
        <el-radio-group v-model="applyToAll" @change="handleApplyToAllChange">
          <el-radio :label="true">所有商品</el-radio>
          <el-radio :label="false">指定商品</el-radio>
        </el-radio-group>
      </div>
    </el-form-item>
    
    <!-- 商品选择器 -->
    <el-form-item v-if="!applyToAll" label="选择商品">
      <div class="selected-foods">
        <div v-if="formData.food_ids.length === 0" class="text-gray-400">
          暂未选择任何商品
        </div>
        <el-tag 
          v-for="food in selectedFoods" 
          :key="food.id"
          closable
          @close="removeFood(food.id)"
          class="mr-2 mb-2"
        >
          {{ food.name }}
        </el-tag>
      </div>
      <el-button type="primary" plain @click="openFoodSelector">选择商品</el-button>
    </el-form-item>
    
    <!-- 商品选择器弹窗 -->
    <el-dialog v-model="foodSelectorVisible" title="选择商品" width="70%">
      <food-selector 
        :selected-ids="formData.food_ids" 
        @confirm="handleFoodSelected"
        @cancel="foodSelectorVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 商品折扣表单组件
 * 用于在促销活动编辑页面中设置商品折扣规则
 */
import { ref, computed, onMounted } from 'vue'
import { DISCOUNT_TYPES } from '../../constants/promotion'
import FoodSelector from '../FoodSelector.vue'

// 定义接口和类型
interface RuleData {
  discount_type: number;
  discount_value: number;
}

interface FormData {
  ruleData: RuleData;
  food_ids: number[];
}

// 定义props和emits
const props = defineProps({
  formData: {
    type: Object as () => FormData,
    required: true
  }
})

// 规则数据
const ruleData = computed({
  get: () => {
    if (!props.formData.ruleData.discount_type) {
      // 初始化默认值
      props.formData.ruleData = {
        discount_type: DISCOUNT_TYPES.PERCENTAGE,
        discount_value: 9.5
      }
    }
    return props.formData.ruleData
  },
  set: (val) => {
    props.formData.ruleData = val
  }
})

// 是否适用于所有商品
const applyToAll = ref(true)

// 商品选择器
const foodSelectorVisible = ref(false)
interface FoodItem {
  id: number;
  name: string;
}

const selectedFoods = ref<FoodItem[]>([])

/**
 * 适用范围变更处理
 */
const handleApplyToAllChange = (val: boolean) => {
  if (val) {
    props.formData.food_ids = []
  }
}

/**
 * 移除选中的商品
 */
const removeFood = (id: number) => {
  const index = props.formData.food_ids.indexOf(id)
  if (index !== -1) {
    props.formData.food_ids.splice(index, 1)
  }
}

/**
 * 打开商品选择器
 */
const openFoodSelector = () => {
  foodSelectorVisible.value = true
}

/**
 * 处理商品选择结果
 */
const handleFoodSelected = (foods: any[]) => {
  props.formData.food_ids = foods.map(food => food.id)
  selectedFoods.value = foods
  foodSelectorVisible.value = false
}

// 初始化
onMounted(() => {
  // 根据是否有选中的商品ID来判断是否为全部商品
  applyToAll.value = !props.formData.food_ids?.length
  
  // 如果有food_ids，应该通过API获取这些商品的详情信息
  // 这里暂时使用模拟数据
  if (props.formData.food_ids?.length) {
    // 模拟从API获取商品详情
    selectedFoods.value = props.formData.food_ids.map(id => ({
      id,
      name: `商品${id}`
    }))
  }
})
</script>

<style scoped>
.product-discount-form {
  margin-top: 20px;
}

.selected-foods {
  margin-bottom: 10px;
  min-height: 40px;
  padding: 5px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.mb-2 {
  margin-bottom: 8px;
}

.text-gray-400 {
  color: #909399;
}
</style>
