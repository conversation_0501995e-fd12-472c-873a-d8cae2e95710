<!--
 * 优惠券表单组件
 * 用于在后台管理页面编辑优惠券信息
-->
<template>
  <div class="coupon-form">
    <el-divider content-position="left">优惠券设置</el-divider>
    
    <!-- 优惠券名称 -->
    <el-form-item label="优惠券名称">
      <el-input v-model="couponData.name" placeholder="请输入优惠券名称" />
    </el-form-item>
    
    <!-- 优惠券类型 -->
    <el-form-item label="优惠券类型">
      <el-select v-model="couponData.type" style="width: 100%;">
        <el-option :value="COUPON_TYPES.AMOUNT" label="金额券" />
        <el-option :value="COUPON_TYPES.DISCOUNT" label="折扣券" />
        <el-option :value="COUPON_TYPES.EXCHANGE" label="兑换券" />
      </el-select>
    </el-form-item>
    
    <!-- 优惠金额/折扣 -->
    <el-form-item :label="couponData.type === COUPON_TYPES.DISCOUNT ? '折扣力度' : '优惠金额'">
      <div class="flex items-center">
        <el-input-number 
          v-model="couponData.amount" 
          :min="0" 
          :precision="couponData.type === COUPON_TYPES.DISCOUNT ? 1 : 2"
          :max="couponData.type === COUPON_TYPES.DISCOUNT ? 10 : 1000"
          :step="couponData.type === COUPON_TYPES.DISCOUNT ? 0.1 : 1"
          style="width: 180px;"
        />
        <span class="ml-2">{{ couponData.type === COUPON_TYPES.DISCOUNT ? '折' : '元' }}</span>
      </div>
    </el-form-item>
    
    <!-- 使用门槛 -->
    <el-form-item label="使用门槛">
      <div class="flex items-center">
        <span>满</span>
        <el-input-number 
          v-model="couponData.min_order_amount" 
          :min="0" 
          :precision="2"
          style="width: 150px;"
          class="mx-2"
        />
        <span>元可用</span>
      </div>
    </el-form-item>
    
    <!-- 每人限领数量 -->
    <el-form-item label="每人限领">
      <div class="flex items-center">
        <el-input-number 
          v-model="couponData.per_user_limit" 
          :min="1" 
          :precision="0"
          style="width: 150px;"
        />
        <span class="ml-2">张</span>
      </div>
    </el-form-item>
    
    <!-- 有效期 -->
    <el-form-item label="有效期">
      <div class="flex items-center">
        <span>领取后</span>
        <el-input-number 
          v-model="couponData.valid_days" 
          :min="1" 
          :precision="0"
          style="width: 150px;"
          class="mx-2"
        />
        <span>天内有效</span>
      </div>
    </el-form-item>
    
    <!-- 适用商品 -->
    <el-form-item label="适用商品">
      <div class="flex items-center">
        <el-radio-group v-model="applyToAll" @change="handleApplyToAllChange">
          <el-radio :label="true">所有商品</el-radio>
          <el-radio :label="false">指定商品</el-radio>
        </el-radio-group>
      </div>
    </el-form-item>
    
    <!-- 商品选择器 -->
    <el-form-item v-if="!applyToAll" label="选择商品">
      <div class="selected-foods">
        <div v-if="formData.food_ids.length === 0" class="text-gray-400">
          暂无选择任何商品
        </div>
        <el-tag 
          v-for="(food, _index) in selectedFoods" 
          :key="food.id"
          closable
          @close="removeFood(food.id)"
          class="mr-2 mb-2"
        >
          {{ food.name }}
        </el-tag>
      </div>
      <el-button type="primary" plain @click="openFoodSelector">选择商品</el-button>
    </el-form-item>
    
    <!-- 商品选择器弹窗 -->
    <el-dialog v-model="foodSelectorVisible" title="选择商品" width="70%">
      <food-selector 
        :selected-ids="formData.food_ids" 
        @confirm="handleFoodSelected"
        @cancel="foodSelectorVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 优惠券表单组件
 * 用于在后台管理页面编辑优惠券信息
 */
import { ref, computed, onMounted } from 'vue'
import { COUPON_TYPES } from '../../constants/promotion'
import FoodSelector from '../FoodSelector.vue'

// 接收父组件传递的数据
const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

// 是否应用于所有商品
const applyToAll = ref(true)

// 优惠券数据
const couponData = computed({
  get: () => {
    if (!props.formData.ruleData.coupon) {
      // 初始化优惠券数据
      props.formData.ruleData = {
        coupon: {
          name: '',
          type: COUPON_TYPES.AMOUNT,
          amount: 10,
          min_order_amount: 100,
          per_user_limit: 1,
          valid_days: 30
        }
      }
    }
    return props.formData.ruleData.coupon
  },
  set: (val) => {
    if (!props.formData.ruleData) {
      props.formData.ruleData = {}
    }
    props.formData.ruleData.coupon = val
  }
})

// 商品选择器弹窗是否显示
const foodSelectorVisible = ref(false)

// 定义商品项接口
interface FoodItem {
  id: number;
  name: string;
}

// 选中的商品列表
const selectedFoods = ref<FoodItem[]>([])

/**
 * 应用于所有商品切换事件处理
 */
const handleApplyToAllChange = (val: boolean) => {
  if (val) {
    props.formData.food_ids = []
  }
}

/**
 * 移除选中的商品
 */
const removeFood = (id: number) => {
  const index = props.formData.food_ids.indexOf(id)
  if (index !== -1) {
    props.formData.food_ids.splice(index, 1)
  }
}

/**
 * 打开商品选择器弹窗
 */
const openFoodSelector = () => {
  foodSelectorVisible.value = true
}

/**
 * 处理商品选择结果
 */
const handleFoodSelected = (foods: any[]) => {
  props.formData.food_ids = foods.map(food => food.id)
  selectedFoods.value = foods
  foodSelectorVisible.value = false
}

// 初始化
onMounted(() => {
  // 根据是否有选中的商品ID来判断是否应用于所有商品
  applyToAll.value = !props.formData.food_ids?.length
  
  // 如果有选中的商品ID，则从API获取商品详细信息
  // 这里暂时使用模拟数据
  if (props.formData.food_ids?.length) {
    selectedFoods.value = props.formData.food_ids.map((id: number) => ({
      id,
      name: `商品${id}`
    }))
  }
})
</script>

<style scoped>
.coupon-form {
  margin-top: 20px;
}

.selected-foods {
  margin-bottom: 10px;
  min-height: 40px;
  padding: 5px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.mb-2 {
  margin-bottom: 8px;
}

.text-gray-400 {
  color: #909399;
}
</style>
