<!--
 * 满减优惠表单组件
 * 用于在促销活动编辑页面中设置满减优惠规则
-->
<template>
  <div class="full-reduction-form">
    <el-divider content-position="left">满减规则设置</el-divider>
    
    <!-- 满减规则 -->
    <el-form-item label="满减规则">
      <div v-for="(condition, index) in conditions" :key="index" class="condition-item">
        <div class="condition-row">
          <span>满</span>
          <el-input-number 
            v-model="condition.threshold" 
            :min="0" 
            :precision="2"
            style="width: 150px;"
            class="mx-2"
          />
          <span>元减</span>
          <el-input-number 
            v-model="condition.discount" 
            :min="0" 
            :max="condition.threshold"
            :precision="2"
            style="width: 150px;"
            class="mx-2"
          />
          <span>元</span>
          <el-button type="danger" icon="Delete" circle @click="removeCondition(index)" class="ml-3" />
        </div>
      </div>

      <el-button type="primary" plain icon="Plus" @click="addCondition" class="mt-3">
        添加满减规则
      </el-button>
    </el-form-item>
    <div class="el-form-item-tip mb-4">
      提示：满减规则会按照金额门槛从高到低自动排序，用户实际消费时只享受最高适用的一档优惠
    </div>
    
    <!-- 适用商品 -->
    <el-form-item label="适用商品">
      <div class="flex items-center">
        <el-radio-group v-model="applyToAll" @change="handleApplyToAllChange">
          <el-radio :label="true">所有商品</el-radio>
          <el-radio :label="false">指定商品</el-radio>
        </el-radio-group>
      </div>
    </el-form-item>
    
    <!-- 商品选择器 -->
    <el-form-item v-if="!applyToAll" label="选择商品">
      <div class="selected-foods">
        <div v-if="formData.food_ids.length === 0" class="text-gray-400">
          暂未选择任何商品
        </div>
        <el-tag 
          v-for="food in selectedFoods" 
          :key="food.id"
          closable
          @close="removeFood(food.id)"
          class="mr-2 mb-2"
        >
          {{ food.name }}
        </el-tag>
      </div>
      <el-button type="primary" plain @click="openFoodSelector">选择商品</el-button>
    </el-form-item>
    
    <!-- 商品选择器弹窗 -->
    <el-dialog v-model="foodSelectorVisible" title="选择商品" width="70%">
      <food-selector 
        :selected-ids="formData.food_ids" 
        @confirm="handleFoodSelected"
        @cancel="foodSelectorVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 满减优惠表单组件
 * 用于在促销活动编辑页面中设置满减优惠规则
 */
import { ref, computed, onMounted, watch } from 'vue'
import FoodSelector from '../FoodSelector.vue'

// 定义接口和类型
interface Condition {
  threshold: number;
  discount: number;
}

interface RuleData {
  conditions: Condition[];
}

interface FormData {
  ruleData: RuleData;
  food_ids: number[];
}

// 定义props
const props = defineProps({
  formData: {
    type: Object as () => FormData,
    required: true
  }
})

// 是否适用于所有商品
const applyToAll = ref(true)

// 满减条件
const conditions = computed({
  get: () => {
    if (!props.formData.ruleData.conditions || !props.formData.ruleData.conditions.length) {
      // 初始化默认值
      props.formData.ruleData = {
        conditions: [
          { threshold: 50, discount: 5 },
          { threshold: 100, discount: 10 }
        ]
      }
    }
    return props.formData.ruleData.conditions
  },
  set: (val) => {
    if (!props.formData.ruleData) {
      props.formData.ruleData = { conditions: [] }
    }
    props.formData.ruleData.conditions = val
  }
})

// 商品选择器
const foodSelectorVisible = ref(false)

interface FoodItem {
  id: number;
  name: string;
}

const selectedFoods = ref<FoodItem[]>([])

/**
 * 添加满减条件
 */
const addCondition = () => {
  const lastCondition = conditions.value[conditions.value.length - 1]
  const newThreshold = lastCondition ? lastCondition.threshold + 50 : 50
  const newDiscount = lastCondition ? lastCondition.discount + 5 : 5
  
  conditions.value.push({
    threshold: newThreshold,
    discount: newDiscount
  })
  
  // 排序条件（从高到低）
  sortConditions()
}

/**
 * 移除满减条件
 */
const removeCondition = (index: number) => {
  conditions.value.splice(index, 1)
  
  // 如果删除完所有条件，添加一个默认条件
  if (conditions.value.length === 0) {
    conditions.value.push({ threshold: 50, discount: 5 })
  }
}

/**
 * 排序满减条件（从高到低）
 */
const sortConditions = () => {
  conditions.value.sort((a: Condition, b: Condition) => b.threshold - a.threshold)
}

/**
 * 适用范围变更处理
 */
const handleApplyToAllChange = (val: boolean) => {
  if (val) {
    props.formData.food_ids = []
  }
}

/**
 * 移除选中的商品
 */
const removeFood = (id: number) => {
  const index = props.formData.food_ids.indexOf(id)
  if (index !== -1) {
    props.formData.food_ids.splice(index, 1)
  }
}

/**
 * 打开商品选择器
 */
const openFoodSelector = () => {
  foodSelectorVisible.value = true
}

/**
 * 处理商品选择结果
 */
const handleFoodSelected = (foods: any[]) => {
  props.formData.food_ids = foods.map(food => food.id)
  selectedFoods.value = foods
  foodSelectorVisible.value = false
}

// 监听条件变化，自动排序
watch(conditions, () => {
  sortConditions()
}, { deep: true })

// 初始化
onMounted(() => {
  // 根据是否有选中的商品ID来判断是否为全部商品
  applyToAll.value = !props.formData.food_ids?.length
  
  // 如果有food_ids，应该通过API获取这些商品的详情信息
  // 这里暂时使用模拟数据
  if (props.formData.food_ids?.length) {
    // 模拟从API获取商品详情
    selectedFoods.value = props.formData.food_ids.map(id => ({
      id,
      name: `商品${id}`
    }))
  }
})
</script>

<style scoped>
.full-reduction-form {
  margin-top: 20px;
}

.condition-item {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.condition-row {
  display: flex;
  align-items: center;
}

.selected-foods {
  margin-bottom: 10px;
  min-height: 40px;
  padding: 5px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.ml-3 {
  margin-left: 12px;
}

.mt-3 {
  margin-top: 12px;
}

.mr-2 {
  margin-right: 8px;
}

.mb-2 {
  margin-bottom: 8px;
}

.text-gray-400 {
  color: #909399;
}
</style>
