<!--
/**
 * 商家聊天组件
 * <AUTHOR>
 * @date 2025-01-27
 * @version 1.0.0
 * @description 商家端聊天功能组件，用于替代原有的通知功能，提供实时聊天、会话管理、消息处理等功能
 * 
 * 主要功能：
 * - 实时WebSocket连接管理
 * - 会话列表展示和筛选（全部、未读、紧急）
 * - 消息发送和接收（文本、图片、文件）
 * - 快捷回复和消息模板
 * - 客户信息展示
 * - 连接状态监控
 * 
 * 使用方式：
 * <MerchantChat :merchant-id="merchantId" :merchant-token="merchantToken" />
 */
-->
<template>
  <div class="merchant-chat">
    <!-- 聊天触发按钮 -->
    <el-badge :value="unreadCount" :max="99" class="chat-badge">
      <el-button 
        :icon="ChatDotRound" 
        circle 
        @click="toggleChat"
        :class="{ 'active': showChat }"
        class="chat-trigger-btn"
      />
    </el-badge>

    <!-- 聊天窗口 -->
    <el-drawer
      v-model="showChat"
      title="客服聊天"
      size="400px"
      direction="rtl"
      class="merchant-chat-drawer"
    >
      <template #header>
        <div class="chat-header">
          <div class="chat-title">
            <el-icon><ChatDotRound /></el-icon>
            <span>客服聊天</span>
            <el-badge :value="unreadCount" :max="99" v-if="unreadCount > 0" />
          </div>
          <div class="chat-actions">
            <!-- 通知管理 -->
            <el-popover
              placement="bottom-end"
              :width="320"
              trigger="click"
              popper-class="notification-popover"
            >
              <template #reference>
                <el-badge :value="unreadNotificationCount" :max="99" :hidden="unreadNotificationCount === 0">
                  <el-button
                    :icon="Bell"
                    circle
                    size="small"
                    class="notification-btn"
                    :class="{ 'has-notifications': unreadNotificationCount > 0 }"
                  />
                </el-badge>
              </template>

              <!-- 通知列表 -->
              <div class="notification-panel">
                <div class="notification-header">
                  <span class="notification-title">通知中心</span>
                  <div class="notification-actions">
                    <el-button
                      text
                      size="small"
                      @click="markAllNotificationsRead"
                      v-if="unreadNotificationCount > 0"
                    >
                      全部已读
                    </el-button>
                    <el-button
                      text
                      size="small"
                      @click="clearAllNotifications"
                      v-if="notifications.length > 0"
                    >
                      清空
                    </el-button>
                  </div>
                </div>

                <div class="notification-list" v-if="notifications.length > 0">
                  <div
                    v-for="notification in notifications.slice(0, 10)"
                    :key="notification.id"
                    class="notification-item"
                    :class="{ 'unread': !notification.read }"
                    @click="handleNotificationClick(notification)"
                  >
                    <div class="notification-icon">
                      <el-icon v-if="notification.type === 'merchant_refund_request'" color="#f56c6c">
                        <Money />
                      </el-icon>
                      <el-icon v-else-if="notification.type === 'merchant_new_order'" color="#67c23a">
                        <ShoppingBag />
                      </el-icon>
                      <el-icon v-else color="#409eff">
                        <Bell />
                      </el-icon>
                    </div>
                    <div class="notification-content">
                      <div class="notification-title">{{ notification.title }}</div>
                      <div class="notification-message">{{ notification.content }}</div>
                      <div class="notification-time">{{ formatTime(notification.timestamp * 1000) }}</div>
                    </div>
                    <div class="notification-status" v-if="!notification.read">
                      <span class="unread-dot"></span>
                    </div>
                  </div>
                </div>

                <div class="notification-empty" v-else>
                  <el-icon><Bell /></el-icon>
                  <span>暂无通知</span>
                </div>
              </div>
            </el-popover>

            <div class="chat-status">
              <span class="status-indicator" :class="connectionStatus"></span>
              <span class="status-text">{{ statusText }}</span>
            </div>
          </div>
        </div>
      </template>

      <!-- 会话列表 -->
      <div v-if="!currentSession" class="session-list-container">
        <div class="session-filters">
          <el-button-group>
            <el-button 
              :type="sessionFilter === 'all' ? 'primary' : 'default'"
              size="small"
              @click="setSessionFilter('all')"
            >
              全部 ({{ sessions.length }})
            </el-button>
            <el-button 
              :type="sessionFilter === 'unread' ? 'primary' : 'default'"
              size="small"
              @click="setSessionFilter('unread')"
            >
              未读 ({{ unreadSessionCount }})
            </el-button>
            <el-button 
              :type="sessionFilter === 'urgent' ? 'primary' : 'default'"
              size="small"
              @click="setSessionFilter('urgent')"
            >
              紧急 ({{ urgentSessionCount }})
            </el-button>
          </el-button-group>
        </div>

        <div class="session-list">
          <div 
            v-for="session in filteredSessions" 
            :key="session.id"
            class="session-item"
            :class="{ 'has-unread': session.unread_count > 0 }"
            @click="selectSession(session)"
          >
            <div class="session-avatar">
              <el-avatar
                :src="session.target_avatar || session.customer_info?.avatar"
                :size="40"
              >
                {{ (session.target_name || session.customer_info?.name || '客户').charAt(0) }}
              </el-avatar>
              <span
                v-if="session.customer_info?.is_online"
                class="online-indicator"
                :class="{
                  'status-active': session.customer_info?.online_status === 'active',
                  'status-idle': session.customer_info?.online_status === 'idle'
                }"
                :title="getOnlineStatusText(session.customer_info)"
              ></span>
            </div>
            
            <div class="session-content">
              <div class="session-header">
                <span class="customer-name">
                  {{ session.target_name || session.customer_info?.name || '客户' }}
                </span>
                <span class="session-time">
                  {{ formatTime(session.updated_at) }}
                </span>
              </div>
              
              <div class="session-preview">
                <span class="last-message">
                  {{ getMessagePreview(session.last_message) }}
                </span>
                <el-badge
                  v-if="session.unread_count > 0"
                  :value="session.unread_count"
                  :max="99"
                  class="unread-badge"
                />
              </div>
              
              <div class="session-tags" v-if="session.customer_info">
                <el-tag 
                  v-if="session.customer_info.level" 
                  size="small" 
                  type="warning"
                >
                  VIP{{ session.customer_info.level }}
                </el-tag>
                <el-tag 
                  v-if="session.customer_info.is_new_customer" 
                  size="small" 
                  type="success"
                >
                  新客户
                </el-tag>
              </div>
            </div>
          </div>
          
          <div v-if="filteredSessions.length === 0" class="empty-sessions">
            <el-empty description="暂无会话" />
          </div>
        </div>
      </div>

      <!-- 聊天窗口 -->
      <div v-else class="chat-window-container">
        <!-- 聊天头部 -->
        <div class="chat-window-header">
          <el-button 
            :icon="ArrowLeft" 
            text 
            @click="backToSessionList"
            class="back-btn"
          >
            返回
          </el-button>
          
          <div class="customer-info">
            <el-avatar
              :src="currentSession.target_avatar || currentSession.customer_info?.avatar"
              :size="32"
            >
              {{ (currentSession.target_name || currentSession.customer_info?.name || '客户').charAt(0) }}
            </el-avatar>
            <div class="customer-details">
              <span class="customer-name">
                {{ currentSession.target_name || currentSession.customer_info?.name || '客户' }}
              </span>
              <span class="customer-status">
                <span
                  class="status-dot"
                  :class="{
                    'online': currentSession.customer_info?.is_online,
                    'active': currentSession.customer_info?.online_status === 'active',
                    'idle': currentSession.customer_info?.online_status === 'idle'
                  }"
                ></span>
                {{ getDetailedStatusText(currentSession.customer_info) }}
              </span>
            </div>
          </div>
          
          <el-dropdown trigger="click">
            <el-button :icon="MoreFilled" text />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="viewCustomerProfile">
                  <el-icon><User /></el-icon>
                  客户资料
                </el-dropdown-item>
                <el-dropdown-item @click="viewOrderHistory">
                  <el-icon><List /></el-icon>
                  订单历史
                </el-dropdown-item>
                <el-dropdown-item @click="transferSession">
                  <el-icon><Switch /></el-icon>
                  转接客服
                </el-dropdown-item>
                <el-dropdown-item divided @click="closeSession">
                  <el-icon><Close /></el-icon>
                  结束会话
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <!-- 消息列表 -->
        <div class="message-list-container">
          <div class="message-list" ref="messageListRef">
            <!-- 加载更多按钮 -->
            <div v-if="hasMoreHistory" class="load-more-container">
              <el-button
                :loading="loadingHistory"
                @click="loadMoreHistory"
                size="small"
                type="primary"
                text
              >
                {{ loadingHistory ? '加载中...' : '加载更多历史记录' }}
              </el-button>
            </div>

            <div
              v-for="message in messages"
              :key="message.id"
              class="message-item"
              :class="{ 'own-message': message.sender_type === 'merchant' }"
            >
            <div class="message-avatar">
              <el-avatar 
                :src="getMessageAvatar(message)" 
                :size="32"
              >
                {{ getMessageSenderName(message).charAt(0) }}
              </el-avatar>
            </div>
            
            <div class="message-content">
              <div class="message-header">
                <span class="sender-name">
                  {{ getMessageSenderName(message) }}
                </span>
                <span class="message-time">
                  {{ formatMessageTime(message.created_at) }}
                </span>
              </div>
              
              <div class="message-bubble">
                <div v-if="message.type === 'text'" class="text-message">
                  {{ message.content }}
                </div>
                <div v-else-if="message.type === 'image'" class="image-message">
                  <el-image 
                    :src="getMediaUrl(message.resource_id)" 
                    fit="cover"
                    class="message-image"
                    :preview-src-list="[getMediaUrl(message.resource_id)]"
                  />
                </div>
                <div v-else-if="message.type === 'file'" class="file-message">
                  <div class="file-info">
                    <el-icon><Document /></el-icon>
                    <div class="file-details">
                      <span class="file-name">{{ (message as any).file_name }}</span>
                      <span class="file-size">{{ formatFileSize((message as any).file_size) }}</span>
                    </div>
                  </div>
                  <el-button 
                    size="small" 
                    @click="downloadFile(message)"
                  >
                    下载
                  </el-button>
                </div>
              </div>
              
              <div 
                v-if="message.sender_type === 'merchant'" 
                class="message-status"
              >
                <el-icon v-if="message.status === 'sent'" class="status-sent">
                  <Check />
                </el-icon>
                <el-icon v-else-if="message.status === 'delivered'" class="status-delivered">
                  <Check />
                </el-icon>
                <el-icon v-else-if="message.status === 'read'" class="status-read">
                  <Check />
                </el-icon>
              </div>
            </div>
          </div>
          
            <div v-if="isTyping" class="typing-indicator">
              <span>客户正在输入...</span>
            </div>
          </div>
        </div>

        <!-- 快捷回复 -->
        <div v-if="quickReplies.length > 0" class="quick-replies">
          <div class="quick-replies-header">
            <span>快捷回复</span>
            <el-button 
              text 
              size="small" 
              @click="toggleQuickReplies"
            >
              {{ showQuickReplies ? '收起' : '展开' }}
            </el-button>
          </div>
          <div v-show="showQuickReplies" class="quick-replies-content">
            <el-button 
              v-for="reply in quickReplies" 
              :key="reply.id"
              size="small"
              @click="sendQuickReply(reply.content)"
              class="quick-reply-btn"
            >
              {{ reply.title }}
            </el-button>
          </div>
        </div>

        <!-- 消息输入 -->
        <div class="message-input-container">
          <div class="input-tools">
            <el-button 
              :icon="Picture" 
              text 
              size="small" 
              @click="selectImage"
              title="发送图片"
            />
            <el-button 
              :icon="Paperclip" 
              text 
              size="small" 
              @click="selectFile"
              title="发送文件"
            />
            <el-button 
              :icon="ChatLineRound" 
              text 
              size="small" 
              @click="showTemplates"
              title="消息模板"
            />
          </div>
          
          <div class="input-area">
            <el-input 
              v-model="messageInput"
              type="textarea"
              :rows="3"
              placeholder="输入回复消息..."
              :maxlength="1000"
              show-word-limit
              @keydown.ctrl.enter="sendMessage"
              @keydown.meta.enter="sendMessage"
            />
            <div class="input-footer">
              <span class="input-tip">Ctrl+Enter 发送</span>
              <el-button 
                type="primary" 
                size="small"
                @click="sendMessage"
                :disabled="!messageInput.trim()"
                :loading="sending"
              >
                发送
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 隐藏的文件输入 -->
    <input 
      ref="imageInputRef" 
      type="file" 
      accept="image/*" 
      style="display: none" 
      @change="handleImageSelect"
    />
    <input 
      ref="fileInputRef" 
      type="file" 
      style="display: none" 
      @change="handleFileSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, withDefaults, defineProps, inject, watch } from 'vue'
import {
  ChatDotRound,
  ArrowLeft,
  MoreFilled,
  User,
  List,
  Switch,
  Close,
  Check,
  Picture,
  Paperclip,
  ChatLineRound,
  Document,
  Bell,
  Money,
  ShoppingBag
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox /* , ElNotification */ } from 'element-plus'
import { useMerchantStore } from '@/modules/merchant/stores/merchantStore'
import { formatTime } from '@/utils/format'
// Props
interface Props {
  merchantId?: number
  merchantToken?: string
}

withDefaults(defineProps<Props>(), {
  merchantId: 0,
  merchantToken: ''
})

// Store
const merchantStore = useMerchantStore()

// Refs
const showChat = ref(false)
const currentSession = ref<any>(null)
const sessions = ref<any[]>([])
const messages = ref<any[]>([])
const messageInput = ref('')
const sending = ref(false)
const isTyping = ref(false)
const sessionFilter = ref('all')
const showQuickReplies = ref(true)
const connectionStatus = ref('disconnected')
const messageListRef = ref<HTMLElement>()
const imageInputRef = ref<HTMLInputElement>()
const fileInputRef = ref<HTMLInputElement>()

// 分页和缓存相关
const loadingHistory = ref(false)
const hasMoreHistory = ref(true)
const currentPage = ref(1)
const pageSize = 10
const messageCache = ref<Map<number, any[]>>(new Map()) // 按会话ID缓存消息

// 通知管理相关
const notifications = ref<any[]>([]) // 通知列表
// const notificationSettings = ref({
//   enabled: true, // 是否启用通知
//   sound: true, // 是否播放声音
//   desktop: true, // 是否显示桌面通知
//   refund: true, // 是否显示退款通知
//   newOrder: true, // 是否显示新订单通知
//   position: 'top-right' as const // 通知位置
// })

// 状态同步定时器
let statusSyncTimer: ReturnType<typeof setInterval> | null = null

// UI初始化状态
let isUIInitialized = false

// 注入后台聊天Store
const backgroundChatStore = inject('chatStore')

// 获取后台服务实例的辅助函数
const getBackgroundService = () => {
  if (!backgroundChatStore) {
    console.warn('后台聊天Store不可用')
    return null
  }
  return backgroundChatStore
}

// 快捷回复数据
const quickReplies = ref([
  { id: 1, title: '您好，有什么可以帮助您的吗？', content: '您好，有什么可以帮助您的吗？' },
  { id: 2, title: '请稍等，我来为您查询', content: '请稍等，我来为您查询' },
  { id: 3, title: '感谢您的咨询', content: '感谢您的咨询，如有其他问题请随时联系我们' },
  { id: 4, title: '商品详情', content: '请问您想了解哪个商品的详细信息？' },
  { id: 5, title: '订单查询', content: '请提供您的订单号，我来为您查询订单状态' }
])

// Computed
const unreadCount = computed(() => {
  return sessions.value.reduce((total, session) => total + (session.unread_count || 0), 0)
})

const unreadSessionCount = computed(() => {
  return sessions.value.filter(session => session.unread_count > 0).length
})

const urgentSessionCount = computed(() => {
  return sessions.value.filter(session => session.is_urgent).length
})

// 未读通知数量
const unreadNotificationCount = computed(() => {
  return notifications.value.filter(notification => !notification.read).length
})

const filteredSessions = computed(() => {
  switch (sessionFilter.value) {
    case 'unread':
      return sessions.value.filter(session => session.unread_count > 0)
    case 'urgent':
      return sessions.value.filter(session => session.is_urgent)
    default:
      return sessions.value
  }
})

const statusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中...'
    case 'disconnected':
      return '已断开'
    case 'error':
      return '连接错误'
    default:
      return '未知状态'
  }
})

// Methods
const toggleChat = () => {
  showChat.value = !showChat.value

  // 只有在打开聊天窗口时才加载数据
  if (showChat.value) {
    const service = getBackgroundService()
    if (!service) {
      console.warn('后台聊天服务未启动，请等待服务启动')
      ElMessage.warning('聊天服务正在启动中，请稍后再试')
      return
    }

    // 确保UI已初始化
    if (!isUIInitialized) {
      console.log('UI未初始化，先初始化聊天UI')
      initChatUI()
    }

    if ((service as any)?.isConnected) {
      console.log('后台聊天服务已连接，加载会话列表')
      loadSessions()
    } else {
      console.warn('后台聊天服务未连接，请等待连接建立')
      ElMessage.warning('聊天服务正在连接中，请稍后再试')

      // 更新连接状态显示
      updateConnectionStatus()
    }
  }
}

// 清理事件监听器
const cleanupEventListeners = () => {
  const service = getBackgroundService()
  if (service) {
    console.log('清理事件监听器')
    // chatStore不支持事件监听器，无需清理
    console.log('清理MerchantChat组件')
  }
}

// 连接状态事件处理器
// const handleConnected = () => {
//   console.log('收到connected事件，更新UI状态')
//   connectionStatus.value = 'connected'
// }

// const handleDisconnected = () => {
//   console.log('收到disconnected事件，更新UI状态')
//   connectionStatus.value = 'disconnected'
// }

// const handleError = () => {
//   console.log('收到error事件，更新UI状态')
//   connectionStatus.value = 'error'
// }

// 初始化聊天UI（注册事件监听器）
const initChatUI = () => {
  const service = getBackgroundService()
  if (!service) {
    console.warn('后台聊天服务不可用，等待服务启动')
    connectionStatus.value = 'disconnected'
    isUIInitialized = false
    return
  }

  // 避免重复初始化
  if (isUIInitialized) {
    console.log('聊天UI已初始化，只更新连接状态')
    updateConnectionStatus()
    return
  }

  console.log('开始初始化聊天UI')

  // 先清理可能存在的旧监听器
  cleanupEventListeners()

  // 注册事件监听器
  console.log('注册事件监听器')
  // chatStore不支持事件监听，使用watch监听状态变化
  console.log('MerchantChat组件已连接到chatStore')

  console.log('事件监听器注册完成')

  // 同步当前连接状态
  updateConnectionStatus()

  // 启动状态同步定时器
  startStatusSync()

  // 标记为已初始化
  isUIInitialized = true

  console.log('聊天UI初始化完成，当前状态:', connectionStatus.value)
}

// 更新连接状态的辅助函数
const updateConnectionStatus = () => {
  const service = getBackgroundService()
  if (!service) {
    console.log('服务不可用，设置状态为disconnected')
    connectionStatus.value = 'disconnected'
    return
  }

  // 直接从服务的status获取状态，而不是isConnected
  const currentStatus = (service as any)?.clientStatus
  console.log('WebSocket服务当前状态:', currentStatus)

  // 将WebSocket状态映射到UI状态
  switch (currentStatus) {
    case 'connected':
      connectionStatus.value = 'connected'
      break
    case 'connecting':
      connectionStatus.value = 'connecting'
      break
    case 'reconnecting':
      connectionStatus.value = 'connecting'
      break
    case 'error':
      connectionStatus.value = 'error'
      break
    default:
      connectionStatus.value = 'disconnected'
  }

  console.log('UI连接状态已更新为:', connectionStatus.value)
}

// 启动状态同步定时器
const startStatusSync = () => {
  // 清除现有定时器
  if (statusSyncTimer) {
    clearInterval(statusSyncTimer)
  }

  // 每5秒同步一次状态
  statusSyncTimer = setInterval(() => {
    updateConnectionStatus()
  }, 5000)
}

// 停止状态同步定时器
const stopStatusSync = () => {
  if (statusSyncTimer) {
    clearInterval(statusSyncTimer)
    statusSyncTimer = null
  }
}

/**
 * 加载会话列表
 */
const loadSessions = async () => {
  const service = getBackgroundService()
  if (!service) {
    console.warn('WebSocket服务未初始化')
    return
  }
  
  try {
    const sessionList = await (service as any)?.getSessions()
    
    // 确保获取到的是数组
    if (Array.isArray(sessionList)) {
      sessions.value = sessionList
      console.log(`成功加载 ${sessionList.length} 个会话`)
    } else {
      console.warn('获取到的会话列表格式异常:', sessionList)
      sessions.value = []
    }
  } catch (error) {
    console.error('加载会话列表失败:', error)
    sessions.value = [] // 确保即使失败也有一个空数组
    
    // 根据错误类型显示不同的提示
    if (error instanceof TypeError && error.message.includes('filter')) {
      ElMessage.error('数据格式错误，请检查后端接口返回格式')
    } else {
      ElMessage.error('加载会话列表失败，请稍后重试')
    }
  }
}

const selectSession = async (session: any) => {
  currentSession.value = session

  // 重置分页状态
  currentPage.value = 1
  hasMoreHistory.value = true

  try {
    // 先检查缓存
    const cachedMessages = messageCache.value.get(session.id)
    if (cachedMessages && cachedMessages.length > 0) {
      console.log(`从缓存加载会话 ${session.id} 的消息，共 ${cachedMessages.length} 条`)
      messages.value = [...cachedMessages]

      // 标记为已读
      session.unread_count = 0

      // 滚动到底部
      await nextTick()
      scrollToBottom()
      return
    }

    // 缓存中没有，从服务器加载最近的消息
    const result = await loadMessages(session.id, 1, true)
    console.log(`首次加载会话 ${session.id}，获得 ${result.messages.length} 条消息`)

    // 标记为已读
    session.unread_count = 0

    // 滚动到底部
    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('加载消息失败:', error)
    ElMessage.error('加载消息失败')
  }
}

// 加载消息的通用函数
const loadMessages = async (sessionId: number, page: number, isInitial: boolean = false) => {
  const service = getBackgroundService()
  if (!service) {
    throw new Error('WebSocket服务未初始化')
  }

  console.log(`加载会话 ${sessionId} 第 ${page} 页消息，每页 ${pageSize} 条`)

  // 使用支持分页的fetchMessages方法
  const result = await (service as any)?.getMessages(sessionId, page, pageSize)
  const { messages: messageList, hasMore, total } = result

  console.log(`API返回: ${messageList.length} 条消息，hasMore: ${hasMore}, total: ${total}`)

  // 处理消息数据，确保sender_type字段正确
  const processedMessages = messageList.map((message: any) => {
    if (message.sender_type === 'user') {
      message.sender_type = 'customer'
    }
    return message
  })

  // 消息已经在API层排序，这里不需要再次排序
  const sortedMessages = processedMessages

  if (isInitial) {
    // 初始加载，直接设置消息
    messages.value = sortedMessages
    // 缓存消息
    messageCache.value.set(sessionId, [...sortedMessages])
  } else {
    // 加载更多，插入到消息列表前面
    const existingMessages = messages.value
    const newMessages = [...sortedMessages, ...existingMessages]

    // 去重（基于消息ID）
    const uniqueMessages = newMessages.filter((message, index, array) =>
      array.findIndex(m => m.id === message.id) === index
    )

    messages.value = uniqueMessages
    // 更新缓存
    messageCache.value.set(sessionId, [...uniqueMessages])
  }

  // 更新是否还有更多历史记录的状态
  hasMoreHistory.value = hasMore

  console.log(`加载了 ${sortedMessages.length} 条消息，当前总数: ${messages.value.length}，还有更多: ${hasMore}`)
  return { messages: sortedMessages, hasMore, total }
}

// 加载更多历史记录
const loadMoreHistory = async () => {
  if (!currentSession.value || loadingHistory.value || !hasMoreHistory.value) {
    return
  }

  loadingHistory.value = true

  try {
    const nextPage = currentPage.value + 1
    const result = await loadMessages(currentSession.value.id, nextPage, false)
    currentPage.value = nextPage

    console.log(`加载更多历史记录完成，当前页数: ${currentPage.value}，还有更多: ${result.hasMore}`)
  } catch (error) {
    console.error('加载更多历史记录失败:', error)
    ElMessage.error('加载历史记录失败')
  } finally {
    loadingHistory.value = false
  }
}

const backToSessionList = () => {
  currentSession.value = null
  messages.value = []
  // 重置分页状态
  currentPage.value = 1
  hasMoreHistory.value = true
}

const sendMessage = async () => {
  const service = getBackgroundService()
  if (!messageInput.value.trim() || !currentSession.value || !service) return

  try {
    sending.value = true

    // 保存消息内容（因为发送成功后会清空输入框）
    const messageContent = messageInput.value.trim()

    // 使用HTTP API发送消息，而不是WebSocket
    const result = await (service as any)?.sendMessage({
       sessionId: currentSession.value.id,
       content: messageContent,
       type: 'text'
     })

    console.log('消息通过HTTP API发送成功:', result)

    // 清空输入框
    messageInput.value = ''

    // 不需要手动添加消息到本地列表
    // WebSocket会推送服务器确认的消息，确保数据一致性
    // 这样可以避免重复消息和数据不一致的问题

    // 滚动到底部
    await nextTick()
    scrollToBottom()

    ElMessage.success('消息发送成功')
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败')
  } finally {
    sending.value = false
  }
}

const sendQuickReply = (content: string) => {
  messageInput.value = content
  sendMessage()
}

const setSessionFilter = (filter: string) => {
  sessionFilter.value = filter
}

const toggleQuickReplies = () => {
  showQuickReplies.value = !showQuickReplies.value
}

const selectImage = () => {
  imageInputRef.value?.click()
}

const selectFile = () => {
  fileInputRef.value?.click()
}

const handleImageSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    // TODO: 实现图片上传
    console.log('选择图片:', file)
  }
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    // TODO: 实现文件上传
    console.log('选择文件:', file)
  }
}

const showTemplates = () => {
  // TODO: 显示消息模板
  ElMessage.info('消息模板功能开发中')
}

const viewCustomerProfile = () => {
  // TODO: 查看客户资料
  ElMessage.info('客户资料功能开发中')
}

const viewOrderHistory = () => {
  // TODO: 查看订单历史
  ElMessage.info('订单历史功能开发中')
}

const transferSession = () => {
  // TODO: 转接客服
  ElMessage.info('转接客服功能开发中')
}

const closeSession = async () => {
  try {
    await ElMessageBox.confirm('确定要结束此会话吗？', '确认', {
      type: 'warning'
    })
    
    // TODO: 实现结束会话
    backToSessionList()
    ElMessage.success('会话已结束')
  } catch {
    // 用户取消
  }
}

// const handleMessage = (message: any) => {
//   console.log('收到消息事件:', message)

//   // 确保消息数据有效
//   if (!message || !message.data) {
//     console.warn('收到无效消息数据:', message)
//     return
//   }

//   handleNewMessage(message)
// }

// const handleNewMessage = (message: any) => {
//   console.log('处理新消息:', message)

//   if (message.event === 'new_message') {
//     const messageData = message.data

//     // 验证消息数据完整性
//     if (!messageData || !messageData.session_id) {
//       console.warn('消息数据不完整:', messageData)
//       return
//     }

//     console.log(`处理会话 ${messageData.session_id} 的新消息:`, messageData.content)

//     // 如果是当前会话的消息，按时间顺序插入到消息列表
//     if (currentSession.value && messageData.session_id === currentSession.value.id) {
//       console.log('插入消息到当前会话')
//       insertMessageInOrder(messageData)

//       // 确保界面更新后滚动到底部
//       nextTick(() => {
//         console.log('消息插入完成，滚动到底部')
//         scrollToBottom()
//       })
//     } else {
//       console.log('消息不属于当前会话，当前会话ID:', currentSession.value?.id)
//     }

//     // 更新会话列表中的未读数和最后消息
//     const session = sessions.value.find(s => s.id === messageData.session_id)
//     if (session) {
//       if (messageData.sender_type !== 'merchant') {
//         session.unread_count = (session.unread_count || 0) + 1
//         console.log(`会话 ${session.id} 未读数更新为:`, session.unread_count)
//       }
//       session.last_message = messageData
//       session.updated_at = messageData.created_at
//     } else {
//       console.warn('未找到对应的会话:', messageData.session_id)
//     }

//     // 播放提示音
//     playNotificationSound()
//   } else {
//     console.log('非new_message事件，忽略:', message.event)
//   }
// }

// const handleNotification = (notification: any) => {
//   console.log('收到通知:', notification)

//   // 检查通知设置是否启用
//   if (!notificationSettings.value.enabled) {
//     return
//   }

//   // 处理不同类型的通知
//   if (notification.event === 'merchant_refund_request') {
//     handleRefundNotification(notification)
//   } else if (notification.event === 'merchant_new_order') {
//     handleNewOrderNotification(notification)
//   } else {
//     // 处理其他类型的通知
//     handleGeneralNotification(notification)
//   }

//   // 添加到通知列表
//   addNotificationToList(notification)
// }

// 处理退款通知
// const handleRefundNotification = (notification: any) => {
//   if (!notificationSettings.value.refund) {
//     return
//   }

//   const data = notification.data
//   if (!data) {
//     console.warn('退款通知数据不完整:', notification)
//     return
//   }

//   // 显示Element Plus通知
//   ElNotification({
//     title: data.title || '收到退款申请',
//     message: data.content || data.message || '有新的退款申请需要处理',
//     type: 'warning',
//     position: notificationSettings.value.position,
//     duration: 8000, // 退款通知显示8秒
//     showClose: true,
//     onClick: () => {
//       // 点击通知时跳转到退款详情页
//       if (data.action_url) {
//         window.open(data.action_url, '_blank')
//       }
//     }
//   })

//   // 播放提示音
//   if (notificationSettings.value.sound) {
//     playNotificationSound()
//   }

//   // 显示桌面通知
//   if (notificationSettings.value.desktop) {
//     showDesktopNotification({
//       title: data.title || '收到退款申请',
//       body: data.content || data.message || '有新的退款申请需要处理',
//       icon: '/icons/refund-notification.png',
//       tag: `refund_${notification.timestamp}`,
//       onClick: () => {
//         if (data.action_url) {
//           window.open(data.action_url, '_blank')
//         }
//       }
//     })
//   }
// }

// 处理新订单通知
// const handleNewOrderNotification = (notification: any) => {
//   if (!notificationSettings.value.enabled || !notificationSettings.value.newOrder) {
//     return
//   }

//   const data = notification.data
//   if (!data) {
//     console.warn('新订单通知数据不完整:', notification)
//     return
//   }

//   // 显示Element Plus通知
//   ElNotification({
//     title: data.title || '收到新订单',
//     message: data.content || '您有新的订单需要处理',
//     type: 'success',
//     position: notificationSettings.value.position,
//     duration: 6000, // 新订单通知显示6秒
//     showClose: true,
//     onClick: () => {
//       // 点击通知时跳转到订单详情页
//       if (data.data?.order_id) {
//         window.open(`/merchant/order/detail/${data.data.order_id}`, '_blank')
//       } else if (data.data?.action_url) {
//         window.open(data.data.action_url, '_blank')
//       }
//     }
//   })

//   // 播放提示音
//   if (notificationSettings.value.sound) {
//     playNotificationSound()
//   }

//   // 显示桌面通知
//   if (notificationSettings.value.desktop) {
//     showDesktopNotification({
//       title: data.title || '收到新订单',
//       body: data.content || '您有新的订单需要处理',
//       icon: '/icons/order-notification.png',
//       tag: `new_order_${notification.timestamp}`,
//       onClick: () => {
//         if (data.data?.order_id) {
//           window.open(`/merchant/order/detail/${data.data.order_id}`, '_blank')
//         } else if (data.data?.action_url) {
//           window.open(data.data.action_url, '_blank')
//         }
//       }
//     })
//   }
// }

// 处理一般通知
// const handleGeneralNotification = (notification: any) => {
//   const data = notification.data
//   if (!data) {
//     console.warn('通知数据不完整:', notification)
//     return
//   }

//   // 显示Element Plus通知
//   ElNotification({
//     title: data.title || '新通知',
//     message: data.content || data.message || '您有新的通知',
//     type: 'info',
//     position: notificationSettings.value.position,
//     duration: 5000,
//     showClose: true,
//     onClick: () => {
//       if (data.action_url) {
//         window.open(data.action_url, '_blank')
//       }
//     }
//   })

//   // 播放提示音
//   if (notificationSettings.value.sound) {
//     playNotificationSound()
//   }
// }

// 添加通知到列表
// const addNotificationToList = (notification: any) => {
//   const notificationItem = {
//     id: `${notification.event}_${notification.timestamp}`,
//     type: notification.event,
//     title: notification.data?.title || '新通知',
//     content: notification.data?.content || notification.data?.message || '',
//     timestamp: notification.timestamp,
//     read: false,
//     data: notification.data
//   }

//   // 添加到通知列表开头
//   notifications.value.unshift(notificationItem)

//   // 限制通知列表长度，保留最近100条
//   if (notifications.value.length > 100) {
//     notifications.value = notifications.value.slice(0, 100)
//   }
// }

// 显示桌面通知
const showDesktopNotification = (options: {
  title: string
  body: string
  icon?: string
  tag?: string
  onClick?: () => void
}) => {
  if (!('Notification' in window)) {
    return
  }

  if (Notification.permission === 'granted') {
    const notification = new Notification(options.title, {
      body: options.body,
      icon: options.icon || '/icons/notification.png',
      tag: options.tag,
      requireInteraction: false
    })

    notification.onclick = () => {
      window.focus()
      if (options.onClick) {
        options.onClick()
      }
      notification.close()
    }

    // 自动关闭
    setTimeout(() => {
      notification.close()
    }, 8000)
  } else if (Notification.permission !== 'denied') {
    Notification.requestPermission().then(permission => {
      if (permission === 'granted') {
        showDesktopNotification(options)
      }
    })
  }
}

// 通知管理相关方法
const markAllNotificationsRead = () => {
  notifications.value.forEach(notification => {
    notification.read = true
  })
}

const clearAllNotifications = () => {
  notifications.value.length = 0
}

const handleNotificationClick = (notification: any) => {
  // 标记为已读
  notification.read = true

  // 如果有操作链接，打开链接
  if (notification.data?.action_url) {
    window.open(notification.data.action_url, '_blank')
  }
}

// 调试客户状态的辅助函数
// const debugCustomerStatus = (action: string, data: any) => {
//   if (import.meta.env.DEV) {
//     console.group(`🟢 客户状态调试 - ${action}`)
//     console.log('时间:', new Date().toLocaleString())
//     console.log('数据:', data)
//     console.log('当前会话列表状态:', sessions.value.map(s => ({
//       id: s.id,
//       customer: s.customer_info?.name || s.target_name,
//       online: s.customer_info?.is_online,
//       status: s.customer_info?.online_status
//     })))
//     console.groupEnd()
//   }
// }

// 处理客户状态更新
// const handleCustomerStatusUpdate = (statusUpdate: any) => {
//   debugCustomerStatus('收到状态更新', statusUpdate)

//   const { event, data, timestamp } = statusUpdate
//   const { session_ids, online_status } = data

//   // 更新相关会话的客户在线状态
//   if (session_ids && Array.isArray(session_ids)) {
//     session_ids.forEach((sessionId: number) => {
//       const session = sessions.value.find(s => s.id === sessionId)
//       if (session) {
//         // 更新客户信息中的在线状态
//         if (session.customer_info) {
//           const wasOnline = session.customer_info.is_online
//           session.customer_info.is_online = online_status !== 'offline'
//           session.customer_info.online_status = online_status
//           session.customer_info.last_seen = timestamp

//           console.log(`会话 ${sessionId} 客户状态更新: ${wasOnline ? '在线' : '离线'} -> ${session.customer_info.is_online ? '在线' : '离线'}`)
//         }

//         // 如果是当前会话，也更新当前会话的状态
//         if (currentSession.value && currentSession.value.id === sessionId) {
//           if (currentSession.value.customer_info) {
//             currentSession.value.customer_info.is_online = online_status !== 'offline'
//             currentSession.value.customer_info.online_status = online_status
//             currentSession.value.customer_info.last_seen = timestamp
//           }
//         }
//       }
//     })
//   }

//   // 显示状态变更提示（可选）
//   if (event === 'customer_online') {
//     ElMessage({
//       message: `客户 ${data.customer_name || '客户'} 已上线`,
//       type: 'info',
//       duration: 2000
//     })
//   } else if (event === 'customer_offline') {
//     ElMessage({
//       message: `客户 ${data.customer_name || '客户'} 已离线`,
//       type: 'info',
//       duration: 2000
//     })
//   }

//   debugCustomerStatus('状态更新完成', {
//     affected_sessions: session_ids,
//     new_status: online_status,
//     event: event
//   })
// }

/**
 * 按时间顺序插入消息到消息列表
 * @param messageData 要插入的消息数据
 */
// const insertMessageInOrder = (messageData: any) => {
//   console.log('开始插入消息:', messageData)

//   // 确保sender_type字段正确（后端可能返回'user'，需要转换为'customer'）
//   if (messageData.sender_type === 'user') {
//     messageData.sender_type = 'customer'
//     console.log('转换sender_type从user到customer')
//   }

//   const messageTime = new Date(messageData.created_at || messageData.timestamp || Date.now()).getTime()

//   // 检查是否已存在相同ID的消息，避免重复
//   const existingIndex = messages.value.findIndex(msg => msg.id === messageData.id)
//   if (existingIndex !== -1) {
//     console.log('消息已存在，跳过插入:', messageData.id)
//     return
//   }

//   // 找到正确的插入位置（按时间升序）
//   let insertIndex = messages.value.length
//   for (let i = messages.value.length - 1; i >= 0; i--) {
//     const existingTime = new Date(messages.value[i].created_at || messages.value[i].timestamp || 0).getTime()
//     if (existingTime <= messageTime) {
//       insertIndex = i + 1
//       break
//     }
//     insertIndex = i
//   }

//   console.log(`准备在位置 ${insertIndex} 插入消息，当前消息数量: ${messages.value.length}`)
//   console.log('消息发送者信息:', {
//     sender_type: messageData.sender_type,
//     sender_name: messageData.sender_name,
//     sender_avatar: messageData.sender_avatar
//   })

//   // 在正确位置插入消息
//   messages.value.splice(insertIndex, 0, messageData)

//   // 更新缓存
//   if (currentSession.value) {
//     messageCache.value.set(currentSession.value.id, [...messages.value])
//   }

//   console.log(`消息已按时间顺序插入到位置 ${insertIndex}:`, messageData.content)
//   console.log(`插入后消息数量: ${messages.value.length}`)

//   // 强制触发响应式更新
//   nextTick(() => {
//     console.log('消息插入完成，触发界面更新')
//   })
// }

const scrollToBottom = () => {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

// const playNotificationSound = () => {
//   try {
//     const audio = new Audio('/sounds/notification.mp3')
//     audio.volume = 0.5
//     audio.play().catch(e => console.log('播放提示音失败:', e))
//   } catch (error) {
//     console.log('播放提示音失败:', error)
//   }
// }

const getMessagePreview = (message: any) => {
  if (!message) return '暂无消息'
  
  const preview: Record<string, string> = {
    'text': message.content,
    'image': '[图片]',
    'file': `[文件] ${(message as any).file_name || ''}`,
    'voice': '[语音消息]',
    'video': '[视频消息]'
  }
  
  return preview[message.type] || '[消息]'
}

const getMessageAvatar = (message: any) => {
  if (message.sender_type === 'merchant') {
    return merchantStore.logo || ''
  } else {
    // 优先使用消息中的sender_avatar，如果没有则使用会话中的目标用户头像
    const avatar = message.sender_avatar ||
                   currentSession.value?.target_avatar ||
                   currentSession.value?.customer_info?.avatar || ''
    console.log('获取发送者头像:', avatar, '消息数据:', message)
    return avatar
  }
}

const getMessageSenderName = (message: any) => {
  if (message.sender_type === 'merchant') {
    return '我'
  } else {
    // 优先使用消息中的sender_name，如果没有则使用会话中的目标用户名称
    const senderName = message.sender_name ||
                       currentSession.value?.target_name ||
                       currentSession.value?.customer_info?.name || '客户'
    console.log('获取发送者名称:', senderName, '消息数据:', message)
    return senderName
  }
}

// 获取在线状态提示文本
const getOnlineStatusText = (customerInfo: any) => {
  if (!customerInfo?.is_online) return '离线'

  switch (customerInfo.online_status) {
    case 'active':
      return '在线 - 活跃'
    case 'idle':
      return '在线 - 空闲'
    default:
      return '在线'
  }
}

// 获取详细状态文本
const getDetailedStatusText = (customerInfo: any) => {
  if (!customerInfo?.is_online) {
    if (customerInfo?.last_seen) {
      const lastSeen = new Date(customerInfo.last_seen)
      const now = new Date()
      const diffMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60))

      if (diffMinutes < 60) {
        return `${diffMinutes}分钟前在线`
      } else if (diffMinutes < 1440) {
        return `${Math.floor(diffMinutes / 60)}小时前在线`
      } else {
        return '离线'
      }
    }
    return '离线'
  }

  switch (customerInfo.online_status) {
    case 'active':
      return '在线'
    case 'idle':
      return '在线 (空闲)'
    default:
      return '在线'
  }
}



const formatMessageTime = (time: string) => {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}

const getMediaUrl = (resourceId: string) => {
  return `${import.meta.env.VITE_API_BASE_URL}/api/v1/files/${resourceId}`
}

const formatFileSize = (size: number) => {
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / (1024 * 1024)).toFixed(1)}MB`
}

const downloadFile = (message: any) => {
  const url = getMediaUrl(message.resource_id)
  const link = document.createElement('a')
  link.href = url
  link.download = (message as any).file_name
  link.click()
}

// Lifecycle
onMounted(() => {
  // 请求通知权限
  if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission()
  }

  // 初始化聊天UI
  initChatUI()
})

onUnmounted(() => {
  console.log('MerchantChat组件卸载，清理资源')

  // 清理事件监听器
  cleanupEventListeners()

  // 清理状态同步定时器
  stopStatusSync()

  // 重置初始化状态
  isUIInitialized = false
})

// 监听后台服务可用性变化
watch(
  () => getBackgroundService(),
  (service, oldService) => {
    if (service && !oldService) {
      // 后台服务从不可用变为可用，重新初始化UI
      console.log('检测到后台服务可用，重新初始化聊天UI')
      // 重置初始化状态，强制重新初始化
      isUIInitialized = false
      initChatUI()
    } else if (!service && oldService) {
      // 后台服务从可用变为不可用
      console.log('检测到后台服务不可用')
      cleanupEventListeners()
      connectionStatus.value = 'disconnected'
      stopStatusSync()
      isUIInitialized = false
    }
  },
  { immediate: true }
)

// 监听后台服务状态变化
watch(
  () => {
    const service = getBackgroundService()
    return (service as any)?.clientStatus
  },
  (newStatus) => {
    if (newStatus) {
      console.log('监听到后台服务状态变化:', newStatus)
      updateConnectionStatus()
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.merchant-chat {
  .chat-badge {
    :deep(.el-badge__content) {
      background-color: #f56c6c;
    }
  }
  
  .chat-trigger-btn {
    &.active {
      background-color: var(--el-color-primary);
      color: white;
    }
  }
}

.merchant-chat-drawer {
  :deep(.el-drawer__body) {
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden; // 防止整体滚动
  }
}

.chat-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  .chat-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
  }
  
  .chat-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .notification-btn {
      position: relative;

      &.has-notifications {
        animation: pulse 2s infinite;
      }
    }

    .chat-status {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      color: #666;

      .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;

        &.connected {
          background-color: #67c23a;
        }

        &.connecting {
          background-color: #e6a23c;
          animation: pulse 1.5s infinite;
        }

        &.disconnected {
          background-color: #909399;
        }

        &.error {
          background-color: #f56c6c;
        }
      }
    }
  }
}

.session-list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .session-filters {
    padding: 16px;
    border-bottom: 1px solid #ebeef5;
  }
  
  .session-list {
    flex: 1;
    overflow-y: auto;
    
    .session-item {
      display: flex;
      padding: 12px 16px;
      cursor: pointer;
      border-bottom: 1px solid #f5f7fa;
      transition: background-color 0.2s;
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      &.has-unread {
        background-color: #fef0f0;
        
        &:hover {
          background-color: #fde2e2;
        }
      }
      
      .session-avatar {
        position: relative;
        margin-right: 12px;
        
        .online-indicator {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 12px;
          height: 12px;
          border: 2px solid white;
          border-radius: 50%;

          // 活跃状态 - 绿色
          &.status-active {
            background-color: #67c23a;
          }

          // 空闲状态 - 黄色
          &.status-idle {
            background-color: #e6a23c;
          }

          // 默认在线状态
          &:not(.status-active):not(.status-idle) {
            background-color: #67c23a;
          }
        }
      }
      
      .session-content {
        flex: 1;
        min-width: 0;
        
        .session-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
          
          .customer-name {
            font-weight: 500;
            color: #303133;
          }
          
          .session-time {
            font-size: 12px;
            color: #909399;
          }
        }
        
        .session-preview {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .last-message {
            flex: 1;
            font-size: 13px;
            color: #606266;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .unread-badge {
            margin-left: 8px;
          }
        }
        
        .session-tags {
          display: flex;
          gap: 4px;
        }
      }
    }
    
    .empty-sessions {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
    }
  }
}

.chat-window-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100vh;
  overflow: hidden;
  
  .chat-window-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #ebeef5;
    flex-shrink: 0; // 防止头部被压缩
    min-height: 60px; // 设置最小高度
    
    .back-btn {
      margin-right: 12px;
    }
    
    .customer-info {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 8px;
      
      .customer-details {
        display: flex;
        flex-direction: column;
        
        .customer-name {
          font-weight: 500;
          color: #303133;
        }
        
        .customer-status {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #909399;
          
          .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #909399;

            &.online {
              &.active {
                background-color: #67c23a;
              }

              &.idle {
                background-color: #e6a23c;
              }

              &:not(.active):not(.idle) {
                background-color: #67c23a;
              }
            }
          }
        }
      }
    }
  }
  
  .message-list-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0; // 重要：允许flex子项收缩
    height: 0; // 强制使用flex计算的高度
  }

  .message-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    scroll-behavior: smooth;
    // 使用flex布局自动计算高度，不设置固定max-height
    // 这样可以适应不同的头部和底部高度

    .load-more-container {
      text-align: center;
      padding: 16px 0;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 16px;

      .el-button {
        font-size: 13px;
      }
    }

    .message-item {
      display: flex;
      margin-bottom: 16px;
      
      &.own-message {
        flex-direction: row-reverse;
        
        .message-content {
          align-items: flex-end;
          
          .message-bubble {
            background-color: var(--el-color-primary);
            color: white;
          }
        }
      }
      
      .message-avatar {
        margin: 0 8px;
      }
      
      .message-content {
        display: flex;
        flex-direction: column;
        max-width: 70%;
        
        .message-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 4px;
          
          .sender-name {
            font-size: 12px;
            color: #606266;
            font-weight: 500;
          }
          
          .message-time {
            font-size: 12px;
            color: #c0c4cc;
          }
        }
        
        .message-bubble {
          padding: 8px 12px;
          border-radius: 8px;
          background-color: #ffffff;
          border: 1px solid #e4e7ed;
          word-wrap: break-word;
          color: #303133;

          .text-message {
            line-height: 1.4;
            color: #303133;
          }
          
          .image-message {
            .message-image {
              max-width: 200px;
              border-radius: 4px;
            }
          }
          
          .file-message {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .file-info {
              display: flex;
              align-items: center;
              gap: 8px;
              
              .file-details {
                display: flex;
                flex-direction: column;
                
                .file-name {
                  font-size: 14px;
                  color: #303133;
                }
                
                .file-size {
                  font-size: 12px;
                  color: #909399;
                }
              }
            }
          }
        }
        
        .message-status {
          align-self: flex-end;
          margin-top: 4px;
          
          .status-sent {
            color: #909399;
          }
          
          .status-delivered {
            color: #67c23a;
          }
          
          .status-read {
            color: var(--el-color-primary);
          }
        }
      }
    }
    
    .typing-indicator {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      color: #909399;
      font-size: 13px;
      font-style: italic;
    }
  }
  
  .quick-replies {
    border-top: 1px solid #ebeef5;
    
    .quick-replies-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 16px;
      background-color: #f5f7fa;
      font-size: 13px;
      color: #606266;
    }
    
    .quick-replies-content {
      padding: 12px 16px;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .quick-reply-btn {
        font-size: 12px;
      }
    }
  }
  
  .message-input-container {
    border-top: 1px solid #ebeef5;
    flex-shrink: 0; // 防止输入区域被压缩
    background: white; // 确保背景色
    
    .input-tools {
      display: flex;
      padding: 8px 16px;
      border-bottom: 1px solid #f5f7fa;
      gap: 4px;
    }
    
    .input-area {
      padding: 16px;
      
      .input-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;
        
        .input-tip {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 通知管理样式 */

.notification-panel {
  .notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0 8px 0;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 8px;

    .notification-title {
      font-weight: 600;
      color: #303133;
    }

    .notification-actions {
      display: flex;
      gap: 8px;
    }
  }

  .notification-list {
    max-height: 300px;
    overflow-y: auto;

    .notification-item {
      display: flex;
      align-items: flex-start;
      padding: 12px 8px;
      border-radius: 6px;
      cursor: pointer;
      transition: background-color 0.2s;
      position: relative;

      &:hover {
        background-color: #f5f7fa;
      }

      &.unread {
        background-color: #f0f9ff;
        border-left: 3px solid #409eff;
      }

      .notification-icon {
        margin-right: 12px;
        margin-top: 2px;
        flex-shrink: 0;
      }

      .notification-content {
        flex: 1;
        min-width: 0;

        .notification-title {
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
          font-size: 14px;
        }

        .notification-message {
          color: #606266;
          font-size: 12px;
          line-height: 1.4;
          margin-bottom: 4px;
          word-break: break-word;
        }

        .notification-time {
          color: #909399;
          font-size: 11px;
        }
      }

      .notification-status {
        flex-shrink: 0;
        margin-left: 8px;

        .unread-dot {
          display: inline-block;
          width: 8px;
          height: 8px;
          background-color: #f56c6c;
          border-radius: 50%;
        }
      }
    }
  }

  .notification-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #909399;

    .el-icon {
      font-size: 32px;
      margin-bottom: 8px;
      opacity: 0.5;
    }

    span {
      font-size: 14px;
    }
  }
}

/* 通知弹窗样式 */
:global(.notification-popover) {
  padding: 12px !important;
}
</style>