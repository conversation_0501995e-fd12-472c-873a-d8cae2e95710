<!-- 
 * 商品图片上传组件
 * 用于上传商品的主图和详情图片
 * 使用通用FileUploader组件实现
 */
-->
<template>
  <div class="food-image-upload">
    <el-form label-position="top">
      <el-form-item label="商品主图">
        <FileUploader
          ref="mainImageUploaderRef"
          action="/v1/merchant/upload"
          :file-limit="1"
          :size-limit="5 * 1024 * 1024"
          accept=".jpg,.jpeg,.png"
          file-usage="product_main"
          :initial-files="initialMainImageFile"
          :auto-upload="true"
          @success="handleMainImageSuccess"
          @remove="handleMainImageRemove"
        >
          <template #tip>
            <p>点击或拖拽图片到此处上传商品主图</p>
            <p>建议尺寸: 800x800 像素，支持 JPG、PNG 格式</p>
          </template>
        </FileUploader>
      </el-form-item>
      
      <el-form-item label="商品详情图">
        <FileUploader
          ref="detailImagesUploaderRef"
          action="/v1/merchant/upload"
          :multiple="true"
          :file-limit="9"
          :size-limit="5 * 1024 * 1024"
          accept=".jpg,.jpeg,.png"
          file-usage="product_detail"
          :initial-files="initialDetailImageFiles"
          :auto-upload="true"
          @success="handleDetailImageSuccess"
          @remove="handleDetailImageRemove"
        >
          <template #tip>
            <p>点击或拖拽图片到此处上传商品详情图</p>
            <p>支持多图上传，最多9张，支持 JPG、PNG 格式</p>
          </template>
        </FileUploader>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { FileUploader } from '@/components/common';

// 上传文件类型
interface UploadFile {
  id?: number | string;
  name: string;
  size: number;
  percentage?: number;
  status: string;
  raw: File;
  url?: string;
  message?: string;
  file_usage?: string;
  mime_type?: string;
}

// 接收父组件的属性
const props = defineProps<{
  mainImage: string;
  detailImages: string[];
  uploadingImages: boolean;
}>();

// 向父组件发送事件
const emit = defineEmits<{
  'update:mainImage': [value: string];
  'update:detailImages': [value: string[]];
  'fileChange': [mainFile: File | null, detailFiles: File[]];
}>();

// 本地状态
const mainImageUploaderRef = ref<InstanceType<typeof FileUploader> | null>(null);
const detailImagesUploaderRef = ref<InstanceType<typeof FileUploader> | null>(null);

// 文件对象
const mainImageFile = ref<File | null>(null);
const detailImageFiles = ref<File[]>([]);

// 初始化文件
const initialMainImageFile = ref<any[]>([]);
const initialDetailImageFiles = ref<any[]>([]);

// 初始化上传组件
onMounted(() => {
  // 如果有初始主图，设置初始文件
  if (props.mainImage) {
    initialMainImageFile.value = [{
      name: 'main-image.jpg',
      url: props.mainImage,
      status: 'success',
      size: 0,
      raw: new File([], 'main-image.jpg')
    }];
  }
  
  // 如果有初始详情图，设置初始文件
  if (props.detailImages.length > 0) {
    initialDetailImageFiles.value = props.detailImages.map((url, index) => ({
      name: `detail-image-${index + 1}.jpg`,
      url: url,
      status: 'success',
      size: 0,
      raw: new File([], `detail-image-${index + 1}.jpg`)
    }));
  }
});

// 处理主图上传成功
const handleMainImageSuccess = (response: any, file: UploadFile) => {
  console.log('Main image uploaded:', response, file);
  // 如果上传成功并有url
  if (response.file_url) {
    mainImageFile.value = file.raw;
    emit('update:mainImage', response.file_url);
    emitFileChange();
  }
};

// 处理主图移除
const handleMainImageRemove = () => {
  mainImageFile.value = null;
  emit('update:mainImage', '');
  emitFileChange();
};

// 处理详情图上传成功
const handleDetailImageSuccess = (response: any, file: UploadFile, fileList: UploadFile[]) => {
  console.log('Detail image uploaded:', response);
  // 如果上传成功并有url
  if (response.file_url) {
    // 添加新文件到详情图文件数组
    detailImageFiles.value.push(file.raw);
    
    // 更新详情图的URL数组
    const urls = fileList.map(f => f.url || '').filter(url => url !== '');
    emit('update:detailImages', urls);
    emitFileChange();
  }
};

// 处理详情图移除
const handleDetailImageRemove = (file: UploadFile, fileList: UploadFile[]) => {
  // 找到删除的文件索引
  const fileIndex = detailImageFiles.value.findIndex(f => 
    f.name === file.raw.name && f.size === file.raw.size
  );
  
  if (fileIndex !== -1) {
    detailImageFiles.value.splice(fileIndex, 1);
  }
  
  // 更新详情图的URL数组
  const urls = fileList.map(f => f.url || '').filter(url => url !== '');
  emit('update:detailImages', urls);
  emitFileChange();
};

// 向父组件通知文件变化
const emitFileChange = () => {
  emit('fileChange', mainImageFile.value, detailImageFiles.value);
};

// 提交所有文件上传
const submitUpload = () => {
  // 如果有主图上传器实例且有主图文件，提交上传
  if (mainImageUploaderRef.value) {
    mainImageUploaderRef.value.submit();
  }
  
  // 如果有详情图上传器实例且有详情图文件，提交上传
  if (detailImagesUploaderRef.value) {
    detailImagesUploaderRef.value.submit();
  }
};

// 暴露方法给父组件
defineExpose({
  submitUpload
});
</script>

<style scoped lang="scss">
.food-image-upload {
  margin-bottom: 20px;
}
</style>
