<!--
 * 商品选择器组件
 * 用于在促销活动中选择适用的商品
-->
<template>
  <div class="food-selector">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchKeyword"
        placeholder="输入商品名称搜索"
        clearable
        @clear="handleSearch"
      >
        <template #append>
          <el-button @click="handleSearch">搜索</el-button>
        </template>
      </el-input>
    </div>
    
    <!-- 商品列表 -->
    <el-table
      :data="foodList"
      border
      style="width: 100%"
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column label="商品图片" width="100">
        <template #default="scope">
          <el-image
            style="width: 60px; height: 60px"
            :src="scope.row.image"
            fit="cover"
            :preview-src-list="[scope.row.image]"
          >
            <template #error>
              <div class="image-placeholder">暂无图片</div>
            </template>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="商品名称" min-width="180" show-overflow-tooltip />
      <el-table-column prop="category_name" label="分类" width="120" />
      <el-table-column prop="price" label="价格" width="100">
        <template #default="scope">
          <span>{{ scope.row.price.toFixed(2) }} 元</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '上架中' : '已下架' }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="page"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 底部按钮栏 -->
    <div class="dialog-footer">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button type="primary" @click="confirmSelection">确认选择</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 商品选择器组件
 * 用于在促销活动中选择适用的商品
 */
import { ref, onMounted } from 'vue'
import { getFoodList } from '../api/'

// 定义props和emits
const props = defineProps({
  selectedIds: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['confirm', 'cancel'])

// 数据状态
const loading = ref(false)
const searchKeyword = ref('')
const foodList = ref([])
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const selectedFoods = ref<any[]>([])

/**
 * 获取商品列表
 */
const fetchFoodList = async () => {
  loading.value = true
  try {
    const res: any = await getFoodList({
      page: page.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value
    })
    console.log("列表：", res)
    // 响应拦截器已处理成功响应，直接使用返回的data
    foodList.value = res.list || []
    total.value = res.total || 0
    
    // 如果有已选商品ID，标记为选中状态
    if (props.selectedIds && props.selectedIds.length > 0) {
      foodList.value.forEach((food: any) => {
        if (props.selectedIds.includes(food.id)) {
          selectedFoods.value.push(food)
        }
      })
    }
  } catch (error) {
    console.error('获取商品列表失败', error)
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  page.value = 1
  fetchFoodList()
}

/**
 * 处理页码变化
 */
const handleCurrentChange = (val: number) => {
  page.value = val
  fetchFoodList()
}

/**
 * 处理每页条数变化
 */
const handleSizeChange = (val: number) => {
  pageSize.value = val
  page.value = 1
  fetchFoodList()
}

/**
 * 处理表格选择变化
 */
const handleSelectionChange = (selection: any[]) => {
  selectedFoods.value = selection
}

/**
 * 确认选择
 */
const confirmSelection = () => {
  emit('confirm', selectedFoods.value)
}

// 初始化时获取商品列表
onMounted(() => {
  fetchFoodList()
})
</script>

<style scoped>
.food-selector {
  padding: 0 0 20px 0;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  margin-top: 20px;
  text-align: right;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 12px;
}
</style>
