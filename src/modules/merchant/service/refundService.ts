/**
 * 退款服务
 */
import { 
  getRefundList, 
  getRefundDetail, 
  handleRefund 
} from '../api/index';
import type { ApiResponse } from '@/types';

/**
 * 退款服务
 */
export const RefundService = {
  /**
   * 获取退款列表
   * @param params 查询参数
   * @returns 退款列表和分页信息
   */
  async getList(params: {
    page: number;
    pageSize: number;
    status?: string;
    startTime?: string;
    endTime?: string;
  }): Promise<ApiResponse<{records: any[], total: number}>> {
    try {
      const res:any = await getRefundList(params);
      return res as ApiResponse<{records: any[], total: number}>;
    } catch (error) {
      console.error('获取退款列表失败', error);
      throw error;
    }
  },

  /**
   * 获取退款详情
   * @param id 退款ID
   * @returns 退款详情
   */
  async getDetail(id: number): Promise<ApiResponse<any>> {
    try {
      const res = await getRefundDetail(id);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('获取退款详情失败', error);
      throw error;
    }
  },

  /**
   * 处理退款申请
   * @param id 退款ID
   * @param action 处理动作
   * @param reason 拒绝原因
   * @returns 处理结果
   */
  async handle(id: number, action: 'approve' | 'reject', reason?: string): Promise<ApiResponse<any>> {
    try {
      const res = await handleRefund(id, { action, reason });
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('处理退款申请失败', error);
      throw error;
    }
  }
};

export default RefundService;
