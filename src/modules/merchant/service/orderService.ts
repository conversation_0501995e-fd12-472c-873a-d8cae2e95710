/**
 * 订单服务
 */
import { 
  getOrderList, 
  getOrderDetail, 
  //shipOrder, 
  cancelOrder 
} from '../api/index';
import type { OrderInfo } from '../types';
import type { ApiResponse } from '@/types';

/**
 * 订单服务
 */
export const OrderService = {
  /**
   * 获取订单列表
   * @param params 查询参数
   * @returns 订单列表和分页信息
   */
  async getList(params: {
    page: number;
    page_size: number;
    status?: number;
    startTime?: string;
    endTime?: string;
  }): Promise<ApiResponse<{records: OrderInfo[], total: number}>> {
    try {
      const res:any = await getOrderList(params);
      return res as ApiResponse<{records: OrderInfo[], total: number}>;
    } catch (error) {
      console.error('获取订单列表失败', error);
      throw error;
    }
  },

  /**
   * 获取订单详情
   * @param id 订单ID
   * @returns 订单详情
   */
  async getDetail(id: number): Promise<any> {
    try {
      const res = await getOrderDetail(id);
      return res as any;
    } catch (error) {
      console.error('获取订单详情失败', error);
      throw error;
    }
  },

  /**
   * 发货
   * @param id 订单ID
   * @param logisticsCompany 物流公司
   * @param trackingNumber 物流单号
   * @returns 发货结果
   */
  // async ship(id: number, logisticsCompany: string, trackingNumber: string): Promise<ApiResponse<any>> {
  //   try {
  //     const res = await shipOrder(id, { logisticsCompany, trackingNumber });
  //     return res as ApiResponse<any>;
  //   } catch (error) {
  //     console.error('发货失败', error);
  //     throw error;
  //   }
  // },

  /**
   * 取消订单
   * @param id 订单ID
   * @param reason 取消原因
   * @returns 取消结果
   */
  async cancel(id: number, reason: string): Promise<ApiResponse<any>> {
    try {
      const res = await cancelOrder(id, reason);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('取消订单失败', error);
      throw error;
    }
  }
};

export default OrderService;
