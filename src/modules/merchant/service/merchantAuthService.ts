/**
 * 商户认证服务
 */
import { login, logout, changePassword } from '../api/index';
import type { MerchantLoginParams, MerchantInfo } from '../types';
import type { ApiResponse } from '@/types';

/**
 * 商户认证服务
 */
export const MerchantAuthService = {
  /**
   * 商户登录
   * @param params 登录参数
   * @returns 登录结果
   */
  async login(params: MerchantLoginParams): Promise<ApiResponse<{ token: string; merchantInfo: MerchantInfo }>> {
    try {
      const res = await login(params);
      return res as unknown as ApiResponse<{ token: string; merchantInfo: MerchantInfo }>;
    } catch (error) {
      console.error('商户登录失败', error);
      throw error;
    }
  },

  /**
   * 商户登出
   * @returns 登出结果
   */
  async logout(): Promise<ApiResponse<any>> {
    try {
      // 获取当前设备信息
      const deviceInfoStr = localStorage.getItem('merchant_current_device_info');
      let deviceId = '';
      
      if (deviceInfoStr) {
        try {
          const deviceInfo = JSON.parse(deviceInfoStr);
          deviceId = deviceInfo.device_id || '';
        } catch (e) {
          console.warn('解析设备信息失败:', e);
        }
      }
      
      if (!deviceId) {
        console.warn('未找到设备ID，使用默认值');
        deviceId = 'unknown_device';
      }
      
      const res = await logout(deviceId);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('商户登出失败', error);
      throw error;
    }
  },

  /**
   * 修改密码
   * @param oldPassword 旧密码
   * @param newPassword 新密码
   * @returns 修改结果
   */
  async changePassword(oldPassword: string, newPassword: string): Promise<ApiResponse<any>> {
    try {
      const res = await changePassword({ oldPassword, newPassword });
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('修改密码失败', error);
      throw error;
    }
  }
};

export default MerchantAuthService;
