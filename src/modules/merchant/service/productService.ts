/**
 * 商品服务
 */
import { 
  getProductList, 
  createProduct, 
  updateProduct, 
  deleteProduct, 
  putOnSale, 
  putOffSale 
} from '../api/index';
import type { ProductInfo } from '../types';
import type { ApiResponse } from '@/types';

/**
 * 商品服务
 */
export const ProductService = {
  /**
   * 获取商品列表
   * @param params 查询参数
   * @returns 商品列表和分页信息
   */
  async getList(params: {
    page: number;
    pageSize: number;
    status?: string;
    categoryId?: number;
    keyword?: string;
  }): Promise<ApiResponse<{records: ProductInfo[], total: number}>> {
    try {
      const res:any = await getProductList(params);
      return res as ApiResponse<{records: ProductInfo[], total: number}>;
    } catch (error) {
      console.error('获取商品列表失败', error);
      throw error;
    }
  },

  /**
   * 创建商品
   * @param data 商品信息
   * @returns 创建的商品信息
   */
  async create(data: Omit<ProductInfo, 'id' | 'createTime'>): Promise<any> {
    try {
      const res = await createProduct(data);
      return res as any;
    } catch (error) {
      console.error('创建商品失败', error);
      throw error;
    }
  },

  /**
   * 更新商品
   * @param id 商品ID
   * @param data 更新的商品信息
   * @returns 更新后的商品信息
   */
  async update(id: number, data: Partial<ProductInfo>): Promise<any> {
    try {
      const res = await updateProduct(id, data);
      return res as any;
    } catch (error) {
      console.error('更新商品失败', error);
      throw error;
    }
  },

  /**
   * 删除商品
   * @param id 商品ID
   * @returns 删除结果
   */
  async delete(id: number): Promise<ApiResponse<any>> {
    try {
      const res = await deleteProduct(id);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('删除商品失败', error);
      throw error;
    }
  },

  /**
   * 上架商品
   * @param id 商品ID
   * @returns 上架结果
   */
  async putOnSale(id: number): Promise<ApiResponse<any>> {
    try {
      const res = await putOnSale(id);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('上架商品失败', error);
      throw error;
    }
  },

  /**
   * 下架商品
   * @param id 商品ID
   * @returns 下架结果
   */
  async putOffSale(id: number): Promise<ApiResponse<any>> {
    try {
      const res = await putOffSale(id);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('下架商品失败', error);
      throw error;
    }
  }
};

export default ProductService;
