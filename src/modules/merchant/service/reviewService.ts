/**
 * 评价服务
 */
import { 
  getReviewList, 
  replyReview 
} from '../api/index';
import type { ApiResponse } from '@/types';

/**
 * 评价服务
 */
export const ReviewService = {
  /**
   * 获取评价列表
   * @param params 查询参数
   * @returns 评价列表和分页信息
   */
  async getList(params: {
    page: number;
    pageSize: number;
    productId?: number;
    rating?: number;
  }): Promise<ApiResponse<{records: any[], total: number}>> {
    try {
      const res:any = await getReviewList(params);
      return res as ApiResponse<{records: any[], total: number}>;
    } catch (error) {
      console.error('获取评价列表失败', error);
      throw error;
    }
  },

  /**
   * 回复评价
   * @param id 评价ID
   * @param content 回复内容
   * @returns 回复结果
   */
  async reply(id: number, content: string): Promise<ApiResponse<any>> {
    try {
      const res = await replyReview(id, content);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('回复评价失败', error);
      throw error;
    }
  }
};

export default ReviewService;
