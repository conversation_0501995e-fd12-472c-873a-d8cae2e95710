/**
 * 通知服务
 */
import { 
  getNotificationList, 
  markNotificationRead 
} from '../api/index';
import type { ApiResponse } from '@/types';

/**
 * 通知服务
 */
export const NotificationService = {
  /**
   * 获取商户通知列表
   * @param params 查询参数
   * @returns 通知列表和分页信息
   */
  async getList(params: {
    page: number;
    pageSize: number;
    type?: string;
    isRead?: boolean;
  }): Promise<ApiResponse<{records: any[], total: number}>> {
    try {
      const res:any = await getNotificationList(params);
      return res as ApiResponse<{records: any[], total: number}>;
    } catch (error) {
      console.error('获取通知列表失败', error);
      throw error;
    }
  },

  /**
   * 标记通知已读
   * @param id 通知ID
   * @returns 标记结果
   */
  async markRead(id: number): Promise<ApiResponse<any>> {
    try {
      const res = await markNotificationRead(id);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('标记通知已读失败', error);
      throw error;
    }
  }
};

export default NotificationService;
