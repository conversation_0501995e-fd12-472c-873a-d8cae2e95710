/**
 * 商户信息服务
 */
import { 
  getMerchantInfo, 
  updateMerchantInfo, 
  getMerchantStats, 
  getMerchantSettings, 
  updateMerchantSettings 
} from '../api/index';
import type { MerchantInfo } from '../types';
import type { ApiResponse } from '@/types';

/**
 * 商户信息服务
 */
export const MerchantProfileService = {
  /**
   * 获取商户信息
   * @returns 商户信息
   */
  async getInfo(): Promise<any> {
    try {
      const res = await getMerchantInfo();
      return res as any;
    } catch (error) {
      console.error('获取商户信息失败', error);
      throw error;
    }
  },

  /**
   * 更新商户信息
   * @param data 更新的商户信息
   * @returns 更新后的商户信息
   */
  async update(data: Partial<MerchantInfo>): Promise<any> {
    try {
      const res = await updateMerchantInfo(data);
      return res as any;
    } catch (error) {
      console.error('更新商户信息失败', error);
      throw error;
    }
  },

  /**
   * 获取商户统计数据
   * @returns 商户统计数据
   */
  async getStats(): Promise<ApiResponse<any>> {
    try {
      console.log('获取商户统计数据 from merchantProfileService');
      const res = await getMerchantStats();
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('获取商户统计数据失败', error);
      throw error;
    }
  },

  /**
   * 获取商户设置
   * @returns 商户设置
   */
  async getSettings(): Promise<ApiResponse<any>> {
    try {
      const res = await getMerchantSettings();
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('获取商户设置失败', error);
      throw error;
    }
  },

  /**
   * 更新商户设置
   * @param data 更新的商户设置
   * @returns 更新结果
   */
  async updateSettings(data: {
    autoShip?: boolean;
    autoReply?: boolean;
    replyTemplate?: string;
    notificationSettings?: {
      orderNotification?: boolean;
      refundNotification?: boolean;
      reviewNotification?: boolean;
    };
  }): Promise<ApiResponse<any>> {
    try {
      const res = await updateMerchantSettings(data);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('更新商户设置失败', error);
      throw error;
    }
  }
};

export default MerchantProfileService;
