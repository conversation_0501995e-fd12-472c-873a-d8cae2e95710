/**
 * 财务服务
 */
import { 
  getBillList, 
  getBillDetail, 
  applyWithdrawal 
} from '../api/index';
import type { ApiResponse } from '@/types';

/**
 * 财务服务
 */
export const FinancialService = {
  /**
   * 获取商户账单列表
   * @param params 查询参数
   * @returns 账单列表和分页信息
   */
  async getBillList(params: {
    page: number;
    pageSize: number;
    type?: string;
    startTime?: string;
    endTime?: string;
  }): Promise<ApiResponse<{records: any[], total: number}>> {
    try {
      const res:any = await getBillList(params);
      return res as ApiResponse<{records: any[], total: number}>;
    } catch (error) {
      console.error('获取账单列表失败', error);
      throw error;
    }
  },

  /**
   * 获取账单详情
   * @param id 账单ID
   * @returns 账单详情
   */
  async getBillDetail(id: number): Promise<ApiResponse<any>> {
    try {
      const res = await getBillDetail(id);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('获取账单详情失败', error);
      throw error;
    }
  },

  /**
   * 申请提现
   * @param amount 提现金额
   * @param bankAccount 银行账号
   * @param bankName 银行名称
   * @param accountName 开户名
   * @returns 申请结果
   */
  async applyWithdrawal(
    amount: number, 
    bankAccount: string, 
    bankName: string, 
    accountName: string
  ): Promise<ApiResponse<any>> {
    try {
      const res = await applyWithdrawal({ 
        amount, 
        bankAccount, 
        bankName, 
        accountName 
      });
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('申请提现失败', error);
      throw error;
    }
  }
};

export default FinancialService;
