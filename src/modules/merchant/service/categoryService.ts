/**
 * 分类服务
 */
import { 
  getCategoryList, 
  createCategory, 
  updateCategory, 
  deleteCategory,
  getTagList,
  createTag,
  updateTag,
  deleteTag
} from '../api/index';
import type { ApiResponse } from '@/types';

/**
 * 分类服务
 */
export const CategoryService = {
  /**
   * 获取商户分类列表
   * @returns 分类列表
   */
  async getList(): Promise<ApiResponse<any[]>> {
    try {
      const res = await getCategoryList();
      return res as ApiResponse<any[]>;
    } catch (error) {
      console.error('获取分类列表失败', error);
      throw error;
    }
  },

  /**
   * 创建分类
   * @param name 分类名称
   * @param parentId 父分类ID
   * @param sort 排序值
   * @returns 创建结果
   */
  async create(name: string, parentId?: number, sort?: number): Promise<ApiResponse<any>> {
    try {
      const res = await createCategory({ name, parentId, sort });
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('创建分类失败', error);
      throw error;
    }
  },

  /**
   * 更新分类
   * @param id 分类ID
   * @param data 更新数据
   * @returns 更新结果
   */
  async update(id: number, data: {
    name?: string;
    parentId?: number;
    sort?: number;
  }): Promise<ApiResponse<any>> {
    try {
      const res = await updateCategory(id, data);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('更新分类失败', error);
      throw error;
    }
  },

  /**
   * 删除分类
   * @param id 分类ID
   * @returns 删除结果
   */
  async delete(id: number): Promise<ApiResponse<any>> {
    try {
      const res = await deleteCategory(id);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('删除分类失败', error);
      throw error;
    }
  },

  /**
   * 获取商户标签列表
   * @returns 标签列表
   */
  async getTagList(): Promise<ApiResponse<any[]>> {
    try {
      const res = await getTagList();
      return res as ApiResponse<any[]>;
    } catch (error) {
      console.error('获取标签列表失败', error);
      throw error;
    }
  },

  /**
   * 创建标签
   * @param name 标签名称
   * @param color 标签颜色
   * @returns 创建结果
   */
  async createTag(name: string, color?: string): Promise<ApiResponse<any>> {
    try {
      const res = await createTag({ name, color });
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('创建标签失败', error);
      throw error;
    }
  },

  /**
   * 更新标签
   * @param id 标签ID
   * @param data 更新数据
   * @returns 更新结果
   */
  async updateTag(id: number, data: {
    name?: string;
    color?: string;
  }): Promise<ApiResponse<any>> {
    try {
      const res = await updateTag(id, data);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('更新标签失败', error);
      throw error;
    }
  },

  /**
   * 删除标签
   * @param id 标签ID
   * @returns 删除结果
   */
  async deleteTag(id: number): Promise<ApiResponse<any>> {
    try {
      const res = await deleteTag(id);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('删除标签失败', error);
      throw error;
    }
  }
};

export default CategoryService;
