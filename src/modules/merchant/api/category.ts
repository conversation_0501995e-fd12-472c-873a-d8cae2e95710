/**
 * 分类相关API接口
 */

import { get, post, put, del } from '@/modules/merchant/utils/request';

/**
 * 获取商户分类列表
 * @returns 分类列表
 */
export function getCategoryList() {
  return get('/v1/merchant/categories');
}

/**
 * 创建分类
 * @param data 分类数据
 * @returns 创建结果
 */
export function createCategory(data: {
  name: string;
  parentId?: number;
  sort?: number;
}) {
  return post('/v1/merchant/categories', data);
}

/**
 * 更新分类
 * @param id 分类ID
 * @param data 更新数据
 * @returns 更新结果
 */
export function updateCategory(id: number, data: {
  name?: string;
  parentId?: number;
  sort?: number;
}) {
  return put(`/v1/merchant/categories/${id}`, data);
}

/**
 * 删除分类
 * @param id 分类ID
 * @returns 删除结果
 */
export function deleteCategory(id: number) {
  return del(`/v1/merchant/categories/${id}`);
}

/**
 * 获取商户标签列表
 * @returns 标签列表
 */
export function getTagList() {
  return get('/v1/merchant/tags');
}

/**
 * 创建标签
 * @param data 标签数据
 * @returns 创建结果
 */
export function createTag(data: {
  name: string;
  color?: string;
}) {
  return post('/v1/merchant/tags', data);
}

/**
 * 更新标签
 * @param id 标签ID
 * @param data 更新数据
 * @returns 更新结果
 */
export function updateTag(id: number, data: {
  name?: string;
  color?: string;
}) {
  return put(`/v1/merchant/tags/${id}`, data);
}

/**
 * 删除标签
 * @param id 标签ID
 * @returns 删除结果
 */
export function deleteTag(id: number) {
  return del(`/v1/merchant/tags/${id}`);
}
