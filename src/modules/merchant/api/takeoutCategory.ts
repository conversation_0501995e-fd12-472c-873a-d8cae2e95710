/**
 * 商家外卖分类相关API接口
 */

import { get, post, put, del } from '@/modules/merchant/utils/request';

/**
 * 获取外卖分类列表
 * @param params 查询参数
 * @returns 分类列表
 */
export function getCategoryList(params?: {
  keyword?: string;
  page?: number;
  page_size?: number;
  parent_id?: number;
  include_children?: boolean;
}) {
  return get('/v1/merchant/takeout/categories', params);
}

/**
 * 获取分类详情
 * @param id 分类ID
 * @returns 分类详情
 */
export function getCategoryDetail(id: number) {
  return get(`/v1/merchant/takeout/categories/${id}`);
}

/**
 * 创建外卖分类
 * @param data 分类信息
 * @returns 创建结果
 */
export function createCategory(data: {
  name: string;
  description?: string;
  image?: string;
  parent_id?: number;
  sort_order?: number;
  is_visible?: boolean;
}) {
  return post('/v1/merchant/takeout/categories', data);
}

/**
 * 更新外卖分类
 * @param id 分类ID
 * @param data 更新数据
 * @returns 更新结果
 */
export function updateCategory(id: number, data: {
  name?: string;
  description?: string;
  image?: string;
  parent_id?: number;
  sort_order?: number;
  is_visible?: boolean;
}) {
  return put(`/v1/merchant/takeout/categories/${id}`, data);
}

/**
 * 删除外卖分类
 * @param id 分类ID
 * @returns 删除结果
 */
export function deleteCategory(id: number) {
  return del(`/v1/merchant/takeout/categories/${id}`);
}
