/**
 * 商家模块API接口
 * 按功能模块拆分，统一导出
 */

// 认证相关API
export * from './auth';

// 分类相关API
export * from './category';

// 财务相关API
export * from './financial';

// 通知相关API
export * from './notification';

// 订单相关API
export * from './order';

// 产品相关API
export * from './product';

// 商家资料相关API
export * from './profile';

// 退款相关API
export * from './refund';

// 评价相关API
export * from './reviews';

// 前端路径相关API
export * from './frontendPath';

// 系统配置相关API
export * from './system';

// 经营时段相关API
export * from './business-hours';

// 优惠券相关API
export * from './coupon';

// 促销活动相关API
export * from './promotion';

// 菜品相关API - 从 takeout.ts 中导出
export {
  getFoodList,
  createFood,
  updateFood,
  createFoodSpecs,
  getFoodSpecs,
  uploadFoodImages,
  getFoodImages,
  submitFoodForAudit,
  getFoodCategories,
  enableFood,
  disableFood,
  batchFoodAction
} from './takeout';
