/**
 * 订单相关API接口
 * 包含商家外卖订单管理的所有功能
 */

import { get, post, put } from '@/modules/merchant/utils/request';
import type { OrderStatistics } from '../types';
import type { OrderInfo } from '../types/order';

/**
 * 获取外卖订单列表
 * @param params 查询参数
 * @returns 订单列表
 */
export function getOrderList(params: {
  page: number;
  page_size: number;
  status?: number; // 订单状态，-1表示全部，20表示已支付待处理的订单
  start_time?: string;
  end_time?: string;
}) {
  // 假设API返回的数据结构为 { code: number, message: string, data: { list: OrderInfo[], total_count: number } }
  // 并且 get 工具函数会自动处理外层的 code 和 message, 返回 data 部分
  // 或者 get 工具函数返回的是包含 data 属性的完整 AxiosResponse，组件中解构了 data
  // 根据 OrderList.vue 中 res.data.list 的用法，我们假设 get 返回 Promise<{ data: T }>
  return get<{ list: OrderInfo[]; total_count: number; }>('/v1/merchant/takeout/orders', params);
}

/**
 * 获取订单详情
 * @param id 订单ID
 * @returns 订单详情
 */
export function getOrderDetail(id: number) {
  return get<any>(`/v1/merchant/takeout/orders/${id}`).then(response => {
    // 确保同时支持小驼峰和大驼峰命名
    if (response && typeof response === 'object') {
      // 调试输出，查看退款信息结构
      console.log('订单详情原始数据:', response);
      console.log('订单详情数据类型:', typeof response);
      console.log('订单详情所有字段:', Object.keys(response));
      
      const data = response as any;
      
      // 检查各种可能的退款信息字段名
      const possibleRefundFields = ['refundInfo', 'refund_info', 'refundData', 'refund_data', 'refund'];
      let refundInfo = null;
      
      for (const field of possibleRefundFields) {
        if (data[field]) {
          console.log(`找到退款信息字段: ${field}`, data[field]);
          refundInfo = data[field];
          break;
        }
      }
      
      // 检查后端新增的直接退款字段
      if (!refundInfo && (data.hasRefund || data.refundNo || data.refundStatus !== undefined || data.refundAmount !== undefined)) {
        console.log('检测到后端新增的直接退款字段，构造退款信息对象');
        refundInfo = {
          refundId: 0, // 默认值，可能需要从其他地方获取
          refundAmount: data.refundAmount || 0,
          refundReason: data.refundReason || '',
          refundStatus: data.refundStatus || 0,
          applyTime: data.refundTime || new Date().toISOString(),
          refundNo: data.refundNo,
          refundTime: data.refundTime
        };
        console.log('从新字段构造的退款信息:', refundInfo);
      }
      
      // 如果订单状态是REFUNDING但没有退款信息，尝试从其他字段构造
      if (data.orderStatus === 70 && !refundInfo) {
        console.log('订单状态为REFUNDING但没有退款信息，检查其他可能的字段...');
        
        // 检查是否有退款相关的单独字段
        const refundFields = Object.keys(data).filter(key => 
          key.toLowerCase().includes('refund') || 
          key.toLowerCase().includes('退款') ||
          key === 'refundingAt' || 
          key === 'refunding_at' ||
          key === 'hasRefund' ||
          key === 'has_refund'
        );
        console.log('包含退款关键字的字段:', refundFields);
        
        // 如果有退款相关字段，尝试构造退款信息
        if (refundFields.length > 0) {
          // 尝试多种可能的退款ID字段
          let refundId = data.refundId || data.refund_id || data.refundID || data.refund_ID;
          
          // 如果仍然没有退款ID，尝试使用订单ID作为备用
          if (!refundId || refundId === 0) {
            console.warn('未找到有效的退款ID，将使用订单ID作为备用');
            refundId = data.id || data.orderID || data.order_id;
          }
          
          refundInfo = {
            refundId: refundId || 0,
            refundAmount: data.refundAmount || data.refund_amount || data.totalAmount,
            refundReason: data.refundReason || data.refund_reason || '用户申请退款',
            refundStatus: data.refundStatus || data.refund_status || 0,
            applyTime: data.refundApplyTime || data.refund_apply_time || data.refundingAt || data.refundTime || new Date().toISOString(),
            processTime: data.refundProcessTime || data.refund_process_time,
            processRemark: data.refundProcessRemark || data.refund_process_remark,
            // 新增字段支持
            refundNo: data.refundNo || data.refund_no,
            refundTime: data.refundTime || data.refund_time
          };
          console.log('构造的退款信息:', refundInfo);
          
          // 记录退款ID的来源
          if (refundInfo.refundId) {
            console.log('退款ID来源:', {
              refundId: refundInfo.refundId,
              来源字段: refundFields,
              是否使用订单ID作为备用: !data.refundId && !data.refund_id
            });
          }
        }
      }
      
      // 设置退款信息
      if (refundInfo) {
        data.refundInfo = refundInfo;
        
        // 确保refundStatus是数字类型
        if (data.refundInfo.refundStatus !== undefined && typeof data.refundInfo.refundStatus !== 'number') {
          data.refundInfo.refundStatus = Number(data.refundInfo.refundStatus);
          console.log('转换后的退款状态:', data.refundInfo.refundStatus);
        }
      } else {
        console.log('未找到任何退款信息');
      }
      
      // 处理用户ID和商家ID的兼容性
      if (data.userId !== undefined) {
        data.userID = data.userId;
      }
      if (data.merchantId !== undefined) {
        data.merchantID = data.merchantId;
      }
      
      // 处理配送信息的兼容性
      console.log('检查配送信息字段:', {
        deliveryInfo: data.deliveryInfo,
        delivery_info: data.delivery_info,
        deliveryData: data.deliveryData,
        delivery_data: data.delivery_data
      });
      
      // 检查各种可能的配送信息字段名
      const possibleDeliveryFields = ['deliveryInfo', 'delivery_info', 'deliveryData', 'delivery_data'];
      let deliveryInfo = null;
      
      for (const field of possibleDeliveryFields) {
        if (data[field]) {
          console.log(`找到配送信息字段: ${field}`, data[field]);
          deliveryInfo = data[field];
          break;
        }
      }
      
      // 如果没有找到配送信息对象，但有配送相关的单独字段，尝试构造
      if (!deliveryInfo) {
        const deliveryFields = Object.keys(data).filter(key => 
          key.toLowerCase().includes('delivery') || 
          key.toLowerCase().includes('配送') ||
          key === 'deliveryStatus' ||
          key === 'delivery_status' ||
          key === 'deliveryStaffId' ||
          key === 'delivery_staff_id' ||
          key === 'deliveryStaffName' ||
          key === 'delivery_staff_name'
        );
        
        console.log('包含配送关键字的字段:', deliveryFields);
        
        if (deliveryFields.length > 0) {
          deliveryInfo = {
            deliveryStaffId: data.deliveryStaffId || data.delivery_staff_id || 0,
            deliveryStaffName: data.deliveryStaffName || data.delivery_staff_name || '',
            deliveryStaffPhone: data.deliveryStaffPhone || data.delivery_staff_phone || '',
            deliveryStatus: data.deliveryStatus || data.delivery_status || 11, // 默认为已确认
            estimatedArrivalTime: data.estimatedArrivalTime || data.estimated_arrival_time || null,
            deliveryStartedAt: data.deliveryStartedAt || data.delivery_started_at || null,
            deliveryCompletedAt: data.deliveryCompletedAt || data.delivery_completed_at || null,
            deliveryAddress: data.deliveryAddress || data.delivery_address || '',
            deliveryDistance: data.deliveryDistance || data.delivery_distance || 0,
            expectedTime: data.expectedTime || data.expected_time || ''
          };
          console.log('从单独字段构造的配送信息:', deliveryInfo);
        }
      }
      
      // 如果仍然没有配送信息，创建默认的配送信息
      if (!deliveryInfo) {
        console.log('未找到配送信息，创建默认配送信息');
        deliveryInfo = {
          deliveryStaffId: 0,
          deliveryStaffName: '',
          deliveryStaffPhone: '',
          deliveryStatus: 10, // 默认为待支付 (PENDING)
          estimatedArrivalTime: null,
          deliveryStartedAt: null,
          deliveryCompletedAt: null,
          deliveryAddress: '',
          deliveryDistance: 0,
          expectedTime: ''
        };
      }
      
      // 确保配送状态是数字类型
      if (deliveryInfo && deliveryInfo.deliveryStatus !== undefined && typeof deliveryInfo.deliveryStatus !== 'number') {
        deliveryInfo.deliveryStatus = Number(deliveryInfo.deliveryStatus);
        console.log('转换后的配送状态:', deliveryInfo.deliveryStatus);
      }
      
      // 映射配送状态码：将后端返回的状态码映射到前端定义的枚举值
      // 后端订单状态流转：待支付(10) → 已支付(20) → 已接单(30) → 已取货(40) → 已完成(50)，各阶段可转为已取消(60)
      if (deliveryInfo && deliveryInfo.deliveryStatus !== undefined) {
        const statusMapping: Record<number, number> = {
          // 如果后端返回的是简化状态码(0-9)，映射到新的状态流转
          0: 10,  // 待支付
          1: 20,  // 已支付
          2: 30,  // 已接单
          3: 40,  // 已取货
          4: 50,  // 已完成
          5: 60,  // 已取消
          // 如果后端直接返回标准状态码，保持不变
          10: 10, // 待支付
          20: 20, // 已支付
          30: 30, // 已接单
          40: 40, // 已取货
          50: 50, // 已完成
          60: 60  // 已取消
        };
        
        // 进行状态码映射
        console.log('映射前的配送状态:', deliveryInfo.deliveryStatus);
        const mappedStatus = statusMapping[deliveryInfo.deliveryStatus];
        if (mappedStatus !== undefined) {
          deliveryInfo.deliveryStatus = mappedStatus;
          console.log('映射后的配送状态:', deliveryInfo.deliveryStatus);
        } else {
          console.log('未找到状态码映射，保持原状态:', deliveryInfo.deliveryStatus);
        }
      }
      
      // 设置配送信息
      data.deliveryInfo = deliveryInfo;
      
      console.log('最终处理后的订单数据:', data);
      console.log('配送信息:', data.deliveryInfo);
      return data as OrderInfo;
    }
    return response as OrderInfo;
  });
}

/**
 * 处理退款申请
 * @param refundId 退款ID
 * @param action 操作类型（"approve" 或 "reject"）
 * @param processRemark 处理备注（拒绝时必填）
 * @returns 处理结果
 */
export function processRefund(refundId: string, action: 'approve' | 'reject', processRemark?: string) {
  console.log('调用退款处理API:', {
    refundID: refundId,
    action: action,
    processRemark: processRemark || ''
  });
  
  return post('/v1/merchant/takeout/orders/refund/process', {
    refundID: refundId,
    action: action,
    processRemark: processRemark || ''
  });
}

/**
 * 通过订单ID处理退款申请（备用方案）
 * @param orderId 订单ID
 * @param action 操作类型（"approve" 或 "reject"）
 * @param processRemark 处理备注（拒绝时必填）
 * @returns 处理结果
 */
export function processRefundByOrderId(orderId: number, action: 'approve' | 'reject', processRemark?: string) {
  console.log('调用订单退款处理API（备用方案）:', {
    order_id: orderId,
    action: action,
    process_remark: processRemark || ''
  });
  
  return post(`/v1/merchant/takeout/orders/${orderId}/refund/process`, {
    action: action,
    process_remark: processRemark || ''
  });
}

/**
 * 接受订单
 * @param orderId 订单ID
 * @returns 接单结果
 */
export function acceptOrder(orderId: number) {
  return post('/v1/merchant/takeout/orders/accept', { order_id: orderId });
}

/**
 * 分配配送员
 * @param orderId 订单ID
 * @param deliveryStaffId 配送员ID
 * @returns 分配结果
 */
export function assignDeliveryStaff(orderId: number, deliveryStaffId: number) {
  return post('/v1/merchant/takeout/orders/assign', { 
    order_id: orderId, 
    delivery_staff_id: deliveryStaffId 
  });
}

/**
 * 开始配送
 * @param orderId 订单ID
 * @param deliveryStaffId 配送员ID
 * @param estimatedArrivalTime 预计送达时间
 * @returns 配送结果
 */
export function startDelivery(orderId: number, deliveryStaffId: number, estimatedArrivalTime: string) {
  return post('/v1/merchant/takeout/delivery/start', {
    order_id: orderId,
    delivery_staff_id: deliveryStaffId,
    estimated_arrival_time: estimatedArrivalTime
  });
}

/**
 * 完成配送
 * @param orderId 订单ID
 * @param deliveryStaffId 配送员ID
 * @returns 完成配送结果
 */
export function completeDelivery(orderId: number, deliveryStaffId: number) {
  return post('/v1/merchant/takeout/delivery/complete', {
    order_id: orderId,
    delivery_staff_id: deliveryStaffId
  });
}

/**
 * 查询配送订单列表
 * @param params 查询参数
 * @returns 配送订单列表
 */
export function getDeliveryOrderList(params: {
  page: number;
  page_size: number;
  status?: number;
}) {
  return get('/v1/merchant/takeout/delivery/list', params);
}

/**
 * 完成订单
 * @param id 订单ID
 * @returns 完成结果
 */
export function completeOrder(id: number) {
  return put(`/v1/merchant/takeout/orders/${id}`, {});
}

/**
 * 获取订单统计数据
 * @returns 订单统计数据
 */
export function getOrderStatistics() {
  return get<OrderStatistics>('/v1/merchant/takeout/orders/statistics');
}

/**
 * 取消订单
 * @param id 订单ID
 * @param reason 取消原因
 * @returns 取消结果
 */
export function cancelOrder(id: number, reason: string) {
  return post(`/v1/merchant/takeout/orders/${id}/cancel`, { reason });
}

// --- 到店自提相关API ---
/**
 * 开始备餐（到店自提）
 * @param orderId 订单ID
 * @returns 开始备餐结果
 */
export function startPickupPreparation(orderId: number) {
  return post('/v1/merchant/takeout/orders/pickup/start-preparation', {
    order_id: orderId
  });
}

/**
 * 完成备餐（到店自提）
 * @param orderId 订单ID
 * @returns 完成备餐结果
 */
export function completePickupPreparation(orderId: number) {
  return post('/v1/merchant/takeout/orders/meal-prep/complete', {
    order_id: orderId
  });
}

/**
 * 确认自提（到店自提）
 * @param orderId 订单ID
 * @param pickupCode 取餐码
 * @returns 确认自提结果
 */
export function completePickup(orderId: number, pickupCode: string) {
  return post('/v1/merchant/takeout/orders/pickup/complete', {
    order_id: orderId,
    pickup_code: pickupCode
  });
}
