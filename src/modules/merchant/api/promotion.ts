/**
 * 商家促销活动相关API接口
 * 提供促销活动的增删改查等功能
 */

import { get, post, put, del } from '@/modules/merchant/utils/request';

/**
 * 获取促销活动列表
 * @param params 查询参数
 * @returns 促销活动列表
 */
export function getPromotionList(params: {
  page: number;
  pageSize: number;
  status?: number;
  type?: number;
  keyword?: string;
}) {
  return get('/v1/merchant/takeout/promotions', params);
}

/**
 * 创建促销活动
 * @param data 促销活动信息
 * @returns 创建的促销活动信息
 */
export function createPromotion(data: {
  name: string;
  description: string;
  type: number;
  start_time: string;
  end_time: string;
  max_usage_count: number;
  status: number;
  rules: string;
  food_ids?: number[];
}) {
  return post('/v1/merchant/takeout/promotions', data);
}

/**
 * 获取促销活动详情
 * @param id 促销活动ID
 * @returns 促销活动详情
 */
export function getPromotionDetail(id: number) {
  return get(`/v1/merchant/takeout/promotions/${id}`);
}

/**
 * 更新促销活动
 * @param id 促销活动ID
 * @param data 促销活动信息
 * @returns 更新后的促销活动信息
 */
export function updatePromotion(id: number, data: {
  name?: string;
  description?: string;
  type?: number;
  start_time?: string;
  end_time?: string;
  max_usage_count?: number;
  status?: number;
  rules?: string;
  food_ids?: number[];
}) {
  return put(`/v1/merchant/takeout/promotions/${id}`, data);
}

/**
 * 删除促销活动
 * @param id 促销活动ID
 * @returns 删除结果
 */
export function deletePromotion(id: number) {
  return del(`/v1/merchant/takeout/promotions/${id}`);
}

/**
 * 暂停促销活动
 * @param id 促销活动ID
 * @returns 操作结果
 */
export function disablePromotion(id: number) {
  return put(`/v1/merchant/takeout/promotions/${id}/cancel`);
}

/**
 * 启用促销活动
 * @param id 促销活动ID
 * @returns 操作结果
 */
export function enablePromotion(id: number) {
  return put(`/v1/merchant/takeout/promotions/${id}/enable`);
}

/**
 * 获取促销活动统计数据
 * @param id 促销活动ID
 * @returns 使用统计数据
 */
export function getPromotionStats(id: number) {
  return get(`/v1/merchant/takeout/promotions/${id}/stats`);
}

/**
 * 发布促销活动
 * @param id 促销活动ID
 * @returns 操作结果
 */
export function publishPromotion(id: number) {
  return put(`/v1/merchant/takeout/promotions/${id}/publish`);
}

/**
 * 取消促销活动
 * @param id 促销活动ID
 * @returns 操作结果
 */
export function cancelPromotion(id: number) {
  return put(`/v1/merchant/takeout/promotions/${id}/cancel`);
}

/**
 * 预览促销活动
 * @param data 促销活动数据
 * @returns 预览数据
 */
export function previewPromotion(data: any) {
  return post('/v1/merchant/takeout/promotions/preview', data);
}
