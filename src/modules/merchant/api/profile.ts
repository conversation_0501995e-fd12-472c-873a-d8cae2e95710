/**
 * 商户信息相关API接口
 */

import { get, put } from '@/modules/merchant/utils/request';
import type { MerchantInfo } from '../types';

/**
 * 获取商户信息
 * @returns 商户信息
 */
export function getMerchantInfo() {
  return get<MerchantInfo>('/v1/merchant/secured/info');
}

/**
 * 更新商户信息
 * @param data 更新的商户信息
 * @returns 更新后的商户信息
 */
export function updateMerchantInfo(data: Partial<MerchantInfo>) {
  return put<MerchantInfo>('/v1/merchant/secured/', data);
}

/**
 * 获取商户统计数据
 * @returns 商户统计数据
 */
export function getMerchantStats() {
  return get('/v1/merchant/takeout/stats');
}

/**
 * 获取商户设置
 * @returns 商户设置
 */
export function getMerchantSettings() {
  return get('/api/v1/merchant/settings');
}

/**
 * 更新商户设置
 * @param data 更新的商户设置
 * @returns 更新结果
 */
export function updateMerchantSettings(data: {
  autoShip?: boolean;
  autoReply?: boolean;
  replyTemplate?: string;
  notificationSettings?: {
    orderNotification?: boolean;
    refundNotification?: boolean;
    reviewNotification?: boolean;
  };
}) {
  return put('/api/v1/merchant/settings', data);
}

// /**
//  * 切换商家经营状态
//  * @param status 要切换的状态 (1-营业中，0-暂停营业)
//  * @returns 更新结果
//  */
// export function updateOperationStatus(status: number) {
//   return put('/v1/merchant/secured/operation-status', { operation_status: status });
// }
