/**
 * 系统配置相关API接口
 * 包含获取系统基本配置信息和隐私协议等功能
 */

import { get } from '@/modules/merchant/utils/request';

/**
 * 系统基本配置信息类型定义
 */
export interface SystemBasicInfo {
  system_name: string;
  system_version: string;
  system_logo: string;
  contact_email: string;
  contact_phone: string;
  support_url: string;
  company_name: string;
  company_address: string;
  // 其他可能的系统基本信息字段
}

/**
 * 隐私协议信息类型定义
 */
export interface PrivacyPolicyInfo {
  content: string;
  last_updated: string;
  version: string;
  // 其他可能的隐私协议相关字段
}

/**
 * 商家服务协议信息类型定义
 */
export interface MerchantServiceAgreementInfo {
  content: string;
  key: string;
  // 其他可能的商家服务协议相关字段
}

/**
 * 获取系统基本配置信息
 * 无需认证的公开接口
 * @returns 系统基本配置信息
 */
export function getSystemBasicInfo() {
  return get<SystemBasicInfo>('/v1/system/info/basic');
}

/**
 * 获取系统隐私协议
 * 无需认证的公开接口
 * @returns 隐私协议信息
 */
export function getPrivacyPolicy() {
  return get<PrivacyPolicyInfo>('/v1/system/info/privacy_policy');
}

/**
 * 获取商家服务协议
 * 无需认证的公开接口
 * @returns 商家服务协议信息
 */
export function getMerchantServiceAgreement() {
  return get<MerchantServiceAgreementInfo>('/v1/system/info/merchant_service_agreement');
}


