/**
 * 商家优惠券相关API接口
 * 提供优惠券的增删改查等功能
 */

import { get, post, put, del } from '@/modules/merchant/utils/request';

/**
 * 获取优惠券列表
 * @param params 查询参数
 * @returns 优惠券列表
 */
export function getCouponList(params: {
  page: number;
  pageSize: number;
  status?: number;
  type?: number;
  keyword?: string;
}) {
  return get('/v1/merchant/takeout/coupons', params);
}

/**
 * 创建优惠券
 * @param data 优惠券信息
 * @returns 创建的优惠券信息
 */
export function createCoupon(data: {
  name: string;
  description: string;
  type: number;
  start_time: string;
  end_time: string;
  max_usage_count: number;
  per_user_limit: number;
  daily_limit: number;
  total_limit: number;
  amount: number;
  min_order_amount: number;
  max_discount_amount?: number;
  valid_days: number;
  status: number;
  apply_to_all: boolean;
  apply_to_categories?: string;
  apply_to_foods?: string;
  exclude_foods?: string;
  user_level_limit?: number;
  promotion_id?: number;
}) {
  return post('/v1/merchant/takeout/coupons', data);
}

/**
 * 获取优惠券详情
 * @param id 优惠券ID
 * @returns 优惠券详情
 */
export function getCouponDetail(id: number) {
  return get(`/v1/merchant/takeout/coupons/${id}`);
}

/**
 * 更新优惠券
 * @param id 优惠券ID
 * @param data 优惠券信息
 * @returns 更新后的优惠券信息
 */
export function updateCoupon(id: number, data: {
  name?: string;
  description?: string;
  type?: number;
  start_time?: string;
  end_time?: string;
  max_usage_count?: number;
  per_user_limit?: number;
  daily_limit?: number;
  total_limit?: number;
  amount?: number;
  min_order_amount?: number;
  max_discount_amount?: number;
  valid_days?: number;
  status?: number;
  apply_to_all?: boolean;
  apply_to_categories?: string;
  apply_to_foods?: string;
  exclude_foods?: string;
  user_level_limit?: number;
}) {
  return put(`/v1/merchant/takeout/coupons/${id}`, data);
}

/**
 * 删除优惠券
 * @param id 优惠券ID
 * @returns 删除结果
 */
export function deleteCoupon(id: number) {
  return del(`/v1/merchant/takeout/coupons/${id}`);
}

/**
 * 停用优惠券
 * @param id 优惠券ID
 * @returns 操作结果
 */
export function disableCoupon(id: number) {
  return put(`/v1/merchant/takeout/coupons/${id}/disable`);
}

/**
 * 启用优惠券
 * @param id 优惠券ID
 * @returns 操作结果
 */
export function enableCoupon(id: number) {
  return put(`/v1/merchant/takeout/coupons/${id}/enable`);
}

/**
 * 发布优惠券
 * @param id 优惠券ID
 * @returns 操作结果
 */
export function publishCoupon(id: number) {
  return put(`/v1/merchant/takeout/coupons/${id}/publish`);
}

/**
 * 获取优惠券使用统计
 * @param id 优惠券ID
 * @returns 使用统计数据
 */
export function getCouponStats(id: number) {
  return get(`/v1/merchant/takeout/coupons/${id}/statistics`);
}

/**
 * 发放优惠券给用户
 * @param id 优惠券ID
 * @param data 发放信息
 * @returns 操作结果
 */
export function issueCoupon(id: number, data: {
  user_ids?: number[];
  user_level?: number;
  issue_count?: number;
}) {
  return post(`/v1/merchant/takeout/coupons/${id}/issue`, data);
}

/**
 * 批量发放优惠券
 * @param data 批量发放信息
 * @returns 操作结果
 */
export function batchIssueCoupons(data: {
  coupon_ids: number[];
  user_ids?: number[];
  user_level?: number;
  issue_count?: number;
}) {
  return post('/v1/merchant/takeout/coupons/batch-issue', data);
}

/**
 * 获取优惠券发放记录
 * @param id 优惠券ID
 * @param params 查询参数
 * @returns 发放记录列表
 */
export function getCouponIssueRecords(id: number, params: {
  page: number;
  pageSize: number;
  user_id?: number;
  status?: number;
}) {
  return get(`/v1/merchant/takeout/coupons/${id}/issue-records`, params);
}
