/**
 * 商家营业时间管理API接口
 * 提供商家营业时间的增删改查、批量设置、特殊营业时间设置等功能
 */

import { get, post, put, del } from '@/modules/merchant/utils/request';

/**
 * 营业时间段接口
 */
export interface BusinessHour {
  key?: string;      // 格式: "{weekday}:{startTime}:{endTime}"
  weekday: number;  // 0-6 表示周日到周六
  startTime: string; // 格式: "HH:mm"
  endTime: string;   // 格式: "HH:mm"
}

/**
 * 特殊营业时间接口
 */
export interface SpecialBusinessHour {
  id?: number;
  start_date: string; // 格式: "yyyy-MM-dd"
  end_date: string;   // 格式: "yyyy-MM-dd"
  business_hours: Array<{
    startTime: string;
    endTime: string;
  }>;
  priority: 'high' | 'medium' | 'low';
  remarks?: string;
}

/**
 * 自动开关店设置接口
 */
export interface AutoScheduleSettings {
  enabled: boolean;
  open_ahead_minutes: number;
  close_delay_minutes: number;
  notifications: {
    open_notification: {
      enabled: boolean;
      ahead_minutes: number;
    };
    close_notification: {
      enabled: boolean;
      ahead_minutes: number;
    };
    status_change_notification: boolean;
    notification_methods: Array<'sms' | 'app' | 'email'>;
  };
}

/**
 * 获取当前营业时间设置
 * @returns 营业时间设置
 */
export function getBusinessHours() {
  return get('/v1/merchant/api/business-hours');
}

/**
 * 添加营业时间段
 * @param businessHours 营业时间段列表
 * @returns 更新结果
 */
export function addBusinessHours(businessHours: BusinessHour[]) {
  return post('/v1/merchant/api/business-hours', { business_hours: businessHours });
}

/**
 * 批量设置营业时间
 * @param data 批量设置参数
 * @returns 更新结果
 */
export function batchSetBusinessHours(data: {
  replace_existing: boolean;
  weekday_hours: Array<{ startTime: string; endTime: string }>;
  weekend_hours: Array<{ startTime: string; endTime: string }>;
}) {
  return put('/v1/merchant/api/business-hours/batch', data);
}

/**
 * 更新营业时间段
 * @param businessHours 需要更新的营业时间段列表
 * @returns 更新结果
 */
export function updateBusinessHours(businessHours: BusinessHour[]) {
  return put('/v1/merchant/api/business-hours', { 
    business_hours: businessHours 
  });
}

/**
 * 删除营业时间段
 * @param params 删除参数
 * @param params.keys 要删除的营业时间段的key数组，格式为 "{weekday}:{startTime}:{endTime}"
 * @param params.filters 筛选条件
 * @param params.filters.weekdays 按星期几筛选，0-6 表示周日到周六
 * @returns 删除结果
 */
export function deleteBusinessHours(params: {
  keys?: string[];
  filters?: {
    weekdays?: number[];
  };
}) {
  return del('/v1/merchant/api/business-hours', { ...params });
}

/**
 * 设置特殊营业时间
 * @param data 特殊营业时间数据
 * @returns 设置结果
 */
export function setSpecialBusinessHours(data: Omit<SpecialBusinessHour, 'id'>) {
  return post('/v1/merchant/api/business-hours/special', data);
}

// /**
//  * 临时修改营业状态
//  * @param data 修改参数
//  * @returns 修改结果
//  */
// export function updateOperationStatus(data: {
//   status: 0 | 1; // 0-休息中, 1-营业中
//   auto_restore?: boolean;
//   restore_time?: string; // ISO 8601格式
//   remarks?: string;
// }) {
//   return post('/v1/merchant/api/business-hours/operation-status', data);
// }

/**
 * 设置经营状态为休息中
 */
export function setResting() {
  console.log('3.1 调用 setResting API');
  return put('/v1/merchant/secured/operation-status-stop').then(res => {
    console.log('3.2 setResting API 返回结果:', res);
    return res;
  }).catch(err => {
    console.error('3.2 setResting API 调用失败:', err);
    throw err;
  });
}

/**
 * 设置经营状态为营业中
 */
export function setOperating() {
  console.log('3.1 调用 setOperating API');
  return put('/v1/merchant/secured/operation-status-start').then(res => {
    console.log('3.2 setOperating API 返回结果:', res);
    return res;
  }).catch(err => {
    console.error('3.2 setOperating API 调用失败:', err);
    throw err;
  });
}

/**
 * 设置经营状态为营业中
 * @param data 自动开关店设置
 * @returns 设置结果
 */
export function setAutoSchedule(data: AutoScheduleSettings) {
  return post('/v1/merchant/api/business-hours/auto-schedule', data);
}

/**
 * 获取自动任务记录
 * @param params 查询参数
 * @returns 任务记录列表
 */
export function getScheduledTasks(params?: {
  type?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  page_size?: number;
}) {
  return get('/v1/merchant/api/business-hours/tasks', { params });
}

/**
 * 获取营业统计信息
 * @param period 统计周期，如'7d'表示7天
 * @returns 营业统计信息
 */
export function getOperationStats(period: string = '7d') {
  return get('/v1/merchant/secured/business-stats', { params: { period } });
}
