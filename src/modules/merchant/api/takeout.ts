/**
 * 商家外卖商品相关API接口
 */

import { get, post, put, del } from '@/modules/merchant/utils/request';

import type { CategoryItem } from '@/modules/merchant/types';

// 获取分类树
export function getCategoryTree(params?: any): Promise<{ data: CategoryItem[] }> {
  return get('/v1/merchant/takeout/global-categories/tree', params);
}
/**
 * 获取外卖商品列表
 * @param params 查询参数
 * @returns 商品列表
 */
export function getFoodList(params: {
  page: number;
  pageSize: number;
  status?: string;
  category_id?: number;
  keyword?: string;
}) {
  return get('/v1/merchant/takeout/foods', params);
}

/**
 * 清除外卖食品缓存
 */
export function clearFoodCache() {
  return post('/v1/merchant/takeout/foods/clear-cache');
}

/**
 * 获取单个外卖商品详情
 * @param id 商品ID
 * @returns 商品详情
 */
export function getFood(id: number) {
  return get(`/v1/merchant/takeout/foods/${id}`);
}

// 创建外卖商品的返回类型
interface CreateFoodResponse {
  id: number;
  [key: string]: any; // 其他可能的字段
}

/**
 * 创建外卖商品
 * @param data 商品信息
 * @returns 创建的商品信息
 */
export function createFood(data: {
  name: string;
  image: string;
  brief: string;
  category_id: number;
  description: string;
  price: number;
  original_price?: number;
  packaging_fee?: number;
  preparation_time?: number;
  daily_limit?: number;
  is_spicy?: boolean;
  is_recommend?: boolean;
  tags?: string[];
  keywords?: string[];
  stock?: number;
  sort_order?: number;
  status?: number;
}): Promise<CreateFoodResponse> {
  return post('/v1/merchant/takeout/foods', data);
}

/**
 * 更新外卖商品
 * @param id 商品ID
 * @param data 更新的商品信息
 * @returns 更新后的商品信息
 */
export function updateFood(
  id: number, 
  data: Partial<{
    name?: string;
    category_id?: number;
    description?: string;
    brief?: string;
    price?: number;
    original_price?: number;
    packaging_fee?: number;
    preparation_time?: number;
    daily_limit?: number;
    is_spicy?: boolean;
    is_recommend?: boolean;
    tags?: string[];
    keywords?: string[];
    image?: string;
    stock?: number;
    sort_order?: number;
    status?: number;
    audit_status?: number;
  }>
): Promise<void> {
  return put(`/v1/merchant/takeout/foods/${id}`, data);
}

/**
 * 删除外卖商品
 * @param id 商品ID
 * @returns 删除结果
 */
export function deleteFood(id: number) {
  return del(`/v1/merchant/takeout/foods/${id}`);
}

/**
 * 创建商品规格
 * @param foodId 商品ID
 * @param specs 规格信息
 * @returns 设置结果
 */
export function createFoodSpecs(foodId: number, specs: {
  specs: Array<{
    name: string;
    options: Array<{
      name: string;
      price_adjustment: number;
      is_default: boolean;
    }>;
  }>;
}): Promise<void> {
  return post(`/v1/merchant/takeout/foods/${foodId}/variants`, specs);
}

/**
 * 获取商品规格
 * @param foodId 商品ID
 * @returns 规格信息
 */
export function getFoodSpecs(foodId: number) {
  return get(`/v1/merchant/takeout/foods/${foodId}/variants`);
}

// 注意：批量设置规格的API已移除
// 现在使用createFoodVariant/updateFoodVariant/deleteFoodVariant单个操作替代

/**
 * 创建单个商品规格变体
 * @param foodId 商品ID
 * @param data 规格变体信息
 * @returns 创建的规格变体
 */
export function createFoodVariant(foodId: number, data: {
  name: string;
  description?: string;
  image?: string;
  price: number;
  original_price?: number;
  stock?: number;
  is_default?: boolean;
  sort_order?: number;
}) {
  return post(`/v1/merchant/takeout/foods/${foodId}/variants`, data);
}

/**
 * 更新单个商品规格变体
 * @param variantId 规格变体ID
 * @param data 更新的规格变体信息
 * @returns 更新结果
 */
export function updateFoodVariant(variantId: number, data: {
  food_id: number;
  name: string;
  description?: string;
  image?: string;
  price: number;
  original_price?: number;
  stock?: number;
  is_default?: boolean;
  sort_order?: number;
}) {
  return put(`/v1/merchant/takeout/variants/${variantId}`, data);
}

/**
 * 删除单个商品规格变体
 * @param variantId 规格变体ID
 * @returns 删除结果
 */
export function deleteFoodVariant(variantId: number) {
  return del(`/v1/merchant/takeout/variants/${variantId}`);
}

/**
 * 上传商品图片
 * @param foodId 商品ID 
 * @param formData 包含图片的FormData
 * @returns 上传结果
 */
export function uploadFoodImages(foodId: number, formData: FormData) {
  return post(`/v1/merchant/takeout/foods/${foodId}/images`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 获取商品图片
 * @param foodId 商品ID
 * @returns 图片信息
 */
export function getFoodImages(foodId: number) {
  return get(`/v1/merchant/takeout/foods/${foodId}/images`);
}

/**
 * 提交商品审核
 * @param foodId 商品ID
 * @param data 提交信息
 * @returns 审核结果
 */
export function submitFoodForAudit(foodId: number, data: { submit_note?: string }) {
  return post(`/v1/merchant/takeout/foods/${foodId}/submit`, data);
}

/**
 * 获取商品分类列表
 * @returns 分类列表
 */
export function getFoodCategories() {
  return get('/v1/merchant/takeout/categories');
}

/**
 * 上架商品
 * @param id 商品ID
 */
export function enableFood(id: number) {
  return put(`/v1/merchant/takeout/foods/${id}/enable`);
}

/**
 * 下架商品
 * @param id 商品ID
 */
export function disableFood(id: number) {
  return put(`/v1/merchant/takeout/foods/${id}/disable`);
}

/**
 * 批量操作商品
 * @param action 操作类型: enable, disable, delete
 * @param ids 商品ID数组
 */
export function batchFoodAction(action: string, ids: number[]) {
  return post(`/v1/merchant/takeout/foods/batch/${action}`, { ids });
}
