/**
 * 商家模块前端路径API
 * 提供获取前端路径数据的接口
 */
import request from '@/modules/merchant/utils/request';

/**
 * 获取商家前端路径数据
 * @returns 前端路径数据
 */
// 定义前端路径模块数据类型
interface FrontendPathItem {
  path: string;
  title: string;
  icon?: string;
  count?: number;
  config_key?: string;
  config_type?: string;
  group?: string;
  id?: number | string;
  version_hash?: string;
}

interface FrontendPathModule {
  module: string;
  paths: FrontendPathItem[];
}

/**
 * 获取商家前端路径数据
 * @returns 前端路径数据模块数组
 */
export const getFrontendPaths = async (): Promise<FrontendPathModule[]> => {
  try {
    console.log('开始请求商家前端路径数据');
    
    // 使用商家模块的前端路径API端点
    const response = await request({
      url: '/v1/ui-config/ui-configs/frontend-paths',
      method: 'get'
    });
    
    console.log('商家前端路径数据原始响应:', response);
    
    // 检查响应数据是否有效
    if (response) {
      // 由于响应拦截器已经处理过响应，response直接就是data内容
      
      // 如果response是数组，并且格式符合预期，只返回商家模块数据
      if (Array.isArray(response)) {
        // 验证数组内容是否符合预期格式
        const isValidFormat = response.every(item => 
          item && typeof item.module === 'string' && Array.isArray(item.paths)
        );
        
        if (isValidFormat) {
          // 筛选出module为'merchant'的项目
          const merchantModule = response.find(item => item.module === 'merchant');
          
          if (merchantModule) {
            // 只返回商家模块数据
            return [merchantModule] as FrontendPathModule[];
          }
        }
      }
      
      // 处理常见的嵌套数据格式
      const responseObj = response as any; // 使用any类型处理可能的数据结构
      
      if (responseObj.list && Array.isArray(responseObj.list)) {
        // 筛选出module为'merchant'的项目
        const merchantModule = responseObj.list.find((item: any) => item.module === 'merchant');
        if (merchantModule) {
          return [merchantModule] as FrontendPathModule[];
        }
      }
      
      if (responseObj.items && Array.isArray(responseObj.items)) {
        // 筛选出module为'merchant'的项目
        const merchantModule = responseObj.items.find((item: any) => item.module === 'merchant');
        if (merchantModule) {
          return [merchantModule] as FrontendPathModule[];
        }
      }
      
      // 如果是对象格式，包装成模块格式
      if (typeof responseObj === 'object' && !Array.isArray(responseObj)) {
        // 构造兼容格式
        return [{
          module: 'merchant',
          paths: Object.entries(response).map(([key, value]) => {
            // 安全地获取值，当value为对象且不为null时才试图访问属性
            const valueObj = (value && typeof value === 'object') ? value : null;
            return {
              path: key,
              title: valueObj && 'title' in valueObj ? String(valueObj.title) : key,
              icon: valueObj && 'icon' in valueObj ? String(valueObj.icon) : undefined,
              count: valueObj && 'count' in valueObj ? Number(valueObj.count) : 0
            };
          })
        }];
      }
    }
    
    // 创建假数据，当API未实现时用于测试UI
    console.log('未能获取到有效的商家前端路径数据，使用默认数据');
    const defaultData: FrontendPathModule[] = [{
      module: 'merchant',
      paths: [
        { path: '/merchant/products', title: '商品管理', icon: 'Goods', count: 0 },
        { path: '/merchant/orders', title: '订单管理', icon: 'List', count: 0 },
        { path: '/merchant/financial', title: '财务管理', icon: 'Money', count: 0 },
        { path: '/merchant/statistics', title: '数据统计', icon: 'DataAnalysis', count: 0 }
      ]
    }];
    return defaultData;
  } catch (error) {
    console.error('获取商家前端路径数据失败:', error);
    
    // 出错时返回默认数据
    return [{
      module: 'merchant',
      paths: [
        { path: '/merchant/products', title: '商品管理', icon: 'Goods', count: 0 },
        { path: '/merchant/orders', title: '订单管理', icon: 'List', count: 0 }
      ]
    }];
  }
};
