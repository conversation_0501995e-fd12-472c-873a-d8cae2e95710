/**
 * 评价相关API接口
 */

import { get, post } from '@/modules/merchant/utils/request';

/**
 * 获取评价列表
 * @param params 查询参数
 * @returns 评价列表
 */
export function getReviewList(params: {
  page: number;
  pageSize: number;
  productId?: number;
  rating?: number;
}) {
  return get('/api/v1/merchant/reviews', params);
}

/**
 * 回复评价
 * @param id 评价ID
 * @param content 回复内容
 * @returns 回复结果
 */
export function replyReview(id: number, content: string) {
  return post(`/api/v1/merchant/reviews/${id}/reply`, { content });
}
