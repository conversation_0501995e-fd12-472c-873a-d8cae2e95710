/**
 * 商品相关API接口
 */

import { get, post, put, del } from '@/modules/merchant/utils/request';
import type { ProductInfo } from '../types';

/**
 * 获取商品列表
 * @param params 查询参数
 * @returns 商品列表
 */
export function getProductList(params: {
  page: number;
  pageSize: number;
  status?: string;
  categoryId?: number;
  keyword?: string;
}) {
  return get('/api/v1/merchant/products', params);
}

/**
 * 创建商品
 * @param data 商品信息
 * @returns 创建的商品信息
 */
export function createProduct(data: Omit<ProductInfo, 'id' | 'createTime'>) {
  return post<ProductInfo>('/api/v1/merchant/products', data);
}

/**
 * 更新商品
 * @param id 商品ID
 * @param data 更新的商品信息
 * @returns 更新后的商品信息
 */
export function updateProduct(id: number, data: Partial<ProductInfo>) {
  return put<ProductInfo>(`/api/v1/merchant/products/${id}`, data);
}

/**
 * 删除商品
 * @param id 商品ID
 * @returns 删除结果
 */
export function deleteProduct(id: number) {
  return del(`/api/v1/merchant/products/${id}`);
}

/**
 * 上架商品
 * @param id 商品ID
 * @returns 上架结果
 */
export function putOnSale(id: number) {
  return post(`/api/v1/merchant/products/${id}/on-sale`);
}

/**
 * 下架商品
 * @param id 商品ID
 * @returns 下架结果
 */
export function putOffSale(id: number) {
  return post(`/api/v1/merchant/products/${id}/off-sale`);
}
