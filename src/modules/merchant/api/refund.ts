/**
 * 退款相关API接口
 */

import { get, post } from '@/modules/merchant/utils/request';

/**
 * 获取退款列表
 * @param params 查询参数
 * @returns 退款列表
 */
export function getRefundList(params: {
  page: number;
  pageSize: number;
  status?: string;
  startTime?: string;
  endTime?: string;
}) {
  return get('/api/v1/merchant/refunds', params);
}

/**
 * 获取退款详情
 * @param id 退款ID
 * @returns 退款详情
 */
export function getRefundDetail(id: number) {
  return get(`/api/v1/merchant/refunds/${id}`);
}

/**
 * 处理退款申请
 * @param id 退款ID
 * @param data 处理数据
 * @returns 处理结果
 */
export function handleRefund(id: number, data: {
  action: 'approve' | 'reject';
  reason?: string;
}) {
  return post(`/api/v1/merchant/refunds/${id}/handle`, data);
}
