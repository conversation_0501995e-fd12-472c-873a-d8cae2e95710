/**
 * 商户认证相关API接口
 */

import { post, get } from '@/modules/merchant/utils/request';
import type { MerchantLoginParams, MerchantInfo, MerchantApplication, SmsLoginParams } from '../types';
import localforage from 'localforage';

/**
 * Token信息类型定义
 */
export interface TokenInfo {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

/**
 * 商户登录
 * @param data 登录参数
 * @returns 登录结果，包含token_info和merchant信息
 */
export function login(data: MerchantLoginParams) {
  return post<{
    token_info: {
      access_token: string;
      refresh_token: string;
      expires_in: number;
      token_type: string;
    };
    merchant: MerchantInfo;
    device_id?: string;
    is_new_device?: boolean;
    risk_level?: string;
  } | { token: string; merchantInfo: MerchantInfo; device_id?: string; is_new_device?: boolean; risk_level?: string }>('/v1/merchant/login', data);
}

/**
 * 商户登出
 * @param deviceId 设备ID
 * @returns 登出结果
 */
export function logout(deviceId: string) {
  return post(`/v1/merchant/devices/${deviceId}/logout`);
}

/**
 * 修改密码
 * @param data 密码修改参数
 * @returns 修改结果
 */
export function changePassword(data: { oldPassword: string; newPassword: string }) {
  return post('/v1/merchant/change-password', data);
}

/**
 * 刷新token
 * @param data 包含refresh_token的对象
 * @returns 刷新后的token信息
 */
export function refreshToken(data: { refresh_token: string }) {
  return post<TokenInfo>('/v1/merchant/refresh-token', data);
}

/**
 * 商家注册
 * @param data 注册参数
 * @returns 注册结果
 */
export function register(data: MerchantApplication) {
  return post('/v1/merchant/register', data);
}

/**
 * 获取商家注册分类
 * @returns 注册分类列表
 */
export function merchantCategory() {
  return get('/v1/merchant/merchant-categories');
}

/**
 * 获取长期token
 * @returns 长期token（refresh_token）或null
 */
export async function getLongTermToken(): Promise<string | null> {
  try {
    return await localforage.getItem<string>('merchant_refresh_token');
  } catch (error) {
    console.error('获取长期token失败:', error);
    return null;
  }
}

/**
 * 发送短信验证码
 * @param mobile 手机号
 * @returns 发送结果
 */
export function sendSmsCode(mobile: string) {
  return post<{
    code: number;
    message: string;
    data: {
      expire_time: number; // 过期时间（秒）
    }
  }>('/v1/merchant/send-code', { mobile });
}

/**
 * 短信验证码登录
 * @param data 登录参数，包含手机号和验证码
 * @returns 登录结果，包含token_info和merchant信息
 */
export function smsLogin(data: SmsLoginParams) {
  const requestData = {
    mobile: data.phone,
    code: data.code,
    ...(data.device_info && { device_info: data.device_info })
  };
  return post<{
    token_info: {
      access_token: string;
      refresh_token: string;
      expires_in: number;
      token_type: string;
    };
    merchant: MerchantInfo;
    device_id?: string;
    is_new_device?: boolean;
    risk_level?: string;
  } | { token: string; merchantInfo: MerchantInfo; device_id?: string; is_new_device?: boolean; risk_level?: string }>('/v1/merchant/login/verify-code', requestData);
}
