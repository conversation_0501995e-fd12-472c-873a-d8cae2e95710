/**
 * 通知相关API接口
 */

import { get, put } from '@/modules/merchant/utils/request';

/**
 * 获取商户通知列表
 * @param params 查询参数
 * @returns 通知列表
 */
export function getNotificationList(params: {
  page: number;
  pageSize: number;
  type?: string;
  isRead?: boolean;
}) {
  return get('/api/v1/merchant/notifications', params);
}

/**
 * 标记通知已读
 * @param id 通知ID
 * @returns 标记结果
 */
export function markNotificationRead(id: number) {
  return put(`/api/v1/merchant/notifications/${id}/read`);
}
