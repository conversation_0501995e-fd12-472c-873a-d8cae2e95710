/**
 * 财务相关API接口
 */

import { get, post } from '@/modules/merchant/utils/request';

/**
 * 获取商户账单列表
 * @param params 查询参数
 * @returns 账单列表
 */
export function getBillList(params: {
  page: number;
  pageSize: number;
  type?: string;
  startTime?: string;
  endTime?: string;
}) {
  return get('/api/v1/merchant/bills', params);
}

/**
 * 获取账单详情
 * @param id 账单ID
 * @returns 账单详情
 */
export function getBillDetail(id: number) {
  return get(`/api/v1/merchant/bills/${id}`);
}

/**
 * 申请提现
 * @param data 提现数据
 * @returns 申请结果
 */
export function applyWithdrawal(data: {
  amount: number;
  bankAccount: string;
  bankName: string;
  accountName: string;
}) {
  return post('/api/v1/merchant/withdrawals', data);
}
