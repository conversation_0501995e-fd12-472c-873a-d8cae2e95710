/**
 * 商家聊天WebSocket服务
 * <AUTHOR>
 * @date 2025-01-27
 * @version 1.0.0
 * @description 全局WebSocket聊天服务，负责管理商家端的实时聊天连接，支持自动连接、断线重连、消息处理等功能
 * 
 * 主要功能：
 * - 自动连接管理：商家登录后自动建立WebSocket连接
 * - 断线重连：网络断开后自动重连，支持指数退避策略
 * - 消息处理：统一处理聊天消息的发送和接收
 * - 状态管理：维护连接状态和在线状态
 * - 事件分发：支持多个组件监听聊天事件
 */

// Vue导入已移除，使用普通JavaScript对象管理状态
import { ElMessage, ElNotification } from 'element-plus'
import { useMerchantStore } from '../stores/merchantStore'
import { useSessionStore } from '@/modules/chat/stores'

// WebSocket连接状态枚举
enum WebSocketStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting', 
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

// 消息类型枚举
enum MessageType {
  CHAT = 'chat',
  MESSAGE = 'message',  // 新增：用户消息类型
  SYSTEM = 'system',
  HEARTBEAT = 'heartbeat',
  MERCHANT_STATUS = 'merchant_status',
  NOTIFICATION = 'notification'
}

// 聊天消息接口
interface ChatMessage {
  id?: string
  session_id: number
  sender_type: 'merchant' | 'customer'
  sender_id: number
  content: string
  message_type: 'text' | 'image' | 'file'
  created_at: string
  read_status?: boolean
}

// 会话信息接口
interface ChatSession {
  id: number
  customer_id: number
  customer_name: string
  customer_avatar?: string
  last_message?: string
  last_message_time?: string
  unread_count: number
  status: 'active' | 'closed'
  priority: 'normal' | 'urgent'
}

// 事件监听器类型
type EventListener = (data: any) => void

/**
 * 商家聊天WebSocket服务类
 */
class ChatWebSocketService {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 10
  private reconnectDelay = 1000 // 初始重连延迟1秒
  private maxReconnectDelay = 30000 // 最大重连延迟30秒
  private heartbeatInterval: ReturnType<typeof setInterval> | null = null
  private heartbeatTimer = 30000 // 心跳间隔30秒
  private reconnectTimer: ReturnType<typeof setTimeout> | null = null
  
  // 内部状态已移除，直接使用公共状态对象

  // 对外提供的响应式状态接口
  public status: { value: WebSocketStatus } = { value: WebSocketStatus.DISCONNECTED }
  public isConnected: { value: boolean } = { value: false }
  public lastError: { value: string } = { value: '' }
  public unreadCount: { value: number } = { value: 0 }

  // 事件监听器
  private eventListeners: Map<string, EventListener[]> = new Map()

  // 初始化状态
  private isInitialized: boolean = false

  constructor() {
    // 初始化状态对象
    this.initializeState()
  }

  /**
   * 初始化状态对象
   */
  private initializeState(): void {
    console.log('初始化WebSocket状态对象')

    // 确保状态对象正确初始化
    this.status = { value: WebSocketStatus.DISCONNECTED }
    this.isConnected = { value: false }
    this.lastError = { value: '' }
    this.unreadCount = { value: 0 }

    // 状态已在上面初始化

    console.log('WebSocket状态对象初始化完成')
  }

  /**
   * 更新状态的内部方法
   */
  private updateStatus(newStatus: WebSocketStatus): void {
    this.status.value = newStatus
    this.isConnected.value = newStatus === WebSocketStatus.CONNECTED

    // 触发状态变化事件
    this.emit('statusChanged', { status: newStatus })
  }

  /**
   * 更新错误信息
   */
  private updateError(error: string): void {
    this.lastError.value = error
  }

  /**
   * 更新未读消息数
   */
  private updateUnreadCount(count: number): void {
    this.unreadCount.value = count
  }

  /**
   * 初始化服务
   * 在 Pinia 激活后调用此方法
   */
  public init(): void {
    if (this.isInitialized) {
      return
    }
    this.isInitialized = true

    // 响应式状态已在构造函数中初始化
    console.log('WebSocket服务初始化完成，等待外部调用connect()方法')
  }


  
  /**
   * 获取商家store实例
   */
  private getMerchantStore() {
    return useMerchantStore()
  }
  

  
  /**
   * 建立WebSocket连接
   * @deprecated 此服务已废弃，请使用统一的chatStore代替
   */
  public async connect(): Promise<void> {
    console.warn('⚠️ ChatWebSocketService.connect() 已废弃，请使用统一的chatStore代替')
    console.warn('⚠️ 为避免重复连接和冲突，此方法将不执行任何操作')
    return

    // 以下代码已禁用，避免与新的WebSocket服务冲突
    /*
    // 确保状态对象正确初始化
    if (!this.status || typeof this.status.value === 'undefined') {
      console.warn('WebSocket服务状态未正确初始化，重新初始化')
      this.initializeState()
    }

    if (this.status.value === WebSocketStatus.CONNECTING ||
        this.status.value === WebSocketStatus.CONNECTED) {
      console.log('WebSocket已连接或正在连接中')
      return
    }

    const merchantStore = this.getMerchantStore()
    const merchantInfo = merchantStore.merchantInfo
    const token = merchantStore.token

    if (!merchantInfo || !merchantInfo.id || !token) {
      console.warn('商家未登录，无法建立WebSocket连接')
      return
    }
    */

    // 以下代码已禁用，避免与新的WebSocket服务冲突
    /*
    try {
      this.updateStatus(WebSocketStatus.CONNECTING)
      this.updateError('')

      // 构建WebSocket URL
      const wsUrl = this.buildWebSocketUrl(merchantInfo.id, token)
      console.log('正在连接WebSocket:', wsUrl)

      // 创建WebSocket连接
      this.ws = new WebSocket(wsUrl)

      // 设置事件监听器
      this.setupWebSocketEventListeners()

    } catch (error: any) {
      console.error('WebSocket连接失败:', error)
      this.updateStatus(WebSocketStatus.ERROR)
      this.updateError(error.message || 'WebSocket连接失败')
      this.scheduleReconnect()
    }
    */
  }
  
  /**
   * 构建WebSocket连接URL - 支持多端登录
   */
  private buildWebSocketUrl(_merchantId: number, token: string): string {
    // 优先使用环境变量中的WebSocket URL
    const wsBaseUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8181'

    // 如果环境变量没有配置，则根据当前页面协议和地址构建
    let baseUrl = wsBaseUrl
    if (!wsBaseUrl || wsBaseUrl === 'ws://localhost:8181') {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const host = window.location.hostname
      const port = import.meta.env.DEV ? '8181' : window.location.port  // 修改为后端WebSocket服务端口
      baseUrl = `${protocol}//${host}:${port}`
    }

    // 获取设备ID用于多端登录
    const deviceId = this.getDeviceId()

    // 构建支持多端登录的WebSocket URL
    const wsUrl = `${baseUrl}/api/v1/chat/ws?token=${encodeURIComponent(token)}&device_id=${encodeURIComponent(deviceId)}`

    console.log('🔗 构建商家WebSocket URL (多端登录):', {
      baseUrl,
      merchantId: _merchantId,
      deviceId,
      tokenLength: token.length,
      finalUrl: wsUrl.replace(token, '***TOKEN***')
    })

    return wsUrl
  }

  /**
   * 获取设备ID
   */
  private getDeviceId(): string {
    try {
      // 优先从商家store中获取设备信息
      const deviceInfo = JSON.parse(localStorage.getItem('merchant_current_device_info') || '{}')
      if (deviceInfo.device_id) {
        console.log('🔍 从localStorage获取商家设备ID:', deviceInfo.device_id)
        return deviceInfo.device_id
      }
    } catch (error) {
      console.warn('获取商家设备信息失败:', error)
    }

    // 同步获取设备ID的备用方案
    const fallbackDeviceId = 'omall_merchant_' + Date.now().toString(36) + '_' + Math.random().toString(36).substring(2, 11)
    console.log('🔍 使用备用商家设备ID:', fallbackDeviceId)
    return fallbackDeviceId
  }
  
  /**
   * 设置WebSocket事件监听器
   */
  private setupWebSocketEventListeners(): void {
    if (!this.ws) return
    
    this.ws.onopen = () => {
      console.log('WebSocket连接已建立')
      this.updateStatus(WebSocketStatus.CONNECTED)
      this.reconnectAttempts = 0
      this.reconnectDelay = 1000

      // 发送商家在线状态
      this.sendMerchantStatus('online')

      // 启动心跳
      this.startHeartbeat()

      // 触发连接成功事件
      this.emit('connected', { merchantId: this.getMerchantStore().merchantInfo?.id })

      ElMessage.success('聊天服务已连接')
    }
    
    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.handleWebSocketMessage(data)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error, event.data)
      }
    }
    
    this.ws.onclose = (event) => {
      console.log('WebSocket连接已关闭:', event.code, event.reason)
      this.updateStatus(WebSocketStatus.DISCONNECTED)
      this.stopHeartbeat()

      // 触发断开连接事件
      this.emit('disconnected', { code: event.code, reason: event.reason })

      // 如果不是主动断开，则尝试重连
      if (event.code !== 1000 && this.getMerchantStore().isLoggedIn) {
        this.scheduleReconnect()
      }
    }
    
    this.ws.onerror = (error) => {
      console.error('WebSocket连接错误:', error)
      this.updateStatus(WebSocketStatus.ERROR)
      this.updateError('WebSocket连接错误')

      // 触发错误事件
      this.emit('error', { error })
    }
  }
  
  /**
   * 处理WebSocket消息
   */
  private handleWebSocketMessage(data: any): void {
    console.log('收到WebSocket消息:', data)

    switch (data.type) {
      case MessageType.CHAT:
        this.handleChatMessage(data)
        break
      case MessageType.MESSAGE:
        // MESSAGE类型和CHAT类型使用相同的处理逻辑
        this.handleChatMessage(data)
        break
      case MessageType.SYSTEM:
        this.handleSystemMessage(data)
        break
      case MessageType.HEARTBEAT:
        this.handleHeartbeatMessage(data)
        break
      case MessageType.NOTIFICATION:
        this.handleNotificationMessage(data)
        break
      case 'customer_status':
        this.handleCustomerStatusMessage(data)
        break
      default:
        console.log('未知消息类型:', data.type)
    }
  }
  
  /**
   * 处理聊天消息
   */
  private handleChatMessage(data: any): void {
    // 根据消息类型获取消息数据
    let message: ChatMessage

    if (data.type === 'message') {
      // 新格式：消息数据在 data.data 中
      message = data.data
      console.log('处理MESSAGE类型消息:', message)
    } else {
      // 旧格式：消息数据在 data.message 中
      message = data.message
      console.log('处理CHAT类型消息:', message)
    }

    if (!message) {
      console.error('消息数据为空:', data)
      return
    }

    // 统一处理sender_type字段（服务器可能使用'user'，我们统一为'customer'）
    if ((message as any).sender_type === 'user') {
      (message as any).sender_type = 'customer'
    }

    // 更新未读计数
    if (message.sender_type === 'customer') {
      this.updateUnreadCount(this.unreadCount.value + 1)
    }

    // 触发新消息事件，使用组件期望的格式
    this.emit('message', {
      event: 'new_message',
      data: message,
      session_id: message.session_id || data.session_id
    })

    // 显示消息通知
    if (message.sender_type === 'customer') {
      ElMessage({
        message: `收到来自客户的新消息: ${message.content}`,
        type: 'info',
        duration: 3000
      })
    }
  }
  
  /**
   * 处理系统消息
   */
  private handleSystemMessage(data: any): void {
    console.log('收到系统消息:', data.message)
    
    // 触发系统消息事件
    this.emit('system_message', data)
    
    // 显示系统通知
    ElMessage({
      message: data.message,
      type: 'warning',
      duration: 5000
    })
  }
  
  /**
   * 处理心跳消息
   */
  private handleHeartbeatMessage(_data: any): void {
    // 心跳响应，保持连接活跃
    console.log('收到心跳响应')
  }

  /**
   * 处理客户状态消息
   */
  private handleCustomerStatusMessage(data: any): void {
    console.log('收到客户状态消息:', data)

    // 触发客户状态更新事件
    this.emit('customer_status_update', {
      event: data.event,
      data: data.data,
      timestamp: data.timestamp
    })
  }

  /**
   * 处理通知消息
   */
  private handleNotificationMessage(data: any): void {
    console.log('收到通知消息:', data)

    // 根据通知事件类型处理
    switch (data.event) {
      case 'connected':
        console.log('服务器确认连接已建立')
        // 确保状态为已连接
        if (this.status.value !== WebSocketStatus.CONNECTED) {
          this.updateStatus(WebSocketStatus.CONNECTED)
          this.emit('connected', { merchantId: this.getMerchantStore().merchantInfo?.id })
        }
        break
      case 'disconnected':
        console.log('服务器通知连接已断开')
        this.updateStatus(WebSocketStatus.DISCONNECTED)
        this.emit('disconnected', {})
        break
      case 'error':
        console.log('服务器通知连接错误')
        this.updateStatus(WebSocketStatus.ERROR)
        this.emit('error', data.data || {})
        break
      // 客户状态相关事件
      case 'customer_online':
      case 'customer_offline':
      case 'customer_status_change':
        console.log('收到客户状态变更通知:', data.event, data.data)
        this.emit('customer_status_update', {
          event: data.event,
          data: data.data,
          timestamp: data.timestamp
        })
        break
      // 商家新订单通知
      case 'order_merchant_new_order':
        console.log('收到新订单通知:', data.event, data.data)
        this.handleNewOrderNotification(data)
        break
      default:
        console.log('未知通知事件:', data.event)
        // 触发通用通知事件
        this.emit('notification', data)
    }
  }
  
  /**
   * 发送消息
   */
  public sendMessage(sessionId: number, content: string, messageType: 'text' | 'image' | 'file' = 'text'): boolean {
    if (!this.isConnected.value || !this.ws) {
      console.error('WebSocket未连接，无法发送消息')
      ElMessage.error('聊天服务未连接，请稍后重试')
      return false
    }
    
    const message = {
      type: MessageType.CHAT,
      session_id: sessionId,
      content,
      message_type: messageType,
      timestamp: new Date().toISOString()
    }
    
    try {
      this.ws.send(JSON.stringify(message))
      console.log('消息已发送:', message)
      return true
    } catch (error) {
      console.error('发送消息失败:', error)
      ElMessage.error('发送消息失败')
      return false
    }
  }
  
  /**
   * 发送商家状态
   */
  private sendMerchantStatus(status: 'online' | 'offline'): void {
    if (!this.ws || this.status.value !== WebSocketStatus.CONNECTED) {
      return
    }
    
    const merchantId = this.getMerchantStore().merchantInfo?.id
    if (!merchantId) {
      console.warn('商家ID不存在，无法发送状态')
      return
    }
    
    const message = {
      type: MessageType.MERCHANT_STATUS,
      status,
      merchant_id: merchantId,
      timestamp: new Date().toISOString()
    }
    
    try {
      this.ws.send(JSON.stringify(message))
      console.log('商家状态已发送:', status)
    } catch (error) {
      console.error('发送商家状态失败:', error)
    }
  }

  /**
   * 处理新订单通知
   */
  private handleNewOrderNotification(data: any): void {
    const notificationData = data.data
    
    if (!notificationData) {
      console.warn('新订单通知数据为空:', data)
      return
    }

    // 弹出ElNotification通知
    ElNotification({
      title: notificationData.title || '收到新订单',
      message: notificationData.content || '您收到新订单，请及时处理',
      type: 'success',
      duration: 0, // 不自动关闭
      position: 'top-right',
      showClose: true,
      onClick: () => {
        // 点击通知时可以跳转到订单详情页
        if (notificationData.data?.order_id) {
          console.log('点击订单通知，订单ID:', notificationData.data.order_id)
          // 这里可以添加路由跳转逻辑
        }
      }
    })

    // 更新sessionStore中的未读计数
    try {
      const sessionStore = useSessionStore()
      // 创建一个虚拟的订单通知会话或更新现有会话的未读计数
      // 这里我们简单地增加第一个会话的未读计数作为示例
      if (sessionStore.sessions && sessionStore.sessions.length > 0) {
        const firstSession = sessionStore.sessions[0]
        if (firstSession) {
          firstSession.unread_count = (firstSession.unread_count || 0) + 1
          console.log('已更新会话未读计数:', firstSession.unread_count)
        }
      }
    } catch (error) {
      console.error('更新sessionStore未读计数失败:', error)
    }

    // 增加未读计数（这里增加聊天图标上的未读提醒）
    this.updateUnreadCount(this.unreadCount.value + 1)

    // 触发新订单事件，供其他组件监听
    this.emit('new_order', {
      event: data.event,
      data: notificationData,
      timestamp: data.timestamp
    })

    console.log('新订单通知处理完成:', {
      title: notificationData.title,
      orderId: notificationData.data?.order_id,
      unreadCount: this.unreadCount.value
    })
  }
  
  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()
    
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.status.value === WebSocketStatus.CONNECTED) {
        const heartbeat = {
          type: MessageType.HEARTBEAT,
          timestamp: new Date().toISOString()
        }
        
        try {
          this.ws.send(JSON.stringify(heartbeat))
        } catch (error) {
          console.error('发送心跳失败:', error)
        }
      }
    }, this.heartbeatTimer)
  }
  
  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }
  
  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('达到最大重连次数，停止重连')
      this.updateStatus(WebSocketStatus.ERROR)
      this.updateError('连接失败，请检查网络后手动重连')
      ElMessage.error('聊天服务连接失败，请检查网络后刷新页面')
      return
    }

    this.reconnectAttempts++
    this.updateStatus(WebSocketStatus.RECONNECTING)
    
    // 清除之前的重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }
    
    console.log(`第${this.reconnectAttempts}次重连，${this.reconnectDelay}ms后重试`)
    
    this.reconnectTimer = setTimeout(() => {
      this.connect()
    }, this.reconnectDelay)
    
    // 指数退避策略
    this.reconnectDelay = Math.min(this.reconnectDelay * 2, this.maxReconnectDelay)
  }
  
  /**
   * 手动重连
   */
  public reconnect(): void {
    console.log('手动重连WebSocket')
    this.reconnectAttempts = 0
    this.reconnectDelay = 1000
    this.disconnect()
    setTimeout(() => {
      this.connect()
    }, 1000)
  }
  
  /**
   * 断开连接
   */
  public disconnect(): void {
    console.log('断开WebSocket连接')
    
    // 清除重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    // 停止心跳
    this.stopHeartbeat()
    
    // 发送离线状态
    this.sendMerchantStatus('offline')
    
    // 关闭WebSocket连接
    if (this.ws) {
      this.ws.close(1000, '主动断开连接')
      this.ws = null
    }
    
    this.updateStatus(WebSocketStatus.DISCONNECTED)
    this.updateUnreadCount(0)
  }
  
  /**
   * 添加事件监听器
   */
  public on(event: string, listener: EventListener): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(listener)
  }
  
  /**
   * 移除事件监听器
   */
  public off(event: string, listener: EventListener): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }
  
  /**
   * 触发事件
   */
  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`事件监听器执行失败 [${event}]:`, error)
        }
      })
    }
  }
  
  /**
   * 获取会话列表
   * @returns {Promise<ChatSession[]>} 会话列表
   */
  public async fetchSessions(): Promise<ChatSession[]> {
    try {
      const token = this.getMerchantStore().token
      if (!token) {
        throw new Error('商家未登录')
      }

      const response = await fetch(`${this.getApiBaseUrl()}/api/v1/chat/sessions`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`获取会话列表失败: ${response.status}`)
      }

      const result = await response.json()
      
      // 验证返回数据结构
      if (!result || !result.data) {
        console.warn('后端返回数据格式异常:', result)
        return []
      }

      // 后端返回的数据结构是 data.list，不是 data.sessions
      const sessionList = result.data.list || result.data.sessions || []

      // 确保返回的是数组
      if (!Array.isArray(sessionList)) {
        console.warn('会话列表不是数组格式:', sessionList)
        return []
      }

      return sessionList.filter((session: any) => 
        session && session.receiver_type === 'merchant'
      )
    } catch (error) {
      console.error('获取商家会话失败:', error)
      throw error
    }
  }

  /**
   * 获取指定会话的消息列表
   * @param {number} sessionId 会话ID
   * @param {number} page 页码，从1开始
   * @param {number} pageSize 每页消息数量
   * @returns {Promise<{messages: ChatMessage[], hasMore: boolean, total: number}>} 消息列表和分页信息
   */
  public async fetchMessages(sessionId: number, page: number = 1, pageSize: number = 10): Promise<{messages: ChatMessage[], hasMore: boolean, total: number}> {
    try {
      if (!sessionId) {
        throw new Error('会话ID不能为空')
      }

      const token = this.getMerchantStore().token
      if (!token) {
        throw new Error('商家未登录')
      }

      // 构建分页参数
      const params = new URLSearchParams({
        page: page.toString(),
        page_size: pageSize.toString()
      })

      const response = await fetch(`${this.getApiBaseUrl()}/api/v1/chat/sessions/${sessionId}/messages?${params}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`获取消息失败: ${response.status}`)
      }

      const result = await response.json()

      // 验证返回数据结构
      if (!result || !result.data) {
        console.warn('后端返回数据格式异常:', result)
        return {
          messages: [],
          hasMore: false,
          total: 0
        }
      }

      // 后端返回的数据结构可能是 data.list 或 data.messages，需要兼容处理
      const messageList = result.data.messages || result.data.list || []
      const total = result.data.total || 0
      const currentPage = result.data.page || page
      const totalPages = result.data.page_count || Math.ceil(total / pageSize)

      // 确保返回的是数组
      if (!Array.isArray(messageList)) {
        console.warn('消息列表不是数组格式:', messageList)
        return {
          messages: [],
          hasMore: false,
          total: 0
        }
      }

      // 按时间排序（升序，最早的消息在前面）
      const sortedMessages = messageList.sort((a, b) => {
        const timeA = new Date(a.created_at || a.timestamp || 0).getTime()
        const timeB = new Date(b.created_at || b.timestamp || 0).getTime()
        return timeA - timeB
      })

      // 判断是否还有更多数据
      const hasMore = currentPage < totalPages

      console.log(`获取到 ${sortedMessages.length} 条消息，第 ${currentPage}/${totalPages} 页，总计 ${total} 条`)

      return {
        messages: sortedMessages,
        hasMore,
        total
      }
    } catch (error) {
      console.error('获取消息失败:', error)
      throw error
    }
  }

  /**
   * 发送消息（HTTP API方式）
   * 根据API文档使用正确的端点
   * @param {number} sessionId 会话ID
   * @param {string} content 消息内容
   * @param {string} type 消息类型 ('text' | 'image' | 'file')
   * @returns {Promise<any>} 发送结果
   */
  public async sendMessageHttp(sessionId: number, content: string, type: 'text' | 'image' | 'file' = 'text'): Promise<any> {
    try {
      const token = this.getMerchantStore().token
      if (!token) {
        throw new Error('商家未登录')
      }

      // 根据消息类型选择正确的API端点
      let endpoint: string
      let requestBody: any

      if (type === 'text') {
        // 文本消息端点
        endpoint = `${this.getApiBaseUrl()}/api/v1/chat/sessions/${sessionId}/messages/text`
        requestBody = {
          content: content
        }
      } else {
        // 媒体消息端点（图片、文件等）
        endpoint = `${this.getApiBaseUrl()}/api/v1/chat/sessions/${sessionId}/messages/media`
        requestBody = {
          content: content,
          media_type: type
        }
      }

      console.log('发送消息到API端点:', endpoint)

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`发送消息失败: ${response.status} - ${errorText}`)
      }

      const result = await response.json()
      console.log('消息发送成功，服务器响应:', result)
      return result
    } catch (error) {
      console.error('发送消息失败:', error)
      throw error
    }
  }

  /**
   * 获取API基础URL
   * @returns {string} API基础URL
   */
  private getApiBaseUrl(): string {
    return import.meta.env.VITE_API_BASE_URL || 'http://localhost:8181'
  }

  /**
   * 获取WebSocket连接的readyState
   */
  public getWebSocketReadyState(): number | null {
    return this.ws?.readyState || null
  }

  /**
   * 获取WebSocket实例（只读访问）
   */
  public getWebSocketInstance(): WebSocket | null {
    return this.ws
  }

  /**
   * 获取连接状态信息
   */
  public getStatus() {
    return {
      status: this.status.value,
      isConnected: this.isConnected.value,
      lastError: this.lastError.value,
      unreadCount: this.unreadCount.value,
      reconnectAttempts: this.reconnectAttempts,
      wsReadyState: this.getWebSocketReadyState()
    }
  }
}

// 延迟创建全局单例实例
let chatWebSocketService: ChatWebSocketService | null = null

/**
 * 获取聊天WebSocket服务实例
 * 确保在 Pinia 激活后才创建实例
 * @deprecated 此服务已废弃，请使用统一的chatStore代替
 */
function getChatWebSocketService(): ChatWebSocketService {
  console.warn('⚠️ getChatWebSocketService() 已废弃，请使用统一的chatStore代替')
  console.warn('⚠️ 为避免重复连接，此服务将不会自动连接WebSocket')

  if (!chatWebSocketService) {
    chatWebSocketService = new ChatWebSocketService()
    // 在创建实例后立即初始化，但不自动连接
    chatWebSocketService.init()
  }
  return chatWebSocketService
}

// 导出服务实例获取函数和相关类型
export default getChatWebSocketService
export { ChatWebSocketService, WebSocketStatus, MessageType }
export type { ChatMessage, ChatSession, EventListener }