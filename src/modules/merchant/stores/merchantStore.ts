/**
 * 商家模块状态管理
 */
import { defineStore } from 'pinia';
import localforage from 'localforage';
import { nextTick } from 'vue';
import { 
  login, 
  getMerchantInfo, 
  updateMerchantInfo, 
  getMerchantStats,
  getOrderList,
  getProductList,
  getFrontendPaths,
  setResting,
  setOperating,
  smsLogin as apiSmsLogin
} from '../api';
import { generateDeviceInfo } from '@/utils/deviceInfo';

// API 响应类型
type OperationStatusResponse = {
  operation_status: number;
  [key: string]: any; // 允许其他可能的字段
};
import { refreshToken, getLongTermToken, sendSmsCode } from '../api/auth';
import type { TokenInfo } from '../types';
import type { 
  Merchant, 
  MerchantLoginParams, 
  MerchantStatistics,
  MerchantProduct,
  MerchantOrder,
  SmsLoginParams
} from '../types';
import { MerchantStatus, MerchantAuditStatus } from '../types';

// 初始化时尝试读取商家信息
// 创建一个函数获取初始 token
// 从 sessionStorage 和 localStorage 中获取token并同步存储
// 这样即使页面刷新也能保持登录状态
function getInitialToken() {
  // 先从SessionStorage获取，因为这个最新鲜
  const sessionToken = sessionStorage.getItem('merchant_access_token');
  
  if (sessionToken) {
    // 确保 localStorage 也保持同步
    localStorage.setItem('merchant_access_token', sessionToken);
    return sessionToken;
  }
  
  // 如果 session 中没有，尝试从 localStorage 获取
  const localToken = localStorage.getItem('merchant_access_token');
  
  if (localToken) {
    // 同步到 sessionStorage
    sessionStorage.setItem('merchant_access_token', localToken);
    return localToken;
  }
  
  return '';
}

// 尝试获取初始商家信息
function getInitialMerchantInfo() {
  const infoStr = sessionStorage.getItem('merchant_info');
  if (infoStr) {
    try {
      return JSON.parse(infoStr);
    } catch (e) {
      console.error('解析商家信息失败:', e);
    }
  }
  return null;
}

/**
 * 商家信息store
 */
export const useMerchantStore = defineStore('merchant', {
  state: () => {
    return {
      merchantInfo: getInitialMerchantInfo() as Merchant | null,
      statistics: null as MerchantStatistics | null,
      token: getInitialToken(),
      // 添加前端路径数据支持
      frontendPaths: [] as any[],
      // token自动刷新相关状态
      tokenRefreshTimer: null as ReturnType<typeof setTimeout> | null,
      tokenExpiryTime: 0, // token过期时间戳
      loading: {
        login: false,
        info: false,
        statistics: false,
        products: false,
        orders: false,
        paths: false,  // 添加前端路径加载状态
        sms: false,    // 短信验证码相关操作状态
        tokenRefresh: false // token刷新状态
      },
      error: '' // 错误信息字段
    };
  },
  
  getters: {
    // 是否已登录
    isLoggedIn: (state) => !!state.token,
    // 商家名称
    merchantName: (state) => state.merchantInfo?.name || '',
    // 商家ID
    merchantId: (state) => state.merchantInfo?.id,
    
    // 商家审核状态（0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定）
    merchantAuditStatus: (state) => state.merchantInfo?.audit_status,
    
    // 兼容旧版 - 商家状态（即原来的merchantStatus）
    merchantStatus: (state) => {
      // 直接根据审核状态映射到旧版状态枚举
      if (state.merchantInfo?.audit_status !== undefined) {
        // 将新的审核状态映射为旧版状态类型
        switch (state.merchantInfo.audit_status) {
          case MerchantAuditStatus.APPROVED:
            return MerchantStatus.APPROVED; // 已审核通过
          case MerchantAuditStatus.PENDING:
            return MerchantStatus.PENDING;  // 待审核
          case MerchantAuditStatus.REJECTED:
            return MerchantStatus.REJECTED; // 已拒绝
          default:
            return MerchantStatus.SUSPENDED; // 其他状态（已禁用或已锁定）统一返回暂停
        }
      }
      return undefined;
    },
    
    // 是否已通过审核
    isApproved: (state) => state.merchantInfo?.audit_status === MerchantAuditStatus.APPROVED,
    
    // 商家经营状态（1-营业中，0-暂停营业）
    operationStatus: (state) => state.merchantInfo?.operationStatus,
    
    // 是否营业中
    isOperating: (state) => state.merchantInfo?.operationStatus === 1,
    
    // 经营时段
    businessHours: (state) => state.merchantInfo?.businessHours || [],
    
    // 商家LOGO
    logo: (state) => state.merchantInfo?.logo || '/images/default-shop.png',
    
    // 商家地理位置信息
    longitude: (state) => state.merchantInfo?.longitude,
    latitude: (state) => state.merchantInfo?.latitude,
    address: (state) => state.merchantInfo?.address || '',
    
    // token过期状态
    isTokenExpiring: (state) => {
      if (!state.tokenExpiryTime) return false;
      const now = Date.now();
      const timeLeft = state.tokenExpiryTime - now;
      // 如果剩余时间少于5分钟，认为即将过期
      return timeLeft < 5 * 60 * 1000;
    },
    
    // 今日订单数
    todayOrders: (state) => state.statistics?.todayOrders || 0,
    // 今日销售额
    todaySales: (state) => state.statistics?.todaySales || 0,
    // 待处理订单数
    pendingOrders: (state) => state.statistics?.pendingOrders || 0,
    // 待发货订单数
    pendingShipment: (state) => state.statistics?.pendingShipment || 0,
    // 待退款订单数
    pendingRefund: (state) => state.statistics?.pendingRefund || 0,
    // 库存不足商品数
    lowStockProducts: (state) => state.statistics?.lowStockProducts || 0,
  },
  
  actions: {
    /**
     * 获取前端路径数据
     * @param forceRefresh 是否强制从服务器刷新数据
     * @returns 前端路径数据
     */
    async fetchFrontendPaths(forceRefresh = false) {
      this.loading.paths = true;
      this.error = '';
      
      try {
        let localPaths = null;
        
        // 如果不是强制刷新，先尝试从localforage获取数据
        if (!forceRefresh) {
          localPaths = await localforage.getItem('merchant_frontend_paths');
          
          // 检查本地存储的数据是否有效
          if (localPaths && Array.isArray(localPaths) && localPaths.length > 0) {
            console.log('从localforage获取前端路径数据', localPaths);
            // 更新store中的数据
            this.frontendPaths = [...localPaths];
            return [...localPaths];
          }
        }
        
        // localforage中没有数据或需要强制刷新，调用远程API
        console.log('从远程API获取前端路径数据');
        const response: any = await getFrontendPaths();
        console.log('fetchFrontendPaths响应', response);
        
        // 确保响应是数组
        if (Array.isArray(response)) {
          // 处理为可序列化的纯数据对象
          const serializablePaths = response.map((module: any) => {
            return {
              module: module.module,
              paths: Array.isArray(module.paths) ? module.paths.map((path: any) => ({
                path: path.path,
                title: path.title,
                count: path.count || 0,
                config_key: path.config_key || '',
                config_type: path.config_type || '',
                group: path.group || '',
                icon: path.icon || '',
                id: path.id || '',
                version_hash: path.version_hash || ''
                // 可以添加其他必要的简单属性
              })) : []
            };
          });
          
          // 验证数据的有效性
          const isValid = serializablePaths.every((module: any) => 
            module && typeof module.module === 'string' && Array.isArray(module.paths) &&
            module.paths.every((path: any) => path && typeof path.path === 'string' && typeof path.title === 'string')
          );
          
          console.log('前端路径数据:', serializablePaths);
          if (!isValid) {
            console.error('从API获取的前端路径数据格式无效');
            throw new Error('前端路径数据格式无效');
          }
          
          // 更新store中的数据
          this.frontendPaths = [...serializablePaths];
          
          // 存储到localforage
          try {
            await localforage.setItem('merchant_frontend_paths', serializablePaths);
            console.log('前端路径数据已存储到localforage');
          } catch (storageError) {
            console.error('保存前端路径数据到localforage失败:', storageError);
            // 即使保存失败，也继续返回数据
          }
          
          console.log('frontendPaths成功更新为', this.frontendPaths);
          return [...serializablePaths];
        } else {
          console.error('前端路径返回格式错误', response);
          this.frontendPaths = [];
          return [];
        }
      } catch (err: any) {
        console.error('获取前端路径失败', err);
        this.error = err.message || '获取前端路径失败';
        return [];
      } finally {
        this.loading.paths = false;
      }
    },
    
    /**
     * 清除前端路径数据缓存
     */
    async clearFrontendPathsCache() {
      try {
        await localforage.removeItem('merchant_frontend_paths');
        this.frontendPaths = [];
        console.log('前端路径数据缓存已清除');
        return true;
      } catch (error) {
        console.error('清除前端路径数据缓存出错:', error);
        return false;
      }
    },

    /**
     * 商家登录
     * @param loginParams 登录参数
     * @param remember 是否记住登录状态
     */
    async login(loginParams: MerchantLoginParams, remember = true) {
      this.loading.login = true;
      this.error = '';
      try {
        // 生成设备信息
        const deviceInfo = this.generateDeviceInfo();
        const loginData = {
          ...loginParams,
          device_info: deviceInfo
        };
        
        // 调用登录接口
        console.log('调用登录接口', loginData);
        const response = await login(loginData);
        
        let tokenInfo;
        let merchantData;
        
        if ('token_info' in response) {
          // 新版API响应格式
          tokenInfo = response.token_info;
          merchantData = response.merchant;
        } else if ('token' in response && 'merchantInfo' in response) {
          // 兼容旧版API响应格式
          tokenInfo = {
            access_token: response.token,
            refresh_token: '', // 旧版API没有refresh_token
            expires_in: 3600 * 24 * 7, // 默认7天过期
            token_type: 'Bearer'
          };
          merchantData = response.merchantInfo;
        } else {
          // 其他不支持的响应格式
          console.error('不支持的API响应格式:', response);
          throw new Error('登录失败，服务器响应格式不正确');
        }
        
        // 存储token和商家信息
        await this.setTokens(tokenInfo, remember);
        await this.setMerchantInfo(merchantData);
        
        // 处理设备相关信息
        if ('device_id' in response && response.device_id) {
          localStorage.setItem('merchant_device_id', response.device_id);
        }
        
        // 保存当前设备信息到本地存储
        localStorage.setItem('merchant_current_device_info', JSON.stringify(deviceInfo));
        
        console.log('登录成功', this.token);
        
        // 获取前端路径数据
        await this.fetchFrontendPaths();
        
        return {
          success: true,
          data: response,
          message: '登录成功'
        };
      } catch (error: any) {
        this.error = error.message || '登录失败，请重试';
        console.error('商家登录失败:', error);
        throw error;
      } finally {
        this.loading.login = false;
      }
    },
    
    /**
     * 发送短信验证码
     * @param mobile 手机号
     * @returns 发送结果，包含过期时间
     */
    async sendVerificationCode(mobile: string) {
      this.loading.sms = true;
      this.error = '';
      
      try {
        // 调用发送验证码API
        console.log('发送验证码到手机:', mobile);
        const response = await sendSmsCode(mobile);
        
        // 响应拦截器已经处理了响应结构，直接使用响应数据
        console.log('验证码发送成功', response);
        
        // 返回包含过期时间的对象，默认60秒
        return { expire_time: 60 };
      } catch (error: any) {
        this.error = error.message || '验证码发送失败，请稍后再试';
        console.error('发送验证码失败:', error);
        throw error;
      } finally {
        this.loading.sms = false;
      }
    },
    
    /**
     * 短信验证码登录
     * @param smsParams 短信登录参数
     * @param remember 是否记住登录状态
     * @returns 登录结果
     */
    async smsLogin(smsParams: SmsLoginParams, remember = true) {
      this.loading.login = true;
      this.error = '';
      
      try {
        // 生成设备信息
        const deviceInfo = this.generateDeviceInfo();
        const smsData = {
          ...smsParams,
          device_info: deviceInfo
        };
        
        // 调用短信登录接口
        console.log('调用短信登录接口', smsData);
        const response = await apiSmsLogin(smsData);
        
        let tokenInfo;
        let merchantData;
        
        if ('token_info' in response) {
          // 新版API响应格式
          tokenInfo = response.token_info;
          merchantData = response.merchant;
        } else if ('token' in response && 'merchantInfo' in response) {
          // 兼容旧版API响应格式
          tokenInfo = {
            access_token: response.token,
            refresh_token: '', // 旧版API没有refresh_token
            expires_in: 3600 * 24 * 7, // 默认7天过期
            token_type: 'Bearer'
          };
          merchantData = response.merchantInfo;
        } else {
          // 其他不支持的响应格式
          console.error('不支持的API响应格式:', response);
          throw new Error('登录失败，服务器响应格式不正确');
        }
        
        // 存储token和商家信息
        await this.setTokens(tokenInfo, remember);
        await this.setMerchantInfo(merchantData);
        
        // 处理设备相关信息
        if ('device_id' in response && response.device_id) {
          localStorage.setItem('merchant_device_id', response.device_id);
        }
        
        // 保存当前设备信息到本地存储
        localStorage.setItem('merchant_current_device_info', JSON.stringify(deviceInfo));
        
        console.log('短信登录成功', this.token);
        
        // 获取前端路径数据
        await this.fetchFrontendPaths();
        
        return {
          success: true,
          data: response,
          message: '登录成功'
        };
      } catch (error: any) {
        this.error = error.message || '登录失败，请检查手机号和验证码';
        console.error('短信登录失败:', error);
        throw error;
      } finally {
        this.loading.login = false;
      }
    },
    
    /**
     * 获取商家信息
     */
    async fetchMerchantInfo() {
      this.loading.info = true;
      try {
        const data: any = await getMerchantInfo();
        
        // 数据字段命名兼容处理
        const normalizedData = { ...data };
        
        // 处理蛇形命名(snake_case)转驼峰命名(camelCase)
        if (normalizedData.audit_status !== undefined && normalizedData.audit_status === undefined) {
          normalizedData.audit_status = normalizedData.audit_status;
          console.log('字段转换(fetchMerchantInfo): audit_status -> audit_status:', normalizedData.audit_status);
        }
        
        // 处理其他可能的蛇形命名字段...
        if (normalizedData.active_status !== undefined && normalizedData.activeStatus === undefined) {
          normalizedData.activeStatus = normalizedData.active_status;
        }
        
        // 设置到state并使用setMerchantInfo进行处理
        await this.setMerchantInfo(normalizedData);
        
        return normalizedData;
      } catch (error) {
        return Promise.reject(error);
      } finally {
        this.loading.info = false;
      }
    },
    
    /**
     * 更新商家信息
     * @param data 商家信息
     */
    async updateInfo(data: Partial<Merchant>) {
      try {
        await updateMerchantInfo(data as any);
        // 更新成功后重新获取商家信息
        return await this.fetchMerchantInfo();
      } catch (error) {
        return Promise.reject(error);
      }
    },
    
    /**
     * 获取商家统计数据
     */
    async fetchStatistics() {
      this.loading.statistics = true;
      try {
        console.log('获取商家统计数据 from merchantStore');
        const data: any = await getMerchantStats();
        this.statistics = data;
        return data;
      } catch (error) {
        return Promise.reject(error);
      } finally {
        this.loading.statistics = false;
      }
    },
    
    /**
     * 商家退出登录
     */
    async logout() {
      try {
        // 获取并解析本地存储的设备信息
        const deviceInfoStr = localStorage.getItem('merchant_current_device_info');
        let deviceInfo = null;
        if (deviceInfoStr) {
          try {
            deviceInfo = JSON.parse(deviceInfoStr);
          } catch (e) {
            console.error('解析设备信息失败:', e);
          }
        }
        
        // 调用登出API，传递设备信息
        if (deviceInfo && deviceInfo.device_id) {
          const { logout: apiLogout } = await import('../api/auth');
          await apiLogout(deviceInfo.device_id);
        } else {
          console.warn('未找到设备ID，跳过服务器登出');
        }
      } catch (error) {
        console.error('登出API调用失败:', error);
        // 即使API调用失败，也要清除本地数据
      }
      
      // 清除定时器
      this.clearTokenRefreshTimer();
      
      this.token = '';
      this.merchantInfo = null;
      this.statistics = null;
      this.tokenExpiryTime = 0;
      
      // 清除所有存储的token信息
      localStorage.removeItem('merchant_token');
      localStorage.removeItem('merchant_access_token');
      localStorage.removeItem('merchant_device_id');
      localStorage.removeItem('merchant_current_device_info');
      sessionStorage.removeItem('merchant_access_token');
      sessionStorage.removeItem('merchant_refresh_token');
      sessionStorage.removeItem('merchant_token_type');
      sessionStorage.removeItem('merchant_token_expiry');
      sessionStorage.removeItem('merchant_info');
      
      // 清除localforage中的数据
      localforage.removeItem('merchant_access_token');
      localforage.removeItem('merchant_refresh_token');
      localforage.removeItem('merchant_token_type');
      localforage.removeItem('merchant_token_expiry');
      localforage.removeItem('merchant_info');
      localforage.removeItem('merchant_remember');
    },

    /**
     * 生成设备信息 - 使用统一的设备信息生成工具
     * @returns 设备信息对象
     */
    generateDeviceInfo() {
      return generateDeviceInfo();
    },

    /**
     * 设置tokens到状态和存储中
     * @param tokenInfo token信息对象
     * @param remember 是否记住登录状态
     */
    async setTokens(tokenInfo: TokenInfo, remember = true) {
      console.log('设置商家token信息:', tokenInfo);
      
      // 设置access_token到state
      this.token = tokenInfo.access_token;
      
      // 计算过期时间 (当前时间 + 过期秒数)
      const expiryTime = Date.now() + (tokenInfo.expires_in * 1000);
      this.tokenExpiryTime = expiryTime;
      
      // 始终保存到 sessionStorage 以保证当前会话可用
      sessionStorage.setItem('merchant_access_token', tokenInfo.access_token);
      sessionStorage.setItem('merchant_refresh_token', tokenInfo.refresh_token);
      sessionStorage.setItem('merchant_token_type', tokenInfo.token_type || 'Bearer');
      sessionStorage.setItem('merchant_token_expiry', expiryTime.toString());
      
      // 如果选择记住登录状态，则保存到本地存储
      if (remember) {
        // 存储到 localStorage 简单快速恢复状态
        localStorage.setItem('merchant_access_token', tokenInfo.access_token);
        localStorage.setItem('merchant_token', tokenInfo.access_token); // 兼容旧代码
        
        // 存储到 localforage 作为持久化存储
        await localforage.setItem('merchant_access_token', tokenInfo.access_token);
        await localforage.setItem('merchant_refresh_token', tokenInfo.refresh_token);
        await localforage.setItem('merchant_token_type', tokenInfo.token_type || 'Bearer');
        await localforage.setItem('merchant_token_expiry', expiryTime.toString());
        await localforage.setItem('merchant_remember', true);
        
        console.log('商家登录状态已保存到本地存储');
      }
      
      // 启动token自动刷新定时器
      this.startTokenRefreshTimer(tokenInfo.expires_in);

      // Token设置成功后，重新连接WebSocket
      try {
        console.log('🔄 [MerchantStore] Token设置成功，尝试重新连接WebSocket');
        const { useChatStore } = await import('@/modules/chat/stores/chat');
        const chatStore = useChatStore();

        // 只有在当前页面是商家页面时才重连WebSocket
        if (window.location.pathname.startsWith('/merchant')) {
          await chatStore.reconnectAfterTokenRefresh('merchant', this.merchantInfo?.id);
          console.log('✅ [MerchantStore] Token设置后WebSocket重连成功');
        }
      } catch (wsError) {
        console.warn('⚠️ [MerchantStore] Token设置后WebSocket重连失败，但不影响token设置:', wsError);
      }
    },
    
    /**
     * 保存商家信息
     * @param merchantData 商家信息数据
     */
    async setMerchantInfo(merchantData: any) {
      // 数据字段命名兼容处理
      const normalizedData = { ...merchantData };
      
      // 处理蛇形命名(snake_case)转驼峰命名(camelCase)
      if (normalizedData.audit_status !== undefined && normalizedData.audit_status === undefined) {
        normalizedData.audit_status = normalizedData.audit_status;
        console.log('字段转换: audit_status -> audit_status:', normalizedData.audit_status);
      }
      
      // 处理其他可能的蛇形命名字段...
      if (normalizedData.active_status !== undefined && normalizedData.activeStatus === undefined) {
        normalizedData.activeStatus = normalizedData.active_status;
      }
      
      // 处理经营状态字段
      if (normalizedData.operation_status !== undefined && normalizedData.operationStatus === undefined) {
        normalizedData.operationStatus = normalizedData.operation_status;
        console.log('字段转换: operation_status -> operationStatus:', normalizedData.operationStatus);
      }
      
      // 处理经营时段字段
      if (normalizedData.business_hours !== undefined && normalizedData.businessHours === undefined) {
        normalizedData.businessHours = normalizedData.business_hours;
        console.log('字段转换: business_hours -> businessHours:', normalizedData.businessHours);
      }
      
      // 处理经纬度字段 - 确保数值类型
      if (normalizedData.longitude !== undefined) {
        normalizedData.longitude = Number(normalizedData.longitude);
      }
      if (normalizedData.latitude !== undefined) {
        normalizedData.latitude = Number(normalizedData.latitude);
      }
      
      // 设置到state
      this.merchantInfo = normalizedData as Merchant;
      
      // 保存到sessionStorage便于页面刷新后恢复
      sessionStorage.setItem('merchant_info', JSON.stringify(normalizedData));
      
      // 尝试保存到localforage作为持久化存储
      try {
        // 创建一个可序列化的数据副本，移除不可序列化的属性
        const serializableData = this.createSerializableData(normalizedData);
        await localforage.setItem('merchant_info', serializableData);
      } catch (err) {
        console.error('存储商家信息到localforage失败:', err);
      }
    },
    
    /**
     * 创建可序列化的数据副本
     * 移除函数、循环引用等不可序列化的属性
     * @param data 原始数据
     * @returns 可序列化的数据副本
     */
    createSerializableData(data: any): any {
      if (data === null || data === undefined) {
        return data;
      }
      
      // 处理基本类型
      if (typeof data !== 'object') {
        return data;
      }
      
      // 处理日期对象
      if (data instanceof Date) {
        return data.toISOString();
      }
      
      // 处理数组
      if (Array.isArray(data)) {
        return data.map(item => this.createSerializableData(item));
      }
      
      // 处理普通对象
      const result: any = {};
      for (const key in data) {
        if (data.hasOwnProperty(key)) {
          const value = data[key];
          
          // 跳过函数和undefined值
          if (typeof value === 'function' || value === undefined) {
            continue;
          }
          
          // 跳过循环引用（简单检测）
          if (typeof value === 'object' && value !== null) {
            try {
              // 尝试JSON序列化来检测循环引用
              JSON.stringify(value);
              result[key] = this.createSerializableData(value);
            } catch (err) {
              // 如果序列化失败，跳过这个属性
              console.warn(`跳过不可序列化的属性: ${key}`, err);
              continue;
            }
          } else {
            result[key] = value;
          }
        }
      }
      
      return result;
    },
    
    /**
     * 确保获取最新的token状态
     * 在页面刷新或重新打开时调用，恢复登录状态
     * @returns 返回true表示恢复了token，false表示没有可用token
     */
    async retoken() {
      // 先从SessionStorage获取token，这个最新鲜
      const sessionToken = sessionStorage.getItem('merchant_access_token');
      const sessionExpiry = sessionStorage.getItem('merchant_token_expiry');
      
      if (sessionToken && sessionExpiry) {
        this.token = sessionToken;
        this.tokenExpiryTime = parseInt(sessionExpiry);
        
        // 检查token是否已过期
        const now = Date.now();
        if (this.tokenExpiryTime > now) {
          // token未过期，计算剩余时间并启动定时器
          const remainingSeconds = Math.floor((this.tokenExpiryTime - now) / 1000);
          this.startTokenRefreshTimer(remainingSeconds);
          return true;
        } else {
          // token已过期，尝试自动刷新
          console.log('Token已过期，尝试自动刷新');
          await this.autoRefreshToken();
          return !!this.token;
        }
      }
      
      // 如果会话token不存在，尝试从localforage获取
      const localToken = await localforage.getItem('merchant_access_token');
      const localExpiry = await localforage.getItem('merchant_token_expiry');
      
      if (localToken && localExpiry) {
        const expiryTime = parseInt(String(localExpiry));
        const now = Date.now();
        
        if (expiryTime > now) {
          // token未过期，恢复到session并启动定时器
          sessionStorage.setItem('merchant_access_token', String(localToken));
          sessionStorage.setItem('merchant_token_expiry', String(localExpiry));
          this.token = String(localToken);
          this.tokenExpiryTime = expiryTime;
          
          const remainingSeconds = Math.floor((expiryTime - now) / 1000);
          this.startTokenRefreshTimer(remainingSeconds);
          return true;
        } else {
          // token已过期，尝试自动刷新
          console.log('本地Token已过期，尝试自动刷新');
          await this.autoRefreshToken();
          return !!this.token;
        }
      }
      
      return false;
    },
    
    /**
     * 切换商家经营状态
     * @returns 切换后的状态
     */
    /**
     * 切换商家经营状态
     * @returns 返回更新后的状态
     */
    async toggleOperationStatus() {
      try {
        const currentStatus = this.merchantInfo?.operationStatus || 0;
        const newStatus = currentStatus === 1 ? 0 : 1;
        
        let response: OperationStatusResponse;
        if (newStatus === 1) {
          response = await setOperating() as OperationStatusResponse;
        } else {
          response = await setResting() as OperationStatusResponse;
        }
        
        // 更新本地状态
        if (this.merchantInfo) {
          this.merchantInfo.operationStatus = response.operation_status;
          // 同步更新到 sessionStorage
          sessionStorage.setItem('merchant_info', JSON.stringify(this.merchantInfo));
        }
        
        return response.operation_status;
      } catch (error) {
        console.error('切换经营状态失败:', error);
        throw error;
      }
    },
    
    /**
     * 使用长期token登录
     * @returns 登录是否成功
     */
    async loginByLongTermTokenAction() {
      this.loading.login = true;
      this.error = '';
      
      // 当前是否记住登录
      const remember = await localforage.getItem('merchant_remember') || false;
      
      try {
        console.log('尝试使用长期token登录');
        
        // 先尝试恢复可用token
        const tokenExists = await this.retoken();
        if (tokenExists) {
          console.log('已恢复token状态，尝试获取商家信息');
        }
        
        // 获取长期token
        const longTermToken = await getLongTermToken();
        console.log('商家长期token:', longTermToken);
        
        if (!longTermToken) {
          this.error = '未找到长期token，请重新登录';
          return false;
        }
        
        // 调用刷新token接口
        const response = await refreshToken({ refresh_token: longTermToken });
        console.log('商家刷新token响应:', response);
        
        if (response) {
          // 保存token信息
          const tokenInfo = response as TokenInfo;
          await this.setTokens(tokenInfo, Boolean(remember));
          
          // 获取最新的商家信息
          await nextTick();
          const merchantData = await this.fetchMerchantInfo();
          
          // 保存商家信息
          if (merchantData) {
            await this.setMerchantInfo(merchantData);
            
            // 加载前端路径数据
            await this.fetchFrontendPaths(false);
          }
          
          return true;
        } else {
          throw new Error('无效的token_info格式');
        }
      } catch (err: any) {
        this.error = err.message || '使用长期token登录失败，请重新登录';
        console.error('商家长期token登录失败:', err);
        return false;
      } finally {
        this.loading.login = false;
      }
    },

    /**
     * 启动token自动刷新定时器
     * @param expiresIn token过期时间（秒）
     */
    startTokenRefreshTimer(expiresIn: number) {
      // 清除现有定时器
      this.clearTokenRefreshTimer();
      
      // 在token过期前5分钟开始刷新，最少提前1分钟
      const refreshBeforeExpiry = Math.max(5 * 60, Math.min(expiresIn / 4, 30 * 60)); // 5分钟到30分钟之间
      const refreshDelay = (expiresIn - refreshBeforeExpiry) * 1000;
      
      console.log(`Token将在${expiresIn}秒后过期，将在${refreshDelay / 1000}秒后自动刷新`);
      
      if (refreshDelay > 0) {
        this.tokenRefreshTimer = setTimeout(() => {
          this.autoRefreshToken();
        }, refreshDelay);
      } else {
        // 如果token即将过期，立即刷新
        console.log('Token即将过期，立即刷新');
        this.autoRefreshToken();
      }
    },

    /**
     * 清除token刷新定时器
     */
    clearTokenRefreshTimer() {
      if (this.tokenRefreshTimer) {
        clearTimeout(this.tokenRefreshTimer);
        this.tokenRefreshTimer = null;
        console.log('Token刷新定时器已清除');
      }
    },

    /**
     * 自动刷新token
     */
    async autoRefreshToken() {
      if (this.loading.tokenRefresh) {
        console.log('Token刷新正在进行中，跳过本次刷新');
        return;
      }

      this.loading.tokenRefresh = true;
      console.log('开始自动刷新token');

      try {
        // 获取refresh_token
        const refreshTokenValue = sessionStorage.getItem('merchant_refresh_token') || 
                                 await localforage.getItem('merchant_refresh_token');

        if (!refreshTokenValue) {
          console.error('未找到refresh_token，无法自动刷新');
          // 如果没有refresh_token，尝试使用长期token登录
          const loginSuccess = await this.loginByLongTermTokenAction();
          if (!loginSuccess) {
            console.error('自动登录失败，需要用户重新登录');
            this.logout();
          }
          return;
        }

        // 调用刷新token接口
        const response = await refreshToken({ refresh_token: String(refreshTokenValue) });
        
        if (response && response.access_token) {
          console.log('Token自动刷新成功');
          
          // 获取当前的记住登录状态
          const remember = await localforage.getItem('merchant_remember') || false;
          
          // 更新token信息
          await this.setTokens(response as TokenInfo, Boolean(remember));

          console.log('Token已自动更新，新的过期时间:', new Date(this.tokenExpiryTime));

          // Token刷新成功后，重新连接WebSocket
          try {
            console.log('🔄 [MerchantStore] Token刷新成功，尝试重新连接WebSocket');
            const { useChatStore } = await import('@/modules/chat/stores/chat');
            const chatStore = useChatStore();

            // 只有在当前页面是商家页面时才重连WebSocket
            if (window.location.pathname.startsWith('/merchant')) {
              await chatStore.reconnectAfterTokenRefresh('merchant', this.merchantInfo?.id);
              console.log('✅ [MerchantStore] WebSocket重连成功');
            }
          } catch (wsError) {
            console.warn('⚠️ [MerchantStore] WebSocket重连失败，但不影响token刷新:', wsError);
          }
        } else {
          throw new Error('刷新token响应格式无效');
        }
      } catch (error: any) {
        console.error('自动刷新token失败:', error);
        
        // 如果刷新失败，尝试使用长期token登录
        try {
          const loginSuccess = await this.loginByLongTermTokenAction();
          if (!loginSuccess) {
            console.error('长期token登录也失败，用户需要重新登录');
            this.logout();
          }
        } catch (longTermError) {
          console.error('长期token登录失败:', longTermError);
          this.logout();
        }
      } finally {
        this.loading.tokenRefresh = false;
      }
    },

    /**
     * 手动刷新token
     * @returns 刷新是否成功
     */
    async manualRefreshToken(): Promise<boolean> {
      try {
        await this.autoRefreshToken();
        return !!this.token;
      } catch (error) {
        console.error('手动刷新token失败:', error);
        return false;
      }
    },


  }
});

/**
 * 商家商品store
 */
export const useMerchantProductStore = defineStore('merchantProduct', {
  state: () => ({
    products: [] as MerchantProduct[],
    currentProduct: null as MerchantProduct | null,
    totalCount: 0,
    loading: {
      value: false
    },
    saving: false
  }),
  
  actions: {
    /**
     * 获取商品列表
     * @param params 查询参数
     */
    async fetchProducts(params: any) {
      this.loading.value = true;
      try {
        const data: any = await getProductList(params);
        this.products = data.list;
        this.totalCount = data.total;
        return data;
      } catch (error) {
        return Promise.reject(error);
      } finally {
        this.loading.value = false;
      }
    },
    
    /**
     * 设置当前商品
     * @param product 商品信息
     */
    setCurrentProduct(product: MerchantProduct | null) {
      this.currentProduct = product;
    },
    
    /**
     * 清空商品列表
     */
    clearProducts() {
      this.products = [];
      this.totalCount = 0;
    }
  }
});

/**
 * 商家订单store
 */
export const useMerchantOrderStore = defineStore('merchantOrder', {
  state: () => ({
    orders: [] as MerchantOrder[],
    currentOrder: null as MerchantOrder | null,
    totalCount: 0,
    loading: {
      value: false
    },
    processing: false
  }),
  
  actions: {
    /**
     * 获取订单列表
     * @param params 查询参数
     */
    async fetchOrders(params: any) {
      this.loading.value = true;
      try {
        const data: any = await getOrderList(params);
        this.orders = data.list;
        this.totalCount = data.total;
        return data;
      } catch (error) {
        return Promise.reject(error);
      } finally {
        this.loading.value = false;
      }
    },
    
    /**
     * 设置当前订单
     * @param order 订单信息
     */
    setCurrentOrder(order: MerchantOrder | null) {
      this.currentOrder = order;
    },
    
    /**
     * 清空订单列表
     */
    clearOrders() {
      this.orders = [];
      this.totalCount = 0;
    }
  }
});