# 用户模块 (User Module)

## 功能概述

用户模块提供O_Mall商城的用户端功能，包括用户注册、登录、个人中心、订单管理、地址管理等基本功能。该模块为普通用户提供完整的账户管理和使用体验。

## 主要功能

- **用户认证**：提供账号密码登录和短信验证码登录两种方式
- **用户注册**：新用户注册流程，包含验证码验证和隐私政策同意
- **个人中心**：用户资料查看与编辑，包括头像上传等功能
- **订单管理**：查看、筛选不同状态的订单，支持订单的取消、支付、退款等操作
- **地址管理**：收货地址的增删改查，设置默认地址
- **账户设置**：密码修改、安全设置、通知管理等

## 目录结构

```
/src/modules/user/
  ├── api/         - API 接口定义
  ├── components/  - 用户模块专用组件
  ├── constants/   - 常量定义
  ├── router/      - 路由配置
  ├── service/     - 业务逻辑服务
  ├── stores/      - 状态管理
  ├── types/       - 类型定义
  └── views/       - 视图页面
```

## 路由配置

用户模块的主要路由包括：

- `/user/login` - 用户登录页面
- `/user/register` - 用户注册页面
- `/user/home` - 用户首页
- `/user/profile` - 个人中心
- `/user/orders` - 订单管理
- `/user/addresses` - 地址管理
- `/user/settings` - 账户设置

## 状态管理

用户模块使用Pinia进行状态管理，主要Store包括：

- `userStore` - 管理用户登录状态、个人信息等

## API模块

用户模块的API分为以下几类：

- `auth.ts` - 认证相关API（登录、注册、登出等）
- `profile.ts` - 个人信息相关API
- `address.ts` - 地址管理相关API
- `order.ts` - 订单管理相关API

## 使用技术

- Vue 3 组合式API
- TypeScript
- Element Plus UI组件库
- Pinia 状态管理
- Vue Router

## 开发指南

### 添加新页面

1. 在 `/views` 目录创建新的视图组件
2. 在 `/router/index.ts` 中添加对应路由配置
3. 如有必要，在 `/api` 目录添加相关API接口
4. 如有必要，在 `/types` 目录添加相关类型定义

### 添加新功能

1. 遵循模块化设计原则，将业务逻辑封装在 `/service` 目录
2. 共享状态应放在 `/stores` 目录中的Store管理
3. 常量定义应放在 `/constants` 目录
4. 组件应遵循Vue 3的组合式API风格编写

## 注意事项

- 所有API调用应处理错误情况，并提供友好的用户反馈
- 表单验证应在客户端和服务器端同时进行
- 用户敏感信息应妥善处理，确保安全
- 布局和样式应保持与商城整体风格一致
