/**
 * 用户模块路由配置
 * 提供静态路由配置和动态路由生成功能
 */
import UserLayout from '@/layouts/UserLayout.vue';
import type { RouteRecordRaw } from 'vue-router';

// 定义前端路径数据的接口
interface PathItem {
  path: string;
  title: string;
  count: number;
  config_key: string;
  config_type: string;
  group: string;
  icon: string;
  id: string;
  version_hash: string;
  [key: string]: any;
}

export interface ModuleData {
  module: string;
  paths: PathItem[];
}

// Runner Module Additions Start
// 跑腿员状态枚举
export enum RunnerStatus {
  // 未申请
  NOT_APPLIED = 'not_applied',
  // 审核中
  PENDING = 'pending',
  // 审核通过
  APPROVED = 'approved',
  // 审核拒绝
  REJECTED = 'rejected',
  // 已禁用
  DISABLED = 'disabled'
}

// 跑腿员工作状态枚举
export enum RunnerWorkStatus {
  // 离线
  OFFLINE = 'offline',
  // 在线
  ONLINE = 'online',
  // 忙碌
  BUSY = 'busy',
  // 休息
  REST = 'rest'
}

// 订单状态枚举 (Runner Context)
export enum OrderStatusRunner {
  // 待接单
  PENDING = 'pending',
  // 已接单
  ACCEPTED = 'accepted',
  // 取餐中
  PICKING_UP = 'picking_up',
  // 配送中
  DELIVERING = 'delivering',
  // 已完成
  COMPLETED = 'completed',
  // 已取消
  CANCELLED = 'cancelled'
}

// 收入类型枚举
export enum IncomeType {
  // 配送收入
  DELIVERY = 'delivery',
  // 奖励收入
  BONUS = 'bonus',
  // 补贴收入
  SUBSIDY = 'subsidy',
  // 扣款
  DEDUCTION = 'deduction'
}

// 提现状态枚举
export enum WithdrawStatus {
  // 待处理
  PENDING = 'pending',
  // 处理中
  PROCESSING = 'processing',
  // 已到账
  SUCCESS = 'success',
  // 失败
  FAILED = 'failed'
}

/**
 * 跑腿员工具函数
 */
export const runnerUtils = {
  /**
   * 获取跑腿员状态文本
   */
  getStatusText(status: RunnerStatus): string {
    switch (status) {
      case RunnerStatus.NOT_APPLIED:
        return '未申请';
      case RunnerStatus.PENDING:
        return '审核中';
      case RunnerStatus.APPROVED:
        return '审核通过';
      case RunnerStatus.REJECTED:
        return '审核拒绝';
      case RunnerStatus.DISABLED:
        return '已禁用';
      default:
        return '未知状态';
    }
  },

  /**
   * 获取工作状态文本
   */
  getWorkStatusText(status: RunnerWorkStatus): string {
    switch (status) {
      case RunnerWorkStatus.OFFLINE:
        return '离线';
      case RunnerWorkStatus.ONLINE:
        return '在线';
      case RunnerWorkStatus.BUSY:
        return '忙碌';
      case RunnerWorkStatus.REST:
        return '休息';
      default:
        return '未知状态';
    }
  },

  /**
   * 获取订单状态文本 (Runner Context)
   */
  getOrderStatusText(status: OrderStatusRunner): string {
    switch (status) {
      case OrderStatusRunner.PENDING:
        return '待接单';
      case OrderStatusRunner.ACCEPTED:
        return '已接单';
      case OrderStatusRunner.PICKING_UP:
        return '取餐中';
      case OrderStatusRunner.DELIVERING:
        return '配送中';
      case OrderStatusRunner.COMPLETED:
        return '已完成';
      case OrderStatusRunner.CANCELLED:
        return '已取消';
      default:
        return '未知状态';
    }
  },

  /**
   * 获取收入类型文本
   */
  getIncomeTypeText(type: IncomeType): string {
    switch (type) {
      case IncomeType.DELIVERY:
        return '配送收入';
      case IncomeType.BONUS:
        return '奖励收入';
      case IncomeType.SUBSIDY:
        return '补贴收入';
      case IncomeType.DEDUCTION:
        return '扣款';
      default:
        return '其他';
    }
  },

  /**
   * 获取提现状态文本
   */
  getWithdrawStatusText(status: WithdrawStatus): string {
    switch (status) {
      case WithdrawStatus.PENDING:
        return '待处理';
      case WithdrawStatus.PROCESSING:
        return '处理中';
      case WithdrawStatus.SUCCESS:
        return '已到账';
      case WithdrawStatus.FAILED:
        return '失败';
      default:
        return '未知状态';
    }
  },

  /**
   * 格式化距离
   */
  formatDistance(distance: number): string {
    if (distance < 1000) {
      return `${distance}m`;
    }
    return `${(distance / 1000).toFixed(1)}km`;
  },

  /**
   * 格式化时间
   */
  formatDuration(minutes: number): string {
    if (minutes < 60) {
      return `${minutes}分钟`;
    }
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}小时${mins}分钟`;
  },

  /**
   * 计算两点间距离（简化版）
   */
  calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371; // 地球半径（公里）
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }
};

/**
 * 跑腿员菜单配置
 */
export const runnerMenus = [
  {
    title: '跑腿员中心',
    icon: 'Bicycle', // Element Plus Icon name or component
    children: [
      {
        title: '注册申请',
        path: '/user/runner/register',
        icon: 'UserFilled'
      },
      {
        title: '申请状态',
        path: '/user/runner/status',
        icon: 'DocumentChecked'
      },
      {
        title: '工作状态',
        path: '/user/runner/work-status',
        icon: 'SwitchButton',
        requiresRunner: true
      },
      {
        title: '订单管理',
        path: '/user/runner/orders',
        icon: 'List',
        requiresRunner: true
      },
      {
        title: '收入管理',
        path: '/user/runner/income',
        icon: 'Money',
        requiresRunner: true
      },
      {
        title: '跑腿员设置',
        path: '/user/runner/settings',
        icon: 'Setting',
        requiresRunner: true
      }
    ]
  }
];
// Runner Module Additions End



// 提前导入并缓存组件，避免懒加载问题
// 这将确保组件在路由初始化时就已经开始加载
const DynamicConfigPagePromise = import('@/modules/admin/views/dynamicGridConfig/DynamicGridConfigPage.vue');

// 强制预加载组件，确保刷新页面时组件已就绪
DynamicConfigPagePromise.then(() => console.log('动态配置页面组件预加载完成'))
  .catch(err => console.error('动态配置页面组件预加载失败:', err));
  
// 使用一个函数返回已经开始加载的Promise
export const getDynamicConfigComponent = () => DynamicConfigPagePromise;

// 静态路由配置
const staticRoutes: RouteRecordRaw[] = [
  // 用户登录
  {
    path: '/user/login',
    name: 'UserLogin',
    component: () => import('../views/Login.vue'),
    meta: { title: '用户登录', requiresAuth: false }
  },
  // 用户注册
  {
    path: '/user/register',
    name: 'UserRegister',
    component: () => import('@/modules/user/views/PhoneRegister.vue'),
    meta: { title: '手机号注册', requiresAuth: false }
  },
  // 用户主布局
  {
    path: '/user',
    name: 'UserMain',
    component: UserLayout,
    redirect: '/user/home',
    meta: { requiresAuth: true },
    children: [
      // 用户首页
      {
        path: 'home',
        name: 'UserHome',
        component: () => import('../views/Home.vue'),
        meta: { title: '用户首页', requiresAuth: true, icon: 'House' }
      },
      
      // 个人中心
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('../views/Profile.vue'),
        meta: { title: '个人中心', requiresAuth: true, icon: 'User' }
      },
      
      // 我的订单
      {
        path: 'orders',
        name: 'UserOrders',
        component: () => import('../views/Orders.vue'),
        meta: { title: '我的订单', requiresAuth: true, icon: 'List' }
      },
      // 外卖商家
      {
        path: '/user/takeout',
        name: 'UserTakeout',
        component: () => import('../views/TakeoutMerchants.vue'),
        meta: { title: '外卖商家', requiresAuth: true, icon: 'Takeout' }
      },
      // 外卖商家详情
      {
        path: '/user/takeout/:id',
        name: 'UserTakeoutDetail',
        component: () => import('../views/takeout/TakeoutMerchantDetail.vue'),
        meta: { title: '外卖商家详情', requiresAuth: true, icon: 'TakeoutBox' },
        props: true
      },
      
      // 外卖购物车
      {
        path: '/user/takeout/cart',
        name: 'UserTakeoutCart',
        component: () => import('../views/takeout/TakeoutCart.vue'),
        meta: { title: '外卖购物车', requiresAuth: true, icon: 'ShoppingCart' }
      },
      
      // 外卖订单详情
      {
        path: '/user/takeout/order/:id',
        name: 'TakeoutOrderDetail',
        component: () => import('../views/takeout/TakeoutOrderDetail.vue'),
        meta: { title: '外卖订单详情', requiresAuth: true, icon: 'Document' },
        props: true
      },
      // 外卖订单支付
      {
        path: '/user/takeout/payment/:orderId',
        name: 'TakeoutPaymentPage',
        component: () => import('../views/takeout/TakeoutPaymentPage.vue'),
        meta: { title: '外卖订单支付', requiresAuth: true, icon: 'Wallet' }, // 使用 Wallet 图标示例
        props: true
      },
      
      // 优惠券中心
      {
        path: '/user/coupons',
        name: 'UserCoupons',
        component: () => import('../views/CouponCenter.vue'),
        meta: { title: '优惠券中心', requiresAuth: true, icon: 'Ticket' }
      },
      
      // 收货地址
      {
        path: '/user/addresses',
        name: 'UserAddresses',
        component: () => import('../views/Addresses.vue'),
        meta: { title: '收货地址', requiresAuth: true, icon: 'Location' }
      },
      
      // 账户设置
      {
        path: 'settings',
        name: 'UserSettings',
        component: () => import('../views/Settings.vue'),
        meta: { title: '账户设置', requiresAuth: true, icon: 'Setting' }
      },
      
      // 动态配置页面 - 根据路径参数渲染不同配置
      {
        path: 'page/:configId',
        name: 'UserDynamicConfigPage',
        component: getDynamicConfigComponent, // 使用预加载的组件
        meta: {
          title: '动态配置页面',
          requiresAuth: true,
          icon: 'DocumentText', // 示例图标
          dynamic: true
        },
        props: true
      },
      // Runner Routes Start
      {
        path: 'runner/register', // Relative to /user path
        name: 'RunnerRegister',
        component: () => import('../views/runner/RunnerRegister.vue'), // Adjusted path
        meta: {
          title: '跑腿员注册',
          requiresAuth: true
        }
      },
      {
        path: 'runner/status',
        name: 'RunnerStatus',
        component: () => import('../views/runner/RunnerStatus.vue'),
        meta: {
          title: '申请状态',
          requiresAuth: true
        }
      },
      {
        path: 'runner/work-status',
        name: 'RunnerWorkStatus',
        component: () => import('../views/runner/RunnerWorkStatus.vue'),
        meta: {
          title: '工作状态',
          requiresAuth: true,
          requiresRunner: true
        }
      },
      {
        path: 'runner/orders',
        name: 'RunnerOrders',
        component: () => import('../views/runner/RunnerOrders.vue'),
        meta: {
          title: '订单管理',
          requiresAuth: true,
          requiresRunner: true
        }
      },
      {
        path: 'runner/income',
        name: 'RunnerIncome',
        component: () => import('../views/runner/RunnerIncome.vue'),
        meta: {
          title: '收入管理',
          requiresAuth: true,
          requiresRunner: true
        }
      },
      {
        path: 'runner/settings',
        name: 'RunnerSettings',
        component: () => import('../views/runner/RunnerSettings.vue'),
        meta: {
          title: '跑腿员设置',
          requiresAuth: true,
          requiresRunner: true
        }
      },
      // Runner Routes End
      
      // 邀请好友页面
      {
        path: 'referral/invite',
        name: 'UserReferralInvite',
        component: () => import('../views/referral/Invite.vue'),
        meta: {
          title: '邀请好友',
          requiresAuth: true,
          icon: 'Share' // 使用Element Plus的Share图标
        }
      }
    ]
  }
];

// 缓存已生成的动态路由配置
// 通过导出变量，确保可以从外部访问和修改缓存状态
export let cachedDynamicRoutes: RouteRecordRaw[] | null = null;

/**
 * 根据前端路径数据生成动态路由配置
 * @param frontendPaths 前端路径数据
 * @returns 动态生成的路由配置
 */
export function generateDynamicRoutes(frontendPaths: ModuleData[]): RouteRecordRaw[] {
  try {
    // 每次都清除缓存，避免无限循环
    cachedDynamicRoutes = null;
    console.log('强制清除路由缓存，重新生成动态路由');
    
    console.log('生成动态路由配置，前端路径数据:', frontendPaths);
    
    // 验证前端路径数据
    if (!frontendPaths || !Array.isArray(frontendPaths) || frontendPaths.length === 0) {
      console.warn('前端路径数据无效，使用静态路由');
      return staticRoutes;
    }
    
    // 记录新添加的路由路径
    const newRoutes: string[] = [];
    
    // 创建用户主路由的子路由副本，以便追加动态路由
    const userMainRouteIndex = staticRoutes.findIndex(route => route.path === '/user');
    if (userMainRouteIndex === -1) {
      console.warn('找不到用户主路由配置，无法添加动态子路由');
      return staticRoutes;
    }
    
    // 复制用户主路由的子路由数组
    const userMainRouteChildren = [...(staticRoutes[userMainRouteIndex].children || [])];
    
    // 处理前端路径数据中的每个模块
    frontendPaths.forEach(moduleData => {
      if (!moduleData || !moduleData.module || !Array.isArray(moduleData.paths)) {
        console.warn('模块数据格式无效，跳过:', moduleData);
        return;
      }
      
      // 只处理用户模块的路径
      if (moduleData.module !== 'user') {
        return;
      }
      
      // 处理模块中的每个路径
      moduleData.paths.forEach(pathItem => {
        // 构建路由路径 (去掉开头的斜杠)
        const routePath = pathItem.path.startsWith('/') ? pathItem.path.substring(1) : pathItem.path;
        
        // 如果路由已存在于静态配置中，则跳过
        const existingRouteIndex = userMainRouteChildren.findIndex(
          route => route.path === routePath
        );
        if (existingRouteIndex !== -1) {
          console.log(`路由已存在于静态配置中，跳过: ${routePath}`);
          return;
        }
        
        // 记录新路由路径
        newRoutes.push(routePath);
        
        // 创建动态路由配置
        const dynamicRoute: RouteRecordRaw = {
          path: routePath,
          name: `User${pathItem.config_key}`,
          component: getDynamicConfigComponent,
          meta: {
            title: pathItem.title,
            requiresAuth: true,
            icon: pathItem.icon,
            dynamic: true,
            configId: pathItem.config_key
          },
          props: _route => ({ configId: pathItem.config_key })
        };
        
        // 添加动态路由到子路由数组
        userMainRouteChildren.push(dynamicRoute);
        
        console.log(`添加动态路由: ${routePath}`, dynamicRoute);
      });
    });
    
    // 更新用户主路由的子路由
    const updatedStaticRoutes = [...staticRoutes];
    updatedStaticRoutes[userMainRouteIndex] = {
      ...updatedStaticRoutes[userMainRouteIndex],
      children: userMainRouteChildren
    };
    
    // 缓存动态路由配置
    cachedDynamicRoutes = updatedStaticRoutes;
    
    console.log('成功生成动态路由配置，新添加的路由:', newRoutes);
    return updatedStaticRoutes;
    
  } catch (error) {
    console.error('生成动态路由时出错:', error);
    return staticRoutes;
  }
}

/**
 * 清除动态路由缓存
 * 在路由更新或需要重新生成时调用
 */
export function clearDynamicRoutesCache() {
  console.log('清除动态路由缓存');
  cachedDynamicRoutes = null;
}

// 默认导出静态路由配置
export default staticRoutes;
