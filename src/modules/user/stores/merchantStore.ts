/**
 * 商家模块状态管理
 * 作者: AI Assistant
 * 日期: 2025-01-27
 * 版本: 1.0.0
 * 描述: 管理商家信息列表，支持商家信息的增删改查和单独更新
 */
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Merchant } from '../types';
import { takeoutService } from '../service/takeoutService';
import { getMerchantsPromotionsCoupons } from '../api/takeout';
import { ElMessage } from 'element-plus';

/**
 * 商家促销信息接口
 */
export interface MerchantPromotion {
  id: string | number;
  merchantId: string | number;
  name: string;
  type: number;
  description?: string;
  rules?: string;
  startTime?: string;
  endTime?: string;
  isActive: boolean;
}

/**
 * 扩展的商家信息接口，包含促销信息
 */
export interface ExtendedMerchant extends Merchant {
  // 地理位置信息
  latitude?: number;
  longitude?: number;
  
  // 促销信息
  promotions?: MerchantPromotion[];
  promotion_info?: string;
  
  // 运营状态
  operation_status?: number; // 1: 营业中, 0: 休息中
  
  // 销售数据
  month_sales?: number;
  
  // 分类信息
  category_name?: string;
  
  // 最小订单金额
  min_order_amount?: number;
}

export const useMerchantStore = defineStore('merchant', () => {
  // 状态
  const merchants = ref<ExtendedMerchant[]>([]);
  const currentMerchant = ref<ExtendedMerchant | null>(null);
  const loading = ref(false);
  
  // 计算属性
  const merchantsMap = computed(() => {
    const map = new Map<string | number, ExtendedMerchant>();
    merchants.value.forEach(merchant => {
      map.set(merchant.id, merchant);
    });
    return map;
  });
  
  const activeMerchants = computed(() => {
    return merchants.value.filter(merchant => merchant.operation_status === 1);
  });
  
  const nearbyMerchants = computed(() => {
    return merchants.value.filter(merchant => 
      merchant.latitude && merchant.longitude
    ).sort((a, b) => {
      // 如果有距离信息，按距离排序
      if (a.distance !== undefined && b.distance !== undefined) {
        return a.distance - b.distance;
      }
      return 0;
    });
  });
  
  /**
   * 获取商家列表
   * @param params 查询参数
   * @returns 商家列表数据
   */
  async function loadMerchants(params?: {
    page?: number;
    pageSize?: number;
    categoryId?: string | number;
    keyword?: string;
    sort?: string;
    latitude?: number;
    longitude?: number;
  }) {
    loading.value = true;
    try {
      const response: any = await takeoutService.merchant.getMerchants(params);
      console.log('获取商家列表结果:', response);
      
      const merchantList = response?.list || [];
      
      // 更新商家列表，保留现有商家的额外信息
      merchantList.forEach((newMerchant: any) => {
        const existingMerchant = merchantsMap.value.get(newMerchant.id);
        if (existingMerchant) {
          // 合并现有信息和新信息
          Object.assign(existingMerchant, newMerchant);
        } else {
          // 添加新商家
          merchants.value.push(newMerchant as ExtendedMerchant);
        }
      });
      
      return {
        data: merchantList,
        total: response?.total || 0
      };
    } catch (error) {
      console.error('获取商家列表失败:', error);
      ElMessage.error('获取商家列表失败，请稍后重试');
      return {
        data: [],
        total: 0
      };
    } finally {
      loading.value = false;
    }
  }
  
  /**
   * 获取商家详情
   * @param merchantId 商家ID
   * @returns 商家详情信息
   */
  async function loadMerchantDetail(merchantId: string | number) {
    try {
      const response: any = await takeoutService.merchant.getMerchants({ id: merchantId });
      console.log('获取商家详情结果:', response);
      
      if (response && typeof response === 'object') {
        const merchantData = response as ExtendedMerchant;
        
        // 更新当前商家
        currentMerchant.value = merchantData;
        
        // 更新商家列表中的对应商家
        updateMerchantInList(merchantId, merchantData);
        
        return merchantData;
      }
      
      return null;
    } catch (error) {
      console.error('获取商家详情失败:', error);
      ElMessage.error('获取商家详情失败，请稍后重试');
      return null;
    }
  }
  
  /**
   * 更新商家列表中的特定商家信息
   * @param merchantId 商家ID
   * @param updates 要更新的信息
   */
  function updateMerchantInList(merchantId: string | number, updates: Partial<ExtendedMerchant>) {
    console.log(`updateMerchantInList: 正在更新商家 ${merchantId}，更新内容:`, updates);
    console.log('当前merchants数组长度:', merchants.value.length);
    console.log('当前merchants数组:', merchants.value.map(m => ({ id: m.id, name: m.name })));
    
    // 确保ID类型一致性进行比较
    const index = merchants.value.findIndex(merchant => 
      merchant.id == merchantId || merchant.id === String(merchantId) || merchant.id === Number(merchantId)
    );
    console.log(`商家 ${merchantId} 在数组中的索引:`, index);
    console.log('ID类型比较:', {
      merchantId,
      merchantIdType: typeof merchantId,
      existingIds: merchants.value.map(m => ({ id: m.id, type: typeof m.id }))
    });
    
    if (index !== -1) {
      // 合并更新信息
      merchants.value[index] = { ...merchants.value[index], ...updates };
      console.log(`商家 ${merchantId} 信息已更新:`, merchants.value[index]);
      
      // 如果更新的是当前商家，也更新当前商家信息
      if (currentMerchant.value?.id === merchantId) {
        currentMerchant.value = { ...currentMerchant.value, ...updates };
      }
    } else {
      console.log(`商家 ${merchantId} 不存在于列表中，尝试创建新商家`);
      // 如果商家不存在于列表中，创建一个基础商家对象
      const newMerchant: ExtendedMerchant = {
         id: merchantId,
         name: `商家${merchantId}`,
         description: '',
         logo: '',
         cover: '',
         address: '',
         phone: '',
         operation_status: 1,
         deliveryFee: 0,
         min_order_amount: 0,
         rating: 0,
         ratingCount: 0,
         categories: [],
         minDeliveryAmount: 0,
         deliveryTime: '30-45分钟',
         isOpen: true,
         isPaused: false,
         createdAt: new Date().toISOString(),
         updatedAt: new Date().toISOString(),
         ...updates
       };
      merchants.value.push(newMerchant);
      console.log(`已添加新商家 ${merchantId}:`, newMerchant);
    }
  }
  
  /**
   * 更新商家地理位置信息
   * @param merchantId 商家ID
   * @param latitude 纬度
   * @param longitude 经度
   */
  function updateMerchantLocation(merchantId: string | number, latitude: number, longitude: number) {
    updateMerchantInList(merchantId, {
      latitude,
      longitude
    });
    console.log(`已更新商家 ${merchantId} 的位置信息: (${latitude}, ${longitude})`);
  }
  
  /**
   * 更新商家促销信息
   * @param merchantId 商家ID
   * @param promotions 促销信息列表
   * @param promotionInfo 促销描述信息
   */
  function updateMerchantPromotions(merchantId: string | number, promotions: MerchantPromotion[], promotionInfo?: string) {
    updateMerchantInList(merchantId, {
      promotions,
      promotion_info: promotionInfo
    });
    console.log(`已更新商家 ${merchantId} 的促销信息`);
  }
  
  /**
   * 加载多个商家的促销优惠信息
   * @param merchantIds 商家ID数组
   * @returns 促销信息加载结果
   */
  async function loadMerchantsPromotions(merchantIds: (string | number)[]) {
    if (!merchantIds || merchantIds.length === 0) {
      console.warn('商家ID数组为空，无法加载促销信息');
      return;
    }
    
    try {
      console.log('正在加载商家促销信息:', merchantIds);
      const response: any = await getMerchantsPromotionsCoupons({
        merchant_ids: merchantIds
      });
      
      console.log('获取商家促销信息结果:', response);
      
      // 处理返回的促销信息数据
      if (response && Array.isArray(response)) {
        // 实际返回的数据结构为数组，每个元素包含merchant_id、promotions、coupons等字段
        response.forEach((merchantData: any) => {
          const merchantId = merchantData.merchant_id;
          const promotions = merchantData.promotions || [];
          const coupons = merchantData.coupons || [];
          
          let allPromotions: MerchantPromotion[] = [];
          
          // 处理promotions数组
          if (Array.isArray(promotions) && promotions.length > 0) {
            const formattedPromotions: MerchantPromotion[] = promotions.map((promo: any) => ({
              id: promo.id,
              merchantId: merchantId,
              name: promo.name || promo.title,
              type: promo.type || 4,
              description: promo.description,
              rules: typeof promo.rules === 'string' ? promo.rules : JSON.stringify(promo.rules || {}),
              startTime: promo.start_time || promo.startTime,
              endTime: promo.end_time || promo.endTime,
              isActive: promo.is_active !== undefined ? promo.is_active : true
            }));
            allPromotions = [...allPromotions, ...formattedPromotions];
          }
          
          // 处理coupons数组，将优惠券转换为促销信息格式
          if (Array.isArray(coupons) && coupons.length > 0) {
            const formattedCoupons: MerchantPromotion[] = coupons.map((couponData: any) => {
              const coupon = couponData.coupon || couponData;
              return {
                id: `coupon_${coupon.id}`,
                merchantId: merchantId,
                name: coupon.name || '优惠券',
                type: 5, // 优惠券类型
                description: coupon.description || '',
                rules: JSON.stringify({
                  coupon: {
                    name: coupon.name,
                    type: coupon.type || 1,
                    amount: coupon.amount || coupon.discount_amount || 0,
                    min_order_amount: coupon.min_order_amount || 0,
                    per_user_limit: coupon.per_user_limit || 1,
                    valid_days: coupon.valid_days || 30
                  }
                }),
                startTime: coupon.start_time || new Date().toISOString(),
                endTime: coupon.end_time || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                isActive: couponData.status === 1 || couponData.status === '可用'
              };
            });
            allPromotions = [...allPromotions, ...formattedCoupons];
          }
          
          // 更新商家的促销信息
          updateMerchantPromotions(merchantId, allPromotions);
          if (allPromotions.length > 0) {
            console.log(`商家 ${merchantId} 促销信息已更新 (${promotions.length}个促销活动, ${coupons.length}个优惠券):`, allPromotions);
          } else {
            console.log(`商家 ${merchantId} 无促销信息`);
          }
        });
      }
      
      return response;
    } catch (error) {
      console.error('加载商家促销信息失败:', error);
      ElMessage.error('加载促销信息失败，请稍后重试');
      return null;
    }
  }
  
  /**
   * 更新商家运营状态
   * @param merchantId 商家ID
   * @param operationStatus 运营状态 (1: 营业中, 0: 休息中)
   */
  function updateMerchantOperationStatus(merchantId: string | number, operationStatus: number) {
    updateMerchantInList(merchantId, {
      operation_status: operationStatus
    });
    console.log(`已更新商家 ${merchantId} 的运营状态: ${operationStatus === 1 ? '营业中' : '休息中'}`);
  }
  
  /**
   * 更新商家销售数据
   * @param merchantId 商家ID
   * @param monthSales 月销量
   * @param rating 评分
   * @param ratingCount 评分数量
   */
  function updateMerchantSalesData(merchantId: string | number, data: {
    monthSales?: number;
    rating?: number;
    ratingCount?: number;
  }) {
    updateMerchantInList(merchantId, {
      month_sales: data.monthSales,
      rating: data.rating,
      ratingCount: data.ratingCount
    });
    console.log(`已更新商家 ${merchantId} 的销售数据`);
  }
  
  /**
   * 根据ID获取商家信息
   * @param merchantId 商家ID
   * @returns 商家信息或null
   */
  function getMerchantById(merchantId: string | number): ExtendedMerchant | null {
    console.log(`getMerchantById: 查找商家 ${merchantId}`);
    console.log('merchantsMap keys:', Array.from(merchantsMap.value.keys()));
    
    // 尝试直接获取
    let merchant = merchantsMap.value.get(merchantId);
    if (merchant) {
      console.log(`直接找到商家 ${merchantId}:`, merchant);
      return merchant;
    }
    
    // 尝试类型转换后获取
    merchant = merchantsMap.value.get(String(merchantId)) || merchantsMap.value.get(Number(merchantId));
    if (merchant) {
      console.log(`类型转换后找到商家 ${merchantId}:`, merchant);
      return merchant;
    }
    
    // 手动遍历查找（兜底方案）
    for (const [key, value] of merchantsMap.value.entries()) {
      if (key == merchantId || key === String(merchantId) || key === Number(merchantId)) {
        console.log(`手动遍历找到商家 ${merchantId}:`, value);
        return value;
      }
    }
    
    console.log(`未找到商家 ${merchantId}`);
    return null;
  }
  
  /**
   * 设置当前商家
   * @param merchant 商家信息
   */
  function setCurrentMerchant(merchant: ExtendedMerchant | null) {
    currentMerchant.value = merchant;
  }
  
  /**
   * 清空商家列表
   */
  function clearMerchants() {
    merchants.value = [];
    currentMerchant.value = null;
  }
  
  /**
   * 从列表中移除商家
   * @param merchantId 商家ID
   */
  function removeMerchant(merchantId: string | number) {
    const index = merchants.value.findIndex(merchant => merchant.id === merchantId);
    if (index !== -1) {
      merchants.value.splice(index, 1);
      
      // 如果移除的是当前商家，清空当前商家
      if (currentMerchant.value?.id === merchantId) {
        currentMerchant.value = null;
      }
    }
  }
  
  /**
   * 添加商家到列表中
   * @param merchant 商家信息
   */
  function addMerchant(merchant: ExtendedMerchant) {
    // 检查商家是否已存在
    const existingIndex = merchants.value.findIndex(m => m.id === merchant.id);
    
    if (existingIndex !== -1) {
      // 如果已存在，更新商家信息
      merchants.value[existingIndex] = merchant;
    } else {
      // 如果不存在，添加到列表
      merchants.value.push(merchant);
    }
  }
  
  /**
   * 获取商家信息（别名方法）
   * @param merchantId 商家ID
   * @returns 商家信息或null
   */
  function getMerchant(merchantId: string | number): ExtendedMerchant | null {
    return getMerchantById(merchantId);
  }
  
  return {
    // 状态
    merchants,
    currentMerchant,
    loading,
    
    // 计算属性
    merchantsMap,
    activeMerchants,
    nearbyMerchants,
    
    // 方法
    loadMerchants,
    loadMerchantDetail,
    loadMerchantsPromotions,
    updateMerchantInList,
    updateMerchantLocation,
    updateMerchantPromotions,
    updateMerchantOperationStatus,
    updateMerchantSalesData,
    getMerchantById,
    getMerchant,
    setCurrentMerchant,
    clearMerchants,
    removeMerchant,
    addMerchant
  };
});