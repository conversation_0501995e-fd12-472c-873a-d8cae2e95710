/**
 * 用户模块状态管理
 * 处理用户登录状态、用户信息和持久化存储
 */
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { UserInfo } from '../types';
import { UserStatus } from '../types';
import { login, logout, getUserInfo, loginBySms, refreshToken, getLongTermToken } from '@/modules/user/api/auth';
import type { UserLoginParams, SmsLoginParams, LoginResponse } from '../types';
import localforage from 'localforage';

// 命名空间常量，统一标识用户模块
// 对应request.ts中使用的名称
// 保证存储键名与请求模块的期望一致
const NAMESPACE = 'user';

// 导入统一的设备信息生成工具
import { generateDeviceInfo } from '@/utils/deviceInfo';

// 初始化时尝试从存储中获取token
function getInitialToken() {
  // 先从会话存储中获取，因为这个最新鲜
  const sessionToken = sessionStorage.getItem(`${NAMESPACE}_access_token`);
  
  if (sessionToken) {
    // 确保localStorage也同步
    localStorage.setItem(`${NAMESPACE}_access_token`, sessionToken);
    return sessionToken;
  }
  
  // 如果会话中没有，尝试从本地存储获取
  const localToken = localStorage.getItem(`${NAMESPACE}_access_token`);
  
  if (localToken) {
    // 同步到会话存储
    sessionStorage.setItem(`${NAMESPACE}_access_token`, localToken);
    return localToken;
  }
  
  // 兼容旧密钥名
  const oldToken = localStorage.getItem('user_token');
  if (oldToken) {
    sessionStorage.setItem(`${NAMESPACE}_access_token`, oldToken);
    localStorage.setItem(`${NAMESPACE}_access_token`, oldToken);
    return oldToken;
  }
  
  return null;
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string | null>(getInitialToken());
  const userInfo = ref<UserInfo | null>(null);
  const loginTime = ref<number | null>(null);
  const expiresIn = ref<number>(30 * 24 * 60 * 60 * 1000); // 默认30天过期时间
  
  // token自动刷新相关状态
  const tokenRefreshTimer = ref<ReturnType<typeof setTimeout> | null>(null);
  const tokenExpiryTime = ref<number>(0); // token过期时间戳
  const isTokenRefreshing = ref<boolean>(false); // token刷新状态
  const pageId = ref<string>(Math.random().toString(36).substr(2, 9)); // 页面唯一标识符
  const isPageVisible = ref<boolean>(!document.hidden); // 页面可见性状态
  
  // 地理位置相关状态
  const userLocation = ref<{ longitude: number; latitude: number } | null>(null);
  const locationError = ref<string | null>(null);
  const isGettingLocation = ref<boolean>(false);
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value);
  const isActive = computed(() => userInfo.value?.status === UserStatus.ACTIVE);
  const isFrozen = computed(() => userInfo.value?.status === UserStatus.FROZEN);
  const username = computed(() => userInfo.value?.username || '');
  const userId = computed(() => userInfo.value?.id || '');
  const nickname = computed(() => userInfo.value?.nickname || userInfo.value?.username || '');
  const avatar = computed(() => userInfo.value?.avatar || '/images/default_avatar.png');
  
  // 地理位置相关计算属性
  const hasLocation = computed(() => !!userLocation.value);
  const currentLongitude = computed(() => userLocation.value?.longitude || null);
  
  // 页面可见性监听器
  function setupVisibilityListener() {
    const handleVisibilityChange = () => {
      isPageVisible.value = !document.hidden;
      console.log(`[页面${pageId.value}] 页面可见性变化:`, isPageVisible.value ? '可见' : '隐藏');
      
      // 当页面变为可见时，检查是否需要接管token刷新
      if (isPageVisible.value && token.value) {
        checkAndTakeOverTokenRefresh();
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // 返回清理函数
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }
  
  /**
   * 检查并接管token刷新任务
   */
  function checkAndTakeOverTokenRefresh() {
    try {
      const timerInfoStr = localStorage.getItem(`${NAMESPACE}_timer_info`);
      if (!timerInfoStr) {
        return;
      }
      
      const timerInfo = JSON.parse(timerInfoStr);
      const now = Date.now();
      
      // 如果token还没过期，但刷新时间已过，立即刷新
      if (now > timerInfo.refreshTime && now < timerInfo.expiryTime) {
        console.log(`[页面${pageId.value}] 检测到错过的token刷新，立即执行`);
        autoRefreshToken();
      }
      // 如果token已过期，立即刷新
      else if (now >= timerInfo.expiryTime) {
        console.log(`[页面${pageId.value}] 检测到token已过期，立即刷新`);
        autoRefreshToken();
      }
      // 如果还没到刷新时间，但当前页面应该处理，重新设置定时器
      else if (shouldHandleTokenRefresh()) {
        const remainingTime = Math.max(0, timerInfo.refreshTime - now);
        if (remainingTime > 0) {
          console.log(`[页面${pageId.value}] 接管token刷新任务，${remainingTime / 1000}秒后刷新`);
          clearTokenRefreshTimer();
          tokenRefreshTimer.value = setTimeout(() => {
            if (shouldHandleTokenRefresh()) {
              autoRefreshToken();
            }
          }, remainingTime);
        }
      }
    } catch (error) {
      console.error('检查token刷新任务失败:', error);
    }
  }
  const currentLatitude = computed(() => userLocation.value?.latitude || null);
  
  // token过期状态检测
  const isTokenExpiring = computed(() => {
    if (!tokenExpiryTime.value) return false;
    const now = Date.now();
    const timeLeft = tokenExpiryTime.value - now;
    // 如果剩余时间少于5分钟，认为即将过期
    return timeLeft < 5 * 60 * 1000;
  });
  
  // 加载本地存储的用户信息
  async function loadUserFromStorage() {
    try {
      // 优先使用统一键名从localforage获取token
      let userToken = await localforage.getItem<string>('user_access_token');
      
      // 如果没有，尝试使用命名空间键名
      if (!userToken) {
        userToken = await localforage.getItem<string>(`${NAMESPACE}_access_token`);
      }
      
      // 如果还没有，尝试从localStorage获取（兼容旧数据）
      if (!userToken) {
        userToken = localStorage.getItem('user_access_token') || localStorage.getItem(`${NAMESPACE}_access_token`) || localStorage.getItem('user_token');
      }
      
      // 获取用户信息
      let userInfoData = await localforage.getItem<string>(`${NAMESPACE}_info`);
      if (!userInfoData) {
        const storedUserInfo = localStorage.getItem(`${NAMESPACE}_info`) || localStorage.getItem('user_info');
        userInfoData = storedUserInfo;
      }
      
      // 获取token过期时间
      let tokenExpiry = await localforage.getItem<string>(`${NAMESPACE}_token_expiry`);
      if (!tokenExpiry) {
        const storedLoginTime = localStorage.getItem(`${NAMESPACE}_login_time`) || localStorage.getItem('user_login_time');
        if (storedLoginTime) {
          // 计算过期时间
          const loginTimeValue = parseInt(storedLoginTime);
          tokenExpiry = (loginTimeValue + expiresIn.value).toString();
        }
      }
      
      // 设置到状态
      if (userToken) {
        token.value = userToken;
      }
      
      if (userInfoData && typeof userInfoData === 'string') {
        try {
          userInfo.value = JSON.parse(userInfoData);
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      } else if (userInfoData) {
        // 如果已经是对象形式
        userInfo.value = userInfoData as any;
      }
      
      // 检查token是否过期
      const now = Date.now();
      if (tokenExpiry) {
        const expireTime = parseInt(tokenExpiry);
        if (now >= expireTime) {
          console.log('Token已过期，清除登录状态');
          await clearUserData();
          return false;
        } else {
          // 设置登录时间和过期时间
          loginTime.value = expireTime - expiresIn.value;
          // 重要：设置tokenExpiryTime，这样计算属性和其他方法才能正确工作
          tokenExpiryTime.value = expireTime;
        }
      }
      
      return !!token.value;
    } catch (error) {
      console.error('加载用户数据出错:', error);
      return false;
    }
  }
  
  // 保存用户信息到本地存储
  async function saveUserToStorage() {
    if (token.value) {
      // 计算过期时间
      const expireTime = (loginTime.value || Date.now()) + expiresIn.value;
      
      // 更新token过期时间状态
      tokenExpiryTime.value = expireTime;
      
      // 先保存到会话存储，确保当前页面从SSR恢复时可用
      sessionStorage.setItem(`${NAMESPACE}_access_token`, token.value);
      sessionStorage.setItem(`${NAMESPACE}_token_expiry`, expireTime.toString());
      
      // 保存到localStorage以便新标签页快速恢复状态
      localStorage.setItem(`${NAMESPACE}_access_token`, token.value);
      localStorage.setItem(`${NAMESPACE}_token_expiry`, expireTime.toString());
      
      // 保存到统一键名（与retoken方法保持一致）
      sessionStorage.setItem('user_access_token', token.value);
      localStorage.setItem('user_access_token', token.value);
      
      // 兼容旧键名
      localStorage.setItem('user_token', token.value);
      
      // 保存到localforage作为持久化存储，为request.ts使用
      await localforage.setItem(`${NAMESPACE}_access_token`, token.value);
      await localforage.setItem('user_access_token', token.value);
      await localforage.setItem(`${NAMESPACE}_token_expiry`, expireTime.toString());
      
      console.log(`用户token已保存到所有存储，键名为${NAMESPACE}_access_token和user_access_token`);
    }
    
    if (userInfo.value) {
      // 创建一个纯对象，确保不包含Vue的响应式属性
      const userInfoData = JSON.parse(JSON.stringify(userInfo.value));
      const userInfoJson = JSON.stringify(userInfoData);
      
      try {
        // 存储到sessionStorage和localStorage
        sessionStorage.setItem(`${NAMESPACE}_info`, userInfoJson);
        localStorage.setItem(`${NAMESPACE}_info`, userInfoJson);
        
        // 存储到localforage，使用纯对象
        await localforage.setItem(`${NAMESPACE}_info`, userInfoData);
        
        // 兼容旧键名
        localStorage.setItem('user_info', userInfoJson);
      } catch (error) {
        console.error('保存用户信息到存储失败:', error);
      }
    }
    
    if (loginTime.value) {
      const loginTimeStr = loginTime.value.toString();
      sessionStorage.setItem(`${NAMESPACE}_login_time`, loginTimeStr);
      localStorage.setItem(`${NAMESPACE}_login_time`, loginTimeStr);
      await localforage.setItem(`${NAMESPACE}_login_time`, loginTimeStr);
      
      // 兼容旧键名
      localStorage.setItem('user_login_time', loginTimeStr);
    }
  }
  
  // 清除用户数据
  async function clearUserData() {
    // 清除token刷新定时器
    clearTokenRefreshTimer();
    
    // 清除内存中的状态
    token.value = null;
    userInfo.value = null;
    loginTime.value = null;
    tokenExpiryTime.value = 0;
    isTokenRefreshing.value = false;
    
    // 清除会话存储中的数据
    sessionStorage.removeItem(`${NAMESPACE}_access_token`);
    sessionStorage.removeItem(`${NAMESPACE}_refresh_token`);
    sessionStorage.removeItem(`${NAMESPACE}_token_expiry`);
    sessionStorage.removeItem(`${NAMESPACE}_info`);
    sessionStorage.removeItem(`${NAMESPACE}_login_time`);
    
    // 清除统一键名的数据
    sessionStorage.removeItem('user_access_token');
    sessionStorage.removeItem('user_refresh_token');
    
    // 清除localStorage中的数据
    localStorage.removeItem(`${NAMESPACE}_access_token`);
    localStorage.removeItem(`${NAMESPACE}_refresh_token`);
    localStorage.removeItem(`${NAMESPACE}_token_expiry`);
    localStorage.removeItem(`${NAMESPACE}_info`);
    localStorage.removeItem(`${NAMESPACE}_login_time`);
    
    // 清除统一键名的数据
    localStorage.removeItem('user_access_token');
    localStorage.removeItem('user_refresh_token');
    
    // 清除旧键名的数据（兼容旧代码）
    localStorage.removeItem('user_token');
    localStorage.removeItem('user_info');
    localStorage.removeItem('user_login_time');
    
    // 清除localforage中的数据
    await localforage.removeItem(`${NAMESPACE}_access_token`);
    await localforage.removeItem(`${NAMESPACE}_refresh_token`);
    await localforage.removeItem(`${NAMESPACE}_token_expiry`);
    await localforage.removeItem(`${NAMESPACE}_info`);
    await localforage.removeItem(`${NAMESPACE}_login_time`);
    
    // 清除统一键名的数据
    await localforage.removeItem('user_access_token');
    await localforage.removeItem('user_refresh_token');
    
    console.log(`清除用户数据完成，命名空间: ${NAMESPACE}`);
  }
  
  // 用户登录
  async function userLogin(loginParams: UserLoginParams): Promise<{ success: boolean; data?: any; message?: string }> {
    try {
      // 生成设备信息
      const deviceInfo = generateDeviceInfo();
      
      // 将设备信息添加到登录参数中
      const loginParamsWithDevice = {
        ...loginParams,
        device_info: deviceInfo
      };
      
      // 使用明确的类型声明
      const response: LoginResponse = await login(loginParamsWithDevice);
      console.log('userLogin response:', response);
      
      // 检查返回的token_info和access_token
      if (response?.token_info?.access_token) {
        // 设置token为access_token
        token.value = response.token_info.access_token;
        
        // 设置登录时间和过期时间
        loginTime.value = Date.now();
        
        // 如果API返回了expires_in，使用API的值（秒转毫秒）
        if (response.token_info.expires_in) {
          expiresIn.value = response.token_info.expires_in * 1000;
        } else {
          // 否则使用默认过期时间
          if (loginParams.rememberMe) {
            expiresIn.value = 30 * 24 * 60 * 60 * 1000; // 30天
          } else {
            expiresIn.value = 24 * 60 * 60 * 1000; // 1天
          }
        }
        
        // 如果API返回了refresh_token，存储它用于长期token刷新
        if (response.token_info.refresh_token) {
          await localforage.setItem('user_refresh_token', response.token_info.refresh_token);
          sessionStorage.setItem('user_refresh_token', response.token_info.refresh_token);
          // 标记为记住登录状态
          await localforage.setItem(`${NAMESPACE}_remember`, loginParams.rememberMe ? '1' : '0');
        }
        
        // 保存access_token到统一的存储位置
        await localforage.setItem('user_access_token', response.token_info.access_token);
        sessionStorage.setItem('user_access_token', response.token_info.access_token);
        
        // 如果API直接返回了用户信息，直接使用
        if (response.user) {
          userInfo.value = response.user;
        } else {
          // 否则尝试获取用户信息
          await fetchUserInfo();
        }
        
        // 保存设备信息到本地存储
        if (response.device_id) {
          localStorage.setItem('user_current_device_info', JSON.stringify({
            ...deviceInfo,
            device_id: response.device_id
          }));
        }
        
        // 保存到本地存储（会保存到sessionStorage、localStorage和localforage）
        await saveUserToStorage();
        
        // 启动token自动刷新定时器
        if (response.token_info.expires_in) {
          startTokenRefreshTimer(response.token_info.expires_in);
        }
        
        console.log(`用户登录成功，已保存token和信息到命名空间'${NAMESPACE}'`);
        
        // 返回包含设备信息的响应
        return { 
          success: true, 
          data: {
            ...response,
            device_id: response.device_id,
            is_new_device: response.is_new_device,
            risk_level: response.risk_level
          }
        };
      }
      
      return { success: false, message: '登录失败：无效的响应数据' };
    } catch (error: any) {
      console.error('用户登录失败:', error);
      return { success: false, message: error.message || '登录失败' };
    }
  }
  
  // 短信验证码登录
  async function userSmsLogin(smsParams: SmsLoginParams): Promise<{ success: boolean; data?: any; message?: string }> {
    try {
      // 生成设备信息
      const deviceInfo = generateDeviceInfo();
      
      // 将设备信息添加到登录参数中
      const smsParamsWithDevice = {
        ...smsParams,
        device_info: deviceInfo
      };
      
      // 使用明确的类型声明
      const response: LoginResponse = await loginBySms(smsParamsWithDevice);
      console.log('userSmsLogin response:', response);
      
      // 检查返回的token_info和access_token
      if (response?.token_info?.access_token) {
        // 设置token为access_token
        token.value = response.token_info.access_token;
        
        // 设置登录时间和过期时间
        loginTime.value = Date.now();
        
        // 如果API返回了expires_in，使用API的值（秒转毫秒）
        if (response.token_info.expires_in) {
          expiresIn.value = response.token_info.expires_in * 1000;
        } else {
          // 否则使用默认过期时间
          if (smsParams.rememberMe) {
            expiresIn.value = 30 * 24 * 60 * 60 * 1000; // 30天
          } else {
            expiresIn.value = 24 * 60 * 60 * 1000; // 1天
          }
        }
        
        // 如果API返回了refresh_token，存储它用于长期token刷新
        if (response.token_info.refresh_token) {
          await localforage.setItem('user_refresh_token', response.token_info.refresh_token);
          sessionStorage.setItem('user_refresh_token', response.token_info.refresh_token);
          // 标记为记住登录状态
          await localforage.setItem(`${NAMESPACE}_remember`, smsParams.rememberMe ? '1' : '0');
        }
        
        // 保存access_token到统一的存储位置
        await localforage.setItem('user_access_token', response.token_info.access_token);
        sessionStorage.setItem('user_access_token', response.token_info.access_token);
        
        // 如果API直接返回了用户信息，直接使用
        if (response.user) {
          userInfo.value = response.user;
        } else {
          // 否则尝试获取用户信息
          await fetchUserInfo();
        }
        
        // 保存设备信息到本地存储
        if (response.device_id) {
          localStorage.setItem('user_current_device_info', JSON.stringify({
            ...deviceInfo,
            device_id: response.device_id
          }));
        }
        
        // 保存到本地存储（会保存到sessionStorage、localStorage和localforage）
        await saveUserToStorage();
        
        // 启动token自动刷新定时器
        if (response.token_info.expires_in) {
          startTokenRefreshTimer(response.token_info.expires_in);
        }
        
        console.log(`短信登录成功，已保存token和信息到命名空间'${NAMESPACE}'`);
        
        // 返回包含设备信息的响应
        return { 
          success: true, 
          data: {
            ...response,
            device_id: response.device_id,
            is_new_device: response.is_new_device,
            risk_level: response.risk_level
          }
        };
      }
      
      return { success: false, message: '登录失败：无效的响应数据' };
    } catch (error: any) {
      console.error('短信登录失败:', error);
      return { success: false, message: error.message || '登录失败' };
    }
  }
  
  /**
   * 获取用户信息
   * @returns 用户信息或null
   */
  async function fetchUserInfo(): Promise<UserInfo | null> {
    try {
      if (!token.value) {
        console.log('获取用户信息失败: 无效的token');
        return null;
      }
      
      const response = await getUserInfo();
      console.log('fetchUserInfo API响应:', response);
      
      // 检查响应中的数据，适配不同的响应格式
      let userData = null;
      
      // 尝试不同的响应格式
      if (response?.data?.data) {
        // 格式1: { data: { data: userInfo } }
        userData = response.data.data;
      } else if (response?.data) {
        // 格式2: { data: userInfo }
        userData = response.data;
      } else if (response && typeof response === 'object' && 'id' in response) {
        // 格式3: 直接返回用户信息对象
        userData = response;
      }
      
      if (userData && typeof userData === 'object') {
        // 更新内存中的用户信息
        userInfo.value = userData;
        
        // 立即保存到所有存储中
        await saveUserToStorage();
        
        console.log(`获取用户信息成功，已保存到${NAMESPACE}命名空间`, userData);
        return userData;
      }
      
      console.log('获取用户信息失败: 服务器返回的数据无效', response);
      return null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }
  
  /**
   * 用户登出方法
   * 先获取必要信息，再调用API，最后清除本地数据
   * @returns Promise<boolean> 登出是否成功
   */
  async function userLogout(): Promise<boolean> {
    console.log('开始用户登出流程');
    try {
      // 先获取token和设备信息，避免清除数据后无法获取
      const lastToken = sessionStorage.getItem(`${NAMESPACE}_access_token`);
      const deviceInfo = JSON.parse(localStorage.getItem('user_current_device_info') || '{}');
      
      console.log('获取到的token:', lastToken ? '存在' : '不存在');
      console.log('获取到的设备信息:', deviceInfo);
      
      // 只有当前有token和设备ID时才调用后端登出接口
      if (lastToken && deviceInfo.device_id) {
        try {
          // 调用后端登出接口，传递设备ID
          await logout(deviceInfo.device_id);
          console.log('服务器端登出成功');
        } catch (e) {
          console.error('服务器端登出失败，但继续清除本地数据', e);
          // 即使服务器端登出失败，也要继续清除本地数据
        }
      } else {
        console.warn('缺少token或设备ID，跳过服务器端登出');
        console.warn('Token存在:', !!lastToken, 'DeviceId存在:', !!deviceInfo.device_id);
      }
      
      // 清除本地数据
      await clearUserData();
      
      // 清除设备信息
      localStorage.removeItem('user_current_device_info');
      
      console.log(`用户已登出，清除了${NAMESPACE}命名空间的所有数据`);
      return true;
    } catch (error) {
      console.error('登出过程发生错误:', error);
      
      // 即使发生错误，也要清除本地数据
      await clearUserData();
      localStorage.removeItem('user_current_device_info');
      
      return false;
    }
  }
  
  // 更新用户信息
  function updateUserInfo(newUserInfo: Partial<UserInfo>) {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...newUserInfo };
      saveUserToStorage();
    }
  }
  
/**
 * 恢复token状态
 * 在页面刷新或重新打开时调用，恢复登录状态
 * @returns 返回true表示恢复了token，false表示没有可用token
 */
const retoken = async (): Promise<boolean> => {
  // 优先从命名空间键名获取token（最新的存储方式）
  let sessionToken = sessionStorage.getItem(`${NAMESPACE}_access_token`);
  if (sessionToken) {
    token.value = sessionToken;
    // 同步到统一键名
    sessionStorage.setItem('user_access_token', sessionToken);
    return true;
  }
  
  // 兼容旧的键名
  sessionToken = sessionStorage.getItem('user_access_token');
  if (sessionToken) {
    token.value = sessionToken;
    // 同步到命名空间键名
    sessionStorage.setItem(`${NAMESPACE}_access_token`, sessionToken);
    return true;
  }
  
  // 如果会话token不存在，尝试从localforage获取（优先命名空间键名）
  let localToken = await localforage.getItem(`${NAMESPACE}_access_token`);
  if (localToken) {
    const tokenStr = String(localToken);
    sessionStorage.setItem(`${NAMESPACE}_access_token`, tokenStr);
    sessionStorage.setItem('user_access_token', tokenStr);
    token.value = tokenStr;
    return true;
  }
  
  // 兼容旧的键名
  localToken = await localforage.getItem('user_access_token');
  if (localToken) {
    const tokenStr = String(localToken);
    sessionStorage.setItem(`${NAMESPACE}_access_token`, tokenStr);
    sessionStorage.setItem('user_access_token', tokenStr);
    token.value = tokenStr;
    return true;
  }
  
  return false;
};

/**
 * 使用长期token登录
 * @returns 登录是否成功
 */
const loginByLongTermTokenAction = async (): Promise<boolean> => {
  try {
    console.log('尝试使用长期token登录');
    
    // 先尝试恢复可用token
    const tokenExists = await retoken();
    if (tokenExists) {
      console.log('已恢复token状态，尝试获取用户信息');
      // 如果token存在，先尝试获取用户信息
      const userInfoResult = await fetchUserInfo();
      if (userInfoResult) {
        console.log('使用现有token获取用户信息成功');
        return true;
      }
    }
    
    // 获取长期token
    const longTermToken = await getLongTermToken();
    console.log('用户长期token:', longTermToken);
    
    if (!longTermToken) {
      console.log('未找到长期token，请重新登录');
      return false;
    }
    
    // 调用刷新token接口
    const response : any = await refreshToken({ refresh_token: longTermToken });
    console.log('用户刷新token响应:', response);
    
    if (response && response.access_token) {
      console.log('刷新token成功');
      
      // 保存新的token信息
      token.value = response.access_token;
      
      // 设置登录时间
      loginTime.value = Date.now();
      
      // 设置过期时间（如果API返回了expires_in，使用API的值）
      if (response.expires_in) {
        expiresIn.value = response.expires_in * 1000; // 秒转毫秒
      } else {
        // 默认30天过期时间
        expiresIn.value = 30 * 24 * 60 * 60 * 1000;
      }
      
      // 保存到统一的存储位置（使用正确的键名）
      await localforage.setItem('user_access_token', response.access_token);
      sessionStorage.setItem('user_access_token', response.access_token);
      
      if (response.refresh_token) {
        await localforage.setItem('user_refresh_token', response.refresh_token);
        sessionStorage.setItem('user_refresh_token', response.refresh_token);
      }
      
      // 获取用户信息
      const userInfoResult = await fetchUserInfo();
      
      if (userInfoResult) {
        // 调用完整的saveUserToStorage方法，确保所有数据都正确保存
        await saveUserToStorage();
        
        // 启动token自动刷新定时器
        if (response.expires_in) {
          startTokenRefreshTimer(response.expires_in);
        }

        // 长期token登录成功后，重新连接WebSocket
        try {
          console.log('🔄 [UserStore] 长期token登录成功，尝试重新连接WebSocket');
          const { useChatStore } = await import('@/modules/chat/stores/chat');
          const chatStore = useChatStore();

          // 只有在当前页面是用户页面时才重连WebSocket
          if (window.location.pathname.startsWith('/user') || window.location.pathname === '/') {
            await chatStore.reconnectAfterTokenRefresh('user', Number(userInfo.value?.id));
            console.log('✅ [UserStore] 长期token登录后WebSocket重连成功');
          }
        } catch (wsError) {
          console.warn('⚠️ [UserStore] 长期token登录后WebSocket重连失败，但不影响登录:', wsError);
        }

        console.log(`使用长期token登录成功，已保存到命名空间'${NAMESPACE}'`);
        return true;
      } else {
        console.error('获取用户信息失败，登录流程中断');
        return false;
      }
    }
    
    console.log('使用长期token登录失败');
    return false;
  } catch (error) {
    console.error('使用长期token登录出错:', error);
    return false;
  }
};
  
  /**
   * 获取用户当前地理位置
   * 使用浏览器的navigator.geolocation API
   * @returns Promise<boolean> 是否成功获取位置
   */
  async function getCurrentLocation(): Promise<boolean> {
    if (isGettingLocation.value) {
      console.log('正在获取位置中，请稍候');
      return false;
    }
    
    if (!navigator.geolocation) {
      locationError.value = '浏览器不支持地理位置获取';
      console.error('浏览器不支持地理位置获取');
      return false;
    }
    
    isGettingLocation.value = true;
    locationError.value = null;
    
    return new Promise((resolve) => {
      const options = {
        enableHighAccuracy: true, // 启用高精度
        timeout: 10000, // 10秒超时
        maximumAge: 300000 // 5分钟内的缓存位置可用
      };
      
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            const { longitude, latitude } = position.coords;
            
            // 保存位置信息
            userLocation.value = { longitude, latitude };
            
            // 持久化存储位置信息
            await saveLocationToStorage();
            
            console.log(`获取位置成功: 经度${longitude}, 纬度${latitude}`);
            isGettingLocation.value = false;
            resolve(true);
          } catch (error) {
            console.error('保存位置信息失败:', error);
            isGettingLocation.value = false;
            resolve(false);
          }
        },
        (error) => {
          let errorMessage = '获取位置失败';
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = '用户拒绝了位置权限请求';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = '位置信息不可用';
              break;
            case error.TIMEOUT:
              errorMessage = '获取位置超时';
              break;
            default:
              errorMessage = '未知的位置获取错误';
              break;
          }
          
          locationError.value = errorMessage;
          console.error('获取位置失败:', errorMessage, error);
          isGettingLocation.value = false;
          resolve(false);
        },
        options
      );
    });
  }
  
  /**
   * 保存位置信息到本地存储
   */
  async function saveLocationToStorage() {
    if (userLocation.value) {
      // 创建纯净的数据对象，避免序列化问题
      const locationData = {
        longitude: userLocation.value.longitude,
        latitude: userLocation.value.latitude
      };
      const locationDataString = JSON.stringify(locationData);
      
      try {
        // 保存到各种存储中
        sessionStorage.setItem(`${NAMESPACE}_location`, locationDataString);
        localStorage.setItem(`${NAMESPACE}_location`, locationDataString);
        // 存储纯净的数据对象到localforage，避免DataCloneError
        await localforage.setItem(`${NAMESPACE}_location`, locationData);
        
        console.log('位置信息已保存到存储');
      } catch (error) {
        console.error('保存位置信息到存储失败:', error);
      }
    }
  }
  
  /**
   * 从本地存储加载位置信息
   */
  async function loadLocationFromStorage() {
    try {
      // 优先从localforage获取
      let locationData = await localforage.getItem<{ longitude: number; latitude: number }>(`${NAMESPACE}_location`);
      
      if (!locationData) {
        // 尝试从localStorage获取
        const storedLocation = localStorage.getItem(`${NAMESPACE}_location`) || sessionStorage.getItem(`${NAMESPACE}_location`);
        if (storedLocation) {
          locationData = JSON.parse(storedLocation);
        }
      }
      
      if (locationData && typeof locationData === 'object' && locationData.longitude && locationData.latitude) {
        userLocation.value = locationData;
        console.log('从存储中恢复位置信息:', locationData);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('从存储加载位置信息失败:', error);
      return false;
    }
  }
  
  /**
   * 清除位置信息
   */
  async function clearLocationData() {
    userLocation.value = null;
    locationError.value = null;
    
    // 清除存储中的位置信息
    sessionStorage.removeItem(`${NAMESPACE}_location`);
    localStorage.removeItem(`${NAMESPACE}_location`);
    await localforage.removeItem(`${NAMESPACE}_location`);
    
    console.log('位置信息已清除');
  }
  
  /**
   * 计算两点之间的距离（使用Haversine公式）
   * 适用于GCJ02坐标系
   * @param lat1 起点纬度
   * @param lon1 起点经度
   * @param lat2 终点纬度
   * @param lon2 终点经度
   * @returns 距离（公里）
   */
  function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // 地球半径（公里）
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return Number(distance.toFixed(3)); // 保留三位小数，更精确
  }
  
  /**
   * 计算商家距离当前位置的距离
   * @param merchantLat 商家纬度
   * @param merchantLon 商家经度
   * @returns 距离（公里），如果没有当前位置则返回null
   */
  function calculateMerchantDistance(merchantLat: number, merchantLon: number): number | null {
    if (!userLocation.value) {
      return null;
    }
    
    return calculateDistance(
      userLocation.value.latitude,
      userLocation.value.longitude,
      merchantLat,
      merchantLon
    );
  }
  
  /**
   * 启动token自动刷新定时器
   * @param expiresIn token过期时间（秒）
   */
  function startTokenRefreshTimer(expiresIn: number) {
    // 清除现有定时器
    clearTokenRefreshTimer();
    
    // 设置token过期时间戳
    tokenExpiryTime.value = Date.now() + (expiresIn * 1000);
    
    // 在token过期前5分钟开始刷新，最少提前1分钟
    const refreshBeforeExpiry = Math.max(5 * 60, Math.min(expiresIn / 4, 30 * 60)); // 5分钟到30分钟之间
    const refreshDelay = (expiresIn - refreshBeforeExpiry) * 1000;
    
    console.log(`[页面${pageId.value}] Token将在${expiresIn}秒后过期，将在${refreshDelay / 1000}秒后自动刷新`);
    
    // 将定时器信息存储到localStorage，用于多页面协调
    const timerInfo = {
      pageId: pageId.value,
      expiryTime: tokenExpiryTime.value,
      refreshTime: Date.now() + refreshDelay,
      timestamp: Date.now()
    };
    localStorage.setItem(`${NAMESPACE}_timer_info`, JSON.stringify(timerInfo));
    
    if (refreshDelay > 0) {
      tokenRefreshTimer.value = setTimeout(() => {
        // 检查是否仍然是负责刷新的页面（在定时器触发时检查，而不是设置时）
        if (shouldHandleTokenRefresh()) {
          autoRefreshToken();
        } else {
          console.log(`[页面${pageId.value}] 其他页面已处理token刷新，跳过`);
        }
      }, refreshDelay);
    } else {
      // 如果token即将过期，立即刷新（但仍需检查是否应该由当前页面处理）
      console.log(`[页面${pageId.value}] Token即将过期，检查是否需要立即刷新`);
      if (shouldHandleTokenRefresh()) {
        autoRefreshToken();
      }
    }
  }
  
  /**
   * 判断当前页面是否应该处理token刷新
   */
  function shouldHandleTokenRefresh(): boolean {
    try {
      const timerInfoStr = localStorage.getItem(`${NAMESPACE}_timer_info`);
      if (!timerInfoStr) {
        return true; // 没有定时器信息，当前页面处理
      }
      
      const timerInfo = JSON.parse(timerInfoStr);
      
      // 如果其他页面正在刷新token，不处理
      if (timerInfo.refreshing && timerInfo.refreshingPageId !== pageId.value) {
        // 检查刷新是否超时（超过2分钟认为刷新失败）
        const refreshTimeout = 2 * 60 * 1000;
        if (Date.now() - timerInfo.timestamp < refreshTimeout) {
          return false;
        }
        console.log(`[页面${pageId.value}] 检测到其他页面刷新超时，接管刷新任务`);
      }
      
      // 如果定时器信息过期（超过5分钟），当前页面处理
      if (Date.now() - timerInfo.timestamp > 5 * 60 * 1000) {
        return true;
      }
      
      // 如果是同一个页面设置的定时器，当前页面处理
      if (timerInfo.pageId === pageId.value) {
        return true;
      }
      
      // 如果其他页面的定时器已经过期，当前页面处理
      if (Date.now() > timerInfo.refreshTime) {
        return true;
      }
      
      // 如果页面不可见但是是唯一的活跃页面，仍然处理刷新
      if (!isPageVisible.value) {
        // 检查是否有其他可见页面在处理
        // 如果当前页面是设置定时器的页面，即使不可见也要处理
        if (timerInfo.pageId === pageId.value) {
          console.log(`[页面${pageId.value}] 页面不可见但仍需处理token刷新`);
          return true;
        }
        return false;
      }
      
      return false;
    } catch (error) {
      console.error('解析定时器信息失败:', error);
      return true; // 出错时默认当前页面处理
    }
  }
  
  /**
   * 清除token刷新定时器
   */
  function clearTokenRefreshTimer() {
    if (tokenRefreshTimer.value) {
      clearTimeout(tokenRefreshTimer.value);
      tokenRefreshTimer.value = null;
      console.log(`[页面${pageId.value}] Token刷新定时器已清除`);
    }
  }
  
  /**
   * 自动刷新token
   */
  async function autoRefreshToken() {
    if (isTokenRefreshing.value) {
      console.log(`[页面${pageId.value}] Token刷新正在进行中，跳过本次刷新`);
      return;
    }
    
    // 再次检查是否应该由当前页面处理刷新
    if (!shouldHandleTokenRefresh()) {
      console.log(`[页面${pageId.value}] 其他页面正在处理token刷新，跳过`);
      return;
    }
    
    isTokenRefreshing.value = true;
    console.log(`[页面${pageId.value}] 开始自动刷新token`);
    
    // 更新localStorage中的刷新状态
    try {
      const timerInfoStr = localStorage.getItem(`${NAMESPACE}_timer_info`);
      if (timerInfoStr) {
        const timerInfo = JSON.parse(timerInfoStr);
        timerInfo.refreshing = true;
        timerInfo.refreshingPageId = pageId.value;
        localStorage.setItem(`${NAMESPACE}_timer_info`, JSON.stringify(timerInfo));
      }
    } catch (error) {
      console.error('更新刷新状态失败:', error);
    }
    
    try {
      // 获取refresh_token
      const refreshTokenValue = sessionStorage.getItem(`${NAMESPACE}_refresh_token`) ||
                               await localforage.getItem(`${NAMESPACE}_refresh_token`);
      
      if (!refreshTokenValue) {
        console.error('未找到refresh_token，无法自动刷新');
        // 如果没有refresh_token，尝试使用长期token登录
        const loginSuccess = await loginByLongTermTokenAction();
        if (!loginSuccess) {
          console.error('自动登录失败，需要用户重新登录');
          await userLogout();
        }
        return;
      }
      
      // 调用刷新token接口
       const response = await refreshToken({ refresh_token: String(refreshTokenValue) }) as any;
       
       // 响应拦截器已经处理了响应格式，直接使用response作为token_info
       if (response?.access_token) {
         console.log('Token自动刷新成功');
         
         // 更新token信息
         token.value = response.access_token;
         loginTime.value = Date.now();
         
         // 如果API返回了expires_in，计算新的过期时间
         if (response.expires_in) {
           tokenExpiryTime.value = Date.now() + (response.expires_in * 1000);
         }
         
         // 如果API返回了新的refresh_token，更新它
         if (response.refresh_token) {
           await localforage.setItem(`${NAMESPACE}_refresh_token`, response.refresh_token);
           sessionStorage.setItem(`${NAMESPACE}_refresh_token`, response.refresh_token);
         }
         
         // 保存更新后的token信息
         await saveUserToStorage();
         
         // 启动新的刷新定时器
         if (response.expires_in) {
           startTokenRefreshTimer(response.expires_in);
         }

         console.log('Token已自动更新，新的过期时间:', new Date(tokenExpiryTime.value));

         // Token刷新成功后，重新连接WebSocket
         try {
           console.log('🔄 [UserStore] Token刷新成功，尝试重新连接WebSocket');
           const { useChatStore } = await import('@/modules/chat/stores/chat');
           const chatStore = useChatStore();

           // 只有在当前页面是用户页面时才重连WebSocket
           if (window.location.pathname.startsWith('/user') || window.location.pathname === '/') {
             await chatStore.reconnectAfterTokenRefresh('user', Number(userInfo.value?.id));
             console.log('✅ [UserStore] WebSocket重连成功');
           }
         } catch (wsError) {
           console.warn('⚠️ [UserStore] WebSocket重连失败，但不影响token刷新:', wsError);
         }
      } else {
        throw new Error('刷新token响应格式无效');
      }
    } catch (error: any) {
      console.error('自动刷新token失败:', error);
      
      // 如果刷新失败，尝试使用长期token登录
      try {
        const loginSuccess = await loginByLongTermTokenAction();
        if (!loginSuccess) {
          console.error('长期token登录也失败，用户需要重新登录');
          await userLogout();
        }
      } catch (longTermError) {
        console.error('长期token登录失败:', longTermError);
        await userLogout();
      }
    } finally {
      isTokenRefreshing.value = false;
      
      // 清除localStorage中的刷新状态
      try {
        const timerInfoStr = localStorage.getItem(`${NAMESPACE}_timer_info`);
        if (timerInfoStr) {
          const timerInfo = JSON.parse(timerInfoStr);
          delete timerInfo.refreshing;
          delete timerInfo.refreshingPageId;
          localStorage.setItem(`${NAMESPACE}_timer_info`, JSON.stringify(timerInfo));
        }
      } catch (error) {
        console.error('清除刷新状态失败:', error);
      }
      
      console.log(`[页面${pageId.value}] Token刷新流程结束`);
    }
  }
  
  /**
   * 手动刷新token
   * @returns 刷新是否成功
   */
  async function manualRefreshToken(): Promise<boolean> {
    try {
      await autoRefreshToken();
      return !!token.value;
    } catch (error) {
      console.error('手动刷新token失败:', error);
      return false;
    }
  }
  
  // 初始化加载用户数据和位置信息
  loadUserFromStorage();
  loadLocationFromStorage();
  
  // 初始化页面可见性监听器
  setupVisibilityListener();
  
  // 页面加载时检查是否需要接管token刷新任务
  if (token.value) {
    checkAndTakeOverTokenRefresh();
  }
  
  return {
    token,
    userInfo,
    loginTime,
    isLoggedIn,
    isActive,
    isFrozen,
    username,
    userId,
    nickname,
    avatar,
    loadUserFromStorage,
    userLogin,
    userSmsLogin,
    userLogout,
    fetchUserInfo,
    updateUserInfo,
    clearUserData,
    retoken,
    loginByLongTermTokenAction,
    // token刷新相关
    tokenExpiryTime,
    isTokenRefreshing,
    isTokenExpiring,
    startTokenRefreshTimer,
    clearTokenRefreshTimer,
    autoRefreshToken,
    manualRefreshToken,
    // 地理位置相关
    userLocation,
    locationError,
    isGettingLocation,
    hasLocation,
    currentLongitude,
    currentLatitude,
    getCurrentLocation,
    saveLocationToStorage,
    loadLocationFromStorage,
    clearLocationData,
    calculateDistance,
    calculateMerchantDistance
  };
});
