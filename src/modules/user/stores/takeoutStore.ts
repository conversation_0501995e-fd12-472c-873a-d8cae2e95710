/**
 * 外卖模块状态管理
 * 统一管理外卖相关的状态，包括商家、食品、购物车等
 * 利用请求模块的响应拦截器处理通用逻辑，简化数据处理
 */
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { takeoutService } from '../service/takeoutService';
import { ElMessage } from 'element-plus';
import type { 
  TakeoutCategory, 
  Merchant, 
  Food,
  CartItem,
  Cart,
  VariantGroup,
  ComboGroup,
  TakeoutOrder
} from '../types';
import { TakeoutOrderStatus } from '../types';

/**
 * 检查响应中是否包含特定属性
 * @param obj 待检查对象
 * @param prop 属性名
 * @returns 是否包含该属性
 */
function hasProperty<T, K extends PropertyKey>(
  obj: T,
  prop: K
): obj is T & Record<K, unknown> {
  return obj !== null && typeof obj === 'object' && prop in obj;
}

export const useTakeoutStore = defineStore('takeout', () => {
  // 商家相关状态
  const currentMerchant = ref<Merchant | null>(null);
  const merchantCategories = ref<TakeoutCategory[]>([]);
  const merchantFoods = ref<Record<string | number, Food[]>>({});
  
  // 食品相关状态
  const currentFood = ref<Food | null>(null);
  const foodVariants = ref<VariantGroup[]>([]);
  const foodComboItems = ref<ComboGroup[]>([]);
  
  // 购物车相关状态
  const cart = ref<Cart>({
    items: [],
    totalPrice: 0,
    totalItems: 0,
    merchantId: '',
    merchantName: ''
  });
  
  // 订单相关状态
  const currentOrder = ref<TakeoutOrder | null>(null);
  const orderList = ref<TakeoutOrder[]>([]);
  
  /**
   * 加载商家详情
   * @param id 商家ID
   * @returns 商家详情信息或null
   */
  async function loadMerchantDetail(id: string | number) {
    try {
      // 获取商家详情信息，响应拦截器已处理返回的data部分
      const result = await takeoutService.merchant.getMerchants({ id });
      console.log('获取商家详情结果:', result);
      
      // 安全地处理返回结果
      if (result && typeof result === 'object') {
        // 先将结果转为unknown，再转为Merchant类型以解决类型问题
        const merchantData = result as unknown as Merchant;
        currentMerchant.value = merchantData;
        return merchantData;
      }
      
      return null;
    } catch (error) {
      console.error('获取商家详情失败:', error);
      ElMessage.error('获取商家详情失败，请稍后重试');
      return null;
    }
  }
  
  /**
   * 加载商家分类
   * @param merchantId 商家ID
   * @returns 商家分类列表
   */
  async function loadMerchantCategories(merchantId: string | number) {
    try {
      // 获取商家分类列表，响应拦截器已处理返回的data部分
      const result = await takeoutService.category.getMerchantCategories(merchantId);
      console.log('获取商家分类结果:', result);
      
      // 安全地转换类型
      const categories = result as unknown as TakeoutCategory[];
      merchantCategories.value = categories || [];
      console.log('商家分类列表:', merchantCategories.value);
      return merchantCategories.value;
    } catch (error) {
      console.error('获取商家分类失败:', error);
      ElMessage.error('获取商家分类失败，请稍后重试');
      return [];
    }
  }
  
  /**
   * 加载商家食品
   * @param merchantId 商家ID
   * @param categoryId 可选的分类ID
   * @returns 食品列表或按分类整理的食品映射
   */
  async function loadMerchantFoods(merchantId: string | number, categoryId?: string | number) {
    console.log('开始获取商家食品');
    try {
      const params: any = {};
      if (categoryId) {
        params.categoryId = categoryId;
      }
      
      // 获取商家食品，响应拦截器已处理返回的data部分
      const result = await takeoutService.merchant.getMerchantFood(merchantId, params);
      console.log('Service获取商家食品结果:', result);
      
      // 安全地处理返回结果
      let foods: Food[] = [];
      
      if (Array.isArray(result)) {
        foods = result as unknown as Food[];
      } else if (result && typeof result === 'object' && 'list' in result) {
        foods = (result.list as unknown) as Food[];
      }
      
      // 按分类整理食品
      if (categoryId) {
        merchantFoods.value[categoryId] = foods;
      } else {
        // 如果没有指定分类，则获取所有食品
        const categoriesMap: Record<string | number, Food[]> = {};
        foods.forEach((food: Food) => {
          if (!categoriesMap[food.categoryId]) {
            categoriesMap[food.categoryId] = [];
          }
          categoriesMap[food.categoryId].push(food);
        });
        merchantFoods.value = categoriesMap;
      }
      
      return categoryId ? foods : merchantFoods.value;
    } catch (error) {
      console.error('获取商家食品失败:', error);
      ElMessage.error('获取商家食品失败，请稍后重试');
      return categoryId ? [] : {};
    }
  }
  
  /**
   * 加载食品详情
   * @param foodId 食品ID
   * @returns 食品详情信息或null
   */
  async function loadFoodDetail(foodId: string | number) {
    try {
      // 获取食品详情，响应拦截器已处理返回的data部分
      const result = await takeoutService.food.getFoodById(foodId);
      console.log('获取食品详情结果:', result);
      
      // 安全地处理返回结果
      if (result && typeof result === 'object' && !Array.isArray(result)) {
        const foodData = result as unknown as Food;
        currentFood.value = foodData;
        return foodData;
      }
      
      currentFood.value = null;
      return null;
    } catch (error) {
      console.error('获取食品详情失败:', error);
      ElMessage.error('获取食品详情失败，请稍后重试');
      return null;
    }
  }
  
  /**
   * 加载食品规格
   * @param foodId 食品ID
   * @returns 食品规格列表
   */
  async function loadFoodVariants(foodId: string | number) {
    try {
      // 获取食品规格，响应拦截器已处理返回的data部分
      const result = await takeoutService.food.getFoodVariants(foodId);
      console.log('获取食品规格结果:', result);
      
      // 安全地处理返回结果
      let variants: VariantGroup[] = [];
      if (Array.isArray(result)) {
        variants = result as unknown as VariantGroup[];
      } else if (result && typeof result === 'object' && 'data' in result) {
        variants = (result.data as unknown) as VariantGroup[];
      }
      
      foodVariants.value = variants;
      return foodVariants.value;
    } catch (error) {
      console.error('获取食品规格失败:', error);
      ElMessage.error('获取食品规格失败，请稍后重试');
      return [];
    }
  }
  
  /**
   * 加载食品套餐项
   * @param foodId 食品ID
   * @returns 套餐项列表
   */
  async function loadFoodComboItems(foodId: string | number) {
    try {
      // 获取食品套餐项，响应拦截器已处理返回的data部分
      const result = await takeoutService.food.getFoodComboItems(foodId);
      console.log('获取食品套餐项结果:', result);
      
      // 安全地处理返回结果
      let comboItems: ComboGroup[] = [];
      if (Array.isArray(result)) {
        comboItems = result as unknown as ComboGroup[];
      } else if (result && typeof result === 'object' && 'data' in result) {
        comboItems = (result.data as unknown) as ComboGroup[];
      }
      
      foodComboItems.value = comboItems;
      return foodComboItems.value;
    } catch (error) {
      console.error('获取食品套餐项失败:', error);
      ElMessage.error('获取食品套餐项失败，请稍后重试');
      return [];
    }
  }
  
  /**
   * 加载购物车
   * @returns 购物车信息
   */
  async function loadCart() {
    console.log('加载购物车...');
    try {
      // 获取购物车数据，响应拦截器已处理返回的data部分
      const result = await takeoutService.cart.getCartList();
      console.log('获取购物车结果:', result);
      
      // 处理返回结果，后端可能返回不同的数据结构
      if (Array.isArray(result)) {
        // 已经是购物车项数组，处理为我们需要的格式
        const items: CartItem[] = result.map(item => {
          // 创建基本食品信息
          const food: Food = {
            id: item.food_id,
            merchantId: currentMerchant.value?.id || 0,
            categoryId: 0, // 不知道分类，设置默认值
            name: item.food_name,
            price: item.price,
            originalPrice: item.original_price,
            image: item.food_image,
            stock: 999, // 默认值
            salesCount: 0,
            rating: 5,
            ratingCount: 0,
            isActive: true,
            isRecommended: false,
            hasVariants: !!item.variant_id,
            hasCombo: !!item.combo_selections,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          
          // 创建符合CartItem类型的对象
          const cartItem: CartItem = {
            id: item.cart_item_id,
            userId: 0, // 由于前端没有用户ID，设置默认值
            foodId: item.food_id,
            food,
            quantity: item.quantity,
            selected: item.selected,
            // 添加商家信息，用于按商家分组展示
            merchant_id: item.merchant_id,
            merchant_name: item.merchant_name,
            // 添加商家经纬度信息，用于计算配送距离
            merchant_latitude: item.merchant_latitude,
            merchant_longitude: item.merchant_longitude,
            packaging_fee: item.packaging_fee,
            totalPrice: item.subtotal,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          
          // 如果有规格，添加规格信息
          if (item.variant_id) {
            cartItem.variants = [{
              variantId: item.variant_id,
              name: item.variant_name || '',
              price: item.price
            }];
          }
          
          // 如果有套餐选择，添加套餐信息
          if (item.combo_selections) {
            try {
              const comboSelections = typeof item.combo_selections === 'string' 
                ? JSON.parse(item.combo_selections) 
                : item.combo_selections;
                
              if (Array.isArray(comboSelections)) {
                cartItem.comboItems = comboSelections.map(combo => ({
                  itemId: combo.item_id || 0,
                  name: combo.name || '',
                  price: combo.price || 0,
                  quantity: combo.quantity || 1
                }));
              }
            } catch (e) {
              console.error('解析套餐选择失败:', e);
            }
          }
          
          return cartItem;
        });
        
        // 计算总价和总数量
        const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
        const totalPrice = items.reduce((sum, item) => sum + item.totalPrice, 0);
        
        cart.value = {
          items,
          totalItems,
          totalPrice,
          merchantId: currentMerchant.value?.id?.toString() || '',
          merchantName: currentMerchant.value?.name || ''
        };
      } else if (result && typeof result === 'object' && 'data' in result) {
        // 包含data属性的对象格式
        cart.value = result.data as unknown as Cart || { items: [], totalPrice: 0, totalItems: 0, merchantId: '', merchantName: '' };
      } else {
        // 默认空购物车
        cart.value = { items: [], totalPrice: 0, totalItems: 0, merchantId: '', merchantName: '' };
      }
      
      return cart.value;
    } catch (error) {
      console.error('获取购物车失败:', error);
      ElMessage.error('获取购物车失败，请稍后重试');
      return cart.value;
    }
  }
  
/**
 * 添加商品到购物车
 * @param data 商品信息，包含ID、数量、规格和套餐项
 * @returns 是否添加成功
 */
async function addToCart(data: {
  food_id: string | number;
  quantity: number;
  variant_id?: string | number;
  combo_items?: Array<{
    item_id: string | number;
    quantity: number;
  }>;
}) {
  try {
    console.log('添加商品到购物车:', data);
    // 调用添加到购物车API，响应拦截器已处理返回的data部分
    const result = await takeoutService.cart.addToCart(data);
    console.log('添加商品到购物车结果:', result);
    
    if (result) {
      // 获取商品详情以创建购物车项
      const foodDetail = await loadFoodDetail(data.food_id);
      
      if (foodDetail) {
        // 创建新的购物车项
        const newItem: CartItem = {
          id: String(Date.now()), // 临时ID，实际应使用后端返回的ID
          userId: 0,
          foodId: String(data.food_id),
          food: foodDetail,
          quantity: data.quantity,
          selected: true,
          merchant_id: foodDetail.merchantId,
          merchant_name: currentMerchant.value?.name || '',
          totalPrice: foodDetail.price * data.quantity,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        // 如果有规格，添加规格信息
        if (data.variant_id) {
          newItem.variants = [{
            variantId: String(data.variant_id),
            name: '', // 理想情况下应获取规格名称
            price: foodDetail.price
          }];
        }
        
        // 如果有套餐选择，添加套餐信息
        if (data.combo_items && Array.isArray(data.combo_items)) {
          newItem.comboItems = data.combo_items.map(combo => ({
            itemId: Number(combo.item_id),
            name: '', // 理想情况下应获取套餐项名称
            price: 0, // 理想情况下应获取套餐项价格
            quantity: combo.quantity
          }));
        }
        
        // 直接更新本地购物车状态
        cart.value.items.push(newItem);
        
        // 重新计算购物车总数和总价
        updateCartTotals();
        
        ElMessage.success('已添加到购物车');
        return true;
      } else {
        // 如果无法获取商品详情，仍然调用loadCart获取完整数据
        await loadCart();
        ElMessage.success('已添加到购物车');
        return true;
      }
    }
    
    ElMessage.warning('添加到购物车失败');
    return false;
  } catch (error) {
    console.error('添加到购物车失败:', error);
    ElMessage.error('添加到购物车失败，请稍后重试');
    return false;
  }
}
  
  /**
   * 更新购物车商品数量
   * @param cartItemId 购物车项ID
   * @param quantity 新数量
   * @returns 是否更新成功
   */
  async function updateCartItemQuantity(cartItemId: string | number, quantity: number) {
    try {
      // 调用购物车更新API，响应拦截器已处理返回的data部分
      const result = await takeoutService.cart.updateCart({
        cartItemId,
        quantity
      });
      console.log('更新购物车结果:', result);
      
      if (result) {
        // 直接更新本地购物车状态
        const itemIndex = cart.value.items.findIndex(item => item.id == cartItemId);
        
        if (itemIndex !== -1) {
          const item = cart.value.items[itemIndex];
          const unitPrice = item.food?.price || 0;
          
          // 更新数量和总价
          item.quantity = quantity;
          item.totalPrice = unitPrice * quantity;
          item.updatedAt = new Date().toISOString();
          
          // 重新计算购物车总数和总价
          updateCartTotals();
          
          return true;
        } else {
          // 如果找不到项目，则重新加载购物车
          console.warn(`找不到ID为${cartItemId}的购物车项，重新加载购物车`);
          await loadCart();
          return true;
        }
      }
      
      ElMessage.warning('更新购物车失败');
      return false;
    } catch (error) {
      console.error('更新购物车失败:', error);
      ElMessage.error('更新购物车失败，请稍后重试');
      return false;
    }
  }
  
  /**
   * 从购物车移除商品
   * @param cartItemId 购物车项ID或ID数组
   * @returns 是否移除成功
   */
  async function removeFromCart(cartItemId: string | number | string[] | number[]) {
    try {
      // 调用移除购物车商品API，响应拦截器已处理返回的data部分
      const result = await takeoutService.cart.removeFromCart({
        cartItemId
      });
      console.log('移除购物车商品结果:', result);
      
      if (result) {
        // 直接更新本地购物车状态
        if (Array.isArray(cartItemId)) {
          // 如果是ID数组，批量移除
          cart.value.items = cart.value.items.filter(item => 
            !cartItemId.includes(item.id as never));
        } else {
          // 如果是单个ID，移除单个项
          cart.value.items = cart.value.items.filter(item => 
            item.id != cartItemId);
        }
        
        // 重新计算购物车总数和总价
        updateCartTotals();
        
        ElMessage.success('已从购物车移除');
        return true;
      }
      
      ElMessage.warning('移除购物车商品失败');
      return false;
    } catch (error) {
      console.error('从购物车移除商品失败:', error);
      ElMessage.error('从购物车移除商品失败，请稍后重试');
      return false;
    }
  }
  
  /**
   * 选择/取消选择购物车商品
   * @param cartItemIds 购物车项ID数组
   * @param selected 是否选中
   * @returns 是否操作成功
   */
  async function selectCartItems(cartItemIds: string[] | number[], selected: boolean) {
    try {
      // 调用选择购物车商品API，响应拦截器已处理返回的data部分
      const result = await takeoutService.cart.selectCartItems({
        cart_item_ids: cartItemIds,
        selected
      });
      console.log('选择购物车商品结果:', result);
      
      // 处理响应结果
      if (result) {
        // 直接更新本地购物车状态
        for (const id of cartItemIds) {
          const item = cart.value.items.find(item => item.id == id);
          if (item) {
            item.selected = selected;
          }
        }
        
        // 不需要更新购物车总数，因为选择状态不影响总数
        // 但需要重新计算已选择的总价
        // updateCartTotals();
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('选择购物车商品失败:', error);
      ElMessage.error('选择购物车商品失败，请稍后重试');
      return false;
    }
  }
  
  /**
   * 结算购物车
   * @param data 结算信息，包含购物车项ID和地址ID
   * @returns 结算结果
   */
  async function checkoutCart(data?: {
    cartItemIds?: string[] | number[];
    addressId?: string | number;
  }) {
    try {
      // 调用结算购物车API，响应拦截器已处理返回的data部分
      const result = await takeoutService.cart.checkoutCart(data);
      console.log('结算购物车结果:', result);
      
      // 如果结算失败，显示错误信息
      if (result && hasProperty(result, 'success') && !result.success) {
        const message = hasProperty(result, 'message') ? String(result.message) : '结算购物车失败';
        ElMessage.warning(message);
      }
      
      return result;
    } catch (error) {
      console.error('结算购物车失败:', error);
      ElMessage.error('结算购物车失败，请稍后重试');
      return { success: false, message: '结算失败，请稍后重试' };
    }
  }
  
  /**
   * 创建多商家订单
   * @param data 多商家订单数据
   * @returns 创建的订单信息
   */
  async function createMultiMerchantOrder(data: {
    takeoutAddressID: number | null;
    paymentMethod: string;
    merchantOrders: Array<{
      merchantID: number;
      cartItemIDs: number[];
      couponID: number;
      deliveryTime: string;
      remark: string;
    }>;
  }) {
    try {
      console.log('创建多商家订单，数据:', data);
      
      if (!data.takeoutAddressID) {
        ElMessage.warning('配送地址不能为空');
        return null;
      }
      
      if (!data.merchantOrders || data.merchantOrders.length === 0) {
        ElMessage.warning('没有有效的订单数据');
        return null;
      }
      
      // 构建新版多商家订单数据
      const orderData = {
        takeoutAddressID: data.takeoutAddressID,
        paymentMethod: data.paymentMethod,
        merchantOrders: data.merchantOrders
      };
      
      console.log('提交新版多商家订单数据:', orderData);
      
      // 调用新版多商家订单创建API，响应拦截器已处理返回的data部分
      const result = await takeoutService.order.createOrder(orderData);
      console.log('创建订单结果:', result);
      
      // 根据实际返回的数据结构处理
      if (result && typeof result === 'object') {
        // 检查是否是包装格式 {success: true, data: orderData}
        if (hasProperty(result, 'success') && result.success && hasProperty(result, 'data')) {
          const orderData = result.data as unknown as TakeoutOrder;
          currentOrder.value = orderData;
          ElMessage.success('订单创建成功');
          return orderData;
        }
        // 检查是否是数组格式（多商家订单返回订单数组）
        else if (Array.isArray(result) && result.length > 0) {
          // 取第一个订单作为主订单
          const orderData = result[0] as unknown as TakeoutOrder;
          currentOrder.value = orderData;
          ElMessage.success('订单创建成功');
          return orderData;
        }
        // 检查是否直接返回订单对象（包含orderID字段）
        else if (hasProperty(result, 'orderID') || hasProperty(result, 'id')) {
          const orderData = result as unknown as TakeoutOrder;
          currentOrder.value = orderData;
          ElMessage.success('订单创建成功');
          return orderData;
        }
        // 检查是否是错误响应
        else if (hasProperty(result, 'success') && !result.success) {
          const message = hasProperty(result, 'message') ? String(result.message) : '创建订单失败';
          ElMessage.warning(message);
          return null;
        }
      }
      
      // 如果无法识别响应格式，显示通用错误信息
      ElMessage.warning('创建订单失败，响应格式异常');
      return null;
    } catch (error) {
      console.error('创建多商家订单失败:', error);
      ElMessage.error('创建订单失败，请稍后重试');
      return null;
    }
  }

  /**
   * 创建订单（旧版单商家格式）
   * @param data 订单信息，包含购物车项ID、地址ID、支付方式和备注
   * @returns 订单信息或null
   */
  async function createOrder(data: {
    takeoutAddressID: string | number;
    paymentMethod: string;
    remark?: string;
    deliveryTime?: string;
    cartItemIDs: string[] | number[];
    couponID?: number;
  }) {
    try {
      // 调用创建订单API，响应拦截器已处理返回的data部分
      const result = await takeoutService.order.createOrderLegacy(data);
      console.log('创建订单结果:', result);
      
      // 根据实际返回的数据结构处理
      if (result && typeof result === 'object') {
        // 检查是否是包装格式 {success: true, data: orderData}
        if (hasProperty(result, 'success') && result.success && hasProperty(result, 'data')) {
          const orderData = result.data as unknown as TakeoutOrder;
          currentOrder.value = orderData;
          ElMessage.success('订单创建成功');
          return orderData;
        }
        // 检查是否直接返回订单对象（包含orderID字段）
        else if (hasProperty(result, 'orderID') || hasProperty(result, 'id')) {
          const orderData = result as unknown as TakeoutOrder;
          currentOrder.value = orderData;
          ElMessage.success('订单创建成功');
          return orderData;
        }
        // 检查是否是错误响应
        else if (hasProperty(result, 'success') && !result.success) {
          const message = hasProperty(result, 'message') ? String(result.message) : '创建订单失败';
          ElMessage.warning(message);
          return null;
        }
      }
      
      // 如果无法识别响应格式，显示通用错误信息
      ElMessage.warning('创建订单失败，响应格式异常');
      return null;
    } catch (error) {
      console.error('创建订单失败:', error);
      ElMessage.error('创建订单失败，请稍后重试');
      return null;
    }
  }
  
  /**
 * 加载订单列表
 * @author: AI Assistant
 * @date: 2025-06-03
 * @version: 1.2.0
 * @description: 加载外卖订单列表，处理后端返回的数据结构
 * @param params 查询参数，包含状态、页码和每页数量
 * @returns 订单列表和总数
 */
  async function loadOrders(params?: {
    status?: string;
    page?: number;
    pageSize?: number;
  }) {
    try {
      // 调用获取订单列表API，响应拦截器已处理返回的data部分
      const result = await takeoutService.order.getOrderList(params);
      console.log('加载外卖订单列表结果:', result);
      
      // 安全地处理返回结果
      if (result && typeof result === 'object') {
        // 后端返回的数据结构是 {total, page, pageSize, totalPage, list}
        const orderData = hasProperty(result, 'list') ? result.list as unknown as TakeoutOrder[] : [];
        const totalCount = hasProperty(result, 'total') ? Number(result.total) : 0;
        
        orderList.value = orderData;
        return {
          data: orderData,
          total: totalCount
        };
      }
      
      return {
        data: [],
        total: 0
      };
    } catch (error) {
      console.error('获取订单列表失败:', error);
      ElMessage.error('获取订单列表失败，请稍后重试');
      return {
        data: [],
        total: 0
      };
    }
  }
  
  /**
   * 加载订单详情
   * @param orderId 订单ID
   * @returns 订单详情信息或null
   */
  async function loadOrderDetail(orderId: string | number) {
    try {
      // 调用获取订单详情API
      const result = await takeoutService.order.getOrderDetail(orderId);
      console.log('加载订单详情结果:', result);
      
      // 安全地处理返回结果
      if (result && typeof result === 'object') {
        // 将 API 返回的订单数据转换为前端使用的 TakeoutOrder 类型
        try {
          let rawOrderData: any;
          
          // 判断返回数据的结构
          if (hasProperty(result, 'data')) {
            // 如果返回的是 { data: {...} } 格式
            rawOrderData = result.data;
          } else if (hasProperty(result, 'orderID')) {
            // 如果返回的是直接的订单对象
            rawOrderData = result;
          } else {
            console.error('订单数据格式不正确:', result);
            return null;
          }
          
          // 状态映射函数：将数字状态转换为字符串枚举
          const mapOrderStatus = (status: number): TakeoutOrderStatus => {
            switch (status) {
              case 10: return TakeoutOrderStatus.PENDING;
              case 20: return TakeoutOrderStatus.PAID;
              case 30: return TakeoutOrderStatus.PROCESSING;
              case 40: return TakeoutOrderStatus.DELIVERING;
              case 50: return TakeoutOrderStatus.COMPLETED;
              case 60: return TakeoutOrderStatus.CANCELLED;
              case 70: return TakeoutOrderStatus.REFUNDING;
              case 80: return TakeoutOrderStatus.REFUNDED;
              default: return TakeoutOrderStatus.PENDING;
            }
          };
          
          // 将API返回的订单数据转换为前端应用使用的TakeoutOrder类型
          const orderData: TakeoutOrder = {
            orderID: rawOrderData.orderID,
            orderNumber: rawOrderData.orderNo,
            userId: rawOrderData.userID,
            userID: rawOrderData.userID,  // 添加驼峰命名支持
            merchantId: rawOrderData.merchantID,
            merchantID: rawOrderData.merchantID,  // 添加驼峰命名支持
            orderStatus: mapOrderStatus(rawOrderData.orderStatus),
            totalAmount: rawOrderData.totalAmount,
            deliveryFee: rawOrderData.deliveryFee || 0,
            packagingFee: rawOrderData.packagingFee || 0,
            discountAmount: rawOrderData.discountAmount || 0,
            payableAmount: rawOrderData.payAmount,
            paymentMethod: rawOrderData.paymentMethod || '',
            remark: rawOrderData.remark || '',
            createdAt: rawOrderData.createTime || '',
            paidAt: rawOrderData.payTime,
            acceptedAt: rawOrderData.acceptedTime || '',
            deliveredAt: rawOrderData.deliveryTime,
            completedAt: rawOrderData.completeTime,
            updatedAt: rawOrderData.updateTime || rawOrderData.createTime || '',
            items: [], // 默认空数组
            address: {
              id: '0',
              name: '',
              phone: '',
              province: '',
              city: '',
              district: '',
              detail: ''
            },
            merchant: {
              id: rawOrderData.merchantID,
              name: rawOrderData.merchantName || '',
              logo: '',
              phone: rawOrderData.merchantPhone || ''
            }
          };
          
          // 如果有配送信息，填充到地址对象
          if (rawOrderData.deliveryInfo) {
            orderData.address = {
              id: '0',
              name: rawOrderData.deliveryInfo.receiverName || '',
              phone: rawOrderData.deliveryInfo.receiverPhone || '',
              province: '',
              city: '',
              district: '',
              detail: rawOrderData.deliveryInfo.deliveryAddress || ''
            };
          }
          
          // 如果有商品列表，转换为前端所需格式
          if (Array.isArray(rawOrderData.items) && rawOrderData.items.length > 0) {
            orderData.items = rawOrderData.items.map((item: any) => ({
              id: String(item.id || item.orderItemID || '0'),
              foodId: String(item.foodID || item.productID || '0'),
              foodName: item.foodName || item.productName || '',
              foodImage: item.image || item.foodImage || item.productImage || '',
              price: item.price || 0,
              quantity: item.quantity || 1,
              totalPrice: item.amount || (item.price * item.quantity) || item.totalPrice || 0,
              variants: item.variants || [],
              comboItems: item.comboItems || []
            }));
          }
          
          // 更新当前订单并返回
          currentOrder.value = orderData;
          console.log('转换后的订单详情结果:', orderData);
          return orderData;
        } catch (parseError) {
          console.error('转换订单数据失败:', parseError);
          return null;
        }
      }
      
      return null;
    } catch (error) {
      console.error('获取订单详情失败:', error);
      ElMessage.error('获取订单详情失败，请稍后重试');
      return null;
    }
  }
  
  /**
   * 取消订单
   * @param orderId 订单ID
   * @param reason 取消原因
   * @returns 是否取消成功
   */
  async function cancelOrder(orderId: string | number, reason: string) {
    try {
      // 调用取消订单API，响应拦截器已处理返回的data部分
      const result = await takeoutService.order.cancelOrder(orderId, {
        reason: reason
      });
      console.log('取消订单结果:', result);
      
      // 处理成功情况
      if (result && hasProperty(result, 'success') && result.success) {
        ElMessage.success('订单已取消');
        // 重新加载订单详情
        await loadOrderDetail(orderId);
        return true;
      }
      
      // 处理失败情况
      const message = result && hasProperty(result, 'message') ? String(result.message) : '取消订单失败';
      ElMessage.warning(message);
      return false;
    } catch (error) {
      console.error('取消订单失败:', error);
      ElMessage.error('取消订单失败，请稍后重试');
      return false;
    }
  }
  
  /**
   * 评价订单
   * @param orderId 订单ID
   * @param rating 评分(1-5)
   * @param comment 评价内容
   * @param foodRatings 食品评分数组
   * @returns 是否评价成功
   */
  async function rateOrder(data: {
    orderId: string | number;
    rating: number;
    comment?: string;
    foodRatings?: Array<{
      foodId: string | number;
      rating: number;
      comment?: string;
    }>;
  }) {
    try {
      // 调用评价订单API，响应拦截器已处理返回的data部分
      const result = await takeoutService.order.rateOrder(data);
      console.log('评价订单结果:', result);
      
      // 处理成功情况
      if (result && hasProperty(result, 'success') && result.success) {
        ElMessage.success('评价成功');
        // 重新加载订单详情
        await loadOrderDetail(data.orderId);
        return true;
      }
      
      // 处理失败情况
      const message = result && hasProperty(result, 'message') ? String(result.message) : '评价失败';
      ElMessage.warning(message);
      return false;
    } catch (error) {
      console.error('评价订单失败:', error);
      ElMessage.error('评价订单失败，请稍后重试');
      return false;
    }
  }
  
  /**
   * 确认收货
   * @param orderId 订单ID
   * @returns 是否确认成功
   */
  async function confirmReceived(orderId: string | number) {
    try {
      // 调用确认收货API，响应拦截器已处理返回的data部分
      const result = await takeoutService.order.confirmReceived(orderId);
      console.log('确认收货结果:', result);
      
      // 处理成功情况
      if (result && hasProperty(result, 'success') && result.success) {
        ElMessage.success('已确认收货');
        // 重新加载订单详情
        await loadOrderDetail(orderId);
        return true;
      }
      
      // 处理失败情况
      const message = result && hasProperty(result, 'message') ? String(result.message) : '确认收货失败';
      ElMessage.warning(message);
      return false;
    } catch (error) {
      console.error('确认收货失败:', error);
      ElMessage.error('确认收货失败，请稍后重试');
      return false;
    }
  }
  
  /**
   * 更新购物车总数和总价
   * 在本地修改购物车后调用此函数更新购物车的总数量和总价格
   */
  function updateCartTotals() {
    // 更新购物车总数量
    cart.value.totalItems = cart.value.items.reduce((sum, item) => sum + item.quantity, 0);
    
    // 更新购物车总价格
    cart.value.totalPrice = cart.value.items.reduce((sum, item) => sum + item.totalPrice, 0);
    
    console.log(`购物车更新: ${cart.value.totalItems} 件商品, 总价 ¥${cart.value.totalPrice.toFixed(2)}`);
  }
  
  // 计算购物车中的商品总数
  const cartItemCount = computed(() => {
    return cart.value.items.reduce((sum, item) => sum + item.quantity, 0);
  });
  
  // 计算购物车中已选择的商品
  const selectedCartItems = computed(() => {
    return cart.value.items.filter(item => item.selected);
  });
  
  // 计算已选择商品的总价
  const selectedTotalPrice = computed(() => {
    return selectedCartItems.value.reduce((sum, item) => sum + item.totalPrice, 0);
  });
  
  return {
    // 状态
    currentMerchant,
    merchantCategories,
    merchantFoods,
    currentFood,
    foodVariants,
    foodComboItems,
    cart,
    currentOrder,
    orderList,
    
    // 计算属性
    cartItemCount,
    selectedCartItems,
    selectedTotalPrice,
    
    // 方法
    loadMerchantDetail,
    loadMerchantCategories,
    loadMerchantFoods,
    loadFoodDetail,
    loadFoodVariants,
    loadFoodComboItems,
    loadCart,
    addToCart,
    updateCartItemQuantity,
    removeFromCart,
    selectCartItems,
    checkoutCart,
    createOrder,
    createMultiMerchantOrder,
    loadOrders,
    loadOrderDetail,
    cancelOrder,
    rateOrder,
    confirmReceived
  };
});
