/**
 * 用户端聊天WebSocket服务
 * 为用户提供实时聊天功能的WebSocket连接管理
 */

// Vue导入已移除，使用普通JavaScript对象管理状态
import { ElMessage } from 'element-plus'
import { useUserStore } from '../stores/userStore'
import { getCurrentDeviceId } from '@/utils/deviceInfo'

// WebSocket状态枚举
export enum WebSocketStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

// 消息类型枚举
export enum MessageType {
  CHAT_MESSAGE = 'chat_message',
  USER_STATUS = 'user_status',
  HEARTBEAT = 'heartbeat',
  SYSTEM_NOTIFICATION = 'system_notification'
}

// 聊天消息接口
export interface ChatMessage {
  id: string
  session_id: string
  sender_id: number
  sender_type: 'user' | 'merchant'
  sender_name: string
  sender_avatar?: string
  content: string
  type: 'text' | 'image' | 'file' | 'voice' | 'video'
  status: number
  created_at: string
  updated_at: string
}

// 聊天会话接口
export interface ChatSession {
  id: string
  title: string
  type: string
  status: string
  participants: any[]
  unread_count: number
  last_message_at: string
  created_at: string
  updated_at: string
}

// 事件监听器类型
export type EventListener = (...args: any[]) => void

/**
 * 用户端聊天WebSocket服务类
 */
export class UserChatWebSocketService {
  private ws: WebSocket | null = null
  private eventListeners: Map<string, EventListener[]> = new Map()
  private reconnectTimer: number | null = null
  private heartbeatTimer: number | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000

  // 响应式状态 - 使用与商家端一致的对象结构
  public status: { value: WebSocketStatus } = { value: WebSocketStatus.DISCONNECTED }
  public isConnected: { value: boolean } = { value: false }
  public error: { value: string } = { value: '' }

  constructor() {
    this.initializeState()
  }

  /**
   * 初始化状态
   */
  private initializeState(): void {
    console.log('初始化用户WebSocket状态对象')

    // 确保状态对象正确初始化
    this.status = { value: WebSocketStatus.DISCONNECTED }
    this.isConnected = { value: false }
    this.error = { value: '' }

    console.log('用户WebSocket状态对象初始化完成')
  }

  /**
   * 获取用户Store实例
   */
  private getUserStore() {
    return useUserStore()
  }

  /**
   * 更新连接状态
   */
  private updateStatus(newStatus: WebSocketStatus): void {
    this.status.value = newStatus
    this.isConnected.value = newStatus === WebSocketStatus.CONNECTED
  }

  /**
   * 更新错误信息
   */
  private updateError(errorMessage: string): void {
    this.error.value = errorMessage
  }

  /**
   * 建立WebSocket连接
   * @deprecated 此服务已废弃，请使用统一的chatStore代替
   */
  public async connect(): Promise<void> {
    console.warn('⚠️ UserChatWebSocketService.connect() 已废弃，请使用统一的chatStore代替')
    console.warn('⚠️ 为避免重复连接和冲突，此方法将不执行任何操作')
    return

    // 以下代码已禁用，避免与新的WebSocket服务冲突
    /*
    // 确保状态对象正确初始化
    if (!this.status || typeof this.status.value === 'undefined') {
      console.warn('用户WebSocket服务状态未正确初始化，重新初始化')
      this.initializeState()
    }

    if (this.status.value === WebSocketStatus.CONNECTING ||
        this.status.value === WebSocketStatus.CONNECTED) {
      console.log('用户WebSocket已连接或正在连接中')
      return
    }
    */

    // 以下代码已禁用，避免与新的WebSocket服务冲突
    /*
    const userStore = this.getUserStore()
    const userInfo = userStore.userInfo
    const token = userStore.token

    console.log('🔍 用户WebSocket连接检查:', {
      isLoggedIn: userStore.isLoggedIn,
      hasUserInfo: !!userInfo,
      hasUserId: !!userInfo?.id,
      hasToken: !!token,
      userInfo: userInfo,
      tokenLength: token ? token.length : 0
    })

    if (!userInfo || !userInfo.id || !token) {
      console.warn('用户未登录，无法建立WebSocket连接:', {
        userInfo: !!userInfo,
        userId: userInfo?.id,
        token: !!token
      })
      this.updateStatus(WebSocketStatus.ERROR)
      this.updateError('用户未登录或token无效')
      return
    }

    try {
      this.updateStatus(WebSocketStatus.CONNECTING)
      this.updateError('')

      // 构建WebSocket URL
      const wsUrl = this.buildWebSocketUrl(userInfo.id, token)
      console.log('正在连接用户WebSocket:', wsUrl)

      // 创建WebSocket连接
      this.ws = new WebSocket(wsUrl)

      // 设置事件监听器
      this.setupWebSocketEventListeners()

    } catch (error: any) {
      console.error('用户WebSocket连接失败:', error)
      this.updateStatus(WebSocketStatus.ERROR)
      this.updateError(error.message || '用户WebSocket连接失败')
      this.scheduleReconnect()
    }
    */
  }

  /**
   * 构建WebSocket连接URL - 支持多端登录
   */
  private buildWebSocketUrl(userId: number | string, token: string): string {
    // 优先使用环境变量中的WebSocket URL
    const wsBaseUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8181'

    // 如果环境变量没有配置，则根据当前页面协议和地址构建
    let baseUrl = wsBaseUrl
    if (!wsBaseUrl || wsBaseUrl === 'ws://localhost:8181') {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const host = window.location.hostname
      const port = import.meta.env.DEV ? '8181' : window.location.port  // 修改为后端WebSocket服务端口
      baseUrl = `${protocol}//${host}:${port}`
    }

    // 获取设备ID用于多端登录
    const deviceId = this.getDeviceId()

    // 构建支持多端登录的WebSocket URL
    const wsUrl = `${baseUrl}/api/v1/chat/ws?token=${encodeURIComponent(token)}&device_id=${encodeURIComponent(deviceId)}`

    console.log('🔗 构建用户WebSocket URL (多端登录):', {
      baseUrl,
      userId,
      deviceId,
      tokenLength: token.length,
      finalUrl: wsUrl.replace(token, '***TOKEN***')
    })

    return wsUrl
  }

  /**
   * 获取设备ID
   */
  private getDeviceId(): string {
    try {
      // 使用统一的设备信息工具获取设备ID
      const deviceId = getCurrentDeviceId()
      console.log('🔍 从deviceInfo工具获取设备ID:', deviceId)
      return deviceId
    } catch (error) {
      console.warn('获取设备信息失败:', error)
      // 同步获取设备ID的备用方案
      const fallbackDeviceId = 'omall_' + Date.now().toString(36) + '_' + Math.random().toString(36).substring(2, 11)
      console.log('🔍 使用备用设备ID:', fallbackDeviceId)
      return fallbackDeviceId
    }
  }

  /**
   * 设置WebSocket事件监听器
   */
  private setupWebSocketEventListeners(): void {
    if (!this.ws) return
    
    this.ws.onopen = () => {
      console.log('✅ 用户WebSocket连接已建立')
      this.updateStatus(WebSocketStatus.CONNECTED)
      this.reconnectAttempts = 0
      this.reconnectDelay = 1000

      // 发送用户在线状态
      this.sendUserStatus('online')

      // 启动心跳
      this.startHeartbeat()

      // 触发连接成功事件
      const userId = this.getUserStore().userInfo?.id
      this.emit('connected', { userId })

      console.log('🎉 用户聊天服务连接成功，用户ID:', userId)
      ElMessage.success('聊天服务已连接')
    }
    
    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.handleWebSocketMessage(data)
      } catch (error) {
        console.error('解析用户WebSocket消息失败:', error, event.data)
      }
    }
    
    this.ws.onclose = (event) => {
      console.log('❌ 用户WebSocket连接已关闭:', {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean
      })
      this.updateStatus(WebSocketStatus.DISCONNECTED)
      this.stopHeartbeat()

      // 触发断开连接事件
      this.emit('disconnected', { code: event.code, reason: event.reason })

      // 如果不是主动断开，则尝试重连
      if (event.code !== 1000 && this.getUserStore().isLoggedIn) {
        console.log('🔄 准备重连用户WebSocket...')
        this.scheduleReconnect()
      } else {
        console.log('🛑 用户WebSocket正常关闭，不重连')
      }
    }
    
    this.ws.onerror = (error) => {
      console.error('💥 用户WebSocket连接错误:', error)
      this.updateStatus(WebSocketStatus.ERROR)
      this.updateError('用户WebSocket连接错误')

      // 触发错误事件
      this.emit('error', { error })

      // 显示用户友好的错误信息
      ElMessage.error('聊天服务连接失败，请检查网络连接')
    }
  }

  /**
   * 处理WebSocket消息
   */
  private handleWebSocketMessage(data: any): void {
    console.log('收到用户WebSocket消息:', data)

    switch (data.type) {
      case 'message':
        // 处理聊天消息
        console.log('🔔 处理聊天消息:', data)
        this.handleChatMessage(data)
        break
      case MessageType.CHAT_MESSAGE:
      case 'chat_message':
        this.emit('message', data.payload || data.data)
        break
      case MessageType.USER_STATUS:
      case 'user_status':
        this.emit('user_status', data.payload || data.data)
        break
      case MessageType.SYSTEM_NOTIFICATION:
      case 'system_notification':
      case 'notification':
        this.emit('notification', data.payload || data.data)
        break
      case MessageType.HEARTBEAT:
      case 'heartbeat':
        // 心跳响应，不需要特殊处理
        break
      default:
        console.warn('未知的用户WebSocket消息类型:', data.type, data)
    }
  }

  /**
   * 处理聊天消息
   */
  private handleChatMessage(data: any): void {
    try {
      const messageData = data.data
      if (!messageData) {
        console.warn('聊天消息数据为空:', data)
        return
      }

      console.log('📨 处理聊天消息数据:', messageData)

      // 检查是否是自己发送的消息，避免重复处理
      const currentUserId = this.getUserStore().userInfo?.id
      const isOwnMessage = messageData.sender_id === currentUserId && messageData.sender_type === 'user'

      if (isOwnMessage) {
        console.log('🚫 跳过自己发送的消息，避免重复显示:', {
          messageId: messageData.id,
          senderId: messageData.sender_id,
          currentUserId: currentUserId,
          senderType: messageData.sender_type
        })
        return
      }

      console.log('✅ 处理来自其他用户的消息:', {
        messageId: messageData.id,
        senderId: messageData.sender_id,
        senderType: messageData.sender_type,
        content: messageData.content?.substring(0, 30) + '...'
      })

      // 触发消息事件，让ChatWindow组件处理
      this.emit('message', {
        type: 'new_message',
        session_id: data.session_id,
        message: messageData
      })

      // 如果是新消息事件，还要触发会话更新
      if (data.event === 'new_message') {
        this.emit('session_update', {
          session_id: data.session_id,
          last_message: messageData,
          unread_count_increment: 1
        })
      }

    } catch (error) {
      console.error('处理聊天消息失败:', error, data)
    }
  }

  /**
   * 发送用户状态
   */
  private sendUserStatus(status: 'online' | 'offline'): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = {
        type: MessageType.USER_STATUS,
        payload: {
          user_id: this.getUserStore().userInfo?.id,
          status,
          timestamp: Date.now()
        }
      }
      this.ws.send(JSON.stringify(message))
    }
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = window.setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        const heartbeat = {
          type: MessageType.HEARTBEAT,
          payload: { timestamp: Date.now() }
        }
        this.ws.send(JSON.stringify(heartbeat))
      }
    }, 30000) // 30秒心跳间隔
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('用户WebSocket重连次数已达上限，停止重连')
      return
    }

    this.reconnectAttempts++
    console.log(`用户WebSocket将在 ${this.reconnectDelay}ms 后进行第 ${this.reconnectAttempts} 次重连`)

    this.reconnectTimer = window.setTimeout(() => {
      this.connect()
    }, this.reconnectDelay)

    // 指数退避
    this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000)
  }

  /**
   * 断开WebSocket连接
   */
  public disconnect(): void {
    console.log('主动断开用户WebSocket连接')
    
    // 清除重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    // 停止心跳
    this.stopHeartbeat()

    // 发送离线状态
    this.sendUserStatus('offline')

    // 关闭WebSocket连接
    if (this.ws) {
      this.ws.close(1000, '用户主动断开连接')
      this.ws = null
    }

    this.updateStatus(WebSocketStatus.DISCONNECTED)
  }

  /**
   * 添加事件监听器
   */
  public on(event: string, listener: EventListener): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(listener)
  }

  /**
   * 移除事件监听器
   */
  public off(event: string, listener: EventListener): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, ...args: any[]): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(...args)
        } catch (error) {
          console.error(`用户WebSocket事件监听器执行失败 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 初始化服务
   */
  public init(): void {
    console.log('初始化用户聊天WebSocket服务')
    // 可以在这里添加初始化逻辑
  }
}

// 延迟创建全局单例实例
let userChatWebSocketService: UserChatWebSocketService | null = null

/**
 * 获取用户聊天WebSocket服务实例
 * @deprecated 此服务已废弃，请使用统一的chatStore代替
 */
function getUserChatWebSocketService(): UserChatWebSocketService {
  console.warn('⚠️ getUserChatWebSocketService() 已废弃，请使用统一的chatStore代替')
  console.warn('⚠️ 为避免重复连接，此服务将不会自动连接WebSocket')

  if (!userChatWebSocketService) {
    userChatWebSocketService = new UserChatWebSocketService()
    userChatWebSocketService.init()
  }
  return userChatWebSocketService
}

// 导出服务实例获取函数
export default getUserChatWebSocketService
