<!--
  用户首页
  展示用户个人概况和常用功能入口
-->
<template>
  <div class="user-home-page">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <AppCard class="welcome-card">
        <div class="user-info-row">
          <div class="user-avatar-area">
            <el-avatar :size="80" :src="userStore.avatar" />
            <div class="user-basic-info">
              <h2 class="user-name">{{ userStore.nickname }}</h2>
              <p class="user-id">用户ID: {{ userStore.userId }}</p>
            </div>
          </div>
          <div class="user-stats">
            <div class="stat-item">
              <h3 class="stat-value">{{ userAssets.balance || 0 }}</h3>
              <p class="stat-label">账户余额</p>
            </div>
            <div class="stat-item">
              <h3 class="stat-value">{{ userAssets.points || 0 }}</h3>
              <p class="stat-label">积分</p>
            </div>
            <div class="stat-item">
              <h3 class="stat-value">{{ userAssets.coupons || 0 }}</h3>
              <p class="stat-label">优惠券</p>
            </div>
          </div>
        </div>
      </AppCard>
    </div>

    <!-- 订单区域 -->
    <div class="orders-section">
      <AppCard class="orders-card">
        <template #header>
          <div class="section-header">
            <h3 class="section-title">我的订单</h3>
            <el-button type="text" @click="navigateTo('/user/orders')">查看全部</el-button>
          </div>
        </template>
        <div class="order-stats">
          <div class="order-stat-item" @click="navigateTo('/user/orders?status=10')">
            <el-icon><Wallet /></el-icon>
            <p>待付款</p>
            <el-badge v-if="orderStats.pendingPayment > 0" :value="orderStats.pendingPayment" class="badge" />
          </div>
          <div class="order-stat-item" @click="navigateTo('/user/orders?status=20')">
            <el-icon><Box /></el-icon>
            <p>待发货</p>
            <el-badge v-if="orderStats.paid > 0" :value="orderStats.paid" class="badge" />
          </div>
          <div class="order-stat-item" @click="navigateTo('/user/orders?status=40')">
            <el-icon><Van /></el-icon>
            <p>待收货</p>
            <el-badge v-if="orderStats.shipped > 0" :value="orderStats.shipped" class="badge" />
          </div>
          <div class="order-stat-item" @click="navigateTo('/user/orders?status=50')">
            <el-icon><StarFilled /></el-icon>
            <p>待评价</p>
            <el-badge v-if="orderStats.delivered > 0" :value="orderStats.delivered" class="badge" />
          </div>
          <div class="order-stat-item" @click="navigateTo('/user/orders?status=70')">
            <el-icon><RefreshRight /></el-icon>
            <p>退款/售后</p>
            <el-badge v-if="orderStats.refunding > 0" :value="orderStats.refunding" class="badge" />
          </div>
        </div>

        <div v-if="recentOrders.length > 0" class="recent-orders">
          <h4 class="recent-orders-title">最近订单</h4>
          <el-table :data="recentOrders" style="width: 100%" :row-class-name="tableRowClassName">
            <el-table-column prop="orderNumber" label="订单号" width="180" />
            <el-table-column prop="createdAt" label="下单时间" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.createdAt) }}
              </template>
            </el-table-column>
            <el-table-column prop="totalAmount" label="金额">
              <template #default="scope">
                ¥{{ scope.row.totalAmount.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag :type="getOrderStatusType(scope.row.status)">
                  {{ getOrderStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button size="small" @click="navigateTo(`/user/orders?id=${scope.row.id}`)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-empty v-else description="暂无订单数据" />
      </AppCard>
    </div>

    <!-- 功能快捷入口 -->
    <div class="features-section">
      <AppCard class="features-card">
        <template #header>
          <div class="section-header">
            <h3 class="section-title">常用功能</h3>
          </div>
        </template>
        <div class="feature-items">
          <div class="feature-item" @click="navigateTo('/user/addresses')">
            <el-icon><Location /></el-icon>
            <p>收货地址</p>
          </div>
          <div class="feature-item" @click="navigateTo('/user/profile')">
            <el-icon><User /></el-icon>
            <p>个人资料</p>
          </div>
          <div class="feature-item" @click="navigateTo('/user/settings')">
            <el-icon><Setting /></el-icon>
            <p>账户设置</p>
          </div>
          <div class="feature-item" @click="navigateTo('/help-center')">
            <el-icon><QuestionFilled /></el-icon>
            <p>帮助中心</p>
          </div>
        </div>
      </AppCard>
    </div>

    <!-- 推荐商品区域 -->
    <div class="recommendations-section">
      <AppCard class="recommendations-card">
        <template #header>
          <div class="section-header">
            <h3 class="section-title">猜你喜欢</h3>
            <el-button type="text" @click="refreshRecommendations">换一批</el-button>
          </div>
        </template>
        <div v-if="recommendedProducts.length > 0" class="product-list">
          <div v-for="product in recommendedProducts" :key="product.id" class="product-item">
            <div class="product-image">
              <img :src="product.image" :alt="product.name" />
            </div>
            <h4 class="product-name">{{ product.name }}</h4>
            <p class="product-price">¥{{ product.price.toFixed(2) }}</p>
          </div>
        </div>
        <el-empty v-else description="暂无推荐商品" />
      </AppCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { safeExecute } from '../utils/errorUtils';
import { useUserStore } from '../stores/userStore';
import { ElMessage } from 'element-plus';
import AppCard from '@/components/base/AppCard.vue';
import { OrderStatus } from '../types';

// 用户商店
const userStore = useUserStore();
const router = useRouter();

// 用户资产
const userAssets = ref({
  balance: 0,
  points: 0,
  coupons: 0
});

// 订单统计
const orderStats = ref({
  pendingPayment: 0,
  paid: 0,
  shipped: 0,
  delivered: 0,
  refunding: 0
});

// 模拟的最近订单数据
const recentOrders = ref([
  {
    id: '1001',
    orderNumber: 'ORD20250520001',
    createdAt: '2025-05-18T14:30:00',
    totalAmount: 299.00,
    status: OrderStatus.PENDING
  },
  {
    id: '1002',
    orderNumber: 'ORD20250515002',
    createdAt: '2025-05-15T10:20:00',
    totalAmount: 128.50,
    status: OrderStatus.DELIVERING
  },
  {
    id: '1003',
    orderNumber: 'ORD20250512003',
    createdAt: '2025-05-12T16:45:00',
    totalAmount: 68.00,
    status: OrderStatus.COMPLETED
  }
]);

// 推荐商品
const recommendedProducts = ref([
  {
    id: '101',
    name: '时尚休闲夏季T恤',
    price: 89.90,
    image: '/images/products/tshirt.jpg'
  },
  {
    id: '102',
    name: '高品质蓝牙耳机',
    price: 199.00,
    image: '/images/products/headphone.jpg'
  },
  {
    id: '103',
    name: '多功能智能手表',
    price: 349.00,
    image: '/images/products/watch.jpg'
  },
  {
    id: '104',
    name: '轻薄笔记本电脑',
    price: 4599.00,
    image: '/images/products/laptop.jpg'
  }
]);

/**
 * 格式化日期
 * @param dateString 日期字符串
 * @returns 格式化后的日期字符串
 */
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * 获取订单状态文本
 * @param status 订单状态
 * @returns 状态文本
 */
function getOrderStatusText(status: OrderStatus): string {
  const statusMap: Record<OrderStatus, string> = {
    [OrderStatus.PENDING]: '待付款',
    [OrderStatus.PAID]: '待发货',
    [OrderStatus.PROCESSING]: '处理中',
    [OrderStatus.DELIVERING]: '配送中',
    [OrderStatus.COMPLETED]: '已完成',
    [OrderStatus.CANCELLED]: '已取消',
    [OrderStatus.REFUNDING]: '退款中',
    [OrderStatus.REFUNDED]: '已退款'
  };
  return statusMap[status] || '未知状态';
}

/**
 * 获取订单状态对应的标签类型
 * @param status 订单状态
 * @returns 标签类型
 */
function getOrderStatusType(status: OrderStatus): string {
  const typeMap: Record<OrderStatus, string> = {
    [OrderStatus.PENDING]: 'warning',
    [OrderStatus.PAID]: 'info',
    [OrderStatus.PROCESSING]: 'primary',
    [OrderStatus.DELIVERING]: 'primary',
    [OrderStatus.COMPLETED]: 'success',
    [OrderStatus.CANCELLED]: 'info',
    [OrderStatus.REFUNDING]: 'danger',
    [OrderStatus.REFUNDED]: 'info'
  };
  return typeMap[status] || 'info';
}

/**
 * 表格行的类名
 * @param row 行数据
 * @returns 类名
 */
function tableRowClassName({ row }: { row: any }): string {
  if (row.status === OrderStatus.PENDING) {
    return 'warning-row';
  }
  return '';
}

/**
 * 导航到指定路径
 * @param path 路径
 */
function navigateTo(path: string): void {
  router.push(path);
}

/**
 * 刷新推荐商品
 */
function refreshRecommendations(): void {
  // 模拟刷新推荐商品
  ElMessage.success('推荐已更新');
  
  // 模拟数据变化
  recommendedProducts.value = [
    {
      id: '105',
      name: '防晒霜SPF50',
      price: 129.00,
      image: '/images/products/sunscreen.jpg'
    },
    {
      id: '106',
      name: '无线充电器',
      price: 89.90,
      image: '/images/products/charger.jpg'
    },
    {
      id: '107',
      name: '防水运动相机',
      price: 899.00,
      image: '/images/products/camera.jpg'
    },
    {
      id: '108',
      name: '智能空气净化器',
      price: 1299.00,
      image: '/images/products/airpurifier.jpg'
    }
  ];
}

/**
 * 获取用户资产
 */
async function fetchUserAssets() {
  return safeExecute(
    async () => {
      try {
        // 模拟API调用，实际开发中替换为真实API
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 模拟数据
        userAssets.value = {
          balance: 320.50,
          points: 1250,
          coupons: 3
        };
        return userAssets.value;
      } catch (err) {
        console.error('获取用户资产失败:', err);
        return null;
      }
    },
    '获取用户资产信息失败',
    null
  );
}

/**
 * 获取订单统计
 */
async function fetchOrderStats(): Promise<void> {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 模拟数据
    orderStats.value = {
      pendingPayment: 1,
      paid: 0,
      shipped: 1,
      delivered: 0,
      refunding: 0
    };
  } catch (error) {
    console.error('获取订单统计失败:', error);
  }
}

onMounted(async () => {
  try {
    console.log('用户首页组件挂载');
    
    // 确保用户信息已加载
    if (!userStore.isLoggedIn) {
      console.log('用户未登录，重定向到登录页');
      router.push('/user/login');
      return;
    }
    
    // 确保UI先渲染，然后再加载数据
    // 使用Promise.allSettled确保即使某个请求失败也不会影响整体渲染
    setTimeout(async () => {
      try {
        const results = await Promise.allSettled([
          fetchUserAssets(),
          fetchOrderStats(),
          refreshRecommendations()
        ]);
        
        // 检查请求结果
        results.forEach((result, index) => {
          if (result.status === 'rejected') {
            console.error(`数据请求失败(${index}):`, result.reason);
            // 请求失败不弹出错误提示，避免影响用户体验
          }
        });
      } catch (error) {
        console.error('加载用户首页数据失败:', error);
      }
    }, 100);
  } catch (error) {
    console.error('用户首页组件挂载失败:', error);
    // 即使出错也允许组件继续渲染
  }
});
</script>

<style scoped>
.user-home-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section,
.orders-section,
.features-section,
.recommendations-section {
  margin-bottom: 20px;
}

.welcome-card,
.orders-card,
.features-card,
.recommendations-card {
  width: 100%;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
}

.section-title {
  font-size: 18px;
  margin: 0;
}

.user-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.user-avatar-area {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-basic-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  margin: 0;
  font-size: 20px;
}

.user-id {
  margin: 5px 0 0;
  color: #909399;
  font-size: 14px;
}

.user-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #409EFF;
}

.stat-label {
  margin: 5px 0 0;
  font-size: 14px;
  color: #606266;
}

.order-stats {
  display: flex;
  justify-content: space-between;
  margin: 20px 0;
}

.order-stat-item {
  text-align: center;
  flex: 1;
  position: relative;
  cursor: pointer;
  padding: 10px;
  transition: all 0.3s;
}

.order-stat-item:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
}

.order-stat-item .el-icon {
  font-size: 28px;
  color: #409EFF;
  margin-bottom: 8px;
}

.order-stat-item p {
  margin: 5px 0;
  font-size: 14px;
}

.badge {
  position: absolute;
  top: 0;
  right: 20px;
}

.recent-orders {
  margin-top: 20px;
}

.recent-orders-title {
  font-size: 16px;
  margin: 0 0 10px;
}

.feature-items {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 20px 0;
}

.feature-item {
  width: 23%;
  text-align: center;
  padding: 15px 0;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 4px;
}

.feature-item:hover {
  background-color: #f5f7fa;
}

.feature-item .el-icon {
  font-size: 28px;
  color: #409EFF;
  margin-bottom: 8px;
}

.feature-item p {
  margin: 5px 0;
  font-size: 14px;
}

.product-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 20px 0;
}

.product-item {
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 4px;
  padding: 10px;
}

.product-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 100%;
  height: 150px;
  overflow: hidden;
  border-radius: 4px;
  margin-bottom: 10px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-name {
  margin: 10px 0 5px;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  margin: 5px 0 0;
  font-size: 16px;
  font-weight: 600;
  color: #f56c6c;
}

/* 表格样式 */
:deep(.warning-row) {
  background-color: #fdf6ec;
}
</style>
