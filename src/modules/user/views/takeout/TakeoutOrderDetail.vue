<!--
  外卖订单详情页面
  展示外卖订单的详细信息，包括订单状态、商品列表、配送信息等
-->
<template>
  <div class="takeout-order-detail">
    <el-card class="order-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <h2 class="page-title">订单详情</h2>
          <el-button @click="goBack" size="small">返回订单列表</el-button>
        </div>
      </template>
      
      <div v-if="order">
        <!-- 订单状态 -->
        <div class="order-status-section">
          <div class="status-info">
            <el-tag :type="getStatusType(order.orderStatus)" size="large">
              {{ getStatusText(order.orderStatus) }}
            </el-tag>
            <div class="order-number">订单号：{{ order.orderNumber }}</div>
            <div class="order-time">下单时间：{{ formatDate(order.createdAt) }}</div>
          </div>
          
          <div class="status-actions">
            <!-- 待付款状态 -->
            <template v-if="order.orderStatus === TakeoutOrderStatus.PENDING">
              <el-button type="primary" @click="payOrder">去支付</el-button>
              <el-button @click="showCancelDialog">取消订单</el-button>
            </template>
            
            <!-- 配送中状态 -->
            <template v-else-if="order.orderStatus === TakeoutOrderStatus.DELIVERING">
              <el-button @click="confirmDelivery">确认收货</el-button>
            </template>
            
            <!-- 已完成状态 -->
            <template v-else-if="order.orderStatus === TakeoutOrderStatus.COMPLETED">
              <el-button type="primary" @click="showRateDialog">评价订单</el-button>
            </template>
          </div>
        </div>
        
        <!-- 订单进度 -->
        <div class="order-progress">
          <el-steps :active="getProgressStep()" finish-status="success">
            <el-step title="下单" :description="formatTime(order.createdAt)" />
            <el-step title="支付" :description="formatTime(order.paidAt)" />
            <el-step title="商家接单" :description="formatTime(order.acceptedAt)" />
            <el-step title="配送中" :description="formatTime(order.deliveredAt ? new Date(new Date(order.deliveredAt).getTime() - 30 * 60 * 1000).toISOString() : '')" />
            <el-step title="送达" :description="formatTime(order.deliveredAt)" />
            <el-step title="完成" :description="formatTime(order.completedAt)" />
          </el-steps>
        </div>
        
        <!-- 商家信息 -->
        <div class="merchant-section">
          <div class="section-title">商家信息</div>
          <div class="merchant-info">
            <div class="merchant-logo" v-if="order.merchant.logo">
              <img :src="order.merchant.logo" :alt="order.merchant.name" />
            </div>
            <div class="merchant-details">
              <div class="merchant-name">{{ order.merchant.name }}</div>
              <div class="merchant-contact" v-if="order.merchant.phone">
                联系电话：{{ order.merchant.phone }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 配送信息 -->
        <div class="delivery-section">
          <div class="section-title">配送信息</div>
          <div class="delivery-info">
            <div class="address-info">
              <div class="address-contact">{{ order.address.name }} {{ order.address.phone }}</div>
              <div class="address-detail">{{ order.address.province }} {{ order.address.city }} {{ order.address.district }} {{ order.address.detail }}</div>
            </div>
            <div class="delivery-time" v-if="order.estimatedDeliveryTime">
              <div class="time-label">预计送达时间</div>
              <div class="time-value">{{ formatDate(order.estimatedDeliveryTime) }}</div>
            </div>
          </div>
        </div>
        
        <!-- 订单商品 -->
        <div class="order-items-section">
          <div class="section-title">订单商品</div>
          <div class="order-items">
            <div v-for="item in order.items" :key="item.foodId || item.id" class="order-item">
              <div class="item-image">
                <img :src="item.foodImage" :alt="item.foodName" />
              </div>
              <div class="item-info">
                <div class="item-name">{{ item.foodName }}</div>
                
                <div class="item-specs" v-if="item.variants && item.variants.length > 0">
                  <div class="specs-content">
                    <span v-for="(variant, index) in item.variants" :key="variant.variantId" class="spec-item">
                      {{ variant.name }}{{ index < item.variants.length - 1 ? '，' : '' }}
                    </span>
                  </div>
                </div>
                
                <div class="item-combos" v-if="item.comboItems && item.comboItems.length > 0">
                  <div class="combos-content">
                    <div v-for="combo in item.comboItems" :key="combo.itemId" class="combo-item">
                      {{ combo.name }} x{{ combo.quantity }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="item-price">
                <div class="price">¥{{ item.price.toFixed(2) }}</div>
                <div class="quantity">x {{ item.quantity }}</div>
              </div>
              <div class="item-total">¥{{ item.totalPrice.toFixed(2) }}</div>
            </div>
          </div>
        </div>
        
        <!-- 订单金额 -->
        <div class="order-amount-section">
          <div class="amount-list">
            <div class="amount-item">
              <span class="item-label">商品金额</span>
              <span class="item-value">¥{{ getItemsTotal().toFixed(2) }}</span>
            </div>
            <div class="amount-item">
              <span class="item-label">配送费</span>
              <span class="item-value">¥{{ order.deliveryFee.toFixed(2) }}</span>
            </div>
            <div class="amount-item" v-if="order.packagingFee">
              <span class="item-label">包装费</span>
              <span class="item-value">¥{{ order.packagingFee.toFixed(2) }}</span>
            </div>
            <div class="amount-item" v-if="order.discountAmount">
              <span class="item-label">优惠金额</span>
              <span class="item-value">-¥{{ order.discountAmount.toFixed(2) }}</span>
            </div>
            <div class="amount-item total">
              <span class="item-label">实付金额</span>
              <span class="item-value">¥{{ order.payableAmount.toFixed(2) }}</span>
            </div>
          </div>
        </div>
        
        <!-- 订单备注 -->
        <div class="order-remark-section" v-if="order.remark">
          <div class="section-title">订单备注</div>
          <div class="remark-content">{{ order.remark }}</div>
        </div>
      </div>
      
      <el-empty v-else-if="!loading" description="订单不存在或已被删除" />
    </el-card>
    
    <!-- 取消订单弹窗 -->
    <el-dialog
      v-model="cancelDialogVisible"
      title="取消订单"
      width="400px"
    >
      <el-form :model="cancelForm" label-width="80px">
        <el-form-item label="取消原因" prop="reason">
          <el-select v-model="cancelForm.reason" placeholder="请选择取消原因" style="width: 100%">
            <el-option label="我不想买了" value="我不想买了" />
            <el-option label="等待时间太长" value="等待时间太长" />
            <el-option label="商品选错了" value="商品选错了" />
            <el-option label="地址信息填写错误" value="地址信息填写错误" />
            <el-option label="其他原因" value="其他原因" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmCancel" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 评价弹窗 -->
    <el-dialog
      v-model="rateDialogVisible"
      title="评价订单"
      width="500px"
    >
      <el-form :model="rateForm" label-width="80px">
        <el-form-item label="总体评分">
          <el-rate v-model="rateForm.rating" :colors="colors" show-text />
        </el-form-item>
        <el-form-item label="评价内容">
          <el-input 
            v-model="rateForm.comment" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入您的评价"
          />
        </el-form-item>
        
        <div class="food-ratings" v-if="order">
          <div class="section-title">商品评价</div>
          <div v-for="item in order.items" :key="item.id" class="food-rating-item">
            <div class="food-info">
              <img :src="item.foodImage" :alt="item.foodName" class="food-image" />
              <span class="food-name">{{ item.foodName }}</span>
            </div>
            <div class="food-rating">
              <el-rate 
                v-model="foodRatings[item.foodId.toString()]" 
                :colors="colors" 
              />
            </div>
          </div>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRating" :loading="submitting">提交评价</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useTakeoutStore } from '../../stores/takeoutStore';
import { takeoutService } from '../../service/takeoutService';
import { TakeoutOrderStatus } from '../../types';
import type { TakeoutOrder } from '../../types';

const route = useRoute();
const router = useRouter();
const takeoutStore = useTakeoutStore();

// 状态
const loading = ref(false);
const order = ref<TakeoutOrder | null>(null);
const cancelDialogVisible = ref(false);
const rateDialogVisible = ref(false);
const submitting = ref(false);

// 表单数据
const cancelForm = ref({
  reason: ''
});

const rateForm = ref({
  rating: 5,
  comment: ''
});

// 食品评分
const foodRatings = reactive<Record<string, number>>({});

// 评分颜色
const colors = {
  'scoreText.3': '#F7BA2A',
  'scoreText.2': '#F7BA2A',
  'scoreText.1': '#F7BA2A'
};

/**
 * 加载订单详情
 */
async function loadOrderDetail() {
  const orderId = route.params.id;
  if (!orderId) {
    ElMessage.error('订单ID不能为空');
    return;
  }
  
  loading.value = true;
  try {
    const result = await takeoutStore.loadOrderDetail(String(orderId));
    console.log('loadOrderDetail 获取订单详情结果:', result);
    order.value = result;
    
    if (!result) {
      ElMessage.error('订单不存在或已被删除');
      return;
    }
    
    // 初始化食品评分
    if (order.value && order.value.items) {
      order.value.items.forEach(item => {
        foodRatings[item.foodId.toString()] = 5;
      });
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
    ElMessage.error('获取订单详情失败，请稍后重试');
  } finally {
    loading.value = false;
  }
}

/**
 * 格式化日期
 * @param dateString 日期字符串
 * @returns 格式化后的日期
 */
function formatDate(dateString?: string): string {
  if (!dateString) return '暂无';
  
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
}

/**
 * 格式化时间（仅显示时间部分）
 * @param dateString 日期字符串
 * @returns 格式化后的时间
 */
function formatTime(dateString?: string): string {
  if (!dateString) return '暂无';
  
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
}

/**
 * 获取状态文本
 * @param status 订单状态
 * @returns 状态文本
 */
function getStatusText(status: TakeoutOrderStatus): string {
  // 状态文本映射
  const statusTextMap: Record<TakeoutOrderStatus, string> = {
    [TakeoutOrderStatus.PENDING]: '待支付',
    [TakeoutOrderStatus.PAID]: '已支付',
    [TakeoutOrderStatus.PROCESSING]: '处理中',
    [TakeoutOrderStatus.DELIVERING]: '配送中',
    [TakeoutOrderStatus.COMPLETED]: '已完成',
    [TakeoutOrderStatus.CANCELLED]: '已取消',
    [TakeoutOrderStatus.REFUNDING]: '退款中',
    [TakeoutOrderStatus.REFUNDED]: '已退款'
  };
  
  return statusTextMap[status] || '未知状态';
}

/**
 * 获取状态对应的标签类型
 * @param status 订单状态
 * @returns 标签类型
 */
function getStatusType(status: TakeoutOrderStatus): string {
  const typeMap: Record<TakeoutOrderStatus, string> = {
    [TakeoutOrderStatus.PENDING]: 'warning',
    [TakeoutOrderStatus.PAID]: 'info',
    [TakeoutOrderStatus.PROCESSING]: 'primary',
    [TakeoutOrderStatus.DELIVERING]: 'primary',
    [TakeoutOrderStatus.COMPLETED]: 'success',
    [TakeoutOrderStatus.CANCELLED]: 'info',
    [TakeoutOrderStatus.REFUNDING]: 'danger',
    [TakeoutOrderStatus.REFUNDED]: 'info'
  };
  
  return typeMap[status] || 'info';
}

/**
 * 获取订单进度步骤
 * @returns 当前进度步骤
 */
function getProgressStep(): number {
  if (!order.value) return 0;
  console.log('tiaoshi ',order.value, order.value.orderStatus)
  switch (order.value.orderStatus) {
    case TakeoutOrderStatus.PENDING:
      return 0;
    case TakeoutOrderStatus.PAID:
      return 1;
    case TakeoutOrderStatus.PROCESSING:
      // 即使acceptedAt为空，只要订单状态为30（商家接单），也应该显示第三步
      return 2;
    case TakeoutOrderStatus.DELIVERING:
      return 3;
    case TakeoutOrderStatus.COMPLETED:
      return 5;
    default:
      return 0;
  }
}

/**
 * 计算商品总金额
 * @returns 商品总金额
 */
function getItemsTotal(): number {
  if (!order.value) return 0;
  
  return order.value.items.reduce((sum, item) => sum + item.totalPrice, 0);
}

/**
 * 显示取消订单对话框
 */
function showCancelDialog() {
  cancelForm.value.reason = '';
  cancelDialogVisible.value = true;
}

/**
 * 确认取消订单
 */
async function confirmCancel() {
  if (!order.value) return;
  if (!cancelForm.value.reason) {
    ElMessage.warning('请选择取消原因');
    return;
  }
  
  submitting.value = true;
  try {
    await takeoutService.order.cancelOrder(order.value.orderID, {
      reason: cancelForm.value.reason
    });
    
    ElMessage.success('订单已取消');
    cancelDialogVisible.value = false;
    // 重新加载订单
    loadOrderDetail();
  } catch (error) {
    console.error('取消订单失败:', error);
    ElMessage.error('取消订单失败，请稍后重试');
  } finally {
    submitting.value = false;
  }
}

/**
 * 去支付
 */
async function payOrder() {
  if (!order.value) return;
  
  try {
    // 获取支付信息，这里直接使用模拟
    ElMessage.success('正在跳转到支付页面...');
    
    // 模拟支付成功
    ElMessageBox.alert('模拟支付成功!', '支付提示', {
      confirmButtonText: '确定',
      callback: () => {
        loadOrderDetail();
      }
    });
  } catch (error) {
    console.error('支付失败:', error);
    ElMessage.error('支付失败，请稍后重试');
  }
}

/**
 * 确认收货
 */
async function confirmDelivery() {
  if (!order.value) return;
  
  try {
    await ElMessageBox.confirm(
      '确认已收到该订单商品？',
      '确认收货',
      {
        confirmButtonText: '确认收货',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    // 调用确认收货API
    await takeoutService.order.confirmReceived(String(order.value.orderID));
    
    ElMessage.success('已确认收货');
    // 重新加载订单
    loadOrderDetail();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认收货失败:', error);
      ElMessage.error('确认收货失败，请稍后重试');
    }
  }
}

/**
 * 显示评价对话框
 */
function showRateDialog() {
  rateForm.value.rating = 5;
  rateForm.value.comment = '';
  
  // 初始化食品评分
  if (order.value) {
    order.value.items.forEach(item => {
      foodRatings[item.foodId.toString()] = 5;
    });
  }
  
  rateDialogVisible.value = true;
}

/**
 * 提交评价
 */
async function submitRating() {
  if (!order.value) return;
  
  submitting.value = true;
  try {
    // 构建食品评分数据
    const foodRatingsList = Object.entries(foodRatings).map(([foodId, rating]) => ({
      foodId,
      rating,
      comment: ''
    }));
    
    await takeoutService.order.rateOrder({
      orderId: order.value.orderID,
      rating: rateForm.value.rating,
      comment: rateForm.value.comment,
      foodRatings: foodRatingsList
    });
    
    ElMessage.success('评价提交成功');
    rateDialogVisible.value = false;
    // 重新加载订单
    loadOrderDetail();
  } catch (error) {
    console.error('评价提交失败:', error);
    ElMessage.error('评价提交失败，请稍后重试');
  } finally {
    submitting.value = false;
  }
}

/**
 * 返回订单列表
 * 跳转回订单列表页面，并保持外卖订单类型的选择
 */
function goBack() {
  router.push('/user/orders?type=takeout');
}

// 页面加载时获取订单详情
onMounted(() => {
  loadOrderDetail();
});
</script>

<style scoped>
.takeout-order-detail {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.order-status-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.order-number,
.order-time {
  color: #606266;
  font-size: 14px;
}

.order-progress {
  margin-bottom: 30px;
}

.merchant-section,
.delivery-section,
.order-items-section,
.order-amount-section,
.order-remark-section {
  margin-bottom: 30px;
}

.merchant-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.merchant-logo {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
}

.merchant-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.merchant-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 5px;
}

.merchant-contact {
  color: #606266;
  font-size: 14px;
}

.delivery-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.address-contact {
  font-weight: 500;
  margin-bottom: 5px;
}

.address-detail {
  color: #606266;
  font-size: 14px;
}

.delivery-time {
  text-align: right;
}

.time-label {
  color: #606266;
  font-size: 14px;
  margin-bottom: 5px;
}

.time-value {
  font-weight: 500;
}

.order-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.item-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 15px;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-info {
  flex: 1;
}

.item-name {
  font-weight: 500;
  margin-bottom: 5px;
}

.item-specs,
.item-combos {
  color: #909399;
  font-size: 13px;
  margin-top: 5px;
}

.item-price {
  margin: 0 20px;
  text-align: right;
}

.price {
  font-weight: 500;
}

.quantity {
  color: #909399;
  font-size: 13px;
  margin-top: 5px;
}

.item-total {
  font-weight: bold;
  color: #f56c6c;
  min-width: 80px;
  text-align: right;
}

.amount-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.amount-item {
  display: flex;
  justify-content: space-between;
}

.amount-item.total {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
  font-weight: bold;
  font-size: 16px;
}

.amount-item.total .item-value {
  color: #f56c6c;
}

.remark-content {
  background-color: #f5f7fa;
  padding: 10px 15px;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
}

.food-ratings {
  margin-top: 20px;
}

.food-rating-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.food-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.food-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
}

.food-name {
  font-size: 14px;
}
</style>
