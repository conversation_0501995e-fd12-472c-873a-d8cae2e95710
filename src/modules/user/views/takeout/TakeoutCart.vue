<!--
  购物车页面
  显示购物车中的商品，提供数量调整、删除、结算等功能
  按商家分组展示商品，支持分别操作不同商家的商品
  结算和地址选择功能由独立组件提供
-->
<template>
  <div class="takeout-cart-page">
    <div class="page-header">
      <h2 class="page-title">我的购物车</h2>
    </div>
    
    <div class="cart-content" v-loading="loading">
      <el-empty 
        v-if="cart.items.length === 0 && !loading" 
        description="购物车空空如也，快去挑选美食吧~"
      >
        <el-button type="primary" @click="goToMerchants">去选购</el-button>
      </el-empty>
      
      <template v-else>
        <div class="cart-header">
          <div class="select-all">
            <el-checkbox 
              v-model="allSelected" 
              @change="handleSelectAllChange"
              :indeterminate="isIndeterminate"
            >
              全选
            </el-checkbox>
          </div>
          <div class="clear-cart">
            <el-button 
              type="primary" 
              @click="handleClearCart"
            >
              清空购物车
            </el-button>
          </div>
        </div>
        
        <!-- 按商家分组展示购物车商品 -->
        <div 
          v-for="group in groupedCartItems" 
          :key="group.merchantId"
          class="merchant-group"
        >
          <!-- 商家信息头部 -->
          <div class="merchant-header">
            <div class="merchant-select">
              <el-checkbox 
                v-model="merchantSelectStatus[group.merchantId]" 
                @change="(val: boolean) => handleMerchantSelectChange(group.merchantId, val)"
                :indeterminate="isMerchantIndeterminate(group.merchantId)"
              >
                <span class="merchant-name">{{ group.merchantName }}</span>
              </el-checkbox>
            </div>
            <div class="merchant-actions">
              <el-button 
                type="primary" 
                @click="(event: Event) => handleClearMerchantItems(group.merchantId, event)"
                size="small"
              >
                清空
              </el-button>
            </div>
          </div>
          
          <!-- 商家的购物车商品 -->
          <div class="merchant-items">
            <cart-item
              v-for="item in group.items"
              :key="item.id"
              :item="item"
              @update="handleItemUpdate"
              @delete="handleItemDelete"
              @select="handleItemSelect"
            />
            
            <!-- 商家配送费区域 -->
            <div class="merchant-delivery-fee" v-if="hasSelectedItemsInMerchant(group.merchantId)">
              <div class="fee-info">
                <span>配送费：</span>
                <el-tooltip>
                  <template #content>
                    <div>配送距离: {{ getMerchantDeliveryDistance(group.merchantId) }} km</div>
                    <div>配送单价: {{ deliveryPricePerKm }}元/km</div>
                  </template>
                  <el-icon><InfoFilled /></el-icon>
                </el-tooltip>
                <span class="fee-amount">¥{{ getMerchantDeliveryFee(group.merchantId).toFixed(2) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="cart-footer">
          <!-- 配送费单价调整区域 -->
          <div class="delivery-settings" v-if="isDebugMode">
            <div class="setting-title">
              <span>配送费单价设置（调试模式）</span>
              <el-tooltip content="这是开发调试模式，用于调整配送费单价参数">
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
            <div class="setting-content">
              <span>配送费单价：</span>
              <el-slider 
                v-model="deliveryPricePerKm"
                :min="1"
                :max="10"
                :step="0.5"
                :show-tooltip="true"
                :format-tooltip="(val: number) => `¥${val}/km`"
                style="width: 200px"
              />
              <span class="current-price">{{ deliveryPricePerKm }}元/公里</span>
            </div>
          </div>

          <div class="cart-summary">
            <div class="select-all">
              <el-checkbox 
                v-model="allSelected" 
                @change="handleSelectAllChange"
                :indeterminate="isIndeterminate"
              >
                全选
              </el-checkbox>
            </div>
            
            <div class="cart-total" style="color: #666;">
              <div class="selected-items-info">
                <span>已选商品 <strong>{{ selectedItems.length }}</strong> 件</span>
              </div>
              
              <!-- 费用明细 -->
              <div class="fee-details">
                <div class="fee-item">
                  <span class="fee-label">商品金额：</span>
                  <span class="fee-value">¥{{ selectedTotalPrice.toFixed(2) }}</span>
                </div>
                <div v-if="totalPackagingFee > 0" class="fee-item">
                  <span class="fee-label">包装费：</span>
                  <span class="fee-value">¥{{ totalPackagingFee.toFixed(2) }}</span>
                </div>
                <div v-if="totalDeliveryFee > 0" class="fee-item">
                  <span class="fee-label">配送费：</span>
                  <span class="fee-value">¥{{ totalDeliveryFee.toFixed(2) }}</span>
                </div>
                <!-- 促销优惠显示 -->
                <div v-if="appliedPromotions.length > 0" class="promotion-section">
                  <div v-for="promotion in appliedPromotions" :key="promotion.id" class="promotion-item fee-item">
                    <span class="fee-label promotion-name">{{ promotion.name }}：</span>
                    <span class="fee-value promotion-discount">-¥{{ promotion.discount_amount.toFixed(2) }}</span>
                  </div>
                </div>
              </div>
              
              <!-- 合计 -->
              <div class="total-section">
                <span class="total-label">合计：</span>
                <span class="total-price">¥{{ finalAmount.toFixed(2) }}</span>
              </div>
            </div>
          </div>
          
          <div class="checkout-action">
            <el-button 
              type="primary" 
              size="large"
              :disabled="selectedItems.length === 0"
              @click="handleCheckout"
            >
              去结算
            </el-button>
          </div>
        </div>
      </template>
    </div>
    
    <!-- 结算弹窗组件 -->
    <checkout-dialog
      v-model="checkoutDialogVisible"
      :total-price="selectedTotalPrice"
      :delivery-fee="deliveryFee"
      :grouped-items="selectedGroupedItems"
      :delivery-price-per-km="deliveryPricePerKm"
      :default-address="defaultAddress"
      @submit="handleCheckoutSubmit"
      @address-changed="handleAddressChanged"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { vLoading } from 'element-plus/es/components/loading/src/directive';
import { InfoFilled } from '@element-plus/icons-vue';
import CartItem from './CartItem.vue';
import CheckoutDialog from './components/CheckoutDialog.vue';
import { useTakeoutStore } from '../../stores/takeoutStore';
import { useMerchantStore } from '../../stores/merchantStore';
import type { CartItem as CartItemType } from '../../types';
import type { UserAddress } from '../../types';
import { getUserAddresses } from '../../api/address';

// 获取路由
const router = useRouter();

// 获取购物车数据存储
const takeoutStore = useTakeoutStore();
const merchantStore = useMerchantStore();

// 用户地址相关
const userAddresses = ref<UserAddress[]>([]);
const defaultAddress = ref<UserAddress | null>(null);

// 配送费计算相关
const deliveryPricePerKm = ref<number>(2); // 每公里配送费单价，可配置
const isDebugMode = ref<boolean>(true); // 调试模式，用于显示配送费调整控件

// 状态
const loading = ref(false);
const cart = computed(() => takeoutStore.cart);
const allSelected = ref(false);
const checkoutDialogVisible = ref(false);
const deliveryFee = ref(5); // 配送费，实际应该从商家信息中获取

// 促销相关数据
const appliedPromotions = ref<any[]>([]);
const promotionDiscount = ref(0);

// 商家选中状态
const merchantSelectStatus = reactive<Record<string | number, boolean>>({});

// 计算属性

/**
 * 商家组类型定义
 */
interface MerchantGroup {
  merchantId: string | number;
  merchantName: string;
  items: CartItemType[];
  merchantDistance?: number; // 与用户地址的距离(米)
  merchantLatitude?: number; // 商家纬度
  merchantLongitude?: number; // 商家经度
}

/**
 * 按商家分组的购物车商品
 */
const groupedCartItems = computed<MerchantGroup[]>(() => {
  // 按商家ID分组商品
  const groups: Record<string | number, MerchantGroup> = {};
  
  // 确保购物车商品存在且为数组
  if (!cart.value.items || !Array.isArray(cart.value.items)) {
    return [];
  }
  
  cart.value.items.forEach(item => {
    // 确保 merchantId 不为 undefined
    const merchantId = item.merchant_id?.toString() || 'unknown';
    const merchantName = item.merchant_name || '未知商家';
    
    if (!groups[merchantId]) {
      groups[merchantId] = {
        merchantId,
        merchantName,
        items: [],
      };
    }
    
    groups[merchantId].items.push(item);
  });
  
  // 将对象转换为数组
  return Object.values(groups);
});

// 这些计算属性将在后续扩展中使用，现在保留
// /**
//  * 按商家计算的商品数量
//  */
// const merchantItemCounts = computed(() => {
//   const counts: Record<string | number, number> = {};
//   
//   Object.entries(groupedCartItems.value).forEach(([merchantId, group]) => {
//     counts[merchantId] = group.items.length;
//   });
//   
//   return counts;
// });
// 
// /**
//  * 按商家计算的已选商品数量
//  */
// const merchantSelectedCounts = computed(() => {
//   const counts: Record<string | number, number> = {};
//   
//   Object.entries(groupedCartItems.value).forEach(([merchantId, group]) => {
//     counts[merchantId] = group.items.filter(item => item.selected).length;
//   });
//   
//   return counts;
// });

/**
 * 所有已选中的商品
 */
const selectedItems = computed(() => {
  // 确保items存在且是数组
  console.log('cart.value.items', cart.value.items);
  if (!cart.value.items || !Array.isArray(cart.value.items)) {
    return [];
  }
  return cart.value.items.filter(item => item.selected);
});

/**
 * 选中的商品按商家分组 - 返回Record格式以匹配CheckoutDialog期望的数据结构
 */
const selectedGroupedItems = computed<Record<string, any>>(() => {
  // 先将选中商品按商家分组
  const selectedMerchantGroups: { [key: string]: any } = {};
  
  selectedItems.value.forEach(item => {
    const merchantId = item.merchant_id?.toString() || 'unknown';
    if (!selectedMerchantGroups[merchantId]) {
      selectedMerchantGroups[merchantId] = {
        merchant_id: merchantId,
        merchant_name: item.merchant_name || '',
        items: [],
        merchant_distance: getMerchantDeliveryDistance(merchantId),
        // 添加商家经纬度信息 - 使用CheckoutDialog期望的字段名
        merchant_latitude: item.merchant_latitude,
        merchant_longitude: item.merchant_longitude
      };
    }
    selectedMerchantGroups[merchantId].items.push(item);
  });
  
  console.log('selectedGroupedItems:', selectedMerchantGroups);
  return selectedMerchantGroups;
});

/**
 * 已选商品总价
 * 直接使用 takeoutStore 中的 selectedTotalPrice 计算属性
 */
const selectedTotalPrice = computed(() => {
  // 使用 store 中的计算属性来确保一致性
  return takeoutStore.selectedTotalPrice;
});

/**
 * 总包装费
 */
const totalPackagingFee = computed(() => {
  return selectedItems.value.reduce((sum, item) => {
    const packagingFee = item.packaging_fee || 0;
    return sum + (packagingFee * item.quantity);
  }, 0);
});

/**
 * 总配送费
 */
const totalDeliveryFee = computed(() => {
  return Object.values(selectedGroupedItems.value).reduce((sum, group) => {
    return sum + getMerchantDeliveryFee(group.merchant_id);
  }, 0);
});

/**
 * 最终支付金额（扣除促销优惠）
 */
const finalAmount = computed(() => {
  return Math.max(0, selectedTotalPrice.value + totalPackagingFee.value + totalDeliveryFee.value - promotionDiscount.value);
});

/**
 * 全选状态是否为不确定状态
 */
const isIndeterminate = computed(() => {
  if (!cart.value.items || cart.value.items.length === 0) {
    return false;
  }
  return selectedItems.value.length > 0 && selectedItems.value.length < cart.value.items.length;
});

// 已在CheckoutDialog组件中处理地址相关逻辑

// 监听购物车商品变化，更新全选状态
watch(() => cart.value.items, () => {
  updateSelectAllState();
  updateMerchantSelectStatus();
}, { deep: true });

// 监听已选商品变化，自动计算促销优惠
watch(selectedItems, async () => {
  await calculatePromotions();
}, { deep: true });

/**
 * 加载购物车数据
 */
async function loadCart() {
  loading.value = true;
  try {
    console.log('开始加载购物车...');
    await takeoutStore.loadCart();
    console.log('购物车加载完成:', cart.value);
    updateSelectAllState();
  } catch (error) {
    console.error('加载购物车失败:', error);
    ElMessage.error('加载购物车失败，请稍后重试');
  } finally {
    loading.value = false;
  }
}

/**
 * 处理结算提交
 * @param orderData 订单数据，来自CheckoutDialog组件，已完成所有验证
 */
async function handleCheckoutSubmit(orderData: any) {
  try {
    console.log('TakeoutCart 接收到的提交数据:', orderData);
    
    // CheckoutDialog已完成所有验证，直接提交订单
    const order = await takeoutStore.createMultiMerchantOrder(orderData);

    console.log('创建订单结果:', order);
    
    if (order) {
      ElMessage.success('订单创建成功，即将跳转到支付页面');
      checkoutDialogVisible.value = false;
      
      // 跳转到支付页面
      router.push(`/user/takeout/payment/${order.orderID}`);
    } else {
      ElMessage.error('订单创建失败');
    }
  } catch (error) {
    console.error('创建订单失败:', error);
    ElMessage.error('创建订单失败，请稍后重试');
  }
}

/**
 * 更新全选状态
 */
function updateSelectAllState() {
  // 确保购物车商品列表存在且非空
  if (!cart.value.items || !Array.isArray(cart.value.items) || cart.value.items.length === 0) {
    allSelected.value = false;
    return;
  }
  
  allSelected.value = cart.value.items.every(item => !!item && item.selected === true);
}

/**
 * 更新商家商品选择状态
 */
function updateMerchantSelectStatus(): void {
  // 由于groupedCartItems是数组，直接遍历数组元素
  groupedCartItems.value.forEach((group) => {
    const merchantId = group.merchantId.toString();
    // 如果商家下所有商品都被选中，则商家选中状态为true
    merchantSelectStatus[merchantId] = group.items.length > 0 && 
      group.items.every((item: CartItemType) => item.selected);
  });
}

/**
 * 判断商家商品是否为部分选中状态
 */
function isMerchantIndeterminate(merchantId: string | number): boolean {
  // 使用find方法查找对应商家组，避免直接使用merchantId作为数组索引
  const group = groupedCartItems.value.find(g => g.merchantId === merchantId);
  if (!group || group.items.length === 0) return false;
  
  const selectedCount = group.items.filter(item => item.selected).length;
  return selectedCount > 0 && selectedCount < group.items.length;
}

/**
 * 处理全选/取消全选
 */
async function handleSelectAllChange(value: boolean) {
  console.log('handleSelectAllChange', value);
  // 确保购物车商品列表存在且非空
  if (!cart.value.items || !Array.isArray(cart.value.items) || cart.value.items.length === 0) return;
  
  try {
    console.log(`全选状态变更为: ${value}`);
    
    // 根据ID类型分组处理
    const stringIds: string[] = [];
    const numberIds: number[] = [];
    
    cart.value.items.forEach(item => {
      if (typeof item.id === 'string') {
        stringIds.push(item.id);
      } else if (typeof item.id === 'number') {
        numberIds.push(item.id);
      }
    });
    
    // 如果有字符串ID，使用字符串数组
    if (stringIds.length > 0) {
      const result = await takeoutStore.selectCartItems(stringIds, value);
      if (!result) {
        ElMessage.error('选择购物车商品失败，请稍后重试');
      } else {
        // 将对应商品的选中状态更新
        console.log('选中状态更新:', result);
        cart.value.items.forEach(item => {
          if (typeof item.id === 'string' && stringIds.includes(item.id)) {
            item.selected = value;
          }
        });
      }
    }
    
    // 如果有数字ID，使用数字数组
    if (numberIds.length > 0) {
      const result = await takeoutStore.selectCartItems(numberIds, value);
      if (!result) {
        ElMessage.error('选择购物车商品失败，请稍后重试');
      } else {
        // 将对应商品的选中状态更新
        console.log('选中状态更新:', result);
        cart.value.items.forEach(item => {
          if (typeof item.id === 'number' && numberIds.includes(item.id)) {
            item.selected = value;
          }
        });
      }
    }
    
    // 更新所有商家的选中状态
    Object.keys(merchantSelectStatus).forEach((merchantId: string) => {
      merchantSelectStatus[merchantId] = value;
    });
  } catch (error) {
    console.error('处理全选失败:', error);
    ElMessage.error('操作失败，请稍后重试');
  }
}

/**
 * 处理商家商品全选/取消全选
 */
async function handleMerchantSelectChange(merchantId: string | number, value: boolean): Promise<void> {
  // 使用find方法查找对应商家组，避免直接使用merchantId作为数组索引
  const group = groupedCartItems.value.find(g => g.merchantId === merchantId);
  if (!group || group.items.length === 0) return;
  
  try {
    console.log(`商家 ${merchantId} 全选状态变更为: ${value}`);
    
    // 根据ID类型分组处理
    const stringIds: string[] = [];
    const numberIds: number[] = [];
    
    group.items.forEach((item: CartItemType) => {
      if (typeof item.id === 'string') {
        stringIds.push(item.id);
      } else if (typeof item.id === 'number') {
        numberIds.push(item.id);
      }
    });
    
    // 更新UI状态
    merchantSelectStatus[merchantId.toString()] = value;
    group.items.forEach((item: CartItemType) => item.selected = value);
    
    // 调用API更新选中状态
    if (stringIds.length > 0) {
      await takeoutStore.selectCartItems(stringIds, value);
    }
    
    if (numberIds.length > 0) {
      await takeoutStore.selectCartItems(numberIds, value);
    }
    
    // 重新计算全选状态
    updateSelectAllState();
  } catch (error) {
    console.error('处理商家选中状态时出错:', error);
    ElMessage.error('更新商品选中状态失败');
    ElMessage.error('操作失败，请稍后重试');
  }
}

/**
 * 处理商品更新
 */
function handleItemUpdate(item: CartItemType) {
  //console.log('handleItemUpdate', item);
  // 商品更新逻辑已在CartItem组件中处理。
  // 这通常意味着 CartItem 组件自身或通过调用 store action 来更新了商品信息 (如数量或选中状态)
  // 这些更新应该已经响应式地反映在 cart.value.items 中。

  if (!item || !item.id) {
    console.warn('收到无效的商品更新:', item);
    return;
  }
  console.log('商品已更新:', item);

  // 当商品信息（如数量、选中状态）更新后，相关的状态和计算属性需要反映这些变化。
  
  // 1. 更新全选和半选状态
  //    Vue 的 watch (deep: true) on cart.value.items 应该已经处理了此逻辑。
  //    但在此处显式调用可以确保在 handleItemUpdate 执行时状态是最新的，
  //    以防事件触发顺序或异步操作导致 watch 尚未完全响应。
  updateSelectAllState();
  
  // 2. 更新各商家的商品选择状态
  //    同样，这通常由 watch 覆盖，但显式调用提供即时性。
  updateMerchantSelectStatus();

  // 3. selectedItems 的数量和 selectedTotalPrice (总金额) 是计算属性。
  //    它们会自动根据 cart.value.items (及其内部项目的 selected, quantity, price 属性) 的变化而重新计算。
  //    此处访问它们将得到最新的值。
  //    下面的日志记录用于确认这些值已按预期更新。
  console.log(
    `购物车状态更新后: 已选商品 ${selectedItems.value.length} 件, 总金额 ¥${selectedTotalPrice.value.toFixed(2)}`
  );

  // 如果UI未正确反映这些更新，请检查：
  // - CartItem.vue 是否正确修改了 item 对象的响应式属性 (或通过 store action)。
  // - takeoutStore 中的 action 是否正确更新了 cart 状态。
  // - cart.value.items 是否确实是响应式的，并且是计算属性的依赖来源。
}

/**
 * 处理商品删除
 */
async function handleItemDelete(itemId: string | number) {
  if (!itemId) {
    console.warn('尝试删除无效的商品ID:', itemId);
    return;
  }
  
  try {
    console.log('删除商品:', itemId);
    await takeoutStore.removeFromCart(itemId);
    ElMessage.success('商品已从购物车移除');
  } catch (error) {
    console.error('删除商品失败:', error);
    ElMessage.error('删除商品失败，请稍后重试');
  }
}

/**
 * 处理商品选择状态变更
 */
async function handleItemSelect(itemId: string | number, selected: boolean) {
  console.log('handleItemSelect', itemId, selected);
  if (!itemId) {
    console.warn('尝试选择无效的商品ID:', itemId);
    return;
  }
  
  try {
    console.log(`商品 ${itemId} 选择状态变更为: ${selected}`);
    
    // 根据ID类型处理
    if (typeof itemId === 'string') {
      const result = await takeoutStore.selectCartItems([itemId], selected);
      console.log('选择购物车商品结果:', result);
      if (!result) {
        ElMessage.error('选择购物车商品失败，请稍后重试');
      } else {
        // 将对应商品的选中状态更新
        console.log('选中状态更新:', result);
        cart.value.items.forEach(item => {
          if (typeof item.id === 'string' && item.id === itemId) {
            item.selected = selected;
          }
        });
      }
    } else if (typeof itemId === 'number') {
      const result = await takeoutStore.selectCartItems([itemId], selected);
      console.log('选择购物车商品结果:', result);
      if (!result) {
        ElMessage.error('选择购物车商品失败，请稍后重试');
      } else {
        // 将对应商品的选中状态更新
        console.log('选中状态更新:', result);
        cart.value.items.forEach(item => {
          if (typeof item.id === 'number' && item.id === itemId) {
            item.selected = selected;
          }
        });
      }
    } else {
      console.error('无法处理的ID类型:', typeof itemId);
    }
  } catch (error) {
    console.error('更新商品选择状态失败:', error);
    ElMessage.error('更新商品选择状态失败，请稍后重试');
  }
}

/**
 * 处理清空购物车
 */
async function handleClearCart() {
  // 确保购物车商品列表存在且非空
  if (!cart.value.items || !Array.isArray(cart.value.items) || cart.value.items.length === 0) {
    ElMessage.info('购物车已经为空');
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      '确定要清空购物车吗？此操作不可恢复。',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    // 根据ID类型分组处理
    const stringIds: string[] = [];
    const numberIds: number[] = [];
    
    cart.value.items.forEach(item => {
      if (typeof item.id === 'string') {
        stringIds.push(item.id);
      } else if (typeof item.id === 'number') {
        numberIds.push(item.id);
      }
    });
    
    console.log('清空购物车，移除商品:', { stringIds, numberIds });
    
    // 如果有字符串ID，使用字符串数组
    if (stringIds.length > 0) {
      await takeoutStore.removeFromCart(stringIds);
    }
    
    // 如果有数字ID，使用数字数组
    if (numberIds.length > 0) {
      await takeoutStore.removeFromCart(numberIds);
    }
    
    ElMessage.success('购物车已清空');
  } catch (error) {
    // 用户取消操作或操作失败
    if (error instanceof Error) {
      console.error('清空购物车失败:', error);
    }
  }
}

/**
 * 处理清空某个商家的购物车商品
 */
async function handleClearMerchantItems(merchantId: string | number, event: Event) {
  // 阻止事件冒泡，避免触发商家选中状态改变
  event.stopPropagation();
  
  // 使用find方法查找对应商家组，避免直接使用merchantId作为数组索引
  const group = groupedCartItems.value.find(g => g.merchantId === merchantId);
  if (!group || group.items.length === 0) {
    ElMessage.info('该商家购物车已经为空');
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要清空${group.merchantName}的所有商品吗？此操作不可恢复。`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    // 根据ID类型分组处理
    const stringIds: string[] = [];
    const numberIds: number[] = [];
    
    group.items.forEach((item: CartItemType) => {
      if (typeof item.id === 'string') {
        stringIds.push(item.id);
      } else if (typeof item.id === 'number') {
        numberIds.push(item.id);
      }
    });
    
    console.log(`清空商家 ${merchantId} 购物车，移除商品:`, { stringIds, numberIds });
    
    // 如果有字符串ID，使用字符串数组
    if (stringIds.length > 0) {
      await takeoutStore.removeFromCart(stringIds);
    }
    
    // 如果有数字ID，使用数字数组
    if (numberIds.length > 0) {
      await takeoutStore.removeFromCart(numberIds);
    }
    
    ElMessage.success(`已清空${group.merchantName}的所有商品`);
  } catch (error) {
    // 用户取消操作或操作失败
    if (error instanceof Error) {
      console.error(`清空商家 ${merchantId} 购物车失败:`, error);
    }
  }
}

/**
 * 点击结算按钮
 */
async function handleCheckout() {
  // 验证是否有选中商品
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请选择需要结算的商品');
    return;
  }
  
  // 计算促销优惠
  await calculatePromotions();
  
  // 显示结算弹窗
  checkoutDialogVisible.value = true;
}

/**
 * 处理地址变化
 * @param newAddress 新选择的地址
 */
function handleAddressChanged(newAddress: UserAddress | null) {
  console.log('地址变化，重新计算配送费:', newAddress);
  if (newAddress) {
    defaultAddress.value = newAddress;
    // 地址变化后，selectedGroupedItems会自动重新计算merchantDistance
    // 因为selectedGroupedItems依赖于getMerchantDeliveryDistance，而该函数使用defaultAddress
  }
}

/**
 * 跳转到商家列表页面
 */
function goToMerchants() {
  router.push('/user/takeout/merchants');
}

// 已移动到AddressSelectDialog组件中

/**
 * 计算促销优惠
 */
async function calculatePromotions() {
  if (selectedItems.value.length === 0) {
    appliedPromotions.value = [];
    promotionDiscount.value = 0;
    return;
  }
  
  try {
    // 按商家分组计算促销
    const merchantGroups = Object.values(selectedGroupedItems.value);
    let totalDiscount = 0;
    const allAppliedPromotions: any[] = [];
    
    for (const group of merchantGroups) {
      // 计算该商家的订单金额
      const merchantTotal = group.items.reduce((total: number, item: any) => {
        return total + (item.price * item.quantity);
      }, 0);
      
      // 模拟满减活动检查（实际应该调用API获取商家促销活动）
      if (merchantTotal >= 50) {
        const discount = Math.min(10, merchantTotal * 0.1); // 满50减10或10%优惠
        totalDiscount += discount;
        allAppliedPromotions.push({
          id: Date.now() + Math.random(),
          name: `${group.merchant_name}满减优惠`,
          discount_amount: discount,
          merchant_id: group.merchant_id
        });
      }
    }
    
    appliedPromotions.value = allAppliedPromotions;
    promotionDiscount.value = totalDiscount;
    
  } catch (error) {
    console.error('Failed to calculate promotions:', error);
  }
}

// 页面加载时获取购物车数据
onMounted(() => {
  loadCart().then(() => {
    // 初始化商家选中状态
    updateMerchantSelectStatus();
    // 加载用户地址信息
    loadUserAddresses();
  });
});

/**
 * 加载用户地址列表
 */
async function loadUserAddresses() {
  try {
    const response = await getUserAddresses() as {
      list: UserAddress[];
    } | UserAddress[];
    
    const addressList = Array.isArray(response) ? response : (response?.list || []);
    userAddresses.value = addressList;
    
    // 设置默认地址
    defaultAddress.value = addressList.find(addr => addr.is_default) || addressList[0] || null;
    console.log('已加载用户地址', defaultAddress.value);
  } catch (error) {
    console.error('加载用户地址失败:', error);
  }
}

/**
 * 计算两个经纬度点之间的距离（单位：公里）
 * 使用 Haversine 公式计算球面距离
 */
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  if (!lat1 || !lon1 || !lat2 || !lon2) return 0;
  
  const R = 6371; // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c;
  
  // 数值计算结果直接格式化为两位小数
  return Math.round(distance * 100) / 100; // 保留两位小数
}

/**
 * 判断商家是否有选中的商品
 */
function hasSelectedItemsInMerchant(merchantId: string | number): boolean {
  const group = groupedCartItems.value.find(g => g.merchantId === merchantId);
  if (!group) return false;
  
  return group.items.some(item => item.selected);
}

/**
 * 获取商家配送距离（公里）
 */
function getMerchantDeliveryDistance(merchantId: string | number): number {
  // 获取商家商品分组
  const group = groupedCartItems.value.find(g => g.merchantId === merchantId);
  if (!group || !defaultAddress.value || group.items.length === 0) return 0;
  
  // 用户地址经纬度
  const userLat = defaultAddress.value.location_latitude || 0;
  const userLng = defaultAddress.value.location_longitude || 0;
  
  if (!userLat || !userLng) {
    // 如果用户地址没有经纬度，返回默认距离
    return 1;
  }
  
  // 优先从商家store中获取商家信息
  const merchant = merchantStore.getMerchant(Number(merchantId));
  let merchantLat = 0;
  let merchantLng = 0;
  
  if (merchant && merchant.latitude && merchant.longitude) {
    // 从商家store获取经纬度
    merchantLat = merchant.latitude;
    merchantLng = merchant.longitude;
  } else {
    // 备用方案：从购物车商品中获取商家的经纬度信息
    const firstItem = group.items[0];
    merchantLat = firstItem.merchant_latitude || 0;
    merchantLng = firstItem.merchant_longitude || 0;
  }
  
  if (!merchantLat || !merchantLng) {
    // 如果商家没有经纬度信息，返回默认距离
    return 1;
  }
  
  // 计算距离，确保传入的都是number类型
  return calculateDistance(
    Number(userLat), 
    Number(userLng), 
    Number(merchantLat), 
    Number(merchantLng)
  );
}

/**
 * 获取商家配送费
 */
function getMerchantDeliveryFee(merchantId: string | number): number {
  // 获取配送距离
  const distance = getMerchantDeliveryDistance(merchantId);
  
  // 距离乘以单价，向上取整到小数点后一位
  let fee = distance * deliveryPricePerKm.value;
  // 直接返回数值结果，无需parseFloat
  return Math.ceil(fee * 10) / 10;
}
</script>

<style scoped>
.takeout-cart-page {
  padding: 20px 0;
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.cart-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 20px;
}

.cart-header {
  display: flex;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.select-all {
  flex-shrink: 0;
  margin-right: 20px;
}

.merchant-info {
  flex: 1;
}

.merchant-name {
  font-weight: 500;
}

.cart-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 15px;
  margin-top: 15px;
  border-top: 1px solid #ebeef5;
}

.cart-summary {
  display: flex;
  align-items: center;
}

.cart-total {
  margin-left: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 14px;
}

.selected-items-info {
  color: #666;
}

.fee-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
  border-bottom: 1px dashed #e4e7ed;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.fee-label {
  color: #909399;
}

.fee-value {
  color: #606266;
  font-weight: 500;
}

.promotion-section {
  margin-top: 5px;
}

.promotion-item .fee-label {
  color: #67c23a;
}

.promotion-item .fee-value {
  color: #67c23a;
}

.total-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  margin-top: 5px;
}

.total-label {
  font-size: 16px;
}

.total-price {
  color: #f56c6c;
  font-size: 20px;
  font-weight: bold;
}

.checkout-form {
  padding: 10px;
}

/* 配送费区域样式 */
.merchant-delivery-fee {
  padding: 10px 0;
  margin-top: 10px;
  border-top: 1px dashed #ebeef5;
}

.fee-info {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 14px;
}

.fee-info .el-icon {
  margin: 0 5px;
  color: #909399;
  cursor: help;
}

.fee-amount {
  margin-left: auto;
  color: #f56c6c;
  font-weight: 500;
}

/* 配送费调试区域样式 */
.delivery-settings {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;
}

.setting-title {
  display: flex;
  align-items: center;
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}

.setting-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.current-price {
  min-width: 75px;
  color: #f56c6c;
  font-weight: 500;
}

.no-address {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #909399;
}

.selected-address {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.address-info {
  flex: 1;
}

.address-contact {
  font-weight: 500;
  margin-bottom: 5px;
}

.address-detail {
  color: #606266;
  font-size: 14px;
}

.checkout-summary {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px dashed #dcdfe6;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.summary-item.total {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px dashed #dcdfe6;
  font-weight: bold;
}

.total-amount {
  color: #f56c6c;
  font-size: 18px;
}

.address-list {
  max-height: 300px;
  overflow-y: auto;
}

.address.cart-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
}

.merchant-group {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
}

.merchant-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f5f7fa;
  padding: 12px 15px;
  border-bottom: 1px solid #ebeef5;
}

.merchant-name {
  font-weight: 600;
  color: #303133;
  margin-left: 5px;
}

.merchant-items {
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.address-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.address-item {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  transition: all 0.3s;
}

.address-item:hover {
  border-color: #409eff;
}

.address-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.address-contact {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.contact-name {
  font-weight: 500;
}

.contact-phone {
  color: #606266;
}

.address-detail {
  color: #606266;
  font-size: 14px;
}
</style>
