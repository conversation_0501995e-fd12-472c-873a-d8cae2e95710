<!--
  食品卡片组件
  展示食品的基本信息，并提供添加到购物车的功能
  2025-05-27 变更：支持展示食品规格（variants）
  2025-05-27 变更：美化规格信息，添加商品数量加减功能
-->
<template>
  <div class="food-card">
    <div class="food-image">
      <img :src="food.image" :alt="food.name" />
      <div class="food-tags" v-if="food.isRecommended">
        <span class="tag-recommended">推荐</span>
      </div>
    </div>
    <div class="food-info">
      <h3 class="food-name">{{ food.name }}</h3>
      <div class="food-desc" v-if="food.description">
        {{ food.description }}
      </div>
      <!-- 打包费和备餐时间信息 -->
      <div class="food-extra-info">
        <div class="extra-item" v-if="food.packagingFee && food.packagingFee > 0">
          <el-icon class="extra-icon"><Box /></el-icon>
          <span class="extra-text">打包费 ¥{{ food.packagingFee.toFixed(2) }}</span>
        </div>
        <div class="extra-item" v-if="food.preparationTime && food.preparationTime > 0">
          <el-icon class="extra-icon"><Clock /></el-icon>
          <span class="extra-text">{{ food.preparationTime }}分钟</span>
        </div>
      </div>
      <div class="food-rating" v-if="food.ratingCount > 0">
        <span class="rating">{{ food.rating.toFixed(1) }}</span>
        <span class="rating-count">{{ food.ratingCount }}+ 评价</span>
      </div>
      <div class="food-attrs" v-if="food.attributes && Object.keys(food.attributes).length > 0">
        <span 
          v-for="(value, key) in food.attributes" 
          :key="key" 
          class="attr-tag"
        >
          {{ key }}: {{ value }}
        </span>
      </div>
      <div class="food-price-action">
        <div class="food-price">
          <span class="current-price">¥{{ food.price.toFixed(2) }}</span>
          <span class="original-price" v-if="food.originalPrice">¥{{ food.originalPrice.toFixed(2) }}</span>
        </div>
        <div class="food-action">
          <!-- 没有规格时的加减购物车控件 -->
          <div class="cart-control" v-if="!food.hasVariants && !food.hasCombo && food.isActive && (food.stock > 0 || food.stock === -1)">
            <el-button 
              type="primary" 
              circle 
              size="small"
              class="cart-btn"
              @click.stop="decreaseQuantity"
              v-if="getBasicFoodQuantity() > 0"
            >
              <el-icon><Minus /></el-icon>
            </el-button>
            <span class="quantity-display" v-if="getBasicFoodQuantity() > 0">
              {{ getBasicFoodQuantity() }}
            </span>
            <el-button 
              type="primary" 
              circle 
              size="small"
              class="cart-btn"
              @click.stop="addToCart"
              :disabled="!food.isActive || (food.stock !== -1 && food.stock <= 0)"
            >
              <el-icon><Plus /></el-icon>
            </el-button>
          </div>
          <!-- 有规格或套餐时只显示加号 -->
          <!-- <el-button 
            v-else
            type="primary" 
            circle 
            size="small"
            @click.stop="addToCart"
            :disabled="!food.isActive || (food.stock !== -1 && food.stock <= 0)"
          >
            <el-icon><Plus /></el-icon>
          </el-button> -->
        </div>
      </div>
      <!-- 规格信息展示，美化版 -->
      <div class="food-variants" v-if="food.variants && food.variants.length > 0">
        <div
          v-for="variant in food.variants"
          :key="variant.id"
          class="variant-item"
          :class="{'variant-active': variant.is_default}"
        >
          <div class="variant-info">
            <div class="variant-header">
              <span class="variant-name">{{ variant.name }}</span>
              <span class="variant-tag" v-if="variant.is_default">默认</span>
            </div>
            <div class="variant-details">
              <span class="variant-price">¥{{ variant.price.toFixed(2) }}</span>
              <span class="variant-original-price" v-if="variant.original_price && variant.original_price > 0">¥{{ variant.original_price.toFixed(2) }}</span>
              <span class="variant-stock" v-if="variant.stock !== undefined && variant.stock !== null && variant.stock >= 0">
                库存: {{ variant.stock }}
              </span>
            </div>
          </div>
          <!-- 规格加减购物车操作 -->
          <div class="variant-action">
            <div class="cart-control" v-if="variant.is_active !== false && (variant.stock > 0 || variant.stock === -1)">
              <el-button 
                type="primary" 
                circle 
                size="small"
                class="cart-btn"
                @click.stop="decreaseVariantQuantity(variant)"
                v-if="getVariantQuantity(variant.id) > 0"
              >
                <el-icon><Minus /></el-icon>
              </el-button>
              <span class="quantity-display" v-if="getVariantQuantity(variant.id) > 0">
                {{ getVariantQuantity(variant.id) }}
              </span>
              <el-button 
                type="primary" 
                circle 
                size="small"
                class="cart-btn"
                @click.stop="addVariantToCart(variant)"
                :disabled="variant.stock !== -1 && variant.stock <= 0"
              >
                <el-icon><Plus /></el-icon>
              </el-button>
            </div>
            <span class="variant-soldout" v-if="variant.stock === 0">已售罄</span>
          </div>
        </div>
      </div>
      <div class="food-stock" v-if="food.stock > 0 && food.stock <= 10">
        剩余 {{ food.stock }} {{ food.unit || '份' }}
      </div>
      <div class="food-soldout" v-else-if="food.stock === 0">
        已售罄
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import { Plus, Minus, Box, Clock } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import type { Food } from '../../types';
import { useTakeoutStore } from '../../stores/takeoutStore';

// 定义属性
const props = defineProps<{
  food: Food;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'showDetail', food: Food): void;
  (e: 'cartUpdated'): void; // 添加购物车更新事件
}>();

// 获取takeout store
const takeoutStore = useTakeoutStore();

// 计算当前购物车中的商品数量
const getBasicFoodQuantity = () => {
  // 找到当前购物车中的该商品（不带规格）
  const cartItem = takeoutStore.cart?.items.find(item => 
    item.foodId === props.food.id && (!item.variants || item.variants.length === 0)
  );
  return cartItem ? cartItem.quantity : 0;
};

// 获取基础商品的购物车项ID
const getBasicFoodCartItemId = () => {
  const cartItem = takeoutStore.cart?.items.find(item => 
    item.foodId === props.food.id && (!item.variants || item.variants.length === 0)
  );
  return cartItem ? cartItem.id : null;
};

// 获取特定规格在购物车中的数量
const getVariantQuantity = (variantId: string | number) => {
  // 找到当前购物车中的该商品（带指定规格）
  const cartItem = takeoutStore.cart?.items.find(item => 
    item.foodId === props.food.id && 
    item.variants && 
    item.variants.some(v => v.variantId === variantId)
  );
  return cartItem ? cartItem.quantity : 0;
};

// 获取特定规格商品的购物车项ID
const getVariantCartItemId = (variantId: string | number) => {
  const cartItem = takeoutStore.cart?.items.find(item => 
    item.foodId === props.food.id && 
    item.variants && 
    item.variants.some(v => v.variantId === variantId)
  );
  return cartItem ? cartItem.id : null;
};

// /**
//  * 显示食品详情
//  */
// function showFoodDetail() {
//   // 如果食品有规格或套餐项，显示详情
//   if (props.food.hasVariants || props.food.hasCombo) {
//     emit('showDetail', props.food);
//   } else {
//     // 否则直接加入购物车
//     addToCart();
//   }
// }

/**
 * 添加基础商品到购物车
 */
async function addToCart() {
  if (!props.food.isActive) {
    ElMessage.warning('该商品已下架');
    return;
  }
  
  // 检查库存，-1表示无限库存
  if (props.food.stock !== -1 && props.food.stock <= 0) {
    ElMessage.warning('该商品已售罄');
    return;
  }
  
  // 如果食品有规格或套餐项，显示详情
  if (props.food.hasVariants || props.food.hasCombo) {
    emit('showDetail', props.food);
    return;
  }
  
  try {
    // 简单商品直接加入购物车
    const result = await takeoutStore.addToCart({
      food_id: props.food.id,
      quantity: 1
    });
    
    if (result) {
      ElMessage.success('已加入购物车');
      emit('cartUpdated'); // 通知购物车已更新
    } else {
      ElMessage.error('加入购物车失败');
    }
  } catch (error) {
    console.error('加入购物车失败:', error);
    ElMessage.error('加入购物车失败，请稍后重试');
  }
}

/**
 * 从购物车减少基础商品数量
 */
async function decreaseQuantity() {
  const cartItemId = getBasicFoodCartItemId();
  if (!cartItemId) {
    ElMessage.error('购物车项不存在');
    return;
  }
  
  // 获取当前数量
  const currentQuantity = getBasicFoodQuantity();
  
  try {
    // 如果当前数量为1，直接从购物车删除
    if (currentQuantity === 1) {
      console.log(`商品数量为0，自动删除商品:`, cartItemId);
      const result = await takeoutStore.removeFromCart(cartItemId);
      
      if (result) {
        emit('cartUpdated'); // 通知购物车已更新
      } else {
        ElMessage.error('删除商品失败');
      }
    } else {
      // 否则更新数量
      const result = await takeoutStore.updateCartItemQuantity(cartItemId, currentQuantity - 1);
      
      if (result) {
        emit('cartUpdated'); // 通知购物车已更新
      } else {
        ElMessage.error('更新购物车失败');
      }
    }
  } catch (error) {
    console.error('更新购物车失败:', error);
    ElMessage.error('更新购物车失败，请稍后重试');
  }
}

/**
 * 添加规格商品到购物车
 * 如果商品已经在购物车中，则增加数量
 */
async function addVariantToCart(variant: any) {
  // 检查库存，-1表示无限库存
  if (variant.stock !== -1 && variant.stock <= 0) {
    ElMessage.warning('该规格已售罄');
    return;
  }
  
  try {
    // 获取商品在购物车中的ID
    const cartItemId = getVariantCartItemId(variant.id);
    let result;
    
    if (cartItemId) {
      // 如果商品已在购物车中，获取当前数量并加1
      const currentQuantity = getVariantQuantity(variant.id);
      result = await takeoutStore.updateCartItemQuantity(cartItemId, currentQuantity + 1);
      
      if (result) {
        ElMessage.success('已增加商品数量');
        emit('cartUpdated'); // 通知购物车已更新
      } else {
        ElMessage.error('更新购物车失败');
      }
    } else {
      // 商品不在购物车中，添加新商品
      result = await takeoutStore.addToCart({
        food_id: props.food.id,
        quantity: 1,
        variant_id: variant.id
      });
      
      if (result) {
        ElMessage.success('已加入购物车');
        emit('cartUpdated'); // 通知购物车已更新
      } else {
        ElMessage.error('加入购物车失败');
      }
    }
  } catch (error) {
    console.error('操作购物车失败:', error);
    ElMessage.error('操作购物车失败，请稍后重试');
  }
}

/**
 * 从购物车减少规格商品数量
 */
async function decreaseVariantQuantity(variant: any) {
  const cartItemId = getVariantCartItemId(variant.id);
  if (!cartItemId) {
    ElMessage.error('购物车项不存在');
    return;
  }
  
  // 获取当前数量
  const currentQuantity = getVariantQuantity(variant.id);
  
  try {
    // 如果当前数量为1，直接从购物车删除
    if (currentQuantity === 1) {
      console.log(`规格商品数量为0，自动删除商品:`, cartItemId);
      const result = await takeoutStore.removeFromCart(cartItemId);
      
      if (result) {
        emit('cartUpdated'); // 通知购物车已更新
      } else {
        ElMessage.error('删除商品失败');
      }
    } else {
      // 否则更新数量
      const result = await takeoutStore.updateCartItemQuantity(cartItemId, currentQuantity - 1);
      
      if (result) {
        emit('cartUpdated'); // 通知购物车已更新
      } else {
        ElMessage.error('更新购物车失败');
      }
    }
  } catch (error) {
    console.error('更新购物车失败:', error);
    ElMessage.error('更新购物车失败，请稍后重试');
  }
}
</script>

<style scoped>
.food-card {
  display: flex;
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: all 0.3s;
}

.food-card:hover {
  background-color: #f5f7fa;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.food-image {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  margin-right: 15px;
  flex-shrink: 0;
}

.food-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.food-tags {
  position: absolute;
  top: 5px;
  left: 5px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.tag-recommended {
  background-color: #f56c6c;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.food-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.food-name {
  color: #666;
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.food-desc {
  color: #606266;
  font-size: 13px;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.food-extra-info {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 8px;
}

.extra-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.extra-icon {
  font-size: 12px;
  color: #909399;
}

.extra-text {
  font-size: 12px;
  color: #606266;
}

.food-rating {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 8px;
}

.rating {
  color: #ff9900;
  font-weight: bold;
}

.rating-count {
  color: #909399;
  font-size: 12px;
}

.food-attrs {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 8px;
}

.attr-tag {
  background-color: #f2f6fc;
  color: #606266;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.food-price-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.food-price {
  display: flex;
  align-items: center;
  gap: 5px;
}

.current-price {
  color: #f56c6c;
  font-weight: bold;
  font-size: 16px;
}

.original-price {
  color: #909399;
  font-size: 12px;
  text-decoration: line-through;
}

.food-stock {
  color: #e6a23c;
  font-size: 12px;
  margin-top: 5px;
}

.food-soldout, .variant-soldout {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 5px;
}

.food-variants {
  margin-top: 10px;
  border-top: 1px dashed #ebeef5;
  padding-top: 10px;
}

.variant-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin-bottom: 5px;
  border-radius: 6px;
  background-color: #f9f9f9;
  transition: all 0.3s;
}

.variant-active {
  background-color: #ecf5ff;
  border-left: 3px solid #409eff;
}

.variant-info {
  flex: 1;
}

.variant-header {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.variant-name {
  color: #666;
  font-weight: bold;
  font-size: 14px;
}

.variant-tag {
  background-color: #409eff;
  color: white;
  font-size: 10px;
  padding: 1px 5px;
  border-radius: 3px;
  margin-left: 5px;
}

.variant-details {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.variant-price {
  color: #f56c6c;
  font-weight: bold;
}

.variant-original-price {
  color: #909399;
  font-size: 12px;
  text-decoration: line-through;
}

.variant-stock {
  color: #909399;
  font-size: 12px;
}

.variant-action {
  display: flex;
  align-items: center;
}

.cart-control {
  display: flex;
  align-items: center;
  gap: 5px;
}

.quantity-display {
  color: #666;
  width: 20px;
  text-align: center;
  font-weight: bold;
}

.cart-btn {
  flex-shrink: 0;
}
</style>
