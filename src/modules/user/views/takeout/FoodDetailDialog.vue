<!--
  食品详情对话框组件
  展示食品详细信息，并提供规格选择、套餐选择等功能
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="food?.name || '食品详情'"
    width="90%"
    top="5vh"
    custom-class="food-detail-dialog"
    :before-close="handleClose"
  >
    <div class="food-detail-content" v-if="food">
      <div class="food-basic-info">
        <div class="food-image">
          <el-carousel 
            height="200px" 
            indicator-position="inside"
            v-if="food.images && food.images.length > 0"
          >
            <el-carousel-item v-for="(image, index) in food.images" :key="index">
              <img :src="image" :alt="food.name" />
            </el-carousel-item>
          </el-carousel>
          <img v-else :src="food.image" :alt="food.name" />
        </div>
        
        <div class="food-info">
          <h2 class="food-name">{{ food.name }}</h2>
          
          <div class="food-desc" v-if="food.description">
            {{ food.description }}
          </div>
          
          <div class="food-meta">
            <div class="food-price">
              <span class="current-price">¥{{ food.price.toFixed(2) }}</span>
              <span class="original-price" v-if="food.originalPrice">¥{{ food.originalPrice.toFixed(2) }}</span>
            </div>
            
            <div class="food-rating" v-if="food.ratingCount > 0">
              <el-rate
                v-model="food.rating"
                disabled
                text-color="#ff9900"
              />
              <span class="rating-count">{{ food.ratingCount }}+ 评价</span>
            </div>
            
            <div class="food-sales">
              月售 {{ food.salesCount }} {{ food.unit || '份' }}
            </div>
          </div>
          
          <div class="food-tags" v-if="food.tags && food.tags.length > 0">
            <el-tag 
              v-for="tag in food.tags" 
              :key="tag" 
              size="small" 
              effect="plain"
              class="tag-item"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </div>
      
      <!-- 规格选择 -->
      <div class="variant-selection" v-if="variantGroups.length > 0">
        <div 
          v-for="group in variantGroups" 
          :key="group.id" 
          class="variant-group"
        >
          <div class="group-header">
            <span class="group-name">{{ group.name }}</span>
            <span class="group-required" v-if="group.required">必选</span>
          </div>
          
          <div class="group-options">
            <el-radio-group 
              v-model="selectedVariants[group.id]" 
              v-if="!group.multiSelect"
              @change="calculateTotalPrice"
            >
              <el-radio 
                v-for="option in group.options" 
                :key="option.id"
                :label="option.id"
                :disabled="!option.isActive"
              >
                <span class="option-name">{{ option.name }}</span>
                <span class="option-price" v-if="option.price > 0">+¥{{ option.price.toFixed(2) }}</span>
              </el-radio>
            </el-radio-group>
            
            <el-checkbox-group 
              v-model="selectedMultiVariants[group.id]" 
              v-else
              @change="(val: any) => handleMultiSelectChange(group, val)"
            >
              <el-checkbox 
                v-for="option in group.options" 
                :key="option.id"
                :label="option.id"
                :disabled="
                  !option?.isActive || 
                  (group && group.max !== undefined && 
                  selectedMultiVariants[group.id]?.length >= group.max && 
                  !selectedMultiVariants[group.id]?.includes(option.id))
                "
              >
                <span class="option-name">{{ option.name }}</span>
                <span class="option-price" v-if="option.price > 0">+¥{{ option.price.toFixed(2) }}</span>
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>
      
      <!-- 套餐选择 -->
      <div class="combo-selection" v-if="comboGroups.length > 0">
        <div 
          v-for="group in comboGroups" 
          :key="group.id" 
          class="combo-group"
        >
          <div class="group-header">
            <span class="group-name">{{ group.name }}</span>
            <span class="group-required" v-if="group.required">必选</span>
          </div>
          
          <div class="group-items">
            <div 
              v-for="item in group.items" 
              :key="item.id"
              class="combo-item"
              :class="{ disabled: !item.isActive }"
            >
              <div class="combo-item-image" v-if="item.image">
                <img :src="item.image" :alt="item.name" />
              </div>
              
              <div class="combo-item-info">
                <div class="combo-item-name">{{ item.name }}</div>
                <div class="combo-item-price">
                  <span v-if="item.price > 0">+¥{{ item.price.toFixed(2) }}</span>
                  <span v-if="item.originalPrice && item.originalPrice > item.price" class="original-price">
                    ¥{{ item.originalPrice.toFixed(2) }}
                  </span>
                </div>
              </div>
              
              <div class="combo-item-action">
                <el-input-number 
                  v-model="selectedComboItems[item.id]" 
                  :min="0"
                  :max="getComboItemMaxQuantity(group, item)"
                  size="small"
                  controls-position="right"
                  @change="calculateTotalPrice"
                  :disabled="!item.isActive"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="food-detail-footer">
      <div class="quantity-selector">
        <span class="quantity-label">数量</span>
        <el-input-number 
          v-model="quantity" 
          :min="1" 
          :max="food?.stock || 99"
          size="small"
          @change="calculateTotalPrice"
        />
      </div>
      
      <div class="total-price">
        <span class="price-label">总计：</span>
        <span class="price-amount">¥{{ totalPrice.toFixed(2) }}</span>
      </div>
      
      <div class="action-buttons">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleAddToCart" 
          :disabled="!isValid || (food?.stock === 0)"
          :loading="submitting"
        >
          {{ food?.stock === 0 ? '已售罄' : '加入购物车' }}
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeUnmount } from 'vue';
import { ElMessage } from 'element-plus';
import { useTakeoutStore } from '../../stores/takeoutStore';
import type { Food, VariantGroup, ComboGroup } from '../../types';

const props = defineProps<{
  visible: boolean;
  food: Food | null;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'added'): void;
}>();

// 获取takeout store
const takeoutStore = useTakeoutStore();

// 本地状态
const dialogVisible = ref(props.visible);
const variantGroups = ref<VariantGroup[]>([]);
const comboGroups = ref<ComboGroup[]>([]);
const selectedVariants = ref<Record<string | number, string | number>>({});
const selectedMultiVariants = ref<Record<string | number, (string | number)[]>>({});
const selectedComboItems = ref<Record<string | number, number>>({});
const quantity = ref(1);
const totalPrice = ref(0);
const submitting = ref(false);

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
});

// 监听dialogVisible变化，同步到父组件
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal);
});

// 监听food变化，重置选择
watch(() => props.food, async (newFood) => {
  if (newFood) {
    // 重置状态
    resetSelections();
    
    // 如果食品有规格，加载规格
    if (newFood.hasVariants) {
      try {
        const variants = await takeoutStore.loadFoodVariants(newFood.id);
        variantGroups.value = variants;
        
        // 为每个规格组设置默认选择
        variantGroups.value.forEach(group => {
          if (!group.multiSelect) {
            // 对于单选，选择默认选项或第一个可用选项
            const defaultOption = group.options.find(opt => opt.isDefault && opt.isActive);
            if (defaultOption) {
              selectedVariants.value[group.id] = defaultOption.id;
            } else {
              const firstActive = group.options.find(opt => opt.isActive);
              if (firstActive) {
                selectedVariants.value[group.id] = firstActive.id;
              }
            }
          } else {
            // 对于多选，初始化空数组
            selectedMultiVariants.value[group.id] = [];
            
            // 如果有默认选项，添加到选择中
            const defaultOptions = group.options.filter(opt => opt.isDefault && opt.isActive);
            if (defaultOptions.length > 0) {
              selectedMultiVariants.value[group.id] = defaultOptions.map(opt => opt.id);
            }
          }
        });
      } catch (error) {
        console.error('加载食品规格失败:', error);
      }
    }
    
    // 如果食品有套餐项，加载套餐项
    if (newFood.hasCombo) {
      try {
        const comboItems = await takeoutStore.loadFoodComboItems(newFood.id);
        comboGroups.value = comboItems;
        
        // 初始化套餐选择
        comboGroups.value.forEach(group => {
          group.items.forEach(item => {
            selectedComboItems.value[item.id] = item.isDefault && item.isActive ? 1 : 0;
          });
        });
      } catch (error) {
        console.error('加载食品套餐项失败:', error);
      }
    }
    
    // 计算初始总价
    calculateTotalPrice();
  }
});

/**
 * 关闭对话框前的处理
 */
function handleClose() {
  dialogVisible.value = false;
}

/**
 * 重置选择状态
 */
function resetSelections() {
  variantGroups.value = [];
  comboGroups.value = [];
  selectedVariants.value = {};
  selectedMultiVariants.value = {};
  selectedComboItems.value = {};
  quantity.value = 1;
  totalPrice.value = 0;
}

/**
 * 处理多选规格变化
 * @param group 规格组
 * @param selectedOptions 已选选项
 */
function handleMultiSelectChange(group: VariantGroup, selectedOptions: (string | number)[]) {
  // 验证选择是否满足最小和最大限制
  if (group.min && selectedOptions.length < group.min) {
    ElMessage.warning(`请至少选择 ${group.min} 个选项`);
  }
  
  calculateTotalPrice();
}

/**
 * 获取套餐项的最大可选数量
 * @param group 套餐组
 * @param item 套餐项
 * @returns 最大可选数量
 */
function getComboItemMaxQuantity(group: ComboGroup, item: any): number {
  if (!item.isActive) return 0;
  
  // 如果没有限制，返回99
  if (!group.max) return 99;
  
  // 计算当前已选择的总数量
  const currentTotal = Object.entries(selectedComboItems.value)
    .filter(([itemId]) => {
      // 找到该项属于哪个组
      const belongsToGroup = group.items.some(groupItem => groupItem.id.toString() === itemId);
      return belongsToGroup;
    })
    .reduce((sum, [_, qty]) => sum + (qty as number), 0);
  
  // 计算当前项可以再选择的数量
  const currentItemQty = selectedComboItems.value[item.id] || 0;
  return group.max - currentTotal + currentItemQty;
}

/**
 * 计算总价格
 */
function calculateTotalPrice() {
  if (!props.food) return;
  
  // 基础价格
  let price = props.food.price;
  
  // 添加规格价格
  for (const groupId in selectedVariants.value) {
    const optionId = selectedVariants.value[groupId];
    const group = variantGroups.value.find(g => g.id.toString() === groupId.toString());
    if (group) {
      const option = group.options.find(o => o.id.toString() === optionId.toString());
      if (option) {
        price += option.price;
      }
    }
  }
  
  // 添加多选规格价格
  for (const groupId in selectedMultiVariants.value) {
    const optionIds = selectedMultiVariants.value[groupId];
    const group = variantGroups.value.find(g => g.id.toString() === groupId.toString());
    if (group) {
      optionIds.forEach(optionId => {
        const option = group.options.find(o => o.id.toString() === optionId.toString());
        if (option) {
          price += option.price;
        }
      });
    }
  }
  
  // 添加套餐项价格
  for (const itemId in selectedComboItems.value) {
    const itemQty = selectedComboItems.value[itemId];
    if (itemQty > 0) {
      // 查找该套餐项
      let item = null;
      for (const group of comboGroups.value) {
        item = group.items.find(i => i.id.toString() === itemId.toString());
        if (item) break;
      }
      
      if (item) {
        price += item.price * itemQty;
      }
    }
  }
  
  // 乘以数量
  totalPrice.value = price * quantity.value;
}

/**
 * 验证是否满足所有必选条件
 */
const isValid = computed(() => {
  // 检查必选规格组
  for (const group of variantGroups.value) {
    if (group.required) {
      if (!group.multiSelect) {
        // 单选必须有选择
        if (!selectedVariants.value[group.id]) {
          return false;
        }
      } else {
        // 多选必须满足最小选择数量
        const selected = selectedMultiVariants.value[group.id] || [];
        if (group.min && selected.length < group.min) {
          return false;
        }
      }
    }
  }
  
  // 检查必选套餐组
  for (const group of comboGroups.value) {
    if (group.required) {
      // 计算该组已选择的数量
      const selectedCount = group.items.reduce((sum, item) => {
        return sum + (selectedComboItems.value[item.id] || 0);
      }, 0);
      
      // 检查是否满足最小选择数量
      if (group.min && selectedCount < group.min) {
        return false;
      }
    }
  }
  
  return true;
});

/**
 * 处理添加到购物车
 */
async function handleAddToCart() {
  if (!props.food) return;
  
  if (!isValid.value) {
    ElMessage.warning('请完成必选项的选择');
    return;
  }
  
  submitting.value = true;
  
  try {
    // 构建要添加到购物车的数据
    const data: any = {
      food_id: props.food.id,
      quantity: quantity.value
    };
    
    // 添加规格
    if (variantGroups.value.length > 0) {
      // 收集所有选择的规格（单选和多选）
      const allVariantIds: (string | number)[] = [];
      
      // 收集单选规格
      Object.values(selectedVariants.value).forEach(id => {
        allVariantIds.push(id);
      });
      
      // 收集多选规格
      Object.values(selectedMultiVariants.value).forEach(ids => {
        ids.forEach(id => allVariantIds.push(id));
      });
      
      // 如果有规格，添加到数据中
      if (allVariantIds.length > 0) {
        data.variant_ids = allVariantIds;
      }
    }
    
    // 添加套餐项
    if (comboGroups.value.length > 0) {
      const comboItems: Array<{ itemId: string | number; quantity: number }> = [];
      
      for (const itemId in selectedComboItems.value) {
        const quantity = selectedComboItems.value[itemId];
        if (quantity > 0) {
          comboItems.push({
            itemId,
            quantity
          });
        }
      }
      
      if (comboItems.length > 0) {
        data.combo_items = comboItems;
      }
    }
    
    // 调用API添加到购物车
    const result = await takeoutStore.addToCart(data);
    
    if (result) {
      ElMessage.success('已加入购物车');
      dialogVisible.value = false;
      emit('added');
    } else {
      ElMessage.error('加入购物车失败');
    }
  } catch (error) {
    console.error('添加到购物车失败:', error);
    ElMessage.error('添加到购物车失败，请稍后重试');
  } finally {
    submitting.value = false;
  }
}

// 组件卸载前清理
onBeforeUnmount(() => {
  resetSelections();
});
</script>

<style scoped>
.food-detail-dialog {
  max-width: 600px;
}

.food-detail-content {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 10px;
}

.food-basic-info {
  margin-bottom: 20px;
}

.food-image {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 15px;
}

.food-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.food-name {
  margin: 0 0 10px 0;
  font-size: 20px;
  font-weight: 600;
}

.food-desc {
  color: #606266;
  font-size: 14px;
  margin-bottom: 15px;
  line-height: 1.5;
}

.food-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
}

.food-price {
  display: flex;
  align-items: center;
  gap: 5px;
}

.current-price {
  color: #f56c6c;
  font-weight: bold;
  font-size: 18px;
}

.original-price {
  color: #909399;
  font-size: 14px;
  text-decoration: line-through;
}

.food-rating {
  display: flex;
  align-items: center;
  gap: 5px;
}

.rating-count {
  color: #909399;
  font-size: 13px;
}

.food-sales {
  color: #606266;
  font-size: 13px;
}

.food-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.tag-item {
  margin-right: 0;
}

.variant-selection,
.combo-selection {
  margin-bottom: 20px;
}

.variant-group,
.combo-group {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.group-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.group-name {
  font-size: 16px;
  font-weight: 600;
}

.group-required {
  color: #f56c6c;
  font-size: 12px;
  background-color: #fef0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.group-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.option-name {
  margin-right: 5px;
}

.option-price {
  color: #f56c6c;
  font-size: 13px;
}

.group-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.combo-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.combo-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.combo-item-image {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 10px;
}

.combo-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.combo-item-info {
  flex: 1;
}

.combo-item-name {
  font-size: 14px;
  margin-bottom: 5px;
}

.combo-item-price {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #f56c6c;
  font-size: 13px;
}

.combo-item-action {
  margin-left: 10px;
}

.food-detail-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.quantity-label {
  font-size: 14px;
  color: #606266;
}

.total-price {
  font-size: 14px;
}

.price-amount {
  color: #f56c6c;
  font-weight: bold;
  font-size: 18px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

:deep(.el-checkbox), :deep(.el-radio) {
  height: auto;
  margin-right: 0;
}
</style>
