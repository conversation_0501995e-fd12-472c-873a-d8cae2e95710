<!--
  商家详情页面
  展示商家信息、食品分类和食品列表，支持食品搜索、加入购物车等功能
-->
<template>
  <div class="merchant-detail-page">
    <!-- 商家基本信息 -->
    <el-card class="merchant-info-card" v-if="merchant" v-loading="loading">
      <div class="merchant-header">
        <div class="merchant-logo">
          <img :src="merchant.logo" :alt="merchant.name" />
        </div>
        
        <div class="merchant-basic">
          <h1 class="merchant-name">{{ merchant.name }}</h1>
          
          <div class="merchant-rating">
            <el-rate
              v-model="merchant.rating"
              disabled
              text-color="#ff9900"
            />
            <span class="rating-count">{{ merchant.ratingCount }}+ 评价</span>
          </div>
          
          <div class="merchant-meta">
            <div class="meta-item">
              <el-icon><Timer /></el-icon>
              <span>{{ merchant.deliveryTime }}</span>
            </div>
            <div class="meta-item">
              <el-icon><Bicycle /></el-icon>
              <span>配送费 ¥{{ merchant.deliveryFee?.toFixed?.(2) || '0.00' }}</span>
            </div>
            <div class="meta-item">
              <el-icon><Money /></el-icon>
              <span>起送 ¥{{ merchant.minDeliveryAmount?.toFixed?.(2) || '0.00' }}</span>
            </div>
          </div>
          
          <div class="merchant-notice" v-if="merchant.description">
            <el-icon><Bell /></el-icon>
            <span>{{ merchant.description }}</span>
          </div>
        </div>
        
        <!-- 优惠券展示区域 -->
        <div class="merchant-coupons" v-if="availableCoupons.length > 0 || couponsLoading">
          <div class="coupons-header">
            <el-icon><Ticket /></el-icon>
            <span class="coupons-title">店铺优惠券</span>
          </div>
          
          <!-- 加载状态 -->
           <div v-if="couponsLoading" class="coupons-loading">
             <el-skeleton :rows="2" animated />
           </div>
           
           <!-- 优惠券列表 -->
           <div v-else-if="availableCoupons.length > 0" class="coupons-list">
            <div 
              v-for="coupon in availableCoupons.slice(0, 3)" 
              :key="coupon.id"
              class="coupon-item"
              :class="{ 'disabled': !coupon.can_claim }"
            >
              <div class="coupon-left">
                <div class="amount">¥{{ coupon.amount }}</div>
                <div class="condition">满{{ coupon.min_order_amount }}可用</div>
              </div>
              <div class="coupon-right">
                <div class="coupon-name">{{ coupon.name }}</div>
                <div class="expire-time">{{ formatCouponExpireTime(coupon.end_time) }}</div>
                <el-button 
                  v-if="coupon.can_claim"
                  @click="handleClaimCoupon(coupon.id)"
                  :loading="claimingCouponId === coupon.id"
                  size="small"
                  type="primary"
                  class="claim-btn"
                >
                  {{ coupon.claim_button_text || '立即领取' }}
                </el-button>
                <span v-else class="claimed-text">{{ getClaimStatusText(coupon) }}</span>
              </div>
            </div>
            
            <!-- 查看更多按钮 -->
            <div class="view-more" v-if="availableCoupons.length > 3">
              <el-button type="primary" link @click="goToCouponCenter">查看更多优惠券</el-button>
            </div>
          </div>
        </div>
        
        <!-- <div class="merchant-status">
          <el-tag size="large" type="success" v-if="merchant.isOpen && !merchant.isPaused">营业中</el-tag>
          <el-tag size="large" type="info" v-else>休息中</el-tag>
        </div> -->
      </div>
    </el-card>
    
    <!-- 食品分类和列表 -->
    <div class="food-container" v-loading="foodsLoading">
      <div class="food-sidebar">
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索食品"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        
        <div class="category-list">
          <div 
            v-for="category in categories" 
            :key="category.id"
            class="category-item"
            :class="{ active: currentCategory === category.id }"
            @click="selectCategory(category.id)"
          >
            {{ category.name }}
          </div>
        </div>
      </div>
      
      <div class="food-content">
        <template v-if="filteredFoodsByCategory.length > 0">
          <div 
            v-for="category in filteredFoodsByCategory" 
            :key="category.id"
            class="food-category-section"
            :id="`category-${category.id}`"
          >
            <div class="category-header">
              <h3 class="category-name">{{ category.name }}</h3>
              <div class="category-desc" v-if="category.description">
                {{ category.description }}
              </div>
            </div>
            
            <div class="food-list">
              <food-card
                v-for="food in category.foods"
                :key="food.id"
                :food="food"
                @cart-updated="handleCartUpdated"
              />
            </div>
          </div>
        </template>
        
        <el-empty v-else description="暂无相关食品" />
      </div>
    </div>
    
    <!-- 浮动购物车 -->
    <div class="floating-cart" @click="goToCart">
      <div class="cart-icon">
        <el-badge :value="cartItemCount" :hidden="cartItemCount === 0">
          <el-icon :size="24"><ShoppingCart /></el-icon>
        </el-badge>
      </div>
      <div class="cart-info">
        <div class="cart-total">¥{{ cartTotal.toFixed(2) }}</div>
        <div class="cart-text">购物车</div>
      </div>
    </div>
    
    <!-- 食品详情弹窗 -->
    <!-- <food-detail-dialog
      v-if="currentFood"
      v-model:visible="foodDetailVisible"
      :food="currentFood"
      @added="handleFoodAdded"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Timer, Search, Bell, Money, Bicycle, ShoppingCart, Ticket } from '@element-plus/icons-vue';
import FoodCard from './FoodCard.vue';
// import FoodDetailDialog from './FoodDetailDialog.vue';
import { useTakeoutStore } from '../../stores/takeoutStore';
import { useMerchantStore } from '../../stores/merchantStore';
import { getMerchantFoods } from '@/modules/user/api/takeout';
import { getMerchantAvailableCoupons, claimCoupon } from '@/modules/user/api/coupon';
import type { CouponTemplate } from '@/modules/user/types/coupon';

// 类型定义
interface Merchant {
  id: string | number;
  name: string;
  logo: string;
  rating: number;
  ratingCount: number;
  deliveryTime: string;
  deliveryFee: number;
  minDeliveryAmount: number;
  description?: string;
  address?: string;
  distance?: number;
  isOpen: boolean;
  isPaused: boolean;
}

interface TakeoutCategory {
  id: string | number;
  name: string;
  description?: string;
  image?: string;
  sort?: number;
}



// 前端组件使用的食品结构
interface Food {
  id: string | number;
  name: string;
  categoryId: string | number; // 注意命名与后端不同
  merchantId: string | number;
  price: number;
  originalPrice?: number;
  image: string;
  description?: string;
  isActive: boolean;
  stock: number;
  unit?: string;
  salesCount: number;
  ratingCount: number;
  rating: number;
  isRecommended: boolean;
  attributes?: Record<string, string>;
  hasVariants: boolean;
  isCombination: boolean;
  hasCombo: boolean;
  createdAt: string;
  updatedAt: string;
  packagingFee?: number; // 打包费
  preparationTime?: number; // 备餐时间（分钟）
  // 保留后端原始属性
  category_id?: string | number;
  category_name?: string;
  original_price?: number;
  min_price?: number;
  max_price?: number;
  brief?: string;
  sold_out?: boolean;
  has_variants?: boolean;
  is_combination?: boolean;
  tags?: string[];
  packaging_fee?: number;
  preparation_time?: number;
  daily_limit?: number;
  total_sold?: number;
  status?: number;
  audit_status?: number;
  is_recommend?: boolean;
  is_spicy?: boolean;
  created_at?: string;
}

// 获取路由
const route = useRoute();
const router = useRouter();

// 获取takeout store
const takeoutStore = useTakeoutStore();
const merchantStore = useMerchantStore();

// 状态
const loading = ref(false);
const foodsLoading = ref(false);
const merchant = ref<Merchant | null>(null);
const categories = ref<TakeoutCategory[]>([]);
const foodsByCategory = ref<Map<string | number, any[]>>(new Map());
const currentCategory = ref<string | number | null>(null);
const searchKeyword = ref('');
// 优惠券相关状态
const availableCoupons = ref<CouponTemplate[]>([]);
const couponsLoading = ref(false);
const claimingCouponId = ref<string | number | null>(null);
// const foodDetailVisible = ref(false);
// const currentFood = ref<any | null>(null);

// 计算属性
const merchantId = computed(() => {
  return typeof route.params.id === 'string' ? route.params.id : String(route.params.id);
});

const cartItemCount = computed(() => {
  return takeoutStore.cartItemCount;
});

const cartTotal = computed(() => {
  return takeoutStore.cart.totalPrice;
});

// 过滤后的食品分类
const filteredFoodsByCategory = computed(() => {
  const result: { id: string | number; name: string; description?: string; foods: Food[] }[] = [];
  
  // 如果有搜索关键词，搜索所有分类中的食品
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    
    // 遍历所有分类
    for (const [categoryId, foods] of foodsByCategory.value.entries()) {
      // 过滤匹配关键词的食品
      const filteredFoods = foods.filter(food => 
        food.name.toLowerCase().includes(keyword) || 
        (food.description && food.description.toLowerCase().includes(keyword))
      );
      
      // 如果该分类下有匹配的食品，添加到结果中
      if (filteredFoods.length > 0) {
        const category = categories.value.find(c => c.id === categoryId);
        if (category) {
          result.push({
            id: category.id,
            name: category.name,
            description: category.description,
            foods: filteredFoods
          });
        }
      }
    }
  } 
  // 如果选择了特定分类，只显示该分类的食品
  else if (currentCategory.value) {
    const foods = foodsByCategory.value.get(currentCategory.value) || [];
    const category = categories.value.find(c => c.id === currentCategory.value);
    
    if (category) {
      result.push({
        id: category.id,
        name: category.name,
        description: category.description,
        foods
      });
    }
  } 
  // 否则显示所有分类的食品
  else {
    for (const category of categories.value) {
      const foods = foodsByCategory.value.get(category.id) || [];
      
      if (foods.length > 0) {
        result.push({
          id: category.id,
          name: category.name,
          description: category.description,
          foods
        });
      }
    }
  }
  
  return result;
});

/**
 * 加载商家可用优惠券
 */
async function loadMerchantCoupons() {
  if (!merchantId.value) return;
  
  couponsLoading.value = true;
  try {
    const response = await getMerchantAvailableCoupons(merchantId.value);
    console.log('商家优惠券数据:', response);
    
    // 解包Axios响应数据
    const responseData = response.data as any;
    console.log('优惠券数组:', responseData?.coupons);
    
    // 处理响应数据 - 适配后端返回的数据结构
    if (responseData && responseData.coupons && Array.isArray(responseData.coupons)) {
      // 后端返回的是 coupons 数组，需要添加 can_claim 属性
      const processedCoupons = responseData.coupons.map((coupon: any) => {
        const processed = {
          ...coupon,
          can_claim: coupon.status === 1 && new Date(coupon.end_time) > new Date(), // 根据状态和过期时间判断是否可领取
          user_claimed_count: 0, // 默认用户未领取
          claimed_count: 0 // 默认已领取数量
        };
        console.log('处理后的优惠券:', processed);
        return processed;
      });
      
      availableCoupons.value = processedCoupons;
      console.log('设置的优惠券数组:', availableCoupons.value);
    } else if (responseData && responseData.available_coupons) {
      // 兼容原有的数据结构
      availableCoupons.value = responseData.available_coupons;
      console.log('使用兼容数据结构:', availableCoupons.value);
    } else {
      console.log('没有找到优惠券数据，设置为空数组');
      availableCoupons.value = [];
    }
  } catch (error) {
    console.error('获取商家优惠券失败:', error);
    availableCoupons.value = [];
  } finally {
    couponsLoading.value = false;
    console.log('优惠券加载完成，最终数组长度:', availableCoupons.value.length);
  }
}

/**
 * 处理优惠券领取
 * @param couponId 优惠券ID
 */
async function handleClaimCoupon(couponId: string | number) {
  claimingCouponId.value = couponId;
  try {
    const response = await claimCoupon({ coupon_id: couponId });
    console.log('领取优惠券成功:', response);
    
    ElMessage.success('领取成功！');
    
    // 更新优惠券状态
    const coupon = availableCoupons.value.find(c => c.id === couponId);
    if (coupon) {
      coupon.can_claim = false;
      coupon.user_claimed_count = (coupon.user_claimed_count || 0) + 1;
      coupon.claimed_count = (coupon.claimed_count || 0) + 1;
    }
  } catch (error: any) {
    console.error('领取优惠券失败:', error);
    
    // 根据错误码显示不同的提示信息
    if (error.code === 40004) {
      ElMessage.error('优惠券已抢完');
    } else if (error.code === 40005) {
      ElMessage.error('您已领取过此优惠券');
    } else if (error.code === 40006) {
      ElMessage.error('优惠券已过期');
    } else {
      ElMessage.error(error.message || '领取失败，请重试');
    }
  } finally {
    claimingCouponId.value = null;
  }
}

/**
 * 格式化优惠券过期时间
 * @param endTime 结束时间
 * @returns 格式化后的时间字符串
 */
function formatCouponExpireTime(endTime: string): string {
  const end = new Date(endTime);
  const now = new Date();
  const diffTime = end.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays <= 0) {
    return '已过期';
  } else if (diffDays === 1) {
    return '今日过期';
  } else if (diffDays <= 7) {
    return `${diffDays}天后过期`;
  } else {
    return end.toLocaleDateString();
  }
}

/**
 * 获取优惠券领取状态文本
 * @param coupon 优惠券对象
 * @returns 状态文本
 */
function getClaimStatusText(coupon: CouponTemplate): string {
  // 检查是否过期
  const now = new Date();
  const endTime = new Date(coupon.end_time);
  if (now > endTime) {
    return '已过期';
  }
  
  // 检查用户是否已领取（根据后端返回的 status_text）
  const couponWithStatus = coupon as any; // 使用类型断言处理可能不在接口中定义的字段
  if (couponWithStatus.status_text === '已使用' || couponWithStatus.status_text === '已领取') {
    return '已领取';
  }
  
  // 检查是否已抢完
  if (coupon.user_claimed_count && coupon.per_user_limit && coupon.user_claimed_count >= coupon.per_user_limit) {
    return '已领取';
  }
  if (coupon.claimed_count && coupon.total_limit && coupon.claimed_count >= coupon.total_limit) {
    return '已抢完';
  }
  
  // 检查优惠券状态
  if (coupon.status !== 1) {
    return '不可领取';
  }
  
  return '不可领取';
}

/**
 * 跳转到优惠券中心
 */
function goToCouponCenter() {
  router.push('/user/takeout/coupon-center');
}

/**
 * 加载商家信息
 */
async function loadMerchant() {
  loading.value = true;
  try {
    // 优先从商家store获取商家信息
    let merchantInfo = merchantStore.getMerchant(Number(merchantId.value));
    
    if (!merchantInfo) {
      // 如果store中没有，则从API加载
      const result = await takeoutStore.loadMerchantDetail(String(merchantId.value));
      console.log('从API加载的商家信息:', result);
      
      if (result) {
        // 将商家信息添加到store中
         await merchantStore.addMerchant({
           ...result,
           latitude: (result as any).latitude,
           longitude: (result as any).longitude
         });
        
        merchantInfo = result;
      }
    }
    
    merchant.value = merchantInfo;
    
    if (!merchantInfo) {
      ElMessage.error('商家不存在或已下架');
      //router.replace('/user/takeout/merchants');
    }
  } catch (error) {
    console.error('获取商家信息失败:', error);
    ElMessage.error('获取商家信息失败，请稍后重试');
  } finally {
    loading.value = false;
  }
}

/**
 * 加载商家分类
 */
async function loadCategories() {
  try {
    const result = await takeoutStore.loadMerchantCategories(String(merchantId.value));
    categories.value = result;
    
    // 默认选择第一个分类
    if (categories.value.length > 0 && !currentCategory.value) {
      currentCategory.value = categories.value[0].id;
    }
  } catch (error) {
    console.error('获取商家分类失败:', error);
    ElMessage.error('获取商家分类失败，请稍后重试');
  }
}



/**
 * 加载商家食品
 */
async function loadFoods() {
  foodsLoading.value = true;
  try {
    const response: any = await getMerchantFoods(String(merchantId.value));
    console.log('获取商家食品结果:', response);
    
    // 分析响应格式
    // 检查是否是Axios响应对象或直接的数据对象
    const responseData = response;
    
    if (!responseData || !responseData.list) {
      console.error('获取食品数据失败，数据格式不正确');
      ElMessage.error('获取食品数据失败，请稍后重试');
      return;
    }
    
    // 将食品按分类整理
    const foodsMap = new Map<string | number, any[]>();
    
    // 处理返回的食品列表 - 支持分页数据结构
    const foodsList = responseData.list || [];
    console.log('食品列表:', foodsList);
    if (Array.isArray(foodsList) && foodsList.length > 0) {
      // 逐个处理食品数据
      foodsList.forEach((item: any) => {
        if (!item || !item.category_id) return;
        
        const categoryId = item.category_id;
        
        // 创建前端显示所需的食品对象
        const foodItem = {
          id: item.id,
          name: item.name,
          categoryId: item.category_id,
          merchantId: merchantId.value, // 强制设置商家ID
          price: item.price || 0,
          originalPrice: item.original_price || item.price || 0,
          image: item.image || '',
          description: item.brief || '',
          isActive: item.status === 1 && !item.sold_out,
          stock: item.sold_out ? 0 : (item.daily_limit || 999),
          salesCount: item.total_sold || 0,
          hasVariants: item.has_variants || false,
          isCombination: item.is_combination || false,
          hasCombo: item.is_combination || false,
          createdAt: item.created_at || '',
          updatedAt: item.created_at || '',
          rating: 5.0,
          ratingCount: item.total_sold || 0,
          unit: '份',
          attributes: {},
          packagingFee: item.packaging_fee || 0, // 打包费
          preparationTime: item.preparation_time || 0, // 备餐时间
          // 保存原始属性
          category_id: item.category_id,
          category_name: item.category_name,
          tags: item.tags || [],
          variants: item.variants || []
        };
        
        // 如果分类不存在，创建新数组
        if (!foodsMap.has(categoryId)) {
          foodsMap.set(categoryId, []);
        }
        
        // 添加到对应分类
        const categoryFoods = foodsMap.get(categoryId);
        if (categoryFoods) {
          categoryFoods.push(foodItem);
        }
      });
      
      foodsByCategory.value = foodsMap;
    } else {
      console.log('没有找到食品数据');
      foodsByCategory.value = new Map();
    }
  } catch (error) {
    console.error('获取商家食品失败:', error);
    ElMessage.error('获取商家食品失败，请稍后重试');
  } finally {
    foodsLoading.value = false;
  }
}

/**
 * 选择分类
 * @param categoryId 分类ID
 */
function selectCategory(categoryId: string | number) {
  currentCategory.value = categoryId;
  
  // 清空搜索关键词
  searchKeyword.value = '';
  
  // 滚动到分类位置
  nextTick(() => {
    const element = document.getElementById(`category-${categoryId}`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  });
}

/**
 * 处理搜索
 */
function handleSearch() {
  // 如果有搜索关键词，取消分类选择
  if (searchKeyword.value) {
    currentCategory.value = null;
  } else if (categories.value.length > 0) {
    // 如果清空搜索框，恢复第一个分类
    currentCategory.value = categories.value[0].id;
  }
}

/**
 * 显示食品详情
 * @param food 食品对象
 */
// function showFoodDetail(food: Food) {
//   currentFood.value = food;
//   foodDetailVisible.value = true;
// }

/**
 * 处理食品添加到购物车
 */
// function handleFoodAdded() {
//   // 更新购物车
//   takeoutStore.loadCart();
// }

/**
 * 处理购物车更新事件
 * 当FoodCard组件中的购物车操作完成后调用
 */
function handleCartUpdated() {
  console.log('购物车已更新，刷新购物车数据');
  // 重新加载购物车数据
  takeoutStore.loadCart();
}

/**
 * 跳转到购物车页面
 */
function goToCart() {
  router.push('/user/takeout/cart');
}

// 页面加载时获取数据
onMounted(async () => {
  // 加载商家信息
  await loadMerchant();
  
  // 加载商家分类
  await loadCategories();
  
  // 加载商家食品
  await loadFoods();
  
  // 加载商家优惠券
  await loadMerchantCoupons();
  
  // 加载购物车
  await takeoutStore.loadCart();
});
</script>

<style scoped>
.merchant-detail-page {
  padding: 20px 0;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

.merchant-info-card {
  margin-bottom: 20px;
}

.merchant-header {
  display: flex;
  gap: 20px;
}

.merchant-logo {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.merchant-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.merchant-basic {
  flex: 1;
}

.merchant-name {
  margin: 0 0 10px 0;
  font-size: 22px;
  font-weight: 600;
}

.merchant-rating {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.rating-count {
  color: #909399;
  font-size: 14px;
}

.merchant-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 10px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #606266;
  font-size: 14px;
}

.merchant-notice {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #e6a23c;
  font-size: 14px;
  margin-top: 10px;
}

/* 优惠券样式 */
.merchant-coupons {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.coupons-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.coupons-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.coupons-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.coupon-item {
  display: flex;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  border-radius: 12px;
  padding: 16px;
  color: white;
  position: relative;
  overflow: hidden;
}

.coupon-item.disabled {
  background: linear-gradient(135deg, #ccc 0%, #ddd 100%);
  opacity: 0.7;
}

.coupon-item::before {
  content: '';
  position: absolute;
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  box-shadow: -10px 0 0 white;
}

.coupon-left {
  flex: 0 0 auto;
  text-align: center;
  padding-right: 20px;
  border-right: 2px dashed rgba(255, 255, 255, 0.5);
  margin-right: 16px;
}

.amount {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 4px;
}

.condition {
  font-size: 12px;
  opacity: 0.9;
}

.coupon-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.coupon-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.expire-time {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 8px;
}

.claim-btn {
  align-self: flex-start;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.5);
  color: white;
  font-weight: 600;
}

.claim-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.claimed-text {
  font-size: 12px;
  opacity: 0.8;
  align-self: flex-start;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.view-more {
  text-align: center;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.food-container {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  height: calc(100vh - 250px);
  min-height: 500px;
}

.food-sidebar {
  width: 200px;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
}

.search-box {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

.category-list {
  flex: 1;
  overflow-y: auto;
}

.category-item {
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
}

.category-item:hover {
  background-color: #f5f7fa;
}

.category-item.active {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  font-weight: 500;
}

.food-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.food-category-section {
  margin-bottom: 30px;
}

.category-header {
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 15px;
}

.category-name {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
}

.category-desc {
  color: #909399;
  font-size: 13px;
}

.food-list {
  display: flex;
  flex-direction: column;
}

.floating-cart {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background-color: var(--el-color-primary);
  color: white;
  width: 120px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  z-index: 99;
}

.floating-cart:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.cart-icon {
  margin-right: 10px;
}

.cart-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.cart-total {
  font-weight: bold;
  font-size: 16px;
}

.cart-text {
  font-size: 12px;
}
</style>
