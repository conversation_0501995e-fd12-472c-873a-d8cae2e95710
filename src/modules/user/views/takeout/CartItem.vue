<!--
  购物车商品项组件
  展示购物车中的商品，并提供数量调整、删除等功能
-->
<template>
  <div class="cart-item">
    <div class="item-select">
      <el-checkbox 
        v-model="itemSelected" 
        @change="handleSelectChange"
      />
    </div>
    
    <div class="item-info">
      <div class="item-image">
        <img :src="item.food.image" :alt="item.food.name" />
      </div>
      
      <div class="item-content">
        <div class="item-name">{{ item.food.name }}</div>
        
        <div class="item-specs" v-if="hasSpecifications">
          <div class="specs-label">规格：</div>
          <div class="specs-content">
            <span v-for="(variant, index) in item.variants" :key="variant.variantId" class="spec-item">
              {{ variant.name }}{{ index < item.variants!.length - 1 ? '，' : '' }}
            </span>
          </div>
        </div>
        
        <div class="item-combos" v-if="hasComboItems">
          <div class="combos-label">套餐：</div>
          <div class="combos-content">
            <div v-for="combo in item.comboItems" :key="combo.itemId" class="combo-item">
              {{ combo.name }} x{{ combo.quantity }}
            </div>
          </div>
        </div>
        
        <div class="item-price">
          <span class="current-price">¥{{ unitPrice.toFixed(2) }}</span>
          <span class="packaging-fee" v-if="hasPackagingFee">
            <el-tooltip content="包装费" placement="top"">
              <span style="color: #909399;">+¥{{ packagingFee.toFixed(2) }}</span>
            </el-tooltip>
          </span>
        </div>
      </div>
    </div>
    
    <div class="item-action">
      <div class="quantity-control">
        <el-input-number 
          v-model="itemQuantity" 
          :min="0" 
          :max="99"
          size="small"
          controls-position="right"
          @change="handleQuantityChange"
        />
      </div>
      
      <div class="item-total">
        <span class="total-price">¥{{ calculatedTotalPrice.toFixed(2) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useTakeoutStore } from '../../stores/takeoutStore';
import type { CartItem as CartItemType } from '../../types';

// 定义属性
const props = defineProps<{
  item: CartItemType;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'update', item: CartItemType): void;
  (e: 'delete', itemId: string | number): void;
  (e: 'select', itemId: string | number, selected: boolean): void;
}>();

// 获取takeout store
const takeoutStore = useTakeoutStore();

// 本地状态
const itemQuantity = ref(props.item.quantity);
const itemSelected = ref(props.item.selected);

// 单价计算属性
const unitPrice = computed(() => {
  // 始终使用商品基础价格作为单价，这样可确保计算的一致性
  // 并避免循环依赖问题（即 totalPrice 依赖 unitPrice 而 unitPrice 又依赖 totalPrice）
  return props.item.food?.price || 0;
  
  // 之前的实现，存在问题：如果连续多次调整数量，单价可能会变化
  // if (!props.item.quantity || props.item.quantity === 0) {
  //   return props.item.food?.price || 0;
  // }
  // return props.item.totalPrice / props.item.quantity;
});

// 包装费计算属性
const packagingFee = computed(() => {
  return props.item.packaging_fee || 0;
});

// 是否有包装费
const hasPackagingFee = computed(() => {
  return !!props.item.packaging_fee && props.item.packaging_fee > 0;
});

// 总价计算属性 - 根据当前数量实时计算，包含包装费
const calculatedTotalPrice = computed(() => {
  const basePrice = unitPrice.value * itemQuantity.value;
  // 包装费按数量计算
  const packagingTotal = hasPackagingFee.value ? packagingFee.value * itemQuantity.value : 0;
  return basePrice + packagingTotal;
});

// 是否有规格
const hasSpecifications = computed(() => {
  return props.item.variants && props.item.variants.length > 0;
});

// 是否有套餐项
const hasComboItems = computed(() => {
  return props.item.comboItems && props.item.comboItems.length > 0;
});

// 监听商品数量变化
watch(() => props.item.quantity, (newQuantity) => {
  itemQuantity.value = newQuantity;
});

// 监听商品选中状态变化
watch(() => props.item.selected, (newSelected) => {
  itemSelected.value = newSelected;
});

/**
 * 处理数量变化
 */
async function handleQuantityChange(value: number) {
  try {
    // 如果数量变为0，则删除该商品
    if (value === 0) {
      console.log(`商品数量为0，自动删除商品:`, props.item.id);
      emit('delete', props.item.id);
      return;
    }
    
    // 否则更新数量
    const result = await takeoutStore.updateCartItemQuantity(props.item.id, value);
    console.log('更新购物车商品数量结果:', result);
    
    // 发送更新事件，更新父组件的数据
    // 使用计算的新总价
    emit('update', {
      ...props.item, 
      quantity: value,
      totalPrice: unitPrice.value * value
    });
  } catch (error) {
    console.error('更新购物车商品数量失败:', error);
    ElMessage.error('更新购物车失败，请稍后重试');
    
    // 恢复原来的数量
    itemQuantity.value = props.item.quantity;
  }
}

/**
 * 处理选中状态变化
 */
async function handleSelectChange(value: boolean) {
  emit('select', props.item.id, value);
}

</script>

<style scoped>
.cart-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #ebeef5;
}

.item-select {
  margin-right: 15px;
}

.item-info {
  display: flex;
  flex: 1;
}

.item-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 15px;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.item-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 5px;
}

.item-specs,
.item-combos {
  display: flex;
  font-size: 13px;
  color: #909399;
}

.specs-label,
.combos-label {
  flex-shrink: 0;
  margin-right: 5px;
}

.specs-content,
.combos-content {
  flex: 1;
}

.item-price {
  margin-top: 5px;
}

.current-price {
  color: #f56c6c;
  font-weight: bold;
}

.item-action {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;
  margin-left: 20px;
}

.item-total {
  font-weight: bold;
}

.total-price {
  color: #f56c6c;
}
</style>
