<!--
  地址选择弹窗组件
  展示用户已有的收货地址列表，提供地址选择功能
  支持跳转到地址管理页面添加新地址
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择收货地址"
    width="500px"
  >
    <div class="address-list">
      <el-empty v-if="addresses.length === 0" description="暂无收货地址">
        <el-button type="primary" @click="goToAddressPage">添加地址</el-button>
      </el-empty>
      <div v-else class="address-items">
        <div 
          v-for="address in addresses" 
          :key="address.id"
          class="address-item"
          :class="{ active: localSelectedAddressId === address.id }"
          @click="selectAddressItem(address)"
        >
          <div class="address-content">
            <div class="address-contact">
              <span class="contact-name">{{ address.receiver_name }}</span>
              <span class="contact-phone">{{ address.receiver_mobile }}</span>
              <el-tag v-if="address.is_default" size="small" type="success">默认</el-tag>
            </div>
            <div class="address-detail">
              {{ address.province }} {{ address.city }} {{ address.district }} {{ address.detailed_address }}
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAddress">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import type { UserAddress } from '@/modules/user/types';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  addresses: {
    type: Array as () => UserAddress[],
    default: () => []
  },
  selectedAddressId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'select']);

// 本地状态
const dialogVisible = ref(props.modelValue);
const localSelectedAddressId = ref(props.selectedAddressId);

const router = useRouter();

// 监听model-value变化
watch(() => props.modelValue, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    // 初始化选中的地址ID
    localSelectedAddressId.value = props.selectedAddressId;
  }
});

// 监听选中地址变化
watch(() => props.selectedAddressId, (newVal) => {
  localSelectedAddressId.value = newVal;
});

// 监听本地弹窗状态变化
watch(() => dialogVisible.value, (newVal) => {
  emit('update:modelValue', newVal);
});

/**
 * 选择地址项
 * @param address 选择的地址对象
 */
function selectAddressItem(address: UserAddress) {
  localSelectedAddressId.value = address.id;
  console.log('选择地址项:', {
    addressId: address.id,
    addressName: address.receiver_name,
    idType: typeof address.id
  });
}

/**
 * 确认地址选择
 */
function confirmAddress() {
  if (!localSelectedAddressId.value) {
    console.warn('No address selected');
    return;
  }
  dialogVisible.value = false;
  emit('select', localSelectedAddressId.value);
}

/**
 * 跳转到地址管理页面
 */
function goToAddressPage() {
  router.push('/user/addresses');
}
</script>

<style scoped>
.address-list {
  max-height: 300px;
  overflow-y: auto;
}

.address-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.address-item {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  transition: all 0.3s;
}

.address-item:hover {
  border-color: #409eff;
}

.address-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.address-contact {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.contact-name {
  font-weight: 500;
}

.contact-phone {
  color: #606266;
}

.address-detail {
  color: #606266;
  font-size: 14px;
}
</style>
