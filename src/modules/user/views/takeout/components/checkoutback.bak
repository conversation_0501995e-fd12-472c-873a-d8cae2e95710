<!--
 * 作者: 张二浩
 * 日期: 2025-01-27
 * 版本: 2.0.0
 * 描述: 结算弹窗组件，展示订单确认信息，包括收货地址、支付方式、配送时间、备注和订单金额汇总
 * 提供地址选择、订单提交等功能
 * 更新: 适配新版多商家订单API，支持按商家分组提交订单数据
 * API格式: 使用 merchantOrders 数组结构，每个商家独立配置优惠券、配送时间和备注
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="订单确认"
    width="700px"
    @closed="onDialogClosed"
  >
    <div class="checkout-form">
      <el-form :model="localForm" label-width="80px">
        <el-form-item label="收货地址" required>
          <div v-if="!selectedAddress" class="no-address">
            <span>您还没有收货地址</span>
            <el-button type="primary" link @click="goToAddressPage">添加地址</el-button>
          </div>
          <div v-else class="selected-address">
            <div class="address-info">
              <p class="address-contact">{{ selectedAddress.receiver_name }} {{ selectedAddress.receiver_mobile }}</p>
              <p class="address-detail">
                {{ selectedAddress.province }} {{ selectedAddress.city }} {{ selectedAddress.district }} {{ selectedAddress.detailed_address }}
              </p>
              <!-- 调试信息：收货地址经纬度 -->
              <p class="debug-info" v-if="selectedAddress.location_latitude && selectedAddress.location_longitude">
                <span class="debug-label">收货地址坐标:</span>
                <span class="debug-value">{{ selectedAddress.location_latitude }}, {{ selectedAddress.location_longitude }}</span>
              </p>
              <p class="debug-info" v-else>
                <span class="debug-label">收货地址坐标:</span>
                <span class="debug-value error">未设置经纬度</span>
              </p>
            </div>
            <el-button type="primary" link @click="openAddressSelect">更换地址</el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="配送时间">
          <el-select v-model="localForm.deliveryTime" placeholder="请选择期望配送时间">
            <el-option label="尽快送达" value=""></el-option>
            <el-option label="今天 12:00-13:00" value="today_12_13"></el-option>
            <el-option label="今天 18:00-19:00" value="today_18_19"></el-option>
            <el-option label="明天 12:00-13:00" value="tomorrow_12_13"></el-option>
            <el-option label="明天 18:00-19:00" value="tomorrow_18_19"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="支付方式" required>
          <el-radio-group v-model="localForm.paymentMethod">
            <el-radio label="alipay">支付宝</el-radio>
            <el-radio label="wechat">微信支付</el-radio>
            <el-radio label="balance">余额支付</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <!-- 备注信息已移至各商家分组中 -->
      </el-form>
      
      <!-- 多商家商品和配送费摘要 -->
      <div class="checkout-summary">
        <h3 class="summary-title">订单摘要 (支持多商家)</h3>
        <p class="api-info">使用新版API格式，支持多商家独立配置</p>
        
        <!-- 商家分组商品列表 -->
        <div v-for="(group, merchantId) in groupedItems" :key="merchantId" class="merchant-group">
          <div class="merchant-header">
            <h4 class="merchant-name">{{ group.merchantName }}</h4>
          </div>
          
          <!-- 商家商品列表 -->
          <div class="merchant-items">
            <div v-for="(item, itemIndex) in group.items" :key="itemIndex" class="item-row">
              <span class="item-name">{{ item.food?.name || '商品' }}</span>
              <span class="item-quantity">x{{ item.quantity }}</span>
              <span class="item-price">¥{{ item.totalPrice.toFixed(2) }}</span>
            </div>
          </div>
          
          <!-- 商家优惠券 -->
          <div v-if="getMerchantCoupons(group.merchantId).length > 0" class="merchant-coupons">
            <div class="coupon-title">可用优惠券</div>
            <div class="coupon-selector">
              <el-select 
                :model-value="getSelectedCouponId(group.merchantId)"
                @change="handleCouponChange(group.merchantId, $event)"
                placeholder="选择优惠券"
                clearable
                class="coupon-select"
              >
                <el-option label="不使用优惠券" :value="null" />
                <el-option 
                  v-for="coupon in getMerchantCoupons(group.merchantId)" 
                  :key="coupon.id"
                  :label="getCouponLabel(coupon)"
                  :value="coupon.id"
                  :disabled="!canUseCoupon(coupon, group)"
                >
                  <div class="coupon-option">
                    <div class="coupon-info">
                      <span class="coupon-name">{{ coupon.name }}</span>
                      <span class="coupon-amount">¥{{ coupon.amount }}</span>
                    </div>
                    <div class="coupon-condition">满{{ coupon.min_order_amount }}可用</div>
                    <div v-if="!canUseCoupon(coupon, group)" class="coupon-tip">
                      {{ getCouponTip(coupon, group) }}
                    </div>
                  </div>
                </el-option>
              </el-select>
            </div>
            <div v-if="getSelectedCoupon(group.merchantId)" class="selected-coupon-info">
              <span class="coupon-discount">已选择：{{ getSelectedCoupon(group.merchantId)?.name }} -¥{{ getSelectedCoupon(group.merchantId)?.amount }}</span>
            </div>
          </div>
          
          <!-- 商家促销活动 -->
          <div v-if="getMerchantPromotions(group.merchantId).length > 0" class="merchant-promotions">
            <div class="promotion-title">可用优惠 (商家ID: {{ group.merchantId }})</div>
            <div v-for="promotion in getMerchantPromotions(group.merchantId)" :key="promotion.id" class="promotion-item">
              <el-checkbox 
                v-model="promotion.selected" 
                @change="handlePromotionChange(promotion, group)"
                :disabled="!canUsePromotion(promotion, group)"
              >
                <div class="promotion-content">
                  <div class="promotion-name">{{ promotion.name }}</div>
                  <div class="promotion-desc">{{ getPromotionDesc(promotion) }}</div>
                  <div v-if="!canUsePromotion(promotion, group)" class="promotion-tip">
                    {{ getPromotionTip(promotion, group) }}
                  </div>
                  <!-- 调试信息：显示促销ID和所属商家 -->
                  <div class="debug-info">
                    <span class="debug-label">促销ID:</span>
                    <span class="debug-value">{{ promotion.id }}</span>
                    <span class="debug-label">所属商家:</span>
                    <span class="debug-value">{{ promotion.merchant_id || '未设置' }}</span>
                  </div>
                </div>
              </el-checkbox>
            </div>
          </div>
          
          <!-- 优惠券选择 -->
          <div v-if="getMerchantCoupons(group.merchantId).length > 0" class="coupon-section">
            <h4>选择优惠券</h4>
            <div v-if="couponsLoading" class="loading-text">加载优惠券中...</div>
            <div v-else class="coupon-list">
              <div class="coupon-item no-coupon" 
                   :class="{ 'selected': !getSelectedCoupon(group.merchantId) }"
                   @click="handleCouponChange(group.merchantId, null)">
                <div class="coupon-info">
                  <div class="coupon-name">不使用优惠券</div>
                </div>
              </div>
              <div 
                v-for="coupon in getMerchantCoupons(group.merchantId)" 
                :key="coupon.id"
                class="coupon-item"
                :class="{ 
                  'selected': getSelectedCoupon(group.merchantId)?.id === coupon.id,
                  'disabled': !canUseCoupon(coupon, group)
                }"
                @click="canUseCoupon(coupon, group) && handleCouponChange(group.merchantId, coupon.id)"
              >
                <div class="coupon-info">
                  <div class="coupon-name">{{ coupon.name }}</div>
                  <div class="coupon-amount">¥{{ coupon.amount }}</div>
                  <div class="coupon-condition">满¥{{ coupon.min_order_amount }}可用</div>
                  <div class="coupon-tip">{{ getCouponTip(coupon, group) }}</div>
                </div>
                <div class="coupon-discount" v-if="getSelectedCoupon(group.merchantId)?.id === coupon.id">
                  -¥{{ coupon.amount.toFixed(2) }}
                </div>
              </div>
            </div>
          </div>
          

          <!-- 商家备注 -->
          <div class="merchant-remark">
            <div class="remark-label">给商家的备注：</div>
            <el-input 
              :model-value="getMerchantRemark(group.merchantId)" 
              type="textarea" 
              :rows="2" 
              :placeholder="`给${group.merchantName}的特殊要求`"
              @input="updateMerchantRemark(group.merchantId, $event)"
            />
          </div>
          
          <!-- 商家金额汇总 -->
          <div class="merchant-summary">
            <div class="merchant-summary-item">
              <span class="summary-label">商品金额：</span>
              <span class="summary-value">¥{{ calculateMerchantItemsTotal(group).toFixed(2) }}</span>
            </div>
            <div v-if="calculatePackagingFee(group) > 0" class="merchant-summary-item">
              <span class="summary-label">包装费：</span>
              <span class="summary-value">¥{{ calculatePackagingFee(group).toFixed(2) }}</span>
            </div>
            <div class="merchant-summary-item">
              <span class="summary-label">配送费：</span>
              <span class="summary-value">¥{{ calculateDeliveryFee(group).toFixed(2) }}</span>
            </div>
            <div v-if="calculateMerchantCouponDiscount(group) > 0" class="merchant-summary-item coupon-discount">
              <span class="summary-label">优惠券优惠：</span>
              <span class="summary-value discount-amount">-¥{{ calculateMerchantCouponDiscount(group).toFixed(2) }}</span>
            </div>
            <div v-if="calculateMerchantPromotionDiscount(group) > 0" class="merchant-summary-item promotion-discount">
              <span class="summary-label">促销优惠：</span>
              <span class="summary-value discount-amount">-¥{{ calculateMerchantPromotionDiscount(group).toFixed(2) }}</span>
            </div>
            <div class="merchant-summary-item merchant-total">
              <span class="summary-label">商家小计：</span>
              <span class="summary-value total-amount">¥{{ calculateMerchantSubtotal(group).toFixed(2) }}</span>
            </div>
          </div>
          
          <!-- 调试信息：商家经纬度和距离计算 -->
          <div class="debug-merchant-info">
            <div class="debug-row">
              <span class="debug-label">商家坐标:</span>
              <span class="debug-value" v-if="group.merchantLatitude && group.merchantLongitude">
                {{ group.merchantLatitude }}, {{ group.merchantLongitude }}
              </span>
              <span class="debug-value error" v-else>未设置经纬度</span>
            </div>
            <div class="debug-row">
              <span class="debug-label">距离计算:</span>
              <span class="debug-value">
                {{ getDistanceDebugInfo(group) }}
              </span>
            </div>
          </div>
        </div>
        
        <!-- 订单总计 -->
        <div class="order-totals">
          <h4 class="totals-title">订单总计</h4>
          <div class="summary-item">
            <span class="item-label">商品总金额：</span>
            <span class="item-value">¥{{ totalPrice.toFixed(2) }}</span>
          </div>
          <div v-if="totalPackagingFee > 0" class="summary-item">
            <span class="item-label">包装费总计：</span>
            <span class="item-value">¥{{ totalPackagingFee.toFixed(2) }}</span>
          </div>
          <div class="summary-item">
            <span class="item-label">配送费总计：</span>
            <span class="item-value">¥{{ totalDeliveryFee.toFixed(2) }}</span>
          </div>
          <!-- 优惠券优惠 -->
          <div v-if="totalCouponDiscount > 0" class="summary-item coupon-discount">
            <span class="item-label">优惠券优惠总计：</span>
            <span class="item-value discount-amount">-¥{{ totalCouponDiscount.toFixed(2) }}</span>
          </div>
          <!-- 促销优惠 -->
          <div v-if="totalPromotionDiscount > 0" class="summary-item promotion-discount">
            <span class="item-label">促销优惠总计：</span>
            <span class="item-value discount-amount">-¥{{ totalPromotionDiscount.toFixed(2) }}</span>
          </div>
          <div class="summary-item total">
            <span class="item-label">应付金额：</span>
            <span class="item-value total-amount">¥{{ finalAmount.toFixed(2) }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :disabled="!selectedAddress || !localForm.paymentMethod"
          @click="submitOrder"
          :loading="submitting"
        >
          提交订单
        </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 引入地址选择组件 -->
  <address-select-dialog
    v-if="addressDialogVisible"
    v-model="addressDialogVisible"
    :addresses="addresses"
    :selected-address-id="localForm.addressId"
    @select="handleAddressSelected"
  />
</template>

<style scoped>
.api-info {
  font-size: 12px;
  color: #666;
  margin: 0 0 15px 0;
  font-style: italic;
}

.debug-info {
  font-size: 11px;
  color: #999;
  margin-top: 5px;
}

.debug-label {
  font-weight: bold;
  margin-right: 5px;
}

.debug-value {
  margin-right: 10px;
}

.debug-value.error {
  color: #f56c6c;
}

.debug-merchant-info {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  margin-top: 10px;
  font-size: 11px;
}

.debug-row {
  margin-bottom: 3px;
}

/* 商家金额汇总样式 */
.merchant-summary {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  margin-top: 15px;
}

.merchant-summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 14px;
}

.merchant-summary-item.merchant-total {
  border-top: 1px solid #dee2e6;
  margin-top: 8px;
  padding-top: 8px;
  font-weight: bold;
}

.merchant-summary-item.promotion-discount .summary-value {
  color: #28a745;
}

.summary-label {
  color: #666;
}

.summary-value {
  font-weight: 500;
  color: #333;
}

.summary-value.total-amount {
  color: #e74c3c;
  font-weight: bold;
}

.summary-value.discount-amount {
  color: #28a745;
}

/* 订单总计样式 */
.order-totals {
  background: #fff;
  border: 2px solid #007bff;
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;
}

.totals-title {
  margin: 0 0 12px 0;
  color: #007bff;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
}

/* 商家备注样式 */
.merchant-remark {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  margin-top: 15px;
}

.remark-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}
</style>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import type { UserAddress } from '@/modules/user/types';
import { getUserAddresses } from '@/modules/user/api/address';
import AddressSelectDialog from './AddressSelectDialog.vue';
import type { CartItem } from '@/modules/user/types';
import { useMerchantStore } from '@/modules/user/stores/merchantStore';
import { getAvailableCouponsForOrder } from '@/modules/user/api/coupon';
import type { UserCoupon, OrderAvailableCouponsResponse } from '@/modules/user/types/coupon';
import { CouponStatus } from '@/modules/user/types/coupon';

/**
 * 商家组类型定义
 * 用于多商家订单的数据结构
 */
interface MerchantGroup {
  merchantId: string | number;
  merchantName: string;
  items: CartItem[];
  merchantDistance?: number; // 与用户地址的距离(公里)
  merchantLatitude?: number; // 商家纬度
  merchantLongitude?: number; // 商家经度
}

/**
 * 新版API商家订单请求结构
 */
interface MerchantOrderRequest {
  merchantID: number;
  cartItemIDs: number[];
  couponID: number;
  deliveryTime: string;
  remark: string;
}

/**
 * 新版API订单创建请求结构
 */
interface CreateOrderRequest {
  takeoutAddressID: number;
  paymentMethod: string;
  merchantOrders: MerchantOrderRequest[];
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  totalPrice: {
    type: Number,
    default: 0
  },
  deliveryFee: {
    type: Number,
    default: 0
  },
  // 添加多商家分组商品支持
  groupedItems: {
    type: Array as () => MerchantGroup[],
    default: () => []
  },
  // 配送费单价参数
  deliveryPricePerKm: {
    type: Number,
    default: 2
  },
  // 用户默认地址
  defaultAddress: {
    type: Object,
    default: null
  }
});

/**
 * 计算两点间距离（公里）
 * 使用 Haversine 公式计算球面距离
 */
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  if (!lat1 || !lon1 || !lat2 || !lon2) return 0;
  
  const R = 6371; // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c;
  
  // 数值计算结果直接格式化为两位小数
  return Math.round(distance * 100) / 100; // 保留两位小数
}

/**
 * 计算商家的配送费
 * @param group 商家分组信息
 * @returns 配送费金额
 */
function calculateDeliveryFee(group: MerchantGroup): number {
  // 如果有选择的地址，重新计算距离
  let distance = group.merchantDistance || 0;
  
  if (selectedAddress.value && selectedAddress.value.location_latitude && selectedAddress.value.location_longitude) {
    // 重新计算距离
    const userLat = selectedAddress.value.location_latitude;
    const userLng = selectedAddress.value.location_longitude;
    
    // 获取商家经纬度
    let merchantLat = 0;
    let merchantLng = 0;
    
    if (group.items && group.items.length > 0) {
      const firstItem = group.items[0];
      merchantLat = firstItem.merchant_latitude || 0;
      merchantLng = firstItem.merchant_longitude || 0;
    }
    
    if (merchantLat && merchantLng) {
      distance = calculateDistance(userLat, userLng, merchantLat, merchantLng);
    }
  }
  
  if (!distance || distance <= 0) {
    return 0;
  }
  
  // 距离乘以单价，向上取整保留一位小数
  const fee = distance * props.deliveryPricePerKm;
  return Math.ceil(fee * 10) / 10;
}

/**
 * 计算商家的包装费
 * @param group 商家分组信息
 * @returns 包装费金额
 */
function calculatePackagingFee(group: MerchantGroup): number {
  return group.items.reduce((sum, item) => {
    const packagingFee = item.packaging_fee || 0;
    return sum + (packagingFee * item.quantity);
  }, 0);
}

/**
 * 计算商家的商品金额
 * @param group 商家分组信息
 * @returns 商品金额
 */
function calculateMerchantItemsTotal(group: MerchantGroup): number {
  return group.items.reduce((sum, item) => {
    return sum + item.totalPrice;
  }, 0);
}

/**
 * 计算商家的促销优惠金额
 * @param group 商家分组信息
 * @returns 促销优惠金额
 */
function calculateMerchantPromotionDiscount(group: MerchantGroup): number {
  const merchantPromotionsList = getMerchantPromotions(group.merchantId);
  return merchantPromotionsList
    .filter(p => p.selected)
    .reduce((total, promotion) => {
      return total + (promotion.discount_amount || 0);
    }, 0);
}

/**
 * 计算商家的优惠券优惠金额
 * @param group 商家分组信息
 * @returns 优惠券优惠金额
 */
function calculateMerchantCouponDiscount(group: MerchantGroup): number {
  const selectedCoupon = selectedCoupons.value[group.merchantId];
  if (!selectedCoupon) return 0;
  
  const merchantTotal = calculateMerchantItemsTotal(group);
  if (merchantTotal >= selectedCoupon.min_order_amount) {
    return selectedCoupon.amount;
  }
  
  return 0;
}

/**
 * 计算商家的小计金额
 * @param group 商家分组信息
 * @returns 商家小计金额
 */
function calculateMerchantSubtotal(group: MerchantGroup): number {
  const itemsTotal = calculateMerchantItemsTotal(group);
  const packagingFee = calculatePackagingFee(group);
  const deliveryFee = calculateDeliveryFee(group);
  const couponDiscount = calculateMerchantCouponDiscount(group);
  const promotionDiscount = calculateMerchantPromotionDiscount(group);
  
  return Math.max(0, itemsTotal + packagingFee + deliveryFee - couponDiscount - promotionDiscount);
}

/**
 * 计算总配送费
 */
const totalDeliveryFee = computed(() => {
  if (!props.groupedItems || props.groupedItems.length === 0) {
    return props.deliveryFee; // 兼容原有的deliveryFee参数
  }
  
  // 累加所有商家的配送费
  return props.groupedItems.reduce((sum, group) => {
    return sum + calculateDeliveryFee(group);
  }, 0);
});

/**
 * 计算总包装费
 */
const totalPackagingFee = computed(() => {
  if (!props.groupedItems || props.groupedItems.length === 0) {
    return 0;
  }
  
  // 累加所有商品的包装费
  return props.groupedItems.reduce((sum, group) => {
    return sum + group.items.reduce((itemSum, item) => {
      const packagingFee = item.packaging_fee || 0;
      return itemSum + (packagingFee * item.quantity);
    }, 0);
  }, 0);
});

/**
 * 计算优惠券总优惠金额
 */
const totalCouponDiscount = computed(() => {
  if (!props.groupedItems || props.groupedItems.length === 0) {
    return 0;
  }
  
  return props.groupedItems.reduce((sum, group) => {
    return sum + calculateMerchantCouponDiscount(group);
  }, 0);
});

/**
 * 计算促销总优惠金额
 */
const totalPromotionDiscount = computed(() => {
  return selectedPromotions.value.reduce((total, promotion) => {
    return total + (promotion.discount_amount || 0);
  }, 0);
});

/**
 * 计算最终支付金额
 */
const finalAmount = computed(() => {
  return Math.max(0, props.totalPrice + totalPackagingFee.value + totalDeliveryFee.value - totalCouponDiscount.value - totalPromotionDiscount.value);
});

const emit = defineEmits(['update:modelValue', 'submit', 'address-select', 'close', 'address-changed']);

// 本地状态
const dialogVisible = ref(props.modelValue);
const addressDialogVisible = ref(false);
const addresses = ref<UserAddress[]>([]);
const submitting = ref(false);
const localForm = ref({
  addressId: '',
  deliveryTime: '', // 配送时间字段
  paymentMethod: 'alipay',
  merchantRemarks: {} as Record<string | number, string> // 商家备注，按商家ID分组
});

// 促销相关状态
const merchantPromotions = ref<Record<string | number, any[]>>({});
const selectedPromotions = ref<any[]>([]);

// 优惠券相关状态
const availableCoupons = ref<Record<string | number, UserCoupon[]>>({});
const selectedCoupons = ref<Record<string | number, UserCoupon | null>>({});
const couponsLoading = ref(false);

// 商家store
const merchantStore = useMerchantStore();

const router = useRouter();

// 监听model-value变化
watch(() => props.modelValue, async (newVal) => {
  console.log('CheckoutDialog modelValue 变化:', { newVal, currentAddressId: localForm.value.addressId });
  dialogVisible.value = newVal;
  if (newVal) {
    console.log('对话框打开，开始加载地址、促销信息和优惠券');
    await loadAddresses();
    await loadMerchantPromotions();
    await loadAvailableCoupons();
  }
});

// 监听本地弹窗状态变化
watch(() => dialogVisible.value, (newVal) => {
  emit('update:modelValue', newVal);
  if (!newVal) {
    emit('close');
  }
});

/**
 * 已选择的地址
 */
const selectedAddress = computed(() => {
  if (!localForm.value.addressId) return null;
  return addresses.value.find(addr => addr.id === localForm.value.addressId);
});

// 监听地址变化，通知父组件重新计算配送费
watch(() => selectedAddress.value, (newAddress) => {
  if (newAddress) {
    emit('address-changed', newAddress);
  }
}, { immediate: false });

/**
 * 加载用户地址
 */
async function loadAddresses() {
  try {
    // 添加类型断言，明确指定返回的数据结构
    const response = await getUserAddresses() as {
      list: UserAddress[];
      total: number;
      page: number;
      pageSize: number;
    } | UserAddress[]; // 兼容两种返回格式
    
    console.log('用户地址加载完成:', response);
    
    // 处理后端返回的数据结构 { list: Array, total: number, page: number, pageSize: number }
    const addressList = Array.isArray(response) ? response : (response?.list || []);
    
    // 确保地址列表是数组
    addresses.value = addressList;
    
    console.log('处理后的地址列表:', addresses.value);
    
    // 如果有默认地址，自动选择
    const defaultAddress = addresses.value.find(addr => addr.is_default);
    if (defaultAddress) {
      localForm.value.addressId = defaultAddress.id;
      console.log('自动选择默认地址:', {
        addressId: defaultAddress.id,
        receiverName: defaultAddress.receiver_name
      });
    } else if (addresses.value.length > 0) {
      localForm.value.addressId = addresses.value[0].id;
      console.log('自动选择第一个地址:', {
        addressId: addresses.value[0].id,
        receiverName: addresses.value[0].receiver_name
      });
    } else {
      console.log('没有可用地址');
      localForm.value.addressId = '';
      ElMessage.warning('您还没有添加收货地址，请先添加地址');
    }
    
    console.log('地址加载完成后的表单状态:', {
      addressId: localForm.value.addressId,
      selectedAddress: selectedAddress.value?.receiver_name || 'null'
    });
  } catch (error) {
    console.error('加载地址失败:', error);
    ElMessage.error('加载地址失败，请稍后重试');
  }
}

/**
 * 打开地址选择弹窗
 */
function openAddressSelect() {
  addressDialogVisible.value = true;
}

/**
 * 处理地址选择
 */
function handleAddressSelected(addressId: string) {
  console.log('地址选择:', {
    selectedAddressId: addressId,
    addressType: typeof addressId,
    availableAddresses: addresses.value.map(addr => ({ id: addr.id, type: typeof addr.id }))
  });
  
  if (!addressId) {
    console.error('选择的地址ID为空');
    ElMessage.error('地址选择失败，请重试');
    return;
  }
  
  // 验证地址是否存在
  const selectedAddr = addresses.value.find(addr => addr.id === addressId);
  if (!selectedAddr) {
    console.error('选择的地址不存在:', {
      selectedId: addressId,
      availableIds: addresses.value.map(addr => addr.id)
    });
    ElMessage.error('选择的地址无效，请重新选择');
    return;
  }
  
  localForm.value.addressId = addressId;
  addressDialogVisible.value = false;
  
  console.log('地址选择成功:', {
    addressId: localForm.value.addressId,
    selectedAddress: selectedAddr.receiver_name
  });
  
  // 地址变化时，通知父组件重新计算配送费
  emit('address-changed', selectedAddress.value);
}

/**
 * 跳转到地址管理页面
 */
function goToAddressPage() {
  router.push('/user/addresses');
}

/**
 * 获取商家备注
 * @param merchantId 商家ID
 * @returns 商家备注内容
 */
function getMerchantRemark(merchantId: string | number): string {
  return localForm.value.merchantRemarks[merchantId] || '';
}

/**
 * 更新商家备注
 * @param merchantId 商家ID
 * @param remark 备注内容
 */
function updateMerchantRemark(merchantId: string | number, remark: string) {
  localForm.value.merchantRemarks[merchantId] = remark;
}

/**
 * 对话框关闭时的处理
 * 重置表单数据和商家备注
 */
function onDialogClosed() {
  console.log('CheckoutDialog 对话框关闭，重置数据');
  
  // 重置表单数据
  localForm.value = {
    addressId: '',
    deliveryTime: '',
    paymentMethod: 'alipay',
    merchantRemarks: {}
  };
  
  // 重置促销选择状态
  selectedPromotions.value = [];
  Object.keys(merchantPromotions.value).forEach(merchantId => {
    merchantPromotions.value[merchantId].forEach(promotion => {
      promotion.selected = false;
    });
  });
  
  // 重置优惠券选择状态
  selectedCoupons.value = {};
  availableCoupons.value = {};
  
  // 重置提交状态
  submitting.value = false;
  
  console.log('CheckoutDialog 数据重置完成');
}

/**
 * 提交订单
 */
function submitOrder() {
  console.log('提交订单 - 当前表单状态:', {
    addressId: localForm.value.addressId,
    selectedAddress: selectedAddress.value,
    paymentMethod: localForm.value.paymentMethod
  });
  
  if (!localForm.value.addressId) {
    ElMessage.warning('请选择收货地址');
    console.error('地址ID为空:', localForm.value.addressId);
    return;
  }
  
  if (!selectedAddress.value) {
    ElMessage.warning('地址信息无效，请重新选择');
    console.error('无法找到对应的地址信息:', {
      addressId: localForm.value.addressId,
      availableAddresses: addresses.value.map(addr => ({ id: addr.id, name: addr.receiver_name }))
    });
    return;
  }
  
  if (!localForm.value.paymentMethod) {
    ElMessage.warning('请选择支付方式');
    return;
  }
  
  if (!props.groupedItems || props.groupedItems.length === 0) {
    ElMessage.warning('购物车为空');
    return;
  }
  
  submitting.value = true;
  
  try {
    // 构建新版多商家订单请求数据
    const merchantOrders = props.groupedItems.map(group => {
      // 获取该商家选中的优惠券
      const selectedCoupon = selectedCoupons.value[group.merchantId];
      const couponID = selectedCoupon ? Number(selectedCoupon.id) : 0;
      
      // 获取该商家的购物车项ID，确保转换为数字类型
      const cartItemIDs = group.items
        .map(item => Number(item.id))
        .filter(id => !isNaN(id) && id > 0);
      
      console.log(`商家 ${group.merchantId} 的购物车项:`, {
        merchantName: group.merchantName,
        itemCount: group.items.length,
        cartItemIDs: cartItemIDs,
        couponID: couponID
      });
      
      return {
        merchantID: Number(group.merchantId),
        cartItemIDs: cartItemIDs,
        couponID: couponID,
        deliveryTime: localForm.value.deliveryTime || "",
        remark: getMerchantRemark(group.merchantId) || ""
      };
    });
    
    // 构建新版API请求数据
    console.log('构建 takeoutAddressID 前的调试信息:', {
      'localForm.value.addressId': localForm.value.addressId,
      'typeof addressId': typeof localForm.value.addressId,
      'addressId 是否为空': !localForm.value.addressId,
      'Number(addressId)': localForm.value.addressId ? Number(localForm.value.addressId) : 'N/A',
      'selectedAddress': selectedAddress.value
    });
    
    const takeoutAddressID = localForm.value.addressId ? Number(localForm.value.addressId) : null;
    
    console.log('最终的 takeoutAddressID:', takeoutAddressID);
    
    // 验证数据完整性
    if (!takeoutAddressID || takeoutAddressID <= 0) {
      ElMessage.warning('收货地址ID无效');
      submitting.value = false;
      return;
    }
    
    if (merchantOrders.length === 0) {
      ElMessage.warning('没有有效的商家订单数据');
      submitting.value = false;
      return;
    }
    
    // 检查每个商家订单是否有有效的购物车项
    const invalidMerchants = merchantOrders.filter(order => order.cartItemIDs.length === 0);
    if (invalidMerchants.length > 0) {
      ElMessage.warning('部分商家没有有效的购物车项');
      submitting.value = false;
      return;
    }
    
    const submitData: CreateOrderRequest = {
      takeoutAddressID: takeoutAddressID,
      paymentMethod: localForm.value.paymentMethod,
      merchantOrders: merchantOrders
    };
    
    console.log('=== 提交订单数据 (新版多商家API格式) ===');
    console.log('完整请求数据:', JSON.stringify(submitData, null, 2));
    console.log('商家订单数量:', merchantOrders.length);
    merchantOrders.forEach((order, index) => {
      console.log(`商家 ${index + 1} (ID: ${order.merchantID}):`, {
        购物车项数量: order.cartItemIDs.length,
        购物车项IDs: order.cartItemIDs,
        优惠券ID: order.couponID,
        配送时间: order.deliveryTime || '尽快送达',
        备注: order.remark || '无'
      });
    });
    
    // 发送订单数据给父组件
    emit('submit', submitData);
    
    console.log('订单数据已发送给父组件处理');
  } catch (error) {
    console.error('构建订单数据失败:', error);
    ElMessage.error('订单数据构建失败，请重试');
    submitting.value = false;
  }
}

/**
 * 获取距离计算调试信息
 * @param group 商家分组信息
 * @returns 调试信息字符串
 */
function getDistanceDebugInfo(group: MerchantGroup): string {
   if (!selectedAddress.value) {
     return '无收货地址';
   }
   
   const userLat = selectedAddress.value.location_latitude;
   const userLng = selectedAddress.value.location_longitude;
   const merchantLat = group.merchantLatitude;
   const merchantLng = group.merchantLongitude;
   
   if (!userLat || !userLng) {
     return '收货地址缺少经纬度';
   }
   
   if (!merchantLat || !merchantLng) {
     return '商家地址缺少经纬度';
   }
   
   // 计算实际距离（使用简单的欧几里得距离公式作为示例）
   const distance = Math.sqrt(
     Math.pow((merchantLat - userLat) * 111000, 2) + 
     Math.pow((merchantLng - userLng) * 111000 * Math.cos(userLat * Math.PI / 180), 2)
   );
   
   return `计算距离: ${distance.toFixed(0)}米, 传入距离: ${((group.merchantDistance || 0) * 1000).toFixed(0)}米 (${(group.merchantDistance || 0).toFixed(2)}km)`;
 }

/**
 * 获取商家促销活动
 * @param merchantId 商家ID
 * @returns 该商家的促销活动列表
 */
function getMerchantPromotions(merchantId: string | number) {
  const promotions = merchantPromotions.value[merchantId] || [];
  
  // 额外验证：确保返回的促销活动确实属于该商家
  return promotions.filter(promotion => {
    // 检查促销活动是否有merchant_id字段，且与传入的merchantId匹配
    return !promotion.merchant_id || promotion.merchant_id == merchantId;
  });
}

/**
 * 检查是否可以使用促销
 */
function canUsePromotion(promotion: any, group: MerchantGroup) {
  if (!promotion.rules) return false;
  
  try {
    const rules = JSON.parse(promotion.rules);
    if (rules.coupon) {
      const merchantTotal = group.items.reduce((total, item) => {
        return total + item.totalPrice;
      }, 0);
      return merchantTotal >= rules.coupon.min_order_amount;
    }
  } catch (e) {
    console.error('Failed to parse promotion rules:', e);
  }
  
  return false;
}

/**
 * 获取促销描述
 */
function getPromotionDesc(promotion: any) {
  try {
    const rules = JSON.parse(promotion.rules);
    if (rules.coupon) {
      return `满¥${rules.coupon.min_order_amount}减¥${rules.coupon.amount}`;
    }
  } catch (e) {
    console.error('Failed to parse promotion rules:', e);
  }
  return promotion.description || '';
}

/**
 * 获取促销提示
 */
function getPromotionTip(promotion: any, group: MerchantGroup) {
  try {
    const rules = JSON.parse(promotion.rules);
    if (rules.coupon) {
      const merchantTotal = group.items.reduce((total, item) => {
        return total + item.totalPrice;
      }, 0);
      const needed = rules.coupon.min_order_amount - merchantTotal;
      if (needed > 0) {
        return `还需¥${needed.toFixed(2)}即可使用`;
      }
    }
  } catch (e) {
    console.error('Failed to parse promotion rules:', e);
  }
  return '不满足使用条件';
}

/**
 * 处理促销选择变化
 */
function handlePromotionChange(promotion: any, _group: MerchantGroup) {
  if (promotion.selected) {
    // 计算优惠金额
    try {
      const rules = JSON.parse(promotion.rules);
      if (rules.coupon) {
        promotion.discount_amount = rules.coupon.amount;
      }
    } catch (e) {
      promotion.discount_amount = 0;
    }
    
    selectedPromotions.value.push(promotion);
  } else {
    const index = selectedPromotions.value.findIndex(p => p.id === promotion.id);
    if (index > -1) {
      selectedPromotions.value.splice(index, 1);
    }
  }
}

/**
 * 加载可用优惠券
 */
async function loadAvailableCoupons() {
  if (!props.groupedItems || props.groupedItems.length === 0) return;
  
  couponsLoading.value = true;
  try {
    // 为每个商家加载可用优惠券
    for (const group of props.groupedItems) {
      const merchantTotal = calculateMerchantItemsTotal(group);
      
      const response = await getAvailableCouponsForOrder({
        merchant_id: Number(group.merchantId),
        total_amount: merchantTotal
      });
      
      console.log(`商家 ${group.merchantId} 的可用优惠券:`, response);
      
      // 解包Axios响应数据
      const responseData = response.data as OrderAvailableCouponsResponse;
      if (responseData && responseData.available_coupons) {
        availableCoupons.value[group.merchantId] = responseData.available_coupons;
        
        // 自动选择最优优惠券
        autoSelectBestCoupon(group.merchantId, responseData.available_coupons, merchantTotal);
      } else {
        availableCoupons.value[group.merchantId] = [];
      }
    }
  } catch (error) {
    console.error('加载优惠券失败:', error);
    // 失败时清空优惠券数据
    props.groupedItems.forEach(group => {
      availableCoupons.value[group.merchantId] = [];
    });
  } finally {
    couponsLoading.value = false;
  }
}

/**
 * 自动选择最优优惠券
 * @param merchantId 商家ID
 * @param coupons 可用优惠券列表
 * @param orderAmount 订单金额
 */
function autoSelectBestCoupon(merchantId: string | number, coupons: UserCoupon[], orderAmount: number) {
  if (!coupons || coupons.length === 0) return;
  
  // 筛选出可用的优惠券（满足最低消费要求）
  const usableCoupons = coupons.filter(coupon => 
    coupon.status === CouponStatus.UNUSED && orderAmount >= coupon.min_order_amount
  );
  
  if (usableCoupons.length === 0) return;
  
  // 选择优惠金额最大的优惠券
  const bestCoupon = usableCoupons.reduce((best, current) => 
    current.amount > best.amount ? current : best
  );
  
  selectedCoupons.value[merchantId] = bestCoupon;
  console.log(`商家 ${merchantId} 自动选择最优优惠券:`, bestCoupon.name, `优惠 ¥${bestCoupon.amount}`);
}

/**
 * 获取商家的可用优惠券
 * @param merchantId 商家ID
 * @returns 优惠券列表
 */
function getMerchantCoupons(merchantId: string | number): UserCoupon[] {
  return availableCoupons.value[merchantId] || [];
}

/**
 * 获取选中的优惠券ID
 * @param merchantId 商家ID
 * @returns 优惠券ID
 */
function getSelectedCouponId(merchantId: string | number): string | number | null {
  const selectedCoupon = selectedCoupons.value[merchantId];
  return selectedCoupon ? selectedCoupon.id : null;
}

/**
 * 获取选中的优惠券
 * @param merchantId 商家ID
 * @returns 优惠券对象
 */
function getSelectedCoupon(merchantId: string | number): UserCoupon | null {
  return selectedCoupons.value[merchantId] || null;
}

/**
 * 处理优惠券选择变化
 * @param merchantId 商家ID
 * @param couponId 优惠券ID
 */
function handleCouponChange(merchantId: string | number, couponId: string | number | null) {
  if (!couponId) {
    selectedCoupons.value[merchantId] = null;
    return;
  }
  
  const coupons = getMerchantCoupons(merchantId);
  const selectedCoupon = coupons.find(c => c.id === couponId);
  
  if (selectedCoupon) {
    selectedCoupons.value[merchantId] = selectedCoupon;
    console.log(`商家 ${merchantId} 选择优惠券:`, selectedCoupon.name, `优惠 ¥${selectedCoupon.amount}`);
  }
}

/**
 * 检查是否可以使用优惠券
 * @param coupon 优惠券对象
 * @param group 商家分组信息
 * @returns 是否可用
 */
function canUseCoupon(coupon: UserCoupon, group: MerchantGroup): boolean {
  if (coupon.status !== CouponStatus.UNUSED) return false;
  
  const merchantTotal = calculateMerchantItemsTotal(group);
  return merchantTotal >= coupon.min_order_amount;
}

/**
 * 获取优惠券标签
 * @param coupon 优惠券对象
 * @returns 标签文本
 */
function getCouponLabel(coupon: UserCoupon): string {
  return `${coupon.name} - ¥${coupon.amount} (满${coupon.min_order_amount}可用)`;
}

/**
 * 获取优惠券提示信息
 * @param coupon 优惠券对象
 * @param group 商家分组信息
 * @returns 提示文本
 */
function getCouponTip(coupon: UserCoupon, group: MerchantGroup): string {
  if (coupon.status !== CouponStatus.UNUSED) {
    return '优惠券已使用或过期';
  }
  
  const merchantTotal = calculateMerchantItemsTotal(group);
  const needed = coupon.min_order_amount - merchantTotal;
  
  if (needed > 0) {
    return `还需¥${needed.toFixed(2)}即可使用`;
  }
  
  return '可以使用';
}

/**
 * 加载商家促销活动
 */
async function loadMerchantPromotions() {
  if (!props.groupedItems || props.groupedItems.length === 0) return;
  
  const merchantIds = [...new Set(props.groupedItems.map(group => group.merchantId))];
  console.log('正在加载商家促销信息，商家IDs:', merchantIds);
  
  try {
    // 使用store中的方法加载促销信息
    await merchantStore.loadMerchantsPromotions(merchantIds);
    
    // 从store中获取促销信息并更新本地状态
    merchantIds.forEach(merchantId => {
      console.log(`正在查找商家 ${merchantId} 的信息`);
      console.log('当前merchants数组:', merchantStore.merchants);
      console.log('merchantsMap:', merchantStore.merchantsMap);
      
      const merchant = merchantStore.getMerchantById(merchantId);
      console.log(`商家 ${merchantId} 查找结果:`, merchant);
      
      if (merchant && merchant.promotions && merchant.promotions.length > 0) {
        // 转换促销信息格式，添加选择状态和折扣金额计算
        const promotions = merchant.promotions.map(promo => {
          let discountAmount = 0;
          
          // 解析规则计算折扣金额
          try {
            const rules = typeof promo.rules === 'string' ? JSON.parse(promo.rules) : promo.rules;
            if (rules && rules.coupon) {
              discountAmount = rules.coupon.amount || 0;
            }
          } catch (error) {
            console.warn('解析促销规则失败:', error);
          }
          
          return {
            ...promo,
            merchant_id: merchantId,
            selected: false,
            discount_amount: discountAmount
          };
        });
        
        merchantPromotions.value[merchantId] = promotions;
        console.log(`商家 ${merchantId} 的促销信息已加载:`, promotions);
      } else {
        console.log(`商家 ${merchantId} 暂无促销信息，merchant:`, merchant);
        merchantPromotions.value[merchantId] = [];
      }
    });
  } catch (error) {
    console.error('加载商家促销信息失败:', error);
    ElMessage.error('加载促销信息失败，请稍后重试');
    
    // 失败时清空促销信息
    merchantIds.forEach(merchantId => {
      merchantPromotions.value[merchantId] = [];
    });
  }
}


</script>

<style scoped>
.checkout-form {
  margin-bottom: 20px;
}

.no-address {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #999;
}

.selected-address {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.address-info {
  flex: 1;
  padding-right: 20px;
}

.address-contact {
  font-weight: 500;
  margin-bottom: 5px;
}

.address-detail {
  color: #666;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.checkout-summary {
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.summary-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  color: #333;
}

/* 商家分组样式 */
.merchant-group {
  margin-bottom: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 12px;
}

.merchant-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  border-bottom: 1px dashed #eee;
  padding-bottom: 8px;
}

.merchant-name {
  font-size: 15px;
  font-weight: 500;
  margin: 0;
}

/* 商品列表样式 */
.merchant-items {
  margin-bottom: 10px;
}

.item-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.item-name {
  flex: 1;
  font-weight: normal;
  color: #333;
}

.item-quantity {
  margin-right: 20px;
  color: #666;
  width: 40px;
  text-align: center;
}

.item-price {
  font-weight: 500;
  min-width: 70px;
  text-align: right;
}

/* 包装费样式 */
.packaging-fee-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px dashed #eee;
  padding-top: 10px;
  margin-bottom: 5px;
  font-size: 13px;
  color: #666;
}

.packaging-label {
  width: 60px;
}

.packaging-info {
  flex: 1;
  text-align: center;
}

.packaging-fee {
  font-weight: 500;
  min-width: 70px;
  text-align: right;
  color: #ff4d4f;
}

/* 配送费样式 */
.delivery-fee-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px dashed #eee;
  padding-top: 10px;
  font-size: 13px;
  color: #666;
}

.delivery-label {
  width: 60px;
}

.delivery-info {
  flex: 1;
  text-align: center;
}

.delivery-fee {
  font-weight: 500;
  min-width: 70px;
  text-align: right;
  color: #ff4d4f;
}

/* 总计部分样式 */
.summary-totals {
  border-top: 2px solid #eee;
  margin-top: 15px;
  padding-top: 15px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 14px;
}

.summary-item.total {
  margin-top: 15px;
  font-size: 16px;
  font-weight: 500;
}

.total-amount {
  color: #ff4d4f;
  font-size: 18px;
  font-weight: bold;
}

/* 促销活动样式 */
.merchant-promotions {
  margin: 10px 0;
  padding: 10px;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
}

.promotion-title {
  font-size: 14px;
  font-weight: 500;
  color: #fa8c16;
  margin-bottom: 8px;
}

.promotion-item {
  margin-bottom: 8px;
}

.promotion-content {
  margin-left: 8px;
}

.promotion-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.promotion-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.promotion-tip {
  font-size: 11px;
  color: #ff4d4f;
  margin-bottom: 2px;
}

.promotion-discount {
  color: #52c41a;
}

.discount-amount {
  color: #52c41a;
  font-weight: 500;
}

/* 调试信息样式 */
.debug-info {
  margin: 5px 0;
  font-size: 11px;
  color: #999;
  line-height: 1.4;
}

.debug-label {
  margin-right: 4px;
  font-weight: 500;
}

.debug-value {
  margin-right: 10px;
}

.debug-value.error {
  color: #ff4d4f;
}

.debug-merchant-info {
  margin-top: 8px;
  padding: 8px;
  background-color: #f0f0f0;
  border-radius: 3px;
  font-size: 11px;
}

.debug-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.debug-row:last-child {
  margin-bottom: 0;
}

.debug-label {
  color: #666;
  font-weight: 500;
  min-width: 80px;
}

.debug-value {
  color: #333;
  flex: 1;
  text-align: right;
}

.debug-value.error {
  color: #ff4d4f;
  font-weight: 500;
}

/* 促销活动样式 */
.merchant-promotions {
  margin-top: 12px;
  padding: 10px;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
}

.promotion-title {
  font-size: 13px;
  font-weight: 500;
  color: #d48806;
  margin-bottom: 8px;
}

.promotion-item {
  margin-bottom: 8px;
}

.promotion-item:last-child {
  margin-bottom: 0;
}

.promotion-content {
  margin-left: 8px;
}

.promotion-name {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.promotion-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.promotion-tip {
  font-size: 11px;
  color: #999;
  font-style: italic;
}

.promotion-discount .discount-amount {
  color: #52c41a;
  font-weight: 500;
}

.el-checkbox {
  width: 100%;
}

.el-checkbox__label {
  width: 100%;
}

/* 优惠券选择样式 */
.coupon-section {
  margin-bottom: 20px;
}

.coupon-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.loading-text {
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

.coupon-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.coupon-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fff;
}

.coupon-item:hover:not(.disabled) {
  border-color: #ff6b35;
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.1);
}

.coupon-item.selected {
  border-color: #ff6b35;
  background: #fff5f2;
}

.coupon-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f5f5f5;
}

.coupon-item.no-coupon {
  background: #f8f9fa;
  border-style: dashed;
}

.coupon-item.no-coupon.selected {
  background: #e3f2fd;
  border-color: #2196f3;
}

.coupon-info {
  flex: 1;
}

.coupon-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.coupon-amount {
  font-size: 16px;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 2px;
}

.coupon-condition {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.coupon-tip {
  font-size: 12px;
  color: #999;
}

.coupon-discount {
  color: #ff6b35;
  font-weight: bold;
  font-size: 14px;
  margin-left: 12px;
}
</style>
