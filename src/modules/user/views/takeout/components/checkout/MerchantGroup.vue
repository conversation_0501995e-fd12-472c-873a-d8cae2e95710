<!--
 * 作者: 张二浩
 * 日期: 2025-01-27
 * 版本: 1.0.0
 * 描述: 商家分组组件，用于显示单个商家的商品列表和相关信息
 * 功能: 显示商家商品、包装费、配送费、促销活动、优惠券选择、商家备注等
-->
<template>
  <div class="merchant-group">
    <h4 class="merchant-name">{{ merchant.merchant_name }}</h4>
    
    <!-- 商品列表 -->
    <div class="items-list">
      <div v-for="item in merchant.items" :key="item.id" class="item-row">
        <img :src="item.food?.image || item.image" :alt="item.food?.name || item.name" class="item-image" />
        <div class="item-info">
          <h5 class="item-name">{{ item.food?.name || item.name || '商品' }}</h5>
          <p class="item-description">{{ item.food?.description || item.description }}</p>
          <div class="item-price-quantity">
            <span class="item-price">¥{{ calculateItemTotalPrice(item).toFixed(2) }}</span>
            <span class="item-price">¥{{ calculateItemUnitPrice(item).toFixed(2) }}</span>
            <span class="item-quantity">x{{ item.quantity }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 包装费 -->
    <div class="packaging-fee">
      <span>包装费</span>
      <span>¥{{ packagingFee.toFixed(2) }}</span>
    </div>

    <!-- 配送费 -->
    <div class="delivery-fee">
      <span>配送费</span>
      <span>¥{{ deliveryFee.toFixed(2) }}</span>
      <!-- 调试信息：配送费计算 -->
      <div class="debug-info">
        <span class="debug-label">配送费计算:</span>
        <span class="debug-value">距离: {{ distance.toFixed(2) }}km, 费用: ¥{{ deliveryFee.toFixed(2) }}</span>
      </div>
    </div>

    <!-- 促销活动 - 仅显示非优惠券类型的促销活动 -->
    <PromotionSelector
      v-if="filteredPromotions.length > 0"
      :promotions="filteredPromotions"
      :selected-promotion="selectedPromotion"
      :merchant-items-total="merchantItemsTotal"
      @update:selected-promotion="handlePromotionChange"
    />

    <!-- 优惠券 -->
    <CouponSelector
      v-if="availableCoupons.length > 0"
      :coupons="availableCoupons"
      :selected-coupon="selectedCoupon"
      :merchant-items-total="merchantItemsTotal"
      @update:selected-coupon="handleCouponChange"
    />

    <!-- 商家备注 -->
    <div class="merchant-remark">
      <label>商家备注：</label>
      <el-input
        :model-value="merchantRemark"
        @update:model-value="handleRemarkChange"
        type="textarea"
        :rows="2"
        placeholder="选填，请输入您需要告诉商家的信息"
        maxlength="100"
        show-word-limit
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import PromotionSelector from './PromotionSelector.vue';
import CouponSelector from './CouponSelector.vue';
import type { CartItemType } from '../../../../types/cart';
import type { MerchantGroupType } from '../../../../types/merchant';


/**
 * 组件属性定义
 */
interface Props {
  merchantId: string | number;
  merchant: MerchantGroupType;
  selectedAddress: {
    location_latitude?: number;
    location_longitude?: number;
  } | null;
  deliveryPricePerKm: number;
  selectedPromotion: Promotion | null;
  selectedCoupon: Coupon | null;
  availablePromotions: Promotion[];
  availableCoupons: Coupon[];
  merchantRemark: string;
}

/**
 * 促销活动类型定义
 * 引用路径中的类型以保持一致
 */
import type { Promotion as BasePromotion } from '@/modules/user/types/promotion';

// 使用类型别名来兼容在此组件中使用的Promotion类型
type Promotion = BasePromotion;

/**
 * 优惠券类型定义
 * 引用路径中的类型以保持一致
 */
import type { UserCoupon } from '@/modules/user/types/coupon';

// 使用类型别名来兼容在此组件中使用的Coupon类型
type Coupon = UserCoupon;

/**
 * 组件事件定义
 */
interface Emits {
  (e: 'update:selected-promotion', value: Promotion | null): void;
  (e: 'update:selected-coupon', value: Coupon | null): void;
  (e: 'update:merchant-remark', value: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

/**
 * 计算两点间距离（简化版本，实际应使用地图API）
 * @param lat1 纬度1
 * @param lon1 经度1
 * @param lat2 纬度2
 * @param lon2 经度2
 * @returns 距离（公里）
 */
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371; // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * 计算商品单价
 * @param item 购物车项
 * @returns 单价
 */
function calculateItemUnitPrice(item: CartItemType): number {
  // 先从 food 对象获取价格，如果没有则使用项目自身的价格
  return item.food?.price !== undefined ? item.food.price : item.price;
}

/**
 * 计算商品总价
 * @param item 购物车项
 * @returns 总价
 */
function calculateItemTotalPrice(item: CartItemType): number {
  // 如果有预计算的总价则使用预计算值
  if (item.totalPrice !== undefined) {
    return item.totalPrice;
  }
  // 否则计算单价 * 数量
  return calculateItemUnitPrice(item) * item.quantity;
}

/**
 * 计算商家商品总价
 */
const merchantItemsTotal = computed(() => {
  return props.merchant.items.reduce((total, item) => {
    return total + calculateItemTotalPrice(item);
  }, 0);
});

/**
 * 计算包装费
 */
const packagingFee = computed(() => {
  return props.merchant.items.reduce((total, item) => {
    return total + (item.packaging_fee || 0) * item.quantity;
  }, 0);
});

/**
 * 过滤出非优惠券类型的活动
 * 仅过滤出类型为满减、折扣或赠品等非优惠券类型的活动
 */
const filteredPromotions = computed(() => {
  if (!props.availablePromotions || !Array.isArray(props.availablePromotions)) {
    console.log('无可用促销活动');
    return [];
  }
  
  const promotions = props.availablePromotions.filter(promotion => {
    // 确定促销是否为优惠券类型 (以避免与优惠券功能重叠)
    const isCoupon = promotion.type === 'coupon';
    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
    console.log(`促销活动过滤: ${promotion.name}, 是否优惠券: ${isCoupon}`);
    return !isCoupon;
  });
  console.log(`过滤后的活动数量: ${promotions.length}`);
  return promotions;
});

/**
 * 计算距离
 */
const distance = computed(() => {
    if (!props.selectedAddress || 
        !props.selectedAddress.location_latitude || 
        !props.selectedAddress.location_longitude) {
      return 0;
    }
    
    // 尝试从不同的字段获取商家经纬度
    let merchantLat = props.merchant.merchant_latitude || props.merchant.merchantLatitude;
    let merchantLng = props.merchant.merchant_longitude || props.merchant.merchantLongitude;
    
    // 如果商家经纬度不存在，尝试从商品项中获取
    if (!merchantLat || !merchantLng) {
      const firstItem = props.merchant.items?.[0];
      if (firstItem) {
        merchantLat = firstItem.merchant_latitude;
        merchantLng = firstItem.merchant_longitude;
      }
    }
    
    if (!merchantLat || !merchantLng) {
      console.warn('商家经纬度信息缺失:', {
        merchant: props.merchant,
        merchantLat,
        merchantLng
      });
      return 0;
    }
    
    const calculatedDistance = calculateDistance(
      props.selectedAddress.location_latitude,
      props.selectedAddress.location_longitude,
      merchantLat,
      merchantLng
    );
    
    console.log('距离计算结果:', {
      userLat: props.selectedAddress.location_latitude,
      userLng: props.selectedAddress.location_longitude,
      merchantLat,
      merchantLng,
      distance: calculatedDistance
    });
    
    return calculatedDistance;
  });

/**
 * 计算配送费
 */
const deliveryFee = computed(() => {
  const dist = distance.value;
  if (dist <= 3) {
    return 5; // 3公里内固定5元
  } else {
    return 5 + (dist - 3) * props.deliveryPricePerKm; // 超出部分按每公里计费
  }
});

/**
 * 处理促销活动变化
 */
function handlePromotionChange(promotion: Promotion | null) {
  console.log('[MerchantGroup] 促销选择变化:', {
    merchantId: props.merchantId,
    promotion: promotion,
    promotionId: promotion?.id || null
  });
  emit('update:selected-promotion', promotion);
}

/**
 * 处理优惠券变化
 */
function handleCouponChange(coupon: Coupon | null) {
  console.log('[MerchantGroup] 优惠券选择变化:', {
    merchantId: props.merchantId,
    coupon: coupon,
    couponId: coupon?.id || null
  });
  emit('update:selected-coupon', coupon);
}

/**
 * 处理商家备注变化
 */
function handleRemarkChange(remark: string) {
  emit('update:merchant-remark', remark);
}
</script>

<style scoped>
.merchant-group {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fff;
}

.merchant-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.items-list {
  margin-bottom: 16px;
}

.item-row {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.item-row:last-child {
  border-bottom: none;
}

.item-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
  margin-right: 12px;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin: 0 0 4px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-description {
  font-size: 12px;
  color: #909399;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-price-quantity {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-price {
  font-size: 14px;
  font-weight: 600;
  color: #e6a23c;
}

.item-quantity {
  font-size: 14px;
  color: #606266;
}

.packaging-fee,
.delivery-fee {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
  color: #606266;
  border-bottom: 1px solid #f5f5f5;
}

.debug-info {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.debug-label {
  margin-right: 4px;
}

.debug-value {
  margin-right: 12px;
}

.merchant-remark {
  margin-top: 16px;
}

.merchant-remark label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.merchant-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #303133;
}

.items-list {
  margin-bottom: 12px;
}

.item-row {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f7fa;
}

.item-row:last-child {
  border-bottom: none;
}

.item-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
  margin-right: 12px;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #303133;
}

.item-description {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  line-height: 1.4;
}

.item-price-quantity {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-price {
  font-size: 14px;
  font-weight: 600;
  color: #e6a23c;
}

.item-quantity {
  font-size: 14px;
  color: #606266;
}

.packaging-fee,
.delivery-fee {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
  color: #606266;
  border-bottom: 1px solid #f5f7fa;
}

.debug-info {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
}

.debug-label {
  font-weight: bold;
  margin-right: 5px;
}

.debug-value {
  margin-right: 10px;
}

.merchant-remark {
  margin-top: 12px;
}

.merchant-remark label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}
</style>