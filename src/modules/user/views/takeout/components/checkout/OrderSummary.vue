<!--
 * 作者: 张二浩
 * 日期: 2025-01-27
 * 版本: 1.0.0
 * 描述: 订单汇总组件，用于显示订单的总计信息
 * 功能: 显示商品总价、包装费、配送费、优惠金额、最终金额等
-->
<template>
  <div class="order-summary">
    <h4 class="summary-title">订单汇总</h4>
    
    <div class="summary-item">
      <span>商品总价</span>
      <span>¥{{ totalPrice.toFixed(2) }}</span>
    </div>
    
    <div class="summary-item">
      <span>包装费</span>
      <span>¥{{ totalPackagingFee.toFixed(2) }}</span>
    </div>
    
    <div class="summary-item">
      <span>配送费</span>
      <span>¥{{ totalDeliveryFee.toFixed(2) }}</span>
    </div>
    
    <div v-if="totalPromotionDiscount > 0" class="summary-item discount">
      <span>促销优惠</span>
      <span>-¥{{ totalPromotionDiscount.toFixed(2) }}</span>
    </div>
    
    <div v-if="totalCouponDiscount > 0" class="summary-item discount">
      <span>优惠券优惠</span>
      <span>-¥{{ totalCouponDiscount.toFixed(2) }}</span>
    </div>
    
    <div class="summary-divider"></div>
    
    <div class="summary-total">
      <span>实付金额</span>
      <span class="total-amount">¥{{ finalAmount.toFixed(2) }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 组件属性定义
 */
interface Props {
  totalPrice: number;
  totalPackagingFee: number;
  totalDeliveryFee: number;
  totalPromotionDiscount: number;
  totalCouponDiscount: number;
  finalAmount: number;
}

defineProps<Props>();
</script>

<style scoped>
.order-summary {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #303133;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
  color: #606266;
}

.summary-item.discount {
  color: #67c23a;
}

.summary-divider {
  height: 1px;
  background: #e4e7ed;
  margin: 12px 0;
}

.summary-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.total-amount {
  color: #e6a23c;
  font-size: 18px;
}
</style>