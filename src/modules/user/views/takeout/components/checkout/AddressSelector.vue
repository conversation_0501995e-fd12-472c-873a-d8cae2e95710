<!--
 * 作者: 张二浩
 * 日期: 2025-01-27
 * 版本: 1.0.0
 * 描述: 收货地址选择组件，用于结算页面的地址选择功能
 * 功能: 显示当前选中地址、支持更换地址、跳转到地址管理页面
-->
<template>
  <el-form-item label="收货地址" required>
    <div v-if="!selectedAddress" class="no-address">
      <span>您还没有收货地址</span>
      <el-button type="primary" link @click="goToAddressPage">添加地址</el-button>
    </div>
    <div v-else class="selected-address">
      <div class="address-info">
        <p class="address-contact">{{ selectedAddress.receiver_name }} {{ selectedAddress.receiver_mobile }}</p>
        <p class="address-detail">
          {{ selectedAddress.province }} {{ selectedAddress.city }} {{ selectedAddress.district }} {{ selectedAddress.detailed_address }}
        </p>
        <!-- 调试信息：收货地址经纬度 -->
        <p class="debug-info" v-if="selectedAddress.location_latitude && selectedAddress.location_longitude">
          <span class="debug-label">收货地址坐标:</span>
          <span class="debug-value">{{ selectedAddress.location_latitude }}, {{ selectedAddress.location_longitude }}</span>
        </p>
        <p class="debug-info" v-else>
          <span class="debug-label">收货地址坐标:</span>
          <span class="debug-value error">未设置经纬度</span>
        </p>
      </div>
      <el-button type="primary" link @click="openAddressSelect">更换地址</el-button>
    </div>
  </el-form-item>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import type { UserAddress } from '@/modules/user/types';

/**
 * 组件属性定义
 */
interface Props {
  selectedAddress: UserAddress | null;
}

/**
 * 组件事件定义
 */
interface Emits {
  (e: 'open-address-select'): void;
}

// 直接使用defineProps的返回值
defineProps<Props>();
const emit = defineEmits<Emits>();
const router = useRouter();

/**
 * 打开地址选择弹窗
 */
function openAddressSelect() {
  emit('open-address-select');
}

/**
 * 跳转到地址管理页面
 */
function goToAddressPage() {
  router.push('/user/addresses');
}
</script>

<style scoped>
.no-address {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #999;
}

.selected-address {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.address-info {
  flex: 1;
  padding-right: 20px;
}

.address-contact {
  font-weight: 500;
  margin-bottom: 5px;
}

.address-detail {
  color: #666;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.debug-info {
  font-size: 11px;
  color: #999;
  margin-top: 5px;
}

.debug-label {
  font-weight: bold;
  margin-right: 5px;
}

.debug-value {
  margin-right: 10px;
}

.debug-value.error {
  color: #f56c6c;
}
</style>