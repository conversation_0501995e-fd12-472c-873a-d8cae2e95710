<!--
 * 作者: 张二浩
 * 日期: 2025-01-27
 * 版本: 1.0.0
 * 描述: 促销活动选择组件，用于结算页面的促销活动选择功能
 * 功能: 显示可用促销活动、处理促销活动选择、显示促销优惠金额
-->
<template>
  <div class="promotion-section">
    <div class="promotion-header">
      <span class="promotion-label">促销活动</span>
      <span v-if="promotionDiscount > 0" class="promotion-discount">
        -¥{{ promotionDiscount.toFixed(2) }}
      </span>
    </div>
    
    <el-select
      :model-value="selectedPromotion?.id || ''"
      @update:model-value="handlePromotionChange"
      placeholder="选择促销活动"
      class="promotion-select"
    >
      <el-option value="" label="不使用促销活动"></el-option>
      <el-option
        v-for="promotion in availablePromotions"
        :key="promotion.id"
        :value="promotion.id"
        :label="getPromotionLabel(promotion)"
        :disabled="!isPromotionAvailable(promotion)"
      >
        <div class="promotion-option">
          <div class="promotion-name">{{ promotion.name }}</div>
          <div class="promotion-desc">{{ getPromotionDescription(promotion) }}</div>
          <div v-if="!isPromotionAvailable(promotion)" class="promotion-tip">
            {{ getPromotionTip(promotion) }}
          </div>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { Promotion } from '@/modules/user/types/promotion';
import { PromotionType } from '@/modules/user/types/promotion';

/**
 * 组件属性定义
 */
interface Props {
  promotions: Promotion[];
  selectedPromotion: Promotion | null;
  merchantItemsTotal: number;
}

/**
 * 组件事件定义
 */
interface Emits {
  (e: 'update:selected-promotion', value: Promotion | null): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

/**
 * 可用的促销活动
 */
const availablePromotions = computed(() => {
  return props.promotions || [];
});

/**
 * 计算促销优惠金额
 */
const promotionDiscount = computed(() => {
  if (!props.selectedPromotion || !isPromotionAvailable(props.selectedPromotion)) {
    return 0;
  }
  
  const promotion = props.selectedPromotion;
  const total = props.merchantItemsTotal;
  
  switch (promotion.type) {
    case PromotionType.FULL_REDUCTION:
      return promotion.discount_amount || 0;
    case PromotionType.DISCOUNT:
      return total * ((promotion.discount_rate || 0) / 100);
    default:
      return 0;
  }
});

/**
 * 检查促销活动是否可用
 * @param promotion 促销活动
 * @returns 是否可用
 */
function isPromotionAvailable(promotion: Promotion): boolean {
  if (!promotion.condition_amount) {
    return true;
  }
  return props.merchantItemsTotal >= promotion.condition_amount;
}

/**
 * 获取促销活动标签
 * @param promotion 促销活动
 * @returns 标签文本
 */
function getPromotionLabel(promotion: Promotion): string {
  return promotion.name;
}

/**
 * 获取促销活动描述
 * @param promotion 促销活动
 * @returns 描述文本
 */
function getPromotionDescription(promotion: Promotion): string {
  if (promotion.description) {
    return promotion.description;
  }
  
  switch (promotion.type) {
    case PromotionType.FULL_REDUCTION:
      return `满${promotion.condition_amount}减${promotion.discount_amount}`;
    case PromotionType.DISCOUNT:
      return `满${promotion.condition_amount}享${promotion.discount_rate}折`;
    case PromotionType.GIFT:
      return '满额赠礼';
    default:
      return '';
  }
}

/**
 * 获取促销活动提示
 * @param promotion 促销活动
 * @returns 提示文本
 */
function getPromotionTip(promotion: Promotion): string {
  if (!promotion.condition_amount) {
    return '';
  }
  
  const diff = promotion.condition_amount - props.merchantItemsTotal;
  if (diff > 0) {
    return `还差¥${diff.toFixed(2)}可用`;
  }
  
  return '';
}

/**
 * 处理促销活动选择变化
 * @param promotionId 选中的促销活动ID
 */
function handlePromotionChange(promotionId: string) {
  console.log('[PromotionSelector] 促销选择变化:', {
    promotionId,
    promotionIdType: typeof promotionId,
    availablePromotions: availablePromotions.value.length,
    promotionsData: availablePromotions.value.map(p => ({ id: p.id, idType: typeof p.id, name: p.name }))
  });
  
  if (!promotionId) {
    console.log('[PromotionSelector] 清除促销选择');
    emit('update:selected-promotion', null);
    return;
  }
  
  // 尝试多种匹配方式
  let promotion = availablePromotions.value.find(p => p.id.toString() === promotionId);
  if (!promotion) {
    promotion = availablePromotions.value.find(p => p.id === parseInt(promotionId));
  }
  if (!promotion) {
    promotion = availablePromotions.value.find(p => p.id === promotionId);
  }
  
  if (promotion) {
    console.log('[PromotionSelector] 找到促销:', promotion.name, '匹配的ID:', promotion.id);
  } else {
    console.log('[PromotionSelector] 未找到促销活动，尝试的匹配:', {
      stringMatch: availablePromotions.value.find(p => p.id.toString() === promotionId),
      numberMatch: availablePromotions.value.find(p => p.id === parseInt(promotionId)),
      directMatch: availablePromotions.value.find(p => p.id === promotionId)
    });
  }
  emit('update:selected-promotion', promotion || null);
}
</script>

<style scoped>
.promotion-section {
  margin: 12px 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.promotion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.promotion-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.promotion-discount {
  font-size: 14px;
  font-weight: 600;
  color: #67c23a;
}

.promotion-select {
  width: 100%;
}

.promotion-option {
  padding: 4px 0;
}

.promotion-name {
  font-weight: 500;
  color: #303133;
}

.promotion-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.promotion-tip {
  font-size: 12px;
  color: #e6a23c;
  margin-top: 2px;
}
</style>