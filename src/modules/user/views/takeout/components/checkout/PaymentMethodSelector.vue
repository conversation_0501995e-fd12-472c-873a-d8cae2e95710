<!--
 * 作者: 张二浩
 * 日期: 2025-01-27
 * 版本: 1.0.0
 * 描述: 支付方式选择组件，用于结算页面的支付方式选择功能
 * 功能: 提供多种支付方式供用户选择
-->
<template>
  <el-form-item label="支付方式">
    <el-radio-group 
      :model-value="paymentMethod" 
      @update:model-value="handlePaymentMethodChange"
    >
      <el-radio value="alipay">支付宝</el-radio>
      <el-radio value="wechat">微信支付</el-radio>
      <el-radio value="balance">余额支付</el-radio>
    </el-radio-group>
  </el-form-item>
</template>

<script setup lang="ts">
/**
 * 组件属性定义
 */
interface Props {
  paymentMethod: string;
}

/**
 * 组件事件定义
 */
interface Emits {
  (e: 'update:payment-method', value: string): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

/**
 * 处理支付方式变化
 * @param value 选中的支付方式值
 */
function handlePaymentMethodChange(value: string) {
  emit('update:payment-method', value);
}
</script>

<style scoped>
/* 组件样式 */
</style>