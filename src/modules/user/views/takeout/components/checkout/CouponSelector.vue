<!--
 * 作者: 张二浩
 * 日期: 2025-01-27
 * 版本: 1.0.0
 * 描述: 优惠券选择组件，用于结算页面的优惠券选择功能
 * 功能: 显示可用优惠券、处理优惠券选择、显示优惠金额
-->
<template>
  <div class="coupon-section">
    <div class="coupon-header">
      <span class="coupon-label">优惠券</span>
      <span v-if="couponDiscount > 0" class="coupon-discount">
        -¥{{ couponDiscount.toFixed(2) }}
      </span>
    </div>
    
    <el-select
      :model-value="selectedCoupon?.id?.toString() || ''"
      @update:model-value="handleCouponChange"
      placeholder="选择优惠券"
      class="coupon-select"
    >
      <!-- 调试信息 -->
      <!-- selectedCoupon: {{ selectedCoupon }} -->
      <!-- selectedCoupon?.id: {{ selectedCoupon?.id }} -->
      <!-- model-value: {{ selectedCoupon?.id?.toString() || '' }} -->
      <el-option value="" label="不使用优惠券"></el-option>
      <el-option
        v-for="coupon in availableCoupons"
        :key="coupon.id"
        :value="coupon.id.toString()"
        :label="getCouponLabel(coupon)"
        :disabled="!isCouponAvailable(coupon)"
      >
        <div class="coupon-option">
          <div class="coupon-name">{{ coupon.name }}</div>
          <div class="coupon-desc">{{ getCouponDescription(coupon) }}</div>
          <div v-if="!isCouponAvailable(coupon)" class="coupon-tip">
            {{ getCouponTip(coupon) }}
          </div>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import type { UserCoupon } from '@/modules/user/types/coupon';
import { CouponType, CouponStatus } from '@/modules/user/types/coupon';

/**
 * 组件属性定义
 */
interface Props {
  coupons: UserCoupon[];
  selectedCoupon: UserCoupon | null;
  merchantItemsTotal: number;
}

/**
 * 组件事件定义
 */
interface Emits {
  (e: 'update:selected-coupon', value: UserCoupon | null): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

/**
 * 可用的优惠券
 */
const availableCoupons = computed(() => {
  return props.coupons || [];
});

/**
 * 计算优惠券优惠金额
 */
const couponDiscount = computed(() => {
  if (!props.selectedCoupon || !isCouponAvailable(props.selectedCoupon)) {
    return 0;
  }
  
  const coupon = props.selectedCoupon;
  const total = props.merchantItemsTotal;
  
  switch (coupon.type) {
    case CouponType.DISCOUNT: // 满减券
      if (coupon.min_order_amount && props.merchantItemsTotal < coupon.min_order_amount) {
        return 0;
      }
      return coupon.amount || 0;
    case CouponType.PERCENTAGE: // 折扣券
      if (coupon.min_order_amount && props.merchantItemsTotal < coupon.min_order_amount) {
        return 0;
      }
      return total * ((coupon.rate || 0) / 100);
    default:
      return 0;
  }
});

/**
 * 检查优惠券是否可用
 * @param coupon 优惠券
 * @returns 是否可用
 */
function isCouponAvailable(coupon: UserCoupon): boolean {
  // 检查coupon对象的can_use属性（如果存在）
  if (coupon.can_use !== undefined) {
    return coupon.can_use;
  }
  
  // 检查status属性（如果存在）
  if (coupon.status !== undefined) {
    // 使用CouponStatus枚举进行比较
    return coupon.status === CouponStatus.UNUSED;
  }
  
  // 检查最低消费金额
  if (coupon.min_order_amount) {
    return props.merchantItemsTotal >= coupon.min_order_amount;
  }
  
  // 默认可用
  return true;
}

/**
 * 获取优惠券标签
 * @param coupon 优惠券
 * @returns 标签文本
 */
function getCouponLabel(coupon: UserCoupon): string {
  return coupon.name;
}

/**
 * 获取优惠券描述
 * @param coupon 优惠券
 * @returns 描述文本
 */
function getCouponDescription(coupon: UserCoupon): string {
  if (coupon.description) {
    return coupon.description;
  }
  
  switch (coupon.type) {
    case CouponType.DISCOUNT:
      return coupon.min_order_amount
        ? `满${coupon.min_order_amount}减${coupon.amount}`
        : `立减${coupon.amount}元`;
    case CouponType.PERCENTAGE:
      return coupon.min_order_amount
        ? `满${coupon.min_order_amount}享${coupon.rate}折`
        : `${coupon.rate}折优惠`;
    default:
      return '';
  }
}

/**
 * 获取优惠券提示
 * @param coupon 优惠券
 * @returns 提示文本
 */
function getCouponTip(coupon: UserCoupon): string {
  if (!coupon.min_order_amount) {
    return '';
  }
  
  const diff = coupon.min_order_amount - props.merchantItemsTotal;
  if (diff > 0) {
    return `还差¥${diff.toFixed(2)}可用`;
  }
  
  return '';
}

/**
 * 处理优惠券选择变化
 * @param couponId 选中的优惠券ID
 */
function handleCouponChange(couponId: string) {
  console.log('[CouponSelector] 优惠券选择变化:', {
    couponId,
    couponIdType: typeof couponId,
    availableCoupons: availableCoupons.value,
    availableCouponsType: typeof availableCoupons.value,
    availableCouponsLength: Array.isArray(availableCoupons.value) ? availableCoupons.value.length : 'Not an array',
    couponsData: Array.isArray(availableCoupons.value) ? availableCoupons.value.map(c => ({ id: c.id, idType: typeof c.id, name: c.name })) : 'Not an array'
  });
  
  if (!couponId) {
    console.log('[CouponSelector] 清除优惠券选择');
    emit('update:selected-coupon', null);
    return;
  }
  
  // 确保availableCoupons是数组
  if (!Array.isArray(availableCoupons.value)) {
    console.error('[CouponSelector] availableCoupons不是数组:', availableCoupons.value);
    emit('update:selected-coupon', null);
    return;
  }
  
  // 尝试多种匹配方式
  let coupon = availableCoupons.value.find(c => c.id.toString() === couponId);
  if (!coupon) {
    coupon = availableCoupons.value.find(c => c.id === parseInt(couponId));
  }
  if (!coupon) {
    coupon = availableCoupons.value.find(c => c.id === couponId);
  }
  
  if (coupon) {
    console.log('[CouponSelector] 找到优惠券:', coupon.name, '匹配的ID:', coupon.id);
  } else {
    console.log('[CouponSelector] 未找到优惠券，尝试的匹配:', {
      stringMatch: availableCoupons.value.find(c => c.id.toString() === couponId),
      numberMatch: availableCoupons.value.find(c => c.id === parseInt(couponId)),
      directMatch: availableCoupons.value.find(c => c.id === couponId)
    });
  }
  emit('update:selected-coupon', coupon || null);
}

// 监听selectedCoupon变化
watch(() => props.selectedCoupon, (newVal, oldVal) => {
  console.log('[CouponSelector] selectedCoupon变化:', {
    oldVal,
    newVal,
    newValId: newVal?.id,
    newValIdString: newVal?.id?.toString(),
    modelValue: newVal?.id?.toString() || ''
  });
}, { immediate: true, deep: true });

// 监听availableCoupons变化
watch(() => props.coupons, (newVal) => {
  console.log('[CouponSelector] coupons变化:', {
    coupons: newVal,
    length: newVal?.length || 0
  });
}, { immediate: true });
</script>

<style scoped>
.coupon-section {
  margin: 12px 0;
  padding: 12px;
  background: #fff2e8;
  border-radius: 6px;
}

.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.coupon-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.coupon-discount {
  font-size: 14px;
  font-weight: 600;
  color: #e6a23c;
}

.coupon-select {
  width: 100%;
}

.coupon-option {
  padding: 4px 0;
}

.coupon-name {
  font-weight: 500;
  color: #303133;
}

.coupon-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.coupon-tip {
  font-size: 12px;
  color: #e6a23c;
  margin-top: 2px;
}
</style>