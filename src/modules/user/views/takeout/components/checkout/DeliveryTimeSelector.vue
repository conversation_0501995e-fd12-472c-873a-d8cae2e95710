<!--
 * 作者: 张二浩
 * 日期: 2025-01-27
 * 版本: 1.0.0
 * 描述: 配送时间选择组件，用于结算页面的配送时间选择功能
 * 功能: 提供多个配送时间选项供用户选择
-->
<template>
  <el-form-item label="配送时间">
    <el-select 
      :model-value="deliveryTime" 
      @update:model-value="handleDeliveryTimeChange"
      placeholder="请选择期望配送时间"
    >
      <el-option label="尽快送达" value=""></el-option>
      <el-option label="今天 12:00-13:00" value="today_12_13"></el-option>
      <el-option label="今天 18:00-19:00" value="today_18_19"></el-option>
      <el-option label="明天 12:00-13:00" value="tomorrow_12_13"></el-option>
      <el-option label="明天 18:00-19:00" value="tomorrow_18_19"></el-option>
    </el-select>
  </el-form-item>
</template>

<script setup lang="ts">
/**
 * 组件属性定义
 */
interface Props {
  deliveryTime: string;
}

/**
 * 组件事件定义
 */
interface Emits {
  (e: 'update:delivery-time', value: string): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

/**
 * 处理配送时间变化
 * @param value 选中的配送时间值
 */
function handleDeliveryTimeChange(value: string) {
  emit('update:delivery-time', value);
}
</script>

<style scoped>
/* 组件样式 */
</style>