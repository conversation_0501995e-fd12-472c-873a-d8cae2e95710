<!--
 * 作者: 张二浩
 * 日期: 2025-01-27
 * 版本: 3.0.0
 * 描述: 结算弹窗组件（重构版本），用于外卖订单的结算流程
 * 功能: 收货地址选择、配送时间选择、支付方式选择、商品确认、优惠券使用、订单提交等
 * 重构: 将大组件拆分为多个小组件，提高代码可维护性
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="确认订单"
    width="700px"
    @closed="onDialogClosed"
    class="checkout-dialog"
  >
    <div class="checkout-form">
      <el-form :model="localForm" label-width="80px">
        <!-- 收货地址选择组件 -->
        <AddressSelector
          :selected-address="selectedAddress"
          @open-address-select="openAddressSelect"
        />
        
        <!-- 调试信息 -->
        <!-- <div class="debug-info" style="margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 4px; font-size: 12px;">
          <div><strong>调试信息:</strong></div>
          <div>地址列表数量: {{ addresses.length }}</div>
          <div>当前选中地址ID: {{ localForm.addressId }}</div>
          <div>选中地址对象: {{ selectedAddress?.receiver_name || '无' }}</div>
          <div>默认地址: {{ props.defaultAddress?.receiver_name || '无' }}</div>
        </div> -->

        <!-- 配送时间选择组件 -->
        <DeliveryTimeSelector
          :delivery-time="localForm.deliveryTime"
          @update:delivery-time="localForm.deliveryTime = $event"
        />

        <!-- 支付方式选择组件 -->
        <PaymentMethodSelector
          :payment-method="localForm.paymentMethod"
          @update:payment-method="localForm.paymentMethod = $event"
        />
      </el-form>
      
      <!-- 商家分组列表 -->
      <div class="merchants-section">
        <h3 class="section-title">订单详情</h3>
        <MerchantGroup
          v-for="(group, merchantId) in groupedItems"
          :key="merchantId"
          :merchant-id="merchantId"
          :merchant="group"
          :selected-address="selectedAddress"
          :delivery-price-per-km="deliveryPricePerKm"
          :selected-promotion="merchantPromotions[merchantId] || null"
          :selected-coupon="selectedCoupons[merchantId] || null"
          :available-promotions="getMerchantPromotions(merchantId)"
          :available-coupons="getMerchantCoupons(merchantId)"
          :merchant-remark="getMerchantRemark(merchantId)"
          @update:selected-promotion="handlePromotionChange(merchantId, $event)"
          @update:selected-coupon="handleCouponChange(merchantId, $event)"
          @update:merchant-remark="updateMerchantRemark(merchantId, $event)"
        />
      </div>

      <!-- 订单汇总组件 -->
      <OrderSummary
        :total-price="totalPrice"
        :total-packaging-fee="totalPackagingFee"
        :total-delivery-fee="totalDeliveryFee"
        :total-promotion-discount="totalPromotionDiscount"
        :total-coupon-discount="totalCouponDiscount"
        :final-amount="finalAmount"
      />
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :disabled="!localForm.addressId || !selectedAddress || !localForm.paymentMethod"
          @click="submitOrder"
          :loading="submitting"
        >
          提交订单
        </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 地址选择弹窗 -->
  <address-select-dialog
    v-if="addressDialogVisible"
    v-model="addressDialogVisible"
    :addresses="addresses"
    :selected-address-id="localForm.addressId ? String(localForm.addressId) : ''"
    @select="handleAddressSelected"
  />
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import type { UserAddress } from '@/modules/user/types';
import { CouponStatus } from '@/modules/user/types/coupon';
import type { UserCoupon } from '@/modules/user/types/coupon';
import type { Promotion } from '@/modules/user/types/promotion';

// 导入我们创建的统一类型定义
import type { CartItemType } from '../../../types/cart';
import type { MerchantGroupType } from '../../../types/merchant';

// 将类型UserCoupon定义为Coupon，方便代码读取
// 使用函数表达式形式避免未使用警告
// @ts-ignore - 该函数用于选择优惠券，保留供未来使用
const useCoupon = (coupon: UserCoupon) => {
  selectedCoupons.value[coupon.merchant_id] = coupon;
};

/**
 * 定义商家订单请求类型
 * 用于结算提交时的数据结构
 */
interface MerchantOrderRequest {
  merchantID: string | number;
  items: {
    foodId: string | number; // 使用foodId而不是foodID，与CartItemType一致
    quantity: number;
    variants?: any[];
    combo_items?: any[];
  }[];
  remark?: string;
  couponID?: string | number | null;
  promotionIDs?: Array<string | number>;
  packagingFee?: number;
  deliveryFee?: number;
  // 兼容代码中使用的属性
  cartItemIDs?: Array<string | number>;
  deliveryTime?: string;
}

// 定义创建订单请求类型
interface CreateOrderRequest {
  takeoutAddressID: string | number;
  paymentMethod: string;
  deliveryTime?: string;
  merchantOrders: MerchantOrderRequest[];
  coupons?: Array<{
    couponID: string | number;
    merchantID: string | number;
  }>;
  promotions?: Array<{
    promotionID: string | number;
    merchantID: string | number;
  }>;
}

import { getUserAddresses } from '@/modules/user/api/address';
import { getAvailableCouponsForOrder } from '@/modules/user/api/coupon';
import { getMerchantPromotions as apiGetMerchantPromotions } from '@/modules/user/api/takeout';

// 使用类型别名，确保类型兼容性
type CouponApiResponse = {
  available_coupons: UserCoupon[];
  unavailable_coupons: UserCoupon[];
  [key: string]: any;
};

// 导入子组件
import AddressSelector from './checkout/AddressSelector.vue';
import DeliveryTimeSelector from './checkout/DeliveryTimeSelector.vue';
import PaymentMethodSelector from './checkout/PaymentMethodSelector.vue';
import MerchantGroup from './checkout/MerchantGroup.vue';
import OrderSummary from './checkout/OrderSummary.vue';
import AddressSelectDialog from './AddressSelectDialog.vue';

/**
 * 组件属性定义
 */
interface Props {
  modelValue: boolean;
  totalPrice: number;
  deliveryFee: number;
  groupedItems: Record<string, MerchantGroupType>;
  deliveryPricePerKm: number;
  defaultAddress: UserAddress | null; // 确保类型兼容性
}

/**
 * 组件事件定义
 */
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'submit', orderData: CreateOrderRequest): void;
  (e: 'address-changed', address: UserAddress | null): void;
}

const props = defineProps<Props>();
// 使用props以消除未使用变量警告
void props;
const emit = defineEmits<Emits>();
const router = useRouter();

// 本地状态
const dialogVisible = ref(false);
const addressDialogVisible = ref(false);
const addresses = ref<UserAddress[]>([]);
const submitting = ref(false);

// 表单数据
const localForm = ref({
  addressId: null as string | null,
  deliveryTime: '',
  paymentMethod: 'alipay',
  remark: ''
});

// 促销和优惠券状态
const merchantPromotions = ref<Record<string, any>>({});
const merchantCoupons = ref<Record<string, any>>({});
const merchantRemarks = ref<Record<string, string>>({});
const availablePromotions = ref<Record<string, Promotion[]>>({});  
const availableCoupons = ref<Record<string, UserCoupon[]>>({});
const selectedPromotions = ref<Promotion[]>([]);  
const selectedCoupons = ref<Record<string | number, UserCoupon | null>>({});
const couponsLoading = ref(false);

/**
 * 计算两点间距离（简化版本，实际应使用地图API）
 */
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371; // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * 计算配送费
 */
function calculateDeliveryFee(group: MerchantGroupType): number {
  if (!selectedAddress.value || 
      !selectedAddress.value.location_latitude || 
      !selectedAddress.value.location_longitude ||
      !group.merchant_latitude ||
      !group.merchant_longitude) {
    return 5; // 默认配送费
  }
  
  const distance = calculateDistance(
    selectedAddress.value.location_latitude,
    selectedAddress.value.location_longitude,
    group.merchant_latitude,
    group.merchant_longitude
  );
  
  if (distance <= 3) {
    return 5; // 3公里内固定5元
  } else {
    return 5 + (distance - 3) * props.deliveryPricePerKm; // 超出部分按每公里计费
  }
}

/**
 * 计算包装费
 */
function calculatePackagingFee(group: MerchantGroupType): number {
  return group.items.reduce((total : any, item : any) => {
    return total + (item.packaging_fee || 0) * item.quantity;
  }, 0);
}

/**
 * 计算商家商品总价
 */
function calculateMerchantItemsTotal(group: MerchantGroupType): number {
  return group.items.reduce((total : any, item : any) => {
    return total + (item.totalPrice || (item.price * item.quantity));
  }, 0);
}

/**
 * 计算商家促销优惠
 */
function calculateMerchantPromotionDiscount(group: MerchantGroupType): number {
  return selectedPromotions.value
    .filter(promotion => promotion.merchant_id === group.merchant_id)
    .reduce((total, promotion) => {
      return total + (promotion.discount_amount || 0);
    }, 0);
}

/**
 * 计算商家优惠券优惠
 */
function calculateMerchantCouponDiscount(group: MerchantGroupType): number {
  const selectedCoupon = selectedCoupons.value[group.merchant_id];
  if (!selectedCoupon) return 0;
  
  const merchantTotal = calculateMerchantItemsTotal(group);
  if (merchantTotal >= selectedCoupon.min_order_amount) {
    return selectedCoupon.amount;
  }
  
  return 0;
}

/**
 * 计算商家小计金额（内部使用）
 * @param group 商家组数据
 * @returns 商家小计金额
 * @description 由于该函数可能在未来需要使用，保留不删除
 */
// @ts-ignore - 保留函数供未来使用
const calculateMerchantSubtotal = function(group: MerchantGroupType): number {
  return group.items.reduce((total : any, item : any) => {
    return total + (item.totalPrice || (item.price * item.quantity));
  }, 0);
}

// 定义计算属性

/**
 * 选中地址对象
 */
const selectedAddress = computed(() => {
  if (!localForm.value.addressId) return props.defaultAddress || null;
  // 使用类型转换确保地址ID匹配，避免字符串和数字类型不匹配的问题
  return addresses.value.find(addr => String(addr.id) === String(localForm.value.addressId)) || props.defaultAddress || null;
});

/**
 * 商品总价（不含配送费和包装费）
 */
const totalPrice = computed(() => {
  return Object.values(props.groupedItems).reduce((sum, group) => {
    return sum + group.items.reduce((groupSum, item) => {
      // 优先使用totalPrice，其次使用food.price，最后使用price
      const itemPrice = item.totalPrice || (item.food?.price || item.price || 0);
      const itemTotal = item.totalPrice || (itemPrice * item.quantity);
      return groupSum + itemTotal;
    }, 0);
  }, 0);
});

const totalDeliveryFee = computed(() => {
  return Object.values(props.groupedItems).reduce((total, group) => {
    return total + calculateDeliveryFee(group);
  }, 0);
});

const totalPackagingFee = computed(() => {
  return Object.values(props.groupedItems).reduce((total, group) => {
    return total + calculatePackagingFee(group);
  }, 0);
});

const totalCouponDiscount = computed(() => {
  return Object.values(props.groupedItems).reduce((total, group) => {
    return total + calculateMerchantCouponDiscount(group);
  }, 0);
});

const totalPromotionDiscount = computed(() => {
  return Object.values(props.groupedItems).reduce((total, group) => {
    return total + calculateMerchantPromotionDiscount(group);
  }, 0);
});

const finalAmount = computed(() => {
  return totalPrice.value + totalPackagingFee.value + totalDeliveryFee.value - totalCouponDiscount.value - totalPromotionDiscount.value;
});

// 监听器
watch(() => props.modelValue, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    console.log('[监听器] 对话框打开，开始加载数据...');
    loadAddresses();
    loadMerchantPromotions();
    loadAvailableCoupons();
  }
});

watch(dialogVisible, (newVal) => {
  emit('update:modelValue', newVal);
});

// 监听地址变化
watch(selectedAddress, (newAddress) => {
  if (newAddress) {
    emit('address-changed', newAddress);
  }
}, { deep: true });

// 地址相关方法
async function loadAddresses() {
  try {
    // 添加类型断言，明确指定返回的数据结构
    const response = await getUserAddresses() as {
      list: UserAddress[];
      total: number;
      page: number;
      pageSize: number;
    } | UserAddress[]; // 兼容两种返回格式
    
    console.log('用户地址加载完成:', response);
    
    // 处理后端返回的数据结构 { list: Array, total: number, page: number, pageSize: number }
    const addressList = Array.isArray(response) ? response : (response?.list || []);
    
    // 确保地址列表是数组
    addresses.value = addressList;
    
    console.log('处理后的地址列表:', addresses.value);
    
    // 如果有默认地址，自动选择
    if (props.defaultAddress) {
      localForm.value.addressId = String(props.defaultAddress.id); // 确保类型一致性
    } else {
      const defaultAddress = addresses.value.find(addr => addr.is_default);
      if (defaultAddress) {
        localForm.value.addressId = String(defaultAddress.id); // 确保类型一致性
        console.log('自动选择默认地址:', {
          addressId: defaultAddress.id,
          addressIdType: typeof defaultAddress.id,
          receiverName: defaultAddress.receiver_name
        });
      } else if (addresses.value.length > 0) {
        localForm.value.addressId = String(addresses.value[0].id); // 确保类型一致性
        console.log('自动选择第一个地址:', {
          addressId: addresses.value[0].id,
          addressIdType: typeof addresses.value[0].id,
          receiverName: addresses.value[0].receiver_name
        });
      } else {
        console.log('没有可用地址');
        localForm.value.addressId = null;
        ElMessage.warning('您还没有添加收货地址，请先添加地址');
      }
    }
    
    console.log('地址加载完成后的表单状态:', {
      addressId: localForm.value.addressId,
      selectedAddress: selectedAddress.value?.receiver_name || 'null'
    });
  } catch (error) {
    console.error('加载地址失败:', error);
    ElMessage.error('加载地址失败，请稍后重试');
  }
}

function openAddressSelect() {
  addressDialogVisible.value = true;
}

function handleAddressSelected(addressId: string) {
  console.log('地址选择:', {
    selectedAddressId: addressId,
    addressType: typeof addressId,
    availableAddresses: addresses.value.map(addr => ({ id: addr.id, type: typeof addr.id }))
  });
  
  if (!addressId) {
    console.error('选择的地址ID为空');
    ElMessage.error('地址选择失败，请重试');
    return;
  }
  
  // 验证地址是否存在，使用类型转换确保匹配
  const selectedAddr = addresses.value.find(addr => String(addr.id) === String(addressId));
  if (!selectedAddr) {
    console.error('选择的地址不存在:', {
      selectedId: addressId,
      selectedIdType: typeof addressId,
      availableIds: addresses.value.map(addr => ({ id: addr.id, type: typeof addr.id }))
    });
    ElMessage.error('选择的地址无效，请重新选择');
    return;
  }
  
  localForm.value.addressId = String(addressId); // 确保类型一致性
  addressDialogVisible.value = false;
  
  console.log('地址选择成功:', {
    addressId: localForm.value.addressId,
    addressIdType: typeof localForm.value.addressId,
    selectedAddress: selectedAddr.receiver_name
  });
  
  // 触发地址变化事件，通知父组件
  emit('address-changed', selectedAddr);
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
// @ts-ignore - 该函数用于点击跳转地址页面，保留以备未来使用
const goToAddressPage = function() {
  router.push('/user/addresses');
}

// 商家备注相关方法
function getMerchantRemark(merchantId: string): string {
  return merchantRemarks.value[merchantId] || '';
}

function updateMerchantRemark(merchantId: string, remark: string) {
  merchantRemarks.value[merchantId] = remark;
}

// 对话框关闭时重置
function onDialogClosed() {
  localForm.value = {
    addressId: null,
    deliveryTime: '',
    paymentMethod: 'alipay',
    remark: ''
  };
  merchantPromotions.value = {};
  merchantCoupons.value = {};
  merchantRemarks.value = {};
  availablePromotions.value = {};
  availableCoupons.value = {};
  selectedPromotions.value = [];
  selectedCoupons.value = {};
  couponsLoading.value = false;
  console.log('对话框已关闭，表单已重置');
}

// 提交订单
async function submitOrder() {
  console.log('选择收货地址:', localForm.value.addressId, selectedAddress.value);
  // 更严格的地址验证
  if (!localForm.value.addressId || !selectedAddress.value) {
    console.error('地址验证失败:', {
      addressId: localForm.value.addressId,
      selectedAddress: selectedAddress.value,
      addressesCount: addresses.value.length
    });
    
    ElMessage.error('请选择收货地址'+localForm.value.addressId);
    return;
  }
  
  if (!localForm.value.paymentMethod) {
    ElMessage.error('请选择支付方式');
    return;
  }
  
  submitting.value = true;
  
  try {
    // 构建订单请求参数，使用用户期望的数据结构
    const takeoutAddressID = parseInt(localForm.value.addressId || '0');
    
    // 额外验证takeoutAddressID
    if (!takeoutAddressID || takeoutAddressID === 0) {
      console.error('takeoutAddressID验证失败:', {
        originalAddressId: localForm.value.addressId,
        parsedAddressId: takeoutAddressID,
        selectedAddress: selectedAddress.value
      });
      ElMessage.error('收货地址ID无效，请重新选择收货地址');
      submitting.value = false;
      return;
    }
    
    const orderRequest = {
      takeoutAddressID: takeoutAddressID, // 转换为数字类型
      paymentMethod: localForm.value.paymentMethod,
      merchantOrders: [] as MerchantOrderRequest[]
    };
    
    // 构建每个商家的订单
    for (const [merchantId, group] of Object.entries(props.groupedItems)) {
      // 获取购物车项目ID数组（使用购物车项目的ID，而不是食品ID）
      const cartItemIDs = group.items.map((item: CartItemType) => item.id);
      
      // 构造商家订单请求对象
      const merchantOrder: MerchantOrderRequest = {
        merchantID: parseInt(merchantId), // 转换为数字类型
        // 将购物车项目ID转换为符合接口的items数组
        items: group.items.map((item: CartItemType) => ({
          foodId: item.foodId || item.food?.id || item.id, // 使用正确的foodId属性名
          quantity: item.quantity
        })),
        cartItemIDs: cartItemIDs, // 保留购物车项目ID
        deliveryTime: localForm.value.deliveryTime || '',
        remark: getMerchantRemark(merchantId) || '',
        couponID: 0,
        promotionIDs: []
      };
      
      // 添加优惠券信息
      const selectedCoupon = selectedCoupons.value[merchantId];
      if (selectedCoupon) {
        merchantOrder.couponID = parseInt(selectedCoupon.id as string);
        console.log(`商家${merchantId}添加优惠券ID:`, selectedCoupon.id);
      } else {
        merchantOrder.couponID = 0; // 没有选择优惠券时设为0
      }
      
      // 添加促销活动信息
      const selectedPromotion = merchantPromotions.value[merchantId];
      if (selectedPromotion && selectedPromotion.id) {
        merchantOrder.promotionIDs = [selectedPromotion.id];
        console.log(`商家${merchantId}添加促销活动ID:`, selectedPromotion.id);
      } else {
        merchantOrder.promotionIDs = []; // 没有选择促销活动时设为空数组
      }
      
      orderRequest.merchantOrders.push(merchantOrder);
    }
    
    console.log('提交订单数据:', orderRequest);
    emit('submit', orderRequest);
    
  } catch (error) {
    console.error('提交订单失败:', error);
    ElMessage.error('提交订单失败');
  } finally {
    submitting.value = false;
  }
}

// 促销活动相关方法（旧版本，已移至下方完整实现）
// function getMerchantPromotions - 已移至下方完整实现
// function handlePromotionChange - 已移至下方完整实现

// 优惠券相关方法（旧版本，已移至下方完整实现）

/**
 * 加载商家促销信息
 * 按照user_promotion_usage_guide.md文档规范处理促销数据结构
 */
async function loadMerchantPromotions() {
  console.log('[促销加载] 开始加载商家促销信息...');
  
  if (!props.groupedItems || Object.keys(props.groupedItems).length === 0) {
    console.log('[促销加载] 空的groupedItems，放弃加载促销信息');
    return;
  }
  
  const merchantIds = Object.keys(props.groupedItems);
  console.log('[促销加载] 需要加载促销信息的商家IDs:', merchantIds);
  
  try {
    // 根据文档规范，使用正确的API接口获取每个商家的促销信息
    for (const merchantId of merchantIds) {
      console.log(`[促销加载] 开始加载商家${merchantId}的促销信息...`);
      try {
        // 计算该商家的订单总金额
        const group = props.groupedItems[merchantId];
        const orderAmount = calculateMerchantItemsTotal(group);
        
        // 获取商品ID列表（去重）
        const foodIds = [...new Set(
          group.items
            .map((item: any) => item.foodId || item.food?.id)
            .filter((id: any) => id)
        )].join(',');
        
        const requestData = {
          total_amount: orderAmount,
          food_ids: foodIds
        };
        
        // console.log(`[促销加载] 调用apiGetMerchantPromotions API，商家ID: ${merchantId}`);
        // console.log(`[促销加载] 请求参数:`, requestData);
        // console.log(`[促销加载] API函数类型:`, typeof apiGetMerchantPromotions);
        // console.log(`[促销加载] 开始执行API调用...`);
        
        // 使用any类型断言，避免类型检查错误
        const response = await apiGetMerchantPromotions(merchantId, requestData) as any;
        
        // console.log(`[促销加载] API调用完成！`);
        console.log(`[促销加载] 商家 ${merchantId} 促销信息API原始响应:`, response);
        // console.log(`[促销加载] 响应数据类型:`, typeof response);
        // console.log(`[促销加载] 响应是否为数组:`, Array.isArray(response));
        
        // 处理促销数据，适配后端响应的数据结构
        // console.log(`[促销加载] 尝试解析促销数据，响应格式:`, response);
        
        // 先检查是否是“无可用促销活动”的情况
        if (response && response.promotions && response.promotions.length === 0) {
          console.log(`[促销加载] 商家${merchantId}无可用促销活动，跳过处理`);
          merchantPromotions.value[merchantId] = [];
          continue; // 跳过后续处理，直接处理下一个商家
        }
        
        let promotions = [];
        if (Array.isArray(response)) {
          promotions = response;
        } else if (response && response.promotions && Array.isArray(response.promotions)) {
          // 直接处理 {promotion_info, promotions: Array} 格式
          promotions = response.promotions;
          console.log(`[促销加载] 从response.promotions获取促销数据:`, promotions);
        } else if (response && response.data && response.data.promotions) {
          // 兼容 {data: {promotions: Array}} 格式 
          promotions = response.data.promotions;
        } else if (response && Array.isArray(response.data)) {
          // 兼容 {data: Array} 格式
          promotions = response.data;
        } else {
          promotions = [];
          console.log(`[促销加载] 无法从响应中提取促销数据，使用空数组:`, response);
        }
        // console.log(`[促销加载] 最终解析的促销数据:`, promotions);
        // console.log(`[促销加载] 商家 ${merchantId} 解析后的促销数据:`, promotions);
        // console.log(`[促销加载] 商家 ${merchantId} 促销数据类型:`, typeof promotions, '是否为数组:', Array.isArray(promotions));
        
        if (promotions && promotions.length > 0) {
          console.log(`[促销加载] 商家 ${merchantId} 原始促销活动数量: ${promotions.length}`, promotions);
          
          // 根据文档规范，只处理类型为4的满减活动
          const filteredPromotions = promotions.filter((promo: any) => {
            // console.log(`[促销加载] 促销活动 ${promo.id} 类型: ${promo.type}`);
            return promo.type === 4;
          });
          // console.log(`[促销加载] 商家 ${merchantId} 过滤后的满减活动数量: ${filteredPromotions.length}`);
          
          const processedPromotions = filteredPromotions.map((promo: any) => {
              const processedPromotion = processPromotionData(promo, merchantId);
              
              // 计算当前订单金额，判断是否满足条件
              const orderAmount = calculateMerchantItemsTotal(props.groupedItems[merchantId]);
              processedPromotion.is_available = orderAmount >= processedPromotion.min_amount;
              
              // console.log(`[促销加载] 促销活动 ${promo.id} 处理结果:`, {
              //   min_amount: processedPromotion.min_amount,
              //   discount_amount: processedPromotion.discount_amount,
              //   order_amount: orderAmount,
              //   is_available: processedPromotion.is_available
              // });
              
              return processedPromotion;
            });
          
          availablePromotions.value[merchantId] = processedPromotions;
          // console.log(`[促销加载] 商家 ${merchantId} 的促销信息已加载完成，共 ${processedPromotions.length} 个:`, processedPromotions);
          
          // 自动选择最优促销活动
          autoSelectBestPromotion(merchantId, processedPromotions);
        } else {
          // console.log(`[促销加载] 商家 ${merchantId} 暂无促销信息或促销数据为空`);
          availablePromotions.value[merchantId] = [];
        }
      } catch (error) {
        // console.error(`[促销加载] 加载商家 ${merchantId} 促销信息失败:`, error);
        // console.error(`[促销加载] 错误详情:`, {
        //   message: error.message,
        //   stack: error.stack,
        //   response: error.response,
        //   request: error.request
        // });
        availablePromotions.value[merchantId] = [];
      }
    }
    
    // console.log('[促销加载] 所有商家促销信息加载完成，最终状态:', 
    //   Object.keys(availablePromotions.value).map(id => 
    //     `商家${id}: ${availablePromotions.value[id]?.length || 0}个促销活动`).join(', '));
  } catch (error) {
    console.error('[促销加载] 加载商家促销信息失败:', error);
    ElMessage.error('加载促销信息失败，请稍后重试');
    
    // 失败时清空促销信息
    merchantIds.forEach(merchantId => {
      availablePromotions.value[merchantId] = [];
    });
  }
}

/**
 * 处理促销数据，按照文档规范标准化数据结构
 */
function processPromotionData(promo: any, merchantId: string): any {
  let discountAmount = 0;
  let minAmount = 0;
  let perUserLimit = 0;
  let validDays = 0;
  
  // 规范化规则解析，确保与文档一致
  try {
    const rules = typeof promo.rules === 'string' ? JSON.parse(promo.rules) : promo.rules;
    if (rules) {
      // 根据文档规范，满减活动的规则结构
      if (rules.coupon) {
        discountAmount = rules.coupon.amount || 0;
        minAmount = rules.coupon.min_order_amount || 0;
        perUserLimit = rules.coupon.per_user_limit || 0;
        validDays = rules.coupon.valid_days || 0;
      }
      // 兼容直接在rules根级别的字段
      minAmount = minAmount || rules.min_amount || 0;
      discountAmount = discountAmount || rules.amount || 0;
    }
  } catch (error) {
    console.warn('解析促销规则失败:', error);
  }
  
  // 按照文档规范构造促销活动对象
  return {
    ...promo,
    merchant_id: merchantId,
    selected: false,
    discount_amount: discountAmount,
    min_amount: minAmount,
    per_user_limit: perUserLimit,
    valid_days: validDays,
    is_available: false, // 将在外部计算
    promotion_type: 'discount', // 满减类型
    description: promo.description || generatePromotionDescription(minAmount, discountAmount)
  };
}

/**
 * 生成促销活动描述
 */
function generatePromotionDescription(minAmount: number, discountAmount: number): string {
  if (minAmount > 0 && discountAmount > 0) {
    return `满¥${minAmount}减¥${discountAmount}`;
  }
  return '优惠活动';
}

/**
 * 自动选择最优促销活动
 */
function autoSelectBestPromotion(merchantId: string, promotions: any[]) {
  if (promotions.length === 0) return;
  
  const availablePromotions = promotions.filter(p => p.is_available);
  if (availablePromotions.length > 0) {
    // 按折扣金额从大到小排序，选择最优惠的
    const bestPromotion = availablePromotions.sort((a, b) => b.discount_amount - a.discount_amount)[0];
    handlePromotionChange(merchantId, bestPromotion);
    console.log(`商家 ${merchantId} 自动选择最优促销活动:`, bestPromotion.description);
  }
}

/**
 * 加载可用优惠券
 */
async function loadAvailableCoupons() {
  console.log('[优惠券加载] 开始加载商家优惠券...');
  if (!props.groupedItems || Object.keys(props.groupedItems).length === 0) {
    console.log('[优惠券加载] 空的groupedItems，放弃加载优惠券');
    return;
  }

  console.log('[优惠券加载] 需要加载优惠券的商家:', Object.keys(props.groupedItems));
  couponsLoading.value = true;
  
  try {
    const merchantIds = Object.keys(props.groupedItems);
    console.log('需要加载优惠券的商家IDs:', merchantIds);
    
    for (const merchantId of merchantIds) {
      console.log(`开始加载商家${merchantId}的优惠券...`);
      try {
        const group = props.groupedItems[merchantId];
        const orderAmount = calculateMerchantItemsTotal(group);
        console.log(`商家${merchantId}的订单总额: ¥${orderAmount}`);
        
        // 获取商品ID列表（去重）
        const foodIds = [...new Set(
          group.items
            .map((item: any) => item.foodId || item.food?.id)
            .filter((id: any) => id)
        )].join(',');
        console.log(`商家${merchantId}的商品IDs: ${foodIds}`);
        
        // 调用API获取该商家的可用优惠券
        console.log(`请求商家${merchantId}的优惠券，参数:`, {
          merchant_id: merchantId,
          total_amount: orderAmount,
          food_ids: foodIds
        });
        
        const apiResponse = await getAvailableCouponsForOrder({
          merchant_id: merchantId,
          total_amount: orderAmount,
          food_ids: foodIds // 添加food_ids参数
        });
        
        // 正确处理API响应类型
        console.log(`[API响应] 商家${merchantId}优惠券API原始响应:`, apiResponse);
        console.log(`[API响应] 响应类型:`, typeof apiResponse);
        console.log(`[API响应] 响应结构:`, Object.keys(apiResponse || {}));

        const response = apiResponse as unknown as CouponApiResponse;

        // 记录原始响应数据
        console.log(`商家${merchantId}优惠券原始响应:`, response);
        
        // 响应拦截器已将API响应数据扁平化，直接使用response即可
        // 不需要再从response.data.data中获取数据
        const availableCouponsList = response.available_coupons || [];
        const unavailableCouponsList = response.unavailable_coupons || [];
        
        console.log(`商家${merchantId}可用优惠券:`, availableCouponsList);
        console.log(`商家${merchantId}不可用优惠券:`, unavailableCouponsList);
        
        console.log(`商家${merchantId}可用优惠券数量: ${availableCouponsList.length}, 不可用优惠券数量: ${unavailableCouponsList.length}`);
        
        console.log(`商家${merchantId}可用优惠券列表:`, availableCouponsList);
        
        // 先清空当前商家的优惠券选择
        if (selectedCoupons.value[merchantId]) {
          console.log(`清除商家${merchantId}的已选优惠券:`, selectedCoupons.value[merchantId]);
          selectedCoupons.value[merchantId] = null;
        }
        
        // 设置可用优惠券列表
        availableCoupons.value[merchantId] = availableCouponsList;
        console.log(`[优惠券加载] 商家${merchantId}优惠券设置完成，当前可用优惠券:`, availableCoupons.value[merchantId]);

        // 验证优惠券数据完整性
        if (availableCouponsList.length > 0) {
          const firstCoupon = availableCouponsList[0];
          console.log(`[优惠券验证] 第一个优惠券数据结构:`, {
            id: firstCoupon.id,
            name: firstCoupon.name,
            amount: firstCoupon.amount,
            min_order_amount: firstCoupon.min_order_amount,
            can_use: firstCoupon.can_use,
            status: firstCoupon.status,
            type: firstCoupon.type
          });
        }

        // 自动选择最优优惠券
        if (availableCouponsList.length > 0) {
          console.log(`[自动选券] 尝试为商家${merchantId}选择最优优惠券`);
          autoSelectBestCoupon(merchantId, availableCouponsList, orderAmount);
        }
      } catch (error) {
        console.error(`加载商家${merchantId}优惠券失败:`, error);
        ElMessage.error('加载优惠券失败');
      }
    }
  } catch (error) {
    console.error('加载优惠券失败:', error);
    ElMessage.error('加载优惠券失败，请稍后重试');
  } finally {
    couponsLoading.value = false;
  }
}

/**
* 获取商家的可用优惠券
*/
function getMerchantCoupons(merchantId: string | number): UserCoupon[] {
  const coupons = availableCoupons.value[merchantId] || [];
  console.log(`[获取优惠券] 商家${merchantId}的优惠券数量:`, coupons.length);
  if (coupons.length > 0) {
    console.log(`[获取优惠券] 商家${merchantId}的优惠券详情:`, coupons.map(c => ({
      id: c.id,
      name: c.name,
      amount: c.amount,
      min_order_amount: c.min_order_amount,
      can_use: c.can_use,
      status: c.status
    })));
  }
  return coupons;
}

/**
 * 获取选中的优惠券ID（修复函数未定义错误）
 */
// function getSelectedCouponId(merchantId: string | number): string | number | null {
//   const selectedCoupon = selectedCoupons.value[merchantId];
//   const couponId = selectedCoupon?.id || null;
//   console.log(`[获取选中优惠券ID] 商家${merchantId}的选中优惠券ID:`, couponId);
//   return couponId;
// }

/**
* 获取商家的可用促销活动
*/
function getMerchantPromotions(merchantId: string | number): Promotion[] {
  const promotions = availablePromotions.value[merchantId] || [];
  console.log(`[获取促销] 商家${merchantId}的促销活动数量:`, promotions.length);
  return promotions;
}

/**
 * 获取选中的促销活动ID（修复函数未定义错误）
 */
// function getSelectedPromotionId(merchantId: string | number): string | number | null {
//   const selectedPromotion = merchantPromotions.value[merchantId];
//   const promotionId = selectedPromotion?.id || null;
//   console.log(`[获取选中促销ID] 商家${merchantId}的选中促销ID:`, promotionId);
//   return promotionId;
// }

/**
* 处理选择优惠券的回调函数
*/
function handleCouponChange(merchantId: string | number, coupon: UserCoupon | null) {
console.log(`商家 ${merchantId} 选择优惠券:`, coupon?.name || '无');
selectedCoupons.value[merchantId] = coupon;
// 重新计算订单总价 (使用totalAmount替代calculateOrderTotal函数)
// 直接使用计算属性，无需额外调用
console.log(`重新计算订单总额: ¥${finalAmount.value}`);
}

// 注意：订单总价计算已通过计算属性实现，无需单独函数

/**
 * 自动选择最优优惠券
 * @description 该函数用于判断并选择最优的可用优惠券，保留供未来自动化逻辑使用
 */
// @ts-ignore - 保留函数供未来使用
const autoSelectBestCoupon = function(merchantId: string | number, coupons: UserCoupon[], orderAmount: number) {
  if (!coupons || coupons.length === 0) {
    console.log(`[自动选券] 商家${merchantId} 无可用优惠券列表，放弃选择`);
    return;
  }
  
  console.log(`[自动选券] 商家${merchantId} 开始选择，订单金额: ¥${orderAmount}`);
  
  // 检查当前商家是否已有选中的优惠券
  if (selectedCoupons.value[merchantId]) {
    console.log(`[自动选券] 商家${merchantId} 已有选中优惠券:`, 
      JSON.stringify(selectedCoupons.value[merchantId]));
  }
  
  // 筛选出可用的优惠券（满足最低消费要求）
  const usableCoupons = coupons.filter(coupon => {
    // 优先使用can_use字段，如果不存在则尝试使用status字段
    const isUsable = coupon.can_use !== undefined ? coupon.can_use : (coupon.status === CouponStatus.UNUSED);
    
    // 检查最低消费要求
    const meetMinAmount = orderAmount >= (coupon.min_order_amount || 0);
    
    return isUsable && meetMinAmount;
  });
  
  console.log(`[自动选券] 商家${merchantId} 筛选后可用优惠券: ${usableCoupons.length}张`);
  
  if (usableCoupons.length === 0) {
    console.log(`[自动选券] 商家${merchantId} 没有可用优惠券，清除已选`);
    selectedCoupons.value[merchantId] = null;
    return;
  }
  
  // 选择优惠金额最大的优惠券
  console.log(`[自动选券] 开始选择最优优惠券...`);
  const bestCoupon = usableCoupons.reduce((best, current) => {
    // 优先使用discount_amount字段，如果不存在则使用amount字段
    const currentDiscount = current.discount_amount !== undefined ? current.discount_amount : current.amount;
    const bestDiscount = best.discount_amount !== undefined ? best.discount_amount : best.amount;
    
    return (currentDiscount || 0) > (bestDiscount || 0) ? current : best;
  });
  
  // 设置选中优惠券
  selectedCoupons.value[merchantId] = bestCoupon;
};

/**
 * 检查促销活动是否在有效期内
 */
const isPromotionValid = (promotion: Promotion): boolean => {
  const now = new Date();
  
  // 检查开始时间
  if (promotion.start_time) {
    const startTime = new Date(promotion.start_time);
    if (now < startTime) {
      return false;
    }
  }
  
  // 检查结束时间
  if (promotion.end_time) {
    const endTime = new Date(promotion.end_time);
    if (now > endTime) {
      return false;
    }
  }
  
  return true;
};

/**
 * 获取促销描述
 */
const getPromotionDesc = (promotion: Promotion): string => {
  // 如果是自定义的description，直接返回
  if (promotion.description) {
    return promotion.description;
  }
  
  // 使用已处理的字段生成描述
  if (promotion.min_amount && promotion.discount_amount) {
    return `满¥${promotion.min_amount}减¥${promotion.discount_amount}`;
  }
  
  // 否则根据规则生成标准描述文本
  try {
    const rules = typeof promotion.rules === 'string' ? JSON.parse(promotion.rules) : promotion.rules;
    // 根据文档规范，满减活动类型为4，规则包含min_amount和coupon.amount
    if (promotion.type === 4 && rules) {
      const minAmount = rules.coupon?.min_order_amount || rules.min_amount || 0;
      const discountAmount = rules.coupon?.amount || rules.amount || 0;
      if (minAmount && discountAmount) {
        return `满¥${minAmount}减¥${discountAmount}`;
      }
    }
  } catch (error) {
    console.warn('解析促销规则失败:', error);
  }
  
  // 如果无法解析规则，返回默认描述
  return promotion.name || '优惠活动';
};

/**
 * 判断促销活动是否可用
 */
const canUsePromotion = (promotion: Promotion, group: MerchantGroupType): boolean => {
  console.log('[促销判断] 判断促销活动是否可用:', promotion, group);
  if (!promotion || !group) return false;
  
  // 根据文档规范，只有type为4的是满减活动
  if (promotion.type !== 4) return false;
  
  // 检查促销活动是否在有效期内
  if (!isPromotionValid(promotion)) {
    return false;
  }
  
  // 计算当前商家的商品总价（不含配送费、包装费）
  const totalAmount = calculateMerchantItemsTotal(group);
  
  // 使用已处理的min_amount字段，如果不存在则解析rules
  let minAmount = promotion.min_amount;
  if (typeof minAmount === 'undefined') {
    try {
      const rules = typeof promotion.rules === 'string' ? JSON.parse(promotion.rules) : promotion.rules;
      minAmount = rules?.coupon?.min_order_amount || rules?.min_amount || 0;
    } catch (error) {
      console.warn('解析促销规则失败:', error);
      return false;
    }
  }
  
  // 比较最低金额要求
  return totalAmount >= (minAmount || 0);
};

/**
 * 处理促销活动选择回调
 */
function handlePromotionChange(merchantId: string | number, promotionInput: Promotion | string | number | null) {
  console.log(`[促销选择] 商家 ${merchantId} 选择促销活动，原始参数:`, promotionInput, '类型:', typeof promotionInput);
  
  // 处理promotionInput可能为ID的情况
  let promotion: Promotion | null = null;
  
  if (promotionInput === null || promotionInput === undefined) {
    // 空值情况，直接处理为取消选择
    promotion = null;
  } else if (typeof promotionInput === 'object') {
    // 已经是对象类型，直接使用
    promotion = promotionInput as Promotion;
  } else {
    // 收到ID，需要查找完整的promotion对象
    const promotionId = promotionInput;
    console.log(`[促销选择] 收到促销活动ID: ${promotionId}，尝试查找对应促销活动对象`);
    
    // 从merchantPromotions中查找正确的促销活动
    if (merchantPromotions.value[merchantId]) {
      promotion = merchantPromotions.value[merchantId].find(
        (p: Promotion) => p.id.toString() === promotionId.toString()
      ) || null;
      
      if (promotion) {
        console.log(`[促销选择] 找到对应的促销活动:`, promotion.name);
      } else {
        console.warn(`[促销选择] 无法找到ID为${promotionId}的促销活动`);
        return; // 找不到对应的促销活动，不处理
      }
    } else {
      console.warn(`[促销选择] 商家${merchantId}没有可用的促销活动列表`);
      return; // 商家没有可用的促销活动列表，不处理
    }
  }
  
  console.log(`[促销选择] 处理后的促销活动:`, promotion?.name || '无');
  
  // 取消选择促销
  if (!promotion) {
    console.log('[促销选择] 取消商家', merchantId, '的促销活动');
    merchantPromotions.value[merchantId] = null;
    
    // 移除该商家的促销选择
    selectedPromotions.value = selectedPromotions.value.filter(p => p.merchant_id !== merchantId);
    return;
  }
  
  // 检查促销活动是否可用
  if (!isPromotionValid(promotion)) {
    console.warn('[促销选择] 促销活动已过期或未开始:', promotion.name);
    ElMessage.warning('该促销活动已过期或未开始');
    return;
  }
  
  const group = props.groupedItems[merchantId];
  if (!group) {
    console.warn('[促销选择] 未找到商家数据:', merchantId);
    ElMessage.warning('未找到商家数据');
    return;
  }
  
  // 检查商家购物车金额是否满足促销条件
  const merchantTotal = group.items.reduce((sum: number, item: CartItemType) => sum + item.price * item.quantity, 0);
  
  if (promotion.min_amount && merchantTotal < promotion.min_amount) {
    console.warn('[促销选择] 未满足满减条件:', merchantTotal, '<', promotion.min_amount);
    ElMessage.warning(`未满足满减条件，还差¥${(promotion.min_amount - merchantTotal).toFixed(2)}`);
    return;
  }
  
  // 跳过无效的促销活动（如“无可用促销活动”）
  if (promotion.name === '无可用促销活动' || promotion.description === '无可用促销活动') {
    console.log('[促销选择] 跳过无效的促销活动:', promotion.name);
    return;
  }
  
  // 检查促销是否可用
  if (!canUsePromotion(promotion, group)) {
    console.warn('[促销选择] 促销活动不满足使用条件:', promotion.name);
    ElMessage.warning('该促销活动不满足使用条件'+promotion.name);
    return;
  }
  
  // 根据文档规范，获取促销活动的折扣金额
  let discountAmount = promotion.discount_amount || 0;
  if (!discountAmount && promotion.rules) {
    try {
      const rules = typeof promotion.rules === 'string' ? 
        JSON.parse(promotion.rules) : promotion.rules;
        
      if (rules.coupon && rules.coupon.amount) {
        discountAmount = rules.coupon.amount;
        // 注意: 不能直接修改 promotion.discount_amount，它可能是只读属性
      }
    } catch (error) {
      console.warn('[促销选择] 解析促销规则失败:', error);
    }
  }
  
  // 更新商家促销选择
  merchantPromotions.value[merchantId] = promotion;
  
  // 先移除该商家之前的促销选择
  selectedPromotions.value = selectedPromotions.value.filter(p => p.merchant_id !== merchantId);
  
  // 添加新的促销选择，只保留必要字段（根据文档规范）
  selectedPromotions.value.push({
    id: promotion.id,
    type: promotion.type, // 促销类型，满减为4
    name: promotion.name,
    description: promotion.description || getPromotionDesc(promotion),
    rules: promotion.rules,
    discount_amount: discountAmount,
    merchant_id: merchantId
  });
  
  // 显示成功提示
  if (discountAmount > 0) {
    ElMessage.success(`已选择促销活动，可节省¥${discountAmount.toFixed(2)}`);
  } else {
    ElMessage.success('已选择促销活动');
  }
  
  console.log('[促销选择] 商家', merchantId, '选择促销:', 
    promotion.name, '折扣金额:', discountAmount);
  console.log('[促销选择] 当前已选促销数量:', selectedPromotions.value.length);
}

// 监听对话框显示状态（已移除，避免与上方的监听器重复）
// 注意：这个监听器与上方的watch(() => props.modelValue)重复，已注释掉
// watch(() => props.modelValue, (newVal) => {
//   dialogVisible.value = newVal;
//   if (newVal) {
//     console.log('对话框打开，开始加载数据...');
//     loadAddresses();
//     loadMerchantPromotions();
//     setTimeout(() => {
//       console.log('开始加载商家优惠券...');
//       loadAvailableCoupons();
//     }, 300);
//   }
// });

/**
 * 调试函数：验证优惠券数据完整性
 */
function debugCouponData() {
  console.log('=== 优惠券数据完整性检查 ===');
  console.log('可用优惠券数据:', availableCoupons.value);
  console.log('选中优惠券数据:', selectedCoupons.value);

  Object.keys(availableCoupons.value).forEach(merchantId => {
    const coupons = availableCoupons.value[merchantId];
    console.log(`商家${merchantId}:`, {
      优惠券数量: coupons.length,
      优惠券列表: coupons.map(c => ({
        id: c.id,
        name: c.name,
        amount: c.amount,
        can_use: c.can_use
      }))
    });
  });
}

// 组件挂载时初始化
onMounted(() => {
  console.log('[onMounted] CheckoutDialog组件挂载，初始状态:', {
    modelValue: props.modelValue,
    defaultAddress: props.defaultAddress?.receiver_name || '无'
  });

  // 注意：不在这里调用loadAvailableCoupons，避免与watch监听器重复调用
  // loadAvailableCoupons会在watch监听器中当对话框打开时调用

  // 选中默认地址
  if (props.defaultAddress) {
    localForm.value.addressId = props.defaultAddress.id;
    console.log('[onMounted] 自动选择默认地址:', props.defaultAddress.receiver_name);
  }

  // 5秒后执行调试检查
  setTimeout(() => {
    debugCouponData();
  }, 5000);
});

// 监听对话框关闭
watch(() => dialogVisible.value, (newVal) => {
  emit('update:modelValue', newVal);
  if (!newVal) {
    onDialogClosed();
  }
});

/**
 * 将组件重要的函数和计算属性暴露给父组件
 */
defineExpose({
  // 计算属性
  totalPrice,
  selectedAddress,
  totalDeliveryFee,
  totalPackagingFee,
  totalCouponDiscount,
  totalPromotionDiscount,
  finalAmount,
  
  // 地址相关
  openAddressSelect,
  handleAddressSelected,
  
  // 优惠相关
  getMerchantPromotions,
  getMerchantCoupons,
  handlePromotionChange,
  handleCouponChange,
  getMerchantRemark,
  updateMerchantRemark,
  
  // 订单提交
  submitOrder,
  
  // 组件引用
  AddressSelector,
});
</script>

<style scoped>
.checkout-dialog {
  /* 修复嵌套CSS语法 */
}

.checkout-dialog .checkout-form {
  max-height: 70vh;
  overflow-y: auto;
}

.merchants-section {
  margin-top: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
