<!--
  用户账户设置页面
  提供账户安全、通知设置、隐私设置、账户余额变动记录等功能
  <AUTHOR>
  @date 2025-01-20
  @version 1.0.1
  @description 用户设置页面，包含账户安全、通知设置、隐私设置、账户余额变动记录查询功能
-->
<template>
  <div class="user-settings-page">
    <el-tabs v-model="activeTab" tab-position="left" class="settings-tabs" @tab-change="handleTabChange">
      <!-- 账户安全选项卡 -->
      <el-tab-pane label="账户安全" name="security">
        <h3 class="section-title">账户安全</h3>
        
        <el-card class="setting-card">
          <div class="setting-item">
            <div class="setting-info">
              <h4>登录密码</h4>
              <p>建议定期修改密码，提高账户安全性</p>
            </div>
            <el-button @click="showChangePasswordDialog">修改</el-button>
          </div>

          <el-divider />
          
          <div class="setting-item">
            <div class="setting-info">
              <h4>手机验证</h4>
              <p v-if="userInfo.phone">已绑定：{{ formatPhone(userInfo.phone) }}</p>
              <p v-else>未绑定手机号</p>
            </div>
            <el-button v-if="userInfo.phone" @click="showUpdatePhoneDialog">修改</el-button>
            <el-button v-else type="primary" @click="showBindPhoneDialog">绑定</el-button>
          </div>

          <el-divider />
          
          <div class="setting-item">
            <div class="setting-info">
              <h4>邮箱验证</h4>
              <p v-if="userInfo.email">已绑定：{{ userInfo.email }}</p>
              <p v-else>未绑定邮箱</p>
            </div>
            <el-button v-if="userInfo.email" @click="showUpdateEmailDialog">修改</el-button>
            <el-button v-else type="primary" @click="showBindEmailDialog">绑定</el-button>
          </div>
        </el-card>
        
        <h3 class="section-title">登录设备管理</h3>
        
        <el-card class="setting-card">
          <div class="device-list">
            <div class="device-item" v-for="(device, index) in loginDevices" :key="index">
              <div class="device-icon">
                <el-icon v-if="device.type === 'desktop'"><Monitor /></el-icon>
                <el-icon v-else-if="device.type === 'mobile'"><Iphone /></el-icon>
                <el-icon v-else><Connection /></el-icon>
              </div>
              <div class="device-info">
                <h4>{{ device.name }}</h4>
                <p>最后登录：{{ device.lastLogin }}</p>
                <p>IP地址：{{ device.ip }}</p>
              </div>
              <div class="device-actions">
                <el-button v-if="device.isCurrent" type="success" size="small" disabled>当前设备</el-button>
                <el-button v-else type="danger" size="small" @click="logoutDevice(device)">退出登录</el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-tab-pane>
      
      <!-- 通知设置选项卡 -->
      <el-tab-pane label="通知设置" name="notifications">
        <h3 class="section-title">通知设置</h3>
        
        <el-card class="setting-card">
          <div class="setting-item">
            <div class="setting-info">
              <h4>订单通知</h4>
              <p>接收订单状态变更、物流信息等通知</p>
            </div>
            <el-switch v-model="notificationSettings.orderNotification" />
          </div>

          <el-divider />
          
          <div class="setting-item">
            <div class="setting-info">
              <h4>促销通知</h4>
              <p>接收优惠、满减、特价商品等促销信息</p>
            </div>
            <el-switch v-model="notificationSettings.promotionNotification" />
          </div>

          <el-divider />
          
          <div class="setting-item">
            <div class="setting-info">
              <h4>系统通知</h4>
              <p>接收系统更新、功能变更等通知</p>
            </div>
            <el-switch v-model="notificationSettings.systemNotification" />
          </div>
        </el-card>
        
        <div class="action-bar">
          <el-button type="primary" @click="saveNotificationSettings" :loading="saving">保存设置</el-button>
        </div>
      </el-tab-pane>
      
      <!-- 隐私设置选项卡 -->
      <el-tab-pane label="隐私设置" name="privacy">
        <h3 class="section-title">隐私设置</h3>
        
        <el-card class="setting-card">
          <div class="setting-item">
            <div class="setting-info">
              <h4>个人信息显示</h4>
              <p>设置个人信息对其他用户的可见范围</p>
            </div>
            <el-select v-model="privacySettings.profileVisibility">
              <el-option label="所有人可见" value="public" />
              <el-option label="仅好友可见" value="friends" />
              <el-option label="完全隐私" value="private" />
            </el-select>
          </div>

          <el-divider />
          
          <div class="setting-item">
            <div class="setting-info">
              <h4>搜索历史</h4>
              <p>允许保存你的搜索历史，为你提供更精准的推荐</p>
            </div>
            <el-switch v-model="privacySettings.saveSearchHistory" />
          </div>

          <el-divider />
          
          <div class="setting-item">
            <div class="setting-info">
              <h4>浏览记录</h4>
              <p>允许保存你的商品浏览记录，为你提供个性化内容</p>
            </div>
            <el-switch v-model="privacySettings.saveBrowsingHistory" />
          </div>
        </el-card>
        
        <div class="action-bar">
          <el-button type="primary" @click="savePrivacySettings" :loading="saving">保存设置</el-button>
        </div>
      </el-tab-pane>
      
      <!-- 账户余额变动记录选项卡 -->
      <el-tab-pane label="余额记录" name="transactions">
        <h3 class="section-title">账户余额</h3>
        
        <!-- 账户信息卡片 -->
        <el-card class="setting-card account-info-card">
          <div class="account-balance">
            <div class="balance-main">
              <div class="balance-label">可用余额</div>
              <div class="balance-amount">¥{{ accountInfo.balance || '0.00' }}</div>
            </div>
            <div class="balance-details">
              <div class="detail-item">
                <span class="detail-label">冻结金额：</span>
                <span class="detail-value">¥{{ accountInfo.frozen_balance || '0.00' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">累计充值：</span>
                <span class="detail-value">¥{{ accountInfo.total_recharge || '0.00' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">累计消费：</span>
                <span class="detail-value">¥{{ accountInfo.total_consume || '0.00' }}</span>
              </div>
            </div>
          </div>
        </el-card>
        
        <h3 class="section-title">变动记录</h3>
        
        <!-- 筛选条件 -->
        <el-card class="setting-card filter-card">
          <div class="filter-bar">
            <el-select 
              v-model="transactionFilters.transaction_type" 
              placeholder="选择交易类型" 
              style="width: 200px; margin-right: 10px;"
              @change="handleFilterChange"
            >
              <el-option label="全部类型" :value="0" />
              <el-option label="充值" :value="1" />
              <el-option label="消费" :value="2" />
              <el-option label="退款" :value="3" />
              <el-option label="提现" :value="4" />
              <el-option label="转账" :value="5" />
            </el-select>
            <el-button @click="refreshTransactions" :loading="transactionLoading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </el-card>
        
        <!-- 交易记录列表 -->
        <el-card class="setting-card transaction-list-card">
          <div v-loading="transactionLoading" class="transaction-list">
            <div v-if="!transactionList || transactionList.length === 0" class="empty-state">
              <el-empty description="暂无交易记录" />
            </div>
            <div v-else>
              <div 
                v-for="item in transactionList" 
                :key="item.id" 
                class="transaction-item"
              >
                <div class="transaction-info">
                  <div class="transaction-header">
                    <span class="transaction-desc">{{ item.description }}</span>
                    <el-tag 
                      :type="getTransactionStatusClass(item.status)" 
                      size="small"
                    >
                      {{ getTransactionStatusName(item.status) }}
                    </el-tag>
                  </div>
                  <div class="transaction-details">
                    <div class="detail-row">
                      <span class="detail-label">交易时间：</span>
                      <span class="detail-value">{{ item.created_at }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">流水号：</span>
                      <span class="detail-value">{{ item.transaction_no }}</span>
                    </div>
                    <div class="detail-row" v-if="item.remark">
                      <span class="detail-label">备注：</span>
                      <span class="detail-value">{{ item.remark }}</span>
                    </div>
                  </div>
                </div>
                <div class="transaction-amount">
                  <div class="amount" :class="getAmountClass(item.operation)">
                    {{ getOperationSymbol(item.operation) }}¥{{ item.amount }}
                  </div>
                  <div class="balance-info">
                    余额：¥{{ item.after_balance }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 分页 -->
          <div class="pagination-wrapper" v-if="transactionPagination.total > 0">
            <el-pagination
              v-model:current-page="transactionPagination.page"
              v-model:page-size="transactionPagination.size"
              :page-sizes="[10, 20, 50, 100]"
              :total="transactionPagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handlePageSizeChange"
              @current-change="handlePageChange"
            />
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 修改密码弹窗 -->
    <el-dialog v-model="changePasswordDialogVisible" title="修改密码" width="400px">
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="100px">
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input v-model="passwordForm.oldPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="changePasswordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="changePassword" :loading="changing">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 绑定手机号弹窗 -->
    <el-dialog v-model="bindPhoneDialogVisible" title="绑定手机号" width="400px">
      <el-form :model="phoneForm" :rules="phoneRules" ref="phoneFormRef" label-width="100px">
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="phoneForm.phone" />
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <div class="code-input-group">
            <el-input v-model="phoneForm.code" />
            <el-button 
              type="primary" 
              :disabled="!canSendPhoneCode || sendingCode" 
              @click="sendPhoneCode"
            >
              {{ phoneCodeText }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="bindPhoneDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="bindPhone" :loading="binding">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { UserStatus } from '../types';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useUserStore } from '../stores/userStore';
import { sendSmsCode } from '../api/auth';
import type { UserInfo } from '../types';
import { 
  getAccountInfo, 
  getAccountTransactions,
  getTransactionStatusName,
  getTransactionStatusClass,
  type AccountInfo,
  type AccountTransaction,
  type TransactionQueryParams
} from '../api/account';

const router = useRouter();
const userStore = useUserStore();
const passwordFormRef = ref();
const phoneFormRef = ref();

// 当前活动的标签页
const activeTab = ref('security');

// 用户信息
const userInfo = reactive<UserInfo>({
  id: '',
  username: '',
  nickname: '',
  avatar: '',
  phone: '',
  email: '',
  status: UserStatus.ACTIVE,
  createdAt: '',
  updatedAt: '',
  role: '',
});

// 登录设备列表
const loginDevices = ref([
  {
    id: '1',
    name: 'Chrome 浏览器 (Windows)',
    type: 'desktop',
    lastLogin: '2025-05-20 10:30:25',
    ip: '*************',
    isCurrent: true
  },
  {
    id: '2',
    name: 'Safari 浏览器 (iPhone)',
    type: 'mobile',
    lastLogin: '2025-05-19 18:45:12',
    ip: '*************',
    isCurrent: false
  }
]);

// 通知设置
const notificationSettings = reactive({
  orderNotification: true,
  promotionNotification: true,
  systemNotification: true
});

// 隐私设置
const privacySettings = reactive({
  profileVisibility: 'friends',
  saveSearchHistory: true,
  saveBrowsingHistory: true
});

// 修改密码
const changePasswordDialogVisible = ref(false);
const changing = ref(false);
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在6到20个字符之间', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (_rule: any, value: string, callback: any) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
};

// 绑定手机号
const bindPhoneDialogVisible = ref(false);
const binding = ref(false);
const sendingCode = ref(false);
const phoneCodeCountdown = ref(0);
const phoneForm = reactive({
  phone: '',
  code: ''
});

const phoneRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码为6位数字', trigger: 'blur' }
  ]
};

const canSendPhoneCode = computed(() => {
  return /^1[3-9]\d{9}$/.test(phoneForm.phone) && phoneCodeCountdown.value === 0;
});

const phoneCodeText = computed(() => {
  return phoneCodeCountdown.value > 0 ? `${phoneCodeCountdown.value}秒后重新发送` : '获取验证码';
});

// 保存状态
const saving = ref(false);

// 账户信息
const accountInfo = reactive<Partial<AccountInfo>>({
  balance: '0.00',
  frozen_balance: '0.00',
  total_recharge: '0.00',
  total_consume: '0.00'
});

// 交易记录相关
const transactionList = ref<AccountTransaction[]>([]);
const transactionLoading = ref(false);
const transactionFilters = reactive<TransactionQueryParams>({
  transaction_type: 0
});
const transactionPagination = reactive({
  page: 1,
  size: 20,
  total: 0
});

/**
 * 格式化手机号，中间4位显示为星号
 */
function formatPhone(phone: string): string {
  if (!phone) return '';
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}

/**
 * 显示修改密码对话框
 */
function showChangePasswordDialog() {
  passwordForm.oldPassword = '';
  passwordForm.newPassword = '';
  passwordForm.confirmPassword = '';
  changePasswordDialogVisible.value = true;
}

/**
 * 修改密码
 */
async function changePassword() {
  if (!passwordFormRef.value) return;
  
  try {
    await passwordFormRef.value.validate();
    
    changing.value = true;
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success('密码修改成功，请重新登录');
    changePasswordDialogVisible.value = false;
    
    // 退出登录
    await userStore.userLogout();
    router.push('/user/login');
  } catch (error) {
    console.error('修改密码失败:', error);
    ElMessage.error('修改密码失败，请稍后重试');
  } finally {
    changing.value = false;
  }
}

/**
 * 显示绑定手机号对话框
 */
function showBindPhoneDialog() {
  phoneForm.phone = '';
  phoneForm.code = '';
  bindPhoneDialogVisible.value = true;
}

/**
 * 显示更新手机号对话框
 */
function showUpdatePhoneDialog() {
  phoneForm.phone = '';
  phoneForm.code = '';
  bindPhoneDialogVisible.value = true;
}

/**
 * 发送手机验证码
 */
async function sendPhoneCode() {
  if (!canSendPhoneCode.value) return;
  
  try {
    sendingCode.value = true;
    
    // 调用发送验证码API
    await sendSmsCode(phoneForm.phone, 'bind');
    
    // 开始倒计时
    startPhoneCodeCountdown();
    
    ElMessage.success('验证码已发送，请注意查收');
  } catch (error) {
    console.error('发送验证码失败:', error);
    ElMessage.error('发送验证码失败，请稍后重试');
  } finally {
    sendingCode.value = false;
  }
}

/**
 * 开始手机验证码倒计时
 */
function startPhoneCodeCountdown(seconds = 60) {
  phoneCodeCountdown.value = seconds;
  const timer = setInterval(() => {
    phoneCodeCountdown.value--;
    if (phoneCodeCountdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
}

/**
 * 绑定手机号
 */
async function bindPhone() {
  if (!phoneFormRef.value) return;
  
  try {
    await phoneFormRef.value.validate();
    
    binding.value = true;
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 更新用户信息
    userInfo.phone = phoneForm.phone;
    
    ElMessage.success('手机号绑定成功');
    bindPhoneDialogVisible.value = false;
  } catch (error) {
    console.error('绑定手机号失败:', error);
    ElMessage.error('绑定手机号失败，请稍后重试');
  } finally {
    binding.value = false;
  }
}

/**
 * 显示绑定邮箱对话框
 */
function showBindEmailDialog() {
  ElMessage.info('邮箱绑定功能即将上线');
}

/**
 * 显示更新邮箱对话框
 */
function showUpdateEmailDialog() {
  ElMessage.info('邮箱更新功能即将上线');
}

/**
 * 退出指定设备的登录
 */
function logoutDevice(device: any) {
  ElMessageBox.confirm(`确定要退出 ${device.name} 的登录吗？`, '退出登录', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟API调用
    loginDevices.value = loginDevices.value.filter(item => item.id !== device.id);
    ElMessage.success('设备已退出登录');
  }).catch(() => {
    // 取消操作
  });
}

/**
 * 保存通知设置
 */
async function saveNotificationSettings() {
  try {
    saving.value = true;
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success('通知设置已保存');
  } catch (error) {
    console.error('保存通知设置失败:', error);
    ElMessage.error('保存设置失败，请稍后重试');
  } finally {
    saving.value = false;
  }
}

/**
 * 保存隐私设置
 */
async function savePrivacySettings() {
  try {
    saving.value = true;
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success('隐私设置已保存');
  } catch (error) {
    console.error('保存隐私设置失败:', error);
    ElMessage.error('保存设置失败，请稍后重试');
  } finally {
    saving.value = false;
  }
}

/**
 * 获取账户信息
 */
async function fetchAccountInfo() {
  try {
    const response = await getAccountInfo();
    if (response) {
      Object.assign(accountInfo, response);
    }
  } catch (error) {
    console.error('获取账户信息失败:', error);
    ElMessage.error('获取账户信息失败');
  }
}

/**
 * 获取交易记录
 */
async function fetchTransactions() {
  try {
    transactionLoading.value = true;
    
    const params: TransactionQueryParams = {
      page: transactionPagination.page,
      page_size: transactionPagination.size,
      transaction_type: transactionFilters.transaction_type
    };
    
    const response = await getAccountTransactions(params);
    transactionList.value = response?.list || [];
    transactionPagination.total = response?.total || 0;
  } catch (error) {
    console.error('获取交易记录失败:', error);
    ElMessage.error('获取交易记录失败');
  } finally {
    transactionLoading.value = false;
  }
}

/**
 * 处理筛选条件变化
 */
function handleFilterChange() {
  transactionPagination.page = 1;
  fetchTransactions();
}

/**
 * 刷新交易记录
 */
function refreshTransactions() {
  fetchTransactions();
}

/**
 * 处理页码变化
 */
function handlePageChange(page: number) {
  transactionPagination.page = page;
  fetchTransactions();
}

/**
 * 处理页面大小变化
 */
function handlePageSizeChange(size: number) {
  transactionPagination.size = size;
  transactionPagination.page = 1;
  fetchTransactions();
}

/**
 * 获取金额样式类
 * @param operation 操作类型：字符串类型（'increase'/'decrease'）或数字类型（1/2）
 */
function getAmountClass(operation: string | number): string {
  if (typeof operation === 'string') {
    return operation === 'increase' ? 'amount-increase' : 'amount-decrease';
  }
  return operation === 1 ? 'amount-increase' : 'amount-decrease';
}

/**
 * 获取操作符号
 * @param operation 操作类型：字符串类型（'increase'/'decrease'）或数字类型（1/2）
 */
function getOperationSymbol(operation: string | number): string {
  if (typeof operation === 'string') {
    return operation === 'increase' ? '+' : '-';
  }
  return operation === 1 ? '+' : '-';
}

// 加载用户数据
onMounted(() => {
  // 确保用户已登录
  if (!userStore.isLoggedIn) {
    router.push('/user/login');
    return;
  }
  
  // 从存储中获取用户信息
  const userData = userStore.userInfo;
  if (userData) {
    Object.assign(userInfo, userData);
  }
  
  // 加载账户信息和交易记录
  fetchAccountInfo();
  fetchTransactions();
  
  // 加载用户设置 - 模拟数据
  // 实际项目中应从API获取
});

// 监听activeTab变化，当切换到交易记录tab时刷新数据
const handleTabChange = (tabName: string) => {
  if (tabName === 'transactions') {
    fetchAccountInfo();
    fetchTransactions();
  }
};
</script>

<style scoped>
.user-settings-page {
  padding: 10px 0;
}

.settings-tabs {
  min-height: 500px;
}

.section-title {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
}

.setting-card {
  margin-bottom: 20px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}

.setting-info h4 {
  margin: 0 0 5px;
  font-size: 16px;
}

.setting-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.action-bar {
  margin-top: 20px;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.device-icon {
  font-size: 24px;
  color: #409EFF;
  margin-right: 15px;
}

.device-info {
  flex: 1;
}

.device-info h4 {
  margin: 0 0 5px;
  font-size: 16px;
}

.device-info p {
  margin: 0 0 3px;
  color: #909399;
  font-size: 14px;
}

.device-actions {
  margin-left: 15px;
}

.code-input-group {
  display: flex;
  gap: 10px;
}

:deep(.el-tabs__content) {
  padding-left: 20px;
}

/* 账户余额样式 */
.account-info-card {
  margin-bottom: 20px;
}

.account-balance {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.balance-main {
  text-align: center;
}

.balance-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.balance-amount {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 10px;
}

.balance-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 14px;
  color: #606266;
}

.detail-value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

/* 筛选条件样式 */
.filter-card {
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 交易记录样式 */
.transaction-list-card {
  margin-bottom: 20px;
}

.transaction-list {
  min-height: 200px;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-info {
  flex: 1;
  margin-right: 20px;
}

.transaction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.transaction-desc {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.transaction-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-row {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.detail-row .detail-label {
  color: #909399;
  margin-right: 8px;
  min-width: 60px;
}

.detail-row .detail-value {
  color: #606266;
}

.transaction-amount {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.amount {
  font-size: 18px;
  font-weight: bold;
}

.amount-increase {
  color: #67C23A;
}

.amount-decrease {
  color: #F56C6C;
}

.balance-info {
  font-size: 12px;
  color: #909399;
}

/* 分页样式 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .account-balance {
    flex-direction: column;
    gap: 20px;
  }
  
  .balance-details {
    width: 100%;
  }
  
  .transaction-item {
    flex-direction: column;
    gap: 10px;
  }
  
  .transaction-amount {
    align-items: flex-start;
    text-align: left;
  }
  
  .filter-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-bar > * {
    width: 100%;
  }
}
</style>
