<!--
  用户登录页面
  提供账号密码登录和短信验证码登录功能
-->
<template>
  <div class="user-login">
    <div class="login-container">
      <div class="login-header">
        <img v-if="systemInfo?.siteLogo" :src="adjustLinkProtocol(systemInfo?.siteLogo)" alt="siteLogo" class="logo" />
        <h1 class="title">{{ systemInfo?.siteName || 'O_Mall 用户中心' }}</h1>
      </div>

      <AppCard class="login-form-card">
        <h2 class="form-title">用户登录</h2>
        
        <!-- 登录方式选项卡 -->
        <el-tabs v-model="activeTab" class="login-tabs">

          <!-- 账号密码登录选项卡 -->
          <el-tab-pane label="账号密码登录" name="password">
            <AppForm
              ref="passwordFormRef"
              :model="passwordForm"
              :rules="passwordRules"
              label-position="top"
              @keyup.enter="handlePasswordSubmit"
            >
              <AppFormItem label="用户名" prop="username">
                <AppInput
                  v-model="passwordForm.username"
                  placeholder="请输入用户名/手机号"
                  prefix-icon="User"
                />
              </AppFormItem>

              <AppFormItem label="密码" prop="password">
                <AppInput
                  v-model="passwordForm.password"
                  type="password"
                  placeholder="请输入密码"
                  prefix-icon="Lock"
                  show-password
                />
              </AppFormItem>

              <AppFormItem>
                <el-checkbox v-model="rememberUsername">记住用户名</el-checkbox>
              </AppFormItem>
              <AppFormItem>
                <el-checkbox v-model="rememberMe">30天内免登录</el-checkbox>
              </AppFormItem>

              <AppFormItem>
                <el-checkbox v-model="agreePrivacyPolicy" :disabled="loading">
                  我已阅读并同意
                  <el-button link type="primary" @click.stop="showPrivacyPolicy">《隐私协议》</el-button>
                </el-checkbox>
              </AppFormItem>

              <div class="form-actions">
                <AppButton
                  type="primary"
                  :loading="loading"
                  class="submit-btn"
                  :disabled="!agreePrivacyPolicy"
                  @click="handlePasswordSubmit"
                >
                  登录
                </AppButton>
              </div>
            </AppForm>
          </el-tab-pane>
          
          <!-- 手机验证码登录选项卡 -->
          <el-tab-pane label="手机验证码登录" name="sms">
            <AppForm
              ref="smsFormRef"
              :model="smsForm"
              :rules="smsRules"
              label-position="top"
              @keyup.enter="handleSmsSubmit"
            >
              <AppFormItem label="手机号" prop="phone">
                <AppInput
                  v-model="smsForm.phone"
                  placeholder="请输入手机号"
                  prefix-icon="Iphone"
                  maxlength="11"
                  @input="formatMobileInput"
                />
              </AppFormItem>

              <AppFormItem label="验证码" prop="code">
                <div class="verification-code-input">
                  <AppInput
                    v-model="smsForm.code"
                    placeholder="请输入验证码"
                    prefix-icon="Key"
                    maxlength="6"
                    @input="formatCodeInput"
                  />
                  <AppButton
                    type="primary"
                    class="send-code-btn"
                    :disabled="!canSendCode || smsLoading"
                    @click.prevent="sendVerificationCode"
                  >
                    {{ sendCodeText }}
                  </AppButton>
                </div>
              </AppFormItem>

              <AppFormItem>
                <el-checkbox v-model="rememberMe">30天内免登录</el-checkbox>
              </AppFormItem>

              <AppFormItem>
                <el-checkbox v-model="agreePrivacyPolicy" :disabled="smsLoading">
                  我已阅读并同意
                  <el-button link type="primary" @click.stop="showPrivacyPolicy">《隐私协议》</el-button>
                </el-checkbox>
              </AppFormItem>

              <div class="form-actions">
                <AppButton
                  type="primary"
                  :loading="smsLoading"
                  class="submit-btn"
                  :disabled="!agreePrivacyPolicy"
                  @click="handleSmsSubmit"
                >
                  登录
                </AppButton>
              </div>
            </AppForm>
          </el-tab-pane>
        </el-tabs>

        <div class="login-footer">
          <p>还没有账号？</p>
          <AppButton
            type="default"
            class="register-btn"
            @click="toRegister"
            text
          >
            立即注册 <el-icon><ArrowRight /></el-icon>
          </AppButton>
        </div>
      </AppCard>
    </div>
  </div>

  <!-- 隐私协议弹窗 -->
  <el-dialog
    v-model="privacyDialogVisible"
    title="隐私协议"
    width="60%"
    :close-on-click-modal="false"
  >
    <div v-if="privacyLoading" class="privacy-loading">
      <el-skeleton :rows="10" animated />
    </div>
    <div v-else-if="privacyError" class="privacy-error">
      <el-empty description="加载隐私协议失败，请稍后再试" />
    </div>
    <div v-else class="privacy-content">
      <h3>{{ privacyPolicy.version }} ({{ privacyPolicy.last_updated }})</h3>
      <div v-html="privacyPolicy.content"></div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="privacyDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="agreeToPrivacyPolicy">
          同意并继续
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { initRouteAndNavigate } from '../utils/routeUtils';
import { useUserStore } from '../stores/userStore';
import { ElMessage } from 'element-plus';
import { sendSmsCode } from '../api/auth';
import AppCard from '@/components/base/AppCard.vue';
import AppForm from '@/components/form/AppForm.vue';
import AppFormItem from '@/components/form/AppFormItem.vue';
import AppInput from '@/components/form/AppInput.vue';
import AppButton from '@/components/base/AppButton.vue';
import { useSystemStore } from '@/stores/systemStore';
import { adjustLinkProtocol } from '@/utils/format';
import type { DeviceInfo } from '../types';

const systemStore = useSystemStore();
const systemInfo = computed(() => systemStore.systemInfo);

// 定义隐私协议类型
interface PrivacyPolicyInfo {
  version: string;
  last_updated: string;
  content: string;
}

const router = useRouter();
const userStore = useUserStore();
const passwordFormRef = ref();
const smsFormRef = ref();
const loading = ref(false);
const smsLoading = ref(false);
const rememberMe = ref(false);
const rememberUsername = ref(false);
const agreePrivacyPolicy = ref(false);
const activeTab = ref('password'); // 默认使用密码登录方式

// 验证码发送相关
const countdown = ref(0);
const canSendCode = computed(() => {
  // 检查手机号格式是否正确
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(smsForm.phone) && countdown.value === 0;
});
const sendCodeText = computed(() => {
  return countdown.value > 0 ? `重新发送(${countdown.value}s)` : '获取验证码';
});

// 隐私协议相关
const privacyDialogVisible = ref(false);
const privacyLoading = ref(false);
const privacyError = ref(false);
const privacyPolicy = reactive<PrivacyPolicyInfo>({
  version: '',
  last_updated: '',
  content: ''
});

/**
 * 跳转到注册页面
 */
function toRegister() {
  router.push('/user/register');
}

// 密码登录表单
const passwordForm = reactive({
  username: 'testuser',
  password: 'admin123'
});

// 密码登录表单验证规则
const passwordRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应为3-20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20个字符', trigger: 'blur' }
  ]
};

// 短信登录表单
const smsForm = reactive({
  phone: '',
  code: ''
});

// 短信登录表单验证规则
const smsRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码应为6位数字', trigger: 'blur' }
  ]
};

/**
 * 显示隐私协议弹窗
 */
function showPrivacyPolicy(e: Event) {
  e.preventDefault();
  privacyDialogVisible.value = true;
  fetchPrivacyPolicy();
}

/**
 * 获取隐私协议内容
 */
async function fetchPrivacyPolicy() {
  privacyLoading.value = true;
  privacyError.value = false;
  
  try {
    // 模拟获取隐私协议内容
    // 实际项目中应该从API获取
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    privacyPolicy.version = 'v1.0.0';
    privacyPolicy.last_updated = '2025-05-20';
    privacyPolicy.content = `
      <h2>用户隐私协议</h2>
      <p>欢迎使用我们的服务。本协议描述了我们如何收集、使用和保护您的个人信息。</p>
      <h3>1. 信息收集</h3>
      <p>我们收集的信息包括但不限于：您的姓名、手机号、邮箱地址、购物记录等。</p>
      <h3>2. 信息使用</h3>
      <p>我们使用收集的信息来提供、维护和改进我们的服务，包括处理订单、发送通知等。</p>
      <h3>3. 信息保护</h3>
      <p>我们采取合理的安全措施来保护您的个人信息不被未经授权访问或泄露。</p>
    `;
  } catch (error) {
    privacyError.value = true;
    console.error('获取隐私协议失败:', error);
  } finally {
    privacyLoading.value = false;
  }
}

/**
 * 同意隐私协议
 */
function agreeToPrivacyPolicy() {
  agreePrivacyPolicy.value = true;
  privacyDialogVisible.value = false;
}

/**
 * 生成设备信息
 * @returns 设备信息对象
 */
function generateDeviceInfo(): DeviceInfo {
  // 生成设备唯一标识
  const deviceId = getOrCreateDeviceId();
  
  // 获取设备信息
  const deviceInfo: DeviceInfo = {
    device_id: deviceId,
    device_name: getDeviceName(),
    device_type: getDeviceType(),
    platform: getPlatform(),
    browser: getBrowserInfo(),
    app_version: getAppVersion(),
    os_version: getOSVersion(),
    user_agent: navigator.userAgent
  };
  
  return deviceInfo;
}

/**
 * 获取或创建设备ID
 * @returns 设备ID
 */
function getOrCreateDeviceId(): string {
  let deviceId = localStorage.getItem('device_id');
  if (!deviceId) {
    deviceId = generateUUID();
    localStorage.setItem('device_id', deviceId);
  }
  return deviceId;
}

/**
 * 生成UUID
 * @returns UUID字符串
 */
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 获取设备类型
 * @returns 设备类型
 */
function getDeviceType(): string {
  const userAgent = navigator.userAgent;
  if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
    return /iPad/.test(userAgent) ? 'tablet' : 'mobile';
  }
  return 'desktop';
}

/**
 * 获取平台信息
 * @returns 平台名称
 */
function getPlatform(): string {
  const userAgent = navigator.userAgent;
  if (/iPhone|iPad/.test(userAgent)) return 'ios';
  if (/Android/.test(userAgent)) return 'android';
  if (/Windows/.test(userAgent)) return 'windows';
  if (/Mac/.test(userAgent)) return 'macos';
  if (/Linux/.test(userAgent)) return 'linux';
  return 'web';
}

/**
 * 获取浏览器信息
 * @returns 浏览器名称
 */
function getBrowserInfo(): string {
  const userAgent = navigator.userAgent;
  if (/Chrome/.test(userAgent)) return 'Chrome';
  if (/Firefox/.test(userAgent)) return 'Firefox';
  if (/Safari/.test(userAgent)) return 'Safari';
  if (/Edge/.test(userAgent)) return 'Edge';
  return 'Unknown';
}

/**
 * 获取设备名称
 * @returns 设备名称
 */
function getDeviceName(): string {
  const platform = getPlatform();
  const browser = getBrowserInfo();
  return `${platform} ${browser}`;
}

/**
 * 获取APP版本
 * @returns APP版本号
 */
function getAppVersion(): string {
  // 如果是APP，从原生获取版本号
  // 如果是Web，可以从package.json或配置获取
  return '1.0.0';
}

/**
 * 获取操作系统版本
 * @returns 操作系统版本
 */
function getOSVersion(): string {
  const userAgent = navigator.userAgent;
  // 简化实现，实际可以更详细解析
  return userAgent.match(/\(([^)]+)\)/)?.[1] || 'Unknown';
}

/**
 * 处理登录响应中的新字段
 * @param loginData 登录响应数据
 */
function handleLoginResponse(loginData: any) {
  if (!loginData) return;
  
  // 处理设备ID
  if (loginData.device_id) {
    localStorage.setItem('device_id', loginData.device_id);
  }
  
  // 保存当前设备信息到本地存储，用于登出时使用
  const currentDeviceInfo = generateDeviceInfo();
  localStorage.setItem('current_device_info', JSON.stringify(currentDeviceInfo));
  
  // 处理新设备提醒
  if (loginData.is_new_device) {
    ElMessage.info('检测到您在新设备上登录，请注意账户安全');
  }
  
  // 处理风险等级
  if (loginData.risk_level) {
    switch (loginData.risk_level) {
      case 'high':
        ElMessage.warning('检测到异常登录行为，请注意账户安全');
        break;
      case 'medium':
        ElMessage.info('登录环境存在一定风险，建议开启双重验证');
        break;
      // low级别不显示提醒
    }
  }
  
  // 处理其他可能的字段
  if (loginData.security_tips) {
    // 可以在适当的时候显示安全提示
    console.log('安全提示:', loginData.security_tips);
  }
}

// 注意：已使用单独的utils文件中定义的initRouteAndNavigate函数代替内部定义

/**
 * 密码登录提交
 */
async function handlePasswordSubmit() {
  if (!agreePrivacyPolicy.value) {
    ElMessage.warning('请先阅读并同意隐私协议');
    return;
  }
  
  // 表单验证
  await passwordFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      return false;
    }
    
    loading.value = true;
    
    try {
      // 生成设备信息
      const deviceInfo = generateDeviceInfo();
      
      // 调用登录接口
      const loginResult = await userStore.userLogin({
        username: passwordForm.username,
        password: passwordForm.password,
        rememberMe: rememberMe.value,
        device_info: deviceInfo
      });
      
      if (loginResult.success) {
        // 记住用户名
        if (rememberUsername.value) {
          localStorage.setItem('rememberUsername', passwordForm.username);
        } else {
          localStorage.removeItem('rememberUsername');
        }
        
        // 处理登录响应中的新字段
        handleLoginResponse(loginResult.data);
        
        // 使用优化的导航方式，解决用户登录后白屏问题
        await initRouteAndNavigate('/user/home');
      } else {
        ElMessage.error(loginResult.message || '登录失败，请检查用户名和密码');
      }
    } catch (error) {
      console.error('登录出错:', error);
      ElMessage.error('登录异常，请稍后重试');
    } finally {
      loading.value = false;
    }
  });
}

/**
 * 发送短信验证码
 */
async function sendVerificationCode() {
  if (!canSendCode.value) {
    return;
  }
  
  // 验证手机号格式
  if (!validateMobile(smsForm.phone)) {
    ElMessage.error('请输入正确的手机号');
    return;
  }
  
  const sendBtn = document.querySelector('.send-code-btn') as HTMLButtonElement;
  if (sendBtn) {
    sendBtn.disabled = true;
  }
  
  try {
    const result = await sendSmsCode(smsForm.phone, 'login');
    
    // 根据参考文档，成功响应应该包含sent、expires_in、retry_after等字段
    if (result) {
      ElMessage.success('验证码已发送，请注意查收');
      // 使用API返回的重试间隔，如果没有则使用默认60秒
      const retryAfter = result.data?.retry_after || 60;
      startCountdown(retryAfter);
    } else {
      ElMessage.error('发送验证码失败，请稍后重试');
      if (sendBtn) {
        sendBtn.disabled = false;
      }
    }
  } catch (error: any) {
    console.error('发送验证码失败:', error);
    
    // 根据错误类型显示不同的错误信息
    let errorMessage = '发送验证码失败，请稍后重试';
    if (error?.message) {
      if (error.message.includes('频率')) {
        errorMessage = '发送过于频繁，请稍后再试';
      } else if (error.message.includes('手机号')) {
        errorMessage = '手机号格式不正确';
      } else {
        errorMessage = error.message;
      }
    }
    
    ElMessage.error(errorMessage);
    if (sendBtn) {
      sendBtn.disabled = false;
    }
  }
}

/**
 * 开始倒计时
 * @param seconds 倒计时时间，默认60秒
 */
function startCountdown(seconds = 60) {
  countdown.value = seconds;
  
  const timer = setInterval(() => {
    countdown.value--;
    
    if (countdown.value <= 0) {
      clearInterval(timer);
      // 重新启用发送按钮
      const sendBtn = document.querySelector('.send-code-btn') as HTMLButtonElement;
      if (sendBtn) {
        sendBtn.disabled = false;
      }
    }
  }, 1000);
}

/**
 * 验证手机号格式
 * @param mobile 手机号
 * @returns 是否有效
 */
function validateMobile(mobile: string): boolean {
  const mobileRegex = /^1[3-9]\d{9}$/;
  return mobileRegex.test(mobile);
}

/**
 * 验证验证码格式
 * @param code 验证码
 * @returns 是否有效
 */
function validateVerifyCode(code: string): boolean {
  const codeRegex = /^\d{6}$/;
  return codeRegex.test(code);
}

/**
 * 格式化手机号输入（只允许数字）
 * @param value 输入值
 */
function formatMobileInput(value: string) {
  // 只保留数字
  const formatted = value.replace(/\D/g, '');
  smsForm.phone = formatted;
}

/**
 * 格式化验证码输入（只允许数字）
 * @param value 输入值
 */
function formatCodeInput(value: string) {
  // 只保留数字
  const formatted = value.replace(/\D/g, '');
  smsForm.code = formatted;
}

/**
 * 短信登录提交
 */
async function handleSmsSubmit() {
  if (!agreePrivacyPolicy.value) {
    ElMessage.warning('请先阅读并同意隐私协议');
    return;
  }
  
  // 表单验证
  await smsFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      return false;
    }
    
    // 参数验证
    if (!validateMobile(smsForm.phone)) {
      ElMessage.error('请输入正确的手机号');
      return;
    }
    
    if (!validateVerifyCode(smsForm.code)) {
      ElMessage.error('请输入6位验证码');
      return;
    }
    
    smsLoading.value = true;
    
    try {
      // 生成设备信息
      const deviceInfo = generateDeviceInfo();
      
      // 调用短信登录接口
      const loginResult = await userStore.userSmsLogin({
        phone: smsForm.phone,
        code: smsForm.code,
        rememberMe: rememberMe.value,
        device_info: deviceInfo
      });
      
      if (loginResult.success) {
        // 处理登录响应中的新字段
        handleLoginResponse(loginResult.data);
        
        // 检查是否为新用户（如果API返回了相关信息）
        const message = '登录成功！';
        ElMessage.success(message);
        
        // 使用优化的导航方式，解决用户登录后白屏问题
        await initRouteAndNavigate('/user/home');
      } else {
        ElMessage.error(loginResult.message || '登录失败，请检查手机号和验证码');
      }
    } catch (error: any) {
      console.error('短信登录出错:', error);
      
      // 根据错误类型显示不同的错误信息
      let errorMessage = '登录异常，请稍后重试';
      if (error?.message) {
        if (error.message.includes('验证码')) {
          errorMessage = '验证码错误或已过期，请重新获取';
        } else if (error.message.includes('手机号')) {
          errorMessage = '手机号格式不正确';
        } else {
          errorMessage = error.message;
        }
      }
      
      ElMessage.error(errorMessage);
    } finally {
      smsLoading.value = false;
    }
  });
}

onMounted(() => {
  // 初始化记住用户名和自动登录
  const savedUsername = localStorage.getItem('rememberUsername');
  if (savedUsername) {
    passwordForm.username = savedUsername;
    rememberUsername.value = true;
  }
  
  // 如果已经登录，直接跳转到首页
  if (userStore.isLoggedIn) {
    router.push('/user/home');
  }
});
</script>

<style scoped>
.user-login {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.login-container {
  width: 100%;
  max-width: 460px;
  padding: 20px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.title {
  margin-top: 10px;
  font-size: 24px;
  color: #303133;
}

.login-form-card {
  padding: 30px;
}

.form-title {
  text-align: center;
  margin-bottom: 30px;
  font-size: 20px;
  color: #303133;
}

.submit-btn {
  width: 100%;
  margin-top: 10px;
}

.login-footer {
  margin-top: 20px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.verification-code-input {
  display: flex;
  gap: 10px;
}

.send-code-btn {
  flex-shrink: 0;
  width: 120px;
}

.privacy-content {
  max-height: 400px;
  overflow-y: auto;
}

.privacy-loading, .privacy-error {
  min-height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-tabs :deep(.el-tabs__nav) {
  display: flex;
  width: 100%;
}

.login-tabs :deep(.el-tabs__item) {
  flex: 1;
  text-align: center;
}
</style>
