<!--
  手机号注册页面
  通过手机号和验证码完成用户注册，支持推荐人功能
-->
<template>
  <div class="phone-register">
    <div class="register-container">
      <div class="register-header">
        <img src="/images/logo.png" alt="O_Mall" class="logo" />
        <h1 class="title">用户中心</h1>
      </div>

      <AppCard class="register-form-card">
        <h2 class="form-title">手机号注册</h2>
        
        <AppForm
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          label-position="top"
          @keyup.enter="handleRegisterSubmit"
        >
          <AppFormItem label="手机号" prop="phone">
            <AppInput
              v-model="registerForm.phone"
              placeholder="请输入手机号"
              prefix-icon="Iphone"
            />
          </AppFormItem>

          <AppFormItem label="验证码" prop="code">
            <div class="verification-code-input">
              <AppInput
                v-model="registerForm.code"
                placeholder="请输入验证码"
                prefix-icon="Key"
              />
              <AppButton
                type="primary"
                class="send-code-btn"
                :disabled="!canSendCode || loading"
                @click.prevent="sendVerificationCode"
              >
                {{ sendCodeText }}
              </AppButton>
            </div>
          </AppFormItem>

          <AppFormItem>
            <el-checkbox v-model="registerForm.agreePrivacyPolicy" :disabled="loading">
              我已阅读并同意
              <el-button link type="primary" @click.stop="showPrivacyPolicy">《隐私协议》</el-button>
            </el-checkbox>
          </AppFormItem>

          <div class="form-actions">
            <AppButton
              type="primary"
              :loading="loading"
              class="submit-btn"
              :disabled="!registerForm.agreePrivacyPolicy"
              @click="handleRegisterSubmit"
            >
              注册
            </AppButton>
          </div>
        </AppForm>

        <div class="register-footer">
          <p>已有账号？</p>
          <AppButton
            type="default"
            class="login-btn"
            @click="toLogin"
            text
          >
            立即登录 <el-icon><ArrowRight /></el-icon>
          </AppButton>
        </div>
      </AppCard>
    </div>
  </div>

  <!-- 隐私协议弹窗 -->
  <el-dialog
    v-model="privacyDialogVisible"
    title="隐私协议"
    width="60%"
    :close-on-click-modal="false"
  >
    <div v-if="privacyLoading" class="privacy-loading">
      <el-skeleton :rows="10" animated />
    </div>
    <div v-else-if="privacyError" class="privacy-error">
      <el-empty description="加载隐私协议失败，请稍后再试" />
    </div>
    <div v-else class="privacy-content">
      <h3>{{ privacyPolicy.version }} ({{ privacyPolicy.last_updated }})</h3>
      <div v-html="privacyPolicy.content"></div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="privacyDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="agreeToPrivacyPolicy">
          同意并继续
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { phoneRegister, sendRegisterSmsCode } from '../api/auth';
import type { PhoneRegisterParams } from '../types';
import { ArrowRight } from '@element-plus/icons-vue';
import AppCard from '@/components/base/AppCard.vue';
import AppForm from '@/components/form/AppForm.vue';
import AppFormItem from '@/components/form/AppFormItem.vue';
import AppInput from '@/components/form/AppInput.vue';
import AppButton from '@/components/base/AppButton.vue';

// 定义隐私协议类型
interface PrivacyPolicyInfo {
  version: string;
  last_updated: string;
  content: string;
}

const router = useRouter();
const route = useRoute();
const registerFormRef = ref();
const loading = ref(false);
const referrer = ref<string | null>(null);

// 注册表单
const registerForm = reactive({
  phone: '',
  code: '',
  agreePrivacyPolicy: false
});

// 注册表单验证规则
const registerRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, max: 6, message: '验证码长度不正确', trigger: 'blur' }
  ]
};

// 短信验证码倒计时
const countdown = ref(0);
const canSendCode = computed(() => {
  return registerForm.phone && registerForm.phone.length === 11 && countdown.value === 0;
});

// 发送验证码按钮文字
const sendCodeText = computed(() => {
  return countdown.value > 0 ? `重新发送(${countdown.value}s)` : '获取验证码';
});

// 隐私协议相关
const privacyDialogVisible = ref(false);
const privacyLoading = ref(false);
const privacyError = ref(false);
const privacyPolicy = reactive<PrivacyPolicyInfo>({
  version: '',
  last_updated: '',
  content: ''
});

// 获取URL中的referrer参数
onMounted(() => {
  const routeReferrer = route.query.referrer;
  if (routeReferrer) {
    referrer.value = String(routeReferrer);
    console.log('检测到推荐人ID:', referrer.value);
  }
});

/**
 * 跳转到登录页面
 */
function toLogin() {
  router.push('/user/login');
}

/**
 * 显示隐私协议弹窗
 */
function showPrivacyPolicy(e: Event) {
  e.preventDefault();
  fetchPrivacyPolicy();
  privacyDialogVisible.value = true;
}

/**
 * 获取隐私协议内容
 */
async function fetchPrivacyPolicy() {
  privacyLoading.value = true;
  privacyError.value = false;
  
  try {
    // 模拟获取隐私协议内容
    // 实际项目中应该从API获取
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    privacyPolicy.version = 'v1.0.0';
    privacyPolicy.last_updated = '2025-05-20';
    privacyPolicy.content = `
      <h2>用户隐私协议</h2>
      <p>欢迎使用我们的服务。本协议描述了我们如何收集、使用和保护您的个人信息。</p>
      <h3>1. 信息收集</h3>
      <p>我们收集的信息包括但不限于：您的姓名、手机号、邮箱地址、购物记录等。</p>
      <h3>2. 信息使用</h3>
      <p>我们使用收集的信息来提供、维护和改进我们的服务，包括处理订单、发送通知等。</p>
      <h3>3. 信息保护</h3>
      <p>我们采取合理的安全措施来保护您的个人信息不被未经授权访问或泄露。</p>
    `;
  } catch (error) {
    privacyError.value = true;
    console.error('获取隐私协议失败:', error);
  } finally {
    privacyLoading.value = false;
  }
}

/**
 * 同意隐私协议
 */
function agreeToPrivacyPolicy() {
  registerForm.agreePrivacyPolicy = true;
  privacyDialogVisible.value = false;
}

/**
 * 发送短信验证码
 */
async function sendVerificationCode() {
  if (!canSendCode.value) {
    return;
  }
  
  try {
    await sendRegisterSmsCode(registerForm.phone);
    ElMessage.success('验证码已发送，请注意查收');
    startCountdown();
  } catch (error) {
    console.error('发送验证码失败:', error);
    ElMessage.error('发送验证码失败，请稍后重试');
  }
}

/**
 * 倒计时
 * @param expireTime 倒计时时间，默认60秒
 */
function startCountdown(expireTime = 60) {
  countdown.value = expireTime;
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
}

/**
 * 注册提交
 */
async function handleRegisterSubmit() {
  if (!registerForm.agreePrivacyPolicy) {
    ElMessage.warning('请先阅读并同意隐私协议');
    return;
  }
  
  // 表单验证
  await registerFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      return false;
    }
    
    loading.value = true;
    
    try {
      // 构建注册参数
      const registerParams: PhoneRegisterParams = {
        mobile: registerForm.phone,
        code: registerForm.code
      };
      
      // 如果存在推荐人ID，添加到注册参数中
      if (referrer.value) {
        registerParams.referrer_id = Number(referrer.value);
      }
      
      // 调用注册接口
      await phoneRegister(registerParams);
      
      ElMessage.success('注册成功，请登录');
      
      // 注册成功后跳转到登录页面
      router.push('/user/login');
    } catch (error) {
      console.error('注册出错:', error);
      ElMessage.error('注册失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  });
}
</script>

<style scoped>
.phone-register {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.register-container {
  width: 100%;
  max-width: 460px;
  padding: 20px;
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.title {
  margin-top: 10px;
  font-size: 24px;
  color: #303133;
}

.register-form-card {
  padding: 30px;
}

.form-title {
  text-align: center;
  margin-bottom: 30px;
  font-size: 20px;
  color: #303133;
}

.submit-btn {
  width: 100%;
  margin-top: 10px;
}

.register-footer {
  margin-top: 20px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.verification-code-input {
  display: flex;
  gap: 10px;
}

.send-code-btn {
  flex-shrink: 0;
  width: 120px;
}

.privacy-content {
  max-height: 400px;
  overflow-y: auto;
}

.privacy-loading, .privacy-error {
  min-height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
