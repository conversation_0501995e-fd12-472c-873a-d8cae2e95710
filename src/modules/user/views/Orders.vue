<!--
  @author: AI Assistant
  @date: 2025-01-27
  @version: 1.1.0
  @description: 用户订单管理页面，显示用户的所有订单（包括普通订单和外卖订单），并提供订单查询、筛选和处理功能
-->
<template>
  <div class="user-orders-page">
    <!-- 订单筛选区域 -->
    <el-card class="filter-card">
      <div class="filter-bar">
        <div class="order-type-switch">
          <el-radio-group v-model="orderType" @change="switchOrderType">
            <el-radio-button v-for="option in orderTypeOptions" :key="option.value" :label="option.value">
              {{ option.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
        
        <div class="filter-tabs">
          <el-radio-group v-model="filterStatus" @change="handleStatusChange">
            <el-radio-button label="">全部订单</el-radio-button>
            <el-radio-button :label="OrderStatus.PENDING">待付款</el-radio-button>
            <el-radio-button :label="OrderStatus.PAID">{{ orderType === 'takeout' ? '处理中' : '待发货' }}</el-radio-button>
            <el-radio-button :label="OrderStatus.DELIVERING">{{ orderType === 'takeout' ? '配送中' : '待收货' }}</el-radio-button>
            <el-radio-button :label="OrderStatus.COMPLETED">待评价</el-radio-button>
            <el-radio-button :label="OrderStatus.REFUNDING">退款/售后</el-radio-button>
          </el-radio-group>
        </div>
        
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="输入订单号或商品名称搜索"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button @click="handleSearch">
                <el-icon><Search /></el-icon>
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
    </el-card>
    
    <!-- 订单列表 -->
    <div class="order-list" v-loading="loading">
      <el-empty v-if="orders.length === 0 && !loading" description="暂无相关订单" />
      
      <el-card v-for="order in orders" :key="order.id" class="order-card">
        <div class="order-header">
          <div class="order-meta">
            <span class="order-number">订单号：{{ order.orderNumber }}</span>
            <span class="order-time">下单时间：{{ formatDate(order.createdAt) }}</span>
          </div>
          <el-tag :type="getStatusType(order.status)">{{ getStatusText(order.status) }}</el-tag>
        </div>
        
        <el-divider />
        
        <div class="order-items">
          <div v-for="item in order.items" :key="item.id" class="order-item">
            <div class="item-image">
              <img :src="item.productImage" :alt="item.productName" />
            </div>
            <div class="item-info">
              <div class="item-name">{{ item.productName }}</div>
              <div class="item-price">
                <span class="price">¥{{ item.price.toFixed(2) }}</span>
                <span class="quantity">× {{ item.quantity }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <el-divider />
        
        <div class="order-footer">
          <div class="order-amount">
            <span>共 {{ getTotalQuantity(order) }} 件商品，</span>
            <span>实付金额：</span>
            <span class="total-price">¥{{ (order.payAmount || order.totalAmount)?.toFixed(2) || '0.00' }}</span>
          </div>
          
          <div class="order-actions">
            <!-- 待付款状态 -->
            <template v-if="order.status === OrderStatus.PENDING">
              <el-button type="primary" @click="payOrder(order)">去支付</el-button>
              <el-button @click="showCancelOrderDialog(order)">取消订单</el-button>
            </template>
            
            <!-- 待发货状态 -->
            <template v-else-if="order.status === OrderStatus.PAID">
              <el-button @click="showRefundDialog(order)">申请退款</el-button>
            </template>
            
            <!-- 待收货状态 -->
            <template v-else-if="order.status === OrderStatus.DELIVERING">
              <el-button type="primary" @click="showConfirmDialog(order)">确认收货</el-button>
              <el-button @click="viewLogistics(order)">查看物流</el-button>
            </template>
            
            <!-- 待评价状态 -->
            <template v-else-if="order.status === OrderStatus.COMPLETED">
              <el-button type="primary" @click="toReview(order)">去评价</el-button>
            </template>
            
            <!-- 所有状态都可以查看详情 -->
            <el-button @click="viewOrderDetail(order)">订单详情</el-button>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        background
        layout="prev, pager, next, jumper"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="handlePageChange"
      />
    </div>
    
    <!-- 取消订单弹窗 -->
    <el-dialog
      v-model="cancelDialogVisible"
      title="取消订单"
      width="400px"
    >
      <el-form :model="cancelForm" label-width="80px">
        <el-form-item label="取消原因" prop="reason">
          <el-select v-model="cancelForm.reason" placeholder="请选择取消原因" style="width: 100%">
            <el-option label="我不想买了" value="我不想买了" />
            <el-option label="信息填写错误，重新下单" value="信息填写错误，重新下单" />
            <el-option label="卖家缺货" value="卖家缺货" />
            <el-option label="同城见面交易" value="同城见面交易" />
            <el-option label="其他原因" value="其他原因" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmCancelOrder" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 确认收货弹窗 -->
    <el-dialog
      v-model="confirmDialogVisible"
      title="确认收货"
      width="380px"
    >
      <p>确认已收到该订单商品？</p>
      <p class="confirm-warning">请收到货后再确认，否则可能会钱货两空！</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="confirmDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmReceive" :loading="submitting">确认收货</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 申请退款弹窗 -->
    <el-dialog
      v-model="refundDialogVisible"
      title="申请退款"
      width="500px"
    >
      <el-form :model="refundForm" :rules="refundRules" ref="refundFormRef" label-width="100px">
        <el-form-item label="退款金额" prop="amount">
          <el-input-number 
            v-model="refundForm.amount" 
            :min="0.01" 
            :max="currentOrder?.payAmount || 0" 
            :precision="2" 
            :step="0.01"
            style="width: 200px"
          />
          <span class="refund-max">最多 ¥{{ currentOrder?.payAmount.toFixed(2) }}</span>
        </el-form-item>
        <el-form-item label="退款原因" prop="reason">
          <el-select v-model="refundForm.reason" placeholder="请选择退款原因" style="width: 100%">
            <el-option label="不想要了/拍错了" value="不想要了/拍错了" />
            <el-option label="商品缺货" value="商品缺货" />
            <el-option label="协商一致退款" value="协商一致退款" />
            <el-option label="其他原因" value="其他原因" />
          </el-select>
        </el-form-item>
        <el-form-item label="退款说明" prop="description">
          <el-input 
            v-model="refundForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入详细退款说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="refundDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmRefund" :loading="submitting">申请退款</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 支付弹窗 -->
    <PaymentDialog
      v-model:visible="paymentDialogVisible"
      :order-info="currentPaymentOrder ? {
        id: parseInt(currentPaymentOrder.id),
        orderNumber: currentPaymentOrder.orderNumber,
        totalAmount: currentPaymentOrder.totalAmount,
        payAmount: currentPaymentOrder.payAmount,
        createdAt: currentPaymentOrder.createdAt,
        merchantName: '',
        orderType: orderType as 'general' | 'takeout'
      } : null"
      @payment-success="handlePaymentSuccess"
      @payment-failed="handlePaymentFailed"
      @payment-timeout="handlePaymentTimeout"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import type { UserAddress } from '../types';
import { useRouter, useRoute } from 'vue-router';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getUserOrders, cancelOrder, confirmReceived, applyRefund } from '../api/order';
import { takeoutService } from '../service/takeoutService';
//import request from '@/modules/user/utils/request';
import { useTakeoutStore } from '../stores/takeoutStore';
import { OrderStatus, TakeoutOrderStatus } from '../types';
import type { UserOrder, OrderItem } from '../types';
import PaymentDialog from './takeout/PaymentDialog.vue';
//import { PaymentMethod, PaymentStatus } from '../api/payment';

// 订单列表数据
const orders = ref<UserOrder[]>([]);
const total = ref(0);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const filterStatus = ref('');
const searchKeyword = ref('');

// 路由
const router = useRouter();
const route = useRoute();

// 取消订单相关
const cancelDialogVisible = ref(false);
const currentOrder = ref<UserOrder | null>(null);
const cancelForm = reactive({
  reason: ''
});

// 确认收货相关
const confirmDialogVisible = ref(false);

// 申请退款相关
const refundDialogVisible = ref(false);
const refundFormRef = ref();
const refundForm = reactive({
  amount: 0,
  reason: '',
  description: ''
});
const refundRules = {
  amount: [
    { required: true, message: '请输入退款金额', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请选择退款原因', trigger: 'change' }
  ]
};

// 支付相关
const paymentDialogVisible = ref(false);
const currentPaymentOrder = ref<UserOrder | null>(null);

// 通用提交状态
const submitting = ref(false);

/**
 * 格式化日期
 * @param dateString 日期字符串
 * @returns 格式化后的日期
 */
function formatDate(dateString: string): string {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * 获取订单状态文本
 * @param status 订单状态
 * @returns 状态文本
 */
function getStatusText(status: OrderStatus): string {
  const statusMap: Record<OrderStatus, string> = {
    [OrderStatus.PENDING]: '待付款',
    [OrderStatus.PAID]: '待发货',
    [OrderStatus.PROCESSING]: '处理中',
    [OrderStatus.DELIVERING]: '配送中',
    [OrderStatus.COMPLETED]: '已完成',
    [OrderStatus.CANCELLED]: '已取消',
    [OrderStatus.REFUNDING]: '退款中',
    [OrderStatus.REFUNDED]: '已退款'
  };
  return statusMap[status] || '未知状态';
}

/**
 * 获取订单状态对应的标签类型
 * @param status 订单状态
 * @returns 标签类型
 */
function getStatusType(status: OrderStatus): string {
  const typeMap: Record<OrderStatus, string> = {
    [OrderStatus.PENDING]: 'warning',
    [OrderStatus.PAID]: 'info',
    [OrderStatus.PROCESSING]: 'primary',
    [OrderStatus.DELIVERING]: 'primary',
    [OrderStatus.COMPLETED]: 'success',
    [OrderStatus.CANCELLED]: 'info',
    [OrderStatus.REFUNDING]: 'danger',
    [OrderStatus.REFUNDED]: 'info'
  };
  return typeMap[status] || 'info';
}

/**
 * 获取订单中商品总数量
 * @param order 订单对象
 * @returns 商品总数量
 */
function getTotalQuantity(order: UserOrder): number {
  return order.items.reduce((sum, item) => sum + item.quantity, 0);
}

// 获取takeout store
const takeoutStore = useTakeoutStore();

// 初始化订单类型
const orderType = ref('takeout'); // 'general' 或 'takeout'

// 订单类型选项
const orderTypeOptions = [
  { label: '普通订单', value: 'general' },
  { label: '外卖订单', value: 'takeout' }
];

// 根据订单类型计算当前过滤状态
const currentFilterStatus = computed(() => {
  // 如果是外卖订单，需要将普通订单状态映射到外卖订单状态
  if (orderType.value === 'takeout') {
    switch (filterStatus.value) {
      case OrderStatus.PENDING.toString():
        return TakeoutOrderStatus.PENDING.toString();
      case OrderStatus.PAID.toString():
        return TakeoutOrderStatus.PAID.toString();
      case OrderStatus.DELIVERING.toString():
        return TakeoutOrderStatus.DELIVERING.toString();
      case OrderStatus.COMPLETED.toString():
        return TakeoutOrderStatus.COMPLETED.toString();
      case OrderStatus.REFUNDING.toString():
        return TakeoutOrderStatus.REFUNDING.toString();
      default:
        return undefined;
    }
  }
  return filterStatus.value || undefined;
});

/**
 * 加载订单列表
 */
async function loadOrders() {
  loading.value = true;
  try {
    // 根据订单类型选择加载不同类型的订单
    if (orderType.value === 'takeout') {
      // 加载外卖订单
      const params = {
        status: currentFilterStatus.value,
        page: currentPage.value,
        pageSize: pageSize.value
      };
      
      const response = await takeoutStore.loadOrders(params);
      
      // 将外卖订单转换为通用订单格式以适配当前UI
      // 后端返回的数据结构是 {total, page, pageSize, totalPage, list}
      const orderList = response.data || [];
      orders.value = orderList.map((takeoutOrder: any) => convertTakeoutOrderToUserOrder(takeoutOrder));
      total.value = response.total;
    } else {
      // 加载普通订单
      const statusFilter = filterStatus.value || undefined;
      const response: any = await getUserOrders({
        status: statusFilter,
        page: currentPage.value,
        pageSize: pageSize.value
      });
      console.log('普通订单列表:', response);
      
      // 从API响应中获取数据
      orders.value = response.data || [];
      total.value = response.total || 0;
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    ElMessage.error('获取订单列表失败，请稍后重试');
  } finally {
    loading.value = false;
  }
}

/**
 * 将外卖订单转换为通用订单格式
 * @param takeoutOrder 外卖订单（后端返回的数据结构）
 * @returns 通用订单格式
 */
function convertTakeoutOrderToUserOrder(takeoutOrder: any): UserOrder {
  // 映射订单状态 - 根据后端返回的orderStatus字段
  let status: OrderStatus;
  switch (takeoutOrder.orderStatus) {
    case 10: // 待付款
      status = OrderStatus.PENDING;
      break;
    case 20: // 已付款
      status = OrderStatus.PAID;
      break;
    case 30: // 处理中
      status = OrderStatus.PROCESSING;
      break;
    case 40: // 配送中
      status = OrderStatus.DELIVERING;
      break;
    case 50: // 已完成
      status = OrderStatus.COMPLETED;
      break;
    case 60: // 已取消
      status = OrderStatus.CANCELLED;
      break;
    case 70: // 退款中
      status = OrderStatus.REFUNDING;
      break;
    case 80: // 已退款
      status = OrderStatus.REFUNDED;
      break;
    default:
      status = OrderStatus.PENDING;
  }
  
  // 转换订单项 - 处理items可能为null的情况
  const items: OrderItem[] = (takeoutOrder.items || []).map((item: any) => ({
    id: item.id?.toString() || '',
    orderId: takeoutOrder.orderID.toString(),
    productId: item.productID?.toString() || '',
    productName: item.productName || '',
    productImage: item.image || '',
    quantity: item.quantity || 0,
    price: item.price || 0,
    totalPrice: item.amount || 0
  }));
  
  // 转换地址格式 - 从deliveryInfo中获取地址信息
  const address: UserAddress = {
    id: '',
    userId: takeoutOrder.userID.toString(),
    receiver_name: '',
    receiver_mobile: '',
    phone: takeoutOrder.deliveryInfo?.deliveryStaffPhone || '',
    province: '',
    city: '',
    district: '',
    detailed_address: takeoutOrder.deliveryInfo?.deliveryAddress || '',
    is_default: false,
    createdAt: takeoutOrder.createTime,
    updatedAt: takeoutOrder.createTime
  };

  // 返回通用订单格式
  return {
    id: takeoutOrder.orderID.toString(),
    userId: takeoutOrder.userID.toString(),
    orderNumber: takeoutOrder.orderNo,
    totalAmount: takeoutOrder.totalAmount,
    payAmount: takeoutOrder.payAmount, // 添加实际支付金额字段
    status,
    paymentMethod: takeoutOrder.paymentMethod || '',
    items,
    address,
    createdAt: takeoutOrder.createTime,
    updatedAt: takeoutOrder.createTime,
    paidAt: takeoutOrder.payTime,
    shippedAt: takeoutOrder.deliveryTime ? new Date(new Date(takeoutOrder.deliveryTime).getTime() - 30 * 60 * 1000).toISOString() : undefined,
    deliveredAt: takeoutOrder.deliveryTime,
    completedAt: takeoutOrder.completeTime,
    cancelledAt: takeoutOrder.cancelTime
  };
}

/**
 * 切换订单类型
 * @param type 订单类型 'general' 或 'takeout'
 */
function switchOrderType(type: 'general' | 'takeout') {
  orderType.value = type;
  // 重置分页和过滤条件
  currentPage.value = 1;
  filterStatus.value = '';
  // 重新加载订单
  loadOrders();
}

/**
 * 处理状态过滤变更
 */
function handleStatusChange() {
  currentPage.value = 1;
  loadOrders();
}

/**
 * 处理搜索
 */
function handleSearch() {
  currentPage.value = 1;
  loadOrders();
}

/**
 * 处理分页变更
 * @param page 页码
 */
function handlePageChange(page: number) {
  currentPage.value = page;
  loadOrders();
}

/**
 * 显示取消订单对话框
 * @param order 订单对象
 */
function showCancelOrderDialog(order: UserOrder) {
  currentOrder.value = order;
  cancelForm.reason = '';
  cancelDialogVisible.value = true;
}

/**
 * 确认取消订单
 */
async function confirmCancelOrder() {
  if (!currentOrder.value) return;
  if (!cancelForm.reason) {
    ElMessage.warning('请选择取消原因');
    return;
  }
  
  submitting.value = true;
  try {
    // 根据订单类型选择不同的API
    if (orderType.value === 'takeout') {
      await takeoutService.order.cancelOrder(currentOrder.value.id, {
        reason: cancelForm.reason
      });
    } else {
      await cancelOrder(currentOrder.value.id, cancelForm.reason);
    }
    
    ElMessage.success('订单已取消');
    cancelDialogVisible.value = false;
    loadOrders();
  } catch (error) {
    console.error('取消订单失败:', error);
    ElMessage.error('取消订单失败，请稍后重试');
  } finally {
    submitting.value = false;
  }
}

/**
 * 显示确认收货对话框
 * @param order 订单对象
 */
function showConfirmDialog(order: UserOrder) {
  currentOrder.value = order;
  confirmDialogVisible.value = true;
}

/**
 * 确认收货
 */
async function confirmReceive() {
  if (!currentOrder.value) return;
  
  submitting.value = true;
  try {
    // 根据订单类型选择不同的API
    if (orderType.value === 'takeout') {
      // 外卖订单存在确认送达功能
      // 这里使用正常订单的confirmReceived方法，如果外卖模块有特定方法则使用特定方法
      await confirmReceived(currentOrder.value.id);
    } else {
      await confirmReceived(currentOrder.value.id);
    }
    
    ElMessage.success('已确认收货');
    confirmDialogVisible.value = false;
    loadOrders();
  } catch (error) {
    console.error('确认收货失败:', error);
    ElMessage.error('确认收货失败，请稍后重试');
  } finally {
    submitting.value = false;
  }
}

/**
 * 显示申请退款对话框
 * @param order 订单对象
 */
function showRefundDialog(order: UserOrder) {
  currentOrder.value = order;
  refundForm.amount = order.totalAmount;
  refundForm.reason = '';
  refundForm.description = '';
  refundDialogVisible.value = true;
}

/**
 * 确认申请退款
 */
async function confirmRefund() {
  if (!currentOrder.value || !refundFormRef.value) return;
  
  try {
    await refundFormRef.value.validate();
    
    submitting.value = true;
    
    // 根据订单类型选择不同的API
    if (orderType.value === 'takeout') {
      // 外卖订单使用正常订单的退款方式
      await applyRefund(
        currentOrder.value.id, 
        refundForm.reason
      );
    } else {
      await applyRefund(
        currentOrder.value.id, 
        refundForm.reason
      );
    }
    
    ElMessage.success('退款申请已提交');
    refundDialogVisible.value = false;
    loadOrders();
  } catch (error) {
    console.error('申请退款失败:', error);
    ElMessage.error('申请退款失败，请稍后重试');
  } finally {
    submitting.value = false;
  }
}

/**
 * 去支付
 * @param order 订单对象
 */
function payOrder(order: UserOrder) {
  currentPaymentOrder.value = order;
  paymentDialogVisible.value = true;
}

/**
 * 处理支付成功
 */
function handlePaymentSuccess() {
  ElMessage.success('支付成功！');
  paymentDialogVisible.value = false;
  loadOrders();
}

/**
 * 处理支付失败
 */
function handlePaymentFailed(error: any) {
  console.error('支付失败:', error);
}

/**
 * 处理支付超时
 */
function handlePaymentTimeout() {
  loadOrders();
}

/**
 * 查看物流信息
 * @param order 订单对象
 */
function viewLogistics(_order: UserOrder) {
  ElMessage.info('物流功能开发中...');
}

/**
 * 去评价
 * @param order 订单对象
 */
function toReview(_order: UserOrder) {
  ElMessage.info('评价功能开发中...');
}

/**
 * 查看订单详情
 * @param order 订单对象
 */
function viewOrderDetail(order: UserOrder) {
  // 根据订单类型跳转到不同的详情页面
  if (orderType.value === 'takeout') {
    router.push(`/user/takeout/order/${order.id}`);
  } else {
    router.push(`/user/order-detail/${order.id}`);
  }
}

onMounted(() => {
  // 从URL参数中获取状态过滤
  const status = route.query.status as string | undefined;
  if (status) {
    filterStatus.value = status;
  }
  
  // 从URL参数中获取订单类型
  const type = route.query.type as 'general' | 'takeout' | undefined;
  if (type) {
    orderType.value = type;
  }
  
  // 加载订单列表
  loadOrders();
});
</script>

<style scoped>
.user-orders-page {
  padding: 10px 0;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.filter-tabs {
  flex: 1;
}

.search-bar {
  width: 300px;
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.order-card {
  transition: all 0.3s;
}

.order-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-meta {
  display: flex;
  gap: 20px;
}

.order-number {
  font-weight: bold;
}

.order-time {
  color: #606266;
}

.order-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin: 15px 0;
}

.order-item {
  display: flex;
  gap: 15px;
}

.item-image {
  width: 80px;
  height: 80px;
  overflow: hidden;
  border-radius: 4px;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-name {
  font-size: 14px;
  margin-bottom: 5px;
}

.item-price {
  display: flex;
  justify-content: space-between;
  color: #606266;
}

.price {
  font-weight: bold;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-amount {
  color: #606266;
}

.total-price {
  color: #f56c6c;
  font-size: 18px;
  font-weight: bold;
}

.order-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.confirm-warning {
  color: #f56c6c;
  font-weight: bold;
}

.refund-max {
  margin-left: 10px;
  color: #999;
  font-size: 12px;
}


</style>
