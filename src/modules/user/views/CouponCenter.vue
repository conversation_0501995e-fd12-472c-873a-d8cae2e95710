<template>
  <div class="coupon-center">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="goBack" type="text" class="back-btn">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <h1 class="page-title">优惠券中心</h1>
    </div>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" class="coupon-tabs" @tab-change="handleTabChange">
      <!-- 可领取优惠券 -->
      <el-tab-pane label="可领取" name="available">
        <div v-loading="availableLoading" class="tab-content">
          <div v-if="availableCoupons.length === 0" class="empty-state">
            <el-empty description="暂无可领取的优惠券" />
          </div>
          <div v-else class="coupons-grid">
            <div 
              v-for="coupon in availableCoupons" 
              :key="coupon.id"
              class="coupon-card available-coupon"
              :class="{ 'disabled': !coupon.can_claim }"
            >
              <div class="coupon-left">
                <div class="amount">¥{{ coupon.amount }}</div>
                <div class="condition">满{{ coupon.min_order_amount }}可用</div>
              </div>
              <div class="coupon-right">
                <div class="coupon-name">{{ coupon.name }}</div>
                <div class="merchant-name" v-if="coupon.merchant_name">{{ coupon.merchant_name }}</div>
                <div class="expire-time">{{ formatCouponExpireTime(coupon.end_time) }}</div>
                <el-button 
                  v-if="coupon.can_claim"
                  @click="handleClaimCoupon(coupon.id)"
                  :loading="claimingCouponId === coupon.id"
                  size="small"
                  type="primary"
                  class="claim-btn"
                >
                  {{ coupon.claim_button_text || '立即领取' }}
                </el-button>
                <span v-else class="claimed-text">{{ getClaimStatusText(coupon) }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 我的优惠券 -->
      <el-tab-pane label="我的优惠券" name="my">
        <div v-loading="myLoading" class="tab-content">
          <!-- 状态筛选 -->
          <div class="filter-bar">
            <el-radio-group v-model="couponStatus" @change="loadMyCoupons">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="unused">未使用</el-radio-button>
              <el-radio-button label="used">已使用</el-radio-button>
              <el-radio-button label="expired">已过期</el-radio-button>
            </el-radio-group>
          </div>

          <div v-if="myCoupons.length === 0" class="empty-state">
            <el-empty description="暂无优惠券" />
          </div>
          <div v-else class="coupons-grid">
            <div 
              v-for="coupon in myCoupons" 
              :key="coupon.id"
              class="coupon-card my-coupon"
              :class="{
                'used': coupon.status === 2,
                'expired': coupon.status === 3
              }"
              @click="showCouponDetail(coupon)"
            >
              <div class="coupon-left">
                <div class="amount">¥{{ coupon.coupon ? coupon.coupon.amount : 0 }}</div>
                <div class="condition">满{{ coupon.coupon ? coupon.coupon.min_order_amount : 0 }}可用</div>
              </div>
              <div class="coupon-middle">
                <div class="coupon-name">{{ coupon.coupon ? coupon.coupon.name : '' }}</div>
                <div class="expire-time">{{ formatUserCouponExpireTime(coupon) }}</div>
                <el-button 
                  v-if="coupon.coupon && coupon.coupon.merchant_id && coupon.status === 1" 
                  @click.stop="goToMerchant(coupon.coupon.merchant_id)" 
                  type="primary" 
                  size="small" 
                  class="shop-btn"
                >
                  去购物
                </el-button>
              </div>
              <div class="coupon-right">
                <div class="merchant-info" v-if="coupon.coupon && coupon.coupon.merchant_id">
                  <img 
                    v-if="coupon.coupon && coupon.coupon.merchant_logo" 
                    :src="coupon.coupon.merchant_logo" 
                    class="merchant-logo" 
                    alt="商家logo"
                    @error="handleImageError"
                  />
                  <div class="merchant-name-wrap">
                    <span class="merchant-name">{{ coupon.coupon.merchant_name }}</span>
                    <div class="status-badge">
                      <el-tag 
                        :type="getCouponStatusType(coupon.status)"
                        size="small"
                      >
                        {{ coupon.status_text || getCouponStatusText(coupon.status) }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination" v-if="myTotal > myPageSize">
            <el-pagination
              v-model:current-page="myCurrentPage"
              v-model:page-size="myPageSize"
              :total="myTotal"
              layout="prev, pager, next"
              @current-change="loadMyCoupons"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 优惠券详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      title="优惠券详情"
      width="90%"
      :show-close="true"
    >
      <div v-if="currentCoupon" class="coupon-detail">
        <div class="detail-coupon-card">
          <div class="coupon-left">
            <div class="amount">¥{{ currentCoupon.coupon?.amount || 0 }}</div>
            <div class="condition">满{{ currentCoupon.coupon?.min_order_amount || 0 }}可用</div>
          </div>
          <div class="coupon-right">
            <div class="coupon-name">{{ currentCoupon.coupon?.name || '' }}</div>
            <div class="merchant-info" v-if="currentCoupon.coupon?.merchant_id">
              <img 
                v-if="currentCoupon.coupon?.merchant_logo" 
                :src="currentCoupon.coupon.merchant_logo" 
                class="merchant-logo-detail" 
                alt="商家logo"
                @error="handleImageError"
              />
              <span class="merchant-name">{{ currentCoupon.coupon?.merchant_name || '' }}</span>
            </div>
            <div class="expire-time">{{ formatUserCouponExpireTime(currentCoupon) }}</div>
            <el-button 
              v-if="currentCoupon.status === 1 && currentCoupon.coupon?.merchant_id" 
              @click="goToMerchant(currentCoupon.coupon.merchant_id)" 
              type="primary" 
              size="small" 
              class="shop-btn"
            >
              去购物
            </el-button>
          </div>
        </div>
        
        <div class="detail-info">
          <div class="info-item">
            <span class="label">优惠券编号：</span>
            <span class="value">{{ currentCoupon.coupon_id }}</span>
          </div>
          <div class="info-item" v-if="currentCoupon.coupon?.description">
            <span class="label">使用说明：</span>
            <span class="value">{{ currentCoupon.coupon.description }}</span>
          </div>
          <div class="info-item">
            <span class="label">获得时间：</span>
            <span class="value">{{ formatDateTime(currentCoupon.created_at) }}</span>
          </div>
          <div class="info-item" v-if="currentCoupon.used_time && currentCoupon.used_time !== '0001-01-01T00:00:00Z'">
            <span class="label">使用时间：</span>
            <span class="value">{{ formatDateTime(currentCoupon.used_time) }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { ArrowLeft } from '@element-plus/icons-vue';
import { 
  getCouponCenter, 
  getMyCoupons, 
  claimCoupon 
} from '@/modules/user/api/coupon';
import type { 
  CouponTemplate, 
  CouponStatus 
} from '@/modules/user/types/coupon';

// 后端返回的优惠券结构定义
interface BackendCoupon {
  id: number;
  name: string;
  description: string;
  amount: number;
  min_order_amount: number;
  start_time: string;
  end_time: string;
  merchant_logo: string;
  merchant_name: string;
  type: number;
  type_text: string;
  status: number;
  merchant_id: number;
}

// 后端返回的用户优惠券结构定义
interface BackendUserCoupon {
  id: number;
  coupon_id: number;
  user_id: number;
  status: number;
  status_text: string;
  used_time: string;
  created_at: string;
  order_id?: number;
  coupon: BackendCoupon;
}

const router = useRouter();

// 状态管理
const activeTab = ref<string>('available');

// 将CouponTemplate类型应用到availableCoupons
const availableLoading = ref(false);
const myLoading = ref(false);
const claimingCouponId = ref<string | number | null>(null);

// 可领取优惠券
const availableCoupons = ref<CouponTemplate[]>([]);

// 我的优惠券数据
const myCoupons = ref<BackendUserCoupon[]>([]);
const couponStatus = ref<string | number>('all');
const myCurrentPage = ref(1);
const myPageSize = ref(10);
const myTotal = ref(0);

// 优惠券详情
const detailVisible = ref(false);
const currentCoupon = ref<BackendUserCoupon | null>(null);

/**
 * 返回上一页
 */
function goBack() {
  router.go(-1);
}

/**
 * 处理标签页切换
 * @param tabName 标签页名称
 */
function handleTabChange(tabName: string) {
  if (tabName === 'available') {
    loadAvailableCoupons();
  } else if (tabName === 'my') {
    loadMyCoupons();
  }
}

/**
 * 加载可领取优惠券
 */
async function loadAvailableCoupons() {
  availableLoading.value = true;
  try {
    const response = await getCouponCenter() as any;
    console.log('优惠券中心数据:', response);

    // 修复：后端返回的是 list 字段，不是 coupons 字段
    if (response && response.list && Array.isArray(response.list)) {
      // 处理字段映射，确保前端模板能正确显示
      availableCoupons.value = response.list.map((coupon: any) => ({
        ...coupon,
        // 字段映射：后端返回 claim_status_text，前端期望 claim_button_text
        claim_button_text: coupon.claim_button_text || coupon.claim_status_text || '立即领取'
      }));
      console.log('成功解析优惠券数据，数量:', response.list.length);
      console.log('处理后的优惠券数据:', availableCoupons.value);
      // 调用调试函数验证数据结构
      debugCouponData(availableCoupons.value);
    } else if (response && response.coupons && Array.isArray(response.coupons)) {
      // 兼容旧的数据结构
      availableCoupons.value = response.coupons.map((coupon: any) => ({
        ...coupon,
        claim_button_text: coupon.claim_button_text || coupon.claim_status_text || '立即领取'
      }));
      console.log('使用兼容模式解析优惠券数据，数量:', response.coupons.length);
    } else {
      console.warn('未找到有效的优惠券数据，response:', response);
      availableCoupons.value = [];
    }
  } catch (error) {
    console.error('获取可领取优惠券失败:', error);
    availableCoupons.value = [];
  } finally {
    availableLoading.value = false;
  }
}

/**
 * 加载我的优惠券
 */
async function loadMyCoupons() {
  myLoading.value = true;
  try {
    // 将字符串状态值映射到对应的数字状态值
    let statusValue;
    switch (couponStatus.value) {
      case 'unused': statusValue = 1; break; // 未使用
      case 'used': statusValue = 2; break;   // 已使用
      case 'expired': statusValue = 3; break; // 已过期
      case 'all': statusValue = undefined; break;
      default: statusValue = undefined;
    }
    
    const params = {
      page: myCurrentPage.value,
      page_size: myPageSize.value,
      status: statusValue
    };
    
    const response = await getMyCoupons(params) as any;
    console.log('我的优惠券数据:', response);
    
    if (response) {
      // 后端返回的是{total, list}结构，其中list中每个元素包含嵌套的coupon对象
      myCoupons.value = response.list || [];
      myTotal.value = response.total || 0;
    } else {
      myCoupons.value = [];
      myTotal.value = 0;
    }
  } catch (error) {
    console.error('获取我的优惠券失败:', error);
    myCoupons.value = [];
    myTotal.value = 0;
  } finally {
    myLoading.value = false;
  }
}

/**
 * 处理优惠券领取
 * @param couponId 优惠券ID
 */
async function handleClaimCoupon(couponId: string | number) {
  claimingCouponId.value = couponId;
  try {
    const response = await claimCoupon({ coupon_id: couponId });
    console.log('领取优惠券成功:', response);
    
    ElMessage.success('领取成功！');
    
    // 更新优惠券状态
    const coupon = availableCoupons.value.find(c => c.id === couponId);
    if (coupon) {
      coupon.can_claim = false;
      coupon.user_claimed_count = (coupon.user_claimed_count || 0) + 1;
      coupon.claimed_count = (coupon.claimed_count || 0) + 1;
    }
    
    // 如果当前在"我的优惠券"标签页，刷新数据
    if (activeTab.value === 'my') {
      loadMyCoupons();
    }
  } catch (error: any) {
    console.error('领取优惠券失败:', error);
    
    // 根据错误码显示不同的提示信息
    if (error.code === 40004) {
      ElMessage.error('优惠券已抢完');
    } else if (error.code === 40005) {
      ElMessage.error('您已领取过此优惠券');
    } else if (error.code === 40006) {
      ElMessage.error('优惠券已过期');
    } else {
      ElMessage.error(error.message || '领取失败，请重试');
    }
  } finally {
    claimingCouponId.value = null;
  }
}

/**
 * 显示优惠券详情
 * @param coupon 优惠券对象
 */
function showCouponDetail(coupon: BackendUserCoupon) {
  currentCoupon.value = coupon;
  detailVisible.value = true;
}

/**
 * 格式化优惠券过期时间
 * @param endTime 结束时间
 * @returns 格式化后的时间字符串
 */
function formatCouponExpireTime(endTime: string): string {
  const end = new Date(endTime);
  const now = new Date();
  const diffTime = end.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays <= 0) {
    return '已过期';
  } else if (diffDays === 1) {
    return '今日过期';
  } else if (diffDays <= 7) {
    return `${diffDays}天后过期`;
  } else {
    return end.toLocaleDateString();
  }
}

/**
 * 格式化用户优惠券过期时间
 * @param coupon 用户优惠券对象
 * @returns 格式化后的时间字符串
 */
function formatUserCouponExpireTime(coupon: any): string {
  if (!coupon || !coupon.coupon) {
    return '暂无信息';
  }
  
  if (coupon.status === 2) { // 已使用
    return `已于 ${formatDateTime(coupon.used_time)} 使用`;
  }
  
  const end = new Date(coupon.coupon.end_time);
  const now = new Date();
  const diffTime = end.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays <= 0) {
    return '已过期';
  } else if (diffDays === 1) {
    return '今日过期';
  } else if (diffDays <= 7) {
    return `${diffDays}天后过期`;
  } else {
    return `${end.toLocaleDateString()} 过期`;
  }
}

/**
 * 格式化日期时间
 * @param dateTime 日期时间字符串
 * @returns 格式化后的日期时间
 */
function formatDateTime(dateTime: string): string {
  const date = new Date(dateTime);
  return date.toLocaleString();
}

/**
 * 获取优惠券领取状态文本
 * @param coupon 优惠券对象
 * @returns 状态文本
 */
function getClaimStatusText(coupon: CouponTemplate): string {
  if (coupon.user_claimed_count >= coupon.per_user_limit) {
    return '已领取';
  }
  if (coupon.claimed_count >= coupon.total_limit) {
    return '已抢完';
  }
  const now = new Date();
  const endTime = new Date(coupon.end_time);
  if (now > endTime) {
    return '已过期';
  }
  return '不可领取';
}

/**
 * 获取优惠券状态文本
 * @param status 优惠券状态
 * @returns 状态文本
 */
function getCouponStatusText(status: CouponStatus | string | number): string {
  const statusTexts: Record<string, string> = {
    '1': '未使用', // 未使用
    '2': '已使用', // 已使用
    '3': '已过期', // 已过期
    'unused': '未使用',
    'used': '已使用',
    'expired': '已过期'
  };
  
  const statusKey = status.toString();
  return statusTexts[statusKey] || '未知';
}

/**
 * 获取优惠券状态标签类型
 * @param status 优惠券状态
 * @returns 标签类型
 */
function getCouponStatusType(status: CouponStatus | string | number): string {
  const statusTypes: Record<string, string> = {
    '1': 'success', // 未使用
    '2': 'info',    // 已使用
    '3': 'danger',  // 已过期
    'unused': 'success',
    'used': 'info',
    'expired': 'danger'
  };
  
  const statusKey = status.toString();
  return statusTypes[statusKey] || '';
}

/**
 * 处理图片加载错误
 * @param {Event} e - 错误事件
 */
function handleImageError(e: Event) {
  // 图片加载失败时隐藏图片元素
  if (e.target) {
    (e.target as HTMLImageElement).style.display = 'none';
  }
}

/**
 * 跳转到商家详情页
 * @param {string|number} merchantId - 商家ID
 */
function goToMerchant(merchantId: string | number) {
  // 跳转到商家详情页
  router.push({
    name: 'UserTakeoutDetail',
    params: { id: String(merchantId) } // 确保id是字符串类型以匹配路由参数
  });
}

// 调试函数：验证优惠券数据结构
const debugCouponData = (coupons: any[]) => {
  console.log('=== 优惠券数据结构验证 ===');
  console.log('优惠券总数:', coupons.length);

  if (coupons.length > 0) {
    const firstCoupon = coupons[0];
    console.log('第一个优惠券数据结构:', firstCoupon);
    console.log('关键字段检查:');
    console.log('- id:', firstCoupon.id);
    console.log('- name:', firstCoupon.name);
    console.log('- amount:', firstCoupon.amount);
    console.log('- min_order_amount:', firstCoupon.min_order_amount);
    console.log('- can_claim:', firstCoupon.can_claim);
    console.log('- claim_button_text:', firstCoupon.claim_button_text);
    console.log('- claim_status_text:', firstCoupon.claim_status_text);
    console.log('- end_time:', firstCoupon.end_time);
  }
};

// 测试函数：使用模拟数据验证修复效果
const testWithMockData = () => {
  console.log('=== 使用模拟数据测试 ===');

  // 模拟后端返回的数据结构
  const mockResponse = {
    code: 200,
    message: "success",
    data: {
      total: 1,
      list: [
        {
          id: 2,
          promotion_id: 0,
          merchant_id: 1,
          merchant_name: "测试商家",
          merchant_logo: "",
          name: "新的活动满减",
          description: "活动满10元减2元",
          type: 1,
          type_text: "满减券",
          amount: 20,
          min_order_amount: 10,
          max_discount_amount: 0,
          apply_to_all: true,
          start_time: "2025-07-20T21:05:47+08:00",
          end_time: "2025-08-19T20:58:47+08:00",
          status: 1,
          status_text: "已发布",
          can_claim: true,
          claim_status_text: "立即领取"
        }
      ]
    }
  };

  // 模拟数据处理逻辑
  if (mockResponse.data && mockResponse.data.list && Array.isArray(mockResponse.data.list)) {
    const processedCoupons = mockResponse.data.list.map((coupon: any) => ({
      ...coupon,
      claim_button_text: coupon.claim_button_text || coupon.claim_status_text || '立即领取'
    }));

    console.log('模拟数据处理结果:', processedCoupons);
    console.log('第一个优惠券的claim_button_text:', processedCoupons[0].claim_button_text);
    console.log('第一个优惠券的can_claim:', processedCoupons[0].can_claim);

    // 验证模板渲染逻辑
    const firstCoupon = processedCoupons[0];
    console.log('模板渲染验证:');
    console.log('- 优惠券名称:', firstCoupon.name);
    console.log('- 优惠金额:', firstCoupon.amount);
    console.log('- 使用门槛:', firstCoupon.min_order_amount);
    console.log('- 是否可领取:', firstCoupon.can_claim);
    console.log('- 按钮文本:', firstCoupon.claim_button_text);
    console.log('- 应该显示的内容:', firstCoupon.can_claim ? '领取按钮' : '不可领取状态');
  }
};

// 页面加载时初始化数据
onMounted(() => {
  // 先运行测试验证逻辑
  testWithMockData();
  // 然后加载实际数据
  loadAvailableCoupons();
});
</script>

<style scoped>
.coupon-center {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 20px;
}

.page-header {
  background: white;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 14px;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.coupon-tabs {
  background: white;
  margin: 0;
}

.coupon-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding: 0 16px;
  background: white;
}

.coupon-tabs :deep(.el-tabs__content) {
  padding: 0;
}

.tab-content {
  padding: 16px;
  min-height: 400px;
}

.filter-bar {
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.coupons-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.coupon-card {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  justify-content: space-between;
}

.coupon-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.available-coupon {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  color: white;
}

.available-coupon.disabled {
  background: linear-gradient(135deg, #ccc 0%, #ddd 100%);
  opacity: 0.7;
}

.my-coupon {
  background: linear-gradient(135deg, #4ecdc4 0%, #6dd5ed 100%);
  color: white;
}

.my-coupon.used {
  background: linear-gradient(135deg, #95a5a6 0%, #bdc3c7 100%);
}

.my-coupon.expired {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.coupon-card::before {
  content: '';
  position: absolute;
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  background: #f5f5f5;
  border-radius: 50%;
  box-shadow: -10px 0 0 #f5f5f5;
}

.coupon-left {
  flex: 0 0 90px;
  text-align: center;
  padding-right: 20px;
  border-right: 2px dashed rgba(255, 255, 255, 0.5);
  margin-right: 16px;
}

.amount {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 4px;
}

.condition {
  font-size: 12px;
  opacity: 0.9;
}

.coupon-middle {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0 10px;
}

.coupon-right {
  flex: 0 0 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-left: 2px dashed rgba(255, 255, 255, 0.5);
  padding-left: 16px;
}

.coupon-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.merchant-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.merchant-logo {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.merchant-logo-detail {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-right: 10px;
}

.shop-btn {
  margin-top: 8px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.5);
  color: white;
  font-weight: 600;
}

.shop-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.merchant-name-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.merchant-name {
  font-size: 14px;
  opacity: 0.8;
}

.expire-time {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 8px;
}

.claim-btn {
  align-self: flex-start;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.5);
  color: white;
  font-weight: 600;
}

.claim-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.claimed-text {
  font-size: 12px;
  opacity: 0.8;
  align-self: flex-start;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.status-badge {
  align-self: flex-start;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 优惠券详情弹窗样式 */
.coupon-detail {
  padding: 16px;
}

.detail-coupon-card {
  display: flex;
  background: linear-gradient(135deg, #4ecdc4 0%, #6dd5ed 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  margin-bottom: 20px;
}

.detail-coupon-card .coupon-left {
  border-right: 2px dashed rgba(255, 255, 255, 0.5);
}

.detail-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  flex: 0 0 100px;
  color: #666;
  font-size: 14px;
}

.value {
  flex: 1;
  color: #333;
  font-size: 14px;
  word-break: break-all;
}
</style>