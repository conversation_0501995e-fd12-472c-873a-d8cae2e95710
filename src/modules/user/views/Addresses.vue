<!--
  用户收货地址管理页面
  提供地址的增删改查功能
-->
<template>
  <div class="user-addresses-page">
    <!-- 地址列表 -->
    <div class="address-list" v-if="!loading">
      <!-- 添加新地址 -->
      <el-card class="add-address-card" @click="showAddAddressDialog">
        <div class="add-address-content">
          <el-icon class="add-icon"><Plus /></el-icon>
          <p>添加新地址</p>
        </div>
      </el-card>
      
      <!-- 已有地址列表 -->
      <el-card 
        v-for="address in addresses" 
        :key="address.id" 
        class="address-card"
        :class="{ 'is-default': address.is_default }"
      >
        <div class="address-header">
          <div class="address-title">
            <span class="name">{{ address.receiver_name }}</span>
            <span class="phone">{{ formatPhone(address.receiver_mobile) }}</span>
          </div>
          <el-tag v-if="address.is_default" type="success" size="small">默认地址</el-tag>
        </div>
        <div class="address-detail">
          {{ formatAddress(address) }}
        </div>
        <div class="address-actions">
          <el-button 
            v-if="!address.is_default" 
            type="primary" 
            text 
            @click="setAsDefault(address.id)"
          >
            设为默认
          </el-button>
          <el-button type="primary" text @click="showEditAddressDialog(address)">
            编辑
          </el-button>
          <el-button type="danger" text @click="showDeleteConfirm(address)">
            删除
          </el-button>
        </div>
      </el-card>
      
      <!-- 空状态 -->
      <el-empty 
        v-if="addresses.length === 0" 
        description="暂无收货地址" 
        :image-size="200"
      >
        <el-button type="primary" @click="showAddAddressDialog">添加新地址</el-button>
      </el-empty>
    </div>
    
    <!-- 加载状态 -->
    <div v-else class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton style="margin-top: 20px" :rows="3" animated />
    </div>
    
    <!-- 地址编辑对话框 -->
    <el-dialog
      v-model="addressDialogVisible"
      :title="isEditing ? '编辑收货地址' : '新增收货地址'"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="addressFormRef"
        :model="addressForm"
        :rules="addressRules"
        label-width="80px"
      >
        <el-form-item label="收货人" prop="name">
          <el-input v-model="addressForm.name" placeholder="请输入收货人姓名" />
        </el-form-item>
        
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="addressForm.phone" placeholder="请输入手机号码" />
        </el-form-item>
        
        <el-form-item label="所在社区" prop="region">
          <CommunityAddressSelector @address-selected="handleCommunityAddressSelected" />
        </el-form-item>
        
        <el-form-item label="详细地址" prop="detail">
          <el-input
            v-model="addressForm.detail"
            type="textarea"
            :rows="3"
            placeholder="请输入详细地址，如街道、门牌号、小区、楼栋号、单元室等"
          />
        </el-form-item>
        
        <el-form-item>
          <el-checkbox v-model="addressForm.isDefault">设为默认收货地址</el-checkbox>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addressDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAddress" :loading="saving">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getUserAddresses, addUserAddress, updateUserAddress, deleteUserAddress, setDefaultAddress } from '../api/address';
import type { UserAddress } from '../types';
import CommunityAddressSelector from '@/components/CommunityAddressSelector.vue';

import { useSystemStore } from '@/stores/systemStore';
const systemStore = useSystemStore();
const systemInfo = computed(() => systemStore.systemInfo);

interface Region {
  province: string;
  city: string;
  district: string;
}
// 地址列表
const addresses = ref<UserAddress[]>([]);
const loading = ref(true);
const regions = ref<Region>(JSON.parse(systemInfo.value?.region || '{}'));

// 地址表单对话框
const addressDialogVisible = ref(false);
const addressFormRef = ref();
const isEditing = ref(false);
const currentAddressId = ref('');
const saving = ref(false);

// 地址表单数据
const addressForm = reactive({
  name: '',
  phone: '',
  region: [] as string[],
  province: '',
  city: '',
  district: '',
  detail: '',
  isDefault: false,
  communityAddress: {
    fullPath: '',
    longitude: 0,
    latitude: 0,
    communityId: null,
    buildingId: null,
    unitId: null
  }
});

// 地址表单验证规则
const addressRules = {
  name: [
    { required: true, message: '请输入收货人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  region: [
    { required: true, message: '请选择所在地区', trigger: 'change' }
  ],
  detail: [
    { required: true, message: '请输入详细地址', trigger: 'blur' },
    { min: 5, max: 100, message: '长度在 5 到 100 个字符', trigger: 'blur' }
  ]
};



/**
 * 加载用户地址列表
 */
async function loadAddresses() {
  console.log('地址组件加载，系统地区设置:', regions.value);

  loading.value = true;
  try {
    const data:any = await getUserAddresses();
    console.log('获取地址列表成功:', data);
    addresses.value = data.list || [];
  } catch (error) {
    console.error('获取地址列表失败:', error);
    ElMessage.error('获取地址列表失败，请稍后重试');
  } finally {
    loading.value = false;
  }
}

/**
 * 格式化手机号
 * @param phone 手机号
 * @returns 格式化后的手机号
 */
function formatPhone(phone: string): string {
  if (!phone) return '';
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}

/**
 * 格式化地址
 * @param address 地址对象
 * @returns 格式化后的地址字符串
 */
function formatAddress(address: UserAddress): string {
  return `${address.province} ${address.city} ${address.district} ${address.detailed_address}`;
}

/**
 * 显示添加地址对话框
 */
function showAddAddressDialog() {
  isEditing.value = false;
  currentAddressId.value = '';
  resetAddressForm();
  addressDialogVisible.value = true;
}

/**
 * 显示编辑地址对话框
 * @param address 地址对象
 */
function showEditAddressDialog(address: UserAddress) {
  isEditing.value = true;
  currentAddressId.value = address.id;
  
  // 填充表单数据 - 字段名与后端 DTO 对应
  addressForm.name = address.receiver_name || '';
  addressForm.phone = address.receiver_mobile || '';
  addressForm.region = [address.province, address.city, address.district];
  addressForm.province = address.province;
  addressForm.city = address.city;
  addressForm.district = address.district;
  addressForm.detail = address.detailed_address || '';
  addressForm.isDefault = address.is_default || false;
  
  addressDialogVisible.value = true;
}

/**
 * 重置地址表单
 */
function resetAddressForm() {
  addressForm.name = '';
  addressForm.phone = '';
  addressForm.region = [];
  addressForm.province = '';
  addressForm.city = '';
  addressForm.district = '';
  addressForm.detail = '';
  addressForm.isDefault = false;
  addressForm.communityAddress = {
    fullPath: '',
    longitude: 0,
    latitude: 0,
    communityId: null,
    buildingId: null,
    unitId: null
  };
  
  if (addressFormRef.value) {
    addressFormRef.value.resetFields();
  }
}

/**
 * 处理社区地址选择变化
 * @param addressInfo 选择的社区地址信息
 */
function handleCommunityAddressSelected(addressInfo :any) {
  // 保存社区地址详情
  addressForm.communityAddress = addressInfo;
  
  // 从fullPath中提取地址信息
  if (addressInfo && addressInfo.fullPath) {
    // 将社区地址设置为详细地址的一部分
    addressForm.detail = addressInfo.fullPath + ' ' + (addressForm.detail || '');
    
    // 如果接口返回的地址包含城市等信息，可以进一步解析
    // 这里作为示例，我们仅使用一个默认的省市区
    // 实际项目中可能需要与后端确认如何获取确切的省市区信息
    addressForm.province = regions.value.province;
    addressForm.city = regions.value.city;
    addressForm.district = regions.value.district;
    addressForm.region = [addressForm.province, addressForm.city, addressForm.district];
  }
}

/**
 * 保存地址
 */
async function saveAddress() {
  if (!addressFormRef.value) return;
  
  await addressFormRef.value.validate(async (valid: boolean) => {
    if (!valid) return;
    
    saving.value = true;
    
    try {
      // 构建地址数据 - 字段名与后端 DTO 保持一致
      const addressData = {
        receiver_name: addressForm.name,
        receiver_mobile: addressForm.phone,
        province: addressForm.province,
        city: addressForm.city,
        district: addressForm.district,
        detailed_address: addressForm.detail,
        is_default: addressForm.isDefault,
        phone: addressForm.phone,
        postal_code: '', // 可选字段
        address_tag: '', // 可选字段
        location_longitude: addressForm.communityAddress.longitude || 0, // 从社区地址选择器获取经度
        location_latitude: addressForm.communityAddress.latitude || 0,   // 从社区地址选择器获取纬度
        community_id: addressForm.communityAddress.communityId || 0,    // 社区ID
        building_id: addressForm.communityAddress.buildingId || 0,      // 楼栋ID
        unit_id: addressForm.communityAddress.unitId || 0               // 单元ID
      };
      
      if (isEditing.value) {
        // 更新地址
        await updateUserAddress(currentAddressId.value, addressData);
        ElMessage.success('地址更新成功');
      } else {
        // 添加新地址
        await addUserAddress(addressData);
        ElMessage.success('地址添加成功');
      }
      
      // 重新加载地址列表
      await loadAddresses();
      addressDialogVisible.value = false;
    } catch (error) {
      console.error('保存地址失败:', error);
      ElMessage.error('保存地址失败，请稍后重试');
    } finally {
      saving.value = false;
    }
  });
}

/**
 * 设置默认地址
 * @param id 地址ID
 */
async function setAsDefault(id: string) {
  try {
    await setDefaultAddress(id);
    ElMessage.success('默认地址设置成功');
    await loadAddresses();
  } catch (error) {
    console.error('设置默认地址失败:', error);
    ElMessage.error('设置默认地址失败，请稍后重试');
  }
}

/**
 * 显示删除地址确认对话框
 * @param address 地址对象
 */
function showDeleteConfirm(address: UserAddress) {
  ElMessageBox.confirm(
    '确定要删除这个地址吗？',
    '删除地址',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteUserAddress(address.id);
      ElMessage.success('地址删除成功');
      await loadAddresses();
    } catch (error) {
      console.error('删除地址失败:', error);
      ElMessage.error('删除地址失败，请稍后重试');
    }
  }).catch(() => {
    // 取消删除，不做任何操作
  });
}

onMounted(() => {
  loadAddresses();
});
</script>

<style scoped>
.user-addresses-page {
  padding: 10px 0;
}

.loading-container {
  padding: 20px;
}

.address-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.address-card, .add-address-card {
  height: 180px;
  transition: all 0.3s;
  position: relative;
}

.address-card:hover, .add-address-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.address-card.is-default {
  border: 1px solid #67c23a;
}

.add-address-card {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border: 1px dashed #ccc;
}

.add-address-content {
  text-align: center;
  color: #909399;
}

.add-icon {
  font-size: 30px;
  margin-bottom: 10px;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
}

.address-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.name {
  font-weight: bold;
}

.phone {
  color: #606266;
}

.address-detail {
  min-height: 60px;
  margin-bottom: 20px;
  line-height: 1.6;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.address-actions {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
