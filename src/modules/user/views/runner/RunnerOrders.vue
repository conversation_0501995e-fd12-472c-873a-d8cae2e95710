<!--
  @component: RunnerOrders
  @author: AI Assistant
  @date: 2025-06-08
  @version: 1.1.0
  @description: 跑腿员订单管理页面，包括接单、订单状态更新、收入管理等功能
-->
<template>
  <div class="runner-orders-page">
    <!-- 搜索筛选区域 -->
    <el-card class="search-card">
      <SearchForm
        :initial-form-data="searchForm"
        @search="handleSearch"
        @reset="handleReset"
      />
    </el-card>

    <!-- 订单统计卡片 -->
    <OrderStats :stats="orderStats" />

    <!-- 订单列表 -->
    <el-card class="table-card">
      <OrdersTable
        :data="tableData"
        :loading="tableLoading"
        :pagination="pagination"
        @update:pagination="updatePagination"
        @refresh="refreshTable"
        @accept-order="acceptOrder"
        @pick-up="handlePickUpOrder"
        @complete-order="completeOrder"
        @cancel-order="cancelOrder"
        @view-detail="viewOrderDetail"
        @update-status="updateDeliveryStatus"
      />
    </el-card>

    <!-- 订单详情对话框 -->
    <OrderDetailDialog
      v-model:visible="orderDetailVisible"
      :order="selectedOrder"
      :show-status-update="selectedOrder?.status === 2"
      @status-update="submitStatusUpdate"
      @close="orderDetailVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
// 导入跑腿员订单相关API
import {
  loadRunnerOrders,
  getRunnerOrderStats,
  acceptRunnerOrder,
  // 注释: 移除未使用的startRunnerOrderDelivery导入，以修复TS6133警告
  // startRunnerOrderDelivery已不再使用，相关功能已在其他函数中实现
  pickupRunnerOrder,
  completeRunnerOrder,
  cancelRunnerOrder,
  updateRunnerOrderStatus
} from '@/modules/user/api/runnerOrder';
import { ElMessage, ElMessageBox } from 'element-plus';

// 引入拆分后的子组件
import SearchForm from './components/orders/SearchForm.vue';
import OrderStats from './components/orders/OrderStats.vue';
import OrdersTable from './components/orders/OrdersTable.vue';
import OrderDetailDialog from './components/orders/OrderDetailDialog.vue';

// 表格数据
const tableData = ref<any[]>([]);

// 表格加载状态
const tableLoading = ref(false);

// 搜索表单
const searchForm = reactive({
  status: '',
  orderNo: '',
  dateRange: [],
  pickupAddress: '',
  deliveryAddress: ''
});

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 订单统计
const orderStats = reactive({
  pending: 0,
  accepted: 0,
  delivering: 0,
  completed: 0
});

// 订单详情对话框
const orderDetailVisible = ref(false);
const selectedOrder = ref<any>(null);

/**
 * 更新分页信息
 * @param {Object} newPagination 新的分页信息
 */
const updatePagination = (newPagination: any) => {
  Object.assign(pagination, newPagination);
};

/**
 * 加载订单列表
 */
const loadOrders = async () => {
  tableLoading.value = true;
  try {
    const params = {
      current: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...searchForm
    };
    
    // 请求返回的数据类型已经被响应拦截器处理为 data 部分
    // 因此返回的是 {total, list} 结构
    const response = await loadRunnerOrders(params) as any;
    console.log('API返回数据:', response)
    
    if (response && Array.isArray(response.list)) {
      // 字段映射，调整字段名以适配表格组件
      tableData.value = response.list.map((item: any) => ({
        ...item,
        // 蛇形命名转驼峰命名，添加表格组件所需字段
        id: item.id,
        orderNo: item.order_no,
        createdAt: item.create_time,
        pickupAddress: item.pickup_address,
        deliveryAddress: item.delivery_address,
        // 计算字段（如果数据中有，则使用数据中的值）
        deliveryFee: Number(item.delivery_fee || 0),
        runnerIncome: Number(item.service_fee || 0),
        estimatedTime: item.estimate_time,
        distance: Number(item.distance || 0),
        statusText: getStatusText(item.status)
      }));
      pagination.total = response.total || 0;
      console.log('表格数据（映射后）:', tableData.value)
    } else {
      tableData.value = [];
      pagination.total = 0;
      console.error('返回数据格式不符合预期', response);
    }
  } catch (error: any) {
    ElMessage.error(error.message || '加载订单列表失败');
  } finally {
    tableLoading.value = false;
  }
};

/**
 * 获取订单统计
 */
const getOrderStats = async () => {
  try {
    const response = await getRunnerOrderStats();
    Object.assign(orderStats, response.data);
  } catch (error: any) {
    console.error('获取订单统计失败:', error);
  }
};

/**
 * 获取状态文本
 * @param {number} status 状态码
 * @returns {string} 状态文本
 */
const getStatusText = (status: number) => {
  switch (status) {
    case 10: return '待支付';
    case 20: return '待接单'; // 已支付，待接单
    case 30: return '已接单'; // 已接单，待取货
    case 40: return '配送中'; // 已取货，配送中
    case 50: return '已完成';
    case 60: return '已取消';
    // 兼容旧状态码
    case 0: return '待接单';
    case 1: return '已接单';
    case 2: return '配送中';
    case 3: return '已完成';
    case 4: return '已取消';
    default: return '未知(' + status + ')';
  }
};

/**
 * 搜索处理
 * @param {Object} formData 表单数据
 */
const handleSearch = (formData: any) => {
  Object.assign(searchForm, formData);
  pagination.currentPage = 1;
  loadOrders();
};

/**
 * 重置处理
 * @param {Object} formData 重置后的表单数据
 */
const handleReset = (formData: any) => {
  Object.assign(searchForm, formData);
  pagination.currentPage = 1;
  loadOrders();
};

/**
 * 刷新表格
 */
const refreshTable = () => {
  loadOrders();
  getOrderStats();
};

/**
 * 接单
 * @param {Object} order 订单数据
 */
const acceptOrder = async (order: any) => {
  try {
    await ElMessageBox.confirm('确定要接受这个订单吗？', '确认接单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    await acceptRunnerOrder(order.id);
    
    ElMessage.success('接单成功');
    refreshTable();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '接单失败');
    }
  }
};

// 注释: 原本的startDelivery方法未被调用，已删除以解决TS6133错误

/**
 * 取货
 * @param {Object} order 订单数据
 * @description 处理跑腿员取货事件，将订单从已接单(30)状态变为已取货/配送中(40)状态
 */
const handlePickUpOrder = async (order: any) => {
  try {
    // 尝试获取取货验证码
    const { value: pickupCode } = await ElMessageBox.prompt(
      '请输入取货验证码（如无验证码可留空）', 
      '确认取货', 
      {
        confirmButtonText: '确认取货',
        cancelButtonText: '取消',
        type: 'info',
        inputPattern: /^[0-9]*$/,
        inputErrorMessage: '验证码应为数字或留空'
      }
    );

    // 使用新的API进行取货
    await pickupRunnerOrder(order.id, pickupCode || undefined);
    
    ElMessage.success('取货成功，已开始配送');
    refreshTable();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '取货操作失败');
    }
  }
};

/**
 * 完成订单
 * @param {Object} order 订单数据
 */
const completeOrder = async (order: any) => {
  try {
    // 尝试获取送达验证码
    const { value: deliveryCode } = await ElMessageBox.prompt(
      '请输入送达验证码（如无验证码可留空）', 
      '确认完成配送', 
      {
        confirmButtonText: '完成配送',
        cancelButtonText: '取消',
        type: 'success',
        inputPattern: /^[0-9]*$/,
        inputErrorMessage: '验证码应为数字或留空'
      }
    );
    
    await completeRunnerOrder(order.id, deliveryCode || undefined);
    
    ElMessage.success('订单完成');
    refreshTable();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '操作失败');
    }
  }
};

/**
 * 取消订单
 * @param {Object} order 订单数据
 */
const cancelOrder = async (order: any) => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入取消原因', '取消订单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '请输入取消原因'
    });
    
    await cancelRunnerOrder(order.id, reason);
    
    ElMessage.success('订单已取消');
    refreshTable();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '操作失败');
    }
  }
};

/**
 * 查看订单详情
 * @param {Object} order 订单数据
 */
const viewOrderDetail = (order: any) => {
  selectedOrder.value = order;
  orderDetailVisible.value = true;
};

/**
 * 更新配送状态
 * @param {Object} order 订单数据
 */
const updateDeliveryStatus = (order: any) => {
  selectedOrder.value = order;
  orderDetailVisible.value = true;
};

/**
 * 提交状态更新
 * @param {Object} statusData 状态更新数据
 */
const submitStatusUpdate = async (statusData: any) => {
  try {
    await updateRunnerOrderStatus(selectedOrder.value.id, statusData.status, statusData.remark);
    
    ElMessage.success('状态更新成功');
    orderDetailVisible.value = false;
    refreshTable();
  } catch (error: any) {
    ElMessage.error(error.message || '状态更新失败');
  }
};

// 页面加载时初始化
onMounted(() => {
  loadOrders();
  getOrderStats();
});
</script>

<style scoped>
.runner-orders-page {
  padding: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}
</style>
