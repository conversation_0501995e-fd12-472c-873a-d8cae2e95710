<!--
  @component: RunnerRegister
  @author: AI Assistant
  @date: 2025-06-06
  @version: 2.0.0
  @description: 跑腿员注册页面，使用 el-form 实现，用户可以申请成为跑腿员，包括基本信息填写、身份认证上传等功能。
-->
<template>
  <div class="runner-register-page">
    <el-card class="register-card">
      <template #header>
        <div class="card-header">
          <h2>注册成为跑腿员</h2>
          <p>加入我们，开启您的配送事业</p>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="registerForm"
        :rules="formRules"
        label-width="120px"
        class="register-form"
      >
        <!-- 基本信息部分 -->
        <div class="form-section">
          <div class="form-section-title">基本信息</div>
          <el-form-item label="真实姓名" prop="realName">
            <el-input 
              v-model="registerForm.realName" 
              placeholder="请输入真实姓名"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="身份证号" prop="idCardNumber">
            <el-input 
              v-model="registerForm.idCardNumber" 
              placeholder="请输入身份证号码"
              maxlength="18" 
            />
          </el-form-item>
          <el-form-item label="手机号码" prop="mobile">
            <el-input 
              v-model="registerForm.mobile" 
              placeholder="请输入手机号码"
              maxlength="11" 
            />
          </el-form-item>
        </div>

        <!-- 服务区域信息部分 -->
        <div class="form-section">
          <div class="form-section-title">服务区域</div>
          <el-form-item label="服务区域" prop="areaCodes">
            <div class="community-address-container">
              <div class="selected-addresses">
                <div v-if="selectedAddresses.length === 0" class="no-selection">
                  请选择服务区域
                </div>
                <el-tag
                  v-for="(address, index) in selectedAddresses"
                  :key="index"
                  closable
                  @close="removeAddress(index)"
                  class="address-tag"
                >
                  {{ address.fullPath }}
                </el-tag>
              </div>
              <div class="address-selector">
                <CommunityAddressSelector
                  @address-selected="handleAddressSelected"
                  ref="addressSelectorRef"
                />
              </div>
            </div>
          </el-form-item>
          <el-form-item label="服务半径" prop="serviceRadius">
            <div style="display: flex; align-items: center;">
              <el-input-number
                v-model="registerForm.serviceRadius"
                :min="1"
                :max="20"
                :step="0.5"
                controls-position="right"
              />
              <span class="unit">公里</span>
            </div>
          </el-form-item>
        </div>

        <!-- 身份认证部分 -->
        <div class="form-section">
          <div class="form-section-title">身份认证</div>
          <p class="upload-tips">
            <el-icon><InfoFilled /></el-icon>
            请上传清晰的身份证照片和人脸照片，照片需四角完整，光线充足
          </p>
          
          <!-- 身份证正面 -->
          <el-form-item label="身份证正面" prop="idCardFrontPic" required>
            <div v-if="registerForm.idCardFrontPic" class="upload-current-file">
              <div v-if="isImageUrl(registerForm.idCardFrontPic)" class="image-preview">
                <el-image
                  :src="registerForm.idCardFrontPic"
                  :preview-src-list="[registerForm.idCardFrontPic]"
                  fit="cover"
                  class="preview-image"
                />
                <div class="preview-url">{{ registerForm.idCardFrontPic }}</div>
              </div>
            </div>
            
            <FileUploader
              :action="uploadAction"
              :headers="uploadHeaders"
              :data="{ type: 'id_front' }"
              :file-limit="1"
              :size-limit="5 * 1024 * 1024"
              accept="image/jpeg,image/jpg,image/png"
              file-usage="runner-id-front"
              upload-style="button"
              :show-file-list="false"
              :initial-files="registerForm.idCardFrontPic ? getInitialFiles(registerForm.idCardFrontPic) : []"
              @success="(response) => onUpload(response, 'idCardFrontPic')"
              @error="handleUploadError"
              class="custom-uploader"
            >
              <template #tip>
                <p>点击或拖拽照片到此区域上传</p>
                <p class="upload-tip">文件大小不超过{{ formatFileSize(5 * 1024 * 1024) }}</p>
              </template>
            </FileUploader>
          </el-form-item>

          <!-- 身份证背面 -->
          <el-form-item label="身份证背面" prop="idCardBackPic" required>
            <div v-if="registerForm.idCardBackPic" class="upload-current-file">
              <div v-if="isImageUrl(registerForm.idCardBackPic)" class="image-preview">
                <el-image
                  :src="registerForm.idCardBackPic"
                  :preview-src-list="[registerForm.idCardBackPic]"
                  fit="cover"
                  class="preview-image"
                />
                <div class="preview-url">{{ registerForm.idCardBackPic }}</div>
              </div>
            </div>
            
            <FileUploader
              :action="uploadAction"
              :headers="uploadHeaders"
              :data="{ type: 'id_back' }"
              :file-limit="1"
              :size-limit="5 * 1024 * 1024"
              accept="image/jpeg,image/jpg,image/png"
              file-usage="runner-id-back"
              upload-style="button"
              :show-file-list="false"
              :initial-files="registerForm.idCardBackPic ? getInitialFiles(registerForm.idCardBackPic) : []"
              @success="(response) => onUpload(response, 'idCardBackPic')"
              @error="handleUploadError"
              class="custom-uploader"
            >
              <template #tip>
                <p>点击或拖拽照片到此区域上传</p>
                <p class="upload-tip">文件大小不超过{{ formatFileSize(5 * 1024 * 1024) }}</p>
              </template>
            </FileUploader>
          </el-form-item>

          <!-- 人脸照片 -->
          <el-form-item label="人脸照片" prop="facePic" required>
            <div v-if="registerForm.facePic" class="upload-current-file">
              <div v-if="isImageUrl(registerForm.facePic)" class="image-preview">
                <el-image
                  :src="registerForm.facePic"
                  :preview-src-list="[registerForm.facePic]"
                  fit="cover"
                  class="preview-image"
                />
                <div class="preview-url">{{ registerForm.facePic }}</div>
              </div>
            </div>
            
            <FileUploader
              :action="uploadAction"
              :headers="uploadHeaders"
              :data="{ type: 'face' }"
              :file-limit="1"
              :size-limit="5 * 1024 * 1024"
              accept="image/jpeg,image/jpg,image/png"
              file-usage="runner-face"
              upload-style="button"
              :show-file-list="false"
              :initial-files="registerForm.facePic ? getInitialFiles(registerForm.facePic) : []"
              @success="(response) => onUpload(response, 'facePic')"
              @error="handleUploadError"
              class="custom-uploader"
            >
              <template #tip>
                <p>点击或拖拽照片到此区域上传</p>
                <p class="upload-tip">文件大小不超过{{ formatFileSize(5 * 1024 * 1024) }}</p>
              </template>
            </FileUploader>
          </el-form-item>
        </div>

        <!-- 表单操作按钮 -->
        <div class="form-actions">
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" :loading="submitting" @click="handleFormSubmit">
            提交申请
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import { FileUploader } from '@/components/common' // 确保路径正确
import CommunityAddressSelector from '@/components/CommunityAddressSelector.vue' // 导入社区地址选择器
import { registerRunner } from '@/modules/user/api/runnerApply' // 导入跑腿员注册API
import type { RunnerRegisterParams } from '@/modules/user/api/runnerApply' // 导入跑腿员注册参数类型
import { useRouter } from 'vue-router'

const router = useRouter()

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const registerForm = reactive({
  realName: '',
  idCardNumber: '',
  mobile: '',
  areaCodes: [] as (string | number)[],
  serviceRadius: 5.0,
  idCardFrontPic: '',
  idCardBackPic: '',
  facePic: ''
})

// 提交状态
const submitting = ref(false)

// 表单校验规则
const formRules = reactive<FormRules>({
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  idCardNumber: [
    { required: true, message: '请输入身份证号码', trigger: 'blur' },
    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号码', trigger: 'blur' }
  ],
  mobile: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  areaCodes: [
    { required: true, message: '请选择至少一个服务区域', trigger: 'change', type: 'array' }
  ],
  serviceRadius: [
    { required: true, message: '请设置服务半径', trigger: 'blur' }
  ],
  idCardFrontPic: [
    { required: true, message: '请上传身份证正面照片', trigger: 'change' }
  ],
  idCardBackPic: [
    { required: true, message: '请上传身份证背面照片', trigger: 'change' }
  ],
  facePic: [
    { required: true, message: '请上传人脸照片', trigger: 'change' }
  ]
})

// 社区地址相关数据
interface SelectedAddressInfo {
  fullPath: string
  longitude: number
  latitude: number
  communityId: number | null
  buildingId: number | null
  unitId: number | null
}

const selectedAddresses = ref<SelectedAddressInfo[]>([])
const addressSelectorRef = ref<any>(null)

// 地址选择相关方法
const handleAddressSelected = (addressInfo: SelectedAddressInfo) => {
  console.log('收到地址选择事件:', addressInfo)
  
  if (!addressInfo || !addressInfo.fullPath) {
    ElMessage.warning('地址信息不完整')
    return
  }
  
  // 检查是否重复
  const isDuplicate = selectedAddresses.value.some(addr => addr.fullPath === addressInfo.fullPath)
  if (isDuplicate) {
    ElMessage.warning('该地址已经添加过了')
    return
  }
  
  // 直接添加到列表
  selectedAddresses.value.push({ ...addressInfo })
  updateAreaCodes()
  
  // 清空选择器
  if (addressSelectorRef.value) {
    addressSelectorRef.value.selectedKeys = []
  }
  
  ElMessage.success('地址添加成功')
}

// 移除了addSelectedAddress方法，现在直接在handleAddressSelected中处理

const removeAddress = (index: number) => {
  selectedAddresses.value.splice(index, 1)
  updateAreaCodes()
}

const updateAreaCodes = () => {
  registerForm.areaCodes = selectedAddresses.value
    .map(addr => addr.communityId)
    .filter((id): id is number => id !== null)
    .map(id => String(id))
  
  console.log('更新后的areaCodes:', registerForm.areaCodes)
  
  // 手动触发表单验证
  if (formRef.value) {
    formRef.value.validateField('areaCodes')
  }
}

// 上传配置
const uploadAction = computed(() => '/v1/user/secured/upload')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${localStorage.getItem('user_access_token') || ''}`,
}))

/**
 * @description 格式化文件大小
 * @param size 文件大小（字节）
 * @returns 格式化后的字符串
 */
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + " B";
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + " KB";
  } else if (size < 1024 * 1024 * 1024) {
    return (size / 1024 / 1024).toFixed(2) + " MB";
  } else {
    return (size / 1024 / 1024 / 1024).toFixed(2) + " GB";
  }
};

/**
 * @description 判断是否为图片URL
 * @param url 需要检查的URL
 * @returns 是否为图片URL
 */
const isImageUrl = (url: string) => {
  if (!url) {
    return false;
  }

  // 检查URL是否以图片扩展名结尾
  const extensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"];
  const lowercasedUrl = url.toLowerCase();

  // 检查扩展名
  return extensions.some((ext) => lowercasedUrl.endsWith(ext));
};

/**
 * @description 从 URL 中提取文件名
 * @param url URL地址
 * @returns 文件名
 */
const getFileNameFromUrl = (url: string): string => {
  if (!url) return '';
  
  try {
    // 尝试从 URL 中提取文件名
    const pathParts = new URL(url).pathname.split('/');
    const fileName = pathParts[pathParts.length - 1];
    
    // 解码 URL 编码的文件名
    return decodeURIComponent(fileName);
  } catch (error) {
    // 如果 URL 解析失败，直接返回最后一部分
    const parts = url.split('/');
    return parts[parts.length - 1];
  }
};

/**
 * @description 创建初始文件对象
 * @param value 文件URL
 * @returns 初始化文件列表
 */
const getInitialFiles = (value: string | undefined) => {
  if (!value) {
    return [];
  }
  
  try {
    // 创建一个包含当前文件URL的初始文件数组
    const initialFile = {
      id: 'initial-file',
      name: getFileNameFromUrl(value),
      size: 0,
      status: 'success' as const,
      raw: new File([], getFileNameFromUrl(value)),
      url: value,
      mime_type: isImageUrl(value) ? 'image/jpeg' : 'application/octet-stream'
    };
    
    return [initialFile];
  } catch (error) {
    console.error("创建初始文件对象失败:", error);
    return [];
  }
};

/**
 * @description 处理文件上传成功的回调
 * @param data 后端返回的响应
 * @param fieldName 表单字段名
 */
const onUpload = (data: Record<string, any>, fieldName: keyof typeof registerForm) => {
  // 提取文件URL
  let fileUrl = "";
  if (data && data.data && data.data.url) {
    fileUrl = data.data.url;
  } else if (data && data.data && data.data.file_url) {
    fileUrl = data.data.file_url;
  } else if (data && data.file_url) {
    fileUrl = data.file_url;
  } else if (data && data.url) {
    fileUrl = data.url;
  } else if (typeof data === "string") {
    fileUrl = data;
  }

  if (fileUrl) {
    // 更新表单数据，使用类型断言解决TypeScript类型错误
    // @ts-ignore 忽略类型错误，这里我们确定fieldName是registerForm的合法属性
    registerForm[fieldName] = fileUrl;
    
    // 触发表单校验
    nextTick(() => {
      // 表单校验
      formRef.value?.validateField(fieldName as string);
    });
    
    ElMessage.success('图片上传成功');
  } else {
    ElMessage.error(data.message || '无法获取上传文件地址');
  }
}

/**
 * @description 处理文件上传失败的回调
 * @param error 错误信息
 */
const handleUploadError = (error: any) => {
  console.error('上传失败:', error);
  ElMessage.error('图片上传失败，请重试');
}

/**
 * @description 提交表单数据
 * @param formData 表单数据
 */
const submitForm = async (formData: typeof registerForm): Promise<void> => {
  // 将表单数据转换为API所需的RunnerRegisterParams格式
  submitting.value = true
  try {
    // 构建接口所需的请求数据格式
    const payload: RunnerRegisterParams = {
      real_name: String(formData.realName || ''),
      id_card_number: String(formData.idCardNumber || ''),
      id_card_front_pic: String(formData.idCardFrontPic || ''),
      id_card_back_pic: String(formData.idCardBackPic || ''),
      face_pic: String(formData.facePic || ''),
      mobile: String(formData.mobile || ''),
      area_codes: Array.isArray(formData.areaCodes) ? formData.areaCodes.join(',') : '',
      service_radius: Number(formData.serviceRadius || 0)
    }

    // 使用专用的API函数代替直接请求
    await registerRunner(payload)
    
    ElMessage.success('申请提交成功，请等待审核')
    router.push('/user/runner/status')
    
  } catch (error: any) {
    ElMessage.error(error.message || '提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

/**
 * @description 处理表单提交事件
 */
const handleFormSubmit = async () => {
  if (!formRef.value) {
    return
  }
  try {
    // 验证表单
    formRef.value.validate((valid: boolean) => {
      if (!valid) {
        return
      }
      // 如果验证通过，提交表单
      submitForm(registerForm);
    });
  } catch (error) {
    console.error('表单验证错误', error);
    ElMessage.error('表单验证失败');
  }
}

/**
 * @description 重置表单数据
 */
const handleReset = () => {
  ElMessageBox.confirm(
    '确定要重置表单吗？所有已填写的信息将被清空。',
    '提示',
    {
      confirmButtonText: '确定重置',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      // 重置表单
      nextTick(() => {
        if (formRef.value) {
          formRef.value.resetFields()
          // 清空手动管理的图片URL
          registerForm.idCardFrontPic = ''
          registerForm.idCardBackPic = ''
          registerForm.facePic = ''
          // 清空选中的社区地址
          selectedAddresses.value = []
          // 重置地址选择器
          if (addressSelectorRef.value) {
            addressSelectorRef.value.selectedKeys = []
          }
        }
      })
      ElMessage.success('表单已重置')
    })
    .catch(() => {
      // 用户取消操作
  });
}


</script>

<style scoped>
.runner-register-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.register-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.card-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.register-form {
  margin-top: 20px;
}

/* PlusForm 会为每个 column 创建一个 div，这里针对 section 标题的样式 */
:deep(.form-section-title) {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
  /* PlusForm 的 group 会有自己的padding，可能需要调整 */
}

.upload-tips {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px; /* 减少与下方上传组件的间距 */
  padding: 12px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  color: #1890ff;
  font-size: 14px;
}

/* 自定义上传组件容器样式 */
.custom-uploader {
  width: 200px; /* 保持原宽度 */
  height: 120px; /* 保持原高度 */
}

.upload-area {
  width: 100%;
  height: 100%;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
  background-color: #fff; /* 确保背景色 */
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #d9d9d9; /* 给预览图一个边框 */
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.unit {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.form-actions .el-button {
  margin: 0 10px;
  min-width: 100px;
}

/* 确保 PlusForm 的 el-form-item 样式能正确应用 */
:deep(.el-form-item) {
  margin-bottom: 22px; /* 保持 Element Plus 默认的 margin-bottom */
}

/* 针对 PlusForm 内 checkbox 组的样式调整 */
:deep(.el-checkbox-group) {
  display: grid !important; /* 强制 grid 布局 */
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)) !important;
  gap: 10px !important;
}

:deep(.el-checkbox) {
  margin-right: 0 !important; /* 移除 el-checkbox 默认的右边距 */
}

/* 确保 FileUploader 内部的 el-upload 占满容器 */
:deep(.custom-uploader .el-upload) {
  width: 100%;
  height: 100%;
  display: block; /* 确保 el-upload 是块级元素 */
}

/* 社区地址选择器样式 */
.community-address-container {
  width: 100%;
}

.selected-addresses {
  min-height: 40px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 10px;
  background-color: #fafafa;
}

.no-selection {
  color: #909399;
  font-size: 14px;
  text-align: center;
  line-height: 20px;
}

.address-tag {
  margin: 2px 4px 2px 0;
  max-width: 300px;
}

.address-tag .el-tag__content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.address-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.address-selector .el-cascader {
  flex: 1;
  min-width: 300px;
}
</style>