/**
 * @file: index.ts
 * @author: AI Assistant
 * @date: 2025-01-27
 * @version: 1.0.0
 * @description: 跑腿员模块路由索引文件，统一导出所有跑腿员相关组件
 */

// 跑腿员注册页面
export { default as RunnerRegister } from './RunnerRegister.vue'

// 跑腿员申请状态页面
export { default as RunnerStatus } from './RunnerStatus.vue'

// 跑腿员工作状态管理页面
export { default as RunnerWorkStatus } from './RunnerWorkStatus.vue'

// 跑腿员订单管理页面
export { default as RunnerOrders } from './RunnerOrders.vue'

// 跑腿员收入管理页面
export { default as RunnerIncome } from './RunnerIncome.vue'

// 跑腿员设置页面
export { default as RunnerSettings } from './RunnerSettings.vue'

/**
 * 跑腿员模块路由配置
 */
export const runnerRoutes = [
  {
    path: '/user/runner/register',
    name: 'RunnerRegister',
    component: () => import('./RunnerRegister.vue'),
    meta: {
      title: '跑腿员注册',
      requiresAuth: true
    }
  },
  {
    path: '/user/runner/status',
    name: 'RunnerStatus',
    component: () => import('./RunnerStatus.vue'),
    meta: {
      title: '申请状态',
      requiresAuth: true
    }
  },
  {
    path: '/user/runner/work-status',
    name: 'RunnerWorkStatus',
    component: () => import('./RunnerWorkStatus.vue'),
    meta: {
      title: '工作状态',
      requiresAuth: true,
      requiresRunner: true
    }
  },
  {
    path: '/user/runner/orders',
    name: 'RunnerOrders',
    component: () => import('./RunnerOrders.vue'),
    meta: {
      title: '订单管理',
      requiresAuth: true,
      requiresRunner: true
    }
  },
  {
    path: '/user/runner/income',
    name: 'RunnerIncome',
    component: () => import('./RunnerIncome.vue'),
    meta: {
      title: '收入管理',
      requiresAuth: true,
      requiresRunner: true
    }
  },
  {
    path: '/user/runner/settings',
    name: 'RunnerSettings',
    component: () => import('./RunnerSettings.vue'),
    meta: {
      title: '跑腿员设置',
      requiresAuth: true,
      requiresRunner: true
    }
  }
]

/**
 * 跑腿员菜单配置
 */
export const runnerMenus = [
  {
    title: '跑腿员中心',
    icon: 'Bicycle',
    children: [
      {
        title: '注册申请',
        path: '/user/runner/register',
        icon: 'UserFilled'
      },
      {
        title: '申请状态',
        path: '/user/runner/status',
        icon: 'DocumentChecked'
      },
      {
        title: '工作状态',
        path: '/user/runner/work-status',
        icon: 'SwitchButton',
        requiresRunner: true
      },
      {
        title: '订单管理',
        path: '/user/runner/orders',
        icon: 'List',
        requiresRunner: true
      },
      {
        title: '收入管理',
        path: '/user/runner/income',
        icon: 'Money',
        requiresRunner: true
      },
      {
        title: '跑腿员设置',
        path: '/user/runner/settings',
        icon: 'Setting',
        requiresRunner: true
      }
    ]
  }
]

/**
 * 跑腿员状态枚举
 */
export enum RunnerStatusEnum {
  // 未申请
  NOT_APPLIED = 'not_applied',
  // 审核中
  PENDING = 'pending',
  // 审核通过
  APPROVED = 'approved',
  // 审核拒绝
  REJECTED = 'rejected',
  // 已禁用
  DISABLED = 'disabled'
}

/**
 * 跑腿员工作状态枚举
 */
export enum RunnerWorkStatusEnum {
  // 离线
  OFFLINE = 'offline',
  // 在线
  ONLINE = 'online',
  // 忙碌
  BUSY = 'busy',
  // 休息
  REST = 'rest'
}

/**
 * 订单状态枚举
 */
export enum OrderStatus {
  // 待接单
  PENDING = 'pending',
  // 已接单
  ACCEPTED = 'accepted',
  // 取餐中
  PICKING_UP = 'picking_up',
  // 配送中
  DELIVERING = 'delivering',
  // 已完成
  COMPLETED = 'completed',
  // 已取消
  CANCELLED = 'cancelled'
}

/**
 * 收入类型枚举
 */
export enum IncomeType {
  // 配送收入
  DELIVERY = 'delivery',
  // 奖励收入
  BONUS = 'bonus',
  // 补贴收入
  SUBSIDY = 'subsidy',
  // 扣款
  DEDUCTION = 'deduction'
}

/**
 * 提现状态枚举
 */
export enum WithdrawStatus {
  // 待处理
  PENDING = 'pending',
  // 处理中
  PROCESSING = 'processing',
  // 已到账
  SUCCESS = 'success',
  // 失败
  FAILED = 'failed'
}

/**
 * 跑腿员工具函数
 */
export const runnerUtils = {
  /**
   * 获取跑腿员状态文本
   */
  getStatusText(status: RunnerStatusEnum): string {
    switch (status) {
      case RunnerStatusEnum.NOT_APPLIED:
        return '未申请'
      case RunnerStatusEnum.PENDING:
        return '审核中'
      case RunnerStatusEnum.APPROVED:
        return '审核通过'
      case RunnerStatusEnum.REJECTED:
        return '审核拒绝'
      case RunnerStatusEnum.DISABLED:
        return '已禁用'
      default:
        return '未知状态'
    }
  },

  /**
   * 获取工作状态文本
   */
  getWorkStatusText(status: RunnerWorkStatusEnum): string {
    switch (status) {
      case RunnerWorkStatusEnum.OFFLINE:
        return '离线'
      case RunnerWorkStatusEnum.ONLINE:
        return '在线'
      case RunnerWorkStatusEnum.BUSY:
        return '忙碌'
      case RunnerWorkStatusEnum.REST:
        return '休息'
      default:
        return '未知状态'
    }
  },

  /**
   * 获取订单状态文本
   */
  getOrderStatusText(status: OrderStatus): string {
    switch (status) {
      case OrderStatus.PENDING:
        return '待接单'
      case OrderStatus.ACCEPTED:
        return '已接单'
      case OrderStatus.PICKING_UP:
        return '取餐中'
      case OrderStatus.DELIVERING:
        return '配送中'
      case OrderStatus.COMPLETED:
        return '已完成'
      case OrderStatus.CANCELLED:
        return '已取消'
      default:
        return '未知状态'
    }
  },

  /**
   * 获取收入类型文本
   */
  getIncomeTypeText(type: IncomeType): string {
    switch (type) {
      case IncomeType.DELIVERY:
        return '配送收入'
      case IncomeType.BONUS:
        return '奖励收入'
      case IncomeType.SUBSIDY:
        return '补贴收入'
      case IncomeType.DEDUCTION:
        return '扣款'
      default:
        return '其他'
    }
  },

  /**
   * 获取提现状态文本
   */
  getWithdrawStatusText(status: WithdrawStatus): string {
    switch (status) {
      case WithdrawStatus.PENDING:
        return '待处理'
      case WithdrawStatus.PROCESSING:
        return '处理中'
      case WithdrawStatus.SUCCESS:
        return '已到账'
      case WithdrawStatus.FAILED:
        return '失败'
      default:
        return '未知状态'
    }
  },

  /**
   * 格式化距离
   */
  formatDistance(distance: number): string {
    if (distance < 1000) {
      return `${distance}m`
    }
    return `${(distance / 1000).toFixed(1)}km`
  },

  /**
   * 格式化时间
   */
  formatDuration(minutes: number): string {
    if (minutes < 60) {
      return `${minutes}分钟`
    }
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}小时${mins}分钟`
  },

  /**
   * 计算两点间距离（简化版）
   */
  calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371 // 地球半径（公里）
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLng = (lng2 - lng1) * Math.PI / 180
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }
}