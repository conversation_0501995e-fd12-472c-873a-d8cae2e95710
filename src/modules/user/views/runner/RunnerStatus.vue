<!--
  @component: Runner<PERSON>tatus
  @author: AI Assistant
  @date: 2025-01-27
  @version: 1.0.0
  @description: 跑腿员申请状态查询页面，显示申请进度和审核结果
-->
<template>
  <div class="runner-status-page">
    <el-card class="status-card">
      <template #header>
        <div class="card-header">
          <h2>申请审核状态</h2>
          <p>查看您的跑腿员申请进度</p>
        </div>
      </template>

      <div v-loading="loading" class="status-content">
        <!-- 未申请状态 -->
        <div v-if="!applicationData" class="no-application">
          <el-empty description="您还未提交跑腿员申请">
            <el-button type="primary" @click="goToRegister">立即申请</el-button>
          </el-empty>
        </div>

        <!-- 有申请记录 -->
        <div v-else class="application-info">
          <!-- 申请基本信息 -->
          <div class="info-section">
            <h3>申请信息</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="申请编号">{{ applicationData.id }}</el-descriptions-item>
              <el-descriptions-item label="申请时间">{{ formatDate(applicationData.create_time) }}</el-descriptions-item>
              <el-descriptions-item label="当前状态">
                <el-tag :type="getStatusType(applicationData.status)">{{ applicationData.status_desc }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="预计审核时间" v-if="applicationData.estimatedAuditDays">
                {{ applicationData.estimatedAuditDays }} 个工作日
              </el-descriptions-item>
              <el-descriptions-item label="审核时间" v-if="applicationData.audit_time && applicationData.audit_time !== '0001-01-01T00:00:00Z'">
                {{ formatDate(applicationData.audit_time) }}
              </el-descriptions-item>
              <el-descriptions-item label="拒绝原因" v-if="applicationData.reject_reason" span="2">
                <span class="reject-reason">{{ applicationData.reject_reason }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 审核进度 -->
          <div class="progress-section">
            <h3>审核进度</h3>
            <el-steps :active="getStepActive()" align-center>
              <el-step title="提交申请" :icon="Check">
                <template #description>
                  <div class="step-desc">
                    <div>已完成</div>
                    <div class="step-time">{{ formatDate(applicationData.create_time) }}</div>
                  </div>
                </template>
              </el-step>
              <el-step 
                title="资料审核" 
                :icon="applicationData.status === 0 ? Loading : (applicationData.status === 1 ? Check : Close)"
                :status="getStepStatus(1)"
              >
                <template #description>
                  <div class="step-desc">
                    <div v-if="applicationData.status === 0">审核中</div>
                    <div v-else-if="applicationData.status === 1">审核通过</div>
                    <div v-else>审核拒绝</div>
                    <div class="step-time" v-if="applicationData.audit_time && applicationData.audit_time !== '0001-01-01T00:00:00Z'">
                      {{ formatDate(applicationData.audit_time) }}
                    </div>
                  </div>
                </template>
              </el-step>
              <el-step 
                title="审核完成" 
                :icon="applicationData.status === 1 ? Check : (applicationData.status === 2 ? Close : Clock)"
                :status="getStepStatus(2)"
              >
                <template #description>
                  <div class="step-desc">
                    <div v-if="applicationData.status === 1">申请通过</div>
                    <div v-else-if="applicationData.status === 2">申请被拒绝</div>
                    <div v-else>待处理</div>
                  </div>
                </template>
              </el-step>
            </el-steps>
          </div>

          <!-- 审核结果提示 -->
          <div class="result-section">
            <el-alert
              v-if="applicationData.status === 0"
              title="审核中"
              description="您的申请正在审核中，请耐心等待。审核结果将通过短信和APP推送通知您。"
              type="info"
              :closable="false"
              show-icon
            />
            <el-alert
              v-else-if="applicationData.status === 1"
              title="审核通过"
              description="恭喜您！您的跑腿员申请已通过审核，现在可以开始接单了。"
              type="success"
              :closable="false"
              show-icon
            >
              <template #default>
                <div class="success-actions">
                  <p>恭喜您！您的跑腿员申请已通过审核，现在可以开始接单了。</p>
                  <el-button type="primary" @click="goToWorkStatus">开始工作</el-button>
                </div>
              </template>
            </el-alert>
            <el-alert
              v-else-if="applicationData.status === 2"
              title="审核未通过"
              :description="`很抱歉，您的申请未通过审核。原因：${applicationData.reject_reason}`"
              type="error"
              :closable="false"
              show-icon
            >
              <template #default>
                <div class="error-actions">
                  <p>很抱歉，您的申请未通过审核。</p>
                  <p class="reject-reason">拒绝原因：{{ applicationData.reject_reason }}</p>
                  <el-button type="primary" @click="goToRegister">重新申请</el-button>
                </div>
              </template>
            </el-alert>
          </div>

          <!-- 操作按钮 -->
          <div class="actions">
            <el-button @click="refreshStatus" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新状态
            </el-button>
            <el-button 
              v-if="applicationData.status === 0" 
              type="primary" 
              @click="goToRegister"
            >
              修改申请
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close, Clock, Loading, Refresh } from '@element-plus/icons-vue'
import { getRunnerApplyStatus, type RunnerApplyStatusResponse } from '@/modules/user/api/runnerApply'
import { useRouter } from 'vue-router'

const router = useRouter()

// 内联 formatDate 函数以解决导入问题
const formatDate = (dateString: string | Date): string => {
  if (!dateString) return ''
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '无效日期'

  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 数据状态
const loading = ref(false)
const applicationData = ref<RunnerApplyStatusResponse | null>(null)

// 获取申请状态
// 获取申请状态
const getApplicationStatus = async () => {
  try {
    loading.value = true
    // 调用新的服务函数，它已经处理了 response.data 和 404 的情况
    const data = await getRunnerApplyStatus()
    console.log(data)
    applicationData.value = data
  } catch (error: any) {
    // getRunnerApplyStatus 在非404错误时会抛出，这里捕获并提示
    ElMessage.error(error.message || '获取申请状态失败')
    applicationData.value = null // 出错时也清空数据
  } finally {
    loading.value = false
  }
}

// 刷新状态
const refreshStatus = () => {
  getApplicationStatus()
}

// 获取状态类型
const getStatusType = (status: number) => {
  switch (status) {
    case 0: return 'warning' // 待审核
    case 1: return 'success' // 审核通过
    case 2: return 'danger'  // 审核拒绝
    default: return 'info'
  }
}

// 获取步骤激活状态
const getStepActive = () => {
  if (!applicationData.value) return 0
  
  switch (applicationData.value.status) {
    case 0: return 1 // 审核中
    case 1: return 3 // 审核通过
    case 2: return 2 // 审核拒绝
    default: return 1
  }
}

// 获取步骤状态
const getStepStatus = (step: number) => {
  if (!applicationData.value) return 'wait'
  
  const status = applicationData.value.status
  
  if (step === 1) {
    if (status === 0) return 'process' // 审核中
    if (status === 1) return 'finish'  // 审核通过
    if (status === 2) return 'error'   // 审核拒绝
  }
  
  if (step === 2) {
    if (status === 0) return 'wait'    // 待处理
    if (status === 1) return 'finish'  // 审核通过
    if (status === 2) return 'error'   // 审核拒绝
  }
  
  return 'wait'
}

// 跳转到注册页面
const goToRegister = () => {
  router.push('/user/runner/register')
}

// 跳转到工作状态页面
const goToWorkStatus = () => {
  router.push('/user/runner/work-status')
}

// 页面加载时获取状态
onMounted(() => {
  getApplicationStatus()
})
</script>

<style scoped>
.runner-status-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.status-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.card-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.status-content {
  min-height: 300px;
}

.no-application {
  padding: 40px 0;
}

.application-info {
  padding: 20px 0;
}

.info-section,
.progress-section,
.result-section {
  margin-bottom: 30px;
}

.info-section h3,
.progress-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.reject-reason {
  color: #f56c6c;
  font-weight: 500;
}

.step-desc {
  text-align: center;
  font-size: 12px;
}

.step-time {
  color: #909399;
  margin-top: 4px;
}

.success-actions,
.error-actions {
  margin-top: 12px;
}

.success-actions p,
.error-actions p {
  margin: 0 0 12px 0;
}

.actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.actions .el-button {
  margin: 0 10px;
}

:deep(.el-steps) {
  margin: 20px 0;
}

:deep(.el-step__description) {
  padding-right: 0;
}

:deep(.el-alert__content) {
  padding-left: 8px;
}
</style>