<!--
  @component: RunnerSettings
  @author: AI Assistant
  @date: 2025-06-08
  @version: 1.0.0
  @description: 跑腿员设置页面，包括个人信息、服务设置、通知设置、安全设置等功能
-->
<template>
  <div class="runner-settings-page">
    <el-row :gutter="20">
      <!-- 左侧菜单 -->
      <el-col :span="6">
        <el-card class="settings-menu">
          <el-menu
            v-model="activeMenu"
            mode="vertical"
            @select="handleMenuSelect"
          >
            <el-menu-item index="profile">
              <el-icon><User /></el-icon>
              <span>个人信息</span>
            </el-menu-item>
            <el-menu-item index="service">
              <el-icon><Setting /></el-icon>
              <span>服务设置</span>
            </el-menu-item>
            <el-menu-item index="notification">
              <el-icon><Bell /></el-icon>
              <span>通知设置</span>
            </el-menu-item>
            <el-menu-item index="security">
              <el-icon><Lock /></el-icon>
              <span>安全设置</span>
            </el-menu-item>
            <el-menu-item index="account">
              <el-icon><CreditCard /></el-icon>
              <span>收款账户</span>
            </el-menu-item>
          </el-menu>
        </el-card>
      </el-col>
      
      <!-- 右侧内容 -->
      <el-col :span="18">
        <!-- 使用重构后的组件 -->
        <ProfileSettings
          v-show="activeMenu === 'profile'"
          v-model="profileForm"
          :submitting="profileSubmitting"
          :upload-url="uploadUrl"
          @submit="updateProfile"
          @avatar-success="handleAvatarSuccess"
        />
        
        <ServiceSettings
          v-show="activeMenu === 'service'"
          v-model="serviceForm"
          :submitting="serviceSubmitting"
          :area-options="areaOptions"
          @submit="updateService"
        />
        
        <NotificationSettings
          v-show="activeMenu === 'notification'"
          v-model="notificationForm"
          :submitting="notificationSubmitting"
          @submit="updateNotification"
        />
        
        <SecuritySettings
          v-show="activeMenu === 'security'"
          :user-info="profileForm"
          :password-submitting="passwordSubmitting"
          :phone-submitting="phoneSubmitting"
          @password-change="handlePasswordChange"
          @phone-change="handlePhoneChange"
          @send-verify-code="handleSendVerifyCode"
        />
        
        <AccountSettings
          v-show="activeMenu === 'account'"
          :accounts="accountList"
          :account-submitting="accountSubmitting"
          @add-account="handleAddAccount"
          @edit-account="handleEditAccount"
          @delete-account="handleDeleteAccount"
          @set-default="handleSetDefault"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { User, Setting, Bell, Lock, CreditCard } from '@element-plus/icons-vue';
import { 
  ProfileSettings,
  ServiceSettings, 
  NotificationSettings,
  SecuritySettings,
  AccountSettings
} from '@/modules/user/views/runner/components/settings';
import { 
  getRunnerProfile, 
  updateRunnerProfile,
  getRunnerServiceSettings,
  updateRunnerServiceSettings,
  getRunnerNotificationSettings,
  updateRunnerNotificationSettings,
  changePassword,
  changePhone,
  sendVerifyCode,
  getRunnerAccounts,
  addRunnerAccount,
  updateRunnerAccount,
  deleteRunnerAccount,
  setDefaultAccount
} from '@/modules/user/api/runner';

// 当前活动菜单
const activeMenu = ref('profile');

// 个人信息表单
const profileForm = reactive({
  id: 0,
  user_id: 0,
  real_name: '',
  mobile: '',
  face_pic: '', // 与后端API返回字段保持一致
  id_card_number: '',
  status: 1,
  status_desc: '',
  working_status: 0,
  working_status_desc: '',
  is_online: false,
  create_time: '',
  last_login_time: '',
  score: 0,
  order_count: 0,
  cancel_count: 0,
  service_areas: [],
  // 添加其他后端可能返回的字段
  area_codes: '',
  current_location: '',
  id_card_front_pic: '',
  id_card_back_pic: ''
});

// 服务设置表单
const serviceForm = reactive({
  runner_id: 0,
  auto_accept_order: false,
  max_order_distance: 5.0,
  order_types: [1, 2, 3, 4],
  working_hours_start: "08:00",
  working_hours_end: "20:00",
  rest_days: [],
  max_simultaneous_order: 2,
  updated_at: ""
});

// 区域选项
const areaOptions = [
  { value: 'area1', label: '海淀区' },
  { value: 'area2', label: '朝阳区' },
  { value: 'area3', label: '西城区' },
  { value: 'area4', label: '东城区' },
  { value: 'area5', label: '丰台区' }
];

// 通知设置表单
const notificationForm = reactive({
  runner_id: 0,
  order_notification: true,
  system_notification: true,
  marketing_notification: false,
  payment_notification: true,
  sound: true,
  vibration: true,
  updated_at: ""
});

// 收款账户列表
const accountList = ref([]);

// 提交状态
const profileSubmitting = ref(false);
const serviceSubmitting = ref(false);
const notificationSubmitting = ref(false);
const passwordSubmitting = ref(false);
const phoneSubmitting = ref(false);
const accountSubmitting = ref(false);

// 上传URL
const uploadUrl = '/api/upload/avatar';

/**
 * 菜单选择处理
 * @param {string} index 菜单索引
 */
const handleMenuSelect = (index: string) => {
  activeMenu.value = index;
};

/**
 * 获取个人信息
 */
const getProfile = async () => {
  try {
    const res = await getRunnerProfile();
    console.log('获取个人信息成功:', res);
    // 响应拦截器已经处理了code和data，直接使用返回的数据
    Object.assign(profileForm, res);
  } catch (error) {
    console.error('获取个人信息失败:', error);
    ElMessage.error('获取个人信息失败');
  }
};

/**
 * 获取服务设置
 */
const getServiceSettings = async () => {
  try {
    const res = await getRunnerServiceSettings();
    // 响应拦截器已经处理了code和data，直接使用返回的数据
    Object.assign(serviceForm, res);
  } catch (error) {
    console.error('获取服务设置失败:', error);
    ElMessage.error('获取服务设置失败');
  }
};

/**
 * 获取通知设置
 */
const getNotificationSettings = async () => {
  try {
    const res = await getRunnerNotificationSettings();
    // 响应拦截器已经处理了code和data，直接使用返回的数据
    Object.assign(notificationForm, res);
  } catch (error) {
    console.error('获取通知设置失败:', error);
    ElMessage.error('获取通知设置失败');
  }
};

/**
 * 获取收款账户列表
 */
const getAccountList = async () => {
  try {
    const res :any = await getRunnerAccounts();
    // 响应拦截器已经处理了code和data，直接使用返回的数据
    accountList.value = res;
  } catch (error) {
    console.error('获取收款账户列表失败:', error);
    ElMessage.error('获取收款账户列表失败');
  }
};

/**
 * 更新个人信息
 */
const updateProfile = async () => {
  profileSubmitting.value = true;
  try {
    await updateRunnerProfile({
      real_name: profileForm.real_name,
      id_card_number: profileForm.id_card_number,
      mobile: profileForm.mobile,
      avatar: profileForm.face_pic // 使用face_pic字段作为avatar传送
    });
    // 响应拦截器已经处理了code和message，请求成功会直接进入这里
    ElMessage.success('个人信息更新成功');
  } catch (error) {
    console.error('个人信息更新失败:', error);
    ElMessage.error('个人信息更新失败');
  } finally {
    profileSubmitting.value = false;
  }
};

/**
 * 更新服务设置
 */
const updateService = async () => {
  serviceSubmitting.value = true;
  try {
    await updateRunnerServiceSettings({
      auto_accept_order: serviceForm.auto_accept_order,
      max_order_distance: serviceForm.max_order_distance,
      order_types: serviceForm.order_types,
      working_hours_start: serviceForm.working_hours_start,
      working_hours_end: serviceForm.working_hours_end,
      rest_days: serviceForm.rest_days,
      max_simultaneous_order: serviceForm.max_simultaneous_order
    });
    // 响应拦截器已经处理了code和message，请求成功会直接进入这里
    ElMessage.success('服务设置更新成功');
  } catch (error) {
    console.error('服务设置更新失败:', error);
    ElMessage.error('服务设置更新失败');
  } finally {
    serviceSubmitting.value = false;
  }
};

/**
 * 更新通知设置
 */
const updateNotification = async () => {
  notificationSubmitting.value = true;
  try {
    await updateRunnerNotificationSettings(notificationForm);
    // 响应拦截器已经处理了code和message，请求成功会直接进入这里
    ElMessage.success('通知设置更新成功');
  } catch (error) {
    console.error('通知设置更新失败:', error);
    ElMessage.error('通知设置更新失败');
  } finally {
    notificationSubmitting.value = false;
  }
};

/**
 * 处理修改密码
 * @param {Object} data 密码数据
 */
const handlePasswordChange = async (data: { oldPassword: string; newPassword: string }) => {
  passwordSubmitting.value = true;
  try {
    await changePassword(data);
    // 响应拦截器已经处理了code和message，请求成功会直接进入这里
    ElMessage.success('密码修改成功');
  } catch (error) {
    console.error('密码修改失败:', error);
    ElMessage.error('密码修改失败');
  } finally {
    passwordSubmitting.value = false;
  }
};

/**
 * 处理更改手机号
 * @param {Object} data 手机号数据
 */
const handlePhoneChange = async (data: { newPhone: string; verifyCode: string }) => {
  phoneSubmitting.value = true;
  try {
    await changePhone(data);
    // 响应拦截器已经处理了code和message，请求成功会直接进入这里
    ElMessage.success('手机号更换成功');
    // 更新个人信息中的手机号
    profileForm.mobile = data.newPhone;
  } catch (error) {
    console.error('手机号更换失败:', error);
    ElMessage.error('手机号更换失败');
  } finally {
    phoneSubmitting.value = false;
  }
};

/**
 * 处理发送验证码
 * @param {string} phone 手机号
 */
const handleSendVerifyCode = async (phone: string) => {
  try {
    await sendVerifyCode(phone);
    // 响应拦截器已经处理了code和message，请求成功会直接进入这里
    ElMessage.success('验证码发送成功');
  } catch (error) {
    console.error('验证码发送失败:', error);
    ElMessage.error('验证码发送失败');
  }
};

/**
 * 处理头像上传成功
 * @param {Object} response 上传响应
 */
const handleAvatarSuccess = (url: string) => {
  // API中头像字段为face_pic
  profileForm.face_pic = url;
};

/**
 * 处理添加账户
 * @param {Object} account 账户数据
 */
const handleAddAccount = async (account: any) => {
  accountSubmitting.value = true;
  try {
    // 将表单字段转换为API需要的格式
    const apiAccount = {
      accountType: account.accountType, // 账户类型 1-微信, 2-支付宝, 3-银行卡
      accountNumber: account.accountNumber,
      accountName: account.accountName,
      bankName: account.accountType === 3 ? account.bankName : undefined, // 仅银行卡需要银行名称
      isDefault: account.isDefault
    };
    
    await addRunnerAccount(apiAccount);
    // 响应拦截器已经处理了code和message，请求成功会直接进入这里
    ElMessage.success('账户添加成功');
    await getAccountList(); // 重新获取账户列表
  } catch (error) {
    console.error('账户添加失败:', error);
    ElMessage.error('账户添加失败');
  } finally {
    accountSubmitting.value = false;
  }
};

/**
 * 处理编辑账户
 * @param {Object} account 账户数据
 */
const handleEditAccount = async (account: any) => {
  accountSubmitting.value = true;
  try {
    // 将表单字段转换为API需要的格式
    const apiAccount = {
      id: account.id,
      accountType: account.accountType, // 账户类型 1-微信, 2-支付宝, 3-银行卡
      accountNumber: account.accountNumber,
      accountName: account.accountName,
      bankName: account.accountType === 3 ? account.bankName : undefined, // 仅银行卡需要银行名称
      isDefault: account.isDefault
    };
    
    await updateRunnerAccount(apiAccount);
    // 响应拦截器已经处理了code和message，请求成功会直接进入这里
    ElMessage.success('账户更新成功');
    await getAccountList(); // 重新获取账户列表
  } catch (error) {
    console.error('账户更新失败:', error);
    ElMessage.error('账户更新失败');
  } finally {
    accountSubmitting.value = false;
  }
};

/**
 * 处理删除账户
 * @param {number|string} id 账户ID
 */
const handleDeleteAccount = async (id: number | string) => {
  try {
    await deleteRunnerAccount(id);
    // 响应拦截器已经处理了code和message，请求成功会直接进入这里
    ElMessage.success('账户删除成功');
    await getAccountList(); // 重新获取账户列表
  } catch (error) {
    console.error('账户删除失败:', error);
    ElMessage.error('账户删除失败');
  }
};

/**
 * 处理设置默认账户
 * @param {number|string} id 账户ID
 */
const handleSetDefault = async (id: number | string) => {
  try {
    await setDefaultAccount(id);
    // 响应拦截器已经处理了code和message，请求成功会直接进入这里
    ElMessage.success('默认账户设置成功');
    await getAccountList(); // 重新获取账户列表
  } catch (error) {
    console.error('默认账户设置失败:', error);
    ElMessage.error('默认账户设置失败');
  }
};

// 页面加载时初始化
onMounted(() => {
  getProfile();
  getServiceSettings();
  getNotificationSettings();
  getAccountList();
});
</script>

<style scoped>
.runner-settings-page {
  padding: 20px;
}

.settings-menu {
  margin-bottom: 20px;
}

.settings-content {
  margin-bottom: 20px;
}
</style>
