<!--
  @component: Runner<PERSON><PERSON>me
  @author: AI Assistant
  @date: 2025-06-08
  @version: 1.0.0
  @description: 跑腿员收入管理页面，包括收入统计、收入明细、提现申请等功能
-->
<template>
  <div class="runner-income-page">
    <!-- 收入概览 -->
    <IncomeOverview :stats="incomeStats" />

    <!-- 账户余额和提现 -->
    <AccountBalance 
      :balanceInfo="balanceInfo" 
      @withdraw="showWithdrawDialog" 
    />

    <!-- 收入明细 -->
    <IncomeDetails
      :loading="incomeLoading"
      :data="incomeList"
      :total="incomeTotal"
      @search="handleIncomeSearch"
      @reset="handleIncomeReset"
      @page-change="handleIncomePageChange"
    />

    <!-- 提现申请对话框 -->
    <WithdrawDialog
      v-model:visible="withdrawDialogVisible"
      :availableBalance="balanceInfo.available"
      :submitting="withdrawSubmitting"
      @submit="submitWithdraw"
      @cancel="withdrawDialogVisible = false"
    />

    <!-- 提现记录 -->
    <WithdrawRecords
      :loading="withdrawLoading"
      :data="withdrawList"
      :total="withdrawTotal"
      @page-change="handleWithdrawPageChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

// 导入子组件
import IncomeOverview from './components/IncomeOverview.vue';
import AccountBalance from './components/AccountBalance.vue';
import IncomeDetails from './components/IncomeDetails.vue';
import WithdrawDialog from './components/WithdrawDialog.vue';
import WithdrawRecords from './components/WithdrawRecords.vue';

// 导入API和类型
import {
  getRunnerIncomeStats,
  getRunnerBalanceInfo,
  getRunnerIncomeList,
  getRunnerWithdrawList,
  submitRunnerWithdraw
} from '@/modules/user/api/runnerIncome';
import type { 
  IncomeStats, 
  BalanceInfo, 
  IncomeRecord, 
  WithdrawRecord,
  WithdrawForm,
  IncomeSearchForm
} from './components/types';

// 收入统计数据
const incomeStats = reactive<IncomeStats>({
  today: 0,
  week: 0,
  month: 0,
  total: 0
});

// 账户余额信息
const balanceInfo = reactive<BalanceInfo>({
  available: 0,
  frozen: 0,
  withdrawing: 0
});

// 收入明细相关数据
const incomeLoading = ref(false);
const incomeList = ref<IncomeRecord[]>([]);
const incomeTotal = ref(0);
const incomeParams = reactive({
  current: 1,
  pageSize: 10,
  type: '',
  status: '',
  startTime: '',
  endTime: '',
  orderNo: ''
});

// 提现对话框相关数据
const withdrawDialogVisible = ref(false);
const withdrawSubmitting = ref(false);

// 提现记录相关数据
const withdrawLoading = ref(false);
const withdrawList = ref<WithdrawRecord[]>([]);
const withdrawTotal = ref(0);
const withdrawParams = reactive({
  current: 1,
  pageSize: 10
});

/**
 * 获取收入统计数据
 */
const getIncomeStatsData = async () => {
  try {
    const res:any = await getRunnerIncomeStats();
    incomeStats.today = res.today;
    incomeStats.week = res.week;
    incomeStats.month = res.month;
    incomeStats.total = res.total;
  } catch (error) {
    console.error('获取收入统计失败:', error);
    ElMessage.error('获取收入统计失败，请稍后重试');
  }
};

/**
 * 获取账户余额信息
 */
const getBalanceInfoData = async () => {
  try {
    const res:any = await getRunnerBalanceInfo();
    balanceInfo.available = res.available;
    balanceInfo.frozen = res.frozen;
    balanceInfo.withdrawing = res.withdrawing;
  } catch (error) {
    console.error('获取账户余额信息失败:', error);
    ElMessage.error('获取账户余额信息失败，请稍后重试');
  }
};

/**
 * 加载收入明细列表
 */
const loadIncomeList = async () => {
  incomeLoading.value = true;
  try {
    const params = {
      ...incomeParams
    };
    
    // 处理日期范围
    if (params.startTime && params.endTime) {
      params.startTime = params.startTime;
      params.endTime = params.endTime;
    }
    
    const res:any = await getRunnerIncomeList(params);
    console.log('loadIncomeList',res);
    
    // 处理后端返回的数据结构
    if (res.list && Array.isArray(res.list)) {
      // 将后端返回的list字段转换为组件期望的records字段
      const recordsList = res.list.map((item: any) => {
        return {
          id: item.id,
          // 类型字段: 0:配送费, 1:小费, 2:奖励, 3:退款
          type: item.type?.toString() || '',
          orderNo: item.order_no || '',
          orderTime: item.order_time || item.create_time,
          amount: Number(item.amount),
          // 状态字段: 0:未结算, 1:已结算, 2:已退款
          status: item.status?.toString() || '',
          createdAt: item.create_time,
          remark: item.description || ''
        };
      });
      
      incomeList.value = recordsList;
      incomeTotal.value = res.total;
    } else {
      // 如果响应格式不正确，设置空数组
      incomeList.value = [];
      incomeTotal.value = 0;
      console.error('收入明细数据格式不正确:', res);
    }
  } catch (error) {
    console.error('加载收入明细失败:', error);
    ElMessage.error('加载收入明细失败，请稍后重试');
  } finally {
    incomeLoading.value = false;
  }
};

/**
 * 加载提现记录列表
 */
const loadWithdrawList = async () => {
  withdrawLoading.value = true;
  try {
    const res:any = await getRunnerWithdrawList(withdrawParams);
    withdrawList.value = res.records;
    withdrawTotal.value = res.total;
  } catch (error) {
    console.error('加载提现记录失败:', error);
    ElMessage.error('加载提现记录失败，请稍后重试');
  } finally {
    withdrawLoading.value = false;
  }
};

/**
 * 处理收入明细搜索
 * @param form 搜索表单数据
 */
const handleIncomeSearch = (form: IncomeSearchForm) => {
  incomeParams.type = form.type || '';
  incomeParams.status = form.status || '';
  incomeParams.orderNo = form.orderNo || '';
  
  // 处理时间范围
  if (form.dateRange && form.dateRange.length === 2) {
    const [startTime, endTime] = form.dateRange;
    incomeParams.startTime = startTime;
    incomeParams.endTime = endTime;
  } else {
    incomeParams.startTime = '';
    incomeParams.endTime = '';
  }
  
  incomeParams.current = 1; // 重置到第一页
  loadIncomeList();
};

/**
 * 处理收入明细重置
 */
const handleIncomeReset = () => {
  incomeParams.type = '';
  incomeParams.status = '';
  incomeParams.orderNo = '';
  incomeParams.startTime = '';
  incomeParams.endTime = '';
  incomeParams.current = 1;
  loadIncomeList();
};

/**
 * 处理收入明细页码变化
 */
const handleIncomePageChange = (page: number, pageSize: number) => {
  incomeParams.current = page;
  incomeParams.pageSize = pageSize;
  loadIncomeList();
};

/**
 * 显示提现对话框
 */
const showWithdrawDialog = () => {
  // 检查可提现余额是否足够
  if (balanceInfo.available <= 0) {
    ElMessage.warning('当前无可提现余额');
    return;
  }
  
  withdrawDialogVisible.value = true;
};

/**
 * 提交提现申请
 * @param form 提现表单数据
 */
const submitWithdraw = async (form: WithdrawForm) => {
  withdrawSubmitting.value = true;
  
  try {
    // 适配后端接口参数格式
    const data = {
      amount: form.amount,
      method: form.method,
      account: form.account,
      account_name: form.accountName
    };
    
    await submitRunnerWithdraw(data);
    
    ElMessage.success('提现申请提交成功');
    withdrawDialogVisible.value = false;
    
    // 刷新账户余额和提现记录
    getBalanceInfoData();
    loadWithdrawList();
  } catch (error) {
    console.error('提交提现申请失败:', error);
    ElMessage.error('提交提现申请失败，请稍后重试');
  } finally {
    withdrawSubmitting.value = false;
  }
};

/**
 * 处理提现记录页码变化
 */
const handleWithdrawPageChange = (page: number, pageSize: number) => {
  withdrawParams.current = page;
  withdrawParams.pageSize = pageSize;
  loadWithdrawList();
};

// 页面加载时获取数据
onMounted(() => {
  getIncomeStatsData();
  getBalanceInfoData();
  loadIncomeList();
  loadWithdrawList();
});
</script>

<style scoped lang="scss">
.runner-income-page {
  padding: 20px;
}
</style>
