<!--
  @component: RunnerWorkStatus
  @author: AI Assistant
  @date: 2025-01-27
  @version: 1.0.0
  @description: 跑腿员工作状态管理页面，包括工作状态设置、位置更新、在线状态管理等功能
-->
<template>
  <div class="runner-work-status-page">
    <!-- 状态概览卡片 -->
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card class="status-overview-card">
          <div class="status-item">
            <div class="status-label">当前状态</div>
            <div class="status-value">
              <el-tag :type="getWorkStatusType(currentStatus.workStatus)" size="large">
                {{ getWorkStatusText(currentStatus.workStatus) }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="status-overview-card">
          <div class="status-item">
            <div class="status-label">在线状态</div>
            <div class="status-value">
              <el-tag :type="currentStatus.isOnline ? 'success' : 'info'" size="large">
                {{ currentStatus.isOnline ? '在线' : '离线' }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="status-overview-card">
          <div class="status-item">
            <div class="status-label">今日收入</div>
            <div class="status-value income">
              ¥{{ todayIncome.toFixed(2) }}
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 工作状态设置 -->
    <el-card class="work-status-card">
      <template #header>
        <h3>工作状态设置</h3>
      </template>
      
      <div class="status-controls">
        <div class="control-section">
          <h4>工作状态</h4>
          <el-radio-group v-model="workStatusForm.status" @change="updateWorkStatus">
            <el-radio :label="0" class="status-radio">
              <div class="radio-content">
                <div class="radio-title">休息中</div>
                <div class="radio-desc">不接收新订单</div>
              </div>
            </el-radio>
            <el-radio :label="1" class="status-radio">
              <div class="radio-content">
                <div class="radio-title">接单中</div>
                <div class="radio-desc">可接收新订单</div>
              </div>
            </el-radio>
            <el-radio :label="2" class="status-radio">
              <div class="radio-content">
                <div class="radio-title">配送中</div>
                <div class="radio-desc">正在配送订单</div>
              </div>
            </el-radio>
          </el-radio-group>
        </div>

        <el-divider />

        <div class="control-section">
          <h4>在线状态</h4>
          <el-switch
            v-model="onlineStatusForm.isOnline"
            @change="updateOnlineStatus"
            active-text="在线 - 用户可以看到您"
            inactive-text="离线 - 用户无法看到您"
            size="large"
          />
        </div>
      </div>
    </el-card>

    <!-- 位置信息 -->
    <el-card class="location-card">
      <template #header>
        <div class="location-header">
          <h3>位置信息</h3>
          <el-button @click="updateLocation" :loading="locationUpdating">
            <el-icon><Refresh /></el-icon>
            更新位置
          </el-button>
        </div>
      </template>
      
      <div class="location-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="当前地址" span="2">
            {{ currentLocation.address || '未获取到位置信息' }}
          </el-descriptions-item>
          <el-descriptions-item label="坐标">
            {{ currentLocation.latitude && currentLocation.longitude 
              ? `${currentLocation.latitude}, ${currentLocation.longitude}` 
              : '未获取' }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ currentLocation.updateTime ? formatTime(currentLocation.updateTime) : '未更新' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="location-settings">
          <el-checkbox v-model="locationSettings.autoUpdate" @change="toggleAutoLocation">
            启用自动定位
          </el-checkbox>
          <el-select v-model="locationSettings.accuracy" @change="updateLocationSettings">
            <el-option label="高精度 (GPS + 网络)" value="high" />
            <el-option label="中等精度 (网络)" value="medium" />
            <el-option label="低精度 (基站)" value="low" />
          </el-select>
        </div>
      </div>
    </el-card>

    <!-- 今日统计 -->
    <el-card class="statistics-card">
      <template #header>
        <h3>今日统计</h3>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ todayStats.orderCount }}</div>
            <div class="stat-label">完成订单</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ todayStats.workHours.toFixed(1) }}h</div>
            <div class="stat-label">工作时长</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ todayStats.distance.toFixed(1) }}km</div>
            <div class="stat-label">配送距离</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ todayStats.rating.toFixed(1) }}</div>
            <div class="stat-label">平均评分</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import {
  getRunnerStatus,
  updateRunnerWorkStatus,
  updateRunnerOnlineStatus,
  updateRunnerLocation,
  getRunnerTodayStats
} from '@/modules/user/api/runnerWorkStatus'
import { formatTime } from '@/utils/format'

// 当前状态
const currentStatus = reactive({
  workStatus: 0, // 0-休息中 1-接单中 2-配送中
  isOnline: false,
  lastUpdateTime: ''
})

// 工作状态表单
const workStatusForm = reactive({
  status: 0,
  reason: ''
})

// 在线状态表单
const onlineStatusForm = reactive({
  isOnline: false
})

// 当前位置信息
const currentLocation = reactive({
  latitude: 0,
  longitude: 0,
  address: '',
  updateTime: ''
})

// 位置设置
const locationSettings = reactive({
  autoUpdate: true,
  accuracy: 'high'
})

// 位置更新状态
const locationUpdating = ref(false)

// 今日收入
const todayIncome = ref(0)

// 今日统计
const todayStats = reactive({
  orderCount: 0,
  workHours: 0,
  distance: 0,
  rating: 0
})

// 自动定位定时器
let locationTimer: any | null = null

// 获取工作状态类型
const getWorkStatusType = (status: number) => {
  switch (status) {
    case 0: return 'info'    // 休息中
    case 1: return 'success' // 接单中
    case 2: return 'warning' // 配送中
    default: return 'info'
  }
}

// 获取工作状态文本
const getWorkStatusText = (status: number) => {
  switch (status) {
    case 0: return '休息中'
    case 1: return '接单中'
    case 2: return '配送中'
    default: return '未知'
  }
}

// 获取当前状态
// 获取当前状态
const getCurrentStatus = async () => {
  try {
    const response = await getRunnerStatus()
    console.log('runner status',response)
    // API服务函数已处理 .data，这里直接使用返回的数据
    currentStatus.workStatus = response.working_status
    currentStatus.isOnline = response.is_online
    currentStatus.lastUpdateTime = new Date().toISOString() // 使用当前时间作为更新时间
    
    // 同步表单数据
    workStatusForm.status = response.working_status
    onlineStatusForm.isOnline = response.is_online
  } catch (error: any) {
    ElMessage.error(error.message || '获取状态失败')
  }
}

// 更新工作状态
// 更新工作状态
const updateWorkStatus = async () => {
  try {
    const res = await updateRunnerWorkStatus({
      working_status: workStatusForm.status,
      reason: workStatusForm.reason
    })
    console.log('', res)
    currentStatus.workStatus = workStatusForm.status
    ElMessage.success('工作状态更新成功')
  } catch (error: any) {
    // 恢复原状态
    workStatusForm.status = currentStatus.workStatus
    ElMessage.error(error.message || '状态更新失败')
  }
}

// 更新在线状态
// 更新在线状态
const updateOnlineStatus = async () => {
  try {
    await updateRunnerOnlineStatus({
      is_online: onlineStatusForm.isOnline
    })
    
    currentStatus.isOnline = onlineStatusForm.isOnline
    ElMessage.success('在线状态更新成功')
  } catch (error: any) {
    // 恢复原状态
    onlineStatusForm.isOnline = currentStatus.isOnline
    ElMessage.error(error.message || '状态更新失败')
  }
}

// 获取当前位置
const getCurrentLocation = (): Promise<GeolocationPosition> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('浏览器不支持地理定位'))
      return
    }
    
    const options = {
      enableHighAccuracy: locationSettings.accuracy === 'high',
      timeout: 10000,
      maximumAge: 60000
    }
    
    navigator.geolocation.getCurrentPosition(resolve, reject, options)
  })
}

// 更新位置信息
const updateLocation = async () => {
  try {
    locationUpdating.value = true
    
    const position = await getCurrentLocation()
    const { latitude, longitude } = position.coords
    
    // 调用API更新位置
    const response = await updateRunnerLocation({
      latitude,
      longitude,
      // address: '' // address 现在是可选的，如果后端能自动获取，可以不传
    })
    
    currentLocation.latitude = latitude
    currentLocation.longitude = longitude
    currentLocation.address = response.address // 假设API服务返回的数据包含address
    currentLocation.updateTime = new Date().toISOString()
    
    ElMessage.success('位置更新成功')
  } catch (error: any) {
    ElMessage.error(error.message || '位置更新失败')
  } finally {
    locationUpdating.value = false
  }
}

// 切换自动定位
const toggleAutoLocation = () => {
  if (locationSettings.autoUpdate) {
    startAutoLocation()
  } else {
    stopAutoLocation()
  }
}

// 开始自动定位
const startAutoLocation = () => {
  if (locationTimer) {
    clearInterval(locationTimer)
  }
  
  // 每5分钟更新一次位置
  locationTimer = setInterval(() => {
    updateLocation()
  }, 5 * 60 * 1000)
}

// 停止自动定位
const stopAutoLocation = () => {
  if (locationTimer) {
    clearInterval(locationTimer)
    locationTimer = null
  }
}

// 更新位置设置
const updateLocationSettings = () => {
  // 如果开启了自动定位，重新启动定时器
  if (locationSettings.autoUpdate) {
    startAutoLocation()
  }
}

// 获取今日统计
// 获取今日统计
const getTodayStats = async () => {
  try {
    const response = await getRunnerTodayStats()
    console.log('getTodayStats', response)
    // API服务函数已处理 .data，这里直接使用返回的数据
    todayIncome.value = response.total_income
    todayStats.orderCount = response.completed_order_count
    todayStats.workHours = response.working_hours
    todayStats.distance = response.delivery_distance
    todayStats.rating = response.average_rating
  } catch (error: any) {
    // 这里保留 console.error，因为通常统计数据获取失败不直接弹窗提示用户，但需要记录错误
    console.error('获取今日统计失败:', error)
    ElMessage.error(error.message || '获取今日统计失败') // 可选：如果需要用户提示
  }
}

// 页面加载时初始化
onMounted(async () => {
  await getCurrentStatus()
  await updateLocation()
  await getTodayStats()
  
  // 如果开启了自动定位，启动定时器
  if (locationSettings.autoUpdate) {
    startAutoLocation()
  }
})

// 页面卸载时清理定时器
onUnmounted(() => {
  stopAutoLocation()
})
</script>

<style scoped>
.runner-work-status-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.status-overview-card {
  margin-bottom: 20px;
  text-align: center;
}

.status-item {
  padding: 20px;
}

.status-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.status-value {
  font-size: 18px;
  font-weight: 600;
}

.status-value.income {
  color: #67c23a;
  font-size: 24px;
}

.work-status-card,
.location-card,
.statistics-card {
  margin-bottom: 20px;
}

.work-status-card h3,
.location-card h3,
.statistics-card h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.location-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-controls {
  padding: 20px 0;
}

.control-section {
  margin-bottom: 20px;
}

.control-section h4 {
  margin: 0 0 16px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 600;
}

.status-radio {
  display: block;
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  transition: all 0.3s;
}

.status-radio:hover {
  border-color: #409eff;
}

.status-radio.is-checked {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.radio-content {
  margin-left: 24px;
}

.radio-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.radio-desc {
  font-size: 12px;
  color: #909399;
}

.location-info {
  padding: 20px 0;
}

.location-settings {
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

:deep(.el-radio-group) {
  width: 100%;
}

:deep(.el-radio) {
  width: 100%;
  margin-right: 0;
}

:deep(.el-switch) {
  --el-switch-on-color: #67c23a;
}
</style>