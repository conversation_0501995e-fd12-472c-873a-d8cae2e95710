/**
 * @file types.ts
 * @description 跑腿员收入管理相关类型定义
 */

// 从API导入基础类型
import type { 
  IncomeStats as ApiIncomeStats, 
  BalanceInfo as ApiBalanceInfo,
  IncomeRecord as ApiIncomeRecord,
  //WithdrawRecord as ApiWithdrawRecord
} from '@/modules/user/api/runnerIncome';

// 导出API类型以便组件使用
export type IncomeStats = ApiIncomeStats;
export type BalanceInfo = ApiBalanceInfo;

// 使用API类型，但为组件适配一些字段名
export interface IncomeRecord extends Omit<ApiIncomeRecord, 'createTime'> {
  createdAt: string | Date; // 重命名字段以适配组件
}

// 收入明细筛选表单接口
export interface IncomeSearchForm {
  type: string;
  status: string;
  dateRange: [string, string] | [];
  orderNo: string;
}

// 提现表单接口
export interface WithdrawForm {
  amount: number;
  method: string;
  account: string;
  accountName: string;
}

// 适配API的提现记录接口
export interface WithdrawRecord {
  id: string | number;
  amount: number;
  fee: number;
  method: string;
  account: string;
  accountName?: string;
  status: string;
  createdAt: string | Date; // 映射自 createTime
  processedAt: string | Date | null; // 映射自 completeTime
  remark?: string;
}

// 分页参数接口
export interface PaginationParams {
  current: number;
  pageSize: number;
}

// 分页响应接口
export interface PaginationResponse<T> {
  records: T[]; // 与API保持一致，使用records
  total: number;
}
