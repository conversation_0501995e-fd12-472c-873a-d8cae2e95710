<!--
  @component: IncomeOverview
  @author: AI Assistant
  @date: 2025-06-08
  @description: 跑腿员收入概览组件，展示今日、本周、本月和累计收入
-->
<template>
  <el-row :gutter="20" class="income-overview">
    <el-col :span="6">
      <el-card class="income-card">
        <div class="income-item">
          <div class="income-label">今日收入</div>
          <div class="income-value today">¥{{ stats.today.toFixed(2) }}</div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="income-card">
        <div class="income-item">
          <div class="income-label">本周收入</div>
          <div class="income-value week">¥{{ stats.week.toFixed(2) }}</div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="income-card">
        <div class="income-item">
          <div class="income-label">本月收入</div>
          <div class="income-value month">¥{{ stats.month.toFixed(2) }}</div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="income-card">
        <div class="income-item">
          <div class="income-label">累计收入</div>
          <div class="income-value total">¥{{ stats.total.toFixed(2) }}</div>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
// import { defineProps } from 'vue'
import type { IncomeStats } from './types'

// 组件接收收入统计数据作为属性
defineProps<{
  stats: IncomeStats
}>()
</script>

<style scoped lang="scss">
.income-overview {
  margin-bottom: 20px;
}

.income-card {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.income-item {
  text-align: center;

  .income-label {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
  }

  .income-value {
    font-size: 24px;
    font-weight: bold;
    
    &.today {
      color: #409EFF;
    }

    &.week {
      color: #67C23A;
    }

    &.month {
      color: #E6A23C;
    }

    &.total {
      color: #F56C6C;
    }
  }
}
</style>
