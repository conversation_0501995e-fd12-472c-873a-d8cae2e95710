<!--
  @component: ServiceSettings
  @author: AI Assistant
  @date: 2025-06-08
  @version: 1.0.0
  @description: 跑腿员服务设置组件
-->
<template>
  <el-card class="settings-content">
    <template #header>
      <h3>服务设置</h3>
    </template>
    
    <el-form
      ref="serviceFormRef"
      :model="serviceForm"
      :rules="serviceRules"
      label-width="120px"
    >
      <el-form-item label="服务区域" prop="serviceAreas">
        <el-select
          v-model="serviceForm.serviceAreas"
          multiple
          placeholder="请选择服务区域"
          style="width: 100%"
        >
          <el-option
            v-for="area in areaOptions"
            :key="area.value"
            :label="area.label"
            :value="area.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="服务时间">
        <div class="service-time-setting">
          <div v-for="(time, index) in serviceForm.serviceTimes" :key="index" class="time-item">
            <el-checkbox v-model="time.enabled" :label="time.day" />
            <el-time-picker
              v-model="time.startTime"
              :disabled="!time.enabled"
              placeholder="开始时间"
              format="HH:mm"
              value-format="HH:mm"
            />
            <span class="time-separator">至</span>
            <el-time-picker
              v-model="time.endTime"
              :disabled="!time.enabled"
              placeholder="结束时间"
              format="HH:mm"
              value-format="HH:mm"
            />
          </div>
        </div>
      </el-form-item>
      
      <el-form-item label="配送工具" prop="deliveryTool">
        <el-radio-group v-model="serviceForm.deliveryTool">
          <el-radio label="bicycle">自行车</el-radio>
          <el-radio label="electric">电动车</el-radio>
          <el-radio label="motorcycle">摩托车</el-radio>
          <el-radio label="car">汽车</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="最大接单距离" prop="maxDistance">
        <el-input-number
          v-model="serviceForm.maxDistance"
          :min="1"
          :max="50"
          :step="1"
          controls-position="right"
        />
        <span class="unit">公里</span>
      </el-form-item>
      
      <el-form-item label="同时接单数量" prop="maxOrders">
        <el-input-number
          v-model="serviceForm.maxOrders"
          :min="1"
          :max="10"
          :step="1"
          controls-position="right"
        />
        <span class="unit">单</span>
      </el-form-item>
      
      <el-form-item label="自动接单">
        <el-switch
          v-model="serviceForm.autoAccept"
          active-text="开启"
          inactive-text="关闭"
        />
        <div class="setting-tips">
          开启后系统将自动为您分配订单
        </div>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          保存设置
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import type { FormInstance } from 'element-plus';

// 区域选项类型
type AreaOption = {
  value: string | number;
  label: string;
  [key: string]: any;
};

/**
 * 组件属性定义
 */
const props = defineProps({
  /**
   * 服务设置表单数据
   */
  modelValue: {
    type: Object,
    required: true
  },
  /**
   * 是否正在提交
   */
  submitting: {
    type: Boolean,
    default: false
  },
  /**
   * 区域选项
   */
  areaOptions: {
    type: Array as () => AreaOption[],
    required: true,
    default: () => [] as AreaOption[]
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits(['update:modelValue', 'submit']);

// 表单引用
const serviceFormRef = ref<FormInstance>();

// 表单数据（从props复制以避免直接修改props）
const serviceForm = reactive({...props.modelValue});

// 表单校验规则
const serviceRules = {
  serviceAreas: [
    { required: true, message: '请选择服务区域', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个服务区域', trigger: 'change' }
  ],
  deliveryTool: [
    { required: true, message: '请选择配送工具', trigger: 'change' }
  ],
  maxDistance: [
    { required: true, message: '请设置最大接单距离', trigger: 'change' }
  ],
  maxOrders: [
    { required: true, message: '请设置同时接单数量', trigger: 'change' }
  ]
};

/**
 * 处理表单提交
 */
const handleSubmit = async () => {
  if (!serviceFormRef.value) return;
  
  await serviceFormRef.value.validate(valid => {
    if (valid) {
      // 发送表单数据到父组件
      emit('update:modelValue', {...serviceForm});
      emit('submit');
    }
  });
};
</script>

<style scoped>
.service-time-setting {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.time-separator {
  margin: 0 8px;
}

.unit {
  margin-left: 8px;
  color: #606266;
}

.setting-tips {
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
}
</style>
