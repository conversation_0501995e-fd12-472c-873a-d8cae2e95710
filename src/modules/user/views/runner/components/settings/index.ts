/**
 * @file: 跑腿员设置组件导出入口文件
 * @author: AI Assistant
 * @date: 2025-06-08
 * @version: 1.0.0
 */

import type { DefineComponent } from 'vue';

// 定义组件类型
interface SettingsComponent extends DefineComponent<{}, {}, any> {}

// 导入组件
import ProfileSettings from './ProfileSettings.vue';
import ServiceSettings from './ServiceSettings.vue';
import NotificationSettings from './NotificationSettings.vue';
import SecuritySettings from './SecuritySettings.vue';
import AccountSettings from './AccountSettings.vue';

// 导出带类型的组件
export {
  ProfileSettings as ProfileSettings,
  ServiceSettings as ServiceSettings,
  NotificationSettings as NotificationSettings,
  SecuritySettings as SecuritySettings,
  AccountSettings as AccountSettings
};

// 导出类型
export type {
  SettingsComponent
};
