<!--
  @component: AccountSettings
  @author: AI Assistant
  @date: 2025-06-08
  @version: 1.0.0
  @description: 跑腿员收款账户设置组件
-->
<template>
  <el-card class="settings-content">
    <template #header>
      <h3>收款账户</h3>
    </template>
    
    <div class="account-list">
      <el-empty v-if="accounts.length === 0" description="暂无收款账户">
        <el-button type="primary" @click="openAccountDialog('add')">添加账户</el-button>
      </el-empty>
      
      <template v-else>
        <div class="account-item" v-for="(account, index) in accounts" :key="account.id">
          <div class="account-info">
            <div class="account-type">
              <el-icon class="account-icon">
                <component :is="getAccountIcon(account.account_type)"></component>
              </el-icon>
              {{ getAccountTypeName(account.account_type) }}
              <el-tag v-if="account.is_default" type="success" size="small" class="default-tag">默认</el-tag>
            </div>
            <div class="account-number">{{ maskAccountNumber(account.account_number) }}</div>
            <div class="account-name">{{ account.account_name }}</div>
          </div>
          
          <div class="account-actions">
            <el-button 
              type="primary" 
              plain 
              size="small" 
              v-if="!account.is_default"
              @click="handleSetDefault(account.id)"
            >
              设为默认
            </el-button>
            <el-button 
              type="primary" 
              link 
              size="small"
              @click="openAccountDialog('edit', index)"
            >
              编辑
            </el-button>
            <el-button 
              type="danger" 
              link 
              size="small" 
              v-if="!account.is_default"
              @click="handleDelete(account.id)"
            >
              删除
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="add-account" v-if="accounts.length > 0">
        <el-button type="primary" plain @click="openAccountDialog('add')">添加账户</el-button>
      </div>
    </div>
    
    <!-- 账户添加/编辑对话框 -->
    <el-dialog
      v-model="accountDialogVisible"
      :title="dialogMode === 'add' ? '添加收款账户' : '编辑收款账户'"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="accountFormRef"
        :model="accountForm"
        :rules="accountRules"
        label-width="100px"
      >
        <el-form-item label="账户类型" prop="accountType">
          <el-select v-model="accountForm.accountType" placeholder="请选择账户类型" style="width: 100%">
            <el-option label="微信" :value="1" />
            <el-option label="支付宝" :value="2" />
            <el-option label="银行卡" :value="3" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="开户行" prop="bankName" v-if="accountForm.accountType === 3">
          <el-input v-model="accountForm.bankName" placeholder="请输入开户银行名称" />
        </el-form-item>
        
        <el-form-item label="账号" prop="accountNumber">
          <el-input v-model="accountForm.accountNumber" placeholder="请输入账号" />
        </el-form-item>
        
        <el-form-item label="姓名" prop="accountName">
          <el-input v-model="accountForm.accountName" placeholder="请输入账户姓名" />
        </el-form-item>
        
        
        
        <el-form-item>
          <el-checkbox v-model="accountForm.isDefault">设为默认收款账户</el-checkbox>
          <!-- isDefault在表单中使用，发送API请求时会转换为is_default -->
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="accountDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="accountSubmitting" 
          @click="handleSubmitAccount"
        >
          确认
        </el-button>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { 
  CreditCard, 
  Wallet, 
  Money 
} from '@element-plus/icons-vue';
import { ElMessageBox } from 'element-plus';
import type { FormInstance } from 'element-plus';

/**
 * 账户类型定义
 */
interface Account {
  id: number | string;
  account_type: number; // 1-微信, 2-支付宝, 3-银行卡
  account_number: string;
  account_name: string;
  is_default: boolean;
  bank_name?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * 组件属性定义
 */
const props = defineProps({
  /**
   * 账户列表
   */
  accounts: {
    type: Array as () => Account[],
    required: true
  },
  /**
   * 是否正在提交
   */
  accountSubmitting: {
    type: Boolean,
    default: false
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits(['add-account', 'edit-account', 'delete-account', 'set-default']);

// 对话框可见性和模式
const accountDialogVisible = ref(false);
const dialogMode = ref<'add' | 'edit'>('add');

// 当前编辑的账户索引
const currentEditIndex = ref(-1);

// 表单引用
const accountFormRef = ref<FormInstance>();

// 账户表单数据
const accountForm = reactive({
  id: '',
  accountType: 3, // 默认银行卡 3-银行卡, 1-微信, 2-支付宝
  bankName: '',
  accountNumber: '',
  accountName: '',
  isDefault: false
});

// 账户表单规则
const accountRules = {
  accountType: [
    { required: true, message: '请选择账户类型', trigger: 'change' }
  ],
  bankName: [
    { required: true, message: '请输入开户银行名称', trigger: 'blur' }
  ],
  accountNumber: [
    { required: true, message: '请输入账号', trigger: 'blur' }
  ],
  accountName: [
    { required: true, message: '请输入账户姓名', trigger: 'blur' }
  ]
};

/**
 * 打开账户对话框
 * @param {string} mode 对话框模式 'add' 或 'edit'
 * @param {number} index 编辑时的账户索引
 */
const openAccountDialog = (mode: 'add' | 'edit', index?: number) => {
  dialogMode.value = mode;
  
  // 重置表单
  if (accountFormRef.value) {
    accountFormRef.value.resetFields();
  }
  
  if (mode === 'add') {
    // 添加模式，初始化表单
    accountForm.id = '';
    accountForm.accountType = 3; // 默认银行卡
    accountForm.bankName = '';
    accountForm.accountNumber = '';
    accountForm.accountName = '';
    accountForm.isDefault = false;
    currentEditIndex.value = -1;
  } else if (mode === 'edit' && index !== undefined) {
    // 编辑模式，填充表单
    const account = props.accounts[index];
    accountForm.id = account.id.toString();
    accountForm.accountType = account.account_type;
    accountForm.bankName = account.bank_name || '';
    accountForm.accountNumber = account.account_number;
    accountForm.accountName = account.account_name;
    accountForm.isDefault = account.is_default;
    currentEditIndex.value = index;
  }
  
  accountDialogVisible.value = true;
};

/**
 * 提交账户表单
 */
const handleSubmitAccount = async () => {
  if (!accountFormRef.value) return;
  
  await accountFormRef.value.validate(valid => {
    if (valid) {
      if (dialogMode.value === 'add') {
        emit('add-account', {...accountForm});
      } else {
        emit('edit-account', {...accountForm});
      }
    }
  });
};

/**
 * 设为默认账户
 * @param {number|string} id 账户ID
 */
const handleSetDefault = (id: number | string) => {
  emit('set-default', id);
};

/**
 * 删除账户
 * @param {number|string} id 账户ID
 */
const handleDelete = (id: number | string) => {
  ElMessageBox.confirm('确定要删除此收款账户吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    emit('delete-account', id);
  }).catch(() => {
    // 用户取消删除
  });
};

/**
 * 获取账户图标
 * @param {number} accountType 账户类型
 * @returns {string} 图标组件名称
 */
const getAccountIcon = (accountType: number) => {
  switch (accountType) {
    case 3: // 银行卡
      return CreditCard;
    case 2: // 支付宝
      return Wallet;
    case 1: // 微信
      return Money;
    default:
      return CreditCard;
  }
};

/**
 * 获取账户类型名称
 * @param {number} accountType 账户类型
 * @returns {string} 账户类型名称
 */
const getAccountTypeName = (accountType: number) => {
  switch (accountType) {
    case 1:
      return '微信';
    case 2:
      return '支付宝';
    case 3:
      return '银行卡';
    default:
      return '未知类型';
  }
};

/**
 * 账号脱敏
 * @param {string} number 账号
 * @returns {string} 脱敏后的账号
 */
const maskAccountNumber = (number: string) => {
  if (!number) return '';
  
  if (number.length <= 8) {
    return '****' + number.slice(-4);
  }
  
  return number.slice(0, 4) + ' **** **** ' + number.slice(-4);
};
</script>

<style scoped>
.account-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.account-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.account-type {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.account-icon {
  margin-right: 8px;
  font-size: 20px;
}

.default-tag {
  margin-left: 8px;
}

.account-number {
  color: #606266;
  font-size: 14px;
}

.account-name {
  color: #909399;
  font-size: 14px;
}

.account-actions {
  display: flex;
  gap: 8px;
}

.add-account {
  display: flex;
  justify-content: center;
  padding: 16px;
}
</style>
