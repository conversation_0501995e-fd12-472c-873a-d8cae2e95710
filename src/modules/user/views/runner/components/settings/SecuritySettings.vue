<!--
  @component: SecuritySettings
  @author: AI Assistant
  @date: 2025-06-08
  @version: 1.0.0
  @description: 跑腿员安全设置组件
-->
<template>
  <el-card class="settings-content">
    <template #header>
      <h3>安全设置</h3>
    </template>
    
    <div class="security-items">
      <div class="security-item">
        <div class="security-info">
          <div class="security-title">登录密码</div>
          <div class="security-desc">用于登录账户的密码</div>
        </div>
        <el-button type="primary" link @click="handlePasswordChange">
          修改
        </el-button>
      </div>
      
      <div class="security-item">
        <div class="security-info">
          <div class="security-title">手机号码</div>
          <div class="security-desc">{{ maskPhone(userInfo.phone) }}</div>
        </div>
        <el-button type="primary" link @click="handlePhoneChange">
          更换
        </el-button>
      </div>
      
      <div class="security-item">
        <div class="security-info">
          <div class="security-title">实名认证</div>
          <div class="security-desc">{{ userInfo.realName }} ({{ maskIdCard(userInfo.idCard) }})</div>
        </div>
        <el-tag type="success">已认证</el-tag>
      </div>
    </div>
    
    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="passwordDialogVisible"
      title="修改密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="passwordDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="passwordSubmitting" @click="handlePasswordSubmit">
          确认修改
        </el-button>
      </template>
    </el-dialog>

    <!-- 更换手机号对话框 -->
    <el-dialog
      v-model="phoneDialogVisible"
      title="更换手机号"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="phoneFormRef"
        :model="phoneForm"
        :rules="phoneRules"
        label-width="100px"
      >
        <el-form-item label="当前手机号">
          <div class="current-phone">{{ maskPhone(userInfo.phone) }}</div>
        </el-form-item>
        <el-form-item label="新手机号" prop="newPhone">
          <el-input
            v-model="phoneForm.newPhone"
            placeholder="请输入新手机号"
          />
        </el-form-item>
        <el-form-item label="验证码" prop="verifyCode">
          <div class="verify-code-input">
            <el-input v-model="phoneForm.verifyCode" placeholder="请输入验证码" />
            <el-button 
              :disabled="codeSending || cooldown > 0" 
              @click="handleSendCode"
            >
              {{ cooldown > 0 ? `${cooldown}秒后重试` : '发送验证码' }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="phoneDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="phoneSubmitting" @click="handlePhoneSubmit">
          确认更换
        </el-button>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus';

/**
 * 组件属性定义
 */
defineProps({
  /**
   * 用户信息
   */
  userInfo: {
    type: Object,
    required: true
  },
  /**
   * 密码表单是否正在提交
   */
  passwordSubmitting: {
    type: Boolean,
    default: false
  },
  /**
   * 手机表单是否正在提交
   */
  phoneSubmitting: {
    type: Boolean,
    default: false
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits(['password-change', 'phone-change', 'send-verify-code']);

// 对话框可见性
const passwordDialogVisible = ref(false);
const phoneDialogVisible = ref(false);

// 表单引用
const passwordFormRef = ref<FormInstance>();
const phoneFormRef = ref<FormInstance>();

// 验证码发送状态
const codeSending = ref(false);
const cooldown = ref(0);

// 密码表单数据
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 手机表单数据
const phoneForm = reactive({
  newPhone: '',
  verifyCode: ''
});

// 密码表单规则
const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (_rule: any, value: string, callback: Function) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
};

// 手机表单规则
const phoneRules = {
  newPhone: [
    { required: true, message: '请输入新手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' }
  ],
  verifyCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码长度应为6位', trigger: 'blur' }
  ]
};

/**
 * 处理修改密码点击
 */
const handlePasswordChange = () => {
  // 重置表单
  passwordForm.oldPassword = '';
  passwordForm.newPassword = '';
  passwordForm.confirmPassword = '';
  
  // 显示对话框
  passwordDialogVisible.value = true;
};

/**
 * 处理修改密码提交
 */
const handlePasswordSubmit = async () => {
  if (!passwordFormRef.value) return;
  
  await passwordFormRef.value.validate(valid => {
    if (valid) {
      emit('password-change', {
        oldPassword: passwordForm.oldPassword,
        newPassword: passwordForm.newPassword
      });
    }
  });
};

/**
 * 处理更换手机号点击
 */
const handlePhoneChange = () => {
  // 重置表单
  phoneForm.newPhone = '';
  phoneForm.verifyCode = '';
  cooldown.value = 0;
  
  // 显示对话框
  phoneDialogVisible.value = true;
};

/**
 * 处理发送验证码
 */
const handleSendCode = async () => {
  if (!phoneForm.newPhone) {
    ElMessage.warning('请先输入新手机号码');
    return;
  }
  
  if (!/^1[3-9]\d{9}$/.test(phoneForm.newPhone)) {
    ElMessage.warning('请输入有效的手机号码');
    return;
  }
  
  codeSending.value = true;
  
  try {
    await emit('send-verify-code', phoneForm.newPhone);
    
    // 开始倒计时
    cooldown.value = 60;
    const timer = setInterval(() => {
      cooldown.value--;
      if (cooldown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
    
  } catch (error) {
    console.error('发送验证码失败:', error);
  } finally {
    codeSending.value = false;
  }
};

/**
 * 处理更换手机号提交
 */
const handlePhoneSubmit = async () => {
  if (!phoneFormRef.value) return;
  
  await phoneFormRef.value.validate(valid => {
    if (valid) {
      emit('phone-change', {
        newPhone: phoneForm.newPhone,
        verifyCode: phoneForm.verifyCode
      });
    }
  });
};

/**
 * 手机号脱敏
 * @param {string} phone 手机号码
 * @returns {string} 脱敏后的手机号码
 */
const maskPhone = (phone: string) => {
  if (!phone || phone.length !== 11) return phone;
  return phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');
};

/**
 * 身份证号脱敏
 * @param {string} idCard 身份证号码
 * @returns {string} 脱敏后的身份证号码
 */
const maskIdCard = (idCard: string) => {
  if (!idCard) return idCard;
  return idCard.replace(/^(.{6}).*(.{4})$/, '$1********$2');
};
</script>

<style scoped>
.security-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.security-title {
  font-size: 16px;
  color: #303133;
  margin-bottom: 4px;
}

.security-desc {
  color: #909399;
  font-size: 14px;
}

.current-phone {
  color: #606266;
}

.verify-code-input {
  display: flex;
  gap: 8px;
}

.verify-code-input .el-input {
  flex: 1;
}
</style>
