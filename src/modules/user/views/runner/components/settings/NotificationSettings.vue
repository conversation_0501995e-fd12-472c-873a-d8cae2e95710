<!--
  @component: NotificationSettings
  @author: AI Assistant
  @date: 2025-06-08
  @version: 1.0.0
  @description: 跑腿员通知设置组件
-->
<template>
  <el-card class="settings-content">
    <template #header>
      <h3>通知设置</h3>
    </template>
    
    <el-form label-width="150px">
      <el-form-item label="新订单通知">
        <el-switch
          v-model="notificationForm.newOrder"
          active-text="开启"
          inactive-text="关闭"
        />
      </el-form-item>
      
      <el-form-item label="订单状态变更">
        <el-switch
          v-model="notificationForm.orderStatus"
          active-text="开启"
          inactive-text="关闭"
        />
      </el-form-item>
      
      <el-form-item label="收入到账通知">
        <el-switch
          v-model="notificationForm.income"
          active-text="开启"
          inactive-text="关闭"
        />
      </el-form-item>
      
      <el-form-item label="系统消息">
        <el-switch
          v-model="notificationForm.system"
          active-text="开启"
          inactive-text="关闭"
        />
      </el-form-item>
      
      <el-form-item label="声音提醒">
        <el-switch
          v-model="notificationForm.sound"
          active-text="开启"
          inactive-text="关闭"
        />
      </el-form-item>
      
      <el-form-item label="震动提醒">
        <el-switch
          v-model="notificationForm.vibration"
          active-text="开启"
          inactive-text="关闭"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          保存设置
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { reactive } from 'vue';

/**
 * 组件属性定义
 */
const props = defineProps({
  /**
   * 通知设置表单数据
   */
  modelValue: {
    type: Object,
    required: true
  },
  /**
   * 是否正在提交
   */
  submitting: {
    type: Boolean,
    default: false
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits(['update:modelValue', 'submit']);

// 表单数据（从props复制以避免直接修改props）
const notificationForm = reactive({...props.modelValue});

/**
 * 处理表单提交
 */
const handleSubmit = () => {
  // 发送表单数据到父组件
  emit('update:modelValue', {...notificationForm});
  emit('submit');
};
</script>

<style scoped>
.settings-content {
  margin-bottom: 20px;
}
</style>
