<!--
  @component: ProfileSettings
  @author: AI Assistant
  @date: 2025-06-08
  @version: 1.0.0
  @description: 跑腿员个人信息设置组件
-->
<template>
  <el-card class="settings-content">
    <template #header>
      <h3>个人信息</h3>
    </template>
    
    <el-form
      ref="profileFormRef"
      :model="profileForm"
      :rules="profileRules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="真实姓名" prop="real_name">
            <el-input v-model="profileForm.real_name" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="身份证号" prop="id_card_number">
            <el-input v-model="profileForm.id_card_number" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="手机号码" prop="mobile">
            <el-input v-model="profileForm.mobile" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <!-- 紧急联系人字段在API返回中不存在 -->
          <!-- <el-form-item label="紧急联系人" prop="emergencyContact">
            <el-input v-model="profileForm.emergencyContact" placeholder="请输入紧急联系人" />
          </el-form-item> -->
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <!-- 紧急联系电话字段在API返回中不存在 -->
          <!-- <el-form-item label="紧急联系电话" prop="emergencyPhone">
            <el-input v-model="profileForm.emergencyPhone" placeholder="请输入紧急联系电话" />
          </el-form-item> -->
        </el-col>
        <el-col :span="12">
          <!-- 性别字段在API返回中不存在 -->
          <!-- <el-form-item label="性别" prop="gender">
            <el-radio-group v-model="profileForm.gender">
              <el-radio label="male">男</el-radio>
              <el-radio label="female">女</el-radio>
            </el-radio-group>
          </el-form-item> -->
        </el-col>
      </el-row>
      
      <el-form-item label="头像">
        <el-upload
          class="avatar-uploader"
          :action="uploadUrl"
          :show-file-list="false"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload"
        >
          <img v-if="profileForm.face_pic" :src="profileForm.face_pic" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          保存修改
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus';

/**
 * 组件属性定义
 */
const props = defineProps({
  /**
   * 个人信息表单数据
   */
  modelValue: {
    type: Object,
    required: true
  },
  /**
   * 是否正在提交
   */
  submitting: {
    type: Boolean,
    default: false
  },
  /**
   * 上传URL
   */
  uploadUrl: {
    type: String,
    default: '/api/upload/avatar'
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits(['update:modelValue', 'submit', 'avatar-success']);

// 表单引用
const profileFormRef = ref<FormInstance>();

// 表单数据（从props复制以避免直接修改props）
const profileForm = reactive({...props.modelValue});

// 监听modelValue变化，更新本地表单数据
watch(() => props.modelValue, (newVal) => {
  // 清空当前对象并替换为新的值，保证响应式
  Object.keys(profileForm).forEach(key => {
    delete profileForm[key];
  });
  Object.assign(profileForm, newVal);
}, { deep: true });


// 表单校验规则
const profileRules = {
  emergencyContact: [
    { required: true, message: '请输入紧急联系人', trigger: 'blur' },
    { min: 2, max: 30, message: '长度应在2到30个字符之间', trigger: 'blur' }
  ],
  emergencyPhone: [
    { required: true, message: '请输入紧急联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ]
};

/**
 * 处理表单提交
 */
const handleSubmit = async () => {
  if (!profileFormRef.value) return;
  
  await profileFormRef.value.validate(valid => {
    if (valid) {
      // 发送表单数据到父组件
      emit('update:modelValue', {...profileForm});
      emit('submit');
    }
  });
};

/**
 * 处理头像上传成功
 * @param {Object} response 上传响应结果
 */
const handleAvatarSuccess = (response: any) => {
  if (response.code === 0 && response.data) {
    // 使用face_pic字段来存储头像路径
    profileForm.face_pic = response.data.url;
    emit('update:modelValue', {...profileForm});
    emit('avatar-success', response.data.url);
    ElMessage.success('头像上传成功');
  } else {
    ElMessage.error(response.message || '头像上传失败');
  }
};

/**
 * 头像上传前的校验
 * @param {File} rawFile 待上传的文件
 * @returns {boolean} 是否通过校验
 */
const beforeAvatarUpload = (rawFile: File) => {
  const isJPG = rawFile.type === 'image/jpeg' || rawFile.type === 'image/png';
  const isLt2M = rawFile.size / 1024 / 1024 < 2;

  if (!isJPG) {
    ElMessage.error('头像只能是 JPG 或 PNG 格式!');
    return false;
  }
  if (!isLt2M) {
    ElMessage.error('头像不能超过 2MB!');
    return false;
  }
  return isJPG && isLt2M;
};
</script>

<style scoped>
.avatar-uploader {
  width: 178px;
  height: 178px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
