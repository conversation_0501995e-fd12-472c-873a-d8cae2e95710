<!--
  @component: OrderStats
  @author: AI Assistant
  @date: 2025-06-08
  @version: 1.0.0
  @description: 跑腿员订单统计卡片组件，展示不同状态订单的数量
-->
<template>
  <el-row :gutter="20" class="stats-row">
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-item">
          <div class="stat-number">{{ stats.pending || 0 }}</div>
          <div class="stat-label">待接单</div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-item">
          <div class="stat-number">{{ stats.accepted || 0 }}</div>
          <div class="stat-label">已接单</div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-item">
          <div class="stat-number">{{ stats.delivering || 0 }}</div>
          <div class="stat-label">配送中</div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-item">
          <div class="stat-number">{{ stats.completed || 0 }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
// import { defineProps } from 'vue';

/**
 * 组件属性定义
 */
defineProps({
  /**
   * 订单统计数据
   */
  stats: {
    type: Object,
    default: () => ({
      pending: 0,
      accepted: 0,
      delivering: 0,
      completed: 0
    })
  }
});
</script>

<style scoped>
.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 20px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}
</style>
