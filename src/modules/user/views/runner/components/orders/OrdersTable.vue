<!--
  @component: OrdersTable
  @author: AI Assistant
  @date: 2025-06-08
  @version: 1.0.0
  @description: 跑腿员订单列表表格组件，展示订单列表及提供订单操作功能
-->
<template>
  <div class="orders-table">
    <el-table
      :data="tableData"
      style="width: 100%"
      row-key="id"
      v-loading="loading"
      @sort-change="handleSortChange"
    >
      <!-- 订单状态 -->
      <el-table-column label="订单状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
        </template>
      </el-table-column>

      <!-- 订单信息 -->
      <el-table-column label="订单信息" width="180">
        <template #default="{ row }">
          <div class="order-info">
            <div class="order-no">{{ row.orderNo }}</div>
            <div class="order-time">{{ formatDate(row.createdAt) }}</div>
          </div>
        </template>
      </el-table-column>

      <!-- 配送信息 -->
      <el-table-column label="配送信息" width="300">
        <template #default="{ row }">
          <div class="delivery-info">
            <div class="address-item">
              <span class="label">取货:</span>
              <span class="address">{{ row.pickupAddress }}</span>
            </div>
            <div class="address-item">
              <span class="label">送达:</span>
              <span class="address">{{ row.deliveryAddress }}</span>
            </div>
            <div class="distance">距离: {{ row.distance }}km</div>
          </div>
        </template>
      </el-table-column>

      <!-- 费用信息 -->
      <el-table-column label="费用信息" width="120">
        <template #default="{ row }">
          <div class="fee-info">
            <div class="delivery-fee">配送费: ¥{{ row.deliveryFee.toFixed(2) }}</div>
            <div class="runner-income">收入: ¥{{ row.runnerIncome.toFixed(2) }}</div>
          </div>
        </template>
      </el-table-column>

      <!-- 预计时间 -->
      <el-table-column label="预计时间" width="100" align="center">
        <template #default="{ row }">
          {{ row.estimatedTime }}分钟
        </template>
      </el-table-column>

      <!-- 操作按钮 -->
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <!-- 待支付状态 -->
            <template v-if="row.status === 10">
              <el-button size="small" @click="handleViewDetail(row)">
                查看详情
              </el-button>
            </template>
            
            <!-- 待接单状态 -->
            <template v-else-if="row.status === 20 || row.status === 0">
              <el-button type="primary" size="small" @click="handleAcceptOrder(row)">
                接单
              </el-button>
              <el-button size="small" @click="handleViewDetail(row)">
                详情
              </el-button>
            </template>
            
            <!-- 已接单状态(待取货) -->
            <template v-else-if="row.status === 30 || row.status === 1">
              <el-button type="success" size="small" @click="handlePickUpOrder(row)">
                取货
              </el-button>
              <el-button type="danger" size="small" @click="handleCancelOrder(row)">
                取消订单
              </el-button>
              <el-button size="small" @click="handleViewDetail(row)">
                详情
              </el-button>
            </template>
            
            <!-- 配送中状态(已取货) -->
            <template v-else-if="row.status === 40 || row.status === 2">
              <el-button type="warning" size="small" @click="handleUpdateStatus(row)">
                更新状态
              </el-button>
              <el-button type="success" size="small" @click="handleCompleteOrder(row)">
                完成配送
              </el-button>
              <el-button size="small" @click="handleViewDetail(row)">
                详情
              </el-button>
            </template>
            
            <!-- 已完成或已取消状态 -->
            <template v-else>
              <el-button size="small" @click="handleViewDetail(row)">
                查看详情
              </el-button>
            </template>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

/**
 * 组件属性定义
 */
const props = defineProps({
  /**
   * 订单数据列表
   */
  data: {
    type: Array,
    default: () => []
  },
  /**
   * 加载状态
   */
  loading: {
    type: Boolean,
    default: false
  },
  /**
   * 分页信息
   */
  pagination: {
    type: Object,
    default: () => ({
      currentPage: 1,
      pageSize: 10,
      total: 0,
      pageSizes: [10, 20, 30, 50]
    })
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits([
  'update:pagination',
  'refresh', 
  'accept-order', 
  'pick-up', 
  'complete-order', 
  'cancel-order', 
  'view-detail', 
  'update-status'
]);

// 表格数据
const tableData = ref(props.data || []);

// 监听props.data变化，更新tableData
const updateTableData = () => {
  tableData.value = props.data || [];
  console.log('OrdersTable.vue - 表格数据:', tableData.value)
};

// 通过props更新tableData
updateTableData();

// 使用watch监听props.data的变化，确保当父组件数据更新时表格也能刷新
watch(
  () => props.data,
  (newData) => {
    console.log('OrdersTable.vue - props.data变化被监听到:', newData);
    updateTableData();
  },
  { deep: true } // 深度监听对象内部变化
);

/**
 * 获取订单状态对应的标签类型
 * @param {number} status 状态码
 * @returns {string} 标签类型
 */
const getStatusType = (status: number) => {
  switch (status) {
    case 10: return 'info'    // 待支付
    case 20: return 'warning' // 待接单
    case 30: return 'primary' // 已接单
    case 40: return 'success' // 配送中
    case 50: return 'success' // 已完成
    case 60: return 'danger'  // 已取消
    // 兼容旧状态码
    case 0: return 'warning'  // 待接单
    case 1: return 'primary'  // 已接单
    case 2: return 'success'  // 配送中
    case 3: return 'success'  // 已完成
    case 4: return 'danger'   // 已取消
    default: return 'info'
  }
};

/**
 * 获取订单状态对应的文本
 * @param {number} status 状态码
 * @returns {string} 状态文本
 */
const getStatusText = (status: number) => {
  switch (status) {
    case 10: return '待支付'
    case 20: return '待接单' // 已支付，待接单
    case 30: return '已接单' // 已接单，待取货
    case 40: return '配送中' // 已取货，配送中
    case 50: return '已完成'
    case 60: return '已取消'
    // 兼容旧状态码
    case 0: return '待接单'
    case 1: return '已接单'
    case 2: return '配送中'
    case 3: return '已完成'
    case 4: return '已取消'
    default: return '未知(' + status + ')'
  }
};

/**
 * 格式化日期
 * @param {string | Date} dateString 日期字符串或日期对象
 * @returns {string} 格式化后的日期字符串
 */
const formatDate = (dateString: string | Date) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString(); // 根据需要调整格式化方式
};

/**
 * 处理排序变化事件
 * @param {Object} param 排序参数
 */
const handleSortChange = (param: any) => {
  // 排序逻辑，可根据需要实现
  emit('refresh', {
    sort: param.prop,
    order: param.order
  });
};

/**
 * 处理页码变化事件
 * @param {number} currentPage 当前页码
 */
const handleCurrentChange = (currentPage: number) => {
  emit('update:pagination', {
    ...props.pagination,
    currentPage
  });
  emit('refresh');
};

/**
 * 处理每页条数变化事件
 * @param {number} pageSize 每页条数
 */
const handleSizeChange = (pageSize: number) => {
  emit('update:pagination', {
    ...props.pagination,
    pageSize,
    currentPage: 1
  });
  emit('refresh');
};

/**
 * 处理接单事件
 * @param {Object} row 订单行数据
 */
const handleAcceptOrder = (row: any) => {
  emit('accept-order', row);
};

/**
 * 处理取货事件
 * @param {Object} row 订单数据
 */
const handlePickUpOrder = (row: any) => {
  emit('pick-up', row);
};

/**
 * 处理完成订单事件
 * @param {Object} row 订单行数据
 */
const handleCompleteOrder = (row: any) => {
  emit('complete-order', row);
};

/**
 * 处理取消订单事件
 * @param {Object} row 订单行数据
 */
const handleCancelOrder = (row: any) => {
  emit('cancel-order', row);
};

/**
 * 处理查看订单详情事件
 * @param {Object} row 订单行数据
 */
const handleViewDetail = (row: any) => {
  emit('view-detail', row);
};

/**
 * 处理更新订单状态事件
 * @param {Object} row 订单行数据
 */
const handleUpdateStatus = (row: any) => {
  emit('update-status', row);
};
</script>

<style scoped>
.orders-table {
  width: 100%;
}

.order-info {
  line-height: 1.5;
}

.order-no {
  font-weight: 600;
  color: #303133;
}

.order-time {
  font-size: 12px;
  color: #909399;
}

.delivery-info {
  line-height: 1.5;
}

.address-item {
  margin-bottom: 4px;
}

.label {
  color: #909399;
  margin-right: 4px;
}

.address {
  color: #303133;
}

.distance {
  font-size: 12px;
  color: #409eff;
}

.fee-info {
  line-height: 1.5;
}

.delivery-fee {
  color: #303133;
  margin-bottom: 4px;
}

.runner-income {
  color: #67c23a;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
