<!--
  @component: OrderDetailDialog
  @author: AI Assistant
  @date: 2025-06-08
  @version: 1.0.0
  @description: 跑腿员订单详情对话框组件
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="600px"
    :close-on-click-modal="false"
    @update:model-value="handleDialogClose"
  >
    <div v-if="order" class="order-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单编号">{{ order.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag :type="getStatusType(order.status)">{{ getStatusText(order.status) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="下单时间">{{ formatDate(order.createdAt) }}</el-descriptions-item>
        <el-descriptions-item label="取货地址">{{ order.pickupAddress }}</el-descriptions-item>
        <el-descriptions-item label="送达地址">{{ order.deliveryAddress }}</el-descriptions-item>
        <el-descriptions-item label="配送距离">{{ order.distance }}km</el-descriptions-item>
        <el-descriptions-item label="配送费用">¥{{ order.deliveryFee.toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="跑腿员收入">¥{{ order.runnerIncome.toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="商品重量">{{ order.goodsWeight }}kg</el-descriptions-item>
        <el-descriptions-item label="备注信息">{{ order.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
      
      <!-- 配送状态更新 -->
      <div v-if="showStatusUpdate" class="status-update">
        <h4>配送状态更新</h4>
        <el-form :model="statusUpdateForm" label-width="100px">
          <el-form-item label="当前状态">
            <el-select v-model="statusUpdateForm.status">
              <el-option label="已取货/配送中" value="40" :disabled="!isOrderAccepted" />
              <el-option label="已完成" value="50" :disabled="!isOrderPickedUp" />
              <el-option label="取消订单" value="60" :disabled="isOrderCompletedOrCanceled" />
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="statusUpdateForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入状态更新备注"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleStatusUpdate">更新状态</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="handleDialogClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';

/**
 * 组件属性定义
 */
const props = defineProps({
  /**
   * 是否显示对话框
   */
  visible: {
    type: Boolean,
    default: false
  },
  /**
   * 订单数据
   */
  order: {
    type: Object,
    default: null
  },
  /**
   * 是否显示状态更新表单
   */
  showStatusUpdate: {
    type: Boolean,
    default: false
  },
  /**
   * 对话框标题
   */
  title: {
    type: String,
    default: '订单详情'
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits(['update:visible', 'status-update', 'close']);

// 对话框可见状态
const dialogVisible = ref(props.visible);

// 状态更新表单
const statusUpdateForm = reactive({
  status: '',
  remark: ''
});

// 订单状态计算属性
// 判断订单是否处于已接单状态（可进入配送中状态）
const isOrderAccepted = computed(() => {
  if (!props.order) return false;
  return props.order.status === 30 || props.order.status === 1;
});

// 判断订单是否处于已取货/配送中状态（可进入完成状态）
const isOrderPickedUp = computed(() => {
  if (!props.order) return false;
  return props.order.status === 40 || props.order.status === 2;
});

// 判断订单是否已完成或已取消（不能再改变状态）
const isOrderCompletedOrCanceled = computed(() => {
  if (!props.order) return false;
  return props.order.status === 50 || props.order.status === 60 || 
         props.order.status === 3 || props.order.status === 4;
});

// 监听props.visible变化，更新dialogVisible
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
});

// 监听dialogVisible变化，更新props.visible
watch(() => dialogVisible.value, (newVal) => {
  emit('update:visible', newVal);
});

// 监听props.order变化，重置状态更新表单
watch(() => props.order, () => {
  statusUpdateForm.status = '';
  statusUpdateForm.remark = '';
});

/**
 * 获取状态类型
 * @param {number} status 状态码
 * @returns {string} 标签类型
 */
const getStatusType = (status: number) => {
  switch (status) {
    case 10: return 'info'    // 待支付
    case 20: return 'warning' // 待接单
    case 30: return 'primary' // 已接单
    case 40: return 'success' // 配送中
    case 50: return 'success' // 已完成
    case 60: return 'danger'  // 已取消
    // 兼容旧状态码
    case 0: return 'warning'  // 待接单
    case 1: return 'primary'  // 已接单
    case 2: return 'success'  // 配送中
    case 3: return 'success'  // 已完成
    case 4: return 'danger'   // 已取消
    default: return 'info'
  }
};

/**
 * 获取状态文本
 * @param {number} status 状态码
 * @returns {string} 状态文本
 */
const getStatusText = (status: number) => {
  switch (status) {
    case 10: return '待支付'
    case 20: return '待接单' // 已支付，待接单
    case 30: return '已接单' // 已接单，待取货
    case 40: return '配送中' // 已取货，配送中
    case 50: return '已完成'
    case 60: return '已取消'
    // 兼容旧状态码
    case 0: return '待接单'
    case 1: return '已接单'
    case 2: return '配送中'
    case 3: return '已完成'
    case 4: return '已取消'
    default: return '未知(' + status + ')'
  }
};

/**
 * 格式化日期
 * @param {string | Date} dateString 日期字符串或日期对象
 * @returns {string} 格式化后的日期字符串
 */
const formatDate = (dateString: string | Date) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString(); // 根据需要调整格式化方式
};

/**
 * 处理对话框关闭事件
 */
const handleDialogClose = () => {
  dialogVisible.value = false;
  emit('close');
};

/**
 * 处理状态更新事件
 */
const handleStatusUpdate = () => {
  if (!statusUpdateForm.status) {
    return;
  }
  
  // 生成适合的状态文本描述
  let statusDesc = '';
  switch (statusUpdateForm.status) {
    case '40':
      statusDesc = '已取货/配送中';
      break;
    case '50':
      statusDesc = '已完成';
      break;
    case '60':
      statusDesc = '已取消';
      break;
  }
  
  // 如果没有备注，自动添加状态描述作为备注
  if (!statusUpdateForm.remark) {
    statusUpdateForm.remark = `订单状态更新为${statusDesc}`;
  }
  
  emit('status-update', statusUpdateForm);
  // 对话框在状态更新成功后可能需要关闭，这部分逻辑由父组件控制
};

/**
 * 打开对话框
 * @param {Object} order 订单数据
 * @param {boolean} showStatusUpdate 是否显示状态更新表单
 */
const openDialog = (_order: any, _showStatusUpdate = false) => {
  dialogVisible.value = true;
};

// 暴露方法给父组件
defineExpose({
  openDialog
});
</script>

<style scoped>
.order-detail {
  max-height: 500px;
  overflow-y: auto;
}

.status-update {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.status-update h4 {
  margin: 0 0 16px 0;
  color: #303133;
}
</style>
