<!--
  @component: SearchForm
  @author: AI Assistant
  @date: 2025-06-08
  @version: 1.0.0
  @description: 跑腿员订单管理搜索表单组件
-->
<template>
  <el-form :model="formData" label-width="80px" class="order-search-form">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="8">
        <el-form-item label="订单状态">
          <el-select v-model="formData.status" placeholder="请选择订单状态" clearable>
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="8">
        <el-form-item label="订单编号">
          <el-input v-model="formData.orderNo" placeholder="请输入订单编号" clearable />
        </el-form-item>
      </el-col>
      
      <el-col :xs="24" :sm="24" :md="8">
        <el-form-item label="下单时间">
          <el-date-picker
            v-model="formData.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            clearable
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      
      <template v-if="showAdvanced">
        <el-col :xs="24" :sm="12" :md="8">
          <el-form-item label="取货地址">
            <el-input v-model="formData.pickupAddress" placeholder="请输入取货地址" clearable />
          </el-form-item>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8">
          <el-form-item label="送达地址">
            <el-input v-model="formData.deliveryAddress" placeholder="请输入送达地址" clearable />
          </el-form-item>
        </el-col>
      </template>
      
      <el-col :xs="24" :sm="24" :md="24" class="search-actions">
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="text" @click="toggleAdvanced">
          {{ showAdvanced ? '收起' : '展开' }}
          <el-icon class="advanced-icon" :class="{ 'is-active': showAdvanced }">
            <ArrowDown />
          </el-icon>
        </el-button>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ArrowDown } from '@element-plus/icons-vue';

/**
 * 组件属性定义
 */
const props = defineProps({
  /**
   * 初始搜索表单数据
   */
  initialFormData: {
    type: Object,
    default: () => ({
      status: '',
      orderNo: '',
      dateRange: [],
      pickupAddress: '',
      deliveryAddress: ''
    })
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits(['search', 'reset']);

// 状态选项列表
const statusOptions = [
  { label: '全部', value: '' },
  { label: '待接单', value: '0' },
  { label: '已接单', value: '1' },
  { label: '配送中', value: '2' },
  { label: '已完成', value: '3' },
  { label: '已取消', value: '4' }
];

// 搜索表单数据
const formData = reactive({...props.initialFormData});

// 是否显示高级搜索选项
const showAdvanced = ref(false);

/**
 * 切换显示高级搜索选项
 */
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value;
};

/**
 * 处理搜索事件
 * 触发search事件并将表单数据传递给父组件
 */
const handleSearch = () => {
  emit('search', formData);
};

/**
 * 处理重置事件
 * 重置表单数据并触发reset事件
 */
const handleReset = () => {
  Object.assign(formData, {
    status: '',
    orderNo: '',
    dateRange: [],
    pickupAddress: '',
    deliveryAddress: ''
  });
  emit('reset', formData);
};
</script>

<style scoped>
.order-search-form {
  padding: 16px 0;
}

.search-actions {
  display: flex;
  justify-content: flex-start;
  padding-top: 10px;
}

.advanced-icon {
  transition: transform 0.3s;
}

.advanced-icon.is-active {
  transform: rotate(180deg);
}
</style>
