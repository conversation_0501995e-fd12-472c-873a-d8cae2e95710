<!--
  @component: AccountBalance
  @author: AI Assistant
  @date: 2025-06-08
  @description: 跑腿员账户余额组件，展示可提现余额、冻结金额和提现中金额，并提供提现按钮
-->
<template>
  <el-card class="balance-card">
    <template #header>
      <div class="balance-header">
        <h3>账户余额</h3>
        <el-button type="primary" @click="handleWithdraw">
          申请提现
        </el-button>
      </div>
    </template>
    
    <div class="balance-info">
      <div class="balance-item">
        <div class="balance-label">可提现余额</div>
        <div class="balance-value available">¥{{ balanceInfo.available.toFixed(2) }}</div>
      </div>
      <div class="balance-item">
        <div class="balance-label">冻结金额</div>
        <div class="balance-value frozen">¥{{ balanceInfo.frozen.toFixed(2) }}</div>
      </div>
      <div class="balance-item">
        <div class="balance-label">提现中金额</div>
        <div class="balance-value withdrawing">¥{{ balanceInfo.withdrawing.toFixed(2) }}</div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
// import { defineProps, defineEmits } from 'vue';
import type { BalanceInfo } from './types';

defineProps<{
  balanceInfo: BalanceInfo
}>();

const emit = defineEmits<{
  (e: 'withdraw'): void
}>();

// 处理提现按钮点击事件
const handleWithdraw = () => {
  emit('withdraw');
};
</script>

<style scoped lang="scss">
.balance-card {
  margin-bottom: 20px;
  
  .balance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 16px;
    }
  }
  
  .balance-info {
    display: flex;
    justify-content: space-between;
    
    .balance-item {
      text-align: center;
      padding: 10px 20px;
      flex: 1;
      
      .balance-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
      }
      
      .balance-value {
        font-size: 20px;
        font-weight: bold;
        
        &.available {
          color: #67C23A;
        }
        
        &.frozen {
          color: #E6A23C;
        }
        
        &.withdrawing {
          color: #409EFF;
        }
      }
    }
  }
}
</style>
