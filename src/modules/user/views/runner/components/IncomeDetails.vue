<!--
  @component: IncomeDetails
  @author: AI Assistant
  @date: 2025-06-08
  @description: 跑腿员收入明细组件，包括搜索筛选、收入列表展示功能
-->
<template>
  <el-card class="income-detail-card">
    <template #header>
      <h3>收入明细</h3>
    </template>
    
    <!-- 搜索筛选 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="收入类型">
          <el-select v-model="searchForm.type" placeholder="请选择收入类型" clearable>
            <el-option label="全部" value="" />
            <el-option label="配送费" value="0" />
            <el-option label="小费" value="1" />
            <el-option label="奖励" value="2" />
            <el-option label="退款" value="3" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="未结算" value="0" />
            <el-option label="已结算" value="1" />
            <el-option label="已退款" value="2" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="订单编号">
          <el-input v-model="searchForm.orderNo" placeholder="请输入订单编号" clearable />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 收入列表 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      style="width: 100%"
      row-key="id"
    >
      <el-table-column label="收入类型" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getIncomeTypeColor(row.type)">{{ getIncomeTypeText(row.type) }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="订单信息" width="180">
        <template #default="{ row }">
          <div class="order-info">
            <div class="order-no">{{ row.orderNo }}</div>
            <div class="order-time">{{ formatDate(row.orderTime) }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="收入金额" width="120" align="right">
        <template #default="{ row }">
          <span class="income-amount" :class="{ positive: row.amount > 0, negative: row.amount < 0 }">
            {{ row.amount > 0 ? '+' : '' }}¥{{ row.amount.toFixed(2) }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusColor(row.status)">{{ getStatusText(row.status) }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="创建时间" width="160">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>
      
      <el-table-column label="备注" min-width="200">
        <template #default="{ row }">
          {{ row.remark }}
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
// import { ElMessage } from 'element-plus'
import type { IncomeRecord, IncomeSearchForm } from './types'

// 定义组件属性和事件
const props = defineProps<{
  loading?: boolean;
  data?: IncomeRecord[];
  total?: number;
}>();

const emit = defineEmits<{
  (e: 'search', form: IncomeSearchForm): void;
  (e: 'reset'): void;
  (e: 'page-change', current: number, pageSize: number): void;
}>();

// 响应式数据
const searchForm = reactive<IncomeSearchForm>({
  type: '',
  status: '',
  dateRange: [],
  orderNo: ''
});

const tableData = ref<IncomeRecord[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

// 监听props变化
import { watch } from 'vue';
watch(() => props.data, (newData) => {
  if (newData) {
    tableData.value = newData;
  }
}, { immediate: true });

watch(() => props.total, (newTotal) => {
  if (newTotal !== undefined) {
    pagination.total = newTotal;
  }
}, { immediate: true });

/**
 * 格式化日期
 * @param dateString 日期字符串或日期对象
 * @returns 格式化后的日期字符串
 */
const formatDate = (dateString: string | Date): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return ''; // 处理无效日期字符串
  const year = date.getFullYear();
  const month = ('0' + (date.getMonth() + 1)).slice(-2);
  const day = ('0' + date.getDate()).slice(-2);
  const hours = ('0' + date.getHours()).slice(-2);
  const minutes = ('0' + date.getMinutes()).slice(-2);
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

/**
 * 获取收入类型颜色
 * @param type 收入类型 (0:配送费, 1:小费, 2:奖励, 3:退款)
 * @returns 对应的Element Plus标签类型
 */
const getIncomeTypeColor = (type: string): string => {
  const typeMap: Record<string, string> = {
    '0': 'success',  // 配送费
    '1': 'success',  // 小费
    '2': 'primary',  // 奖励
    '3': 'danger'    // 退款
  };
  return typeMap[type] || 'info';
};

/**
 * 获取收入类型文本
 * @param type 收入类型 (0:配送费, 1:小费, 2:奖励, 3:退款)
 * @returns 收入类型的中文描述
 */
const getIncomeTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    '0': '配送费',
    '1': '小费',
    '2': '奖励',
    '3': '退款'
  };
  return typeMap[type] || '其他收入';
};

/**
 * 获取状态颜色
 * @param status 收入状态 (0:未结算, 1:已结算, 2:已退款)
 * @returns 对应的Element Plus标签类型
 */
const getStatusColor = (status: string): string => {
  const statusMap: Record<string, string> = {
    '0': 'warning',  // 未结算
    '1': 'success',  // 已结算
    '2': 'info'      // 已退款
  };
  return statusMap[status] || 'info';
};

/**
 * 获取状态文本
 * @param status 收入状态 (0:未结算, 1:已结算, 2:已退款)
 * @returns 状态的中文描述
 */
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '0': '未结算',
    '1': '已结算',
    '2': '已退款'
  };
  return statusMap[status] || '未知状态';
};

/**
 * 处理搜索按钮点击事件
 */
const handleSearch = () => {
  pagination.current = 1; // 重置到第一页
  emit('search', { ...searchForm });
};

/**
 * 处理重置按钮点击事件
 */
const handleReset = () => {
  // 重置搜索表单
  searchForm.type = '';
  searchForm.status = '';
  searchForm.dateRange = [];
  searchForm.orderNo = '';
  pagination.current = 1; // 重置到第一页
  emit('reset');
};

/**
 * 处理每页显示数量变化事件
 * @param size 新的每页显示数量
 */
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  emit('page-change', pagination.current, size);
};

/**
 * 处理当前页码变化事件
 * @param page 新的页码
 */
const handleCurrentChange = (page: number) => {
  pagination.current = page;
  emit('page-change', page, pagination.pageSize);
};
</script>

<style scoped lang="scss">
.income-detail-card {
  margin-bottom: 20px;
  
  .search-section {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .order-info {
    .order-no {
      font-weight: bold;
      margin-bottom: 4px;
    }
    
    .order-time {
      font-size: 12px;
      color: #909399;
    }
  }
  
  .income-amount {
    font-weight: bold;
    
    &.positive {
      color: #67C23A;
    }
    
    &.negative {
      color: #F56C6C;
    }
  }
}
</style>
