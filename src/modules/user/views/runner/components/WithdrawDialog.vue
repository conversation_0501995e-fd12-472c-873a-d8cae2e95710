<!--
  @component: WithdrawDialog
  @author: AI Assistant
  @date: 2025-06-08
  @description: 跑腿员提现申请对话框组件，包括提现金额、提现方式、收款账户等表单内容
-->
<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="emit('update:visible', $event)"
    title="申请提现"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="提现金额" prop="amount">
        <el-input-number
          v-model="form.amount"
          :min="1"
          :max="availableBalance"
          :precision="2"
          :step="1"
          controls-position="right"
          style="width: 100%"
        />
        <div class="amount-tips">
          可提现余额：¥{{ availableBalance.toFixed(2) }}
        </div>
      </el-form-item>
      
      <el-form-item label="提现方式" prop="method">
        <el-radio-group v-model="form.method">
          <el-radio label="alipay">支付宝</el-radio>
          <el-radio label="wechat">微信</el-radio>
          <el-radio label="bank">银行卡</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="收款账户" prop="account">
        <el-input
          v-model="form.account"
          :placeholder="getAccountPlaceholder(form.method)"
        />
      </el-form-item>
      
      <el-form-item label="账户姓名" prop="accountName">
        <el-input
          v-model="form.accountName"
          placeholder="请输入收款账户姓名"
        />
      </el-form-item>
      
      <div class="withdraw-tips">
        <el-alert
          title="提现说明"
          type="info"
          :closable="false"
          show-icon
        >
          <ul>
            <li>提现申请提交后，将在1-3个工作日内到账</li>
            <li>单次提现金额不得少于1元</li>
            <li>提现手续费：2元/笔</li>
            <li>请确保收款账户信息准确无误</li>
          </ul>
        </el-alert>
      </div>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          确认提现
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import type { WithdrawForm } from './types';

// 组件属性
const props = defineProps<{
  visible: boolean;
  availableBalance: number;
  submitting?: boolean;
}>();

// 组件事件
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'submit', form: WithdrawForm): void;
  (e: 'cancel'): void;
}>();

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const form = reactive<WithdrawForm>({
  amount: 0,
  method: 'alipay',
  account: '',
  accountName: ''
});

// 表单验证规则
const rules = reactive<FormRules>({
  amount: [
    { required: true, message: '请输入提现金额', trigger: 'blur' },
    { type: 'number', min: 1, message: '提现金额不得少于1元', trigger: 'blur' }
  ],
  method: [
    { required: true, message: '请选择提现方式', trigger: 'change' }
  ],
  account: [
    { required: true, message: '请输入收款账户', trigger: 'blur' }
  ],
  accountName: [
    { required: true, message: '请输入账户姓名', trigger: 'blur' }
  ]
});

/**
 * 获取账户输入提示
 * @param method 提现方式
 * @returns 对应的输入框提示文本
 */
const getAccountPlaceholder = (method: string): string => {
  const placeholders: Record<string, string> = {
    alipay: '请输入支付宝账号',
    wechat: '请输入微信号',
    bank: '请输入银行卡号'
  };
  return placeholders[method] || '请输入收款账户';
};

/**
 * 监听visible属性变化，初始化表单
 */
watch(() => props.visible, (val) => {
  if (val) {
    // 对话框打开，初始化表单
    form.amount = 0;
    form.method = 'alipay';
    form.account = '';
    form.accountName = '';
  }
});

/**
 * 处理提交按钮点击事件
 */
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 验证金额是否超过可提现余额
    if (form.amount <= 0) {
      return;
    }
    
    if (form.amount > props.availableBalance) {
      return;
    }
    
    // 表单验证通过，发射提交事件
    emit('submit', {
      amount: form.amount,
      method: form.method,
      account: form.account,
      accountName: form.accountName
    });
  } catch (error) {
    // 表单验证失败
    return;
  }
};

/**
 * 处理取消按钮点击事件
 */
const handleCancel = () => {
  emit('cancel');
  emit('update:visible', false);
};
</script>

<style scoped lang="scss">
.amount-tips {
  margin-top: 5px;
  color: #909399;
  font-size: 13px;
}

.withdraw-tips {
  margin: 20px 0 10px;
  
  ul {
    padding-left: 15px;
    margin: 5px 0 0;
    
    li {
      line-height: 1.8;
      font-size: 13px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
