<!--
  @component: WithdrawRecords
  @author: AI Assistant
  @date: 2025-06-08
  @description: 跑腿员提现记录组件，展示提现申请的状态、金额等信息
-->
<template>
  <el-card class="withdraw-record-card">
    <template #header>
      <h3>提现记录</h3>
    </template>
    
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      style="width: 100%"
      row-key="id"
    >
      <el-table-column label="申请时间" width="160">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>
      
      <el-table-column label="提现金额" width="120">
        <template #default="{ row }">
          <div class="withdraw-amount">
            <div class="amount">¥{{ row.amount.toFixed(2) }}</div>
            <div class="fee">手续费：¥{{ row.fee.toFixed(2) }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="提现方式" width="150">
        <template #default="{ row }">
          <div class="withdraw-method">
            <div class="method">{{ getWithdrawMethodText(row.method) }}</div>
            <div class="account">{{ maskAccount(row.account) }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getWithdrawStatusColor(row.status)">{{ getWithdrawStatusText(row.status) }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="处理时间" width="160">
        <template #default="{ row }">
          {{ row.processedAt ? formatDate(row.processedAt) : '-' }}
        </template>
      </el-table-column>
      
      <el-table-column label="备注" min-width="200">
        <template #default="{ row }">
          {{ row.remark }}
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import type { WithdrawRecord } from './types';

// 组件属性
const props = defineProps<{
  loading?: boolean;
  data?: WithdrawRecord[];
  total?: number;
}>();

// 组件事件
const emit = defineEmits<{
  (e: 'page-change', current: number, pageSize: number): void;
}>();

// 响应式数据
const tableData = ref<WithdrawRecord[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

// 监听props变化
watch(() => props.data, (newData) => {
  if (newData) {
    tableData.value = newData;
  }
}, { immediate: true });

watch(() => props.total, (newTotal) => {
  if (newTotal !== undefined) {
    pagination.total = newTotal;
  }
}, { immediate: true });

/**
 * 格式化日期
 * @param dateString 日期字符串或日期对象
 * @returns 格式化后的日期字符串
 */
const formatDate = (dateString: string | Date): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return ''; // 处理无效日期字符串
  const year = date.getFullYear();
  const month = ('0' + (date.getMonth() + 1)).slice(-2);
  const day = ('0' + date.getDate()).slice(-2);
  const hours = ('0' + date.getHours()).slice(-2);
  const minutes = ('0' + date.getMinutes()).slice(-2);
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

/**
 * 获取提现状态颜色
 * @param status 提现状态
 * @returns 对应的Element Plus标签类型
 */
const getWithdrawStatusColor = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: 'info',
    processing: 'warning',
    success: 'success',
    failed: 'danger'
  };
  return statusMap[status] || 'info';
};

/**
 * 获取提现状态文本
 * @param status 提现状态
 * @returns 状态的中文描述
 */
const getWithdrawStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    success: '已到账',
    failed: '提现失败'
  };
  return statusMap[status] || '未知状态';
};

/**
 * 获取提现方式文本
 * @param method 提现方式
 * @returns 提现方式的中文描述
 */
const getWithdrawMethodText = (method: string): string => {
  const methodMap: Record<string, string> = {
    alipay: '支付宝',
    wechat: '微信',
    bank: '银行卡'
  };
  return methodMap[method] || '其他方式';
};

/**
 * 账户号码脱敏处理
 * @param account 账户号码
 * @returns 脱敏后的账户号码
 */
const maskAccount = (account: string): string => {
  if (!account) return '';
  if (account.length <= 4) return account;
  // 保留前两位和后两位，中间用星号替代
  return account.slice(0, 2) + '****' + account.slice(-2);
};

/**
 * 处理每页显示数量变化事件
 * @param size 新的每页显示数量
 */
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  emit('page-change', pagination.current, size);
};

/**
 * 处理当前页码变化事件
 * @param page 新的页码
 */
const handleCurrentChange = (page: number) => {
  pagination.current = page;
  emit('page-change', page, pagination.pageSize);
};
</script>

<style scoped lang="scss">
.withdraw-record-card {
  margin-bottom: 20px;
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .withdraw-amount {
    .amount {
      font-weight: bold;
      margin-bottom: 4px;
    }
    
    .fee {
      font-size: 12px;
      color: #909399;
    }
  }
  
  .withdraw-method {
    .method {
      margin-bottom: 4px;
    }
    
    .account {
      font-size: 12px;
      color: #909399;
    }
  }
}
</style>
