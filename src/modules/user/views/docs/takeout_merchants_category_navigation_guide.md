# 外卖商家分类导航 - 前端H5开发指导文件

## 概述

本文档基于 `TakeoutMerchants.vue` 文件的深入分析，提供外卖商家分类导航功能的完整开发指导，包括API接口、TypeScript类型定义、状态管理和UI实现等所有相关信息。

## 目录

1. [核心功能概述](#核心功能概述)
2. [TypeScript 类型定义](#typescript-类型定义)
3. [API 接口定义](#api-接口定义)
4. [状态管理](#状态管理)
5. [组件实现](#组件实现)
6. [样式设计](#样式设计)
7. [最佳实践](#最佳实践)
8. [常见问题](#常见问题)

## 核心功能概述

外卖商家分类导航系统包含以下核心功能：

- **全局分类导航**：支持一级分类的展示和切换
- **子分类导航**：预留二级分类支持（当前系统只有一级分类）
- **商家列表筛选**：根据分类筛选商家
- **搜索功能**：支持关键词搜索商家
- **排序功能**：支持多种排序方式
- **地理位置**：基于用户位置显示距离和排序

## TypeScript 类型定义

### 1. 分类相关类型

```typescript
// 基础外卖分类接口
export interface TakeoutCategory {
  id: string | number;
  name: string;
  icon?: string;
  image?: string;
  description?: string;
  parentId?: string | number;
  level: number;
  sortOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 全局分类接口（支持树形结构）
export interface GlobalCategory extends TakeoutCategory {
  children?: GlobalCategory[];
}
```

### 2. 商家相关类型

```typescript
// 基础商家信息接口
export interface Merchant {
  id: string | number;
  name: string;
  logo: string;
  cover: string;
  description?: string;
  address: string;
  phone?: string;
  businessHours?: string;
  categories: TakeoutCategory[];
  rating: number;
  ratingCount: number;
  minDeliveryAmount: number;
  deliveryFee: number;
  deliveryTime: string;
  distance?: number; // 单位：米
  isOpen: boolean;
  isPaused: boolean;
  createdAt: string;
  updatedAt: string;
}

// 扩展商家信息接口（包含促销等额外信息）
export interface ExtendedMerchant extends Merchant {
  // 地理位置信息
  latitude?: number;
  longitude?: number;
  
  // 促销信息
  promotions?: MerchantPromotion[];
  promotion_info?: string;
  
  // 运营状态
  operation_status?: number; // 1: 营业中, 0: 休息中
  
  // 销售数据
  month_sales?: number;
  
  // 分类信息
  category_name?: string;
  
  // 最小订单金额
  min_order_amount?: number;
}

// 商家促销信息接口
export interface MerchantPromotion {
  id: string | number;
  merchantId: string | number;
  name: string;
  type: number;
  description?: string;
  rules?: string;
  startTime?: string;
  endTime?: string;
  isActive: boolean;
}
```

## API 接口定义

### 1. 分类相关API

```typescript
// 分类服务API
const categoryAPI = {
  // 获取商户分类列表
  getMerchantCategoryList: () => request('/takeout/categories/merchant'),
  
  // 获取分类列表
  getCategoryList: () => request('/takeout/categories'),
  
  // 获取特定商家的分类列表
  getMerchantCategories: (merchantId: string | number) => 
    request(`/takeout/categories/merchant/${merchantId}`),
  
  // 获取分类详情
  getCategoryDetail: (categoryId: string | number) => 
    request(`/takeout/categories/${categoryId}`),
  
  // 获取全局分类列表
  getGlobalCategories: () => request('/takeout/categories/global'),
  
  // 获取全局分类树
  getGlobalCategoryTree: () => request('/takeout/categories/global/tree')
};
```

### 2. 商家相关API

```typescript
// 商家服务API
const merchantAPI = {
  // 获取商家列表
  getMerchants: (params?: {
    page?: number;
    pageSize?: number;
    categoryId?: string | number;
    keyword?: string;
    sort?: string;
    latitude?: number;
    longitude?: number;
  }) => request('/takeout/merchants', { params }),
  
  // 获取指定商家的食品列表
  getMerchantFoods: (merchantId: string | number, params?: {
    categoryId?: string | number;
    keyword?: string;
    page?: number;
    pageSize?: number;
  }) => request(`/takeout/merchants/${merchantId}/foods`, { params })
};
```

### 3. 服务层封装

```typescript
// takeoutService.ts
export const takeoutService = {
  category: {
    async getMerchantCategoryList() {
      try {
        const response = await categoryAPI.getMerchantCategoryList();
        return response.data || [];
      } catch (error) {
        console.error('获取商户分类列表失败:', error);
        throw error;
      }
    },
    
    async getGlobalCategories() {
      try {
        const response = await categoryAPI.getGlobalCategories();
        return response.data || [];
      } catch (error) {
        console.error('获取全局分类失败:', error);
        throw error;
      }
    }
  },
  
  merchant: {
    async getMerchants(params?: any) {
      try {
        const response = await merchantAPI.getMerchants(params);
        return response.data || { list: [], total: 0 };
      } catch (error) {
        console.error('获取商家列表失败:', error);
        throw error;
      }
    }
  }
};
```

## 状态管理

### 1. 商家状态管理 (merchantStore)

```typescript
// stores/merchantStore.ts
export const useMerchantStore = defineStore('merchant', () => {
  // 状态
  const merchants = ref<ExtendedMerchant[]>([]);
  const currentMerchant = ref<ExtendedMerchant | null>(null);
  const loading = ref(false);
  
  // 计算属性
  const merchantsMap = computed(() => {
    const map = new Map<string | number, ExtendedMerchant>();
    merchants.value.forEach(merchant => {
      map.set(merchant.id, merchant);
    });
    return map;
  });
  
  const activeMerchants = computed(() => {
    return merchants.value.filter(merchant => merchant.operation_status === 1);
  });
  
  const nearbyMerchants = computed(() => {
    return merchants.value.filter(merchant => 
      merchant.latitude && merchant.longitude
    ).sort((a, b) => {
      if (a.distance !== undefined && b.distance !== undefined) {
        return a.distance - b.distance;
      }
      return 0;
    });
  });
  
  // 核心方法
  async function loadMerchants(params?: {
    page?: number;
    pageSize?: number;
    categoryId?: string | number;
    keyword?: string;
    sort?: string;
    latitude?: number;
    longitude?: number;
  }) {
    loading.value = true;
    try {
      const response: any = await takeoutService.merchant.getMerchants(params);
      const merchantList = response?.list || [];
      
      // 更新商家列表
      merchantList.forEach((newMerchant: any) => {
        const existingMerchant = merchantsMap.value.get(newMerchant.id);
        if (existingMerchant) {
          Object.assign(existingMerchant, newMerchant);
        } else {
          merchants.value.push(newMerchant as ExtendedMerchant);
        }
      });
      
      return {
        data: merchantList,
        total: response?.total || 0
      };
    } catch (error) {
      console.error('获取商家列表失败:', error);
      return { data: [], total: 0 };
    } finally {
      loading.value = false;
    }
  }
  
  return {
    merchants,
    currentMerchant,
    loading,
    merchantsMap,
    activeMerchants,
    nearbyMerchants,
    loadMerchants
  };
});
```

### 2. 用户状态管理 (userStore)

```typescript
// stores/userStore.ts
export const useUserStore = defineStore('user', () => {
  // 地理位置相关状态
  const userLocation = ref<{ longitude: number; latitude: number } | null>(null);
  const locationError = ref<string | null>(null);
  const isGettingLocation = ref<boolean>(false);
  
  // 地理位置相关计算属性
  const hasLocation = computed(() => !!userLocation.value);
  const currentLongitude = computed(() => userLocation.value?.longitude || null);
  const currentLatitude = computed(() => userLocation.value?.latitude || null);
  
  return {
    userLocation,
    locationError,
    isGettingLocation,
    hasLocation,
    currentLongitude,
    currentLatitude
  };
});
```

## 组件实现

### 1. 核心组件结构

```vue
<template>
  <div class="takeout-merchants">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="location-info" @click="handleGetLocation">
        <el-icon><Location /></el-icon>
        <span class="location-text">{{ locationText }}</span>
        <el-icon class="refresh-icon" @click.stop="handleRefreshLocation">
          <Refresh />
        </el-icon>
      </div>
    </div>

    <!-- 全局分类导航 -->
    <div class="global-categories">
      <div class="category-scroll">
        <div 
          v-for="category in globalCategories" 
          :key="category.id"
          class="category-item"
          :class="{ active: selectedGlobalCategory?.id === category.id }"
          @click="selectGlobalCategory(category)"
        >
          <img v-if="category.icon" :src="category.icon" :alt="category.name" class="category-icon">
          <span class="category-name">{{ category.name }}</span>
        </div>
      </div>
    </div>

    <!-- 子分类导航（预留） -->
    <div v-if="subCategories.length > 0" class="sub-categories">
      <div class="category-scroll">
        <div 
          v-for="category in subCategories" 
          :key="category.id"
          class="sub-category-item"
          :class="{ active: selectedSubCategory?.id === category.id }"
          @click="selectSubCategory(category)"
        >
          {{ category.name }}
        </div>
      </div>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-bar">
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索商家或美食"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <div class="sort-options">
        <el-select v-model="sortOption" @change="handleSortChange" placeholder="排序">
          <el-option label="综合排序" value="comprehensive" />
          <el-option label="距离最近" value="distance" />
          <el-option label="评分最高" value="rating" />
          <el-option label="销量最高" value="sales" />
        </el-select>
      </div>
    </div>

    <!-- 商家列表 -->
    <div class="merchants-list">
      <div 
        v-for="merchant in merchants" 
        :key="merchant.id"
        class="merchant-card"
        @click="goToMerchantDetail(merchant.id)"
      >
        <!-- 商家卡片内容 -->
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="totalMerchants"
        @current-change="handlePageChange"
        layout="prev, pager, next"
      />
    </div>
  </div>
</template>
```

### 2. 核心逻辑实现

```vue
<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Location, Refresh, Search } from '@element-plus/icons-vue';
import type { GlobalCategory, ExtendedMerchant } from '../types';
import { takeoutService } from '../service/takeoutService';
import { useUserStore } from '../stores/userStore';
import { useMerchantStore } from '../stores/merchantStore';

const router = useRouter();
const userStore = useUserStore();
const merchantStore = useMerchantStore();

// 响应式数据
const globalCategories = ref<GlobalCategory[]>([]);
const subCategories = ref<GlobalCategory[]>([]);
const selectedGlobalCategory = ref<GlobalCategory | null>(null);
const selectedSubCategory = ref<GlobalCategory | null>(null);
const searchKeyword = ref('');
const sortOption = ref('comprehensive');
const currentPage = ref(1);
const pageSize = ref(10);
const totalMerchants = ref(0);
const loading = ref(false);

// 计算属性
const merchants = computed(() => merchantStore.merchants);
const locationText = computed(() => {
  if (userStore.isGettingLocation) return '定位中...';
  if (userStore.locationError) return '定位失败，点击重试';
  if (userStore.hasLocation) return '当前位置';
  return '点击定位';
});

// 核心方法

/**
 * 加载全局分类
 */
async function loadGlobalCategories() {
  try {
    const categories = await takeoutService.category.getMerchantCategoryList();
    globalCategories.value = categories;
    
    // 默认选择第一个分类
    if (categories.length > 0) {
      selectedGlobalCategory.value = categories[0];
    }
  } catch (error) {
    console.error('加载全局分类失败:', error);
    ElMessage.error('加载分类失败');
  }
}

/**
 * 选择全局分类
 */
function selectGlobalCategory(category: GlobalCategory) {
  selectedGlobalCategory.value = category;
  selectedSubCategory.value = null;
  currentPage.value = 1;
  loadMerchants();
}

/**
 * 加载子分类（当前系统只有一级分类）
 */
function loadSubCategories() {
  // 当前系统只有一级分类，此方法预留
  subCategories.value = [];
}

/**
 * 选择子分类
 */
function selectSubCategory(category: GlobalCategory) {
  selectedSubCategory.value = category;
  currentPage.value = 1;
  loadMerchants();
}

/**
 * 加载商家列表
 */
async function loadMerchants() {
  const params = {
    page: currentPage.value,
    pageSize: pageSize.value,
    categoryId: selectedSubCategory.value?.id || selectedGlobalCategory.value?.id,
    keyword: searchKeyword.value,
    sort: sortOption.value,
    latitude: userStore.currentLatitude,
    longitude: userStore.currentLongitude
  };
  
  const result = await merchantStore.loadMerchants(params);
  totalMerchants.value = result.total;
}

/**
 * 处理搜索
 */
function handleSearch() {
  currentPage.value = 1;
  loadMerchants();
}

/**
 * 处理排序变更
 */
function handleSortChange() {
  currentPage.value = 1;
  loadMerchants();
}

/**
 * 处理分页变更
 */
function handlePageChange(page: number) {
  currentPage.value = page;
  loadMerchants();
}

/**
 * 获取商家距离
 */
function getDistance(merchant: ExtendedMerchant): string {
  if (!merchant.distance) return '';
  
  if (merchant.distance < 1000) {
    return `${Math.round(merchant.distance)}m`;
  } else {
    return `${(merchant.distance / 1000).toFixed(1)}km`;
  }
}

/**
 * 获取促销描述
 */
function getPromotionDesc(merchant: ExtendedMerchant): string {
  if (merchant.promotion_info) {
    return merchant.promotion_info;
  }
  
  if (merchant.promotions && merchant.promotions.length > 0) {
    const activePromotions = merchant.promotions.filter(p => p.isActive);
    if (activePromotions.length > 0) {
      return activePromotions.map(p => p.name).join('，');
    }
  }
  
  return '';
}

/**
 * 处理获取位置
 */
function handleGetLocation() {
  // 位置获取逻辑
}

/**
 * 处理刷新位置
 */
function handleRefreshLocation() {
  // 位置刷新逻辑
}

/**
 * 跳转到商家详情
 */
function goToMerchantDetail(merchantId: string | number) {
  router.push({
    name: 'TakeoutMerchantDetail',
    params: { id: merchantId }
  });
}

// 监听器
watch(
  () => selectedGlobalCategory.value,
  () => {
    loadSubCategories();
    loadMerchants();
  }
);

// 生命周期
onMounted(async () => {
  await handleGetLocation();
  await loadGlobalCategories();
  await loadMerchants();
});
</script>
```

## 样式设计

### 1. 核心样式结构

```scss
.takeout-merchants {
  min-height: 100vh;
  background-color: #f5f5f5;
  
  // 页面头部
  .page-header {
    background: white;
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
    
    .location-info {
      display: flex;
      align-items: center;
      color: #333;
      cursor: pointer;
      
      .location-text {
        margin: 0 8px;
        font-size: 14px;
      }
      
      .refresh-icon {
        color: #999;
        font-size: 16px;
      }
    }
  }
  
  // 全局分类导航
  .global-categories {
    background: white;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
    
    .category-scroll {
      display: flex;
      overflow-x: auto;
      padding: 0 16px;
      
      &::-webkit-scrollbar {
        display: none;
      }
    }
    
    .category-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 60px;
      margin-right: 20px;
      cursor: pointer;
      
      .category-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-bottom: 4px;
      }
      
      .category-name {
        font-size: 12px;
        color: #666;
        text-align: center;
        white-space: nowrap;
      }
      
      &.active {
        .category-name {
          color: #ff6b35;
          font-weight: 500;
        }
      }
    }
  }
  
  // 子分类导航
  .sub-categories {
    background: white;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    
    .category-scroll {
      display: flex;
      overflow-x: auto;
      padding: 0 16px;
    }
    
    .sub-category-item {
      padding: 6px 12px;
      margin-right: 12px;
      background: #f8f8f8;
      border-radius: 16px;
      font-size: 12px;
      color: #666;
      white-space: nowrap;
      cursor: pointer;
      
      &.active {
        background: #ff6b35;
        color: white;
      }
    }
  }
  
  // 筛选栏
  .filter-bar {
    background: white;
    padding: 12px 16px;
    display: flex;
    gap: 12px;
    border-bottom: 1px solid #eee;
    
    .search-box {
      flex: 1;
    }
    
    .sort-options {
      width: 120px;
    }
  }
  
  // 商家列表
  .merchants-list {
    padding: 12px 16px;
  }
  
  // 商家卡片
  .merchant-card {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s;
    
    &:hover {
      transform: translateY(-2px);
    }
    
    .merchant-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      
      .merchant-logo {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        margin-right: 12px;
      }
      
      .merchant-info {
        flex: 1;
        
        .merchant-name {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin-bottom: 4px;
        }
        
        .merchant-status {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .status-badge {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            
            &.open {
              background: #e8f5e8;
              color: #52c41a;
            }
            
            &.closed {
              background: #fff2e8;
              color: #fa8c16;
            }
          }
          
          .rating {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #666;
            
            .star {
              color: #fadb14;
              margin-right: 2px;
            }
          }
        }
      }
    }
    
    .merchant-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      margin-bottom: 12px;
      
      .tag {
        padding: 2px 6px;
        background: #f0f0f0;
        border-radius: 4px;
        font-size: 10px;
        color: #666;
        
        &.category {
          background: #e6f7ff;
          color: #1890ff;
        }
        
        &.promotion {
          background: #fff2e8;
          color: #fa8c16;
        }
      }
    }
    
    .merchant-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #666;
      
      .delivery-info {
        display: flex;
        gap: 12px;
      }
      
      .min-amount {
        color: #ff6b35;
        font-weight: 500;
      }
    }
  }
  
  // 分页
  .pagination {
    padding: 20px;
    text-align: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .takeout-merchants {
    .filter-bar {
      flex-direction: column;
      gap: 8px;
      
      .sort-options {
        width: 100%;
      }
    }
    
    .merchant-card {
      .merchant-header {
        .merchant-logo {
          width: 50px;
          height: 50px;
        }
      }
      
      .merchant-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }
  }
}
```

## 最佳实践

### 1. 性能优化

- **虚拟滚动**：商家列表较长时使用虚拟滚动
- **图片懒加载**：商家logo和封面图使用懒加载
- **防抖搜索**：搜索输入使用防抖处理
- **缓存策略**：分类数据使用缓存，减少重复请求

### 2. 用户体验

- **加载状态**：提供清晰的加载状态反馈
- **错误处理**：友好的错误提示和重试机制
- **无数据状态**：提供有意义的空状态页面
- **下拉刷新**：支持下拉刷新功能

### 3. 代码组织

- **组件拆分**：将复杂组件拆分为更小的子组件
- **类型安全**：充分利用TypeScript类型检查
- **状态管理**：合理使用Pinia进行状态管理
- **API封装**：统一的API调用和错误处理

### 4. 移动端适配

- **触摸友好**：确保触摸目标足够大
- **响应式布局**：适配不同屏幕尺寸
- **手势支持**：支持滑动等手势操作
- **性能优化**：针对移动设备进行性能优化

## 常见问题

### 1. 分类数据加载失败

**问题**：分类数据加载失败，页面显示空白

**解决方案**：
```typescript
// 添加错误处理和重试机制
async function loadGlobalCategories() {
  try {
    const categories = await takeoutService.category.getMerchantCategoryList();
    globalCategories.value = categories;
  } catch (error) {
    console.error('加载全局分类失败:', error);
    ElMessage.error('加载分类失败，请重试');
    // 可以添加重试逻辑
  }
}
```

### 2. 地理位置获取失败

**问题**：用户拒绝位置权限或定位失败

**解决方案**：
```typescript
// 提供备选方案
function handleLocationError() {
  userStore.locationError = '定位失败';
  // 使用默认位置或让用户手动选择
  ElMessage.warning('定位失败，将使用默认位置');
}
```

### 3. 商家列表性能问题

**问题**：商家列表过长导致页面卡顿

**解决方案**：
```vue
<!-- 使用虚拟滚动 -->
<virtual-list
  :data-key="'id'"
  :data-sources="merchants"
  :data-component="MerchantCard"
  :estimate-size="120"
/>
```

### 4. 搜索性能优化

**问题**：搜索输入频繁触发API请求

**解决方案**：
```typescript
// 使用防抖
import { debounce } from 'lodash-es';

const debouncedSearch = debounce(() => {
  currentPage.value = 1;
  loadMerchants();
}, 300);

function handleSearch() {
  debouncedSearch();
}
```

---

## 总结

本指导文件提供了外卖商家分类导航功能的完整实现方案，包括：

1. **完整的TypeScript类型定义**：确保类型安全
2. **标准化的API接口设计**：便于前后端协作
3. **高效的状态管理方案**：使用Pinia管理复杂状态
4. **响应式的UI组件实现**：适配移动端H5页面
5. **优雅的样式设计**：提供良好的用户体验
6. **最佳实践指导**：确保代码质量和性能

开发者可以基于此文档快速实现外卖商家分类导航功能，并根据具体需求进行定制化开发。