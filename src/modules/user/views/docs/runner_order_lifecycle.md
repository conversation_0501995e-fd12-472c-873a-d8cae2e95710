# 跑腿订单生命周期与流程文档

## 概述

本文档详细描述跑腿模块（modules/runner）中订单的完整生命周期、状态流转以及相关业务流程。通过对模块的深入分析，本文档旨在帮助开发者和维护者更好地理解跑腿订单系统的工作原理和实现细节。

## 目录结构

跑腿模块的主要目录结构如下：

```
modules/runner/
├── constants/         - 常量定义
├── controllers/       - 控制器层，处理HTTP请求
├── core/              - 核心功能模块
├── dto/               - 数据传输对象
├── factory/           - 工厂类，用于创建对象实例
├── models/            - 数据模型
├── repositories/      - 数据访问层
│   └── impl/          - 数据访问层实现
├── routers/           - 路由定义
└── services/          - 业务服务层
    └── impl/          - 业务服务实现
```

## 跑腿订单状态定义

### 订单状态 (OrderStatus)

跑腿订单状态定义于 `services/impl/runner_order_service_impl.go` 文件中：

| 状态码 | 常量名 | 描述 |
|-------|--------|------|
| 10 | OrderStatusWaitingPay | 待支付 |
| 20 | OrderStatusPaid | 已支付，待接单 |
| 30 | OrderStatusAccepted | 已接单，待取货 |
| 40 | OrderStatusPickedUp | 已取货，配送中 |
| 50 | OrderStatusCompleted | 已完成 |
| 60 | OrderStatusCanceled | 已取消 |

### 支付状态 (PayStatus)

| 状态码 | 常量名 | 描述 |
|-------|--------|------|
| 0 | PayStatusUnpaid | 未支付 |
| 1 | PayStatusPaid | 已支付 |
| 2 | PayStatusRefund | 已退款 |

### 支付方式 (PayMethod)

| 代码 | 常量名 | 描述 |
|-------|--------|------|
| 1 | PayMethodWeChat | 微信支付 |
| 2 | PayMethodAliPay | 支付宝 |
| 3 | PayMethodBalance | 余额支付 |

### 订单类型 (OrderType)

| 代码 | 常量名 | 描述 |
|-------|--------|------|
| 1 | OrderTypeNormal | 普通订单 |
| 2 | OrderTypeUrgent | 加急订单 |
| 3 | OrderTypeSpecial | 特殊订单 |

### 跑腿员状态 (RunnerStatus)

| 状态码 | 描述 |
|-------|------|
| 0 | 待审核 |
| 1 | 审核通过 |
| 2 | 审核拒绝 |
| 3 | 暂停服务 |
| 4 | 黑名单 |

### 跑腿员工作状态 (WorkingStatus)

| 状态码 | 描述 |
|-------|------|
| 1 | 接单中 |
| 2 | 配送中 |
| 0 | 休息中 |

## 收入相关常量 (IncomeStatus)

| 状态码 | 常量名 | 描述 |
|-------|--------|------|
| 0 | IncomeStatusPending | 待入账 |
| 1 | IncomeStatusSuccess | 已入账 |
| 2 | IncomeStatusFailed | 入账失败 |

## 提现状态 (WithdrawalStatus)

| 状态码 | 常量名 | 描述 |
|-------|--------|------|
| 0 | WithdrawalStatusPending | 待处理 |
| 1 | WithdrawalStatusProcessing | 处理中 |
| 2 | WithdrawalStatusSuccess | 成功 |
| 3 | WithdrawalStatusFailed | 失败 |

## 跑腿订单生命周期流程

跑腿订单从创建到结束的完整生命周期如下：

```
创建订单 --> 待支付 --> 已支付(待接单) --> 已接单(待取货) --> 已取货(配送中) --> 已完成
                |                |                |                |
                v                v                v                v
              已取消          已取消            已取消           已取消
```

### 订单状态转换详解

1. **创建订单(OrderStatusWaitingPay, 10)**
   - 用户通过 `RunnerServiceImpl.CreateRunnerOrder` 方法创建跑腿订单
   - 初始状态为待支付(OrderStatusWaitingPay)
   - 计算配送费、服务费、小费等金额
   - 保存订单基本信息，包括取货地址、配送地址、联系人等

2. **支付订单 (OrderStatusPaid, 20)**
   - 订单支付完成后，状态变更为已支付(待接单)
   - 更新订单 PayStatus 为 PayStatusPaid (1)
   - 系统开始寻找合适的跑腿员，或等待跑腿员主动接单

3. **接单 (OrderStatusAccepted, 30)**
   - 跑腿员通过 `RunnerServiceImpl.AcceptOrder` 方法接单
   - 订单状态更新为已接单(待取货)
   - 记录接单时间(AcceptTime)
   - 更新跑腿员工作状态为配送中(WorkingStatus=2)

4. **取货 (OrderStatusPickedUp, 40)**
   - 跑腿员通过 `RunnerServiceImpl.PickupOrder` 方法确认取货
   - 订单状态更新为已取货(配送中)
   - 记录取货时间(PickupTime)

5. **完成订单 (OrderStatusCompleted, 50)**
   - 跑腿员通过 `RunnerServiceImpl.CompleteOrder` 方法确认送达
   - 订单状态更新为已完成
   - 记录完成时间(DeliveryTime)
   - 更新跑腿员状态为接单中(WorkingStatus=1)
   - 开始处理跑腿员收入
   - 可选：创建收入记录，默认为待入账(IncomeStatusPending)

6. **取消订单 (OrderStatusCanceled, 60)**
   - 用户或跑腿员可通过 `RunnerServiceImpl.CancelOrder` 方法取消订单
   - 记录取消时间(CancelTime)和取消原因(CancelReason)
   - 记录取消用户类型(CancelUserType)
   - 若已支付，触发退款流程
   - 如果是跑腿员取消，更新跑腿员状态为接单中(WorkingStatus=1)

### 订单相关操作

订单状态变更由以下几个核心方法实现，这些方法定义在 `services/impl/runner_order_operations.go` 文件中：

1. **AcceptOrder**: 跑腿员接单
   - 检查订单状态必须为已支付(OrderStatusPaid)
   - 检查订单是否已被分配给其他跑腿员
   - 检查跑腿员状态是否正常(Status=1)
   - 更新订单状态为已接单(OrderStatusAccepted)并记录时间
   - 更新跑腿员工作状态为配送中(WorkingStatus=2)

2. **CancelOrder**: 取消订单
   - 校验用户权限(普通用户或跑腿员)
   - 检查订单状态是否允许取消(不能取消已完成订单)
   - 更新订单状态为已取消(OrderStatusCanceled)并记录取消信息
   - 处理支付退款(如需要)
   - 更新跑腿员状态(如跑腿员取消)

3. **PickupOrder**: 取货
   - 校验跑腿员权限
   - 检查订单状态必须为已接单(OrderStatusAccepted)
   - 更新订单状态为已取货(OrderStatusPickedUp)并记录时间

4. **CompleteOrder**: 完成订单
   - 校验跑腿员权限
   - 检查订单状态必须为已取货(OrderStatusPickedUp)
   - 更新订单状态为已完成(OrderStatusCompleted)并记录时间
   - 计算跑腿员收入并创建收入记录
   - 更新跑腿员工作状态为接单中(WorkingStatus=1)

5. **UserRateOrder/RunnerRateOrder**: 评价订单
   - 只能评价已完成的订单
   - 检查是否已评价过
   - 更新订单评分信息
   - 用户评价订单时会更新跑腿员的平均评分

## 跑腿订单数据模型

跑腿订单主要涉及以下核心字段(基于业务逻辑推断):

```go
type RunnerOrder struct {
    ID                    int64     // 订单ID
    OrderNo               string    // 订单编号
    OrderType             int       // 订单类型：1-普通，2-加急，3-特殊
    UserID                int64     // 用户ID
    RunnerID              int64     // 跑腿员ID
    Status                int       // 订单状态
    PayStatus             int       // 支付状态
    PayMethod             int       // 支付方式
    TotalAmount           float64   // 订单总金额
    DeliveryFee           float64   // 配送费
    ServiceFee            float64   // 服务费
    TipAmount             float64   // 小费
    Distance              float64   // 配送距离
    EstimateTime          int       // 预计送达时间(分钟)
    
    // 取货信息
    PickupAddress         string    // 取货地址
    PickupAddressDetail   string    // 取货详细地址
    PickupLat             float64   // 取货地点纬度
    PickupLng             float64   // 取货地点经度
    PickupContact         string    // 取货联系人
    PickupPhone           string    // 取货联系电话
    
    // 配送信息
    DeliveryAddress       string    // 配送地址
    DeliveryAddressDetail string    // 配送详细地址
    DeliveryLat           float64   // 配送地点纬度
    DeliveryLng           float64   // 配送地点经度
    DeliveryContact       string    // 配送联系人
    DeliveryPhone         string    // 配送联系电话
    
    // 货物信息
    Goods                 string    // 货物描述
    GoodsWeight           float64   // 货物重量
    GoodsValue            float64   // 货物价值
    Remark                string    // 订单备注
    
    // 时间节点
    AcceptTime            time.Time // 接单时间
    PickupTime            time.Time // 取货时间
    DeliveryTime          time.Time // 送达时间
    CancelTime            time.Time // 取消时间
    CancelReason          string    // 取消原因
    CancelUserType        int       // 取消用户类型：1-用户，2-跑腿员
    
    // 评价信息
    ScoreByUser           float64   // 用户评分
    CommentByUser         string    // 用户评价
    ScoreByRunner         float64   // 跑腿员评分
    CommentByRunner       string    // 跑腿员评价
    
    CreateTime            time.Time // 创建时间
    UpdateTime            time.Time // 更新时间
}
```

## 配送费计算规则

配送费计算由 `RunnerServiceImpl.CalculateDeliveryFee` 方法实现：

1. **基础配送费**
   - 3公里内：固定 8.0 元
   - 超过3公里：每增加1公里加收 2.0 元

2. **重量附加费**
   - 5公斤内：无附加费
   - 超过5公斤：每增加1公斤加收 1.0 元

3. **订单类型附加费**
   - 普通订单：无附加费
   - 加急订单：配送费增加 50%
   - 特殊订单：配送费增加 30%

4. **服务费**
   - 基本为配送费的 10%

5. **预计配送时间**
   - 基础：10分钟
   - 每公里增加：5分钟

## 跑腿员收入流程

1. **收入类型**
   - 订单完成收入
   - 小费收入
   - 其他收入

2. **收入状态**
   - 待入账(IncomeStatusPending)
   - 已入账(IncomeStatusSuccess)
   - 入账失败(IncomeStatusFailed)

3. **收入入账**
   - 订单完成后创建收入记录
   - 系统审核后更新为已入账状态
   - 更新跑腿员余额

4. **提现流程**
   - 跑腿员申请提现
   - 创建提现记录(待处理)
   - 系统审核并处理提现
   - 提现成功后扣减余额
