# 外卖订单创建API详细文档

> 本文档详细说明了外卖订单创建API的使用方法，包括前端需要提交的支付方式、包装费、配送信息等详细参数说明。

## 目录

- [1. API概述](#1-api概述)
- [2. 请求参数详解](#2-请求参数详解)
- [3. 支付方式说明](#3-支付方式说明)
- [4. 包装费计算](#4-包装费计算)
- [5. 配送信息处理](#5-配送信息处理)
- [6. 跑腿配送说明](#6-跑腿配送说明)
- [7. 完整请求示例](#7-完整请求示例)
- [8. 响应数据说明](#8-响应数据说明)
- [9. 错误处理](#9-错误处理)
- [10. 注意事项](#10-注意事项)

## 1. API概述

### 1.1 接口信息

- **接口名称**: 创建外卖订单（支持多商家）
- **请求方式**: POST
- **URL**: `/api/takeout/orders/create`
- **描述**: 从购物车创建外卖订单，自动按商家拆分为多个订单
- **授权**: 需要JWT认证(用户)

### 1.2 功能特点

- 支持多商家订单自动拆分
- 自动计算包装费、配送费
- 支持多种支付方式
- 支持配送时间预约
- 支持优惠券使用
- 实时库存校验

## 2. 请求参数详解

### 2.1 新版多商家请求体结构（推荐）

```json
{
  "takeoutAddressID": 1001,
  "paymentMethod": "wechat",
  "merchantOrders": [
    {
      "merchantID": 2001,
      "cartItemIDs": [3001, 3002],
      "couponID": 2001,
      "deliveryTime": "2025-05-21 18:30:00",
      "remark": "少辣，不要香菜"
    },
    {
      "merchantID": 2002,
      "cartItemIDs": [3003, 3004],
      "couponID": 2002,
      "deliveryTime": "2025-05-21 19:00:00",
      "remark": "多加点辣椒"
    }
  ]
}
```

### 2.2 兼容旧版请求体结构（已废弃）

```json
{
  "takeoutAddressID": 1001,
  "deliveryTime": "2025-05-21 18:30:00",
  "remark": "少辣，不要香菜",
  "paymentMethod": "wechat",
  "couponID": 2001,
  "cartItemIDs": [3001, 3002, 3003]
}
```

> **注意**: 旧版结构仍然支持，但建议使用新版多商家结构以获得更好的功能支持。

### 2.3 新版参数说明

#### 2.3.1 主要参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| takeoutAddressID | int64 | 是 | 外卖配送地址ID，必须是用户已保存的有效地址 |
| paymentMethod | string | 是 | 支付方式，详见支付方式说明 |
| merchantOrders | []MerchantOrderRequest | 是 | 按商家分组的订单信息，不能为空 |

#### 2.3.2 商家订单参数（MerchantOrderRequest）

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| merchantID | int64 | 是 | 商家ID，必须是有效的商家 |
| cartItemIDs | []int64 | 是 | 该商家的购物车项ID列表，不能为空 |
| couponID | int64 | 否 | 该商家的优惠券ID，0表示不使用优惠券 |
| deliveryTime | string | 否 | 期望配送时间，格式："2006-01-02 15:04:05"，为空表示尽快送达 |
| remark | string | 否 | 对该商家的订单备注，最大长度200字符 |

### 2.4 兼容旧版参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| takeoutAddressID | int64 | 是 | 外卖配送地址ID，必须是用户已保存的有效地址 |
| deliveryTime | string | 否 | 期望配送时间，格式："2006-01-02 15:04:05"，为空表示尽快送达 |
| remark | string | 否 | 订单备注，最大长度200字符 |
| paymentMethod | string | 是 | 支付方式，详见支付方式说明 |
| couponID | int64 | 否 | 优惠券ID，0表示不使用优惠券 |
| cartItemIDs | []int64 | 是 | 购物车项ID列表，不能为空 |

### 2.5 新版参数校验规则

#### 2.5.1 主要参数校验

1. **takeoutAddressID**: 必须大于0，且地址必须属于当前用户
2. **paymentMethod**: 必填，必须是支持的支付方式之一
3. **merchantOrders**: 必填，数组不能为空，至少包含一个商家订单

#### 2.5.2 商家订单参数校验

1. **merchantID**: 必须大于0，且商家必须是有效且营业中的商家
2. **cartItemIDs**: 必填，数组不能为空，所有ID必须是当前用户购物车中属于该商家的有效项
3. **couponID**: 可选，如果提供必须是该商家的有效且可用的优惠券
4. **deliveryTime**: 
   - 可选参数，为空时系统默认为"尽快送达"（当前时间+30分钟）
   - 如果提供，必须是未来时间
   - 如果超过当前时间1小时，将被视为预订单
5. **remark**: 可选，最大长度200字符

### 2.6 兼容旧版参数校验规则

1. **takeoutAddressID**: 必须大于0，且地址必须属于当前用户
2. **deliveryTime**: 
   - 可选参数，为空时系统默认为"尽快送达"（当前时间+30分钟）
   - 如果提供，必须是未来时间
   - 如果超过当前时间1小时，将被视为预订单
3. **remark**: 可选，最大长度200字符
4. **paymentMethod**: 必填，必须是支持的支付方式之一
5. **couponID**: 可选，如果提供必须是有效且可用的优惠券
6. **cartItemIDs**: 必填，数组不能为空，所有ID必须是当前用户购物车中的有效项

## 3. 支付方式说明

### 3.1 支持的支付方式

| 支付方式代码 | 枚举值 | 显示名称 | 说明 |
|-------------|--------|----------|------|
| wechat | 1 | 微信支付 | 微信扫码或APP支付 |
| alipay | 2 | 支付宝 | 支付宝扫码或APP支付 |
| creditcard | 3 | 信用卡 | 银行信用卡支付 |
| banktransfer | 4 | 银行转账 | 网银转账支付 |
| balance | 5 | 余额支付 | 使用账户余额支付 |
| combination | 6 | 组合支付 | 多种支付方式组合 |

### 3.2 支付方式处理逻辑

```go
// 前端可以提交字符串或数字
// 系统会自动转换为对应的枚举值
switch paymentMethodStr {
case "wechat":
    req.PaymentMethod = int(paymentModels.PaymentMethodWechat)
case "alipay":
    req.PaymentMethod = int(paymentModels.PaymentMethodAlipay)
case "creditcard":
    req.PaymentMethod = int(paymentModels.PaymentMethodCreditCard)
case "banktransfer":
    req.PaymentMethod = int(paymentModels.PaymentMethodBankTransfer)
case "balance":
    req.PaymentMethod = int(paymentModels.PaymentMethodBalance)
case "combination":
    req.PaymentMethod = int(paymentModels.PaymentMethodCombination)
}
```

### 3.3 支付方式选择建议

- **微信支付**: 推荐移动端用户使用
- **支付宝**: 适合所有平台用户
- **余额支付**: 适合有充值余额的用户
- **组合支付**: 适合部分余额+其他支付方式的场景

## 4. 包装费计算

### 4.1 包装费来源

包装费由以下几个部分组成：

1. **商品包装费**: 每个商品都有独立的包装费设置
2. **规格包装费**: 如果商品有规格，规格可以覆盖商品的包装费
3. **数量计算**: 包装费 × 商品数量

### 4.2 包装费计算逻辑

```go
// 单个商品包装费计算
var packageFee float64
if variant != nil {
    // 如果有规格，使用规格的包装费
    packageFee = variant.PackagingFee
} else {
    // 否则使用商品的包装费
    packageFee = food.PackagingFee
}

// 总包装费 = 单个包装费 × 数量
totalPackagingFee := packageFee * float64(quantity)
```

### 4.3 包装费展示

在订单详情中，包装费会分别显示：
- 单个商品的包装费
- 订单总包装费
- 所有商家的包装费汇总

## 5. 配送信息处理

### 5.1 配送地址处理

系统会根据 `takeoutAddressID` 获取完整的配送地址信息：

```json
{
  "province": "广东省",
  "city": "深圳市",
  "district": "南山区",
  "street": "科技园南区",
  "detail": "腾讯大厦A座1001室",
  "contact_name": "张三",
  "contact_phone": "13800138000"
}
```

### 5.2 配送费计算

目前配送费采用固定费率：
- **标准配送费**: 5.0元/单
- **多商家订单**: 每个商家单独计算配送费
- **未来扩展**: 支持基于距离、时间段的动态配送费

### 5.3 配送时间处理

#### 5.3.1 尽快送达（默认）

```json
{
  "deliveryTime": "",  // 空字符串或不传
  "estimatedTime": "30分钟",  // 系统计算的预计送达时间
  "expectedTime": "2025-05-21 19:00:00"  // 当前时间+30分钟
}
```

#### 5.3.2 预约配送

```json
{
  "deliveryTime": "2025-05-21 20:30:00",  // 用户指定时间
  "estimatedTime": "预约送达",
  "expectedTime": "2025-05-21 20:30:00"  // 用户指定时间
}
```

#### 5.3.3 预订单处理

如果配送时间超过当前时间1小时，系统会标记为预订单：
- 订单状态特殊处理
- 商家可以提前准备
- 配送时间更精确

## 6. 跑腿配送说明

### 6.1 配送类型

| 配送类型 | 代码 | 说明 |
|----------|------|------|
| 平台配送 | 0 | 平台统一配送，由平台配送员负责 |
| 商家自配 | 1 | 商家自己的配送团队 |
| 到店自取 | 2 | 用户到店自取，无需配送 |

### 6.2 配送状态流转

```
等待配送(0) → 正在取餐(1) → 配送中(2) → 已送达(3)
                ↓
            已取消(4)
```

### 6.3 配送员信息

订单创建时，配送员信息初始为空，后续通过以下流程分配：

1. **商家接单**: 订单状态变为"处理中"
2. **分配配送员**: 商家或平台分配配送员
3. **开始配送**: 配送员取餐，开始配送
4. **完成配送**: 配送员送达，订单完成

### 6.4 配送距离计算

系统会计算商家到配送地址的距离：
- 用于配送费计算（未来功能）
- 用于配送时间估算
- 用于配送员分配优化

## 7. 完整请求示例

### 7.1 基础订单创建

```bash
curl -X POST \
  http://localhost:8181/api/takeout/orders/create \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer {user_token}' \
  -d '{
    "takeoutAddressID": 1001,
    "deliveryTime": "",
    "remark": "不要辣椒",
    "paymentMethod": "wechat",
    "couponID": 0,
    "cartItemIDs": [3001, 3002]
  }'
```

### 7.2 预约配送订单

```bash
curl -X POST \
  http://localhost:8181/api/takeout/orders/create \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer {user_token}' \
  -d '{
    "takeoutAddressID": 1001,
    "deliveryTime": "2025-05-21 20:30:00",
    "remark": "请准时送达，谢谢",
    "paymentMethod": "alipay",
    "couponID": 2001,
    "cartItemIDs": [3001, 3002, 3003]
  }'
```

### 7.3 使用优惠券订单

```bash
curl -X POST \
  http://localhost:8181/api/takeout/orders/create \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer {user_token}' \
  -d '{
    "takeoutAddressID": 1001,
    "deliveryTime": "",
    "remark": "",
    "paymentMethod": "balance",
    "couponID": 2001,
    "cartItemIDs": [3001]
  }'
```

## 8. 响应数据说明

### 8.1 成功响应结构

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "orderID": 10001,
      "orderNo": "TO202505211001",
      "userID": 1001,
      "merchantID": 2001,
      "totalAmount": 68.0,
      "payAmount": 63.0,
      "discountAmount": 5.0,
      "orderStatus": 10,
      "payStatus": 0,
      "deliveryStatus": 0,
      "deliveryFee": 5.0,
      "packagingFee": 2.0,
      "deliveryInfo": {
        "deliveryStaffID": 0,
        "deliveryStaffName": "",
        "deliveryStaffPhone": "",
        "deliveryStatus": 0,
        "deliveryAddress": "广东省深圳市南山区科技园南区腾讯大厦A座1001室",
        "deliveryDistance": 0,
        "expectedTime": "2025-05-21 19:00:00",
        "startTime": null,
        "endTime": null
      },
      "items": [
        {
          "id": 20001,
          "orderID": 10001,
          "productID": 3001,
          "productName": "香辣鸡腿堡",
          "productType": "food",
          "price": 15.9,
          "quantity": 2,
          "amount": 31.8,
          "specText": "大份",
          "image": "http://example.com/images/chicken.jpg",
          "isCombination": false,
          "comboItems": []
        }
      ],
      "paymentMethod": "微信支付",
      "remark": "不要辣椒",
      "couponInfo": {
        "couponID": 2001,
        "couponName": "满50减5",
        "discountAmount": 5.0
      },
      "isRated": false,
      "createTime": "2025-05-21T18:30:00+08:00",
      "payTime": null,
      "deliveryTime": null,
      "completeTime": null,
      "cancelTime": null,
      "cancelReason": "",
      "statusText": "待支付"
    }
  ]
}
```

### 8.2 多商家订单响应

当购物车包含多个商家的商品时，系统会自动拆分为多个订单：

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "orderID": 10001,
      "merchantID": 2001,
      "totalAmount": 68.0,
      "deliveryFee": 5.0,
      // ... 商家A的订单信息
    },
    {
      "orderID": 10002,
      "merchantID": 2002,
      "totalAmount": 45.0,
      "deliveryFee": 5.0,
      // ... 商家B的订单信息
    }
  ]
}
```

### 8.3 费用明细说明

| 字段名 | 说明 |
|--------|------|
| totalAmount | 商品总金额（不含配送费和包装费） |
| deliveryFee | 配送费 |
| packagingFee | 包装费总额 |
| discountAmount | 优惠金额（优惠券、促销等） |
| payAmount | 实际支付金额 = totalAmount + deliveryFee + packagingFee - discountAmount |

## 9. 错误处理

### 9.1 常见错误码

| 错误码 | 错误信息 | 说明 | 解决方案 |
|--------|----------|------|----------|
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 | 检查请求参数格式和完整性 |
| 400 | 配送地址不能为空 | takeoutAddressID <= 0 | 提供有效的配送地址ID |
| 400 | 购物车不能为空 | cartItemIDs为空数组 | 确保购物车中有商品 |
| 400 | 支付方式无效 | 不支持的支付方式 | 使用支持的支付方式 |
| 404 | 地址不存在 | 配送地址ID无效或不属于当前用户 | 使用有效的地址ID |
| 404 | 购物车项不存在 | 购物车项ID无效或已被删除 | 刷新购物车，重新选择商品 |
| 400 | 商品库存不足 | 商品库存不够 | 减少商品数量或选择其他商品 |
| 400 | 优惠券不可用 | 优惠券已过期、已使用或不满足使用条件 | 选择其他优惠券或不使用 |
| 500 | 创建订单失败 | 服务器内部错误 | 稍后重试或联系客服 |

### 9.2 错误响应示例

```json
{
  "code": 400,
  "message": "配送地址不能为空",
  "data": null
}
```

```json
{
  "code": 400,
  "message": "商品库存不足",
  "data": {
    "productID": 3001,
    "productName": "香辣鸡腿堡",
    "requestQuantity": 5,
    "availableStock": 3
  }
}
```

## 10. 注意事项

### 10.1 业务规则

1. **库存校验**: 创建订单时会实时校验商品库存，库存不足会创建失败
2. **地址验证**: 配送地址必须在商家配送范围内
3. **营业时间**: 只能在商家营业时间内下单
4. **最小起送**: 订单金额必须满足商家的最小起送金额
5. **配送距离**: 配送距离不能超过商家设置的最大配送距离

### 10.2 性能优化

1. **批量处理**: 多个购物车项会批量处理，提高性能
2. **缓存使用**: 商品信息、地址信息等会使用缓存
3. **异步处理**: 订单创建成功后，库存扣减等操作异步处理
4. **事务保证**: 使用数据库事务确保数据一致性

### 10.3 安全考虑

1. **用户认证**: 必须通过JWT认证
2. **权限校验**: 只能操作自己的购物车和地址
3. **参数校验**: 严格校验所有输入参数
4. **防重复提交**: 建议前端实现防重复提交机制

### 10.4 扩展功能

1. **定时订单**: 支持预约未来时间的订单
2. **团购订单**: 支持多人拼团下单
3. **积分抵扣**: 支持使用积分抵扣部分金额
4. **分期支付**: 支持信用卡分期支付
5. **配送保险**: 支持购买配送保险

### 10.5 监控和日志

系统会记录详细的操作日志：
- 订单创建请求日志
- 库存校验日志
- 支付处理日志
- 配送分配日志
- 错误异常日志

这些日志用于问题排查、性能监控和业务分析。

---

## 附录

### A. 状态码对照表

#### A.1 订单状态

| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| 10 | 待支付 | 订单已创建，等待用户支付 |
| 20 | 已支付 | 用户已支付，等待商家接单 |
| 30 | 处理中 | 商家已接单，正在准备餐食 |
| 40 | 配送中 | 餐食已准备完成，正在配送 |
| 50 | 已完成 | 订单已完成 |
| 60 | 已取消 | 订单已取消 |
| 70 | 已退款 | 订单已退款 |

#### A.2 支付状态

| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 未支付 | 等待用户支付 |
| 1 | 已支付 | 支付成功 |
| 2 | 支付失败 | 支付失败 |
| 3 | 已退款 | 已退款 |

#### A.3 配送状态

| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 等待配送 | 等待分配配送员 |
| 1 | 正在取餐 | 配送员正在取餐 |
| 2 | 配送中 | 正在配送途中 |
| 3 | 已送达 | 已送达客户 |
| 4 | 已取消 | 配送已取消 |

### B. 测试用例

#### B.1 正常流程测试

```bash
# 1. 添加商品到购物车
curl -X POST http://localhost:8181/api/takeout/cart/add \
  -H 'Authorization: Bearer {token}' \
  -d '{"foodID": 1001, "quantity": 2}'

# 2. 创建订单
curl -X POST http://localhost:8181/api/takeout/orders/create \
  -H 'Authorization: Bearer {token}' \
  -d '{"takeoutAddressID": 1001, "paymentMethod": "wechat", "cartItemIDs": [3001]}'

# 3. 支付订单
curl -X POST http://localhost:8181/api/takeout/orders/pay \
  -H 'Authorization: Bearer {token}' \
  -d '{"orderID": 10001, "paymentMethod": 1}'
```

#### B.2 异常情况测试

```bash
# 测试无效地址
curl -X POST http://localhost:8181/api/takeout/orders/create \
  -H 'Authorization: Bearer {token}' \
  -d '{"takeoutAddressID": 0, "paymentMethod": "wechat", "cartItemIDs": [3001]}'

# 测试空购物车
curl -X POST http://localhost:8181/api/takeout/orders/create \
  -H 'Authorization: Bearer {token}' \
  -d '{"takeoutAddressID": 1001, "paymentMethod": "wechat", "cartItemIDs": []}'

# 测试无效支付方式
curl -X POST http://localhost:8181/api/takeout/orders/create \
  -H 'Authorization: Bearer {token}' \
  -d '{"takeoutAddressID": 1001, "paymentMethod": "invalid", "cartItemIDs": [3001]}'
```

## 9. 前端开发指导

### 9.1 多商家订单创建流程

#### 9.1.1 购物车数据分组

前端需要将购物车数据按商家进行分组，以下是推荐的实现方式：

```javascript
// 假设从购物车API获取的数据结构
const cartItems = [
  {
    id: 3001,
    merchantId: 2001,
    merchantName: "麦当劳",
    productId: 1001,
    quantity: 2,
    // ... 其他字段
  },
  {
    id: 3002,
    merchantId: 2001,
    merchantName: "麦当劳",
    productId: 1002,
    quantity: 1,
    // ... 其他字段
  },
  {
    id: 3003,
    merchantId: 2002,
    merchantName: "肯德基",
    productId: 2001,
    quantity: 3,
    // ... 其他字段
  }
];

// 按商家分组
function groupCartItemsByMerchant(cartItems) {
  const merchantGroups = {};
  
  cartItems.forEach(item => {
    if (!merchantGroups[item.merchantId]) {
      merchantGroups[item.merchantId] = {
        merchantId: item.merchantId,
        merchantName: item.merchantName,
        items: [],
        cartItemIds: []
      };
    }
    
    merchantGroups[item.merchantId].items.push(item);
    merchantGroups[item.merchantId].cartItemIds.push(item.id);
  });
  
  return Object.values(merchantGroups);
}

const merchantGroups = groupCartItemsByMerchant(cartItems);
```

#### 9.1.2 构建订单请求数据

```javascript
// 构建多商家订单请求
function buildCreateOrderRequest(merchantGroups, addressId, paymentMethod) {
  const merchantOrders = merchantGroups.map(group => ({
    merchantID: group.merchantId,
    cartItemIDs: group.cartItemIds,
    couponID: group.selectedCouponId || 0, // 用户为该商家选择的优惠券
    deliveryTime: group.deliveryTime || "", // 用户为该商家设置的配送时间
    remark: group.remark || "" // 用户对该商家的备注
  }));
  
  return {
    takeoutAddressID: addressId,
    paymentMethod: paymentMethod,
    merchantOrders: merchantOrders
  };
}

// 使用示例
const orderRequest = buildCreateOrderRequest(
  merchantGroups,
  1001, // 配送地址ID
  "wechat" // 支付方式
);
```

#### 9.1.3 前端UI设计建议

**订单确认页面布局：**

```html
<!-- 按商家分组显示 -->
<div class="order-confirmation">
  <div class="address-section">
    <!-- 配送地址选择 -->
  </div>
  
  <div class="merchant-orders">
    <div v-for="merchant in merchantGroups" :key="merchant.merchantId" class="merchant-section">
      <div class="merchant-header">
        <h3>{{ merchant.merchantName }}</h3>
      </div>
      
      <div class="merchant-items">
        <!-- 商品列表 -->
      </div>
      
      <div class="merchant-options">
        <!-- 该商家的优惠券选择 -->
        <div class="coupon-selection">
          <label>优惠券:</label>
          <select v-model="merchant.selectedCouponId">
            <option value="0">不使用优惠券</option>
            <option v-for="coupon in merchant.availableCoupons" :key="coupon.id" :value="coupon.id">
              {{ coupon.name }}
            </option>
          </select>
        </div>
        
        <!-- 该商家的配送时间 -->
        <div class="delivery-time">
          <label>配送时间:</label>
          <input type="datetime-local" v-model="merchant.deliveryTime" />
        </div>
        
        <!-- 该商家的备注 -->
        <div class="remark">
          <label>备注:</label>
          <textarea v-model="merchant.remark" placeholder="对该商家的特殊要求"></textarea>
        </div>
      </div>
    </div>
  </div>
  
  <div class="payment-section">
    <!-- 支付方式选择 -->
  </div>
  
  <div class="submit-section">
    <button @click="createOrder">提交订单</button>
  </div>
</div>
```

### 9.2 优惠券处理

#### 9.2.1 获取商家优惠券

```javascript
// 为每个商家获取可用优惠券
async function loadMerchantCoupons(merchantGroups) {
  for (const merchant of merchantGroups) {
    try {
      const response = await fetch(`/api/coupons/merchant/${merchant.merchantId}`);
      const coupons = await response.json();
      merchant.availableCoupons = coupons.data || [];
    } catch (error) {
      console.error(`获取商家${merchant.merchantId}优惠券失败:`, error);
      merchant.availableCoupons = [];
    }
  }
}
```

#### 9.2.2 优惠券选择逻辑

```javascript
// 优惠券选择处理
function handleCouponSelection(merchantId, couponId) {
  const merchant = merchantGroups.find(m => m.merchantId === merchantId);
  if (merchant) {
    merchant.selectedCouponId = couponId;
    // 重新计算该商家的订单金额
    calculateMerchantOrderAmount(merchant);
  }
}

// 计算商家订单金额（包含优惠券折扣）
function calculateMerchantOrderAmount(merchant) {
  let subtotal = merchant.items.reduce((sum, item) => {
    return sum + (item.price * item.quantity);
  }, 0);
  
  // 应用优惠券折扣
  if (merchant.selectedCouponId) {
    const coupon = merchant.availableCoupons.find(c => c.id === merchant.selectedCouponId);
    if (coupon) {
      subtotal = applyCouponDiscount(subtotal, coupon);
    }
  }
  
  merchant.finalAmount = subtotal;
  return subtotal;
}
```

### 9.3 错误处理

#### 9.3.1 常见错误处理

```javascript
// 订单创建错误处理
async function createOrder() {
  try {
    const orderRequest = buildCreateOrderRequest(
      merchantGroups,
      selectedAddressId,
      selectedPaymentMethod
    );
    
    const response = await fetch('/api/takeout/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(orderRequest)
    });
    
    const result = await response.json();
    
    if (response.ok) {
      // 订单创建成功
      handleOrderSuccess(result.data);
    } else {
      // 处理业务错误
      handleOrderError(result);
    }
  } catch (error) {
    // 处理网络错误
    console.error('订单创建失败:', error);
    showErrorMessage('网络错误，请稍后重试');
  }
}

// 业务错误处理
function handleOrderError(result) {
  switch (result.code) {
    case 40001:
      showErrorMessage('配送地址无效');
      break;
    case 40002:
      showErrorMessage('购物车商品已失效，请重新选择');
      break;
    case 40003:
      showErrorMessage('优惠券不可用');
      break;
    case 40004:
      showErrorMessage('商家暂停营业');
      break;
    default:
      showErrorMessage(result.message || '订单创建失败');
  }
}
```

### 9.4 兼容性处理

#### 9.4.1 向后兼容

如果需要支持旧版API，可以提供一个转换函数：

```javascript
// 将新版请求转换为旧版格式（用于兼容）
function convertToLegacyFormat(newRequest) {
  // 如果只有一个商家，可以转换为旧版格式
  if (newRequest.merchantOrders.length === 1) {
    const merchantOrder = newRequest.merchantOrders[0];
    return {
      takeoutAddressID: newRequest.takeoutAddressID,
      deliveryTime: merchantOrder.deliveryTime,
      remark: merchantOrder.remark,
      paymentMethod: newRequest.paymentMethod,
      couponID: merchantOrder.couponID,
      cartItemIDs: merchantOrder.cartItemIDs
    };
  }
  
  throw new Error('多商家订单无法转换为旧版格式');
}
```

### 9.5 最佳实践

1. **数据验证**: 在提交前验证所有必填字段
2. **用户体验**: 为每个商家提供独立的配置选项
3. **错误提示**: 提供清晰的错误信息和解决建议
4. **性能优化**: 合理使用缓存，避免重复请求
5. **状态管理**: 使用状态管理工具（如Vuex、Redux）管理复杂的订单状态

---

*文档版本: v2.0*  
*最后更新: 2025-05-21*  
*维护者: 外卖系统开发团队*