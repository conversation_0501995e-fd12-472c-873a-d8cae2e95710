# 跑腿组件API使用示例

## 1. 跑腿员相关API示例

### 1.1 注册跑腿员

**请求**：

```http
POST /v1/runner/register
Content-Type: application/json

{
  "real_name": "张三",
  "id_card_number": "330101199001011234",
  "id_card_front_pic": "https://example.com/front.jpg",
  "id_card_back_pic": "https://example.com/back.jpg",
  "face_pic": "https://example.com/face.jpg",
  "mobile": "13800138000",
  "area_codes": "330100,330106",
  "service_radius": 5.0
}
```

**响应**：

```json
{
  "code": 200,
  "message": "申请提交成功，请等待审核",
  "data": null
}
```

### 1.2 获取跑腿员信息

**请求**：

```http
GET /v1/runner/info
```

**响应**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 10001,
    "real_name": "张三",
    "mobile": "138****8000",
    "status": 1,
    "current_location": "杭州市西湖区文三西路",
    "is_online": true,
    "working_status": 1,
    "score": 4.8,
    "order_count": 156,
    "success_count": 150,
    "balance": 1520.50,
    "join_time": "2023-01-15T10:00:00Z"
  }
}
```

### 1.3 更新跑腿员状态

**请求**：

```http
PUT /v1/runner/status
Content-Type: application/json

{
  "is_online": true,
  "working_status": 1
}
```

**响应**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 1.4 更新跑腿员位置

**请求**：

```http
PUT /v1/runner/location
Content-Type: application/json

{
  "latitude": 30.259924,
  "longitude": 120.219375,
  "location": "杭州市西湖区文三西路"
}
```

**响应**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 1.5 查询附近跑腿员

**请求**：

```http
POST /v1/runner/nearby
Content-Type: application/json

{
  "latitude": 30.259924,
  "longitude": 120.219375,
  "radius": 3.0,
  "limit": 10
}
```

**响应**：

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 10001,
      "real_name": "张*",
      "distance": 0.3,
      "score": 4.8,
      "latitude": 30.259100,
      "longitude": 120.220100
    },
    {
      "id": 10005,
      "real_name": "李*",
      "distance": 0.8,
      "score": 4.6,
      "latitude": 30.261200,
      "longitude": 120.222500
    }
  ]
}
```

## 2. 跑腿订单相关API示例

### 2.1 创建跑腿订单

**请求**：

```http
POST /v1/runner-order
Content-Type: application/json

{
  "pickup_name": "王五",
  "pickup_mobile": "13900139000",
  "pickup_address": "杭州市西湖区西湖银泰大型超市",
  "pickup_latitude": 30.259924,
  "pickup_longitude": 120.219375,
  "delivery_name": "赵六",
  "delivery_mobile": "13700137000",
  "delivery_address": "杭州市上城区平海路128号",
  "delivery_latitude": 30.265666,
  "delivery_longitude": 120.235555,
  "goods_type": 1,
  "goods_weight": 2.5,
  "goods_remark": "小心轻放，易碎",
  "expected_pickup_time": "2024-05-14T15:30:00Z",
  "expected_delivery_time": "2024-05-14T16:30:00Z"
}
```

**响应**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 20001,
    "order_no": "RO202405141530001",
    "status": 0,
    "delivery_fee": 15.00,
    "total_amount": 15.00,
    "create_time": "2024-05-14T14:25:33Z",
    "payment_deadline": "2024-05-14T14:40:33Z"
  }
}
```

### 2.2 获取订单详情

**请求**：

```http
GET /v1/runner-order/20001
```

**响应**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 20001,
    "order_no": "RO202405141530001",
    "user_id": 5001,
    "runner_id": 10001,
    "status": 2,
    "payment_status": 1,
    "pickup_name": "王五",
    "pickup_mobile": "139****9000",
    "pickup_address": "杭州市西湖区西湖银泰大型超市",
    "delivery_name": "赵六",
    "delivery_mobile": "137****7000",
    "delivery_address": "杭州市上城区平海路128号",
    "goods_type": 1,
    "goods_weight": 2.5,
    "goods_remark": "小心轻放，易碎",
    "delivery_distance": 3.2,
    "delivery_fee": 15.00,
    "total_amount": 15.00,
    "expected_pickup_time": "2024-05-14T15:30:00Z",
    "expected_delivery_time": "2024-05-14T16:30:00Z",
    "create_time": "2024-05-14T14:25:33Z",
    "accept_time": "2024-05-14T14:30:15Z",
    "pickup_time": "2024-05-14T15:25:42Z",
    "runner_info": {
      "id": 10001,
      "real_name": "张*",
      "mobile": "138****8000",
      "score": 4.8
    }
  }
}
```

### 2.3 接单

**请求**：

```http
POST /v1/runner-order/accept
Content-Type: application/json

{
  "order_id": 20001
}
```

**响应**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 2.4 取货

**请求**：

```http
POST /v1/runner-order/pickup
Content-Type: application/json

{
  "order_id": 20001,
  "pickup_code": "123456"
}
```

**响应**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 2.5 开始配送

**请求**：

```http
POST /v1/runner-order/start-delivery
Content-Type: application/json

{
  "order_id": 20001
}
```

**响应**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 2.6 完成订单

**请求**：

```http
POST /v1/runner-order/complete
Content-Type: application/json

{
  "order_id": 20001,
  "delivery_code": "654321"
}
```

**响应**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 2.6 评价订单

**请求**：

```http
POST /v1/runner-order/user/rate
Content-Type: application/json

{
  "order_id": 20001,
  "score": 5,
  "comment": "送货很快，服务态度好"
}
```

**响应**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

## 3. 跑腿员收入相关API示例

### 3.1 获取收入统计

**请求**：

```http
GET /v1/runner/income
```

**响应**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "balance": 1520.50,
    "total_income": 8350.25,
    "total_withdrawn": 6829.75,
    "today_income": 120.00,
    "yesterday_income": 135.50,
    "this_week_income": 645.75,
    "this_month_income": 2150.25
  }
}
```

### 3.2 获取收入记录

**请求**：

```http
GET /v1/runner/income/logs?page=1&pageSize=10
```

**响应**：

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 30001,
      "order_id": 20050,
      "order_no": "RO202405141700003",
      "amount": 15.00,
      "type": 1,
      "status": 1,
      "create_time": "2024-05-14T17:45:22Z"
    },
    {
      "id": 30000,
      "order_id": 20045,
      "order_no": "*****************",
      "amount": 12.50,
      "type": 1,
      "status": 1,
      "create_time": "2024-05-14T17:10:15Z"
    }
  ],
  "pagination": {
    "total": 156,
    "page": 1,
    "page_size": 10,
    "total_pages": 16
  }
}
```

### 3.3 申请提现

**请求**：

```http
POST /v1/runner/withdrawal
Content-Type: application/json

{
  "amount": 1000.00,
  "withdraw_type": 1,
  "account_name": "张三",
  "account_no": "****************",
  "bank_name": "中国建设银行"
}
```

**响应**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 40001,
    "withdraw_no": "W202405141823001",
    "amount": 1000.00,
    "status": 0,
    "create_time": "2024-05-14T18:23:45Z"
  }
}
```

## 4. 参数说明

### 4.1 跑腿员状态(status)

- 0: 待审核
- 1: 审核通过
- 2: 审核拒绝
- 3: 暂停服务
- 4: 黑名单

### 4.2 跑腿员工作状态(working_status)

- 0: 休息中
- 1: 接单中
- 2: 配送中

### 4.3 订单状态(status)

- 0: 待支付
- 1: 待接单
- 2: 待取货
- 3: 待配送
- 4: 待确认
- 5: 已完成
- 6: 已取消

### 4.4 支付状态(payment_status)

- 0: 未支付
- 1: 已支付
- 2: 已退款

### 4.5 商品类型(goods_type)

- 1: 普通物品
- 2: 餐饮美食
- 3: 生鲜食品
- 4: 文件文书
- 5: 数码产品
- 6: 其他

### 4.6 收入类型(type)

- 1: 订单收入
- 2: 补贴收入
- 3: 奖励收入

### 4.7 提现状态(status)

- 0: 处理中
- 1: 处理成功
- 2: 处理失败

### 4.8 提现类型(withdraw_type)

- 1: 银行卡
- 2: 支付宝
- 3: 微信
