# 跑腿员收入管理系统后端API需求文档

## 概述

本文档详细说明跑腿员收入管理页面(RunnerIncome.vue)所需的全部API接口。该页面用于展示跑腿员收入统计、账户余额信息、收入明细和提现记录，并支持提现申请功能。

## API接口列表

### 1. 获取收入统计数据

**接口信息:**
- **URL路径:** `/v1/runner/secured/income-stats`
- **请求方法:** `GET`
- **鉴权要求:** 需要跑腿员身份认证

**请求参数:** 无

**响应数据:**
```json
{
  "today": 150.5,    // 今日收入(元)
  "week": 1050.75,   // 本周收入(元)
  "month": 4500.25,  // 本月收入(元)
  "total": 12500.8   // 累计收入(元)
}
```

**业务逻辑:**
- 统计当前登录跑腿员的今日、本周、本月和累计收入金额
- 今日指当天0点到当前时间
- 本周指本周一到当前时间
- 本月指本月1日到当前时间
- 累计收入指所有已结算的历史收入总和

### 2. 获取账户余额信息

**接口信息:**
- **URL路径:** `/v1/runner/secured/balance`
- **请求方法:** `GET`
- **鉴权要求:** 需要跑腿员身份认证

**请求参数:** 无

**响应数据:**
```json
{
  "available": 1250.5,   // 可提现余额(元)
  "frozen": 320.75,      // 冻结金额(元)
  "withdrawing": 500.0   // 提现中金额(元)
}
```

**业务逻辑:**
- 获取当前登录跑腿员的账户余额状态
- 可提现余额指跑腿员当前可以申请提现的金额
- 冻结金额指由于订单未完成等原因暂时不可提现的金额
- 提现中金额指已申请提现但尚未处理完成的金额

### 3. 获取收入明细列表

**接口信息:**
- **URL路径:** `/v1/runner/secured/income-list`
- **请求方法:** `GET`
- **鉴权要求:** 需要跑腿员身份认证

**请求参数:**
| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| current | number | 是 | 当前页码，从1开始 |
| pageSize | number | 是 | 每页记录数 |
| type | string | 否 | 收入类型筛选，如'order_settlement'(订单结算)、'bonus'(奖励金)等 |
| status | string | 否 | 状态筛选，如'completed'(已完成)、'pending'(处理中)等 |
| startTime | string | 否 | 开始时间，格式：YYYY-MM-DD HH:MM:SS |
| endTime | string | 否 | 结束时间，格式：YYYY-MM-DD HH:MM:SS |
| orderNo | string | 否 | 订单号模糊搜索 |

**响应数据:**
```json
{
  "records": [
    {
      "id": "12345",
      "type": "order_settlement",  // 收入类型
      "orderNo": "ORD20250708001", // 订单号(可能为空)
      "orderTime": "2025-07-07 15:30:25", // 订单时间(可能为空)
      "amount": 25.5,              // 收入金额(可正可负)
      "status": "completed",       // 状态
      "createTime": "2025-07-07 16:30:25", // 创建时间
      "remark": "订单配送费"        // 备注(可能为空)
    }
    // 更多记录...
  ],
  "total": 158  // 总记录数
}
```

**业务逻辑:**
- 分页查询当前登录跑腿员的收入明细记录
- 支持按收入类型、状态、时间范围和订单号进行筛选
- 收入类型包括但不限于：订单结算(order_settlement)、奖励金(bonus)、补贴(subsidy)等
- 状态包括但不限于：已完成(completed)、处理中(pending)等
- 结果按创建时间降序排列

### 4. 获取提现记录列表

**接口信息:**
- **URL路径:** `/v1/runner/secured/withdraw-list`
- **请求方法:** `GET`
- **鉴权要求:** 需要跑腿员身份认证

**请求参数:**
| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| current | number | 是 | 当前页码，从1开始 |
| pageSize | number | 是 | 每页记录数 |

**响应数据:**
```json
{
  "records": [
    {
      "id": "67890",
      "withdrawNo": "WD20250708001", // 提现单号
      "amount": 500.0,               // 提现金额(元)
      "fee": 2.5,                    // 手续费(元)
      "actualAmount": 497.5,         // 实际到账金额(元)
      "method": "alipay",            // 提现方式
      "account": "<EMAIL>", // 收款账户
      "accountName": "张三",         // 收款人姓名(可能为空)
      "status": "processing",        // 状态
      "createTime": "2025-07-05 10:20:30", // 申请时间
      "completeTime": null,          // 完成时间(可能为空)
      "remark": null                 // 备注(可能为空)
    }
    // 更多记录...
  ],
  "total": 15  // 总记录数
}
```

**业务逻辑:**
- 分页查询当前登录跑腿员的提现申请记录
- 提现方式包括但不限于：支付宝(alipay)、银行转账(bank_transfer)等
- 状态包括但不限于：处理中(processing)、已完成(completed)、失败(failed)等
- 结果按申请时间降序排列

### 5. 提交提现申请

**接口信息:**
- **URL路径:** `/v1/runner/secured/withdraw`
- **请求方法:** `POST`
- **鉴权要求:** 需要跑腿员身份认证

**请求参数:**
```json
{
  "amount": 500.0,              // 提现金额(元)
  "method": "alipay",           // 提现方式：alipay(支付宝)、bank_transfer(银行转账)等
  "account": "<EMAIL>", // 收款账户
  "account_name": "张三"         // 收款人姓名
}
```

**响应数据:**
```json
{
  "success": true,
  "message": "提现申请提交成功",
  "data": {
    "withdrawNo": "WD20250708002" // 提现单号
  }
}
```

**业务逻辑:**
- 验证提现金额是否大于0且不超过可提现余额
- 验证提现方式是否有效
- 验证账户信息是否完整
- 创建提现申请记录
- 更新账户余额：减少可提现余额，增加提现中金额
- 返回生成的提现单号

## 数据模型定义

### 1. 收入统计(IncomeStats)
```typescript
export interface IncomeStats {
  today: number;   // 今日收入
  week: number;    // 本周收入
  month: number;   // 本月收入
  total: number;   // 累计收入
}
```

### 2. 账户余额信息(BalanceInfo)
```typescript
export interface BalanceInfo {
  available: number;     // 可提现余额
  frozen: number;        // 冻结金额
  withdrawing: number;   // 提现中金额
}
```

### 3. 收入明细记录(IncomeRecord)
```typescript
export interface IncomeRecord {
  id: string | number;
  type: string;           // 收入类型 (e.g., 'order_settlement', 'bonus')
  orderNo?: string;       // 订单号 (可选)
  orderTime?: string;     // 订单时间 (可选)
  amount: number;         // 收入金额 (可正可负)
  status: string;         // 状态 (e.g., 'completed', 'pending')
  createTime: string;     // 创建时间
  remark?: string;        // 备注 (可选)
}
```

### 4. 提现记录(WithdrawRecord)
```typescript
export interface WithdrawRecord {
  id: string | number;
  withdrawNo: string;     // 提现单号
  amount: number;         // 提现金额
  fee: number;            // 手续费
  actualAmount: number;   // 实际到账金额
  method: string;         // 提现方式 (e.g., 'alipay', 'bank_transfer')
  account: string;        // 收款账户
  accountName?: string;   // 收款人姓名 (可选)
  status: string;         // 状态 (e.g., 'processing', 'completed', 'failed')
  createTime: string;     // 申请时间
  completeTime?: string;  // 完成时间 (可选)
  remark?: string;        // 备注 (可选)
}
```

## 安全与性能考虑

1. **接口安全性**
   - 所有接口均需验证用户身份并确认是跑腿员角色
   - 提现操作应考虑增加二次验证机制（如短信验证码）

2. **性能优化**
   - 收入明细和提现记录接口应支持分页查询
   - 考虑对收入统计数据进行缓存，减轻数据库压力

3. **业务限制**
   - 提现金额应有最小限额(如10元)和最大限额(如50000元)
   - 应限制提现申请频率(如24小时内最多3次)

## 附录：状态码定义

建议使用以下状态码标识收入明细和提现记录的状态：

### 收入明细状态(status)
- `completed`: 已完成，表示收入已结算到账户
- `pending`: 处理中，表示收入正在处理但尚未到账
- `canceled`: 已取消，表示该笔收入已被取消

### 提现状态(status)
- `processing`: 处理中，表示提现申请正在处理
- `completed`: 已完成，表示提现已成功转入指定账户
- `failed`: 失败，表示提现申请处理失败
- `rejected`: 已拒绝，表示提现申请被平台拒绝
