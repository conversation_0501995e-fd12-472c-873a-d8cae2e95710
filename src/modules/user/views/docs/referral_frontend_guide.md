# 分销模块前端设计指导

## 概述

本文档为分销模块的前端开发提供详细的设计指导，包括页面结构、组件设计、交互流程、状态管理等方面的建议。帮助前端开发者快速构建用户友好的分销功能界面。

## 页面结构设计

### 1. 分销中心主页

**页面路径**: `/referral/dashboard`

**页面功能**: 分销数据概览和快速操作入口

**布局结构**:
```
┌─────────────────────────────────────────┐
│                页面标题                  │
├─────────────────────────────────────────┤
│  统计卡片区域（4个卡片横向排列）          │
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐      │
│  │总推荐│ │今日 │ │本月 │ │累计 │      │
│  │用户数│ │佣金 │ │佣金 │ │佣金 │      │
│  └─────┘ └─────┘ └─────┘ └─────┘      │
├─────────────────────────────────────────┤
│              快速操作区域                │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐  │
│  │ 邀请好友 │ │ 推荐记录 │ │ 佣金明细 │  │
│  └─────────┘ └─────────┘ └─────────┘  │
├─────────────────────────────────────────┤
│              分销层级图表                │
│        （树状图或层级展示）              │
└─────────────────────────────────────────┘
```

### 2. 推荐用户列表页

**页面路径**: `/referral/users`

**页面功能**: 查看和管理推荐的用户

**布局结构**:
```
┌─────────────────────────────────────────┐
│  筛选条件栏                              │
│  [等级筛选] [时间筛选] [搜索框] [筛选按钮] │
├─────────────────────────────────────────┤
│              用户列表                    │
│  ┌─────────────────────────────────────┐ │
│  │ 头像 | 用户名 | 等级 | 推荐时间 | 佣金 │ │
│  ├─────────────────────────────────────┤ │
│  │ [用户1数据]                         │ │
│  │ [用户2数据]                         │ │
│  │ [用户3数据]                         │ │
│  └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│              分页组件                    │
└─────────────────────────────────────────┘
```

### 3. 邀请好友页

**页面路径**: `/referral/invite`

**页面功能**: 生成邀请链接和二维码

**布局结构**:
```
┌─────────────────────────────────────────┐
│              邀请说明                    │
│        （分销规则和奖励说明）            │
├─────────────────────────────────────────┤
│              邀请方式                    │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐  │
│  │ 邀请链接 │ │ 二维码  │ │ 社交分享 │  │
│  │ [复制]  │ │ [下载]  │ │ [分享]  │  │
│  └─────────┘ └─────────┘ └─────────┘  │
├─────────────────────────────────────────┤
│              邀请记录                    │
│        （最近的邀请活动）                │
└─────────────────────────────────────────┘
```

## 组件设计

### 1. 统计卡片组件 (StatCard)

```vue
<template>
  <div class="stat-card">
    <div class="stat-icon">
      <i :class="iconClass"></i>
    </div>
    <div class="stat-content">
      <div class="stat-value">{{ formattedValue }}</div>
      <div class="stat-label">{{ label }}</div>
      <div class="stat-trend" :class="trendClass">
        <i :class="trendIcon"></i>
        <span>{{ trendText }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StatCard',
  props: {
    value: {
      type: [Number, String],
      required: true
    },
    label: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      default: 'users'
    },
    trend: {
      type: Object,
      default: () => ({ value: 0, type: 'up' })
    },
    format: {
      type: String,
      default: 'number' // number, currency, percentage
    }
  },
  computed: {
    formattedValue() {
      if (this.format === 'currency') {
        return `¥${this.value.toFixed(2)}`;
      } else if (this.format === 'percentage') {
        return `${this.value}%`;
      }
      return this.value.toLocaleString();
    },
    iconClass() {
      return `icon-${this.icon}`;
    },
    trendClass() {
      return `trend-${this.trend.type}`;
    },
    trendIcon() {
      return this.trend.type === 'up' ? 'icon-arrow-up' : 'icon-arrow-down';
    },
    trendText() {
      return `${this.trend.value}% 较昨日`;
    }
  }
};
</script>
```

### 2. 用户列表组件 (UserList)

```vue
<template>
  <div class="user-list">
    <!-- 筛选条件 -->
    <div class="filter-bar">
      <el-select v-model="filters.level" placeholder="选择等级" clearable>
        <el-option label="一级推荐" :value="1"></el-option>
        <el-option label="二级推荐" :value="2"></el-option>
        <el-option label="三级推荐" :value="3"></el-option>
      </el-select>
      
      <el-date-picker
        v-model="filters.dateRange"
        type="daterange"
        placeholder="选择时间范围"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD">
      </el-date-picker>
      
      <el-input
        v-model="filters.keyword"
        placeholder="搜索用户名或手机号"
        clearable>
      </el-input>
      
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button @click="handleReset">重置</el-button>
    </div>
    
    <!-- 用户表格 -->
    <el-table :data="userList" v-loading="loading">
      <el-table-column label="用户信息" min-width="200">
        <template #default="{ row }">
          <div class="user-info">
            <el-avatar :src="row.user.avatar" :size="40">
              {{ row.user.nickname.charAt(0) }}
            </el-avatar>
            <div class="user-details">
              <div class="user-name">{{ row.user.nickname }}</div>
              <div class="user-mobile">{{ row.user.mobile }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="分销等级" prop="level" width="100">
        <template #default="{ row }">
          <el-tag :type="getLevelTagType(row.level)">
            {{ getLevelText(row.level) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="推荐时间" prop="referral_time" width="180">
        <template #default="{ row }">
          {{ formatDate(row.referral_time) }}
        </template>
      </el-table-column>
      
      <el-table-column label="累计佣金" prop="commission" width="120">
        <template #default="{ row }">
          <span class="commission-amount">¥{{ row.commission.toFixed(2) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" prop="status" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '有效' : '无效' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button type="text" @click="viewDetails(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
    </el-pagination>
  </div>
</template>

<script>
import { getReferrals } from '@/api/referral';

export default {
  name: 'UserList',
  data() {
    return {
      userList: [],
      loading: false,
      filters: {
        level: null,
        dateRange: null,
        keyword: ''
      },
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0
      }
    };
  },
  mounted() {
    this.loadUserList();
  },
  methods: {
    async loadUserList() {
      this.loading = true;
      try {
        const params = {
          page: this.pagination.page,
          page_size: this.pagination.pageSize,
          level: this.filters.level,
          only_direct: false
        };
        
        const response = await getReferrals(params);
        this.userList = response.list;
        this.pagination.total = response.total;
      } catch (error) {
        this.$message.error('加载用户列表失败');
      } finally {
        this.loading = false;
      }
    },
    
    handleSearch() {
      this.pagination.page = 1;
      this.loadUserList();
    },
    
    handleReset() {
      this.filters = {
        level: null,
        dateRange: null,
        keyword: ''
      };
      this.pagination.page = 1;
      this.loadUserList();
    },
    
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.page = 1;
      this.loadUserList();
    },
    
    handleCurrentChange(page) {
      this.pagination.page = page;
      this.loadUserList();
    },
    
    getLevelTagType(level) {
      const types = { 1: 'success', 2: 'warning', 3: 'info' };
      return types[level] || 'info';
    },
    
    getLevelText(level) {
      const texts = { 1: '一级', 2: '二级', 3: '三级' };
      return texts[level] || '未知';
    },
    
    formatDate(date) {
      return new Date(date).toLocaleString('zh-CN');
    },
    
    viewDetails(row) {
      // 查看用户详情逻辑
      this.$router.push(`/referral/users/${row.user.id}`);
    }
  }
};
</script>
```

### 3. 邀请组件 (InviteCard)

```vue
<template>
  <div class="invite-card">
    <div class="invite-header">
      <h3>邀请好友</h3>
      <p>邀请好友注册，获得丰厚佣金奖励</p>
    </div>
    
    <div class="invite-content">
      <!-- 邀请链接 -->
      <div class="invite-method">
        <h4>邀请链接</h4>
        <div class="link-container">
          <el-input v-model="inviteLink" readonly>
            <template #append>
              <el-button @click="copyLink">复制</el-button>
            </template>
          </el-input>
        </div>
      </div>
      
      <!-- 二维码 -->
      <div class="invite-method">
        <h4>邀请二维码</h4>
        <div class="qrcode-container">
          <canvas ref="qrcode" class="qrcode"></canvas>
          <div class="qrcode-actions">
            <el-button @click="downloadQRCode">下载二维码</el-button>
          </div>
        </div>
      </div>
      
      <!-- 社交分享 -->
      <div class="invite-method">
        <h4>社交分享</h4>
        <div class="share-buttons">
          <el-button type="success" @click="shareToWechat">
            <i class="icon-wechat"></i> 微信
          </el-button>
          <el-button type="primary" @click="shareToQQ">
            <i class="icon-qq"></i> QQ
          </el-button>
          <el-button type="info" @click="shareToWeibo">
            <i class="icon-weibo"></i> 微博
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 邀请规则 -->
    <div class="invite-rules">
      <h4>邀请规则</h4>
      <ul>
        <li>好友通过您的邀请链接注册成功，您将获得一级分销资格</li>
        <li>好友首次消费，您将获得订单金额5%的佣金</li>
        <li>好友再次邀请其他用户，您将获得二级分销佣金3%</li>
        <li>最多支持三级分销，三级佣金为1%</li>
      </ul>
    </div>
  </div>
</template>

<script>
import QRCode from 'qrcode';

export default {
  name: 'InviteCard',
  data() {
    return {
      inviteLink: '',
      userId: null
    };
  },
  mounted() {
    this.generateInviteLink();
    this.generateQRCode();
  },
  methods: {
    generateInviteLink() {
      // 获取当前用户ID
      this.userId = this.$store.getters.userId;
      // 生成邀请链接
      this.inviteLink = `${window.location.origin}/register?referrer=${this.userId}`;
    },
    
    async generateQRCode() {
      try {
        await QRCode.toCanvas(this.$refs.qrcode, this.inviteLink, {
          width: 200,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });
      } catch (error) {
        console.error('生成二维码失败:', error);
      }
    },
    
    async copyLink() {
      try {
        await navigator.clipboard.writeText(this.inviteLink);
        this.$message.success('邀请链接已复制到剪贴板');
      } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = this.inviteLink;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        this.$message.success('邀请链接已复制到剪贴板');
      }
    },
    
    downloadQRCode() {
      const canvas = this.$refs.qrcode;
      const link = document.createElement('a');
      link.download = 'invite-qrcode.png';
      link.href = canvas.toDataURL();
      link.click();
      this.$message.success('二维码已下载');
    },
    
    shareToWechat() {
      // 微信分享逻辑
      if (navigator.share) {
        navigator.share({
          title: '邀请您加入我们的平台',
          text: '通过我的邀请链接注册，我们都能获得奖励！',
          url: this.inviteLink
        });
      } else {
        this.copyLink();
        this.$message.info('请在微信中粘贴分享链接');
      }
    },
    
    shareToQQ() {
      const shareUrl = `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(this.inviteLink)}&title=${encodeURIComponent('邀请您加入我们的平台')}`;
      window.open(shareUrl, '_blank');
    },
    
    shareToWeibo() {
      const shareUrl = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(this.inviteLink)}&title=${encodeURIComponent('邀请您加入我们的平台')}`;
      window.open(shareUrl, '_blank');
    }
  }
};
</script>
```

## 状态管理

### Vuex Store 设计

```javascript
// store/modules/referral.js
const state = {
  statistics: {
    totalReferrals: 0,
    level1Referrals: 0,
    level2Referrals: 0,
    level3Referrals: 0,
    totalCommission: 0,
    todayCommission: 0,
    thisMonthCommission: 0,
    thisYearCommission: 0
  },
  referralList: [],
  referrer: null,
  loading: false
};

const mutations = {
  SET_STATISTICS(state, statistics) {
    state.statistics = statistics;
  },
  
  SET_REFERRAL_LIST(state, list) {
    state.referralList = list;
  },
  
  SET_REFERRER(state, referrer) {
    state.referrer = referrer;
  },
  
  SET_LOADING(state, loading) {
    state.loading = loading;
  },
  
  ADD_REFERRAL(state, referral) {
    state.referralList.unshift(referral);
    state.statistics.totalReferrals++;
    if (referral.level === 1) {
      state.statistics.level1Referrals++;
    }
  }
};

const actions = {
  async fetchStatistics({ commit }) {
    commit('SET_LOADING', true);
    try {
      const response = await getReferralStatistics();
      commit('SET_STATISTICS', response);
    } catch (error) {
      throw error;
    } finally {
      commit('SET_LOADING', false);
    }
  },
  
  async fetchReferralList({ commit }, params) {
    commit('SET_LOADING', true);
    try {
      const response = await getReferrals(params);
      commit('SET_REFERRAL_LIST', response.list);
      return response;
    } catch (error) {
      throw error;
    } finally {
      commit('SET_LOADING', false);
    }
  },
  
  async fetchReferrer({ commit }) {
    try {
      const response = await getReferrer();
      commit('SET_REFERRER', response);
    } catch (error) {
      throw error;
    }
  },
  
  async createReferral({ commit }, params) {
    try {
      const response = await createReferral(params);
      // 可以选择重新获取列表或直接添加到状态中
      return response;
    } catch (error) {
      throw error;
    }
  }
};

const getters = {
  hasReferrer: state => state.referrer !== null,
  
  totalCommissionFormatted: state => {
    return `¥${state.statistics.totalCommission.toFixed(2)}`;
  },
  
  todayCommissionFormatted: state => {
    return `¥${state.statistics.todayCommission.toFixed(2)}`;
  },
  
  referralsByLevel: state => level => {
    return state.referralList.filter(item => item.level === level);
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
```

## 路由配置

```javascript
// router/modules/referral.js
export default {
  path: '/referral',
  component: () => import('@/layouts/DefaultLayout.vue'),
  meta: {
    title: '分销中心',
    requiresAuth: true
  },
  children: [
    {
      path: '',
      redirect: '/referral/dashboard'
    },
    {
      path: 'dashboard',
      name: 'ReferralDashboard',
      component: () => import('@/views/referral/Dashboard.vue'),
      meta: {
        title: '分销概览'
      }
    },
    {
      path: 'users',
      name: 'ReferralUsers',
      component: () => import('@/views/referral/Users.vue'),
      meta: {
        title: '推荐用户'
      }
    },
    {
      path: 'users/:id',
      name: 'ReferralUserDetail',
      component: () => import('@/views/referral/UserDetail.vue'),
      meta: {
        title: '用户详情'
      }
    },
    {
      path: 'invite',
      name: 'ReferralInvite',
      component: () => import('@/views/referral/Invite.vue'),
      meta: {
        title: '邀请好友'
      }
    },
    {
      path: 'commission',
      name: 'ReferralCommission',
      component: () => import('@/views/referral/Commission.vue'),
      meta: {
        title: '佣金明细'
      }
    }
  ]
};
```

## 样式设计指南

### 1. 色彩规范

```scss
// 分销模块专用色彩
$referral-primary: #1890ff;     // 主色调
$referral-success: #52c41a;     // 成功色（佣金、收益）
$referral-warning: #faad14;     // 警告色（待处理）
$referral-error: #f5222d;       // 错误色（失效）
$referral-info: #722ed1;        // 信息色（等级标识）

// 等级色彩
$level-1-color: #52c41a;        // 一级分销
$level-2-color: #faad14;        // 二级分销
$level-3-color: #1890ff;        // 三级分销

// 背景色
$bg-light: #fafafa;
$bg-card: #ffffff;
$bg-hover: #f5f5f5;
```

### 2. 组件样式

```scss
// 统计卡片样式
.stat-card {
  background: $bg-card;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  
  .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    
    &.icon-users {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    &.icon-money {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      color: white;
    }
  }
  
  .stat-value {
    font-size: 28px;
    font-weight: bold;
    color: #262626;
    margin-bottom: 8px;
  }
  
  .stat-label {
    font-size: 14px;
    color: #8c8c8c;
    margin-bottom: 12px;
  }
  
  .stat-trend {
    font-size: 12px;
    display: flex;
    align-items: center;
    
    &.trend-up {
      color: $referral-success;
    }
    
    &.trend-down {
      color: $referral-error;
    }
    
    i {
      margin-right: 4px;
    }
  }
}

// 用户信息样式
.user-info {
  display: flex;
  align-items: center;
  
  .user-details {
    margin-left: 12px;
    
    .user-name {
      font-weight: 500;
      color: #262626;
      margin-bottom: 4px;
    }
    
    .user-mobile {
      font-size: 12px;
      color: #8c8c8c;
    }
  }
}

// 佣金金额样式
.commission-amount {
  font-weight: 600;
  color: $referral-success;
}

// 等级标签样式
.level-tag {
  &.level-1 {
    background: $level-1-color;
    color: white;
  }
  
  &.level-2 {
    background: $level-2-color;
    color: white;
  }
  
  &.level-3 {
    background: $level-3-color;
    color: white;
  }
}
```

## 交互设计

### 1. 加载状态
- 使用骨架屏提升用户体验
- 数据加载时显示适当的loading动画
- 避免页面空白状态

### 2. 错误处理
- 网络错误时显示重试按钮
- 数据为空时显示友好的空状态页面
- 操作失败时给出明确的错误提示

### 3. 响应式设计
- 移动端优先的设计理念
- 统计卡片在小屏幕上垂直排列
- 表格在移动端使用卡片式布局

### 4. 动画效果
- 页面切换使用平滑过渡动画
- 数据更新时使用淡入淡出效果
- 按钮点击提供视觉反馈

## 性能优化建议

### 1. 数据缓存
- 使用Vuex缓存统计数据
- 实现智能刷新机制
- 避免重复请求相同数据

### 2. 懒加载
- 路由级别的代码分割
- 图片懒加载
- 长列表虚拟滚动

### 3. 防抖节流
- 搜索输入防抖处理
- 按钮点击防重复提交
- 滚动事件节流处理

## 测试建议

### 1. 单元测试
- 组件渲染测试
- 方法功能测试
- 状态管理测试

### 2. 集成测试
- API接口测试
- 用户交互流程测试
- 错误场景测试

### 3. 端到端测试
- 完整业务流程测试
- 跨浏览器兼容性测试
- 移动端适配测试

## 部署注意事项

1. **环境变量配置**: 确保API地址在不同环境下正确配置
2. **CDN优化**: 静态资源使用CDN加速
3. **缓存策略**: 合理设置浏览器缓存策略
4. **监控告警**: 配置前端错误监控和性能监控
5. **SEO优化**: 分销页面的SEO优化，提高搜索引擎收录