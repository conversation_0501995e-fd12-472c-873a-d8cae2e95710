# 用户地址管理H5页面开发指导文档

## 1. 功能概述

用户地址管理页面(`Addresses.vue`)提供用户收货地址的完整管理功能，包括：

- 查看所有已保存的收货地址
- 添加新的收货地址
- 编辑现有收货地址
- 删除不需要的收货地址
- 设置默认收货地址
- 与社区地址系统集成，支持选择小区、楼栋、单元等多级地址

## 2. 页面结构与组件

### 2.1 主页面结构

页面主体采用响应式卡片布局，主要包含以下几个部分：

1. **地址列表区域**：
   - 添加新地址卡片
   - 已有地址列表卡片
   - 空状态提示

2. **加载状态**：
   - 使用骨架屏展示加载中状态

3. **地址编辑对话框**：
   - 表单区域
   - 底部操作按钮

### 2.2 主要组件

```
Addresses.vue
├── Element Plus 组件
│   ├── el-card
│   ├── el-dialog
│   ├── el-form
│   ├── el-input
│   ├── el-checkbox
│   ├── el-button
│   ├── el-tag
│   ├── el-empty
│   └── el-skeleton
└── 自定义组件
    └── CommunityAddressSelector.vue (地址选择器)
```

### 2.3 社区地址选择器

`CommunityAddressSelector.vue`是一个独立的级联选择器组件，提供小区-楼栋-单元的多级联动选择，并返回经纬度信息。

核心功能：
- 动态加载小区、楼栋、单元数据
- 支持搜索过滤
- 返回完整的地址信息对象（包含ID、名称、路径、经纬度）

## 3. 数据结构

### 3.1 地址数据结构

```typescript
interface UserAddress {
  id: string;
  userId: string;
  receiver_name: string;      // 收货人姓名
  receiver_mobile: string;    // 收货人手机
  phone: string;              // 电话号码
  province: string;           // 省
  city: string;               // 市
  district: string;           // 区/县
  detailed_address: string;   // 详细地址
  is_default: boolean;        // 是否为默认地址
  postal_code?: string;       // 邮政编码(可选)
  address_tag?: string;       // 地址标签(可选)
  location_longitude?: number; // 经度(可选)
  location_latitude?: number;  // 纬度(可选)
  createdAt: string;          // 创建时间
  updatedAt: string;          // 更新时间
}
```

### 3.2 社区地址数据结构

```typescript
interface SelectedAddressInfo {
  fullPath: string;           // 完整地址路径
  longitude: number | null;   // 经度
  latitude: number | null;    // 纬度
  communityId: number | null; // 小区ID
  buildingId: number | null;  // 楼栋ID
  unitId: number | null;      // 单元ID
}
```

## 4. 接口定义

地址管理页面使用的API接口位于`src/modules/user/api/address.ts`：

### 4.1 获取地址列表

```typescript
function getUserAddresses(): Promise<UserAddress[]> {
  return request({
    url: '/v1/user/secured/addresses',
    method: 'get'
  });
}
```

### 4.2 获取单个地址详情

```typescript
function getUserAddressById(id: string): Promise<UserAddress> {
  return request({
    url: `/v1/user/secured/addresses/${id}`,
    method: 'get'
  });
}
```

### 4.3 添加新地址

```typescript
function addUserAddress(data: Omit<UserAddress, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) {
  return request({
    url: '/v1/user/secured/addresses',
    method: 'post',
    data
  });
}
```

### 4.4 更新地址

```typescript
function updateUserAddress(id: string, data: Partial<UserAddress>) {
  return request({
    url: `/v1/user/secured/addresses/${id}`,
    method: 'put',
    data
  });
}
```

### 4.5 删除地址

```typescript
function deleteUserAddress(id: string) {
  return request({
    url: `/v1/user/secured/addresses/${id}`,
    method: 'delete'
  });
}
```

### 4.6 设置默认地址

```typescript
function setDefaultAddress(id: string) {
  return request({
    url: `/v1/user/secured/addresses/${id}/default`,
    method: 'put'
  });
}
```

### 4.7 地址选择器相关接口

地址选择器组件使用系统模块的地址接口：

```typescript
// 获取地址选项数据
const response = await get(`/v1/system/addresses/options`);

// 获取子级地址数据(动态加载)
const url = `${API_BASE_URL}/addresses/children`;
const response = await get(url, { params: { parentId: id } });
```

## 5. 核心业务逻辑

### 5.1 地址列表加载

页面初始化时通过`loadAddresses()`函数加载用户的地址列表：

```typescript
async function loadAddresses() {
  try {
    loading.value = true;
    addresses.value = await getUserAddresses();
  } catch (error) {
    console.error('获取地址列表失败:', error);
    ElMessage.error('获取地址列表失败，请稍后重试');
    addresses.value = [];
  } finally {
    loading.value = false;
  }
}
```

### 5.2 地址添加/编辑流程

1. **显示对话框**：调用`showAddAddressDialog()`或`showEditAddressDialog(address)`
2. **表单验证**：使用Element Plus的表单验证
3. **选择社区地址**：使用CommunityAddressSelector组件
4. **保存地址**：调用`saveAddress()`函数，根据`isEditing`状态决定是创建还是更新地址

### 5.3 设置默认地址

```typescript
async function setAsDefault(id: string) {
  try {
    await setDefaultAddress(id);
    ElMessage.success('默认地址设置成功');
    await loadAddresses();
  } catch (error) {
    console.error('设置默认地址失败:', error);
    ElMessage.error('设置默认地址失败，请稍后重试');
  }
}
```

### 5.4 删除地址

使用确认对话框防止误操作：

```typescript
function showDeleteConfirm(address: UserAddress) {
  ElMessageBox.confirm(
    '确定要删除这个地址吗？',
    '删除地址',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteUserAddress(address.id);
      ElMessage.success('地址删除成功');
      await loadAddresses();
    } catch (error) {
      console.error('删除地址失败:', error);
      ElMessage.error('删除地址失败，请稍后重试');
    }
  });
}
```

## 6. UI设计与样式

### 6.1 布局设计

- 使用CSS Grid响应式布局，自动适配不同屏幕尺寸
- 地址卡片采用高度固定设计，确保视觉统一
- 默认地址使用不同边框样式和标签进行突出显示

### 6.2 交互设计

- 卡片hover效果：轻微上浮并增加阴影
- 按钮文字化设计，减少视觉干扰
- 表单验证与错误提示
- 加载状态使用骨架屏优化体验

### 6.3 关键样式

```css
.address-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.address-card, .add-address-card {
  height: 180px;
  transition: all 0.3s;
  position: relative;
}

.address-card:hover, .add-address-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.address-card.is-default {
  border: 1px solid #67c23a;
}
```

## 7. 性能与优化建议

1. **按需加载**：
   - 地址选择器组件使用懒加载策略，减少初始数据量

2. **状态管理**：
   - 考虑将常用地址加入状态管理，减少重复请求

3. **表单优化**：
   - 添加防抖处理，减少频繁验证的性能消耗
   - 表单组件可考虑进一步拆分

4. **缓存策略**：
   - 对社区地址数据进行适当缓存，减少重复请求

## 8. 开发指南

### 8.1 开发步骤建议

1. 创建基础页面框架和路由配置
2. 实现地址列表展示功能
3. 添加地址表单组件
4. 实现地址编辑和删除功能
5. 集成社区地址选择器
6. 实现设置默认地址功能
7. 完善UI样式和交互体验
8. 进行兼容性和性能测试

### 8.2 测试要点

- 表单验证
- 空状态展示
- 加载状态展示
- 响应式布局适配
- 错误处理
- 权限验证
- 社区地址选择联动效果

### 8.3 注意事项

- 新增/编辑地址时必须正确处理社区地址选择器返回的经纬度和ID信息
- 删除默认地址后需要特别处理
- 移动端适配时注意表单交互细节
- 考虑地址数量过多时的性能问题

## 9. 未来扩展方向

1. **地址智能排序**：
   - 根据使用频率或距离自动排序

2. **地图选点功能**：
   - 集成地图API，支持地图选点设置地址

3. **地址共享功能**：
   - 允许用户分享地址给家人或朋友

4. **地址标签分类**：
   - 支持自定义标签（家、公司、学校等）

5. **历史地址查询**：
   - 提供曾经使用过但已删除的历史地址查询功能
