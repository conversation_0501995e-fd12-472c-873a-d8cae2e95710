# 手机号验证码登录前端开发指南

本文档为前端开发者提供手机号验证码登录功能的完整实现指南，包括API接口说明、前端实现示例和最佳实践。

## 功能概述

手机号验证码登录功能允许用户通过手机号和短信验证码进行登录，如果手机号未注册则自动创建新用户账号。该功能提供了更便捷的登录方式，提升用户体验。

## API接口说明

### 1. 发送验证码接口

**接口地址：** `POST /v1/user/send-verification-code`

**请求参数：**
```json
{
  "mobile": "13800138000",  // 手机号，必填，11位中国大陆手机号
  "purpose": "login"        // 用途，必填，固定值"login"
}
```

**响应示例（成功）：**
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": {
    "sent": true,
    "expires_in": 300,  // 验证码有效期（秒）
    "retry_after": 60   // 重新发送间隔（秒）
  }
}
```

**错误情况：**
- 手机号格式不正确
- 发送频率过快（需等待重试间隔）
- 短信服务异常

### 2. 验证码登录接口

**接口地址：** `POST /v1/user/login/verify-code`

**请求参数：**
```json
{
  "mobile": "13800138000",    // 手机号，必填
  "verify_code": "123456"    // 验证码，必填，6位数字
}
```

**响应示例（成功）：**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "user_id": 10001,
    "username": "user_13800138000",  // 自动生成的用户名
    "nickname": "用户13800138000",   // 自动生成的昵称
    "mobile": "138****8000",        // 脱敏处理的手机号
    "avatar": "https://example.com/avatar/default.png",
    "is_new_user": true,            // 是否为新注册用户
    "token": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expires_in": 7200  // 访问令牌有效期（秒）
    }
  }
}
```

**错误情况：**
- 验证码错误或已过期
- 手机号格式不正确
- 验证码已使用

## 前端实现示例

### 1. HTML结构

```html
<div class="mobile-login-form">
  <div class="form-group">
    <label for="mobile">手机号</label>
    <input type="tel" id="mobile" placeholder="请输入手机号" maxlength="11">
  </div>
  
  <div class="form-group">
    <label for="verifyCode">验证码</label>
    <div class="verify-code-input">
      <input type="text" id="verifyCode" placeholder="请输入验证码" maxlength="6">
      <button id="sendCodeBtn" class="send-code-btn">发送验证码</button>
    </div>
  </div>
  
  <button id="loginBtn" class="login-btn">登录</button>
</div>
```

### 2. JavaScript实现

```javascript
class MobileVerificationLogin {
  constructor() {
    this.baseURL = 'http://localhost:8181/api/v1/user';
    this.countdownTimer = null;
    this.retryAfter = 60; // 重发间隔
    
    this.initEventListeners();
  }
  
  initEventListeners() {
    document.getElementById('sendCodeBtn').addEventListener('click', () => {
      this.sendVerificationCode();
    });
    
    document.getElementById('loginBtn').addEventListener('click', () => {
      this.loginWithVerifyCode();
    });
    
    // 手机号输入格式化
    document.getElementById('mobile').addEventListener('input', (e) => {
      e.target.value = e.target.value.replace(/\D/g, '').substring(0, 11);
    });
    
    // 验证码输入格式化
    document.getElementById('verifyCode').addEventListener('input', (e) => {
      e.target.value = e.target.value.replace(/\D/g, '').substring(0, 6);
    });
  }
  
  // 发送验证码
  async sendVerificationCode() {
    const mobile = document.getElementById('mobile').value;
    
    // 验证手机号格式
    if (!this.validateMobile(mobile)) {
      this.showMessage('请输入正确的手机号', 'error');
      return;
    }
    
    const sendBtn = document.getElementById('sendCodeBtn');
    sendBtn.disabled = true;
    
    try {
      const response = await fetch(`${this.baseURL}/send-verification-code`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          mobile: mobile,
          purpose: 'login'
        })
      });
      
      const result = await response.json();
      
      if (result.code === 200) {
        this.showMessage('验证码发送成功', 'success');
        this.startCountdown(result.data.retry_after || this.retryAfter);
      } else {
        this.showMessage(result.message || '发送失败', 'error');
        sendBtn.disabled = false;
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      this.showMessage('网络错误，请重试', 'error');
      sendBtn.disabled = false;
    }
  }
  
  // 验证码登录
  async loginWithVerifyCode() {
    const mobile = document.getElementById('mobile').value;
    const verifyCode = document.getElementById('verifyCode').value;
    
    // 参数验证
    if (!this.validateMobile(mobile)) {
      this.showMessage('请输入正确的手机号', 'error');
      return;
    }
    
    if (!this.validateVerifyCode(verifyCode)) {
      this.showMessage('请输入6位验证码', 'error');
      return;
    }
    
    const loginBtn = document.getElementById('loginBtn');
    loginBtn.disabled = true;
    loginBtn.textContent = '登录中...';
    
    try {
      const response = await fetch(`${this.baseURL}/login/verify-code`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          mobile: mobile,
          verify_code: verifyCode
        })
      });
      
      const result = await response.json();
      
      if (result.code === 200) {
        // 登录成功，保存token
        this.saveTokens(result.data.token);
        
        // 显示成功消息
        const message = result.data.is_new_user ? 
          '注册并登录成功！' : '登录成功！';
        this.showMessage(message, 'success');
        
        // 跳转到主页或执行其他逻辑
        setTimeout(() => {
          this.redirectToHome();
        }, 1000);
      } else {
        this.showMessage(result.message || '登录失败', 'error');
      }
    } catch (error) {
      console.error('登录失败:', error);
      this.showMessage('网络错误，请重试', 'error');
    } finally {
      loginBtn.disabled = false;
      loginBtn.textContent = '登录';
    }
  }
  
  // 开始倒计时
  startCountdown(seconds) {
    const sendBtn = document.getElementById('sendCodeBtn');
    let countdown = seconds;
    
    sendBtn.textContent = `${countdown}秒后重发`;
    
    this.countdownTimer = setInterval(() => {
      countdown--;
      sendBtn.textContent = `${countdown}秒后重发`;
      
      if (countdown <= 0) {
        clearInterval(this.countdownTimer);
        sendBtn.disabled = false;
        sendBtn.textContent = '发送验证码';
      }
    }, 1000);
  }
  
  // 验证手机号格式
  validateMobile(mobile) {
    const mobileRegex = /^1[3-9]\d{9}$/;
    return mobileRegex.test(mobile);
  }
  
  // 验证验证码格式
  validateVerifyCode(code) {
    const codeRegex = /^\d{6}$/;
    return codeRegex.test(code);
  }
  
  // 保存token到本地存储
  saveTokens(tokenData) {
    localStorage.setItem('access_token', tokenData.access_token);
    localStorage.setItem('refresh_token', tokenData.refresh_token);
    localStorage.setItem('token_expires_at', 
      Date.now() + tokenData.expires_in * 1000);
  }
  
  // 显示消息
  showMessage(message, type) {
    // 这里可以使用你的UI框架的消息组件
    // 例如：Toast、Alert等
    console.log(`[${type.toUpperCase()}] ${message}`);
    alert(message); // 简单示例，实际项目中应使用更好的UI组件
  }
  
  // 跳转到主页
  redirectToHome() {
    window.location.href = '/home';
  }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  new MobileVerificationLogin();
});
```

### 3. CSS样式示例

```css
.mobile-login-form {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #fff;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  box-sizing: border-box;
}

.verify-code-input {
  display: flex;
  gap: 10px;
}

.verify-code-input input {
  flex: 1;
}

.send-code-btn {
  padding: 12px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  white-space: nowrap;
  font-size: 14px;
}

.send-code-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.login-btn {
  width: 100%;
  padding: 12px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

.login-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.login-btn:hover:not(:disabled) {
  background: #218838;
}
```

## 最佳实践

### 1. 用户体验优化

- **倒计时显示**：发送验证码后显示倒计时，防止频繁发送
- **输入格式化**：自动格式化手机号和验证码输入
- **状态反馈**：及时显示操作状态和错误信息
- **自动跳转**：登录成功后自动跳转到目标页面

### 2. 错误处理

```javascript
// 统一错误处理函数
function handleApiError(error, defaultMessage = '操作失败') {
  if (error.response) {
    // 服务器返回错误
    const { code, message } = error.response.data;
    switch (code) {
      case 400:
        return message || '请求参数错误';
      case 429:
        return '操作过于频繁，请稍后重试';
      case 500:
        return '服务器错误，请稍后重试';
      default:
        return message || defaultMessage;
    }
  } else if (error.request) {
    // 网络错误
    return '网络连接失败，请检查网络设置';
  } else {
    // 其他错误
    return defaultMessage;
  }
}
```

### 3. 安全考虑

- **输入验证**：前端和后端都要进行参数验证
- **防重放攻击**：验证码使用后立即失效
- **频率限制**：限制验证码发送频率
- **Token管理**：安全存储和使用访问令牌

### 4. 兼容性处理

```javascript
// 检查浏览器支持
function checkBrowserSupport() {
  // 检查fetch支持
  if (!window.fetch) {
    console.warn('浏览器不支持fetch，请使用polyfill或XMLHttpRequest');
    return false;
  }
  
  // 检查localStorage支持
  if (!window.localStorage) {
    console.warn('浏览器不支持localStorage');
    return false;
  }
  
  return true;
}
```

### 5. 性能优化

- **防抖处理**：防止用户快速点击发送按钮
- **缓存策略**：合理缓存用户信息
- **懒加载**：按需加载相关资源

```javascript
// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 使用防抖处理发送验证码
const debouncedSendCode = debounce(sendVerificationCode, 1000);
```

## 测试建议

### 1. 单元测试

- 测试手机号格式验证
- 测试验证码格式验证
- 测试API调用逻辑
- 测试错误处理逻辑

### 2. 集成测试

- 测试完整的登录流程
- 测试各种错误场景
- 测试网络异常情况

### 3. 用户体验测试

- 测试在不同设备上的表现
- 测试网络慢的情况
- 测试用户操作习惯

## 常见问题

### Q1: 验证码收不到怎么办？

**A1:** 
1. 检查手机号是否正确
2. 检查短信是否被拦截
3. 等待1-2分钟后重试
4. 联系客服处理

### Q2: 验证码过期了怎么办？

**A2:** 
1. 重新发送验证码
2. 验证码有效期为5分钟
3. 建议在有效期内尽快使用

### Q3: 登录后如何处理新用户？

**A3:** 
1. 检查响应中的`is_new_user`字段
2. 新用户可引导完善个人信息
3. 可显示欢迎页面或新手指引

### Q4: Token过期如何处理？

**A4:** 
1. 使用refresh_token刷新访问令牌
2. 如果refresh_token也过期，需重新登录
3. 建议实现自动刷新机制

## 总结

手机号验证码登录功能为用户提供了便捷的登录方式，前端实现时需要注意用户体验、错误处理、安全性等方面。通过合理的设计和实现，可以大大提升用户的登录体验。

建议在实际项目中根据具体需求调整实现细节，并进行充分的测试以确保功能的稳定性和可靠性。