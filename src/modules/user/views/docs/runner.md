# 跑腿员管理操作指南

> 本文档提供了用户注册为跑腿员、管理跑腿员状态以及与外卖模块集成的详细操作示例，包括界面截图、请求参数和响应结果的说明。

## 目录

- [1. 跑腿员注册流程](#1-跑腿员注册流程)
  - [1.1 用户注册为跑腿员](#11-用户注册为跑腿员)
  - [1.2 身份认证上传](#12-身份认证上传)
  - [1.3 审核状态查询](#13-审核状态查询)
- [2. 跑腿员状态管理](#2-跑腿员状态管理)
  - [2.1 工作状态设置](#21-工作状态设置)
  - [2.2 位置信息更新](#22-位置信息更新)
  - [2.3 在线状态管理](#23-在线状态管理)
- [3. 与外卖模块集成](#3-与外卖模块集成)
  - [3.1 接单状态跑腿员查询](#31-接单状态跑腿员查询)
  - [3.2 订单分配机制](#32-订单分配机制)
  - [3.3 配送费计算](#33-配送费计算)
- [4. 跑腿员订单管理](#4-跑腿员订单管理)
  - [4.1 接单操作](#41-接单操作)
  - [4.2 订单状态更新](#42-订单状态更新)
  - [4.3 收入管理](#43-收入管理)

## 1. 跑腿员注册流程

### 1.1 用户注册为跑腿员

#### 界面示例

```
+-------------------------------------------------------+
|                    注册成为跑腿员                        |
+-------------------------------------------------------+
| 基本信息:                                               |
|                                                       |
| 真实姓名: [张三                         ] *必填        |
|                                                       |
| 身份证号: [330101199001011234           ] *必填        |
|                                                       |
| 手机号码: [13800138000                  ] *必填        |
|                                                       |
| 服务区域:                                               |
| [✓] 西湖区  [✓] 上城区  [ ] 下城区  [ ] 拱墅区          |
| [ ] 江干区  [ ] 滨江区  [ ] 萧山区  [ ] 余杭区          |
|                                                       |
| 服务半径: [5.0           ] 公里                        |
|                                                       |
| 身份认证:                                               |
| 身份证正面: [选择文件] [已选择: front.jpg]              |
| 身份证背面: [选择文件] [已选择: back.jpg]               |
| 人脸照片:   [选择文件] [已选择: face.jpg]               |
|                                                       |
| [ 提交申请 ]   [ 取消 ]                                 |
+-------------------------------------------------------+
```

#### API请求示例

```json
POST /api/v1/runner/secured/register
Content-Type: application/json
Authorization: Bearer {user_token}

{
  "real_name": "张三",
  "id_card_number": "330101199001011234",
  "id_card_front_pic": "https://oss.example.com/runner/id_front_20250521_001.jpg",
  "id_card_back_pic": "https://oss.example.com/runner/id_back_20250521_001.jpg",
  "face_pic": "https://oss.example.com/runner/face_20250521_001.jpg",
  "mobile": "13800138000",
  "area_codes": "330106,330102",
  "service_radius": 5.0
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "message": "申请提交成功，请等待审核"
  }
}
```

### 1.2 身份认证上传

#### 界面示例

```
+-------------------------------------------------------+
|                    身份认证上传                         |
+-------------------------------------------------------+
| 上传要求:                                               |
| • 身份证照片需清晰可见，四角完整                          |
| • 人脸照片需正面免冠，光线充足                            |
| • 支持格式：JPG、PNG，大小不超过5MB                      |
|                                                       |
| 身份证正面:                                             |
| +--------------------------------------------------+ |
| |                                                  | |
| |              [点击上传图片]                        | |
| |                                                  | |
| +--------------------------------------------------+ |
| 预览: [front_preview.jpg] [重新上传]                   |
|                                                       |
| 身份证背面:                                             |
| +--------------------------------------------------+ |
| |                                                  | |
| |              [点击上传图片]                        | |
| |                                                  | |
| +--------------------------------------------------+ |
| 预览: [back_preview.jpg] [重新上传]                    |
|                                                       |
| 人脸照片:                                               |
| +--------------------------------------------------+ |
| |                                                  | |
| |              [点击上传图片]                        | |
| |                                                  | |
| +--------------------------------------------------+ |
| 预览: [face_preview.jpg] [重新上传]                    |
|                                                       |
| [ 确认上传 ]   [ 取消 ]                                 |
+-------------------------------------------------------+
```

#### 文件上传API示例

```json
POST /api/v1/upload/runner-cert
Content-Type: multipart/form-data
Authorization: Bearer {user_token}

{
  "file": "(binary data)",
  "type": "id_front",  // id_front, id_back, face
  "user_id": 12345
}
```

#### 上传响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "url": "https://oss.example.com/runner/id_front_20250521_001.jpg",
    "file_size": 1024000,
    "upload_time": "2025-05-21T10:30:45+08:00"
  }
}
```

### 1.3 审核状态查询

#### 界面示例

```
+-------------------------------------------------------+
|                    申请审核状态                         |
+-------------------------------------------------------+
| 申请信息:                                               |
| 申请时间: 2025-05-21 10:30:45                          |
| 申请状态: [审核中]                                      |
|                                                       |
| 审核进度:                                               |
| ● 提交申请     ✓ 已完成                                |
| ● 资料审核     🔄 审核中                               |
| ● 审核完成     ⏳ 待处理                               |
|                                                       |
| 预计审核时间: 1-3个工作日                                |
|                                                       |
| 审核结果将通过短信和APP推送通知您                         |
|                                                       |
| [ 刷新状态 ]   [ 修改申请 ]                             |
+-------------------------------------------------------+
```

#### API请求示例

```json
GET /api/v1/runner/secured/apply-status
Authorization: Bearer {user_token}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "apply_id": 10001,
    "status": 0,  // 0-待审核 1-审核通过 2-审核拒绝
    "status_desc": "待审核",
    "apply_time": "2025-05-21T10:30:45+08:00",
    "audit_time": null,
    "reject_reason": null,
    "estimated_audit_days": 3
  }
}
```

## 2. 跑腿员状态管理

### 2.1 工作状态设置

#### 界面示例

```
+-------------------------------------------------------+
|                    工作状态设置                         |
+-------------------------------------------------------+
| 当前状态: 接单中                                        |
|                                                       |
| 工作状态:                                               |
| ○ 休息中     - 不接收新订单                            |
| ● 接单中     - 可接收新订单                            |
| ○ 配送中     - 正在配送订单                            |
|                                                       |
| 在线状态:                                               |
| ● 在线       - 用户可以看到您                          |
| ○ 离线       - 用户无法看到您                          |
|                                                       |
| 当前位置: 杭州市西湖区文三西路                            |
| 更新时间: 2025-05-21 14:25:30                          |
|                                                       |
| [ 更新状态 ]   [ 更新位置 ]                             |
+-------------------------------------------------------+
```

#### API请求示例 - 更新工作状态

```json
PUT /api/v1/runner/secured/status
Content-Type: application/json
Authorization: Bearer {runner_token}

{
  "status": 1,  // 0-休息中 1-接单中 2-配送中
  "reason": "开始接单"
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": null
}
```

### 2.2 位置信息更新

#### 界面示例

```
+-------------------------------------------------------+
|                    位置信息更新                         |
+-------------------------------------------------------+
| 当前位置:                                               |
| 地址: 杭州市西湖区文三西路                               |
| 坐标: 30.259924, 120.219375                           |
| 更新: 2025-05-21 14:25:30                             |
|                                                       |
| +--------------------------------------------------+ |
| |                    地图显示                        | |
| |                      📍                           | |
| |                   您的位置                         | |
| |                                                  | |
| +--------------------------------------------------+ |
|                                                       |
| 自动定位: [✓] 启用                                     |
| 定位精度: 高精度 (GPS + 网络)                           |
|                                                       |
| [ 手动更新位置 ]   [ 定位设置 ]                         |
+-------------------------------------------------------+
```

#### API请求示例 - 更新位置

```json
PUT /api/v1/runner/secured/location
Content-Type: application/json
Authorization: Bearer {runner_token}

{
  "latitude": 30.259924,
  "longitude": 120.219375,
  "address": "杭州市西湖区文三西路"
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": null
}
```

### 2.3 在线状态管理

#### 界面示例

```
+-------------------------------------------------------+
|                    在线状态管理                         |
+-------------------------------------------------------+
| 状态概览:                                               |
| 在线状态: ● 在线                                       |
| 工作状态: 接单中                                        |
| 最后活跃: 刚刚                                          |
|                                                       |
| 今日统计:                                               |
| 在线时长: 6小时32分钟                                   |
| 接单数量: 8单                                           |
| 完成数量: 6单                                           |
| 收入金额: ¥156.50                                      |
|                                                       |
| 状态历史:                                               |
| 14:25  上线 - 开始接单                                 |
| 13:45  下线 - 休息                                     |
| 11:30  上线 - 开始接单                                 |
| 09:00  上线 - 开始接单                                 |
|                                                       |
| [ 下线休息 ]   [ 查看详情 ]                             |
+-------------------------------------------------------+
```

## 3. 与外卖模块集成

### 3.1 接单状态跑腿员查询

#### 外卖模块调用示例

```json
POST /api/v1/runner/nearby
Content-Type: application/json
Authorization: Bearer {system_token}

{
  "latitude": 30.259924,
  "longitude": 120.219375,
  "radius": 5.0,
  "limit": 10,
  "working_status": 1,  // 只查询接单中的跑腿员
  "min_score": 4.0      // 最低评分要求
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 10001,
      "real_name": "张*",
      "distance": 0.3,
      "score": 4.8,
      "order_count": 156,
      "working_status": 1,
      "service_radius": 5.0,
      "current_orders": 0  // 当前配送订单数
    },
    {
      "id": 10005,
      "real_name": "李*",
      "distance": 0.8,
      "score": 4.6,
      "order_count": 89,
      "working_status": 1,
      "service_radius": 3.0,
      "current_orders": 1
    }
  ]
}
```

### 3.2 订单分配机制

#### 界面示例 - 外卖商家端

```
+-------------------------------------------------------+
|                    订单分配设置                         |
+-------------------------------------------------------+
| 配送方式选择:                                           |
| ● 平台配送 (跑腿员)                                    |
| ○ 商家自配送                                           |
| ○ 第三方配送                                           |
|                                                       |
| 跑腿员筛选条件:                                         |
| 最低评分: [4.0    ] 分                                 |
| 配送距离: [5.0    ] 公里内                             |
| 优先级:   [距离优先 v]                                 |
|          (距离优先/评分优先/接单速度优先)                |
|                                                       |
| 自动分配设置:                                           |
| [✓] 启用自动分配                                       |
| [✓] 无人接单时推送给更多跑腿员                           |
| 等待时间: [5     ] 分钟后扩大范围                       |
|                                                       |
| [ 保存设置 ]   [ 手动分配 ]                             |
+-------------------------------------------------------+
```

#### 订单分配API示例

```json
POST /api/v1/takeout/orders/{order_id}/assign-runner
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "pickup_address": "杭州市西湖区某某餐厅",
  "pickup_lat": 30.259924,
  "pickup_lng": 120.219375,
  "delivery_address": "杭州市上城区某某小区",
  "delivery_lat": 30.265666,
  "delivery_lng": 120.235555,
  "goods_weight": 1.5,
  "estimated_prep_time": 15,  // 预计备餐时间(分钟)
  "delivery_fee": 8.0,
  "auto_assign": true,
  "assignment_criteria": {
    "min_score": 4.0,
    "max_distance": 5.0,
    "priority": "distance"  // distance, score, speed
  }
}
```

#### 分配响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "assignment_id": "ASG202505211430001",
    "status": "assigned",  // assigned, pending, failed
    "runner_id": 10001,
    "runner_name": "张*",
    "runner_mobile": "138****8000",
    "estimated_pickup_time": "2025-05-21T15:00:00+08:00",
    "estimated_delivery_time": "2025-05-21T15:30:00+08:00"
  }
}
```

### 3.3 配送费计算

#### 界面示例 - 用户下单页面

```
+-------------------------------------------------------+
|                    配送费用明细                         |
+-------------------------------------------------------+
| 配送距离: 2.3 公里                                      |
| 预计时间: 25-35 分钟                                    |
|                                                       |
| 费用明细:                                               |
| 基础配送费:    ¥6.00                                   |
| 距离费用:      ¥2.30  (超出2公里部分)                   |
| 重量费用:      ¥0.00  (2公斤以内免费)                   |
| 时段费用:      ¥1.00  (高峰时段)                        |
| 服务费:        ¥0.50                                   |
| ────────────────────                                   |
| 配送费小计:    ¥9.80                                   |
|                                                       |
| 优惠:                                                   |
| 新用户减免:    -¥2.00                                  |
| ────────────────────                                   |
| 实付配送费:    ¥7.80                                   |
|                                                       |
| [ 确认下单 ]                                           |
+-------------------------------------------------------+
```

#### 配送费计算API示例

```json
POST /api/v1/runner/delivery-fee/calculate
Content-Type: application/json

{
  "pickup_lat": 30.259924,
  "pickup_lng": 120.219375,
  "delivery_lat": 30.265666,
  "delivery_lng": 120.235555,
  "goods_weight": 1.5,
  "order_type": 0,  // 0-商品配送 1-代购 2-代取
  "order_time": "2025-05-21T14:30:00+08:00",
  "user_level": 1   // 用户等级，影响优惠
}
```

#### 计算响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "distance": 2.3,
    "base_delivery_fee": 6.0,
    "distance_fee": 2.3,
    "weight_fee": 0.0,
    "time_fee": 1.0,
    "service_fee": 0.5,
    "total_delivery_fee": 9.8,
    "discount_amount": 2.0,
    "final_delivery_fee": 7.8,
    "estimate_time": 30,
    "fee_breakdown": {
      "base_fee_desc": "基础配送费 6元",
      "distance_fee_desc": "超出2公里，每公里1元",
      "weight_fee_desc": "2公斤以内免费",
      "time_fee_desc": "高峰时段加收1元",
      "discount_desc": "新用户优惠2元"
    }
  }
}
```

## 4. 跑腿员订单管理

### 4.1 接单操作

#### 界面示例 - 跑腿员APP

```
+-------------------------------------------------------+
|                    新订单通知                          |
+-------------------------------------------------------+
| 🔔 您有新的配送订单                                    |
|                                                       |
| 订单编号: TO202505211430001                            |
| 配送费用: ¥8.00                                        |
| 配送距离: 2.3公里                                       |
| 预计时间: 30分钟                                        |
|                                                       |
| 取货地址: 杭州市西湖区某某餐厅                           |
| 📍 距离您 0.3公里                                      |
|                                                       |
| 送货地址: 杭州市上城区某某小区                           |
| 📍 距离取货点 2.3公里                                  |
|                                                       |
| 商品信息: 外卖订单 (1.5kg)                             |
| 备注: 小心轻放，汤类较多                                |
|                                                       |
| 剩余时间: 00:45 (自动取消)                             |
|                                                       |
| [ 接受订单 ]   [ 拒绝订单 ]   [ 查看详情 ]              |
+-------------------------------------------------------+
```

#### 接单API示例

```json
POST /api/v1/runner-order/accept
Content-Type: application/json
Authorization: Bearer {runner_token}

{
  "order_id": 20001
}
```

#### 接单响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "order_id": 20001,
    "order_no": "TO202505211430001",
    "status": 1,  // 已接单
    "accept_time": "2025-05-21T14:35:00+08:00",
    "pickup_deadline": "2025-05-21T15:10:00+08:00",
    "delivery_deadline": "2025-05-21T15:40:00+08:00"
  }
}
```

### 4.2 订单状态更新

#### 界面示例 - 配送中

```
+-------------------------------------------------------+
|                    配送中订单                          |
+-------------------------------------------------------+
| 订单: TO202505211430001                               |
| 状态: 配送中                                           |
|                                                       |
| 进度条:                                                |
| ● 已接单 ──● 已取货 ──🚚 配送中 ──○ 已送达            |
|                                                       |
| 取货信息:                                               |
| ✓ 已从某某餐厅取货                                     |
| 取货时间: 15:05                                        |
|                                                       |
| 送货信息:                                               |
| 地址: 杭州市上城区某某小区3号楼                          |
| 联系人: 王先生 (138****9999)                          |
| 预计送达: 15:35                                        |
|                                                       |
| 导航信息:                                               |
| 剩余距离: 1.2公里                                       |
| 预计时间: 8分钟                                         |
|                                                       |
| [ 联系客户 ]   [ 导航 ]   [ 确认送达 ]                  |
+-------------------------------------------------------+
```

#### 状态更新API示例

```json
POST /api/v1/runner-order/pickup
Content-Type: application/json
Authorization: Bearer {runner_token}

{
  "order_id": 20001
}
```

#### 完成配送API示例

```json
POST /api/v1/runner-order/complete
Content-Type: application/json
Authorization: Bearer {runner_token}

{
  "order_id": 20001,
  "delivery_photo": "https://oss.example.com/delivery/proof_20250521_001.jpg",
  "customer_signature": "王先生",
  "completion_note": "已送达，客户已签收"
}
```

### 4.3 收入管理

#### 界面示例 - 收入统计

```
+-------------------------------------------------------+
|                    收入统计                            |
+-------------------------------------------------------+
| 今日收入: ¥156.50                                      |
| 本周收入: ¥892.30                                      |
| 本月收入: ¥3,245.80                                    |
|                                                       |
| 账户余额:                                               |
| 可用余额: ¥2,890.50                                    |
| 提现中:   ¥500.00                                     |
| 总余额:   ¥3,390.50                                    |
|                                                       |
| 今日明细:                                               |
| +--------------------------------------------------+ |
| | 时间  | 订单号      | 类型   | 金额    | 状态     | |
| |-------|-------------|--------|---------|----------| |
| | 15:35 | TO...1430001| 配送费 | +8.00   | 已入账   | |
| | 15:35 | TO...1430001| 小费   | +2.00   | 已入账   | |
| | 14:20 | TO...1315002| 配送费 | +6.50   | 已入账   | |
| | 13:45 | TO...1230003| 配送费 | +9.00   | 已入账   | |
| +--------------------------------------------------+ |
|                                                       |
| [ 申请提现 ]   [ 查看详情 ]   [ 收入报表 ]              |
+-------------------------------------------------------+
```

#### 收入查询API示例

```json
GET /api/v1/runner/secured/income
Authorization: Bearer {runner_token}
```

#### 收入响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total_income": 3245.80,
    "available_balance": 2890.50,
    "withdrawing_amount": 500.00,
    "today_income": 156.50,
    "yesterday_income": 89.30,
    "month_income": 3245.80,
    "total_order_count": 89
  }
}
```

#### 提现申请API示例

```json
POST /api/v1/runner/secured/withdrawal
Content-Type: application/json
Authorization: Bearer {runner_token}

{
  "amount": 500.00,
  "withdrawal_method": 1,  // 1-微信 2-支付宝 3-银行卡
  "account_name": "张三",
  "account_no": "wxid_abc123",
  "bank_name": null
}
```

#### 提现响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "withdrawal_id": 30001,
    "withdrawal_no": "*****************",
    "amount": 500.00,
    "status": 0,  // 0-处理中 1-已完成 2-已拒绝
    "estimated_arrival": "2025-05-22T16:00:00+08:00",
    "fee": 2.00,
    "actual_amount": 498.00
  }
}
```

## 5. 集成配置说明

### 5.1 外卖模块配置

在外卖模块中需要添加以下配置来支持跑腿员配送：

```go
// 配送方式枚举
const (
    DeliveryTypeMerchant = 1  // 商家自配送
    DeliveryTypeRunner   = 2  // 跑腿员配送
    DeliveryTypeThirdParty = 3 // 第三方配送
)

// 跑腿员配送配置
type RunnerDeliveryConfig struct {
    Enabled           bool    `json:"enabled"`             // 是否启用跑腿员配送
    AutoAssign        bool    `json:"auto_assign"`         // 是否自动分配
    MinScore          float64 `json:"min_score"`           // 最低评分要求
    MaxDistance       float64 `json:"max_distance"`        // 最大配送距离
    AssignmentTimeout int     `json:"assignment_timeout"`  // 分配超时时间(秒)
    Priority          string  `json:"priority"`            // 优先级策略
}
```

### 5.2 状态同步机制

```go
// 订单状态同步
type OrderStatusSync struct {
    TakeoutOrderID int64 `json:"takeout_order_id"`
    RunnerOrderID  int64 `json:"runner_order_id"`
    Status         int   `json:"status"`
    UpdateTime     time.Time `json:"update_time"`
}

// 状态映射关系
var StatusMapping = map[int]int{
    // 跑腿员订单状态 -> 外卖订单状态
    0: 3, // 待接单 -> 待配送
    1: 4, // 已接单 -> 配送中
    2: 4, // 取货中 -> 配送中
    3: 4, // 配送中 -> 配送中
    4: 5, // 已完成 -> 已送达
    5: 6, // 已取消 -> 已取消
}
```

### 5.3 事件通知机制

```go
// 事件类型
const (
    EventRunnerAssigned   = "runner.assigned"   // 跑腿员已分配
    EventRunnerAccepted   = "runner.accepted"   // 跑腿员已接单
    EventRunnerPickedUp   = "runner.picked_up"  // 跑腿员已取货
    EventRunnerDelivered  = "runner.delivered"  // 跑腿员已送达
    EventRunnerCanceled   = "runner.canceled"   // 跑腿员已取消
)

// 事件数据结构
type RunnerEvent struct {
    EventType      string    `json:"event_type"`
    TakeoutOrderID int64     `json:"takeout_order_id"`
    RunnerOrderID  int64     `json:"runner_order_id"`
    RunnerID       int64     `json:"runner_id"`
    Timestamp      time.Time `json:"timestamp"`
    Data           map[string]interface{} `json:"data"`
}
```

这个文档提供了完整的跑腿员管理操作指南，包括用户注册、状态管理、与外卖模块的集成等各个方面的详细说明和API示例。