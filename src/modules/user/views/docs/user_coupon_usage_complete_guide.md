# 外卖模块用户优惠券使用完整指南

## 概述

本文档详细说明用户在外卖平台上领取、管理和使用优惠券的完整流程，包括API接口、前端实现和用户体验设计。

## 用户优惠券生命周期

```
商家发布优惠券模板
        ↓
用户发现优惠券 → 用户领取优惠券 → 用户查看优惠券 → 用户使用优惠券 → 优惠券状态更新
        ↓              ↓              ↓              ↓              ↓
   优惠券中心        创建实例        我的优惠券      订单结算        已使用/已过期
```

## 第一阶段：用户发现优惠券

### 1.1 优惠券展示渠道

#### 商家页面优惠券展示

**API接口**: `GET /v1/user/takeout/merchants/{merchant_id}/available-coupons`

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "available_coupons": [
      {
        "id": 201,
        "name": "满50减10优惠券",
        "description": "订单满50元可使用此券减10元",
        "type": 1,
        "amount": 10,
        "min_order_amount": 50,
        "total_limit": 500,
        "claimed_count": 120,
        "per_user_limit": 1,
        "user_claimed_count": 0,
        "can_claim": true,
        "start_time": "2025-05-01T00:00:00+08:00",
        "end_time": "2025-05-31T23:59:59+08:00"
      }
    ]
  }
}
```

#### 优惠券中心

**API接口**: `GET /v1/user/takeout/coupons/center`

**查询参数**:
- `category`: 分类筛选 (all/food/delivery/new_user)
- `page`: 页码
- `page_size`: 每页数量

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "categories": [
      {"key": "all", "name": "全部", "count": 25},
      {"key": "food", "name": "美食券", "count": 15},
      {"key": "delivery", "name": "配送券", "count": 8},
      {"key": "new_user", "name": "新人券", "count": 2}
    ],
    "coupons": [
      {
        "id": 201,
        "merchant_id": 502,
        "merchant_name": "美味餐厅",
        "merchant_logo": "https://example.com/logo.jpg",
        "name": "满50减10优惠券",
        "type": 1,
        "amount": 10,
        "min_order_amount": 50,
        "can_claim": true,
        "claim_button_text": "立即领取",
        "end_time": "2025-05-31T23:59:59+08:00"
      }
    ],
    "total": 25,
    "page": 1,
    "page_size": 10
  }
}
```

### 1.2 前端展示组件

#### 优惠券卡片组件

```vue
<template>
  <div class="coupon-card" :class="{ 'disabled': !coupon.can_claim }">
    <div class="coupon-left">
      <div class="amount">¥{{ coupon.amount }}</div>
      <div class="condition">满{{ coupon.min_order_amount }}可用</div>
    </div>
    <div class="coupon-right">
      <div class="merchant-info">
        <img :src="coupon.merchant_logo" class="merchant-logo" />
        <span class="merchant-name">{{ coupon.merchant_name }}</span>
      </div>
      <div class="coupon-name">{{ coupon.name }}</div>
      <div class="expire-time">有效期至：{{ formatDate(coupon.end_time) }}</div>
      <button 
        v-if="coupon.can_claim"
        @click="claimCoupon(coupon.id)"
        class="claim-btn"
      >
        {{ coupon.claim_button_text }}
      </button>
      <span v-else class="claimed-text">已领取</span>
    </div>
  </div>
</template>
```

## 第二阶段：用户领取优惠券

### 2.1 优惠券领取

**API接口**: `POST /v1/user/takeout/coupons/claim`

**请求示例**:
```json
{
  "coupon_id": 201
}
```

**系统验证流程**:
1. 验证用户登录状态
2. 验证优惠券模板状态（必须为可发放状态：status = 1）
3. 验证优惠券有效期
4. 验证总发放数量限制
5. 验证用户领取次数限制
6. 验证用户等级限制
7. 创建用户优惠券实例

**成功响应**:
```json
{
  "code": 0,
  "message": "领取成功",
  "data": {
    "user_coupon_id": 1001,
    "coupon_name": "满50减10优惠券",
    "amount": 10,
    "min_order_amount": 50,
    "expire_time": "2025-05-31T23:59:59+08:00",
    "status": 1,
    "merchant_name": "美味餐厅"
  }
}
```

**错误响应示例**:
```json
{
  "code": 40004,
  "message": "优惠券已达领取上限",
  "data": null
}
```

### 2.2 领取成功处理

#### 前端处理逻辑

```javascript
async claimCoupon(couponId) {
  try {
    this.claiming = true
    
    const response = await this.$api.post('/v1/user/takeout/coupons/claim', {
      coupon_id: couponId
    })
    
    // 显示成功提示
    this.$toast.success('领取成功！')
    
    // 更新UI状态
    this.updateCouponStatus(couponId, false)
    
    // 可选：跳转到我的优惠券页面
    this.$router.push('/my-coupons')
    
  } catch (error) {
    if (error.code === 40004) {
      this.$toast.error('优惠券已抢完')
    } else if (error.code === 40005) {
      this.$toast.error('您已领取过此优惠券')
    } else {
      this.$toast.error('领取失败，请重试')
    }
  } finally {
    this.claiming = false
  }
}
```

## 第三阶段：用户查看优惠券

### 3.1 我的优惠券列表

**API接口**: `GET /v1/user/takeout/coupons/my-list`

**查询参数**:
- `status`: 状态筛选 (1:未使用 2:已使用 3:已过期)
- `merchant_id`: 商家筛选（可选）
- `page`: 页码
- `page_size`: 每页数量

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "statistics": {
      "unused_count": 5,
      "used_count": 12,
      "expired_count": 3
    },
    "coupons": [
      {
        "id": 1001,
        "coupon_id": 201,
        "name": "满50减10优惠券",
        "description": "订单满50元可使用此券减10元",
        "type": 1,
        "amount": 10,
        "min_order_amount": 50,
        "merchant_id": 502,
        "merchant_name": "美味餐厅",
        "merchant_logo": "https://example.com/logo.jpg",
        "status": 1,
        "status_text": "未使用",
        "expire_time": "2025-05-31T23:59:59+08:00",
        "received_time": "2025-05-15T10:30:00+08:00",
        "days_to_expire": 16,
        "can_use": true
      }
    ],
    "total": 20,
    "page": 1,
    "page_size": 10
  }
}
```

### 3.2 优惠券详情

**API接口**: `GET /v1/user/takeout/coupons/{user_coupon_id}`

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1001,
    "coupon_id": 201,
    "name": "满50减10优惠券",
    "description": "订单满50元可使用此券减10元",
    "type": 1,
    "amount": 10,
    "min_order_amount": 50,
    "merchant_info": {
      "id": 502,
      "name": "美味餐厅",
      "logo": "https://example.com/logo.jpg",
      "address": "北京市朝阳区xxx街道"
    },
    "usage_rules": {
      "apply_to_all": true,
      "apply_to_categories": [],
      "apply_to_foods": [],
      "exclude_foods": [],
      "can_combine": false
    },
    "status": 1,
    "status_text": "未使用",
    "expire_time": "2025-05-31T23:59:59+08:00",
    "received_time": "2025-05-15T10:30:00+08:00",
    "used_time": null,
    "order_id": null
  }
}
```

### 3.3 前端列表页面实现

```vue
<template>
  <div class="my-coupons">
    <!-- 状态统计 -->
    <div class="status-tabs">
      <div 
        v-for="tab in statusTabs" 
        :key="tab.status"
        :class="['tab-item', { active: currentStatus === tab.status }]"
        @click="switchStatus(tab.status)"
      >
        {{ tab.name }}
        <span class="count">({{ tab.count }})</span>
      </div>
    </div>
    
    <!-- 优惠券列表 -->
    <div class="coupon-list">
      <div 
        v-for="coupon in coupons" 
        :key="coupon.id"
        class="my-coupon-item"
        :class="{
          'expired': coupon.status === 3,
          'used': coupon.status === 2
        }"
        @click="viewCouponDetail(coupon.id)"
      >
        <div class="coupon-main">
          <div class="amount-section">
            <span class="currency">¥</span>
            <span class="amount">{{ coupon.amount }}</span>
            <div class="condition">满{{ coupon.min_order_amount }}可用</div>
          </div>
          
          <div class="info-section">
            <div class="merchant-info">
              <img :src="coupon.merchant_logo" class="merchant-logo" />
              <span class="merchant-name">{{ coupon.merchant_name }}</span>
            </div>
            <div class="coupon-name">{{ coupon.name }}</div>
            <div class="time-info">
              <span v-if="coupon.status === 1" class="expire-time">
                {{ coupon.days_to_expire }}天后过期
              </span>
              <span v-else-if="coupon.status === 2" class="used-time">
                {{ formatDate(coupon.used_time) }} 已使用
              </span>
              <span v-else class="expired-text">已过期</span>
            </div>
          </div>
        </div>
        
        <div class="coupon-action">
          <button 
            v-if="coupon.status === 1 && coupon.can_use"
            @click.stop="useCoupon(coupon)"
            class="use-btn"
          >
            立即使用
          </button>
          <span v-else class="status-badge">
            {{ coupon.status_text }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-if="coupons.length === 0" class="empty-state">
      <img src="/images/empty-coupon.png" class="empty-image" />
      <p class="empty-text">暂无优惠券</p>
      <button @click="goToCouponCenter" class="go-center-btn">
        去优惠券中心看看
      </button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentStatus: 1, // 1:未使用 2:已使用 3:已过期
      coupons: [],
      statistics: {},
      loading: false
    }
  },
  
  computed: {
    statusTabs() {
      return [
        { status: 1, name: '未使用', count: this.statistics.unused_count || 0 },
        { status: 2, name: '已使用', count: this.statistics.used_count || 0 },
        { status: 3, name: '已过期', count: this.statistics.expired_count || 0 }
      ]
    }
  },
  
  async mounted() {
    await this.loadCoupons()
  },
  
  methods: {
    async loadCoupons() {
      try {
        this.loading = true
        
        const response = await this.$api.get('/v1/user/takeout/coupons/my-list', {
          params: {
            status: this.currentStatus,
            page: 1,
            page_size: 20
          }
        })
        
        this.coupons = response.data.coupons
        this.statistics = response.data.statistics
        
      } catch (error) {
        this.$toast.error('获取优惠券列表失败')
      } finally {
        this.loading = false
      }
    },
    
    async switchStatus(status) {
      this.currentStatus = status
      await this.loadCoupons()
    },
    
    useCoupon(coupon) {
      // 跳转到商家页面，并预选优惠券
      this.$router.push({
        path: `/merchant/${coupon.merchant_id}`,
        query: { 
          user_coupon_id: coupon.id 
        }
      })
    },
    
    viewCouponDetail(userCouponId) {
      this.$router.push(`/my-coupons/${userCouponId}`)
    },
    
    goToCouponCenter() {
      this.$router.push('/coupon-center')
    }
  }
}
</script>
```

## 第四阶段：用户使用优惠券

### 4.1 订单页面优惠券选择

**API接口**: `GET /v1/user/takeout/coupons/available-for-order`

**查询参数**:
- `merchant_id`: 商家ID
- `total_amount`: 订单总金额
- `food_ids`: 商品ID列表（逗号分隔）

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "available_coupons": [
      {
        "id": 1001,
        "coupon_id": 201,
        "name": "满50减10优惠券",
        "amount": 10,
        "min_order_amount": 50,
        "can_use": true,
        "reason": "",
        "discount_amount": 10,
        "final_amount": 50
      }
    ],
    "unavailable_coupons": [
      {
        "id": 1002,
        "coupon_id": 202,
        "name": "满100减20优惠券",
        "amount": 20,
        "min_order_amount": 100,
        "can_use": false,
        "reason": "订单金额不满足使用条件"
      }
    ]
  }
}
```

### 4.2 优惠券验证

**API接口**: `POST /v1/user/takeout/coupons/validate-for-order`

**请求示例**:
```json
{
  "user_coupon_id": 1001,
  "order": {
    "merchant_id": 502,
    "total_amount": 60.00,
    "items": [
      {
        "food_id": 1024,
        "quantity": 2,
        "price": 25.00,
        "category_id": 1
      },
      {
        "food_id": 1025,
        "quantity": 1,
        "price": 10.00,
        "category_id": 2
      }
    ]
  }
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "valid": true,
    "original_amount": 60.00,
    "discount_amount": 10.00,
    "final_amount": 50.00,
    "coupon_info": {
      "id": 1001,
      "name": "满50减10优惠券",
      "type": 1
    }
  }
}
```

### 4.3 订单页面优惠券选择组件

```vue
<template>
  <div class="order-coupon-section">
    <!-- 优惠券选择入口 -->
    <div class="coupon-selector" @click="showCouponModal = true">
      <div class="selector-left">
        <i class="coupon-icon"></i>
        <span v-if="!selectedCoupon" class="placeholder">选择优惠券</span>
        <div v-else class="selected-coupon">
          <span class="coupon-name">{{ selectedCoupon.name }}</span>
          <span class="discount">-¥{{ selectedCoupon.discount_amount }}</span>
        </div>
      </div>
      <div class="selector-right">
        <span v-if="availableCoupons.length > 0" class="available-count">
          {{ availableCoupons.length }}张可用
        </span>
        <i class="arrow-right"></i>
      </div>
    </div>
    
    <!-- 优惠券选择弹窗 -->
    <div v-if="showCouponModal" class="coupon-modal-overlay" @click="showCouponModal = false">
      <div class="coupon-modal" @click.stop>
        <div class="modal-header">
          <h3>选择优惠券</h3>
          <button @click="showCouponModal = false" class="close-btn">×</button>
        </div>
        
        <div class="modal-content">
          <!-- 可用优惠券 -->
          <div v-if="availableCoupons.length > 0" class="coupon-section">
            <h4>可用优惠券 ({{ availableCoupons.length }})</h4>
            <div 
              v-for="coupon in availableCoupons"
              :key="coupon.id"
              class="coupon-option"
              :class="{ 'selected': selectedCoupon?.id === coupon.id }"
              @click="selectCoupon(coupon)"
            >
              <div class="coupon-info">
                <div class="amount">¥{{ coupon.amount }}</div>
                <div class="details">
                  <div class="name">{{ coupon.name }}</div>
                  <div class="condition">满{{ coupon.min_order_amount }}可用</div>
                </div>
              </div>
              <div class="coupon-action">
                <div class="discount">-¥{{ coupon.discount_amount }}</div>
                <i v-if="selectedCoupon?.id === coupon.id" class="check-icon">✓</i>
              </div>
            </div>
          </div>
          
          <!-- 不可用优惠券 -->
          <div v-if="unavailableCoupons.length > 0" class="coupon-section">
            <h4>不可用优惠券 ({{ unavailableCoupons.length }})</h4>
            <div 
              v-for="coupon in unavailableCoupons"
              :key="coupon.id"
              class="coupon-option disabled"
            >
              <div class="coupon-info">
                <div class="amount">¥{{ coupon.amount }}</div>
                <div class="details">
                  <div class="name">{{ coupon.name }}</div>
                  <div class="reason">{{ coupon.reason }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="availableCoupons.length === 0 && unavailableCoupons.length === 0" class="empty-coupons">
            <p>暂无可用优惠券</p>
            <button @click="goToCouponCenter" class="get-coupon-btn">
              去领取优惠券
            </button>
          </div>
        </div>
        
        <div class="modal-footer">
          <button @click="clearSelection" class="clear-btn">不使用优惠券</button>
          <button @click="confirmSelection" class="confirm-btn">确认选择</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    merchantId: {
      type: Number,
      required: true
    },
    orderAmount: {
      type: Number,
      required: true
    },
    orderItems: {
      type: Array,
      required: true
    }
  },
  
  data() {
    return {
      showCouponModal: false,
      availableCoupons: [],
      unavailableCoupons: [],
      selectedCoupon: null,
      loading: false
    }
  },
  
  watch: {
    orderAmount() {
      this.loadAvailableCoupons()
    },
    orderItems: {
      handler() {
        this.loadAvailableCoupons()
      },
      deep: true
    }
  },
  
  async mounted() {
    await this.loadAvailableCoupons()
  },
  
  methods: {
    async loadAvailableCoupons() {
      try {
        this.loading = true
        
        const foodIds = this.orderItems.map(item => item.food_id).join(',')
        
        const response = await this.$api.get('/v1/user/takeout/coupons/available-for-order', {
          params: {
            merchant_id: this.merchantId,
            total_amount: this.orderAmount,
            food_ids: foodIds
          }
        })
        
        this.availableCoupons = response.data.available_coupons
        this.unavailableCoupons = response.data.unavailable_coupons
        
        // 如果当前选择的优惠券不可用了，清除选择
        if (this.selectedCoupon && !this.availableCoupons.find(c => c.id === this.selectedCoupon.id)) {
          this.selectedCoupon = null
          this.$emit('coupon-changed', null)
        }
        
      } catch (error) {
        console.error('获取可用优惠券失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    selectCoupon(coupon) {
      this.selectedCoupon = coupon
    },
    
    clearSelection() {
      this.selectedCoupon = null
      this.showCouponModal = false
      this.$emit('coupon-changed', null)
    },
    
    confirmSelection() {
      this.showCouponModal = false
      this.$emit('coupon-changed', this.selectedCoupon)
    },
    
    goToCouponCenter() {
      this.$router.push('/coupon-center')
    }
  }
}
</script>
```

### 4.4 订单提交使用优惠券

**API接口**: `POST /v1/user/takeout/orders`

**请求示例**:
```json
{
  "merchant_id": 502,
  "items": [
    {
      "food_id": 1024,
      "quantity": 2,
      "price": 25.00
    }
  ],
  "user_coupon_id": 1001,
  "delivery_address_id": 123,
  "remark": "不要辣",
  "payment_method": "wechat"
}
```

**系统处理流程**:
1. 验证订单基本信息
2. 再次验证优惠券可用性
3. 计算优惠金额
4. 创建订单记录
5. 更新用户优惠券状态为已使用
6. 返回订单信息

**响应示例**:
```json
{
  "code": 0,
  "message": "订单创建成功",
  "data": {
    "order_id": 12345,
    "order_no": "TO202505150001",
    "original_amount": 60.00,
    "coupon_discount": 10.00,
    "final_amount": 50.00,
    "payment_amount": 50.00,
    "coupon_info": {
      "user_coupon_id": 1001,
      "coupon_name": "满50减10优惠券"
    }
  }
}
```

## 第五阶段：优惠券状态管理

### 5.1 使用记录查询

**API接口**: `GET /v1/user/takeout/coupons/usage-history`

**查询参数**:
- `page`: 页码
- `page_size`: 每页数量
- `start_date`: 开始日期（可选）
- `end_date`: 结束日期（可选）

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "history": [
      {
        "id": 1001,
        "coupon_name": "满50减10优惠券",
        "merchant_name": "美味餐厅",
        "order_id": 12345,
        "order_no": "TO202505150001",
        "discount_amount": 10.00,
        "used_time": "2025-05-15T12:30:00+08:00",
        "order_status": "已完成"
      }
    ],
    "total": 12,
    "page": 1,
    "page_size": 10,
    "total_saved": 120.00
  }
}
```

### 5.2 过期处理

系统定时任务（每天凌晨执行）：

```sql
-- 更新过期的用户优惠券
UPDATE takeout_user_coupon uc
JOIN takeout_coupon c ON uc.coupon_id = c.id
SET uc.status = 3, uc.updated_at = NOW()
WHERE uc.status = 1 
  AND c.end_time < NOW();
```

### 5.3 过期提醒

**API接口**: `GET /v1/user/takeout/coupons/expiring-soon`

**查询参数**:
- `days`: 即将过期天数（默认3天）

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "expiring_coupons": [
      {
        "id": 1003,
        "name": "满50减10优惠券",
        "merchant_name": "美味餐厅",
        "amount": 10,
        "expire_time": "2025-05-18T23:59:59+08:00",
        "days_left": 2
      }
    ],
    "count": 1
  }
}
```

## 用户体验优化建议

### 6.1 智能推荐

1. **个性化推荐**: 根据用户历史订单推荐相关商家优惠券
2. **场景推荐**: 根据时间、地点推荐合适的优惠券
3. **组合推荐**: 推荐可以叠加使用的优惠券组合

### 6.2 提醒机制

1. **过期提醒**: 优惠券即将过期时推送提醒
2. **使用提醒**: 下单时智能提醒可用优惠券
3. **新券通知**: 关注商家发布新优惠券时通知

### 6.3 视觉设计

1. **状态区分**: 不同状态的优惠券使用不同的视觉样式
2. **金额突出**: 优惠金额使用醒目的颜色和字体
3. **过期警告**: 即将过期的优惠券显示警告标识

## 错误处理和异常情况

### 7.1 常见错误码

| 错误码 | 错误信息 | 处理建议 |
|--------|----------|----------|
| 40001 | 优惠券不存在 | 刷新页面重试 |
| 40002 | 优惠券已过期 | 提示用户选择其他优惠券 |
| 40003 | 优惠券已被使用 | 刷新优惠券列表 |
| 40004 | 优惠券已达领取上限 | 提示用户关注其他优惠券 |
| 40005 | 用户已达领取次数限制 | 提示用户查看已领取的优惠券 |
| 40006 | 订单金额不满足使用条件 | 提示用户增加商品或选择其他优惠券 |
| 40007 | 商品不在优惠券适用范围内 | 提示用户选择适用商品 |
| 40008 | 商家不匹配 | 提示用户在对应商家使用 |

### 7.2 网络异常处理

```javascript
// 网络重试机制
async function apiWithRetry(apiCall, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await apiCall()
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}

// 使用示例
try {
  const result = await apiWithRetry(() => 
    this.$api.get('/v1/user/takeout/coupons/my-list')
  )
} catch (error) {
  this.$toast.error('网络异常，请检查网络连接')
}
```

## 性能优化

### 8.1 缓存策略

1. **用户优惠券列表缓存**: 缓存5分钟
2. **可用优惠券缓存**: 根据订单内容缓存1分钟
3. **优惠券详情缓存**: 缓存10分钟

### 8.2 分页加载

```javascript
// 无限滚动加载
data() {
  return {
    coupons: [],
    currentPage: 1,
    hasMore: true,
    loading: false
  }
},

methods: {
  async loadMore() {
    if (this.loading || !this.hasMore) return
    
    this.loading = true
    try {
      const response = await this.$api.get('/v1/user/takeout/coupons/my-list', {
        params: {
          page: this.currentPage,
          page_size: 10
        }
      })
      
      this.coupons.push(...response.data.coupons)
      this.currentPage++
      this.hasMore = response.data.coupons.length === 10
      
    } finally {
      this.loading = false
    }
  }
}
```

## 总结

用户优惠券系统通过完整的生命周期管理，为用户提供了便捷的优惠券领取、查看和使用体验。系统设计考虑了各种业务场景和异常情况，确保了功能的稳定性和用户体验的流畅性。通过合理的API设计、前端组件化和性能优化，为用户提供了高质量的优惠券服务。