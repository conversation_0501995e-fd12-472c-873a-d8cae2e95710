# 多端多设备登录前端集成指导文档

## 概述

本文档指导前端开发者如何适配新的多端多设备登录功能。该功能支持用户在多个设备上同时登录，并提供设备管理、登录历史查看等功能。

## 主要变更

### 1. 登录接口变更

#### 1.1 用户名密码登录

**接口地址**: `POST /api/v1/user/login`

**请求参数变更**:
```json
{
  "username": "用户名",
  "password": "密码",
  "device_info": {  // 新增：设备信息（可选）
    "device_id": "设备唯一标识",
    "device_name": "设备名称",
    "device_type": "设备类型", // mobile, desktop, tablet, web
    "platform": "平台", // ios, android, windows, macos, web
    "browser": "浏览器信息",
    "app_version": "APP版本",
    "os_version": "操作系统版本",
    "user_agent": "用户代理字符串"
  }
}
```

**响应参数变更**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token_info": {
      "access_token": "访问令牌",
      "refresh_token": "刷新令牌",
      "expires_in": 600,
      "token_type": "Bearer"
    },
    "user": {
      // 用户信息...
    },
    "device_id": "设备ID",        // 新增
    "is_new_device": true,        // 新增：是否新设备
    "risk_level": 0               // 新增：风险等级 0-正常 1-低风险 2-中风险 3-高风险
  }
}
```

#### 1.2 短信验证码登录

**接口地址**: `POST /api/v1/user/login-by-verify-code`

**请求参数变更**:
```json
{
  "mobile": "手机号",
  "code": "验证码",
  "device_info": {  // 新增：设备信息（可选）
    // 同上
  }
}
```

**响应参数**: 同用户名密码登录

### 2. 设备信息生成

前端需要生成设备信息，建议实现方式：

```javascript
// 生成设备信息的工具函数
function generateDeviceInfo() {
  // 生成设备唯一标识
  const deviceId = getOrCreateDeviceId();
  
  // 获取设备信息
  const deviceInfo = {
    device_id: deviceId,
    device_name: getDeviceName(),
    device_type: getDeviceType(),
    platform: getPlatform(),
    browser: getBrowserInfo(),
    app_version: getAppVersion(),
    os_version: getOSVersion(),
    user_agent: navigator.userAgent
  };
  
  return deviceInfo;
}

// 获取或创建设备ID
function getOrCreateDeviceId() {
  let deviceId = localStorage.getItem('device_id');
  if (!deviceId) {
    deviceId = generateUUID();
    localStorage.setItem('device_id', deviceId);
  }
  return deviceId;
}

// 生成UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 获取设备类型
function getDeviceType() {
  const userAgent = navigator.userAgent;
  if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
    return /iPad/.test(userAgent) ? 'tablet' : 'mobile';
  }
  return 'desktop';
}

// 获取平台信息
function getPlatform() {
  const userAgent = navigator.userAgent;
  if (/iPhone|iPad/.test(userAgent)) return 'ios';
  if (/Android/.test(userAgent)) return 'android';
  if (/Windows/.test(userAgent)) return 'windows';
  if (/Mac/.test(userAgent)) return 'macos';
  if (/Linux/.test(userAgent)) return 'linux';
  return 'web';
}

// 获取浏览器信息
function getBrowserInfo() {
  const userAgent = navigator.userAgent;
  if (/Chrome/.test(userAgent)) return 'Chrome';
  if (/Firefox/.test(userAgent)) return 'Firefox';
  if (/Safari/.test(userAgent)) return 'Safari';
  if (/Edge/.test(userAgent)) return 'Edge';
  return 'Unknown';
}

// 获取设备名称
function getDeviceName() {
  const platform = getPlatform();
  const browser = getBrowserInfo();
  return `${platform} ${browser}`;
}

// 获取APP版本（如果是APP）
function getAppVersion() {
  // 如果是APP，从原生获取版本号
  // 如果是Web，可以从package.json或配置获取
  return '1.0.0';
}

// 获取操作系统版本
function getOSVersion() {
  const userAgent = navigator.userAgent;
  // 简化实现，实际可以更详细解析
  return userAgent.match(/\(([^)]+)\)/)?.[1] || 'Unknown';
}
```

### 3. 新增设备管理API

#### 3.1 获取设备列表

**接口地址**: `GET /api/v1/user/devices`

**请求头**: 需要携带Authorization头

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "devices": [
      {
        "device_id": "设备ID",
        "device_name": "设备名称",
        "device_type": "mobile",
        "platform": "ios",
        "browser": "Safari",
        "is_trusted": false,
        "is_current_device": true,
        "last_active_at": "2024-01-01T12:00:00Z",
        "login_at": "2024-01-01T10:00:00Z",
        "location": "北京市",
        "ip_address": "***********",
        "status": 1
      }
    ],
    "total": 3
  }
}
```

#### 3.2 登出指定设备

**接口地址**: `POST /api/v1/user/devices/{device_id}/logout`

**请求示例**:
```javascript
fetch('/api/v1/user/devices/abc123/logout', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + accessToken,
    'Content-Type': 'application/json'
  }
});
```

#### 3.3 登出所有其他设备

**接口地址**: `POST /api/v1/user/devices/logout-all`

#### 3.4 设置受信任设备

**接口地址**: `PUT /api/v1/user/devices/{device_id}/trust`

#### 3.5 获取登录历史

**接口地址**: `GET /api/v1/user/devices/login-history?page=1&page_size=20`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "history": [
      {
        "id": 1,
        "device_id": "设备ID",
        "device_name": "设备名称",
        "login_type": "password",
        "ip_address": "***********",
        "location": "北京市",
        "is_success": true,
        "failure_reason": "",
        "risk_level": 0,
        "created_at": "2024-01-01T12:00:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "size": 20
  }
}
```

#### 3.6 获取登录统计

**接口地址**: `GET /api/v1/user/devices/login-stats?days=30`

#### 3.7 刷新Token

**接口地址**: `POST /api/v1/user/devices/refresh-token`

**请求参数**:
```json
{
  "refresh_token": "刷新令牌"
}
```

### 4. 前端页面实现建议

#### 4.1 登录页面修改

```javascript
// 登录函数修改
async function login(username, password) {
  const deviceInfo = generateDeviceInfo();

  const response = await fetch('/api/v1/user/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      username,
      password,
      device_info: deviceInfo
    })
  });

  const result = await response.json();

  if (result.code === 200) {
    // 保存Token
    localStorage.setItem('access_token', result.data.token_info.access_token);
    localStorage.setItem('refresh_token', result.data.token_info.refresh_token);

    // 保存设备ID
    localStorage.setItem('current_device_id', result.data.device_id);

    // 处理新设备提示
    if (result.data.is_new_device) {
      showNewDeviceNotification();
    }

    // 处理风险等级
    if (result.data.risk_level > 0) {
      showRiskWarning(result.data.risk_level);
    }

    return result.data;
  } else {
    throw new Error(result.message);
  }
}

// 新设备提示
function showNewDeviceNotification() {
  // 显示新设备登录提示
  alert('检测到您在新设备上登录，请注意账户安全。');
}

// 风险警告
function showRiskWarning(riskLevel) {
  const messages = {
    1: '检测到低风险登录，请注意账户安全。',
    2: '检测到中风险登录，建议立即修改密码。',
    3: '检测到高风险登录，请立即修改密码并检查账户安全。'
  };

  alert(messages[riskLevel] || '检测到异常登录。');
}
```

#### 4.2 设备管理页面

```javascript
// 设备管理页面组件
class DeviceManagement {
  constructor() {
    this.devices = [];
    this.currentDeviceId = localStorage.getItem('current_device_id');
  }

  // 加载设备列表
  async loadDevices() {
    const response = await fetch('/api/v1/user/devices', {
      headers: {
        'Authorization': 'Bearer ' + localStorage.getItem('access_token')
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      this.devices = result.data.devices;
      this.renderDevices();
    }
  }

  // 渲染设备列表
  renderDevices() {
    const container = document.getElementById('device-list');
    container.innerHTML = '';

    this.devices.forEach(device => {
      const deviceElement = this.createDeviceElement(device);
      container.appendChild(deviceElement);
    });
  }

  // 创建设备元素
  createDeviceElement(device) {
    const div = document.createElement('div');
    div.className = 'device-item';

    const statusClass = device.is_current_device ? 'current' : 'other';
    const trustIcon = device.is_trusted ? '🔒' : '🔓';

    div.innerHTML = `
      <div class="device-info ${statusClass}">
        <h4>${device.device_name} ${trustIcon}</h4>
        <p>类型: ${device.device_type} | 平台: ${device.platform}</p>
        <p>最后活跃: ${new Date(device.last_active_at).toLocaleString()}</p>
        <p>登录地点: ${device.location || '未知'} (${device.ip_address})</p>
        ${device.is_current_device ? '<span class="current-badge">当前设备</span>' : ''}
      </div>
      <div class="device-actions">
        ${!device.is_current_device ? `
          <button onclick="this.logoutDevice('${device.device_id}')">登出</button>
        ` : ''}
        ${!device.is_trusted ? `
          <button onclick="this.trustDevice('${device.device_id}')">设为受信任</button>
        ` : ''}
      </div>
    `;

    return div;
  }

  // 登出设备
  async logoutDevice(deviceId) {
    if (!confirm('确定要登出该设备吗？')) return;

    const response = await fetch(`/api/v1/user/devices/${deviceId}/logout`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + localStorage.getItem('access_token')
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      alert('设备已登出');
      this.loadDevices();
    } else {
      alert('登出失败: ' + result.message);
    }
  }

  // 登出所有其他设备
  async logoutAllOtherDevices() {
    if (!confirm('确定要登出所有其他设备吗？')) return;

    const response = await fetch('/api/v1/user/devices/logout-all', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + localStorage.getItem('access_token')
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      alert('所有其他设备已登出');
      this.loadDevices();
    } else {
      alert('操作失败: ' + result.message);
    }
  }

  // 设置受信任设备
  async trustDevice(deviceId) {
    const response = await fetch(`/api/v1/user/devices/${deviceId}/trust`, {
      method: 'PUT',
      headers: {
        'Authorization': 'Bearer ' + localStorage.getItem('access_token')
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      alert('设备已设为受信任');
      this.loadDevices();
    } else {
      alert('操作失败: ' + result.message);
    }
  }
}

// 初始化设备管理
const deviceManagement = new DeviceManagement();
deviceManagement.loadDevices();
```

#### 4.3 登录历史页面

```javascript
// 登录历史页面组件
class LoginHistory {
  constructor() {
    this.history = [];
    this.currentPage = 1;
    this.pageSize = 20;
    this.total = 0;
  }

  // 加载登录历史
  async loadHistory(page = 1) {
    const response = await fetch(`/api/v1/user/devices/login-history?page=${page}&page_size=${this.pageSize}`, {
      headers: {
        'Authorization': 'Bearer ' + localStorage.getItem('access_token')
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      this.history = result.data.history;
      this.total = result.data.total;
      this.currentPage = result.data.page;
      this.renderHistory();
      this.renderPagination();
    }
  }

  // 渲染历史记录
  renderHistory() {
    const container = document.getElementById('history-list');
    container.innerHTML = '';

    this.history.forEach(item => {
      const historyElement = this.createHistoryElement(item);
      container.appendChild(historyElement);
    });
  }

  // 创建历史记录元素
  createHistoryElement(item) {
    const div = document.createElement('div');
    div.className = `history-item ${item.is_success ? 'success' : 'failed'}`;

    const riskLevels = ['正常', '低风险', '中风险', '高风险'];
    const riskClass = ['normal', 'low', 'medium', 'high'];

    div.innerHTML = `
      <div class="history-info">
        <h4>${item.device_name}</h4>
        <p>登录方式: ${this.getLoginTypeText(item.login_type)}</p>
        <p>IP地址: ${item.ip_address} | 地点: ${item.location || '未知'}</p>
        <p>时间: ${new Date(item.created_at).toLocaleString()}</p>
        ${!item.is_success ? `<p class="error">失败原因: ${item.failure_reason}</p>` : ''}
      </div>
      <div class="history-status">
        <span class="status ${item.is_success ? 'success' : 'failed'}">
          ${item.is_success ? '成功' : '失败'}
        </span>
        <span class="risk ${riskClass[item.risk_level]}">
          ${riskLevels[item.risk_level]}
        </span>
      </div>
    `;

    return div;
  }

  // 获取登录类型文本
  getLoginTypeText(type) {
    const types = {
      'password': '密码登录',
      'sms': '短信验证码',
      'wechat': '微信登录',
      'wx_mini': '微信小程序',
      'wx_web': '微信网页'
    };
    return types[type] || type;
  }

  // 渲染分页
  renderPagination() {
    const totalPages = Math.ceil(this.total / this.pageSize);
    const pagination = document.getElementById('pagination');

    pagination.innerHTML = '';

    for (let i = 1; i <= totalPages; i++) {
      const button = document.createElement('button');
      button.textContent = i;
      button.className = i === this.currentPage ? 'active' : '';
      button.onclick = () => this.loadHistory(i);
      pagination.appendChild(button);
    }
  }
}

// 初始化登录历史
const loginHistory = new LoginHistory();
loginHistory.loadHistory();
```

### 5. CSS样式建议

```css
/* 设备管理样式 */
.device-item {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-item.current {
  border-color: #007bff;
  background-color: #f8f9ff;
}

.current-badge {
  background-color: #007bff;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.device-actions button {
  margin-left: 8px;
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.device-actions button:hover {
  background-color: #f5f5f5;
}

/* 登录历史样式 */
.history-item {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-item.failed {
  border-color: #dc3545;
  background-color: #fff5f5;
}

.status.success {
  color: #28a745;
}

.status.failed {
  color: #dc3545;
}

.risk.normal {
  color: #28a745;
}

.risk.low {
  color: #ffc107;
}

.risk.medium {
  color: #fd7e14;
}

.risk.high {
  color: #dc3545;
}

/* 分页样式 */
#pagination button {
  margin: 0 4px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
}

#pagination button.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}
```

### 6. 注意事项

1. **设备ID持久化**: 设备ID应该持久化存储，建议使用localStorage
2. **Token管理**: 新的Token包含设备信息，需要相应更新Token管理逻辑
3. **错误处理**: 增加对新错误类型的处理，如设备数量超限等
4. **用户体验**: 新设备登录和风险登录应该有相应的用户提示
5. **兼容性**: 设备信息是可选的，确保不影响现有登录流程
6. **安全性**: 敏感操作（如登出所有设备）应该有二次确认

### 7. 测试建议

1. **多设备测试**: 在不同设备/浏览器上测试登录和设备管理功能
2. **Token刷新测试**: 测试Token过期后的自动刷新机制
3. **异常情况测试**: 测试网络异常、服务器错误等情况的处理
4. **用户体验测试**: 测试新设备登录提示、风险警告等用户交互

这个指导文档涵盖了前端适配多端多设备登录功能的所有关键点，开发者可以根据实际项目情况进行调整和实现。
```
