<!--
  外卖商家列表页面
  显示外卖商家列表，支持分类筛选、搜索等功能
-->
<template>
  <div class="takeout-merchants-page">
    <!-- 页面顶部 -->
    <div class="page-header">
      <h2 class="page-title">外卖商家</h2>
      <div class="location-actions">
        <el-button 
          v-if="!userStore.hasLocation" 
          type="primary" 
          size="small" 
          :loading="userStore.isGettingLocation"
          @click="handleGetLocation"
        >
          <el-icon><Location /></el-icon>
          获取位置
        </el-button>
        <div v-else class="location-info">
          <el-icon><Location /></el-icon>
          <span>已获取位置 ({{ userStore.currentLatitude?.toFixed(6) }}, {{ userStore.currentLongitude?.toFixed(6) }})</span>
          <el-button 
            type="primary" 
            size="small" 
            @click="handleRefreshLocation"
            :loading="userStore.isGettingLocation"
          >
            刷新
          </el-button>
        </div>
        <div v-if="userStore.locationError" class="location-error">
          <el-text type="danger" size="small">{{ userStore.locationError }}</el-text>
        </div>
      </div>
    </div>
    
    <!-- 分类导航 -->
    <el-card class="category-card">
      <div class="category-nav">
        <div class="global-categories">
          <el-scrollbar>
            <div class="global-category-list">
              <div 
                v-for="category in globalCategories" 
                :key="category.id"
                class="global-category-item"
                :class="{ active: currentGlobalCategory === category.id }"
                @click="selectGlobalCategory(category.id)"
              >
                <div class="category-icon" v-if="category.icon">
                  <img :src="adjustLinkProtocol(category.icon)" :alt="category.name" style="width: 24px; height: 24px; object-fit: cover;" />
                </div>
                <div class="category-name">{{ category.name }}</div>
              </div>
            </div>
          </el-scrollbar>
        </div>
        
        <el-divider />
        
        <div class="sub-categories" v-if="subCategories.length > 0">
          <el-scrollbar>
            <div class="sub-category-list">
              <div 
                class="sub-category-item"
                :class="{ active: currentCategory === null }"
                @click="selectCategory(null)"
              >
                全部
              </div>
              <div 
                v-for="category in subCategories" 
                :key="category.id"
                class="sub-category-item"
                :class="{ active: currentCategory === category.id }"
                @click="selectCategory(category.id)"
              >
                {{ category.name }}
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </el-card>
    
    <!-- 筛选栏 -->
    <div class="filter-bar">
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索商家"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
      
      <div class="sort-options">
        <el-radio-group v-model="sortOption" @change="handleSortChange">
          <el-radio-button label="default">默认排序</el-radio-button>
          <el-radio-button label="rating">评分最高</el-radio-button>
          <el-radio-button label="distance">距离最近</el-radio-button>
          <el-radio-button label="delivery_fee">配送费最低</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    
    <!-- 商家列表 -->
    <div class="merchant-list" v-loading="merchantStore.loading">
      <el-empty v-if="merchantStore.merchants.length === 0 && !merchantStore.loading" description="暂无相关商家" />
      
      <el-card 
        v-for="merchant in merchantStore.merchants" 
        :key="merchant.id" 
        class="merchant-card"
        @click="goToMerchantDetail(merchant.id)"
      >
        <div class="merchant-info">
          <div class="merchant-logo">
            <img :src="adjustLinkProtocol(merchant.logo)" :alt="merchant.name" />
          </div>
          <div class="merchant-content">
            <div class="merchant-header">
              <h3 class="merchant-name">{{ merchant.name }}</h3>
              <div class="merchant-status">
                <el-tag size="small" type="success" v-if="merchant.operation_status === 1">营业中</el-tag>
                <el-tag size="small" type="info" v-else>休息中</el-tag>
              </div>
            </div>
            
            <div class="merchant-rating">
              <el-rate
                v-model="merchant.rating"
                disabled
                text-color="#ff9900"
                score-template="{value}"
              />
              <span class="rating-count">{{ merchant.month_sales || 0 }}+ 月销量</span>
            </div>
            
            <!-- 促销活动信息 -->
            <div class="merchant-promotion" v-if="merchant.promotion_info || (merchant.promotions && merchant.promotions.length > 0)">
              <div class="promotion-banner">
                <div class="promotion-tag">
                  <el-icon><Present /></el-icon>
                  <span>促销优惠</span>
                </div>
                <div class="promotion-content">
                  <div class="promotion-title" v-if="merchant.promotion_info">
                    {{ merchant.promotion_info }}
                  </div>
                  <div class="promotion-details" v-if="merchant.promotions && merchant.promotions.length > 0">
                    <div 
                      v-for="promotion in merchant.promotions.slice(0, 2)" 
                      :key="promotion.id"
                      class="promotion-item"
                    >
                      <span class="promotion-name">{{ promotion.name }}</span>
                      <span class="promotion-desc" v-if="getPromotionDesc(promotion)">{{ getPromotionDesc(promotion) }}</span>
                    </div>
                    <div v-if="merchant.promotions.length > 2" class="more-promotions">
                      +{{ merchant.promotions.length - 2 }}个优惠
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="merchant-meta">
              <div class="meta-item">
                <el-icon><Timer /></el-icon>
                <span>{{ merchant.deliveryTime || 30 }}分钟</span>
              </div>
              <div class="meta-item">
                <el-icon><Location /></el-icon>
                <span>{{ merchant.address ? merchant.address.substring(0, 10) + (merchant.address.length > 10 ? '...' : '') : '未知位置' }}</span>
              </div>
              <div class="meta-item" v-if="getMerchantDistance(merchant)">
                <el-icon><Location /></el-icon>
                <span>{{ getMerchantDistance(merchant) }}</span>
              </div>
              <div class="meta-item">
                <el-icon><Bicycle /></el-icon>
                <span>配送费 ¥{{ (merchant.deliveryFee || 0).toFixed(2) }}</span>
              </div>
            </div>
            
            <div class="merchant-tags">
              <span class="min-amount">起送 ¥{{ (merchant.min_order_amount || 0).toFixed(2) }}</span>
              <div class="category-tags">
                <el-tag 
                  v-if="merchant.category_name"
                  size="small"
                  effect="plain"
                >
                  {{ merchant.category_name || '其他' }}
                </el-tag>
                <el-tag 
                  size="small"
                  effect="plain"
                  type="success"
                  v-if="merchant.operation_status === 1"
                >
                  营业中
                </el-tag>
                <el-tag 
                  size="small"
                  effect="plain"
                  type="info"
                  v-else
                >
                  休息中
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        background
        layout="prev, pager, next, jumper"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Search, Timer, Location, Bicycle, Present } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { takeoutService } from '../service/takeoutService';
import { useUserStore } from '../stores/userStore';
import { useMerchantStore } from '../stores/merchantStore';
import { adjustLinkProtocol } from '@/utils/format';

const router = useRouter();
const userStore = useUserStore();
const merchantStore = useMerchantStore();

// 状态
// 使用any类型来处理后端返回的实际数据结构
const globalCategories = ref<any[]>([]);
const subCategories = ref<any[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchKeyword = ref('');
const sortOption = ref('default');
const currentGlobalCategory = ref<number | string | null>(null);
const currentCategory = ref<number | string | null>(null);

/**
 * 加载全局分类
 */
async function loadGlobalCategories() {
  try {
    const response: any = await takeoutService.category.getMerchantCategoryList();
    console.log('商户分类列表:', response);
    
    // 从商户分类列表中提取list字段
    globalCategories.value = response?.list || [];
    
    // 默认选择第一个全局分类
    if (globalCategories.value.length > 0 && !currentGlobalCategory.value) {
      selectGlobalCategory(globalCategories.value[0].id);
    }
  } catch (error) {
    console.error('获取全局分类失败:', error);
    ElMessage.error('获取分类失败，请稍后重试');
  }
}

/**
 * 选择全局分类
 * @param id 分类ID
 */
function selectGlobalCategory(id: number | string) {
  currentGlobalCategory.value = id;
  currentCategory.value = null;
  loadSubCategories();
  currentPage.value = 1;
  loadMerchants();
}

/**
 * 加载子分类 - 目前系统只有一级分类
 */
async function loadSubCategories() {
  // 暂时不需要加载子分类，因为只有一级分类
  subCategories.value = [];
}

/**
 * 选择子分类
 * @param id 分类ID
 */
function selectCategory(id: number | string | null) {
  currentCategory.value = id;
  currentPage.value = 1;
  loadMerchants();
}

/**
 * 加载商家列表
 */
async function loadMerchants() {
  try {
    const params: any = {
      page: currentPage.value,
      pageSize: pageSize.value
    };
    
    // 添加分类过滤
    if (currentGlobalCategory.value) {
      params.globalCategoryId = currentGlobalCategory.value;
    }
    
    if (currentCategory.value) {
      params.categoryId = currentCategory.value;
    }
    
    // 添加搜索关键词
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value;
    }
    
    // 添加排序
    if (sortOption.value !== 'default') {
      params.sort = sortOption.value;
    }
    
    // 添加用户位置信息用于距离计算
    if (userStore.hasLocation) {
      params.latitude = userStore.currentLatitude;
      params.longitude = userStore.currentLongitude;
    }
    
    const result = await merchantStore.loadMerchants(params);
    total.value = result.total;
  } catch (error) {
    console.error('获取商家列表失败:', error);
    ElMessage.error('获取商家列表失败，请稍后重试');
  }
}

/**
 * 处理搜索
 */
function handleSearch() {
  currentPage.value = 1;
  loadMerchants();
}

/**
 * 处理排序变更
 */
function handleSortChange() {
  currentPage.value = 1;
  loadMerchants();
}

/**
 * 处理分页变更
 * @param page 页码
 */
function handlePageChange(page: number) {
  currentPage.value = page;
  loadMerchants();
}

/**
 * 获取商家距离当前位置的距离
 * @param merchant 商家信息
 * @returns 距离字符串或null
 */
function getMerchantDistance(merchant: any): string | null {
  if (!merchant.longitude || !merchant.latitude) {
    return null;
  }
  
  const distance = userStore.calculateMerchantDistance(merchant.latitude, merchant.longitude);
  
  if (distance === null) {
    return null;
  }
  
  // 根据距离显示不同的格式
  if (distance < 1) {
    return `${Math.round(distance * 1000)}m`;
  } else {
    return `${distance.toFixed(1)}km`;
  }
}

/**
 * 解析促销规则并返回优惠描述
 * @param promotion 促销活动信息
 * @returns 优惠描述字符串或null
 */
function getPromotionDesc(promotion: any): string | null {
  if (!promotion.rules) {
    return null;
  }
  
  try {
    const rules = JSON.parse(promotion.rules);
    
    // 处理满减活动
    if (promotion.type === 4 && rules.coupon) {
      const coupon = rules.coupon;
      const minAmount = coupon.min_order_amount || 0;
      const amount = coupon.amount || 0;
      const perUserLimit = coupon.per_user_limit || 0;
      
      let desc = `满${minAmount}元减${amount}元`;
      
      if (perUserLimit > 0) {
        desc += `，每人限用${perUserLimit}次`;
      }
      
      return desc;
    }
    
    return null;
  } catch (error) {
    console.error('解析促销规则失败:', error);
    return null;
  }
}

/**
 * 处理获取位置
 */
async function handleGetLocation() {
  try {
    const success = await userStore.getCurrentLocation();
    if (success) {
      ElMessage.success('位置获取成功');
    } else {
      ElMessage.warning('位置获取失败，请检查浏览器权限设置');
    }
  } catch (error) {
    console.error('获取位置失败:', error);
    ElMessage.error('获取位置失败');
  }
}

/**
 * 处理刷新位置
 */
async function handleRefreshLocation() {
  try {
    const success = await userStore.getCurrentLocation();
    if (success) {
      ElMessage.success('位置刷新成功');
    } else {
      ElMessage.warning('位置刷新失败，请检查浏览器权限设置');
    }
  } catch (error) {
    console.error('刷新位置失败:', error);
    ElMessage.error('刷新位置失败');
  }
}

/**
 * 跳转到商家详情页
 * @param id 商家ID
 */
function goToMerchantDetail(id: string | number) {
  router.push(`/user/takeout/${id}`);
}

// 监视全局分类变化，目前不需要加载子分类
watch(currentGlobalCategory, (newVal) => {
  if (newVal) {
    loadSubCategories();
  }
});

onMounted(async () => {
  // 尝试获取用户当前位置
  try {
    const hasStoredLocation = await userStore.loadLocationFromStorage();
    if (!hasStoredLocation) {
      // 如果没有存储的位置，尝试获取当前位置
      await userStore.getCurrentLocation();
    }
  } catch (error) {
    console.log('获取位置信息失败，将不显示距离信息:', error);
  }
  
  // 加载全局分类
  loadGlobalCategories();
  
  // 加载商家列表
  loadMerchants();
});
</script>

<style scoped>
.takeout-merchants-page {
  padding: 10px 0;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.location-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #67c23a;
  font-size: 14px;
}

.location-error {
  font-size: 12px;
}

.category-card {
  margin-bottom: 20px;
}

.category-nav {
  width: 100%;
}

.global-categories {
  margin-bottom: 10px;
}

.global-category-list {
  display: flex;
  gap: 15px;
  padding: 5px 0;
}

.global-category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 5px 15px;
  border-radius: 4px;
  transition: all 0.3s;
}

.global-category-item:hover {
  background-color: #f5f7fa;
}

.global-category-item.active {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.category-icon {
  margin-bottom: 5px;
  font-size: 20px;
}

.category-name {
  font-size: 14px;
}

.sub-category-list {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  flex-wrap: wrap;
}

.sub-category-item {
  padding: 6px 15px;
  border-radius: 16px;
  background-color: #f5f7fa;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 13px;
}

.sub-category-item:hover {
  background-color: #e6e8eb;
}

.sub-category-item.active {
  color: #fff;
  background-color: var(--el-color-primary);
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.search-box {
  width: 300px;
}

.merchant-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.merchant-card {
  cursor: pointer;
  transition: all 0.3s;
}

.merchant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.merchant-info {
  display: flex;
  gap: 15px;
}

.merchant-logo {
  width: 80px;
  height: 80px;
  overflow: hidden;
  border-radius: 4px;
  flex-shrink: 0;
}

.merchant-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.merchant-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.merchant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.merchant-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.merchant-rating {
  display: flex;
  align-items: center;
  gap: 5px;
}

.rating-count {
  color: #909399;
  font-size: 13px;
}

.merchant-meta {
  display: flex;
  gap: 15px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #606266;
  font-size: 13px;
}

.merchant-tags {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.min-amount {
  color: #f56c6c;
  font-size: 13px;
}

.category-tags {
  display: flex;
  gap: 5px;
  align-items: center;
  font-size: 13px;
  color: #909399;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

/* 促销活动样式 */
.merchant-promotion {
  margin: 8px 0;
}

.promotion-banner {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  border-radius: 6px;
  padding: 8px 12px;
  color: white;
  font-size: 12px;
}

.promotion-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
  margin-bottom: 4px;
}

.promotion-tag .el-icon {
  font-size: 14px;
}

.promotion-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.promotion-title {
  font-weight: 500;
  font-size: 13px;
  line-height: 1.3;
}

.promotion-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.promotion-item {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.promotion-name {
  font-weight: 500;
  font-size: 12px;
}

.promotion-desc {
  font-size: 11px;
  opacity: 0.9;
  line-height: 1.2;
}

.more-promotions {
  font-size: 11px;
  opacity: 0.8;
  font-style: italic;
  margin-top: 2px;
}
</style>
