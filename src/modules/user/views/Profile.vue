<!--
  用户个人中心页面
  管理用户个人资料、头像等信息
-->
<template>
  <div class="user-profile-page">
    <el-row :gutter="20">
      <!-- 左侧信息卡片 -->
      <el-col :span="8">
        <AppCard class="profile-card">
          <div class="avatar-container">
            <el-avatar :size="120" :src="userInfo.avatar || defaultAvatar" />
            <el-upload
              class="avatar-uploader"
              action="#"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleAvatarChange"
            >
              <div class="upload-trigger">
                <el-icon><Edit /></el-icon>
                <span>更换头像</span>
              </div>
            </el-upload>
          </div>
          
          <div class="user-basic-info">
            <h2>{{ userInfo.nickname || userInfo.username }}</h2>
            <p class="user-id">ID: {{ userInfo.id }}</p>
            <p class="join-time">注册时间: {{ formatDate(userInfo.createdAt) }}</p>
          </div>
          
          <div class="user-stats">
            <div class="stat-item">
              <h3>{{ userAssets.balance || 0 }}元</h3>
              <p>账户余额</p>
            </div>
            <div class="stat-item">
              <h3>{{ userAssets.points || 0 }}</h3>
              <p>积分</p>
            </div>
          </div>
        </AppCard>
      </el-col>
      
      <!-- 右侧个人资料 -->
      <el-col :span="16">
        <AppCard class="profile-form-card">
          <template #header>
            <div class="form-header">
              <h3>个人资料</h3>
              <el-button v-if="!editing" type="primary" @click="startEditing">编辑</el-button>
              <div v-else class="edit-actions">
                <el-button @click="cancelEditing">取消</el-button>
                <el-button type="primary" @click="saveProfile" :loading="saving">保存</el-button>
              </div>
            </div>
          </template>
          
          <AppForm 
            ref="profileForm"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            class="profile-form"
          >
            <AppFormItem label="用户名" prop="username">
              <AppInput 
                v-model="formData.username" 
                :disabled="true" 
                placeholder="用户名不可修改"
              />
            </AppFormItem>
            
            <AppFormItem label="昵称" prop="nickname">
              <AppInput 
                v-model="formData.nickname" 
                :disabled="!editing" 
                placeholder="请输入昵称"
              />
            </AppFormItem>
            
            <AppFormItem label="性别" prop="gender">
              <el-radio-group v-model="formData.gender" :disabled="!editing">
                <el-radio label="male">男</el-radio>
                <el-radio label="female">女</el-radio>
                <el-radio label="other">其他</el-radio>
                <el-radio label="unknown">未知</el-radio>
              </el-radio-group>
            </AppFormItem>
            
            <AppFormItem label="生日" prop="birthday">
              <el-date-picker
                v-model="formData.birthday"
                type="date"
                placeholder="选择生日"
                :disabled="!editing"
                value-format="YYYY-MM-DD"
              />
            </AppFormItem>
            
            <AppFormItem label="手机号" prop="phone">
              <div class="phone-input">
                <AppInput 
                  v-model="formData.phone" 
                  :disabled="true" 
                  placeholder="未绑定手机号"
                />
                <el-button 
                  v-if="!formData.phone" 
                  type="primary" 
                  @click="showBindPhoneDialog" 
                  :disabled="!editing"
                >
                  绑定
                </el-button>
                <el-button 
                  v-else 
                  type="primary" 
                  @click="showUpdatePhoneDialog" 
                  :disabled="!editing"
                >
                  更换
                </el-button>
              </div>
            </AppFormItem>
            
            <AppFormItem label="邮箱" prop="email">
              <div class="email-input">
                <AppInput 
                  v-model="formData.email" 
                  :disabled="true" 
                  placeholder="未绑定邮箱"
                />
                <el-button 
                  v-if="!formData.email" 
                  type="primary" 
                  @click="showBindEmailDialog" 
                  :disabled="!editing"
                >
                  绑定
                </el-button>
                <el-button 
                  v-else 
                  type="primary" 
                  @click="showUpdateEmailDialog" 
                  :disabled="!editing"
                >
                  更换
                </el-button>
              </div>
            </AppFormItem>
          </AppForm>
        </AppCard>
        
        <AppCard class="security-card">
          <template #header>
            <div class="form-header">
              <h3>账号安全</h3>
            </div>
          </template>
          
          <div class="security-items">
            <div class="security-item">
              <div class="security-item-info">
                <h4>登录密码</h4>
                <p>建议定期修改密码，确保账号安全</p>
              </div>
              <el-button @click="showChangePasswordDialog">修改</el-button>
            </div>
            
            <div class="security-item">
              <div class="security-item-info">
                <h4>登录设备管理</h4>
                <p>查看并管理您的登录设备</p>
              </div>
              <el-button @click="navigateTo('/user/security/devices')">查看</el-button>
            </div>
          </div>
        </AppCard>
      </el-col>
    </el-row>
    
    <!-- 绑定手机号弹窗 -->
    <el-dialog v-model="bindPhoneDialogVisible" title="绑定手机号" width="400px">
      <AppForm :model="bindPhoneForm" :rules="bindPhoneRules" ref="bindPhoneFormRef">
        <AppFormItem label="手机号" prop="phone">
          <AppInput v-model="bindPhoneForm.phone" placeholder="请输入手机号"/>
        </AppFormItem>
        <AppFormItem label="验证码" prop="code">
          <div class="verification-code-input">
            <AppInput v-model="bindPhoneForm.code" placeholder="请输入验证码"/>
            <el-button 
              type="primary" 
              class="send-code-btn"
              :disabled="!canSendPhoneCode || sendingPhoneCode"
              @click="sendPhoneCode"
            >
              {{ phoneCodeText }}
            </el-button>
          </div>
        </AppFormItem>
      </AppForm>
      <template #footer>
        <el-button @click="bindPhoneDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBindPhone" :loading="binding">确认</el-button>
      </template>
    </el-dialog>
    
    <!-- 修改密码弹窗 -->
    <el-dialog v-model="changePasswordDialogVisible" title="修改密码" width="400px">
      <AppForm :model="passwordForm" :rules="passwordRules" ref="passwordFormRef">
        <AppFormItem label="旧密码" prop="oldPassword">
          <AppInput 
            v-model="passwordForm.oldPassword" 
            type="password" 
            show-password
            placeholder="请输入旧密码"
          />
        </AppFormItem>
        <AppFormItem label="新密码" prop="newPassword">
          <AppInput 
            v-model="passwordForm.newPassword" 
            type="password" 
            show-password
            placeholder="请输入新密码"
          />
        </AppFormItem>
        <AppFormItem label="确认密码" prop="confirmPassword">
          <AppInput 
            v-model="passwordForm.confirmPassword" 
            type="password" 
            show-password
            placeholder="请再次输入新密码"
          />
        </AppFormItem>
      </AppForm>
      <template #footer>
        <el-button @click="changePasswordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitChangePassword" :loading="changingPassword">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useUserStore } from '../stores/userStore';

import { sendSmsCode, changePassword } from '../api/auth';
import { updateUserProfile, uploadAvatar, bindPhone } from '../api/profile';
import AppCard from '@/components/base/AppCard.vue';
import AppForm from '@/components/form/AppForm.vue';
import AppFormItem from '@/components/form/AppFormItem.vue';
import AppInput from '@/components/form/AppInput.vue';

const router = useRouter();
const userStore = useUserStore();
const profileForm = ref(); // 引用表单DOM元素
const defaultAvatar = '/images/default_avatar.png';

// 用户信息
const userInfo = reactive({
  id: '',
  username: '',
  nickname: '',
  avatar: '',
  phone: '',
  email: '',
  gender: 'unknown',
  birthday: '',
  createdAt: '',
  updatedAt: ''
});

// 用户资产
const userAssets = reactive({
  balance: 0,
  points: 0
});

// 编辑状态
const editing = ref(false);
const saving = ref(false);

// 表单对象
const formData = reactive({
  username: '',
  nickname: '',
  gender: 'unknown' as 'male' | 'female' | 'other' | 'unknown',
  birthday: '',
  phone: '',
  email: ''
});

// 表单规则
const formRules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ]
};

// 绑定手机号弹窗
const bindPhoneDialogVisible = ref(false);
const bindPhoneFormRef = ref();
const binding = ref(false);
const sendingPhoneCode = ref(false);
const phoneCodeCountdown = ref(0);
const bindPhoneForm = reactive({
  phone: '',
  code: ''
});

const bindPhoneRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码应为6位数字', trigger: 'blur' }
  ]
};

// 是否可以发送手机验证码
const canSendPhoneCode = computed(() => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(bindPhoneForm.phone) && phoneCodeCountdown.value === 0;
});

// 验证码按钮文字
const phoneCodeText = computed(() => {
  return phoneCodeCountdown.value > 0 ? 
    `重新发送(${phoneCodeCountdown.value}s)` : 
    '获取验证码';
});

// 修改密码弹窗
const changePasswordDialogVisible = ref(false);
const passwordFormRef = ref();
const changingPassword = ref(false);
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入旧密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (_rule: any, value: string, callback: any) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
};

/**
 * 格式化日期
 */
function formatDate(dateString: string) {
  if (!dateString) return '未知';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
}

/**
 * 开始编辑
 */
function startEditing() {
  editing.value = true;
}

/**
 * 取消编辑
 */
function cancelEditing() {
  editing.value = false;
  
  // 恢复原始数据
  Object.assign(formData, {
    username: userInfo.username,
    nickname: userInfo.nickname,
    gender: userInfo.gender,
    birthday: userInfo.birthday,
    phone: userInfo.phone,
    email: userInfo.email
  });
}

/**
 * 保存个人资料
 */
async function saveProfile() {
  try {
    saving.value = true;
    
    // 调用API保存资料
    await updateUserProfile({
      nickname: formData.nickname,
      gender: formData.gender,
      birthday: formData.birthday
    });
    
    // 更新本地用户信息
    Object.assign(userInfo, {
      nickname: formData.nickname,
      gender: formData.gender,
      birthday: formData.birthday
    });
    
    // 更新用户存储
    userStore.updateUserInfo({
      nickname: formData.nickname,
      gender: formData.gender,
      birthday: formData.birthday
    });
    
    ElMessage.success('保存成功');
    editing.value = false;
  } catch (error) {
    console.error('保存资料失败:', error);
    ElMessage.error('保存失败，请稍后重试');
  } finally {
    saving.value = false;
  }
}

/**
 * 处理头像变更
 */
async function handleAvatarChange(file: any) {
  if (!file) return;
  
  try {
    const formData = new FormData();
    formData.append('avatar', file.raw);
    
    // 上传头像
    const response = await uploadAvatar(file.raw);
    
    // 更新头像URL
    userInfo.avatar = response.avatarUrl;
    
    // 更新用户存储
    userStore.updateUserInfo({
      avatar: response.avatarUrl
    });
    
    ElMessage.success('头像更新成功');
  } catch (error) {
    console.error('上传头像失败:', error);
    ElMessage.error('上传头像失败，请稍后重试');
  }
}

/**
 * 显示绑定手机号弹窗
 */
function showBindPhoneDialog() {
  bindPhoneDialogVisible.value = true;
  bindPhoneForm.phone = '';
  bindPhoneForm.code = '';
}

/**
 * 显示更新手机号弹窗
 */
function showUpdatePhoneDialog() {
  // 复用绑定手机号的弹窗，但标题和内容不同
  bindPhoneDialogVisible.value = true;
  bindPhoneForm.phone = '';
  bindPhoneForm.code = '';
}

/**
 * 发送手机验证码
 */
async function sendPhoneCode() {
  if (!canSendPhoneCode.value) return;
  
  try {
    sendingPhoneCode.value = true;
    await sendSmsCode(bindPhoneForm.phone, 'bind');
    startPhoneCodeCountdown();
    ElMessage.success('验证码已发送，请注意查收');
  } catch (error) {
    console.error('发送验证码失败:', error);
    ElMessage.error('发送验证码失败，请稍后重试');
  } finally {
    sendingPhoneCode.value = false;
  }
}

/**
 * 手机验证码倒计时
 */
function startPhoneCodeCountdown(seconds = 60) {
  phoneCodeCountdown.value = seconds;
  const timer = setInterval(() => {
    phoneCodeCountdown.value--;
    if (phoneCodeCountdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
}

/**
 * 提交绑定手机号
 */
async function submitBindPhone() {
  if (!bindPhoneFormRef.value) return;
  
  try {
    await bindPhoneFormRef.value.validate();
    
    binding.value = true;
    
    // 调用绑定手机号API
    await bindPhone(bindPhoneForm.phone, bindPhoneForm.code);
    
    // 更新本地用户信息
    userInfo.phone = bindPhoneForm.phone;
    formData.phone = bindPhoneForm.phone;
    
    // 更新用户存储
    userStore.updateUserInfo({
      phone: bindPhoneForm.phone
    });
    
    ElMessage.success('手机号绑定成功');
    bindPhoneDialogVisible.value = false;
  } catch (error) {
    console.error('绑定手机号失败:', error);
    ElMessage.error('绑定失败，请检查手机号和验证码');
  } finally {
    binding.value = false;
  }
}

/**
 * 显示绑定邮箱弹窗
 */
function showBindEmailDialog() {
  ElMessage.info('邮箱绑定功能即将上线');
}

/**
 * 显示更新邮箱弹窗
 */
function showUpdateEmailDialog() {
  ElMessage.info('邮箱更新功能即将上线');
}

/**
 * 显示修改密码弹窗
 */
function showChangePasswordDialog() {
  changePasswordDialogVisible.value = true;
  passwordForm.oldPassword = '';
  passwordForm.newPassword = '';
  passwordForm.confirmPassword = '';
}

/**
 * 提交修改密码
 */
async function submitChangePassword() {
  if (!passwordFormRef.value) return;
  
  try {
    await passwordFormRef.value.validate();
    
    changingPassword.value = true;
    
    // 调用修改密码API
    await changePassword(passwordForm.oldPassword, passwordForm.newPassword);
    
    ElMessage.success('密码修改成功，请重新登录');
    changePasswordDialogVisible.value = false;
    
    // 退出登录，跳转到登录页面
    await userStore.userLogout();
    router.push('/user/login');
  } catch (error) {
    console.error('修改密码失败:', error);
    ElMessage.error('修改密码失败，请检查旧密码是否正确');
  } finally {
    changingPassword.value = false;
  }
}

/**
 * 导航到指定页面
 */
function navigateTo(path: string) {
  router.push(path);
}

// 加载用户数据
onMounted(async () => {
  // 确保用户已登录
  if (!userStore.isLoggedIn) {
    router.push('/user/login');
    return;
  }
  
  // 从存储中获取用户信息
  const userData = userStore.userInfo;
  if (userData) {
    Object.assign(userInfo, userData);
    
    // 初始化表单数据
    Object.assign(formData, {
      username: userData.username || '',
      nickname: userData.nickname || '',
      gender: userData.gender || 'unknown',
      birthday: userData.birthday || '',
      phone: userData.phone || '',
      email: userData.email || ''
    });
  }
  
  // 模拟获取用户资产
  userAssets.balance = 320.50;
  userAssets.points = 1250;
});
</script>

<style scoped>
.user-profile-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.profile-card, .profile-form-card, .security-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  margin-bottom: 20px;
}

.avatar-uploader {
  margin-top: 10px;
}

.upload-trigger {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #409EFF;
}

.upload-trigger .el-icon {
  margin-right: 5px;
}

.user-basic-info {
  text-align: center;
  margin-bottom: 20px;
}

.user-basic-info h2 {
  margin: 10px 0 5px;
  font-size: 20px;
}

.user-id, .join-time {
  margin: 5px 0;
  color: #909399;
  font-size: 14px;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
}

.stat-item {
  text-align: center;
}

.stat-item h3 {
  margin: 0;
  font-size: 20px;
  color: #409EFF;
}

.stat-item p {
  margin: 5px 0 0;
  font-size: 14px;
  color: #606266;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
}

.form-header h3 {
  margin: 0;
  font-size: 18px;
}

.edit-actions {
  display: flex;
  gap: 10px;
}

.profile-form {
  padding: 20px 0;
}

.phone-input, .email-input {
  display: flex;
  gap: 10px;
}

.verification-code-input {
  display: flex;
  gap: 10px;
}

.send-code-btn {
  flex-shrink: 0;
  width: 120px;
}

.security-items {
  padding: 10px 0;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #EBEEF5;
}

.security-item:last-child {
  border-bottom: none;
}

.security-item-info h4 {
  margin: 0 0 5px;
  font-size: 16px;
}

.security-item-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}
</style>
