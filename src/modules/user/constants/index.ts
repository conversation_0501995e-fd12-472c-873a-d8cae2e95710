/**
 * 用户模块常量定义
 * 提供模块内共享的常量值
 */

// 用户角色
export const USER_ROLES = {
  NORMAL: 'normal',        // 普通用户
  VIP: 'vip',              // VIP用户
  ADMIN: 'admin'           // 管理员
};

// 用户等级
export const USER_LEVELS = [
  { value: 1, label: '普通会员', minPoints: 0 },
  { value: 2, label: '银卡会员', minPoints: 1000 },
  { value: 3, label: '金卡会员', minPoints: 5000 },
  { value: 4, label: '钻石会员', minPoints: 10000 }
];

// 默认头像
export const DEFAULT_AVATAR = '/images/default_avatar.png';

// 订单处理相关常量
export const ORDER_ACTIONS = {
  PAY: 'pay',                  // 支付
  CANCEL: 'cancel',            // 取消
  REFUND: 'refund',            // 退款
  CONFIRM: 'confirm',          // 确认收货
  REVIEW: 'review',            // 评价
  VIEW_LOGISTICS: 'logistics'  // 查看物流
};

// 支付方式
export const PAYMENT_METHODS = [
  { value: 'alipay', label: '支付宝' },
  { value: 'wechat', label: '微信支付' },
  { value: 'creditcard', label: '信用卡' },
  { value: 'balance', label: '余额支付' }
];

// 订单过滤选项
export const ORDER_FILTER_OPTIONS = [
  { value: '', label: '全部订单' },
  { value: 10, label: '待付款' },
  { value: 20, label: '待发货' },
  { value: 40, label: '待收货' },
  { value: 50, label: '待评价' },
  { value: 50, label: '已完成' },
  { value: 60, label: '已取消' },
  { value: 70, label: '退款中' },
  { value: 80, label: '已退款' }
];

// 订单取消原因
export const ORDER_CANCEL_REASONS = [
  '我不想买了',
  '信息填写错误，重新下单',
  '卖家缺货',
  '同城见面交易',
  '其他原因'
];

// 退款原因
export const REFUND_REASONS = [
  '不想要了/拍错了',
  '商品缺货',
  '协商一致退款',
  '其他原因'
];

// 验证码类型
export const SMS_CODE_TYPES = {
  LOGIN: 'login',          // 登录
  REGISTER: 'register',    // 注册
  RESET: 'reset',          // 重置密码
  BIND: 'bind'             // 绑定手机
};

// 验证规则
export const VALIDATION_RULES = {
  USERNAME: {
    pattern: /^[a-zA-Z0-9_]{3,20}$/,
    message: '用户名为3-20个字符，支持字母、数字和下划线'
  },
  PASSWORD: {
    pattern: /^.{6,20}$/,
    message: '密码长度为6-20个字符'
  },
  PHONE: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号码'
  },
  EMAIL: {
    pattern: /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/,
    message: '请输入正确的邮箱地址'
  },
  SMS_CODE: {
    pattern: /^\d{6}$/,
    message: '验证码为6位数字'
  }
};
