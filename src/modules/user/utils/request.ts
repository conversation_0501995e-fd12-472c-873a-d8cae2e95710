/**
 * 用户模块请求工具
 * 专门为用户模块处理HTTP请求，包含请求拦截器、响应拦截器和错误处理
 * 注意：runner模块也使用此请求工具
 */
import { nextTick } from 'vue';
import axios from 'axios';
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios';
import { ElMessage } from 'element-plus';
import router from '@/router';
import type { BaseResponse } from '@/modules/admin/constants/apiTypes';
import { getApiBaseUrl } from '@/utils/apiConfig';
import localforage from 'localforage';

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// token过期前的提前刷新时间（这里设置为5分钟）
const TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5分钟，单位毫秒

// 标志位，用于控制是否正在刷新token
let isRefreshingToken = false;
// 上次刷新token的时间
let lastTokenRefreshTime = 0;
// 刷新token的最小间隔时间（10秒）
const TOKEN_REFRESH_INTERVAL = 10 * 1000;

// URL 白名单，不需要权限认证的请求路径
const URL_WHITE_LIST = [
  // runner模块白名单
  '/v1/runner/login',
  '/v1/runner/refresh-token',
  '/v1/runner/resetpassword',
  // user模块白名单
  '/v1/user/login',
  '/v1/user/refresh-token',
  '/v1/user/resetpassword'
];

// 模块命名空间（user和runner都使用user的token）
const NAMESPACE = 'user';

/**
 * 从URL中提取namespace（runner 或 user）
 * @param url 请求URL
 * @returns 提取的namespace
 */
function extractNamespaceFromUrl(url: string | undefined): string | null {
  if (!url) return null;
  // 统一使用单数形式匹配namespace，支持/api/v1/和/v1/开头的路径
  const match = url.match(/\/(?:api\/)?v1\/(runner|user)\//); 
  if (!match) return null;
  
  console.log(`从 URL ${url} 中提取的命名空间是: ${match[1]}`);
  return match[1];
}

/**
 * 检查URL是否在白名单中
 * @param url 请求URL
 * @returns 是否在白名单中
 */
function isUrlInWhiteList(url: string | undefined): boolean {
  if (!url) return false;
  return URL_WHITE_LIST.some(whiteUrl => url.includes(whiteUrl));
}

// 检查token是否需要刷新
async function checkAndRefreshToken(config: InternalAxiosRequestConfig): Promise<string | null> {
  const tokenKey = `${NAMESPACE}_access_token`;
  const tokenExpiryKey = `${NAMESPACE}_token_expiry`;
  
  // 如果是刷新token的请求或白名单内的请求，直接返回当前token（如果有）
  if (config.url && isUrlInWhiteList(config.url)) {
    const token = await localforage.getItem<string>(tokenKey);
    return token || null;
  }
  
  const token = await localforage.getItem<string>(tokenKey);
  const tokenExpireTime = await localforage.getItem<string>(tokenExpiryKey);
  
  if (!token) {
    return null;
  }
  
  // 如果token存在但过期时间不存在，设置一个默认过期时间（24小时）
  const now = Date.now();
  let expireTime = 0;
  if (!tokenExpireTime) {
    console.warn(`Token expiry time not found for user module, using default expiry time`);
    // 设置一个默认的过期时间（当前时间+24小时）
    expireTime = now + 24 * 60 * 60 * 1000;
    // 保存默认过期时间以便后续使用
    await localforage.setItem(tokenExpiryKey, expireTime.toString());
  } else {
    expireTime = parseInt(tokenExpireTime);
  }
  
  // 如果token已经过期，强制刷新
  if (now >= expireTime) {
    console.warn(`Token has expired for user module, must refresh before proceeding`);
    return await refreshTokenProcess(token);
  }
  
  // 如果token即将过期（阈值内）
  if (now >= expireTime - TOKEN_REFRESH_THRESHOLD) {
    console.log(`Token will expire soon for user module, refreshing...`);
    // 立即刷新token但不阻塞当前请求
    refreshTokenProcess(token).catch(err => {
      console.error(`Background token refresh failed for user module`, err);
    });
  }

  return token;
}

// 提取token刷新逻辑到独立函数
async function refreshTokenProcess(currentToken: string): Promise<string> {
  // 检查是否正在刷新token或者刚刚刷新过
  const now = Date.now();
  if (isRefreshingToken || (now - lastTokenRefreshTime < TOKEN_REFRESH_INTERVAL)) {
    console.log(`Token refresh in progress or recently refreshed for user module. Using current token.`);
    return currentToken;
  }
  
  isRefreshingToken = true;
  try {
    // 导入user store（user和runner都使用userStore）
    const { useUserStore } = await import('@/modules/user/stores/userStore');
    const userStore = useUserStore();
    const success = await userStore.loginByLongTermTokenAction();
    
    // 记录刷新时间
    lastTokenRefreshTime = Date.now();
    isRefreshingToken = false;
    
    if (success) {
      await nextTick();
      console.log(`Token refreshed successfully for user module`);
      // 返回新的token
      const newToken = await localforage.getItem<string>(`${NAMESPACE}_access_token`);
      return newToken || currentToken; // 如果获取不到新token，则返回当前token
    } else {
      console.log(`Token refresh failed for user module`);
      // 清除长期token
      await localforage.removeItem(`${NAMESPACE}_refresh_token`);
      return currentToken;
    }
  } catch (error) {
    console.error(`Token refresh failed for user module:`, error);
    // 清除长期token
    await localforage.removeItem(`${NAMESPACE}_refresh_token`);
    isRefreshingToken = false;
    return currentToken; // 返回旧token，让请求继续，可能会在响应中处理401错误
  }
}

// 请求拦截器
service.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    console.log('User request interceptor config url is:', config.url);
    
    // 检查并刷新token
    const token = await checkAndRefreshToken(config);
    console.log(`获取的user token：${token ? '存在' : '不存在'}, URL: ${config.url}`);
    
    // 如果是白名单中的请求，则不添加token（除非是刷新token的请求需要带上旧token）
    const isRefreshTokenRequest = config.url?.includes(`/v1/user/refresh-token`) || config.url?.includes(`/v1/runner/refresh-token`);
    const isInWhiteList = isUrlInWhiteList(config.url);
    console.log(`是否是刷新token请求：${isRefreshTokenRequest}, 是否在白名单中：${isInWhiteList}`);
    
    if ((token && !isInWhiteList) || isRefreshTokenRequest) {
      // 使用类型断言确保能够正确设置headers
      config.headers = config.headers || {};
      config.headers['Authorization'] = `Bearer ${token}`;
      console.log(`设置Authorization头部：Bearer ${token?.substring(0, 10)}...`);
    } else {
      console.log(`不设置Authorization头部，token状态: ${token ? '存在' : '不存在'}, 是否在白名单: ${isInWhiteList}`);
    }
    
    return config;
  },
  (error) => {
    console.error('User request error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  async (response: AxiosResponse<BaseResponse<any>>) => {
    console.log('=== User响应拦截器开始 ===');
    console.log('响应URL:', response.config.url);
    console.log('响应状态码:', response.status);
    console.log('响应数据:', response.data);
    
    const { code, message, data } = response.data || {};
    console.log('=== User响应拦截器处理非200状态码 ===');
    console.log('状态码:', code, '消息:', message);
    
    // 请求成功
    if (code === 200) {
      console.log('=== User响应拦截器处理成功 ===');
      if(data) {
        return data;
      } else {
        return {message: 'success'};
      }
    }

    // 处理特定错误码
    switch (code) {
      case 401:
        // 防止重复执行401处理逻辑
        if (isRefreshingToken) {
          console.log('Already handling 401 error for user module, skipping duplicate handling');
          break;
        }
        
        // 未登录或token过期，清除user模块的token
        await localforage.removeItem(`${NAMESPACE}_access_token`);
        
        // 清除sessionStorage和localStorage中的token
        sessionStorage.removeItem(`${NAMESPACE}_access_token`);
        
        // 如果请求不是刷新token的请求，才尝试用长期token登录
        const refreshTokenUrls = [
          '/v1/runner/refresh-token',
          '/v1/user/refresh-token'
        ];
        const isRefreshTokenRequest = refreshTokenUrls.some(url => response.config.url?.includes(url));
        
        if (!isRefreshTokenRequest) {
          isRefreshingToken = true;
          try {
            // 导入user store
            const { useUserStore } = await import('@/modules/user/stores/userStore');
            const userStore = useUserStore();
            const longTermLoginSuccess = await userStore.loginByLongTermTokenAction();
            
            isRefreshingToken = false;
            lastTokenRefreshTime = Date.now();
            
            if (longTermLoginSuccess) {
              ElMessage.success('长期Token登录成功');
              break;
            }
          } catch (error) {
            console.error(`User long term token login failed:`, error);
            isRefreshingToken = false;
          }
        }
        
        // 如果长期token登录失败或者是刷新token请求本身失败，则跳转到登录页
        // 根据URL判断是跳转到user还是runner登录页
        const namespace = extractNamespaceFromUrl(response.config.url) || 'user';
        console.log('User拦截器跳转！', namespace);
        nextTick(() => {
          if (namespace === 'runner') {
            router.push('/user/login'); // runner使用user的登录页
          } else {
            router.push('/user/login');
          }
        });
        ElMessage.error('登录已过期，请重新登录');
        break;
      case 403:
        ElMessage.error('没有权限访问该资源');
        break;
      case 404:
        ElMessage.error('请求的资源不存在');
        break;
      case 500:
        ElMessage.error('服务器错误，请稍后重试');
        break;
      default:
        ElMessage.error(message || '请求失败');
    }

    return Promise.reject(new Error(message || '请求失败'));
  },
  (error) => {
    console.error('User response error:', error);
    const message = error.response?.data?.message || '网络错误，请稍后重试';
    ElMessage.error(message);
    return Promise.reject(error);
  }
);

// 封装GET请求
export async function get<T>(url: string, params?: any): Promise<T> {
  const response = await service.get<BaseResponse<T>>(url, { params });
  return response as any;
}

// 封装POST请求
export async function post<T>(url: string, data?: any, config?: any): Promise<T> {
  const response = await service.post<BaseResponse<T>>(url, data, config);
  console.log('user post请求响应', response);
  return response as any;
}

// 封装PUT请求
export async function put<T>(url: string, data?: any): Promise<T> {
  const response = await service.put<BaseResponse<T>>(url, data);
  return response as any;
}

// 封装DELETE请求
export async function del<T>(url: string, data?: any): Promise<T> {
  const response = await service.delete<BaseResponse<T>>(url, { data });
  return response as any;
}

// 封装PATCH请求
export async function patch<T>(url: string, data?: any): Promise<T> {
  const response = await service.patch<BaseResponse<T>>(url, data);
  return response as any;
}

export default service;