/**
 * 用户模块路由工具函数
 * 提供路由初始化和优化的导航功能，解决刷新和登录后白屏问题
 */
import { initializeRoutes } from '@/router';
import { ElMessage } from 'element-plus';

/**
 * 初始化路由并导航到目标页面
 * 使用Vue Router的导航方法并延迟导航以确保令牌刷新流程完成
 * 
 * @param targetPath 目标路径，默认为用户首页
 * @param message 可选的提示消息
 */
export async function initRouteAndNavigate(targetPath = '/user/home', message = '登录成功，正在跳转...') {
  try {
    // 显示成功消息
    if (message) {
      ElMessage.success(message);
    }
    
    // 初始化路由确保路由表完整
    await initializeRoutes();
    
    // 导入router对象
    const router = await import('@/router').then(module => module.default);
    
    // 使用延迟跳转，确保令牌刷新并发送到localStorage的流程完成
    setTimeout(() => {
      // 使用Vue Router的导航方法而不是原生导航，避免页面刷新
      router.push(targetPath);
      console.log(`跳转到${targetPath}完成`);
    }, 1000); // 增加延迟时间以确保令牌刷新完成
  } catch (error) {
    console.error('路由初始化失败:', error);
    ElMessage.error('系统错误，请重试');
  }
}
