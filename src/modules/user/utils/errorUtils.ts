/**
 * 用户模块错误处理工具
 * 提供组件错误捕获和防御性编程功能
 */

import { ElMessage } from 'element-plus';

/**
 * 安全执行异步函数
 * 捕获并记录错误，确保即使出错也不会阻止组件渲染
 * 
 * @param asyncFn 异步函数
 * @param errorMessage 错误提示消息
 * @param fallbackValue 发生错误时的返回值
 * @returns 异步函数的结果或fallbackValue
 */
export async function safeExecute<T>(
  asyncFn: () => Promise<T>, 
  errorMessage = '操作执行失败',
  fallbackValue: T | null = null
): Promise<T | null> {
  try {
    return await asyncFn();
  } catch (error) {
    console.error(`${errorMessage}:`, error);
    ElMessage.error(errorMessage);
    return fallbackValue;
  }
}

/**
 * 组件错误处理装饰器
 * 包装组件挂载和更新钩子，确保即使出错也能正常渲染
 * 
 * @param setupFn 组件setup函数
 * @returns 包装后的setup函数
 */
export function withErrorHandling(setupFn: Function): Function {
  return function(...args: any[]) {
    try {
      const result = setupFn(...args);
      
      // 处理异步setup
      if (result instanceof Promise) {
        return result.catch(error => {
          console.error('组件初始化失败:', error);
          ElMessage.error('页面初始化失败，请刷新重试');
          return {}; // 返回空对象避免进一步错误
        });
      }
      
      return result;
    } catch (error) {
      console.error('组件初始化失败:', error);
      ElMessage.error('页面初始化失败，请刷新重试');
      return {}; // 返回空对象避免进一步错误
    }
  };
}
