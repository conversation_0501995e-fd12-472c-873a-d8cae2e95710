/**
 * 外卖服务
 * 封装外卖相关的业务逻辑，提供给组件使用
 */
import {
  getCategories,
  getCategoryById,
  getMerchantCategories,
  getGlobalCategories,
  getGlobalCategoryTree,
  getMerchants,
  getMerchantFoods,
  getFoodById,
  getFoodVariants,
  getFoodComboItems,
  addToCart,
  updateCart,
  removeFromCart,
  getCartList,
  getCartCount,
  selectCartItems,
  checkoutCart,
  createOrder,
  createOrderLegacy,
  getOrderPayment,
  getOrderDetail,
  getOrderList,
  getOrderCount,
  cancelOrder,
  rateOrder,
  confirmReceived,
  getMerchantCategoryList
} from '../api/takeout';

import type { TakeoutCategory } from '../types';
import { TakeoutOrderStatus } from '../types';

/**
 * 分类服务
 */
export const categoryService = {

  /**
   * 获取商户分类列表
   * @returns 分类列表
   */
  async getMerchantCategoryList() {
    try {
      return await getMerchantCategoryList();
    } catch (error) {
      console.error('获取商户分类列表失败:', error);
      return { data: [] };
    }
  },

  /**
   * 获取分类列表
   * @returns 分类列表
   */
  async getCategories() {
    try {
      return await getCategories();
    } catch (error) {
      console.error('获取分类列表失败:', error);
      return { data: [] };
    }
  },

  /**
   * 获取特定商家的分类列表
   * @param merchantId 商家ID
   * @returns 分类列表
   */
  async getMerchantCategories(merchantId: string | number): Promise<TakeoutCategory[]> {
    try {
      const response = await getMerchantCategories(merchantId);
      // 响应拦截器已经处理了response.data
      // response直接就是分页数据对象
      return (response as any).list || [];
    } catch (error) {
      console.error('获取商家分类列表失败:', error);
      return [];
    }
  },

  /**
   * 获取分类详情
   * @param id 分类ID
   * @returns 分类详情
   */
  async getCategoryById(id: string | number) {
    try {
      return await getCategoryById(id);
    } catch (error) {
      console.error(`获取分类详情失败, ID: ${id}:`, error);
      return null;
    }
  },

  /**
   * 获取全局分类列表
   * @returns 全局分类列表
   */
  async getGlobalCategories() {
    try {
      return await getGlobalCategories();
    } catch (error) {
      console.error('获取全局分类列表失败:', error);
      return { data: [] };
    }
  },

  /**
   * 获取全局分类树
   * @returns 全局分类树
   */
  async getGlobalCategoryTree() {
    try {
      return await getGlobalCategoryTree();
    } catch (error) {
      console.error('获取全局分类树失败:', error);
      return { data: [] };
    }
  }
};

/**
 * 商家服务
 */
export const merchantService = {
  /**
   * 获取商家列表
   * @param params 查询参数
   * @returns 商家列表
   */
  async getMerchants(params?: {
    categoryId?: string | number;
    page?: number;
    pageSize?: number;
    sort?: string;
    [key: string]: any;
  }) {
    try {
      return await getMerchants(params);
    } catch (error) {
      console.error('获取商家列表失败:', error);
      return { data: [], total: 0, page: params?.page || 1, pageSize: params?.pageSize || 10 };
    }
  },

  /**
   * 获取指定商家的食品列表
   * @param id 商家ID
   * @param params 查询参数
   * @returns 食品列表
   */
  async getMerchantFood(id: string | number, params?: {
    categoryId?: string | number;
    page?: number;
    pageSize?: number;
    [key: string]: any;
  }) {
    try {
      return await getMerchantFoods(id, params);
    } catch (error) {
      console.error(`获取商家(ID: ${id})的食品列表失败:`, error);
      return { data: [], total: 0, page: params?.page || 1, pageSize: params?.pageSize || 10 };
    }
  }
};

/**
 * 食品服务
 */
export const foodService = {
  /**
   * 获取食品详情
   * @param id 食品ID
   * @returns 食品详情
   */
  async getFoodById(id: string | number) {
    try {
      return await getFoodById(id);
    } catch (error) {
      console.error(`获取食品详情失败, ID: ${id}:`, error);
      return null;
    }
  },

  /**
   * 获取食品规格
   * @param id 食品ID
   * @returns 食品规格列表
   */
  async getFoodVariants(id: string | number) {
    try {
      return await getFoodVariants(id);
    } catch (error) {
      console.error(`获取食品规格失败, ID: ${id}:`, error);
      return { data: [] };
    }
  },

  /**
   * 获取食品套餐项
   * @param id 食品ID
   * @returns 食品套餐项列表
   */
  async getFoodComboItems(id: string | number) {
    try {
      return await getFoodComboItems(id);
    } catch (error) {
      console.error(`获取食品套餐项失败, ID: ${id}:`, error);
      return { data: [] };
    }
  }
};

/**
 * 购物车服务
 */
export const cartService = {
  /**
   * 添加商品到购物车
   * @param data 购物车添加参数
   * @returns 添加结果
   */
  async addToCart(data: {
    food_id: string | number;
    quantity: number;
    variant_id?: string | number;
    combo_items?: Array<{
      item_id: string | number;
      quantity: number;
    }>;
    [key: string]: any;
  }) {
    try {
      return await addToCart(data);
    } catch (error) {
      console.error('添加商品到购物车失败:', error);
      return { success: false, message: '添加失败，请稍后重试' };
    }
  },

  /**
   * 更新购物车
   * @param data 购物车更新参数
   * @returns 更新结果
   */
  async updateCart(data: {
    cartItemId: string | number;
    quantity: number;
    [key: string]: any;
  }) {
    try {
      return await updateCart(data);
    } catch (error) {
      console.error('更新购物车失败:', error);
      return { success: false, message: '更新失败，请稍后重试' };
    }
  },

  /**
   * 从购物车移除商品
   * @param data 购物车移除参数
   * @returns 移除结果
   */
  async removeFromCart(data: {
    cartItemId: string | number | string[] | number[];
    [key: string]: any;
  }) {
    try {
      return await removeFromCart(data);
    } catch (error) {
      console.error('从购物车移除商品失败:', error);
      return { success: false, message: '移除失败，请稍后重试' };
    }
  },

  /**
   * 获取购物车列表
   * @returns 购物车商品列表
   */
  async getCartList() {
    try {
      return await getCartList();
    } catch (error) {
      console.error('获取购物车列表失败:', error);
      return { data: { items: [], totalPrice: 0, totalItems: 0 } };
    }
  },

  /**
   * 获取购物车商品数量
   * @returns 购物车商品数量
   */
  async getCartCount() {
    try {
      return await getCartCount();
    } catch (error) {
      console.error('获取购物车商品数量失败:', error);
      return { count: 0 };
    }
  },

  /**
   * 选择购物车商品
   * @param data 选择参数
   * @returns 选择结果
   */
  async selectCartItems(data: {
    cart_item_ids: string[] | number[];
    selected: boolean;
    [key: string]: any;
  }) {
    try {
      return await selectCartItems(data);
    } catch (error) {
      console.error('选择购物车商品失败:', error);
      return { success: false, message: '操作失败，请稍后重试' };
    }
  },

  /**
   * 结算购物车
   * @param data 结算参数
   * @returns 结算结果
   */
  async checkoutCart(data?: {
    cartItemIds?: string[] | number[];
    addressId?: string | number;
    [key: string]: any;
  }) {
    try {
      return await checkoutCart(data);
    } catch (error) {
      console.error('结算购物车失败:', error);
      return { success: false, message: '结算失败，请稍后重试' };
    }
  }
};

/**
 * 订单服务
 */
export const takeoutOrderService = {
  /**
   * 创建订单（新版多商家格式）
   * @param data 订单创建参数
   * @returns 创建的订单信息
   */
  async createOrder(data: {
    takeoutAddressID: number;
    paymentMethod: string;
    merchantOrders: Array<{
      merchantID: number;
      cartItemIDs: number[];
      couponID: number;
      deliveryTime: string;
      remark: string;
    }>;
  }) {
    try {
      return await createOrder(data);
    } catch (error) {
      console.error('创建多商家订单失败:', error);
      return { success: false, message: '创建订单失败，请稍后重试' };
    }
  },

  /**
   * 创建订单（旧版单商家格式，保持向后兼容）
   * @param data 订单创建参数
   * @returns 创建的订单信息
   */
  async createOrderLegacy(data: {
    cartItemIDs: string[] | number[];
    takeoutAddressID: string | number;
    paymentMethod: string;
    remark?: string;
    [key: string]: any;
  }) {
    try {
      return await createOrderLegacy(data);
    } catch (error) {
      console.error('创建订单失败:', error);
      return { success: false, message: '创建订单失败，请稍后重试' };
    }
  },

  /**
   * 获取订单支付信息
   * @param orderID 订单ID
   * @returns 订单支付信息
   */
  async getOrderPayment(orderID: string | number) {
    try {
      return await getOrderPayment(orderID);
    } catch (error) {
      console.error(`获取订单支付信息失败, 订单ID: ${orderID}:`, error);
      return null;
    }
  },

  /**
   * 获取订单详情
   * @param orderID 订单ID
   * @returns 订单详情
   */
  async getOrderDetail(orderID: string | number) {
    try {
      const result = await getOrderDetail(orderID);
      console.log('获取订单详情结果:', result);
      return result;
    } catch (error) {
      console.error(`获取订单详情失败, 订单ID: ${orderID}:`, error);
      return null;
    }
  },

  /**
   * 获取用户订单列表
   * @param params 查询参数
   * @returns 订单列表
   */
  async getOrderList(params?: {
    status?: string;
    page?: number;
    pageSize?: number;
    [key: string]: any;
  }) {
    try {
      return await getOrderList(params);
    } catch (error) {
      console.error('获取用户订单列表失败:', error);
      return { data: [], total: 0, page: params?.page || 1, pageSize: params?.pageSize || 10 };
    }
  },

  /**
   * 获取用户订单数量
   * @param params 查询参数
   * @returns 订单数量
   */
  async getOrderCount(params?: {
    status?: string;
    [key: string]: any;
  }) {
    try {
      return await getOrderCount(params);
    } catch (error) {
      console.error('获取用户订单数量失败:', error);
      return { count: 0 };
    }
  },

  /**
   * 取消订单
   * @param data 取消参数
   * @returns 取消结果
   */
  async cancelOrder(orderId: string | number, data: {
    reason?: string;
    [key: string]: any;
  }) {
    try {
      return await cancelOrder(orderId, data);
    } catch (error) {
      console.error('取消订单失败:', error);
      return { success: false, message: '取消订单失败，请稍后重试' };
    }
  },

  /**
   * 评价订单
   * @param data 评价参数
   * @returns 评价结果
   */
  async rateOrder(data: {
    orderId: string | number;
    rating: number;
    comment?: string;
    foodRatings?: Array<{
      foodId: string | number;
      rating: number;
      comment?: string;
    }>;
    [key: string]: any;
  }) {
    try {
      return await rateOrder(data);
    } catch (error) {
      console.error('评价订单失败:', error);
      return { success: false, message: '评价订单失败，请稍后重试' };
    }
  },

  /**
   * 确认收货
   * @param orderId 订单ID
   * @returns 确认结果
   */
  async confirmReceived(orderId: string | number) {
    try {
      return await confirmReceived(orderId);
    } catch (error) {
      console.error('确认收货失败:', error);
      return { success: false, message: '确认收货失败，请稍后重试' };
    }
  },

  /**
   * 获取订单状态显示文本
   * @param status 订单状态
   * @returns 状态显示文本
   */
  getOrderStatusText(status: TakeoutOrderStatus): string {
    const statusMap: Record<TakeoutOrderStatus, string> = {
      [TakeoutOrderStatus.PENDING]: '待支付',
      [TakeoutOrderStatus.PAID]: '已支付',
      [TakeoutOrderStatus.PROCESSING]: '处理中',
      [TakeoutOrderStatus.DELIVERING]: '配送中',
      [TakeoutOrderStatus.COMPLETED]: '已完成',
      [TakeoutOrderStatus.CANCELLED]: '已取消',
      [TakeoutOrderStatus.REFUNDING]: '退款中',
      [TakeoutOrderStatus.REFUNDED]: '已退款'
    };
    
    return statusMap[status] || '未知状态';
  }
};

// 导出合并的服务对象，方便统一引入
export const takeoutService = {
  category: categoryService,
  merchant: merchantService,
  food: foodService,
  cart: cartService,
  order: takeoutOrderService
};
