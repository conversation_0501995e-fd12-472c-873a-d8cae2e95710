/**
 * 地址服务
 * 封装用户地址管理相关的业务逻辑
 */
import { getUserAddresses, getUserAddressById, addUserAddress, updateUserAddress, deleteUserAddress, setDefaultAddress } from '../api/address';
import type { UserAddress } from '../types';

/**
 * 地址服务
 */
export const addressService = {
  /**
   * 获取用户所有地址
   * @returns 地址列表
   */
  async getAddresses(): Promise<UserAddress[]> {
    try {
      return await getUserAddresses();
    } catch (error) {
      console.error('获取地址列表失败:', error);
      return [];
    }
  },

  /**
   * 获取单个地址详情
   * @param id 地址ID
   * @returns 地址详情
   */
  async getAddressById(id: string): Promise<UserAddress | null> {
    try {
      return await getUserAddressById(id);
    } catch (error) {
      console.error('获取地址详情失败:', error);
      return null;
    }
  },

  /**
   * 获取默认地址
   * @param addresses 地址列表
   * @returns 默认地址，如不存在则返回null
   */
  getDefaultAddress(addresses: UserAddress[]): UserAddress | null {
    return addresses.find(address => address.is_default) || null;
  },

  /**
   * 添加新地址
   * @param addressData 地址数据
   * @returns 添加结果
   */
  async addAddress(addressData: Omit<UserAddress, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<boolean> {
    try {
      await addUserAddress(addressData);
      return true;
    } catch (error) {
      console.error('添加地址失败:', error);
      return false;
    }
  },

  /**
   * 更新地址
   * @param id 地址ID
   * @param addressData 地址数据
   * @returns 更新结果
   */
  async updateAddress(id: string, addressData: Partial<UserAddress>): Promise<boolean> {
    try {
      await updateUserAddress(id, addressData);
      return true;
    } catch (error) {
      console.error('更新地址失败:', error);
      return false;
    }
  },

  /**
   * 删除地址
   * @param id 地址ID
   * @returns 删除结果
   */
  async deleteAddress(id: string): Promise<boolean> {
    try {
      await deleteUserAddress(id);
      return true;
    } catch (error) {
      console.error('删除地址失败:', error);
      return false;
    }
  },

  /**
   * 设置默认地址
   * @param id 地址ID
   * @returns 设置结果
   */
  async setDefaultAddress(id: string): Promise<boolean> {
    try {
      await setDefaultAddress(id);
      return true;
    } catch (error) {
      console.error('设置默认地址失败:', error);
      return false;
    }
  },

  /**
   * 格式化地址
   * @param address 地址对象
   * @returns 格式化后的地址字符串
   */
  formatAddress(address: UserAddress): string {
    return `${address.province} ${address.city} ${address.district}`;
  },
  
  /**
   * 格式化手机号（中间4位显示为星号）
   * @param phone 手机号
   * @returns 格式化后的手机号
   */
  formatPhone(phone: string): string {
    if (!phone) return '';
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }
};
