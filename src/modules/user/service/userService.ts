/**
 * 用户服务
 * 封装用户相关的业务逻辑，提供给组件使用
 */
import { useUserStore } from '../stores/userStore';
import { register } from '../api/auth';
import { uploadAvatar, updateUserProfile } from '../api/profile';
import { USER_LEVELS } from '../constants';
import type { UserInfo, UserLoginParams, SmsLoginParams, UserRegisterParams } from '../types';

/**
 * 用户登录服务
 */
export const userAuthService = {
  /**
   * 用户密码登录
   * @param params 登录参数
   * @returns 登录结果
   */
  async login(params: UserLoginParams): Promise<boolean> {
    const userStore = useUserStore();
    try {
      const result = await userStore.userLogin(params);
      return result.success;
    } catch (error) {
      console.error('登录失败:', error);
      return false;
    }
  },

  /**
   * 短信验证码登录
   * @param params 短信登录参数
   * @returns 登录结果
   */
  async loginBySms(params: SmsLoginParams): Promise<boolean> {
    const userStore = useUserStore();
    try {
      const result = await userStore.userSmsLogin(params);
      return result.success;
    } catch (error) {
      console.error('短信登录失败:', error);
      return false;
    }
  },

  /**
   * 用户注册
   * @param params 注册参数
   * @returns 注册结果
   */
  async register(params: UserRegisterParams): Promise<boolean> {
    try {
      await register(params);
      return true;
    } catch (error) {
      console.error('注册失败:', error);
      return false;
    }
  },

  /**
   * 用户登出
   * @returns 登出结果
   */
  async logout(): Promise<boolean> {
    const userStore = useUserStore();
    try {
      return await userStore.userLogout();
    } catch (error) {
      console.error('登出失败:', error);
      return false;
    }
  },

  /**
   * 获取用户信息
   * @param forceRefresh 是否强制刷新
   * @returns 用户信息
   */
  async getUserInfo(forceRefresh = false): Promise<UserInfo | null> {
    const userStore = useUserStore();
    
    // 如果不强制刷新且已有用户信息，则直接返回
    if (!forceRefresh && userStore.userInfo) {
      return userStore.userInfo;
    }
    
    try {
      return await userStore.fetchUserInfo();
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  },

  /**
   * 检查用户是否已登录
   * @returns 登录状态
   */
  isLoggedIn(): boolean {
    const userStore = useUserStore();
    return userStore.isLoggedIn;
  }
};

/**
 * 用户资料服务
 */
export const userProfileService = {
  /**
   * 更新用户头像
   * @param file 头像文件
   * @returns 新头像URL
   */
  async updateAvatar(file: File): Promise<string | null> {
    const userStore = useUserStore();
    try {
      const result = await uploadAvatar(file);
      if (result && result.avatarUrl) {
        userStore.updateUserInfo({ avatar: result.avatarUrl });
        return result.avatarUrl;
      }
      return null;
    } catch (error) {
      console.error('更新头像失败:', error);
      return null;
    }
  },

  /**
   * 更新用户资料
   * @param profileData 资料数据
   * @returns 更新结果
   */
  async updateProfile(profileData: Partial<UserInfo>): Promise<boolean> {
    const userStore = useUserStore();
    try {
      await updateUserProfile(profileData);
      userStore.updateUserInfo(profileData);
      return true;
    } catch (error) {
      console.error('更新资料失败:', error);
      return false;
    }
  },

  /**
   * 根据积分获取用户等级
   * @param points 积分
   * @returns 用户等级信息
   */
  getUserLevel(points: number) {
    // 按从高到低排序，找到第一个满足条件的等级
    const levels = [...USER_LEVELS].sort((a, b) => b.minPoints - a.minPoints);
    return levels.find(level => points >= level.minPoints) || levels[levels.length - 1];
  }
};

/**
 * 用户安全服务
 */
export const userSecurityService = {
  /**
   * 检查密码强度
   * @param password 密码
   * @returns 密码强度评级 (1-5)
   */
  checkPasswordStrength(password: string): number {
    if (!password) return 0;
    
    let strength = 0;
    
    // 密码长度
    if (password.length >= 8) strength += 1;
    if (password.length >= 10) strength += 1;
    
    // 包含数字
    if (/\d/.test(password)) strength += 1;
    
    // 包含小写字母
    if (/[a-z]/.test(password)) strength += 1;
    
    // 包含大写字母
    if (/[A-Z]/.test(password)) strength += 1;
    
    // 包含特殊字符
    if (/[^a-zA-Z0-9]/.test(password)) strength += 1;
    
    return Math.min(5, strength);
  },

  /**
   * 获取密码强度描述
   * @param strength 密码强度 (1-5)
   * @returns 描述文本
   */
  getPasswordStrengthText(strength: number): string {
    const texts = ['极弱', '弱', '一般', '强', '很强'];
    return texts[Math.min(Math.max(0, strength - 1), 4)];
  },

  /**
   * 获取密码强度对应的颜色
   * @param strength 密码强度 (1-5)
   * @returns 颜色代码
   */
  getPasswordStrengthColor(strength: number): string {
    const colors = ['#F56C6C', '#E6A23C', '#909399', '#67C23A', '#409EFF'];
    return colors[Math.min(Math.max(0, strength - 1), 4)];
  }
};
