/**
 * 跑腿员订单管理相关API
 * 包括订单列表、统计、接单、状态更新等
 */
import request from '@/modules/user/utils/request';

/**
 * @description 获取跑腿员订单列表
 * @param params 查询参数，包含分页和筛选条件
 * @returns Promise 订单列表及分页信息
 */
export function loadRunnerOrders(params: any) {
  return request({
    url: '/v1/runner-order/runner/list',
    method: 'GET',
    params: {
      page: params.current, // 假设params.current是页码
      size: params.pageSize, // 假设params.pageSize是每页数量
      ...params.searchForm // 其他搜索条件
    }
  });
}

/**
 * @description 获取跑腿员订单统计数据
 * @returns Promise 订单统计数据
 */
export function getRunnerOrderStats() {
  return request({
    url: '/v1/runner-order/stats',
    method: 'GET'
  });
}

/**
 * @description 跑腿员接受订单
 * @param orderId 订单ID
 * @returns Promise 操作结果
 */
export function acceptRunnerOrder(orderId: string | number) {
  return request({
    url: '/v1/runner-order/accept',
    method: 'POST',
    data: { order_id: orderId }
  });
}

/**
 * @description 跑腿员取货
 * @param orderId 订单ID
 * @param pickupCode 取货验证码（可选）
 * @returns Promise 操作结果
 */
export function pickupRunnerOrder(orderId: string | number, pickupCode?: string) {
  return request({
    url: '/v1/runner-order/pickup',
    method: 'POST',
    data: { 
      order_id: orderId,
      pickup_code: pickupCode
    }
  });
}

/**
 * @description 跑腿员开始配送订单
 * @param orderId 订单ID
 * @returns Promise 操作结果
 */
export function startRunnerOrderDelivery(orderId: string | number) {
  return request({
    url: '/v1/runner-order/start-delivery',
    method: 'POST',
    data: { order_id: orderId }
  });
}

/**
 * @description 跑腿员完成订单
 * @param orderId 订单ID
 * @param deliveryCode 送达验证码（可选）
 * @returns Promise 操作结果
 */
export function completeRunnerOrder(orderId: string | number, deliveryCode?: string) {
  return request({
    url: '/v1/runner-order/complete',
    method: 'POST',
    data: { 
      order_id: orderId,
      delivery_code: deliveryCode 
    }
  });
}

/**
 * @description 跑腿员取消订单
 * @param orderId 订单ID
 * @param reason 取消原因
 * @returns Promise 操作结果
 */
export function cancelRunnerOrder(orderId: string | number, reason: string) {
  return request({
    url: '/v1/runner-order/cancel',
    method: 'POST',
    data: {
      order_id: orderId,
      reason
    }
  });
}

/**
 * @description 更新跑腿员订单状态
 * @param orderId 订单ID
 * @param status 更新的状态
 * @param remark 备注信息
 * @returns Promise 操作结果
 */
export function updateRunnerOrderStatus(orderId: string | number, status: string, remark: string) {
  return request({
    url: '/v1/runner-order/update-status',
    method: 'POST',
    data: {
      order_id: orderId,
      status,
      remark
    }
  });
}
