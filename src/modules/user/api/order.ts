/**
 * 用户订单管理相关API
 * 包括订单的查询、支付、取消等功能
 * /**
 * 商家订单管理文档
 *
 * 本文档详细介绍了外卖模块中商家管理订单的功能、API接口以及使用示例。
 * 包括订单列表查询、订单详情查询、订单状态更新、接单、分配配送等核心功能。
 

# 商家订单管理

## 概述

外卖模块的商家订单管理系统提供了一套完整的API，用于商家管理自己店铺的外卖订单。包括但不限于：订单列表查询、订单详情查询、订单状态更新、接单、分配配送员、开始配送、完成配送、完成订单等功能。

商家订单管理的核心流程为：
1. 查看订单列表与订单详情
2. 接受新订单
3. 分配配送员
4. 开始配送
5. 完成配送
6. 完成订单

## API接口列表

所有商家订单管理API均以 `/v1/merchant/takeout/orders` 为前缀，需要商家登录验证。

| 接口 | 请求方式 | 描述 |
| --- | --- | --- |
| `/v1/merchant/takeout/orders` | GET | 查询订单列表 |
| `/v1/merchant/takeout/orders/:id` | GET | 获取订单详情 |
| `/v1/merchant/takeout/orders/:id` | PUT | 更新订单状态（当前实现为完成订单） |
| `/v1/merchant/takeout/orders/accept` | POST | 接受订单 |
| `/v1/merchant/takeout/orders/assign` | POST | 分配配送员 |
| `/v1/merchant/takeout/orders/statistics` | GET | 获取商家订单统计 |
| `/v1/merchant/takeout/delivery/start` | POST | 开始配送 |
| `/v1/merchant/takeout/delivery/complete` | POST | 完成配送 |
| `/v1/merchant/takeout/delivery/list` | GET | 查询配送订单列表 |

## 订单状态

订单状态是商家管理订单的核心，订单流转过程中会经历以下状态：

```
OrderStatusPending     = 10 // 待支付
OrderStatusPaid        = 20 // 已支付
OrderStatusProcessing  = 30 // 处理中（商家已接单，准备中）
OrderStatusDelivering  = 40 // 配送中
OrderStatusCompleted   = 50 // 已完成
OrderStatusCancelled   = 60 // 已取消
OrderStatusRefunding   = 70 // 退款中
OrderStatusRefunded    = 80 // 已退款
```

## 使用示例

以下是商家订单管理的API使用示例：

### 1. 查询订单列表

**请求示例：**

```http
GET /v1/merchant/takeout/orders?status=20&page=1&page_size=10
```

参数说明：
- `status`: 订单状态，-1表示全部，20表示已支付待处理的订单
- `page`: 页码，默认为1
- `page_size`: 每页记录数，默认为10

**响应示例：**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 10086,
        "order_number": "TK20250605123456",
        "user_id": 1001,
        "merchant_id": 2001,
        "merchant_name": "好味餐厅",
        "status": 20,
        "status_text": "已支付",
        "total_amount": 45.5,
        "items_count": 3,
        "address": "北京市海淀区中关村大街1号",
        "created_at": "2025-06-05T09:30:25+08:00",
        "paid_at": "2025-06-05T09:32:10+08:00",
        "updated_at": "2025-06-05T09:32:10+08:00"
      },
      ...
    ],
    "total": 28,
    "page": 1,
    "page_size": 10
  }
}
```

### 2. 获取订单详情

**请求示例：**

```http
GET /v1/merchant/takeout/orders/10086
```

**响应示例：**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 10086,
    "order_number": "TK20250605123456",
    "user_id": 1001,
    "user_name": "张三",
    "user_phone": "138****1234",
    "merchant_id": 2001,
    "merchant_name": "好味餐厅",
    "status": 20,
    "status_text": "已支付",
    "total_amount": 45.5,
    "order_items": [
      {
        "id": 1,
        "food_id": 101,
        "food_name": "宫保鸡丁",
        "food_image": "https://example.com/images/kungpao.jpg",
        "variant_name": "标准辣度",
        "quantity": 1,
        "price": 28.0,
        "subtotal": 28.0,
        "combo_selections": [
          {
            "combo_name": "配菜选择",
            "options": [
              {
                "option_name": "米饭",
                "quantity": 1,
                "extra_price": 2.0
              }
            ]
          }
        ]
      },
      {
        "id": 2,
        "food_id": 102,
        "food_name": "可乐",
        "food_image": "https://example.com/images/cola.jpg",
        "variant_name": "中杯",
        "quantity": 1,
        "price": 5.0,
        "subtotal": 5.0,
        "combo_selections": []
      },
      {
        "id": 3,
        "food_id": 103,
        "food_name": "薯条",
        "food_image": "https://example.com/images/fries.jpg",
        "variant_name": "标准",
        "quantity": 1,
        "price": 8.0,
        "subtotal": 8.0,
        "combo_selections": []
      }
    ],
    "delivery_fee": 5.0,
    "packaging_fee": 1.5,
    "discount_amount": 2.0,
    "address": {
      "id": 100,
      "receiver": "张三",
      "phone": "13812341234",
      "province": "北京市",
      "city": "北京市",
      "district": "海淀区",
      "detail": "中关村大街1号",
      "is_default": true
    },
    "remark": "不要放葱，多加一点辣",
    "delivery_info": {
      "delivery_staff_id": 0,
      "delivery_staff_name": "",
      "delivery_staff_phone": "",
      "delivery_status": 0,
      "estimated_arrival_time": null,
      "delivery_started_at": null,
      "delivery_completed_at": null
    },
    "created_at": "2025-06-05T09:30:25+08:00",
    "paid_at": "2025-06-05T09:32:10+08:00",
    "accepted_at": null,
    "completed_at": null,
    "cancelled_at": null,
    "refunding_at": null,
    "refunded_at": null,
    "updated_at": "2025-06-05T09:32:10+08:00"
  }
}
```

### 3. 接受订单

**请求示例：**

```http
POST /v1/merchant/takeout/orders/accept
Content-Type: application/json

{
  "order_id": 10086
}
```

**响应示例：**

```json
{
  "code": 0,
  "message": "订单接受成功",
  "data": null
}
```

接受订单后，订单状态将从已支付(`OrderStatusPaid`=20)变更为处理中(`OrderStatusProcessing`=30)。

### 4. 分配配送员

**请求示例：**

```http
POST /v1/merchant/takeout/orders/assign
Content-Type: application/json

{
  "order_id": 10086,
  "delivery_staff_id": 3001
}
```

**响应示例：**

```json
{
  "code": 0,
  "message": "配送员分配成功",
  "data": null
}
```

### 5. 开始配送

**请求示例：**

```http
POST /v1/merchant/takeout/delivery/start
Content-Type: application/json

{
  "order_id": 10086,
  "delivery_staff_id": 3001,
  "estimated_arrival_time": "2025-06-05T10:25:00+08:00"
}
```

**响应示例：**

```json
{
  "code": 0,
  "message": "开始配送成功",
  "data": null
}
```

开始配送后，订单状态将从处理中(`OrderStatusProcessing`=30)变更为配送中(`OrderStatusDelivering`=40)。

### 6. 完成配送

**请求示例：**

```http
POST /v1/merchant/takeout/delivery/complete
Content-Type: application/json

{
  "order_id": 10086,
  "delivery_staff_id": 3001
}
```

**响应示例：**

```json
{
  "code": 0,
  "message": "配送完成",
  "data": null
}
```

### 7. 完成订单

**请求示例：**

```http
PUT /v1/merchant/takeout/orders/10086
Content-Type: application/json

{}
```

**响应示例：**

```json
{
  "code": 0,
  "message": "订单已完成",
  "data": null
}
```

完成订单后，订单状态将变更为已完成(`OrderStatusCompleted`=50)。

### 8. 获取订单统计数据

**请求示例：**

```http
GET /v1/merchant/takeout/orders/statistics
```

**响应示例：**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total_count": 250,
    "completed_count": 180,
    "processing_count": 15,
    "cancelled_count": 5,
    "today_orders": 25,
    "today_amount": 2568.50,
    "month_orders": 150,
    "month_amount": 15420.80
  }
}
```

## 常见问题与解决方案

### 问题1：无法接单或状态无法更新

请确保订单处于正确的状态，例如只有已支付(`OrderStatusPaid`=20)的订单才能被接受。

### 问题2：接单成功但客户端未更新

接单成功后，客户端需要主动刷新或通过WebSocket接收服务器推送的通知。

### 问题3：配送员无法分配

请确保：
1. 订单已经被商家接受(状态为`OrderStatusProcessing`=30)
2. 配送员ID存在且有效
3. 配送员未被分配到其他订单

### 问题4：订单统计数据不准确

统计数据可能有缓存延迟，如需实时数据，请在请求URL后添加时间戳参数，如：`?_t=1622943656`。

## 最佳实践

1. **定期轮询新订单** - 建议每30秒查询一次新订单（已支付状态），以便及时处理
2. **快速响应** - 建议在2分钟内接受或拒绝新订单，提高客户满意度
3. **正确流转状态** - 严格按照订单状态流程处理订单，避免状态错乱
4. **异常处理** - 在网络异常或服务器错误时，添加适当的重试逻辑
5. **完整记录** - 记录所有订单状态变更，便于后续问题排查

## 订单流程图

```
+---------------+     +--------------+     +----------------+
| 用户下单支付  | --> | 商家接单处理 | --> | 分配配送员     |
+---------------+     +--------------+     +----------------+
        |                                          |
        v                                          v
+---------------+     +--------------+     +----------------+
| 订单已取消    | <-- | 订单已完成   | <-- | 配送进行中     |
+---------------+     +--------------+     +----------------+
```

 */
import request from '@/modules/user/utils/request';
import type { UserOrder } from '../types';

/**
 * 获取用户订单列表
 * @param status 订单状态，可选，不传则查询所有状态
 * @param page 页码，默认1
 * @param pageSize 每页条数，默认10
 * @returns 订单列表和分页信息
 */
export function getUserOrders(params?: {
  status?: string;
  page?: number;
  pageSize?: number;
  [key: string]: any;
}) {
  return request({
    url: '/v1/user/takeout/order/list',
    method: 'get',
    params
  });
}

/**
 * 获取订单详情
 * @param id 订单ID
 * @returns 订单详情
 */
export function getOrderById(id: string): Promise<UserOrder> {
  return request({
    url: `/v1/user/secured/order/detail/${id}`,
    method: 'get'
  });
}

/**
 * 取消订单
 * @param id 订单ID
 * @param reason 取消原因
 * @returns 取消结果
 */
export function cancelOrder(id: string, reason: string) {
  return request({
    url: `/v1/user/secured/order/cancel/${id}/`,
    method: 'post',
    data: {
      reason
    }
  });
}

/**
 * 确认收货
 * @param id 订单ID
 * @returns 确认结果
 */
export function confirmReceived(id: string) {
  return request({
    url: `/v1/user/secured/orders/${id}/confirm`,
    method: 'post'
  });
}

/**
 * 申请退款
 * @param id 订单ID
 * @param reason 退款原因
 * @param amount 退款金额，不传则为订单总金额
 * @returns 申请结果
 */
export function applyRefund(id: string, reason: string, amount?: number) {
  return request({
    url: `/v1/user/takeout/order/${id}/refund`,
    method: 'post',
    data: {
      reason,
      amount
    }
  });
}

/**
 * 支付订单
 * @param id 订单ID
 * @param paymentMethod 支付方式，如 'alipay', 'wechat'
 * @returns 支付结果，包含跳转链接或支付参数
 */
export function payOrder(id: string, paymentMethod: string) {
  return request({
    url: `/v1/user/secured/orders/${id}/pay`,
    method: 'post',
    data: {
      payment_method: paymentMethod
    }
  });
}

/**
 * 查询支付状态
 * @param id 订单ID
 * @returns 支付状态
 */
export function getPaymentStatus(id: string) {
  return request({
    url: `/v1/user/secured/orders/${id}/payment-status`,
    method: 'get'
  });
}
