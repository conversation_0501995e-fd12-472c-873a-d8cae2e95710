/**
 * 跑腿员API统一导出文件
 * 集中导出所有与跑腿员相关的API接口和类型
 * 包括：申请相关、收入相关、订单相关、设置相关、工作状态相关等API
 */

// 导出跑腿员申请相关
export * from './runnerApply';

// 导出跑腿员收入相关
export * from './runnerIncome';

// 导出跑腿员订单相关
export * from './runnerOrder';

// 导出跑腿员设置相关类型
export type {
  RunnerProfileData,
  UpdateProfilePayload,
  RunnerServiceSettingsData,
  RunnerNotificationSettingsData,
  ChangePasswordPayload,
  PaymentAccount,
  AddPaymentAccountPayload
} from './runnerSettings';

// 从顶层runnerSettings导入ServiceTime接口并重导出
import type { ServiceTime } from '@/modules/user/api/runnerSettings';
export type { ServiceTime };

// 导出跑腿员设置相关类接口（原始定义，标记为原始版本以避免冲突）
export {
  getRunnerProfile as getRunnerProfileOriginal,
  updateRunnerProfile as updateRunnerProfileOriginal,
  getRunnerServiceSettings as getRunnerServiceSettingsOriginal,
  updateRunnerServiceSettings as updateRunnerServiceSettingsOriginal,
  getRunnerNotificationSettings as getRunnerNotificationSettingsOriginal,
  updateRunnerNotificationSettings as updateRunnerNotificationSettingsOriginal,
  changeRunnerPassword,
  getRunnerPaymentAccounts,
  addRunnerPaymentAccount,
  setDefaultRunnerPaymentAccount,
  deleteRunnerPaymentAccount
} from './runnerSettings';

// 导出跑腿员设置相关API接口（RunnerSettings.vue中使用的版本）
export {
  getRunnerProfile,
  updateRunnerProfile,
  getRunnerServiceSettings,
  updateRunnerServiceSettings,
  getRunnerNotificationSettings,
  updateRunnerNotificationSettings,
  changePassword,
  changePhone,
  sendVerifyCode,
  getRunnerAccounts,
  addRunnerAccount,
  updateRunnerAccount,
  deleteRunnerAccount,
  setDefaultAccount
} from './runnerSettingsApi';

// 导出跑腿员工作状态相关
export * from './runnerWorkStatus';
