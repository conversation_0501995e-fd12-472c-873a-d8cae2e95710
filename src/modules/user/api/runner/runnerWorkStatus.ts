import request from '@/modules/user/utils/request'

// 定义请求参数和响应数据的接口，以增强类型安全
interface RunnerStatusResponse {
  workStatus: number
  isOnline: boolean
  lastUpdateTime: string
}

interface UpdateWorkStatusPayload {
  status: number
  reason?: string // reason是可选的
}

interface UpdateOnlineStatusPayload {
  is_online: boolean
}

interface UpdateLocationPayload {
  latitude: number
  longitude: number
  address?: string // address是可选的，后端可能会根据坐标自动获取
}

interface LocationResponse {
  address: string
  // 可能还有其他字段，根据实际API调整
}

interface TodayStatsResponse {
  income: number
  orderCount: number
  workHours: number
  distance: number
  rating: number
}

/**
 * @description 获取跑腿员当前状态（工作状态、在线状态）
 * @returns {Promise<RunnerStatusResponse>}
 */
/**
 * @description 获取跑腿员当前状态（工作状态、在线状态）
 * @returns {Promise<RunnerStatusResponse>}
 */
export const getRunnerStatus = async (): Promise<RunnerStatusResponse> => {
  const response = await request<RunnerStatusResponse>({
    url: '/v1/runner/secured/status',
    method: 'GET'
  })
  return response.data // 显式返回 response.data
}

/**
 * @description 更新跑腿员工作状态
 * @param {UpdateWorkStatusPayload} data - 工作状态数据
 * @returns {Promise<any>} // 根据实际API调整返回类型
 */
/**
 * @description 更新跑腿员工作状态
 * @param {UpdateWorkStatusPayload} data - 工作状态数据
 * @returns {Promise<any>} // 根据实际API调整返回类型
 */
export const updateRunnerWorkStatus = async (data: UpdateWorkStatusPayload): Promise<any> => {
  const response = await request<any>({
    url: '/v1/runner/secured/status',
    method: 'PUT',
    data
  })
  return response.data // 显式返回 response.data
}

/**
 * @description 更新跑腿员在线状态
 * @param {UpdateOnlineStatusPayload} data - 在线状态数据
 * @returns {Promise<any>} // 根据实际API调整返回类型
 */
/**
 * @description 更新跑腿员在线状态
 * @param {UpdateOnlineStatusPayload} data - 在线状态数据
 * @returns {Promise<any>} // 根据实际API调整返回类型
 */
export const updateRunnerOnlineStatus = async (data: UpdateOnlineStatusPayload): Promise<any> => {
  const response = await request<any>({
    url: '/v1/runner/secured/online-status',
    method: 'PUT',
    data
  })
  return response.data // 显式返回 response.data
}

/**
 * @description 更新跑腿员位置信息
 * @param {UpdateLocationPayload} data - 位置数据
 * @returns {Promise<LocationResponse>}
 */
/**
 * @description 更新跑腿员位置信息
 * @param {UpdateLocationPayload} data - 位置数据
 * @returns {Promise<LocationResponse>}
 */
export const updateRunnerLocation = async (data: UpdateLocationPayload): Promise<LocationResponse> => {
  const response = await request<LocationResponse>({
    url: '/v1/runner/secured/location',
    method: 'PUT',
    data
  })
  return response.data // 显式返回 response.data
}

/**
 * @description 获取跑腿员今日统计数据
 * @returns {Promise<TodayStatsResponse>}
 */
/**
 * @description 获取跑腿员今日统计数据
 * @returns {Promise<TodayStatsResponse>}
 */
export const getRunnerTodayStats = async (): Promise<TodayStatsResponse> => {
  const response = await request<TodayStatsResponse>({
    url: '/v1/runner/secured/today-stats',
    method: 'GET'
  })
  return response.data // 显式返回 response.data
}
