/**
 * @file: runnerSettingsApi.ts
 * @author: AI Assistant
 * @date: 2025-06-08
 * @version: 1.0.0
 * @description: 跑腿员设置API接口，适配RunnerSettings.vue中使用的函数名称
 */

import request from '@/modules/user/utils/request';
import type {
  RunnerNotificationSettingsData,
  ChangePasswordPayload,
  AddPaymentAccountPayload
} from './runnerSettings';

// 个人信息
export const getRunnerProfile = async () => {
  return await request.get('/v1/runner/secured/profile');
};

export const updateRunnerProfile = async (data: {
  real_name?: string;
  id_card_number?: string;
  mobile?: string;
  avatar?: string;
}) => {
  return await request.put('/v1/runner/secured/profile', data);
};

// 服务设置
export const getRunnerServiceSettings = async () => {
  return await request.get('/v1/runner/secured/service-settings');
};

export const updateRunnerServiceSettings = async (data: {
  auto_accept_order: boolean;
  max_order_distance: number;
  order_types: number[];
  working_hours_start: string;
  working_hours_end: string;
  rest_days: number[];
  max_simultaneous_order: number;
}) => {
  return await request.put('/v1/runner/secured/service-settings', data);
};

// 通知设置
export const getRunnerNotificationSettings = async () => {
  return await request.get('/v1/runner/secured/notification-settings');
};

export const updateRunnerNotificationSettings = async (data: RunnerNotificationSettingsData) => {
  return await request.put('/v1/runner/secured/notification-settings', data);
};

// 安全设置 - 修改密码
export const changePassword = async (data: { oldPassword: string; newPassword: string }) => {
  const payload: ChangePasswordPayload = {
    old_password: data.oldPassword,
    new_password: data.newPassword
  };
  return await request.put('/v1/runner/secured/change-password', payload);
};

// 安全设置 - 修改手机号
export const changePhone = async (data: { newPhone: string; verifyCode: string }) => {
  return await request.put('/v1/runner/secured/change-phone', {
    new_phone: data.newPhone,
    verify_code: data.verifyCode
  });
};

// 发送验证码
export const sendVerifyCode = async (phone: string) => {
  return await request.post('/v1/runner/secured/send-verify-code', { phone });
};

// 支付账户
export const getRunnerAccounts = async () => {
  return await request.get('/v1/runner/secured/payment-accounts');
};

export const addRunnerAccount = async (account: {
  accountType: number;
  accountNumber: string;
  accountName: string;
  bankName?: string;
  isDefault: boolean;
}) => {
  const payload: AddPaymentAccountPayload = {
    account_type: account.accountType,
    account_number: account.accountNumber,
    account_name: account.accountName,
    bank_name: account.bankName,
    is_default: account.isDefault
  };
  return await request.post('/v1/runner/secured/payment-accounts', payload);
};

export const updateRunnerAccount = async (account: {
  id: number;
  accountType: number;
  accountNumber: string;
  accountName: string;
  bankName?: string;
  isDefault: boolean;
}) => {
  const payload = {
    account_type: account.accountType,
    account_number: account.accountNumber,
    account_name: account.accountName,
    bank_name: account.bankName,
    is_default: account.isDefault
  };
  return await request.put(`/v1/runner/secured/payment-accounts/${account.id}`, payload);
};

export const deleteRunnerAccount = async (id: number | string) => {
  return await request.delete(`/v1/runner/secured/payment-accounts/${id}`);
};

export const setDefaultAccount = async (id: number | string) => {
  return await request.put(`/v1/runner/secured/payment-accounts/${id}/default`);
};
