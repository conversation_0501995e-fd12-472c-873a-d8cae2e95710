import request from '@/modules/user/utils/request';

// --- Interfaces for Payloads and Responses ---

// Profile
export interface RunnerProfileData {
  id: number;
  user_id: number;
  real_name: string;
  mobile: string;
  face_pic: string; // 与后端API返回字段保持一致
  id_card_number: string;
  status: number;
  status_desc: string;
  working_status: number;
  working_status_desc: string;
  is_online: boolean;
  create_time: string;
  last_login_time: string;
  score: number;
  order_count: number;
  cancel_count: number;
  service_areas: string[];
  // 补充后端返回的其他字段
  area_codes?: string;
  current_location?: string;
  frontend_remark?: string;
  id_card_back_pic?: string;
  id_card_front_pic?: string;
  join_days?: number;
  join_time?: string;
  last_online_time?: string;
  latitude?: number;
  longitude?: number;
  remark?: string;
  service_radius?: number;
  success_count?: number;
  wallet?: number;
}

export interface UpdateProfilePayload {
  real_name?: string;
  id_card_number?: string;
  mobile?: string;
  avatar?: string; // 请求依然使用avatar字段
}

// Service Settings
export interface RunnerServiceSettingsData {
  runner_id: number;
  auto_accept_order: boolean;
  max_order_distance: number;
  order_types: number[];
  working_hours_start: string;
  working_hours_end: string;
  rest_days: number[];
  max_simultaneous_order: number;
  updated_at: string;
}

// Notification Settings
export interface RunnerNotificationSettingsData {
  runner_id: number;
  order_notification: boolean;
  system_notification: boolean;
  marketing_notification: boolean;
  payment_notification: boolean;
  sound: boolean;
  vibration: boolean;
  updated_at: string;
}

// Change Password
export interface ChangePasswordPayload {
  old_password: string;
  new_password: string;
}

// Payment Account
export interface PaymentAccount {
  id: number;
  runner_id: number;
  account_type: number; // 1-微信, 2-支付宝, 3-银行卡
  account_name: string;
  account_number: string;
  bank_name?: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface AddPaymentAccountPayload {
  account_type: number;
  account_name: string;
  account_number: string;
  bank_name?: string;
  is_default: boolean;
}

// --- API Service Functions ---

// Profile
export const getRunnerProfile = async (): Promise<RunnerProfileData> => {
  const response = await request<RunnerProfileData>({
    url: '/v1/runner/secured/profile',
    method: 'GET',
  });
  return response.data;
};

export const updateRunnerProfile = async (data: UpdateProfilePayload): Promise<any> => {
  const response = await request<any>({
    url: '/v1/runner/secured/profile',
    method: 'PUT',
    data,
  });
  return response.data;
};

// Service Settings
export const getRunnerServiceSettings = async (): Promise<RunnerServiceSettingsData> => {
  const response = await request<RunnerServiceSettingsData>({
    url: '/v1/runner/secured/service-settings',
    method: 'GET',
  });
  return response.data;
};

export const updateRunnerServiceSettings = async (data: RunnerServiceSettingsData): Promise<any> => {
  const response = await request<any>({
    url: '/v1/runner/secured/service-settings',
    method: 'PUT',
    data,
  });
  return response.data;
};

// Notification Settings
export const getRunnerNotificationSettings = async (): Promise<RunnerNotificationSettingsData> => {
  const response = await request<RunnerNotificationSettingsData>({
    url: '/v1/runner/secured/notification-settings',
    method: 'GET',
  });
  return response.data;
};

export const updateRunnerNotificationSettings = async (data: RunnerNotificationSettingsData): Promise<any> => {
  const response = await request<any>({
    url: '/v1/runner/secured/notification-settings',
    method: 'PUT',
    data,
  });
  return response.data;
};

// Security - Change Password
export const changeRunnerPassword = async (data: ChangePasswordPayload): Promise<any> => {
  const response = await request<any>({
    url: '/v1/runner/secured/change-password',
    method: 'PUT',
    data,
  });
  return response.data;
};

// Payment Accounts
export const getRunnerPaymentAccounts = async (): Promise<PaymentAccount[]> => {
  const response = await request<PaymentAccount[]>({
    url: '/v1/runner/secured/payment-accounts',
    method: 'GET',
  });
  return response.data;
};

export const addRunnerPaymentAccount = async (data: AddPaymentAccountPayload): Promise<any> => {
  const response = await request<any>({
    url: '/v1/runner/secured/payment-accounts',
    method: 'POST',
    data,
  });
  return response.data;
};

export const setDefaultRunnerPaymentAccount = async (accountId: number): Promise<any> => {
  const response = await request<any>({
    url: `/v1/runner/secured/payment-accounts/${accountId}/default`,
    method: 'PUT',
  });
  return response.data;
};

export const deleteRunnerPaymentAccount = async (accountId: number): Promise<any> => {
  const response = await request<any>({
    url: `/v1/runner/secured/payment-accounts/${accountId}`,
    method: 'DELETE',
  });
  return response.data;
};
