/**
 * 用户个人资料相关API
 * 包括获取和更新个人信息、头像上传等功能
 */
import request from '@/modules/user/utils/request';
import type { UserInfo } from '../types';

/**
 * 更新用户个人资料
 * @param data 用户信息
 * @returns 更新结果
 */
export function updateUserProfile(data: Partial<UserInfo>) {
  return request({
    url: '/v1/user/secured/info',
    method: 'put',
    data
  });
}

/**
 * 上传用户头像 (需后端实现)
 * @param file 头像文件
 * @returns 上传结果，包含头像URL
 */
export function uploadAvatar(file: File) {
  // 由于后端API未实现，使用模拟数据
  console.log(`模拟上传头像，文件名: ${file.name}`);
  
  // 返回模拟的头像URL
  return Promise.resolve({
    avatarUrl: `/images/avatars/mock_avatar_${Date.now()}.jpg`
  });
  
  /* 实际的实现应该类似于下面的代码
  const formData = new FormData();
  formData.append('avatar', file);
  
  return request({
    url: '/v1/user/secured/avatar',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
  */
}

/**
 * 修改密码
 * @param oldPassword 旧密码
 * @param newPassword 新密码
 * @returns 修改结果
 */
export function changePassword(oldPassword: string, newPassword: string) {
  return request({
    url: '/v1/user/secured/password',
    method: 'put',
    data: {
      old_password: oldPassword,
      new_password: newPassword
    }
  });
}

/**
 * 绑定手机号 (需后端实现)
 * @param phone 手机号
 * @param code 验证码
 * @returns 绑定结果
 */
export function bindPhone(phone: string, code: string) {
  // 由于后端API未实现，使用模拟数据
  console.log(`模拟绑定手机号: ${phone}, 验证码: ${code}`);
  return Promise.resolve({ message: '手机号绑定成功' });
}

/**
 * 绑定邮箱 (需后端实现)
 * @param email 邮箱
 * @param code 验证码
 * @returns 绑定结果
 */
export function bindEmail(email: string, code: string) {
  // 由于后端API未实现，使用模拟数据
  console.log(`模拟绑定邮箱: ${email}, 验证码: ${code}`);
  return Promise.resolve({ message: '邮箱绑定成功' });
}
