/**
 * 支付相关API
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */

import request from '@/modules/user/utils/request';

// 支付方式枚举
export enum PaymentMethod {
  WECHAT = 'wechat',
  ALIPAY = 'alipay',
  CREDITCARD = 'creditcard',
  BANK_TRANSFER = 'bank_transfer',
  BALANCE = 'balance',
  COMBINATION = 'combination'
}

// 支付状态枚举
export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  TIMEOUT = 'timeout'
}

// 支付信息接口
export interface PaymentInfo {
  paymentId: string;
  orderId: string | number;
  paymentMethod: PaymentMethod;
  paymentAmount: number;
  status: PaymentStatus;
  qrCode?: string;
  codeUrl?: string;
  expiresAt?: string;
  transactionId?: string;
  paidAt?: string;
  errorMessage?: string;
  orderIds?: (string | number)[]; // 多订单支付时的订单ID列表
}

// 创建支付请求参数
export interface CreatePaymentRequest {
  payment_method: PaymentMethod;
  payment_amount: number;
  coupon_id?: number;
  delivery_address?: {
    address: string;
    phone: string;
    contact_name: string;
  };
  remark?: string;
  order_ids?: (string | number)[]; // 多订单支付时的订单ID列表
}

// 创建支付响应
export interface CreatePaymentResponse {
  paymentID: string;
  order_id: number;
  payment_method: PaymentMethod;
  payment_amount: number;
  original_amount?: number;
  discount_amount?: number;
  status: PaymentStatus;
  created_at: string;
  expireTime: number;
  qrCodeURL: string;
  paymentURL: string;
  transactionNo: string;
  appPayParams: string;
  webPayParams: string;
}

// 支付状态查询响应
export interface PaymentStatusResponse {
  payment_id: string;
  order_id: number;
  status: PaymentStatus;
  payment_method: PaymentMethod;
  payment_amount: number;
  paid_at?: string;
  transaction_id?: string;
  error_message?: string;
  payment_info?: {
    bank_type?: string;
    cash_fee?: number;
    fee_type?: string;
  };
}

/**
 * 创建普通订单支付
 * @param orderId 订单ID
 * @param paymentMethod 支付方式
 * @returns 支付信息
 */
export async function createOrderPayment(
  orderId: number,
  paymentMethod: PaymentMethod
): Promise<CreatePaymentResponse> {
  return request({
    url: `/v1/user/order/${orderId}/payment`,
    method: 'post',
    data: {
      payment_method: paymentMethod
    }
  });
}

/**
 * 创建外卖订单支付
 * @param orderId 订单ID
 * @param params 支付参数
 * @returns 支付信息
 */
export async function createTakeoutPayment(
  orderId: number,
  params: CreatePaymentRequest
): Promise<CreatePaymentResponse> {
  return request({
    url: `/v1/user/takeout/order/pay/${orderId}/create`,
    method: 'post',
    data: params
  });
}

/**
 * 查询普通订单支付状态
 * @param orderId 订单ID
 * @returns 支付状态
 */
export async function getOrderPaymentStatus(
  orderId: number
): Promise<PaymentStatusResponse> {
  return request({
    url: `/v1/user/orders/${orderId}/payment/status`,
    method: 'get'
  });
}

/**
 * 查询外卖订单支付状态
 * @param paymentId 支付ID
 * @returns 支付状态
 */
export async function getTakeoutPaymentStatus(
  paymentId: string
): Promise<PaymentStatusResponse> {
  return request({
    url: `/v1/user/takeout/payments/${paymentId}/status`,
    method: 'get'
  });
}

/**
 * 取消支付
 * @param paymentId 支付ID
 * @returns 取消结果
 */
export async function cancelPayment(
  paymentId: string
): Promise<{ success: boolean }> {
  return request({
    url: `/v1/user/payments/${paymentId}/cancel`,
    method: 'post'
  });
}

/**
 * 获取支付方式文本
 * @param method 支付方式
 * @returns 支付方式文本
 */
export function getPaymentMethodText(method: PaymentMethod | string): string {
  const methodMap: Record<string, string> = {
    [PaymentMethod.WECHAT]: '微信支付',
    [PaymentMethod.ALIPAY]: '支付宝',
    [PaymentMethod.CREDITCARD]: '信用卡',
    [PaymentMethod.BANK_TRANSFER]: '银行转账',
    [PaymentMethod.BALANCE]: '余额支付',
    [PaymentMethod.COMBINATION]: '组合支付'
  };
  return methodMap[method] || method;
}

/**
 * 获取支付状态文本
 * @param status 支付状态
 * @returns 支付状态文本
 */
export function getPaymentStatusText(status: PaymentStatus | string): string {
  const statusMap: Record<string, string> = {
    [PaymentStatus.PENDING]: '待支付',
    [PaymentStatus.PAID]: '已支付',
    [PaymentStatus.FAILED]: '支付失败',
    [PaymentStatus.CANCELLED]: '已取消',
    [PaymentStatus.TIMEOUT]: '支付超时'
  };
  return statusMap[status] || status;
}