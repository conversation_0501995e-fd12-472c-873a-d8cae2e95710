/**
 * 用户地址管理相关API
 * 包括地址的增删改查功能
 */
import request from '@/modules/user/utils/request';
import type { UserAddress } from '../types';

/**
 * 获取用户地址列表
 * @returns 地址列表
 */
export function getUserAddresses(): Promise<UserAddress[]> {
  return request({
    url: '/v1/user/secured/addresses',
    method: 'get'
  });
}

/**
 * 获取单个地址详情
 * @param id 地址ID
 * @returns 地址详情
 */
export function getUserAddressById(id: string): Promise<UserAddress> {
  return request({
    url: `/v1/user/secured/addresses/${id}`,
    method: 'get'
  });
}

/**
 * 添加新地址
 * @param data 地址信息
 * @returns 添加结果
 */
export function addUserAddress(data: Omit<UserAddress, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) {
  return request({
    url: '/v1/user/secured/addresses',
    method: 'post',
    data
  });
}

/**
 * 更新地址信息
 * @param id 地址ID
 * @param data 地址信息
 * @returns 更新结果
 */
export function updateUserAddress(id: string, data: Partial<UserAddress>) {
  return request({
    url: `/v1/user/secured/addresses/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除地址
 * @param id 地址ID
 * @returns 删除结果
 */
export function deleteUserAddress(id: string) {
  return request({
    url: `/v1/user/secured/addresses/${id}`,
    method: 'delete'
  });
}

/**
 * 设置默认地址
 * @param id 地址ID
 * @returns 设置结果
 */
export function setDefaultAddress(id: string) {
  return request({
    url: `/v1/user/secured/addresses/${id}/default`,
    method: 'put'
  });
}
