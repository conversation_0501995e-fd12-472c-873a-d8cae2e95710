import request from '@/modules/user/utils/request';

// 定义跑腿员申请状态的响应数据接口
export interface RunnerApplyStatusResponse {
  id: number; // 申请编号
  user_id: number; // 用户ID
  real_name: string; // 真实姓名
  mobile: string; // 手机号
  create_time: string; // 申请时间
  status: number; // 审核状态：0-审核中，1-审核通过，2-审核拒绝
  status_desc: string; // 状态描述
  audit_time: string; // 审核时间
  reject_reason: string; // 拒绝原因
  estimatedAuditDays?: number; // 预计审核天数 (可选，前端使用)
}

/**
 * @description 获取跑腿员申请状态
 * @returns {Promise<RunnerApplyStatusResponse | null>} 返回申请状态数据或在404时返回null
 */
/**
 * @description 注册成为跑腿员
 * @param data 跑腿员注册参数
 * @returns {Promise<any>} 返回注册结果
 */
export const registerRunner = async (data: RunnerRegisterParams): Promise<any> => {
  return request({
    url: '/v1/runner/secured/register',
    method: 'POST',
    data
  });
};

/**
 * @description 获取跑腿员申请状态
 * @returns {Promise<RunnerApplyStatusResponse | null>} 返回申请状态数据或在404时返回null
 */
export const getRunnerApplyStatus = async (): Promise<RunnerApplyStatusResponse | null> => {
  try {
    const response :any = await request<RunnerApplyStatusResponse>({
      url: '/v1/runner/secured/apply-status',
      method: 'GET'
    });
    return response; // 显式返回 response.data
  } catch (error: any) {
    if (error.response && error.response.status === 404) {
      // 后端返回404表示未找到申请记录，这是一种业务逻辑上的正常情况
      return null;
    }
    // 对于其他错误，继续抛出，由调用方处理
    throw error;
  }
};

/**
 * 定义跑腿员注册参数接口
 */
export interface RunnerRegisterParams {
  real_name: string;            // 真实姓名
  id_card_number: string;       // 身份证号码
  id_card_front_pic: string;    // 身份证正面照片URL
  id_card_back_pic: string;     // 身份证背面照片URL
  face_pic: string;             // 人脸照片URL
  mobile: string;               // 手机号
  area_codes: string;           // 服务区域编码，逗号分隔
  service_radius: number;       // 服务半径(公里)
}
