/**
 * 用户认证相关API
 * 包括登录、登出、注册、验证码发送等功能
 */
import request from '@/modules/user/utils/request';
import type { UserLoginParams, SmsLoginParams, UserRegisterParams, LoginResponse, PhoneRegisterParams } from '../types';

/**
 * 用户密码登录
 * @param data 登录参数
 * @returns 登录结果，包含token等信息
 */
export function login(data: UserLoginParams): Promise<LoginResponse> {
  // 构建请求参数，包含设备信息
  const requestParams = {
    username: data.username,
    password: data.password,
    rememberMe: data.rememberMe,
    ...(data.device_info && { device_info: data.device_info })
  };
  
  return request({
    url: '/v1/user/login',
    method: 'post',
    data: requestParams
  });
}

/**
 * 刷新token
 * @param data 包含refresh_token的对象
 * @returns 新的token信息
 */
export function refreshToken(data: { refresh_token: string }) {
  return request({
    url: '/v1/user/refresh-token',
    method: 'post',
    data
  });
}

/**
 * 用户短信验证码登录
 * @param data 短信登录参数
 * @returns 登录结果，包含token等信息
 */
export function loginBySms(data: SmsLoginParams): Promise<LoginResponse> {
  // 构建请求参数，包含设备信息
  const requestParams = {
    mobile: data.phone,
    code: data.code,
    rememberMe: data.rememberMe,
    ...(data.device_info && { device_info: data.device_info })
  };
  
  return request({
    url: '/v1/user/login/verify-code',
    method: 'post',
    data: requestParams
  });
}

/**
 * 用户注册
 * @param data 注册参数
 * @returns 注册结果
 */
export function register(data: UserRegisterParams) {
  return request({
    url: '/v1/user/register',
    method: 'post',
    data
  });
}

/**
 * 手机号注册
 * @param data 手机号注册参数
 * @returns 注册结果
 */
export function phoneRegister(data: PhoneRegisterParams) {
  return request({
    url: '/v1/user/register/verify-code',
    method: 'post',
    data
  });
}

/**
 * 用户登出
 * @param deviceId 设备ID，用于标识要登出的设备
 * @returns 登出结果
 */
export const logout = async (deviceId: string): Promise<void> => {
  await request({
    url: `/v1/user/devices/${deviceId}/logout`,
    method: 'post'
  });
};

/**
 * 获取用户信息
 * @returns 用户信息响应
 */
export function getUserInfo() {
  return request({
    url: '/v1/user/secured/info',
    method: 'get'
  });
}

/**
 * 修改密码
 * @param oldPassword 旧密码
 * @param newPassword 新密码
 * @returns 修改结果
 */
export function changePassword(oldPassword: string, newPassword: string) {
  return request({
    url: '/v1/user/secured/password',
    method: 'put',
    data: {
      old_password: oldPassword,
      new_password: newPassword
    }
  });
}

/**
 * 发送短信验证码
 * @param mobile 手机号
 * @param purpose 验证码用途，login：登录，register：注册，reset：重置密码
 * @returns 发送结果
 */
export function sendSmsCode(mobile: string, purpose: 'login' | 'register' | 'reset' | 'bind') {
  return request({
    url: '/v1/user/send-verification-code',
    method: 'post',
    data: {
      mobile,
      purpose
    }
  });
}

/**
 * 发送短信验证码
 * @param mobile 手机号
 * @param purpose 验证码用途，login：登录，register：注册，reset：重置密码
 * @returns 发送结果
 */
export function sendRegisterSmsCode(mobile: string) {
  return request({
    url: '/v1/user/register/send-code',
    method: 'post',
    data: {
      mobile,
      purpose: 'register'
    }
  });
}
/**
 * 验证短信验证码 (需后端实现)
 * @param phone 手机号
 * @param code 验证码
 * @param type 验证码类型
 * @returns 验证结果
 */
export function verifySmsCode(_phone: string, _code: string, _type: 'login' | 'register' | 'reset' | 'bind') {
  // 由于后端API未完全实现，这里使用模拟数据
  console.log(`模拟验证码校验，手机: ${_phone}, 验证码: ${_code}, 类型: ${_type}`);
  return Promise.resolve({ valid: true });
}

/**
 * 重置密码 (需后端实现)
 * @param phone 手机号
 * @param code 验证码
 * @param newPassword 新密码
 * @returns 重置结果
 */
export function resetPassword(_phone: string, _code: string, _newPassword: string) {
  // 由于后端API未完全实现，这里使用模拟数据
  console.log(`模拟密码重置，手机: ${_phone}, 验证码: ${_code}`);
  return Promise.resolve({ message: '密码重置成功' });
}

/**
 * 获取长期token
 * 从本地存储中获取用户的refresh_token
 * @returns 长期token字符串或null
 */
export async function getLongTermToken(): Promise<string | null> {
  try {
    // 优先从sessionStorage获取
    const sessionToken = sessionStorage.getItem('user_refresh_token');
    if (sessionToken) {
      return sessionToken;
    }
    
    // 从localforage获取
    const { default: localforage } = await import('localforage');
    const localToken = await localforage.getItem('user_refresh_token');
    return localToken ? String(localToken) : null;
  } catch (error) {
    console.error('获取用户长期token失败:', error);
    return null;
  }
}
