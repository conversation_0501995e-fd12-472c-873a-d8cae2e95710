/**
 * 外卖模块客户端API
 * 包括分类、商家、食品、购物车、订单等相关功能
 */
import request from '@/modules/user/utils/request';
import { get } from '@/modules/user/utils/request';

// =============== 分类相关 API ===============

/**
 * 获取商户分类列表
 * @returns 分类列表
 */
export function getMerchantCategoryList() {
  return get('/v1/merchant/merchant-categories');
}

/**
 * 获取分类列表
 * @returns 分类列表
 */
export function getCategories() {
  return request({
    url: '/v1/user/takeout/categories',
    method: 'get'
  });
}

/**
 * 获取特定商家的分类列表
 * @param merchantId 商家ID
 * @returns 分类列表
 */
export function getMerchantCategories(merchantId: string | number) {
  return request({
    url: `/v1/user/takeout/merchants/${merchantId}/categories`,
    method: 'get'
  });
}
 

/**
 * 获取分类详情
 * @param id 分类ID
 * @returns 分类详情
 */
export function getCategoryById(id: string | number) {
  return request({
    url: `/v1/user/takeout/categories/${id}`,
    method: 'get'
  });
}

// =============== 全局分类相关 API ===============

/**
 * 获取全局分类列表
 * @returns 全局分类列表
 */
export function getGlobalCategories() {
  return request({
    url: '/v1/user/takeout/global-categories',
    method: 'get'
  });
}

/**
 * 获取全局分类树
 * @returns 全局分类树状结构
 */
export function getGlobalCategoryTree() {
  return request({
    url: '/v1/user/takeout/global-categories/tree',
    method: 'get'
  });
}

// =============== 商家相关 API ===============

/**
 * 获取商家列表
 * @param params 查询参数，如分类ID、排序方式等
 * @returns 商家列表
 */
export function getMerchants(params?: {
  categoryId?: string | number;
  page?: number;
  pageSize?: number;
  sort?: string;
  [key: string]: any;
}) {
  return request({
    url: '/v1/user/takeout/merchants',
    method: 'get',
    params
  });
}

/**
 * 获取指定商家的食品列表
 * @param id 商家ID
 * @param params 查询参数，如分类ID等
 * @returns 食品列表
 */
export function getMerchantFoods(id: string | number, params?: {
  categoryId?: string | number;
  page?: number;
  pageSize?: number;
  [key: string]: any;
}) {
  return request({
    url: `/v1/user/takeout/merchants/${id}/foods`,
    method: 'get',
    params
  });
}

/**
 * 获取单个商家的促销活动列表
 * @param merchantId 商家ID
 * @param data 请求参数
 * @param data.total_amount 订单总金额
 * @param data.food_ids 商品ID列表，英文逗号分隔
 * @returns 商家促销活动信息
 */
export function getMerchantPromotions(merchantId: string | number, data: {
  total_amount: number;
  food_ids: string;
}) {
  return request({
    url: `/v1/user/takeout/merchant/${merchantId}/promotions`,
    method: 'post',
    data
  });
}

/**
 * 获取多个商家的促销优惠信息
 * @param data 包含商家ID数组的请求数据
 * @returns 商家促销优惠信息
 */
export function getMerchantsPromotionsCoupons(data: {
  merchant_ids: (string | number)[];
  [key: string]: any;
}) {
  return request({
    url: '/v1/user/takeout/merchants/promotions-coupons',
    method: 'post',
    data
  });
}

// =============== 食品相关 API ===============

/**
 * 获取食品详情
 * @param id 食品ID
 * @returns 食品详情
 */
export function getFoodById(id: string | number) {
  return request({
    url: `/v1/user/takeout/foods/${id}`,
    method: 'get'
  });
}

/**
 * 获取食品规格
 * @param id 食品ID
 * @returns 食品规格列表
 */
export function getFoodVariants(id: string | number) {
  return request({
    url: `/v1/user/takeout/foods/${id}/variants`,
    method: 'get'
  });
}

/**
 * 获取食品套餐项
 * @param id 食品ID
 * @returns
 */
export function getFoodComboItems(id: string | number) {
  return request({
    url: `/v1/user/takeout/foods/${id}/combo-items`,
    method: 'get'
  });
}

// =============== 购物车相关 API (需要登录) ===============

/**
 * 添加商品到购物车
 * @param data 购物车添加参数
 * @returns 添加结果
 */
export function addToCart(data: {
  food_id: string | number;
  quantity: number;
  variant_id?: string | number;
  combo_items?: Array<{
    item_id: string | number;
    quantity: number;
  }>;
  [key: string]: any;
}) {
  return request({
    url: '/v1/user/takeout/cart/add',
    method: 'post',
    data
  });
}

/**
 * 更新购物车
 * @param data 购物车更新参数
 * @returns 更新结果
 */
export function updateCart(data: {
  cartItemId: string | number;
  quantity: number;
  [key: string]: any;
}) {
  return request({
    url: '/v1/user/takeout/cart/update',
    method: 'post',
    data
  });
}

/**
 * 从购物车移除商品
 * @param data 购物车移除参数
 * @returns 移除结果
 */
export function removeFromCart(data: {
  cartItemId: string | number | string[] | number[];
  [key: string]: any;
}) {
  return request({
    url: '/v1/user/takeout/cart/remove',
    method: 'post',
    data
  });
}

/**
 * 获取购物车列表
 * @returns 购物车商品列表
 */
export function getCartList() {
  return request({
    url: '/v1/user/takeout/cart/list',
    method: 'get'
  });
}

/**
 * 获取购物车商品数量
 * @returns 购物车商品数量
 */
export function getCartCount() {
  return request({
    url: '/v1/user/takeout/cart/count',
    method: 'get'
  });
}

/**
 * 选择购物车商品（用于结算前勾选）
 * @param data 选择参数
 * @returns 选择结果
 */
export function selectCartItems(data: {
  cart_item_ids: string[] | number[];
  selected: boolean;
  [key: string]: any;
}) {
  return request({
    url: '/v1/user/takeout/cart/select',
    method: 'post',
    data
  });
}

/**
 * 结算购物车
 * @param data 结算参数
 * @returns 结算结果，包含预订单信息
 */
export function checkoutCart(data?: {
  cartItemIds?: string[] | number[];
  addressId?: string | number;
  [key: string]: any;
}) {
  return request({
    url: '/v1/user/takeout/cart/checkout',
    method: 'post',
    data: data || {}
  });
}

// =============== 订单相关 API (需要登录) ===============

/**
 * 创建订单（支持新版多商家格式）
 * @param data 订单创建参数
 * @returns 创建的订单信息
 */
export function createOrder(data: {
  takeoutAddressID: number;
  paymentMethod: string;
  merchantOrders: Array<{
    merchantID: number;
    cartItemIDs: number[];
    couponID: number;
    deliveryTime: string;
    remark: string;
  }>;
}) {
  return request({
    url: '/v1/user/takeout/order/create',
    method: 'post',
    data
  });
}

/**
 * 创建订单（旧版单商家格式，保持向后兼容）
 * @param data 订单创建参数
 * @returns 创建的订单信息
 */
export function createOrderLegacy(data: {
  cartItemIDs: string[] | number[];
  takeoutAddressID: string | number;
  paymentMethod: string;
  remark?: string;
  [key: string]: any;
}) {
  return request({
    url: '/v1/user/takeout/order/create',
    method: 'post',
    data
  });
}

/**
 * 获取订单支付信息
 * @param orderID 订单ID
 * @returns 订单支付信息
 */
export function getOrderPayment(orderID: string | number) {
  return request({
    url: `/v1/user/takeout/order/payment/${orderID}`,
    method: 'get'
  });
}

/**
 * 获取订单详情
 * @param orderID 订单ID
 * @returns 订单详情
 */
export function getOrderDetail(orderID: string | number) {
  return request({
    url: `/v1/user/takeout/order/detail/${orderID}`,
    method: 'get'
  });
}

/**
 * 获取用户订单列表
 * @param params 查询参数
 * @returns 订单列表
 */
export function getOrderList(params?: {
  status?: string;
  page?: number;
  pageSize?: number;
  [key: string]: any;
}) {
  return request({
    url: '/v1/user/takeout/order/list',
    method: 'get',
    params
  });
}

/**
 * 获取用户订单数量
 * @param params 查询参数
 * @returns 订单数量
 */
export function getOrderCount(params?: {
  status?: string;
  [key: string]: any;
}) {
  return request({
    url: '/v1/user/takeout/order/count',
    method: 'get',
    params
  });
}

/**
 * 取消订单
 * @param data 取消参数
 * @returns 取消结果
 */
export function cancelOrder(orderID: string | number, data: {
  reason?: string;
  [key: string]: any;
}) {
  return request({
    url: `/v1/user/takeout/order/cancel/${orderID}`,
    method: 'post',
    data
  });
}

/**
 * 评价订单
 * @param data 评价参数
 * @returns 评价结果
 */
export function rateOrder(data: {
  orderId: string | number;
  rating: number;
  comment?: string;
  foodRatings?: Array<{
    foodId: string | number;
    rating: number;
    comment?: string;
  }>;
  [key: string]: any;
}) {
  return request({
    url: '/v1/user/takeout/order/rate',
    method: 'post',
    data
  });
}

/**
 * 确认收货
 * @param orderId 订单ID
 * @returns 确认结果
 */
export function confirmReceived(orderId: string | number) {
  return request({
    url: '/v1/user/takeout/order/confirm',
    method: 'post',
    data: { orderId }
  });
}

/**
 * 获取外卖订单详细信息（支付页面专用）
 * @param orderId 订单ID
 * @returns 外卖订单详情
 */
export function getTakeoutOrderDetails(orderId: string | number) {
  return request({
    url: `/v1/user/takeout/orders/${orderId}/details`,
    method: 'get'
  });
}
