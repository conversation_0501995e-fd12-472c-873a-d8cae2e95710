import request from '@/modules/user/utils/request';

// --- Interfaces for Payloads and Responses ---

// Profile
export interface RunnerProfileData {
  realName: string;
  idCard: string;
  phone: string;
  emergencyContact: string;
  emergencyPhone: string;
  gender: 'male' | 'female';
  avatar: string;
}

export interface UpdateProfilePayload {
  emergency_contact: string;
  emergency_phone: string;
  gender: 'male' | 'female';
  avatar: string;
}

// Service Settings
export interface ServiceTime {
  day: string;
  enabled: boolean;
  startTime: string;
  endTime: string;
}

export interface RunnerServiceSettingsData {
  serviceAreas: string[];
  serviceTimes: ServiceTime[];
  deliveryTool: 'bicycle' | 'electric' | 'motorcycle' | 'car';
  maxDistance: number;
  maxOrders: number;
  autoAccept: boolean;
}

// Notification Settings
export interface RunnerNotificationSettingsData {
  newOrder: boolean;
  orderStatus: boolean;
  income: boolean;
  system: boolean;
  sound: boolean;
  vibration: boolean;
}

// Change Password
export interface ChangePasswordPayload {
  old_password: string;
  new_password: string;
}

// Payment Account
export interface PaymentAccount {
  id: number; // Assuming ID is a number
  type: 'alipay' | 'wechat' | 'bank';
  accountNumber: string;
  accountName: string;
  bankName?: string;
  isDefault: boolean;
}

export interface AddPaymentAccountPayload {
  type: 'alipay' | 'wechat' | 'bank';
  account_number: string;
  account_name: string;
  bank_name?: string;
  is_default: boolean;
}

// --- API Service Functions ---

// Profile
export const getRunnerProfile = async (): Promise<RunnerProfileData> => {
  const response = await request<RunnerProfileData>({
    url: '/v1/runner/secured/profile',
    method: 'GET',
  });
  return response.data;
};

export const updateRunnerProfile = async (data: UpdateProfilePayload): Promise<any> => {
  const response = await request<any>({
    url: '/v1/runner/secured/profile',
    method: 'PUT',
    data,
  });
  return response.data;
};

// Service Settings
export const getRunnerServiceSettings = async (): Promise<RunnerServiceSettingsData> => {
  const response = await request<RunnerServiceSettingsData>({
    url: '/v1/runner/secured/service-settings',
    method: 'GET',
  });
  return response.data;
};

export const updateRunnerServiceSettings = async (data: RunnerServiceSettingsData): Promise<any> => {
  const response = await request<any>({
    url: '/v1/runner/secured/service-settings',
    method: 'PUT',
    data,
  });
  return response.data;
};

// Notification Settings
export const getRunnerNotificationSettings = async (): Promise<RunnerNotificationSettingsData> => {
  const response = await request<RunnerNotificationSettingsData>({
    url: '/v1/runner/secured/notification-settings',
    method: 'GET',
  });
  return response.data;
};

export const updateRunnerNotificationSettings = async (data: RunnerNotificationSettingsData): Promise<any> => {
  const response = await request<any>({
    url: '/v1/runner/secured/notification-settings',
    method: 'PUT',
    data,
  });
  return response.data;
};

// Security - Change Password
export const changeRunnerPassword = async (data: ChangePasswordPayload): Promise<any> => {
  const response = await request<any>({
    url: '/v1/runner/secured/change-password',
    method: 'PUT',
    data,
  });
  return response.data;
};

// Payment Accounts
export const getRunnerPaymentAccounts = async (): Promise<PaymentAccount[]> => {
  const response = await request<PaymentAccount[]>({
    url: '/v1/runner/secured/payment-accounts',
    method: 'GET',
  });
  return response.data;
};

export const addRunnerPaymentAccount = async (data: AddPaymentAccountPayload): Promise<any> => {
  const response = await request<any>({
    url: '/v1/runner/secured/payment-accounts',
    method: 'POST',
    data,
  });
  return response.data;
};

export const setDefaultRunnerPaymentAccount = async (accountId: number): Promise<any> => {
  const response = await request<any>({
    url: `/v1/runner/secured/payment-accounts/${accountId}/default`,
    method: 'PUT',
  });
  return response.data;
};

export const deleteRunnerPaymentAccount = async (accountId: number): Promise<any> => {
  const response = await request<any>({
    url: `/v1/runner/secured/payment-accounts/${accountId}`,
    method: 'DELETE',
  });
  return response.data;
};
