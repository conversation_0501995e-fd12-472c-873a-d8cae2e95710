import request from '@/modules/user/utils/request';

// 定义收入统计数据的接口
export interface IncomeStats {
  today: number; // 今日收入
  week: number; // 本周收入
  month: number; // 本月收入
  total: number; // 累计收入
}

// 定义账户余额信息的接口
export interface BalanceInfo {
  available: number; // 可提现余额
  frozen: number; // 冻结金额
  withdrawing: number; // 提现中金额
}

// 定义收入明细项的接口
export interface IncomeRecord {
  id: string | number;
  type: string; // 收入类型 (e.g., 'order_settlement', 'bonus')
  orderNo?: string; // 订单号 (可选)
  orderTime?: string; // 订单时间 (可选)
  amount: number; // 收入金额 (可正可负)
  status: string; // 状态 (e.g., 'completed', 'pending')
  createTime: string; // 创建时间
  remark?: string; // 备注 (可选)
}

// 定义提现记录项的接口
export interface WithdrawRecord {
  id: string | number;
  withdrawNo: string; // 提现单号
  amount: number; // 提现金额
  fee: number; // 手续费
  actualAmount: number; // 实际到账金额
  method: string; // 提现方式 (e.g., 'alipay', 'bank_transfer')
  account: string; // 收款账户
  accountName?: string; // 收款人姓名 (可选)
  status: string; // 状态 (e.g., 'processing', 'completed', 'failed')
  createTime: string; // 申请时间
  completeTime?: string; // 完成时间 (可选)
  remark?: string; // 备注 (可选)
}

// 定义分页列表的通用响应接口
interface PaginatedResponse<T> {
  records: T[];
  total: number;
}

/**
 * @description 获取跑腿员收入统计
 * @returns {Promise<IncomeStats>}
 */
export const getRunnerIncomeStats = () => {
  return request<IncomeStats>({
    url: '/v1/runner/secured/income-stats',
    method: 'GET',
  });
};

/**
 * @description 获取跑腿员账户余额信息
 * @returns {Promise<BalanceInfo>}
 */
export const getRunnerBalanceInfo = () => {
  return request<BalanceInfo>({
    url: '/v1/runner/secured/balance',
    method: 'GET',
  });
};

/**
 * @description 获取跑腿员收入明细列表
 * @param {object} params - 查询参数，包含分页和筛选条件
 * @returns {Promise<PaginatedResponse<IncomeRecord>>}
 */
export const getRunnerIncomeList = (params: any) => {
  return request<PaginatedResponse<IncomeRecord>>({
    url: '/v1/runner/secured/income-list',
    method: 'GET',
    params,
  });
};

/**
 * @description 获取跑腿员提现记录列表
 * @param {object} params - 查询参数，包含分页条件
 * @returns {Promise<PaginatedResponse<WithdrawRecord>>}
 */
export const getRunnerWithdrawList = (params: any) => {
  return request<PaginatedResponse<WithdrawRecord>>({
    url: '/v1/runner/secured/withdraw-list',
    method: 'GET',
    params,
  });
};

/**
 * @description 提交跑腿员提现申请
 * @param {object} data - 提现申请数据
 * @returns {Promise<any>}
 */
export const submitRunnerWithdraw = (data: {
  amount: number;
  method: string;
  account: string;
  account_name: string;
}) => {
  return request({
    url: '/v1/runner/secured/withdraw',
    method: 'POST',
    data,
  });
};
