/**
 * 用户账户相关API
 * <AUTHOR>
 * @date 2025-01-20
 * @version 1.0.0
 * @description 提供用户账户信息查询、账户变动记录查询等功能
 */

import request from '@/utils/request';

/**
 * 账户信息接口
 */
export interface AccountInfo {
  id: number;
  user_id: number;
  balance: string;
  frozen_balance: string;
  total_recharge: string;
  total_consume: string;
  status: number;
  last_recharge: string;
  last_consume: string;
  updated_at: string;
  created_at: string;
}

/**
 * 账户变动记录接口
 */
export interface AccountTransaction {
  id: number;
  transaction_no: string;
  related_id: number;
  related_type: string;
  amount: string;
  before_balance: string;
  after_balance: string;
  type: string;
  operation: string | number; // 支持字符串类型（'increase'/'decrease'）和数字类型（1/2）
  status: number;
  description: string;
  remark: string;
  client_ip: string;
  created_at: string;
}

/**
 * 账户变动记录查询参数
 */
export interface TransactionQueryParams {
  page?: number;
  page_size?: number;
  transaction_type?: number;
}

/**
 * 账户变动记录响应数据
 */
export interface TransactionListResponse {
  list: AccountTransaction[];
  total: number;
  page: number;
  size: number;
}

/**
 * 获取账户信息
 * @returns Promise<AccountInfo>
 */
export function getAccountInfo(): Promise<AccountInfo> {
  return request({
    url: '/v1/user/secured/account/info',
    method: 'get'
  });
}

/**
 * 获取账户变动记录
 * @param params 查询参数
 * @returns Promise<TransactionListResponse>
 */
export function getAccountTransactions(params?: TransactionQueryParams): Promise<TransactionListResponse> {
  return request({
    url: '/v1/user/secured/account/transactions',
    method: 'get',
    params
  });
}

/**
 * 交易类型枚举
 */
export enum TransactionType {
  ALL = 0,      // 全部
  RECHARGE = 1, // 充值
  CONSUME = 2,  // 消费
  REFUND = 3,   // 退款
  WITHDRAW = 4, // 提现
  TRANSFER = 5  // 转账
}

/**
 * 操作类型枚举
 */
export enum OperationType {
  INCREASE = 1, // 增加
  DECREASE = 2  // 减少
}

/**
 * 交易状态枚举
 */
export enum TransactionStatus {
  SUCCESS = 1,    // 成功
  FAILED = 2,     // 失败
  PROCESSING = 3  // 处理中
}

/**
 * 获取交易类型名称
 * @param type 交易类型
 * @returns 类型名称
 */
export function getTransactionTypeName(type: number): string {
  const typeMap: Record<number, string> = {
    [TransactionType.RECHARGE]: '充值',
    [TransactionType.CONSUME]: '消费',
    [TransactionType.REFUND]: '退款',
    [TransactionType.WITHDRAW]: '提现',
    [TransactionType.TRANSFER]: '转账'
  };
  return typeMap[type] || '未知';
}

/**
 * 获取交易状态名称
 * @param status 交易状态
 * @returns 状态名称
 */
export function getTransactionStatusName(status: number): string {
  const statusMap: Record<number, string> = {
    [TransactionStatus.SUCCESS]: '成功',
    [TransactionStatus.FAILED]: '失败',
    [TransactionStatus.PROCESSING]: '处理中'
  };
  return statusMap[status] || '未知';
}

/**
 * 获取交易状态样式类
 * @param status 交易状态
 * @returns 样式类名
 */
export function getTransactionStatusClass(status: number): string {
  const classMap: Record<number, string> = {
    [TransactionStatus.SUCCESS]: 'success',
    [TransactionStatus.FAILED]: 'danger',
    [TransactionStatus.PROCESSING]: 'warning'
  };
  return classMap[status] || 'info';
}