/**
 * 用户优惠券相关API接口
 * 包括优惠券领取、查看、使用等功能
 */
import request from '@/modules/user/utils/request';

// =============== 优惠券发现和领取 ===============

/**
 * 获取商家可领取的优惠券
 * @param merchantId 商家ID
 * @returns 可领取的优惠券列表
 */
export function getMerchantAvailableCoupons(merchantId: string | number) {
  return request({
    url: `/v1/user/takeout/merchants/${merchantId}/available-coupons`,
    method: 'get'
  });
}

/**
 * 获取优惠券中心列表
 * @param params 查询参数
 * @returns 优惠券中心数据
 */
export function getCouponCenter(params?: {
  category?: string; // all/food/delivery/new_user
  page?: number;
  page_size?: number;
}) {
  return request({
    url: '/v1/user/takeout/coupons/center',
    method: 'get',
    params
  });
}

/**
 * 领取优惠券
 * @param data 领取参数
 * @returns 领取结果
 */
export function claimCoupon(data: {
  coupon_id: string | number;
}) {
  return request({
    url: '/v1/user/takeout/coupons/claim',
    method: 'post',
    data
  });
}

// =============== 我的优惠券管理 ===============

/**
 * 获取我的优惠券列表
 * @param params 查询参数
 * @returns 我的优惠券列表
 */
export function getMyCoupons(params?: {
  status?: number; // 1:未使用 2:已使用 3:已过期
  merchant_id?: string | number;
  page?: number;
  page_size?: number;
}) {
  return request({
    url: '/v1/user/takeout/coupons/my-list',
    method: 'get',
    params
  });
}

/**
 * 获取优惠券详情
 * @param userCouponId 用户优惠券ID
 * @returns 优惠券详情
 */
export function getCouponDetail(userCouponId: string | number) {
  return request({
    url: `/v1/user/takeout/coupons/${userCouponId}`,
    method: 'get'
  });
}

/**
 * 获取优惠券使用历史
 * @param params 查询参数
 * @returns 使用历史
 */
export function getCouponUsageHistory(params?: {
  page?: number;
  page_size?: number;
  start_date?: string;
  end_date?: string;
}) {
  return request({
    url: '/v1/user/takeout/coupons/usage-history',
    method: 'get',
    params
  });
}

/**
 * 获取即将过期的优惠券
 * @param params 查询参数
 * @returns 即将过期的优惠券
 */
export function getExpiringSoonCoupons(params?: {
  days?: number; // 即将过期天数，默认3天
}) {
  return request({
    url: '/v1/user/takeout/coupons/expiring-soon',
    method: 'get',
    params
  });
}

// =============== 订单中使用优惠券 ===============

/**
 * 获取订单可用的优惠券
 * @param params 查询参数
 * @returns 可用和不可用的优惠券列表
 */
export function getAvailableCouponsForOrder(params: {
  merchant_id: string | number;
  total_amount: number;
  food_ids?: string; // 商品ID列表，逗号分隔
}) {
  return request({
    url: '/v1/user/takeout/coupons/available-for-order',
    method: 'get',
    params
  });
}

/**
 * 验证优惠券是否可用于订单
 * @param data 验证参数
 * @returns 验证结果
 */
export function validateCouponForOrder(data: {
  user_coupon_id: string | number;
  order: {
    merchant_id: string | number;
    total_amount: number;
    items: Array<{
      food_id: string | number;
      quantity: number;
      price: number;
      category_id?: string | number;
    }>;
  };
}) {
  return request({
    url: '/v1/user/takeout/coupons/validate-for-order',
    method: 'post',
    data
  });
}