/**
 * 用户邀请/分销模块API
 * 提供用户邀请和分销相关的后端接口封装
 */
import { get } from '@/modules/user/utils/request';

/**
 * 获取用户的邀请链接和邀请码
 * @returns Promise 包含邀请链接和邀请码信息
 */
export function getUserReferralInfo() {
  return get<{
    referral_code: string;
    referral_link: string;
  }>('/v1/user/secured/referral/info');
}

/**
 * 获取分销统计数据
 * @returns Promise 包含分销统计数据
 */
export function getReferralStatistics() {
  return get<{
    totalReferrals: number;
    level1Referrals: number;
    level2Referrals: number;
    level3Referrals: number;
    totalCommission: number;
    todayCommission: number;
    thisMonthCommission: number;
    thisYearCommission: number;
  }>('/v1/user/secured/referral/statistics');
}

/**
 * 获取推荐用户列表
 * @param params 查询参数
 * @returns Promise 包含推荐用户列表
 */
export function getReferrals(params: {
  page: number;
  page_size: number;
  level?: number;
  only_direct?: boolean;
}) {
  return get<{
    list: any[];
    total: number;
  }>('/v1/user/secured/referral/users', params);
}

/**
 * 获取用户的推荐人信息
 * @returns Promise 包含推荐人信息
 */
export function getReferrer() {
  return get<{
    id: number;
    nickname: string;
    avatar: string;
  }>('/v1/user//secured/referral/referrer');
}
