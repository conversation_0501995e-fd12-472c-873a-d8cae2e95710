/**
 * 作者: 张二浩
 * 日期: 2025-07-03
 * 版本: 1.0.0
 * 描述: 统一的商家类型定义文件
 * 功能: 定义商家组类型，供各组件统一使用，避免类型冲突
 */

import type { CartItemType } from './cart';

/**
 * 商家组类型定义
 * 统一后的MerchantGroupType类型，兼容MerchantGroup.vue和CheckoutDialog.vue使用
 */
export interface MerchantGroupType {
  id: number;
  merchant_id: number | string;
  merchant_name: string;
  
  // 位置信息
  merchant_latitude?: number;
  merchant_longitude?: number;
  
  // 兼容旧版字段名
  merchantLatitude?: number;
  merchantLongitude?: number;
  
  // 商品列表
  items: CartItemType[];
  
  // 价格相关
  total_amount?: number;
  packaging_fee?: number;
  total_price?: number;
  merchant_distance?: number;
}
