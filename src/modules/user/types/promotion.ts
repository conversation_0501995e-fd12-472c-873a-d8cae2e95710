/**
 * 促销活动相关类型定义
 */

// 促销活动类型枚举
export enum PromotionType {
  FULL_REDUCTION = 'full_reduction', // 满减
  DISCOUNT = 'discount', // 折扣
  GIFT = 'gift' // 赠品
}

// 促销活动接口
export interface Promotion {
  id: string | number;
  name: string;
  type: string | number; // 支持数字类型的促销类型
  condition_amount?: number;
  discount_amount?: number;
  discount_rate?: number;
  description?: string;
  start_time?: string;
  end_time?: string;
  merchant_id?: string | number;
  status?: number;
  is_active?: boolean;
  min_amount?: number; // 最低消费金额
  // 促销活动规则（可以是字符串或对象）
  rules?: string | {
    coupon?: {
      min_order_amount?: number;
      amount?: number;
    };
    min_amount?: number;
    amount?: number;
  };
  // 是否可用（运行时动态判断）
  is_available?: boolean;
}

// 促销活动验证结果
export interface PromotionValidationResult {
  valid: boolean;
  original_amount: number;
  discount_amount: number;
  final_amount: number;
  promotion_info?: {
    id: string | number;
    name: string;
    type: PromotionType | string;
  };
  error_message?: string;
}

// 可用促销活动响应数据
export interface AvailablePromotionsResponse {
  available_promotions: Array<Promotion & {
    can_use: boolean;
    reason: string;
    discount_amount: number;
    final_amount: number;
  }>;
  unavailable_promotions: Array<Promotion & {
    can_use: boolean;
    reason: string;
  }>;
}
