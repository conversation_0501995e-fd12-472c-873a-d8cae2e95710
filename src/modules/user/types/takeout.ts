/**
 * 外卖模块类型定义
 * 定义外卖相关的接口和枚举类型
 */

// 外卖分类接口
export interface TakeoutCategory {
  id: string | number;
  name: string;
  icon?: string;
  image?: string;
  description?: string;
  parentId?: string | number;
  level: number;
  sortOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 全局分类接口
export interface GlobalCategory extends TakeoutCategory {
  children?: GlobalCategory[];
}

// 商家信息接口
export interface Merchant {
  id: string | number;
  name: string;
  logo: string;
  cover: string;
  description?: string;
  address: string;
  phone?: string;
  businessHours?: string;
  categories: TakeoutCategory[];
  rating: number;
  ratingCount: number;
  minDeliveryAmount: number;
  deliveryFee: number;
  deliveryTime: string;
  distance?: number; // 单位：米
  isOpen: boolean;
  isPaused: boolean;
  createdAt: string;
  updatedAt: string;
}

// 食品接口
export interface Food {
  id: string | number;
  merchantId: string | number;
  categoryId: string | number;
  name: string;
  description?: string;
  price: number;
  originalPrice?: number;
  image: string;
  images?: string[];
  unit?: string;
  stock: number;
  salesCount: number;
  rating: number;
  ratingCount: number;
  isActive: boolean;
  isRecommended: boolean;
  hasVariants: boolean;
  hasCombo: boolean;
  attributes?: Record<string, string>; // 如 {"辣度": "中辣", "热量": "高"}
  tags?: string[];
  packagingFee?: number; // 打包费
  preparationTime?: number; // 备餐时间（分钟）
  createdAt: string;
  updatedAt: string;
  variants?: Variant[];
}

// 规格接口
export interface Variant {
  id: string | number;
  name: string;
  price: number;
  original_price?: number; // 原价
  stock: number;
  is_active: boolean;
  is_default: boolean;
}

// 食品规格组接口
export interface VariantGroup {
  id: string | number;
  name: string;
  required: boolean;
  multiSelect: boolean;
  min?: number;
  max?: number;
  options: VariantOption[];
}

// 规格选项接口
export interface VariantOption {
  id: string | number;
  name: string;
  price: number;
  isDefault: boolean;
  isActive: boolean;
}

// 套餐组接口
export interface ComboGroup {
  id: string | number;
  name: string;
  required: boolean;
  multiSelect: boolean;
  min?: number;
  max?: number;
  items: ComboItem[];
}

// 套餐项接口
export interface ComboItem {
  id: string | number;
  name: string;
  image?: string;
  price: number;
  originalPrice?: number;
  isDefault: boolean;
  isActive: boolean;
}

// 购物车项接口
export interface CartItem {
  id: string | number;
  userId: string | number;
  foodId: string | number;
  food: Food;
  quantity: number;
  selected: boolean;
  // 商家信息，用于购物车按商家分组
  merchant_id?: string | number;
  merchant_name?: string;
  // 商家经纬度信息，用于计算配送距离
  merchant_latitude?: number;
  merchant_longitude?: number;
  variants?: {
    variantId: string | number;
    name: string;
    price: number;
  }[];
  comboItems?: {
    itemId: string | number;
    name: string;
    price: number;
    quantity: number;
  }[];
  packaging_fee?: number; // 包装费用
  totalPrice: number;
  createdAt: string;
  updatedAt: string;
}

// 购物车接口
export interface Cart {
  items: CartItem[];
  totalPrice: number;
  totalItems: number;
  merchantId: string | number;
  merchantName: string;
}

// 外卖订单状态枚举
export enum TakeoutOrderStatus {
  PENDING = 10,     // 待支付
  PAID = 20,        // 已支付
  PROCESSING = 30,  // 处理中
  DELIVERING = 40,  // 配送中
  COMPLETED = 50,   // 已完成
  CANCELLED = 60,   // 已取消
  REFUNDING = 70,   // 退款中
  REFUNDED = 80     // 已退款
}

// 外卖订单接口
export interface TakeoutOrder {
  orderID: string | number;
  orderNumber: string;
  userId: string | number;
  userID: string | number;  // 添加驼峰命名支持
  merchantId: string | number;
  merchantID: string | number;  // 添加驼峰命名支持
  merchant: {
    id: string | number;
    name: string;
    logo: string;
    phone?: string;
  };
  items: TakeoutOrderItem[];
  address: {
    id: string | number;
    name: string;
    phone: string;
    province: string;
    city: string;
    district: string;
    detail: string;
  };
  orderStatus: TakeoutOrderStatus;
  totalAmount: number;
  deliveryFee: number;
  packagingFee?: number;
  discountAmount?: number;
  payableAmount: number;
  paymentMethod?: string;
  paidAt?: string;
  acceptedAt?: string;
  preparedAt?: string;
  readyAt?: string;
  deliveredAt?: string;
  completedAt?: string;
  cancelledAt?: string;
  refundedAt?: string;
  estimatedDeliveryTime?: string;
  deliveryTime?: number; // 配送时间（分钟）
  remark?: string;
  createdAt: string;
  updatedAt: string;
}

// 外卖订单项接口
export interface TakeoutOrderItem {
  id: string | number;
  orderId: string | number;
  foodId: string | number;
  foodName: string;
  foodImage: string;
  quantity: number;
  price: number;
  variants?: {
    variantId: string | number;
    name: string;
    price: number;
  }[];
  comboItems?: {
    itemId: string | number;
    name: string;
    price: number;
    quantity: number;
  }[];
  totalPrice: number;
  rating?: number;
  comment?: string;
  ratedAt?: string;
}

// 订单评价接口
export interface OrderRating {
  id: string | number;
  orderId: string | number;
  userId: string | number;
  merchantId: string | number;
  rating: number;
  comment?: string;
  images?: string[];
  foodRatings?: {
    foodId: string | number;
    rating: number;
    comment?: string;
  }[];
  createdAt: string;
  updatedAt: string;
}
