/**
 * 作者: 张二浩
 * 日期: 2025-07-03
 * 版本: 1.0.0
 * 描述: 统一的购物车类型定义文件
 * 功能: 定义购物车项类型，供各组件统一使用，避免类型冲突
 */

/**
 * 购物车项类型定义
 * 统一后的CartItemType类型，兼容MerchantGroup.vue和CheckoutDialog.vue使用
 */
export interface CartItemType {
  // 通用基本字段
  id: string | number; // 改为联合类型以提高兼容性
  quantity: number;
  price: number;
  
  // 商品信息
  name?: string;
  description?: string;
  totalPrice?: number;
  image?: string;
  
  // 包装费
  packaging_fee?: number;
  
  // 商家相关信息
  merchant_id?: number | string;
  merchant_name?: string;
  merchant_latitude?: number;
  merchant_longitude?: number;
  
  // 状态字段
  selected?: boolean;
  
  // 食品详情（兼容外卖模块）
  food?: {
    id: string | number; // 改为联合类型以提高兼容性
    name: string;
    description?: string;
    price: number;
    image?: string;
  };
  
  // 商品组合与变体
  variants?: any[]; // 选项变体列表
  combo_items?: any[]; // 组合项列表
  attributes?: any; // 用于存储额外属性
  current_stock?: number; // 当前库存
  
  // 其他兼容字段
  foodId?: number | string; // 兼容旧版API
  item_price?: number; // 单项价格（兼容旧版）
}
