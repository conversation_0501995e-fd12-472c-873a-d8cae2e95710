/**
 * 用户模块类型定义
 * 定义用户相关的接口和枚举类型
 */

// 导出外卖模块相关类型
export * from './takeout';

// 登录API响应类型
export interface LoginResponse {
  token_info: {
    access_token: string;
    expires_in: number;
    refresh_token: string;
    token_type: string;
  };
  user: UserInfo;
  device_id?: string;
  is_new_device?: boolean;
  risk_level?: string;
}

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 'active',       // 正常状态
  FROZEN = 'frozen',       // 账户冻结
  DELETED = 'deleted'      // 已注销
}

// 用户信息接口
export interface UserInfo {
  id: string;
  username: string;
  nickname: string;
  avatar?: string;
  email?: string;
  phone?: string;
  status: UserStatus;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  role: string;
  balance?: number;
  points?: number;
  level?: number;
  gender?: 'male' | 'female' | 'other' | 'unknown';
  birthday?: string;
}

// 设备信息接口
export interface DeviceInfo {
  device_id: string;
  device_name: string;
  device_type: string;
  platform: string;
  browser: string;
  app_version: string;
  os_version: string;
  user_agent: string;
}

// 用户登录请求参数
export interface UserLoginParams {
  username: string;
  password: string;
  rememberMe?: boolean;
  device_info?: DeviceInfo;
}

// 短信验证码登录参数
export interface SmsLoginParams {
  phone: string;
  code: string;
  rememberMe?: boolean;
  device_info?: DeviceInfo;
}

// 用户注册参数
export interface UserRegisterParams {
  username: string;
  password: string;
  confirmPassword: string;
  phone: string;
  email?: string;
  code: string;
  agreePrivacyPolicy: boolean;
  referrer?: string; // 推荐人ID
}

// 手机号注册参数
export interface PhoneRegisterParams {
  mobile: string;
  code: string;
  referrer_id?: number; // 推荐人ID
}

// 用户地址信息
export interface UserAddress {
  id: string;
  userId: string;
  receiver_name: string;
  receiver_mobile: string;
  phone: string;
  province: string;
  city: string;
  district: string;
  detailed_address: string;
  is_default: boolean;
  postal_code?: string;
  address_tag?: string;
  location_longitude?: number;
  location_latitude?: number;
  createdAt: string;
  updatedAt: string;
}

// 用户订单状态枚举
export enum OrderStatus {
  PENDING = 10,     // 待支付
  PAID = 20,        // 已支付
  PROCESSING = 30,  // 处理中
  DELIVERING = 40,  // 配送中
  COMPLETED = 50,   // 已完成
  CANCELLED = 60,   // 已取消
  REFUNDING = 70,   // 退款中
  REFUNDED = 80     // 已退款
}

// 用户订单信息
export interface UserOrder {
  id: string;
  userId: string;
  orderNumber: string;
  totalAmount: number;
  payAmount: number; // 实际支付金额
  status: OrderStatus;
  paymentMethod: string;
  items: OrderItem[];
  address: UserAddress;
  createdAt: string;
  updatedAt: string;
  paidAt?: string;
  shippedAt?: string;
  deliveredAt?: string;
  completedAt?: string;
  cancelledAt?: string;
}

// 订单项信息
export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  productName: string;
  productImage: string;
  quantity: number;
  price: number;
  totalPrice: number;
}
