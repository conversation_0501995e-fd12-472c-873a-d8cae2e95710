/**
 * 优惠券相关类型定义
 */

// 优惠券类型枚举
export enum CouponType {
  DISCOUNT = 1, // 满减券
  PERCENTAGE = 2, // 折扣券
  FREE_DELIVERY = 3, // 免配送费券
  NEW_USER = 4 // 新用户券
}

// 优惠券状态枚举
export enum CouponStatus {
  UNUSED = 1, // 未使用
  USED = 2, // 已使用
  EXPIRED = 3 // 已过期
}

// 优惠券模板（商家发布的优惠券）
export interface CouponTemplate {
  id: string | number;
  merchant_id: string | number;
  merchant_name: string;
  merchant_logo: string;
  name: string;
  description: string;
  type: CouponType;
  amount: number; // 优惠金额或折扣比例
  min_order_amount: number; // 最低订单金额
  total_limit: number; // 总发放数量限制
  claimed_count: number; // 已领取数量
  per_user_limit: number; // 每用户限领数量
  user_claimed_count: number; // 当前用户已领取数量
  can_claim: boolean; // 是否可以领取
  claim_button_text: string; // 领取按钮文本
  start_time: string;
  end_time: string;
  status: number; // 模板状态
  created_at: string;
  updated_at: string;
}

// 用户优惠券实例
export interface UserCoupon {
  id: string | number;
  coupon_id: string | number;
  user_id: string | number;
  name: string;
  description: string;
  type: CouponType;
  amount: number;
  min_order_amount: number;
  merchant_id: string | number;
  merchant_name: string;
  merchant_logo: string;
  status: CouponStatus;
  status_text: string;
  expire_time: string;
  received_time: string;
  used_time?: string;
  order_id?: string | number;
  days_to_expire: number;
  can_use: boolean;
  // 使用规则
  usage_rules?: {
    apply_to_all: boolean;
    apply_to_categories: string[];
    apply_to_foods: string[];
    exclude_foods: string[];
    can_combine: boolean;
  };
  // 扩展属性，用于结算流程中
  discount_amount?: number; // 优惠金额
  final_amount?: number; // 最终金额
  reason?: string; // 不可用原因
  rate?: number; // 折扣率
}

// 优惠券中心响应数据
export interface CouponCenterResponse {
  categories: Array<{
    key: string;
    name: string;
    count: number;
  }>;
  coupons: CouponTemplate[];
  total: number;
  page: number;
  page_size: number;
}

// 我的优惠券响应数据
export interface MyCouponsResponse {
  statistics: {
    unused_count: number;
    used_count: number;
    expired_count: number;
  };
  coupons: UserCoupon[];
  total: number;
  page: number;
  page_size: number;
}

// 订单可用优惠券响应数据
export interface OrderAvailableCouponsResponse {
  available_coupons: Array<UserCoupon & {
    can_use: boolean;
    reason: string;
    discount_amount: number;
    final_amount: number;
  }>;
  unavailable_coupons: Array<UserCoupon & {
    can_use: boolean;
    reason: string;
  }>;
}

// 优惠券验证结果
export interface CouponValidationResult {
  valid: boolean;
  original_amount: number;
  discount_amount: number;
  final_amount: number;
  coupon_info: {
    id: string | number;
    name: string;
    type: CouponType;
  };
  error_message?: string;
}

// 优惠券使用历史
export interface CouponUsageHistory {
  id: string | number;
  coupon_name: string;
  merchant_name: string;
  order_id: string | number;
  order_no: string;
  discount_amount: number;
  used_time: string;
  order_status: string;
}

// 优惠券使用历史响应数据
export interface CouponUsageHistoryResponse {
  history: CouponUsageHistory[];
  total: number;
  page: number;
  page_size: number;
  total_saved: number; // 总共节省金额
}

// 即将过期优惠券响应数据
export interface ExpiringSoonCouponsResponse {
  expiring_coupons: Array<UserCoupon & {
    days_left: number;
  }>;
  total_count: number;
}

// 领取优惠券响应数据
export interface ClaimCouponResponse {
  user_coupon_id: string | number;
  coupon_name: string;
  amount: number;
  min_order_amount: number;
  expire_time: string;
  status: CouponStatus;
  merchant_name: string;
}