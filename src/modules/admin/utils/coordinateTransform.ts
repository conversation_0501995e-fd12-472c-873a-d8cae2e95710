/**
 * @Description: 坐标系转换工具函数
 * @Author: AI Assistant
 * @Date: 2025-06-09
 * @Version: 1.0.0
 */

/**
 * @function convertToWGS84
 * @description 将GCJ02坐标系转换为WGS84坐标系
 * @param gcjLon GCJ02经度
 * @param gcjLat GCJ02纬度
 * @returns WGS84坐标对象
 */
export const convertToWGS84 = (gcjLon: number, gcjLat: number) => {
  if (!gcjLon || !gcjLat) {
    return { longitude: 0, latitude: 0 }
  }
  
  const a = 6378245.0 // 长半轴
  const ee = 0.00669342162296594323 // 偏心率平方
  
  const dLat = transformLat(gcjLon - 105.0, gcjLat - 35.0)
  const dLon = transformLon(gcjLon - 105.0, gcjLat - 35.0)
  const radLat = (gcjLat / 180.0) * Math.PI
  let magic = Math.sin(radLat)
  magic = 1 - ee * magic * magic
  const sqrtMagic = Math.sqrt(magic)
  const deltaLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * Math.PI)
  const deltaLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * Math.PI)
  
  return {
    longitude: gcjLon - deltaLon,
    latitude: gcjLat - deltaLat
  }
}

/**
 * @function transformLat
 * @description 纬度转换辅助函数
 * @param x 经度偏移
 * @param y 纬度偏移
 * @returns 转换后的纬度偏移
 */
const transformLat = (x: number, y: number) => {
  let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x))
  ret += (20.0 * Math.sin(6.0 * x * Math.PI) + 20.0 * Math.sin(2.0 * x * Math.PI)) * 2.0 / 3.0
  ret += (20.0 * Math.sin(y * Math.PI) + 40.0 * Math.sin(y / 3.0 * Math.PI)) * 2.0 / 3.0
  ret += (160.0 * Math.sin(y / 12.0 * Math.PI) + 320 * Math.sin(y * Math.PI / 30.0)) * 2.0 / 3.0
  return ret
}

/**
 * @function transformLon
 * @description 经度转换辅助函数
 * @param x 经度偏移
 * @param y 纬度偏移
 * @returns 转换后的经度偏移
 */
const transformLon = (x: number, y: number) => {
  let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x))
  ret += (20.0 * Math.sin(6.0 * x * Math.PI) + 20.0 * Math.sin(2.0 * x * Math.PI)) * 2.0 / 3.0
  ret += (20.0 * Math.sin(x * Math.PI) + 40.0 * Math.sin(x / 3.0 * Math.PI)) * 2.0 / 3.0
  ret += (150.0 * Math.sin(x / 12.0 * Math.PI) + 300.0 * Math.sin(x / 30.0 * Math.PI)) * 2.0 / 3.0
  return ret
}

/**
 * @function formatDate
 * @description 格式化日期
 * @param date 日期字符串或日期对象
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | Date) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
