/**
 * 管理员模块路由配置
 * 提供静态路由配置和动态路由生成功能
 */
import Layout from '@/layouts/AdminLayout.vue';
import type { RouteRecordRaw } from 'vue-router';

// 定义前端路径数据的接口
interface PathItem {
  path: string;
  title: string;
  count: number;
  config_key: string;
  config_type: string;
  group: string;
  icon: string;
  id: string;
  version_hash: string;
  [key: string]: any;
}

export interface ModuleData {
  module: string;
  paths: PathItem[];
}

// 提前导入并缓存组件，避免懒加载问题
// 这将确保组件在路由初始化时就已经开始加载
const DynamicConfigPagePromise = import('../views/dynamicGridConfig/DynamicGridConfigPage.vue');
// 使用一个函数返回已经开始加载的Promise
export const getDynamicConfigComponent = () => DynamicConfigPagePromise;


// 静态路由配置
const staticRoutes: RouteRecordRaw[] = [
  // 管理员登录 - 不使用AdminLayout
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: () => import('../views/Login.vue'),
    meta: { title: '管理员登录', requiresAuth: false }
  },
  
  // 使用AdminLayout的管理员页面
  {
    path: '/admin',
    name: 'AdminLayout',
    component: Layout,
    redirect: '/admin/dashboard',
    meta: { requiresAuth: true, roles: ['admin'] },
    children: [
      // 管理员控制台
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: () => import('../views/DashboardView.vue'),
        meta: { title: '控制台', requiresAuth: true }
      },

      // 文件上传测试
      // {
      //   path: 'file',
      //   name: 'FileUploadDemo',
      //   component: () => import('../views/FileUploadStyleDemo.vue'),
      //   meta: { title: '文件上传测试', requiresAuth: true }
      // },

      // 个人信息
      {
        path: 'profile',
        name: 'AdminProfile',
        component: () => import('../views/AdminProfileView.vue'),
        meta: { title: '个人信息', requiresAuth: true }
      },
      
      // 管理员管理
      {
        path: 'admins',
        name: 'AdminManagement',
        component: () => import('../views/AdminManagementView.vue'),
        meta: { 
          title: '管理员管理', 
          requiresAuth: true, 
          roles: ['SUPER_ADMIN', 'ADMIN'],
          icon: 'User' 
        }
      },
      
      // 角色管理
      {
        path: 'system/roles',
        name: 'RoleManagement',
        component: () => import('../views/RoleManagementView.vue'),
        meta: { title: '角色管理', requiresAuth: true, roles: ['SUPER_ADMIN'] }
      },
      
      // 权限管理
      {
        path: 'system/permissions',
        name: 'PermissionManagement',
        component: () => import('../views/PermissionManagementView.vue'),
        meta: { title: '权限管理', requiresAuth: true, roles: ['SUPER_ADMIN'] }
      },
      
      // 商家管理
      {
        path: 'merchants',
        name: 'MerchantManagement',
        component: () => import('../views/merchants/MerchantView.vue'),
        meta: { title: '商家管理', requiresAuth: true, roles: ['admin'] }
      },
      
      // 商家详情
      {
        path: 'merchants/:id',
        name: 'MerchantDetail',
        component: () => import('../views/merchants/MerchantDetailView.vue'),
        meta: { title: '商家详情', requiresAuth: true, roles: ['admin'] },
        props: true
      },

      // 外卖商品管理
      // {
      //   path: 'foods',
      //   name: 'FoodManagement',
      //   component: () => import('../views/takeout/TakeoutFoodView.vue'),
      //   meta: { title: '外卖商品管理', requiresAuth: true, roles: ['admin'] }
      // },
      
      // 外卖商品详情
      // {
      //   path: 'foods/:id',
      //   name: 'FoodDetail',
      //   component: () => import('../views/takeout/TakeoutFoodDetailView.vue'),
      //   meta: { title: '外卖商品详情', requiresAuth: true, roles: ['admin'] },
      //   props: true
      // },

      // Grid管理
      {
        path: 'system/gridmanagement',
        name: 'GridManagement',
        component: () => import('../views/gridManagement/GridManagement.vue'),
        meta: { title: 'Grid管理', requiresAuth: true, roles: ['admin'] }
      },
      
      // 网格信息管理
      {
        path: 'system/gridinfos',
        name: 'GridInfoManagement',
        component: () => import('../views/GridInfoManagement.vue'),
        meta: { title: '网格信息管理', requiresAuth: true, roles: ['admin'] }
      },
      
      // 网格仪表盘
      // {
      //   path: 'system/grid',
      //   name: 'GridDashboard',
      //   component: () => import('../views/GridDashboardView.vue'),
      //   meta: { title: '网格仪表盘', requiresAuth: true, roles: ['SUPER_ADMIN'] }
      // },
      
      // 系统配置
      {
        path: 'system/config',
        name: 'SystemConfig',
        component: () => import('../views/system-config/index.vue'),
        meta: { title: '系统配置', requiresAuth: true, roles: ['admin'] }
      },
      
      // 全局分类管理
      {
        path: 'global-categories',
        name: 'GlobalCategoryManagement',
        component: () => import('../views/global-category/GlobalCategoryList.vue'),
        meta: { 
          title: '全局分类管理', 
          requiresAuth: true, 
          roles: ['admin'],
          icon: 'FolderOpened' 
        }
      },
      
      // GridStack拖动手柄演示Demo
      // {
      //   path: 'gridstack-demo',
      //   name: 'GridStackHandleDemo',
      //   component: () => import('@/components/base/GridStackHandleDemo.vue'),
      //   meta: { title: 'GridStack拖动手柄演示', requiresAuth: false }
      // },
      
      // 动态配置页面 - 根据路径参数渲染不同配置
      {
        path: 'page/:configId',
        name: 'DynamicConfigPage',
        component: getDynamicConfigComponent,
        meta: { 
          title: '配置页面',
          requiresAuth: true,
          dynamic: true
        },
        props: true
      },
      
      // 特定的动态配置页面示例
      // {
      //   path: 'system/settings',
      //   name: 'SystemSettings',
      //   component: getDynamicConfigComponent,
      //   meta: { 
      //     title: '系统设置',
      //     requiresAuth: true
      //   },
      //   props: { configId: 'system_settings' }
      // },
      
      {
        path: 'content/articles',
        name: 'ArticleManagement',
        component: getDynamicConfigComponent,
        meta: { 
          title: '文章管理',
          requiresAuth: true
        },
        props: { configId: 'content_articles' }
      },
      
      // 外卖商品管理
      {
        path: 'takeout/food/list',
        name: 'AdminTakeoutFoodList',
        component: () => import('../views/takeout/TakeoutFoodView.vue'),
        meta: { 
          title: '外卖商品管理',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      
      // 用户管理
      {
        path: 'users',
        name: 'AdminUserManagement',
        component: () => import('../views/user/UserView.vue'),
        meta: { 
          title: '用户管理',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      
      // 用户详情
      {
        path: 'users/:id',
        name: 'AdminUserDetail',
        component: () => import('../views/user/UserDetailView.vue'),
        meta: { 
          title: '用户详情',
          requiresAuth: true,
          roles: ['admin']
        },
        props: true
      },
      
      // 外卖商品详情与审核
      {
        path: 'takeout/food/detail/:id',
        name: 'AdminTakeoutFoodDetail',
        component: () => import('../views/takeout/TakeoutFoodDetailView.vue'),
        meta: { 
          title: '外卖商品详情',
          requiresAuth: true,
          roles: ['admin']
        },
        props: true
      },
      
      // 跑腿员管理
      {
        path: 'runners',
        name: 'AdminRunnerManagement',
        component: () => import('../views/runner/RunnerView.vue'),
        meta: { 
          title: '跑腿员管理',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      
      // 分销配置管理
      {
        path: 'referral',
        name: 'ReferralManagement',
        component: () => import('../views/referral/ReferralView.vue'),
        meta: { 
          title: '分销配置管理',
          requiresAuth: true,
          roles: ['admin'],
          icon: 'Share'
        }
      },
      
      // 分销等级配置
      {
        path: 'referral/levels',
        name: 'ReferralLevels',
        component: () => import('../views/referral/ReferralLevelsView.vue'),
        meta: { 
          title: '分销等级配置',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      
      // 佣金配置
      {
        path: 'referral/commission',
        name: 'CommissionConfig',
        component: () => import('../views/referral/CommissionConfigView.vue'),
        meta: { 
          title: '佣金配置',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      
      // 分销关系管理
      {
        path: 'referral/relationships',
        name: 'ReferralRelationships',
        component: () => import('../views/referral/ReferralRelationshipsView.vue'),
        meta: { 
          title: '分销关系管理',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      
      // 分销统计
      {
        path: 'referral/statistics',
        name: 'ReferralStatistics',
        component: () => import('../views/referral/ReferralStatisticsView.vue'),
        meta: { 
          title: '分销统计',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      
      // 佣金管理
      {
        path: 'referral/commission-management',
        name: 'CommissionManagement',
        component: () => import('../views/referral/CommissionManagementView.vue'),
        meta: { 
          title: '佣金管理',
          requiresAuth: true,
          roles: ['admin']
        }
      }
    ]
  }
];

// 缓存已生成的动态路由配置
let cachedDynamicRoutes: RouteRecordRaw[] | null = null;

/**
 * 根据前端路径数据生成动态路由配置
 * @param frontendPaths 前端路径数据
 * @returns 动态生成的路由配置
 */
export function generateDynamicRoutes(frontendPaths: ModuleData[]): RouteRecordRaw[] {
  if (!frontendPaths || !Array.isArray(frontendPaths) || frontendPaths.length === 0) {
    console.warn('前端路径数据为空，无法生成动态路由');
    return staticRoutes;
  }

  // 如果有缓存且使用相同的输入，直接返回缓存
  if (cachedDynamicRoutes) {
    console.log('使用缓存的动态路由配置');
    return cachedDynamicRoutes;
  }

  console.log('开始根据前端路径生成动态路由', JSON.stringify(frontendPaths));
  
  try {
    // 找到管理员主路由配置
    const adminMainRoute = staticRoutes.find(route => route.path === '/admin');
    if (!adminMainRoute || !adminMainRoute.children) {
      console.error('无法找到管理员主路由配置');
      return staticRoutes;
    }
    
    // 创建主路由的深拷贝，避免修改原始配置
    const adminMainRouteCopy = JSON.parse(JSON.stringify(adminMainRoute));
    const adminMainRouteChildren = adminMainRouteCopy.children || [];
    
    // 跟踪新添加的路由，用于日志
    const newRoutes: string[] = [];
    
    // 遍历路径数据生成动态路由
    frontendPaths.forEach(moduleData => {
      if (!moduleData.module || !moduleData.paths || !Array.isArray(moduleData.paths)) {
        console.warn('无效的模块数据', moduleData);
        return;
      }
      
      const moduleName = moduleData.module;
      const modulePaths = moduleData.paths;
      
      if (modulePaths.length === 0) {
        console.warn(`模块 ${moduleName} 没有路径数据`);
        return;
      }
      
      console.log(`处理模块 ${moduleName} 的路径数据, 共 ${modulePaths.length} 条`);
      
      // 根据模块名称处理不同的路由结构
      switch(moduleName) {
        case 'admin':
          // admin模块的路径直接添加到管理员子路由
          modulePaths.forEach((pathItem: PathItem) => {
            if (!pathItem.path || !pathItem.title) {
              console.warn('路径项缺少必要属性', pathItem);
              return;
            }
            
            // 确保路径格式正确 - 不要以/开头，因为它会作为子路由添加
            const sanitizedPath = pathItem.path.startsWith('/') 
              ? pathItem.path.substring(1) 
              : pathItem.path;
            
            // 检查是否已存在相同路径的路由（避免重复）
            const existingRoute = adminMainRouteChildren.find((route: any) => 
              route.path === sanitizedPath
            );
            
            if (!existingRoute) {
              // 确保路径格式正确
              if (typeof sanitizedPath !== 'string') {
                console.warn('路径格式不正确，跳过', pathItem);
                return;
              }
              
              // 路由名称始终使用特定格式以确保唯一性
              const routeName = `Admin${sanitizedPath.charAt(0).toUpperCase() + sanitizedPath.slice(1)}`;
              
              // 创建新的路由配置，直接使用动态配置页面组件而不是懒加载
              const newRoute: RouteRecordRaw = {
                path: sanitizedPath,
                name: routeName,
                component: getDynamicConfigComponent,
                meta: { 
                  title: pathItem.title,
                  requiresAuth: true,
                  dynamic: true
                },
                // 传递所有path属性作为props
                props: { 
                  configId: sanitizedPath,
                  pathConfig: {
                    title: pathItem.title,
                    icon: pathItem.icon,
                    group: pathItem.group,
                    configKey: pathItem.config_key,
                    configType: pathItem.config_type,
                    id: pathItem.id,
                    versionHash: pathItem.version_hash,
                    count: pathItem.count || 0
                  }
                }
              };
              
              // 添加到管理员子路由
              adminMainRouteChildren.push(newRoute);
              
              // 记录完整路径，包括父路由路径
              const fullPath = `/admin/${sanitizedPath}`;
              newRoutes.push(fullPath);
              
              console.log(`已添加动态路由: ${fullPath}, 名称: ${routeName}`);
            } else {
              console.log(`路由已存在，跳过: /admin/${sanitizedPath}`);
            }
          });
          break;
          
        case 'ui_config':
          // UI配置模块添加到system目录下
          modulePaths.forEach((pathItem: PathItem) => {
            if (!pathItem.path || !pathItem.title) {
              console.warn('路径项缺少必要属性', pathItem);
              return;
            }
            
            // 确保路径格式正确 - 不要以/开头
            const sanitizedPath = pathItem.path.startsWith('/') 
              ? pathItem.path.substring(1) 
              : pathItem.path;
              
            // 构造完整的子路由路径
            const systemPath = `system/${sanitizedPath}`;
            
            // 检查是否已存在相同路径的路由
            const existingRoute = adminMainRouteChildren.find((route: any) => 
              route.path === systemPath
            );
            
            if (!existingRoute) {
              // 确保路径格式正确
              if (typeof sanitizedPath !== 'string') {
                console.warn('路径格式不正确，跳过', pathItem);
                return;
              }
              
              // 路由名称始终使用特定格式以确保唯一性
              const routeName = `AdminSystem${sanitizedPath.charAt(0).toUpperCase() + sanitizedPath.slice(1)}`;
              
              // 创建新的路由配置
              const newRoute: RouteRecordRaw = {
                path: systemPath,
                name: routeName,
                component: getDynamicConfigComponent,
                meta: { 
                  title: pathItem.title,
                  requiresAuth: true,
                  dynamic: true
                },
                // 传递所有path属性作为props
                props: { 
                  configId: sanitizedPath,
                  pathConfig: {
                    title: pathItem.title,
                    icon: pathItem.icon,
                    group: pathItem.group,
                    configKey: pathItem.config_key,
                    configType: pathItem.config_type,
                    id: pathItem.id,
                    versionHash: pathItem.version_hash,
                    count: pathItem.count || 0
                  }
                }
              };
              
              // 添加到管理员子路由
              adminMainRouteChildren.push(newRoute);
              
              // 记录完整路径
              const fullPath = `/admin/${systemPath}`;
              newRoutes.push(fullPath);
              
              console.log(`已添加动态路由: ${fullPath}, 名称: ${routeName}`);
            } else {
              console.log(`路由已存在，跳过: /admin/${systemPath}`);
            }
          });
          break;
          
        default:
          // 其他模块创建新的子路由目录
          modulePaths.forEach((pathItem: PathItem) => {
            if (!pathItem.path || !pathItem.title) {
              console.warn('路径项缺少必要属性', pathItem);
              return;
            }
            
            // 确保路径格式正确 - 不要以/开头
            const sanitizedPath = pathItem.path.startsWith('/') 
              ? pathItem.path.substring(1) 
              : pathItem.path;
            
            // 构造完整的子路由路径  
            const modulePath = `${moduleName}/${sanitizedPath}`;
            
            // 检查是否已存在相同路径的路由
            const existingRoute = adminMainRouteChildren.find((route: any) => 
              route.path === modulePath
            );
            
            if (!existingRoute) {
              // 路由名称始终使用特定格式以确保唯一性
              const routeName = `Admin${moduleName.charAt(0).toUpperCase() + moduleName.slice(1)}${sanitizedPath.charAt(0).toUpperCase() + sanitizedPath.slice(1)}`;
              
              // 创建新的路由配置
              const newRoute: RouteRecordRaw = {
                path: modulePath,
                name: routeName,
                component: getDynamicConfigComponent,
                meta: { 
                  title: pathItem.title,
                  requiresAuth: true,
                  dynamic: true
                },
                // 传递所有path属性作为props
                props: { 
                  configId: `${moduleName}_${sanitizedPath}`,
                  pathConfig: {
                    title: pathItem.title,
                    icon: pathItem.icon,
                    group: pathItem.group,
                    configKey: pathItem.config_key,
                    configType: pathItem.config_type,
                    id: pathItem.id,
                    versionHash: pathItem.version_hash,
                    count: pathItem.count || 0
                  }
                }
              };
              
              // 添加到管理员子路由
              adminMainRouteChildren.push(newRoute);
              
              // 记录完整路径
              const fullPath = `/admin/${modulePath}`;
              newRoutes.push(fullPath);
              
              console.log(`已添加动态路由: ${fullPath}, 名称: ${routeName}`);
            } else {
              console.log(`路由已存在，跳过: /admin/${modulePath}`);
            }
          });
          break;
      }
    });
    
    if (newRoutes.length > 0) {
      console.log(`动态路由生成完成，共添加 ${newRoutes.length} 个路由:`, newRoutes);
    } else {
      console.log('未添加任何新的动态路由');
    }
    
    // 创建新的静态路由深拷贝
    const newStaticRoutes = JSON.parse(JSON.stringify(staticRoutes));
    // 更新管理员主路由的子路由
    const adminMainRouteIndex = newStaticRoutes.findIndex((route: RouteRecordRaw) => route.path === '/admin');
    if (adminMainRouteIndex !== -1 && newStaticRoutes[adminMainRouteIndex]) {
      newStaticRoutes[adminMainRouteIndex].children = adminMainRouteChildren;
    }
    
    // 重要：确保所有路由都有对应的组件引用
    newStaticRoutes.forEach((route: RouteRecordRaw) => {
      // 处理登录路由
      if (route.path === '/admin/login' && !route.component) {
        route.component = () => import('../views/Login.vue');
      }
      
      // 处理管理员主路由
      if (route.path === '/admin') {
        if (!route.component) {
          route.component = Layout;
        }
        
        // 处理子路由
        if (route.children) {
          route.children.forEach((childRoute: RouteRecordRaw) => {
            if (!childRoute.component) {
              // 根据路径模式添加组件引用
              if (childRoute.meta?.dynamic || childRoute.path === 'page/:configId' ||
                  childRoute.path === 'system/settings' || childRoute.path === 'content/articles' ||
                  ['permissions', 'roles', 'users'].includes(childRoute.path)) {
                childRoute.component = getDynamicConfigComponent;
              } else {
                // 基于约定的路径命名模式导入组件
                const viewName = childRoute.path.split('/').pop();
                if (viewName) {
                  const capitalized = viewName.charAt(0).toUpperCase() + viewName.slice(1);
                  // 设置延迟加载的组件
                  childRoute.component = () => import(`../views/${capitalized}View.vue`);
                }
              }
            }
          });
        }
      }
    });
    
    // 输出最终路由信息以便调试
    const routeDebugInfo = newStaticRoutes
      .filter((route: RouteRecordRaw) => route.path === '/admin')[0]?.children
      ?.map((child: RouteRecordRaw) => ({
        path: child.path, 
        name: child.name,
        fullPath: `/admin/${child.path}`,
        component: child.component ? 'Available' : 'Missing',
        meta: child.meta
      }));
    
    console.log('生成的所有子路由:', routeDebugInfo);
    
    // 缓存结果以便后续使用
    cachedDynamicRoutes = newStaticRoutes;
    
    // 不再尝试保存路由配置到localforage，因为路由配置包含无法序列化的函数
    // 我们只依赖保存前端路径数据，而不是完整的路由配置
    
    // 返回完整的路由配置（包含静态路由和新添加的动态路由）
    return newStaticRoutes;
  } catch (error) {
    console.error('生成动态路由时发生错误:', error);
    return staticRoutes;
  }
}

/**
 * 清除动态路由缓存
 * 在路由更新或需要重新生成时调用
 */
export function clearDynamicRoutesCache() {
  cachedDynamicRoutes = null;
  console.log('动态路由缓存已清除');
}

// 默认导出静态路由配置
export default staticRoutes;