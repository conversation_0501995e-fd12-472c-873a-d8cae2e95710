/**
 * 文件：GridItemAdapter.ts
 * 职责：负责网格项数据在前端和后端之间的转换。
 * 主要功能：提供数据格式转换的方法，保证数据在不同层之间传递时格式一致。
 * 作者：张二浩
 * 创建时间：2025-04-17
 * 更新时间：2025-04-18
 */

import type { AdminGridInfoDTO } from '../types';

/**
 * 网格项适配器，负责在前端和后端数据结构之间进行转换
 */
export class GridItemAdapter {
  /**
   * 将后端数据转换为前端数据结构
   * @param backendData 后端原始数据
   * @returns 前端可用的数据结构
   */
  static toFrontend(backendData: any): AdminGridInfoDTO {
    return {
      id: backendData.id,
      uiConfigId: backendData.uiConfigId || backendData.ui_config_id || 0,
      name: backendData.name || '',
      content: this.parseContent(backendData.content),
      position: this.parsePosition(backendData.position),
      api: backendData.api || '',
      dto: backendData.dto || '',
      remark: backendData.remark || '',
      status: backendData.status !== undefined ? backendData.status : 1,
      created_at: backendData.created_at || '',
      updated_at: backendData.updated_at || ''
    };
  }

  /**
   * 批量转换后端数据为前端数据结构
   * @param backendDataList 后端原始数据列表
   * @returns 前端可用的数据结构列表
   */
  static batchToFrontend(backendDataList: any[]): AdminGridInfoDTO[] {
    if (!Array.isArray(backendDataList)) return [];
    return backendDataList.map(item => this.toFrontend(item));
  }

  /**
   * 将前端数据转换为后端数据结构
   * @param frontendData 前端数据
   * @returns 后端可用的数据结构
   */
  static toBackend(frontendData: AdminGridInfoDTO): any {
    return {
      id: frontendData.id,
      uiConfigId: frontendData.uiConfigId,
      name: frontendData.name,
      content: typeof frontendData.content === 'object' 
        ? JSON.stringify(frontendData.content) 
        : frontendData.content,
      position: typeof frontendData.position === 'object' 
        ? JSON.stringify(frontendData.position) 
        : frontendData.position,
      api: frontendData.api,
      dto: frontendData.dto,
      remark: frontendData.remark,
      status: frontendData.status
    };
  }

  /**
   * 批量转换前端数据为后端数据结构
   * @param frontendDataList 前端数据列表
   * @returns 后端可用的数据结构列表
   */
  static batchToBackend(frontendDataList: AdminGridInfoDTO[]): any[] {
    if (!Array.isArray(frontendDataList)) return [];
    return frontendDataList.map(item => this.toBackend(item));
  }

  /**
   * 解析位置数据
   * @param position 位置数据，可能是字符串或对象
   * @returns 位置对象
   */
  static parsePosition(position: any): any {
    if (typeof position === 'string') {
      try {
        return JSON.parse(position);
      } catch (e) {
        console.error('解析位置数据失败', e);
        return { x: 0, y: 0, w: 4, h: 4 };
      }
    }
    return position || { x: 0, y: 0, w: 4, h: 4 };
  }

  /**
   * 解析内容数据
   * @param content 内容数据，可能是字符串或对象
   * @returns 内容对象
   */
  static parseContent(content: any): any {
    if (typeof content === 'string') {
      try {
        return JSON.parse(content);
      } catch (e) {
        console.error('解析内容数据失败', e);
        return {};
      }
    }
    return content || {};
  }

  /**
   * 将前端网格项集合转换为GridStackNode数组
   * @param items 网格项数组
   * @returns GridStackNode数组
   */
  static toGridStackNodes(items: AdminGridInfoDTO[]): any[] {
    if (!Array.isArray(items)) return [];
    
    return items.map(item => ({
      id: `grid-item-${item.id}`,
      x: item.position?.x ?? 0,
      y: item.position?.y ?? 0,
      w: item.position?.w ?? 4,
      h: item.position?.h ?? 4,
      minW: item.position?.minW ?? 1,
      minH: item.position?.minH ?? 1,
      maxW: item.position?.maxW ?? 0,
      maxH: item.position?.maxH ?? 0,
      // locked: item.position?.locked ?? false,
      // noMove: item.position?.noMove ?? false,
      // noResize: item.position?.noResize ?? false,
      // autoPosition: item.position?.autoPosition ?? false,
      // lastUiPosition: item.position?.lastUiPosition,
      content: item.content || {}
    }));
  }

  /**
   * 从GridStackNode更新网格项集合
   * @param nodes GridStackNode数组
   * @param existingItems 现有网格项数组
   * @returns 更新后的网格项数组
   */
  static fromGridStackNodes(nodes: any[], existingItems: AdminGridInfoDTO[]): AdminGridInfoDTO[] {
    if (!Array.isArray(nodes) || !Array.isArray(existingItems)) return [];
    
    return nodes.map(node => {
      // 从id中提取gridItem id (格式如 "grid-item-1")
      const idMatch = node.id?.toString().match(/grid-item-(-?\d+)/);
      if (!idMatch || !idMatch[1]) return null;
      
      const itemId = parseInt(idMatch[1]);
      const existingItem = existingItems.find(item => Number(item.id) === itemId);
      
      if (!existingItem) return null;
      
      const updatedItem = { ...existingItem };
      updatedItem.position = {
        ...updatedItem.position,
        x: node.x ?? 0,
        y: node.y ?? 0,
        w: node.w ?? 4,
        h: node.h ?? 4,
        minW: node.minW ?? 1,
        minH: node.minH ?? 1,
        maxW: node.maxW ?? 0,
        maxH: node.maxH ?? 0,
        //locked: node.locked ?? false,
        //noMove: node.no_move ?? false,
        //noResize: node.no_resize ?? false,
        //autoPosition: node.auto_position ?? false
      };
      
      return updatedItem;
    }).filter((item): item is AdminGridInfoDTO => item !== null);
  }

  /**
   * 从配置项创建网格项
   * @param configItem 布局配置项
   * @returns 网格项数据
   */
  static fromConfigItem(configItem: any): AdminGridInfoDTO {
    if (!configItem) return {} as AdminGridInfoDTO;
    
    return {
      id: configItem.id || Date.now(),
      uiConfigId: 0, // 默认为0，表示新建
      name: configItem.component_type || '网格项',
      content: {
        type: configItem.component_type || '',
        title: configItem.title || configItem.component_type || '网格项',
        icon: configItem.icon || 'Grid',
        showTitle: configItem.show_title !== undefined ? configItem.show_title : true,
        refreshable: configItem.refreshable !== undefined ? configItem.refreshable : true,
        configurable: configItem.configurable !== undefined ? configItem.configurable : true,
        editable: configItem.editable !== undefined ? configItem.editable : true,
        closable: configItem.closable !== undefined ? configItem.closable : true,
        themeMode: configItem.theme_mode || 'light',
        config: {
          componentProps: configItem.component_props || {},
          componentEvents: configItem.component_events || {},
          componentSlots: configItem.component_slots || {}
        }
      },
      position: {
        x: configItem.x || 0,
        y: configItem.y || 0,
        w: configItem.w || 4,
        h: configItem.h || 4,
        i: configItem.id || Date.now().toString(),
        minW: 1,
        minH: 1,
        maxW: 12,
        maxH: 12,
        //locked: false,
        //no_move: false,
        //no_resize: false,
        //auto_position: false
      },
      api: configItem.service_config?.api || '',
      dto: configItem.service_config?.dto || '',
      remark: '',
      status: 1,
      created_at: '',
      updated_at: ''
    };
  }

  /**
   * 批量从配置项创建网格项
   * @param configItems 布局配置项数组
   * @returns 网格项数据数组
   */
  static batchFromConfigItems(configItems: any[]): AdminGridInfoDTO[] {
    if (!Array.isArray(configItems)) return [];
    return configItems.map(item => this.fromConfigItem(item));
  }
}

export default GridItemAdapter;
