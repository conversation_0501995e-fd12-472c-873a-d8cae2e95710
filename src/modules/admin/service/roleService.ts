/**
 * 角色服务
 * 处理角色数据的转换和业务逻辑
 */
import { 
  getRoleList,
  getRole,
  createRole,
  updateRole,
  deleteRole,
  assignPermissions
} from '../api/role';
import type { Role, RoleListParams } from '../types';
import type { ApiResponse } from '@/types';

/**
 * 角色状态转换映射
 * 前端枚举 <-> 后端数字编码
 */
const ROLE_STATUS_MAP = {
  // 前端 -> 后端
  toBackend: {
    ENABLED: 1,
    DISABLED: 0
  },
  // 后端 -> 前端
  toFrontend: {
    '1': 'ENABLED',
    '0': 'DISABLED'
  }
};

/**
 * 角色服务
 */
export const RoleService = {
  /**
   * 获取角色列表
   * @param params 查询参数
   * @returns 角色列表和分页信息
   */
  async getList(params: RoleListParams): Promise<ApiResponse<{list: Role[], total: number}>> {
    try {
      // 转换状态参数从前端枚举到后端编码
      const backendParams = { ...params } as any;
      if (params.status) {
        backendParams.status = ROLE_STATUS_MAP.toBackend[params.status as keyof typeof ROLE_STATUS_MAP.toBackend];
      }
      
      const res: any = await getRoleList(backendParams);
      
      // 转换返回数据中的状态码为前端枚举
      if (res.list) {
        const roles = res.list.map((role: any) => ({
          ...role,
          status: ROLE_STATUS_MAP.toFrontend[role.status as keyof typeof ROLE_STATUS_MAP.toFrontend] || role.status
        }));
        
        return {
          ...res,
          data: {
            ...res.data,
            list: roles
          }
        };
      }
      
      return res;
    } catch (error) {
      console.error('获取角色列表失败', error);
      throw error;
    }
  },
  
  /**
   * 获取角色详情
   * @param id 角色ID
   * @returns 角色详情
   */
  async getDetail(id: number): Promise<ApiResponse<Role>> {
    try {
      const res: any = await getRole(id);
      
      if (res.data) {
        return {
          ...res,
          data: {
            ...res.data,
            status: ROLE_STATUS_MAP.toFrontend[res.data.status as keyof typeof ROLE_STATUS_MAP.toFrontend] || res.data.status
          }
        };
      }
      
      return res;
    } catch (error) {
      console.error('获取角色详情失败', error);
      throw error;
    }
  },
  
  /**
   * 创建角色
   * @param role 角色信息
   * @returns 创建的角色信息
   */
  async create(role: Partial<Role>): Promise<ApiResponse<Role>> {
    try {
      const backendData = { ...role } as any;
      if (role.status) {
        backendData.status = ROLE_STATUS_MAP.toBackend[role.status];
      }
      
      const res: any = await createRole(backendData);
      
      if (res.data) {
        return {
          ...res,
          data: {
            ...res.data,
            status: ROLE_STATUS_MAP.toFrontend[res.data.status as keyof typeof ROLE_STATUS_MAP.toFrontend] || res.data.status
          }
        };
      }
      
      return res;
    } catch (error) {
      console.error('创建角色失败', error);
      throw error;
    }
  },
  
  /**
   * 更新角色信息
   * @param id 角色ID
   * @param role 更新的角色信息
   * @returns 更新后的角色信息
   */
  async update(id: number, role: Partial<Role>): Promise<ApiResponse<Role>> {
    try {
      const backendData = { ...role } as any;
      if (role.status) {
        backendData.status = ROLE_STATUS_MAP.toBackend[role.status];
      }
      
      const res: any = await updateRole(id, backendData);
      
      if (res.data) {
        return {
          ...res,
          data: {
            ...res.data,
            status: ROLE_STATUS_MAP.toFrontend[res.data.status as keyof typeof ROLE_STATUS_MAP.toFrontend] || res.data.status
          }
        };
      }
      
      return res;
    } catch (error) {
      console.error('更新角色失败', error);
      throw error;
    }
  },
  
  /**
   * 删除角色
   * @param id 角色ID
   * @returns 操作结果
   */
  async delete(id: number): Promise<ApiResponse<any>> {
    try {
      const res = await deleteRole(id);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('删除角色失败', error);
      throw error;
    }
  },
  
  /**
   * 为角色分配权限
   * @param roleId 角色ID
   * @param permissionIds 权限ID列表
   * @returns 操作结果
   */
  async assignPermissions(roleId: number, permissionIds: number[]): Promise<ApiResponse<any>> {
    try {
      const res = await assignPermissions(roleId, { permissionIds });
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('分配权限失败', error);
      throw error;
    }
  }
};

export default RoleService;
