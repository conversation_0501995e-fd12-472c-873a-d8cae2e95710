/**
 * 产品服务
 * 处理产品数据的转换和业务逻辑
 */
import { 
  getProductList,
  createProduct,
  updateProduct,
  deleteProduct,
  updateProductStatus
} from '../api/product';
import type { ProductStatus } from '../types';
import type { ApiResponse } from '@/types';

/**
 * 产品状态转换映射
 * 前端枚举 <-> 后端数字编码
 */
const PRODUCT_STATUS_MAP = {
  // 前端 -> 后端
  toBackend: {
    DRAFT: 0,
    PENDING: 1,
    ONLINE: 2,
    OFFLINE: 3,
    REJECTED: 4
  },
  // 后端 -> 前端
  toFrontend: {
    '0': 'DRAFT' as ProductStatus,
    '1': 'PENDING' as ProductStatus,
    '2': 'ONLINE' as ProductStatus,
    '3': 'OFFLINE' as ProductStatus,
    '4': 'REJECTED' as ProductStatus
  }
};

/**
 * 产品服务
 */
export const ProductService = {
  /**
   * 获取产品列表
   * @param params 查询参数
   * @returns 产品列表和分页信息
   */
  async getList(params: {
    page: number;
    pageSize: number;
    status?: ProductStatus;
    keyword?: string;
    categoryId?: number;
    merchantId?: number;
  }): Promise<ApiResponse<{records: any[], total: number}>> {
    try {
      // 转换状态参数从前端枚举到后端编码
      const backendParams = { ...params } as any;
      if (params.status) {
        backendParams.status = PRODUCT_STATUS_MAP.toBackend[params.status as unknown as keyof typeof PRODUCT_STATUS_MAP.toBackend];
      }
      
      const res: any = await getProductList(backendParams);
      
      // 转换返回数据中的状态码为前端枚举
      if (res.data?.records) {
        const products = res.data.records.map((product: any) => ({
          ...product,
          status: PRODUCT_STATUS_MAP.toFrontend[product.status as unknown as keyof typeof PRODUCT_STATUS_MAP.toFrontend] || product.status
        }));
        
        return {
          ...res,
          data: {
            ...res.data,
            records: products
          }
        };
      }
      
      return res;
    } catch (error) {
      console.error('获取产品列表失败', error);
      throw error;
    }
  },
  
  /**
   * 创建产品
   * @param product 产品信息
   * @returns 创建的产品信息
   */
  async create(product: any): Promise<ApiResponse<any>> {
    try {
      const backendData = { ...product } as any;
      if (product.status) {
        backendData.status = PRODUCT_STATUS_MAP.toBackend[product.status as unknown as keyof typeof PRODUCT_STATUS_MAP.toBackend];
      }
      
      const res = await createProduct(backendData);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('创建产品失败', error);
      throw error;
    }
  },
  
  /**
   * 更新产品信息
   * @param id 产品ID
   * @param product 更新的产品信息
   * @returns 更新后的产品信息
   */
  async update(id: number, product: any): Promise<ApiResponse<any>> {
    try {
      const backendData = { ...product } as any;
      if (product.status) {
        backendData.status = PRODUCT_STATUS_MAP.toBackend[product.status as unknown as keyof typeof PRODUCT_STATUS_MAP.toBackend];
      }
      
      const res = await updateProduct(id, backendData);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('更新产品失败', error);
      throw error;
    }
  },
  
  /**
   * 更新产品状态
   * @param id 产品ID
   * @param status 新状态
   * @param reason 拒绝原因（当状态为REJECTED时必填）
   * @returns 操作结果
   */
  async updateStatus(id: number, status: ProductStatus, reason?: string): Promise<ApiResponse<any>> {
    try {
      const backendStatus = PRODUCT_STATUS_MAP.toBackend[status as unknown as keyof typeof PRODUCT_STATUS_MAP.toBackend];
      const data: any = { status: String(backendStatus) };
      
      if (status === 'REJECTED' && reason) {
        data.reason = reason;
      }
      
      const res = await updateProductStatus(id, data);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('更新产品状态失败', error);
      throw error;
    }
  },
  
  /**
   * 删除产品
   * @param id 产品ID
   * @returns 操作结果
   */
  async delete(id: number): Promise<ApiResponse<any>> {
    try {
      const res = await deleteProduct(id);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('删除产品失败', error);
      throw error;
    }
  },
  
  /**
   * 获取产品状态标签类型
   * @param status 产品状态
   * @returns 对应的Element Plus标签类型
   */
  getStatusTagType(status: ProductStatus): string {
    switch (status) {
      case 'DRAFT':
        return 'info';
      case 'PENDING':
        return 'warning';
      case 'ONLINE':
        return 'success';
      case 'OFFLINE':
        return '';
      case 'REJECTED':
        return 'danger';
      default:
        return 'info';
    }
  },
  
  /**
   * 获取产品状态显示文本
   * @param status 产品状态
   * @returns 对应的中文文本
   */
  getStatusText(status: ProductStatus): string {
    switch (status) {
      case 'DRAFT':
        return '草稿';
      case 'PENDING':
        return '待审核';
      case 'ONLINE':
        return '上架中';
      case 'OFFLINE':
        return '已下架';
      case 'REJECTED':
        return '已拒绝';
      default:
        return '未知';
    }
  }
};

export default ProductService;
