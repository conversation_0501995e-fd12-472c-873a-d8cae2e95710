/**
 * 系统服务
 * 处理系统数据的转换和业务逻辑
 */
import { 
  getSystemConfig,
  updateSystemConfig,
  //getDashboardData,
  getSystemLogs
} from '../api/system';
import type { ApiResponse } from '@/types';

/**
 * 系统服务
 */
export const SystemService = {
  /**
   * 获取系统配置
   * @returns 系统配置信息
   */
  async getConfig(): Promise<ApiResponse<any>> {
    try {
      const res = await getSystemConfig();
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('获取系统配置失败', error);
      throw error;
    }
  },
  
  /**
   * 更新系统配置
   * @param config 系统配置
   * @returns 操作结果
   */
  async updateConfig(config: any): Promise<ApiResponse<any>> {
    try {
      const res = await updateSystemConfig(config);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('更新系统配置失败', error);
      throw error;
    }
  },
  
  /**
   * 获取仪表盘数据
   * @returns 仪表盘数据
   */
  // async getDashboardData(): Promise<ApiResponse<any>> {
  //   try {
  //     const res = await getDashboardData();
  //     return res;
  //   } catch (error) {
  //     console.error('获取仪表盘数据失败', error);
  //     throw error;
  //   }
  // },
  
  /**
   * 获取系统日志
   * @param params 查询参数
   * @returns 日志列表和分页信息
   */
  async getLogs(params: {
    page: number;
    pageSize: number;
    operationType?: string;
    operator?: string;
    startTime?: string;
    endTime?: string;
  }): Promise<ApiResponse<{records: any[], total: number}>> {
    try {
      const res: any = await getSystemLogs(params);
      return res;
    } catch (error) {
      console.error('获取系统日志失败', error);
      throw error;
    }
  },
  
  /**
   * 格式化操作类型文本
   * @param type 操作类型
   * @returns 格式化后的中文文本
   */
  getOperationTypeText(type: string): string {
    const typeMap: Record<string, string> = {
      'LOGIN': '登录',
      'LOGOUT': '登出',
      'CREATE': '创建',
      'UPDATE': '更新',
      'DELETE': '删除',
      'QUERY': '查询',
      'IMPORT': '导入',
      'EXPORT': '导出',
      'APPROVE': '审批',
      'REJECT': '拒绝',
      'CONFIG': '配置'
    };
    
    return typeMap[type] || type;
  },
  
  /**
   * 获取操作类型标签类型
   * @param type 操作类型
   * @returns 对应的Element Plus标签类型
   */
  getOperationTypeTagType(type: string): string {
    const typeTagMap: Record<string, string> = {
      'LOGIN': 'success',
      'LOGOUT': 'info',
      'CREATE': 'success',
      'UPDATE': 'warning',
      'DELETE': 'danger',
      'QUERY': 'info',
      'IMPORT': 'warning',
      'EXPORT': 'warning',
      'APPROVE': 'success',
      'REJECT': 'danger',
      'CONFIG': 'warning'
    };
    
    return typeTagMap[type] || 'info';
  }
};

export default SystemService;
