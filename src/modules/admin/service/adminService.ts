/**
 * 管理员服务
 * 处理管理员数据的转换和业务逻辑
 */
import { 
  getAdminList,
  updateAdmin,
  createAdmin,
  deleteAdmin,
  //updateAdminStatus,
  resetAdminPassword
} from '../api/index';
import type { 
  Admin, 
  AdminInfo, 
  AdminStatus, 
  AdminRole,
} from '../types';
import type { ApiResponse } from '@/types';

/**
 * 管理员状态转换映射
 * 前端枚举 <-> 后端数字编码
 */
const ADMIN_STATUS_MAP = {
  // 前端 -> 后端
  toBackend: {
    ACTIVE: 1,
    LOCKED: 2,
    DISABLED: 0
  } as unknown as Record<AdminStatus, number>,
  // 后端 -> 前端
  toFrontend: {
   1: 1 as AdminStatus,
   2: 2 as AdminStatus,
   0: 0 as AdminStatus
  } as unknown as Record<number, AdminStatus>
};

/**
 * 管理员服务
 */
export const AdminService = {
  /**
   * 获取管理员列表
   * @param params 查询参数
   * @returns 管理员列表和分页信息
   */
  async getList(params: {
    page: number;
    pageSize: number;
    status?: AdminStatus;
    username?: string;
    role?: AdminRole;
  }): Promise<ApiResponse<{records: Admin[], total: number}>> {
    try {
      // 转换状态参数从前端枚举到后端编码
      const backendParams = { ...params } as any;
      if (params.status) {
        backendParams.status = ADMIN_STATUS_MAP.toBackend[params.status];
      }
      
      const res:any = await getAdminList(backendParams);
      console.log('获取管理员列表成功', res);
      // 转换返回数据中的状态码为前端枚举
      if (res.list) {
        const admins = res.list.map((admin: any) => ({
          ...admin,
          status: ADMIN_STATUS_MAP.toFrontend[admin.status] || admin.status
        }));
        
        return {
          ...res,
          data: {
            ...res.list,
            records: admins
          }
        };
      }
      
      return res as ApiResponse<{records: Admin[], total: number}>;
    } catch (error) {
      console.error('获取管理员列表失败', error);
      throw error;
    }
  },
  
  /**
   * 获取管理员详情
   * @param id 管理员ID
   * @returns 管理员详情
   */
  async getDetail(id: number): Promise<ApiResponse<AdminInfo>> {
    try {
      // 由于没有直接获取管理员详情的API，我们从列表中筛选
      const res:any = await getAdminList({ page: 1, pageSize: 10, id: id } as any);
      
      if (res.data?.records && res.data.records.length > 0) {
        const admin = res.data.records[0];
        return {
          ...res,
          data: {
            ...admin,
            status: ADMIN_STATUS_MAP.toFrontend[admin.status] || admin.status
          } as AdminInfo
        } as ApiResponse<AdminInfo>;
      }
      
      throw new Error('管理员不存在');
    } catch (error) {
      console.error('获取管理员详情失败', error);
      throw error;
    }
  },
  
  /**
   * 创建管理员
   * @param admin 管理员信息
   * @returns 创建的管理员信息
   */
  async create(admin: {
    username: string;
    nickname: string;
    name: string;
    password: string;
    role: AdminRole;
    status: AdminStatus;
  }): Promise<ApiResponse<AdminInfo>> {
    try {
      const backendData = {
        ...admin,
        status: ADMIN_STATUS_MAP.toBackend[admin.status]
      };
      
      const res:any = await createAdmin(backendData as any);
      
      if (res.data) {
        return {
          ...res,
          data: {
            ...res.data,
            status: ADMIN_STATUS_MAP.toFrontend[res.data.status] || res.data.status
          }
        } as ApiResponse<AdminInfo>;
      }
      
      return res as ApiResponse<AdminInfo>;
    } catch (error) {
      console.error('创建管理员失败', error);
      throw error;
    }
  },
  
  /**
   * 更新管理员信息
   * @param id 管理员ID
   * @param admin 更新的管理员信息
   * @returns 更新后的管理员信息
   */
  async update(id: number, admin: Partial<AdminInfo>): Promise<ApiResponse<AdminInfo>> {
    try {
      const backendData = { ...admin } as any;
      if (admin.status) {
        backendData.status = ADMIN_STATUS_MAP.toBackend[admin.status];
      }
      
      const res:any = await updateAdmin(id, backendData);
      
      if (res.data) {
        return {
          ...res,
          data: {
            ...res.data,
            status: ADMIN_STATUS_MAP.toFrontend[res.data.status] || res.data.status
          }
        } as ApiResponse<AdminInfo>;
      }
      
      return res as ApiResponse<AdminInfo>;
    } catch (error) {
      console.error('更新管理员失败', error);
      throw error;
    }
  },
  
  /**
   * 删除管理员
   * @param id 管理员ID
   * @returns 操作结果
   */
  async delete(id: number): Promise<ApiResponse<any>> {
    try {
      const res = await deleteAdmin(id);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('删除管理员失败', error);
      throw error;
    }
  },
  
  /**
   * 重置管理员密码
   * @param id 管理员ID
   * @returns 操作结果
   */
  async resetPassword(id: number): Promise<ApiResponse<any>> {
    try {
      const res = await resetAdminPassword(id);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('重置密码失败', error);
      throw error;
    }
  },
  
  /**
   * 获取管理员状态标签类型
   * @param status 管理员状态
   * @returns 对应的Element Plus标签类型
   */
  getStatusTagType(status: AdminStatus): string {
    switch (status) {
      case 1:
        return 'success';
      case 0:
        return 'danger';
      default:
        return 'info';
    }
  },
  
  /**
   * 获取管理员状态显示文本
   * @param status 管理员状态
   * @returns 对应的中文文本
   */
  getStatusText(status: AdminStatus): string {
    switch (status) {
      case 1:
        return '正常';
      case 0:
        return '禁用';
      default:
        return '未知';
    }
  }
};

export default AdminService;
