/**
 * 订单服务
 * 处理订单数据的转换和业务逻辑
 */
import { 
  getOrderList,
  getOrderDetail,
  updateOrderStatus
} from '../api/order';
import { OrderStatus } from '../types';
import type { ApiResponse } from '@/types';

/**
 * 订单状态转换映射
 * 前端枚举 <-> 后端数字编码
 */
const ORDER_STATUS_MAP = {
  // 前端 -> 后端
  toBackend: {
    [OrderStatus.PENDING]: 10,
    [OrderStatus.PAID]: 20,
    [OrderStatus.PROCESSING]: 30,
    [OrderStatus.DELIVERING]: 40,
    [OrderStatus.COMPLETED]: 50,
    [OrderStatus.CANCELLED]: 60,
    [OrderStatus.REFUNDING]: 70,
    [OrderStatus.REFUNDED]: 80
  },
  // 后端 -> 前端
  toFrontend: {
    '10': OrderStatus.PENDING,
    '20': OrderStatus.PAID,
    '30': OrderStatus.PROCESSING,
    '40': OrderStatus.DELIVERING,
    '50': OrderStatus.COMPLETED,
    '60': OrderStatus.CANCELLED,
    '70': OrderStatus.REFUNDING,
    '80': OrderStatus.REFUNDED
  }
};

/**
 * 订单服务
 */
export const OrderService = {
  /**
   * 获取订单列表
   * @param params 查询参数
   * @returns 订单列表和分页信息
   */
  async getList(params: {
    page: number;
    pageSize: number;
    status?: OrderStatus;
    orderNo?: string;
    userId?: number;
    merchantId?: number;
    startTime?: string;
    endTime?: string;
  }): Promise<ApiResponse<{records: any[], total: number}>> {
    try {
      // 转换状态参数从前端枚举到后端编码
      const backendParams = { ...params } as any;
      if (params.status) {
        backendParams.status = ORDER_STATUS_MAP.toBackend[params.status];
      }
      
      const res: any = await getOrderList(backendParams);
      
      // 转换返回数据中的状态码为前端枚举
      if (res.data?.records) {
        const orders = res.data.records.map((order: any) => ({
          ...order,
          status: ORDER_STATUS_MAP.toFrontend[order.status as keyof typeof ORDER_STATUS_MAP.toFrontend] || order.status
        }));
        
        return {
          ...res,
          data: {
            ...res.data,
            records: orders
          }
        };
      }
      
      return res;
    } catch (error) {
      console.error('获取订单列表失败', error);
      throw error;
    }
  },
  
  /**
   * 获取订单详情
   * @param id 订单ID
   * @returns 订单详情
   */
  async getDetail(id: number): Promise<ApiResponse<any>> {
    try {
      const res: any = await getOrderDetail(id);
      
      if (res.data) {
        return {
          ...res,
          data: {
            ...res.data,
            status: ORDER_STATUS_MAP.toFrontend[res.data.status as keyof typeof ORDER_STATUS_MAP.toFrontend] || res.data.status
          }
        };
      }
      
      return res;
    } catch (error) {
      console.error('获取订单详情失败', error);
      throw error;
    }
  },
  
  /**
   * 更新订单状态
   * @param id 订单ID
   * @param status 新状态
   * @param remark 备注信息
   * @returns 操作结果
   */
  async updateStatus(id: number, status: OrderStatus, remark?: string): Promise<ApiResponse<any>> {
    try {
      const backendStatus = ORDER_STATUS_MAP.toBackend[status];
      const res = await updateOrderStatus(id, { 
        status: String(backendStatus),
        remark: remark
      });
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('更新订单状态失败', error);
      throw error;
    }
  },
  
  /**
   * 获取订单状态标签类型
   * @param status 订单状态
   * @returns 对应的Element Plus标签类型
   */
  getStatusTagType(status: OrderStatus): string {
    switch (status) {
      case OrderStatus.PENDING:
        return 'info';
      case OrderStatus.PAID:
        return 'success';
      case OrderStatus.PROCESSING:
        return 'primary';
      case OrderStatus.DELIVERING:
        return 'warning';
      case OrderStatus.COMPLETED:
        return 'success';
      case OrderStatus.CANCELLED:
        return 'danger';
      case OrderStatus.REFUNDING:
        return 'warning';
      case OrderStatus.REFUNDED:
        return 'info';
      default:
        return 'info';
    }
  },
  
  /**
   * 获取订单状态显示文本
   * @param status 订单状态
   * @returns 对应的中文文本
   */
  getStatusText(status: OrderStatus): string {
    switch (status) {
      case OrderStatus.PENDING:
        return '待付款';
      case OrderStatus.PAID:
        return '已付款';
      case OrderStatus.PROCESSING:
        return '处理中';
      case OrderStatus.DELIVERING:
        return '配送中';
      case OrderStatus.COMPLETED:
        return '已完成';
      case OrderStatus.CANCELLED:
        return '已取消';
      case OrderStatus.REFUNDING:
        return '退款中';
      case OrderStatus.REFUNDED:
        return '已退款';
      default:
        return '未知';
    }
  }
};

export default OrderService;
