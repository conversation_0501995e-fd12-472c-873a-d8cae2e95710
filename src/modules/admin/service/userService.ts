/**
 * 用户服务
 * 处理用户数据的转换和业务逻辑
 */
import { 
  getUserList,
  updateUserStatus,
  createUser
} from '../api/user';
import type { User, UserStatus } from '../types';
import type { ApiResponse } from '@/types';
//import { ElMessage } from 'element-plus';

/**
 * 用户状态转换映射
 * 前端枚举 <-> 后端数字编码
 */
const USER_STATUS_MAP = {
  // 前端 -> 后端
  toBackend: {
    ACTIVE: 1,
    LOCKED: 2,
    DISABLED: 0
  },
  // 后端 -> 前端
  toFrontend: {
    '1': 'ACTIVE' as UserStatus,
    '2': 'LOCKED' as UserStatus,
    '0': 'DISABLED' as UserStatus
  }
};

/**
 * 用户服务
 */
export const UserService = {
  /**
   * 获取用户列表
   * @param params 查询参数
   * @returns 用户列表和分页信息
   */
  async getList(params: {
    page: number;
    pageSize: number;
    status?: UserStatus;
    keyword?: string;
  }): Promise<ApiResponse<{records: User[], total: number}>> {
    try {
      // 转换状态参数从前端枚举到后端编码
      const backendParams = { ...params } as any;
      if (params.status) {
        backendParams.status = USER_STATUS_MAP.toBackend[params.status];
      }
      
      const res: any = await getUserList(backendParams);
      
      // 转换返回数据中的状态码为前端枚举
      if (res.list) {
        const users = res.list.map((user: any) => ({
          ...user,
          status: USER_STATUS_MAP.toFrontend[user.status as keyof typeof USER_STATUS_MAP.toFrontend] || user.status
        }));
        
        return {
          ...res,
          data: {
            ...res.data,
            records: users
          }
        };
      }
      
      return res;
    } catch (error) {
      console.error('获取用户列表失败', error);
      throw error;
    }
  },
  
  /**
   * 更新用户状态
   * @param id 用户ID
   * @param status 新状态
   * @returns 操作结果
   */
  async updateStatus(id: number, status: UserStatus): Promise<ApiResponse<any>> {
    try {
      const backendStatus = USER_STATUS_MAP.toBackend[status];
      const res: any = await updateUserStatus(id, { status: backendStatus });
      return res;
    } catch (error) {
      console.error('更新用户状态失败', error);
      throw error;
    }
  },

  async createUser(user: { username: string; password: string; nickname: string; mobile: string; email: string; gender: number; status: number; }): Promise<ApiResponse<any>> {
    try {
      const res: any = await createUser(user);
      return res;
    } catch (error) {
      console.error('创建用户失败', error);
      throw error;
    }
  },
  
  /**
   * 获取用户状态标签类型
   * @param status 用户状态
   * @returns 对应的Element Plus标签类型
   */
  getStatusTagType(status: UserStatus): string {
    switch (status) {
      case 'ACTIVE':
        return 'success';
      case 'LOCKED':
        return 'warning';
      case 'DISABLED':
        return 'danger';
      default:
        return 'info';
    }
  },
  
  /**
   * 获取用户状态显示文本
   * @param status 用户状态
   * @returns 对应的中文文本
   */
  getStatusText(status: UserStatus): string {
    switch (status) {
      case 'ACTIVE':
        return '正常';
      case 'LOCKED':
        return '锁定';
      case 'DISABLED':
        return '禁用';
      default:
        return '未知';
    }
  }
};

export default UserService;
