/**
 * 权限服务
 * 处理权限数据的转换和业务逻辑
 */
import { 
  getPermissionList,
  getPermission,
  createPermission,
  updatePermission,
  deletePermission
} from '../api/permission';
import type { Permission, PermissionListParams } from '../types';
import type { ApiResponse } from '@/types';

/**
 * 权限状态转换映射
 * 前端枚举 <-> 后端数字编码
 */
const PERMISSION_STATUS_MAP = {
  // 前端 -> 后端
  toBackend: {
    ENABLED: 1,
    DISABLED: 0
  },
  // 后端 -> 前端
  toFrontend: {
    '1': 'ENABLED',
    '0': 'DISABLED'
  }
};

/**
 * 权限类型转换映射
 * 前端 <-> 后端
 */
const PERMISSION_TYPE_MAP = {
  // 前端 -> 后端
  toBackend: {
    'MENU': 1,
    'OPERATION': 2,
    'API': 3
  },
  // 后端 -> 前端
  toFrontend: {
    '1': 'MENU',
    '2': 'OPERATION',
    '3': 'API'
  }
};

/**
 * 权限服务
 */
export const PermissionService = {
  /**
   * 获取权限列表
   * @param params 查询参数
   * @returns 权限列表和分页信息
   */
  async getList(params: PermissionListParams): Promise<ApiResponse<{list: Permission[], total: number}>> {
    try {
      // 转换状态参数从前端字符串到后端编码
      const backendParams = { ...params } as any;
      if (params.status) {
        backendParams.status = PERMISSION_STATUS_MAP.toBackend[params.status as keyof typeof PERMISSION_STATUS_MAP.toBackend];
      }
      if (params.type) {
        backendParams.type = PERMISSION_TYPE_MAP.toBackend[params.type as keyof typeof PERMISSION_TYPE_MAP.toBackend];
      }
      
      const res: any = await getPermissionList(backendParams);
      
      // 转换返回数据中的状态码和类型为前端字符串
      if (res.list) {
        const permissions = res.list.map((permission: any) => ({
          ...permission,
          status: PERMISSION_STATUS_MAP.toFrontend[permission.status as keyof typeof PERMISSION_STATUS_MAP.toFrontend] || permission.status,
          type: PERMISSION_TYPE_MAP.toFrontend[permission.type as keyof typeof PERMISSION_TYPE_MAP.toFrontend] || permission.type
        }));
        
        return {
          ...res,
          data: {
            ...res.data,
            list: permissions
          }
        };
      }
      
      return res;
    } catch (error) {
      console.error('获取权限列表失败', error);
      throw error;
    }
  },
  
  /**
   * 获取权限详情
   * @param id 权限ID
   * @returns 权限详情
   */
  async getDetail(id: number): Promise<ApiResponse<Permission>> {
    try {
      const res: any = await getPermission(id);
      
      if (res.data) {
        return {
          ...res,
          data: {
            ...res.data,
            status: PERMISSION_STATUS_MAP.toFrontend[res.data.status as keyof typeof PERMISSION_STATUS_MAP.toFrontend] || res.data.status,
            type: PERMISSION_TYPE_MAP.toFrontend[res.data.type as keyof typeof PERMISSION_TYPE_MAP.toFrontend] || res.data.type
          }
        };
      }
      
      return res;
    } catch (error) {
      console.error('获取权限详情失败', error);
      throw error;
    }
  },
  
  /**
   * 创建权限
   * @param permission 权限信息
   * @returns 创建的权限信息
   */
  async create(permission: Partial<Permission>): Promise<ApiResponse<Permission>> {
    try {
      const backendData = { ...permission } as any;
      if (permission.status) {
        backendData.status = PERMISSION_STATUS_MAP.toBackend[permission.status];
      }
      if (permission.type) {
        backendData.type = PERMISSION_TYPE_MAP.toBackend[permission.type as keyof typeof PERMISSION_TYPE_MAP.toBackend];
      }
      
      const res: any = await createPermission(backendData);
      
      if (res.data) {
        return {
          ...res,
          data: {
            ...res.data,
            status: PERMISSION_STATUS_MAP.toFrontend[res.data.status as keyof typeof PERMISSION_STATUS_MAP.toFrontend] || res.data.status,
            type: PERMISSION_TYPE_MAP.toFrontend[res.data.type as keyof typeof PERMISSION_TYPE_MAP.toFrontend] || res.data.type
          }
        };
      }
      
      return res;
    } catch (error) {
      console.error('创建权限失败', error);
      throw error;
    }
  },
  
  /**
   * 更新权限信息
   * @param id 权限ID
   * @param permission 更新的权限信息
   * @returns 更新后的权限信息
   */
  async update(id: number, permission: Partial<Permission>): Promise<ApiResponse<Permission>> {
    try {
      const backendData = { ...permission } as any;
      if (permission.status) {
        backendData.status = PERMISSION_STATUS_MAP.toBackend[permission.status];
      }
      if (permission.type) {
        backendData.type = PERMISSION_TYPE_MAP.toBackend[permission.type as keyof typeof PERMISSION_TYPE_MAP.toBackend];
      }
      
      const res: any = await updatePermission(id, backendData);
      
      if (res.data) {
        return {
          ...res,
          data: {
            ...res.data,
            status: PERMISSION_STATUS_MAP.toFrontend[res.data.status as keyof typeof PERMISSION_STATUS_MAP.toFrontend] || res.data.status,
            type: PERMISSION_TYPE_MAP.toFrontend[res.data.type as keyof typeof PERMISSION_TYPE_MAP.toFrontend] || res.data.type
          }
        };
      }
      
      return res;
    } catch (error) {
      console.error('更新权限失败', error);
      throw error;
    }
  },
  
  /**
   * 删除权限
   * @param id 权限ID
   * @returns 操作结果
   */
  async delete(id: number): Promise<ApiResponse<any>> {
    try {
      const res = await deletePermission(id);
      return res as ApiResponse<any>;
    } catch (error) {
      console.error('删除权限失败', error);
      throw error;
    }
  }
};

export default PermissionService;
