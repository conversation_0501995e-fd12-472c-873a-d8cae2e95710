/**
 * 文件：CacheService.ts
 * 职责：提供数据缓存功能，优化频繁访问的数据性能。
 * 主要功能：设置缓存、获取缓存、清除缓存等，支持过期时间设置。
 * 作者：张二浩
 * 创建时间：2025-04-17
 */

import localforage from 'localforage';

/**
 * 缓存项类型定义
 */
interface CacheItem<T> {
  value: T;
  expiry: number;
}

/**
 * 缓存服务类，用于管理应用数据的本地缓存
 */
export class CacheService {
  private storage: LocalForage;
  
  /**
   * 构造函数
   * @param namespace 缓存命名空间，用于区分不同模块的缓存
   */
  constructor(namespace = 'gridManagement') {
    this.storage = localforage.createInstance({
      name: namespace
    });
  }
  
  /**
   * 设置缓存
   * @param key 缓存键
   * @param value 缓存值
   * @param ttl 过期时间，单位毫秒，默认1小时
   */
  async set<T>(key: string, value: T, ttl = 3600000): Promise<void> {
    const cacheItem: CacheItem<T> = {
      value,
      expiry: Date.now() + ttl
    };
    
    await this.storage.setItem(key, cacheItem);
  }
  
  /**
   * 获取缓存
   * @param key 缓存键
   * @returns 缓存值，如果不存在或已过期则返回null
   */
  async get<T>(key: string): Promise<T | null> {
    const cached = await this.storage.getItem<CacheItem<T>>(key);
    
    if (!cached) return null;
    
    if (cached.expiry < Date.now()) {
      // 缓存已过期，移除并返回null
      await this.storage.removeItem(key);
      return null;
    }
    
    return cached.value;
  }
  
  /**
   * 清除缓存
   * @param prefix 缓存键前缀，如果提供则只清除指定前缀的缓存
   */
  async clear(prefix = ''): Promise<void> {
    if (!prefix) {
      // 清除所有缓存
      await this.storage.clear();
      return;
    }
    
    // 清除指定前缀的缓存
    const keys = await this.storage.keys();
    for (const key of keys) {
      if (key.startsWith(prefix)) {
        await this.storage.removeItem(key);
      }
    }
  }
  
  /**
   * 移除指定键的缓存
   * @param key 缓存键
   */
  async remove(key: string): Promise<void> {
    await this.storage.removeItem(key);
  }
  
  /**
   * 获取所有缓存键
   * @returns 所有缓存键数组
   */
  async keys(): Promise<string[]> {
    return this.storage.keys();
  }
  
  /**
   * 检查缓存键是否存在
   * @param key 缓存键
   * @returns 是否存在且未过期
   */
  async has(key: string): Promise<boolean> {
    const value = await this.get(key);
    return value !== null;
  }
}

export default CacheService;
