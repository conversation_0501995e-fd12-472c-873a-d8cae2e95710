import { defineStore } from 'pinia';
import localforage from 'localforage';

export const useApiManagementStore = defineStore('apiManagement', {
  state: () => ({
    apis: {} as { [key: string]: any[] },
    groups: {} as { [key: string]: string[] },
    dtos: {} as { [key: string]: any[] },
    controllers: {} as { [key: string]: any[] }, // 新增 controllers 状态
  }),
  actions: {
    async saveApis(module: string, apis: any[]) {
      this.apis[module] = apis; // 更新对象中的对应模块数据
      const key = `${module}::apis`;
      await localforage.setItem(key, apis);
    },
    async loadApis(module: string) {
      const key = `${module}::apis`;
      const apis = await localforage.getItem<any[]>(key);
      if (apis) {
        this.apis[module] = apis; // 更新对象中的对应模块数据
      }
    },
    async getApis(module: string): Promise<any[]> {
      if (this.apis[module]&&this.apis[module].length>0) {  
        return this.apis[module]; // 返回对应模块的apis列表
      } else {
        await this.loadApis(module);
        return this.apis[module]; // 返回对应模块的apis列表
      }
    },
    async saveGroups(module: string, groups: any[]) {
      this.groups[module] = groups; // 更新对象中的对应模块数据
      const key = `${module}::groups`;
      await localforage.setItem(key, groups);
    },
    async loadGroups(module: string) {
      const key = `${module}::groups`;
      const groups = await localforage.getItem<any[]>(key);
      if (groups) {
        this.groups[module] = groups; // 更新对象中的对应模块数据
      }
    },
    async getGroups(module: string): Promise<any[]> {
      if (this.groups[module] && this.groups[module].length > 0) {  
        return this.groups[module]; // 返回对应模块的groups列表
      } else {
        await this.loadGroups(module);
        return this.groups[module]; // 返回对应模块的groups列表
      }
    },
    async saveDtos(module: string, dtos: any[]) {
      this.dtos[module] = dtos; // 更新对象中的对应模块数据
      const key = `${module}::dtos`;
      await localforage.setItem(key, dtos);
    },
    async loadDtos(module: string) {
      const key = `${module}::dtos`;
      const dtos = await localforage.getItem<any[]>(key);
      if (dtos) {
        this.dtos[module] = dtos; // 更新对象中的对应模块数据
      }
    },
    async getDtos(module: string): Promise<any[]> {
      if (this.dtos[module]&&this.dtos[module].length>0) {  
        return this.dtos[module]; // 返回对应模块的dtos列表
      } else {
        await this.loadDtos(module);
        return this.dtos[module]; // 返回对应模块的dtos列表
      }
    },
    // 新增删除方法
    async removeApis(module: string) {
      delete this.apis[module]; // 删除 store 中的数据
      const key = `${module}::apis`;
      await localforage.removeItem(key); // 删除 localforage 中的数据
    },
    async removeGroups(module: string) {
      delete this.groups[module]; // 删除 store 中的数据
      const key = `${module}::groups`;
      await localforage.removeItem(key); // 删除 localforage 中的数据
    },
    async removeDtos(module: string) {
      delete this.dtos[module]; // 删除 store 中的数据
      const key = `${module}::dtos`;
      await localforage.removeItem(key); // 删除 localforage 中的数据
    },
    async saveControllers(module: string, controllers: any[]) {
      this.controllers[module] = controllers; // 更新对象中的对应模块数据
      const key = `${module}::controllers`;
      await localforage.setItem(key, controllers);
    },
    async loadControllers(module: string) {
      console.log('loadControllers module:', module);
      const key = `${module}::controllers`;
      const controllers = await localforage.getItem<any[]>(key);
      console.log('loadControllers controllers:', controllers);
      if (controllers) {
        this.controllers[module] = controllers; // 更新对象中的对应模块数据
        console.log('loadControllers module:', module, this.controllers[module]);
        //return this.controllers[module];
      }
    },
    async getControllers(module: string): Promise<any[]> {
      if (this.controllers[module] && this.controllers[module].length > 0) {
        console.log('this.controllers[module]:', this.controllers[module]);
        return this.controllers[module]; // 返回对应模块的 controllers 列表
      } else {
        console.log('getControllers module:', module);
        await this.loadControllers(module)

        return this.controllers[module]; // 返回对应模块的 controllers 列表
      }
    },
    async removeControllers(module: string) {
      delete this.controllers[module]; // 删除 store 中的数据
      const key = `${module}::controllers`;
      await localforage.removeItem(key); // 删除 localforage 中的数据
    },
  },
});