/**
 * 路由存储
 * 用于管理动态路由状态并使用localforage持久化
 * 解决页面刷新导致动态路由丢失的问题
 */
import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { RouteRecordRaw } from 'vue-router';
import localforage from 'localforage';
import { generateDynamicRoutes } from '../router';

/**
 * 路由存储
 */
export const useRouteStore = defineStore('adminRoute', () => {
  // 动态路由配置
  const dynamicRoutes = ref<RouteRecordRaw[]>([]);
  
  // 路由加载状态
  const routesLoaded = ref(false);
  
  // 初始化时从localforage恢复路由配置
  const initRoutes = async () => {
    try {
      // 从localforage获取保存的前端路径数据
      const frontendPaths = await localforage.getItem('admin_frontend_paths');
      
      if (frontendPaths && Array.isArray(frontendPaths) && frontendPaths.length > 0) {
        // 使用保存的前端路径数据生成动态路由
        const routes = generateDynamicRoutes(frontendPaths);
        dynamicRoutes.value = routes;
        routesLoaded.value = true;
        console.log('已从localforage恢复动态路由配置', routes.length, '个路由');
        return routes;
      }
      
      return [];
    } catch (error) {
      console.error('恢复动态路由配置失败:', error);
      return [];
    }
  };
  
  /**
   * 更新路由配置（只在内存中）
   * @param routes 路由配置数组
   */
  const updateRoutes = (routes: RouteRecordRaw[]) => {
    try {
      dynamicRoutes.value = routes;
      routesLoaded.value = true;
      console.log('动态路由配置已更新（仅内存）');
    } catch (error) {
      console.error('更新动态路由配置失败:', error);
    }
  };
  
  /**
   * 清除路由配置
   */
  const clearRoutes = () => {
    try {
      dynamicRoutes.value = [];
      routesLoaded.value = false;
      console.log('动态路由配置已清除');
    } catch (error) {
      console.error('清除动态路由配置失败:', error);
    }
  };
  
  return {
    dynamicRoutes,
    routesLoaded,
    initRoutes,
    updateRoutes,
    clearRoutes
  };
}); 