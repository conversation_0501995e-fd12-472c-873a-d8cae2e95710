<!--
 * @Description: 配送费配置编辑器组件
 * @Author: AI Assistant
 * @Date: 2025-01-20
 * @Version: 1.0.0
 * 用于管理外卖配送费相关配置参数
 * 输出格式为JSON对象
-->
<template>
  <div class="delivery-fee-config-editor">
    <div class="config-toolbar">
      <div>
        <el-button type="primary" @click="saveConfig">
          <el-icon><Check /></el-icon> 保存配置
        </el-button>
        <el-button type="info" @click="resetToDefault">
          <el-icon><Refresh /></el-icon> 重置为默认
        </el-button>
        <el-button type="info" @click="showJsonSource = !showJsonSource">
          <el-icon>
            <Hide v-if="showJsonSource" />
            <View v-else />
          </el-icon> 
          {{ showJsonSource ? '隐藏源码' : '查看源码' }}
        </el-button>
      </div>
    </div>
    
    <!-- JSON源码查看器 -->
    <div v-if="showJsonSource" class="json-source-viewer">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>配送费配置JSON</span>
          </div>
        </template>
        <JsonViewer 
          :value="configData" 
          :expandDepth="2" 
          copyable 
          sort 
          boxed 
          theme="light"
          class="text-left"
        />
      </el-card>
    </div>

    <!-- 配送费配置表单 -->
    <el-card class="config-form-card">
      <template #header>
        <div class="card-header">
          <span>配送费配置</span>
        </div>
      </template>
      
      <el-form :model="configData" label-width="150px" class="config-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="基础配送费">
              <el-input-number
                v-model="configData.deliveryBaseFee"
                :min="0"
                :precision="2"
                :step="0.1"
                placeholder="请输入基础配送费"
                style="width: 100%"
              />
              <div class="form-item-tip">单位：元</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="每公里配送费">
              <el-input-number
                v-model="configData.deliveryKmFee"
                :min="0"
                :precision="2"
                :step="0.1"
                placeholder="请输入每公里配送费"
                style="width: 100%"
              />
              <div class="form-item-tip">单位：元/公里</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最低起送金额">
              <el-input-number
                v-model="configData.deliveryMinOrderAmount"
                :min="0"
                :precision="2"
                :step="1"
                placeholder="请输入最低起送金额"
                style="width: 100%"
              />
              <div class="form-item-tip">单位：元</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="满额免配送费">
              <el-switch
                v-model="configData.deliveryFreeEnabled"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" v-if="configData.deliveryFreeEnabled">
          <el-col :span="12">
            <el-form-item label="免配送费金额">
              <el-input-number
                v-model="configData.deliveryFreeAmount"
                :min="0"
                :precision="2"
                :step="1"
                placeholder="请输入免配送费金额"
                style="width: 100%"
              />
              <div class="form-item-tip">单位：元，订单金额达到此值免配送费</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="配送费折扣">
              <el-switch
                v-model="configData.deliveryDiscountEnabled"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" v-if="configData.deliveryDiscountEnabled">
          <el-col :span="12">
            <el-form-item label="折扣起始金额">
              <el-input-number
                v-model="configData.deliveryDiscountAmount"
                :min="0"
                :precision="2"
                :step="1"
                placeholder="请输入折扣起始金额"
                style="width: 100%"
              />
              <div class="form-item-tip">单位：元</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="折扣率">
              <el-input-number
                v-model="configData.deliveryDiscountRate"
                :min="0.1"
                :max="1"
                :precision="2"
                :step="0.1"
                placeholder="请输入折扣率"
                style="width: 100%"
              />
              <div class="form-item-tip">范围：0.1-1.0，如0.8表示8折</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Refresh, Hide, View } from '@element-plus/icons-vue'
import JsonViewer from 'vue3-json-viewer'
import 'vue3-json-viewer/dist/index.css'

// 定义props
const props = defineProps<{
  modelValue: string
}>()

// 定义emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

// 显示JSON源码
const showJsonSource = ref(false)

// 默认配置数据
const defaultConfig = {
  deliveryBaseFee: 10.0,
  deliveryKmFee: 2.0,
  deliveryMinOrderAmount: 0.0,
  deliveryFreeEnabled: false,
  deliveryFreeAmount: 30.0,
  deliveryDiscountEnabled: false,
  deliveryDiscountAmount: 20.0,
  deliveryDiscountRate: 0.8
}

// 配置数据
const configData = ref({ ...defaultConfig })

// 解析传入的JSON字符串
const parseConfigValue = (value: string) => {
  try {
    if (!value || value.trim() === '') {
      configData.value = { ...defaultConfig }
      return
    }
    const parsed = JSON.parse(value)
    configData.value = { ...defaultConfig, ...parsed }
  } catch (error) {
    console.error('解析配送费配置失败:', error)
    configData.value = { ...defaultConfig }
    ElMessage.warning('配置数据格式错误，已重置为默认配置')
  }
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  parseConfigValue(newValue)
}, { immediate: true })

// 监听配置数据变化，同步到父组件
watch(configData, (newData) => {
  const jsonString = JSON.stringify(newData, null, 2)
  emit('update:modelValue', jsonString)
}, { deep: true })

/**
 * 保存配置
 */
const saveConfig = () => {
  try {
    const jsonString = JSON.stringify(configData.value, null, 2)
    emit('update:modelValue', jsonString)
    ElMessage.success('配置已保存')
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  }
}

/**
 * 重置为默认配置
 */
const resetToDefault = () => {
  configData.value = { ...defaultConfig }
  ElMessage.success('已重置为默认配置')
}
</script>

<style scoped>
.delivery-fee-config-editor {
  width: 100%;
}

.config-toolbar {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.json-source-viewer {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form-card {
  margin-bottom: 20px;
}

.config-form {
  padding: 20px;
}

.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-card__body) {
  padding: 20px;
}

.text-left {
  text-align: left;
}
</style>