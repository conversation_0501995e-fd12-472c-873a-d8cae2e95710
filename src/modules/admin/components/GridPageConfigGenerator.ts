/**
 * @file GridPageConfigGenerator.ts
 * @description 网格页面配置生成器，用于创建标准化的网格页面配置对象
 * <AUTHOR> Assistant
 * @created 2025-04-14
 */

/**
 * 用户状态枚举
 */
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  LOCKED = 'LOCKED',
  DISABLED = 'DISABLED'
}

/**
 * 自定义操作配置接口
 */
export interface CustomAction {
  url: string;
  method: string;
  confirmMessage?: string;
  successMessage?: string;
  formatParams?: (params: any) => any;
  formatResponse?: (response: any) => any;
  onSuccess?: (response: any, params: any) => void;
  onError?: (error: any, params: any) => void;
}

/**
 * 服务配置接口
 */
export interface ServiceConfig {
  baseUrl: string;
  apiPrefix?: string;
  formatResponse?: (response: any) => any;
  customActions?: Record<string, CustomAction>;
  messages?: {
    addSuccess?: string;
    updateSuccess?: string;
    deleteConfirm?: string;
    deleteSuccess?: string;
    [key: string]: string | undefined;
  };
  addTitle?: string;
  editTitle?: string;
  viewTitle?: string;
  permissions?: {
    add?: string;
    edit?: string;
    delete?: string;
    view?: string;
    [key: string]: string | undefined;
  };
}

/**
 * 页面配置接口
 */
export interface GridPageConfig {
  version: string;
  title: string;
  moduleName?: string;
  frontendPath?: string;
  icon?: string;
  serviceConfig: ServiceConfig;
  keepAlive?: boolean;
}

/**
 * 字段结构描述接口
 */
export interface FieldStructure {
  description: string;
  type: string;
  validation?: string;
  [key: string]: any;
}

/**
 * 接口结构描述，键为字段名，值为字段结构描述
 */
export interface ApiStructure {
  [key: string]: FieldStructure;
}

/**
 * 网格页面配置生成器
 */
export class GridPageConfigGenerator {
  /**
   * 从描述中解析选项
   * @param {string} description - 描述文本，例如："性别 (1:男,2:女,0:保密)"
   * @returns {object} 解析后的选项对象
   */
  static parseOptionsFromDescription(description: string): {
    cleanLabel: string;
    options: { label: string; value: any }[] | null;
  } {
    // 默认返回值
    const result = {
      cleanLabel: description,
      options: null as { label: string; value: any }[] | null
    };
    
    // 如果描述为空，直接返回
    if (!description) {
      return result;
    }
    
    // 匹配括号中的选项 (1:男,2:女,0:保密) 或 (男,女,保密)
    const optionsMatch = description.match(/\((.*?)\)/);
    if (!optionsMatch) {
      return result;
    }
    
    // 提取干净的标签（不包含选项部分）
    result.cleanLabel = description.replace(/\s*\(.*?\)/, '').trim();
    
    // 获取选项字符串并分割
    const optionsStr = optionsMatch[1];
    const optionParts = optionsStr.split(',').map(part => part.trim());
    
    // 处理有值和标签对的情况: "1:男,2:女,0:保密"
    if (optionParts[0].includes(':')) {
      result.options = optionParts.map(part => {
        const [value, label] = part.split(':').map(p => p.trim());
        return {
          label,
          value: isNaN(Number(value)) ? value : Number(value)
        };
      });
    } else {
      // 处理只有标签的情况: "男,女,保密"
      result.options = optionParts.map((label, index) => ({
        label,
        value: index
      }));
    }
    
    return result;
  }

  /**
   * 根据API结构创建基础页面配置
   * @param {string} title - 页面标题
   * @param {string} baseUrl - 服务基础URL
   * @param {ApiStructure} structure - API结构描述
   * @param {object} options - 其他选项配置
   * @returns {GridPageConfig} 页面配置
   */
  static createConfigFromStructure(
    title: string,
    baseUrl: string,
    _structure: ApiStructure,
    options: {
      customActions?: Record<string, CustomAction>;
      messages?: Record<string, string>;
      moduleName?: string;
      frontendPath?: string;
      icon?: string;
      apiPrefix?: string;
      [key: string]: any;
    } = {}
  ): GridPageConfig {
    // 默认的消息提示
    const defaultMessages = {
      addSuccess: `${title}创建成功`,
      updateSuccess: `${title}更新成功`,
      deleteConfirm: `确定要删除该${title}吗？`,
      deleteSuccess: `${title}删除成功`
    };
    
    const messages = { ...defaultMessages, ...options.messages };
    
    // 默认的自定义操作
    const customActions: Record<string, CustomAction> = { ...options.customActions };
    
    // 生成最终的页面配置
    return {
      version: '1.0.0',
      title,
      moduleName: options.moduleName || '',
      frontendPath: options.frontendPath || '',
      icon: options.icon || '',
      serviceConfig: {
        baseUrl,
        apiPrefix: options.apiPrefix || '/api',
        formatResponse: (response: any) => {
          return {
            data: response.list || response.data || [], 
            total: response.total || 0 
          };
        },
        customActions,
        messages,
        addTitle: options.addTitle || `新增${title}`,
        editTitle: options.editTitle || `编辑${title}信息`,
        viewTitle: options.viewTitle || `查看${title}信息`
      },
      keepAlive: options.keepAlive
    };
  }

  /**
   * 根据DTO对象生成标准PageData页面配置
   * @param dto DTO对象（结构示例见业务说明）
   * @param apiStructure API结构（可选）
   * @param extra 额外补充字段（可选）
   * @returns PageData对象
   */
  public generateConfig(dto: any, _apiStructure: any = {}, extra: any = {}): any {
    // 生成唯一key和hash
    const configKey = `page_${dto.module || 'default'}_${dto.id}`;
    const versionHash = `${dto.id}_${dto.updated_at || Date.now()}`;
    const now = new Date().toISOString();

    // 生成简单的UI配置（如表单字段）
    const configContent: any = {
      fields: Object.entries(dto.structure || {}).map(([key, value]: [string, any]) => ({
        field: key,
        label: value.description || key,
        type: value.type || 'string',
        required: value.validation?.toLowerCase() === 'required',
        ...value
      }))
      // 可扩展更多UI配置
    };

    // 生成简单的grid项（每个字段一个）
    const gridItems = Object.keys(dto.structure || {}).map((key, idx) => ({
      id: idx + 1,
      name: key,
      position: { x: idx % 4, y: Math.floor(idx / 4), w: 3, h: 2, i: idx + 1 },
      content: {
        type: 'field',
        title: dto.structure[key].description || key,
        config: { field: key }
      },
      remark: '',
      status: 1,
      created_at: now,
      updated_at: now
    }));

    // 组装PageData
    return {
      id: dto.id || 0,
      config_key: configKey,
      config_type: dto.type || 'page',
      created_at: dto.created_at || now,
      updated_at: dto.updated_at || now,
      draggable: true,
      resizable: true,
      status: 1,
      title: dto.name || dto.description || '',
      version: '1.0.0',
      version_hash: versionHash,
      remark: dto.description || '',
      module: dto.module || '',
      group: dto.group || '',
      icon: dto.icon || '',
      frontend_path: dto.frontend_path || '',
      config_content: configContent,
      dto,
      grid_items: gridItems,
      ...extra
    };
  }
}

export default GridPageConfigGenerator;
