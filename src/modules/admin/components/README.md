# 页面配置生成器 (PageConfigGenerator)

## 简介

`PageConfigGenerator` 是一个用于生成标准化页面配置对象的工具类，它提供了创建各种管理页面所需的配置结构。通过使用这个组件，您可以快速创建具有完整CRUD功能的管理页面，而无需重复编写大量的配置代码。

## 主要功能

1. 提供标准化的页面配置接口
2. 内置常用页面配置生成方法
3. 支持自定义配置项的灵活扩展
4. 包含完整的类型定义和接口文档

## 使用方法

### 1. 使用预设模板创建页面配置

```typescript
import { PageConfigGenerator } from '@/modules/admin/components/PageConfigGenerator';

// 使用内置的用户管理页面配置模板
const userPageConfig = PageConfigGenerator.createUserPageConfig();

// 在组件中使用
export default {
  setup() {
    return {
      pageConfig: userPageConfig
    };
  }
};
```

### 2. 根据API结构生成页面配置

```typescript
import { PageConfigGenerator, ApiStructure } from '@/modules/admin/components/PageConfigGenerator';

// API结构描述
const userStructure: ApiStructure = {
  id: { description: '用户ID', type: 'number', validation: 'Required' },
  username: { description: '用户名', type: 'string', validation: 'Required;MinSize(3);MaxSize(20)' },
  nickname: { description: '昵称', type: 'string', validation: 'MaxSize(20)' },
  password: { description: '密码', type: 'string', validation: 'Required;MinSize(6);MaxSize(20)' },
  mobile: { description: '手机号', type: 'string', validation: 'Required;Mobile' },
  email: { description: '邮箱', type: 'string', validation: 'Email' },
  avatar: { description: '头像', type: 'string' },
  gender: { description: '性别：0-未知，1-男，2-女', type: 'number', validation: 'Range(0,2)' },
  status: { description: '状态', type: 'string' },
  created_at: { description: '创建时间', type: 'object' },
  last_login_at: { description: '最后登录时间', type: 'object' }
};

// 创建用户管理页面配置
const userConfig = PageConfigGenerator.createConfigFromStructure(
  '用户管理',
  'v1/admin/secured/users',
  userStructure
);

// 或者使用更多自定义选项
const customUserConfig = PageConfigGenerator.createConfigFromStructure(
  '用户管理',
  'v1/admin/secured/users',
  userStructure,
  {
    // 自定义状态选项
    statusOptions: [
      { value: 'ACTIVE', label: '正常', status: 'success' },
      { value: 'LOCKED', label: '锁定', status: 'warning' },
      { value: 'DISABLED', label: '禁用', status: 'danger' }
    ],
    // 指定日期字段
    dateFields: ['created_at', 'last_login_at', 'birthday'],
    // 指定图片字段
    imageFields: ['avatar', 'cover']
  }
);
```

### 3. 创建自定义页面配置

```typescript
import { PageConfigGenerator, TableValueType, FormItemValueType } from '@/modules/admin/components/PageConfigGenerator';

// 创建自定义页面配置
const customPageConfig = PageConfigGenerator.createCustomPageConfig(
  '商品管理', // 页面标题
  'v1/admin/secured/products', // 服务基础URL
  [
    // 表格列配置
    { label: 'ID', prop: 'id', width: 80 },
    { label: '商品名称', prop: 'name', width: 150 },
    { label: '价格', prop: 'price', width: 100 },
    // 更多列...
  ],
  [
    // 表单项配置
    {
      label: '商品名称',
      prop: 'name',
      required: true,
      rules: [
        { required: true, message: '请输入商品名称', trigger: 'blur' }
      ]
    },
    {
      label: '价格',
      prop: 'price',
      valueType: FormItemValueType.NUMBER,
      required: true
    },
    // 更多表单项...
  ],
  {
    // 选项配置
    toolbarButtons: [
      {
        text: '新增商品',
        type: 'primary',
        icon: 'Plus',
        action: 'add'
      }
    ],
    // 更多选项...
  }
);
```

## 配置项说明

### 1. 页面配置 (PageConfig)

页面配置是最顶层的配置对象，包含以下主要部分：

- `version`: 配置版本号
- `title`: 页面标题
- `serviceConfig`: 服务配置
- `tableConfig`: 表格配置
- `formConfig`: 表单配置
- `toolbarConfig`: 工具栏配置
- `searchConfig`: 搜索配置

### 2. 服务配置 (ServiceConfig)

服务配置定义了与后端API交互的相关设置：

- `baseUrl`: API基础URL
- `formatResponse`: 响应数据格式化函数
- `customActions`: 自定义操作配置
- `messages`: 操作消息提示
- `addTitle`: 新增表单标题
- `editTitle`: 编辑表单标题

### 3. 表格配置 (TableConfig)

表格配置定义了数据表格的显示方式：

- `columns`: 表格列配置
- `actions`: 操作列配置
- `pagination`: 分页配置
- `showSelection`: 是否显示选择框
- `showIndex`: 是否显示序号列
- `showActions`: 是否显示操作列
- `rowKey`: 行唯一标识字段

### 4. 表单配置 (FormConfig)

表单配置定义了新增/编辑表单的结构：

- `columns`: 表单项配置
- `labelWidth`: 标签宽度
- `labelPosition`: 标签位置
- `size`: 表单尺寸

### 5. 工具栏配置 (ToolbarConfig)

工具栏配置定义了页面顶部工具栏按钮：

- `buttons`: 工具栏按钮配置
- `showSelection`: 是否显示批量操作按钮
- `selectionActions`: 批量操作按钮配置

### 6. 搜索配置 (SearchConfig)

搜索配置定义了搜索表单的结构：

- `columns`: 搜索表单项配置
- `showReset`: 是否显示重置按钮
- `showCollapse`: 是否显示折叠按钮
- `labelWidth`: 标签宽度
- `labelPosition`: 标签位置

## 类型定义

组件导出了多种有用的类型和枚举：

- `TableValueType`: 表格列值类型枚举
- `FormItemValueType`: 表单项值类型枚举
- `UserStatus`: 用户状态枚举
- `TableColumn`: 表格列配置接口
- `FormItem`: 表单项配置接口
- 等等...

## 示例

查看组件代码中的 `createUserPageConfig` 方法，了解完整的配置示例。
