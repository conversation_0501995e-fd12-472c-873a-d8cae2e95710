/**
 * @Description: App菜单配置编辑器组件
 * @Author: AI Assistant
 * @Date: 2025-01-20
 * @Version: 1.1.0
 * 用于管理App功能区菜单列表，支持添加、编辑、删除和排序菜单项
 * 输出格式为JSON数组
 * 更新：内置图标支持使用wotUi图标命名，适用于app和小程序环境
 */
<template>
  <div class="app-menu-config-editor">
    <div class="menu-toolbar">
      <div>
        <el-button type="primary" @click="addMenuItem">
          <el-icon><Plus /></el-icon> 添加菜单项
        </el-button>
        <el-button type="info" @click="showJsonSource = !showJsonSource">
          <el-icon>
            <Hide v-if="showJsonSource" />
            <View v-else />
          </el-icon> 
          {{ showJsonSource ? '隐藏源码' : '查看源码' }}
        </el-button>
      </div>
    </div>
    
    <!-- JSON源码查看器 -->
    <div v-if="showJsonSource" class="json-source-viewer">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>App菜单数据JSON</span>
          </div>
        </template>
        <JsonViewer 
          :value="parsedMenuData" 
          :expandDepth="2" 
          copyable 
          sort 
          boxed 
          theme="light"
          class="text-left"
        />
      </el-card>
    </div>
    
    <el-empty v-if="menuList.length === 0" description="暂无菜单数据" />
    
    <div class="table-container">
    <el-table
      v-if="menuList.length > 0"
      :data="menuList"
      row-key="id"
      border
      style="width: 100%; max-width: 100%;"
    >
      <el-table-column label="序号" width="60">
        <template #default="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="图标" width="120">
        <template #default="scope">
          <div class="icon-preview">
            <!-- 使用Element Plus图标映射方案在管理端渲染wotUi图标 -->
            <div v-if="scope.row.iconType === 'builtin' && scope.row.icon" class="menu-icon wot-icon-preview">
              <el-icon :size="24">
                <component :is="getElementIconFromWot(scope.row.icon)" />
              </el-icon>
              <span class="wot-icon-name">{{ scope.row.icon }}</span>
            </div>
            <div v-else-if="scope.row.iconType === 'custom' && scope.row.iconUrl" class="menu-icon custom-icon-preview">
              <el-image 
                :src="scope.row.iconUrl" 
                fit="cover"
                style="width: 24px; height: 24px"
              >
                <template #error>
                  <div class="icon-error">加载失败</div>
                </template>
              </el-image>
            </div>
            <span v-else class="no-icon">无图标</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="title" label="菜单名称" min-width="120" />
      
      <el-table-column prop="type" label="菜单类型" width="100">
        <template #default="scope">
          <el-tag :type="getMenuTypeTag(scope.row.type)">
            {{ getMenuTypeText(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="action" label="跳转动作" min-width="150" show-overflow-tooltip />
      
      <el-table-column prop="sort_order" label="排序" width="80" />
      
      <el-table-column label="状态" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="220">
        <template #default="scope">
          <el-button size="small" @click="editMenuItem(scope.row, scope.$index)">
            编辑
          </el-button>
          <el-button 
            size="small" 
            type="primary" 
            plain
            :disabled="scope.$index === 0"
            @click="moveMenu(scope.$index, scope.$index - 1)"
          >
            <el-icon><ArrowUp /></el-icon>
          </el-button>
          <el-button 
            size="small" 
            type="primary" 
            plain
            :disabled="scope.$index === menuList.length - 1"
            @click="moveMenu(scope.$index, scope.$index + 1)"
          >
            <el-icon><ArrowDown /></el-icon>
          </el-button>
          <el-button size="small" type="danger" @click="removeMenuItem(scope.$index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    </div>
    
    <!-- 添加/编辑菜单项对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editingIndex === -1 ? '添加菜单项' : '编辑菜单项'"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="currentMenu"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="菜单名称" prop="title">
          <el-input v-model="currentMenu.title" placeholder="请输入菜单名称" />
        </el-form-item>
        
        <el-form-item label="菜单类型" prop="type">
          <el-select v-model="currentMenu.type" placeholder="请选择菜单类型" @change="handleMenuTypeChange">
            <el-option label="页面跳转" value="page" />
            <el-option label="外部链接" value="link" />
            <el-option label="小程序" value="miniapp" />
            <el-option label="功能操作" value="action" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="图标类型" prop="iconType">
          <el-radio-group v-model="currentMenu.iconType" @change="handleIconTypeChange">
            <el-radio label="builtin">内置图标</el-radio>
            <el-radio label="custom">自定义图片</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item v-if="currentMenu.iconType === 'builtin'" label="内置图标" prop="icon">
          <el-select v-model="currentMenu.icon" placeholder="请选择图标" filterable>
            <el-option
              v-for="iconName in availableIcons"
              :key="iconName"
              :label="iconName"
              :value="iconName"
            >
              <div style="display: flex; align-items: center; gap: 8px;">
                <el-icon>
                  <component :is="getElementIconFromWot(iconName)" />
                </el-icon>
                <span>{{ iconName }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="currentMenu.iconType === 'custom'" label="图标图片" prop="iconUrl">
          <div class="icon-uploader">
            <el-image 
              v-if="currentMenu.iconUrl" 
              :src="currentMenu.iconUrl" 
              class="uploaded-icon"
            />
            <FileUploader
              v-else
              class="icon-image-uploader"
              file-usage="appmenu"
              accept="image/*"
              :max-size="2"
              button-type="primary"
              :show-file-list="false"
              @success="handleIconUploadSuccess"
            >
              <template #tip>
                <div class="icon-uploader-tip">
                  建议尺寸: 64x64像素，格式：PNG，大小不超过1MB
                </div>
              </template>
            </FileUploader>
          </div>
        </el-form-item>
        
        <el-form-item label="跳转动作" prop="action">
          <el-input 
            v-model="currentMenu.action" 
            :placeholder="getActionPlaceholder(currentMenu.type)"
            type="textarea"
            :rows="2"
          />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="currentMenu.description" 
            placeholder="请输入菜单描述（可选）"
            type="textarea"
            :rows="2"
          />
        </el-form-item>
        
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="currentMenu.sort_order" :min="1" :max="999" />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="currentMenu.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveMenuItem">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
// 导入Element Plus图标组件
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { FileUploader } from '@/components/common'
import { JsonViewer } from 'vue3-json-viewer'
import 'vue3-json-viewer/dist/index.css'

// 菜单项接口
interface MenuItem {
  id: number
  title: string
  description?: string
  type: string
  iconType: string
  icon?: string // wotUi图标名称格式
  iconUrl?: string
  action: string
  sort_order: number
  status: number // 0-禁用，1-启用
}

// 定义组件属性
const props = defineProps<{
  modelValue: string // 接收v-model绑定的值
}>()

// 定义事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

// 可用的内置图标列表 (wotUi图标名称格式)
const availableIcons = [
  // 基础图标
  'add-circle', 'arrow', 'arrow-down', 'arrow-right', 'arrow-up', 'arrow-left', 
  'camera', 'check', 'clock', 'close', 'computer', 'delete', 'edit', 'eye', 'eye-close',
  'filter', 'goods', 'home', 'info', 'loading', 'location', 'more', 'notice', 
  'phone', 'picture', 'question', 'refresh', 'scan', 'search', 'setting', 'share',
  'shop', 'success', 'user', 'video', 'view', 'warning',
  // 购物相关
  'cart', 'cart-o', 'shopping', 'delete-thin', 'chat', 'evaluation', 'bags',
  // 数据与文件类
  'download', 'upload', 'copy', 'list', 'note', 'keywords', 'translate', 'translate-bold', 
  'folder', 'files', 'document', 'tickets', 'collection', 
  // 分析与统计
  'data-analysis', 'data-board', 'chart', 'pie-chart', 'discount', 'trophy',
  // 其他
  'spool', 'jdm', 'lenovo', 'decrease', 'transfer', 'menu', 'grid', 'message', 'notification'
]

// wotUi图标名称到Element Plus图标的映射
const wotToElementMap: { [key: string]: string } = {
  // 基础图标
  'add-circle': 'Plus',
  'arrow': 'Right',
  'arrow-down': 'ArrowDown',
  'arrow-right': 'ArrowRight',
  'arrow-up': 'ArrowUp',
  'arrow-left': 'ArrowLeft',
  'camera': 'Camera',
  'check': 'Check',
  'clock': 'Clock',
  'close': 'Close',
  'computer': 'Monitor',
  'delete': 'Delete',
  'edit': 'Edit',
  'eye': 'View',
  'eye-close': 'Hide',
  'filter': 'Filter',
  'goods': 'Goods',
  'home': 'House',
  'info': 'Info',
  'loading': 'Loading',
  'location': 'Location',
  'more': 'More',
  'notice': 'Bell',
  'phone': 'Iphone',
  'picture': 'Picture',
  'question': 'Question',
  'refresh': 'Refresh',
  'scan': 'Aim',
  'search': 'Search',
  'setting': 'Setting',
  'share': 'Share',
  'shop': 'Shop',
  'success': 'SuccessFilled',
  'user': 'User',
  'video': 'VideoCamera',
  'view': 'View',
  'warning': 'Warning',
  // 购物相关
  'cart': 'ShoppingCart',
  'cart-o': 'ShoppingCartFull',
  'shopping': 'ShoppingBag',
  'delete-thin': 'Delete',
  'chat': 'ChatDotRound',
  'evaluation': 'ChatLineRound',
  'bags': 'Suitcase',
  // 数据与文件
  'download': 'Download',
  'upload': 'Upload',
  'copy': 'CopyDocument',
  'folder': 'Folder',
  'files': 'Files',
  'document': 'Document',
  'tickets': 'Tickets',
  'collection': 'Collection',
  'list': 'List',
  'note': 'Document',
  'keywords': 'MessageBox',
  'translate': 'Operation',
  'translate-bold': 'Operation',
  // 分析与统计
  'data-analysis': 'DataAnalysis',
  'data-board': 'DataBoard',
  'chart': 'PieChart',
  'pie-chart': 'PieChart',
  'discount': 'Discount',
  'trophy': 'Trophy',
  // 其他
  'spool': 'Document',
  'jdm': 'Opportunity',
  'lenovo': 'Monitor',
  'decrease': 'Star',
  'transfer': 'Briefcase',
  'menu': 'Menu',
  'grid': 'Grid',
  'message': 'MessageBox',
  'notification': 'Notification'
}

// 获取wotUi图标名对应的Element Plus图标组件
const getElementIconFromWot = (wotIconName: string) => {
  const iconName = wotToElementMap[wotIconName] || 'InfoFilled' // 默认返回InfoFilled图标
  // 返回对应的Element Plus图标组件
  return ElementPlusIconsVue[iconName as keyof typeof ElementPlusIconsVue]
}

// 本地状态
const menuList = ref<MenuItem[]>([])
const dialogVisible = ref(false)
const editingIndex = ref(-1)
const formRef = ref<FormInstance>()
const showJsonSource = ref(false) // 控制JSON源码显示

// 当前编辑的菜单项
const currentMenu = reactive<MenuItem>({
  id: 0,
  title: '',
  type: 'page',
  iconType: 'builtin',
  icon: '',
  iconUrl: '',
  action: '',
  description: '',
  sort_order: 1,
  status: 1
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' },
    { max: 20, message: '菜单名称长度不能超过20个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择菜单类型', trigger: 'change' }
  ],
  action: [
    { required: true, message: '请输入跳转动作', trigger: 'blur' }
  ],
  sort_order: [
    { required: true, message: '请设置排序值', trigger: 'change' }
  ]
}

// 获取菜单类型标签样式
const getMenuTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    page: 'primary',
    link: 'success',
    miniapp: 'warning',
    action: 'info'
  }
  return tagMap[type] || 'info'
}

// 获取菜单类型文本
const getMenuTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    page: '页面跳转',
    link: '外部链接',
    miniapp: '小程序',
    action: '功能操作'
  }
  return textMap[type] || '未知'
}

// 获取动作输入框占位符
const getActionPlaceholder = (type: string) => {
  const placeholderMap: Record<string, string> = {
    page: '请输入页面路径，如：/pages/product/list',
    link: '请输入完整URL，如：https://www.example.com',
    miniapp: '请输入小程序路径，如：pages/index/index',
    action: '请输入功能标识，如：scan_qr_code'
  }
  return placeholderMap[type] || '请输入跳转动作'
}

// 确保每个菜单项都有唯一ID
const ensureMenuIds = () => {
  let maxId = 0
  menuList.value.forEach(item => {
    if (!item.id) {
      item.id = Date.now() + Math.floor(Math.random() * 1000)
    }
    maxId = Math.max(maxId, Number(item.id))
  })
  
  // 保存当前最大ID值供新增使用
  return maxId
}

// 监听输入值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      try {
        const parsed = JSON.parse(newValue)
        if (Array.isArray(parsed)) {
          menuList.value = parsed
          ensureMenuIds()
        }
      } catch (error) {
        console.warn('解析菜单配置JSON失败:', error)
        menuList.value = []
      }
    } else {
      menuList.value = []
    }
  },
  { immediate: true }
)

// 计算JSON数据用于显示
const parsedMenuData = computed(() => {
  return menuList.value
})

// 监听菜单列表变化，更新输出值
watch(
  menuList,
  (newList) => {
    // 更新排序字段
    newList.forEach((item, index) => {
      item.sort_order = index + 1
    })
    
    // 发送更新事件
    emit('update:modelValue', JSON.stringify(newList))
  },
  { deep: true }
)

// 重置表单
const resetForm = () => {
  Object.assign(currentMenu, {
    id: 0,
    title: '',
    type: 'page',
    iconType: 'builtin',
    icon: '',
    iconUrl: '',
    action: '',
    description: '',
    sort_order: menuList.value.length + 1,
    status: 1
  })
  formRef.value?.clearValidate()
}

// 添加菜单项
const addMenuItem = () => {
  resetForm()
  editingIndex.value = -1
  dialogVisible.value = true
}

// 编辑菜单项
const editMenuItem = (item: MenuItem, index: number) => {
  Object.assign(currentMenu, { ...item })
  editingIndex.value = index
  dialogVisible.value = true
}

// 保存菜单项
const saveMenuItem = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 根据图标类型清理不需要的图标数据
    const menuData = { ...currentMenu }
    if (menuData.iconType === 'builtin') {
      // 如果是内置图标，清空自定义图标URL
      menuData.iconUrl = ''
    } else if (menuData.iconType === 'custom') {
      // 如果是自定义图标，清空内置图标名称
      menuData.icon = ''
    }
    
    if (editingIndex.value === -1) {
      // 新增
      const maxId = ensureMenuIds()
      menuData.id = maxId + 1
      menuList.value.push(menuData)
      ElMessage.success('菜单项添加成功')
    } else {
      // 编辑
      menuList.value[editingIndex.value] = menuData
      ElMessage.success('菜单项更新成功')
    }
    
    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 删除菜单项
const removeMenuItem = (index: number) => {
  menuList.value.splice(index, 1)
  ElMessage.success('菜单项删除成功')
}

// 移动菜单项调整顺序
const moveMenu = (fromIndex: number, toIndex: number) => {
  if (toIndex < 0 || toIndex >= menuList.value.length) return
  
  const item = menuList.value[fromIndex]
  menuList.value.splice(fromIndex, 1)
  menuList.value.splice(toIndex, 0, item)
}

// 处理菜单类型变化
const handleMenuTypeChange = () => {
  // 清空动作字段，让用户重新输入
  currentMenu.action = ''
}

// 处理图标类型变化
const handleIconTypeChange = () => {
  // 当切换图标类型时，清理之前的图标数据
  if (currentMenu.iconType === 'builtin') {
    // 切换到内置图标时，清空自定义图标URL
    currentMenu.iconUrl = ''
  } else if (currentMenu.iconType === 'custom') {
    // 切换到自定义图标时，清空内置图标名称
    currentMenu.icon = ''
  }
}

// 处理图标上传成功
const handleIconUploadSuccess = (response: any) => {
  console.log('图标上传成功:', response)
  if (response && response.file_url) {
    currentMenu.iconUrl = response.file_url
    ElMessage.success('图标上传成功')
  } else {
    ElMessage.error('图标上传失败，请重试')
  }
}
</script>

<style scoped>
/* 引入wotUi图标样式，实际项目中应在全局样式中引入 */
/* 管理端不需要引入wotUi图标样式，使用Element Plus映射方案 */
.app-menu-config-editor {
  width: 100%;
}

.menu-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

.json-source-viewer {
  margin-bottom: 16px;
}

.table-container {
  overflow-x: auto;
}

.icon-preview {
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-icon {
  color: #409EFF;
}

.wot-icon-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.custom-icon-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.wot-icon-name {
  font-size: 10px;
  color: #909399;
  margin-top: 2px;
}

.icon-error {
  font-size: 12px;
  color: #f56c6c;
}

.no-icon {
  font-size: 12px;
  color: #909399;
}

.icon-uploader {
  display: flex;
  align-items: center;
  gap: 12px;
}

.icon-image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-image-uploader:hover {
  border-color: #409EFF;
}

.uploaded-icon {
  width: 64px;
  height: 64px;
  display: block;
  object-fit: cover;
  border-radius: 4px;
}

.icon-uploader-tip {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
  margin-top: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  text-align: right;
}

.text-left {
  text-align: left;
}
</style>