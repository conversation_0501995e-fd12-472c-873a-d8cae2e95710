/**
 * Banner配置编辑器组件
 * 用于管理Banner列表，支持添加、编辑、删除和排序Banner项
 * 输出格式为JSON数组
 */
<template>
  <div class="banner-config-editor">
    <div class="banner-toolbar">
      <div>
        <el-button type="primary" @click="addBannerItem">
          <el-icon><Plus /></el-icon> 添加Banner
        </el-button>
        <el-button type="info" @click="showJsonSource = !showJsonSource">
          <el-icon>
            <Hide v-if="showJsonSource" />
            <View v-else />
          </el-icon> 
          {{ showJsonSource ? '隐藏源码' : '查看源码' }}
        </el-button>
      </div>
    </div>
    
    <!-- JSON源码查看器 -->
    <div v-if="showJsonSource" class="json-source-viewer">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>Banner数据JSON</span>
          </div>
        </template>
        <JsonViewer 
          :value="parsedBannerData" 
          :expandDepth="2" 
          copyable 
          sort 
          boxed 
          theme="light"
          class="text-left"
        />
      </el-card>
    </div>
    
    <el-empty v-if="bannerList.length === 0" description="暂无Banner数据" />
    
    <div class="table-container">
    <el-table
      v-if="bannerList.length > 0"
      :data="bannerList"
      row-key="id"
      border
      style="width: 100%; max-width: 100%;"
    >
      <el-table-column label="序号" width="60">
        <template #default="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="图片" width="120">
        <template #default="scope">
          <el-image 
            v-if="scope.row.image" 
            :src="scope.row.image" 
            fit="cover"
            style="width: 80px; height: 50px"
            :preview-src-list="[scope.row.image]"
          >
            <template #error>
              <div class="image-error">加载失败</div>
            </template>
          </el-image>
          <span v-else>无图片</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="title" label="标题" min-width="120" />
      
      <el-table-column prop="link" label="链接" min-width="150" show-overflow-tooltip />
      
      <el-table-column prop="sort_order" label="排序" width="80" />
      
      <el-table-column label="状态" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="220">
        <template #default="scope">
          <el-button size="small" @click="editBannerItem(scope.row, scope.$index)">
            编辑
          </el-button>
          <el-button 
            size="small" 
            type="primary" 
            plain
            :disabled="scope.$index === 0"
            @click="moveBanner(scope.$index, scope.$index - 1)"
          >
            <el-icon><ArrowUp /></el-icon>
          </el-button>
          <el-button 
            size="small" 
            type="primary" 
            plain
            :disabled="scope.$index === bannerList.length - 1"
            @click="moveBanner(scope.$index, scope.$index + 1)"
          >
            <el-icon><ArrowDown /></el-icon>
          </el-button>
          <el-button size="small" type="danger" @click="removeBannerItem(scope.$index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>

    <!-- Banner编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editingIndex === -1 ? '添加Banner' : '编辑Banner'"
      width="550px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="currentBanner"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="图片" prop="image">
          <div class="image-uploader-container">
            <FileUploader
              action="/v1/admin/upload"
              :file-limit="1"
              :size-limit="2 * 1024 * 1024"
              accept=".jpg,.jpeg,.png"
              file-usage="banner"
              upload-style="button"
              button-text="选择图片"
              button-icon="Picture"
              button-type="primary"
              :show-file-list="false"
              @success="handleUploadSuccess"
            >
              <template #tip>
                <div class="image-uploader-tip">
                  建议尺寸: 750x350像素，格式：JPG/PNG，大小不超过2MB
                </div>
              </template>
            </FileUploader>
            
            <div v-if="currentBanner.image" class="preview-container">
              <img :src="currentBanner.image" class="uploaded-image" />
              <el-button type="danger" size="small" circle class="remove-image" @click="removeImage">
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="标题" prop="title">
          <el-input v-model="currentBanner.title" placeholder="请输入标题" />
        </el-form-item>
        
        <el-form-item label="链接" prop="link">
          <el-input v-model="currentBanner.link" placeholder="请输入链接地址" />
        </el-form-item>
        
        <el-form-item label="排序" prop="sort_order">
          <el-input-number 
            v-model="currentBanner.sort_order" 
            :min=1
            :max=999 
            style="width: 150px"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="currentBanner.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveBannerItem">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Plus, ArrowUp, ArrowDown, Close, View, Hide } from '@element-plus/icons-vue'
import { FileUploader } from '@/components/common'
import { JsonViewer } from 'vue3-json-viewer'
import 'vue3-json-viewer/dist/index.css'

// 定义Banner项接口
interface BannerItem {
  id: number
  image: string
  title: string
  link: string
  sort_order: number
  status: number
}

// 定义组件属性
const props = defineProps<{
  modelValue: string // 接收v-model绑定的值
}>()

// 定义事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

// 本地状态
const bannerList = ref<BannerItem[]>([])
const dialogVisible = ref(false)
const editingIndex = ref(-1)
const formRef = ref<FormInstance>()
const showJsonSource = ref(false) // 控制JSON源码显示

// 当前编辑的Banner项
const currentBanner = reactive<BannerItem>({
  id: 0,
  image: '',
  title: '',
  link: '',
  sort_order: 1,
  status: 1
})

// 表单验证规则
const rules = {
  image: [
    { required: true, message: '请上传Banner图片', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入Banner标题', trigger: 'blur' },
    { max: 50, message: '标题长度不能超过50个字符', trigger: 'blur' }
  ],
  link: [
    { required: true, message: '请输入链接地址', trigger: 'blur' }
  ],
  sort_order: [
    { required: true, message: '请设置排序值', trigger: 'change' }
  ]
}

// 确保每个Banner项都有唯一ID
const ensureBannerIds = () => {
  let maxId = 0
  bannerList.value.forEach(item => {
    if (!item.id) {
      item.id = Date.now() + Math.floor(Math.random() * 1000)
    }
    maxId = Math.max(maxId, Number(item.id))
  })
  
  // 保存当前最大ID值供新增使用
  return maxId
}

// 监听输入值变化
watch(
  () => props.modelValue,
  (newValue) => {
    try {
      if (newValue) {
        const parsedValue = JSON.parse(newValue)
        // 确保是数组
        if (Array.isArray(parsedValue)) {
          bannerList.value = parsedValue
          // 确保每个banner项都有id
          ensureBannerIds()
        } else {
          bannerList.value = []
        }
      } else {
        bannerList.value = []
      }
    } catch (e) {
      console.error('解析Banner JSON数据失败:', e)
      bannerList.value = []
    }
  },
  { immediate: true }
)

// 计算JSON数据用于显示
const parsedBannerData = computed(() => {
  return bannerList.value;
})

// 监听Banner列表变化，更新输出值
watch(
  bannerList,
  (newList) => {
    // 更新排序字段
    newList.forEach((item, index) => {
      item.sort_order = index + 1
    })
    
    // 发送更新事件
    emit('update:modelValue', JSON.stringify(newList))
  },
  { deep: true }
)

// 添加Banner项
const addBannerItem = () => {
  editingIndex.value = -1
  // 重置当前编辑的Banner
  Object.assign(currentBanner, {
    id: ensureBannerIds() + 1,
    image: '',
    title: '',
    link: '',
    sort_order: bannerList.value.length + 1,
    status: 1
  })
  
  dialogVisible.value = true
}

// 编辑Banner项
const editBannerItem = (item: BannerItem, index: number) => {
  editingIndex.value = index
  Object.assign(currentBanner, JSON.parse(JSON.stringify(item)))
  dialogVisible.value = true
}

// 保存Banner项
const saveBannerItem = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (editingIndex.value === -1) {
      // 添加新项
      bannerList.value.push({ ...currentBanner })
    } else {
      // 更新现有项
      bannerList.value[editingIndex.value] = { ...currentBanner }
    }
    
    dialogVisible.value = false
    ElMessage.success(editingIndex.value === -1 ? 'Banner添加成功' : 'Banner更新成功')
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 删除Banner项
const removeBannerItem = (index: number) => {
  bannerList.value.splice(index, 1)
  ElMessage.success('Banner删除成功')
}

// 移动Banner项调整顺序
const moveBanner = (fromIndex: number, toIndex: number) => {
  if (toIndex < 0 || toIndex >= bannerList.value.length) return
  
  const item = bannerList.value[fromIndex]
  bannerList.value.splice(fromIndex, 1)
  bannerList.value.splice(toIndex, 0, item)
}

// 处理文件上传成功
const handleUploadSuccess = (response: any, _file: any, _fileList: any[]) => {
  console.log('上传成功:', response)
  if (response && response.file_url) {
    // 设置当前编辑的Banner图片URL
    currentBanner.image = response.file_url
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败，请重试')
  }
}

// 移除当前图片
const removeImage = () => {
  currentBanner.image = ''
}
</script>

<style scoped>
.banner-config-editor {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: hidden;
}

.banner-toolbar {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

.json-source-viewer {
  margin-bottom: 15px;
}

.image-uploader-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.banner-image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.banner-image-uploader:hover {
  border-color: #409EFF;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 160px;
  height: 90px;
  line-height: 90px;
  text-align: center;
}

.uploaded-image {
  width: 160px;
  height: 90px;
  display: block;
  object-fit: cover;
}

.image-uploader-tip {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}

.image-error {
  width: 80px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 12px;
}

.preview-container {
  position: relative;
  margin-top: 10px;
}

.remove-image {
  position: absolute;
  top: 5px;
  right: 5px;
}

/* 添加JSON显示样式 */
.text-left {
  text-align: left;
}
</style>
