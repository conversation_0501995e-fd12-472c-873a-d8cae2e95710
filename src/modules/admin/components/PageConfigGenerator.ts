/**
 * @file PageConfigGenerator.ts
 * @description 页面配置生成器，用于创建标准化的页面配置对象
 * <AUTHOR> Assistant
 * @created 2025-03-30
 */

import { h } from 'vue';
import { ElEmpty } from 'element-plus';

/**
 * 表格列的值类型枚举
 */
export enum TableValueType {
  TEXT = 'text',
  TAG = 'tag',
  BADGE = 'badge',
  IMG = 'img',
  DATETIME = 'dateTime',
  DATE = 'date',
  TIME = 'time',
  MONEY = 'money',
  NUMBER = 'number',
  PERCENT = 'percent',
  CODE = 'code',
  LINK = 'link',
  OPTION = 'option',
  ENUM = 'enum',
  PROGRESS = 'progress',
  RATE = 'rate',
  AVATAR = 'avatar',
  SWITCH = 'switch',
  BUTTON = 'button'
}

/**
 * 表单项的值类型枚举
 */
export enum FormItemValueType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  PASSWORD = 'password',
  NUMBER = 'number',
  SELECT = 'select',
  DATE = 'date',
  DATETIME = 'dateTime',
  TIME = 'time',
  RADIO = 'radio',
  CHECKBOX = 'checkbox',
  SWITCH = 'switch',
  SLIDER = 'slider',
  UPLOAD = 'upload',
  CASCADER = 'cascader',
  TRANSFER = 'transfer',
  TREESELECT = 'treeSelect',
  EDITOR = 'editor',
  COLOR = 'color',
  RATE = 'rate'
}

/**
 * 用户状态枚举
 */
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  LOCKED = 'LOCKED',
  DISABLED = 'DISABLED'
}

/**
 * 表格列配置接口
 */
export interface TableColumn {
  label: string;
  prop: string;
  width?: number;
  minWidth?: number;
  valueType?: TableValueType;
  valueEnum?: Record<string, { text: string; status: string }>;
  fieldProps?: Record<string, any>;
  fieldSlots?: Record<string, Function>;
  hideInSearch?: boolean;
  hideInTable?: boolean;
  fixed?: 'left' | 'right';
  options?: { label: string; value: any }[];
  [key: string]: any;
}

/**
 * 表格动作按钮配置接口
 */
export interface TableActionButton {
  text: string;
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default';
  action: string;
  icon?: string;
  condition?: (row: any) => boolean;
  disabled?: (row: any) => boolean;
  onClick?: (row: any) => void;
}

/**
 * 表格操作列配置接口
 */
export interface TableActions {
  title: string;
  width: number;
  buttons: TableActionButton[];
  fixed?: 'left' | 'right';
}

/**
 * 表格分页配置接口
 */
export interface TablePagination {
  pageSize: number;
  pageSizes: number[];
  layout?: string;
  background?: boolean;
}

/**
 * 表格配置接口
 */
export interface TableConfig {
  columns: TableColumn[];
  actions?: TableActions;
  pagination?: TablePagination;
  showSelection?: boolean;
  showIndex?: boolean;
  showActions?: boolean;
  rowKey?: string;
  showPagination?: boolean;
  stripe?: boolean;
  border?: boolean;
  size?: 'medium' | 'small' | 'mini';
  height?: string | number;
  maxHeight?: string | number;
  headerCellStyle?: Record<string, any>;
  cellStyle?: Record<string, any>;
  rowStyle?: Record<string, any>;
}

/**
 * 表单项配置接口
 */
export interface FormItem {
  label: string;
  prop: string;
  required?: boolean;
  valueType?: FormItemValueType;
  rules?: any[];
  fieldProps?: Record<string, any>;
  options?: { label: string; value: any }[];
  defaultValue?: any;
  placeholder?: string;
  disabled?: boolean;
  hidden?: boolean;
  uploadProps?: {
    action: string;
    accept: string;
    fileLimit: number;
    sizeLimit: number;
    fileUsage: string;
    tip: string;
  };
  colProps?: {
    span?: number;
    offset?: number;
    push?: number;
    pull?: number;
  };
  [key: string]: any;
}

/**
 * 表单配置接口
 */
export interface FormConfig {
  columns: FormItem[];
  labelWidth?: number | string;
  labelPosition?: 'left' | 'right' | 'top';
  size?: 'medium' | 'small' | 'mini';
  gutter?: number;
  submitText?: string;
  cancelText?: string;
  showReset?: boolean;
  resetText?: string;
  inline?: boolean;
  width?: number | string;
  rules?: Record<string, any[]>;
}

/**
 * 详情查看配置接口
 */
export interface InfoConfig {
  columns: FormItem[];
  border?: boolean;
  column?: number;
  size?: 'large' | 'default' | 'small';
  direction?: 'horizontal' | 'vertical';
}

/**
 * 自定义操作配置接口
 */
export interface CustomAction {
  url: string;
  method: string;
  confirmMessage?: string;
  successMessage?: string;
  formatParams?: (params: any) => any;
  formatResponse?: (response: any) => any;
  onSuccess?: (response: any, params: any) => void;
  onError?: (error: any, params: any) => void;
}

/**
 * 服务配置接口
 */
export interface ServiceConfig {
  baseUrl: string;
  formatResponse?: (response: any) => any;
  customActions?: Record<string, CustomAction>;
  messages?: {
    addSuccess?: string;
    updateSuccess?: string;
    deleteConfirm?: string;
    deleteSuccess?: string;
    [key: string]: string | undefined;
  };
  addTitle?: string;
  editTitle?: string;
  viewTitle?: string;
  permissions?: {
    add?: string;
    edit?: string;
    delete?: string;
    view?: string;
    [key: string]: string | undefined;
  };
}

/**
 * 工具栏按钮配置接口
 */
export interface ToolbarButton {
  text: string;
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default';
  icon?: string;
  action: string;
  disabled?: boolean;
  hidden?: boolean;
  permission?: string;
  onClick?: () => void;
}

/**
 * 工具栏配置接口
 */
export interface ToolbarConfig {
  buttons: ToolbarButton[];
  showSelection?: boolean;
  selectionActions?: ToolbarButton[];
}

/**
 * 搜索配置接口
 */
export interface SearchConfig {
  columns: Partial<TableColumn>[];
  showReset?: boolean;
  showCollapse?: boolean;
  labelWidth?: number | string;
  labelPosition?: 'left' | 'right' | 'top';
  size?: 'medium' | 'small' | 'mini';
  collapsed?: boolean;
}

/**
 * 页面配置接口
 */
export interface PageConfig {
  version: string;
  title: string;
  serviceConfig: ServiceConfig;
  tableConfig: TableConfig;
  formConfig: FormConfig;
  toolbarConfig?: ToolbarConfig;
  searchConfig?: SearchConfig;
  infoConfig?: InfoConfig;
  keepAlive?: boolean;
}

/**
 * 用户接口
 */
export interface User {
  id: number | string;
  username: string;
  nickname?: string;
  avatar?: string;
  mobile?: string;
  email?: string;
  status: UserStatus;
  gender?: number;
  created_at?: string;
  last_login_at?: string;
  [key: string]: any;
}

/**
 * 字段结构描述接口
 */
export interface FieldStructure {
  description: string;
  type: string;
  validation?: string;
  [key: string]: any;
}

/**
 * 接口结构描述，键为字段名，值为字段结构描述
 */
export interface ApiStructure {
  [key: string]: FieldStructure;
}

/**
 * @class PageConfigGenerator
 * @description 页面配置生成器类，用于创建标准化的页面配置对象
 */
export class PageConfigGenerator {
  /**
   * 解析描述中的选项信息
   * 例如：将"状态：0-禁用，1-正常"解析为[{label: "禁用", value: 0}, {label: "正常", value: 1}]
   * 或者将"性别：0-未知，1-男，2-女"解析为[{label: "未知", value: 0}, {label: "男", value: 1}, {label: "女", value: 2}]
   * @param description 字段描述
   * @returns {object} 解析结果，包含纯净的标签和选项数组
   */
  static parseOptionsFromDescription(description: string): {
    cleanLabel: string;
    options: { label: string; value: any }[] | null;
  } {
    // 匹配格式如："状态：0-禁用，1-正常"或"性别：0-未知，1-男，2-女"
    const regex = /(.+)[:：](.+)/;
    const match = description.match(regex);
    
    if (!match) {
      return { cleanLabel: description, options: null };
    }
    
    const cleanLabel = match[1].trim();
    const optionsStr = match[2].trim();
    
    // 尝试不同模式的匹配
    
    // 1. 匹配所有的"数字-文本"对 (如：0-未知，1-男，2-女)
    const standardOptionsRegex = /(\d+|[A-Za-z_]+)-([^,，]+)/g;
    const options: { label: string; value: any }[] = [];
    let optionMatch;
    
    while ((optionMatch = standardOptionsRegex.exec(optionsStr)) !== null) {
      // 判断值是否为数字
      const rawValue = optionMatch[1];
      const value = /^\d+$/.test(rawValue) ? parseInt(rawValue) : rawValue;
      const label = optionMatch[2].trim();
      options.push({ label, value });
    }
    
    // 2. 如果没有匹配到标准格式，尝试匹配 "文本/文本/文本" 或 "文本、文本、文本" 格式
    if (options.length === 0) {
      const alternativeOptions = optionsStr.split(/[\/,，]/);
      if (alternativeOptions.length > 1) {
        alternativeOptions.forEach((option, index) => {
          const label = option.trim();
          options.push({ label, value: index });
        });
      }
    }
    
    return {
      cleanLabel,
      options: options.length > 0 ? options : null
    };
  }

  /**
   * 将选项数组转换为valueEnum格式
   * @param options 选项数组
   * @returns valueEnum格式的对象
   */
  static convertOptionsToValueEnum(options: { label: string; value: any }[] | null): Record<string, { text: string; status: string }> | undefined {
    if (!options || options.length === 0) {
      return undefined;
    }
    
    const valueEnum: Record<string, { text: string; status: string }> = {};
    
    options.forEach(option => {
      let status = 'info'; // 默认状态
      
      // 根据标签内容确定状态颜色
      if (option.label.includes('正常') || option.label.includes('启用') || option.label === '男') {
        status = 'success';
      } else if (option.label.includes('禁用') || option.label.includes('停用')) {
        status = 'danger';
      } else if (option.label.includes('锁定') || option.label.includes('警告') || option.label === '女') {
        status = 'warning';
      } else if (option.label.includes('未知') || option.label.includes('保密')) {
        status = 'info';
      } else if (typeof option.value === 'number') {
        // 如果是数值，根据数值大小确定颜色
        if (option.value === 0) status = 'info';
        else if (option.value === 1) status = 'success';
        else if (option.value === 2) status = 'warning';
        else status = 'info';
      }
      
      valueEnum[option.value] = { text: option.label, status };
    });
    
    return valueEnum;
  }

  /**
   * 获取字段类型的默认值
   * @param type 字段类型
   * @param options 可选项数组
   * @returns 对应类型的默认值
   */
  static getDefaultValueByType(type: FormItemValueType, options?: { label: string; value: any }[]): any {
    switch(type) {
      case FormItemValueType.TEXT:
      case FormItemValueType.TEXTAREA:
      case FormItemValueType.PASSWORD:
        return '';
      case FormItemValueType.NUMBER:
      case FormItemValueType.SLIDER:
      case FormItemValueType.RATE:
        return 0;
      case FormItemValueType.SWITCH:
        return false;
      case FormItemValueType.CHECKBOX:
      case FormItemValueType.TRANSFER:
        return [];
      case FormItemValueType.DATE:
      case FormItemValueType.DATETIME:
      case FormItemValueType.TIME:
        return '';
      case FormItemValueType.SELECT:
      case FormItemValueType.RADIO:
      case FormItemValueType.CASCADER:
      case FormItemValueType.TREESELECT:
        // 如果有选项，取第一个选项的值作为默认值
        if (options && options.length > 0) {
          return options[0].value;
        }
        return '';
      case FormItemValueType.UPLOAD:
        return [];
      case FormItemValueType.COLOR:
        return '#409EFF';
      case FormItemValueType.EDITOR:
        return '';
      default:
        return '';
    }
  }

  /**
   * 创建用户管理页面配置
   * @returns {PageConfig} 用户管理页面配置
   */
  static createUserPageConfig(): PageConfig {
    return {
      version: '1.0.0',
      title: '用户管理',
      serviceConfig: {
        baseUrl: 'v1/admin/secured/users',
        formatResponse: (response: any) => {
          return {
            data: response.list || [],
            page: response.page || 1,
            pageSize: response.pageSize || 10,
            total: response.total || 0
          };
        },
        customActions: {
          activate: {
            url: '/status',
            method: 'put',
            confirmMessage: '确定要激活该用户吗？',
            successMessage: '用户已激活',
            formatParams: (params: any) => {
              return {
                id: params.id,
                status: 'ACTIVE' as UserStatus
              };
            }
          },
          lock: {
            url: '/status',
            method: 'put',
            confirmMessage: '确定要锁定该用户吗？',
            successMessage: '用户已锁定',
            formatParams: (params: any) => {
              return {
                id: params.id,
                status: 'LOCKED' as UserStatus
              };
            }
          },
          disable: {
            url: '/status',
            method: 'put',
            confirmMessage: '确定要禁用该用户吗？',
            successMessage: '用户已禁用',
            formatParams: (params: any) => {
              return {
                id: params.id,
                status: 'DISABLED' as UserStatus
              };
            }
          }
        },
        messages: {
          addSuccess: '用户创建成功',
          updateSuccess: '用户更新成功',
          deleteConfirm: '确定要删除该用户吗？',
          deleteSuccess: '用户删除成功'
        },
        addTitle: '新增用户',
        editTitle: '编辑用户信息',
        viewTitle: '查看用户信息'
      },
      tableConfig: {
        columns: [
          { label: 'ID', prop: 'id', width: 80 },
          { label: '用户名', prop: 'username', width: 120 },
          { label: '昵称', prop: 'nickname', width: 120 },
          { 
            label: '头像', 
            prop: 'avatar', 
            width: 100,
            valueType: TableValueType.IMG,
            fieldProps: {
              style: {
                width: '100%'
              }
            },
            fieldSlots: {
              error: () => h(ElEmpty, { description: '图片坏了' })
            }
          },
          { label: '手机号', prop: 'mobile', width: 120 },
          { label: '邮箱', prop: 'email', minWidth: 180 },
          { 
            label: '状态', 
            prop: 'status', 
            width: 100,
            valueType: TableValueType.TAG,
            valueEnum: {
              ACTIVE: { text: '正常', status: 'success' },
              LOCKED: { text: '锁定', status: 'warning' },
              DISABLED: { text: '禁用', status: 'danger' }
            }
          },
          { 
            label: '注册时间', 
            prop: 'created_at', 
            width: 180,
            valueType: TableValueType.DATETIME,
            hideInSearch: true // 隐藏在搜索表单中
          },
          { 
            label: '最后登录时间', 
            prop: 'last_login_at', 
            width: 180,
            valueType: TableValueType.DATETIME,
            hideInSearch: true // 隐藏在搜索表单中
          }
        ],
        actions: {
          title: '操作',
          width: 200,
          buttons: [
            {
              text: '激活',
              type: 'success',
              action: 'activate',
              condition: (row: User) => row.status !== UserStatus.ACTIVE
            },
            {
              text: '锁定',
              type: 'warning',
              action: 'lock',
              condition: (row: User) => row.status === UserStatus.ACTIVE
            },
            {
              text: '禁用',
              type: 'danger',
              action: 'disable',
              condition: (row: User) => row.status !== UserStatus.DISABLED
            },
            {
              text: '编辑',
              type: 'primary',
              action: 'edit'
            }
          ]
        },
        pagination: {
          pageSize: 15,
          pageSizes: [10, 20, 30, 50]
        },
        showSelection: true,
        showIndex: true,
        showActions: true,
        rowKey: 'id',
        showPagination: true
      },
      formConfig: {
        columns: [
          {
            label: '用户名',
            prop: 'username',
            required: true,
            rules: [
              { required: true, message: '请输入用户名', trigger: 'blur' },
              { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
            ],
            fieldProps: {
              placeholder: '请输入用户名'
            }
          },
          {
            label: '密码',
            prop: 'password',
            required: false,
            valueType: FormItemValueType.PASSWORD,
            fieldProps: {
              placeholder: '请输入密码',
              showPassword: true
            },
            rules: [
              { required: false, message: '请输入密码', trigger: 'blur' },
              { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
            ]
          },
          {
            label: '昵称',
            prop: 'nickname',
            fieldProps: {
              placeholder: '请输入昵称'
            }
          },
          {
            label: '手机号',
            prop: 'mobile',
            fieldProps: {
              placeholder: '请输入手机号'
            },
            rules: [
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
            ]
          },
          {
            label: '邮箱',
            prop: 'email',
            fieldProps: {
              placeholder: '请输入邮箱'
            },
            rules: [
              { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
            ]
          },
          {
            label: '头像',
            prop: 'avatar',
            valueType: FormItemValueType.UPLOAD,
            uploadProps: {
              action: '/v1/admin/upload',
              accept: '.jpg,.jpeg,.png,.gif',
              fileLimit: 1,
              sizeLimit: 2 * 1024 * 1024,
              fileUsage: 'avatar',
              tip: '点击或拖拽图片到此区域上传头像'
            }
          },
          {
            label: '性别',
            prop: 'gender',
            valueType: FormItemValueType.RADIO,
            options: [
              { label: '男', value: 1 },
              { label: '女', value: 2 },
              { label: '保密', value: 0 }
            ],
            defaultValue: 1
          },
          {
            label: '状态',
            prop: 'status',
            valueType: FormItemValueType.RADIO,
            options: [
              { label: '正常', value: UserStatus.ACTIVE },
              { label: '锁定', value: UserStatus.LOCKED },
              { label: '禁用', value: UserStatus.DISABLED }
            ],
            defaultValue: UserStatus.ACTIVE
          }
        ],
        labelWidth: 80,
        labelPosition: 'right',
        rules: {}
      },
      toolbarConfig: {
        buttons: [
          {
            text: '新增用户',
            type: 'primary',
            icon: 'Plus',
            action: 'add'
          },
          {
            text: '刷新',
            type: 'primary',
            icon: 'Refresh',
            action: 'refresh'
          }
        ]
      },
      searchConfig: {
        columns: [
          { label: '用户名', prop: 'username' },
          { label: '昵称', prop: 'nickname' },
          { label: '手机号', prop: 'mobile' },
          { label: '邮箱', prop: 'email' },
          { label: '状态', prop: 'status' }
        ]
      },
      infoConfig: {
        columns: [
          { label: '用户名', prop: 'username' },
          { label: '昵称', prop: 'nickname' },
          { label: '手机号', prop: 'mobile' },
          { label: '邮箱', prop: 'email' },
          { label: '状态', prop: 'status' }
        ],
        border: true,
        column: 2,
        size: 'default',
        direction: 'horizontal'
      }
    };
  }

  /**
   * 根据API结构创建页面配置
   * @param {string} title - 页面标题
   * @param {string} baseUrl - 服务基础URL
   * @param {ApiStructure} structure - API结构描述
   * @param {object} options - 其他选项配置
   * @returns {PageConfig} 页面配置
   */
  static createConfigFromStructure(
    title: string,
    baseUrl: string,
    structure: ApiStructure,
    options: {
      customActions?: Record<string, CustomAction>;
      toolbarButtons?: ToolbarButton[];
      actionButtons?: TableActionButton[];
      messages?: Record<string, string>;
      statusField?: string;
      statusOptions?: { value: any; label: string; status: string }[];
      dateFields?: string[];
      imageFields?: string[];
      [key: string]: any;
    } = {}
  ): PageConfig {
    // 解析结构，生成表格列配置
    const tableColumns: TableColumn[] = [];
    const formItems: FormItem[] = [];
    const searchColumns: Partial<TableColumn>[] = [];
    const infoColumns: FormItem[] = [];
    
    // 集中管理的表单校验规则
    const formRules: Record<string, any[]> = {};
    
    // 需要从搜索中排除的字段
    const searchExcludeFields = ['password', 'new_password', 'page', 'pageSize', 'created_at', 'updated_at', 'last_login_at'];
    
    // 处理每个字段
    for (const [field, fieldStructure] of Object.entries(structure)) {
      // 排除分页字段
      if (field === 'page' || field === 'pageSize') {
        continue;
      }
      
      // 解析描述中的选项信息
      const { cleanLabel, options: descriptionOptions } = PageConfigGenerator.parseOptionsFromDescription(fieldStructure.description);
      
      // 基本列配置 - 使用处理后的干净标签
      const column: TableColumn = {
        label: cleanLabel,
        prop: field
      };
      
      // 根据字段类型设置宽度
      if (field === 'id') {
        column.width = 80;
      } else if (['username', 'nickname', 'mobile'].includes(field)) {
        column.width = 120;
      } else if (['email'].includes(field)) {
        column.minWidth = 180;
      } else if (['created_at', 'updated_at', 'last_login_at', 'birthday'].includes(field)) {
        column.width = 180;
      } else if (['avatar'].includes(field)) {
        column.width = 100;
      } else if (['status', 'gender', 'level'].includes(field)) {
        column.width = 100;
      }
      
      // 日期时间字段处理
      const dateFields = options.dateFields || ['created_at', 'updated_at', 'last_login_at', 'birthday'];
      if (dateFields.includes(field)) {
        column.valueType = TableValueType.DATETIME;
        column.hideInSearch = true;
      }
      
      // 图片字段处理
      const imageFields = options.imageFields || ['avatar'];
      if (imageFields.includes(field)) {
        column.valueType = TableValueType.IMG;
        column.fieldProps = {
          style: {
            width: '100%'
          }
        };
        column.fieldSlots = {
          error: () => h(ElEmpty, { description: '图片坏了' })
        };
      }
      
      // 如果从描述中解析出了选项，处理状态类字段
      if (descriptionOptions && descriptionOptions.length > 0) {
        column.valueType = TableValueType.TAG;
        
        // 创建valueEnum对象
        const valueEnum = PageConfigGenerator.convertOptionsToValueEnum(descriptionOptions);
        
        column.valueEnum = valueEnum;
        column.options = descriptionOptions; // 同时保存原始选项数组
      } else {
        // 状态字段处理
        const statusField = options.statusField || 'status';
        if (field === statusField) {
          column.valueType = TableValueType.TAG;
          
          // 默认状态选项
          const defaultStatusOptions = [
            { value: 'ACTIVE', label: '正常', status: 'success' },
            { value: 'LOCKED', label: '锁定', status: 'warning' },
            { value: 'DISABLED', label: '禁用', status: 'danger' }
          ];
          
          const statusOptions = options.statusOptions || defaultStatusOptions;
          
          // 创建valueEnum对象
          const valueEnum: Record<string, { text: string; status: string }> = {};
          for (const opt of statusOptions) {
            valueEnum[opt.value] = { text: opt.label, status: opt.status };
          }
          
          column.valueEnum = valueEnum;
          column.options = statusOptions; // 同时保存选项数组
        }
        
        // 金钱字段处理
        if (['price', 'balance', 'amount'].includes(field)) {
          column.valueType = TableValueType.MONEY;
        }
        
        // 百分比字段处理
        if (['discount', 'rate', 'percentage'].includes(field)) {
          column.valueType = TableValueType.PERCENT;
        }
      }
      
      // 将列添加到表格列配置中
      tableColumns.push(column);
      
      // 生成表单项配置
      if (!['id', 'created_at', 'updated_at', 'last_login_at'].includes(field)) {
        const formItem: FormItem = {
          label: cleanLabel, // 使用干净的标签
          prop: field
        };
        
        // 如果从描述中解析出了选项（如性别、状态等）
        if (descriptionOptions && descriptionOptions.length > 0) {
          // 确定表单项类型 - 如果选项少用单选，多选项用下拉
          formItem.valueType = descriptionOptions.length <= 3 ? 
            FormItemValueType.RADIO : 
            FormItemValueType.SELECT;
          
          formItem.options = descriptionOptions;
          
          // 设置默认值为第一个选项
          if (descriptionOptions.length > 0) {
            formItem.defaultValue = PageConfigGenerator.getDefaultValueByType(formItem.valueType, descriptionOptions);
          }
        } else {
          // 设置表单项类型
          if (field === 'password' || field === 'new_password') {
            formItem.valueType = FormItemValueType.PASSWORD;
            formItem.fieldProps = {
              placeholder: `请输入${fieldStructure.description}`,
              showPassword: true
            };
            formItem.defaultValue = PageConfigGenerator.getDefaultValueByType(FormItemValueType.PASSWORD);
          } else if (field === 'avatar') {
            formItem.valueType = FormItemValueType.UPLOAD;
            formItem.uploadProps = {
              action: '/v1/admin/upload',
              accept: '.jpg,.jpeg,.png,.gif',
              fileLimit: 1,
              sizeLimit: 2 * 1024 * 1024,
              fileUsage: 'avatar',
              tip: '点击或拖拽图片到此区域上传头像'
            };
            formItem.defaultValue = PageConfigGenerator.getDefaultValueByType(FormItemValueType.UPLOAD);
          } else if (field === 'gender') {
            formItem.valueType = FormItemValueType.RADIO;
            formItem.options = [
              { label: '男', value: 1 },
              { label: '女', value: 2 },
              { label: '保密', value: 0 }
            ];
            formItem.defaultValue = PageConfigGenerator.getDefaultValueByType(FormItemValueType.RADIO, formItem.options);
          } else if (field === options.statusField) {
            formItem.valueType = FormItemValueType.RADIO;
            
            // 默认状态选项
            const defaultStatusOptions = [
              { value: 'ACTIVE', label: '正常' },
              { value: 'LOCKED', label: '锁定' },
              { value: 'DISABLED', label: '禁用' }
            ];
            
            const statusOptions = options.statusOptions || defaultStatusOptions;
            formItem.options = statusOptions.map(opt => ({ label: opt.label, value: opt.value }));
            formItem.defaultValue = PageConfigGenerator.getDefaultValueByType(FormItemValueType.RADIO, formItem.options);
          } else if (['birthday', 'expire_date'].includes(field)) {
            formItem.valueType = FormItemValueType.DATE;
            formItem.defaultValue = PageConfigGenerator.getDefaultValueByType(FormItemValueType.DATE);
          } else if (['description', 'remark', 'content'].includes(field)) {
            formItem.valueType = FormItemValueType.TEXTAREA;
            formItem.defaultValue = PageConfigGenerator.getDefaultValueByType(FormItemValueType.TEXTAREA);
          } else if (['price', 'balance', 'amount', 'level', 'points'].includes(field)) {
            formItem.valueType = FormItemValueType.NUMBER;
            formItem.defaultValue = PageConfigGenerator.getDefaultValueByType(FormItemValueType.NUMBER);
          } else {
            // formItem.valueType = FormItemValueType.TEXT;
            formItem.defaultValue = PageConfigGenerator.getDefaultValueByType(FormItemValueType.TEXT);
          }
        }
        
        // 设置字段属性
        if (!formItem.fieldProps) {
          formItem.fieldProps = {};
        }
        
        if (!formItem.fieldProps.placeholder) {
          formItem.fieldProps.placeholder = `请输入${fieldStructure.description}`;
        }
        
        // 解析验证规则并集中管理
        if (fieldStructure.validation) {
          const rules: any[] = [];
          const validations = fieldStructure.validation.split(';');
          
          for (const validation of validations) {
            if (validation === 'Required') {
              rules.push({ required: true, message: `请输入${cleanLabel}`, trigger: 'blur' });
              //formItem.required = true;
            } else if (validation.startsWith('MinSize')) {
              const min = parseInt(validation.match(/\((\d+)/)?.[1] || '0');
              if (min > 0) {
                rules.push({ min, message: `${cleanLabel}长度不能少于${min}个字符`, trigger: 'blur' });
              }
            } else if (validation.startsWith('MaxSize')) {
              const max = parseInt(validation.match(/\((\d+)/)?.[1] || '0');
              if (max > 0) {
                rules.push({ max, message: `${cleanLabel}长度不能超过${max}个字符`, trigger: 'blur' });
              }
            } else if (validation === 'Email') {
              rules.push({ type: 'email', message: `请输入正确的${cleanLabel}`, trigger: 'blur' });
            } else if (validation === 'Mobile') {
              rules.push({ pattern: /^1[3-9]\d{9}$/, message: `请输入正确的${cleanLabel}`, trigger: 'blur' });
            } else if (validation.startsWith('Range')) {
              const matches = validation.match(/\((\d+),(\d+)\)/);
              if (matches) {
                const min = parseInt(matches[1]);
                const max = parseInt(matches[2]);
                rules.push({ min, max, message: `${cleanLabel}必须在${min}到${max}之间`, trigger: 'blur' });
              }
            }
          }
          
          if (rules.length > 0) {
            // 将规则添加到集中管理的规则对象中
            formRules[field] = rules;
            // 标记字段为必填，但不直接挂载规则
            // if (rules.some(rule => rule.required)) {
            //   formItem.required = true;
            // }
          }
        }
        
        // 将表单项添加到表单配置中
        formItems.push(formItem);
        
        // 创建详情列的副本
        const infoItem = { ...formItem };
        // 为详情页面设置特定的属性
        if (column.valueType === TableValueType.TAG && column.valueEnum) {
          infoItem.valueType = FormItemValueType.RADIO; // 在详情页面使用单选形式展示
          infoItem.options = column.options; // 使用相同的选项
        }
        
        // 将表单项添加到详情配置中
        infoColumns.push(infoItem);
      }
      
      // 生成搜索列配置
      if (!searchExcludeFields.includes(field) && !column.hideInSearch) {
        const searchColumn: Partial<TableColumn> = {
          label: cleanLabel,
          prop: field
        };
        
        // 如果有选项数据，设置为下拉选择
        if (descriptionOptions && descriptionOptions.length > 0) {
          searchColumn.valueType = TableValueType.OPTION;
          searchColumn.options = descriptionOptions;
        }
        
        searchColumns.push(searchColumn);
      }
    }
    
    // 默认的操作按钮
    const defaultActionButtons: TableActionButton[] = [
      {
        text: '编辑',
        type: 'primary',
        action: 'edit'
      }
    ];
    
    // 如果有状态字段，添加状态相关的操作按钮
    if (structure[options.statusField || 'status']) {
      defaultActionButtons.unshift(
        {
          text: '激活',
          type: 'success',
          action: 'activate',
          condition: (row: any) => row[options.statusField || 'status'] !== 'ACTIVE' && row[options.statusField || 'status'] !== 1
        },
        {
          text: '锁定',
          type: 'warning',
          action: 'lock',
          condition: (row: any) => row[options.statusField || 'status'] === 'ACTIVE' || row[options.statusField || 'status'] === 1
        },
        {
          text: '禁用',
          type: 'danger',
          action: 'disable',
          condition: (row: any) => row[options.statusField || 'status'] !== 'DISABLED' && row[options.statusField || 'status'] !== 0
        }
      );
    }
    
    const actionButtons = options.actionButtons || defaultActionButtons;
    
    // 默认的工具栏按钮
    const defaultToolbarButtons: ToolbarButton[] = [
      {
        text: `新增${title}`,
        type: 'primary',
        icon: 'Plus',
        action: 'add'
      },
      {
        text: '刷新',
        type: 'primary',
        icon: 'Refresh',
        action: 'refresh'
      }
    ];
    
    const toolbarButtons = options.toolbarButtons || defaultToolbarButtons;
    
    // 默认的消息提示
    const defaultMessages = {
      addSuccess: `${title}创建成功`,
      updateSuccess: `${title}更新成功`,
      deleteConfirm: `确定要删除该${title}吗？`,
      deleteSuccess: `${title}删除成功`
    };
    
    const messages = { ...defaultMessages, ...options.messages };
    
    // 默认的自定义操作
    const defaultCustomActions: Record<string, CustomAction> = {};
    
    // 如果有状态字段，添加状态相关的自定义操作
    if (structure[options.statusField || 'status']) {
      const field = options.statusField || 'status';
      // 获取状态字段的选项
      const { options: statusOptions } = PageConfigGenerator.parseOptionsFromDescription(structure[field]?.description || '');
      
      // 如果从描述中解析出了选项，使用这些选项来配置操作
      if (statusOptions && statusOptions.length > 0) {
        // 找出表示"正常"状态的值
        const normalOption = statusOptions.find(opt => opt.label.includes('正常') || opt.label.includes('启用'));
        const normalValue = normalOption ? normalOption.value : 1;
        
        // 找出表示"禁用"状态的值
        const disabledOption = statusOptions.find(opt => opt.label.includes('禁用') || opt.label.includes('停用'));
        const disabledValue = disabledOption ? disabledOption.value : 0;
        
        defaultCustomActions.activate = {
          url: '/status',
          method: 'put',
          confirmMessage: `确定要激活该${title}吗？`,
          successMessage: `${title}已激活`,
          formatParams: (params: any) => {
            return {
              id: params.id,
              [field]: normalValue
            };
          }
        };
        
        defaultCustomActions.disable = {
          url: '/status',
          method: 'put',
          confirmMessage: `确定要禁用该${title}吗？`,
          successMessage: `${title}已禁用`,
          formatParams: (params: any) => {
            return {
              id: params.id,
              [field]: disabledValue
            };
          }
        };
      } else {
        // 使用默认值
        defaultCustomActions.activate = {
          url: '/status',
          method: 'put',
          confirmMessage: `确定要激活该${title}吗？`,
          successMessage: `${title}已激活`,
          formatParams: (params: any) => {
            return {
              id: params.id,
              [field]: 'ACTIVE'
            };
          }
        };
        
        defaultCustomActions.lock = {
          url: '/status',
          method: 'put',
          confirmMessage: `确定要锁定该${title}吗？`,
          successMessage: `${title}已锁定`,
          formatParams: (params: any) => {
            return {
              id: params.id,
              [field]: 'LOCKED'
            };
          }
        };
        
        defaultCustomActions.disable = {
          url: '/status',
          method: 'put',
          confirmMessage: `确定要禁用该${title}吗？`,
          successMessage: `${title}已禁用`,
          formatParams: (params: any) => {
            return {
              id: params.id,
              [field]: 'DISABLED'
            };
          }
        };
      }
    }
    
    const customActions = { ...defaultCustomActions, ...options.customActions };
    
    // 生成最终的页面配置
    return {
      version: '1.0.0',
      title,
      serviceConfig: {
        baseUrl,
        formatResponse: options.formatResponse || ((response: any) => {
          return {
            data: response.list || response.data || [], 
            total: response.total || 0 
          };
        }),
        customActions,
        messages,
        addTitle: options.addTitle || `新增${title}`,
        editTitle: options.editTitle || `编辑${title}信息`,
        viewTitle: options.viewTitle || `查看${title}信息`
      },
      tableConfig: {
        columns: tableColumns,
        actions: actionButtons.length > 0 ? {
          title: '操作',
          width: options.actionsWidth || 200,
          buttons: actionButtons
        } : undefined,
        pagination: options.pagination || {
          pageSize: 15,
          pageSizes: [10, 20, 30, 50]
        },
        showSelection: options.showSelection !== undefined ? options.showSelection : true,
        showIndex: options.showIndex !== undefined ? options.showIndex : true,
        showActions: options.showActions !== undefined ? options.showActions : true,
        rowKey: options.rowKey || 'id',
        showPagination: options.showPagination !== undefined ? options.showPagination : true
      },
      formConfig: {
        columns: formItems,
        labelWidth: options.labelWidth || 80,
        labelPosition: options.labelPosition || 'right',
        rules: formRules // 使用集中管理的规则对象
      },
      toolbarConfig: toolbarButtons ? {
        buttons: toolbarButtons
      } : undefined,
      searchConfig: searchColumns ? {
        columns: searchColumns
      } : undefined,
      infoConfig: infoColumns ? {
        columns: infoColumns,
        border: options.infoBorder !== false,
        column: options.infoColumn || 2,
        size: options.infoSize || 'default',
        direction: options.infoDirection || 'horizontal'
      } : undefined
    };
  }

  /**
   * 创建自定义页面配置
   * @param {string} title - 页面标题
   * @param {string} baseUrl - 服务基础URL
   * @param {TableColumn[]} tableColumns - 表格列配置
   * @param {FormItem[]} formItems - 表单项配置
   * @param {object} options - 其他选项
   * @returns {PageConfig} 自定义页面配置
   */
  static createCustomPageConfig(
    title: string,
    baseUrl: string,
    tableColumns: TableColumn[],
    formItems: FormItem[],
    options: {
      customActions?: Record<string, CustomAction>;
      toolbarButtons?: ToolbarButton[];
      searchColumns?: Partial<TableColumn>[];
      actionButtons?: TableActionButton[];
      messages?: Record<string, string>;
      infoColumns?: FormItem[];
      [key: string]: any;
    } = {}
  ): PageConfig {
    const {
      customActions,
      toolbarButtons,
      searchColumns,
      actionButtons = [],
      messages,
      infoColumns,
    } = options;

    // 生成集中管理的规则对象
    const formRules: Record<string, any[]> = {};
    
    // 从formItems中提取规则
    formItems.forEach(item => {
      // 安全地检查rules属性
      const rules = item.rules;
      if (rules && Array.isArray(rules) && rules.length > 0) {
        // 复制规则到集中管理的规则对象
        formRules[item.prop] = [...rules];
        
        // 检查是否有必填规则，保留required标记
        const hasRequired = rules.some((rule: any) => rule.required === true);
        if (hasRequired) {
          item.required = true;
        }
        
        // 移除字段中的规则定义
        delete item.rules;
      }
    });

    // 返回完整配置
    return {
      version: '1.0',
      title,
      serviceConfig: {
        baseUrl,
        // 响应格式化
        formatResponse: options.formatResponse || ((response: any) => ({ 
          data: response.list || response.data || [], 
          total: response.total || 0 
        })),
        customActions,
        messages: messages || {
          addSuccess: '添加成功',
          updateSuccess: '更新成功',
          deleteConfirm: '确定要删除吗？',
          deleteSuccess: '删除成功'
        },
        addTitle: options.addTitle || '新增',
        editTitle: options.editTitle || '编辑',
        viewTitle: options.viewTitle || '查看详情'
      },
      tableConfig: {
        columns: tableColumns,
        actions: actionButtons.length > 0 ? {
          title: '操作',
          width: options.actionWidth || 150,
          buttons: actionButtons
        } : undefined,
        showSelection: options.showSelection !== false,
        showIndex: options.showIndex !== false,
        showActions: true,
        rowKey: options.rowKey || 'id',
        showPagination: options.showPagination !== false,
        pagination: {
          pageSize: options.pageSize || 10,
          pageSizes: options.pageSizes || [10, 20, 30, 50]
        }
      },
      formConfig: {
        columns: formItems,
        labelWidth: options.labelWidth || 100,
        labelPosition: options.labelPosition || 'right',
        width: options.formWidth || '500px',
        rules: formRules
      },
      toolbarConfig: toolbarButtons ? {
        buttons: toolbarButtons
      } : undefined,
      searchConfig: searchColumns ? {
        columns: searchColumns,
        labelWidth: options.searchLabelWidth || 100
      } : undefined,
      infoConfig: infoColumns ? {
        columns: infoColumns,
        border: options.infoBorder !== false,
        column: options.infoColumn || 2,
        size: options.infoSize || 'default',
        direction: options.infoDirection || 'horizontal'
      } : undefined
    };
  }
}

export default PageConfigGenerator;
