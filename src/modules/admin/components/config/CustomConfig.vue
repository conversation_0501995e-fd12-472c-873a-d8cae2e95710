<!-- 
  @description CustomConfig - 自定义内容组件配置
  <AUTHOR> AI
  @date 2025-04-13
-->
<template>
  <el-form label-width="100px">
    <el-form-item label="标题">
      <el-input v-model="localContent.content.title" placeholder="请输入标题" @input="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="图标">
      <IconSelector v-model="localContent.content.icon" @update:modelValue="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="显示标题">
      <el-switch v-model="localContent.content.showTitle" @change="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="内容类型">
      <el-radio-group v-model="contentType" @change="handleContentTypeChange">
        <el-radio :value="'html'">HTML内容</el-radio>
        <el-radio :value="'component'">组件</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <!-- HTML内容配置 -->
    <template v-if="contentType === 'html'">
      <el-form-item label="HTML内容">
        <div class="editor-container">
          <Toolbar
            style="border-bottom: 1px solid #ccc"
            :editor="editorRef"
            :defaultConfig="toolbarConfig"
            :mode="mode"
          />
          <Editor
            style="height: 300px; overflow-y: hidden;"
            v-model="localContent.content.config.html"
            :defaultConfig="editorConfig"
            :mode="mode"
            @onChange="handleEditorChange"
            @onCreated="handleCreated"
          />
        </div>
      </el-form-item>
    </template>
    
    <!-- 组件配置 -->
    <template v-if="contentType === 'component'">
      <el-form-item label="组件名称">
        <el-input 
          v-model="localContent.content.config.component" 
          placeholder="请输入组件名称"
          @input="emitUpdate" 
        />
      </el-form-item>
      
      <el-form-item label="组件属性">
        <el-input 
          v-model="propsText" 
          type="textarea" 
          :rows="6" 
          placeholder="请输入JSON格式的组件属性"
          @input="updateComponentProps" 
        />
      </el-form-item>
    </template>
    
    <el-form-item label="透明度">
      <el-slider 
        v-model="opacity" 
        :min="0" 
        :max="1" 
        :step="0.1"
        @change="emitUpdate"
      />
    </el-form-item>
    
    <el-form-item label="主题模式">
      <el-radio-group v-model="localContent.content.config.themeMode" @change="emitUpdate">
        <el-radio value="light">亮色</el-radio>
        <el-radio value="dark">暗色</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="功能设置">
      <el-checkbox v-model="localContent.content.config.refreshable" @change="emitUpdate">可刷新</el-checkbox>
      <el-checkbox v-model="localContent.content.config.configurable" @change="emitUpdate">可配置</el-checkbox>
      <el-checkbox v-model="localContent.content.config.editable" @change="emitUpdate">可编辑</el-checkbox>
      <el-checkbox v-model="localContent.content.config.closable" @change="emitUpdate">可关闭</el-checkbox>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
/**
 * CustomConfig组件 - 配置自定义内容组件
 * 可以配置HTML内容或动态组件
 */
import { ref, watch, onBeforeUnmount, shallowRef, nextTick, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import IconSelector from '@/components/page/IconSelector.vue';
import type { AdminGridInfoDTO, GridItemContent } from '../../types';
import '@wangeditor/editor/dist/css/style.css';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import EditorUploader, { type InsertImageFnType, type InsertVideoFnType } from '@/components/common/EditorUploader';

// 组件属性
const props = defineProps({
  store: {
    type: Object,
    required: true
  }
});

// 组件事件
const emit = defineEmits(['update:content']);

// store实例
const store = props.store;

// 初始化空的默认内容，防止undefined错误
const defaultContent: GridItemContent = {
  type: 'custom',
  title: '',
  icon: '',
  showTitle: true,
  refreshable: true,
  configurable: true,
  editable: true,
  closable: true,
  config: {},
  customStyle: { opacity: '1' }
};

// 使用本地数据，避免直接修改props
// 重要：初始化为包含默认content的对象，防止空引用错误
const localContent = ref<AdminGridInfoDTO>({
  id: -1,
  name: '',
  uiConfigId: 0,
  status: 1,
  api: '',
  remark: '',
  step: [0],
  position: { i: 'temp', x: 0, y: 0, w: 6, h: 4, static: false, isDraggable: true, isResizable: true, isBounded: false },
  content: { ...defaultContent },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
});

// 内容类型：html 或 component
const contentType = ref('html');

// 组件属性文本
const propsText = ref('{}');

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();
// 编辑器内容
const editorValue = ref('');
// 编辑器模式
const mode = ref('default');

// 编辑器配置
const editorConfig = {
  placeholder: '请输入内容...',
  MENU_CONF: {
    // 图片上传配置
    uploadImage: {
      // 自定义上传
      customUpload(file: File, insertFn: InsertImageFnType) {
        EditorUploader.uploadImage(file, insertFn);
      },
      // 小于该值就插入base64格式
      base64LimitSize: 5 * 1024, // 5kb
    },
    
    // 视频上传配置
    uploadVideo: {
      // 自定义上传
      customUpload(file: File, insertFn: InsertVideoFnType) {
        EditorUploader.uploadVideo(file, insertFn);
      },
      // 视频文件大小限制
      maxFileSize: 50 * 1024 * 1024, // 50MB
    }
  }
};
// 工具栏配置
const toolbarConfig = {
  excludeKeys: [],
};

// 保证content/config结构完整，避免渲染异常
/**
 * 确保内容结构完整性，避免渲染异常
 * 同时处理嵌套content的问题
 * @param obj AdminGridInfoDTO对象
 */
function ensureContentStructure(obj: AdminGridInfoDTO) {
  console.log('[CustomConfig] ensureContentStructure', obj);
  if (!obj.content) obj.content = {} as any;
  
  // 处理嵌套content问题 - 检查是否存在循环嵌套的content
  // 使用类型断言绕过TypeScript的类型检查
  const contentAny = obj.content as any;
  if (contentAny && typeof contentAny === 'object' && contentAny.content) {
    console.log('[CustomConfig] 检测到嵌套content，正在修复...');
    // 提取内层content
    const innerContent = contentAny.content;
    // 合并配置
    const mergedConfig = { 
      ...contentAny.config,
      ...(innerContent.config || {}) 
    };
    // 更新content，避免嵌套
    obj.content = {
      ...innerContent,
      config: mergedConfig
    };
    console.log('[CustomConfig] 修复后的content:', obj.content);
  }
  
  // 确保各字段存在
  if (!obj.content.config) obj.content.config = {};
  if (typeof obj.content.config.html !== 'string') obj.content.config.html = '';
  if (typeof obj.content.config.component !== 'string') obj.content.config.component = '';
  if (typeof obj.content.config.props !== 'object') obj.content.config.props = {};
  
  // 主题模式、功能设置也可做兼容
  if (typeof obj.content.config.themeMode !== 'string') obj.content.config.themeMode = 'light';
  if (typeof obj.content.config.refreshable !== 'boolean') obj.content.config.refreshable = true;
  if (typeof obj.content.config.configurable !== 'boolean') obj.content.config.configurable = true;
  if (typeof obj.content.config.editable !== 'boolean') obj.content.config.editable = true;
  if (typeof obj.content.config.closable !== 'boolean') obj.content.config.closable = true;
  if (!obj.content.customStyle) obj.content.customStyle = { opacity: "1" };
}

// 编辑器创建时的回调函数
function handleCreated(editor: any) {
  editorRef.value = editor;
  // 添加null检查和默认值
  if (localContent.value?.content?.config?.html) {
    editorValue.value = localContent.value.content.config.html;
  } else {
    editorValue.value = '';
  }
}

// 编辑器内容变化时的回调函数
function handleEditorChange(editor: any) {
  // 获取HTML值
  const html = editor.getHtml();
  // 更新配置
  localContent.value.content.config.html = html;
  // 发送更新
  emitUpdate();
}

// 处理内容类型变更
function handleContentTypeChange(value: string) {
  if (value === 'html') {
    // 切换到HTML类型，清除组件配置
    localContent.value.content.config.component = undefined;
    localContent.value.content.config.props = undefined;
    if (!localContent.value.content.config.html) {
      localContent.value.content.config.html = '';
    }
  } else if (value === 'component') {
    // 切换到组件类型，清除HTML配置
    localContent.value.content.config.html = undefined;
    if (!localContent.value.content.config.component) {
      localContent.value.content.config.component = '';
    }
    if (!localContent.value.content.config.props) {
      localContent.value.content.config.props = {};
    }
  }
  emitUpdate();
}

// 更新组件属性
function updateComponentProps() {
  try {
    localContent.value.content.config.props = JSON.parse(propsText.value);
    emitUpdate();
  } catch (error) {
    ElMessage.error('组件属性格式错误，请检查JSON格式');
  }
}

// 向父组件发送更新事件
function emitUpdate() {
  // 只发送必要的content字段，避免循环嵌套
  const contentToEmit = {
    ...localContent.value,
    content: {
      type: localContent.value.content.type,
      title: localContent.value.content.title,
      icon: localContent.value.content.icon,
      showTitle: localContent.value.content.showTitle,
      refreshable: localContent.value.content.config?.refreshable ?? true,
      configurable: localContent.value.content.config?.configurable ?? true,
      editable: localContent.value.content.config?.editable ?? true,
      closable: localContent.value.content.config?.closable ?? true,
      config: localContent.value.content.config || {},
      customStyle: localContent.value.content.customStyle || { opacity: '1' }
    }
  };
  emit('update:content', JSON.parse(JSON.stringify(contentToEmit)));
}

// 透明度处理
const opacity = ref(1);

// 初始化透明度
watch(() => localContent.value?.content?.customStyle, (newStyle) => {
  if (newStyle && newStyle.opacity) {
    opacity.value = parseFloat(newStyle.opacity);
  } else {
    opacity.value = 1;
  }
}, { immediate: true, deep: true });

/**
 * 创建默认的网格项模板
 * @returns 默认的网格项对象
 */
function createDefaultGridItem(): AdminGridInfoDTO {
  // 生成随机字符串作为网格项的i标识
  const randomId = `item_${Math.random().toString(36).substring(2, 9)}`;
  
  return {
    id: store.currentId, // 使用当前的负数ID作为临时ID
    name: '',
    uiConfigId: store.selectedUiConfig?.id || 0,
    step: [0], // 默认步骤为0
    status: 1, // 默认启用
    api: '',
    remark: '',
    position: {
      i: randomId,         // GridLayout 必需的标识字段
      static: false,      // 默认不锁定位置
      isDraggable: true,  // 默认可拖动
      isResizable: true,  // 默认可调整大小
      isBounded: false,   // 默认不受限制
      x: 0,               // 默认位置 x
      y: 0,               // 默认位置 y
      w: 6,               // 默认宽度
      h: 4                // 默认高度
    },
    content: {
      type: 'custom',      // 默认类型为自定义
      title: '',          // 默认标题为空
      icon: '',           // 默认无图标
      showTitle: true,    // 默认显示标题
      refreshable: true,  // 默认可刷新
      configurable: true, // 默认可配置
      editable: true,     // 默认可编辑
      closable: true,     // 默认可关闭
      config: {}          // 默认空配置
    },
    created_at: new Date().toISOString(), // 创建时间
    updated_at: new Date().toISOString()  // 更新时间
  };
}

/**
 * 初始化网格项数据
 */
const initGridItemData = () => {
  console.log('[CustomConfig] initGridItemData开始执行，currentId:', store.currentId);
  
  // 处理负数ID（新建模式）
  if (store.currentId < 0) {
    console.log('[CustomConfig] 检测到负数ID，创建默认网格项');
    localContent.value = createDefaultGridItem();
    ensureContentStructure(localContent.value);
    console.log('[CustomConfig] 创建了默认网格项:', localContent.value);
    return;
  }
  
  if(!store.gridLayoutStore.structureData || !store.gridLayoutStore.structureData.grid_items) {
    console.log('[CustomConfig] 结构数据不存在');
    return;
  }
  
  // 根据id查找对应的grid item
  const foundItem = store.gridLayoutStore.structureData.grid_items.find(
    (item: AdminGridInfoDTO) => String(item.id) === String(store.currentId)
  );
  
  if (!foundItem) {
    console.log('[CustomConfig] 找不到匹配的grid item, currentId:', store.currentId);
    return;
  }
  
  // 深拷贝避免引用问题
  const clonedItem = JSON.parse(JSON.stringify(foundItem));
  
  // 添加日志用于调试数据结构
  console.log('[CustomConfig] 原始item数据结构:', JSON.stringify(clonedItem, null, 2));
  
  // 保证结构完整并处理可能的嵌套content问题
  localContent.value = clonedItem;
  ensureContentStructure(localContent.value);
  
  console.log('[CustomConfig] 处理后的currentGridItem:', localContent.value);
}

watch(() => store.currentId, async (newId, oldId) => {
  console.log('[CustomConfig] currentId变化:', { oldId, newId });
  await nextTick();
  initGridItemData();
  console.log('[CustomConfig] watch回调后 localContent:', localContent.value);
}, { deep: true, immediate: true });

// 组件挂载时初始化
onMounted(() => {
  console.log('[CustomConfig] 组件挂载，初始化数据');
  // 确保localContent已初始化，防止模板渲染错误
  if (!localContent.value || !localContent.value.content) {
    initGridItemData();
  } else {
    // 即使已经初始化，也需要确保结构正确
    ensureContentStructure(localContent.value);
  }
  
  // 设置初始内容类型
  if (localContent.value?.content?.config?.html) {
    contentType.value = 'html';
  } else if (localContent.value?.content?.config?.component) {
    contentType.value = 'component';
    // 初始化组件属性文本
    if (localContent.value?.content?.config?.props) {
      propsText.value = JSON.stringify(localContent.value.content.config.props, null, 2);
    }
  }
});

// 监听透明度变化，更新localContent
watch(opacity, (val) => {
  if (!localContent.value?.content?.customStyle) {
    localContent.value.content.customStyle = {};
  }
  localContent.value.content.customStyle.opacity = val.toString();
  emitUpdate();
});

// 组件销毁前，销毁编辑器实例
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});
</script>

<style scoped>
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.gap-2 {
  gap: 0.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.editor-container {
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: hidden;
}
</style>