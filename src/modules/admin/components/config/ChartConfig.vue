<template>
  <el-form label-width="100px">
    <el-form-item label="标题">
      <el-input v-model="localContent.content.title" placeholder="请输入标题" @input="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="图标">
      <IconSelector v-model="localContent.content.icon" @update:modelValue="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="显示标题">
      <el-switch v-model="localContent.content.showTitle" @change="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="图表类型">
      <el-select v-model="localContent.content.config.chartType" @change="emitUpdate">
        <el-option label="柱状图" value="bar"/>
        <el-option label="折线图" value="line"/>
        <el-option label="饼图" value="pie"/>
      </el-select>
    </el-form-item>
    
    <el-form-item label="数据源">
      <el-radio-group v-model="localContent.content.config.dataSource" @change="emitUpdate">
        <el-radio value="static">静态数据</el-radio>
        <el-radio value="api">API数据</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="透明度">
      <el-slider v-model="opacity" :min="0" :max="1" :step="0.1" @change="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="主题模式">
      <el-radio-group v-model="localContent.content.config.themeMode" @change="emitUpdate">
        <el-radio value="light">亮色</el-radio>
        <el-radio value="dark">暗色</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="功能设置">
      <el-checkbox v-model="localContent.content.config.refreshable" @change="emitUpdate">可刷新</el-checkbox>
      <el-checkbox v-model="localContent.content.config.configurable" @change="emitUpdate">可配置</el-checkbox>
      <el-checkbox v-model="localContent.content.config.editable" @change="emitUpdate">可编辑</el-checkbox>
      <el-checkbox v-model="localContent.content.config.closable" @change="emitUpdate">可关闭</el-checkbox>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from 'vue';
import IconSelector from '@/components/page/IconSelector.vue';
import type { AdminGridInfoDTO } from '../../types';

const props = defineProps({
  store: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:content']);

// store实例
const store = props.store;
// 使用本地数据，避免直接修改props
const localContent = ref<AdminGridInfoDTO>(store.defaultGridItem);

// 组件挂载时确保数据结构
onMounted(() => {
  ensureDataStructure();
});

// 确保必要的数据结构存在
function ensureDataStructure() {
  // 确保config.chartType和数据源存在
  if (!localContent.value.content.config) {
    localContent.value.content.config = {};
  }
  if (!localContent.value.content.config.chartType) {
    localContent.value.content.config.chartType = 'bar';
  }
  if (!localContent.value.content.config.dataSource) {
    localContent.value.content.config.dataSource = 'static';
  }
  if (!localContent.value.content.config.data) {
    localContent.value.content.config.data = {
      xAxis: {
        data: ['类别1', '类别2', '类别3', '类别4', '类别5']
      },
      series: [{
        name: '系列1',
        type: 'bar',
        data: [10, 52, 200, 334, 390]
      }]
    };
  }
  
  // 确保自定义样式存在
  if (!localContent.value.content.customStyle) {
    localContent.value.content.customStyle = { opacity: "1" };
  }
}

// 向父组件发送更新事件
function emitUpdate() {
  emit('update:content', JSON.parse(JSON.stringify(localContent.value)));
}

// 透明度处理
const opacity = ref(1);

// 初始化透明度
watch(() => localContent.value.content.customStyle, (newStyle) => {
  if (newStyle && newStyle.opacity) {
    opacity.value = parseFloat(newStyle.opacity);
  } else {
    opacity.value = 1;
  }
}, { immediate: true, deep: true });

// 监听透明度变化，更新localContent
watch(opacity, (val) => {
  if (!localContent.value.content.customStyle) {
    localContent.value.content.customStyle = {};
  }
  localContent.value.content.customStyle.opacity = val.toString();
  emitUpdate();
});
const initGridItemData = () => {
  if(!store.gridLayoutStore.structureData || !store.gridLayoutStore.structureData.grid_items) return;
  console.log('[GridItemDialog] initGridItemData', store.gridLayoutStore.structureData, store.currentId,store.gridLayoutStore.structureData.grid_items);
    // 根据id查找对应的grid item
    localContent.value = store.gridLayoutStore.structureData.grid_items.find(
      (item: AdminGridInfoDTO) => String(item.id) === String(store.currentId)
    );
  if (!localContent.value) return;
  //ensureContentStructure(localContent.value); // 新增：保证结构完整
  console.log('[GridItemDialog] currentGridItem:', localContent.value);
}

watch(() => store.currentId, async (newId) => {
  console.log('[GridItemDialog] currentId变化:', newId);
  await nextTick();
  initGridItemData();
}, { deep: true, immediate: true });
</script>

<style scoped>
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.gap-2 {
  gap: 0.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.width-32 {
  width: 8rem;
}
</style>
