<template>
  <el-form label-width="100px">
    <el-form-item label="标题">
      <el-input v-model="localContent.content.title" placeholder="请输入标题" @input="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="图标">
      <IconSelector v-model="localContent.content.icon" @update:modelValue="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="显示标题">
      <el-switch v-model="localContent.content.showTitle" @change="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="表格列配置">
      <el-transfer
        v-model="selectedColumns"
        :data="dtoFields"
        :titles="['可选字段', '已选字段']"
        :props="{
          key: 'key',
          label: 'label'
        }"
        @change="handleTransferChange"
      >
        <template #right-footer style="text-align: center;">
            <el-button link @click="showAdvancedConfig">高级配置</el-button>
        </template>
      </el-transfer>
    </el-form-item>
    
    <el-form-item label="显示分页">
      <el-switch v-model="localContent.content.config.showPagination" @change="handleShowPaginationChange" />
    </el-form-item>
    
    <el-form-item label="每页条数" v-if="localContent?.content?.config?.showPagination">
      <el-input-number v-model="paginationPageSize" :min="1" :max="100" @change="handlePageSizeChange" />
    </el-form-item>
    
    <el-form-item label="透明度">
      <!-- <el-slider v-model="opacity" :min="0" :max="1" :step="0.1" @change="emitUpdate" /> -->
      <el-slider 
                v-model="opacity" 
                :min="0" 
                :max="100" 
                :step="1"
                @change="emitUpdate"
              />
    </el-form-item>

    <el-form-item label="批量删除">
      <el-switch v-model="localContent.content.config.batchDelete" @change="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="主题模式">
      <el-radio-group v-model="localContent.content.config.themeMode" @change="emitUpdate">
        <el-radio value="light">亮色</el-radio>
        <el-radio value="dark">暗色</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="功能设置">
      <el-checkbox v-model="localContent.content.config.refreshable" @change="emitUpdate">可刷新</el-checkbox>
      <el-checkbox v-model="localContent.content.config.configurable" @change="emitUpdate">可配置</el-checkbox>
      <el-checkbox v-model="localContent.content.config.editable" @change="emitUpdate">可编辑</el-checkbox>
      <el-checkbox v-model="localContent.content.config.closable" @change="emitUpdate">可关闭</el-checkbox>
    </el-form-item>
  </el-form>

  <!-- 新增弹窗组件 -->
  <el-dialog
    v-model="dialogVisible"
    title="页面高级配置"
    width="800px"
  >
    <TableConfigEditor 
      type="page"
      v-model="localContent.content.config" 
      @update:modelValue="handleConfigEditorChange" 
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed, nextTick } from 'vue';
import IconSelector from '@/components/page/IconSelector.vue';
import TableConfigEditor from '@/components/base/TableConfigEditor.vue';
import type { TableColumn, DtoField, AdminGridInfoDTO } from '../../types';
// import { useGridManagementStore } from '../../stores/gridManagementStore';

//const store = useGridManagementStore();

const props = defineProps({
  // contentData: {
  //   type: Object as () => GridItemContent,
  //   required: true
  // },
  store: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:content']);

const store = props.store;
// 使用本地数据，避免直接修改props
const localContent = ref<AdminGridInfoDTO>(store.defaultGridItem);

// 组件挂载时确保数据结构
onMounted(() => {
  ensureDataStructure();
  console.log('[PageConfig] 组件已挂载，数据结构初始化完成');
});

// 监听数据变化，确保结构完整
watch(() => props.store.defaultGridItem, () => {
  console.log('[PageConfig] defaultGridItem变化，正在更新localContent');
  // 更新本地引用
  localContent.value = JSON.parse(JSON.stringify(props.store.defaultGridItem));
  // 确保数据结构完整
  nextTick(() => {
    ensureDataStructure();
  });
}, { deep: true, immediate: true });

// 监听localContent变化，确保数据结构完整
watch(() => localContent.value, () => {
  console.log('[PageConfig] localContent变化，确保数据结构完整');
  ensureDataStructure();
}, { deep: true });


/**
 * 确保必要的数据结构存在
 * 该函数用于验证并初始化组件所需的所有数据结构
 */
function ensureDataStructure() {
  console.log('[PageConfig] 执行ensureDataStructure，确保数据结构完整');

  // 首先确保content对象存在并有所有必需属性
  if (!localContent.value) {
    console.warn('[PageConfig] localContent为空！初始化为默认值');
    localContent.value = JSON.parse(JSON.stringify(store.defaultGridItem));
    return; // 重新调用确保完整初始化
  }

  // 确保content属性存在且完整
  if (!localContent.value.content) {
    console.warn('[PageConfig] content属性不存在，正在创建');
    localContent.value.content = {
      type: 'table',
      title: '',
      icon: '',
      showTitle: true,
      refreshable: true,
      configurable: true,
      editable: true,
      closable: true,
      themeMode: 'light',
      config: {}
    };
  }

  // 确保config属性存在
  if (!localContent.value.content.config) {
    console.warn('[PageConfig] config属性不存在，正在创建');
    localContent.value.content.config = {};
  }

  // 确保columns数组存在
  if (!localContent.value.content.config.columns) {
    console.warn('[PageConfig] columns属性不存在，正在创建默认列');
    localContent.value.content.config.columns = [
      { label: '列1', prop: 'col1' },
      { label: '列2', prop: 'col2' },
      { label: '列3', prop: 'col3' }
    ] as TableColumn[];
  }

  // 确保分页配置存在
  if (!localContent.value.content.config.pagination) {
    console.warn('[PageConfig] pagination属性不存在，正在创建默认分页配置');
    localContent.value.content.config.pagination = {
      pageSize: 10,
      pageSizes: [10, 20, 50, 100]
    };
  }

  // 确保显示分页属性存在
  if (localContent.value.content.config.showPagination === undefined) {
    console.warn('[PageConfig] showPagination属性不存在，默认设置为true');
    localContent.value.content.config.showPagination = true;
  }
  
  // 确保自定义样式存在
  if (!localContent.value.content.customStyle) {
    console.warn('[PageConfig] customStyle属性不存在，正在创建');
    localContent.value.content.customStyle = { opacity: "1" };
  }

  console.log('[PageConfig] 数据结构确认完成', 
              JSON.stringify({
                hasContent: !!localContent.value.content,
                hasConfig: !!(localContent.value.content && localContent.value.content.config),
                hasColumns: !!(localContent.value.content && localContent.value.content.config && localContent.value.content.config.columns),
                columnsLength: localContent.value.content?.config?.columns?.length || 0
              }));
}

/**
 * 向父组件发送更新事件
 * 在发送前先确保数据结构完整，并使用深拷贝防止引用问题
 */
function emitUpdate() {
  // 在发送更新前确保数据结构完整
  ensureDataStructure();
  
  // 记录发送前的数据状态
  console.log('[PageConfig] 发送更新前的数据状态:', 
             JSON.stringify({
               hasConfig: !!localContent.value?.content?.config,
               hasColumns: !!localContent.value?.content?.config?.columns,
               columnsLength: localContent.value?.content?.config?.columns?.length || 0
             }));
  
  // 只发送content部分，而不是整个对象，避免在GridItemDialog中被错误处理
  // 使用深拷贝确保数据完整性
  const clonedContent = JSON.parse(JSON.stringify(localContent.value.content));
  
  // 明确设置类型为'page'，确保不被其他组件覆盖
  clonedContent.type = 'page';
  
  console.log('[PageConfig] 发送数据前确认type:', clonedContent.type);
  emit('update:content', clonedContent);
}

// 处理配置编辑器的更新事件
function handleConfigEditorChange(newConfig: any) {
  localContent.value.content.config = newConfig;
  emitUpdate();
}

// 透明度处理
const opacity = ref(1);

// 初始化透明度
watch(() => localContent.value.content.customStyle, (newStyle) => {
  if (newStyle && newStyle.opacity) {
    opacity.value = parseFloat(newStyle.opacity);
  } else {
    opacity.value = 1;
  }
}, { immediate: true, deep: true });

// 监听透明度变化，更新localContent
watch(opacity, (val) => {
  if (!localContent.value.content.customStyle) {
    localContent.value.content.customStyle = {};
  }
  localContent.value.content.customStyle.opacity = val.toString();
  emitUpdate();
});

// 分页处理相关函数

// 分页大小计算属性，确保安全访问
const paginationPageSize = computed({
  get: () => {
    // 确保所有路径都存在
    if (!localContent.value?.content?.config?.pagination) {
      // 如果不存在，初始化一个默认对象
      // 先检查config是否存在
      const hasConfig = !!localContent.value?.content?.config;
      
      if (hasConfig) {
        // 添加分页对象
        localContent.value.content.config.pagination = { pageSize: 10, pageSizes: [10, 20, 50, 100] };
      }
      return 10; // 默认值
    }
    return localContent.value.content.config.pagination.pageSize || 10;
  },
  set: (val) => {
    // 确保所有路径都存在并有效
    if (!localContent.value?.content?.config?.pagination) {
      // 使用handleShowPaginationChange初始化必要的结构
      handleShowPaginationChange(true); // 这会创建必要的pagination对象
    }
    
    // 现在应该安全设置值
    if (localContent.value?.content?.config?.pagination) {
      localContent.value.content.config.pagination.pageSize = val;
    }
  }
});

// 处理显示分页开关变化
/**
 * 处理显示分页开关变化
 * @param value 是否显示分页
 */
function handleShowPaginationChange(value: boolean) {
  // 确保 config 存在
  if (!localContent.value?.content?.config) {
    // 使用中间变量避免TypeScript类型检查问题
    const hasContent = !!localContent.value?.content;
    
    if (!hasContent) {
      // 创建带有所有必需属性的content对象
      localContent.value.content = {
        type: 'page',
        title: '',
        icon: '',
        showTitle: true,
        refreshable: true,
        configurable: true,
        editable: true,
        closable: true,
        themeMode: 'light',
        config: {}
      };
    }
    // 确保有config对象
    if (!localContent.value.content.config) {
      localContent.value.content.config = {};
    }
  }
  
  // 更新分页显示状态
  localContent.value.content.config.showPagination = value;
  
  // 如果启用分页，确保分页对象存在
  if (value && !localContent.value.content.config.pagination) {
    localContent.value.content.config.pagination = { pageSize: 10, pageSizes: [10, 20, 50, 100] };
  }
  
  // 记录一下
  console.log('[PageConfig] 分页状态更新:', value);
  emitUpdate();
}

/**
 * 处理分页大小变化
 * @param value 新的分页大小
 */
function handlePageSizeChange(value: number) {
  // 直接通过计算属性的setter来更新值
  paginationPageSize.value = value;
  emitUpdate();
}

// 列管理相关函数 - 当前使用Transfer组件代替手动管理
// 如果将来需要手动添加/移除列，这些函数可以启用
/*
function addColumn() {
  // 确保安全访问
  if (!localContent.value?.content?.config?.columns) {
    // 初始化columns数组
    if (localContent.value?.content?.config) {
      localContent.value.content.config.columns = [];
    } else {
      handleShowPaginationChange(false); // 创建必要的配置结构
      localContent.value.content.config.columns = [];
    }
  }
  
  localContent.value.content.config.columns.push({ label: '新列', prop: 'newCol' } as TableColumn);
  emitUpdate();
}

function removeColumn(index: number) {
  if (localContent.value?.content?.config?.columns && index >= 0 && index < localContent.value.content.config.columns.length) {
    localContent.value.content.config.columns.splice(index, 1);
    emitUpdate();
  }
}
*/

// 处理穿梭框数据
const dtoFields = computed(() => store.processedDtoFields.map((field: DtoField) => ({
  key: field.prop,
  label: field.label || field.prop,
  type: field.type
})));

const selectedColumns = ref<string[]>([]);

// 初始化已选择的列
watch(() => {
  try {
    // 打印当前的localContent结构，帮助调试
    if (localContent.value) {
      console.log('[PageConfig] localContent结构:', JSON.stringify({
        hasContent: !!localContent.value.content,
        hasConfig: !!(localContent.value.content && localContent.value.content.config),
        hasColumns: !!(localContent.value.content && localContent.value.content.config && localContent.value.content.config.columns)
      }));
    }

    // 优先检查config.columns
    if (localContent.value?.content?.config?.columns && Array.isArray(localContent.value.content.config.columns)) {
      return localContent.value.content.config.columns;
    }
    
    // 如果没有，尝试检查content对象的其他可能路径
    // 由于TypeScript类型定义问题，这里使用as any绕过类型检查
    if ((localContent.value?.content as any)?.content?.columns && Array.isArray((localContent.value?.content as any).content.columns)) {
      return (localContent.value.content as any).content.columns;
    }
    
    // 如果两者都没有，初始化空数组
    if (localContent.value?.content?.config && !localContent.value.content.config.columns) {
      localContent.value.content.config.columns = [];
      return localContent.value.content.config.columns;
    }

    return [];
  } catch (error) {
    console.warn('[PageConfig] 读取columns时出错:', error);
    return [];
  }
}, (columns: TableColumn[] | undefined) => {
  console.log('[PageConfig] columns变化:', columns);
  if (columns && Array.isArray(columns)) {
    selectedColumns.value = columns.map((col: TableColumn) => col.prop);
  } else {
    // 当columns未定义时重置selectedColumns
    selectedColumns.value = [];
  }
}, { immediate: true, deep: true });

// 处理穿梭框变更
function handleTransferChange(value: string[]) {
   if(!localContent.value.content.config.columns){
    localContent.value.content.config.columns = [];
   }
  // 创建新的列数组
  const newColumns = value.map(key => {

    // 查找现有的列配置
    const existingColumn = localContent.value.content.config.columns.find((col: TableColumn) => col.prop === key);
    if (existingColumn) {
      return existingColumn;
    }
    
    // 查找字段定义
    const field = store.processedDtoFields.find((f: DtoField) => f.prop === key);
    return {
      label: field?.label || key,
      prop: key
    } as TableColumn;
  });
  
  // 更新列配置并触发更新事件
  localContent.value.content.config.columns = newColumns;
  emitUpdate();
}

// 添加弹窗控制变量
const dialogVisible = ref(false);

// 添加显示弹窗的方法
function showAdvancedConfig() {
  dialogVisible.value = true;
}

const initGridItemData = () => {
  if(!store.gridLayoutStore.structureData || !store.gridLayoutStore.structureData.grid_items) return;
  console.log('[GridItemDialog] initGridItemData', store.gridLayoutStore.structureData, store.currentId,store.gridLayoutStore.structureData.grid_items);
    // 根据id查找对应的grid item
    localContent.value = store.gridLayoutStore.structureData.grid_items.find(
      (item: AdminGridInfoDTO) => String(item.id) === String(store.currentId)
    );
  if (!localContent.value) return;
  //ensureContentStructure(localContent.value); // 新增：保证结构完整
  console.log('[GridItemDialog] currentGridItem:', localContent.value);
}

watch(() => store.currentId, async (newId) => {
  console.log('[GridItemDialog] currentId变化:', newId);
  await nextTick();
  initGridItemData();
}, { deep: true, immediate: true });
</script>

<style scoped>
:deep(.el-transfer-panel__footer) {
  text-align: center;
}
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.gap-2 {
  gap: 0.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.width-32 {
  width: 8rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
</style>
