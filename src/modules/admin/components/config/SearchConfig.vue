<!-- 
  @description SearchConfig - 搜索内容组件配置
  <AUTHOR> AI
  @date 2025-04-21
-->
<template>
  <el-form label-width="100px">
    <el-form-item label="标题">
      <el-input v-model="localContent.content.title" placeholder="请输入标题" @input="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="图标">
      <IconSelector v-model="localContent.content.icon" @update:modelValue="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="显示标题">
      <el-switch v-model="localContent.content.showTitle" @change="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="搜索字段配置">
      <el-transfer
        v-model="selectedFields"
        :data="dtoFields"
        :titles="['可选字段', '已选字段']"
        :props="{
          key: 'key',
          label: 'label'
        }"
        @change="handleTransferChange"
      >
        <template #right-footer>
            <el-button link @click="showAdvancedConfig">高级配置</el-button>
        </template>
      </el-transfer>
    </el-form-item>
    
    <el-form-item label="搜索模式">
      <el-radio-group v-model="localContent.content.config.mode" @change="emitUpdate">
        <el-radio value="simple">简单模式</el-radio>
        <el-radio value="advanced">高级模式</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="折叠状态">
      <el-radio-group v-model="localContent.content.config.defaultCollapsed" @change="emitUpdate">
        <el-radio :value="true">默认折叠</el-radio>
        <el-radio :value="false">默认展开</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="布局列数">
      <el-input-number 
        v-model="localContent.content.config.columns" 
        :min="1" 
        :max="4" 
        :step="1"
        @change="emitUpdate" 
      />
    </el-form-item>
    
    <el-form-item label="透明度">
      <el-slider 
        v-model="opacity" 
        :min="0" 
        :max="1" 
        :step="0.1"
        @change="emitUpdate"
      />
    </el-form-item>
    
    <el-form-item label="主题模式">
      <el-radio-group v-model="localContent.content.config.themeMode" @change="emitUpdate">
        <el-radio value="light">亮色</el-radio>
        <el-radio value="dark">暗色</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="功能设置">
      <el-checkbox v-model="localContent.content.config.refreshable" @change="emitUpdate">可刷新</el-checkbox>
      <el-checkbox v-model="localContent.content.config.configurable" @change="emitUpdate">可配置</el-checkbox>
      <el-checkbox v-model="localContent.content.config.editable" @change="emitUpdate">可编辑</el-checkbox>
      <el-checkbox v-model="localContent.content.config.closable" @change="emitUpdate">可关闭</el-checkbox>
    </el-form-item>
  </el-form>

  <!-- 高级配置弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    title="搜索高级配置"
    width="800px"
  >
    <SearchConfigEditor 
      type="search"
      v-model="localContent.content.config" 
      @update:modelValue="handleConfigEditorChange" 
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/**
 * SearchConfig组件 - 搜索内容组件配置
 * 支持字段选择、模式切换、透明度、主题等配置
 */
import { ref, watch, computed, nextTick, onMounted } from 'vue';
import IconSelector from '@/components/page/IconSelector.vue';
import SearchConfigEditor from '@/components/base/SearchConfigEditor.vue';
import type { FormField, DtoField, AdminGridInfoDTO } from '../../types';

// 组件属性定义
const props = defineProps({
  store: {
    type: Object,
    required: true
  }
});

// 组件事件定义
const emit = defineEmits(['update:content']);

// store实例
const store = props.store;

// 使用本地数据，避免直接修改props
const localContent = ref<AdminGridInfoDTO>(store.defaultGridItem);


// 透明度（0-1浮点数，UI层slider步长0.1）
const opacity = ref(1);

// 确保数据结构完整，避免渲染异常
/**
 * 保证localContent结构完整，填充默认值
 */
function ensureDataStructure() {
  if (!localContent.value.content.config) localContent.value.content.config = {};
  if (!localContent.value.content.config.fields) {
    localContent.value.content.config.fields = [
      { label: '关键字', prop: 'keyword', type: 'input' },
      { label: '状态', prop: 'status', type: 'select' },
      { label: '日期', prop: 'date', type: 'datepicker' }
    ];
  }
  if (localContent.value.content.config.mode === undefined) localContent.value.content.config.mode = 'simple';
  if (localContent.value.content.config.defaultCollapsed === undefined) localContent.value.content.config.defaultCollapsed = false;
  if (!localContent.value.content.config.columns) localContent.value.content.config.columns = 3;
  if (!localContent.value.content.customStyle) localContent.value.content.customStyle = { opacity: "1" };
  if (typeof localContent.value.content.themeMode !== 'string') localContent.value.content.themeMode = 'light';
  if (typeof localContent.value.content.refreshable !== 'boolean') localContent.value.content.refreshable = true;
  if (typeof localContent.value.content.configurable !== 'boolean') localContent.value.content.configurable = true;
  if (typeof localContent.value.content.editable !== 'boolean') localContent.value.content.editable = true;
  if (typeof localContent.value.content.closable !== 'boolean') localContent.value.content.closable = true;
}

// 组件挂载时确保数据结构
onMounted(() => {
  ensureDataStructure();
});

/**
 * 向父组件发送更新事件
 */
function emitUpdate() {
  emit('update:content', JSON.parse(JSON.stringify(localContent.value)));
}

/**
 * 处理配置编辑器的更新事件
 */
function handleConfigEditorChange(newConfig: any) {
  localContent.value.content.config = newConfig;
  emitUpdate();
}

// 透明度初始化和监听，内部0-1浮点，UI层slider步长0.1
watch(() => localContent.value.content.customStyle, (newStyle) => {
  if (newStyle && newStyle.opacity) {
    opacity.value = parseFloat(newStyle.opacity);
  } else {
    opacity.value = 1;
  }
}, { immediate: true, deep: true });

watch(opacity, (val) => {
  if (!localContent.value.content.customStyle) {
    localContent.value.content.customStyle = {};
  }
  localContent.value.content.customStyle.opacity = val.toString();
  emitUpdate();
});

// 处理穿梭框数据
const dtoFields = computed(() => store.processedDtoFields.map((field: DtoField) => ({
  key: field.prop,
  label: field.label || field.prop,
  type: field.type
})));

const selectedFields = ref<string[]>([]);

// 初始化已选择的字段
watch(() => localContent.value.content.config.fields, (fields: FormField[]) => {
  if (fields && Array.isArray(fields)) {
    selectedFields.value = fields.map((field: FormField) => field.prop);
  }
}, { immediate: true, deep: true });

/**
 * 处理穿梭框变更
 */
function handleTransferChange(value: string[]) {
  const newFields: FormField[] = [];
  const existingFields = new Map(
    localContent.value.content.config.fields.map((field: FormField) => [field.prop, field])
  );
  for (const prop of value) {
    if (existingFields.has(prop)) {
      newFields.push(existingFields.get(prop) as FormField);
    } else {
      const dtoField = store.processedDtoFields.find((f: DtoField) => f.prop === prop);
      if (dtoField) {
        newFields.push({
          label: dtoField.label || dtoField.prop,
          prop: dtoField.prop,
          type: mapDtoTypeToSearchType(dtoField.type),
          operator: getDefaultOperator(dtoField.type)
        } as FormField);
      }
    }
  }
  localContent.value.content.config.fields = newFields;
  emitUpdate();
}

/**
 * 映射DTO类型到搜索类型
 */
function mapDtoTypeToSearchType(dtoType: string | undefined): string {
  if (!dtoType) return 'input';
  switch (dtoType.toLowerCase()) {
    case 'string': return 'input';
    case 'number':
    case 'integer':
    case 'float':
    case 'double': return 'number';
    case 'boolean': return 'switch';
    case 'date':
    case 'datetime': return 'datepicker';
    case 'array':
    case 'enum': return 'select';
    default: return 'input';
  }
}

/**
 * 获取默认的搜索运算符
 */
function getDefaultOperator(dtoType: string | undefined): string {
  if (!dtoType) return 'eq';
  switch (dtoType.toLowerCase()) {
    case 'string': return 'contains';
    case 'number':
    case 'integer':
    case 'float':
    case 'double': return 'eq';
    case 'boolean': return 'eq';
    case 'date':
    case 'datetime': return 'between';
    default: return 'eq';
  }
}

// 弹窗控制
const dialogVisible = ref(false);
/**
 * 显示高级配置弹窗
 */
function showAdvancedConfig() {
  dialogVisible.value = true;
}

const initGridItemData = () => {
  if(!store.gridLayoutStore.structureData || !store.gridLayoutStore.structureData.grid_items) return;
  console.log('[GridItemDialog] initGridItemData', store.gridLayoutStore.structureData, store.currentId,store.gridLayoutStore.structureData.grid_items);
    // 根据id查找对应的grid item
    localContent.value = store.gridLayoutStore.structureData.grid_items.find(
      (item: AdminGridInfoDTO) => String(item.id) === String(store.currentId)
    );
  if (!localContent.value) return;
  ensureDataStructure(); // 新增：保证结构完整
  console.log('[GridItemDialog] currentGridItem:', localContent.value);
}

watch(() => store.currentId, async (newId) => {
  console.log('[GridItemDialog] currentId变化:', newId);
  await nextTick();
  initGridItemData();
}, { deep: true, immediate: true });
</script>

<style scoped>
:deep(.el-transfer-panel__footer) {
  text-align: center;
}

.el-transfer {
  margin-bottom: 15px;
}
</style>
