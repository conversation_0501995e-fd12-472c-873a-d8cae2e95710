<template>
  <el-form label-width="100px">
    <el-form-item label="标题">
      <el-input v-model="localContent.content.title" placeholder="请输入标题" @input="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="图标">
      <IconSelector v-model="localContent.content.icon" @update:modelValue="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="显示标题">
      <el-switch v-model="localContent.content.showTitle" @change="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="表格列配置">
      <el-transfer
        v-model="selectedColumns"
        :data="dtoFields"
        :titles="['可选字段', '已选字段']"
        :props="{
          key: 'key',
          label: 'label'
        }"
        @change="handleTransferChange"
      >
        <template #right-footer style="text-align: center;">
            <el-button link @click="showAdvancedConfig">高级配置</el-button>
        </template>
      </el-transfer>
    </el-form-item>
    
    <el-form-item label="显示分页">
      <el-switch v-model="localContent.content.config.showPagination" @change="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="每页条数" v-if="localContent.content.config.showPagination">
      <el-input-number v-model="localContent.content.config.pagination.pageSize" :min="1" :max="100" @change="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="透明度">
      <!-- <el-slider v-model="opacity" :min="0" :max="1" :step="0.1" @change="emitUpdate" /> -->
      <el-slider 
                v-model="opacity" 
                :min="0" 
                :max="100" 
                :step="1"
                @change="emitUpdate"
              />
    </el-form-item>
    
    <el-form-item label="主题模式">
      <el-radio-group v-model="localContent.content.config.themeMode" @change="emitUpdate">
        <el-radio value="light">亮色</el-radio>
        <el-radio value="dark">暗色</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="功能设置">
      <el-checkbox v-model="localContent.content.config.refreshable" @change="emitUpdate">可刷新</el-checkbox>
      <el-checkbox v-model="localContent.content.config.configurable" @change="emitUpdate">可配置</el-checkbox>
      <el-checkbox v-model="localContent.content.config.editable" @change="emitUpdate">可编辑</el-checkbox>
      <el-checkbox v-model="localContent.content.config.closable" @change="emitUpdate">可关闭</el-checkbox>
    </el-form-item>
  </el-form>

  <!-- 新增弹窗组件 -->
  <el-dialog
    v-model="dialogVisible"
    title="表格高级配置"
    width="800px"
  >
    <TableConfigEditor 
      v-model="localContent.content.config" 
      @update:modelValue="handleConfigEditorChange" 
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue';
import IconSelector from '@/components/page/IconSelector.vue';
import TableConfigEditor from '@/components/base/TableConfigEditor.vue';
import type { TableColumn, DtoField, AdminGridInfoDTO } from '../../types';
// import { useGridManagementStore } from '../../stores/gridManagementStore';

//const store = useGridManagementStore();

const props = defineProps({
  // contentData: {
  //   type: Object as () => GridItemContent,
  //   required: true
  // },
  store: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:content']);

const store = props.store;
// 使用本地数据，避免直接修改props
const localContent = ref<AdminGridInfoDTO>(store.defaultGridItem);

// 组件挂载时确保数据结构
// onMounted(() => {
//   ensureDataStructure();
// });

// // 确保必要的数据结构存在
// function ensureDataStructure() {
//   // 确保config.columns和分页配置存在
//   if (!localContent.value.content.config) {
//     localContent.value.content.config = {};
//   }
//   if (!localContent.value.content.config.columns) {
//     localContent.value.content.config.columns = [
//       { label: '列1', prop: 'col1' },
//       { label: '列2', prop: 'col2' },
//       { label: '列3', prop: 'col3' }
//     ] as TableColumn[];
//   }
//   if (!localContent.value.content.config.pagination) {
//     localContent.value.content.config.pagination = {
//       pageSize: 10,
//       pageSizes: [10, 20, 50, 100]
//     };
//   }
//   if (localContent.value.content.config.showPagination === undefined) {
//     localContent.value.content.config.showPagination = true;
//   }
  
//   // 确保自定义样式存在
//   if (!localContent.value.content.customStyle) {
//     localContent.value.content.customStyle = { opacity: "1" };
//   }
// }

// 向父组件发送更新事件
function emitUpdate() {
  // 只发送content部分，而不是整个对象，避免在GridItemDialog中被错误处理
  const clonedContent = JSON.parse(JSON.stringify(localContent.value.content));
  
  // 明确设置类型为'table'，确保不被其他组件覆盖
  clonedContent.type = 'table';
  
  console.log('[TableConfig] 发送数据前确认type:', clonedContent.type);
  emit('update:content', clonedContent);
}

// 处理配置编辑器的更新事件
function handleConfigEditorChange(newConfig: any) {
  localContent.value.content.config = newConfig;
  emitUpdate();
}

// 透明度处理
const opacity = ref(1);

// 初始化透明度
watch(() => localContent.value.content.customStyle, (newStyle) => {
  if (newStyle && newStyle.opacity) {
    opacity.value = parseFloat(newStyle.opacity);
  } else {
    opacity.value = 1;
  }
}, { immediate: true, deep: true });

// 监听透明度变化，更新localContent
watch(opacity, (val) => {
  if (!localContent.value.content.customStyle) {
    localContent.value.content.customStyle = {};
  }
  localContent.value.content.customStyle.opacity = val.toString();
  emitUpdate();
});

// 添加和移除列
// function addColumn() {
//   localContent.value.content.config.columns.push({ label: '新列', prop: 'newCol' } as TableColumn);
//   emitUpdate();
// }

// function removeColumn(index: number) {
//   localContent.value.content.config.columns.splice(index, 1);
//   emitUpdate();
// }

// 处理穿梭框数据
const dtoFields = computed(() => store.processedDtoFields.map((field: DtoField) => ({
  key: field.prop,
  label: field.label || field.prop,
  type: field.type
})));

const selectedColumns = ref<string[]>([]);

// 初始化已选择的列
watch(() => localContent.value.content.config.columns, (columns: TableColumn[]) => {
  if (columns && Array.isArray(columns)) {
    selectedColumns.value = columns.map((col: TableColumn) => col.prop);
  }
}, { immediate: true, deep: true });

// 处理穿梭框变更
function handleTransferChange(value: string[]) {
  // 创建新的列数组
  const newColumns = value.map(key => {
    // 查找现有的列配置
    const existingColumn = localContent.value.content.config.columns.find((col: TableColumn) => col.prop === key);
    if (existingColumn) {
      return existingColumn;
    }
    
    // 查找字段定义
    const field = store.processedDtoFields.find((f: DtoField) => f.prop === key);
    return {
      label: field?.label || key,
      prop: key
    } as TableColumn;
  });
  
  // 更新列配置并触发更新事件
  localContent.value.content.config.columns = newColumns;
  emitUpdate();
}

// 添加弹窗控制变量
const dialogVisible = ref(false);

// 添加显示弹窗的方法
function showAdvancedConfig() {
  dialogVisible.value = true;
}

const initGridItemData = () => {
  if(!store.gridLayoutStore.structureData || !store.gridLayoutStore.structureData.grid_items) return;
  console.log('[GridItemDialog] initGridItemData', store.gridLayoutStore.structureData, store.currentId,store.gridLayoutStore.structureData.grid_items);
    // 根据id查找对应的grid item
    localContent.value = store.gridLayoutStore.structureData.grid_items.find(
      (item: AdminGridInfoDTO) => String(item.id) === String(store.currentId)
    );
  if (!localContent.value) return;
  //ensureContentStructure(localContent.value); // 新增：保证结构完整
  console.log('[GridItemDialog] currentGridItem:', localContent.value);
}

watch(() => store.currentId, async (newId) => {
  console.log('[GridItemDialog] currentId变化:', newId);
  await nextTick();
  initGridItemData();
}, { deep: true, immediate: true });
</script>

<style scoped>
:deep(.el-transfer-panel__footer) {
  text-align: center;
}
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.gap-2 {
  gap: 0.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.width-32 {
  width: 8rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
</style>
