<template>
  <el-form label-width="100px">
    <el-form-item label="标题">
      <el-input v-model="localContent.content.title" placeholder="请输入标题" @input="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="图标">
      <IconSelector v-model="localContent.content.icon" @update:modelValue="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="显示标题">
      <el-switch v-model="localContent.content.showTitle" @change="emitUpdate" />
    </el-form-item>
    
    <el-form-item label="表单字段配置">
      <el-transfer
        v-model="selectedFields"
        :data="dtoFields"
        :titles="['可选字段', '已选字段']"
        :props="{
          key: 'key',
          label: 'label'
        }"
        @change="handleTransferChange"
      >
        <template #right-footer>
            <el-button link @click="showAdvancedConfig">高级配置</el-button>
        </template>
      </el-transfer>
    </el-form-item>
    
    <el-form-item label="表单布局">
      <el-radio-group v-model="localContent.content.config.layout" @change="emitUpdate">
        <el-radio value="vertical">垂直布局</el-radio>
        <el-radio value="horizontal">水平布局</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="标签宽度">
      <el-input-number 
        v-model="labelWidth" 
        :min="60" 
        :max="200" 
        :step="10" 
        @change="handleLabelWidthChange" 
      />
    </el-form-item>
    
    <el-form-item label="标签位置">
      <el-select v-model="localContent.content.config.labelPosition" @change="emitUpdate">
        <el-option label="左对齐" value="left" />
        <el-option label="右对齐" value="right" />
        <el-option label="顶部对齐" value="top" />
      </el-select>
    </el-form-item>
    
    <el-form-item label="透明度">
      <el-slider 
        v-model="opacity" 
        :min="0" 
        :max="100" 
        :step="1"
        @change="emitUpdate"
      />
    </el-form-item>
    
    <el-form-item label="主题模式">
      <el-radio-group v-model="localContent.content.config.themeMode" @change="emitUpdate">
        <el-radio value="light">亮色</el-radio>
        <el-radio value="dark">暗色</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="功能设置">
      <el-checkbox v-model="localContent.content.config.refreshable" @change="emitUpdate">可刷新</el-checkbox>
      <el-checkbox v-model="localContent.content.config.configurable" @change="emitUpdate">可配置</el-checkbox>
      <el-checkbox v-model="localContent.content.config.editable" @change="emitUpdate">可编辑</el-checkbox>
      <el-checkbox v-model="localContent.content.config.closable" @change="emitUpdate">可关闭</el-checkbox>
    </el-form-item>
  </el-form>

  <!-- 高级配置弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    title="表单高级配置"
    width="800px"
  >
    <FormConfigEditor 
      v-model="localContent.content.config" 
      @update:modelValue="handleConfigEditorChange" 
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed, nextTick } from 'vue';
import IconSelector from '@/components/page/IconSelector.vue';
import FormConfigEditor from '@/components/base/FormConfigEditor.vue';
import type { DtoField, AdminGridInfoDTO, FormField } from '../../types';
//import { useGridManagementStore } from '../../stores/gridManagementStore';

const props = defineProps({
  store: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:content']);
const store = props.store;
// 使用本地数据，避免直接修改props
const localContent = ref<AdminGridInfoDTO>(store.defaultGridItem);


// 组件挂载时确保数据结构
onMounted(() => {
  ensureDataStructure();
});

// 确保必要的数据结构存在
function ensureDataStructure() {
  // 确保config和表单字段配置存在
  if (!localContent.value.content.config) {
    localContent.value.content.config = {};
  }
  if (!localContent.value.content.config.fields) {
    localContent.value.content.config.fields = [
      { label: '字段1', prop: 'field1', type: 'input' },
      { label: '字段2', prop: 'field2', type: 'input' },
      { label: '字段3', prop: 'field3', type: 'input' }
    ];
  }
  
  // 确保表单配置项存在
  if (!localContent.value.content.config.layout) {
    localContent.value.content.config.layout = 'vertical';
  }
  if (!localContent.value.content.config.labelWidth) {
    localContent.value.content.config.labelWidth = 100;
  }
  if (!localContent.value.content.config.labelPosition) {
    localContent.value.content.config.labelPosition = 'right';
  }
  
  // 确保自定义样式存在
  if (!localContent.value.content.customStyle) {
    localContent.value.content.customStyle = { opacity: "1" };
  }
}

// 向父组件发送更新事件
function emitUpdate() {
  emit('update:content', JSON.parse(JSON.stringify(localContent.value)));
}

// 处理配置编辑器的更新事件
function handleConfigEditorChange(newConfig: any) {
  localContent.value.content.config = newConfig;
  emitUpdate();
}

// 透明度处理
const opacity = ref(1);

// 初始化透明度
watch(() => localContent.value.content.customStyle, (newStyle) => {
  if (newStyle && newStyle.opacity) {
    opacity.value = parseFloat(newStyle.opacity) * 100; // 转换为百分比值
  } else {
    opacity.value = 100;
  }
}, { immediate: true, deep: true });

// 监听透明度变化，更新localContent
watch(opacity, (val) => {
  if (!localContent.value.content.customStyle) {
    localContent.value.content.customStyle = {};
  }
  localContent.value.content.customStyle.opacity = (val / 100).toString(); // 转换回0-1范围
  emitUpdate();
});

// 标签宽度处理
const labelWidth = ref(100);

// 初始化标签宽度
watch(() => localContent.value.content.config.labelWidth, (width) => {
  if (width !== undefined) {
    labelWidth.value = width;
  } else {
    labelWidth.value = 100;
  }
}, { immediate: true });

// 处理标签宽度变化
function handleLabelWidthChange(val: number) {
  localContent.value.content.config.labelWidth = val;
  emitUpdate();
}

// 处理穿梭框数据
const dtoFields = computed(() => store.processedDtoFields.map((field: DtoField) => ({
  key: field.prop,
  label: field.label || field.prop,
  type: field.type
})));

const selectedFields = ref<string[]>([]);

// 初始化已选择的字段
watch(() => localContent.value.content.config.fields, (fields: FormField[]) => {
  if (fields && Array.isArray(fields)) {
    selectedFields.value = fields.map((field) => field.prop);
  }
}, { immediate: true, deep: true });

// 处理穿梭框变更
function handleTransferChange(value: string[]) {
  // 构建新的字段列表
  const newFields: FormField[] = [];
  
  // 保留已存在字段的配置
  const existingFields = new Map(
    localContent.value.content.config.fields.map((field: FormField) => [field.prop, field])
  );
  
  // 按照选择的顺序重建字段列表
  for (const prop of value) {
    if (existingFields.has(prop)) {
      // 使用已存在的字段配置
      newFields.push(existingFields.get(prop) as FormField);
    } else {
      // 从DTO字段中查找对应字段信息
      const dtoField = store.processedDtoFields.find((f: DtoField) => f.prop === prop);
      if (dtoField) {
        // 创建新的字段配置
        newFields.push({
          label: dtoField.label || dtoField.prop,
          prop: dtoField.prop,
          type: mapDtoTypeToFormType(dtoField.type)
        } as FormField);
      }
    }
  }
  
  // 更新配置
  localContent.value.content.config.fields = newFields;
  emitUpdate();
}

// 映射DTO类型到表单类型
function mapDtoTypeToFormType(dtoType: string | undefined): string {
  if (!dtoType) return 'input';
  
  switch (dtoType.toLowerCase()) {
    case 'string':
      return 'input';
    case 'number':
    case 'integer':
    case 'float':
    case 'double':
      return 'number';
    case 'boolean':
      return 'switch';
    case 'date':
    case 'datetime':
      return 'datepicker';
    case 'array':
      return 'select';
    case 'object':
      return 'json';
    default:
      return 'input';
  }
}

// 弹窗控制
const dialogVisible = ref(false);

// 显示高级配置弹窗
function showAdvancedConfig() {
  dialogVisible.value = true;
}

const initGridItemData = () => {
  if(!store.gridLayoutStore.structureData || !store.gridLayoutStore.structureData.grid_items) return;
  console.log('[GridItemDialog] initGridItemData', store.gridLayoutStore.structureData, store.currentId,store.gridLayoutStore.structureData.grid_items);
    // 根据id查找对应的grid item
    localContent.value = store.gridLayoutStore.structureData.grid_items.find(
      (item: AdminGridInfoDTO) => String(item.id) === String(store.currentId)
    );
  if (!localContent.value) return;
  //ensureContentStructure(localContent.value); // 新增：保证结构完整
  console.log('[GridItemDialog] currentGridItem:', localContent.value);
}

watch(() => store.currentId, async (newId) => {
  console.log('[GridItemDialog] currentId变化:', newId);
  await nextTick();
  initGridItemData();
}, { deep: true, immediate: true });
</script>

<style scoped>
:deep(.el-transfer-panel__footer) {
  text-align: center;
}


.el-transfer {
  margin-bottom: 15px;
}
</style>
