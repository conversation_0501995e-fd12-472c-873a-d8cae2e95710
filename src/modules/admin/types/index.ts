// Token信息接口
export interface TokenInfo {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

// 管理员类型定义
export interface Admin {
  id: number;
  username: string;
  nickname: string;
  avatar?: string;
  email?: string;
  mobile?: string;
  role: AdminRole;
  status: AdminStatus;
  lastLoginTime?: string;
  createTime: string;
  updateTime?: string;
  creatorId: number; // 添加 creatorId 字段
}

export interface AdminLoginParams {
  username: string;
  password: string;
  device_info?: {
    device_type: string;
    device_id: string;
    device_name: string;
    os: string;
    browser: string;
    ip?: string;
    user_agent?: string;
  };
}

export interface AdminInfo {
  id: number;
  username: string;
  nickname: string;
  avatar?: string;
  email?: string;
  mobile?: string;
  role: AdminRole;
  status: AdminStatus;
  lastLoginTime?: string;
  createTime: string;
  updateTime?: string;
  creatorId: number; // 添加 creatorId 字段  //  也向 AdminInfo 类型添加 creatorId 字段
}

export interface AdminListParams {
  page: number;
  pageSize: number;
  username?: string;
  nickname?: string;
  role?: AdminRole;
  status?: AdminStatus;
  createTime?: string;
  updateTime?: string;
}

export interface AdminCreateParams {
  username: string;
  nickname: string;
  password: string;
  role: AdminRole;
  status: AdminStatus;
}


// 管理员角色枚举
/**
 * 管理员角色枚举
 */
export enum AdminRole {
  /** 超级管理员 */
  SUPER_ADMIN = 'super',
  /** 普通管理员 */
  ADMIN = 'normal',
  /** 客服人员 */
  CUSTOMER_SERVICE = 'customer_serv',
  /** 运营人员 */
  OPERATIONS = 'operations'
}

// 管理员状态枚举
export enum AdminStatus {
  ACTIVE = 1, // 正常
  DISABLED = 0, // 禁用
}

// 权限类型定义
export interface Permission {
  id: number;
  name: string;        // 权限名称
  code: string;        // 权限编码
  level: number;       // 权限等级
  description?: string; // 权限描述
  type: string;        // 权限类型
  status: 'ENABLED' | 'DISABLED'; // 状态
  createTime: string;
  updateTime?: string;
}

// 权限列表查询参数
export interface PermissionListParams {
  page: number;
  pageSize: number;
  name?: string;
  code?: string;
  type?: string;
  status?: string;
}

// 角色类型定义
export interface Role {
  id: number;
  name: string;        // 角色名称
  code: string;        // 角色编码
  description?: string; // 角色描述
  status: 'ENABLED' | 'DISABLED'; // 状态
  permissions?: Permission[]; // 拥有的权限
  createTime: string;
  updateTime?: string;
}

// 角色列表查询参数
export interface RoleListParams {
  page: number;
  pageSize: number;
  name?: string;
  code?: string;
  status?: string;
}

// 用户类型定义
export interface User {
  id: number;
  username: string;
  nickname?: string;
  avatar?: string;
  email?: string;
  phone?: string;
  status: UserStatus;
  registerTime: string;
  lastLoginTime?: string;
}

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 'ACTIVE', // 正常 
  LOCKED = 'LOCKED', // 锁定
  DISABLED = 'DISABLED', // 禁用
}

// 商家类型定义
export interface Merchant {
  id: number;
  name: string;
  logo?: string;
  description?: string;
  contact_name: string;
  contact_mobile: string;
  contact_email?: string;
  contactName?: string; // 兼容性字段
  contactMobile?: string; // 兼容性字段
  contactEmail?: string; // 兼容性字段
  address?: string;
  latitude?: string; // 纬度
  longitude?: string; // 经度
  status: number; // 使用数字类型，与后端一致
  operation_status: number; // 运营状态
  audit_status: number; // 审核状态
  audit_remark?: string; // 审核备注
  reject_reason?: string; // 拒绝原因
  business_license?: string; // 营业执照
  business_hours?: string; // 营业时间
  balance?: number; // 余额
  level?: number; // 等级
  username?: string; // 用户名
  audit_time?: string; // 审核时间
  is_recommended?: number; // 是否推荐：0-否，1-是
  created_at: string;
  updated_at: string;
  createTime?: string; // 兼容性字段
  updateTime?: string; // 兼容性字段
}

// 商家状态枚举
export enum MerchantStatus {
  ACTIVE = 'ACTIVE', // 营业中
  CLOSED = 'CLOSED', // 休息中
}

export enum MerchantOperationStatus {
  ACTIVE = 1, // 营业中
  CLOSED = 0, // 休息中
}

// 商家审核状态枚举
export enum MerchantAuditStatus {
  PENDING = 'PENDING', // 待审核
  APPROVED = 'APPROVED', // 已通过
  REJECTED = 'REJECTED', // 已拒绝
}

// 商品类型定义
export interface Product {
  id: number;
  name: string;
  description?: string;
  price: number;
  originalPrice?: number;
  stock: number;
  images: string[];
  category: string;
  merchantId: number;
  merchantName: string;
  status: ProductStatus;
  audit_status: ProductAuditStatus;
  createTime: string;
  updateTime?: string;
}



// 商品状态枚举
export enum ProductStatus {
  ON_SALE = 'ON_SALE', // 在售
  OFF_SHELF = 'OFF_SHELF', // 下架
  SOLD_OUT = 'SOLD_OUT', // 售罄
}

// 商品审核状态枚举
export enum ProductAuditStatus {
  PENDING = 'PENDING', // 待审核
  APPROVED = 'APPROVED', // 已通过
  REJECTED = 'REJECTED', // 已拒绝
}

// 订单类型定义
export interface Order {
  id: number;
  orderNo: string;
  userId: number;
  username: string;
  merchantId: number;
  merchantName: string;
  products: OrderProduct[];
  totalAmount: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  paymentMethod?: string;
  deliveryStatus: DeliveryStatus;
  deliveryAddress?: string;
  deliveryPhone?: string;
  deliveryName?: string;
  createTime: string;
  payTime?: string;
  deliveryTime?: string;
  completeTime?: string;
}

// 订单商品类型定义
export interface OrderProduct {
  productId: number;
  productName: string;
  productImage: string;
  price: number;
  quantity: number;
  subtotal: number;
}

// 订单状态枚举
export enum OrderStatus {
  PENDING = 10, // 待支付
  PAID = 20, // 已支付
  PROCESSING = 30, // 处理中
  DELIVERING = 40, // 配送中
  COMPLETED = 50, // 已完成
  CANCELLED = 60, // 已取消
  REFUNDING = 70, // 退款中
  REFUNDED = 80, // 已退款
}

// 支付状态枚举
export enum PaymentStatus {
  UNPAID = 'UNPAID', // 未支付
  PAID = 'PAID', // 已支付
  REFUNDING = 'REFUNDING', // 退款中
  REFUNDED = 'REFUNDED', // 已退款
}

// 配送状态枚举
export enum DeliveryStatus {
  UNSHIPPED = 10, // 未发货
  SHIPPED = 20, // 已发货
  DELIVERED = 30, // 已送达
}

// 分页请求参数
export interface PaginationParams {
  page: number;
  pageSize: number;
}

// 分页响应数据
export interface PaginationResult<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 通用响应结构
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

/**
 * PageData 页面配置对象
 * @description 用于描述完整的页面配置，包括基础属性、内容配置、DTO、网格项等
 * <AUTHOR>
 * @date 2025-04-19
 */
export interface PageData {
  id: number;                       // 配置ID
  config_key: string;               // 配置唯一标识
  config_type: string;              // 配置类型
  created_at: string;               // 创建时间
  updated_at: string;               // 更新时间
  draggable: boolean;               // 是否可拖拽
  resizable: boolean;               // 是否可调整大小
  status: number;                   // 状态
  title: string;                    // 页面标题
  version: string;                  // 配置版本
  version_hash: string;             // 版本识别号
  remark: string;                   // 备注
  module: string;                   // 模块名称
  group: string;                    // 分组
  icon: string;                     // 图标
  frontend_path: string;            // 前端路径
  config_content: UiPageConfig;     // 页面内容配置（参见 UiPageConfig 接口）
  dto?: DTOInfoDTO;                  // DTO数据（参见 DTOInfoDTO 接口）
  grid_items: AdminGridInfoDTO[];   // 网格项数组（参见 AdminGridInfoDTO 接口）
}

/**
 * GridItem 网格项布局配置
 * @description 用于描述动态layout中每个网格项的属性
 * <AUTHOR>
 * @date 2025-04-19
 */
export interface LayoutGridItem {
  /** 唯一标识 */
  i: number | string;
  /** 初始横向位置（第几列） */
  x: number;
  /** 初始纵向位置（第几行） */
  y: number;
  /** 初始宽度（占多少列） */
  w: number;
  /** 初始高度（占多少行） */
  h: number;
  /** 最小宽度，默认1 */
  minW?: number;
  /** 最小高度，默认1 */
  minH?: number;
  /** 最大宽度，默认Infinity */
  maxW?: number;
  /** 最大高度，默认Infinity */
  maxH?: number;
  /** 是否可拖拽，null表示继承父级 */
  isDraggable?: boolean | null;
  /** 是否可缩放，null表示继承父级 */
  isResizable?: boolean | null;
  /** 拖拽是否受限于容器，null表示继承父级 */
  isBounded?: boolean | null;
  /** 是否静态（不可拖拽、缩放、移动） */
  static?: boolean;
  /** 拖拽忽略元素，css选择器 */
  dragIgnoreFrom?: string;
  /** 拖拽允许元素，css选择器 */
  dragAllowFrom?: string;
  /** 缩放忽略元素，css选择器 */
  resizeIgnoreFrom?: string;
  /** 是否保持宽高比 */
  preserveAspectRatio?: boolean;
  /** 拖拽配置 */
  dragOption?: Record<string, any>;
  /** 缩放配置 */
  resizeOption?: Record<string, any>;
}
/**
 * 商家页面配置内容接口
 * @description 用于描述 config_content 字段的结构
 * <AUTHOR>
 * @date 2025-04-19
 */
export interface UiPageConfig {
  frontendPath: string;         // 前端路径
  icon: string;                 // 图标
  moduleName: string;           // 模块名称
  serviceConfig: ServiceConfig; // 服务配置
  formDialogConfig?: DialogConfig;   // 表单对话框配置
  //viewDialogConfig?: DialogConfig;   // 查看对话框配置
}

// dialogConfig 对话框配置接口
export interface DialogConfig {
  dialogType: string;                 // 对话框类型 dialog/drawer
  title: string;                // 对话框标题
  width: string | number;       // 对话框宽度
  rules: Record<string, FormRule[]>;                // 对话框标题
  columns: PlusColumn[];        // 对话框列配置
}

interface FormRule {
  required?: boolean;
  validator?: string | Function;
  threshold?: number;
  message: string;
  trigger: string;
  [key: string]: any;
}
/**
 * PlusColumn 表格列配置
 * 用于描述PlusTable/PlusForm/PlusDescriptions等组件的列属性
 * <AUTHOR>
 * @date 2025-04-26
 */
export interface PlusColumn {
  /** 表格对应列内容的字段名（支持多级 x.y.z） */
  prop: string;
  /** 表格表头显示的标题，可为字符串或计算属性 */
  label?: string | import('vue').ComputedRef<string>;
  /** 表格列宽 */
  width?: string | number;
  /** 表格列最小宽 */
  minWidth?: string | number;
  /** 值的类型（text、select、img、date-picker、radio、checkbox、plus-radio、link等） */
  valueType?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 禁用表头过滤（已废弃，建议用disabledHeaderFilter） */
  headerFilter?: boolean;
  /** 禁用表头过滤 */
  disabledHeaderFilter?: boolean;
  /** 当前表格项是否勾选 */
  headerIsChecked?: boolean;
  /** 透传给el-table-column的props */
  tableColumnProps?: Record<string, any>;
  /** 图片预览，仅当 valueType=img 时生效 */
  preview?: boolean;
  /** link类型时的文字 */
  linkText?: string;
  /** 透传给el-descriptions-item的props */
  descriptionsItemProps?: Record<string, any>;
  /** 自定义描述行内容 */
  renderDescriptionsItem?: (row: any, column: PlusColumn, index: number) => any;
  /** 自定义描述行label内容 */
  renderDescriptionsLabel?: (row: any, column: PlusColumn, index: number) => any;
  /** 选项配置，valueType为select/radio/checkbox/plus-radio时生效 */
  options?: Array<any> | (() => Array<any>) | Promise<Array<any>>;
  /** 对 options 进行映射 */
  optionsMap?: Record<string, string>;
  /** 自定义 options 的显示逻辑 */
  customGetStatus?: (row: any) => any;
  /** tooltip配置，支持字符串、对象、计算属性 */
  tooltip?: string | import('vue').ComputedRef<string> | Record<string, any>;
  /** 自定义渲染表格/描述行内容，优先级最高 */
  render?: (row: any, column: PlusColumn, index: number) => any;
  /** 自定义渲染表格/描述行内容，返回HTML字符串，优先级低于render */
  renderHTML?: (row: any, column: PlusColumn, index: number) => string;
  /** 自定义渲染表头 */
  renderHeader?: (column: PlusColumn) => any;
  /** 格式化显示内容 */
  formatter?: (row: any, column: PlusColumn, value: any, index: number) => any;
  /** 透传给PlusForm的props */
  formProps?: Record<string, any> | (() => Record<string, any>);
  /** 透传给el-form-item的props */
  formItemProps?: Record<string, any> | (() => Record<string, any>);
  /** 透传给具体表单项/表格显示项的props */
  fieldProps?: Record<string, any> | (() => Record<string, any>);
  /** 单项插槽配置 */
  fieldSlots?: Record<string, any>;
  /** 子项插槽 */
  fieldChildrenSlot?: (row: any, column: PlusColumn, index: number) => any;
  /** 自定义渲染表单field */
  renderField?: (row: any, column: PlusColumn, index: number, onChange: (val: any) => void) => any;
  /** 自定义渲染el-form-item label */
  renderLabel?: (row: any, column: PlusColumn, index: number) => any;
  /** 是否需要label */
  hasLabel?: boolean;
  /** 渲染el-form-item下一行额外内容 */
  renderExtra?: (row: any, column: PlusColumn, index: number) => any;
  /** el-col的props */
  colProps?: Record<string, any>;
  /** 查询表单权重，越大越靠前 */
  order?: number;
  /** 在PlusDescriptions中隐藏 */
  hideInDescriptions?: boolean;
  /** 在PlusForm中隐藏 */
  hideInForm?: boolean;
  /** 在PlusPage中隐藏 */
  hideInTable?: boolean;
  /** 在PlusSearch中隐藏 */
  hideInSearch?: boolean;
  /** 其他扩展属性 */
  [key: string]: any;
}

// serviceConfig 服务配置接口
// export interface ServiceConfig {
//   addTitle: string;           // 新增标题
//   apiPrefix: string;          // API前缀
//   baseUrl: string;            // 基础URL
//   customActions: Record<string, any>; // 自定义操作
//   gridOptions: {
//     cellHeight: number;       // 单元格高度
//     column: number;           // 列数
//     margin: number;           // 间距
//   };
//   messages: {
//     addSuccess: string;       // 新增成功提示
//     deleteConfirm: string;    // 删除确认提示
//     deleteSuccess: string;    // 删除成功提示
//     updateSuccess: string;    // 更新成功提示
//   };
//   resizable: boolean;         // 是否可调整大小
//   draggable: boolean;         // 是否可拖拽
//   viewTitle: string;          // 查看标题
//   editTitle: string;          // 编辑标题
//   version: string;            // 版本号
  
// }
/**
 * 页面数据参数
 */
export interface PageDataParams {
  /** 页码 */
  page?: number;
  /** 每页数量 */
  pageSize?: number;
  /** 排序字段 */
  sortField?: string;
  /** 排序方向 */
  sortOrder?: 'ascend' | 'descend';
  /** 其他参数 */
  [key: string]: any;
}
/**
 * 服务配置接口
 */
export interface ServiceConfig {
  /** 基础URL */
  baseUrl: string;
  /** API前缀 */
  apiPrefix?: string;
  /** 自定义API方法 */
  api?: (params: any, method?: string, url?: string) => Promise<any>;
  /** 默认查询参数 */
  defaultParams?: PageDataParams;
  /** 网格选项 */
  gridOptions?: GridOptions;
  /** URL配置 */
  urls?: {
    /** 列表接口 */
    list?: string;
    /** 详情接口 */
    detail?: string;
    /** 添加接口 */
    create?: string;
    /** 更新接口 */
    update?: string;
    /** 删除接口 */
    delete?: string;
    /** 其他自定义接口 */
    [key: string]: string | undefined;
  };
  /** 格式化请求参数 */
  formatRequest?: (params: Record<string, any>) => Record<string, any>;
  /** 格式化响应数据 */
  formatResponse?: (response: ResponseData<any>) => { data: any[]; total: number };
  /** 格式化详情响应数据 */
  formatDetailResponse?: (response: any) => any;
  /** 列表数据获取成功回调 */
  onListSuccess?: (data: any[], total: number) => void;
  /** 列表数据获取失败回调 */
  onListError?: (error: any) => void;
  /** 获取详情成功回调 */
  onDetailSuccess?: (data: any) => void;
  /** 获取详情失败回调 */
  onDetailError?: (error: any) => void;
  /** 主键字段名 */
  idField?: string;
  /** 添加标题 */
  addTitle?: string;
  /** 编辑标题 */
  editTitle?: string;
  /** 查看标题 */
  viewTitle?: string;
  /** 消息提示配置 */
  messages?: {
    /** 添加成功 */
    addSuccess?: string;
    /** 编辑成功 */
    editSuccess?: string;
    /** 删除确认 */
    deleteConfirm?: string;
    /** 删除成功 */
    deleteSuccess?: string;
  };
  /** 分页映射配置 */
  pageInfoMap?: PageInfoMap;
  /** 是否立即获取数据 */
  immediate?: boolean;
  /** 格式化表单数据 */
  formatFormData?: (data: Record<string, any>, mode: 'add' | 'edit') => Record<string, any>;
  /** 添加成功回调 */
  onAddSuccess?: (response: any) => void;
  /** 添加失败回调 */
  onAddError?: (error: any) => void;
  /** 更新成功回调 */
  onUpdateSuccess?: (response: any) => void;
  /** 更新失败回调 */
  onUpdateError?: (error: any) => void;
  /** 删除成功回调 */
  onDeleteSuccess?: (response: any) => void;
  /** 删除失败回调 */
  onDeleteError?: (error: any) => void;
  /** 自定义操作配置 */
  customActions?: Record<string, CustomActionConfig | ((params: any) => Promise<any>)>;
  /** 默认表单数据，可手动设置或从列配置中自动生成 */
  defaultFormData?: Record<string, any>;
  /** 表单列配置，用于生成默认值和表单验证规则 */
  formColumns?: CustomPlusColumn[];

  [key: string]: any;
}

/**
 * 网格选项
 */
export interface GridOptions {
  /** 列数 */
  column: number;
  /** 单元格高度 */
  cellHeight: number;
  /** 间距 */
  margin: number;
}

/**
 * 响应数据
 */
export interface ResponseData<T = any> {
  /** 响应代码 */
  code?: number;
  /** 响应消息 */
  message?: string;
  /** 响应数据 */
  data?: T[];
  /** 响应列表 */
  list?: T[];
  /** 响应项目 */
  items?: T[];
  /** 总数 */
  total?: number;
  /** 其他字段 */
  [key: string]: any;
}

/**
 * 分页映射配置接口
 */
export interface PageInfoMap {
  /** 总数字段名 */
  total: string;
  /** 当前页字段名 */
  current: string;
  /** 每页条数字段名 */
  pageSize: string;
  /** 列表数据字段名 */
  pageSizeList: [number]
}

/**
 * 自定义操作配置接口
 */
export interface CustomActionConfig {
  /** 操作标签 */
  label?: string;
  /** 操作文本 */
  text?: string;
  /** 操作类型 */
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text';
  /** 操作图标 */
  icon?: string;
  /** 操作动作名称 */
  action?: string;
  /** 是否禁用 */
  disabled?: boolean | ((row: any) => boolean);
  /** 是否显示 */
  hidden?: boolean | ((row: any) => boolean);
  /** 点击处理函数 */
  handler?: (row: any) => void;
  /** 条件函数 */
  condition?: (row: any) => boolean;
  /** 确认消息 */
  confirmMessage?: string;
  /** 成功消息 */
  successMessage?: string;
  /** URL */
  url?: string;
  /** 请求方法 */
  method?: string;
  /** 格式化参数 */
  formatParams?: (params: any) => any;
}

// 自定义PlusColumn，因为原始类型定义出现了问题
export type CustomPlusColumn = PlusColumn & {
  /** 值类型，支持字符串 */
  valueType?: string | any;
  /** 是否在搜索表单中隐藏 */
  hideInSearch?: boolean;
  /** 是否在表格中隐藏 */
  hideInTable?: boolean;
  /** 是否在表单中隐藏 */
  hideInForm?: boolean;
  /** 其他扩展属性 */
  [key: string]: any;
};

/**
 * AdminUIConfigDTO 管理员模块使用的UI配置数据传输对象
 */
export interface AdminUIConfigDTO {
  
  id: number;                 // 配置ID
  frontend_path: string;      // 前端路径
  version_hash: string;       // 版本识别号
  config_type: string;        // 配置类型
  config_key: string;         // 配置唯一标识
  config_content: UiPageConfig;     // 配置内容
  draggable: boolean;        // 是否可拖拽
  resizable: boolean;         // 是否可调整大小
  status: number;             // 状态
  remark: string;             // 备注
  created_at: string;         // 创建时间
  updated_at: string;         // 更新时间
  module: string;             // 模块名称
  title: string;              // 配置标题
  group: string;              // 配置分组
  version: string;            // 配置版本
  icon: string;               // 图标名称
  dto: DTOInfoDTO;                // DTO数据
  grid_items: AdminGridInfoDTO[]; // 关联的网格布局项目
}


/**
 * DTOInfoDTO 数据传输对象
 * @description 用于描述后端DTO结构定义
 * <AUTHOR>
 * @date 2025-04-19
 */
export interface DTOInfoDTO {
  /** 主键ID */
  id: number;
  /** 模块名称 */
  module: string;
  /** DTO名称 */
  name: string;
  /** DTO描述 */
  description: string;
  /** DTO类型 (request-请求对象, response-响应对象, common-通用对象) */
  type: string;
  /** DTO结构定义 (JSON格式) */
  structure: Array<any>;
  /** 创建时间 */
  created_at: string;
  /** 更新时间 */
  updated_at: string;
}

// layout布局配置接口
/**
 * Breakpoints 响应式断点配置
 */
export interface Breakpoints {
  [key: string]: number;
}

/**
 * ResponsiveLayout 响应式布局配置
 */
export interface ResponsiveLayout {
  [breakpoint: string]: LayoutGridItem[];
}

/**
 * LayoutConfig 布局配置接口
 * @description grid-layout-plus 主体布局配置
 * <AUTHOR>
 * @date 2025-04-19
 */
export interface LayoutConfig {
  /** 主layout数组，每项为一个网格项 */
  layout: LayoutGridItem[];
  /** 响应式布局配置（按断点） */
  responsiveLayouts?: Partial<ResponsiveLayout>;
  /** 列数，默认12 */
  colNum?: number;
  /** 行高，默认150 */
  rowHeight?: number;
  /** 最大行数，默认Infinity */
  maxRows?: number;
  /** 元素间距，默认[10,10] */
  margin?: [number, number];
  /** 是否可拖拽，默认true */
  isDraggable?: boolean;
  /** 是否可缩放，默认true */
  isResizable?: boolean;
  /** 是否RTL/LTR反转，默认false */
  isMirrored?: boolean;
  /** 拖拽是否受限于容器，默认false */
  isBounded?: boolean;
  /** 容器高度自适应，默认true */
  autoSize?: boolean;
  /** 是否垂直紧凑，默认true */
  verticalCompact?: boolean;
  /** 拖拽后是否还原，默认false */
  restoreOnDrag?: boolean;
  /** 是否防止碰撞，默认false */
  preventCollision?: boolean;
  /** 是否使用CSS transform，默认true */
  useCssTransforms?: boolean;
  /** 是否响应式，默认false */
  responsive?: boolean;
  /** 断点配置，默认 { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 } */
  breakpoints?: Breakpoints;
  /** 各断点的列数，默认 { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 } */
  cols?: Breakpoints;
  /** 是否动态设置鼠标样式，默认true */
  useStyleCursor?: boolean;
  /** 缩放比例，默认1 */
  transformScale?: number;
}

// index.ts
// 该文件用于定义网格管理相关的类型，包括 GridItem 接口等
/**
 * GridItem 接口，表示网格项的数据结构
 * @property id - 网格项唯一标识
 * @property name - 网格项名称
 * @property content - 网格项内容配置（包含 type、title、icon、showTitle、refreshable 等）
 * @property created_at - 创建时间
 * @property updated_at - 更新时间
 * @property dto - 预留字段
 * @property permission - 权限列表
 * @property position - 位置与尺寸信息
 * @property remark - 备注
 * @property status - 状态
 * @property step - 步骤号 网格项可以在多个步骤中显示
 * @property ui_config_id - UI 配置ID
 */
export interface GridItem {
  id: number;
  name: string;
  content: {
    type: string;
    title: string;
    icon: string;
    showTitle: boolean;
    refreshable: boolean;
    // 其他可选字段可根据实际情况补充
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
  dto: string;
  permission: any[];
  position: LayoutGridItem;
  remark: string;
  status: number;
  step: number[];
  ui_config_id: number;
}

/**
 * AdminGridInfoDTO 管理员模块使用的网格布局数据传输对象
 */
export interface AdminGridInfoDTO {
  id: number;
  uiConfigId?: number;
  ui_config_id?: number;
  name: string;
  step?: number[];
  permission?: any[];
  content: GridItemContent;
  position: LayoutGridItem;
  api: string;
  dto?: DTOInfoDTO;
  remark: string;
  status: number;
  created_at: string;
  updated_at: string;
}

// 扩展GridStackNode的内容类型，以支持前端需要的内容格式
export interface GridItemContent {
  type: string;
  title: string;
  icon: string;
  showTitle: boolean;
  refreshable: boolean;
  configurable: boolean;
  editable: boolean;
  closable: boolean;
  themeMode?: string;
  config: any;
  customStyle?: Record<string, string>;
}

// 表格列配置接口
export interface TableColumn {
  prop: string;
  label: string;
  width?: string | number;
  sortable?: boolean;
  fixed?: boolean | string;
  formatter?: (row: any, column: any, cellValue: any, index: number) => any;
  [key: string]: any;
}

// DTO字段信息接口
export interface DtoField {
  prop: string;
  label: string;
  type: string;
  validation?: string;
  defaultValue?: any;
  options?: Array<{value: any, label: string, key: string}>;
}

/**
 * FormField 表单字段配置接口
 * @description 用于描述表单中的字段配置信息
 * <AUTHOR>
 * @date 2025-04-19
 */
export interface FormField {
  /** 字段标签 */
  label: string;
  /** 字段属性名 */
  prop: string;
  /** 字段类型 */
  type: string;
  /** 字段验证规则 */
  validation?: string;
  /** 默认值 */
  defaultValue?: any;
  /** 选项列表(用于select、checkbox、radio等) */
  options?: Array<{value: any, label: string, key?: string}>;
  /** 是否必填 */
  required?: boolean;
  /** 占位文本 */
  placeholder?: string;
  /** 字段说明 */
  description?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义配置 */
  config?: Record<string, any>;
}

// griditemcontent 接口
// export interface GridItemContent {
//   id?: number;
//   content: any;
//   type: string;
//   title: string;
//   icon: string;
//   showTitle: boolean;
//   refreshable: boolean;
//   configurable: boolean;
//   editable: boolean;
//   closable: boolean;
//   themeMode?: string;
//   config: any;
//   customStyle?: Record<string, string>;
// }
