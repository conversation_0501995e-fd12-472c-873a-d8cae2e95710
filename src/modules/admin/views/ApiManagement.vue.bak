<template>
  <div class="api-management">
    <!-- 选择行放入 el-card -->
    <el-card class="box-card">
      <div class="module-selector">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-select v-model="selectedModule" placeholder="请选择模块" @change="handleModuleChange">
              <el-option
                v-for="module in modules"
                :key="module"
                :label="module"
                :value="module"
              />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-select v-model="selectedGroup" placeholder="请选择分组" :disabled="!groupNames.length" @change="handleGroupChange">
              <el-option
                v-for="group in groupNames"
                :key="group.page_name"
                :label="group.page_name"
                :value="group.page_name"
              />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-button @click="refreshCurrentModule" type="primary">刷新当前</el-button>
            <el-button @click="refreshAllModules" type="danger">刷新所有</el-button>
            <!-- 新增按钮：查看 DTOs -->
            <el-button @click="showDtosDrawer = true" type="primary">查看 DTOs</el-button>
            <!-- 新增按钮：获取现有 UI -->
            <!-- <el-button @click="fetchUiConfigs" type="success">获取UI</el-button> -->
          </el-col>
        </el-row>
        
        <!-- 页面配置列表卡片 -->
        <div style="margin-top: 20px">
        <el-row :gutter="20">
          <el-col :span="16">

            <div v-if="!isConfigListCollapsed">
              <el-table :data="uiConfigs" style="width: 100%" border>
                <el-table-column type="expand">
                  <template #default="props">
                    <p><strong>路径:</strong> {{ props.row.frontend_path }}</p>
                    <p><strong>模块/分组:</strong> {{ props.row.module }}/{{ props.row.group }}</p>
                    <p><strong>备注:</strong> {{ props.row.remark }}</p>
                    <p><strong>更新时间:</strong> {{ props.row.updated_at }}</p>
                  </template>
                </el-table-column>
                <el-table-column label="ID" prop="id" width="80" />
                <el-table-column label="图标" width="100">
                  <template #default="scope">
                    <IconSelector v-model="scope.row.icon" @update:model-value="() => updateConfigIcon(scope.row)" />
                  </template>
                </el-table-column>
                <el-table-column label="version" prop="version" />
                <el-table-column label="标题" prop="title" width="180" />
                <el-table-column label="配置键" prop="config_key" />
                <el-table-column label="状态" prop="status" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                      {{ scope.row.status === 1 ? '启用' : '禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template #default="scope">
                    <el-button 
                      type="primary" 
                      link 
                      @click="loadConfigById(scope.row)"
                    >
                      加载配置
                    </el-button>
                    
                    <el-button 
                      v-if="scope.row.status === 0"
                      type="success" 
                      link 
                      @click="updateConfigStatus(scope.row, 1)"
                    >
                      启用
                    </el-button>
                    
                    <el-button 
                      v-if="scope.row.status === 1"
                      type="warning" 
                      link 
                      @click="updateConfigStatus(scope.row, 0)"
                    >
                      禁用
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else class="collapsed-card-icon" @click="isConfigListCollapsed = false">
              <el-icon><Document /></el-icon>
              <span>配置列表 ({{ uiConfigs.length }})</span>
            </div>
          </el-col>
        </el-row>
        </div>

          <!-- 新增一行 -->
          <el-row :gutter="20" class="page-config-row">
            <el-col :span="8">
              <el-input
                v-model="pageTitle"
                placeholder="请输入页面标题"
                class="w-100"
              />
            </el-col>
            <el-col :span="8">
              <el-input v-model="frontendPath" placeholder="请输入前端路径"><template #prepend>
                <IconSelector v-model="selectedIcon" />
              </template>
            </el-input>
            </el-col>
            <el-col :span="8">
              <el-button @click="generateConfigFromSource" type="success">生成配置</el-button>
              <!-- 新增按钮：从已有配置加载 -->
              <!-- <el-button @click="loadSelectedConfig" type="primary" :disabled="!selectedUiConfig">加载配置</el-button> -->
            </el-col>
          </el-row>
        </div>
    </el-card>

    <!-- api列表卡片 -->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>api列表</span>
          <el-button 
            type="text" 
            @click="isApiListCollapsed = !isApiListCollapsed"
            :icon="isApiListCollapsed ? 'Expand' : 'Fold'"
          >
            {{ isApiListCollapsed ? '展开' : '折叠' }}
          </el-button>
        </div>
      </template>
      <div v-if="!isApiListCollapsed">
        <el-table :data="apis" style="width: 100%" row-key="id" border>
          <el-table-column type="expand">
            <template #default="props">
              <p><strong>Path:</strong> {{ props.row.path }}</p>
              <p><strong>Method:</strong> {{ props.row.method }}</p>
              <p><strong>Description:</strong> {{ props.row.description }}</p>
              <p><strong>Controller:</strong> {{ props.row.controller_name }}</p>
            </template>
          </el-table-column>
          <el-table-column label="ID" prop="id" width="80" />
          <el-table-column label="Name" prop="name" width="200" />
          <el-table-column label="Path" prop="path" />
          <el-table-column label="Method" prop="method" width="100" />
          <el-table-column label="Description" prop="description" />
          <el-table-column label="Controller" prop="controller_name" />
          <el-table-column label="Status" prop="status" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? 'Active' : 'Inactive' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button 
                type="primary" 
                link 
                @click="setBaseUrl(scope.row.path)"
                :disabled="!selectedDto || !pageConfig"
              >
                设为baseUrl
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else class="collapsed-card-icon" @click="isApiListCollapsed = false">
        <el-icon><Document /></el-icon>
        <span>API列表 ({{ apis.length }})</span>
      </div>
    </el-card>

    <!-- controllers列表卡片 -->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>controllers列表</span>
          <el-button 
            type="text" 
            @click="isControllersListCollapsed = !isControllersListCollapsed"
            :icon="isControllersListCollapsed ? 'Expand' : 'Fold'"
          >
            {{ isControllersListCollapsed ? '展开' : '折叠' }}
          </el-button>
        </div>
      </template>
      <div v-if="!isControllersListCollapsed">
        <el-table :data="controllers" style="width: 100%" border>
          <el-table-column type="expand">
            <template #default="props">
              <p><strong>方法介绍:</strong> {{ props.row.method_description }}</p>
            </template>
          </el-table-column>
          <el-table-column label="ID" prop="id" width="80" />
          <el-table-column label="方法名" prop="method_name" width="200" />
          <el-table-column label="描述" prop="method_description" />
          <el-table-column label="dtos" prop="used_dto_ids" />
        </el-table>
      </div>
      <div v-else class="collapsed-card-icon" @click="isControllersListCollapsed = false">
        <el-icon><Setting /></el-icon>
        <span>控制器列表 ({{ controllers.length }})</span>
      </div>
    </el-card>

    <!-- 配置编辑组件 -->
    <PageConfigEditor
      v-if="selectedDto && pageConfig"
      v-model="pageConfig"
      @refresh-page="refreshBasePage"
      :dto-source-json="dtoSourceJson"
      @update:dto-source-json="updateDtoSourceJson"
      @generate-from-dto="handleGenerateFromDto"
    />

    <!-- 使用BasePage组件渲染自动生成的配置 -->
    <el-card class="box-card" v-if="selectedDto && pageConfig && showPage">
      <template #header>
        <div class="card-header">
          <span>{{ clonedPageConfig.title }}</span>
          <div>
            <el-button type="primary" size="small" @click="regenerateConfig">重新生成配置</el-button>
            <el-button type="success" size="small" @click="saveConfig">保存配置</el-button>
          </div>
        </div>
      </template>
      
      <BasePage
        :title="clonedPageConfig.title || '自动生成页面'"
        :service-config="clonedPageConfig.serviceConfig"
        :table-config="clonedPageConfig.tableConfig"
        :form-config="clonedPageConfig.formConfig"
        :search-config="clonedPageConfig.searchConfig"
        :toolbar-config="clonedPageConfig.toolbarConfig"
        :info-config="clonedPageConfig.infoConfig"
        @toolbar-action="handleToolbarAction"
        @row-action="handleRowAction"
        @search="handleSearch"
        @upload-success="handleUploadSuccess"
      />
    </el-card>



    <!-- 新增 Drawer 组件，用于展示 DTOs -->
    <el-drawer
      v-model="showDtosDrawer"
      title="DTOs 列表"
      size="50%"
      :close-on-click-modal="true"
      :append-to-body="false"
    >
      <template #header>
        <el-input
          v-model="filterKeyword"
          placeholder="请输入关键词筛选"
          style="margin-bottom: 20px;"
          clearable
        />
      </template>
      <div v-for="(dto, index) in visibleDtos" :key="dto.id+':'+index" class="dto-card">
        <el-card 
          :class="{ 'dto-card-selected': selectedDto && selectedDto.id === dto.id }"
          @click="setDtoSource(dto)"
          shadow="hover"
        >
          <div slot="header" class="clearfix">
            <span>{{ dto.name }}</span>
            <el-button style="float: right; padding: 3px 0" link @click.stop="setDtoSource(dto)">选择为DTO源</el-button>
          </div>
          <el-table :data="Object.entries(dto.structure).map(([key, value]) => ({ key, ...(value as any) }))" border>
            <el-table-column label="字段名" prop="key" width="180" />
            <el-table-column label="描述" prop="description" />
            <el-table-column label="类型" prop="type" width="100" />
            <el-table-column label="验证" prop="validation" width="280" />
          </el-table>
        </el-card>
      </div>
      <!-- 懒加载占位元素 -->
      <div ref="lazyLoadTrigger" style="height: 20px;"></div>
    </el-drawer>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick } from 'vue';
import { getApiDoc, getGroupNames, getDtos, getControllers, getUiConfigs, createConfigs, updateUiConfig } from '../api'; 
import { useApiManagementStore } from '@/modules/admin/stores/apiManagement'; 
import { PageConfigGenerator } from '@/modules/admin/components/PageConfigGenerator';
import type { ApiStructure } from '@/modules/admin/components/PageConfigGenerator';
import { BasePage } from '@/components/page';
import PageConfigEditor from '@/components/page/PageConfigEditor.vue';
import { ElMessage } from 'element-plus';
import localforage from 'localforage';
import { cloneDeep } from 'lodash';
import IconSelector from '@/components/page/IconSelector.vue'
import type { AdminUIConfigDTO } from '../types';
//import type { PageConfig } from '@/types/page';

const showPage = ref(false);

const apiManagementStore = useApiManagementStore(); 

const modules = ref(['admin', 'merchant', 'user', 'order', 'apidoc', 'ui_config', 'payment', 'delivery', 'notification', 'report', 'setting']);
const selectedModule = ref('admin');
const apiDoc = ref('');

const originalApis = ref<any[]>([]);
const apis = ref<any[]>([]);
const groupNames = ref<GroupNames[]>([]);
const selectedGroup = ref('');
const dtos = ref<any[]>([]);
const controllers = ref<any[]>([]);
const originalControllers = ref<any[]>([]);
const selectedDto = ref<any>(null);
const pageConfig = ref<any>(null);
//const loading = ref(false);
const showDtosDrawer = ref(false); // 新增状态变量，用于控制 Drawer 显示
const filterKeyword = ref(''); // 筛选关键词状态变量
const selectedUiConfig = ref<any>(null);
const isApiListCollapsed = ref(false);
const isControllersListCollapsed = ref(false);
const isConfigListCollapsed = ref(false);

// 定义 configData 的接口
interface ConfigData {
  icon: string;
  module: string;
  group: string;
  title: string;
  frontend_path: string;
  config_type: string;
  config_key: string;
  config_content: string;
  remark: string;
  status: number;
  version: string;
  version_hash: string;
  dto?: string; // 添加 dto 属性，类型为 string，可选
}

const filteredDtos = computed(() => {
  console.log('filterKeyword.value:', filterKeyword.value);
  if (!filterKeyword.value) return dtos.value;

  return dtos.value.filter(dto =>
    dto.name.toLowerCase().includes(filterKeyword.value.toLowerCase())
  );
});

interface GroupNames {
  page_name: string;
}

async function handleModuleChange() {
  selectedGroup.value = '';
  await fetchApiDoc();
  await fetchGroupNames();
  await fetchDtos();
  await fetchControllers();
}

async function handleGroupChange() {
  console.log('handleGroupChange selectedGroup.value:', selectedGroup.value, originalApis.value, originalControllers.value);
  filterApiDoc(cloneDeep(originalApis.value));
  const tempControllers = cloneDeep(originalControllers.value);
  console.log('apis.value:', apis.value, selectedGroup.value, originalApis.value);
  await fetchUiConfigs();
  // 新增：筛选 controllers
  const actNames = apis.value.map(api => api.act_name).filter(Boolean); // 提取 act_name 数组
  if (actNames.length > 0) {
    controllers.value = tempControllers.filter(controller => 
      actNames.includes(controller.method_name)
    );
  } else {
    controllers.value = [];
  }
}

async function fetchApiDoc() {
  try {
    const storedApis: any = await apiManagementStore.getApis(selectedModule.value);
    if (storedApis) {
      console.log('storedApis:', storedApis);
      apiDoc.value = JSON.stringify({ list: storedApis }, null, 2);
      originalApis.value = storedApis; // 存储原始数据
      filterApiDoc(originalApis.value); // 使用克隆数据过滤
    } else {
      const response: any = await getApiDoc({ module: selectedModule.value, page: 1, pageSize: 1000 });
      console.log('apiDoc response:', response);
      apiDoc.value = JSON.stringify(response, null, 2);
      originalApis.value = response.list; // 存储原始数据
      // 保存 apis 到 store
      await apiManagementStore.saveApis(selectedModule.value, response.list);
      filterApiDoc(originalApis.value); // 使用克隆数据过滤
    }
  } catch (error) {
    console.error('Failed to fetch API documentation:', error);
    apiDoc.value = 'Failed to fetch API documentation.';
  }
}

async function fetchGroupNames() {
  try {
    const storedGroups = await apiManagementStore.getGroups(selectedModule.value);
    if (storedGroups) {
      groupNames.value = storedGroups;
    } else {
      const response:any = await getGroupNames(selectedModule.value);
      console.log('groupNames response:', response);
      groupNames.value = response.list;
      // 保存 groups 到 store
      await apiManagementStore.saveGroups(selectedModule.value, response.list);
    }
  } catch (error) {
    console.error('Failed to fetch group names:', error);
    groupNames.value = [];
  }
}

async function fetchDtos() {
  try {
    const storedDtos = await apiManagementStore.getDtos(selectedModule.value);
    if (storedDtos) {
      dtos.value = storedDtos;
    } else {
      const response: any = await getDtos({ module: selectedModule.value, page: 1, pageSize: 1000 });
      dtos.value = response.list;
      await apiManagementStore.saveDtos(selectedModule.value, response.list);
    }
  } catch (error) {
    console.error('Failed to fetch dtos:', error);
    dtos.value = [];
  }
}

async function fetchControllers() {
  console.log('fetchControllers selectedModule.value:', selectedModule.value);
  try {
    console.log('fetchControllers try');
    const storedControllers = await apiManagementStore.getControllers(selectedModule.value);
    console.log('fetchControllers storedControllers:', storedControllers);
    if (storedControllers) {
      originalControllers.value = storedControllers;
      controllers.value = storedControllers;
      console.log('fetchControllers originalControllers:', originalControllers.value);
    } else {
      const response: any = await getControllers({ module: selectedModule.value, page: 1, pageSize: 1000 });
      originalControllers.value = response.list;
      await apiManagementStore.saveControllers(selectedModule.value, response.list);
    }
  } catch (error) {
    console.error('Failed to fetch controllers:', error);
    originalControllers.value = [];
  }
}

function filterApiDoc(inapis: any[]) {
  console.log('filterApiDoc inapis:', inapis, selectedGroup.value);
  if (selectedGroup.value) {
    console.log('inapis:', inapis, selectedGroup.value, `${selectedModule.value}/${selectedGroup.value}`);
    // 将 selectedModule 和 selectedGroup 中的 - 和 _ 替换为统一字符
    const normalizedModule = selectedModule.value.replace(/[-_]/g, '');
    const normalizedGroup = selectedGroup.value.replace(/[-_]/g, '');
    
    const filteredApis = inapis.filter(api => {
      // 将 api.path 中的 - 和 _ 替换为统一字符
      const normalizedPath = api.path.replace(/[-_]/g, '');
      return normalizedPath.includes(`${normalizedModule}/${normalizedGroup}`) || 
             normalizedPath.includes(`${normalizedModule}/secured/${normalizedGroup}`);
    });
    console.log('filteredApis:', filteredApis);
    apiDoc.value = JSON.stringify({ list: filteredApis }, null, 2);

    // 使用 splice 清空并重新填充 apis，确保响应式更新
    apis.value.splice(0, apis.value.length, ...filteredApis);
  } else {
    apiDoc.value = JSON.stringify({ list: inapis }, null, 2);

    // 使用 splice 清空并重新填充 apis，确保响应式更新
    apis.value.splice(0, apis.value.length, ...inapis);
  }
}

/**
 * 选择DTO以生成配置页面
 * @param dto DTO对象
 */
function selectDto(dto: any) {
  showPage.value = false;
  selectedDto.value = null;
  nextTick(() => {
    selectedDto.value = dto;
    // 保存当前的baseUrl
    const currentBaseUrl = pageConfig.value?.serviceConfig?.baseUrl;
    generateConfig(dto);
    // 恢复原有的baseUrl
    if (pageConfig.value && currentBaseUrl) {
      pageConfig.value.serviceConfig.baseUrl = currentBaseUrl;
    }
    showPage.value = true;
  });
}

/**
 * 生成页面配置
 * @param dto DTO对象
 */
function generateConfig(dto: any) {
  if (!dto || !dto.structure) {
    ElMessage.warning('DTO结构不完整，无法生成配置');
    return;
  }

  // 将DTO结构转换为API结构
  const apiStructure: ApiStructure = {};
  Object.entries(dto.structure).forEach(([key, value]: [string, any]) => {
    apiStructure[key] = {
      description: value.description || key,
      type: value.type || 'string',
      validation: value.validation || ''
    };
  });

  // 使用PageConfigGenerator生成配置
  const config = PageConfigGenerator.createConfigFromStructure(
    dto.name,
    `v1/admin/${dto.name.toLowerCase()}`,
    apiStructure,
    {
      statusField: 'status',
      dateFields: ['created_at', 'updated_at', 'last_login_at', 'birthday'],
      imageFields: ['avatar', 'image', 'cover', 'thumbnail']
    }
  );
  
  // 确保infoConfig存在
  if (!config.infoConfig) {
    // 从表单配置中复制列生成详情配置
    const infoColumns = [...config.formConfig.columns];
    config.infoConfig = {
      columns: infoColumns,
      border: true,
      column: 2, // 默认2列
      size: 'default',
      direction: 'horizontal'
    };
  }
  
  // 如果服务配置里没有viewTitle，则添加
  if (!config.serviceConfig.viewTitle) {
    config.serviceConfig.viewTitle = `查看${dto.name}详情`;
  }
  
  pageConfig.value = config;
  ElMessage.success(`已为 ${dto.name} 生成页面配置`);
}

/**
 * 重新生成配置
 */
function regenerateConfig() {
  if (dtoSource.value) {
    generateConfig(dtoSource.value);
  } else {
    ElMessage.warning('请先选择一个DTO');
  }
}

/**
 * 保存配置到本地存储
 */
async function saveConfig() {
  if (!pageConfig.value) {
    ElMessage.warning('没有可保存的配置');
    return;
  }
  
  try {
    // 创建一个安全的可克隆版本，通过JSON序列化和反序列化来移除不可克隆的对象
    const safeConfig = JSON.parse(JSON.stringify(pageConfig.value));
    
    // 保存到本地存储
    await localforage.setItem(`page_config_${selectedDto.value.name}`, {
      version: safeConfig.version,
      config: safeConfig,
      updated: new Date().toISOString()
    });
    
    // 构建后端需要的配置结构 - 根据后端DTO结构调整
    const configData: ConfigData = {
      module: selectedModule.value,
      icon: selectedIcon.value,
      group: selectedGroup.value || '',
      title: pageTitle.value || selectedDto.value.name,
      frontend_path: frontendPath.value || `v1/admin/${selectedDto.value.name.toLowerCase()}`,
      config_type: 'page', 
      config_key: `${selectedModule.value}_${selectedGroup.value || 'default'}_${selectedDto.value.name}`,
      config_content: JSON.stringify(safeConfig), // 将对象转为字符串，避免嵌套对象导致的解析问题
      remark: `${pageTitle.value || selectedDto.value.name}的页面配置`,
      status: 1,
      version: safeConfig.version || '1.0.0',
      version_hash: calculateConfigChecksum(safeConfig)
    };
    
    // 处理DTO数据 - 确保可以安全序列化
    if (dtoSourceJson.value) {
      try {
        // 尝试解析dtoSourceJson，确保它是一个有效的JSON字符串
        const parsedDto = typeof dtoSourceJson.value === 'string' 
          ? JSON.parse(dtoSourceJson.value) 
          : dtoSourceJson.value;
        
        // 使用JSON序列化和反序列化移除可能的循环引用
        const safeDto = JSON.parse(JSON.stringify(parsedDto));
        
        // 保存为字符串
        configData.dto = JSON.stringify(safeDto);
      } catch (error) {
        console.error('处理DTO数据失败:', error);
        // 如果处理失败，使用一个简化版本
        if (selectedDto.value) {
          const simplifiedDto = {
            id: selectedDto.value.id,
            name: selectedDto.value.name,
            structure: selectedDto.value.structure ? JSON.parse(JSON.stringify(selectedDto.value.structure)) : {}
          };
          configData.dto = JSON.stringify(simplifiedDto);
        }
      }
    }
    
    let response: any;
    
    // 判断是更新还是创建
    if (selectedUiConfig.value && selectedUiConfig.value.id) {
      // 更新现有配置
      console.log('更新配置ID:', selectedUiConfig.value.id);
      response = await updateUiConfig(selectedUiConfig.value.id, configData);
      console.log('更新后端响应:', response);
      if (response) {
        ElMessage.success('配置已成功更新并保存到本地存储');
      } else {
        ElMessage.warning(`配置已保存到本地，但更新后端失败: ${response?.msg || '未知错误'}`);
      }
    } else {
      // 创建新配置
      response = await createConfigs(configData);
      console.log('创建后端响应:', response);
      if (response) {
        // 如果创建成功，保存创建的配置ID以便后续更新
        if (response.data && response.data.id) {
          selectedUiConfig.value = { 
            ...configData, 
            id: response.data.id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
        }
        ElMessage.success('配置已成功创建并保存到本地存储');
      } else {
        ElMessage.warning(`配置已保存到本地，但保存到后端失败: ${response?.msg || '未知错误'}`);
      }
    }
  } catch (error) {
    console.error('保存配置失败:', error);
    ElMessage.error(`保存配置失败: ${error instanceof Error ? error.message : '未知错误'}`);
    
    // 尝试仅保存到后端
    try {
      const safeConfig = JSON.parse(JSON.stringify(pageConfig.value));
      
      // 根据后端DTO格式调整
      const configData: ConfigData = {
        icon: selectedIcon.value,
        module: selectedModule.value,
        group: selectedGroup.value || '',
        title: pageTitle.value || selectedDto.value.name,
        frontend_path: frontendPath.value || `v1/admin/${selectedDto.value.name.toLowerCase()}`,
        config_type: 'page',
        config_key: `${selectedModule.value}_${selectedGroup.value || 'default'}_${selectedDto.value.name}`,
        config_content: JSON.stringify(safeConfig), // 将对象转为字符串
        remark: `${pageTitle.value || selectedDto.value.name}的页面配置`,
        status: 1,
        version: safeConfig.version || '1.0.0',
        version_hash: calculateConfigChecksum(safeConfig)
      };
      
      // 处理DTO数据 - 确保可以安全序列化
      if (dtoSourceJson.value) {
        try {
          // 尝试解析dtoSourceJson，确保它是一个有效的JSON字符串
          const parsedDto = typeof dtoSourceJson.value === 'string' 
            ? JSON.parse(dtoSourceJson.value) 
            : dtoSourceJson.value;
          
          // 使用JSON序列化和反序列化移除可能的循环引用
          const safeDto = JSON.parse(JSON.stringify(parsedDto));
          
          // 保存为字符串
          configData.dto = JSON.stringify(safeDto);
        } catch (error) {
          console.error('处理DTO数据失败:', error);
          // 如果处理失败，使用一个简化版本
          if (selectedDto.value) {
            const simplifiedDto = {
              id: selectedDto.value.id,
              name: selectedDto.value.name,
              structure: selectedDto.value.structure ? JSON.parse(JSON.stringify(selectedDto.value.structure)) : {}
            };
            configData.dto = JSON.stringify(simplifiedDto);
          }
        }
      }
      
      let response: any;
      
      // 判断是更新还是创建
      if (selectedUiConfig.value && selectedUiConfig.value.id) {
        // 更新现有配置
        response = await updateUiConfig(selectedUiConfig.value.id, configData);
        console.log('更新后端响应:', response);
        if (response) {
          ElMessage.success('配置已成功更新（但本地保存失败）');
        } else {
          ElMessage.warning(`更新配置失败: ${response?.msg || '未知错误'}`);
        }
      } else {
        // 创建新配置
        response = await createConfigs(configData);
        console.log('创建后端响应:', response);
        if (response) {
          // 如果创建成功，保存创建的配置ID以便后续更新
          if (response.data && response.data.id) {
            selectedUiConfig.value = { 
              ...configData,
              id: response.data.id,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };
          }
          ElMessage.success('配置已成功保存到后端（但本地保存失败）');
        } else {
          ElMessage.warning(`保存到后端失败: ${response?.msg || '未知错误'}`);
        }
      }
    } catch (backendError) {
      console.error('保存到后端也失败:', backendError);
      ElMessage.error('本地保存和后端保存均失败');
    }
  }
}

/**
 * 从本地存储加载配置
 */
// async function loadConfig(dtoName: string) {
//   try {
//     const localConfig = await localforage.getItem<{version: string, config: any}>(`page_config_${dtoName}`);
    
//     if (localConfig) {
//       console.log('从本地存储加载配置:', localConfig);
//       pageConfig.value = localConfig.config;
//       return true;
//     }
//   } catch (error) {
//     console.error('从本地存储加载配置失败:', error);
//   }
//   return false;
// }

/**
 * 处理工具栏操作
 */
function handleToolbarAction(action: string) {
  console.log('工具栏操作:', action);
}

/**
 * 处理行操作
 */
function handleRowAction(action: string, row: any) {
  console.log('行操作:', action, row);
}

/**
 * 处理搜索
 */
function handleSearch(params: any) {
  console.log('搜索参数:', params);
}

/**
 * 处理上传成功
 */
function handleUploadSuccess(url: string, field: string, response: any) {
  console.log('上传成功:', url, field, response);
}

// 监听DTO列表变化，添加点击事件
// watch(dtos, (newDtos) => {
//   if (newDtos && newDtos.length > 0) {
//     // 如果有DTO，默认选择第一个
//     //selectDto(newDtos[0]);
//   }
// });

// 获取DTO数据
// async function getDtoData() {
//   try {
//     const response = await getDtos({ module: selectedModule.value, page: 1, pageSize: 1000 }); 
//     console.log('getDtoData response:', response);
//     // if (response && response.success) {
//     //   dtos.value = response.data || [];
//     // }
//   } catch (error) {
//     console.error('获取DTO数据失败:', error);
//   }
// }

// 初始化时获取默认模块的API文档
onMounted(async () => {
  const storedApis = await apiManagementStore.getApis(selectedModule.value);
  console.log('storedApis:', storedApis);
  if (storedApis) {
    apiDoc.value = JSON.stringify({ list: storedApis }, null, 2);
    originalApis.value = [...storedApis]; // 存储原始数据
    filterApiDoc([...storedApis]); // 使用克隆数据过滤
    apis.value = storedApis;
  } else {
    await fetchApiDoc();
  }
  // 初始化时获取默认模块的分组名
  await fetchGroupNames();
  // 初始化时获取默认模块的 dtos
  await fetchDtos(); 
  // 初始化时获取默认模块的 controllers
  await fetchControllers();
});

async function refreshCurrentModule() {
  await apiManagementStore.removeApis(selectedModule.value);
  await apiManagementStore.removeGroups(selectedModule.value);
  await apiManagementStore.removeDtos(selectedModule.value);
  await apiManagementStore.removeControllers(selectedModule.value);
  await handleModuleChange();
}

async function refreshAllModules() {
  for (const module of modules.value) {
    await apiManagementStore.removeApis(module);
    await apiManagementStore.removeGroups(module);
    await apiManagementStore.removeDtos(module);
    await apiManagementStore.removeControllers(module);
  }
  await handleModuleChange();
}

const visibleDtos = ref<any[]>([]);
const loadedCount = ref(10); // 每次加载的 DTO 数量

// 初始化可见 DTO 列表
watch(filteredDtos, (newDtos) => {
  visibleDtos.value = newDtos.slice(0, loadedCount.value);
});

// 懒加载逻辑
onMounted(() => {
  const observer = new IntersectionObserver((entries) => {
    if (entries[0].isIntersecting) {
      loadMoreDtos();
    }
  });

  if (lazyLoadTrigger.value) {
    observer.observe(lazyLoadTrigger.value);
  }
});

function loadMoreDtos() {
  const start = visibleDtos.value.length;
  const end = start + loadedCount.value;
  visibleDtos.value = [...visibleDtos.value, ...filteredDtos.value.slice(start, end)];
}

// 模板中引用的懒加载触发器
const lazyLoadTrigger = ref<HTMLElement | null>(null);

/**
 * 刷新BasePage组件
 */
function refreshBasePage() {
  showPage.value = false;
  nextTick(() => {
    showPage.value = true;
  });
}

/**
 * 计算配置的校验和
 * @param config 页面配置
 * @returns 校验和字符串
 */
function calculateConfigChecksum(config: any): string {
  const configString = JSON.stringify(config, (key, value) => {
    // 排除version字段，避免循环计算
    if (key === 'version') return undefined;
    return value;
  });
  
  // 使用简单的字符串哈希算法
  let hash = 0;
  for (let i = 0; i < configString.length; i++) {
    const char = configString.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return Math.abs(hash).toString(36);
}

/**
 * 设置baseUrl
 * @param path API路径
 */
function setBaseUrl(path: string) {
  if (!selectedDto.value || !pageConfig.value) return;
  
  // 移除路径中的/api/前缀
  const baseUrl = path.replace(/^\/api\//, '');
  
  // 更新serviceConfig的baseUrl
  pageConfig.value.serviceConfig.baseUrl = baseUrl;
  
  // 重新计算并更新version
  pageConfig.value.version = calculateConfigChecksum(pageConfig.value);
  
  ElMessage.success('已更新baseUrl');
}

// 修改handleConfigChange方法
// function handleConfigChange() {
//   if (pageConfig.value) {
//     // 重新计算并更新version
//     pageConfig.value.version = calculateConfigChecksum(pageConfig.value);
//   }
// }

// 新增响应式变量，用于存储 UI 配置信息
const uiConfigs = ref<any[]>([]);

// 新增方法：获取现有 UI 配置
async function fetchUiConfigs() {
  try {
    const response:any = await getUiConfigs({modules: selectedModule.value, group: selectedGroup.value, page: 1, pageSize: 1000});
    console.log('fetchUiConfigs response:', response);
    if (response.list && response.list.length > 0) {
      uiConfigs.value = response.list;
      ElMessage.success('成功获取现有 UI 配置');
    } else {
      ElMessage.warning('获取 UI 配置失败：后端返回错误');
    }
  } catch (error) {
    console.error('获取现有 UI 配置失败:', error);
    ElMessage.error('获取现有 UI 配置失败');
  }
}

// 新增状态变量
const pageTitle = ref('');
// 前端路径
const frontendPath = ref('');
// 选中的图标
const selectedIcon = ref('')
const dtoSource = ref<any>(null);
const dtoSourceJson = ref('');

// 更新DTO源JSON
function updateDtoSourceJson(val: string) {
  dtoSourceJson.value = val;
  try {
    // 尝试解析JSON
    const dto = JSON.parse(val);
    dtoSource.value = dto;
    
    // 如果还没有选择过DTO，则自动设置
    if (!selectedDto.value && dto && Object.keys(dto).length > 0) {
      selectedDto.value = dto;
      
      // 如果还没有配置，则生成一个基础配置
      if (!pageConfig.value) {
        generateConfigFromSource();
      }
    }
  } catch (error) {
    console.error('解析DTO源JSON失败:', error);
  }
}

// 修改setDtoSource函数
function setDtoSource(dto: any) {
  const modifiedDto = { ...dto, name: pageTitle.value };
  dtoSource.value = modifiedDto;
  dtoSourceJson.value = JSON.stringify(modifiedDto, null, 2);
  ElMessage.success(`已将 ${modifiedDto.name} 设置为DTO源`);
  showDtosDrawer.value = false;
  
  // 如果还没有选择DTO，则自动选择此DTO
  if (!selectedDto.value) {
    selectedDto.value = modifiedDto;
  }
}

/**
 * 从DTO源生成配置
 */
function handleGenerateFromDto() {
  try {
    if (!dtoSource.value) {
      ElMessage.warning('DTO源为空，请先选择一个DTO');
      return;
    }
    
    if (!dtoSource.value.structure) {
      ElMessage.warning('DTO源结构为空');
      return;
    }
    
    // 将DTO结构转换为API结构
    const apiStructure: ApiStructure = {};
    Object.entries(dtoSource.value.structure).forEach(([key, value]: [string, any]) => {
      apiStructure[key] = {
        description: value.description || key,
        type: value.type || 'string',
        validation: value.validation || ''
      };
    });

    // 使用PageConfigGenerator生成配置
    const config = PageConfigGenerator.createConfigFromStructure(
      pageTitle.value || dtoSource.value.name,
      frontendPath.value || `v1/admin/${dtoSource.value.name.toLowerCase()}`,
      apiStructure,
      {
        statusField: 'status',
        dateFields: ['created_at', 'updated_at', 'last_login_at', 'birthday'],
        imageFields: ['avatar', 'image', 'cover', 'thumbnail']
      }
    );
    
    // 更新标题
    config.title = pageTitle.value || dtoSource.value.name;
    
    // 确保infoConfig存在
    if (!config.infoConfig) {
      // 从表单配置中复制列生成详情配置
      const infoColumns = [...config.formConfig.columns];
      config.infoConfig = {
        columns: infoColumns,
        border: true,
        column: 2,
        size: 'default',
        direction: 'horizontal'
      };
    }
    
    // 更新配置
    pageConfig.value = config;
    selectedDto.value = dtoSource.value;
    showPage.value = true;
    
    ElMessage.success(`已从DTO源生成配置：${config.title}`);
  } catch (error) {
    console.error('从DTO源生成配置失败:', error);
    ElMessage.error('从DTO源生成配置失败');
  }
}

// 添加generateConfigFromSource方法
function generateConfigFromSource() {
  if (!dtoSource.value) {
    ElMessage.warning('请先选择一个DTO源');
    return;
  }
  
  if (!pageTitle.value) {
    ElMessage.warning('请输入页面标题');
    return;
  }
  
  // 调用handleGenerateFromDto方法来生成配置
  handleGenerateFromDto();
}

/**
 * 为el-autocomplete提供建议
 * @param queryString 输入的查询字符串
 * @param callback 回调函数，用于返回建议列表
 */
// function queryUiConfigs(queryString: string, callback: (data: any[]) => void) {
//   const results = queryString
//     ? uiConfigs.value.filter(config => {
//         const title = config.title || '';
//         const key = config.config_key || '';
//         const path = config.frontend_path || '';
//         return title.toLowerCase().includes(queryString.toLowerCase()) || 
//                key.toLowerCase().includes(queryString.toLowerCase()) ||
//                path.toLowerCase().includes(queryString.toLowerCase());
//       })
//     : uiConfigs.value;
  
//   callback(results);
// }

/**
 * 处理UI配置选择事件
 * @param item 选择的配置项
 */
// function handleUiConfigSelect(item: any) {
//   console.log('handleUiConfigSelect item:', item);
//   selectedUiConfig.value = item;
//   pageTitle.value = item.title || '';
//   frontendPath.value = item.frontend_path || '';
//   selectedIcon.value = item.icon || '';
// }

/**
 * 从选中的UI配置加载数据到PageConfigEditor
 */
function loadSelectedConfig(config: AdminUIConfigDTO) {
  if (!config) {
    ElMessage.warning('请先选择一个配置');
    return;
  }
  
  try {
    console.log('loadSelectedConfig config:', config);
    // 先重置编辑器状态
    showPage.value = false;
    selectedDto.value = null;
    pageConfig.value = null;
    
    // 解析config_content从JSON字符串到对象
    //const configContent = JSON.parse(config.config_content);
    //const dto = config.dto ? JSON.parse(config.dto) : null;
    
    // 使用nextTick确保DOM更新后再设置值
    nextTick(() => {
      // 设置DTO源数据
      if (config.dto) {
        dtoSource.value = config.dto;
        dtoSourceJson.value = JSON.stringify(config.dto, null, 2);
        selectedDto.value = config.dto;
      }
      
      // 设置页面标题和前端路径
      pageTitle.value = config.title || '';
      frontendPath.value = config.frontend_path || '';
      
      // 确保页面配置是完整的对象 - 使用深拷贝避免引用问题
      pageConfig.value = config.config_content;
      
      // 再次使用nextTick确保配置完全加载后再显示页面
      nextTick(() => {
        showPage.value = true;
        ElMessage.success(`已加载配置: ${config.title || config.config_key}`);
      });
    });
  } catch (error) {
    console.error('加载配置失败:', error);
    ElMessage.error(`加载配置失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 加载配置
 * @param config 配置项
 */
function loadConfigById(config: any) {
  selectedUiConfig.value = config;
  loadSelectedConfig(config);
}

/**
 * 更新配置图标
 * @param config 需要更新图标的配置
 */
function updateConfigIcon(config: any) {
  if (!config || !config.id) {
    ElMessage.warning('无法更新图标：无效的配置');
    return;
  }
  
  try {
    // 构建更新对象
    const updateData = {
      icon: config.icon
    };
    
    // 调用API更新配置
    updateUiConfig(config.id, updateData)
      .then((response: any) => {
        if (response) {
          ElMessage.success('图标已更新');
        } else {
          ElMessage.warning('图标更新失败');
        }
      })
      .catch((error: any) => {
        console.error('更新图标出错:', error);
        ElMessage.error(`更新图标失败: ${error instanceof Error ? error.message : '未知错误'}`);
      });
  } catch (error) {
    console.error('处理图标更新出错:', error);
    ElMessage.error(`处理图标更新失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

async function updateConfigStatus(config: any, status: number) {
  try {
    // 构建更新对象
    const updateData = {
      status: status
    };
    
    // 调用API更新配置
    const response = await updateUiConfig(config.id, updateData);
    console.log('更新后端响应:', response);
    if (response) {
      // 更新本地缓存
      config.status = status;
      
      // 如果是启用操作，则禁用其他配置
      if (status === 1) {
        ElMessage.success('配置已启用，其他配置将被自动禁用');
        
        // 禁用其他所有配置
        for (const item of uiConfigs.value) {
          // 跳过当前项
          if (item.id === config.id) continue;
          
          // 如果发现其他启用状态的配置，将其禁用
          if (item.status === 1) {
            try {
              await updateUiConfig(item.id, { status: 0 });
              item.status = 0; // 更新本地状态
            } catch (disableError) {
              console.error(`禁用配置 ${item.id} 失败:`, disableError);
            }
          }
        }
      } else {
        ElMessage.success('配置已禁用');
      }
    } else {
      ElMessage.warning('配置状态更新失败');
    }
  } catch (error) {
    console.error('更新配置状态出错:', error);
    ElMessage.error(`更新配置状态失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

const clonedPageConfig = computed(() => {
  return JSON.parse(JSON.stringify(pageConfig.value));
});
</script>

<style scoped>
.api-management {
  padding: 20px;
}

.module-selector {
  margin-bottom: 20px;
}

.page-config-row {
  margin-top: 20px;
}

.api-doc-container {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.box-card {
  margin-bottom: 20px;
}

.dto-card {
  margin-bottom: 20px;
}

.dto-card-selected {
  border-color: #409eff;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.2);
}

.custom-action-item,
.column-item,
.form-item,
.button-item,
.search-item {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.el-divider {
  margin: 15px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.collapsed-card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  cursor: pointer;
  color: #409eff;
  transition: all 0.3s ease;
}

.collapsed-card-icon:hover {
  background-color: #f5f7fa;
}

.collapsed-card-icon .el-icon {
  font-size: 24px;
  margin-right: 10px;
}
</style>