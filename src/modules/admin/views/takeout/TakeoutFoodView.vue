<!-- 
 * 管理员外卖商品管理页面
 * 显示所有商家的外卖商品，支持分页、搜索和状态筛选
 * 提供审核功能
 -->
<template>
  <div class="food-admin-container">
    <el-card class="header-card">
      <div class="page-header">
        <h2>外卖商品管理</h2>
      </div>
    </el-card>
    
    <!-- 搜索和筛选区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="商品名称">
          <el-input v-model="searchForm.keyword" placeholder="请输入商品名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="商家ID">
          <el-input v-model="searchForm.merchantId" placeholder="请输入商家ID" clearable></el-input>
        </el-form-item>
        <el-form-item label="商品分类">
          <el-select v-model="searchForm.categoryId" placeholder="全部分类" clearable>
            <el-option 
              v-for="category in categories" 
              :key="category.id" 
              :label="category.name" 
              :value="category.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
            <el-option label="草稿" :value="FoodStatus.DRAFT"></el-option>
            <el-option label="上架销售中" :value="FoodStatus.ON_SALE"></el-option>
            <el-option label="已下架" :value="FoodStatus.OFF_SALE"></el-option>
            <el-option label="已售罄" :value="FoodStatus.SOLD_OUT"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="searchForm.audit_status" placeholder="全部审核状态" clearable>
            <el-option label="未审核" :value="AuditStatus.PENDING"></el-option>
            <el-option label="审核通过" :value="AuditStatus.APPROVED"></el-option>
            <el-option label="审核拒绝" :value="AuditStatus.REJECTED"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchFoods">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 商品列表 -->
    <el-card class="list-card">
      <el-table
        v-loading="loading"
        :data="foodList"
        border
        style="width: 100%"
        max-height="500"
      >
        <el-table-column label="商品图片" width="100">
          <template #default="scope">
            <el-image 
              :src="scope.row.image || '/images/default-food.png'" 
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 4px;"
            ></el-image>
          </template>
        </el-table-column>
        
        <el-table-column prop="name" label="商品名称" show-overflow-tooltip></el-table-column>
        
        <el-table-column prop="merchant_name" label="所属商家" width="150" show-overflow-tooltip></el-table-column>
        
        <el-table-column prop="category_name" label="商品分类" width="120"></el-table-column>
        
        <el-table-column prop="price" label="价格" width="100">
          <template #default="scope">
            <span>{{ formatPrice(scope.row.price) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="daily_limit" label="每日限购" width="100"></el-table-column>
        
        <el-table-column prop="status" label="商品状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="audit_status" label="审核状态" width="100">
          <template #default="scope">
            <el-tag :type="getAuditStatusTagType(scope.row.audit_status)" size="small">
              {{ getAuditStatusText(scope.row.audit_status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="scope">
            <el-button 
              link 
              type="primary" 
              size="small" 
              @click="viewFoodDetail(scope.row.id)"
            >查看详情</el-button>
            
            <el-button 
              v-if="scope.row.audit_status === TakeoutFoodAuditStatus.PENDING" 
              link 
              type="success" 
              size="small" 
              @click="showAuditDialog(scope.row)"
            >审核</el-button>
            
            <el-button 
              v-if="scope.row.status === FoodStatus.ON_SALE" 
              link 
              type="warning" 
              size="small" 
              @click="offlineFood(scope.row.id)"
            >下架</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-card>
    
    <!-- 审核对话框 -->
    <el-dialog v-model="auditDialogVisible" title="商品审核" width="500px">
      <div class="audit-dialog-content">
        <div class="audit-food-info">
          <div class="audit-food-image">
            <el-image 
              :src="currentFood?.image || '/images/default-food.png'" 
              fit="cover"
              style="width: 80px; height: 80px; border-radius: 4px;"
            ></el-image>
          </div>
          <div class="audit-food-details">
            <h3>{{ currentFood?.name }}</h3>
            <p>商家：{{ currentFood?.merchant_name }}</p>
            <p>分类：{{ currentFood?.category_name }}</p>
            <p>价格：{{ formatPrice(currentFood?.price || 0) }}</p>
          </div>
        </div>
        
        <div class="audit-actions">
          <el-radio-group v-model="auditForm.status">
            <el-radio :label="AuditStatus.APPROVED">审核通过</el-radio>
            <el-radio :label="AuditStatus.REJECTED">审核拒绝</el-radio>
          </el-radio-group>
        </div>
        
        <div v-if="auditForm.status === AuditStatus.REJECTED" class="audit-reason">
          <el-form-item label="拒绝原因">
            <el-input 
              v-model="auditForm.reason" 
              type="textarea" 
              :rows="3" 
              placeholder="请输入拒绝原因"
            ></el-input>
          </el-form-item>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAudit" :loading="auditLoading">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getAdminFoodList, getAdminFoodCategories, auditFood } from '@/modules/admin/api/takeout';

// 导入商家模块的类型
// 注意：这是从商家模块导入的类型，如果类型不可用，需要在admin模块中再定义
import { TakeoutFoodStatus, TakeoutFoodAuditStatus } from '@/modules/merchant/types';
import type { TakeoutFoodCategory } from '@/modules/merchant/types';

// 商品列表接口
interface FoodListResponse {
  list: TakeoutFood[];
  total: number;
  page: number;
  pageSize: number;
}

// 定义管理员视角的外卖商品接口
interface TakeoutFood {
  id: number;
  merchant_id: number;
  merchant_name: string;
  name: string;
  category_id: number;
  category_name: string;
  image: string;
  brief: string;
  description: string;
  price: number;
  original_price: number;
  packaging_fee: number;
  preparation_time: number;
  daily_limit: number;
  status: number;
  created_at: string;
  updated_at: string;
  is_spicy: boolean;
  tags: string[];
  sort_order: number;
}

// 使用枚举作为常量
const FoodStatus = TakeoutFoodStatus;
const AuditStatus = TakeoutFoodAuditStatus;

// 路由实例
const router = useRouter();

// 页面状态
const loading = ref(false);
const foodList = ref<TakeoutFood[]>([]);
const categories = ref<TakeoutFoodCategory[]>([]);

// 审核相关状态
const auditDialogVisible = ref(false);
const auditLoading = ref(false);
const currentFood = ref<TakeoutFood | null>(null);
const auditForm = reactive({
  status: AuditStatus.APPROVED,
  reason: ''
});

// 搜索表单
const searchForm = reactive({
  keyword: '',
  merchantId: undefined as number | undefined,
  categoryId: undefined as number | undefined,
  status: undefined as number | undefined,
  audit_status: undefined as number | undefined,
});

// 分页参数
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

/**
 * 加载商品分类
 * 获取并显示外卖分类列表
 */
const loadCategories = async () => {
  try {
    const data = await getAdminFoodCategories() as TakeoutFoodCategory[];
    categories.value = data || [];
  } catch (error) {
    console.error('加载分类失败:', error);
    ElMessage.error('加载分类失败');
  }
};

/**
 * 加载商品列表
 * 根据查询条件和分页参数获取外卖商品列表
 */
const loadFoodList = async () => {
  loading.value = true;
  try {
    // 处理搜索参数
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword,
      merchantId: searchForm.merchantId,
      categoryId: searchForm.categoryId,
      status: searchForm.status
    };
    
    const response = await getAdminFoodList(params) as FoodListResponse;
    
    foodList.value = response?.list || [];
    pagination.total = response?.total || 0;
  } catch (error: any) {
    console.error('加载商品列表失败:', error);
    ElMessage.error(error?.message || '加载商品列表失败');
  } finally {
    loading.value = false;
  }
};

/**
 * 查询商品
 * 重置分页并加载符合条件的商品列表
 */
const searchFoods = () => {
  pagination.page = 1;
  loadFoodList();
};

/**
 * 重置查询条件
 * 清空所有查询条件并重新加载列表
 */
const resetSearch = () => {
  searchForm.keyword = '';
  searchForm.merchantId = undefined;
  searchForm.categoryId = undefined;
  searchForm.status = undefined;
  pagination.page = 1;
  loadFoodList();
};

/**
 * 格式化价格
 * 将数字格式化为带人民币符号的价格字符串
 * @param price 价格数值
 * @returns 格式化后的价格字符串
 */
const formatPrice = (price: number) => {
  return `¥${price.toFixed(2)}`;
};

/**
 * 格式化日期时间
 * 将时间戳转换为本地日期时间格式
 * @param timestamp 时间戳
 * @returns 格式化后的日期时间字符串
 */
const formatDateTime = (timestamp: string | number) => {
  if (!timestamp) return '-';
  
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * 获取商品状态文本
 * 根据商品状态码返回对应的状态文本
 * @param status 商品状态码
 * @returns 状态文本
 */
const getStatusText = (status: number) => {
  switch (status) {
    case FoodStatus.DRAFT: return '草稿';
    case FoodStatus.ON_SALE: return '上架销售中';
    case FoodStatus.OFF_SALE: return '已下架';
    case FoodStatus.SOLD_OUT: return '已售罄';
    default: return '未知';
  }
};

/**
 * 获取审核状态文本
 * 根据审核状态码返回对应的文本
 * @param status 审核状态码
 * @returns 状态文本
 */
const getAuditStatusText = (status: number) => {
  switch (status) {
    case AuditStatus.PENDING: return '未审核';
    case AuditStatus.APPROVED: return '审核通过';
    case AuditStatus.REJECTED: return '审核拒绝';
    default: return '未知';
  }
};

/**
 * 获取商品状态标签类型
 * 根据商品状态返回对应的Element Plus标签类型
 * @param status 状态码
 * @returns 标签类型
 */
const getStatusTagType = (status: number) => {
  switch (status) {
    case FoodStatus.DRAFT: return 'info';
    case FoodStatus.ON_SALE: return 'success';
    case FoodStatus.OFF_SALE: return 'info';
    case FoodStatus.SOLD_OUT: return 'danger';
    default: return 'info';
  }
};

/**
 * 获取审核状态标签类型
 * 根据审核状态返回对应的Element Plus标签类型
 * @param status 审核状态码
 * @returns 标签类型
 */
const getAuditStatusTagType = (status: number) => {
  switch (status) {
    case AuditStatus.PENDING: return 'warning';
    case AuditStatus.APPROVED: return 'success';
    case AuditStatus.REJECTED: return 'danger';
    default: return 'info';
  }
};

/**
 * 查看商品详情
 * 跳转到商品详情页面
 * @param id 商品ID
 */
const viewFoodDetail = (id: number) => {
  router.push(`/admin/takeout/food/detail/${id}`);
};

/**
 * 显示审核对话框
 * 设置当前选中的商品并显示审核对话框
 * @param food 商品信息
 */
const showAuditDialog = (food: TakeoutFood) => {
  currentFood.value = food;
  auditForm.status = AuditStatus.APPROVED; // 默认选中审核通过
  auditForm.reason = '';
  auditDialogVisible.value = true;
};

/**
 * 下架商品
 * 将已上架的商品下架
 * @param id 商品ID
 */
const offlineFood = async (id: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要下架该商品吗？',
      '下架确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    await auditFood(id, { audit_status: FoodStatus.OFF_SALE });
    ElMessage.success('商品已下架');
    loadFoodList(); // 重新加载列表
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('下架商品失败:', error);
      ElMessage.error(error?.message || '下架商品失败');
    }
  }
};

/**
 * 提交审核
 * 提交商品审核结果
 */
const submitAudit = async () => {
  if (!currentFood.value) return;
  
  // 如果选择拒绝但没有填写原因
  if (auditForm.status === AuditStatus.REJECTED && !auditForm.reason.trim()) {
    ElMessage.warning('请填写拒绝原因');
    return;
  }
  
  auditLoading.value = true;
  try {
    await auditFood(currentFood.value.id, {
      audit_status: auditForm.status,
      reason: auditForm.reason
    });
    
    auditDialogVisible.value = false;
    ElMessage.success('商品审核成功');
    loadFoodList(); // 重新加载列表
  } catch (error: any) {
    console.error('审核失败:', error);
    ElMessage.error(error?.message || '审核失败');
  } finally {
    auditLoading.value = false;
  }
};

/**
 * 处理分页大小变化
 * @param size 新的页大小
 */
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  loadFoodList();
};

/**
 * 处理当前页变化
 * @param page 新的页码
 */
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  loadFoodList();
};

// 页面加载时初始化
onMounted(() => {
  loadCategories();
  loadFoodList();
});
</script>

<style scoped lang="scss">
.food-admin-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .header-card,
  .search-card,
  .list-card {
    margin-bottom: 20px;
  }
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
  }
  
  .search-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .audit-dialog-content {
    .audit-food-info {
      display: flex;
      margin-bottom: 20px;
      
      .audit-food-image {
        margin-right: 15px;
      }
      
      .audit-food-details {
        h3 {
          margin-top: 0;
          margin-bottom: 8px;
        }
        
        p {
          margin: 5px 0;
          color: #606266;
        }
      }
    }
    
    .audit-actions {
      margin-bottom: 20px;
    }
    
    .audit-reason {
      margin-top: 15px;
    }
  }
}
</style>
