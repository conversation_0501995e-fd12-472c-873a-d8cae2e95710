<!-- 
 * 管理员外卖商品详情和审核页面
 * 显示商品的详细信息和规格，提供审核功能
 * 管理员可以查看并审核待审核的商品
 -->
<template>
  <div class="food-detail-container">
    <el-card class="header-card">
      <div class="page-header">
        <h2>外卖商品详情</h2>
        <div class="header-actions">
          <el-button @click="goBack">返回列表</el-button>
          
          <el-button 
            v-if="foodDetail.audit_status === FoodAuditStatus.PENDING" 
            type="success" 
            @click="approveFood"
            :loading="approving"
          >审核通过</el-button>
          
          <el-button 
            v-if="foodDetail.audit_status === FoodAuditStatus.PENDING" 
            type="danger" 
            @click="showRejectDialog"
          >审核拒绝</el-button>
          
          <el-button 
            v-if="foodDetail.status === FoodStatus.ON_SALE" 
            type="warning" 
            @click="offlineFood"
            :loading="offlining"
          >下架商品</el-button>
        </div>
      </div>
    </el-card>
    
    <div v-loading="loading" class="detail-content" v-if="foodDetail.id">
      <!-- 基本信息卡片 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <h3>基本信息</h3>
            <div class="status-tags">
              <el-tag :type="getStatusTagType(foodDetail.status)" size="small" class="status-tag">
                {{ getStatusText(foodDetail.status) }}
              </el-tag>
              <el-tag :type="getAuditStatusTagType(foodDetail.audit_status)" size="small" class="status-tag">
                {{ getAuditStatusText(foodDetail.audit_status) }}
              </el-tag>
            </div>
          </div>
        </template>
        
        <div class="basic-info">
          <div class="food-main-info">
            <div class="food-image">
              <el-image 
                :src="foodDetail.image || '/images/default-food.png'" 
                fit="cover"
                style="width: 200px; height: 200px; border-radius: 8px;"
                :preview-src-list="[foodDetail.image]"
              ></el-image>
            </div>
            
            <div class="food-info">
              <h2>{{ foodDetail.name }}</h2>
              
              <div class="info-row">
                <div class="info-label">菜单分类：</div>
                <div class="info-value">{{ foodDetail.category_name }}</div>
              </div>
              
              <div class="info-row">
                <div class="info-label">商家信息：</div>
                <div class="info-value">
                  <el-link type="primary" :underline="false" @click="goToMerchant(foodDetail.merchant_id)">
                    {{ foodDetail.merchant_name }} (ID: {{ foodDetail.merchant_id }})
                  </el-link>
                </div>
              </div>
              
              <div class="info-row">
                <div class="info-label">销售价格：</div>
                <div class="info-value price">{{ formatPrice(foodDetail.price) }}</div>
                <div v-if="foodDetail.original_price > 0" class="info-label ml-20">原价：</div>
                <div v-if="foodDetail.original_price > 0" class="info-value original-price">
                  {{ formatPrice(foodDetail.original_price) }}
                </div>
              </div>
              
              <div class="info-row">
                <div class="info-label">包装费：</div>
                <div class="info-value">{{ formatPrice(foodDetail.packaging_fee || 0) }}</div>
                <div class="info-label ml-20">备餐时间：</div>
                <div class="info-value">{{ foodDetail.preparation_time || 0 }} 分钟</div>
              </div>
              
              <div class="info-row">
                <div class="info-label">每日限量：</div>
                <div class="info-value">{{ foodDetail.daily_limit > 0 ? foodDetail.daily_limit : '不限量' }}</div>
                <div class="info-label ml-20">辣度：</div>
                <div class="info-value">
                  <el-tag v-if="foodDetail.is_spicy" type="danger" size="small">辣</el-tag>
                  <span v-else>不辣</span>
                </div>
              </div>
              
              <div class="info-row">
                <div class="info-label">标签：</div>
                <div class="info-value">
                  <el-tag 
                    v-for="(tag, index) in foodDetail.tags" 
                    :key="index"
                    type="info"
                    size="small"
                    class="tag-item"
                  >{{ tag }}</el-tag>
                  <span v-if="!foodDetail.tags || foodDetail.tags.length === 0">无</span>
                </div>
              </div>
              
              <div class="info-row">
                <div class="info-label">创建时间：</div>
                <div class="info-value">{{ formatTime(foodDetail.created_at) }}</div>
              </div>
              
              <div class="info-row">
                <div class="info-label">更新时间：</div>
                <div class="info-value">{{ formatTime(foodDetail.updated_at) }}</div>
              </div>
            </div>
          </div>
          
          <div class="food-description">
            <div class="desc-section">
              <h4>商品简介</h4>
              <p>{{ foodDetail.brief || '暂无简介' }}</p>
            </div>
            
            <div class="desc-section">
              <h4>商品描述</h4>
              <p>{{ foodDetail.description || '暂无描述' }}</p>
            </div>
            
            <div class="desc-section">
              <h4>关键词</h4>
              <p>{{ foodDetail.keywords || '无' }}</p>
            </div>
          </div>
        </div>
      </el-card>
      
      <!-- 规格信息卡片 -->
      <el-card class="detail-card" v-if="foodSpecs.length > 0">
        <template #header>
          <div class="card-header">
            <h3>规格信息</h3>
          </div>
        </template>
        
        <div class="specs-info">
          <el-table :data="foodSpecs" border style="width: 100%">
            <el-table-column label="规格名称" prop="name" min-width="150"></el-table-column>
            <el-table-column label="价格" width="150">
              <template #default="scope">
                {{ formatPrice(scope.row.price) }}
              </template>
            </el-table-column>
            <el-table-column label="原价" width="150">
              <template #default="scope">
                {{ scope.row.original_price ? formatPrice(scope.row.original_price) : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="库存" width="100" prop="stock"></el-table-column>
            <el-table-column label="默认规格" width="100">
              <template #default="scope">
                <el-tag v-if="scope.row.is_default" type="success" size="small">默认</el-tag>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
      
      <!-- 审核记录卡片 -->
      <el-card class="detail-card" v-if="auditRecords.length > 0">
        <template #header>
          <div class="card-header">
            <h3>审核记录</h3>
          </div>
        </template>
        
        <div class="audit-records">
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in auditRecords"
              :key="index"
              :timestamp="formatTime(record.created_at)"
              :type="getAuditTypeIcon(record.action)"
            >
              <div class="audit-item">
                <div class="audit-action">{{ getAuditActionText(record.action) }}</div>
                <div v-if="record.reason" class="audit-reason">原因：{{ record.reason }}</div>
                <div class="audit-operator">操作人：{{ record.operator_name }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-card>
    </div>
    
    <div v-else-if="!loading" class="empty-data">
      <el-empty description="商品不存在或已被删除"></el-empty>
    </div>
    
    <!-- 拒绝原因对话框 -->
    <el-dialog v-model="rejectDialogVisible" title="审核拒绝" width="500px">
      <el-form :model="rejectForm" label-position="top">
        <el-form-item 
          label="拒绝原因" 
          prop="reason"
          :rules="[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]"
        >
          <el-input 
            v-model="rejectForm.reason" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入拒绝原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="rejectFood" :loading="rejecting">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { formatTime } from '@/utils/format';
import { getAdminFoodDetail, auditFood } from '@/modules/admin/api/takeout';

// 导入商家模块的类型
import { TakeoutFoodStatus, TakeoutFoodAuditStatus } from '@/modules/merchant/types';

// 定义商品规格接口
interface FoodVariant {
  id: number;
  food_id: number;
  name: string;
  price: number;
  original_price: number;
  stock: number;
  is_default: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

// 定义商品详情接口
interface FoodDetail {
  id: number;
  merchant_id: number;
  merchant_name: string;
  name: string;
  category_id: number;
  category_name: string;
  image: string;
  brief: string;
  description: string;
  price: number;
  original_price: number;
  packaging_fee: number;
  preparation_time: number;
  daily_limit: number;
  status: TakeoutFoodStatus;
  audit_status: TakeoutFoodAuditStatus;
  created_at: string;
  updated_at: string;
  is_spicy: boolean;
  tags: string[];
  keywords: string;
  sort_order: number;
  variants: FoodVariant[];
  total_sold: number;
  is_recommend: boolean;
  is_combination: boolean;
  combo_items: any[] | null;
  has_variants: boolean;
}

// 定义规格接口
interface FoodSpec {
  id: number;
  food_id: number;
  name: string;
  description: string;
  price: number;
  original_price: number;
  stock: number;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

// 定义审核记录接口
interface AuditRecord {
  id: number;
  food_id: number;
  action: string; // APPROVE, REJECT, OFFLINE
  reason: string;
  operator_id: number;
  operator_name: string;
  created_at: string;
}

// 使用路由和路由参数
const route = useRoute();
const router = useRouter();

/**
 * 获取商品ID
 * 处理路由参数中的ID，确保返回有效的数字ID
 */
const foodId = computed(() => {
  const id = route.params.id;
  if (!id) return 0;
  // 处理数组形式的参数
  const idStr = Array.isArray(id) ? id[0] : id;
  // 转换为数字
  const numId = Number(idStr);
  // 确保是有效的数字
  return isNaN(numId) ? 0 : numId;
});

// 页面状态
const loading = ref(false);
const approving = ref(false);
const rejecting = ref(false);
const offlining = ref(false);

// 商品信息
const foodDetail = ref<FoodDetail>({} as FoodDetail);
const foodSpecs = ref<FoodSpec[]>([]);
const auditRecords = ref<AuditRecord[]>([]);

// 拒绝对话框
const rejectDialogVisible = ref(false);
const rejectForm = reactive({
  reason: ''
});

// 使用枚举作为常量
const FoodStatus = TakeoutFoodStatus;
const FoodAuditStatus = TakeoutFoodAuditStatus;

/**
 * 加载商品详情
 * 获取并显示商品的详细信息、规格和审核记录
 */
const loadFoodDetail = async () => {
  console.log('开始加载商品详情，商品ID:', foodId.value);
  if (!foodId.value) {
    console.warn('商品ID无效，取消加载');
    return;
  }
  
  loading.value = true;
  try {
    const response = await getAdminFoodDetail(foodId.value);
    console.log('请求返回数据:', response);
    
      // 判断响应是否有效
    if (!response || !response.id) {
      console.error('无效的商品数据:', response);
      ElMessage.error('商品不存在或已被删除');
      return;
    }

    // 将商品对象直接赋值给foodDetail
    foodDetail.value = {
      ...response,
      // 确保tags和variants是数组
      tags: response.tags || [],
      variants: response.variants || []
    };
      
    console.log('更新后的foodDetail:', foodDetail.value);
      
    // 如果有变体，直接使用变体作为规格
    if (response.variants && response.variants.length > 0) {
      foodSpecs.value = response.variants.map((variant: any) => ({
        ...variant,
        // 确保必填字段有默认值
        stock: variant.stock ?? -1,
        price: variant.price ?? 0,
        original_price: variant.original_price ?? variant.price ?? 0,
        is_default: variant.is_default ?? false
      }));
    }
    
  } catch (error: any) {
    console.error('加载商品详情失败:', error);
    ElMessage.error(error?.message || '加载商品详情失败');
  } finally {
    loading.value = false;
  }
};



/**
 * 格式化价格
 * @param price 价格数值
 * @returns 格式化后的价格字符串
 */
const formatPrice = (price: number) => {
  return `¥${price.toFixed(2)}`;
};

/**
 * 格式化日期时间
 * @param timestamp 时间戳
 * @returns 格式化后的日期时间字符串
 */
// const formatDateTime = (timestamp: string | number) => {
//   if (!timestamp) return '-';
  
//   const date = new Date(timestamp);
//   return date.toLocaleString('zh-CN', {
//     year: 'numeric',
//     month: '2-digit',
//     day: '2-digit',
//     hour: '2-digit',
//     minute: '2-digit'
//   });
// };

/**
 * 获取商品状态文本
 * @param status 商品状态码
 * @returns 状态文本
 */
const getStatusText = (status: number) => {
  switch (status) {
    case FoodStatus.DRAFT: return '草稿';
    case FoodStatus.ON_SALE: return '上架销售中';
    case FoodStatus.OFF_SALE: return '已下架';
    case FoodStatus.SOLD_OUT: return '已售罄';
    default: return '未知';
  }
};

/**
 * 获取审核状态文本
 * @param status 审核状态码
 * @returns 审核状态文本
 */
const getAuditStatusText = (status: number) => {
  switch (status) {
    case FoodAuditStatus.PENDING: return '未审核';
    case FoodAuditStatus.APPROVED: return '审核通过';
    case FoodAuditStatus.REJECTED: return '审核拒绝';
    default: return '未知';
  }
};

/**
 * 获取商品状态标签类型
 * @param status 商品状态码
 * @returns 标签类型
 */
const getStatusTagType = (status: number) => {
  switch (status) {
    case FoodStatus.DRAFT: return 'info';
    case FoodStatus.ON_SALE: return 'success';
    case FoodStatus.OFF_SALE: return 'info';
    case FoodStatus.SOLD_OUT: return 'danger';
    default: return 'info';
  }
};

/**
 * 获取审核状态标签类型
 * @param status 审核状态码
 * @returns 标签类型
 */
const getAuditStatusTagType = (status: number) => {
  switch (status) {
    case FoodAuditStatus.PENDING: return 'warning';
    case FoodAuditStatus.APPROVED: return 'success';
    case FoodAuditStatus.REJECTED: return 'danger';
    default: return 'info';
  }
};

/**
 * 获取审核记录图标类型
 * @param action 审核动作
 * @returns 时间线图标类型
 */
const getAuditTypeIcon = (action: string) => {
  switch (action) {
    case 'APPROVE': return 'success';
    case 'REJECT': return 'danger';
    case 'OFFLINE': return 'warning';
    default: return 'info';
  }
};

/**
 * 获取审核动作文本
 * @param action 审核动作
 * @returns 审核动作文本
 */
const getAuditActionText = (action: string) => {
  switch (action) {
    case 'APPROVE': return '审核通过';
    case 'REJECT': return '审核拒绝';
    case 'OFFLINE': return '下架商品';
    default: return action;
  }
};

/**
 * 返回列表页面
 */
const goBack = () => {
  router.push('/admin/takeout/food/list');
};

/**
 * 跳转到商家详情页面
 * @param merchantId 商家ID
 */
const goToMerchant = (merchantId: number) => {
  console.log('跳转到商家详情页面，商家ID:', `/admin/merchants/${merchantId}`);
  router.push(`/admin/merchants/${merchantId}`);
};

/**
 * 审核通过
 * 将商品状态设置为上架
 */
const approveFood = async () => {
  if (!foodId.value) return;
  
  try {
    await ElMessageBox.confirm(
      '确定审核通过并上架该商品吗？',
      '审核确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      }
    );
    
    approving.value = true;
    
    await auditFood(foodId.value, { audit_status: FoodAuditStatus.APPROVED });
    ElMessage.success('商品审核通过');
    
    // 刷新商品详情
    await loadFoodDetail();
    
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('审核失败:', error);
      ElMessage.error(error?.message || '审核失败');
    }
  } finally {
    approving.value = false;
  }
};

/**
 * 显示拒绝对话框
 */
const showRejectDialog = () => {
  rejectForm.reason = '';
  rejectDialogVisible.value = true;
};

/**
 * 审核拒绝
 * 将商品状态设置为拒绝
 */
const rejectFood = async () => {
  if (!foodId.value) return;
  
  if (!rejectForm.reason.trim()) {
    ElMessage.warning('请输入拒绝原因');
    return;
  }
  
  rejecting.value = true;
  try {
    await auditFood(foodId.value, {
      audit_status: TakeoutFoodAuditStatus.REJECTED,
      reason: rejectForm.reason
    });
    
    rejectDialogVisible.value = false;
    ElMessage.success('商品已审核拒绝');
    
    // 刷新商品详情
    await loadFoodDetail();
    
  } catch (error: any) {
    console.error('审核拒绝失败:', error);
    ElMessage.error(error?.message || '审核拒绝失败');
  } finally {
    rejecting.value = false;
  }
};

/**
 * 下架商品
 * 将商品状态设置为下架
 */
const offlineFood = async () => {
  if (!foodId.value) return;
  
  try {
    await ElMessageBox.confirm(
      '确定要下架该商品吗？',
      '下架确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    offlining.value = true;
    
    await auditFood(foodId.value, { audit_status: FoodStatus.OFF_SALE });
    ElMessage.success('商品已下架');
    
    // 刷新商品详情
    await loadFoodDetail();
    
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('下架商品失败:', error);
      ElMessage.error(error?.message || '下架商品失败');
    }
  } finally {
    offlining.value = false;
  }
};

// 页面加载时初始化
onMounted(() => {
  console.log('页面加载，路由参数:', route.params);
  console.log('计算得到的foodId:', foodId.value);
  console.log('当前模板渲染条件:', {
    'loading': loading.value,
    'foodDetail.id': foodDetail.value?.id,
    'v-if': !!foodDetail.value?.id,
    'v-else-if': !loading.value,
  });
  
  // 检查foodDetail实例化是否正确
  if (!foodDetail.value || typeof foodDetail.value !== 'object') {
    console.error('foodDetail初始化错误:', foodDetail.value);
    // 强制初始化为空对象
    foodDetail.value = {} as FoodDetail;
  }
  
  loadFoodDetail();
});
</script>

<style scoped lang="scss">
.food-detail-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .header-card {
    margin-bottom: 20px;
  }
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .detail-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
      
      .el-tag {
        margin-left: 10px;
      }
    }
  }
  
  .basic-info {
    .food-main-info {
      display: flex;
      margin-bottom: 30px;
      
      .food-image {
        margin-right: 30px;
      }
      
      .food-info {
        flex: 1;
        
        h2 {
          margin-top: 0;
          margin-bottom: 20px;
          font-size: 22px;
          font-weight: 500;
        }
        
        .info-row {
          display: flex;
          margin-bottom: 15px;
          font-size: 14px;
          
          .info-label {
            width: 80px;
            color: #606266;
          }
          
          .info-value {
            flex: 1;
            
            &.price {
              color: #f56c6c;
              font-weight: bold;
              font-size: 16px;
            }
            
            &.original-price {
              color: #909399;
              text-decoration: line-through;
            }
            
            .tag-item {
              margin-right: 5px;
            }
          }
          
          .ml-20 {
            margin-left: 20px;
          }
        }
      }
    }
    
    .food-description {
      .desc-section {
        margin-bottom: 20px;
        
        h4 {
          margin-top: 0;
          margin-bottom: 10px;
          font-size: 15px;
          font-weight: 500;
          color: #303133;
        }
        
        p {
          margin: 0;
          color: #606266;
          line-height: 1.6;
          white-space: pre-wrap;
        }
      }
    }
  }
  
  .specs-info {
    margin-bottom: 20px;
  }
  
  .audit-records {
    padding: 10px;
    
    .audit-item {
      .audit-action {
        font-weight: bold;
        margin-bottom: 5px;
      }
      
      .audit-reason {
        color: #606266;
        margin-bottom: 5px;
      }
      
      .audit-operator {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .empty-data {
    padding: 40px 0;
  }
}
</style>
