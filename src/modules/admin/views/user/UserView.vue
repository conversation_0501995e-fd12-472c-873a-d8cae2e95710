<template>
  <div class="user-view">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <div class="action-box">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入用户名/手机号/邮箱"
              clearable
              @keyup.enter="handleSearch"
              class="search-input"
            >
              <template #append>
                <el-button @click="handleSearch">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
            <el-button type="primary" @click="handleAddUser">
              <el-icon><Plus /></el-icon> 新增用户
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="userList"
        style="width: 100%"
        border
        stripe
        :max-height="tableMaxHeight"
        highlight-current-row
        table-layout="auto"
        class="user-table"
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="username" label="用户名" min-width="120" show-overflow-tooltip />
        <el-table-column label="头像" width="100" align="center">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column prop="nickname" label="昵称" width="120" show-overflow-tooltip />
        <el-table-column prop="mobile" label="手机号" width="120" show-overflow-tooltip />
        <el-table-column prop="email" label="邮箱" width="180" show-overflow-tooltip />
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="注册时间" width="180" show-overflow-tooltip>
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="180">
          <template #default="{ row }">
            <!-- 状态操作 -->
            <el-button
              v-if="row.status === 1"
              type="warning"
              size="small"
              @click="handleStatusChange(row.id, 0)"
            >
              禁用
            </el-button>
            <el-button
              v-else
              type="success"
              size="small"
              @click="handleStatusChange(row.id, 1)"
            >
              启用
            </el-button>
            
            <!-- 查看详情 -->
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增用户对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="新增用户"
      width="500px"
      destroy-on-close
    >
      <el-form :model="addForm" :rules="addFormRules" ref="addFormRef" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="addForm.username" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="addForm.password" type="password" show-password />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="addForm.nickname" />
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="addForm.mobile" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="addForm.email" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAddUser">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 用户管理列表页面组件
 * 提供用户列表展示、查询、新增用户、修改用户状态等功能
 */
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import { useRouter, onBeforeRouteLeave } from 'vue-router';
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus';
import { Plus, Search, User } from '@element-plus/icons-vue';
import request from '@/modules/admin/utils/request';
import { getUserList } from '../../api/user';
import { formatTime } from '@/utils/format';

// 定义用户接口
interface User {
  id: number;
  username: string;
  nickname?: string;
  avatar?: string;
  mobile?: string;
  email?: string;
  status: number;
  created_at: string;
  updated_at: string;
}

// 表格高度
const tableMaxHeight = ref(window.innerHeight - 300);

// 用户列表数据
const userList = ref<User[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchKeyword = ref('');

// 新增用户对话框
const addDialogVisible = ref(false);
const addFormRef = ref<FormInstance>();
const addForm = reactive({
  username: '',
  password: '',
  nickname: '',
  mobile: '',
  email: ''
});

// 表单验证规则
const addFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 5, max: 20, message: '长度在 5 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 8, max: 20, message: '长度在 8 到 20 个字符', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  mobile: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
};

/**
 * 处理新增用户
 */
const handleAddUser = () => {
  addDialogVisible.value = true;
  // 重置表单
  Object.keys(addForm).forEach(key => {
    addForm[key as keyof typeof addForm] = '';
  });
};

/**
 * 提交新增用户
 */
const submitAddUser = async () => {
  if (!addFormRef.value) return;
  
  await addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true;
        await request.post('/v1/admin/secured/users', addForm);
        ElMessage.success('创建用户成功');
        addDialogVisible.value = false;
        fetchUserList();
      } catch (error) {
        console.error('创建用户失败:', error);
      } finally {
        loading.value = false;
      }
    }
  });
};

/**
 * 获取用户列表
 */
const fetchUserList = async () => {
  try {
    loading.value = true;
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value
    };
    
    const response: any = await getUserList(params);
    userList.value = response.list || [];
    total.value = response.total || 0;
  } catch (error) {
    console.error('获取用户列表失败:', error);
  } finally {
    loading.value = false;
  }
};

/**
 * 处理页码变化
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  fetchUserList();
};

/**
 * 处理每页条数变化
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchUserList();
};

/**
 * 处理搜索
 */
const handleSearch = () => {
  currentPage.value = 1;
  fetchUserList();
};

/**
 * 处理状态变更
 */
const handleStatusChange = async (id: number, status: number) => {
  try {
    const actionText = status === 1 ? '启用' : '禁用';
    await ElMessageBox.confirm(
      `确定要${actionText}该用户吗?`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    loading.value = true;
    await request.put(`/v1/admin/secured/users/${id}/status`, { status });
    ElMessage.success(`${actionText}用户成功`);
    fetchUserList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新用户状态失败:', error);
    }
  } finally {
    loading.value = false;
  }
};

/**
 * 查看用户详情
 */
const router = useRouter();
const handleViewDetail = (user: User) => {
  console.log(user);
  router.push(`/admin/users/${user.id}`);
};

/**
 * 重置组件状态
 */
const resetComponentState = () => {
  // 重置列表数据
  userList.value = []
  total.value = 0
  currentPage.value = 1
  pageSize.value = 10
  searchKeyword.value = ''
  loading.value = false
  
  // 重置新增表单
  addDialogVisible.value = false
  Object.assign(addForm, {
    username: '',
    password: '',
    nickname: '',
    mobile: '',
    email: ''
  })
  
  // 重置表单验证
  if (addFormRef.value) {
    addFormRef.value.resetFields()
  }
}

/**
 * 路由离开前的处理
 */
onBeforeRouteLeave((to, from, next) => {
  console.log('🔄 UserView 路由离开:', from.path, '->', to.path)
  
  // 如果有打开的对话框，先关闭
  if (addDialogVisible.value) {
    addDialogVisible.value = false
  }
  
  resetComponentState()
  next()
})

/**
 * 组件卸载前的清理
 */
onBeforeUnmount(() => {
  console.log('🧹 UserView 组件卸载清理')
  resetComponentState()
})

// 组件挂载后获取用户列表
onMounted(() => {
  fetchUserList();
});
</script>

<style scoped>
.user-view {
  padding: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-box {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-input {
  width: 300px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.user-table :deep(.el-table__body tr.current-row > td) {
  background-color: var(--el-table-row-hover-bg-color);
}
</style>
