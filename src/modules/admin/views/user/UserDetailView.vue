<template>
  <div class="user-detail-view">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="left">
            <el-button @click="goBack" type="primary" plain size="small">
              <el-icon><ArrowLeft /></el-icon> 返回
            </el-button>
            <span class="title">用户详情</span>
          </div>
          
          <div class="action-buttons">
            <!-- 状态操作 -->
            <el-button
              v-if="userInfo.status === 1"
              type="warning"
              @click="handleStatusChange(0)"
            >
              <el-icon><Lock /></el-icon> 禁用账户
            </el-button>
            <el-button
              v-else
              type="success"
              @click="handleStatusChange(1)"
            >
              <el-icon><Unlock /></el-icon> 启用账户
            </el-button>
            <el-button
              type="primary"
              @click="handleEditPointsAndBalance"
            >
              <el-icon><Coin /></el-icon> 修改积分/余额
            </el-button>
          </div>
        </div>
      </template>
      
      <div v-loading="loading" class="detail-content">
        <!-- 基本信息卡片 -->
        <el-card class="info-card">
          <template #header>
            <div class="info-header">
              <span>基本信息</span>
              <el-button type="primary" plain size="small" @click="handleEdit">
                <el-icon><Edit /></el-icon> 编辑
              </el-button>
            </div>
          </template>
          
          <div class="user-header">
            <div class="user-avatar">
              <el-avatar :size="100" :src="userInfo.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
            </div>
            <div class="user-main-info">
              <h2>{{ userInfo.username }}</h2>
              <div class="user-status">
                <el-tag 
                  :type="userInfo.status === 1 ? 'success' : 'danger'"
                  class="status-tag"
                >
                  {{ userInfo.status === 1 ? '正常' : '禁用' }}
                </el-tag>
              </div>
            </div>
          </div>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户ID">{{ userInfo.id }}</el-descriptions-item>
            <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
            <el-descriptions-item label="余额">{{ userInfo.balance }}</el-descriptions-item>
            <el-descriptions-item label="积分">{{ userInfo.points }}</el-descriptions-item>
            <el-descriptions-item label="昵称">{{ userInfo.nickname || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="手机号">{{ userInfo.mobile || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="邮箱">{{ userInfo.email || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="userInfo.status === 1 ? 'success' : 'danger'">
                {{ userInfo.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">
              {{ formatTime(userInfo.created_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="最后登录">
              {{ userInfo.last_login_at ? formatTime(userInfo.last_login_at) : '未登录' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <!-- 账户安全卡片 -->
        <el-card class="info-card">
          <template #header>
            <div class="info-header">
              <span>账户安全</span>
            </div>
          </template>
          
          <el-descriptions :column="1" border>
            <el-descriptions-item label="密码">
              ******** 
              <el-button type="primary" link @click="handleEdit">
                修改信息
              </el-button>
              <el-button type="warning" link @click="handlePasswordChange">
                修改密码
              </el-button>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <!-- 用户统计卡片 -->
        <el-card class="info-card">
          <template #header>
            <div class="info-header">
              <span>用户数据统计</span>
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="stat-card">
                <div class="stat-value">{{ userStats.orderCount || 0 }}</div>
                <div class="stat-label">订单总数</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-card">
                <div class="stat-value">{{ userStats.totalSpent ? `¥${userStats.totalSpent}` : '¥0' }}</div>
                <div class="stat-label">消费总额</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-card">
                <div class="stat-value">{{ userStats.lastOrderTime ? formatTime(userStats.lastOrderTime) : '无' }}</div>
                <div class="stat-label">最近下单</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </el-card>
    
    <!-- 编辑用户信息对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑用户信息"
      width="500px"
      destroy-on-close
    >
      <el-form :model="editForm" :rules="editFormRules" ref="editFormRef" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" disabled />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="editForm.nickname" />
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="editForm.mobile" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" />
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <el-input v-model="editForm.password" type="password" show-password placeholder="不修改请留空" />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="editForm.confirmPassword" type="password" show-password placeholder="不修改请留空" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEdit">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="passwordDialogVisible"
      title="修改密码"
      width="500px"
      destroy-on-close
    >
      <el-form :model="passwordForm" :rules="passwordFormRules" ref="passwordFormRef" label-width="100px">
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPasswordChange">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 修改积分和余额对话框 -->
    <el-dialog
      v-model="pointsBalanceDialogVisible"
      title="修改积分和余额"
      width="500px"
      destroy-on-close
      @closed="handlePointsBalanceDialogClosed"
    >
      <el-form 
        :model="pointsBalanceForm" 
        :rules="pointsBalanceRules" 
        ref="pointsBalanceFormRef" 
        label-width="120px"
      >
        <el-form-item label="当前积分" prop="points">
          <el-input-number 
            v-model="pointsBalanceForm.points" 
            :min="0" 
            :precision="0"
            :controls="false"
            placeholder="不修改请留空"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="当前余额(元)" prop="balance">
          <el-input-number 
            v-model="pointsBalanceForm.balance" 
            :min="0" 
            :precision="2"
            :controls="false"
            placeholder="不修改请留空"
            style="width: 100%"
          />
        </el-form-item>
        <el-alert
          title="提示"
          type="info"
          description="请谨慎修改用户积分和余额，修改后不可撤销"
          show-icon
          :closable="false"
        />
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="pointsBalanceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPointsBalanceChange" :loading="pointsBalanceLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ArrowLeft, Lock, Unlock, User, Edit, Coin } from '@element-plus/icons-vue';
import { useRouter, useRoute } from 'vue-router';
import { formatTime } from '@/utils/format';
import { getUserDetail, updateUser, updatePassword, updateUserStatus, updateUserPointsAndBalance } from '../../api/user';

const router = useRouter();
const route = useRoute();

const userId = parseInt(route.params.id as string, 10);

// 加载状态
const loading = ref(false);

// 用户信息
interface UserInfo {
  id: number;
  username: string;
  nickname: string;
  mobile: string;
  email: string;
  avatar: string;
  status: number;
  created_at: string;
  last_login_at: string;
  points?: number;
  balance?: number;
}

const userInfo = ref<UserInfo>({
  id: 0,
  username: '',
  nickname: '',
  mobile: '',
  email: '',
  avatar: '',
  status: 1,
  created_at: '',
  last_login_at: ''
});

// 用户统计数据
const userStats = ref({
  orderCount: 0,
  totalSpent: 0,
  lastOrderTime: ''
});

// 编辑用户信息对话框相关
const editDialogVisible = ref(false);
const editFormRef = ref();
const editForm = reactive({
  username: '',
  nickname: '',
  mobile: '',
  email: '',
  password: '',
  confirmPassword: ''
});

// 编辑用户信息表单验证规则
const editFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 5, max: 20, message: '长度在 5 到 20 个字符', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  mobile: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: (_rule: any, value: string, callback: (error?: Error) => void) => {
      if (value && value !== editForm.password) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    }, trigger: 'blur' }
  ]
};

// 修改密码对话框相关
const passwordDialogVisible = ref(false);
const passwordFormRef = ref();
const passwordForm = reactive({
  newPassword: '',
  confirmPassword: ''
});

// 修改密码表单验证规则
const passwordFormRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: (_rule: any, value: string, callback: (error?: Error) => void) => {
      if (value && value !== passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    }, trigger: 'blur' }
  ]
};

// 修改积分和余额对话框相关
const pointsBalanceDialogVisible = ref(false);
const pointsBalanceFormRef = ref();
const pointsBalanceForm = reactive({
  points: undefined as number | undefined,
  balance: undefined as number | undefined
});

// 积分和余额表单验证规则
const pointsBalanceRules = {
  points: [
    { type: 'number', message: '积分必须为数字', trigger: 'blur' },
    { validator: (_rule: any, value: any, callback: any) => {
      if (value === '' || value === null || value === undefined) {
        return callback();
      }
      if (isNaN(Number(value))) {
        return callback(new Error('积分必须为数字'));
      }
      if (value < 0) {
        return callback(new Error('积分不能为负数'));
      }
      callback();
    }, trigger: 'blur' }
  ],
  balance: [
    { type: 'number', message: '余额必须为数字', trigger: 'blur' },
    { validator: (_rule: any, value: any, callback: any) => {
      if (value === '' || value === null || value === undefined) {
        return callback();
      }
      if (isNaN(Number(value))) {
        return callback(new Error('余额必须为数字'));
      }
      if (value < 0) {
        return callback(new Error('余额不能为负数'));
      }
      callback();
    }, trigger: 'blur' }
  ]
};

// 加载状态
const pointsBalanceLoading = ref(false);

/**
 * 处理打开修改积分和余额对话框
 */
const handleEditPointsAndBalance = () => {
  // 设置当前用户的积分和余额
  pointsBalanceForm.points = userInfo.value.points;
  pointsBalanceForm.balance = userInfo.value.balance;
  pointsBalanceDialogVisible.value = true;
};

/**
 * 处理积分和余额对话框关闭
 */
const handlePointsBalanceDialogClosed = () => {
  // 重置表单
  pointsBalanceFormRef.value?.resetFields();
};

/**
 * 提交修改积分和余额
 */
const submitPointsBalanceChange = async () => {
  if (!pointsBalanceFormRef.value) return;
  
  try {
    pointsBalanceLoading.value = true;
    
    // 验证表单
    try {
      await pointsBalanceFormRef.value.validate();
    } catch (error) {
      return;
    }
    
    // 准备请求数据，只包含有值的字段
    const requestData: { points?: number; balance?: number } = {};
    if (pointsBalanceForm.points !== undefined) {
      requestData.points = pointsBalanceForm.points;
    }
    if (pointsBalanceForm.balance !== undefined) {
      requestData.balance = pointsBalanceForm.balance;
    }
    
    // 如果没有需要更新的字段，直接返回
    if (Object.keys(requestData).length === 0) {
      ElMessage.warning('请至少修改一个字段');
      return;
    }
    
    // 调用API更新积分和余额
    await updateUserPointsAndBalance(userInfo.value.id, requestData);
    
    ElMessage.success('更新成功');
    pointsBalanceDialogVisible.value = false;
    
    // 重新加载用户信息
    await fetchUserDetail();
  } catch (error) {
    console.error('更新积分和余额失败:', error);
    ElMessage.error('更新失败，请稍后重试');
  } finally {
    pointsBalanceLoading.value = false;
  }
};

// 返回列表页
const goBack = () => {
  router.push('/admin/users');
};

// 加载用户详情
const fetchUserDetail = async () => {
  try {
    loading.value = true;
    const response = await getUserDetail(userId);
    if (!response) {
      ElMessage.error('获取用户信息失败');
      return;
    }
    userInfo.value = response as any;
    
    // 加载用户统计数据(这里根据实际接口调整)
    // 这可能需要额外的API，或者在用户详情API中返回
    try {
      // 假设有一个获取用户统计数据的API
      // const statsResponse = await request.get(`/v1/admin/secured/users/${userId}/stats`);
      // userStats.value = statsResponse;
      
      // 如果没有相关API，可以设置一些模拟数据
      userStats.value = {
        orderCount: Math.floor(Math.random() * 50),
        totalSpent: Math.floor(Math.random() * 10000) / 100,
        lastOrderTime: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString()
      };
    } catch (error) {
      console.error('获取用户统计数据失败:', error);
    }
  } catch (error) {
    console.error('获取用户详情失败:', error);
    ElMessage.error('获取用户详情失败');
  } finally {
    loading.value = false;
  }
};

// 处理编辑用户信息
const handleEdit = () => {
  editDialogVisible.value = true;
  // 填充表单数据
  editForm.username = userInfo.value.username;
  editForm.nickname = userInfo.value.nickname || '';
  editForm.mobile = userInfo.value.mobile || '';
  editForm.email = userInfo.value.email || '';
  editForm.password = '';
  editForm.confirmPassword = '';
};

// 提交编辑用户信息
const submitEdit = async () => {
  if (!editFormRef.value) return;
  
  try {
    await editFormRef.value.validate();
    
    loading.value = true;
    
    try {
      // 准备更新数据
      const updateData: any = {
        username: editForm.username,
        nickname: editForm.nickname,
        mobile: editForm.mobile,
        email: editForm.email
      };
      
      // 如果密码不为空，则更新密码
      if (editForm.password) {
        updateData.password = editForm.password;
      }
      
      // 调用更新接口
      await updateUser(userId, updateData);
      
      ElMessage.success('更新成功');
      editDialogVisible.value = false;
      
      // 重新加载用户信息
      await fetchUserDetail();
    } catch (error) {
      console.error('更新用户信息失败:', error);
      ElMessage.error('更新失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

/**
 * 处理修改密码
 */
const handlePasswordChange = () => {
  passwordDialogVisible.value = true;
  passwordForm.newPassword = '';
  passwordForm.confirmPassword = '';
};

/**
 * 提交修改密码
 */
const submitPasswordChange = async () => {
  if (!passwordFormRef.value) return;
  
  try {
    await passwordFormRef.value.validate();
    
    loading.value = true;
    
    try {
      // 调用更新密码接口
      await updatePassword(userId, {
        new_password: passwordForm.newPassword
      });
      
      ElMessage.success('密码修改成功');
      passwordDialogVisible.value = false;
      
      // 重置表单
      passwordForm.newPassword = '';
      passwordForm.confirmPassword = '';
    } catch (error) {
      console.error('修改密码失败:', error);
      ElMessage.error('修改失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

/**
 * 处理状态变更
 */
const handleStatusChange = async (status: number) => {
  try {
    const actionText = status === 1 ? '启用' : '禁用';
    await ElMessageBox.confirm(
      `确定要${actionText}该用户账户吗?`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    loading.value = true;
    await updateUserStatus(userId, { status });
    ElMessage.success(`${actionText}用户成功`);
    // 更新本地状态
    userInfo.value.status = status;
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新用户状态失败:', error);
    }
  } finally {
    loading.value = false;
  }
};

// 组件挂载后获取用户详情
onMounted(() => {
  if (!userId) {
    ElMessage.error('用户ID不能为空');
    router.push('/admin/user');
    return;
  }
  
  fetchUserDetail();
});
</script>

<style scoped>
.user-detail-view {
  padding: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title {
  font-size: 16px;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card {
  margin-bottom: 20px;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-header {
  display: flex;
  margin-bottom: 24px;
  align-items: center;
}

.user-avatar {
  margin-right: 24px;
}

.user-main-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-main-info h2 {
  margin: 0;
}

.user-status {
  display: flex;
  gap: 8px;
}

.status-tag {
  font-size: 12px;
}

.stat-card {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 16px;
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-color-primary);
  margin-bottom: 8px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}
</style>
