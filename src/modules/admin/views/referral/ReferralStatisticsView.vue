<!--
  分销统计页面
  作者：张二浩
  日期：2025-01-15
  版本：1.0.0
  描述：展示分销相关的统计数据和图表分析
-->

<template>
  <div class="referral-statistics-view">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total-users">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.total_referrers || 0 }}</div>
              <div class="stat-label">总推荐人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total-relationships">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.total_relationships || 0 }}</div>
              <div class="stat-label">总分销关系</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total-commission">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">¥{{ statistics.total_commission || 0 }}</div>
              <div class="stat-label">总佣金金额</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active-rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.active_rate || 0 }}%</div>
              <div class="stat-label">活跃率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>分销趋势图</span>
              <el-select v-model="trendPeriod" @change="loadTrendData" style="width: 120px">
                <el-option label="最近7天" value="7d" />
                <el-option label="最近30天" value="30d" />
                <el-option label="最近90天" value="90d" />
              </el-select>
            </div>
          </template>
          <div ref="trendChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>层级分布图</span>
          </template>
          <div ref="levelChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>佣金统计图</span>
          </template>
          <div ref="commissionChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>TOP推荐人排行</span>
              <el-button type="primary" size="small" @click="handleExport">
                <el-icon><Download /></el-icon> 导出
              </el-button>
            </div>
          </template>
          <div class="top-referrers">
            <div v-for="(item, index) in topReferrers" :key="item.user_id" class="referrer-item">
              <div class="rank">{{ index + 1 }}</div>
              <div class="user-info">
                <el-avatar :size="40" :src="item.avatar" />
                <div class="user-details">
                  <div class="user-name">{{ item.name }}</div>
                  <div class="user-stats">
                    <span>推荐: {{ item.referral_count }}人</span>
                    <span>佣金: ¥{{ item.commission_earned }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>分销数据详情</span>
          <div class="filter-box">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleDateChange"
            />
            <el-button type="primary" @click="handleRefresh">
              <el-icon><Refresh /></el-icon> 刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="detailData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="new_referrers" label="新增推荐人" width="120" />
        <el-table-column prop="new_relationships" label="新增关系" width="120" />
        <el-table-column prop="commission_amount" label="佣金金额" width="120">
          <template #default="{ row }">
            <span class="amount">¥{{ row.commission_amount || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="active_referrers" label="活跃推荐人" width="120" />
        <el-table-column prop="conversion_rate" label="转化率" width="100">
          <template #default="{ row }">
            {{ row.conversion_rate || 0 }}%
          </template>
        </el-table-column>
        <el-table-column prop="avg_commission" label="平均佣金" width="120">
          <template #default="{ row }">
            <span class="amount">¥{{ row.avg_commission || 0 }}</span>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-box">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue';
import { ElMessage } from 'element-plus';
import {
  User,
  Connection,
  Money,
  TrendCharts,
  Download,
  Refresh
} from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import {
  getReferralStatistics,
  getReferralTrendData,
  getReferralLevelDistribution,
  getCommissionStatistics,
  getTopReferrers,
  exportReferralData
} from '@/modules/admin/api/referral';

// ===================== 响应式数据 =====================

const loading = ref(false);
const trendPeriod = ref('30d');
const dateRange = ref<[Date, Date] | null>(null);

// 图表引用
const trendChartRef = ref<HTMLElement>();
const levelChartRef = ref<HTMLElement>();
const commissionChartRef = ref<HTMLElement>();

// 图表实例
let trendChart: echarts.ECharts | null = null;
let levelChart: echarts.ECharts | null = null;
let commissionChart: echarts.ECharts | null = null;

// 统计数据
const statistics = reactive({
  total_referrers: 0,
  total_relationships: 0,
  total_commission: 0,
  active_rate: 0
});

// TOP推荐人数据
const topReferrers = ref<any[]>([]);

// 详细数据
const detailData = ref<any[]>([]);

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
});

// 分页计算属性
const paginationValue = computed(() => ({
  currentPage: pagination.page,
  pageSize: pagination.size
}));

// ===================== 生命周期 =====================

onMounted(async () => {
  await loadStatistics();
  await loadTopReferrers();
  await loadDetailData();
  
  nextTick(() => {
    initCharts();
    loadTrendData();
    loadLevelData();
    loadCommissionData();
  });
});

// ===================== 方法定义 =====================

/**
 * 加载统计数据
 */
const loadStatistics = async () => {
  try {
    const response = await getReferralStatistics();
    if (response) {
      Object.assign(statistics, response);
    }
  } catch (error: any) {
    console.error('加载统计数据失败:', error);
    ElMessage.error(`加载统计数据失败: ${error.message || '未知错误'}`);
  }
};

/**
 * 加载TOP推荐人数据
 */
const loadTopReferrers = async () => {
  try {
    const response: any = await getTopReferrers({ limit: 10 });
    if (response) {
      topReferrers.value = response.items || [];
    }
  } catch (error: any) {
    console.error('加载TOP推荐人失败:', error);
  }
};

/**
 * 加载详细数据
 */
const loadDetailData = async () => {
  try {
    loading.value = true;
    const params: any = {
       page: paginationValue.value.currentPage,
       size: paginationValue.value.pageSize
     };
    
    if (dateRange.value) {
      params.start_date = dateRange.value[0].toISOString().split('T')[0];
      params.end_date = dateRange.value[1].toISOString().split('T')[0];
    }
    
    // 模拟数据，实际应该调用API
    const mockData = {
      items: Array.from({ length: 20 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        new_referrers: Math.floor(Math.random() * 50) + 10,
        new_relationships: Math.floor(Math.random() * 100) + 20,
        commission_amount: (Math.random() * 10000 + 1000).toFixed(2),
        active_referrers: Math.floor(Math.random() * 30) + 5,
        conversion_rate: (Math.random() * 20 + 5).toFixed(1),
        avg_commission: (Math.random() * 500 + 100).toFixed(2)
      })),
      total: 100
    };
    
    detailData.value = mockData.items;
    pagination.total = mockData.total;
  } catch (error: any) {
    console.error('加载详细数据失败:', error);
    ElMessage.error(`加载详细数据失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

/**
 * 初始化图表
 */
const initCharts = () => {
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value);
  }
  if (levelChartRef.value) {
    levelChart = echarts.init(levelChartRef.value);
  }
  if (commissionChartRef.value) {
    commissionChart = echarts.init(commissionChartRef.value);
  }
};

/**
 * 加载趋势数据
 */
const loadTrendData = async () => {
  try {
    await getReferralTrendData();
    console.log('趋势数据加载完成');
    
    // 模拟数据
    const mockData = {
      dates: Array.from({ length: 30 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - 29 + i);
        return date.toISOString().split('T')[0];
      }),
      referrers: Array.from({ length: 30 }, () => Math.floor(Math.random() * 50) + 10),
      relationships: Array.from({ length: 30 }, () => Math.floor(Math.random() * 100) + 20)
    };
    
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['新增推荐人', '新增关系']
      },
      xAxis: {
        type: 'category',
        data: mockData.dates
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '新增推荐人',
          type: 'line',
          data: mockData.referrers,
          smooth: true
        },
        {
          name: '新增关系',
          type: 'line',
          data: mockData.relationships,
          smooth: true
        }
      ]
    };
    
    trendChart?.setOption(option);
  } catch (error: any) {
    console.error('加载趋势数据失败:', error);
  }
};

/**
 * 加载层级分布数据
 */
const loadLevelData = async () => {
  try {
    await getReferralLevelDistribution();
    console.log('层级分布数据加载完成');
    
    // 模拟数据
    const mockData = [
      { name: '一级分销', value: 234 },
      { name: '二级分销', value: 135 },
      { name: '三级分销', value: 89 },
      { name: '其他层级', value: 42 }
    ];
    
    const option = {
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          type: 'pie',
          radius: '50%',
          data: mockData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    
    levelChart?.setOption(option);
  } catch (error: any) {
    console.error('加载层级分布数据失败:', error);
  }
};

/**
 * 加载佣金统计数据
 */
const loadCommissionData = async () => {
  try {
    await getCommissionStatistics();
    console.log('佣金统计数据加载完成');
    
    // 模拟数据
    const mockData = {
      categories: ['一级佣金', '二级佣金', '三级佣金'],
      values: [12500, 8900, 5600]
    };
    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: mockData.categories
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          type: 'bar',
          data: mockData.values,
          itemStyle: {
            color: '#409EFF'
          }
        }
      ]
    };
    
    commissionChart?.setOption(option);
  } catch (error: any) {
    console.error('加载佣金统计数据失败:', error);
  }
};

/**
 * 导出数据
 */
const handleExport = async () => {
  try {
    await exportReferralData({
      format: 'excel',
      date_range: dateRange.value ? `${dateRange.value[0].toISOString().split('T')[0]},${dateRange.value[1].toISOString().split('T')[0]}` : undefined
    });
    ElMessage.success('导出成功');
  } catch (error: any) {
    console.error('导出数据失败:', error);
    ElMessage.error(`导出失败: ${error.message || '未知错误'}`);
  }
};

/**
 * 日期范围改变
 */
const handleDateChange = () => {
  pagination.page = 1;
  loadDetailData();
};

/**
 * 刷新数据
 */
const handleRefresh = () => {
  loadStatistics();
  loadTopReferrers();
  loadDetailData();
  loadTrendData();
  loadLevelData();
  loadCommissionData();
};

/**
 * 分页大小改变
 */
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.page = 1;
  loadDetailData();
};

/**
 * 当前页改变
 */
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  loadDetailData();
};
</script>

<style scoped>
.referral-statistics-view {
  padding: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.total-users {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.total-relationships {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.total-commission {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.active-rate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 320px;
}

.top-referrers {
  height: 320px;
  overflow-y: auto;
}

.referrer-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.rank {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-details {
  margin-left: 10px;
}

.user-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.user-stats {
  font-size: 12px;
  color: #909399;
}

.user-stats span {
  margin-right: 15px;
}

.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-box {
  display: flex;
  gap: 10px;
  align-items: center;
}

.amount {
  color: #f56c6c;
  font-weight: bold;
}

.pagination-box {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>