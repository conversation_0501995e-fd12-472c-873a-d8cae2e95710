<!--
  分销等级配置页面
  作者：张二浩
  日期：2025-01-15
  版本：1.0.0
  描述：管理分销等级配置，包括等级名称、所需推荐人数、佣金比例等
-->

<template>
  <div class="referral-levels-view">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>分销等级配置</span>
          <div class="action-box">
            <el-button type="primary" @click="handleAddLevel">
              <el-icon><Plus /></el-icon> 新增等级
            </el-button>
            <el-button type="success" @click="handleSave" :loading="saving">
              <el-icon><Check /></el-icon> 保存配置
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon> 重置
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="levels"
        style="width: 100%"
        border
        stripe
        class="levels-table"
      >
        <el-table-column prop="level" label="等级" width="80" align="center" />
        <el-table-column label="等级名称" min-width="150">
          <template #default="{ row }">
            <el-input
              v-model="row.name"
              placeholder="请输入等级名称"
              :disabled="!row.editable"
            />
          </template>
        </el-table-column>
        <el-table-column label="所需推荐人数" width="150">
          <template #default="{ row }">
            <el-input-number
              v-model="row.required_referrals"
              :min="0"
              :max="9999"
              :disabled="!row.editable"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="佣金比例(%)" width="150">
          <template #default="{ row }">
            <el-input-number
              v-model="row.commission_rate"
              :min="0"
              :max="100"
              :precision="2"
              :step="0.01"
              :disabled="!row.editable"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="描述" min-width="200">
          <template #default="{ row }">
            <el-input
              v-model="row.description"
              placeholder="请输入等级描述"
              :disabled="!row.editable"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template #default="{ row, $index }">
            <el-button
              v-if="!row.editable"
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <template v-else>
              <el-button
                type="success"
                size="small"
                @click="handleConfirm(row)"
              >
                确认
              </el-button>
              <el-button
                size="small"
                @click="handleCancel(row, $index)"
              >
                取消
              </el-button>
            </template>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete($index)"
              :disabled="levels.length <= 1"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="tips-box">
        <el-alert
          title="配置说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <ul>
              <li>等级数字越大，等级越高</li>
              <li>所需推荐人数应该递增设置</li>
              <li>佣金比例建议随等级递增</li>
              <li>至少需要保留一个等级配置</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Check, Refresh } from '@element-plus/icons-vue';
import {
  getReferralLevels,
  updateReferralLevels,
  type ReferralLevel
} from '@/modules/admin/api/referral';

// ===================== 响应式数据 =====================

const loading = ref(false);
const saving = ref(false);
const levels = ref<(ReferralLevel & { editable?: boolean; originalData?: ReferralLevel })[]>([]);
const originalLevels = ref<ReferralLevel[]>([]);

// ===================== 生命周期 =====================

onMounted(() => {
  loadLevels();
});

// ===================== 方法定义 =====================

/**
 * 加载分销等级配置
 */
const loadLevels = async () => {
  try {
    loading.value = true;
    const response: any = await getReferralLevels();
    
    if (response && response.levels) {
      levels.value = response.levels.map((level: ReferralLevel) => ({
        ...level,
        editable: false,
        commission_rate: level.commission_rate * 100 // 转换为百分比显示
      }));
      originalLevels.value = JSON.parse(JSON.stringify(response.levels));
    } else {
      // 如果没有配置，创建默认配置
      levels.value = [
        {
          level: 1,
          name: '初级推广员',
          required_referrals: 5,
          commission_rate: 5.0,
          description: '推荐5人即可成为初级推广员',
          editable: false
        }
      ];
    }
  } catch (error: any) {
    console.error('加载分销等级配置失败:', error);
    ElMessage.error(`加载分销等级配置失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

/**
 * 新增等级
 */
const handleAddLevel = () => {
  const maxLevel = Math.max(...levels.value.map(l => l.level), 0);
  const newLevel = {
    level: maxLevel + 1,
    name: `等级${maxLevel + 1}`,
    required_referrals: 0,
    commission_rate: 0,
    description: '',
    editable: true
  };
  levels.value.push(newLevel);
};

/**
 * 编辑等级
 */
const handleEdit = (row: any) => {
  // 保存原始数据
  row.originalData = JSON.parse(JSON.stringify(row));
  row.editable = true;
};

/**
 * 确认编辑
 */
const handleConfirm = (row: any) => {
  // 验证数据
  if (!row.name.trim()) {
    ElMessage.error('等级名称不能为空');
    return;
  }
  if (row.required_referrals < 0) {
    ElMessage.error('所需推荐人数不能小于0');
    return;
  }
  if (row.commission_rate < 0 || row.commission_rate > 100) {
    ElMessage.error('佣金比例必须在0-100之间');
    return;
  }
  
  row.editable = false;
  delete row.originalData;
};

/**
 * 取消编辑
 */
const handleCancel = (row: any, index: number) => {
  if (row.originalData) {
    // 恢复原始数据
    Object.assign(row, row.originalData);
    delete row.originalData;
  } else {
    // 新增的项目，直接删除
    levels.value.splice(index, 1);
    return;
  }
  row.editable = false;
};

/**
 * 删除等级
 */
const handleDelete = async (index: number) => {
  if (levels.value.length <= 1) {
    ElMessage.warning('至少需要保留一个等级配置');
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      '确定要删除该等级配置吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    levels.value.splice(index, 1);
    ElMessage.success('删除成功');
  } catch {
    // 用户取消删除
  }
};

/**
 * 保存配置
 */
const handleSave = async () => {
  // 检查是否有正在编辑的项目
  const editingItems = levels.value.filter(item => item.editable);
  if (editingItems.length > 0) {
    ElMessage.warning('请先完成正在编辑的项目');
    return;
  }
  
  // 验证数据
  for (const level of levels.value) {
    if (!level.name.trim()) {
      ElMessage.error('等级名称不能为空');
      return;
    }
    if (level.required_referrals < 0) {
      ElMessage.error('所需推荐人数不能小于0');
      return;
    }
    if (level.commission_rate < 0 || level.commission_rate > 100) {
      ElMessage.error('佣金比例必须在0-100之间');
      return;
    }
  }
  
  try {
    saving.value = true;
    
    // 转换数据格式
    const levelsData = levels.value.map(level => ({
      level: level.level,
      name: level.name,
      required_referrals: level.required_referrals,
      commission_rate: level.commission_rate / 100, // 转换为小数
      description: level.description
    }));
    
    await updateReferralLevels({ levels: levelsData });
    ElMessage.success('分销等级配置已保存');
    
    // 重新加载数据
    await loadLevels();
  } catch (error: any) {
    console.error('保存分销等级配置失败:', error);
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`);
  } finally {
    saving.value = false;
  }
};

/**
 * 重置配置
 */
const handleReset = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有配置吗？这将丢失所有未保存的修改。',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await loadLevels();
    ElMessage.success('配置已重置');
  } catch {
    // 用户取消重置
  }
};
</script>

<style scoped>
.referral-levels-view {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-box {
  display: flex;
  gap: 10px;
}

.levels-table {
  margin-bottom: 20px;
}

.tips-box {
  margin-top: 20px;
}

.tips-box ul {
  margin: 0;
  padding-left: 20px;
}

.tips-box li {
  margin-bottom: 5px;
}
</style>