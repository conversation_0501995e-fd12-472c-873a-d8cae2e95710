<!--
  分销管理主页面
  作者：张二浩
  日期：2025-01-15
  版本：1.0.0
  描述：分销管理模块的主页面，包含分销等级配置、佣金配置、分销关系管理、分销统计和佣金管理等功能
-->

<template>
  <div class="referral-view">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>分销管理</span>
          <div class="action-box">
            <el-button type="primary" @click="handleInitialize" :loading="initializing">
              <el-icon><Setting /></el-icon> 初始化配置
            </el-button>
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon> 刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 功能导航 -->
      <div class="nav-tabs">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="分销等级配置" name="levels">
            <ReferralLevelsView />
          </el-tab-pane>
          <el-tab-pane label="佣金配置" name="commission">
            <CommissionConfigView />
          </el-tab-pane>
          <el-tab-pane label="分销关系管理" name="relationships">
            <ReferralRelationshipsView />
          </el-tab-pane>
          <el-tab-pane label="分销统计" name="statistics">
            <ReferralStatisticsView />
          </el-tab-pane>
          <el-tab-pane label="佣金管理" name="commission-management">
            <CommissionManagementView />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>

    <!-- 快捷操作面板 -->
    <el-card class="quick-actions" v-if="activeTab === 'levels'">
      <template #header>
        <span>快捷操作</span>
      </template>
      <div class="actions-grid">
        <div class="action-item" @click="handleQuickAction('add-level')">
          <el-icon class="action-icon"><Plus /></el-icon>
          <div class="action-text">
            <div class="action-title">新增等级</div>
            <div class="action-desc">快速添加新的分销等级</div>
          </div>
        </div>
        <div class="action-item" @click="handleQuickAction('batch-update')">
          <el-icon class="action-icon"><Edit /></el-icon>
          <div class="action-text">
            <div class="action-title">批量更新</div>
            <div class="action-desc">批量更新等级配置</div>
          </div>
        </div>
        <div class="action-item" @click="handleQuickAction('export-config')">
          <el-icon class="action-icon"><Download /></el-icon>
          <div class="action-text">
            <div class="action-title">导出配置</div>
            <div class="action-desc">导出当前分销配置</div>
          </div>
        </div>
        <div class="action-item" @click="handleQuickAction('import-config')">
          <el-icon class="action-icon"><Upload /></el-icon>
          <div class="action-text">
            <div class="action-title">导入配置</div>
            <div class="action-desc">从文件导入配置</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 系统状态面板 -->
    <el-card class="system-status" v-if="activeTab === 'statistics'">
      <template #header>
        <span>系统状态</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="status-item">
            <div class="status-icon online">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">分销系统</div>
              <div class="status-desc">运行正常</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-item">
            <div class="status-icon online">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">佣金计算</div>
              <div class="status-desc">运行正常</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-item">
            <div class="status-icon warning">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">数据同步</div>
              <div class="status-desc">延迟5分钟</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-item">
            <div class="status-icon online">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">支付系统</div>
              <div class="status-desc">运行正常</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Setting,
  Refresh,
  Plus,
  Edit,
  Download,
  Upload,
  CircleCheck,
  Warning
} from '@element-plus/icons-vue';
import { initializeReferralConfigs } from '@/modules/admin/api/referral';
import ReferralLevelsView from './ReferralLevelsView.vue';
import CommissionConfigView from './CommissionConfigView.vue';
import ReferralRelationshipsView from './ReferralRelationshipsView.vue';
import ReferralStatisticsView from './ReferralStatisticsView.vue';
import CommissionManagementView from './CommissionManagementView.vue';

// ===================== 响应式数据 =====================

const activeTab = ref('levels');
const initializing = ref(false);

// ===================== 生命周期 =====================

onMounted(() => {
  // 页面加载时的初始化逻辑
  console.log('分销管理页面已加载');
});

// ===================== 方法定义 =====================

/**
 * 初始化分销配置
 */
const handleInitialize = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要初始化分销配置吗？这将重置所有配置为系统默认值。',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    initializing.value = true;
    await initializeReferralConfigs();
    ElMessage.success('分销配置初始化成功');
    
    // 刷新当前页面数据
    handleRefresh();
  } catch (error: any) {
    if (error.message) {
      console.error('初始化分销配置失败:', error);
      ElMessage.error(`初始化失败: ${error.message}`);
    }
  } finally {
    initializing.value = false;
  }
};

/**
 * 刷新数据
 */
const handleRefresh = () => {
  // 触发当前活动标签页的数据刷新
  ElMessage.info('数据已刷新');
};

/**
 * 标签页切换
 */
const handleTabChange = (tabName: string) => {
  console.log('切换到标签页:', tabName);
  // 可以在这里添加标签页切换时的逻辑
};

/**
 * 快捷操作处理
 */
const handleQuickAction = (action: string) => {
  switch (action) {
    case 'add-level':
      ElMessage.info('新增等级功能');
      break;
    case 'batch-update':
      ElMessage.info('批量更新功能');
      break;
    case 'export-config':
      handleExportConfig();
      break;
    case 'import-config':
      handleImportConfig();
      break;
    default:
      ElMessage.warning('功能开发中...');
  }
};

/**
 * 导出配置
 */
const handleExportConfig = () => {
  try {
    // 模拟导出配置
    const config = {
      levels: [],
      commission: {},
      export_time: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(config, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `referral_config_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
    ElMessage.success('配置导出成功');
  } catch (error) {
    console.error('导出配置失败:', error);
    ElMessage.error('导出配置失败');
  }
};

/**
 * 导入配置
 */
const handleImportConfig = () => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  
  input.onchange = (event: any) => {
    const file = event.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (e: any) => {
      try {
        const config = JSON.parse(e.target.result);
        console.log('导入的配置:', config);
        ElMessage.success('配置导入成功');
        // 这里可以调用API更新配置
      } catch (error) {
        console.error('解析配置文件失败:', error);
        ElMessage.error('配置文件格式错误');
      }
    };
    reader.readAsText(file);
  };
  
  input.click();
};
</script>

<style scoped>
.referral-view {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-box {
  display: flex;
  gap: 10px;
}

.nav-tabs {
  margin-top: 20px;
}

.quick-actions {
  margin-top: 20px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.action-icon {
  font-size: 24px;
  color: #409eff;
  margin-right: 12px;
}

.action-text {
  flex: 1;
}

.action-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.action-desc {
  font-size: 12px;
  color: #909399;
}

.system-status {
  margin-top: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
}

.status-icon.online {
  background-color: #f0f9ff;
  color: #67c23a;
}

.status-icon.warning {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.status-desc {
  font-size: 12px;
  color: #909399;
}

/* 标签页样式优化 */
:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  padding: 0 20px;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: bold;
}

:deep(.el-tabs__active-bar) {
  background-color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .referral-view {
    padding: 10px;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .action-item {
    padding: 12px;
  }
  
  .action-icon {
    font-size: 20px;
  }
  
  .action-title {
    font-size: 14px;
  }
  
  :deep(.el-tabs__item) {
    font-size: 14px;
    padding: 0 15px;
  }
}
</style>