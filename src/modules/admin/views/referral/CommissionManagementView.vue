<!--
  佣金管理页面
  作者：张二浩
  日期：2025-01-15
  版本：1.0.0
  描述：管理佣金记录，包括查看、审核、发放佣金等功能
-->

<template>
  <div class="commission-management-view">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>佣金管理</span>
          <div class="action-box">
            <el-button type="success" @click="handleBatchApprove" :disabled="!selectedCommissions.length">
              <el-icon><Check /></el-icon> 批量审核
            </el-button>
            <el-button type="warning" @click="handleBatchReject" :disabled="!selectedCommissions.length">
              <el-icon><Close /></el-icon> 批量拒绝
            </el-button>
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon> 刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-box">
        <el-form :model="searchForm" inline>
          <el-form-item label="用户ID">
            <el-input
              v-model="searchForm.user_id"
              placeholder="请输入用户ID"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="订单号">
            <el-input
              v-model="searchForm.order_id"
              placeholder="请输入订单号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="佣金状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <el-option label="待审核" value="pending" />
              <el-option label="已审核" value="approved" />
              <el-option label="已拒绝" value="rejected" />
              <el-option label="已发放" value="paid" />
              <el-option label="已冻结" value="frozen" />
            </el-select>
          </el-form-item>
          <el-form-item label="佣金类型">
            <el-select
              v-model="searchForm.type"
              placeholder="请选择类型"
              clearable
              style="width: 150px"
            >
              <el-option label="推荐佣金" value="referral" />
              <el-option label="销售佣金" value="sales" />
              <el-option label="团队佣金" value="team" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="searchForm.date_range"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon> 搜索
            </el-button>
            <el-button @click="handleResetSearch">
              <el-icon><Refresh /></el-icon> 重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 统计信息 -->
      <div class="summary-box">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="summary-item">
              <span class="label">总佣金金额：</span>
              <span class="value total">¥{{ summary.total_amount || 0 }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <span class="label">待审核金额：</span>
              <span class="value pending">¥{{ summary.pending_amount || 0 }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <span class="label">已发放金额：</span>
              <span class="value paid">¥{{ summary.paid_amount || 0 }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <span class="label">已冻结金额：</span>
              <span class="value frozen">¥{{ summary.frozen_amount || 0 }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="佣金ID" width="100" />
        <el-table-column prop="user_id" label="用户ID" width="100" />
        <el-table-column prop="user_name" label="用户姓名" width="120">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="30" :src="row.user_avatar" />
              <span class="user-name">{{ row.user_name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="order_id" label="订单号" width="150" />
        <el-table-column prop="type" label="佣金类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">{{ getTypeText(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="分销层级" width="100">
          <template #default="{ row }">
            <el-tag :type="getLevelTagType(row.level)">{{ row.level }}级</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="佣金金额" width="120">
          <template #default="{ row }">
            <span class="amount">¥{{ row.amount || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="rate" label="佣金比例" width="100">
          <template #default="{ row }">
            {{ row.rate || 0 }}%
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">{{ getStatusText(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              type="success"
              size="small"
              @click="handleApprove(row)"
            >
              审核
            </el-button>
            <el-button
              v-if="row.status === 'approved'"
              type="warning"
              size="small"
              @click="handlePay(row)"
            >
              发放
            </el-button>
            <el-button
              v-if="['pending', 'approved'].includes(row.status)"
              type="danger"
              size="small"
              @click="handleFreeze(row)"
            >
              冻结
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-box">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="佣金详情"
      width="800px"
    >
      <div v-if="currentDetail" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="佣金ID">{{ currentDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ currentDetail.user_id }}</el-descriptions-item>
          <el-descriptions-item label="用户姓名">{{ currentDetail.user_name }}</el-descriptions-item>
          <el-descriptions-item label="订单号">{{ currentDetail.order_id }}</el-descriptions-item>
          <el-descriptions-item label="佣金类型">
            <el-tag :type="getTypeTagType(currentDetail.type)">{{ getTypeText(currentDetail.type) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="分销层级">
            <el-tag :type="getLevelTagType(currentDetail.level)">{{ currentDetail.level }}级</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="佣金金额">¥{{ currentDetail.amount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="佣金比例">{{ currentDetail.rate || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="订单金额">¥{{ currentDetail.order_amount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(currentDetail.status)">{{ getStatusText(currentDetail.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(currentDetail.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(currentDetail.updated_at) }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="currentDetail.remark" class="remark-section">
          <h4>备注信息</h4>
          <p>{{ currentDetail.remark }}</p>
        </div>
        
        <div v-if="currentDetail.audit_logs && currentDetail.audit_logs.length" class="audit-logs">
          <h4>审核记录</h4>
          <el-timeline>
            <el-timeline-item
              v-for="log in currentDetail.audit_logs"
              :key="log.id"
              :timestamp="formatDateTime(log.created_at)"
            >
              <div class="log-content">
                <div class="log-action">{{ log.action }}</div>
                <div class="log-operator">操作人：{{ log.operator }}</div>
                <div v-if="log.remark" class="log-remark">备注：{{ log.remark }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="auditDialogVisible"
      title="佣金审核"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="auditFormRef"
        :model="auditForm"
        :rules="auditRules"
        label-width="100px"
      >
        <el-form-item label="审核结果" prop="result">
          <el-radio-group v-model="auditForm.result">
            <el-radio label="approve">通过</el-radio>
            <el-radio label="reject">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="remark">
          <el-input
            v-model="auditForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入审核备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAuditSubmit" :loading="auditing">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';
import {
  Check,
  Close,
  Refresh,
  Search
} from '@element-plus/icons-vue';
import {
  getCommissionRecords,
  getCommissionSummary,
  approveCommission,
  rejectCommission,
  payCommission,
  freezeCommission,
  batchApproveCommissions,
  batchRejectCommissions,
  type CommissionRecord,
  type CommissionQueryParams
} from '@/modules/admin/api/referral';

// ===================== 响应式数据 =====================

const loading = ref(false);
const auditing = ref(false);
const detailDialogVisible = ref(false);
const auditDialogVisible = ref(false);
const auditFormRef = ref<FormInstance>();

const tableData = ref<CommissionRecord[]>([]);
const selectedCommissions = ref<CommissionRecord[]>([]);
const currentDetail = ref<CommissionRecord | null>(null);
const currentAuditRecord = ref<CommissionRecord | null>(null);

// 搜索表单
const searchForm = reactive<CommissionQueryParams>({
  user_id: '',
  order_id: '',
  status: '',
  type: '',
  date_range: null as [Date, Date] | null
});

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
});

// 统计数据
const summary = reactive({
  total_amount: 0,
  pending_amount: 0,
  paid_amount: 0,
  frozen_amount: 0
});

// 审核表单
const auditForm = reactive({
  result: 'approve',
  remark: ''
});

// 审核表单验证规则
const auditRules: FormRules = {
  result: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  remark: [
    { required: true, message: '请输入审核备注', trigger: 'blur' }
  ]
};

// ===================== 生命周期 =====================

onMounted(() => {
  loadData();
  loadSummary();
});

// ===================== 方法定义 =====================

/**
 * 加载数据
 */
const loadData = async () => {
  try {
    loading.value = true;
    const params: CommissionQueryParams = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    };
    
    // 处理日期范围
    if (searchForm.date_range) {
      params.start_date = searchForm.date_range[0].toISOString().split('T')[0];
      params.end_date = searchForm.date_range[1].toISOString().split('T')[0];
      delete params.date_range;
    }
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null) {
        delete params[key];
      }
    });
    
    const response = await getCommissionRecords(params);
    
    if (response) {
      tableData.value = response.items || [];
      pagination.total = response.total || 0;
    }
  } catch (error: any) {
    console.error('加载佣金记录失败:', error);
    ElMessage.error(`加载数据失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

/**
 * 加载统计数据
 */
const loadSummary = async () => {
  try {
    const response = await getCommissionSummary();
    if (response) {
      Object.assign(summary, response);
    }
  } catch (error: any) {
    console.error('加载统计数据失败:', error);
  }
};

/**
 * 搜索
 */
const handleSearch = () => {
  pagination.page = 1;
  loadData();
};

/**
 * 重置搜索
 */
const handleResetSearch = () => {
  Object.assign(searchForm, {
    user_id: '',
    order_id: '',
    status: '',
    type: '',
    date_range: null
  });
  pagination.page = 1;
  loadData();
};

/**
 * 刷新数据
 */
const handleRefresh = () => {
  loadData();
  loadSummary();
};

/**
 * 查看详情
 */
const handleView = (row: CommissionRecord) => {
  currentDetail.value = row;
  detailDialogVisible.value = true;
};

/**
 * 审核佣金
 */
const handleApprove = (row: CommissionRecord) => {
  currentAuditRecord.value = row;
  auditForm.result = 'approve';
  auditForm.remark = '';
  auditDialogVisible.value = true;
};

/**
 * 发放佣金
 */
const handlePay = async (row: CommissionRecord) => {
  try {
    await ElMessageBox.confirm(
      `确定要发放佣金ID为 ${row.id} 的佣金吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await payCommission(row.id);
    ElMessage.success('发放成功');
    loadData();
    loadSummary();
  } catch (error: any) {
    if (error.message) {
      console.error('发放佣金失败:', error);
      ElMessage.error(`发放失败: ${error.message}`);
    }
  }
};

/**
 * 冻结佣金
 */
const handleFreeze = async (row: CommissionRecord) => {
  try {
    await ElMessageBox.confirm(
      `确定要冻结佣金ID为 ${row.id} 的佣金吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await freezeCommission(row.id);
    ElMessage.success('冻结成功');
    loadData();
    loadSummary();
  } catch (error: any) {
    if (error.message) {
      console.error('冻结佣金失败:', error);
      ElMessage.error(`冻结失败: ${error.message}`);
    }
  }
};

/**
 * 批量审核通过
 */
const handleBatchApprove = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要批量审核通过选中的 ${selectedCommissions.value.length} 条佣金记录吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    const ids = selectedCommissions.value.map(item => item.id);
    await batchApproveCommissions({ ids, remark: '批量审核通过' });
    ElMessage.success('批量审核成功');
    loadData();
    loadSummary();
  } catch (error: any) {
    if (error.message) {
      console.error('批量审核失败:', error);
      ElMessage.error(`批量审核失败: ${error.message}`);
    }
  }
};

/**
 * 批量拒绝
 */
const handleBatchReject = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要批量拒绝选中的 ${selectedCommissions.value.length} 条佣金记录吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    const ids = selectedCommissions.value.map(item => item.id);
    await batchRejectCommissions({ ids, remark: '批量拒绝' });
    ElMessage.success('批量拒绝成功');
    loadData();
    loadSummary();
  } catch (error: any) {
    if (error.message) {
      console.error('批量拒绝失败:', error);
      ElMessage.error(`批量拒绝失败: ${error.message}`);
    }
  }
};

/**
 * 审核提交
 */
const handleAuditSubmit = async () => {
  if (!auditFormRef.value || !currentAuditRecord.value) return;
  
  try {
    await auditFormRef.value.validate();
    
    auditing.value = true;
    
    if (auditForm.result === 'approve') {
      await approveCommission(currentAuditRecord.value.id, {
        remark: auditForm.remark
      });
      ElMessage.success('审核通过');
    } else {
      await rejectCommission(currentAuditRecord.value.id, {
        remark: auditForm.remark
      });
      ElMessage.success('审核拒绝');
    }
    
    auditDialogVisible.value = false;
    loadData();
    loadSummary();
  } catch (error: any) {
    if (error.message) {
      console.error('审核失败:', error);
      ElMessage.error(`审核失败: ${error.message}`);
    }
  } finally {
    auditing.value = false;
  }
};

/**
 * 选择改变
 */
const handleSelectionChange = (selection: CommissionRecord[]) => {
  selectedCommissions.value = selection;
};

/**
 * 分页大小改变
 */
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.page = 1;
  loadData();
};

/**
 * 当前页改变
 */
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  loadData();
};

/**
 * 获取类型标签类型
 */
const getTypeTagType = (type: string) => {
  switch (type) {
    case 'referral': return 'success';
    case 'sales': return 'warning';
    case 'team': return 'info';
    default: return 'default';
  }
};

/**
 * 获取类型文本
 */
const getTypeText = (type: string) => {
  switch (type) {
    case 'referral': return '推荐佣金';
    case 'sales': return '销售佣金';
    case 'team': return '团队佣金';
    default: return '未知';
  }
};

/**
 * 获取层级标签类型
 */
const getLevelTagType = (level: number) => {
  if (level === 1) return 'success';
  if (level === 2) return 'warning';
  if (level === 3) return 'info';
  return 'default';
};

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'pending': return 'warning';
    case 'approved': return 'success';
    case 'rejected': return 'danger';
    case 'paid': return 'info';
    case 'frozen': return 'default';
    default: return 'default';
  }
};

/**
 * 获取状态文本
 */
const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待审核';
    case 'approved': return '已审核';
    case 'rejected': return '已拒绝';
    case 'paid': return '已发放';
    case 'frozen': return '已冻结';
    default: return '未知';
  }
};

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};
</script>

<style scoped>
.commission-management-view {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-box {
  display: flex;
  gap: 10px;
}

.search-box {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.summary-box {
  margin-bottom: 20px;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.summary-item {
  text-align: center;
}

.summary-item .label {
  font-size: 14px;
  opacity: 0.9;
}

.summary-item .value {
  font-size: 24px;
  font-weight: bold;
  margin-top: 5px;
  display: block;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-size: 14px;
}

.amount {
  color: #f56c6c;
  font-weight: bold;
}

.pagination-box {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.detail-content {
  padding: 20px 0;
}

.remark-section,
.audit-logs {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.remark-section h4,
.audit-logs h4 {
  margin: 0 0 10px 0;
  color: #606266;
}

.remark-section p {
  margin: 0;
  line-height: 1.6;
}

.log-content {
  padding: 10px 0;
}

.log-action {
  font-weight: bold;
  margin-bottom: 5px;
}

.log-operator,
.log-remark {
  font-size: 12px;
  color: #909399;
  margin-bottom: 3px;
}
</style>