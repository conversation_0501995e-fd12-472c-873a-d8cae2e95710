<!--
  佣金配置页面
  作者：张二浩
  日期：2025-01-15
  版本：1.0.0
  描述：配置不同层级的佣金比例和相关参数
-->

<template>
  <div class="commission-config-view">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>佣金配置</span>
          <div class="action-box">
            <el-button type="success" @click="handleSave" :loading="saving">
              <el-icon><Check /></el-icon> 保存配置
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon> 重置
            </el-button>
          </div>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="commission"
        :rules="rules"
        label-width="150px"
        v-loading="loading"
        class="commission-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="一级佣金比例" prop="level1_rate">
              <el-input-number
                v-model="commission.level1_rate"
                :min="0"
                :max="100"
                :precision="2"
                :step="0.01"
                style="width: 100%"
              />
              <span class="unit">%</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="二级佣金比例" prop="level2_rate">
              <el-input-number
                v-model="commission.level2_rate"
                :min="0"
                :max="100"
                :precision="2"
                :step="0.01"
                style="width: 100%"
              />
              <span class="unit">%</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="三级佣金比例" prop="level3_rate">
              <el-input-number
                v-model="commission.level3_rate"
                :min="0"
                :max="100"
                :precision="2"
                :step="0.01"
                style="width: 100%"
              />
              <span class="unit">%</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大分销层级" prop="max_levels">
              <el-input-number
                v-model="commission.max_levels"
                :min="1"
                :max="10"
                style="width: 100%"
              />
              <span class="unit">级</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最小订单金额" prop="min_order_amount">
              <el-input-number
                v-model="commission.min_order_amount"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
              <span class="unit">元</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="佣金结算周期" prop="settlement_cycle">
              <el-select v-model="commission.settlement_cycle" style="width: 100%">
                <el-option label="实时结算" value="realtime" />
                <el-option label="每日结算" value="daily" />
                <el-option label="每周结算" value="weekly" />
                <el-option label="每月结算" value="monthly" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="佣金提现门槛" prop="withdraw_threshold">
              <el-input-number
                v-model="commission.withdraw_threshold"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
              <span class="unit">元</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="佣金有效期" prop="commission_validity">
              <el-input-number
                v-model="commission.commission_validity"
                :min="0"
                style="width: 100%"
              />
              <span class="unit">天</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="启用自动审核">
          <el-switch
            v-model="commission.auto_audit"
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>

        <el-form-item label="配置说明">
          <el-input
            v-model="commission.description"
            type="textarea"
            :rows="3"
            placeholder="请输入配置说明"
          />
        </el-form-item>
      </el-form>

      <div class="preview-box">
        <el-card>
          <template #header>
            <span>配置预览</span>
          </template>
          <div class="preview-content">
            <div class="preview-item">
              <span class="label">一级推荐佣金：</span>
              <span class="value">{{ commission.level1_rate }}%</span>
            </div>
            <div class="preview-item">
              <span class="label">二级推荐佣金：</span>
              <span class="value">{{ commission.level2_rate }}%</span>
            </div>
            <div class="preview-item">
              <span class="label">三级推荐佣金：</span>
              <span class="value">{{ commission.level3_rate }}%</span>
            </div>
            <div class="preview-item">
              <span class="label">最大分销层级：</span>
              <span class="value">{{ commission.max_levels }}级</span>
            </div>
            <div class="preview-item">
              <span class="label">最小订单金额：</span>
              <span class="value">{{ commission.min_order_amount }}元</span>
            </div>
            <div class="preview-item">
              <span class="label">佣金提现门槛：</span>
              <span class="value">{{ commission.withdraw_threshold }}元</span>
            </div>
          </div>
        </el-card>
      </div>

      <div class="tips-box">
        <el-alert
          title="配置说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <ul>
              <li>佣金比例建议递减设置，一级 > 二级 > 三级</li>
              <li>最小订单金额用于过滤小额订单，避免恶意刷单</li>
              <li>佣金提现门槛可以减少频繁提现的处理成本</li>
              <li>佣金有效期为0表示永久有效</li>
              <li>启用自动审核后，符合条件的佣金将自动通过审核</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';
import { Check, Refresh } from '@element-plus/icons-vue';
import {
  getCommissionConfig,
  updateCommissionConfig,
  type CommissionConfig
} from '@/modules/admin/api/referral';

// ===================== 响应式数据 =====================

const loading = ref(false);
const saving = ref(false);
const formRef = ref<FormInstance>();

const commission = reactive<CommissionConfig & {
  settlement_cycle?: string;
  withdraw_threshold?: number;
  commission_validity?: number;
  auto_audit?: boolean;
  description?: string;
}>({
  level1_rate: 5.0,
  level2_rate: 3.0,
  level3_rate: 2.0,
  max_levels: 3,
  min_order_amount: 100,
  settlement_cycle: 'daily',
  withdraw_threshold: 100,
  commission_validity: 365,
  auto_audit: false,
  description: ''
});

const originalCommission = ref<any>({});

// 表单验证规则
const rules: FormRules = {
  level1_rate: [
    { required: true, message: '请输入一级佣金比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '佣金比例必须在0-100之间', trigger: 'blur' }
  ],
  level2_rate: [
    { required: true, message: '请输入二级佣金比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '佣金比例必须在0-100之间', trigger: 'blur' }
  ],
  level3_rate: [
    { required: true, message: '请输入三级佣金比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '佣金比例必须在0-100之间', trigger: 'blur' }
  ],
  max_levels: [
    { required: true, message: '请输入最大分销层级', trigger: 'blur' },
    { type: 'number', min: 1, max: 10, message: '分销层级必须在1-10之间', trigger: 'blur' }
  ],
  min_order_amount: [
    { required: true, message: '请输入最小订单金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '最小订单金额不能小于0', trigger: 'blur' }
  ]
};

// ===================== 生命周期 =====================

onMounted(() => {
  loadCommissionConfig();
});

// ===================== 方法定义 =====================

/**
 * 加载佣金配置
 */
const loadCommissionConfig = async () => {
  try {
    loading.value = true;
    const response = await getCommissionConfig();
    
    if (response) {
      // 转换百分比显示
      Object.assign(commission, {
        ...response,
        level1_rate: (response.level1_rate || 0) * 100,
        level2_rate: (response.level2_rate || 0) * 100,
        level3_rate: (response.level3_rate || 0) * 100
      });
      originalCommission.value = JSON.parse(JSON.stringify(commission));
    }
  } catch (error: any) {
    console.error('加载佣金配置失败:', error);
    ElMessage.error(`加载佣金配置失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

/**
 * 保存配置
 */
const handleSave = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    saving.value = true;
    
    // 转换数据格式
    const configData = {
      ...commission,
      level1_rate: commission.level1_rate / 100,
      level2_rate: commission.level2_rate / 100,
      level3_rate: commission.level3_rate / 100
    };
    
    await updateCommissionConfig(configData);
    ElMessage.success('佣金配置已保存');
    
    // 重新加载数据
    await loadCommissionConfig();
  } catch (error: any) {
    if (error.message) {
      console.error('保存佣金配置失败:', error);
      ElMessage.error(`保存失败: ${error.message}`);
    }
  } finally {
    saving.value = false;
  }
};

/**
 * 重置配置
 */
const handleReset = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有配置吗？这将丢失所有未保存的修改。',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    Object.assign(commission, originalCommission.value);
    ElMessage.success('配置已重置');
  } catch {
    // 用户取消重置
  }
};
</script>

<style scoped>
.commission-config-view {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-box {
  display: flex;
  gap: 10px;
}

.commission-form {
  margin-bottom: 20px;
}

.unit {
  margin-left: 8px;
  color: #909399;
}

.preview-box {
  margin: 20px 0;
}

.preview-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.preview-item .label {
  color: #606266;
}

.preview-item .value {
  font-weight: bold;
  color: #409eff;
}

.tips-box {
  margin-top: 20px;
}

.tips-box ul {
  margin: 0;
  padding-left: 20px;
}

.tips-box li {
  margin-bottom: 5px;
}
</style>