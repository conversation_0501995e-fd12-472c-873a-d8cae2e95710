<!--
  佣金配置表单组件
  作者：张二浩
  日期：2025-01-15
  版本：1.0.0
  描述：用于配置佣金相关参数的表单组件
-->

<template>
  <el-card class="commission-config-form">
    <template #header>
      <div class="card-header">
        <span>佣金配置</span>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            @click="handleSave"
            :loading="saving"
          >
            保存配置
          </el-button>
          <el-button
            size="small"
            @click="handleReset"
          >
            重置
          </el-button>
        </div>
      </div>
    </template>
    
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      v-loading="loading"
    >
      <!-- 基础佣金配置 -->
      <el-divider content-position="left">
        <el-icon><Money /></el-icon>
        基础佣金配置
      </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="一级佣金比例" prop="first_level_rate">
            <el-input-number
              v-model="formData.first_level_rate"
              :min="0"
              :max="100"
              :precision="2"
              :step="0.01"
              style="width: 100%"
            />
            <span class="unit">%</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="二级佣金比例" prop="second_level_rate">
            <el-input-number
              v-model="formData.second_level_rate"
              :min="0"
              :max="100"
              :precision="2"
              :step="0.01"
              style="width: 100%"
            />
            <span class="unit">%</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="三级佣金比例" prop="third_level_rate">
            <el-input-number
              v-model="formData.third_level_rate"
              :min="0"
              :max="100"
              :precision="2"
              :step="0.01"
              style="width: 100%"
            />
            <span class="unit">%</span>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 分销层级配置 -->
      <el-divider content-position="left">
        <el-icon><Histogram /></el-icon>
        分销层级配置
      </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最大分销层级" prop="max_levels">
            <el-input-number
              v-model="formData.max_levels"
              :min="1"
              :max="10"
              style="width: 100%"
            />
            <div class="form-tip">支持的最大分销层级数量</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最小订单金额" prop="min_order_amount">
            <el-input-number
              v-model="formData.min_order_amount"
              :min="0"
              :precision="2"
              style="width: 100%"
            />
            <span class="unit">元</span>
            <div class="form-tip">产生佣金的最小订单金额</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 结算配置 -->
      <el-divider content-position="left">
        <el-icon><Calendar /></el-icon>
        结算配置
      </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="结算周期" prop="settlement_period">
            <el-select
              v-model="formData.settlement_period"
              style="width: 100%"
              placeholder="请选择结算周期"
            >
              <el-option label="实时结算" value="realtime" />
              <el-option label="每日结算" value="daily" />
              <el-option label="每周结算" value="weekly" />
              <el-option label="每月结算" value="monthly" />
            </el-select>
            <div class="form-tip">佣金结算的时间周期</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="结算延迟天数" prop="settlement_delay_days">
            <el-input-number
              v-model="formData.settlement_delay_days"
              :min="0"
              :max="365"
              style="width: 100%"
            />
            <span class="unit">天</span>
            <div class="form-tip">订单完成后延迟结算的天数</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="提现门槛" prop="withdrawal_threshold">
            <el-input-number
              v-model="formData.withdrawal_threshold"
              :min="0"
              :precision="2"
              style="width: 100%"
            />
            <span class="unit">元</span>
            <div class="form-tip">允许提现的最小金额</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 佣金有效期配置 -->
      <el-divider content-position="left">
        <el-icon><Clock /></el-icon>
        有效期配置
      </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="佣金有效期" prop="commission_validity_days">
            <el-input-number
              v-model="formData.commission_validity_days"
              :min="0"
              :max="3650"
              style="width: 100%"
            />
            <span class="unit">天</span>
            <div class="form-tip">佣金的有效期，0表示永久有效</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关系有效期" prop="relationship_validity_days">
            <el-input-number
              v-model="formData.relationship_validity_days"
              :min="0"
              :max="3650"
              style="width: 100%"
            />
            <span class="unit">天</span>
            <div class="form-tip">分销关系的有效期，0表示永久有效</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 审核配置 -->
      <el-divider content-position="left">
        <el-icon><DocumentChecked /></el-icon>
        审核配置
      </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="自动审核">
            <el-switch
              v-model="formData.auto_approve"
              active-text="开启"
              inactive-text="关闭"
            />
            <div class="form-tip">是否自动审核佣金发放</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="自动审核金额" prop="auto_approve_threshold" v-if="formData.auto_approve">
            <el-input-number
              v-model="formData.auto_approve_threshold"
              :min="0"
              :precision="2"
              style="width: 100%"
            />
            <span class="unit">元</span>
            <div class="form-tip">小于此金额的佣金自动审核通过</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 其他配置 -->
      <el-divider content-position="left">
        <el-icon><Setting /></el-icon>
        其他配置
      </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="允许自推">
            <el-switch
              v-model="formData.allow_self_referral"
              active-text="允许"
              inactive-text="禁止"
            />
            <div class="form-tip">是否允许用户推荐自己</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重复推荐">
            <el-switch
              v-model="formData.allow_duplicate_referral"
              active-text="允许"
              inactive-text="禁止"
            />
            <div class="form-tip">是否允许重复建立推荐关系</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="配置说明">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入配置说明"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <!-- 配置预览 -->
    <div class="config-preview">
      <el-card>
        <template #header>
          <span>配置预览</span>
        </template>
        <div class="preview-content">
          <div class="preview-item">
            <span class="label">佣金层级：</span>
            <span class="value">
              一级{{ formData.first_level_rate }}% / 
              二级{{ formData.second_level_rate }}% / 
              三级{{ formData.third_level_rate }}%
            </span>
          </div>
          <div class="preview-item">
            <span class="label">最大层级：</span>
            <span class="value">{{ formData.max_levels }}级</span>
          </div>
          <div class="preview-item">
            <span class="label">最小订单：</span>
            <span class="value">{{ formData.min_order_amount }}元</span>
          </div>
          <div class="preview-item">
            <span class="label">结算周期：</span>
            <span class="value">{{ getSettlementPeriodText(formData.settlement_period) }}</span>
          </div>
          <div class="preview-item">
            <span class="label">提现门槛：</span>
            <span class="value">{{ formData.withdrawal_threshold }}元</span>
          </div>
        </div>
      </el-card>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import {
  Money,
  Histogram,
  Calendar,
  Clock,
  DocumentChecked,
  Setting
} from '@element-plus/icons-vue';
import type { CommissionConfig } from '@/modules/admin/api/referral';

// ===================== Props & Emits =====================

interface Props {
  data?: CommissionConfig | null;
  loading?: boolean;
}

interface Emits {
  (e: 'save', data: CommissionConfig): void;
  (e: 'reset'): void;
}

const props = withDefaults(defineProps<Props>(), {
  data: null,
  loading: false
});

const emit = defineEmits<Emits>();

// ===================== 响应式数据 =====================

const formRef = ref<FormInstance>();
const saving = ref(false);

// 表单数据
const formData = reactive<Partial<CommissionConfig> & {
  auto_approve?: boolean;
  auto_approve_threshold?: number;
  allow_self_referral?: boolean;
  allow_duplicate_referral?: boolean;
  description?: string;
}>({
  first_level_rate: 5,
  second_level_rate: 3,
  third_level_rate: 1,
  max_levels: 3,
  min_order_amount: 0,
  settlement_period: 'monthly',
  settlement_delay_days: 7,
  withdrawal_threshold: 100,
  commission_validity_days: 365,
  relationship_validity_days: 0,
  auto_approve: true,
  auto_approve_threshold: 1000,
  allow_self_referral: false,
  allow_duplicate_referral: false,
  description: ''
});

// 表单验证规则
const formRules: FormRules = {
  first_level_rate: [
    { required: true, message: '请输入一级佣金比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '佣金比例必须在0-100之间', trigger: 'blur' }
  ],
  second_level_rate: [
    { required: true, message: '请输入二级佣金比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '佣金比例必须在0-100之间', trigger: 'blur' }
  ],
  third_level_rate: [
    { required: true, message: '请输入三级佣金比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '佣金比例必须在0-100之间', trigger: 'blur' }
  ],
  max_levels: [
    { required: true, message: '请输入最大分销层级', trigger: 'blur' },
    { type: 'number', min: 1, max: 10, message: '分销层级必须在1-10之间', trigger: 'blur' }
  ],
  min_order_amount: [
    { required: true, message: '请输入最小订单金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '最小订单金额不能小于0', trigger: 'blur' }
  ],
  settlement_period: [
    { required: true, message: '请选择结算周期', trigger: 'change' }
  ],
  settlement_delay_days: [
    { required: true, message: '请输入结算延迟天数', trigger: 'blur' },
    { type: 'number', min: 0, max: 365, message: '延迟天数必须在0-365之间', trigger: 'blur' }
  ],
  withdrawal_threshold: [
    { required: true, message: '请输入提现门槛', trigger: 'blur' },
    { type: 'number', min: 0, message: '提现门槛不能小于0', trigger: 'blur' }
  ],
  commission_validity_days: [
    { required: true, message: '请输入佣金有效期', trigger: 'blur' },
    { type: 'number', min: 0, max: 3650, message: '有效期必须在0-3650天之间', trigger: 'blur' }
  ],
  relationship_validity_days: [
    { required: true, message: '请输入关系有效期', trigger: 'blur' },
    { type: 'number', min: 0, max: 3650, message: '有效期必须在0-3650天之间', trigger: 'blur' }
  ],
  auto_approve_threshold: [
    { type: 'number', min: 0, message: '自动审核金额不能小于0', trigger: 'blur' }
  ]
};

// ===================== 监听器 =====================

watch(
  () => props.data,
  (newData) => {
    if (newData) {
      Object.assign(formData, {
        ...newData,
        first_level_rate: (newData.first_level_rate || 0) * 100,
        second_level_rate: (newData.second_level_rate || 0) * 100,
        third_level_rate: (newData.third_level_rate || 0) * 100,
        auto_approve: newData.auto_approve !== false,
        allow_self_referral: newData.allow_self_referral === true,
        allow_duplicate_referral: newData.allow_duplicate_referral === true
      });
    }
  },
  { immediate: true }
);

// ===================== 方法定义 =====================

/**
 * 获取结算周期文本
 */
const getSettlementPeriodText = (period: string | undefined) => {
  if (!period) return '';
  const periodMap: Record<string, string> = {
    realtime: '实时结算',
    daily: '每日结算',
    weekly: '每周结算',
    monthly: '每月结算'
  };
  return periodMap[period] || period;
};

/**
 * 保存配置
 */
const handleSave = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    saving.value = true;
    
    // 转换数据格式
    const saveData = {
      ...formData,
      first_level_rate: formData.first_level_rate! / 100,
      second_level_rate: formData.second_level_rate! / 100,
      third_level_rate: formData.third_level_rate! / 100
    } as CommissionConfig;
    
    emit('save', saveData);
    
    ElMessage.success('配置保存成功');
  } catch (error) {
    console.error('表单验证失败:', error);
    ElMessage.error('请检查表单输入');
  } finally {
    saving.value = false;
  }
};

/**
 * 重置配置
 */
const handleReset = () => {
  formRef.value?.resetFields();
  emit('reset');
  ElMessage.info('配置已重置');
};
</script>

<style scoped>
.commission-config-form {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.unit {
  margin-left: 8px;
  color: #909399;
}

.config-preview {
  margin-top: 20px;
}

.preview-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.preview-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.preview-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.preview-item .value {
  color: #409eff;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .preview-content {
    grid-template-columns: 1fr;
  }
  
  .preview-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .preview-item .label {
    min-width: auto;
  }
}
</style>