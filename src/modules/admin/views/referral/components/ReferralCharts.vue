<!--
  分销统计图表组件
  作者：张二浩
  日期：2025-01-15
  版本：1.0.0
  描述：用于展示分销统计数据的图表组件
-->

<template>
  <div class="referral-charts">
    <!-- 图表控制栏 -->
    <div class="chart-controls">
      <el-row :gutter="20" align="middle">
        <el-col :span="8">
          <el-select
            v-model="selectedTimeRange"
            placeholder="选择时间范围"
            @change="handleTimeRangeChange"
          >
            <el-option label="最近7天" value="7d" />
            <el-option label="最近30天" value="30d" />
            <el-option label="最近90天" value="90d" />
            <el-option label="最近1年" value="1y" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="customDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleCustomDateChange"
          />
        </el-col>
        <el-col :span="8">
          <div class="control-buttons">
            <el-button
              type="primary"
              :icon="Refresh"
              @click="refreshCharts"
              :loading="loading"
            >
              刷新
            </el-button>
            <el-button
              :icon="Download"
              @click="exportCharts"
            >
              导出
            </el-button>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <!-- 图表网格 -->
    <div class="charts-grid" v-loading="loading">
      <!-- 分销趋势图 -->
      <el-card class="chart-card">
        <template #header>
          <div class="chart-header">
            <span>分销趋势</span>
            <el-tag size="small" type="info">{{ selectedTimeRange }}</el-tag>
          </div>
        </template>
        <div ref="trendChartRef" class="chart-container"></div>
      </el-card>
      
      <!-- 层级分布图 -->
      <el-card class="chart-card">
        <template #header>
          <div class="chart-header">
            <span>层级分布</span>
            <el-tooltip content="显示各层级的用户分布情况" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <div ref="levelChartRef" class="chart-container"></div>
      </el-card>
      
      <!-- 佣金统计图 -->
      <el-card class="chart-card">
        <template #header>
          <div class="chart-header">
            <span>佣金统计</span>
            <el-switch
              v-model="showCommissionDetails"
              active-text="详细"
              inactive-text="概览"
              @change="updateCommissionChart"
            />
          </div>
        </template>
        <div ref="commissionChartRef" class="chart-container"></div>
      </el-card>
      
      <!-- 地域分布图 -->
      <el-card class="chart-card">
        <template #header>
          <div class="chart-header">
            <span>地域分布</span>
            <el-select
              v-model="selectedRegionType"
              size="small"
              @change="updateRegionChart"
            >
              <el-option label="省份" value="province" />
              <el-option label="城市" value="city" />
            </el-select>
          </div>
        </template>
        <div ref="regionChartRef" class="chart-container"></div>
      </el-card>
      
      <!-- 转化漏斗图 -->
      <el-card class="chart-card">
        <template #header>
          <div class="chart-header">
            <span>转化漏斗</span>
            <el-tooltip content="显示从访问到成交的转化过程" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <div ref="funnelChartRef" class="chart-container"></div>
      </el-card>
      
      <!-- 活跃度热力图 -->
      <el-card class="chart-card">
        <template #header>
          <div class="chart-header">
            <span>活跃度热力图</span>
            <el-select
              v-model="selectedHeatmapType"
              size="small"
              @change="updateHeatmapChart"
            >
              <el-option label="推荐活跃度" value="referral" />
              <el-option label="订单活跃度" value="order" />
            </el-select>
          </div>
        </template>
        <div ref="heatmapChartRef" class="chart-container"></div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Refresh, Download, QuestionFilled } from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import type { ECharts } from 'echarts';
import { getReferralChartData, exportReferralData } from '@/modules/admin/api/referral';

// ===================== Props & Emits =====================

interface Props {
  loading?: boolean;
}

interface Emits {
  (e: 'refresh'): void;
  (e: 'export'): void;
}

withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// ===================== 响应式数据 =====================

const selectedTimeRange = ref('30d');
const customDateRange = ref<[string, string] | null>(null);
const showCommissionDetails = ref(false);
const selectedRegionType = ref('province');
const selectedHeatmapType = ref('referral');

// 图表引用
const trendChartRef = ref<HTMLElement>();
const levelChartRef = ref<HTMLElement>();
const commissionChartRef = ref<HTMLElement>();
const regionChartRef = ref<HTMLElement>();
const funnelChartRef = ref<HTMLElement>();
const heatmapChartRef = ref<HTMLElement>();

// 图表实例
let trendChart: ECharts | null = null;
let levelChart: ECharts | null = null;
let commissionChart: ECharts | null = null;
let regionChart: ECharts | null = null;
let funnelChart: ECharts | null = null;
let heatmapChart: ECharts | null = null;

// 图表数据
const chartData = reactive<{
  trend: any;
  level: any;
  commission: any;
  region: any;
  funnel: any;
  heatmap: any;
}>({
  trend: null,
  level: null,
  commission: null,
  region: null,
  funnel: null,
  heatmap: null
});

// ===================== 生命周期 =====================

onMounted(async () => {
  await nextTick();
  initCharts();
  loadChartData();
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  // 销毁图表实例
  trendChart?.dispose();
  levelChart?.dispose();
  commissionChart?.dispose();
  regionChart?.dispose();
  funnelChart?.dispose();
  heatmapChart?.dispose();
  
  window.removeEventListener('resize', handleResize);
});

// ===================== 方法定义 =====================

/**
 * 初始化图表
 */
const initCharts = () => {
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value);
  }
  if (levelChartRef.value) {
    levelChart = echarts.init(levelChartRef.value);
  }
  if (commissionChartRef.value) {
    commissionChart = echarts.init(commissionChartRef.value);
  }
  if (regionChartRef.value) {
    regionChart = echarts.init(regionChartRef.value);
  }
  if (funnelChartRef.value) {
    funnelChart = echarts.init(funnelChartRef.value);
  }
  if (heatmapChartRef.value) {
    heatmapChart = echarts.init(heatmapChartRef.value);
  }
};

/**
 * 加载图表数据
 */
const loadChartData = async () => {
  try {
    const params = {
      time_range: selectedTimeRange.value,
      start_date: customDateRange.value?.[0],
      end_date: customDateRange.value?.[1]
    };
    
    const response: any = await getReferralChartData(params);
    const data = response.data;
    
    chartData.trend = data.trend;
    chartData.level = data.level;
    chartData.commission = data.commission;
    chartData.region = data.region;
    chartData.funnel = data.funnel;
    chartData.heatmap = data.heatmap;
    
    // 更新所有图表
    updateTrendChart();
    updateLevelChart();
    updateCommissionChart();
    updateRegionChart();
    updateFunnelChart();
    updateHeatmapChart();
  } catch (error) {
    console.error('加载图表数据失败:', error);
    ElMessage.error('加载图表数据失败');
  }
};

/**
 * 更新分销趋势图
 */
const updateTrendChart = () => {
  if (!trendChart || !chartData.trend) return;
  
  const option = {
    title: {
      text: '分销趋势',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['新增推荐', '新增关系', '佣金金额'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.trend.dates,
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left',
        axisLine: {
          lineStyle: {
            color: '#e4e7ed'
          }
        }
      },
      {
        type: 'value',
        name: '金额(元)',
        position: 'right',
        axisLine: {
          lineStyle: {
            color: '#e4e7ed'
          }
        }
      }
    ],
    series: [
      {
        name: '新增推荐',
        type: 'line',
        data: chartData.trend.referrals,
        smooth: true,
        itemStyle: {
          color: '#409eff'
        }
      },
      {
        name: '新增关系',
        type: 'line',
        data: chartData.trend.relationships,
        smooth: true,
        itemStyle: {
          color: '#67c23a'
        }
      },
      {
        name: '佣金金额',
        type: 'line',
        yAxisIndex: 1,
        data: chartData.trend.commissions,
        smooth: true,
        itemStyle: {
          color: '#e6a23c'
        }
      }
    ]
  };
  
  trendChart.setOption(option);
};

/**
 * 更新层级分布图
 */
const updateLevelChart = () => {
  if (!levelChart || !chartData.level) return;
  
  const option = {
    title: {
      text: '层级分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: chartData.level.map((item: any) => item.name)
    },
    series: [
      {
        name: '层级分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data: chartData.level,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  
  levelChart.setOption(option);
};

/**
 * 更新佣金统计图
 */
const updateCommissionChart = () => {
  if (!commissionChart || !chartData.commission) return;
  
  const option = {
    title: {
      text: '佣金统计',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: showCommissionDetails.value ? 
        ['待审核', '已发放', '已拒绝', '已冻结'] : 
        ['总佣金', '已发放佣金'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.commission.categories,
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '金额(元)',
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      }
    },
    series: showCommissionDetails.value ? [
      {
        name: '待审核',
        type: 'bar',
        stack: '总量',
        data: chartData.commission.pending,
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '已发放',
        type: 'bar',
        stack: '总量',
        data: chartData.commission.paid,
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '已拒绝',
        type: 'bar',
        stack: '总量',
        data: chartData.commission.rejected,
        itemStyle: { color: '#f56c6c' }
      },
      {
        name: '已冻结',
        type: 'bar',
        stack: '总量',
        data: chartData.commission.frozen,
        itemStyle: { color: '#909399' }
      }
    ] : [
      {
        name: '总佣金',
        type: 'bar',
        data: chartData.commission.total,
        itemStyle: { color: '#409eff' }
      },
      {
        name: '已发放佣金',
        type: 'bar',
        data: chartData.commission.paid,
        itemStyle: { color: '#67c23a' }
      }
    ]
  };
  
  commissionChart.setOption(option);
};

/**
 * 更新地域分布图
 */
const updateRegionChart = () => {
  if (!regionChart || !chartData.region) return;
  
  const data = selectedRegionType.value === 'province' ? 
    chartData.region?.provinces : chartData.region?.cities;
  
  const option = {
    title: {
      text: `${selectedRegionType.value === 'province' ? '省份' : '城市'}分布`,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'treemap',
        data: data,
        roam: false,
        nodeClick: false,
        breadcrumb: {
          show: false
        },
        label: {
          show: true,
          formatter: '{b}\n{c}'
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 2
        }
      }
    ]
  };
  
  regionChart.setOption(option);
};

/**
 * 更新转化漏斗图
 */
const updateFunnelChart = () => {
  if (!funnelChart || !chartData.funnel) return;
  
  const option = {
    title: {
      text: '转化漏斗',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}'
    },
    series: [
      {
        name: '转化漏斗',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: chartData.funnel
      }
    ]
  };
  
  funnelChart.setOption(option);
};

/**
 * 更新活跃度热力图
 */
const updateHeatmapChart = () => {
  if (!heatmapChart || !chartData.heatmap) return;
  
  const data = selectedHeatmapType.value === 'referral' ? 
    chartData.heatmap?.referral : chartData.heatmap?.order;
  
  const option = {
    title: {
      text: `${selectedHeatmapType.value === 'referral' ? '推荐' : '订单'}活跃度热力图`,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      position: 'top'
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: data.hours,
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: data.days,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: data.max,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%'
    },
    series: [
      {
        name: '活跃度',
        type: 'heatmap',
        data: data.values,
        label: {
          show: true
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  
  heatmapChart.setOption(option);
};

/**
 * 处理时间范围变化
 */
const handleTimeRangeChange = () => {
  customDateRange.value = null;
  loadChartData();
};

/**
 * 处理自定义日期变化
 */
const handleCustomDateChange = () => {
  if (customDateRange.value) {
    selectedTimeRange.value = 'custom';
    loadChartData();
  }
};

/**
 * 刷新图表
 */
const refreshCharts = () => {
  emit('refresh');
  loadChartData();
};

/**
 * 导出图表
 */
const exportCharts = async () => {
  try {
    const params = {
      time_range: selectedTimeRange.value,
      start_date: customDateRange.value?.[0],
      end_date: customDateRange.value?.[1],
      format: 'excel'
    };
    
    await exportReferralData(params);
    ElMessage.success('导出成功');
    emit('export');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  }
};

/**
 * 处理窗口大小变化
 */
const handleResize = () => {
  trendChart?.resize();
  levelChart?.resize();
  commissionChart?.resize();
  regionChart?.resize();
  funnelChart?.resize();
  heatmapChart?.resize();
};
</script>

<style scoped>
.referral-charts {
  padding: 20px;
}

.chart-controls {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.control-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
}

.chart-card {
  min-height: 400px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  width: 100%;
  height: 350px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }
}

@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-container {
    height: 300px;
  }
  
  .control-buttons {
    justify-content: center;
  }
}
</style>