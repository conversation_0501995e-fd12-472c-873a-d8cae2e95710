<!--
  分销关系表单组件
  作者：张二浩
  日期：2025-01-15
  版本：1.0.0
  描述：用于新增和编辑分销关系的表单组件
-->

<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="推荐人" prop="referrer_id">
            <el-select
              v-model="formData.referrer_id"
              filterable
              remote
              reserve-keyword
              placeholder="请输入用户名或手机号搜索"
              :remote-method="searchReferrer"
              :loading="referrerLoading"
              style="width: 100%"
              @change="handleReferrerChange"
            >
              <el-option
                v-for="user in referrerOptions"
                :key="user.id"
                :label="`${user.username} (${user.phone})`"
                :value="user.id"
              >
                <div class="user-option">
                  <el-avatar :size="24" :src="user.avatar" />
                  <div class="user-info">
                    <div class="username">{{ user.username }}</div>
                    <div class="phone">{{ user.phone }}</div>
                  </div>
                </div>
              </el-option>
            </el-select>
            <div class="form-tip">选择推荐人用户</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="被推荐人" prop="referee_id">
            <el-select
              v-model="formData.referee_id"
              filterable
              remote
              reserve-keyword
              placeholder="请输入用户名或手机号搜索"
              :remote-method="searchReferee"
              :loading="refereeLoading"
              style="width: 100%"
              @change="handleRefereeChange"
            >
              <el-option
                v-for="user in refereeOptions"
                :key="user.id"
                :label="`${user.username} (${user.phone})`"
                :value="user.id"
              >
                <div class="user-option">
                  <el-avatar :size="24" :src="user.avatar" />
                  <div class="user-info">
                    <div class="username">{{ user.username }}</div>
                    <div class="phone">{{ user.phone }}</div>
                  </div>
                </div>
              </el-option>
            </el-select>
            <div class="form-tip">选择被推荐人用户</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关系层级" prop="level">
            <el-input-number
              v-model="formData.level"
              :min="1"
              :max="10"
              style="width: 100%"
            />
            <div class="form-tip">在分销体系中的层级</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关系状态" prop="status">
            <el-select
              v-model="formData.status"
              style="width: 100%"
              placeholder="请选择关系状态"
            >
              <el-option label="有效" value="active" />
              <el-option label="无效" value="inactive" />
              <el-option label="暂停" value="suspended" />
              <el-option label="过期" value="expired" />
            </el-select>
            <div class="form-tip">分销关系的当前状态</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="生效时间" prop="effective_at">
            <el-date-picker
              v-model="formData.effective_at"
              type="datetime"
              placeholder="选择生效时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
            <div class="form-tip">关系开始生效的时间</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="过期时间" prop="expires_at">
            <el-date-picker
              v-model="formData.expires_at"
              type="datetime"
              placeholder="选择过期时间（可选）"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
            <div class="form-tip">关系过期时间，留空表示永久有效</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="备注信息">
        <el-input
          v-model="formData.notes"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      
      <!-- 关系验证提示 -->
      <el-alert
        v-if="validationMessage"
        :title="validationMessage"
        :type="validationType"
        :closable="false"
        style="margin-bottom: 20px"
      />
    </el-form>
    
    <!-- 关系预览 -->
    <div class="relationship-preview" v-if="selectedReferrer && selectedReferee">
      <el-card>
        <template #header>
          <span>关系预览</span>
        </template>
        <div class="preview-content">
          <div class="user-card">
            <el-avatar :size="50" :src="selectedReferrer.avatar" />
            <div class="user-details">
              <div class="username">{{ selectedReferrer.username }}</div>
              <div class="user-meta">{{ selectedReferrer.phone }}</div>
              <el-tag size="small" type="success">推荐人</el-tag>
            </div>
          </div>
          
          <div class="relationship-arrow">
            <el-icon size="24" color="#409eff">
              <ArrowRight />
            </el-icon>
            <div class="level-badge">{{ formData.level }}级</div>
          </div>
          
          <div class="user-card">
            <el-avatar :size="50" :src="selectedReferee.avatar" />
            <div class="user-details">
              <div class="username">{{ selectedReferee.username }}</div>
              <div class="user-meta">{{ selectedReferee.phone }}</div>
              <el-tag size="small" type="info">被推荐人</el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          :disabled="!isFormValid"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { ArrowRight } from '@element-plus/icons-vue';
import type { ReferralRelationship, User } from '@/modules/admin/api/referral';
import { searchUsers } from '@/modules/admin/api/user';

// ===================== Props & Emits =====================

interface Props {
  modelValue: boolean;
  data?: ReferralRelationship | null;
  loading?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'submit', data: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  data: null,
  loading: false
});

const emit = defineEmits<Emits>();

// ===================== 响应式数据 =====================

const formRef = ref<FormInstance>();
const submitting = ref(false);
const referrerLoading = ref(false);
const refereeLoading = ref(false);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isEdit = computed(() => !!props.data?.id);
const title = computed(() => isEdit.value ? '编辑分销关系' : '新增分销关系');

// 表单数据
const formData = reactive<Partial<ReferralRelationship> & {
  notes?: string;
}>({
  referrer_id: undefined,
  referee_id: undefined,
  level: 1,
  status: 'active',
  effective_at: '',
  expires_at: '',
  notes: ''
});

// 用户选项
const referrerOptions = ref<User[]>([]);
const refereeOptions = ref<User[]>([]);
const selectedReferrer = ref<User | null>(null);
const selectedReferee = ref<User | null>(null);

// 验证信息
const validationMessage = ref('');
const validationType = ref<'success' | 'warning' | 'error'>('success');

// 表单验证规则
const formRules: FormRules = {
  referrer_id: [
    { required: true, message: '请选择推荐人', trigger: 'change' }
  ],
  referee_id: [
    { required: true, message: '请选择被推荐人', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请输入关系层级', trigger: 'blur' },
    { type: 'number', min: 1, max: 10, message: '层级必须在1-10之间', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择关系状态', trigger: 'change' }
  ],
  effective_at: [
    { required: true, message: '请选择生效时间', trigger: 'change' }
  ]
};

// ===================== 计算属性 =====================

const isFormValid = computed(() => {
  return formData.referrer_id && 
         formData.referee_id && 
         formData.referrer_id !== formData.referee_id &&
         formData.level &&
         formData.status &&
         formData.effective_at;
});

// ===================== 监听器 =====================

watch(
  () => props.data,
  (newData) => {
    if (newData) {
      Object.assign(formData, {
        ...newData,
        effective_at: newData.effective_at || new Date().toISOString().slice(0, 19).replace('T', ' '),
        expires_at: newData.expires_at || '',
        notes: newData.notes || ''
      });
      
      // 加载用户信息
      if (newData.referrer_id) {
        loadUserInfo(newData.referrer_id, 'referrer');
      }
      if (newData.referee_id) {
        loadUserInfo(newData.referee_id, 'referee');
      }
    } else {
      // 重置表单
      Object.assign(formData, {
        referrer_id: undefined,
        referee_id: undefined,
        level: 1,
        status: 'active',
        effective_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        expires_at: '',
        notes: ''
      });
      
      // 清空选项
      referrerOptions.value = [];
      refereeOptions.value = [];
      selectedReferrer.value = null;
      selectedReferee.value = null;
      validationMessage.value = '';
    }
  },
  { immediate: true }
);

// 监听用户选择变化
watch(
  [() => formData.referrer_id, () => formData.referee_id],
  () => {
    validateRelationship();
  }
);

// ===================== 方法定义 =====================

/**
 * 搜索推荐人
 */
const searchReferrer = async (query: string) => {
  if (!query) {
    referrerOptions.value = [];
    return;
  }
  
  referrerLoading.value = true;
  try {
    const response: any = await searchUsers(query);
    referrerOptions.value = response.items || [];
  } catch (error) {
    console.error('搜索推荐人失败:', error);
    ElMessage.error('搜索推荐人失败');
  } finally {
    referrerLoading.value = false;
  }
};

/**
 * 搜索被推荐人
 */
const searchReferee = async (query: string) => {
  if (!query) {
    refereeOptions.value = [];
    return;
  }
  
  refereeLoading.value = true;
  try {
    const response: any = await searchUsers(query);
    refereeOptions.value = response.items || [];
  } catch (error) {
    console.error('搜索被推荐人失败:', error);
    ElMessage.error('搜索被推荐人失败');
  } finally {
    refereeLoading.value = false;
  }
};

/**
 * 加载用户信息
 */
const loadUserInfo = async (userId: number, type: 'referrer' | 'referee') => {
  try {
    const response: any = await searchUsers(userId.toString());
    const user = response.items?.[0];
    if (user) {
      if (type === 'referrer') {
        selectedReferrer.value = user;
        if (!referrerOptions.value.find(u => u.id === user.id)) {
          referrerOptions.value.push(user);
        }
      } else {
        selectedReferee.value = user;
        if (!refereeOptions.value.find(u => u.id === user.id)) {
          refereeOptions.value.push(user);
        }
      }
    }
  } catch (error) {
    console.error('加载用户信息失败:', error);
  }
};

/**
 * 处理推荐人选择变化
 */
const handleReferrerChange = (userId: number) => {
  const user = referrerOptions.value.find(u => u.id === userId);
  selectedReferrer.value = user || null;
};

/**
 * 处理被推荐人选择变化
 */
const handleRefereeChange = (userId: number) => {
  const user = refereeOptions.value.find(u => u.id === userId);
  selectedReferee.value = user || null;
};

/**
 * 验证分销关系
 */
const validateRelationship = () => {
  validationMessage.value = '';
  
  if (!formData.referrer_id || !formData.referee_id) {
    return;
  }
  
  if (formData.referrer_id === formData.referee_id) {
    validationMessage.value = '推荐人和被推荐人不能是同一个用户';
    validationType.value = 'error';
    return;
  }
  
  // 这里可以添加更多验证逻辑，比如检查是否已存在关系等
  validationMessage.value = '关系验证通过';
  validationType.value = 'success';
};

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    if (!isFormValid.value) {
      ElMessage.error('请完善表单信息');
      return;
    }
    
    if (formData.referrer_id === formData.referee_id) {
      ElMessage.error('推荐人和被推荐人不能是同一个用户');
      return;
    }
    
    submitting.value = true;
    emit('submit', { ...formData });
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    submitting.value = false;
  }
};

/**
 * 关闭对话框
 */
const handleClose = () => {
  visible.value = false;
  // 重置表单验证状态
  formRef.value?.resetFields();
};
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.user-option {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 0;
}

.user-info {
  flex: 1;
}

.username {
  font-weight: 500;
  color: #303133;
}

.phone {
  font-size: 12px;
  color: #909399;
}

.relationship-preview {
  margin-top: 20px;
}

.preview-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.user-card {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.user-details {
  flex: 1;
}

.user-details .username {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
  color: white;
}

.user-details .user-meta {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 8px;
}

.relationship-arrow {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  margin: 0 20px;
}

.level-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-content {
    flex-direction: column;
    gap: 20px;
  }
  
  .relationship-arrow {
    transform: rotate(90deg);
    margin: 10px 0;
  }
  
  .user-card {
    justify-content: center;
    text-align: center;
  }
}
</style>