<!--
  分销等级表单组件
  作者：张二浩
  日期：2025-01-15
  版本：1.0.0
  描述：用于新增和编辑分销等级的表单组件
-->

<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="等级名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入等级名称，如：青铜会员、白银会员"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="等级编码" prop="code">
        <el-input
          v-model="formData.code"
          placeholder="请输入等级编码，如：bronze、silver"
          maxlength="20"
          :disabled="isEdit"
        />
        <div class="form-tip">等级编码创建后不可修改</div>
      </el-form-item>
      
      <el-form-item label="等级层级" prop="level">
        <el-input-number
          v-model="formData.level"
          :min="1"
          :max="10"
          style="width: 100%"
        />
        <div class="form-tip">数字越小等级越高，1为最高等级</div>
      </el-form-item>
      
      <el-form-item label="所需推荐人数" prop="required_referrals">
        <el-input-number
          v-model="formData.required_referrals"
          :min="0"
          :max="9999"
          style="width: 100%"
        />
        <div class="form-tip">达到该等级所需的推荐人数</div>
      </el-form-item>
      
      <el-form-item label="佣金比例" prop="commission_rate">
        <el-input-number
          v-model="formData.commission_rate"
          :min="0"
          :max="100"
          :precision="2"
          :step="0.01"
          style="width: 100%"
        />
        <span class="unit">%</span>
        <div class="form-tip">该等级用户的佣金比例</div>
      </el-form-item>
      
      <el-form-item label="升级奖励" prop="upgrade_bonus">
        <el-input-number
          v-model="formData.upgrade_bonus"
          :min="0"
          :precision="2"
          style="width: 100%"
        />
        <span class="unit">元</span>
        <div class="form-tip">升级到该等级时的一次性奖励</div>
      </el-form-item>
      
      <el-form-item label="月度奖励" prop="monthly_bonus">
        <el-input-number
          v-model="formData.monthly_bonus"
          :min="0"
          :precision="2"
          style="width: 100%"
        />
        <span class="unit">元</span>
        <div class="form-tip">该等级用户每月固定奖励</div>
      </el-form-item>
      
      <el-form-item label="等级图标">
        <div class="icon-selector">
          <div class="current-icon">
            <el-icon :size="40" :color="formData.icon_color || '#409eff'">
              <component :is="formData.icon || 'Star'" />
            </el-icon>
          </div>
          <div class="icon-options">
            <div
              v-for="icon in iconOptions"
              :key="icon.name"
              class="icon-option"
              :class="{ active: formData.icon === icon.name }"
              @click="formData.icon = icon.name"
            >
              <el-icon :size="24">
                <component :is="icon.name" />
              </el-icon>
            </div>
          </div>
        </div>
      </el-form-item>
      
      <el-form-item label="图标颜色">
        <el-color-picker
          v-model="formData.icon_color"
          :predefine="colorPresets"
        />
      </el-form-item>
      
      <el-form-item label="等级描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入等级描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="启用状态">
        <el-switch
          v-model="formData.is_active"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
    </el-form>
    
    <!-- 预览区域 -->
    <div class="preview-section">
      <el-card>
        <template #header>
          <span>等级预览</span>
        </template>
        <div class="level-preview">
          <div class="preview-icon">
            <el-icon :size="60" :color="formData.icon_color || '#409eff'">
              <component :is="formData.icon || 'Star'" />
            </el-icon>
          </div>
          <div class="preview-info">
            <div class="preview-name">{{ formData.name || '等级名称' }}</div>
            <div class="preview-level">等级：{{ formData.level || 1 }}</div>
            <div class="preview-commission">佣金比例：{{ formData.commission_rate || 0 }}%</div>
            <div class="preview-required">所需推荐：{{ formData.required_referrals || 0 }}人</div>
          </div>
        </div>
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { type FormInstance, type FormRules } from 'element-plus';
// import {
//   Star,
//   Trophy,
//   Medal,
//   Coin,
//   Flag
// } from '@element-plus/icons-vue';
import type { ReferralLevel } from '@/modules/admin/api/referral';

// ===================== Props & Emits =====================

interface Props {
  modelValue: boolean;
  data?: ReferralLevel | null;
  loading?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'submit', data: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  data: null,
  loading: false
});

const emit = defineEmits<Emits>();

// ===================== 响应式数据 =====================

const formRef = ref<FormInstance>();
const submitting = ref(false);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isEdit = computed(() => !!props.data?.id);
const title = computed(() => isEdit.value ? '编辑分销等级' : '新增分销等级');

// 表单数据
const formData = reactive<Partial<ReferralLevel> & {
  icon?: string;
  icon_color?: string;
  upgrade_bonus?: number;
  monthly_bonus?: number;
  is_active?: boolean;
}>({
  name: '',
  code: '',
  level: 1,
  required_referrals: 0,
  commission_rate: 0,
  upgrade_bonus: 0,
  monthly_bonus: 0,
  description: '',
  icon: 'Star',
  icon_color: '#409eff',
  is_active: true
});

// 图标选项
const iconOptions = [
  { name: 'Star', label: '星星' },
  { name: 'Trophy', label: '奖杯' },
  { name: 'Crown', label: '皇冠' },
  { name: 'Medal', label: '奖牌' },
  { name: 'Gift', label: '礼物' },
  { name: 'Diamond', label: '钻石' },
  { name: 'Coin', label: '金币' },
  { name: 'Flag', label: '旗帜' }
];

// 颜色预设
const colorPresets = [
  '#409eff',
  '#67c23a',
  '#e6a23c',
  '#f56c6c',
  '#909399',
  '#c71585',
  '#ff6347',
  '#32cd32',
  '#1e90ff',
  '#ff1493'
];

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入等级名称', trigger: 'blur' },
    { min: 2, max: 50, message: '等级名称长度在2-50个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入等级编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请输入等级层级', trigger: 'blur' },
    { type: 'number', min: 1, max: 10, message: '等级层级必须在1-10之间', trigger: 'blur' }
  ],
  required_referrals: [
    { required: true, message: '请输入所需推荐人数', trigger: 'blur' },
    { type: 'number', min: 0, message: '推荐人数不能小于0', trigger: 'blur' }
  ],
  commission_rate: [
    { required: true, message: '请输入佣金比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '佣金比例必须在0-100之间', trigger: 'blur' }
  ]
};

// ===================== 监听器 =====================

watch(
  () => props.data,
  (newData) => {
    if (newData) {
      Object.assign(formData, {
        ...newData,
        commission_rate: (newData.commission_rate || 0) * 100, // 转换为百分比显示
        icon: newData.icon || 'Star',
        icon_color: newData.icon_color || '#409eff',
        upgrade_bonus: newData.upgrade_bonus || 0,
        monthly_bonus: newData.monthly_bonus || 0,
        is_active: newData.is_active !== false
      });
    } else {
      // 重置表单
      Object.assign(formData, {
        name: '',
        code: '',
        level: 1,
        required_referrals: 0,
        commission_rate: 0,
        upgrade_bonus: 0,
        monthly_bonus: 0,
        description: '',
        icon: 'Star',
        icon_color: '#409eff',
        is_active: true
      });
    }
  },
  { immediate: true }
);

// ===================== 方法定义 =====================

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    submitting.value = true;
    
    // 转换数据格式
    const submitData = {
      ...formData,
      commission_rate: formData.commission_rate! / 100 // 转换为小数
    };
    
    emit('submit', submitData);
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    submitting.value = false;
  }
};

/**
 * 关闭对话框
 */
const handleClose = () => {
  visible.value = false;
  // 重置表单验证状态
  formRef.value?.resetFields();
};
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.unit {
  margin-left: 8px;
  color: #909399;
}

.icon-selector {
  display: flex;
  align-items: center;
  gap: 15px;
}

.current-icon {
  padding: 10px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex: 1;
}

.icon-option {
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-option:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.icon-option.active {
  border-color: #409eff;
  background-color: #409eff;
  color: white;
}

.preview-section {
  margin-top: 20px;
}

.level-preview {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.preview-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10px);
}

.preview-info {
  flex: 1;
}

.preview-name {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.preview-level,
.preview-commission,
.preview-required {
  font-size: 14px;
  margin-bottom: 4px;
  opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .icon-selector {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .level-preview {
    flex-direction: column;
    text-align: center;
  }
  
  .preview-name {
    font-size: 20px;
  }
}
</style>