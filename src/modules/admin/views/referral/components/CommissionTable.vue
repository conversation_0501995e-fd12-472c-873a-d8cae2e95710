<!--
  佣金记录表格组件
  作者：张二浩
  日期：2025-01-15
  版本：1.0.0
  描述：用于展示和管理佣金记录的表格组件
-->

<template>
  <div class="commission-table">
    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <el-row :gutter="20" align="middle">
        <el-col :span="12">
          <div class="search-filters">
            <el-input
              v-model="searchParams.keyword"
              placeholder="搜索用户名、手机号或订单号"
              style="width: 250px; margin-right: 10px"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            
            <el-select
              v-model="searchParams.status"
              placeholder="状态"
              style="width: 120px; margin-right: 10px"
              clearable
              @change="handleSearch"
            >
              <el-option label="待审核" value="pending" />
              <el-option label="已发放" value="paid" />
              <el-option label="已拒绝" value="rejected" />
              <el-option label="已冻结" value="frozen" />
            </el-select>
            
            <el-date-picker
              v-model="searchParams.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 240px; margin-right: 10px"
              @change="handleSearch"
            />
            
            <el-button
              type="primary"
              :icon="Search"
              @click="handleSearch"
            >
              搜索
            </el-button>
            
            <el-button
              :icon="Refresh"
              @click="handleReset"
            >
              重置
            </el-button>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="action-buttons">
            <el-button
              type="success"
              :icon="Check"
              @click="handleBatchApprove"
              :disabled="selectedRows.length === 0"
            >
              批量审核通过
            </el-button>
            
            <el-button
              type="danger"
              :icon="Close"
              @click="handleBatchReject"
              :disabled="selectedRows.length === 0"
            >
              批量拒绝
            </el-button>
            
            <el-button
              type="warning"
              :icon="Lock"
              @click="handleBatchFreeze"
              :disabled="selectedRows.length === 0"
            >
              批量冻结
            </el-button>
            
            <el-button
              :icon="Download"
              @click="handleExport"
            >
              导出
            </el-button>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <!-- 统计信息 -->
    <div class="statistics-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic
            title="总佣金金额"
            :value="statistics.total_amount"
            suffix="元"
            :precision="2"
          />
        </el-col>
        <el-col :span="6">
          <el-statistic
            title="已发放金额"
            :value="statistics.paid_amount"
            suffix="元"
            :precision="2"
          />
        </el-col>
        <el-col :span="6">
          <el-statistic
            title="待审核金额"
            :value="statistics.pending_amount"
            suffix="元"
            :precision="2"
          />
        </el-col>
        <el-col :span="6">
          <el-statistic
            title="总记录数"
            :value="statistics.total_count"
            suffix="条"
          />
        </el-col>
      </el-row>
    </div>
    
    <!-- 数据表格 -->
    <el-table
      ref="tableRef"
      :data="tableData"
      v-loading="loading"
      stripe
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <el-table-column type="selection" width="55" fixed="left" />
      
      <el-table-column
        prop="id"
        label="ID"
        width="80"
        sortable="custom"
        fixed="left"
      />
      
      <el-table-column
        prop="user_info"
        label="用户信息"
        width="200"
        fixed="left"
      >
        <template #default="{ row }">
          <div class="user-info">
            <el-avatar :size="32" :src="row.user_avatar" />
            <div class="user-details">
              <div class="username">{{ row.user_name }}</div>
              <div class="phone">{{ row.user_phone }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="order_no"
        label="订单号"
        width="150"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <el-link
            type="primary"
            @click="viewOrder(row.order_id)"
          >
            {{ row.order_no }}
          </el-link>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="commission_type"
        label="佣金类型"
        width="100"
      >
        <template #default="{ row }">
          <el-tag
            :type="getCommissionTypeTag(row.commission_type)"
            size="small"
          >
            {{ getCommissionTypeText(row.commission_type) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="level"
        label="层级"
        width="80"
        align="center"
      >
        <template #default="{ row }">
          <el-tag size="small" type="info">
            {{ row.level }}级
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="commission_rate"
        label="佣金比例"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          {{ (row.commission_rate * 100).toFixed(2) }}%
        </template>
      </el-table-column>
      
      <el-table-column
        prop="order_amount"
        label="订单金额"
        width="120"
        align="right"
        sortable="custom"
      >
        <template #default="{ row }">
          <span class="amount">¥{{ row.order_amount.toFixed(2) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="commission_amount"
        label="佣金金额"
        width="120"
        align="right"
        sortable="custom"
      >
        <template #default="{ row }">
          <span class="commission-amount">
            ¥{{ row.commission_amount.toFixed(2) }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="status"
        label="状态"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag
            :type="getStatusTag(row.status)"
            size="small"
          >
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="created_at"
        label="创建时间"
        width="160"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.created_at) }}
        </template>
      </el-table-column>
      
      <el-table-column
        prop="processed_at"
        label="处理时间"
        width="160"
      >
        <template #default="{ row }">
          {{ row.processed_at ? formatDateTime(row.processed_at) : '-' }}
        </template>
      </el-table-column>
      
      <el-table-column
        prop="processor"
        label="处理人"
        width="100"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ row.processor || '-' }}
        </template>
      </el-table-column>
      
      <el-table-column
        label="操作"
        width="200"
        fixed="right"
      >
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="small"
              @click="viewDetails(row)"
            >
              详情
            </el-button>
            
            <el-button
              v-if="row.status === 'pending'"
              type="success"
              size="small"
              @click="approveCommission(row)"
            >
              审核
            </el-button>
            
            <el-button
              v-if="row.status === 'paid'"
              type="warning"
              size="small"
              @click="freezeCommission(row)"
            >
              冻结
            </el-button>
            
            <el-dropdown
              v-if="row.status !== 'rejected'"
              @command="(command: string) => handleDropdownAction(command, row)"
            >
              <el-button size="small">
                更多
                <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="history">历史记录</el-dropdown-item>
                  <el-dropdown-item command="export" divided>导出记录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox, type TableInstance } from 'element-plus';
import {
  Search,
  Refresh,
  Check,
  Close,
  Lock,
  Download,
  ArrowDown
} from '@element-plus/icons-vue';
import type { CommissionRecord } from '@/modules/admin/api/referral';
import {
  getCommissionRecords,
  approveCommissionRecord,
  freezeCommissionRecord,
  batchApproveCommissions,
  batchRejectCommissions,
  batchFreezeCommissions,
  exportCommissionRecords
} from '@/modules/admin/api/referral';

// ===================== Props & Emits =====================

interface Props {
  loading?: boolean;
}

interface Emits {
  (e: 'view-details', record: CommissionRecord): void;
  (e: 'view-order', orderId: number): void;
  (e: 'edit-record', record: CommissionRecord): void;
  (e: 'view-history', record: CommissionRecord): void;
  (e: 'refresh'): void;
}

withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// ===================== 响应式数据 =====================

const tableRef = ref<TableInstance>();
const tableData = ref<CommissionRecord[]>([]);
const selectedRows = ref<CommissionRecord[]>([]);

// 搜索参数
const searchParams = reactive({
  keyword: '',
  status: '',
  dateRange: null as [string, string] | null,
  sort_field: 'created_at',
  sort_order: 'desc'
});

// 分页参数
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
});

// 统计数据
const statistics = reactive({
  total_amount: 0,
  paid_amount: 0,
  pending_amount: 0,
  total_count: 0
});

// ===================== 计算属性 =====================

// const hasSelection = computed(() => selectedRows.value.length > 0);

// ===================== 生命周期 =====================

onMounted(() => {
  loadTableData();
  loadStatistics();
});

// ===================== 方法定义 =====================

/**
 * 加载表格数据
 */
const loadTableData = async () => {
  try {
    const params = {
      ...searchParams,
      page: pagination.page,
      size: pagination.size,
      start_date: searchParams.dateRange?.[0],
      end_date: searchParams.dateRange?.[1]
    };
    
    const response = await getCommissionRecords(params);
    
    tableData.value = response.items || [];
    pagination.total = response.total || 0;
  } catch (error) {
    console.error('加载佣金记录失败:', error);
    ElMessage.error('加载佣金记录失败');
  }
};

/**
 * 加载统计数据
 */
const loadStatistics = async () => {
  try {
    // const params = {
    //   keyword: searchParams.keyword,
    //   status: searchParams.status,
    //   start_date: searchParams.dateRange?.[0],
    //   end_date: searchParams.dateRange?.[1]
    // };
    
    // 这里应该调用统计API，暂时使用模拟数据
    statistics.total_amount = 125680.50;
    statistics.paid_amount = 98450.30;
    statistics.pending_amount = 27230.20;
    statistics.total_count = pagination.total;
  } catch (error) {
    console.error('加载统计数据失败:', error);
  }
};

/**
 * 获取佣金类型标签
 */
const getCommissionTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    order: 'success',
    referral: 'primary',
    bonus: 'warning'
  };
  return tagMap[type] || 'info';
};

/**
 * 获取佣金类型文本
 */
const getCommissionTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    order: '订单佣金',
    referral: '推荐佣金',
    bonus: '奖励佣金'
  };
  return textMap[type] || type;
};

/**
 * 获取状态标签
 */
const getStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    pending: 'warning',
    paid: 'success',
    rejected: 'danger',
    frozen: 'info'
  };
  return tagMap[status] || 'info';
};

/**
 * 获取状态文本
 */
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '待审核',
    paid: '已发放',
    rejected: '已拒绝',
    frozen: '已冻结'
  };
  return textMap[status] || status;
};

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.page = 1;
  loadTableData();
  loadStatistics();
};

/**
 * 处理重置
 */
const handleReset = () => {
  Object.assign(searchParams, {
    keyword: '',
    status: '',
    dateRange: null,
    sort_field: 'created_at',
    sort_order: 'desc'
  });
  pagination.page = 1;
  loadTableData();
  loadStatistics();
};

/**
 * 处理选择变化
 */
const handleSelectionChange = (selection: CommissionRecord[]) => {
  selectedRows.value = selection;
};

/**
 * 处理排序变化
 */
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  searchParams.sort_field = prop;
  searchParams.sort_order = order === 'ascending' ? 'asc' : 'desc';
  loadTableData();
};

/**
 * 处理页面大小变化
 */
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.page = 1;
  loadTableData();
};

/**
 * 处理当前页变化
 */
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  loadTableData();
};

/**
 * 查看详情
 */
const viewDetails = (record: CommissionRecord) => {
  emit('view-details', record);
};

/**
 * 查看订单
 */
const viewOrder = (orderId: number) => {
  emit('view-order', orderId);
};

/**
 * 审核佣金
 */
const approveCommission = async (record: CommissionRecord) => {
  try {
    await ElMessageBox.confirm(
      `确认审核通过佣金记录 ${record.id}？`,
      '确认审核',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await approveCommissionRecord(record.id, {
      remark: '管理员审核通过'
    });
    
    ElMessage.success('审核成功');
    loadTableData();
    loadStatistics();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核失败:', error);
      ElMessage.error('审核失败');
    }
  }
};

/**
 * 冻结佣金
 */
const freezeCommission = async (record: CommissionRecord) => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入冻结原因',
      '冻结佣金',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '请输入冻结原因'
      }
    );
    
    await freezeCommissionRecord(record.id, {
      remark: reason
    });
    
    ElMessage.success('冻结成功');
    loadTableData();
    loadStatistics();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('冻结失败:', error);
      ElMessage.error('冻结失败');
    }
  }
};

/**
 * 批量审核通过
 */
const handleBatchApprove = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要审核的记录');
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      `确认审核通过选中的 ${selectedRows.value.length} 条记录？`,
      '批量审核',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    const ids = selectedRows.value.map(row => row.id);
    await batchApproveCommissions({
      ids: ids,
      remark: '批量审核通过'
    });
    
    ElMessage.success('批量审核成功');
    selectedRows.value = [];
    loadTableData();
    loadStatistics();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量审核失败:', error);
      ElMessage.error('批量审核失败');
    }
  }
};

/**
 * 批量拒绝
 */
const handleBatchReject = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要拒绝的记录');
    return;
  }
  
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入拒绝原因',
      '批量拒绝',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '请输入拒绝原因'
      }
    );
    
    const ids = selectedRows.value.map(row => row.id);
    await batchRejectCommissions({
      ids: ids,
      remark: reason
    });
    
    ElMessage.success('批量拒绝成功');
    selectedRows.value = [];
    loadTableData();
    loadStatistics();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量拒绝失败:', error);
      ElMessage.error('批量拒绝失败');
    }
  }
};

/**
 * 批量冻结
 */
const handleBatchFreeze = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要冻结的记录');
    return;
  }
  
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入冻结原因',
      '批量冻结',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '请输入冻结原因'
      }
    );
    
    const ids = selectedRows.value.map(row => row.id);
    await batchFreezeCommissions({
      ids: ids,
      remark: reason
    });
    
    ElMessage.success('批量冻结成功');
    selectedRows.value = [];
    loadTableData();
    loadStatistics();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量冻结失败:', error);
      ElMessage.error('批量冻结失败');
    }
  }
};

/**
 * 导出数据
 */
const handleExport = async () => {
  try {
    const params = {
      ...searchParams,
      start_date: searchParams.dateRange?.[0],
      end_date: searchParams.dateRange?.[1],
      format: 'excel'
    };
    
    await exportCommissionRecords(params);
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  }
};

/**
 * 处理下拉菜单操作
 */
const handleDropdownAction = (command: string, record: CommissionRecord) => {
  switch (command) {
    case 'edit':
      emit('edit-record', record);
      break;
    case 'history':
      emit('view-history', record);
      break;
    case 'export':
      exportSingleRecord();
      break;
  }
};

/**
 * 导出单条记录
 */
const exportSingleRecord = async () => {
  try {
    await exportCommissionRecords({
      format: 'excel'
    });
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  }
};

// 暴露方法给父组件
defineExpose({
  refresh: loadTableData,
  clearSelection: () => {
    tableRef.value?.clearSelection();
  }
});
</script>

<style scoped>
.commission-table {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.table-toolbar {
  margin-bottom: 20px;
}

.search-filters {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  flex-wrap: wrap;
}

.statistics-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-details {
  flex: 1;
}

.username {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.phone {
  font-size: 12px;
  color: #909399;
}

.amount {
  font-weight: 600;
  color: #303133;
}

.commission-amount {
  font-weight: 600;
  color: #67c23a;
}

.action-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-filters {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .action-buttons {
    justify-content: flex-start;
    margin-top: 10px;
  }
}

@media (max-width: 768px) {
  .commission-table {
    padding: 10px;
  }
  
  .table-toolbar {
    margin-bottom: 15px;
  }
  
  .statistics-bar {
    padding: 15px;
  }
}
</style>