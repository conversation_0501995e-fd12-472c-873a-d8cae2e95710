<!--
  分销关系管理页面
  作者：张二浩
  日期：2025-01-15
  版本：1.0.0
  描述：管理用户之间的分销关系，包括查看、创建、删除分销关系
-->

<template>
  <div class="referral-relationships-view">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>分销关系管理</span>
          <div class="action-box">
            <el-button type="primary" @click="handleCreate">
              <el-icon><Plus /></el-icon> 新增关系
            </el-button>
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon> 刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-box">
        <el-form :model="searchForm" inline>
          <el-form-item label="推荐人ID">
            <el-input
              v-model="searchForm.referrer_id"
              placeholder="请输入推荐人ID"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="被推荐人ID">
            <el-input
              v-model="searchForm.referee_id"
              placeholder="请输入被推荐人ID"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="关系状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <el-option label="有效" value="active" />
              <el-option label="无效" value="inactive" />
              <el-option label="已删除" value="deleted" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon> 搜索
            </el-button>
            <el-button @click="handleResetSearch">
              <el-icon><Refresh /></el-icon> 重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="关系ID" width="80" />
        <el-table-column prop="referrer_id" label="推荐人ID" width="120" />
        <el-table-column prop="referrer_name" label="推荐人" width="150">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="30" :src="row.referrer_avatar" />
              <span class="user-name">{{ row.referrer_name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="referee_id" label="被推荐人ID" width="120" />
        <el-table-column prop="referee_name" label="被推荐人" width="150">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="30" :src="row.referee_avatar" />
              <span class="user-name">{{ row.referee_name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="分销层级" width="100">
          <template #default="{ row }">
            <el-tag :type="getLevelTagType(row.level)">{{ row.level }}级</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">{{ getStatusText(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="commission_earned" label="累计佣金" width="120">
          <template #default="{ row }">
            <span class="amount">¥{{ row.commission_earned || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              :disabled="row.status === 'deleted'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-box">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="推荐人ID" prop="referrer_id">
          <el-input
            v-model="formData.referrer_id"
            placeholder="请输入推荐人用户ID"
          />
        </el-form-item>
        <el-form-item label="被推荐人ID" prop="referee_id">
          <el-input
            v-model="formData.referee_id"
            placeholder="请输入被推荐人用户ID"
          />
        </el-form-item>
        <el-form-item label="分销层级" prop="level">
          <el-input-number
            v-model="formData.level"
            :min="1"
            :max="10"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="分销关系详情"
      width="800px"
    >
      <div v-if="currentDetail" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="关系ID">{{ currentDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="分销层级">{{ currentDetail.level }}级</el-descriptions-item>
          <el-descriptions-item label="推荐人ID">{{ currentDetail.referrer_id }}</el-descriptions-item>
          <el-descriptions-item label="推荐人姓名">{{ currentDetail.referrer_name }}</el-descriptions-item>
          <el-descriptions-item label="被推荐人ID">{{ currentDetail.referee_id }}</el-descriptions-item>
          <el-descriptions-item label="被推荐人姓名">{{ currentDetail.referee_name }}</el-descriptions-item>
          <el-descriptions-item label="关系状态">
            <el-tag :type="getStatusTagType(currentDetail.status)">{{ getStatusText(currentDetail.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="累计佣金">¥{{ currentDetail.commission_earned || 0 }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(currentDetail.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(currentDetail.updated_at) }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="currentDetail.remark" class="remark-section">
          <h4>备注信息</h4>
          <p>{{ currentDetail.remark }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';
import { Plus, Refresh, Search } from '@element-plus/icons-vue';
import {
  getReferralRelationships,
  createReferralRelationship,
  deleteReferralRelationship,
  type ReferralRelationship
} from '@/modules/admin/api/referral';

// ===================== 响应式数据 =====================

const loading = ref(false);
const submitting = ref(false);
const dialogVisible = ref(false);
const detailDialogVisible = ref(false);
const dialogTitle = ref('新增分销关系');
const formRef = ref<FormInstance>();

const tableData = ref<ReferralRelationship[]>([]);
const currentDetail = ref<ReferralRelationship | null>(null);

// 搜索表单
const searchForm = reactive({
  referrer_id: '',
  referee_id: '',
  status: ''
});

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
});

// 表单数据
const formData = reactive({
  referrer_id: '',
  referee_id: '',
  level: 1,
  remark: ''
});

// 表单验证规则
const formRules: FormRules = {
  referrer_id: [
    { required: true, message: '请输入推荐人ID', trigger: 'blur' }
  ],
  referee_id: [
    { required: true, message: '请输入被推荐人ID', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择分销层级', trigger: 'blur' }
  ]
};

// ===================== 生命周期 =====================

onMounted(() => {
  loadData();
});

// ===================== 方法定义 =====================

/**
 * 加载数据
 */
const loadData = async () => {
  try {
    loading.value = true;
    const params: any = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    };
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '') {
        delete params[key];
      }
    });
    
    const response: any = await getReferralRelationships(params);
    
    if (response) {
      tableData.value = response.items || [];
      pagination.total = response.total || 0;
    }
  } catch (error: any) {
    console.error('加载分销关系失败:', error);
    ElMessage.error(`加载数据失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

/**
 * 搜索
 */
const handleSearch = () => {
  pagination.page = 1;
  loadData();
};

/**
 * 重置搜索
 */
const handleResetSearch = () => {
  Object.assign(searchForm, {
    referrer_id: '',
    referee_id: '',
    status: ''
  });
  pagination.page = 1;
  loadData();
};

/**
 * 刷新数据
 */
const handleRefresh = () => {
  loadData();
};

/**
 * 新增关系
 */
const handleCreate = () => {
  dialogTitle.value = '新增分销关系';
  Object.assign(formData, {
    referrer_id: '',
    referee_id: '',
    level: 1,
    remark: ''
  });
  dialogVisible.value = true;
};

/**
 * 查看详情
 */
const handleView = (row: ReferralRelationship) => {
  currentDetail.value = row;
  detailDialogVisible.value = true;
};

/**
 * 删除关系
 */
const handleDelete = async (row: ReferralRelationship) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除关系ID为 ${row.id} 的分销关系吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await deleteReferralRelationship(row.id);
    ElMessage.success('删除成功');
    loadData();
  } catch (error: any) {
    if (error.message) {
      console.error('删除分销关系失败:', error);
      ElMessage.error(`删除失败: ${error.message}`);
    }
  }
};

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    submitting.value = true;
    
    await createReferralRelationship({
      referrer_id: Number(formData.referrer_id),
      referee_id: Number(formData.referee_id)
    });
    ElMessage.success('创建成功');
    
    dialogVisible.value = false;
    loadData();
  } catch (error: any) {
    if (error.message) {
      console.error('创建分销关系失败:', error);
      ElMessage.error(`创建失败: ${error.message}`);
    }
  } finally {
    submitting.value = false;
  }
};

/**
 * 分页大小改变
 */
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.page = 1;
  loadData();
};

/**
 * 当前页改变
 */
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  loadData();
};

/**
 * 获取层级标签类型
 */
const getLevelTagType = (level: number) => {
  if (level === 1) return 'success';
  if (level === 2) return 'warning';
  if (level === 3) return 'info';
  return 'default';
};

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'active': return 'success';
    case 'inactive': return 'warning';
    case 'deleted': return 'danger';
    default: return 'default';
  }
};

/**
 * 获取状态文本
 */
const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '有效';
    case 'inactive': return '无效';
    case 'deleted': return '已删除';
    default: return '未知';
  }
};

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};
</script>

<style scoped>
.referral-relationships-view {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-box {
  display: flex;
  gap: 10px;
}

.search-box {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-size: 14px;
}

.amount {
  color: #f56c6c;
  font-weight: bold;
}

.pagination-box {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.detail-content {
  padding: 20px 0;
}

.remark-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.remark-section h4 {
  margin: 0 0 10px 0;
  color: #606266;
}

.remark-section p {
  margin: 0;
  line-height: 1.6;
}
</style>