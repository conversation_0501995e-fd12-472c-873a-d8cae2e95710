/**
 * 商家详情页面组件
 * 重构后的组件，采用小组件模式提高可维护性
 */
<template>
  <div class="merchant-detail-view">
    <el-card class="box-card">
      <template #header>
        <MerchantHeader 
          :merchantInfo="merchantInfo" 
          @go-back="goBack"
          @audit="handleAudit"
          @status-change="handleStatusChange"
        />
      </template>
      
      <div v-loading="loading" class="detail-content">
        <!-- 基本信息卡片 -->
        <MerchantBasicInfo 
          :merchantInfo="merchantInfo"
          @edit-merchant="handleEditMerchant"
          @reset-password="handleResetPassword"
        />
        
        <!-- 商品统计 -->
        <MerchantFoodStats 
          :foodStats="foodStats"
          @open-foods-drawer="openFoodsDrawer"
        />
        
        <!-- 订单统计 -->
        <MerchantOrderStats 
          :orderStats="orderStats"
        />
      </div>
    </el-card>
    
    <!-- 审核对话框 -->
    <MerchantAuditDialog
      v-model="auditDialogVisible"
      :auditType="auditForm.audit_status"
      @submit="submitAudit"
    />
    
    <!-- 商品列表抽屉 -->
    <MerchantFoodsDrawer
      v-model="foodsDrawerVisible"
      :foodsList="foodsList"
      :loading="foodsLoading"
      @view-food="goToFoodDetail"
    />
    
    <!-- 编辑商家信息对话框 -->
    <MerchantEditDialog
      v-model="editDialogVisible"
      :merchantInfo="merchantInfo"
      @submit="submitEditMerchant"
    />
    
    <!-- 重置密码对话框 -->
    <MerchantPasswordDialog
      v-model="passwordDialogVisible"
      :username="merchantInfo.username"
      @submit="submitResetPassword"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  MerchantBasicInfo,
  MerchantFoodStats,
  MerchantOrderStats,
  MerchantAuditDialog,
  MerchantFoodsDrawer,
  MerchantEditDialog,
  MerchantPasswordDialog,
  MerchantHeader
} from './components';
import { 
  getMerchantDetail, 
  getAdminMerchantStatistics, 
  updateMerchantAudit, 
  getMerchantFoods, 
  updateMerchantOperationStatus,
  updateMerchant, 
  updateMerchantPassword 
} from '@/modules/admin/api/merchant';

const route = useRoute();
const router = useRouter();

// 获取路由参数中的商家ID
const merchantId = ref<number>(Number(route.params.id));

// 商家详情数据
const merchantInfo = ref({
  id: 0,
  name: '',
  username: '',
  logo: '',
  business_license: '',
  contact_name: '',
  contact_mobile: '',
  contact_email: '',
  address: '',
  longitude: 0,
  latitude: 0,
  level: 1,
  operation_status: 0,
  audit_status: 0,
  description: '',
  reject_reason: '',
  audit_remark: '',
  created_at: '',
  updated_at: ''
});

// 加载状态
const loading = ref(false);

/**
 * 返回列表页
 */
const goBack = () => {
  router.push('/admin/merchants');
};

/**
 * 加载商家详情
 */
const fetchMerchantDetail = async () => {
  loading.value = true;
  try {
    // utils/request.ts 响应拦截器已经处理了响应，直接获取data
    const data = await getMerchantDetail(merchantId.value);
    // 确保类型安全的赋值
    if (data) {
      merchantInfo.value = {
        ...merchantInfo.value,
        ...data
      };
    }
  } catch (error) {
    console.error('获取商家详情失败:', error);
    ElMessage.error('获取商家详情失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 定义类型来解决类型推断问题
interface FoodStats {
  on_sale_count: number;
  pending_count: number;
  sold_out_count: number;
  total_count: number;
}

interface OrderStats {
  total_count: number;
  completed_count: number;
  processing_count: number;
  cancelled_count: number;
}

// 商品统计数据
const foodStats = ref<FoodStats>({
  on_sale_count: 0,
  pending_count: 0,
  sold_out_count: 0,
  total_count: 0
});

// 订单统计数据
const orderStats = ref<OrderStats>({
  total_count: 0,
  completed_count: 0,
  processing_count: 0,
  cancelled_count: 0
});

/**
 * 获取商家统计信息
 */
const fetchMerchantStatistics = async () => {
  try {
    // 使用getAdminMerchantStatistics函数获取商家统计数据
    // 该函数只需要一个参数：merchantId
    const data: any = await getAdminMerchantStatistics<any>(merchantId.value);
    // 使用any类型解决类型推断问题
    const foods = (data && data.foods) || {};
    const orders = (data && data.orders) || {};
    
    // 更新商品统计
    foodStats.value = {
      on_sale_count: foods.on_sale_count || 0,
      pending_count: foods.pending_count || 0,
      sold_out_count: foods.sold_out_count || 0,
      total_count: foods.total_count || 0
    };
    
    // 更新订单统计
    orderStats.value = {
      total_count: orders.total_count || 0,
      completed_count: orders.completed_count || 0,
      processing_count: orders.processing_count || 0,
      cancelled_count: orders.cancelled_count || 0
    };
  } catch (error) {
    console.error('获取商家统计信息失败:', error);
    ElMessage.error('获取商家统计信息失败，请稍后重试');
  }
};

// 审核对话框相关
const auditDialogVisible = ref(false);
const auditForm = ref({
  audit_status: '',
  reason: ''
});

/**
 * 处理审核
 * @param status 审核状态 APPROVED|REJECTED
 */
const handleAudit = (status: 'APPROVED' | 'REJECTED') => {
  auditForm.value.audit_status = status;
  auditDialogVisible.value = true;
};

/**
 * 提交审核
 * @param data 审核表单数据
 */
const submitAudit = async (data: any) => {
  try {
    const status = data.audit_status === 'APPROVED' ? 1 : 2; // 1-通过 2-拒绝
    // 根据API接口调整参数，使用merchantId和audit_status
    await updateMerchantAudit(merchantId.value, {
      audit_status: status,
      reason: data.reason
    });
    
    ElMessage.success(data.audit_status === 'APPROVED' ? '审核通过成功' : '审核拒绝成功');
    
    // 关闭对话框
    auditDialogVisible.value = false;
    
    // 刷新商家数据
    fetchMerchantDetail();
  } catch (error) {
    console.error('审核操作失败:', error);
    ElMessage.error('审核操作失败，请稍后重试');
  }
};

/**
 * 处理状态变更
 * @param status 状态值
 */
const handleStatusChange = (status: number) => {
  const statusText = status === 1 ? '营业中' : '休息中';
  
  ElMessageBox.confirm(
    `确定将商家状态设置为【${statusText}】吗？`,
    '状态变更确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 使用正确的API函数名称和参数结构
      await updateMerchantOperationStatus(merchantId.value, { operation_status: status });
      
      ElMessage.success(`状态已成功更新为【${statusText}】`);
      // 刷新商家数据
      fetchMerchantDetail();
    } catch (error) {
      console.error('更新状态失败:', error);
      ElMessage.error('状态更新失败，请稍后重试');
    }
  }).catch(() => {
    // 用户取消操作
  });
};

// 商品列表抽屉相关
const foodsDrawerVisible = ref(false);
const foodsList = ref([]);
const foodsLoading = ref(false);

/**
 * 打开商品列表抽屉
 */
const openFoodsDrawer = () => {
  foodsDrawerVisible.value = true;
  fetchMerchantFoods();
};

/**
 * 获取商家商品列表
 */
const fetchMerchantFoods = async () => {
  foodsLoading.value = true;
  try {
    // 直接传递商家ID参数
    const data: any = await getMerchantFoods(merchantId.value);
    // 使用any类型处理响应数据，防止类型错误
    if (data && data.list && Array.isArray(data.list)) {
      foodsList.value = data.list;
    } else {
      foodsList.value = [];
    }
  } catch (error) {
    console.error('获取商品列表失败:', error);
    ElMessage.error('获取商品列表失败，请稍后重试');
    foodsList.value = [];
  } finally {
    foodsLoading.value = false;
  }
};

/**
 * 跳转到商品详情页
 * @param foodId 商品ID
 */
const goToFoodDetail = (foodId: number) => {
  router.push(`/admin/takeout/food/detail/${foodId}`);
};

// 商家编辑对话框相关
const editDialogVisible = ref(false);

/**
 * 打开编辑商家信息弹窗
 */
const handleEditMerchant = () => {
  editDialogVisible.value = true;
};

/**
 * 提交编辑商家信息
 * @param data 编辑表单数据
 */
const submitEditMerchant = async (data: any) => {
  try {
    await updateMerchant(merchantId.value, data);
    ElMessage.success('商家信息更新成功');
    editDialogVisible.value = false;
    // 刷新商家数据
    fetchMerchantDetail();
  } catch (error) {
    console.error('更新商家信息失败:', error);
    ElMessage.error('更新商家信息失败，请稍后重试');
  }
};

// 重置密码对话框相关
const passwordDialogVisible = ref(false);

/**
 * 打开重置密码弹窗
 */
const handleResetPassword = () => {
  passwordDialogVisible.value = true;
};

/**
 * 提交重置密码
 * @param data 密码表单数据
 */
const submitResetPassword = async (data: any) => {
  try {
    await updateMerchantPassword(merchantId.value, {
      new_password: data.new_password,
      confirm_password: data.confirm_password
    });
    
    ElMessage.success('密码重置成功');
    passwordDialogVisible.value = false;
  } catch (error) {
    console.error('密码重置失败:', error);
    ElMessage.error('密码重置失败，请稍后重试');
  }
};

onMounted(() => {
  fetchMerchantDetail();
  fetchMerchantStatistics();
});
</script>

<style lang="scss" scoped>
.merchant-detail-view {
  padding: 20px;
  
  .box-card {
    margin-bottom: 20px;
  }
  
  .detail-content {
    padding: 10px 0;
  }
}
</style>
