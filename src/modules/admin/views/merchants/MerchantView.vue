<template>
  <div class="merchant-view">
    <!-- 地图卡片 -->
    <el-card class="map-card" v-if="showMap">
      <template #header>
        <div class="card-header">
          <span>商家地图分布</span>
          <div class="action-box">
            <el-button @click="toggleMap" size="small">
              <el-icon><Hide /></el-icon> 隐藏地图
            </el-button>
            <el-button @click="fitMapToMarkers" size="small" type="primary">
              <el-icon><Aim /></el-icon> 适应视图
            </el-button>
          </div>
        </div>
      </template>
      <div class="map-container">
        <TiandituMap
          ref="mapRef"
          :api-key="tiandituApiKey"
          :center="mapCenter"
          :zoom="mapZoom"
          :markers="mapMarkers"
          :height="'400px'"
          @marker-click="handleMarkerClick"
          @map-click="handleMapClick"
        />
      </div>
    </el-card>

    <el-card class="box-card">
      <template #header>
        <!-- 搜索过滤器区域 -->
      <div class="filter-container">
        <div class="filter-layout">
          <!-- 左侧操作按钮区域 -->
          <div class="filter-actions">
            <el-button @click="toggleMap" size="small" :type="showMap ? 'default' : 'primary'" circle>
              <el-icon><Location /></el-icon>
            </el-button>
            <el-button type="primary" @click="handleAddMerchant">
              <el-icon><Plus /></el-icon> 增加商家
            </el-button>
          </div>
          
          <!-- 右侧搜索区域 -->
          <div class="filter-search">
            <el-form :inline="true" :model="filterForm" class="filter-form">
              <!-- 简单搜索字段 -->
              <el-form-item label="商家名称">
                <el-input
                  v-model="filterForm.name"
                  placeholder="请输入商家名称"
                  clearable
                  @keyup.enter="handleSearch"
                  class="filter-input"
                />
              </el-form-item>
              
              <el-form-item label="联系电话">
                <el-input
                  v-model="filterForm.contactMobile"
                  placeholder="请输入联系电话"
                  clearable
                  @keyup.enter="handleSearch"
                  class="filter-input"
                />
              </el-form-item>
              
              <!-- 高级搜索字段 -->
              <template v-if="isAdvancedSearch">
                <el-form-item label="联系人">
                  <el-input
                    v-model="filterForm.contactName"
                    placeholder="请输入联系人"
                    clearable
                    @keyup.enter="handleSearch"
                    class="filter-input"
                  />
                </el-form-item>
                
                <el-form-item label="推荐状态">
                  <el-select
                    v-model="filterForm.isRecommended"
                    placeholder="请选择推荐状态"
                    clearable
                    class="filter-select"
                  >
                    <el-option label="全部" value="" />
                    <el-option label="推荐" :value="1" />
                    <el-option label="普通" :value="0" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="审核状态">
                  <el-select
                    v-model="filterForm.auditStatus"
                    placeholder="请选择审核状态"
                    clearable
                    class="filter-select"
                  >
                    <el-option label="全部" value="" />
                    <el-option label="待审核" value="PENDING" />
                    <el-option label="已通过" value="APPROVED" />
                    <el-option label="已拒绝" value="REJECTED" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="商家状态">
                  <el-select
                    v-model="filterForm.status"
                    placeholder="请选择商家状态"
                    clearable
                    class="filter-select"
                  >
                    <el-option label="全部" value="" />
                    <el-option label="正常" value="ACTIVE" />
                    <el-option label="暂停" value="SUSPENDED" />
                    <el-option label="关闭" value="CLOSED" />
                  </el-select>
                </el-form-item>
              </template>
              
              <el-form-item>
                <el-button type="primary" @click="handleSearch">
                  <el-icon><Search /></el-icon> 搜索
                </el-button>
                <el-button @click="handleReset">
                  重置
                </el-button>
                <el-button 
                  size="small" 
                  :type="isAdvancedSearch ? 'primary' : 'default'"
                  @click="toggleSearchMode"
                >
                  {{ isAdvancedSearch ? '简单搜索' : '高级搜索' }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      </template>
      
      
      
      <el-table
        v-loading="loading"
        :data="merchantList"
        style="width: 100%"
        border
        stripe
        :max-height="tableMaxHeight"
        highlight-current-row
        table-layout="auto"
        class="merchant-table"
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="name" label="商家名称" min-width="150" show-overflow-tooltip />
        <el-table-column label="Logo" width="100" align="center">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.logo">
              <el-icon><Shop /></el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column prop="contact_name" label="联系人" width="100" show-overflow-tooltip />
        <el-table-column prop="contact_mobile" label="联系电话" width="120" show-overflow-tooltip />
        <el-table-column prop="contact_email" label="联系邮箱" width="180" show-overflow-tooltip />
        <el-table-column label="推荐状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.is_recommended === 1 ? 'success' : 'info'">
              {{ row.is_recommended === 1 ? '推荐' : '普通' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="STATUS_TAG_CONFIG.merchantStatus[row.status as keyof typeof MerchantStatus].type">
              {{ STATUS_TAG_CONFIG.merchantStatus[row.status as keyof typeof MerchantStatus].label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="审核状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="STATUS_TAG_CONFIG.merchantAuditStatus[row.audit_status as keyof typeof MerchantAuditStatus].type">
              {{ STATUS_TAG_CONFIG.merchantAuditStatus[row.audit_status as keyof typeof MerchantAuditStatus].label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="270" show-overflow-tooltip>
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <!-- 审核操作 -->
            <el-button
              v-if="row.audit_status === 'PENDING'"
              type="success"
              size="small"
              @click="handleAudit(row, 'APPROVED')"
            >
              通过
            </el-button>
            <el-button
              v-if="row.audit_status === 'PENDING'"
              type="danger"
              size="small"
              @click="handleAudit(row, 'REJECTED')"
            >
              拒绝
            </el-button>
            
            <!-- 状态操作 -->
            <el-button
              v-if="row.audit_status === 'APPROVED' && row.status === 'ACTIVE'"
              type="warning"
              size="small"
              @click="handleStatusChange(row.id, 'SUSPENDED')"
            >
              暂停
            </el-button>
            <el-button
              v-if="row.audit_status === 'APPROVED' && row.status === 'SUSPENDED'"
              type="success"
              size="small"
              @click="handleStatusChange(row.id, 'ACTIVE')"
            >
              恢复
            </el-button>
            <el-button
              v-if="row.audit_status === 'APPROVED' && row.status !== 'CLOSED'"
              type="danger"
              size="small"
              @click="handleStatusChange(row.id, 'CLOSED')"
            >
              关闭
            </el-button>
            <el-button
              v-if="row.audit_status === 'APPROVED' && row.status === 'CLOSED'"
              type="success"
              size="small"
              @click="handleStatusChange(row.id, 'ACTIVE')"
            >
              开启
            </el-button>
            
            <!-- 推荐操作 -->
            <el-button
              v-if="row.is_recommended !== 1"
              type="warning"
              size="small"
              @click="handleRecommendChange(row.id, 1)"
            >
              推荐
            </el-button>
            <el-button
              v-if="row.is_recommended === 1"
              type="info"
              size="small"
              @click="handleRecommendChange(row.id, 0)"
            >
              取消推荐
            </el-button>
            
            <!-- 查看详情 -->
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 审核对话框 -->
    <el-dialog
      v-model="auditDialogVisible"
      :title="auditDialogTitle"
      width="500px"
      destroy-on-close
    >
      <el-form :model="auditForm" label-width="80px">
        <el-form-item v-if="auditForm.audit_status === 'REJECTED'" label="拒绝原因" required>
          <el-input
            v-model="auditForm.reason"
            type="textarea"
            rows="4"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAudit">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 商家详情对话框已移除，改为详情页面 -->

    <!-- 新增商家对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="新增商家"
      width="500px"
      destroy-on-close
    >
      <el-form :model="addForm" label-width="100px">
        <el-form-item label="登陆名" prop="username">
          <el-input v-model="addForm.username" />
        </el-form-item>
        <el-form-item label="商家名称" prop="name">
          <el-input v-model="addForm.name" />
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="addForm.contactPerson" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactMobile">
          <el-input v-model="addForm.contactMobile" />
        </el-form-item>
        <el-form-item label="联系邮箱" prop="contactEmail">
          <el-input v-model="addForm.contactEmail" />
        </el-form-item>
        <el-form-item label="商家地址" prop="address">
          <el-input v-model="addForm.address" />
        </el-form-item>
        <el-form-item label="商家描述" prop="description">
          <el-input v-model="addForm.description" type="textarea" />
        </el-form-item>
        <el-form-item label="推荐状态" prop="isRecommended">
          <el-select v-model="addForm.isRecommended" placeholder="请选择推荐状态">
            <el-option label="普通" :value="0" />
            <el-option label="推荐" :value="1" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAddMerchant">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// 确保组件正确注册
import * as TiandituMapComponents from '@/components/map';
import { calculateCenter } from '@/components/map/utils';

export default {
  components: {
    TiandituMap: TiandituMapComponents.TiandituMap
  }
};
</script>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { MerchantStatus, MerchantAuditStatus } from '../../types';
import { Search, Shop, Plus, Location, Hide, Aim } from '@element-plus/icons-vue';
import { MerchantService } from './merchantService';
import { STATUS_TAG_CONFIG } from '../../constants';
import { formatTime } from '@/utils/format';
import type { Merchant } from '../../types';
// 只导入类型，组件在外部script中注册
import type { MarkerData } from '@/components/map/types';

const merchantList = ref<Merchant[]>([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchKeyword = ref('');
const audit_status = ref('');
const tableMaxHeight = ref(500);

// 过滤器表单数据
const filterForm = reactive({
  name: '',
  contactName: '',
  contactMobile: '',
  isRecommended: '',
  auditStatus: '',
  status: ''
});

// 搜索模式状态
const isAdvancedSearch = ref(false);

// 地图相关
const showMap = ref(true);
const mapRef = ref();
const mapCenter = ref([106.636235, 26.380342]); // 默认
const mapZoom = ref(16);
const tiandituApiKey = import.meta.env.VITE_TIANDITU_API_KEY;

// 计算地图标记点
const mapMarkers = computed<MarkerData[]>(() => {
  return merchantList.value
    .filter(merchant => merchant.latitude && merchant.longitude)
    .map(merchant => ({
      id: merchant.id.toString(),
      lng: Number(merchant.longitude),
      lat: Number(merchant.latitude),
      title: merchant.name,
      content: `
        <div class="marker-info">
          <h4>${merchant.name}</h4>
          <p><strong>联系人:</strong> ${merchant.contact_name}</p>
          <p><strong>电话:</strong> ${merchant.contact_mobile}</p>
          <p><strong>地址:</strong> ${merchant.address || '暂无地址'}</p>
          <p><strong>状态:</strong> ${STATUS_TAG_CONFIG.merchantStatus[merchant.status as unknown as keyof typeof MerchantStatus]?.label || merchant.status}</p>
        </div>
      `,
      icon: {
        url: merchant.logo || '/images/default-marker.png',
        size: [32, 32],
        anchor: [16, 32]
      }
    }));
});

// 审核对话框
const auditDialogVisible = ref(false);
const auditDialogTitle = ref('');
const auditForm = reactive({
  id: 0,
  audit_status: '',
  reason: '',
});

// 新增商家表单
const addForm = reactive({
  username: '',
  name: '',
  contactPerson: '',
  contactMobile: '',
  contactEmail: '',
  address: '',
  description: '',
  isRecommended: 0,
});

// 新增对话框
const addDialogVisible = ref(false);

// 处理新增商家
const handleAddMerchant = () => {
  addDialogVisible.value = true;
};

// 提交新增商家
const submitAddMerchant = async () => {
  // TODO: 调用新增商家 API 接口
  console.log('新增商家表单数据', addForm);
  const response = await MerchantService.create({
    name: addForm.name,
    contact_name: addForm.contactPerson,
    contact_mobile: addForm.contactMobile,
    contact_email: addForm.contactEmail,
    address: addForm.address,
    description: addForm.description,
    is_recommended: addForm.isRecommended,
  });
  console.log('createMerchant response', response);
  ElMessage.success('新增商家成功');
  addDialogVisible.value = false;
  // 重置表单
  Object.assign(addForm, {
    username: '',
    name: '',
    contactPerson: '',
    contactMobile: '',
    contactEmail: '',
    address: '',
    description: '',
    isRecommended: 0,
  });
  fetchMerchantList();
};

// 获取商家列表
const fetchMerchantList = async () => {
  loading.value = true;
  try {
    const params: any = {
      page: currentPage.value,
      pageSize: pageSize.value,
    };
    
    // 兼容旧的搜索参数
    if (audit_status.value) {
      params.audit_status = audit_status.value;
    }
    
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value;
    }
    
    // 新的过滤器参数
    if (filterForm.name) {
      params.name = filterForm.name;
    }
    
    if (filterForm.contactName) {
      params.contact_name = filterForm.contactName;
    }
    
    if (filterForm.contactMobile) {
      params.contact_mobile = filterForm.contactMobile;
    }
    
    if (filterForm.isRecommended !== '') {
      params.is_recommended = filterForm.isRecommended;
    }
    
    if (filterForm.auditStatus) {
      params.audit_status = filterForm.auditStatus;
    }
    
    if (filterForm.status) {
      params.status = filterForm.status;
    }
    
    const response:any = await MerchantService.getList(params);
    merchantList.value = response.data.records;
    total.value = response.total;
    console.log('merchantList：', merchantList.value);
  } catch (error: any) {
    ElMessage.error(error.message || '获取商家列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  fetchMerchantList();
};

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchMerchantList();
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  fetchMerchantList();
};

// 重置过滤器
const handleReset = () => {
  Object.assign(filterForm, {
    name: '',
    contactName: '',
    contactMobile: '',
    isRecommended: '',
    auditStatus: '',
    status: ''
  });
  currentPage.value = 1;
  fetchMerchantList();
};

// 切换搜索模式
const toggleSearchMode = () => {
  isAdvancedSearch.value = !isAdvancedSearch.value;
  
  // 如果切换到简单搜索，清空高级搜索字段
  if (!isAdvancedSearch.value) {
    filterForm.contactName = '';
    filterForm.isRecommended = '';
    filterForm.auditStatus = '';
    filterForm.status = '';
    // 自动执行搜索以更新结果
    handleSearch();
  }
};

// 处理筛选变化
// const handleFilterChange = () => {
//   currentPage.value = 1;
//   fetchMerchantList();
// };

// 处理审核
const handleAudit = (merchant: Merchant, status: string) => {
  auditForm.id = merchant.id;
  auditForm.audit_status = status;
  auditForm.reason = '';
  
  auditDialogTitle.value = status === 'APPROVED' ? '审核通过' : '审核拒绝';
  auditDialogVisible.value = true;
};

// 提交审核
const submitAudit = async () => {
  if (auditForm.audit_status === 'REJECTED' && !auditForm.reason) {
    ElMessage.warning('请输入拒绝原因');
    return;
  }
  
  try {
    const response = await MerchantService.audit(
      auditForm.id,
      auditForm.audit_status as MerchantAuditStatus,
      auditForm.reason
    );
    console.log('auditMerchant response', response);
    ElMessage.success('审核操作成功');
    auditDialogVisible.value = false;
    fetchMerchantList();
  } catch (error: any) {
    ElMessage.error(error.message || '审核操作失败');
  }
};

// 处理状态变更
const handleStatusChange = (id: number, status: string) => {
  const statusTextMap: Record<string, string> = {
    ACTIVE: '开启',
    SUSPENDED: '暂停',
    CLOSED: '关闭'
  };
  
  ElMessageBox.confirm(
    `确定要${statusTextMap[status]}该商家吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        const response:any = await MerchantService.updateStatus(id, status as MerchantStatus);  
        console.log('updateMerchantStatus response', response);
        ElMessage.success(`${statusTextMap[status]}成功`);
        fetchMerchantList();
      } catch (error: any) {
        ElMessage.error(error.message || `${statusTextMap[status]}失败`);
      }
    })
    .catch(() => {
      // 取消操作
    });
};

// 处理推荐状态变更
const handleRecommendChange = (id: number, isRecommended: number) => {
  const actionText = isRecommended === 1 ? '推荐' : '取消推荐';
  
  ElMessageBox.confirm(
    `确定要${actionText}该商家吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        const response: any = await MerchantService.updateRecommendStatus(id, isRecommended);
        console.log('updateRecommendStatus response', response);
        ElMessage.success(`${actionText}成功`);
        fetchMerchantList();
      } catch (error: any) {
        ElMessage.error(error.message || `${actionText}失败`);
      }
    })
    .catch(() => {
      // 取消操作
    });
};

// 查看商家详情 - 跳转到详情页面
const router = useRouter();
const handleViewDetail = (merchant: Merchant) => {
  router.push(`/admin/merchants/${merchant.id}`);
};

// 地图相关方法
const toggleMap = () => {
  showMap.value = !showMap.value;
};

// 使所有标记点在地图视野内
const fitMapToMarkers = () => {
  if (mapRef.value && mapMarkers.value.length > 0) {
    // 计算所有标记点的中心点
    const centerPoint = calculateCenter(mapMarkers.value);
    if (centerPoint) {
      // 设置地图中心点和适当的缩放级别
      mapRef.value.setCenter(centerPoint.lng, centerPoint.lat, 12);
    } else {
      ElMessage.info('无法计算商家位置中心点');
    }
  } else {
    ElMessage.info('暂无商家位置信息');
  }
};

const handleMarkerClick = (marker: MarkerData) => {
  const merchant = merchantList.value.find(m => m.id.toString() === marker.id);
  if (merchant) {
    ElMessage.info(`点击了商家: ${merchant.name}`);
    // 可以在这里添加更多交互逻辑，比如高亮表格中对应的行
  }
};

const handleMapClick = (event: any) => {
  console.log('地图点击事件:', event);
};

// 监听商家列表变化，自动调整地图视图
watch(mapMarkers, (newMarkers) => {
  if (newMarkers.length > 0 && showMap.value) {
    // 延迟执行以确保地图已渲染
    setTimeout(() => {
      fitMapToMarkers();
    }, 1000);
  }
}, { immediate: false });

onMounted(() => {
  fetchMerchantList();
});
</script>

<style scoped lang="scss">
.merchant-view {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.action-box {
  display: flex;
  align-items: center;
  gap: 10px;
  .search-input {
    width: 250px;
  }
}

.filter-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.filter-layout {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.filter-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
}

.filter-search {
  flex: 1;
  min-width: 0;
}

.filter-form {
  .el-form-item {
    margin-bottom: 15px;
    margin-right: 20px;
    
    .filter-input {
      width: 200px;
    }
    
    .filter-select {
      width: 150px;
    }
  }
  
  .el-form-item:last-child {
    margin-right: 0;
  }
}

.map-card {
  margin-bottom: 20px;
  
  .map-container {
    width: 100%;
    height: 400px;
    border-radius: 4px;
    overflow: hidden;
  }
}

.merchant-table {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 地图标记信息窗口样式
:deep(.marker-info) {
  padding: 10px;
  min-width: 200px;
  
  h4 {
    margin: 0 0 10px 0;
    color: #409eff;
    font-size: 16px;
  }
  
  p {
    margin: 5px 0;
    font-size: 14px;
    line-height: 1.4;
    
    strong {
      color: #606266;
    }
  }
}
</style>