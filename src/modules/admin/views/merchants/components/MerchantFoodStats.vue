/**
 * 商家商品统计组件
 * 展示商家的商品统计数据
 */
<template>
  <el-card class="info-card">
    <template #header>
      <div class="info-header">
        <span>商品统计</span>
      </div>
    </template>
    <el-row :gutter="20">
      <el-col :span="6">
        <div class="stat-card clickable" @click="openFoodsDrawer">
          <div class="stat-value">{{ foodStats.total_count || 0 }}</div>
          <div class="stat-label">商品总数</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-value">{{ foodStats.on_sale_count || 0 }}</div>
          <div class="stat-label">在售商品</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-value">{{ foodStats.pending_count || 0 }}</div>
          <div class="stat-label">待审核商品</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-value">{{ foodStats.sold_out_count || 0 }}</div>
          <div class="stat-label">已售罄</div>
        </div>
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup lang="ts">
// import { defineProps, defineEmits } from 'vue';

defineProps({
  /**
   * 商品统计数据
   */
  foodStats: {
    type: Object,
    required: true,
    default: () => ({
      total_count: 0,
      on_sale_count: 0,
      pending_count: 0,
      sold_out_count: 0
    })
  }
});

const emit = defineEmits(['open-foods-drawer']);

/**
 * 打开商品列表抽屉
 */
const openFoodsDrawer = () => {
  emit('open-foods-drawer');
};
</script>

<style lang="scss" scoped>
.info-card {
  margin-bottom: 20px;

  .info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.stat-card {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  text-align: center;
  transition: all 0.3s;
  margin-bottom: 10px;

  &.clickable {
    cursor: pointer;
    &:hover {
      background-color: #e6e8eb;
      transform: translateY(-3px);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }

  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #409EFF;
    margin-bottom: 8px;
  }

  .stat-label {
    font-size: 14px;
    color: #606266;
  }
}
</style>
