/**
 * 商家商品列表抽屉组件
 * 展示商家的所有商品信息
 */
<template>
  <el-drawer
    v-model="visible"
    title="商家商品列表"
    size="70%"
    destroy-on-close
    @close="handleClose"
  >
    <div v-loading="loading" class="foods-container">
      <div v-if="foodsList.length === 0" class="empty-data">
        暂无商品数据
      </div>
      <el-row :gutter="20">
        <el-col 
          v-for="food in foodsList" 
          :key="food.id" 
          :xs="24" 
          :sm="12" 
          :md="8" 
          :lg="6"
          class="food-item-col"
        >
          <el-card 
            class="food-card" 
            shadow="hover" 
            @click="goToFoodDetail(food.id)"
          >
            <div class="food-image-container">
              <!-- 审核状态标记 -->
              <el-tag 
                class="food-audit-tag"
                size="small"
                :type="getFoodAuditStatusTagType(food.audit_status)"
              >
                {{ getFoodAuditStatusText(food.audit_status) }}
              </el-tag>
              <el-image 
                class="food-image" 
                :src="food.image || ''"
                fit="cover"
                :preview-src-list="[food.image]"
                :initial-index="0"
              >
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              
              <el-tag 
                class="food-status-tag" 
                :type="getFoodStatusTagType(food.status)"
              >
                {{ getFoodStatusText(food.status) }}
              </el-tag>
            </div>
            
            <div class="food-info">
              <h3 class="food-name">{{ food.name }}</h3>
              <div class="food-price">¥{{ food.price }}</div>
              <div class="food-desc">{{ food.brief || food.description || '暂无描述' }}</div>
              <div class="food-category">分类：{{ food.category_name || '未分类' }}</div>
              <div class="food-sales">销量：{{ food.total_sold ?? food.sales_count ?? 0 }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Picture } from '@element-plus/icons-vue';

/**
 * 食品/商品接口定义
 */
interface Food {
  id: number;
  name: string;
  price: number;
  image?: string;
  status: number;
  audit_status: number;
  brief?: string;
  description?: string;
  category_name?: string;
  total_sold?: number;
  sales_count?: number;
}

const props = defineProps({
  /**
   * 抽屉是否可见
   */
  modelValue: {
    type: Boolean,
    default: false
  },
  /**
   * 商品列表数据
   */
  foodsList: {
    type: Array as () => Food[],
    default: () => []
  },
  /**
   * 数据加载状态
   */
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'view-food']);

// 抽屉是否可见
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

/**
 * 关闭抽屉
 */
const handleClose = () => {
  visible.value = false;
};

/**
 * 跳转到商品详情页
 * @param foodId 商品ID
 */
const goToFoodDetail = (foodId: number) => {
  emit('view-food', foodId);
};

/**
 * 获取商品审核状态标签类型
 * @param audit_status 审核状态值
 * @returns 标签类型
 */
const getFoodAuditStatusTagType = (audit_status: number) => {
  switch (audit_status) {
    case 0: return 'warning';  // 待审核
    case 1: return 'success';  // 已通过
    case 2: return 'danger';   // 已拒绝
    default: return 'info';
  }
};

/**
 * 获取商品审核状态文本
 * @param audit_status 审核状态值
 * @returns 审核状态文本
 */
const getFoodAuditStatusText = (audit_status: number) => {
  switch (audit_status) {
    case 0: return '待审核';
    case 1: return '已通过';
    case 2: return '已拒绝';
    default: return '未知';
  }
};

/**
 * 获取商品状态标签类型
 * @param status 状态值
 * @returns 标签类型
 */
const getFoodStatusTagType = (status: number) => {
  switch (status) {
    case 0: return 'info';      // 已下架
    case 1: return 'success';   // 已上架
    case 2: return 'danger';    // 已售罄
    case 3: return 'warning';   // 已删除
    default: return 'info';
  }
};

/**
 * 获取商品状态文本
 * @param status 状态值
 * @returns 状态文本
 */
const getFoodStatusText = (status: number) => {
  switch (status) {
    case 0: return '已下架';
    case 1: return '已上架';
    case 2: return '已售罄';
    case 3: return '已删除';
    default: return '未知';
  }
};
</script>

<style lang="scss" scoped>
.foods-container {
  padding: 20px;
}

.empty-data {
  text-align: center;
  padding: 40px 0;
  color: #909399;
  font-size: 14px;
}

.food-item-col {
  margin-bottom: 20px;
}

.food-card {
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  
  .food-image-container {
    position: relative;
    height: 160px;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 10px;
    
    .food-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .image-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background-color: #f5f7fa;
      color: #909399;
      font-size: 30px;
    }
    
    .food-audit-tag {
      position: absolute;
      top: 10px;
      left: 10px;
      z-index: 1;
    }
    
    .food-status-tag {
      position: absolute;
      bottom: 10px;
      right: 10px;
      z-index: 1;
    }
  }
  
  .food-info {
    padding: 10px 0;
    
    .food-name {
      margin: 0 0 10px 0;
      font-size: 16px;
      font-weight: bold;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .food-price {
      color: #F56C6C;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .food-desc {
      color: #606266;
      font-size: 12px;
      margin-bottom: 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      height: 36px;
    }
    
    .food-category,
    .food-sales {
      color: #909399;
      font-size: 12px;
    }
  }
}
</style>
