/**
 * 编辑商家信息对话框组件
 * 用于编辑商家的基本信息和位置等数据
 */
<template>
  <el-dialog
    v-model="visible"
    title="编辑商家信息"
    width="800px"
    destroy-on-close
    @close="handleClose"
  >
    <el-form
      ref="editFormRef"
      :model="editForm"
      :rules="editFormRules"
      label-width="100px"
    >
      <el-form-item label="商家名称" prop="name">
        <el-input v-model="editForm.name" placeholder="请输入商家名称" />
      </el-form-item>
      <el-form-item label="登录名" prop="username">
        <el-input v-model="editForm.username" placeholder="请输入登录名" />
      </el-form-item>
      <el-form-item label="商家LOGO" prop="logo">
        <FileUploader
          action="/v1/admin/upload"
          :file-limit="1"
          :size-limit="2 * 1024 * 1024"
          accept=".jpg,.jpeg,.png,.gif"
          file-usage="merchant_logo"
          upload-style="button"
          button-text="上传LOGO"
          button-icon="Upload"
          :initial-files="logoInitialFiles"
          @success="handleLogoUploadSuccess"
        />
        <div v-if="editForm.logo" class="preview-image">
          <el-image :src="editForm.logo" style="width: 100px; height: 100px;" fit="cover" />
          <el-button type="danger" size="small" icon="Delete" circle @click="removeLogo" />
        </div>
      </el-form-item>
      <el-form-item label="营业执照" prop="business_license">
        <FileUploader
          action="/v1/admin/upload"
          :file-limit="1"
          :size-limit="5 * 1024 * 1024"
          accept=".jpg,.jpeg,.png,.pdf"
          file-usage="business_license"
          upload-style="button"
          button-text="上传营业执照"
          button-icon="Upload"
          :initial-files="licenseInitialFiles"
          @success="handleLicenseUploadSuccess"
        />
        <div v-if="editForm.business_license" class="preview-image">
          <el-image :src="editForm.business_license" style="width: 100px; height: 100px;" fit="cover" />
          <el-button type="danger" size="small" icon="Delete" circle @click="removeLicense" />
        </div>
      </el-form-item>
      <el-form-item label="联系人" prop="contact_name">
        <el-input v-model="editForm.contact_name" placeholder="请输入联系人姓名" />
      </el-form-item>
      <el-form-item label="联系电话" prop="contact_mobile">
        <el-input v-model="editForm.contact_mobile" placeholder="请输入联系电话" />
      </el-form-item>
      <el-form-item label="联系邮箱" prop="contact_email">
        <el-input v-model="editForm.contact_email" placeholder="请输入联系邮箱" />
      </el-form-item>
      <el-form-item label="地址" prop="address">
        <el-input v-model="editForm.address" placeholder="请输入商家地址" />
      </el-form-item>
      <el-form-item label="位置选择" style="width: 100%;">
        <div class="location-selector" style="width: 100%;">
          <TiandituMap
            :width="'100%'"
            :height="'300px'"
            :longitude="(editForm.longitude && editForm.latitude) ? editForm.longitude : defaultMapConfig.center[0]"
            :latitude="(editForm.longitude && editForm.latitude) ? editForm.latitude : defaultMapConfig.center[1]"
            :zoom="(editForm.longitude && editForm.latitude) ? 16 : defaultMapConfig.zoom"
            :editable-marker="editableMarkerData"
            :edit-mode="true"
            :add-marker-mode="!editForm.longitude || !editForm.latitude || editForm.longitude === 0 || editForm.latitude === 0"
            @map-click="handleMapClick"
            @editable-marker-update="handleMarkerUpdate"
          />
          <div class="coordinate-info">
            <span>经度: {{ editForm.longitude || '未设置' }}</span>
            <span>纬度: {{ editForm.latitude || '未设置' }}</span>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="商家等级" prop="level">
        <el-select v-model="editForm.level" placeholder="请选择商家等级">
          <el-option label="普通商家" :value="1" />
          <el-option label="金牌商家" :value="2" />
          <el-option label="钻石商家" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="商家描述" prop="description">
        <el-input
          v-model="editForm.description"
          type="textarea"
          :rows=4
          placeholder="请输入商家描述"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="audit_status">
        <el-select v-model="editForm.audit_status" placeholder="请选择审核状态">
          <el-option label="通过" :value="1" />
          <el-option label="拒绝" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="拒绝原因" prop="reason">
        <el-input
          v-model="editForm.reason"
          type="textarea"
          :rows=4
          placeholder="请输入拒绝原因"
        />
      </el-form-item>
      <el-form-item label="推荐状态" prop="is_recommended">
        <el-select v-model="editForm.is_recommended" placeholder="请选择推荐状态">
          <el-option label="普通" :value="0" />
          <el-option label="推荐" :value="1" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts">
// 确保组件正确注册
import * as TiandituMapComponents from '@/components/map';
//import { calculateCenter } from '@/components/map/utils';

export default {
  components: {
    TiandituMap: TiandituMapComponents.TiandituMap
  }
};
</script>
<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { FileUploader } from '@/components/common';

const props = defineProps({
  /**
   * 对话框是否可见
   */
  modelValue: {
    type: Boolean,
    default: false
  },
  /**
   * 商家信息
   */
  merchantInfo: {
    type: Object,
    required: true,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue', 'submit']);

// 表单引用
const editFormRef = ref<FormInstance>();

// 对话框是否可见
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

// 编辑表单数据
const editForm = ref({
  id: 0,
  name: '',
  username: '',
  logo: '',
  business_license: '',
  contact_name: '',
  contact_mobile: '',
  contact_email: '',
  address: '',
  longitude: 0,
  latitude: 0,
  level: 1,
  description: '',
  audit_status: 1,
  reason: '',
  is_recommended: 0
});

// 表单验证规则
const editFormRules: FormRules = {
  name: [
    { required: true, message: '请输入商家名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入登录名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  contact_name: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  contact_mobile: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  contact_email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入商家地址', trigger: 'blur' }
  ]
};

// 地图默认配置
const defaultMapConfig = {
  center: [116.397428, 39.90923], // 默认位置北京天安门
  zoom: 12
};

// 可编辑标记点数据
const editableMarkerData = computed(() => {
  if (editForm.value.longitude && editForm.value.latitude && 
      editForm.value.longitude !== 0 && editForm.value.latitude !== 0) {
    return {
      lng: editForm.value.longitude,
      lat: editForm.value.latitude,
      title: editForm.value.name || '商家位置'
    };
  }
  return null;
});

// LOGO初始文件
const logoInitialFiles = computed(() => {
  if (editForm.value.logo) {
    return [{
      name: '商家LOGO',
      url: editForm.value.logo
    }];
  }
  return [];
});

// 营业执照初始文件
const licenseInitialFiles = computed(() => {
  if (editForm.value.business_license) {
    return [{
      name: '营业执照',
      url: editForm.value.business_license
    }];
  }
  return [];
});

/**
 * 初始化表单数据
 */
const initFormData = () => {
  // 确保所有字段为 string 类型
  editForm.value = {
    id: props.merchantInfo.id || 0,
    name: props.merchantInfo.name || '',
    username: props.merchantInfo.username || '',
    logo: props.merchantInfo.logo || '',
    business_license: props.merchantInfo.business_license || '',
    contact_name: props.merchantInfo.contact_name || '',
    contact_mobile: props.merchantInfo.contact_mobile || '',
    contact_email: props.merchantInfo.contact_email || '',
    address: props.merchantInfo.address || '',
    longitude: Number(props.merchantInfo.longitude) || 0,
    latitude: Number(props.merchantInfo.latitude) || 0,
    level: Number(props.merchantInfo.level) || 1,
    description: props.merchantInfo.description || '',
    audit_status: Number(props.merchantInfo.audit_status) || 1,
    reason: props.merchantInfo.reject_reason || '',
    is_recommended: Number(props.merchantInfo.is_recommended) || 0
  };
};

// 监听对话框显示状态
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    initFormData();
  }
}, { immediate: true });

// 监听商家信息变化
watch(() => props.merchantInfo, () => {
  if (visible.value) {
    initFormData();
  }
}, { deep: true });

/**
 * 处理LOGO上传成功
 * @param response 上传响应
 */
const handleLogoUploadSuccess = (response: any) => {
  console.log(response)
  if (response  && response.file_url) {
    editForm.value.logo = response.file_url;
  }
};

/**
 * 处理营业执照上传成功
 * @param response 上传响应
 */
const handleLicenseUploadSuccess = (response: any) => {
  console.log(response)
  if (response && response.file_url) {
    editForm.value.business_license = response.file_url;
  }
};

/**
 * 移除LOGO
 */
const removeLogo = () => {
  editForm.value.logo = '';
};

/**
 * 移除营业执照
 */
const removeLicense = () => {
  editForm.value.business_license = '';
};

/**
 * 处理地图点击事件
 * @param event 点击事件
 */
const handleMapClick = (event: any) => {
  // 当商家经纬度为0时，第一次点击地图创建可编辑标记点
  if (!editForm.value.longitude || !editForm.value.latitude || 
      editForm.value.longitude === 0 || editForm.value.latitude === 0) {
    if (event && event.lnglat) {
      editForm.value.longitude = event.lnglat.lng;
      editForm.value.latitude = event.lnglat.lat;
    }
  }
};

/**
 * 处理标记点更新事件
 * @param lnglat 更新的经纬度
 */
const handleMarkerUpdate = (lnglat: { lng: number, lat: number }) => {
  // 当用户拖拽标记点时更新坐标数据
  if (lnglat && lnglat.lng && lnglat.lat) {
    // 保留6位小数
    editForm.value.longitude = parseFloat(lnglat.lng.toFixed(6));
    editForm.value.latitude = parseFloat(lnglat.lat.toFixed(6));
  }
};

/**
 * 关闭对话框
 */
const handleClose = () => {
  visible.value = false;
};

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!editFormRef.value) return;
  
  await editFormRef.value.validate((valid) => {
    if (valid) {
      emit('submit', { ...editForm.value });
    }
  });
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.preview-image {
  margin-top: 10px;
  position: relative;
  display: inline-block;
  
  .el-button {
    position: absolute;
    top: -10px;
    right: -10px;
  }
}

.location-selector {
  .coordinate-info {
    margin-top: 10px;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-size: 14px;
    color: #606266;
    display: flex;
    justify-content: space-between;
  }
}
</style>
