/**
 * 商家重置密码对话框组件
 * 用于重置商家账号密码
 */
<template>
  <el-dialog
    v-model="visible"
    title="重置密码"
    width="500px"
    destroy-on-close
    @close="handleClose"
  >
    <el-form
      ref="passwordFormRef"
      :model="passwordForm"
      :rules="passwordFormRules"
      label-width="100px"
    >
      <el-form-item label="新密码" prop="newPassword">
        <el-input
          v-model="passwordForm.newPassword"
          type="password"
          placeholder="请输入新密码"
          show-password
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model="passwordForm.confirmPassword"
          type="password"
          placeholder="请再次输入新密码"
          show-password
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';

const props = defineProps({
  /**
   * 对话框是否可见
   */
  modelValue: {
    type: Boolean,
    default: false
  },
  /**
   * 商家用户名
   */
  username: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'submit']);

// 表单引用
const passwordFormRef = ref<FormInstance>();

// 对话框是否可见
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

// 重置密码表单数据
const passwordForm = ref({
  newPassword: '',
  confirmPassword: ''
});

/**
 * 密码确认校验规则
 */
const validatePasswordConfirm = (_rule: any, value: string, callback: Function) => {
  if (value !== passwordForm.value.newPassword) {
    callback(new Error('两次输入的密码不一致'));
  } else {
    callback();
  }
};

// 密码表单验证规则
const passwordFormRules: FormRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { validator: validatePasswordConfirm, trigger: 'blur' }
  ]
};

/**
 * 关闭对话框
 */
const handleClose = () => {
  visible.value = false;
  passwordForm.value.newPassword = '';
  passwordForm.value.confirmPassword = '';
};

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!passwordFormRef.value) return;
  
  await passwordFormRef.value.validate((valid) => {
    if (valid) {
      emit('submit', {
        new_password: passwordForm.value.newPassword,
        confirm_password: passwordForm.value.confirmPassword
      });
    }
  });
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
