/**
 * 商家订单统计组件
 * 展示商家的订单统计数据
 */
<template>
  <el-card class="info-card">
    <template #header>
      <div class="info-header">
        <span>订单统计</span>
      </div>
    </template>
    <el-row :gutter="20">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-value">{{ orderStats.total_count || 0 }}</div>
          <div class="stat-label">订单总数</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-value">{{ orderStats.completed_count || 0 }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-value">{{ orderStats.processing_count || 0 }}</div>
          <div class="stat-label">处理中</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-value">{{ orderStats.cancelled_count || 0 }}</div>
          <div class="stat-label">已取消</div>
        </div>
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup lang="ts">

defineProps({
  /**
   * 订单统计数据
   */
  orderStats: {
    type: Object,
    required: true,
    default: () => ({
      total_count: 0,
      completed_count: 0,
      processing_count: 0,
      cancelled_count: 0
    })
  }
});
</script>

<style lang="scss" scoped>
.info-card {
  margin-bottom: 20px;

  .info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.stat-card {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  text-align: center;
  transition: all 0.3s;
  margin-bottom: 10px;

  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #67C23A;
    margin-bottom: 8px;
  }

  .stat-label {
    font-size: 14px;
    color: #606266;
  }
}
</style>
