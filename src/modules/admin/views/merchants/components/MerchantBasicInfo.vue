/**
 * 商家基本信息组件
 * 展示商家的基本信息和状态
 */
<template>
  <el-card class="info-card">
    <template #header>
      <div class="info-header">
        <span>基本信息</span>
        <el-button type="primary" plain size="small" @click="handleEditMerchant">
          <el-icon><Edit /></el-icon> 编辑
        </el-button>
      </div>
    </template>
    
    <div class="merchant-header">
      <div class="merchant-logo">
        <el-avatar :size="100" :src="merchantInfo.logo">
          <el-icon><Shop /></el-icon>
        </el-avatar>
      </div>
      <div class="merchant-main-info">
        <div class="merchant-name-wrapper">
          <h2 class="merchant-name">{{ merchantInfo.name }}</h2>
          <div class="username-tag" @click.stop="handleResetPassword">
            <el-tag type="info" size="small" effect="dark">
              {{ merchantInfo.username || '未设置' }}
            </el-tag>
          </div>
        </div>
        <div class="merchant-status">
          <el-tag 
            :type="getStatusTagType(merchantInfo.operation_status)"
            class="status-tag"
          >
            {{ getStatusText(merchantInfo.operation_status) }}
          </el-tag>
          <el-tag 
            :type="getAuditStatusTagType(merchantInfo.audit_status)"
            class="status-tag"
          >
            {{ getAuditStatusText(merchantInfo.audit_status) }}
          </el-tag>
        </div>
      </div>
    </div>
    
    <el-descriptions :column="2" border>
      <el-descriptions-item label="商家ID">{{ merchantInfo.id }}</el-descriptions-item>
      <el-descriptions-item label="商家名称">{{ merchantInfo.name }}</el-descriptions-item>
      <el-descriptions-item label="联系人">{{ merchantInfo.contact_name }}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{ merchantInfo.contact_mobile }}</el-descriptions-item>
      <el-descriptions-item label="联系邮箱">{{ merchantInfo.contact_email }}</el-descriptions-item>
      <el-descriptions-item label="地址">{{ merchantInfo.address }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ formatTime(merchantInfo.created_at) }}
      </el-descriptions-item>
      <el-descriptions-item label="更新时间">
        {{ formatTime(merchantInfo.updated_at) }}
      </el-descriptions-item>
      <el-descriptions-item label="商家描述" :span="2">
        {{ merchantInfo.description || '暂无描述' }}
      </el-descriptions-item>
      <el-descriptions-item label="审核状态">
        <el-tag :type="getAuditStatusTagType(merchantInfo.audit_status)">
          {{ getAuditStatusText(merchantInfo.audit_status) }}
        </el-tag>
        <template v-if="merchantInfo.audit_status === 2">
          <div class="mt-2 text-gray-500 text-sm">
            拒绝原因：{{ merchantInfo.reject_reason || '无' }}
          </div>
        </template>
      </el-descriptions-item>
      <el-descriptions-item label="营业状态">
        <el-tag :type="getStatusTagType(merchantInfo.operation_status)">
          {{ getStatusText(merchantInfo.operation_status) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="审核备注">
        {{ merchantInfo.audit_remark || '无' }}
      </el-descriptions-item>
      <el-descriptions-item label="推荐状态">
        <el-tag :type="merchantInfo.is_recommended === 1 ? 'success' : 'info'">
          {{ merchantInfo.is_recommended === 1 ? '推荐商家' : '普通商家' }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top: 18px;">
      <el-button type="warning" plain size="small" @click="handleResetPassword">
        <el-icon><Lock /></el-icon> 重置密码
      </el-button>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { Edit, Shop, Lock } from '@element-plus/icons-vue';
// import { defineProps, defineEmits } from 'vue';
import { formatTime } from '@/utils/format';

defineProps({
  /**
   * 商家信息对象
   */
  merchantInfo: {
    type: Object,
    required: true,
    default: () => ({})
  }
});

const emit = defineEmits(['edit-merchant', 'reset-password']);

/**
 * 处理编辑商家信息
 */
const handleEditMerchant = () => {
  emit('edit-merchant');
};

/**
 * 处理重置密码
 */
const handleResetPassword = () => {
  emit('reset-password');
};

/**
 * 获取审核状态标签类型
 * @param status 状态值
 * @returns 标签类型
 */
const getAuditStatusTagType = (status: number) => {
  switch (status) {
    case 0: return 'warning';  // 待审核
    case 1: return 'success';  // 已通过
    case 2: return 'danger';   // 已拒绝
    default: return 'info';
  }
};

/**
 * 获取状态标签类型
 * @param status 状态值
 * @returns 标签类型
 */
const getStatusTagType = (status: number) => {
  switch (status) {
    case 0: return 'warning';  // 休息中
    case 1: return 'success';  // 营业中
    default: return 'info';
  }
};

/**
 * 获取审核状态文本
 * @param status 状态值
 * @returns 状态文本
 */
const getAuditStatusText = (status: number) => {
  switch (status) {
    case 0: return '待审核';
    case 1: return '已通过';
    case 2: return '已拒绝';
    default: return '未知状态';
  }
};

/**
 * 获取状态文本
 * @param status 状态值
 * @returns 状态文本
 */
const getStatusText = (status: number) => {
  switch (status) {
    case 0: return '休息中';
    case 1: return '营业中';
    default: return '未知状态';
  }
};
</script>

<style lang="scss" scoped>
.info-card {
  margin-bottom: 20px;
  
  .info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .merchant-header {
    display: flex;
    margin-bottom: 20px;
    
    .merchant-logo {
      margin-right: 20px;
    }
    
    .merchant-main-info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      
      .merchant-name-wrapper {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        
        .merchant-name {
          margin: 0;
          margin-right: 10px;
        }
        
        .username-tag {
          cursor: pointer;
        }
      }
      
      .merchant-status {
        display: flex;
        gap: 10px;
      }
    }
  }
}

.status-tag {
  margin-right: 10px;
}

.mt-2 {
  margin-top: 8px;
}

.text-gray-500 {
  color: #909399;
}

.text-sm {
  font-size: 14px;
}
</style>
