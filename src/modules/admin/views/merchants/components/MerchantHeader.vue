/**
 * 商家头部操作区组件
 * 包含返回按钮和各种状态操作按钮
 */
<template>
  <div class="card-header">
    <div class="left">
      <el-button @click="goBack" type="primary" plain size="small">
        <el-icon><ArrowLeft /></el-icon> 返回
      </el-button>
      <span class="title">商家详情</span>
    </div>
    
    <div class="action-buttons">
      <!-- 审核操作 -->
      <el-button
        v-if="merchantInfo.audit_status === 0"
        type="success"
        @click="handleAudit('APPROVED')"
      >
        <el-icon><Check /></el-icon> 审核通过
      </el-button>
      <el-button
        v-if="merchantInfo.audit_status === 0"
        type="danger"
        @click="handleAudit('REJECTED')"
      >
        <el-icon><Close /></el-icon> 审核拒绝
      </el-button>
      
      <!-- 状态操作 -->
      <el-button
        v-if="merchantInfo.audit_status === 1 && merchantInfo.operation_status === 1"
        type="warning"
        @click="handleStatusChange(0)"
      >
        <el-icon><VideoPause /></el-icon> 设为休息中
      </el-button>
      <el-button
        v-if="merchantInfo.audit_status === 1 && merchantInfo.operation_status === 0"
        type="success"
        @click="handleStatusChange(1)"
      >
        <el-icon><VideoPlay /></el-icon> 设为营业中
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft, Check, Close, VideoPause, VideoPlay } from '@element-plus/icons-vue';
// import { defineProps, defineEmits } from 'vue';

defineProps({
  /**
   * 商家信息对象
   */
  merchantInfo: {
    type: Object,
    required: true,
    default: () => ({})
  }
});

const emit = defineEmits(['go-back', 'audit', 'status-change']);

/**
 * 返回上一页
 */
const goBack = () => {
  emit('go-back');
};

/**
 * 处理审核操作
 * @param status 审核状态 APPROVED|REJECTED
 */
const handleAudit = (status: 'APPROVED' | 'REJECTED') => {
  emit('audit', status);
};

/**
 * 处理状态变更
 * @param status 状态值
 */
const handleStatusChange = (status: number) => {
  emit('status-change', status);
};
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .left {
    display: flex;
    align-items: center;
    
    .title {
      margin-left: 10px;
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .action-buttons {
    display: flex;
    gap: 10px;
  }
}
</style>
