/**
 * 商家审核对话框组件
 * 用于审核商家的通过或拒绝操作
 */
<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="500px"
    destroy-on-close
    @close="handleClose"
  >
    <el-form 
      ref="auditFormRef"
      :model="auditForm" 
      :rules="auditRules"
      label-width="100px"
    >
      <el-form-item 
        v-if="auditForm.audit_status === 'REJECTED'" 
        label="拒绝原因" 
        prop="reason"
      >
        <el-input
          v-model="auditForm.reason"
          type="textarea"
          rows="4"
          maxlength="500"
          show-word-limit
          placeholder="请详细说明拒绝原因，以便商家了解并改进"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';

const props = defineProps({
  /**
   * 对话框是否可见
   */
  modelValue: {
    type: Boolean,
    default: false
  },
  /**
   * 审核类型：APPROVED | REJECTED
   */
  auditType: {
    type: String,
    default: '',
    validator: (value: string) => ['APPROVED', 'REJECTED', ''].includes(value)
  }
});

const emit = defineEmits(['update:modelValue', 'submit']);

// 表单引用
const auditFormRef = ref<FormInstance>();

// 对话框是否可见
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  return props.auditType === 'APPROVED' ? '审核通过确认' : '审核拒绝确认';
});

// 审核表单数据
const auditForm = ref({
  audit_status: props.auditType,
  reason: ''
});

// 监听审核类型变化
watch(() => props.auditType, (newVal) => {
  auditForm.value.audit_status = newVal;
  auditForm.value.reason = '';
}, { immediate: true });

// 审核表单验证规则
const auditRules: FormRules = {
  reason: [
    { required: true, message: '请输入拒绝原因', trigger: 'blur' },
    { 
      validator: (_rule: any, value: string, callback: Function) => {
        if (auditForm.value.audit_status === 'REJECTED' && (!value || value.trim() === '')) {
          callback(new Error('拒绝原因不能为空'));
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    },
    { min: 5, max: 500, message: '长度在 5 到 500 个字符', trigger: 'blur' }
  ]
};

/**
 * 关闭对话框
 */
const handleClose = () => {
  visible.value = false;
  auditForm.value.reason = '';
};

/**
 * 提交审核
 */
const handleSubmit = async () => {
  if (auditForm.value.audit_status === 'REJECTED') {
    if (!auditFormRef.value) return;
    
    await auditFormRef.value.validate((valid) => {
      if (valid) {
        submitForm();
      }
    });
  } else {
    submitForm();
  }
};

/**
 * 提交表单
 */
const submitForm = () => {
  const data = {
    audit_status: auditForm.value.audit_status,
    reason: auditForm.value.reason
  };
  emit('submit', data);
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
