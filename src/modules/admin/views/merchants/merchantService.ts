/**
 * 商家服务
 * 处理商家数据的转换和业务逻辑
 */
import { 
  getMerchantList,
  createMerchant,
  auditMerchant,
  updateMerchantStatus,
  getMerchantDetail,
  updateMerchantRecommendStatus
} from '../../api/merchant';
import type { MerchantStatus, MerchantAuditStatus } from '../../types';
import type { ApiResponse } from '@/types';
//import { ElMessage } from 'element-plus';

/**
 * 商家状态转换映射
 * 前端枚举 <-> 后端数字编码
 */
const MERCHANT_STATUS_MAP = {
  // 前端 -> 后端
  toBackend: {
    ACTIVE: 1,
    SUSPENDED: 2,
    CLOSED: 0
  },
  // 后端 -> 前端
  toFrontend: {
    '1': 'ACTIVE' as MerchantStatus,
    '0': 'CLOSED' as MerchantStatus
  }
};

/**
 * 商家审核状态转换映射
 * 前端枚举 <-> 后端数字编码
 * 0-待审核,1-已审核,2-已拒绝,3-已禁用
 */
const MERCHANT_AUDIT_STATUS_MAP = {
  // 前端 -> 后端
  toBackend: {
    PENDING: 0,
    APPROVED: 1,
    REJECTED: 2
  },
  // 后端 -> 前端
  toFrontend: {
    '0': 'PENDING' as MerchantAuditStatus,
    '1': 'APPROVED' as MerchantAuditStatus,
    '2': 'REJECTED' as MerchantAuditStatus
  }
};

/**
 * 商家服务
 */
export const MerchantService = {
  /**
   * 获取商家列表
   * @param params 查询参数
   * @returns 商家列表和分页信息
   */
  async getList(params: {
    page: number;
    pageSize: number;
    audit_status?: MerchantAuditStatus;
    status?: MerchantStatus;
  }): Promise<ApiResponse<{records: any[], total: number}>> {
    try {
      // 转换状态参数从前端枚举到后端编码
      const backendParams = { ...params } as any;
      if (params.audit_status) {
        backendParams.audit_status = MERCHANT_AUDIT_STATUS_MAP.toBackend[params.audit_status];
      }
      if (params.status) {
        backendParams.status = MERCHANT_STATUS_MAP.toBackend[params.status];
      }
      
      const res: any = await getMerchantList(backendParams);
      
      // 转换返回数据中的状态码为前端枚举
      if (res.list) {
        console.log('获取商家列表成功', res);
        const merchants = res.list.map((merchant: any) => ({
          ...merchant,
          status: MERCHANT_STATUS_MAP.toFrontend[merchant.status as keyof typeof MERCHANT_STATUS_MAP.toFrontend] || merchant.status,
          audit_status: MERCHANT_AUDIT_STATUS_MAP.toFrontend[merchant.audit_status as keyof typeof MERCHANT_AUDIT_STATUS_MAP.toFrontend] || merchant.audit_status
        }));
        
        return {
          ...res,
          data: {
            ...res.list,
            records: merchants
          }
        };
      }
      
      return res;
    } catch (error) {
      console.error('获取商家列表失败', error);
      throw error;
    }
  },
  
  /**
   * 创建商家
   * @param data 商家信息
   * @returns 创建的商家信息
   */
  async create(data: {
    name: string;
    contact_name: string;
    contact_mobile: string;
    contact_email: string;
    address: string;
    description: string;
    is_recommended?: number;
  }): Promise<ApiResponse<any>> {
    try {
      // 转换前端字段名称为后端字段名称
      // const backendData = {
      //   name: data.name,
      //   contactName: data.contact_name,
      //   contactMobile: data.contact_mobile,
      //   contactEmail: data.contact_email,
      //   address: data.address,
      //   description: data.description
      // };
      
      const res:any = await createMerchant({
        name: data.name,
        contact_name: data.contact_name,
        contact_mobile: data.contact_mobile,
        contact_email: data.contact_email,
        address: data.address,
        description: data.description,
        is_recommended: data.is_recommended || 0
      });
      return res;
    } catch (error) {
      console.error('创建商家失败', error);
      throw error;
    }
  },
  
  /**
   * 审核商家
   * @param id 商家ID
   * @param audit_status 审核状态
   * @param reason 拒绝原因（审核状态为REJECTED时必填）
   * @returns 操作结果
   */
  async audit(id: number, audit_status: MerchantAuditStatus, reason?: string): Promise<ApiResponse<any>> {
    try {
      const backendAuditStatus = MERCHANT_AUDIT_STATUS_MAP.toBackend[audit_status];
      const res:any = await auditMerchant(id, {
        audit_status: String(backendAuditStatus),
        reason: audit_status === 'REJECTED' ? reason : undefined
      });
      return res;
    } catch (error) {
      console.error('审核商家失败', error);
      throw error;
    }
  },
  
  /**
   * 更新商家状态
   * @param id 商家ID
   * @param status 新状态
   * @returns 操作结果
   */
  async updateStatus(id: number, status: MerchantStatus): Promise<ApiResponse<any>> {
    try {
      const backendStatus = MERCHANT_STATUS_MAP.toBackend[status];
      const res:any = await updateMerchantStatus(id, { status: Number(backendStatus) });
      return res;
    } catch (error) {
      console.error('更新商家状态失败', error);
      throw error;
    }
  },
  
  /**
   * 获取商家状态标签类型
   * @param status 商家状态
   * @returns 对应的Element Plus标签类型
   */
  getStatusTagType(status: MerchantStatus): string {
    switch (status) {
      case 'ACTIVE':
        return 'success';
      case 'CLOSED':
        return 'warning';
      default:
        return 'info';
    }
  },
  
  /**
   * 获取商家审核状态标签类型
   * @param status 商家审核状态
   * @returns 对应的Element Plus标签类型
   */
  getAuditStatusTagType(status: MerchantAuditStatus): string {
    switch (status) {
      case 'PENDING':
        return 'info';
      case 'APPROVED':
        return 'success';
      case 'REJECTED':
        return 'danger';
      default:
        return 'info';
    }
  },
  
  /**
   * 获取商家状态显示文本
   * @param status 商家状态
   * @returns 对应的中文文本
   */
  getStatusText(status: MerchantStatus): string {
    switch (status) {
      case 'ACTIVE':
        return '营业中';
      case 'CLOSED':
        return '休息中';
      default:
        return '未知';
    }
  },
  
  /**
   * 获取商家审核状态显示文本
   * @param status 商家审核状态
   * @returns 对应的中文文本
   */
  getAuditStatusText(status: MerchantAuditStatus): string {
    switch (status) {
      case 'PENDING':
        return '待审核';
      case 'APPROVED':
        return '已通过';
      case 'REJECTED':
        return '已拒绝';
      default:
        return '未知';
    }
  },
  
  /**
   * 获取商家详情
   * @param id 商家ID
   * @returns 商家详情信息
   */
  async getDetail(id: number): Promise<ApiResponse<any>> {
    try {
      const res = await getMerchantDetail(id);
      
      if (res.data) {
        // 转换状态码为前端枚举
        const merchant = {
          ...res.data,
          status: MERCHANT_STATUS_MAP.toFrontend[res.data.status as keyof typeof MERCHANT_STATUS_MAP.toFrontend] || res.data.status,
          audit_status: MERCHANT_AUDIT_STATUS_MAP.toFrontend[res.data.audit_status as keyof typeof MERCHANT_AUDIT_STATUS_MAP.toFrontend] || res.data.audit_status
        };
        
        return {
          ...res,
          data: merchant
        };
      }
      
      return res;
    } catch (error) {
      console.error('获取商家详情失败', error);
      throw error;
    }
  },

  /**
   * 更新商家推荐状态
   * @param id 商家ID
   * @param isRecommended 是否推荐：0-否，1-是
   * @returns API响应
   */
  async updateRecommendStatus(id: number, isRecommended: number): Promise<ApiResponse<any>> {
    try {
      return await updateMerchantRecommendStatus(id, isRecommended);
    } catch (error) {
      console.error('更新商家推荐状态失败', error);
      throw error;
    }
  }
};

export default MerchantService;
