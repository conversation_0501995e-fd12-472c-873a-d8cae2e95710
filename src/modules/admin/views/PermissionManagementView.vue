<template>
  <div class="permission-management-view">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>权限管理</span>
          <div class="action-box">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入权限名称/编码"
              clearable
              @keyup.enter="handleSearch"
              class="search-input"
            >
              <template #append>
                <el-button @click="handleSearch">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon> 新增权限
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="permissionList"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="权限名称" width="150" />
        <el-table-column prop="code" label="权限编码" width="160" />
        <el-table-column prop="description" label="描述" min-width="220" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag>{{ row.type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="等级" width="100">
          <template #default="{ row }">
            <el-tag>{{ row.level }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag type="success" v-if="row.status === 'ENABLED'">启用</el-tag>
            <el-tag type="danger" v-else>禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.status === 'ENABLED'"
              type="warning"
              size="small"
              @click="handleStatusChange(row.id, 'DISABLED')"
            >
              禁用
            </el-button>
            <el-button
              v-else
              type="success"
              size="small"
              @click="handleStatusChange(row.id, 'ENABLED')"
            >
              启用
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑权限对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑权限' : '新增权限'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="formData.name" />
        </el-form-item>
        <el-form-item label="权限编码" prop="code">
          <el-input v-model="formData.code" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="权限等级" prop="level">
          <el-select v-model="formData.level" placeholder="请选择权限等级">
            <el-option label="1级" value=1 />
            <el-option label="2级" value=2 />
            <el-option label="3级" value=3 />
          </el-select>
        </el-form-item>
        <el-form-item label="权限描述" prop="description">
          <el-input v-model="formData.description" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="权限类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择权限类型">
            <el-option label="菜单" value="MENU" />
            <el-option label="按钮" value="BUTTON" />
            <el-option label="接口" value="API" />
            <el-option label="数据" value="DATA" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio value=1>启用</el-radio>
            <el-radio value=0>禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * @file PermissionManagementView.vue
 * @description 权限管理页面
 */
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';
import { Search, Plus } from '@element-plus/icons-vue';
import { PermissionService } from '../service';
import type { Permission } from '../types';

// 表格数据
const permissionList = ref<Permission[]>([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchKeyword = ref('');

// 表单数据
const dialogVisible = ref(false);
const isEdit = ref(false);
const formRef = ref<FormInstance>();
const formData = reactive<Partial<Permission>>({
  id: undefined,
  name: '',
  code: '',
  level: 1,
  description: '',
  type: 'API',
  status: 'ENABLED'
});

// 表单验证规则
const formRules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入权限编码', trigger: 'blur' },
    { pattern: /^[A-Z_]+$/, message: '权限编码只能包含大写字母和下划线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
});

/**
 * 获取权限列表
 */
const fetchPermissionList = async () => {
  loading.value = true;
  try {
    const res: any = await PermissionService.getList({
      page: currentPage.value,
      pageSize: pageSize.value,
      name: searchKeyword.value || undefined,
      code: searchKeyword.value || undefined
    });
    permissionList.value = (res.data?.list || []) as Permission[];
    total.value = res.data?.total || 0;
  } catch (error) {
    console.error("获取权限列表失败", error);
    ElMessage.error('获取权限列表失败');
  } finally {
    loading.value = false;
  }
};

/**
 * @description 搜索权限
 */
const handleSearch = () => {
  currentPage.value = 1;
  fetchPermissionList();
};

/**
 * @description 每页数量改变时触发
 * @param {number} size - 每页数量
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  fetchPermissionList();
};

/**
 * @description 当前页码改变时触发
 * @param {number} page - 当前页码
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  fetchPermissionList();
};

/**
 * @description 新增权限
 */
const handleAdd = () => {
  isEdit.value = false;
  dialogVisible.value = true;
  // 重置表单
  formData.id = undefined;
  formData.name = '';
  formData.code = '';
  formData.description = '';
  formData.type = 'API';
  formData.status = 'ENABLED';
};

/**
 * @description 编辑权限
 * @param {Permission} row - 权限信息
 */
const handleEdit = async (row: Permission) => {
  isEdit.value = true;
  dialogVisible.value = true;
  
  try {
    // 获取权限详情
    const res = await PermissionService.getDetail(row.id);
    const permissionData = res.data as Permission;
    // 回显表单
    formData.id = permissionData.id;
    formData.name = permissionData.name;
    formData.code = permissionData.code;
    formData.description = permissionData.description;
    formData.type = permissionData.type;
    formData.status = permissionData.status;
  } catch (error) {
    console.error("获取权限详情失败", error);
    ElMessage.error('获取权限详情失败');
    dialogVisible.value = false;
  }
};

/**
 * @description 提交表单 (新增/编辑)
 */
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (isEdit.value) {
          // 编辑权限
          await PermissionService.update(formData.id as number, formData);
          ElMessage.success('编辑权限成功');
        } else {
          // 新增权限
          await PermissionService.create(formData);
          ElMessage.success('新增权限成功');
        }
        dialogVisible.value = false;
        fetchPermissionList(); // 刷新列表
      } catch (error) {
        console.error(isEdit.value ? "编辑权限失败" : "新增权限失败", error);
        ElMessage.error(isEdit.value ? '编辑权限失败' : '新增权限失败');
      }
    }
  });
};

/**
 * @description 更改权限状态 (启用/禁用)
 * @param {number} id - 权限ID
 * @param {string} status - 目标状态
 */
const handleStatusChange = async (id: number, status: 'ENABLED' | 'DISABLED') => {
  try {
    await PermissionService.update(id, { status });
    ElMessage.success('权限状态更新成功');
    fetchPermissionList(); // 刷新列表
  } catch (error) {
    console.error("权限状态更新失败", error);
    ElMessage.error('权限状态更新失败');
  }
};

/**
 * @description 删除权限
 * @param {Permission} row - 权限信息
 */
const handleDelete = async (row: Permission) => {
  ElMessageBox.confirm(
    `确定要删除权限 ${row.name} 吗?`,
    '删除权限',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        const res: any = await PermissionService.delete(row.id);
        if (res.code === 200) {
          ElMessage.success('删除权限成功');
          fetchPermissionList(); // 刷新列表
        } else {
          ElMessage.error(res.message || '删除权限失败');
        }
      } catch (error) {
        console.error("删除权限失败", error);
        ElMessage.error('删除权限失败');
      }
    })
    .catch(() => {
      ElMessage.info('已取消删除');
    });
};

// 页面加载时获取权限列表
onMounted(() => {
  fetchPermissionList();
});
</script>

<style scoped lang="scss">
.permission-management-view {
  padding: 20px;
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .action-box {
    display: flex;
    align-items: center;
    .search-input {
      width: 250px;
      margin-right: 10px;
    }
  }
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
