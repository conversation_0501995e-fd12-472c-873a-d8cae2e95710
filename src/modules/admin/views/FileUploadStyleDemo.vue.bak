<template>
  <div class="file-upload-style-demo">
    <h1>文件上传组件样式示例</h1>
    
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>默认样式（拖拽上传）</h3>
          <p>适用于需要拖拽上传的场景</p>
        </div>
      </template>
      
      <FileUploader
        action="/v1/admin/upload"
        :file-limit="5"
        :size-limit="5 * 1024 * 1024"
        accept=".jpg,.jpeg,.png,.pdf"
        upload-style="default"
        @success="handleUploadSuccess"
      />
    </el-card>
    
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>按钮样式</h3>
          <p>适用于表单中的文件上传</p>
        </div>
      </template>
      
      <div class="button-style-container">
        <div class="style-item">
          <h4>默认按钮</h4>
          <FileUploader
            action="/v1/admin/upload"
            upload-style="button"
            button-type="primary"
            button-text="上传文件"
            @success="handleUploadSuccess"
          />
        </div>
        
        <div class="style-item">
          <h4>成功按钮</h4>
          <FileUploader
            action="/v1/admin/upload"
            upload-style="button"
            button-type="success"
            button-text="上传图片"
            button-icon="Picture"
            @success="handleUploadSuccess"
          />
        </div>
        
        <div class="style-item">
          <h4>警告按钮</h4>
          <FileUploader
            action="/v1/admin/upload"
            upload-style="button"
            button-type="warning"
            button-text="上传文档"
            button-icon="Document"
            @success="handleUploadSuccess"
          />
        </div>
        
        <div class="style-item">
          <h4>小尺寸按钮</h4>
          <FileUploader
            action="/v1/admin/upload"
            upload-style="button"
            button-type="info"
            button-size="small"
            button-text="上传"
            @success="handleUploadSuccess"
          />
        </div>
        
        <div class="style-item">
          <h4>大尺寸按钮</h4>
          <FileUploader
            action="/v1/admin/upload"
            upload-style="button"
            button-type="danger"
            button-size="large"
            button-text="上传视频"
            button-icon="VideoCamera"
            @success="handleUploadSuccess"
          />
        </div>
      </div>
    </el-card>
    
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>链接样式</h3>
          <p>适用于需要轻量级上传入口的场景</p>
        </div>
      </template>
      
      <div class="link-style-container">
        <div class="style-item">
          <h4>默认链接</h4>
          <FileUploader
            action="/v1/admin/upload"
            upload-style="link"
            link-type="primary"
            link-text="点击上传文件"
            @success="handleUploadSuccess"
          />
        </div>
        
        <div class="style-item">
          <h4>成功链接</h4>
          <FileUploader
            action="/v1/admin/upload"
            upload-style="link"
            link-type="success"
            link-text="点击上传图片"
            link-icon="Picture"
            @success="handleUploadSuccess"
          />
        </div>
        
        <div class="style-item">
          <h4>警告链接</h4>
          <FileUploader
            action="/v1/admin/upload"
            upload-style="link"
            link-type="warning"
            link-text="点击上传文档"
            link-icon="Document"
            @success="handleUploadSuccess"
          />
        </div>
        
        <div class="style-item">
          <h4>危险链接</h4>
          <FileUploader
            action="/v1/admin/upload"
            upload-style="link"
            link-type="danger"
            link-text="点击上传视频"
            link-icon="VideoCamera"
            @success="handleUploadSuccess"
          />
        </div>
      </div>
    </el-card>
    
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>实际应用场景</h3>
          <p>在表单中使用不同样式的上传组件</p>
        </div>
      </template>
      
      <el-form :model="formData" label-width="100px">
        <el-form-item label="用户头像">
          <FileUploader
            action="/v1/admin/upload"
            upload-style="button"
            button-type="primary"
            button-text="上传头像"
            button-icon="User"
            :file-limit="1"
            accept=".jpg,.jpeg,.png"
            @success="handleAvatarUpload"
          />
          <div v-if="formData.avatar" class="avatar-preview">
            <img :src="formData.avatar" alt="头像预览" />
          </div>
        </el-form-item>
        
        <el-form-item label="身份证明">
          <FileUploader
            action="/v1/admin/upload"
            upload-style="link"
            link-type="primary"
            link-text="上传身份证明文件"
            link-icon="Document"
            accept=".pdf,.jpg,.jpeg,.png"
            @success="handleIdCardUpload"
          />
          <div v-if="formData.idCard" class="file-info-text">
            已上传: {{ formData.idCard }}
          </div>
        </el-form-item>
        
        <el-form-item label="相关附件">
          <FileUploader
            action="/v1/admin/upload"
            upload-style="default"
            :multiple="true"
            :file-limit="5"
            @success="handleAttachmentsUpload"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">提交表单</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { FileUploader } from '@/components/common';
import { Upload, Document, Picture, VideoCamera, User } from '@element-plus/icons-vue';

// 表单数据
const formData = ref({
  avatar: '',
  idCard: '',
  attachments: []
});

// 处理上传成功
const handleUploadSuccess = (response: any) => {
  ElMessage.success(`文件 ${response.filename} 上传成功`);
};

// 处理头像上传
const handleAvatarUpload = (response: any) => {
  formData.value.avatar = response.url;
  ElMessage.success('头像上传成功');
};

// 处理身份证明上传
const handleIdCardUpload = (response: any) => {
  formData.value.idCard = response.filename;
  ElMessage.success('身份证明上传成功');
};

// 处理附件上传
const handleAttachmentsUpload = (response: any, _file: any, fileList: any[]) => {
  formData.value.attachments = fileList
    .filter(item => item.status === 'success')
    .map(item => ({
      id: item.id,
      name: item.name,
      url: item.url
    }));
  ElMessage.success(`附件 ${response.filename} 上传成功`);
};

// 提交表单
const submitForm = () => {
  ElMessage.success('表单提交成功');
  console.log('表单数据:', formData.value);
};
</script>

<style scoped lang="scss">
.file-upload-style-demo {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 24px;
    color: $text-primary;
  }
}

.demo-card {
  margin-bottom: 20px;
  
  .card-header {
    h3 {
      margin: 0 0 5px 0;
      font-size: 18px;
      color: $text-primary;
    }
    
    p {
      margin: 0;
      font-size: 14px;
      color: $text-secondary;
    }
  }
}

.button-style-container,
.link-style-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.style-item {
  margin-bottom: 15px;
  
  h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: $text-primary;
  }
}

.avatar-preview {
  margin-top: 10px;
  
  img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid #eee;
  }
}

.file-info-text {
  margin-top: 10px;
  font-size: 14px;
  color: $text-secondary;
}
</style>