<template>
  <div class="file-upload-demo">
    <h1>文件上传组件示例</h1>
    
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>单文件上传</h3>
          <p>适用于头像、单张图片等场景</p>
        </div>
      </template>
      
      <FileUploader
        action="/v1/admin/upload"
        :file-limit="1"
        :size-limit="5 * 1024 * 1024"
        accept=".jpg,.jpeg,.png,.gif"
        file-usage="avatar"
        @success="handleSingleUploadSuccess"
      />
      
      <div v-if="singleFileUrl" class="preview-area">
        <h4>上传结果预览：</h4>
        <img :src="singleFileUrl" class="preview-image" />
      </div>
    </el-card>
    
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>多文件上传</h3>
          <p>适用于相册、文档批量上传等场景</p>
        </div>
      </template>
      
      <FileUploader
        action="/v1/admin/upload"
        :multiple="true"
        :file-limit="5"
        :size-limit="10 * 1024 * 1024"
        accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.png"
        file-usage="document"
        @success="handleMultiUploadSuccess"
      />
      
      <div v-if="multiFileList.length > 0" class="preview-area">
        <h4>上传文件列表：</h4>
        <el-table :data="multiFileList" style="width: 100%">
          <el-table-column prop="filename" label="文件名" />
          <el-table-column prop="size" label="大小" :formatter="formatSize" width="120" />
          <el-table-column prop="mime_type" label="类型" width="120" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button link @click="previewFile(scope.row)">预览</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>手动上传模式</h3>
          <p>适用于需要在表单提交时一并上传文件的场景</p>
        </div>
      </template>
      
      <FileUploader
        ref="manualUploaderRef"
        action="/v1/admin/upload"
        :auto-upload="false"
        file-usage="manual"
      />
      
      <div class="action-buttons">
        <el-button type="primary" @click="submitManualUpload">开始上传</el-button>
        <el-button @click="clearFiles">清空文件</el-button>
      </div>
    </el-card>
    
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>自定义上传提示</h3>
          <p>适用于需要自定义上传区域提示文本的场景</p>
        </div>
      </template>
      
      <FileUploader action="/v1/admin/upload" file-usage="custom">
        <template #tip>
          <p><el-icon><Upload /></el-icon> 点击或拖拽图片到此处上传</p>
          <p class="custom-tip">仅支持JPG、PNG格式，文件大小不超过2MB</p>
          <p class="custom-tip">建议尺寸：1080 x 1080像素</p>
        </template>
      </FileUploader>
    </el-card>
    
    <!-- 文件预览对话框 -->
    <el-dialog v-model="previewDialogVisible" title="文件预览" width="60%">
      <div class="preview-dialog-content">
        <template v-if="currentPreviewFile && isImage(currentPreviewFile.mime_type)">
          <img :src="currentPreviewFile.url" class="preview-dialog-image" />
        </template>
        <template v-else>
          <div class="non-image-preview">
            <el-icon class="preview-icon"><Document /></el-icon>
            <p>{{ currentPreviewFile?.filename }}</p>
            <el-button type="primary" @click="downloadFile(currentPreviewFile)">下载文件</el-button>
          </div>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { Upload, Document } from '@element-plus/icons-vue';
import { FileUploader } from '@/components/common';

// 单文件上传结果
const singleFileUrl = ref('');

// 多文件上传结果
const multiFileList = ref<any[]>([]);

// 手动上传相关
const manualUploaderRef = ref<any>(null);

// 预览相关
const previewDialogVisible = ref(false);
const currentPreviewFile = ref<any>(null);

// 处理单文件上传成功
const handleSingleUploadSuccess = (response: any) => {
  ElMessage.success('文件上传成功');
  singleFileUrl.value = response.url;
};

// 处理多文件上传成功
const handleMultiUploadSuccess = (response: any, _file: any, fileList: any[]) => {
  ElMessage.success(`文件 ${response.filename} 上传成功`);
  
  // 更新文件列表
  multiFileList.value = fileList.map(item => {
    if (item.status === 'success' && item.id) {
      return {
        id: item.id,
        filename: item.name,
        size: item.size,
        mime_type: item.mime_type,
        url: item.url
      };
    }
    return null;
  }).filter(Boolean);
};

// 手动提交上传
const submitManualUpload = () => {
  if (manualUploaderRef.value) {
    manualUploaderRef.value.submit();
    ElMessage.info('开始上传文件');
  }
};

// 清空文件列表
const clearFiles = () => {
  if (manualUploaderRef.value) {
    manualUploaderRef.value.clearFiles();
    ElMessage.info('文件列表已清空');
  }
};

// 预览文件
const previewFile = (file: any) => {
  currentPreviewFile.value = file;
  previewDialogVisible.value = true;
};

// 下载文件
const downloadFile = (file: any) => {
  if (!file || !file.url) return;
  
  const link = document.createElement('a');
  link.href = file.url;
  link.download = file.filename;
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 格式化文件大小
const formatSize = (row: any) => {
  const size = row.size;
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }
};

// 检查是否为图片
const isImage = (mimeType: string) => {
  return mimeType && mimeType.startsWith('image/');
};
</script>

<style scoped lang="scss">
.file-upload-demo {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 24px;
    color: $text-primary;
  }
}

.demo-card {
  margin-bottom: 20px;
  
  .card-header {
    h3 {
      margin: 0 0 5px 0;
      font-size: 18px;
      color: $text-primary;
    }
    
    p {
      margin: 0;
      font-size: 14px;
      color: $text-secondary;
    }
  }
}

.preview-area {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  
  h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: $text-primary;
  }
}

.preview-image {
  max-width: 300px;
  max-height: 300px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.action-buttons {
  margin-top: 20px;
}

.custom-tip {
  font-size: 12px;
  color: #909399;
  margin: 4px 0;
}

.preview-dialog-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-dialog-image {
  max-width: 100%;
  max-height: 500px;
}

.non-image-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  
  .preview-icon {
    font-size: 48px;
    color: #909399;
    margin-bottom: 16px;
  }
  
  p {
    margin: 10px 0 20px;
    font-size: 16px;
    color: $text-primary;
  }
}
</style>