/**
 * 文件：dynamicGridConfigStore.ts
 * 职责：管理动态网格配置的状态，提供响应式数据和业务逻辑
 * 主要功能：配置数据管理、网格项操作、状态同步
 * 作者：张二浩
 * 创建时间：2025-04-22
 * 更新时间：2025-04-24 添加本地缓存支持
 * 动态网格配置状态管理，使用Pinia实现
 */

import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import { DynamicGridConfigService } from './DynamicGridConfigService';
import { createGridLayoutStore } from '@/components/page/gridLayoutStore';
import { createGridComponentStore } from '@/components/page/gridComponentStore';
import type { PageData, ServiceConfig } from '../../types';
import { ElMessage } from 'element-plus';
import type { StructureData } from '@/components/page/gridLayoutStore';
import { parseGridItemsFromBackend } from '@/components/page/gridLayoutStore';
// 导入缓存服务
import { saveStructureData, getStructureData, checkPageVersionUpdate } from '@/services/structureDataCacheService';

// 创建服务实例
const service = new DynamicGridConfigService();

/**
 * 自定义GridStackNode类型
 */
interface CustomGridStackNode {
  x: number;
  y: number;
  w: number;
  h: number;
  i?: number | string;
  content?: any;
  [key: string]: any;
}

/**
 * 定义状态存储
 */
export const useDynamicGridConfigStore = defineStore('dynamicGridConfig', () => {
  // ===================== 状态定义 =====================
  
  // 页面状态
  const loading = ref(true);
  const error = ref(false);
  const errorMessage = ref('');
  const gridKey = ref(Date.now()); // 用于强制刷新组件
  const debug = ref(false); // 调试模式
  
  // 默认Grid服务配置
  const defaultServiceConfig: ServiceConfig = {
    baseUrl: '/api/grid',
    apiPrefix: '/api',
    addTitle: '新增',
    viewTitle: '查看',
    title: '网格管理',
    version: '1.0.0',
    customActions: {},
    gridOptions: {
      column: 12,
      cellHeight: 50,
      margin: 10
    },
    messages: {
      addSuccess: '新增成功',
      deleteSuccess: '删除成功',
      deleteConfirm: '确定删除吗？',
      editSuccess: '更新成功'
    },
    resizable: true,
    draggable: true
  };
  
  // 业务数据 - 默认页面配置
  const defaultPageConfig: PageData = {
    id: 0,
    config_key: 'default_key',
    config_type: 'page',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    draggable: true,
    resizable: true,
    status: 1,
    title: '默认页面',
    version: '1.0.0',
    version_hash: '',
    remark: '',
    module: '',
    group: '',
    icon: '',
    frontend_path: '',
    config_content: {
      frontendPath: '',
      icon: '',
      moduleName: '',
      serviceConfig: defaultServiceConfig
    },
    dto: {
      id: 0,
      module: '',
      name: '',
      description: '',
      type: '',
      structure: [],
      created_at: '',
      updated_at: ''
    },
    grid_items: []
  };
  
  // 页面配置
  const pageConfig = ref<PageData>({...defaultPageConfig});
  
  // 实例化 gridLayoutStore
  const gridLayoutStore = createGridLayoutStore(defaultServiceConfig);
  
  // 初始化structureData - 这是GridLayout组件需要的关键数据结构
  // 我们需要确保它与PageData结构一致，特别是config_content字段
  const emptyStructureData: StructureData = {
    ...defaultPageConfig,
    layout: []
  };
  
  // 设置structureData到gridLayoutStore
  gridLayoutStore.structureData = ref<StructureData>(emptyStructureData);
  
  // 路径和配置信息
  const pathConfig = ref<Record<string, any>>({});
  const configId = ref('');
  const routePath = ref('');
  
  // ===================== 计算属性 =====================
  
  /**
   * GridLayoutStore服务配置
   */
  const serviceConfig = computed(() => {
    return gridLayoutStore.serviceConfig.value;
  });
  
  /**
   * 缓存键参数
   */
  const cacheKeyParams = computed(() => {
    return {
      configId: configId.value,
      frontendPath: routePath.value
    };
  });
  
  // ===================== 方法定义 =====================
  
  /**
   * 设置配置ID和路径配置
   */
  function setPathInfo(id: string, path: Record<string, any> = {}, currentPath: string = '') {
    configId.value = id;
    pathConfig.value = path;
    routePath.value = currentPath;
  }
  
  /**
   * 从页面配置更新structureData
   * 这是关键方法，将PageData转换为GridLayout需要的结构
   */
  function updateStructureDataFromPageConfig() {
    if (!pageConfig.value) return;
    
    // 转换网格项为前端可用的布局项
    const layoutItems = parseGridItemsFromBackend(pageConfig.value.grid_items || []);
    
    // 更新structureData，确保包含所有PageData字段，特别是config_content
    gridLayoutStore.structureData.value = {
      ...pageConfig.value,
      layout: layoutItems
    };
    
    console.log('更新structureData完成:', gridLayoutStore.structureData.value);
  }
  
  /**
   * 初始化页面配置
   * 流程：
   * 1. 重置页面状态
   * 2. 尝试从后端获取配置
   * 3. 更新页面配置
   * 4. 处理可能的错误
   */
  async function initPageConfig() {
    loading.value = true;
    error.value = false;
    errorMessage.value = '';
    
    try {
      console.log(`开始初始化页面配置: ${configId.value}，路径配置:`, pathConfig.value);
      
      // 查询参数
      const queryParams = {
        configId: configId.value,
        configKey: pathConfig.value.configKey,
        configType: pathConfig.value.configType || 'grid',
        group: pathConfig.value.group,
        versionHash: pathConfig.value.versionHash
      };
      
      // 1. 尝试从后端获取配置
      let config;
      try {
        config = await service.fetchRemoteConfig(configId.value, queryParams);
      } catch (err) {
        // 如果获取远程配置失败，尝试从本地缓存加载
        config = await service.loadLocalConfig(cacheKeyParams.value);
        if (!config) {
          // 如果本地缓存也没有，则使用默认配置
          config = service.createDefaultConfig(configId.value, pathConfig.value, routePath.value);
        }
      }
      
      if (!config) {
        throw new Error('获取配置失败，未返回有效数据');
      }
      
      // 2. 保存到页面配置
      pageConfig.value = config;
      
      // 3. 更新serviceConfig
      if (config.config_content?.serviceConfig) {
        gridLayoutStore.serviceConfig.value = config.config_content.serviceConfig;
      }
      
      // 4. 关键：更新structureData
      await setStructureDataFromBackend(config);
      
      // 5. 刷新网格布局组件
      refreshGridLayout();
      
      console.log('页面配置初始化完成', config);
    } catch (err: any) {
      console.error('初始化页面配置失败:', err);
      error.value = true;
      
      // 设置更详细的错误信息
      if (err.response) {
        errorMessage.value = `服务器错误(${err.response.status}): ${err.message}`;
      } else if (err.request) {
        errorMessage.value = '网络请求失败，请检查网络连接';
      } else {
        errorMessage.value = err.message || '加载配置失败，请重试';
      }
    } finally {
      // 无论是否成功，都结束加载状态
      loading.value = false;
    }
  }
  
  /**
   * 初始化页面配置 (带缓存支持)
   * 流程：
   * 1. 尝试从本地缓存加载
   * 2. 如果需要更新（版本变更或无缓存），从后端获取
   * 3. 更新页面配置和缓存
   * 4. 处理可能的错误
   */
  async function initPageConfigWithCache(frontendPath: string) {
    loading.value = true;
    error.value = false;
    errorMessage.value = '';
    
    try {
      console.log(`开始初始化页面配置(带缓存): ${configId.value}，路径: ${frontendPath}`);
      
      // 查询参数
      const queryParams = {
        configId: configId.value,
        configKey: pathConfig.value.configKey,
        configType: pathConfig.value.configType || 'grid',
        group: pathConfig.value.group,
        versionHash: pathConfig.value.versionHash
      };
      
      // 1. 尝试从缓存加载
      let config: PageData | null = null;
      let cachedData = await getStructureData(frontendPath);
      
      if (cachedData) {
        // 如果有缓存数据，先使用缓存数据
        console.log('从缓存加载配置数据:', cachedData);
        config = cachedData;
        
        // 检查是否需要更新
        const needUpdate = await checkPageVersionUpdate(frontendPath, cachedData);
        
        if (needUpdate) {
          console.log('缓存数据需要更新，从服务器获取最新数据');
          try {
            // 从后端获取最新配置
            const remoteConfig = await service.fetchRemoteConfig(configId.value, queryParams);
            if (remoteConfig) {
              config = remoteConfig;
              // 更新缓存
              await saveStructureData(frontendPath, config);
              console.log('缓存已更新');
            }
          } catch (err) {
            console.warn('从服务器更新配置失败，使用缓存数据:', err);
            // 继续使用缓存数据
          }
        }
      } else {
        // 如果没有缓存，从后端获取
        console.log('无缓存数据，从服务器获取');
        try {
          config = await service.fetchRemoteConfig(configId.value, queryParams);
          if (config) {
            // 保存到缓存
            await saveStructureData(frontendPath, config);
            console.log('配置已保存到缓存');
          }
        } catch (err) {
          console.error('从服务器获取配置失败:', err);
          // 创建默认配置
          config = service.createDefaultConfig(configId.value, pathConfig.value, routePath.value);
          // 保存默认配置到缓存
          await saveStructureData(frontendPath, config);
        }
      }
      
      if (!config) {
        throw new Error('获取配置失败，未返回有效数据');
      }
      
      // 2. 保存到页面配置
      pageConfig.value = config;
      
      // 3. 更新serviceConfig
      if (config.config_content?.serviceConfig) {
        gridLayoutStore.serviceConfig.value = config.config_content.serviceConfig;
      }
      
      // 4. 关键：更新structureData
      await setStructureDataFromBackend(config);
      
      // 5. 刷新网格布局组件
      refreshGridLayout();
      
      console.log('页面配置初始化完成', config);
    } catch (err: any) {
      console.error('初始化页面配置失败:', err);
      error.value = true;
      
      // 设置更详细的错误信息
      if (err.response) {
        errorMessage.value = `服务器错误(${err.response.status}): ${err.message}`;
      } else if (err.request) {
        errorMessage.value = '网络请求失败，请检查网络连接';
      } else {
        errorMessage.value = err.message || '加载配置失败，请重试';
      }
    } finally {
      // 无论是否成功，都结束加载状态
      loading.value = false;
    }
  }

  /**
   * 清除页面配置缓存
   * 删除指定前端路径对应的结构数据缓存
   * @param frontendPath 前端路径
   */
  async function clearCacheData(frontendPath: string) {
    try {
      console.log(`清除页面缓存: 路径: ${frontendPath}`);
      
      // 导入structureDataCacheService中的清除缓存方法
      const { clearStructureDataCache } = await import('@/services/structureDataCacheService');
      
      // 使用正确的缓存清除方法
      await clearStructureDataCache(frontendPath);
      
      console.log(`缓存已清除: 路径 ${frontendPath}`);
      return true;
    } catch (err) {
      console.error('清除缓存失败:', err);
      return false;
    }
  }
  
  /**
   * 设置StructureData
   * 模拟gridLayoutStore的setStructureDataFromBackend方法
   */
  async function setStructureDataFromBackend(pageData: PageData) {
    console.log('设置structureData, 源数据:', pageData);
    
    // 确保有grid_items数组
    const gridItems = pageData.grid_items || [];
    
    // 确保pageData.config_content存在
    if (!pageData.config_content) {
      pageData.config_content = defaultPageConfig.config_content;
    }
    
    // 确保config_content.serviceConfig存在
    if (!pageData.config_content.serviceConfig) {
      pageData.config_content.serviceConfig = defaultServiceConfig;
    }

    // 创建componentStore实例，确保能关联到gridLayoutStore
    console.log('[DynamicGridConfigStore] 创建componentStore实例');
    
    try {
      // 创建符合UiPageConfig类型的配置对象
      const uiPageConfig = {
        frontendPath: pageData.frontend_path,
        icon: pageData.icon || '',
        moduleName: pageData.module || '',
        serviceConfig: gridLayoutStore.getServiceConfig()
      };
      
      // 创建componentStore并传递给gridLayoutStore
      const componentStore = createGridComponentStore(gridLayoutStore.getService(), uiPageConfig);
      
      // 关联componentStore到gridLayoutStore
      gridLayoutStore.connectComponentStore(componentStore);
      console.log('[DynamicGridConfigStore] componentStore已关联到gridLayoutStore');
    } catch (error) {
      console.error('[DynamicGridConfigStore] 创建componentStore失败:', error);
    }
    
    // 更新structureData，包含所有PageData属性，特别是config_content
    gridLayoutStore.structureData.value = {
      ...pageData,
      layout: parseGridItemsFromBackend(gridItems)
    };
    
    // 设置网格项
    gridLayoutStore.setGridItems(gridItems);
    
    console.log('设置structureData完成, 结果:', gridLayoutStore.structureData.value);
    return gridLayoutStore.structureData.value;
  }
  
  /**
   * 使用默认配置
   */
  function useDefaultConfig() {
    // 显示加载状态
    loading.value = true;
    
    try {
      // 重置错误状态
      error.value = false;
      errorMessage.value = '';
      
      // 创建默认配置
      const defaultConfig = service.createDefaultConfig(configId.value, pathConfig.value, routePath.value);
      console.log('使用默认配置:', defaultConfig);
      
      // 更新页面配置
      pageConfig.value = defaultConfig;
      
      // 更新structureData
      setStructureDataFromBackend(defaultConfig);
      
      // 保存到本地存储
      service.saveLocalConfig(cacheKeyParams.value, defaultConfig, pathConfig.value);
      
      // 刷新网格布局组件
      refreshGridLayout();
      
      console.log('默认配置已应用并保存到本地存储');
    } catch (err) {
      console.error('应用默认配置失败:', err);
      error.value = true;
      errorMessage.value = '应用默认配置失败';
    } finally {
      // 结束加载状态
      loading.value = false;
    }
  }
  
  /**
   * 刷新网格布局
   */
  function refreshGridLayout() {
    gridKey.value = Date.now();
  }
  
  /**
   * 重新加载配置
   */
  function reloadConfig() {
    refreshGridLayout();
    initPageConfig();
  }
  
  /**
   * 处理网格变化事件
   */
  async function handleGridChange(items: CustomGridStackNode[]) {
    console.log('网格变化:', items);
    
    // 确保有配置
    if (!pageConfig.value) return;
    
    // 使用服务层更新网格项位置
    const updatedItems = service.updateGridItemsPosition(items, pageConfig.value.grid_items || []);
    
    // 更新页面配置中的网格项
    pageConfig.value.grid_items = updatedItems;
    
    // 更新structureData
    updateStructureDataFromPageConfig();
    
    // 保存到本地缓存
    if (routePath.value) {
      try {
        await saveStructureData(routePath.value, pageConfig.value);
        console.log('更新的布局已保存到缓存');
      } catch (err) {
        console.error('保存到缓存失败:', err);
      }
    }
    
    ElMessage.success('网格布局已更新');
  }
  
  /**
   * 删除网格项
   * @param itemId 要删除的网格项ID
   */
  async function deleteGridItem(itemId: string | number) {
    console.log('删除网格项:', itemId);
    
    // 确保有配置
    if (!pageConfig.value || !pageConfig.value.grid_items) return;
    
    // 找到要删除的项目索引
    const index = pageConfig.value.grid_items.findIndex(item => 
      item.id === itemId || String(item.id) === String(itemId)
    );
    
    if (index === -1) {
      console.warn('未找到指定的网格项:', itemId);
      return;
    }
    
    // 删除网格项
    pageConfig.value.grid_items.splice(index, 1);
    
    // 更新structureData
    updateStructureDataFromPageConfig();
    
    // 将更改保存到缓存
    if (routePath.value) {
      try {
        await saveStructureData(routePath.value, pageConfig.value);
        console.log('删除后的配置已保存到缓存');
      } catch (err) {
        console.error('保存到缓存失败:', err);
      }
    }
    
    // 刷新布局
    refreshGridLayout();
    
    ElMessage.success('网格项已删除');
  }
  
  /**
   * 设置调试模式
   */
  function setDebugMode(isDebug: boolean) {
    debug.value = isDebug;
  }
  
  /**
   * 设置页面配置
   */
  function setConfig(config: PageData) {
    if (!config) return;
    
    pageConfig.value = config;
    
    // 更新structureData
    setStructureDataFromBackend(config);
    
    refreshGridLayout();
  }
  
  /**
   * 缓存管理
   */
  async function saveToCacheIfNeeded() {
    if (routePath.value && pageConfig.value) {
      try {
        console.log('保存前的原始配置:', pageConfig.value);
        
        // 创建一个深拷贝，移除不可序列化的内容
        const safeConfig = JSON.parse(JSON.stringify(pageConfig.value));
        
        // 确保grid_items也是安全可序列化的
        if (safeConfig.grid_items && Array.isArray(safeConfig.grid_items)) {
          console.log('处理前的网格项数组:', safeConfig.grid_items);
          
          // 清理每个网格项的不安全属性
          safeConfig.grid_items = safeConfig.grid_items.map((item: any) => {
            // 定义安全网格项类型
            interface SafeGridItem {
              id: any;
              ui_config_id?: any;
              name: any;
              x: number;
              y: number; 
              w: number;
              h: number;
              minW?: number;
              minH?: number;
              maxW?: number;
              maxH?: number;
              i: string | number;
              step?: any;
              permission?: any;
              created_at: any;
              updated_at: any;
              content?: any;
              position?: any;
            }
            
            // 确保位置信息是数字
            const x = typeof item.x === 'number' ? item.x : parseInt(item.x || '0', 10);
            const y = typeof item.y === 'number' ? item.y : parseInt(item.y || '0', 10);
            const w = typeof item.w === 'number' ? item.w : parseInt(item.w || '1', 10);
            const h = typeof item.h === 'number' ? item.h : parseInt(item.h || '1', 10);
            
            // 确保i是有效的字符串或数字
            const i = item.i !== undefined ? item.i : (item.id !== undefined ? item.id : '');
            
            // 只保留必要的属性，移除可能的循环引用
            const safeItem: SafeGridItem = {
              id: item.id,
              ui_config_id: item.ui_config_id,
              name: item.name,
              x, // 明确使用处理后的数字值
              y, 
              w,
              h,
              minW: item.minW,
              minH: item.minH,
              maxW: item.maxW,
              maxH: item.maxH,
              i,   // 明确使用处理后的值
              step: item.step,
              permission: item.permission,
              created_at: item.created_at,
              updated_at: item.updated_at
            };
            
            // 显式保存位置信息到position字段，以防万一
            safeItem.position = {
              x, y, w, h, i
            };
            
            // 安全处理content对象
            if (item.content) {
              try {
                // 尝试序列化和反序列化content
                safeItem.content = JSON.parse(JSON.stringify(item.content));
                
                // 确保位置信息也可以从content获取（如果存在）
                if (!safeItem.x && safeItem.content.x) safeItem.x = safeItem.content.x;
                if (!safeItem.y && safeItem.content.y) safeItem.y = safeItem.content.y;
                if (!safeItem.w && safeItem.content.w) safeItem.w = safeItem.content.w;
                if (!safeItem.h && safeItem.content.h) safeItem.h = safeItem.content.h;
              } catch (e) {
                console.warn('忽略不可序列化的content属性:', e);
                // 创建一个简单的替代内容
                safeItem.content = { 
                  type: item.content?.type || 'unknown',
                  title: item.content?.title || '未命名组件'
                };
              }
            }
            
            console.log(`处理后的网格项 ${safeItem.id || safeItem.i}: x=${safeItem.x}, y=${safeItem.y}, w=${safeItem.w}, h=${safeItem.h}`);
            return safeItem;
          });
          
          console.log('处理后的网格项数组:', safeConfig.grid_items);
        }
        
        // 保存安全处理后的配置
        await saveStructureData(routePath.value, safeConfig);
        console.log('配置已安全保存到缓存:', routePath.value);
      } catch (err) {
        console.error('保存到缓存失败:', err);
      }
    }
  }
  
  /**
   * 重置 store 状态
   * 在路由切换或组件卸载时调用，确保状态正确清理
   */
  function resetState() {
    console.log('🔄 重置 dynamicGridConfigStore 状态');
    
    try {
      // 重置基本状态
      loading.value = true;
      error.value = false;
      errorMessage.value = '';
      debug.value = false;
      
      // 重置配置数据
      pageConfig.value = {...defaultPageConfig};
      pathConfig.value = {};
      configId.value = '';
      routePath.value = '';
      
      // 重置网格布局 store
      if (gridLayoutStore && gridLayoutStore.structureData) {
        gridLayoutStore.structureData.value = {...emptyStructureData};
      }
      
      // 更新 gridKey 以触发组件重新渲染
      gridKey.value = Date.now();
      
      console.log('✅ dynamicGridConfigStore 状态重置完成');
    } catch (error) {
      console.error('❌ 重置 dynamicGridConfigStore 状态失败:', error);
    }
  }
  
  // 返回状态和方法
  return {
    // 状态
    loading,
    error,
    errorMessage,
    gridKey,
    debug,
    pageConfig,
    configId,
    pathConfig,
    routePath,
    gridLayoutStore,
    serviceConfig,
    cacheKeyParams,
    // 操作方法
    setPathInfo,
    updateStructureDataFromPageConfig,
    initPageConfig,
    initPageConfigWithCache,
    setStructureDataFromBackend,
    useDefaultConfig,
    refreshGridLayout,
    reloadConfig,
    handleGridChange,
    setDebugMode,
    setConfig,
    deleteGridItem,
    saveToCacheIfNeeded,
    clearCacheData, // 添加清除缓存方法
    resetState // 添加重置状态方法
  };
});
