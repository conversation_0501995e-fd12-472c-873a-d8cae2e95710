/**
 * 文件：DynamicGridConfigService.ts
 * 职责：封装所有动态网格配置相关的后端API调用和数据处理
 * 主要功能：提供配置数据的获取、缓存和处理
 * 作者：张二浩
 * 创建时间：2025-04-22
 * 更新时间：2025-04-22
 * 动态网格配置服务层，处理API请求和数据转换，不存储状态
 */

import { ElMessage } from 'element-plus';
import { getConfigById } from '../../api/uiConfig';
import type { PageData, AdminGridInfoDTO } from '../../types';
import localforage from 'localforage';

/**
 * 生成缓存键的参数类型
 */
interface CacheKeyParams {
  configId: string;
  versionHash?: string;
  [key: string]: any;
}

/**
 * 动态网格配置服务类
 * 负责处理API请求和数据转换，不存储状态
 */
export class DynamicGridConfigService {
  // 缓存过期时间配置（毫秒）
  // private readonly CACHE_EXPIRY = {
  //   SHORT: 300000,    // 5分钟
  //   MEDIUM: 1800000,  // 30分钟
  //   LONG: 3600000     // 1小时
  // };

  /**
   * 构造函数
   */
  constructor() {
    // 初始化逻辑如果需要
  }

  /**
   * 生成缓存键
   * @param baseKey 基础键名
   * @param params 参数对象
   * @returns 完整缓存键
   */
  private generateCacheKey(params: CacheKeyParams): string {
    // 使用configId和versionHash组合作为缓存键，确保版本更新时自动失效
    const versionPart = params.versionHash ? `-${params.versionHash}` : '';
    return `grid_config_${params.configId}${versionPart}`;
  }

  /**
   * 从本地存储加载配置
   * @param params 缓存键参数
   * @returns 配置数据或null
   */
  async loadLocalConfig(params: CacheKeyParams): Promise<PageData | null> {
    try {
      const cacheKey = this.generateCacheKey(params);
      console.log(`尝试从本地存储加载配置: ${cacheKey}`);
      
      const data = await localforage.getItem(cacheKey);
      
      if (data) {
        console.log(`从本地存储加载配置成功:`, data);
        return data as PageData;
      } else {
        console.log(`本地存储中不存在配置: ${cacheKey}`);
        return null;
      }
    } catch (error) {
      console.error(`从本地存储加载配置失败:`, error);
      return null;
    }
  }

  /**
   * 保存配置到本地存储
   * @param params 缓存键参数
   * @param config 页面配置
   * @param pathConfig 路径配置
   * @returns 保存是否成功
   */
  async saveLocalConfig(params: CacheKeyParams, config: PageData, pathConfig?: any): Promise<boolean> {
    try {
      const cacheKey = this.generateCacheKey(params);
      
      // 将pathConfig信息合并到配置中，方便后续使用
      const enrichedConfig = {
        ...config,
        _pathConfig: pathConfig || {},
        _lastUpdated: new Date().toISOString()
      };
      
      console.log(`保存配置到本地存储: ${cacheKey}`, enrichedConfig);
      await localforage.setItem(cacheKey, enrichedConfig);
      return true;
    } catch (error) {
      console.error(`保存配置到本地存储失败:`, error);
      return false;
    }
  }

  /**
   * 从后端API获取配置
   * @param configId 配置ID
   * @param queryParams 查询参数
   * @returns 配置数据
   */
  async fetchRemoteConfig(configId: string, queryParams: any = {}): Promise<PageData | null> {
    try {
      console.log(`开始获取页面配置，ID: ${configId}`);
      
      // 请求后端获取配置
      const response: any = await getConfigById(configId, queryParams);
      
      if (!response || !response.list || response.list.length === 0) {
        console.log('未找到远程配置');
        return null;
      }
      
      // 解析配置内容
      response.list.forEach((item: any) => {
        item.config_content = typeof item.config_content === 'string' 
          ? JSON.parse(item.config_content) 
          : item.config_content;
        
        item.dto = typeof item.dto === 'string' 
          ? JSON.parse(item.dto) 
          : item.dto;
        
        item.grid_items?.forEach((gridItem: AdminGridInfoDTO) => {
          gridItem.position = typeof gridItem.position === 'string' 
            ? JSON.parse(gridItem.position) 
            : gridItem.position;
          
          gridItem.content = typeof gridItem.content === 'string' 
            ? JSON.parse(gridItem.content) 
            : gridItem.content;
        });
      });
      
      const config = response.list[0];
      console.log('获取到页面配置数据:', config);
      
      // 保存到本地缓存
      await this.saveLocalConfig(
        { configId, ...queryParams },
        config
      );
      
      return config;
    } catch (error) {
      console.error('获取页面配置失败:', error);
      throw error;
    }
  }

  /**
   * 创建默认配置
   * @param configId 配置ID
   * @param pathConfig 路径配置
   * @param routePath 当前路由路径
   * @returns 默认配置
   */
  createDefaultConfig(configId: string, pathConfig: any = {}, routePath: string = ''): PageData {
    const title = pathConfig.title || '默认页面';
    const baseUrl = `/api/${configId}`;
    
    return {
      id: 0,
      config_key: configId,
      config_type: 'page',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      draggable: true,
      resizable: true,
      status: 1,
      title: title,
      version: '1.0.0',
      version_hash: '',
      remark: '自动创建的默认配置',
      module: pathConfig.module || '',
      group: pathConfig.group || '',
      icon: pathConfig.icon || '',
      frontend_path: pathConfig.frontendPath || routePath,
      config_content: {
        frontendPath: pathConfig.frontendPath || routePath,
        icon: pathConfig.icon || '',
        moduleName: pathConfig.module || '',
        serviceConfig: {
          addTitle: '新增',
          apiPrefix: '',
          baseUrl: baseUrl,
          customActions: {},
          gridOptions: {
            cellHeight: 80,
            column: 12,
            margin: 10
          },
          messages: {
            addSuccess: '创建成功',
            deleteConfirm: '确定要删除吗？',
            deleteSuccess: '删除成功',
            editSuccess: '更新成功'
          },
          resizable: true,
          draggable: true,
          viewTitle: '查看',
          title: title,
          version: '1.0.0'
        }
      },
      dto: {
        id: 0,
        module: pathConfig.module || '',
        name: title,
        description: '自动创建的默认配置',
        type: 'grid',
        structure: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      grid_items: []
    };
  }

  /**
   * 更新网格项位置
   * 注意：此为示例方法，若需要同步到后端，需要对接相应API
   * @param gridItems 网格项数组
   * @param originalItems 原始网格项数组
   * @returns 更新后的网格项数组
   */
  updateGridItemsPosition(gridItems: any[], originalItems: AdminGridInfoDTO[]): AdminGridInfoDTO[] {
    // 构建更新的网格项列表
    return gridItems.map(item => {
      // 找到对应的原始网格项
      const originalItem = originalItems?.find(
        gi => gi.id && String(gi.id) === String(item.i)
      );
      
      if (originalItem) {
        // 创建一个新对象，避免直接修改原始对象
        const updatedItem = { ...originalItem };
        
        // 更新位置
        updatedItem.position = {
          ...(updatedItem.position || {}),
          x: item.x,
          y: item.y,
          w: item.w,
          h: item.h,
          i: item.i
        };
        
        return updatedItem;
      }
      
      // 如果找不到对应项，创建一个基本的网格项结构
      return {
        id: Number(item.i),
        name: `网格项 ${item.i}`,
        remark: '自动创建的网格项',
        status: 1,
        api: '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        position: {
          x: item.x,
          y: item.y,
          w: item.w,
          h: item.h,
          i: item.i
        },
        content: item.content || {
          type: 'custom',
          title: `网格项 ${item.i}`,
          icon: '',
          showTitle: true,
          refreshable: true,
          configurable: true,
          editable: true,
          closable: true,
          config: {}
        }
      } as AdminGridInfoDTO;
    });
  }

  /**
   * 保存配置到后端
   * 注意：此为示例方法，需要对接实际的API
   * @param config 配置数据
   * @returns 保存结果
   */
  async saveConfig(config: PageData): Promise<boolean> {
    try {
      // 这里应该调用实际的API来保存配置
      // const response = await saveConfigApi(config);
      
      // 临时模拟保存成功
      console.log('保存配置到后端:', config);
      ElMessage.success('配置保存成功');
      return true;
    } catch (error) {
      console.error('保存配置到后端失败:', error);
      ElMessage.error('配置保存失败');
      return false;
    }
  }
}

// 导出服务实例
export default DynamicGridConfigService;
