<!-- 
  文件：DynamicGridConfigPage.vue
  职责：动态配置网格页面组件
  主要功能：通过路由参数加载不同配置并使用GridPage渲染对应的功能页面
  作者：张二浩
  创建时间：2025-04-22
  更新时间：2025-04-24 添加本地缓存支持
  支持本地缓存、版本控制和配置热更新
-->
<template>
  <div class="dynamic-grid-config-page">
    <el-skeleton :loading="store.loading" animated>
      <template #template>
        <div style="padding: 20px;">
          <el-skeleton-item variant="p" style="width: 100%; height: 60px; margin-bottom: 20px" />
          <div style="display: flex; justify-content: space-between; margin-bottom: 20px">
            <el-skeleton-item variant="button" style="width: 250px; height: 40px;" />
            <el-skeleton-item variant="button" style="width: 150px; height: 40px;" />
          </div>
          <el-skeleton-item variant="p" style="width: 100%; height: 400px" />
        </div>
      </template>
      <template #default>
        <template v-if="!store.error">
          <div class="grid-actions" v-if="!store.loading && store.gridLayoutStore">
            <el-button type="primary" @click="handleRefreshData" size="small" :loading="refreshing">
              <el-icon><RefreshRight /></el-icon> 刷新配置
            </el-button>
          </div>
          <GridLayout
            v-if="!store.loading && store.gridLayoutStore && store.gridLayoutStore.structureData"
            :key="store.gridKey" 
            :store="store.gridLayoutStore"
            @item-click="handleItemClick"
            @item-delete="handleItemDelete"
            @items-updated="handleItemsUpdated"
            ref="gridLayoutRef"
          />
          <div v-else-if="!store.loading && !store.gridLayoutStore" class="empty-state">
            <el-empty description="暂无配置数据">
              <el-button type="primary" @click="store.useDefaultConfig">使用默认配置</el-button>
            </el-empty>
          </div>
        </template>
        <template v-else>
          <el-result
            icon="error"
            title="配置加载失败"
            :sub-title="store.errorMessage"
          >
            <template #extra>
              <el-button type="primary" @click="store.reloadConfig">重试</el-button>
              <el-button @click="store.useDefaultConfig">使用默认配置</el-button>
            </template>
          </el-result>
        </template>
      </template>
    </el-skeleton>

    <!-- 调试信息 -->
    <div v-if="store.debug" class="debug-info">
      <p><strong>配置ID:</strong> {{ store.configId }}</p>
      <p><strong>前端路径:</strong> {{ route.path }}</p>
      <p><strong>路径配置:</strong> {{ JSON.stringify(store.pathConfig, null, 2) }}</p>
      <p><strong>缓存状态:</strong> {{ cacheStatus }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick, onBeforeUnmount } from 'vue';
import { useRoute, onBeforeRouteLeave } from 'vue-router';
import { RefreshRight } from '@element-plus/icons-vue';
import GridLayout from '@/components/page/GridLayout.vue';
import type { PageData } from '../../types';
import { useDynamicGridConfigStore } from './dynamicGridConfigStore';
import type { ExtendedGridItem } from '@/components/page/gridLayoutStore';
import { clearRouteProcessCache } from '@/utils/routeGuard';

// 定义组件Props
const props = defineProps({
  // 配置ID
  configId: {
    type: String,
    required: true
  },
  // 路径配置对象
  pathConfig: {
    type: Object,
    default: () => ({})
  },
  // 调试模式
  debug: {
    type: Boolean,
    default: false
  }
});

// 获取路由
const route = useRoute();
// 使用Pinia store
const store = useDynamicGridConfigStore();
// 网格布局引用
const gridLayoutRef = ref<any>(null);
// 刷新状态
const refreshing = ref(false);
// 缓存状态
const cacheStatus = ref('未知');

/**
 * 处理网格项点击事件
 * @param item 被点击的网格项
 */
function handleItemClick(item: ExtendedGridItem) {
  console.log('网格项被点击:', item);
  // 根据需要实现更多交互逻辑
}

/**
 * 处理网格项删除事件
 * @param itemId 被删除的网格项ID
 */
function handleItemDelete(itemId: string | number) {
  console.log('网格项被删除:', itemId);
  
  try {
    // 调用store的删除方法
    store.deleteGridItem(itemId);
    
    // 删除网格项后，保存当前配置到缓存
    if (route.path) {
      // 等待下一个更新周期，确保删除操作完成
      nextTick(() => {
        // 保存当前配置到缓存
        store.saveToCacheIfNeeded();
        
        // 更新缓存状态信息
        cacheStatus.value = '已更新(删除) - ' + new Date().toLocaleTimeString();
        
        console.log(`网格项 ${itemId} 已删除并更新缓存`);
      });
    }
  } catch (error) {
    console.error('删除网格项失败:', error);
  }
}

/**
 * 处理网格项更新事件
 * @param updatedItems 更新后的布局项数组
 */
function handleItemsUpdated(updatedItems: any[]) {
  console.log('网格项已更新，共', updatedItems.length, '项', updatedItems);
  
  // 更新当前路径的缓存数据
  if (route.path) {
    try {
      // 使用深拷贝解包Vue的Proxy对象，获取完整布局
      const allLayoutItems = JSON.parse(JSON.stringify(updatedItems));
      
      console.log('完整布局数据:', allLayoutItems);
      
      // 确保store.pageConfig中的grid_items数据存在
      if (store.pageConfig && store.pageConfig.grid_items && allLayoutItems && allLayoutItems.length > 0) {
        // 创建ID映射表，提高查找效率
        const itemMap = new Map();
        store.pageConfig.grid_items.forEach((item: any) => {
          const id = item.id?.toString() || '';
          if (id) itemMap.set(id, item);
        });
        
        // 遍历所有布局项，更新位置信息
        allLayoutItems.forEach((layoutItem: any) => {
          const layoutId = (layoutItem.id || layoutItem.i)?.toString() || '';
          
          // 优先直接通过ID查找
          let gridItem = itemMap.get(layoutId);
          
          // 如果没找到，尝试使用_originalData中的ID
          if (!gridItem && layoutItem._originalData && layoutItem._originalData.id) {
            const originalId = layoutItem._originalData.id.toString();
            gridItem = itemMap.get(originalId);
            
            if (gridItem) {
              console.log(`通过_originalData找到网格项: ID=${originalId}`);
            }
          }
          
          // 如果找到了对应的网格项，更新位置信息
          if (gridItem) {
            // 确保位置信息为数字类型
            gridItem.x = typeof layoutItem.x === 'number' ? layoutItem.x : parseInt(layoutItem.x || '0', 10);
            gridItem.y = typeof layoutItem.y === 'number' ? layoutItem.y : parseInt(layoutItem.y || '0', 10);
            gridItem.w = typeof layoutItem.w === 'number' ? layoutItem.w : parseInt(layoutItem.w || '1', 10);
            gridItem.h = typeof layoutItem.h === 'number' ? layoutItem.h : parseInt(layoutItem.h || '1', 10);
            gridItem.i = layoutItem.i;
            
            // 同时更新position字段
            if (!gridItem.position) gridItem.position = {};
            gridItem.position.x = gridItem.x;
            gridItem.position.y = gridItem.y;
            gridItem.position.w = gridItem.w;
            gridItem.position.h = gridItem.h;
            
            console.log(`已更新网格项 ${layoutItem.i || layoutItem.id} 的位置信息: x=${gridItem.x}, y=${gridItem.y}, w=${gridItem.w}, h=${gridItem.h}`);
          } else {
            console.warn(`未找到匹配的网格项: ID=${layoutItem.i || layoutItem.id}`);
          }
        });
        
        // 检查所有网格项是否都有位置信息
        const missingPositionItems = store.pageConfig.grid_items.filter((item: any) => 
          item.x === undefined || item.y === undefined || item.w === undefined || item.h === undefined
        );
        
        if (missingPositionItems.length > 0) {
          console.warn(`发现 ${missingPositionItems.length} 个网格项缺少位置信息`);
        }
      }
      
      // 保存当前配置到缓存
      store.saveToCacheIfNeeded();
      // 更新缓存状态信息
      cacheStatus.value = '已更新 - ' + new Date().toLocaleTimeString();
    } catch (error) {
      console.error('更新缓存失败:', error);
    }
  }
}

/**
 * 处理数据刷新
 * 先清除缓存，再重新初始化配置
 */
async function handleRefreshData() {
  refreshing.value = true;
  try {
    // 清除缓存后，重新初始化配置
    if (store) {
      // 使用新添加的clearCacheData方法清除缓存
      await store.clearCacheData(route.path);
      await nextTick();
      // 重新初始化配置
      await store.initPageConfigWithCache(route.path);
      store.gridKey++; // 更新key触发组件重新渲染
      cacheStatus.value = '已刷新 - ' + new Date().toLocaleTimeString();
    }
  } catch (error) {
    console.error('刷新配置失败:', error);
  } finally {
    refreshing.value = false;
  }
}

// 设置调试模式
watch(() => props.debug, (newValue) => {
  store.setDebugMode(newValue);
}, { immediate: true });

// 生成配置ID
const configId = computed(() => {
  // 优先使用props中的configId
  if (props.configId) {
    return props.configId;
  }
  
  // 其次使用路由参数
  if (route.params.configId) {
    return String(route.params.configId);
  } 
  
  // 最后使用路径作为配置ID
  return route.path.replace(/\//g, '_');
});

// 获取路径配置信息
const pathConfig = computed(() => {
  return props.pathConfig || {};
});

/**
 * 刷新网格布局
 * 提供给父组件调用
 */
function refreshLayout() {
  if (gridLayoutRef.value && typeof gridLayoutRef.value.refreshLayout === 'function') {
    gridLayoutRef.value.refreshLayout();
  } else {
    console.warn('网格布局组件未定义或不支持refreshLayout方法');
  }
}

// 对外暴露方法
defineExpose({
  refreshLayout,
  reloadConfig: store.reloadConfig,
  refreshData: handleRefreshData,
  setConfig: (config: PageData) => {
    store.setConfig(config);
  }
});

// 监听路由参数变化，重新加载配置
watch(() => route.params.configId, (newId, oldId) => {
  if (newId !== oldId) {
    console.log('配置ID变更，重新加载配置:', newId);
    store.setPathInfo(configId.value, pathConfig.value, route.path);
    store.initPageConfigWithCache(route.path);
  }
});

// 监听路由props变化
watch(() => props.configId, (newId, oldId) => {
  if (newId !== oldId) {
    console.log('Props配置ID变更，重新加载配置:', newId);
    store.setPathInfo(configId.value, pathConfig.value, route.path);
    store.initPageConfigWithCache(route.path);
  }
});

// 组件挂载时初始化配置
onMounted(async () => {
  // 设置路径信息
  store.setPathInfo(configId.value, pathConfig.value, route.path);
  
  // 确保所有路由都已经注册
  await nextTick();
  
  // 使用支持缓存的初始化方法
  await store.initPageConfigWithCache(route.path);
  
  // 更新缓存状态
  cacheStatus.value = '已加载 - ' + new Date().toLocaleTimeString();
});

/**
 * 路由离开前的清理工作
 * 确保状态正确重置，避免影响下一个页面
 */
onBeforeRouteLeave((to, from, next) => {
  console.log(`🚪 离开动态网格配置页面: ${from.path} -> ${to.path}`);
  
  try {
    // 保存当前配置到缓存（如果有未保存的更改）
    if (store.pageConfig && route.path) {
      store.saveToCacheIfNeeded();
      console.log('💾 保存当前配置到缓存');
    }
    
    // 清理路由处理缓存
    clearRouteProcessCache(from.path);
    
    // 重置 store 状态
    if (typeof store.resetState === 'function') {
      store.resetState();
      console.log('🧹 重置 store 状态');
    }
    
    console.log('✅ 动态网格配置页面清理完成');
  } catch (error) {
    console.error('❌ 动态网格配置页面清理失败:', error);
  }
  
  next();
});

/**
 * 组件卸载前的清理工作
 * 作为路由守卫的补充，确保组件状态正确清理
 */
onBeforeUnmount(() => {
  console.log('🗑️ 动态网格配置组件即将卸载');
  
  try {
    // 清理定时器、事件监听器等
    if (gridLayoutRef.value) {
      gridLayoutRef.value = null;
    }
    
    // 重置状态变量
    refreshing.value = false;
    cacheStatus.value = '未知';
    
    console.log('✅ 动态网格配置组件清理完成');
  } catch (error) {
    console.error('❌ 动态网格配置组件清理失败:', error);
  }
});
</script>

<style scoped>
.dynamic-grid-config-page {
  height: 100%;
  padding: 0px;
}

.grid-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  padding: 0 10px;
}

.debug-info {
  margin-bottom: 16px;
  padding: 8px;
  border-radius: 4px;
  background-color: #f0f9ff;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
