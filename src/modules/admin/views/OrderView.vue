<template>
  <div class="order-view">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>订单管理</span>
          <div class="filter-box">
            <el-select v-model="orderStatus" placeholder="订单状态" clearable @change="handleFilterChange">
              <el-option label="全部" value="" />
              <el-option label="待支付" :value="10" />
              <el-option label="已支付" :value="20" />
              <el-option label="处理中" :value="30" />
              <el-option label="配送中" :value="40" />
              <el-option label="已完成" :value="50" />
              <el-option label="已取消" :value="60" />
              <el-option label="退款中" :value="70" />
              <el-option label="已退款" :value="80" />
            </el-select>
            <el-select v-model="merchantId" placeholder="商家筛选" clearable @change="handleFilterChange">
              <el-option label="全部商家" value="" />
              <!-- 实际应用中这里需要动态加载商家列表 -->
            </el-select>
            <el-input
              v-model="searchKeyword"
              placeholder="请输入订单号/用户名"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #append>
                <el-button @click="handleSearch">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="orderList"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="orderNo" label="订单号" width="180" />
        <el-table-column prop="username" label="用户" width="120" />
        <el-table-column prop="merchantName" label="商家" width="120" />
        <el-table-column label="金额" width="100">
          <template #default="{ row }">
            <span class="price">¥{{ row.totalAmount.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" width="100">
          <template #default="{ row }">
            <el-tag :type="STATUS_TAG_CONFIG.orderStatus[row.status as keyof typeof STATUS_TAG_CONFIG['orderStatus']].type">
              {{ STATUS_TAG_CONFIG.orderStatus[row.status as keyof typeof STATUS_TAG_CONFIG['orderStatus']].label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="支付状态" width="100">
          <template #default="{ row }">
            <el-tag :type="STATUS_TAG_CONFIG.paymentStatus[row.paymentStatus as keyof typeof STATUS_TAG_CONFIG['paymentStatus']].type">
              {{ STATUS_TAG_CONFIG.paymentStatus[row.paymentStatus as keyof typeof STATUS_TAG_CONFIG['paymentStatus']].label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="配送状态" width="100">
          <template #default="{ row }">
            <el-tag :type="STATUS_TAG_CONFIG.deliveryStatus[row.deliveryStatus as keyof typeof STATUS_TAG_CONFIG['deliveryStatus']].type">
              {{ STATUS_TAG_CONFIG.deliveryStatus[row.deliveryStatus as keyof typeof STATUS_TAG_CONFIG['deliveryStatus']].label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" fixed="right" width="150">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
            <el-button
              v-if="row.status === 20 && row.deliveryStatus === 10"
              type="success"
              size="small"
              @click="handleShip(row.id)"
            >
              发货
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="订单详情"
      width="800px"
    >
      <div v-if="currentOrder" class="order-detail">
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="订单ID">{{ currentOrder.id }}</el-descriptions-item>
          <el-descriptions-item label="订单号">{{ currentOrder.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ currentOrder.userId }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ currentOrder.username }}</el-descriptions-item>
          <el-descriptions-item label="商家ID">{{ currentOrder.merchantId }}</el-descriptions-item>
          <el-descriptions-item label="商家名称">{{ currentOrder.merchantName }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="STATUS_TAG_CONFIG.orderStatus[currentOrder.status].type">
              {{ STATUS_TAG_CONFIG.orderStatus[currentOrder.status].label }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付状态">
            <el-tag :type="STATUS_TAG_CONFIG.paymentStatus[currentOrder.paymentStatus].type">
              {{ STATUS_TAG_CONFIG.paymentStatus[currentOrder.paymentStatus].label }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="配送状态">
            <el-tag :type="STATUS_TAG_CONFIG.deliveryStatus[currentOrder.deliveryStatus].type">
              {{ STATUS_TAG_CONFIG.deliveryStatus[currentOrder.deliveryStatus].label }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ currentOrder.paymentMethod || '暂无' }}</el-descriptions-item>
          <el-descriptions-item label="订单金额">¥{{ currentOrder.totalAmount.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentOrder.createTime }}</el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ currentOrder.payTime || '暂无' }}</el-descriptions-item>
          <el-descriptions-item label="发货时间">{{ currentOrder.deliveryTime || '暂无' }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ currentOrder.completeTime || '暂无' }}</el-descriptions-item>
        </el-descriptions>
        
        <el-divider content-position="center">收货信息</el-divider>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="收货人">{{ currentOrder.deliveryName || '暂无' }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ currentOrder.deliveryPhone || '暂无' }}</el-descriptions-item>
          <el-descriptions-item label="收货地址" :span="2">{{ currentOrder.deliveryAddress || '暂无' }}</el-descriptions-item>
        </el-descriptions>
        
        <el-divider content-position="center">商品信息</el-divider>
        
        <el-table :data="currentOrder.products" border style="width: 100%">
          <el-table-column label="商品图片" width="100">
            <template #default="{ row }">
              <el-image 
                style="width: 60px; height: 60px" 
                :src="row.productImage" 
                fit="cover"
              >
                <template #error>
                  <div class="image-slot">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column prop="productName" label="商品名称" />
          <el-table-column label="单价" width="100">
            <template #default="{ row }">
              <span class="price">¥{{ row.price.toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="数量" width="80" />
          <el-table-column label="小计" width="120">
            <template #default="{ row }">
              <span class="price">¥{{ row.subtotal.toFixed(2) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    
    <!-- 发货对话框 -->
    <el-dialog
      v-model="shipDialogVisible"
      title="订单发货"
      width="500px"
    >
      <el-form :model="shipForm" label-width="100px">
        <el-form-item label="物流公司" required>
          <el-input v-model="shipForm.logisticsCompany" placeholder="请输入物流公司" />
        </el-form-item>
        <el-form-item label="物流单号" required>
          <el-input v-model="shipForm.trackingNumber" placeholder="请输入物流单号" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="shipForm.remark" type="textarea" rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="shipDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitShip">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, Picture } from '@element-plus/icons-vue';
import { getOrderList, getOrderDetail, updateOrderStatus } from '../api';
import { STATUS_TAG_CONFIG } from '../constants';
import type { Order } from '../types';

const orderList = ref<Order[]>([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchKeyword = ref('');
const orderStatus = ref('');
const merchantId = ref('');

// 详情对话框
const detailDialogVisible = ref(false);
const currentOrder = ref<Order | null>(null);

// 发货对话框
const shipDialogVisible = ref(false);
const shipForm = reactive({
  orderId: 0,
  logisticsCompany: '',
  trackingNumber: '',
  remark: '',
});

// 获取订单列表
const fetchOrderList = async () => {
  loading.value = true;
  try {
    const params: any = {
      page: currentPage.value,
      pageSize: pageSize.value,
    };
    
    if (orderStatus.value) {
      params.status = orderStatus.value;
    }
    
    if (merchantId.value) {
      params.merchantId = Number(merchantId.value);
    }
    
    // 实际接口可能需要添加搜索关键词参数
    // if (searchKeyword.value) {
    //   params.keyword = searchKeyword.value;
    // }
    
    const response:any = await getOrderList(params);
    
    if (response.data.code === 0) {
      const { list, total: totalCount } = response.data.data;
      orderList.value = list;
      total.value = totalCount;
    } else {
      ElMessage.error(response.data.message || '获取订单列表失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取订单列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  fetchOrderList();
};

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchOrderList();
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  fetchOrderList();
};

// 处理筛选变化
const handleFilterChange = () => {
  currentPage.value = 1;
  fetchOrderList();
};

// 查看订单详情
const handleViewDetail = async (order: Order) => {
  try {
    const response:any = await getOrderDetail(order.id);
    if (response.data.code === 0) {
      currentOrder.value = response.data.data;
      detailDialogVisible.value = true;
    } else {
      ElMessage.error(response.data.message || '获取订单详情失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取订单详情失败');
  }
};

// 处理发货
const handleShip = (orderId: number) => {
  shipForm.orderId = orderId;
  shipForm.logisticsCompany = '';
  shipForm.trackingNumber = '';
  shipForm.remark = '';
  shipDialogVisible.value = true;
};

// 提交发货
const submitShip = async () => {
  if (!shipForm.logisticsCompany) {
    ElMessage.warning('请输入物流公司');
    return;
  }
  
  if (!shipForm.trackingNumber) {
    ElMessage.warning('请输入物流单号');
    return;
  }
  
  try {
    // 实际应用中这里需要调用发货接口
    // 这里简化为更新订单状态
    const response:any = await updateOrderStatus(shipForm.orderId, {
      status: '40',
      remark: shipForm.remark
    });
    
    if (response.data.code === 0) {
      ElMessage.success('发货成功');
      shipDialogVisible.value = false;
      fetchOrderList();
    } else {
      ElMessage.error(response.data.message || '发货失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '发货失败');
  }
};

onMounted(() => {
  fetchOrderList();
});
</script>

<style scoped>
.order-view {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-box {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.price {
  font-weight: bold;
  color: #f56c6c;
}

.order-detail {
  padding: 20px 0;
}
</style>