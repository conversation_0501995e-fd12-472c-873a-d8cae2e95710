<template>
  <div class="admin-profile-view">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>个人中心</span>
        </div>
      </template>

      <el-row :gutter="20">
        <!-- 左侧个人信息卡片 -->
        <el-col :span="8">
          <el-card class="profile-card">
            <div class="avatar-container">
              <el-avatar :size="100" :src="adminInfo.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="upload-btn">
                <FileUploader
                  action="/v1/admin/upload"
                  :headers="uploadHeaders"
                  :show-file-list="false"
                  :file-limit="1"
                  :size-limit="2 * 1024 * 1024"
                  accept=".jpg,.jpeg,.png"
                  file-usage="avatar"
                  @success="handleAvatarSuccess"
                >
                  <template #tip>
                    <p>点击或拖拽头像图片到此区域上传</p>
                    <p class="upload-tip">仅支持JPG、PNG格式，文件大小不超过2MB</p>
                  </template>
                </FileUploader>
              </div>
            </div>
            <div class="profile-info">
              <h3>{{ adminInfo.username }}</h3>
              <p class="username">{{ adminInfo.username }}</p>
              <p>
                <el-tag :type="getRoleTagType(adminInfo.role)">
                  {{ getRoleLabel(adminInfo.role) }}
                </el-tag>
              </p>
              <p class="info-item">
                <el-icon><Phone /></el-icon>
                {{ adminInfo.mobile || '未设置' }}
              </p>
              <p class="info-item">
                <el-icon><Message /></el-icon>
                {{ adminInfo.email || '未设置' }}
              </p>
              <p class="info-item">
                <el-icon><Timer /></el-icon>
                上次登录: {{ adminInfo.lastLoginTime || '未记录' }}
              </p>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧设置选项卡 -->
        <el-col :span="16">
          <el-tabs v-model="activeTab">
            <!-- 基本信息设置 -->
            <el-tab-pane label="基本信息" name="basic">
              <el-form
                ref="basicFormRef"
                :model="basicForm"
                :rules="basicRules"
                label-width="100px"
              >
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="basicForm.username" disabled />
                </el-form-item>
                <el-form-item label="姓名" prop="nickname">
                  <el-input v-model="basicForm.nickname" />
                </el-form-item>
                <el-form-item label="手机号" prop="phone">
                  <el-input v-model="basicForm.mobile" />
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="basicForm.email" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="updateBasicInfo">保存修改</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 密码修改 -->
            <el-tab-pane label="修改密码" name="password">
              <el-form
                ref="passwordFormRef"
                :model="passwordForm"
                :rules="passwordRules"
                label-width="100px"
              >
                <el-form-item label="当前密码" prop="oldPassword">
                  <el-input v-model="passwordForm.oldPassword" type="password" show-password />
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                  <el-input v-model="passwordForm.newPassword" type="password" show-password />
                </el-form-item>
                <el-form-item label="确认新密码" prop="confirmPassword">
                  <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="updatePassword">修改密码</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 界面设置 -->
            <el-tab-pane label="界面设置" name="ui">
              <el-form label-width="100px">
                <el-form-item label="主题色">
                  <el-color-picker v-model="uiSettings.themeColor" />
                </el-form-item>
                <el-form-item label="字体大小">
                  <el-slider v-model="uiSettings.fontSize" :min="12" :max="18" :step="1" show-stops />
                </el-form-item>
                <el-form-item label="紧凑模式">
                  <el-switch v-model="uiSettings.compact" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="saveUiSettings">保存设置</el-button>
                  <el-button @click="resetUiSettings">恢复默认</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 通知设置 -->
            <el-tab-pane label="通知设置" name="notification">
              <el-form label-width="100px">
                <el-form-item label="系统通知">
                  <el-switch v-model="notificationSettings.system" />
                </el-form-item>
                <el-form-item label="订单通知">
                  <el-switch v-model="notificationSettings.order" />
                </el-form-item>
                <el-form-item label="商家通知">
                  <el-switch v-model="notificationSettings.merchant" />
                </el-form-item>
                <el-form-item label="用户通知">
                  <el-switch v-model="notificationSettings.user" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="notificationSettings">保存设置</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/**
 * @file AdminProfileView.vue
 * @description 管理员个人中心页面，允许管理员修改个人信息、密码和偏好设置
 */
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage} from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { User, Phone, Message, Timer } from '@element-plus/icons-vue';
import { getAdminInfo, updateAdminInfo, changePassword, updateAdminAvatar } from '../api';
//import { STATUS_TAG_CONFIG } from '../constants';
import { useAdminStore } from '../stores/adminStore';
import type { AdminInfo } from '../types';
import { AdminRole } from '../types';
import { FileUploader } from '@/components/common';

// 获取当前管理员信息
const adminStore = useAdminStore();
const adminInfo = ref<AdminInfo>(adminStore.currentAdmin || {} as AdminInfo);

// 上传头像的请求头
const uploadHeaders = computed(() => {
  return {
    Authorization: `Bearer ${localStorage.getItem('admin_access_token')}`
  };
});

// 标签页控制
const activeTab = ref('basic');

// 表单引用
const basicFormRef = ref<FormInstance>();
const passwordFormRef = ref<FormInstance>();

// 基本信息表单
const basicForm = reactive({
  id: 0,
  username: '',
  nickname: '',
  mobile: '',
  email: ''
});

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// UI设置
const uiSettings = reactive({
  themeColor: localStorage.getItem('admin_theme_color') || '#409EFF',
  fontSize: parseInt(localStorage.getItem('admin_font_size') || '14'),
  compact: localStorage.getItem('admin_compact_mode') === 'true'
});

// 通知设置
// 通知设置
const notificationSettings = reactive({
  system: localStorage.getItem('admin_notification_system') !== 'false',
  order: localStorage.getItem('admin_notification_order') !== 'false',
  merchant: localStorage.getItem('admin_notification_merchant') !== 'false',
  user: localStorage.getItem('admin_notification_user') !== 'false'
});

// 基本信息表单验证规则
const basicRules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱', trigger: 'blur' }
  ]
});

/**
 * @description 保存UI设置
 * @returns {void}
 */
const saveUiSettings = () => {
  localStorage.setItem('admin_theme_color', uiSettings.themeColor);
  localStorage.setItem('admin_font_size', uiSettings.fontSize.toString());
  localStorage.setItem('admin_compact_mode', uiSettings.compact.toString());
  ElMessage.success('界面设置保存成功');
};

/**
 * @description 恢复默认UI设置
 * @returns {void}
 */
const resetUiSettings = () => {
  uiSettings.themeColor = '#409EFF';
  uiSettings.fontSize = 14;
  uiSettings.compact = false;
  localStorage.setItem('admin_theme_color', uiSettings.themeColor);
  localStorage.setItem('admin_font_size', uiSettings.fontSize.toString());
  localStorage.setItem('admin_compact_mode', uiSettings.compact.toString());
  ElMessage.success('界面设置已恢复默认');
};

// 密码表单验证规则
const passwordRules = reactive<FormRules>({
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (_rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
});

/**
 * @description 获取角色标签类型
 * @param {AdminRole} role - 角色
 * @returns {string} 标签类型
 */
const getRoleTagType = (role: AdminRole): string => {
  switch (role) {
    case AdminRole.ADMIN:
      return 'primary';
    case AdminRole.SUPER_ADMIN:
      return 'danger';
    default:
      return 'info';
  }
};

/**
 * @description 获取角色标签文本
 * @param {AdminRole} role - 角色
 * @returns {string} 标签文本
 */
const getRoleLabel = (role: AdminRole): string => {
  switch (role) {
    case AdminRole.ADMIN:
      return '管理员';
    case AdminRole.SUPER_ADMIN:
      return '超级管理员';
    default:
      return '未知角色';
  }
};

/**
 * @description 获取管理员信息
 * @returns {Promise<void>}
 */
const fetchAdminInfo = async () => {
  try {
    const res = await getAdminInfo();
    adminInfo.value = res;
    // 填充基本信息表单
    basicForm.id = res.id;
    basicForm.username = res.username;
    basicForm.nickname = res.nickname;
    basicForm.mobile = res.mobile || '';
    basicForm.email = res.email || '';
  } catch (error) {
    console.error('获取管理员信息失败', error);
    ElMessage.error('获取管理员信息失败');
  }
};

/**
 * @description 更新基本信息
 * @returns {Promise<void>}
 */
const updateBasicInfo = async () => {
  if (!basicFormRef.value) return;
  await basicFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await updateAdminInfo({
          id: basicForm.id,
          nickname: basicForm.nickname,
          mobile: basicForm.mobile,
          email: basicForm.email
        });
        ElMessage.success('基本信息更新成功');
        fetchAdminInfo(); // 刷新信息
      } catch (error) {
        console.error('更新基本信息失败', error);
        ElMessage.error('更新基本信息失败');
      }
    }
  });
};

/**
 * @description 更新密码
 * @returns {Promise<void>}
 */
const updatePassword = async () => {
  if (!passwordFormRef.value) return;
  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await changePassword({
          oldPassword: passwordForm.oldPassword,
          newPassword: passwordForm.newPassword
        });
        ElMessage.success('密码修改成功');
        // 清空密码表单
        passwordForm.oldPassword = '';
        passwordForm.newPassword = '';
        passwordForm.confirmPassword = '';
      } catch (error) {
        console.error('密码修改失败', error);
        ElMessage.error('密码修改失败');
      }
    }
  });
};

/**
 * @description 头像上传成功的处理
 * @param {any} response - 上传响应
 * @param {any} file - 上传的文件信息
 * @param {any[]} fileList - 文件列表
 * @returns {void}
 */
const handleAvatarSuccess = async (response: any, file: any, fileList: any[]) => {
  console.log('头像上传成功', response, file, fileList);
  // 根据API返回格式，正确获取文件URL
  adminInfo.value.avatar = response.file_url;
  await updateAdminAvatar({
    avatar_url: response.file_url
  });
  ElMessage.success('头像更新成功');
};

// FileUploader组件已内置文件验证功能，不再需要单独的验证函数

onMounted(() => {
  fetchAdminInfo();
});
</script>

<style scoped lang="scss">
.admin-profile-view {
  padding: 20px;
  .box-card {
    width: 1200px;
    margin: 0 auto;
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .profile-card {
      .avatar-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 20px;
        .el-avatar {
          margin-bottom: 10px;
        }
        .upload-btn {
          width: 100%;
          max-width: 300px;
          margin-top: 10px;
          
          .upload-tip {
            font-size: 12px;
            color: #909399;
          }
          
          :deep(.file-uploader .upload-area) {
            padding: 10px;
          }
          
          :deep(.upload-icon) {
            font-size: 20px;
            margin-bottom: 5px;
          }
        }
      }
      .profile-info {
        text-align: center;
        .username {
          color: #999;
          margin-bottom: 10px;
        }
        .info-item {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 5px;
          color: #666;
          .el-icon {
            margin-right: 5px;
          }
        }
      }
    }
  }
}
</style>
