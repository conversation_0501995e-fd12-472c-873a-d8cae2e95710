<template>
  <el-dialog
    v-model="dialogVisible"
    title="订单列表"
    width="900px"
    :before-close="handleClose"
  >
    <div class="order-dialog-content">
      <!-- 跑腿员信息 -->
      <div class="runner-info">
        <el-card shadow="never">
          <div class="info-row">
            <span class="label">跑腿员：</span>
            <span>{{ runner?.real_name }} ({{ runner?.mobile }})</span>
          </div>
          <div class="info-row">
            <span class="label">总订单数：</span>
            <span class="highlight">{{ runner?.order_count || 0 }}</span>
          </div>
          <div class="info-row">
            <span class="label">完成订单数：</span>
            <span class="highlight success">{{ runner?.success_count || 0 }}</span>
          </div>
        </el-card>
      </div>

      <!-- 订单列表 -->
      <div class="order-list">
        <el-table
          v-loading="loading"
          :data="orderList"
          stripe
          style="width: 100%"
          empty-text="暂无订单数据"
        >
          <el-table-column prop="id" label="订单ID" width="100" />
          
          <el-table-column prop="order_no" label="订单号" width="150" />
          
          <el-table-column label="订单状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getOrderStatusType(row.status)">
                {{ getOrderStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="pickup_address" label="取货地址" min-width="150" show-overflow-tooltip />
          
          <el-table-column prop="delivery_address" label="送货地址" min-width="150" show-overflow-tooltip />
          
          <el-table-column label="订单金额" width="100">
            <template #default="{ row }">
              <span class="amount">¥{{ row.amount }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="配送费" width="100">
            <template #default="{ row }">
              <span class="delivery-fee">¥{{ row.delivery_fee }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="创建时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="完成时间" width="150">
            <template #default="{ row }">
              <span v-if="row.completed_at">{{ formatDateTime(row.completed_at) }}</span>
              <span v-else class="not-completed">未完成</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue';
import { 
  getRunnerOrders, 
  type Runner, 
  type RunnerOrder,
  OrderStatus,
  getOrderStatusDesc
} from '../../../api/runner';

// Props
interface Props {
  modelValue: boolean;
  runner: Runner | null;
}

const props = defineProps<Props>();

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const dialogVisible = ref(false);
const loading = ref(false);
const orderList = ref<RunnerOrder[]>([]);

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
});

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal && props.runner) {
      loadOrderList();
    }
  },
  { immediate: true }
);

// 监听对话框内部状态变化
watch(dialogVisible, (newVal) => {
  emit('update:modelValue', newVal);
  if (!newVal) {
    resetData();
  }
});

/**
 * 获取订单状态类型
 */
function getOrderStatusType(status: number): string {
  switch (status) {
    case OrderStatus.PENDING:
      return 'warning';
    case OrderStatus.ACCEPTED:
      return 'primary';
    case OrderStatus.PICKING:
      return 'info';
    case OrderStatus.DELIVERING:
      return 'info';
    case OrderStatus.COMPLETED:
      return 'success';
    case OrderStatus.CANCELLED:
      return 'danger';
    default:
      return 'info';
  }
}

/**
 * 获取订单状态文本
 */
function getOrderStatusText(status: number): string {
  return getOrderStatusDesc(status);
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTime: string): string {
  if (!dateTime) return '';
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * 加载订单列表
 */
async function loadOrderList() {
  if (!props.runner) return;
  
  try {
    loading.value = true;
    
    const response = await getRunnerOrders(props.runner.id, {
      page: pagination.page,
      size: pagination.size
    });
    
    orderList.value = response.list;
    pagination.total = response.total;
  } catch (error) {
    console.error('加载订单列表失败:', error);
    ElMessage.error('加载订单列表失败');
  } finally {
    loading.value = false;
  }
}

/**
 * 处理页面大小变化
 */
function handleSizeChange(newSize: number) {
  pagination.size = newSize;
  pagination.page = 1;
  loadOrderList();
}

/**
 * 处理当前页变化
 */
function handleCurrentChange(newPage: number) {
  pagination.page = newPage;
  loadOrderList();
}

/**
 * 刷新数据
 */
function handleRefresh() {
  loadOrderList();
}

/**
 * 重置数据
 */
function resetData() {
  orderList.value = [];
  pagination.page = 1;
  pagination.size = 10;
  pagination.total = 0;
}

/**
 * 关闭对话框
 */
function handleClose() {
  dialogVisible.value = false;
}
</script>

<style scoped lang="scss">
.order-dialog-content {
  .runner-info {
    margin-bottom: 20px;
    
    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        font-weight: 500;
        color: #606266;
        margin-right: 8px;
        min-width: 80px;
      }
      
      .highlight {
        font-weight: 600;
        color: #409eff;
        
        &.success {
          color: #67c23a;
        }
      }
    }
  }
  
  .order-list {
    .amount {
      font-weight: 600;
      color: #e6a23c;
    }
    
    .delivery-fee {
      font-weight: 600;
      color: #67c23a;
    }
    
    .not-completed {
      color: #c0c4cc;
      font-style: italic;
    }
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    text-align: center;
  }
}

.dialog-footer {
  text-align: right;
}
</style>