<template>
  <el-dialog
    v-model="dialogVisible"
    title="状态管理"
    width="500px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="跑腿员">
        <span>{{ runner?.real_name }} ({{ runner?.mobile }})</span>
      </el-form-item>
      
      <el-form-item label="当前状态">
        <el-tag :type="getStatusType(runner?.status)">{{ getStatusText(runner?.status) }}</el-tag>
      </el-form-item>
      
      <el-form-item label="新状态" prop="status">
        <el-select v-model="formData.status" placeholder="请选择新状态" style="width: 100%">
          <el-option
            v-for="option in statusOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
            :disabled="option.value === runner?.status"
          >
            <span>{{ option.label }}</span>
            <span v-if="option.value === runner?.status" style="color: #999; margin-left: 8px;">(当前)</span>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="操作原因" prop="reason">
        <el-input
          v-model="formData.reason"
          type="textarea"
          :rows="3"
          placeholder="请输入状态变更原因"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          :loading="submitting" 
          @click="handleSubmit"
        >
          确认更新
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { 
  updateRunnerStatus, 
  type Runner, 
  type RunnerStatusParams,
  getRunnerStatusDesc,
  RunnerStatus
} from '../../../api/runner';

// Props
interface Props {
  modelValue: boolean;
  runner: Runner | null;
}

const props = defineProps<Props>();

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const dialogVisible = ref(false);
const submitting = ref(false);
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive<RunnerStatusParams>({
  status: RunnerStatus.APPROVED,
  reason: ''
});

// 状态选项
const statusOptions = [
  { value: RunnerStatus.PENDING, label: '待审核' },
  { value: RunnerStatus.APPROVED, label: '审核通过' },
  { value: RunnerStatus.SUSPENDED, label: '暂停服务' },
  { value: RunnerStatus.BLACKLIST, label: '黑名单' },
  { value: RunnerStatus.REJECTED, label: '审核拒绝' }
];

// 表单验证规则
const formRules: FormRules = {
  status: [
    { required: true, message: '请选择新状态', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入状态变更原因', trigger: 'blur' },
    { min: 5, message: '原因至少5个字符', trigger: 'blur' }
  ]
};

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal) {
      resetForm();
    }
  },
  { immediate: true }
);

// 监听对话框内部状态变化
watch(dialogVisible, (newVal) => {
  emit('update:modelValue', newVal);
});

/**
 * 获取状态类型
 */
function getStatusType(status?: number): string {
  switch (status) {
    case RunnerStatus.PENDING:
      return 'warning';
    case RunnerStatus.APPROVED:
      return 'success';
    case RunnerStatus.SUSPENDED:
      return 'info';
    case RunnerStatus.BLACKLIST:
      return 'danger';
    case RunnerStatus.REJECTED:
      return 'danger';
    default:
      return 'info';
  }
}

/**
 * 获取状态文本
 */
function getStatusText(status?: number): string {
  return getRunnerStatusDesc(status || 0);
}

/**
 * 重置表单
 */
function resetForm() {
  formData.status = props.runner?.status || RunnerStatus.APPROVED;
  formData.reason = '';
  
  if (formRef.value) {
    formRef.value.clearValidate();
  }
}

/**
 * 提交状态更新
 */
async function handleSubmit() {
  if (!props.runner) return;
  
  try {
    // 表单验证
    await formRef.value?.validate();
    
    // 检查状态是否有变化
    if (formData.status === props.runner.status) {
      ElMessage.warning('请选择不同的状态');
      return;
    }
    
    submitting.value = true;
    
    // 提交状态更新
    await updateRunnerStatus(props.runner.id, formData);
    
    ElMessage.success('状态更新成功');
    emit('success');
    handleClose();
  } catch (error: any) {
    console.error('状态更新失败:', error);
    ElMessage.error('状态更新失败');
  } finally {
    submitting.value = false;
  }
}

/**
 * 关闭对话框
 */
function handleClose() {
  dialogVisible.value = false;
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}
</style>