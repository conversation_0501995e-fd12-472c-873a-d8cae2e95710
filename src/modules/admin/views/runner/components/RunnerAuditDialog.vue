<template>
  <el-dialog
    v-model="dialogVisible"
    :title="auditTitle"
    width="500px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="跑腿员">
        <span>{{ runner?.real_name }} ({{ runner?.mobile }})</span>
      </el-form-item>
      
      <el-form-item label="审核结果">
        <el-tag :type="auditStatus === 1 ? 'success' : 'danger'">
          {{ auditStatus === 1 ? '审核通过' : '审核拒绝' }}
        </el-tag>
      </el-form-item>
      
      <el-form-item 
        v-if="auditStatus === 2" 
        label="拒绝原因" 
        prop="reject_reason"
      >
        <el-input
          v-model="formData.reject_reason"
          type="textarea"
          :rows="3"
          placeholder="请输入拒绝原因"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="管理员备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入管理员备注（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          :loading="submitting" 
          @click="handleSubmit"
        >
          确认{{ auditStatus === 1 ? '通过' : '拒绝' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { auditRunner, type Runner, type RunnerAuditParams } from '../../../api/runner';

// Props
interface Props {
  modelValue: boolean;
  runner: Runner | null;
  auditStatus: number; // 1: 通过, 2: 拒绝
}

const props = defineProps<Props>();

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const dialogVisible = ref(false);
const submitting = ref(false);
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive<RunnerAuditParams>({
  status: 1,
  reject_reason: '',
  remark: ''
});

// 表单验证规则
const formRules: FormRules = {
  reject_reason: [
    {
      required: true,
      message: '请输入拒绝原因',
      trigger: 'blur',
      validator: (_rule, value, callback) => {
        if (props.auditStatus === 2 && !value) {
          callback(new Error('审核拒绝时必须填写拒绝原因'));
        } else {
          callback();
        }
      }
    }
  ]
};

// 计算属性
const auditTitle = computed(() => {
  return props.auditStatus === 1 ? '审核通过' : '审核拒绝';
});

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal) {
      resetForm();
    }
  },
  { immediate: true }
);

// 监听对话框内部状态变化
watch(dialogVisible, (newVal) => {
  emit('update:modelValue', newVal);
});

// 监听审核状态变化
watch(
  () => props.auditStatus,
  (newVal) => {
    formData.status = newVal;
    if (newVal === 1) {
      formData.reject_reason = '';
    }
  },
  { immediate: true }
);

/**
 * 重置表单
 */
function resetForm() {
  formData.status = props.auditStatus;
  formData.reject_reason = '';
  formData.remark = '';
  
  if (formRef.value) {
    formRef.value.clearValidate();
  }
}

/**
 * 提交审核
 */
async function handleSubmit() {
  if (!props.runner) return;
  
  try {
    // 表单验证
    await formRef.value?.validate();
    
    submitting.value = true;
    
    // 提交审核
    await auditRunner(props.runner.id, formData);
    
    ElMessage.success(`审核${props.auditStatus === 1 ? '通过' : '拒绝'}成功`);
    emit('success');
    handleClose();
  } catch (error: any) {
    if (error?.message) {
      console.error('审核失败:', error);
      ElMessage.error('审核失败');
    }
  } finally {
    submitting.value = false;
  }
}

/**
 * 关闭对话框
 */
function handleClose() {
  dialogVisible.value = false;
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}
</style>