<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑备注"
    width="500px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="跑腿员">
        <span>{{ runner?.real_name }} ({{ runner?.mobile }})</span>
      </el-form-item>
      
      <el-form-item label="当前备注">
        <div class="current-remark">
          <span v-if="runner?.remark" class="remark-text">{{ runner.remark }}</span>
          <span v-else class="no-remark">暂无备注</span>
        </div>
      </el-form-item>
      
      <el-form-item label="新备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="4"
          placeholder="请输入管理员备注"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          :loading="submitting" 
          @click="handleSubmit"
        >
          保存备注
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { 
  updateRunnerRemark, 
  type Runner, 
  type RunnerRemarkParams
} from '../../../api/runner';

// Props
interface Props {
  modelValue: boolean;
  runner: Runner | null;
}

const props = defineProps<Props>();

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const dialogVisible = ref(false);
const submitting = ref(false);
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive<RunnerRemarkParams>({
  remark: ''
});

// 表单验证规则
const formRules: FormRules = {
  remark: [
    { max: 500, message: '备注不能超过500个字符', trigger: 'blur' }
  ]
};

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal) {
      resetForm();
    }
  },
  { immediate: true }
);

// 监听对话框内部状态变化
watch(dialogVisible, (newVal) => {
  emit('update:modelValue', newVal);
});

/**
 * 重置表单
 */
function resetForm() {
  formData.remark = props.runner?.remark || '';
  
  if (formRef.value) {
    formRef.value.clearValidate();
  }
}

/**
 * 提交备注更新
 */
async function handleSubmit() {
  if (!props.runner) return;
  
  try {
    // 表单验证
    await formRef.value?.validate();
    
    // 检查备注是否有变化
    if (formData.remark === (props.runner.remark || '')) {
      ElMessage.warning('备注内容没有变化');
      return;
    }
    
    submitting.value = true;
    
    // 提交备注更新
    await updateRunnerRemark(props.runner.id, formData);
    
    ElMessage.success('备注更新成功');
    emit('success');
    handleClose();
  } catch (error: any) {
    console.error('备注更新失败:', error);
    ElMessage.error('备注更新失败');
  } finally {
    submitting.value = false;
  }
}

/**
 * 关闭对话框
 */
function handleClose() {
  dialogVisible.value = false;
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}

.current-remark {
  .remark-text {
    color: #606266;
    line-height: 1.5;
    word-break: break-all;
  }
  
  .no-remark {
    color: #c0c4cc;
    font-style: italic;
  }
}
</style>