<template>
  <el-dialog
    v-model="dialogVisible"
    title="跑腿员详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="runner-detail">
      <el-descriptions v-if="runner" :column="2" border>
        <el-descriptions-item label="跑腿员ID">
          {{ runner.id }}
        </el-descriptions-item>
        <el-descriptions-item label="用户ID">
          {{ runner.user_id }}
        </el-descriptions-item>
        <el-descriptions-item label="真实姓名">
          {{ runner.real_name }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号">
          {{ runner.mobile }}
        </el-descriptions-item>
        <el-descriptions-item label="身份证号">
          {{ runner.id_card_number || '未填写' }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTagType(runner.status)">{{ runner.status_desc }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="在线状态">
          <el-tag :type="runner.is_online ? 'success' : 'info'">
            {{ runner.is_online ? '在线' : '离线' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="工作状态">
          <el-tag :type="getWorkingStatusTagType(runner.working_status)">{{ runner.working_status_desc }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="当前位置" :span="2">
          {{ runner.current_location || '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="经纬度" :span="2" v-if="runner.latitude && runner.longitude">
          {{ runner.latitude }}, {{ runner.longitude }}
        </el-descriptions-item>
        <el-descriptions-item label="评分">
          <el-rate v-model="runner.score" disabled show-score text-color="#ff9900" />
        </el-descriptions-item>
        <el-descriptions-item label="服务半径">
          {{ runner.service_radius }}km
        </el-descriptions-item>
        <el-descriptions-item label="订单总数">
          {{ runner.order_count }}
        </el-descriptions-item>
        <el-descriptions-item label="成功订单">
          {{ runner.success_count }}
        </el-descriptions-item>
        <el-descriptions-item label="取消订单">
          {{ runner.cancel_count }}
        </el-descriptions-item>
        <el-descriptions-item label="账户余额">
          ¥{{ runner.balance.toFixed(2) }}
        </el-descriptions-item>
        <el-descriptions-item label="服务区域" :span="2" v-if="runner.area_codes">
          {{ runner.area_codes }}
        </el-descriptions-item>
        <el-descriptions-item label="加入时间">
          {{ formatDateTime(runner.join_time) }}
        </el-descriptions-item>
        <el-descriptions-item label="最后登录">
          {{ formatDateTime(runner.last_login_time || '') }}
        </el-descriptions-item>
        <el-descriptions-item label="最后在线">
          {{ formatDateTime(runner.last_online_time || '') }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDateTime(runner.create_time) }}
        </el-descriptions-item>
        <el-descriptions-item label="管理员备注" :span="2">
          {{ runner.remark || '无' }}
        </el-descriptions-item>
        <el-descriptions-item label="前端备注" :span="2">
          {{ runner.frontend_remark || '无' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 身份证照片 -->
      <div v-if="runner && (runner.id_card_front_pic || runner.id_card_back_pic || runner.face_pic)" class="id-photos">
        <h4>身份证照片</h4>
        <el-row :gutter="20">
          <el-col :span="8" v-if="runner.id_card_front_pic">
            <div class="photo-item">
              <h5>身份证正面</h5>
              <el-image
                :src="runner.id_card_front_pic"
                fit="cover"
                style="width: 200px; height: 120px"
                :preview-src-list="[runner.id_card_front_pic]"
                preview-teleported
              />
            </div>
          </el-col>
          <el-col :span="8" v-if="runner.id_card_back_pic">
            <div class="photo-item">
              <h5>身份证背面</h5>
              <el-image
                :src="runner.id_card_back_pic"
                fit="cover"
                style="width: 200px; height: 120px"
                :preview-src-list="[runner.id_card_back_pic]"
                preview-teleported
              />
            </div>
          </el-col>
          <el-col :span="8" v-if="runner.face_pic">
            <div class="photo-item">
              <h5>人脸照片</h5>
              <el-image
                :src="runner.face_pic"
                fit="cover"
                style="width: 120px; height: 120px"
                :preview-src-list="[runner.face_pic]"
                preview-teleported
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleRefresh">
          <el-icon><Refresh /></el-icon> 刷新
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue';
import { getRunnerDetail, type Runner, RunnerStatus, WorkingStatus } from '../../../api/runner';
import { formatTime } from '@/utils/format';

/**
 * 格式化日期时间
 */
function formatDateTime(dateTime: string): string {
  if (!dateTime) return '';
  return formatTime(dateTime, 'YYYY-MM-DD HH:mm:ss');
}

// Props
interface Props {
  modelValue: boolean;
  runnerId: number;
}

const props = defineProps<Props>();

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'refresh'): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const dialogVisible = ref(false);
const loading = ref(false);
const runner = ref<Runner | null>(null);

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal && props.runnerId) {
      fetchRunnerDetail();
    }
  },
  { immediate: true }
);

// 监听对话框内部状态变化
watch(dialogVisible, (newVal) => {
  emit('update:modelValue', newVal);
});

/**
 * 获取状态标签类型
 */
function getStatusTagType(status: number): string {
  const typeMap: Record<number, string> = {
    [RunnerStatus.PENDING]: 'warning',
    [RunnerStatus.APPROVED]: 'success',
    [RunnerStatus.REJECTED]: 'danger',
    [RunnerStatus.SUSPENDED]: 'info',
    [RunnerStatus.BLACKLIST]: 'danger'
  };
  return typeMap[status] || 'info';
}

/**
 * 获取工作状态标签类型
 */
function getWorkingStatusTagType(status: number): string {
  const typeMap: Record<number, string> = {
    [WorkingStatus.OFFLINE]: 'info',
    [WorkingStatus.IDLE]: 'success',
    [WorkingStatus.WORKING]: 'warning',
    [WorkingStatus.RESTING]: 'info'
  };
  return typeMap[status] || 'info';
}

/**
 * 获取跑腿员详情
 */
async function fetchRunnerDetail() {
  if (!props.runnerId) return;
  
  try {
    loading.value = true;
    runner.value = await getRunnerDetail(props.runnerId);
  } catch (error) {
    console.error('获取跑腿员详情失败:', error);
    ElMessage.error('获取跑腿员详情失败');
  } finally {
    loading.value = false;
  }
}

/**
 * 刷新数据
 */
function handleRefresh() {
  fetchRunnerDetail();
  emit('refresh');
}

/**
 * 关闭对话框
 */
function handleClose() {
  dialogVisible.value = false;
  runner.value = null;
}
</script>

<style scoped lang="scss">
.runner-detail {
  .id-photos {
    margin-top: 20px;
    
    h4 {
      margin-bottom: 15px;
      color: #303133;
      font-size: 16px;
    }
    
    .photo-item {
      text-align: center;
      
      h5 {
        margin-bottom: 10px;
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>