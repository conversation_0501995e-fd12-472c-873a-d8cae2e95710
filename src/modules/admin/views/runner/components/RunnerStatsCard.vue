<template>
  <el-card class="stats-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="title">跑腿员统计</span>
        <el-button 
          type="primary" 
          size="small" 
          :loading="loading" 
          @click="refreshStats"
        >
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </template>

    <div v-loading="loading" class="stats-content">
      <div class="stats-grid">
        <!-- 总数统计 -->
        <div class="stat-item total">
          <div class="stat-icon">
            <el-icon size="24"><User /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats?.total_runners || 0 }}</div>
            <div class="stat-label">总跑腿员</div>
          </div>
        </div>

        <!-- 待审核 -->
        <div class="stat-item pending">
          <div class="stat-icon">
            <el-icon size="24"><Clock /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats?.pending_audit || 0 }}</div>
            <div class="stat-label">待审核</div>
          </div>
        </div>

        <!-- 正常状态 -->
        <div class="stat-item active">
          <div class="stat-icon">
            <el-icon size="24"><Check /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats?.approved_runners || 0 }}</div>
            <div class="stat-label">正常</div>
          </div>
        </div>

        <!-- 暂停状态 -->
        <div class="stat-item suspended">
          <div class="stat-icon">
            <el-icon size="24"><VideoPause /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats?.suspended_runners || 0 }}</div>
            <div class="stat-label">暂停</div>
          </div>
        </div>

        <!-- 封禁状态 -->
        <div class="stat-item banned">
          <div class="stat-icon">
            <el-icon size="24"><Lock /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats?.blacklist_runners || 0 }}</div>
            <div class="stat-label">封禁</div>
          </div>
        </div>

        <!-- 已拒绝 -->
        <div class="stat-item rejected">
          <div class="stat-icon">
            <el-icon size="24"><Close /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats?.rejected_runners || 0 }}</div>
            <div class="stat-label">已拒绝</div>
          </div>
        </div>
      </div>

      <!-- 今日统计 -->
      <div class="today-stats">
        <el-divider content-position="left">今日统计</el-divider>
        <div class="today-grid">
          <div class="today-item">
            <span class="today-label">新增申请：</span>
            <span class="today-value primary">{{ stats?.today_new_runners || 0 }}</span>
          </div>
          <div class="today-item">
            <span class="today-label">本周新增：</span>
            <span class="today-value success">{{ stats?.this_week_new_runners || 0 }}</span>
          </div>
          <div class="today-item">
            <span class="today-label">本月新增：</span>
            <span class="today-value danger">{{ stats?.this_month_new_runners || 0 }}</span>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  Refresh, 
  User, 
  Clock, 
  Check, 
  VideoPause, 
  Lock, 
  Close 
} from '@element-plus/icons-vue';
import { getRunnerStatistics, type RunnerStatistics } from '../../../api/runner';

// 响应式数据
const loading = ref(false);
const stats = ref<RunnerStatistics | null>(null);

// 组件挂载时加载统计数据
onMounted(() => {
  loadStatistics();
});

/**
 * 加载统计数据
 */
async function loadStatistics() {
  try {
    loading.value = true;
    stats.value = await getRunnerStatistics();
  } catch (error) {
    console.error('加载统计数据失败:', error);
    ElMessage.error('加载统计数据失败');
  } finally {
    loading.value = false;
  }
}

/**
 * 刷新统计数据
 */
function refreshStats() {
  loadStatistics();
}

// 暴露方法给父组件
defineExpose({
  refreshStats: loadStatistics
});
</script>

<style scoped lang="scss">
.stats-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .stats-content {
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
      margin-bottom: 20px;
      
      .stat-item {
        display: flex;
        align-items: center;
        padding: 16px;
        border-radius: 8px;
        background: #f8f9fa;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .stat-icon {
          margin-right: 12px;
          padding: 8px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .stat-info {
          .stat-value {
            font-size: 24px;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 12px;
            color: #909399;
            line-height: 1;
          }
        }
        
        // 不同状态的颜色
        &.total {
          .stat-icon {
            background: #e1f3ff;
            color: #409eff;
          }
          .stat-value {
            color: #409eff;
          }
        }
        
        &.pending {
          .stat-icon {
            background: #fdf6ec;
            color: #e6a23c;
          }
          .stat-value {
            color: #e6a23c;
          }
        }
        
        &.active {
          .stat-icon {
            background: #f0f9ff;
            color: #67c23a;
          }
          .stat-value {
            color: #67c23a;
          }
        }
        
        &.suspended {
          .stat-icon {
            background: #f4f4f5;
            color: #909399;
          }
          .stat-value {
            color: #909399;
          }
        }
        
        &.banned,
        &.rejected {
          .stat-icon {
            background: #fef0f0;
            color: #f56c6c;
          }
          .stat-value {
            color: #f56c6c;
          }
        }
      }
    }
    
    .today-stats {
      .today-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 16px;
        
        .today-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 12px;
          border-radius: 6px;
          background: #fafafa;
          
          .today-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }
          
          .today-value {
            font-size: 18px;
            font-weight: 600;
            
            &.primary {
              color: #409eff;
            }
            
            &.success {
              color: #67c23a;
            }
            
            &.danger {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }
}
</style>