<template>
  <div class="runner-view">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>跑腿员管理</span>
          <div class="action-box">
            <el-select
              v-model="searchParams.status"
              placeholder="状态筛选"
              clearable
              style="width: 120px; margin-right: 10px"
              @change="handleSearch"
            >
              <el-option label="全部" :value="-1" />
              <el-option label="待审核" :value="0" />
              <el-option label="审核通过" :value="1" />
              <el-option label="审核拒绝" :value="2" />
              <el-option label="暂停服务" :value="3" />
              <el-option label="黑名单" :value="4" />
            </el-select>
            <el-input
              v-model="searchParams.keyword"
              placeholder="请输入姓名/手机号"
              clearable
              @keyup.enter="handleSearch"
              class="search-input"
              style="width: 200px; margin-right: 10px"
            >
              <template #append>
                <el-button @click="handleSearch">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
            <el-button type="primary" @click="refreshStatistics">
              <el-icon><Refresh /></el-icon> 刷新统计
            </el-button>
          </div>
        </div>
      </template>

      <!-- 统计信息 -->
      <div class="statistics-row" v-if="statistics">
        <el-row :gutter="20">
          <el-col :span="3">
            <el-statistic title="总跑腿员" :value="statistics.total_runners" />
          </el-col>
          <el-col :span="3">
            <el-statistic title="待审核" :value="statistics.pending_audit" />
          </el-col>
          <el-col :span="3">
            <el-statistic title="审核通过" :value="statistics.approved_runners" />
          </el-col>
          <el-col :span="3">
            <el-statistic title="在线人数" :value="statistics.online_runners" />
          </el-col>
          <el-col :span="3">
            <el-statistic title="工作中" :value="statistics.working_runners" />
          </el-col>
          <el-col :span="3">
            <el-statistic title="今日新增" :value="statistics.today_new_runners" />
          </el-col>
          <el-col :span="3">
            <el-statistic title="本周新增" :value="statistics.this_week_new_runners" />
          </el-col>
          <el-col :span="3">
            <el-statistic title="本月新增" :value="statistics.this_month_new_runners" />
          </el-col>
        </el-row>
      </div>

      <!-- 跑腿员列表 -->
      <el-table
        v-loading="loading"
        :data="runnerList"
        style="width: 100%; margin-top: 20px"
        border
        stripe
        :max-height="tableMaxHeight"
        highlight-current-row
        table-layout="auto"
        class="runner-table"
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="real_name" label="姓名" width="100" show-overflow-tooltip />
        <el-table-column prop="mobile" label="手机号" width="120" show-overflow-tooltip />
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">{{ row.status_desc }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="在线状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.is_online ? 'success' : 'info'">
              {{ row.is_online ? '在线' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="工作状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getWorkingStatusTagType(row.working_status)">{{ row.working_status_desc }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="current_location" label="当前位置" min-width="120" show-overflow-tooltip />
        <el-table-column label="评分" min-width="120" align="center">
          <template #default="{ row }">
            <el-rate v-model="row.score" disabled show-score text-color="#ff9900" />
          </template>
        </el-table-column>
        <el-table-column prop="order_count" label="订单数" width="80" align="center" />
        <el-table-column prop="success_count" label="成功数" width="80" align="center" />
        <el-table-column label="余额" width="100" align="center">
          <template #default="{ row }">
            ¥{{ row.balance.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="加入时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.join_time) }}
          </template>
        </el-table-column>
        <el-table-column label="最后在线" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.last_online_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)">
              详情
            </el-button>
            <el-button 
              v-if="row.status === 0" 
              type="success" 
              size="small" 
              @click="handleAudit(row, 1)"
            >
              通过
            </el-button>
            <el-button 
              v-if="row.status === 0" 
              type="warning" 
              size="small" 
              @click="handleAudit(row, 2)"
            >
              拒绝
            </el-button>
            <el-dropdown v-if="row.status !== 0" @command="(command: string) => handleStatusAction(row, command)">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="row.status === 1" command="suspend">暂停服务</el-dropdown-item>
                  <el-dropdown-item v-if="row.status === 3" command="resume">恢复服务</el-dropdown-item>
                  <el-dropdown-item v-if="row.status !== 4" command="blacklist">加入黑名单</el-dropdown-item>
                  <el-dropdown-item command="remark">编辑备注</el-dropdown-item>
                  <el-dropdown-item command="orders">查看订单</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="searchParams.page"
          v-model:page-size="searchParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 跑腿员详情对话框 -->
    <RunnerDetailDialog
      v-model="detailDialogVisible"
      :runner-id="selectedRunnerId"
      @refresh="fetchRunnerList"
    />

    <!-- 审核对话框 -->
    <RunnerAuditDialog
      v-model="auditDialogVisible"
      :runner="selectedRunner"
      :audit-status="auditStatus"
      @success="handleAuditSuccess"
    />

    <!-- 状态更新对话框 -->
    <RunnerStatusDialog
      v-model="statusDialogVisible"
      :runner="selectedRunner"
      :action="statusAction"
      @success="handleStatusSuccess"
    />

    <!-- 备注编辑对话框 -->
    <RunnerRemarkDialog
      v-model="remarkDialogVisible"
      :runner="selectedRunner"
      @success="handleRemarkSuccess"
    />

    <!-- 订单列表对话框 -->
    <RunnerOrderDialog
      v-model="ordersDialogVisible"
      :runner="selectedRunner"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, onBeforeUnmount } from 'vue';
import { onBeforeRouteLeave } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Refresh, ArrowDown } from '@element-plus/icons-vue';
import { 
  getRunnerList, 
  getRunnerStatistics, 
  deleteRunner,
  type Runner, 
  type RunnerListParams, 
  type RunnerStatistics,
  RunnerStatus,
  WorkingStatus
} from '../../api/runner';
import { formatTime } from '@/utils/format';

/**
 * 格式化日期时间
 */
function formatDateTime(dateTime: string): string {
  if (!dateTime) return '';
  return formatTime(dateTime, 'YYYY-MM-DD HH:mm:ss');
}
import RunnerDetailDialog from './components/RunnerDetailDialog.vue';
import RunnerAuditDialog from './components/RunnerAuditDialog.vue';
import RunnerStatusDialog from './components/RunnerStatusDialog.vue';
import RunnerRemarkDialog from './components/RunnerRemarkDialog.vue';
import RunnerOrderDialog from './components/RunnerOrderDialog.vue';

// 响应式数据
const loading = ref(false);
const runnerList = ref<Runner[]>([]);
const total = ref(0);
const statistics = ref<RunnerStatistics | null>(null);

// 搜索参数
const searchParams = reactive<RunnerListParams>({
  page: 1,
  pageSize: 20,
  status: -1,
  keyword: ''
});

// 对话框状态
const detailDialogVisible = ref(false);
const auditDialogVisible = ref(false);
const statusDialogVisible = ref(false);
const remarkDialogVisible = ref(false);
const ordersDialogVisible = ref(false);

// 选中的跑腿员
const selectedRunner = ref<Runner | null>(null);
const selectedRunnerId = ref<number>(0);
const auditStatus = ref<number>(1);
const statusAction = ref<string>('');

// 计算属性
const tableMaxHeight = computed(() => {
  return window.innerHeight - 400;
});

/**
 * 获取状态标签类型
 */
function getStatusTagType(status: number): string {
  const typeMap: Record<number, string> = {
    [RunnerStatus.PENDING]: 'warning',
    [RunnerStatus.APPROVED]: 'success',
    [RunnerStatus.REJECTED]: 'danger',
    [RunnerStatus.SUSPENDED]: 'info',
    [RunnerStatus.BLACKLIST]: 'danger'
  };
  return typeMap[status] || 'info';
}

/**
 * 获取工作状态标签类型
 */
function getWorkingStatusTagType(status: number): string {
  const typeMap: Record<number, string> = {
    [WorkingStatus.OFFLINE]: 'info',
    [WorkingStatus.IDLE]: 'success',
    [WorkingStatus.WORKING]: 'warning',
    [WorkingStatus.RESTING]: 'info'
  };
  return typeMap[status] || 'info';
}

/**
 * 获取跑腿员列表
 */
async function fetchRunnerList() {
  try {
    loading.value = true;
    const response = await getRunnerList(searchParams);
    runnerList.value = response.list;
    total.value = response.total;
  } catch (error) {
    console.error('获取跑腿员列表失败:', error);
    ElMessage.error('获取跑腿员列表失败');
  } finally {
    loading.value = false;
  }
}

/**
 * 获取统计信息
 */
async function fetchStatistics() {
  try {
    statistics.value = await getRunnerStatistics();
  } catch (error) {
    console.error('获取统计信息失败:', error);
  }
}

/**
 * 刷新统计信息
 */
function refreshStatistics() {
  fetchStatistics();
  ElMessage.success('统计信息已刷新');
}

/**
 * 搜索处理
 */
function handleSearch() {
  searchParams.page = 1;
  fetchRunnerList();
}

/**
 * 页面大小变化处理
 */
function handleSizeChange(size: number) {
  searchParams.pageSize = size;
  searchParams.page = 1;
  fetchRunnerList();
}

/**
 * 当前页变化处理
 */
function handleCurrentChange(page: number) {
  searchParams.page = page;
  fetchRunnerList();
}

/**
 * 查看详情
 */
function handleViewDetail(runner: Runner) {
  selectedRunnerId.value = runner.id;
  detailDialogVisible.value = true;
}

/**
 * 审核处理
 */
function handleAudit(runner: Runner, status: number) {
  selectedRunner.value = runner;
  auditStatus.value = status;
  auditDialogVisible.value = true;
}

/**
 * 审核成功回调
 */
function handleAuditSuccess() {
  fetchRunnerList();
  fetchStatistics();
}

/**
 * 状态操作处理
 */
function handleStatusAction(runner: Runner, command: string) {
  selectedRunner.value = runner;
  
  switch (command) {
    case 'suspend':
    case 'resume':
    case 'blacklist':
      statusAction.value = command;
      statusDialogVisible.value = true;
      break;
    case 'remark':
      remarkDialogVisible.value = true;
      break;
    case 'orders':
      selectedRunner.value = runner;
      selectedRunnerId.value = runner.id;
      ordersDialogVisible.value = true;
      break;
    case 'delete':
      handleDelete(runner);
      break;
  }
}

/**
 * 状态更新成功回调
 */
function handleStatusSuccess() {
  fetchRunnerList();
  fetchStatistics();
}

/**
 * 备注更新成功回调
 */
function handleRemarkSuccess() {
  fetchRunnerList();
}

/**
 * 删除处理
 */
async function handleDelete(runner: Runner) {
  try {
    await ElMessageBox.confirm(
      `确定要删除跑腿员 "${runner.real_name}" 吗？此操作不可恢复！`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await deleteRunner(runner.id);
    ElMessage.success('删除成功');
    fetchRunnerList();
    fetchStatistics();
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除跑腿员失败:', error);
      ElMessage.error('删除失败');
    }
  }
}

/**
 * 重置组件状态
 */
const resetComponentState = () => {
  // 重置列表数据
  runnerList.value = []
  total.value = 0
  statistics.value = null
  loading.value = false
  
  // 重置搜索参数
  Object.assign(searchParams, {
    page: 1,
    pageSize: 20,
    status: -1,
    keyword: ''
  })
  
  // 重置对话框状态
  detailDialogVisible.value = false
  auditDialogVisible.value = false
  statusDialogVisible.value = false
  remarkDialogVisible.value = false
  ordersDialogVisible.value = false
  
  // 重置选中状态
  selectedRunner.value = null
  selectedRunnerId.value = 0
  auditStatus.value = 1
  statusAction.value = ''
}

/**
 * 路由离开前的处理
 */
onBeforeRouteLeave((to, from, next) => {
  console.log('🔄 RunnerView 路由离开:', from.path, '->', to.path)
  
  // 关闭所有打开的对话框
  detailDialogVisible.value = false
  auditDialogVisible.value = false
  statusDialogVisible.value = false
  remarkDialogVisible.value = false
  ordersDialogVisible.value = false
  
  resetComponentState()
  next()
})

/**
 * 组件卸载前的清理
 */
onBeforeUnmount(() => {
  console.log('🧹 RunnerView 组件卸载清理')
  resetComponentState()
})

// 组件挂载时获取数据
onMounted(() => {
  fetchRunnerList();
  fetchStatistics();
});
</script>

<style scoped lang="scss">
.runner-view {
  padding: 20px;
  
  .box-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .action-box {
        display: flex;
        align-items: center;
        
        .search-input {
          width: 200px;
        }
      }
    }
    
    .statistics-row {
      margin-bottom: 20px;
      padding: 20px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
    
    .runner-table {
      .el-rate {
        height: 20px;
      }
    }
    
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}
</style>