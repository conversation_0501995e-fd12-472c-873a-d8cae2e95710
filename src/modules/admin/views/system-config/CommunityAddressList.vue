<!--
 * @Description: 社区地址管理 - 列表页面
 * @Author: AI Assistant
 * @Date: 2025-06-09
 * @Version: 1.0.0
 * 提供社区地址的增删改查功能，支持多级地址展示和管理
-->
<template>
  <div class="community-address-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h2>社区地址管理</h2>
        </div>
      </template>

      <!-- 搜索和操作区域 -->
      <ListSearch 
        v-model="listQuery"
        @search="handleFilter"
        @reset="resetQuery"
      />

      <!-- 操作按钮 -->
      <ActionButtons 
        :view-mode="viewMode"
        @create="handleCreate"
        @refresh-cache="handleRefreshCache"
        @toggle-view="toggleViewMode"
      />

      <!-- 树形视图 -->
      <TreeView
        v-if="viewMode === 'tree'"
        :address-list="addressList"
        :loading="listLoading"
        @create="handleCreate"
        @update="handleUpdate"
        @delete="handleDelete"
      />

      <!-- 列表视图 -->
      <TableView
        v-else
        :address-list="paginatedAddressList"
        :loading="listLoading"
        :current-page="currentPage"
        :page-size="pageSize"
        :total="totalItems"
        @create="handleCreate"
        @update="handleUpdate"
        @delete="handleDelete"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
      />

      <!-- 添加/编辑地址对话框 -->
      <AddressDialog
        v-model:visible="dialogFormVisible"
        :address-options="addressOptions"
        :is-create="dialogStatus === 'create'"
        :address-data="temp"
        @create="createData"
        @update="updateData"
      />
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { cloneDeep } from 'lodash'
import {
  getCommunityAddressList,
  getCommunityAddressTree,
  getCommunityAddressOptions,
  createCommunityAddress,
  updateCommunityAddress,
  deleteCommunityAddress,
  refreshCommunityAddressCache,
  type CommunityAddress,
  type CommunityAddressQueryParams,
  type CommunityAddressOption,
  type CreateCommunityAddressRequest,
  type UpdateCommunityAddressRequest
} from '@/modules/admin/api/communityAddress'

// 导入子组件
import { 
  ListSearch, 
  ActionButtons, 
  TreeView, 
  TableView, 
  AddressDialog 
} from './components'

// 响应式数据
const listLoading = ref(false)
const dialogFormVisible = ref(false)
const dialogStatus = ref('')
const addressList = ref<CommunityAddress[]>([])
const flatAddressList = ref<CommunityAddress[]>([])
const addressOptions = ref<CommunityAddressOption[]>([])
const viewMode = ref<'tree' | 'list'>('tree')
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)

// 查询参数
const listQuery = reactive<CommunityAddressQueryParams>({
  name: '',
  level: undefined,
  status: undefined,
  page: 1,
  pageSize: 10
})

// 表单临时数据
const temp = ref<Partial<CommunityAddress & CreateCommunityAddressRequest & UpdateCommunityAddressRequest>>({
  id: undefined,
  name: '',
  parentId: null,
  level: 1,
  longitude: 0,
  latitude: 0,
  sort: 0,
  status: 1,
  description: ''
})

// 计算属性
const paginatedAddressList = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return flatAddressList.value.slice(start, end)
})

// 生命周期钩子
onMounted(() => {
  getList()
  loadAddressOptions()
})

// 方法
/**
 * @function getList
 * @description 获取地址列表数据
 */
const getList = async () => {
  listLoading.value = true
  try {
    if (viewMode.value === 'tree') {
      const res:any = await getCommunityAddressTree()
      addressList.value = Array.isArray(res) ? res : (res.items || [])
      // 同时获取扁平化列表用于搜索
      flattenAddressList(addressList.value)
    } else {
      // 处理listquery参数
      const params = cloneDeep(listQuery)
      if (params.name !== undefined && params.name === '') {
        delete params.name;
      }
      
      const res:any = await getCommunityAddressList({
        ...params,
        page: currentPage.value,
        pageSize: pageSize.value
      })
      
      if (res && typeof res === 'object') {
        // 适配新的API返回格式：{list, total, page, pageSize}
        flatAddressList.value = res.list || res.items || []
        totalItems.value = res.total || 0
      } else {
        flatAddressList.value = Array.isArray(res) ? res : []
        totalItems.value = flatAddressList.value.length
      }
    }
  } catch (error) {
    console.error('获取地址列表失败:', error)
    ElMessage.error('获取地址列表失败')
  } finally {
    listLoading.value = false
  }
}

/**
 * @function flattenAddressList
 * @description 将树形结构扁平化
 * @param tree 树形数据
 */
const flattenAddressList = (tree: CommunityAddress[]) => {
  const result: CommunityAddress[] = []
  const flatten = (nodes: CommunityAddress[]) => {
    nodes.forEach(node => {
      result.push(node)
      if (node.children && node.children.length > 0) {
        flatten(node.children)
      }
    })
  }
  flatten(tree)
  flatAddressList.value = result
  totalItems.value = result.length
}

/**
 * @function loadAddressOptions
 * @description 加载地址选择器选项
 */
const loadAddressOptions = async () => {
  try {
    const res:any = await getCommunityAddressOptions()
    addressOptions.value = res.options || []
  } catch (error) {
    console.error('获取地址选项失败:', error)
  }
}

/**
 * @function resetTemp
 * @description 重置临时数据
 */
const resetTemp = () => {
  temp.value = {
    id: undefined,
    name: '',
    parentId: null,
    level: 1,
    longitude: 0,
    latitude: 0,
    sort: 0,
    status: 1,
    description: ''
  }
}

/**
 * @function handleCreate
 * @description 处理创建地址
 * @param row 父级地址数据
 */
const handleCreate = (row?: CommunityAddress) => {
  resetTemp()
  dialogStatus.value = 'create'
  if (row) {
    temp.value.parentId = row.id || null
    temp.value.level = (row.level || 0) + 1
    // 如果存在父组件，使用父组件的经纬度
    temp.value.longitude = row.longitude || 0
    temp.value.latitude = row.latitude || 0
  } else {
    // 如果没有父组件，使用环境变量中的默认经纬度
    temp.value.longitude = Number(import.meta.env.VITE_TIANDITU_DEFAULT_LNG) || 106.635604
    temp.value.latitude = Number(import.meta.env.VITE_TIANDITU_DEFAULT_LAT) || 26.379516
  }
  dialogFormVisible.value = true
}

/**
 * @function handleUpdate
 * @description 处理编辑地址
 * @param row 地址数据
 */
const handleUpdate = (row: CommunityAddress) => {
  temp.value = { ...row }
  dialogStatus.value = 'update'
  dialogFormVisible.value = true
}

/**
 * @function createData
 * @description 创建地址数据
 */
const createData = async (createData: CreateCommunityAddressRequest) => {
  try {
    await createCommunityAddress(createData)
    ElMessage.success('创建成功')
    dialogFormVisible.value = false
    getList()
    loadAddressOptions() // 刷新选项
  } catch (error) {
    console.error('创建地址失败:', error)
    ElMessage.error('创建地址失败')
  }
}

/**
 * @function updateData
 * @description 更新地址数据
 */
const updateData = async (updateData: UpdateCommunityAddressRequest) => {
  try {
    await updateCommunityAddress(updateData)
    ElMessage.success('更新成功')
    dialogFormVisible.value = false
    getList()
    loadAddressOptions() // 刷新选项
  } catch (error) {
    console.error('更新地址失败:', error)
    ElMessage.error('更新地址失败')
  }
}

/**
 * @function handleDelete
 * @description 处理删除地址
 * @param row 地址数据
 */
const handleDelete = async (row: CommunityAddress) => {
  if (!row.id) return
  try {
    await deleteCommunityAddress(row.id)
    ElMessage.success('删除成功')
    getList()
    loadAddressOptions() // 刷新选项
  } catch (error) {
    console.error('删除地址失败:', error)
    ElMessage.error('删除地址失败')
  }
}

/**
 * @function handleFilter
 * @description 处理搜索过滤
 */
const handleFilter = () => {
  currentPage.value = 1
  getList()
}

/**
 * @function resetQuery
 * @description 重置查询条件
 */
const resetQuery = () => {
  listQuery.name = ''
  listQuery.level = undefined
  listQuery.status = undefined
  handleFilter()
}

/**
 * @function handlePageChange
 * @description 处理页码变化
 * @param page 新页码
 */
const handlePageChange = (page: number) => {
  currentPage.value = page
  if (viewMode.value === 'list') {
    getList()
  }
}

/**
 * @function handleSizeChange
 * @description 处理每页大小变化
 * @param size 新的每页大小
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  if (viewMode.value === 'list') {
    getList()
  }
}

/**
 * @function toggleViewMode
 * @description 切换视图模式
 */
const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'tree' ? 'list' : 'tree'
  currentPage.value = 1
  getList()
}

/**
 * @function handleRefreshCache
 * @description 刷新缓存
 */
const handleRefreshCache = async () => {
  try {
    await ElMessageBox.confirm('确定要刷新地址缓存吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await refreshCommunityAddressCache()
    ElMessage.success('缓存刷新成功')
    getList()
    loadAddressOptions()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('刷新缓存失败:', error)
      ElMessage.error('刷新缓存失败')
    }
  }
}
</script>

<style scoped>
.community-address-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
