<!--
 * @Description: 系统配置管理主组件
 * @Author: AI Assistant
 * @Date: 2025-05-28
 -->
<template>
  <div class="system-config-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h2>系统配置管理</h2>
        </div>
      </template>
      
      <!-- 标签页切换 -->
      <el-tabs v-model="activeCategory" @tab-click="handleCategoryChange">
        <el-tab-pane label="系统配置" name="system"></el-tab-pane>
        <el-tab-pane label="Banner配置" name="banner"></el-tab-pane>
        <el-tab-pane label="App菜单配置" name="appmenu"></el-tab-pane>
        <el-tab-pane label="上传配置" name="upload"></el-tab-pane>
        <el-tab-pane label="短信配置" name="sms"></el-tab-pane>
        <el-tab-pane label="社区地址管理" name="community-address"></el-tab-pane> <!-- 新增社区地址管理标签页 -->
        <el-tab-pane label="文件用途配置" name="fileUsage"></el-tab-pane>
        <el-tab-pane label="配送费配置" name="deliveryFee"></el-tab-pane>
      </el-tabs>
      
      <!-- 工具栏组件 -->
      <config-toolbar
        v-if="activeCategory === 'system' || activeCategory === 'banner' || activeCategory === 'appmenu' || activeCategory === 'deliveryFee'"
        :search-query="searchQuery"
        @search="handleSearch"
        @refresh-cache="refreshCache"
        @add="openDialog"
      />

      <!-- 配置列表组件 - 只在系统配置、Banner配置、App菜单配置和配送费配置标签页显示 -->
      <config-list
        v-if="activeCategory === 'system' || activeCategory === 'banner' || activeCategory === 'appmenu' || activeCategory === 'deliveryFee'"
        :config-list="configList"
        :loading="loading"
        :current-page="currentPage"
        :page-size="pageSize"
        :total="totalItems"
        @edit="editConfig"
        @delete="handleDeleteConfig"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
      />
      
      <!-- 上传配置组件 - 只在上传配置标签页显示 -->
      <upload-config v-if="activeCategory === 'upload'" />

      <!-- 短信配置组件 - 只在短信配置标签页显示 -->
      <sms-config v-if="activeCategory === 'sms'" />

      <!-- 社区地址管理组件 - 只在社区地址管理标签页显示 -->
      <community-address-list v-if="activeCategory === 'community-address'" />

      <!-- 文件用途配置组件 - 只在文件用途配置标签页显示 -->
      <file-usage-config v-if="activeCategory === 'fileUsage'" />
    </el-card>

    <!-- 配置表单组件 -->
    <config-form
      v-model:visible="dialogVisible"
      :is-edit="isEdit"
      :config-data="form"
      :category="activeCategory"
      :loading="loading"
      @submit="submitForm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'

import { 
  getConfigList, 
  createConfig, 
  updateConfig, 
  deleteConfig, 
  refreshConfigCache,
  type SystemConfig,
  type QueryParams
} from '../../api/systemConfig'

// 导入子组件
import ConfigToolbar from './components/ConfigToolbar.vue'
import ConfigList from './components/ConfigList.vue'
import ConfigForm from './components/ConfigForm.vue'
import UploadConfig from './UploadConfig.vue'
import SmsConfig from './SmsConfig.vue'
import CommunityAddressList from './CommunityAddressList.vue' // 新增社区地址管理组件导入
import FileUsageConfig from './FileUsageConfig.vue'

// 状态管理
const loading = ref(false)
const configList = ref<SystemConfig[]>([])
const totalItems = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')
const activeCategory = ref('system') // 当前激活的分类标签
const dialogVisible = ref(false)
const isEdit = ref(false)

// 表单数据
const form = reactive<SystemConfig>({
  id: '',
  configKey: '',
  configValue: '',
  configType: 'text',
  description: '',
  category: 'system',
  status: 0,
  isSystem: 0,
})

/**
 * 处理分类切换
 * @param tab 标签页信息
 */
const handleCategoryChange = (tab: any) => {
  // 切换分类时重置页码并重新加载
  console.log('切换到分类——:', tab.props.name)
  
  // 如果切换到上传配置、短信配置、社区地址管理或文件用途配置，不需要加载配置列表
  if (tab.props.name === 'upload' || tab.props.name === 'sms' || tab.props.name === 'community-address' || tab.props.name === 'fileUsage') {
    return
  }
  
  currentPage.value = 1
  // 手动指定当前分类，不使用activeCategory，因为它可能在loadConfigs调用时还没有更新
  loadConfigs(tab.props.name)
}

/**
 * 获取配置列表
 * @param categoryParam 可选的分类参数
 */
const loadConfigs = async (categoryParam?: string) => {
  try {
    loading.value = true
    // 使用传入的分类参数或当前活动分类
    const category = categoryParam || activeCategory.value
    
    console.log('加载配置列表，分类:', category)
    
    const params: QueryParams = {
      page: currentPage.value,
      page_size: pageSize.value,
      query: searchQuery.value,
      category // 使用确定的分类参数
    }
    
    const response = await getConfigList(params)
    console.log('API返回数据:', response)
    
    // 处理返回的数据列表
    if (Array.isArray(response)) {
      // 如果是直接返回数组，应用搜索过滤
      const filteredConfigs = searchQuery.value ? 
        response.filter(item => 
          item.configKey?.toLowerCase().includes(searchQuery.value.toLowerCase()) || 
          item.description?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
          String(item.configValue).toLowerCase().includes(searchQuery.value.toLowerCase())
        ) : response
      
      // 处理前端分页
      const startIdx = (currentPage.value - 1) * pageSize.value
      const endIdx = startIdx + pageSize.value
      
      configList.value = filteredConfigs.slice(startIdx, endIdx)
      totalItems.value = filteredConfigs.length
    } else if (response && typeof response === 'object') {
      // 如果返回是分页对象格式
      if (Array.isArray(response.list)) {
        // 新的API返回格式: {list, total, page, pageSize}
        configList.value = response.list
        totalItems.value = response.total || 0
      } else if (Array.isArray(response.items)) {
        // 兼容旧格式: {items, total}
        configList.value = response.items
        totalItems.value = response.total || 0
      } else {
        configList.value = []
        totalItems.value = 0
      }
    } else {
      configList.value = []
      totalItems.value = 0
    }
  } catch (error) {
    console.error('获取配置列表失败', error)
    ElMessage.error('获取配置列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索
 * @param value 搜索关键字
 */
const handleSearch = (value: string) => {
  searchQuery.value = value
  currentPage.value = 1 // 重置到第一页
  loadConfigs()
}

/**
 * 处理页码变化
 * @param page 新的页码
 */
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadConfigs()
}

/**
 * 处理每页大小变化
 * @param size 新的每页大小
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1 // 重置到第一页
  loadConfigs()
}

/**
 * 打开对话框
 */
const openDialog = () => {
  isEdit.value = false
  // 规划不同分类的默认配置
  let defaultConfig = {
    id: '',
    configKey: '',
    configValue: '',
    configType: 'text',
    description: '',
    category: activeCategory.value, // 设置当前分类
    status: 0,
    isSystem: 0,
  }
  
  // Banner分类默认使用JSON格式
  if (activeCategory.value === 'banner') {
    defaultConfig.configType = 'json'
    defaultConfig.configValue = '[]' // 空数组作为默认值
  }
  
  // App菜单分类默认使用JSON格式
  if (activeCategory.value === 'appmenu') {
    defaultConfig.configType = 'json'
    defaultConfig.configValue = '[]' // 空数组作为默认值
  }
  
  // 配送费分类默认使用JSON格式
  if (activeCategory.value === 'deliveryFee') {
    defaultConfig.configType = 'json'
    defaultConfig.configValue = JSON.stringify({
      deliveryBaseFee: 10.0,
      deliveryKmFee: 2.0,
      deliveryMinOrderAmount: 0.0,
      deliveryFreeEnabled: false,
      deliveryFreeAmount: 30.0,
      deliveryDiscountEnabled: false,
      deliveryDiscountAmount: 20.0,
      deliveryDiscountRate: 0.8
    }, null, 2)
  }
  
  Object.assign(form, defaultConfig)
  dialogVisible.value = true
}

/**
 * 编辑配置
 * @param row 当前行数据
 */
const editConfig = (row: SystemConfig) => {
  isEdit.value = true
  Object.assign(form, {
    id: row.id,
    configKey: row.configKey,
    configValue: row.configValue,
    configType: row.configType,
    description: row.description,
    category: row.category || activeCategory.value, // 保留原分类或使用当前分类
    status: row.status,
    isSystem: row.isSystem,
  })
  dialogVisible.value = true
}

/**
 * 删除系统配置
 * @param id 配置ID
 */
const handleDeleteConfig = async (id: string | number) => {
  try {
    loading.value = true
    await deleteConfig(id)
    ElMessage.success('删除成功')
    loadConfigs()
  } catch (error) {
    console.error('删除配置失败', error)
    ElMessage.error('删除配置失败')
  } finally {
    loading.value = false
  }
}

/**
 * 提交表单
 * @param formData 表单数据
 */
const submitForm = async (formData: SystemConfig) => {
  try {
    loading.value = true
    
    if (isEdit.value) {
      // 更新
      await updateConfig(formData.id, formData)
      ElMessage.success('更新成功')
    } else {
      // 创建
      await createConfig(formData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadConfigs()
  } catch (error) {
    console.error(isEdit.value ? '更新配置失败' : '创建配置失败', error)
    ElMessage.error(isEdit.value ? '更新配置失败' : '创建配置失败')
  } finally {
    loading.value = false
  }
}

/**
 * 刷新缓存
 */
const refreshCache = async () => {
  try {
    loading.value = true
    await refreshConfigCache()
    ElMessage.success('缓存已刷新')
  } catch (error) {
    console.error('刷新缓存失败', error)
    ElMessage.error('刷新缓存失败')
  } finally {
    loading.value = false
  }
}

// 监听分页变化自动加载配置
watch([currentPage, pageSize], () => {
  loadConfigs()
})

// 组件加载完成后加载配置
onMounted(() => {
  loadConfigs()
})
</script>

<style scoped>
.system-config-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
