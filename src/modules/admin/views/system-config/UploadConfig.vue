<!--
 * @Description: 系统模块 - 上传配置管理主组件
 * @Author: Cascade
 * @Date: 2025-06-07
 -->
<template>
  <div class="upload-config-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h2>文件上传配置</h2>
        </div>
      </template>

      <el-spin :spinning="loading">
        <el-form
          ref="formRef"
          :model="formState"
          :rules="rules"
          label-width="120px"
        >
          <el-divider>基本配置</el-divider>
          
          <el-form-item label="存储方式" prop="storageMode">
            <el-radio-group v-model="formState.storageMode" @change="handleStorageModeChange">
              <el-radio-button value="local">本地存储</el-radio-button>
              <el-radio-button value="oss">阿里云OSS</el-radio-button>
              <el-radio-button value="cos">腾讯云COS</el-radio-button>
              <el-radio-button value="s3">AWS S3</el-radio-button>
              <el-radio-button value="qiniu">七牛云</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="最大文件大小" prop="maxSizeMB">
            <el-input-number 
              v-model="formState.maxSizeMB" 
              :min="0" 
              :precision="0" 
            />
            <span class="form-hint">MB (0表示不限制)</span>
          </el-form-item>
          
          <el-form-item label="允许的文件类型" prop="allowedExtensions">
            <el-input 
              v-model="formState.allowedExtensions" 
              placeholder="输入允许上传的文件扩展名，用英文逗号分隔，如：jpg,png,pdf"
              style="width: 500px"
            />
          </el-form-item>
          
          <el-form-item label="CDN加速" prop="enableCdn">
            <el-radio-group v-model="formState.enableCdn">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item 
            v-if="formState.enableCdn === 1" 
            label="CDN域名" 
            prop="cdnDomain"
          >
            <el-input 
              v-model="formState.cdnDomain" 
              placeholder="请输入CDN域名，例如：https://cdn.example.com"
              style="width: 500px"
            />
          </el-form-item>
          
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formState.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="备注" prop="remark">
            <el-input 
              v-model="formState.remark" 
              type="textarea"
              :rows="2"
              placeholder="备注信息"
              style="width: 500px"
            />
          </el-form-item>

          <!-- 根据选择的存储方式显示不同的配置组件 -->
          <local-storage-config v-if="formState.storageMode === 'local'" v-model:config="formState.config" />
          <oss-storage-config v-if="formState.storageMode === 'oss'" v-model:config="formState.config" />
          <cos-storage-config v-if="formState.storageMode === 'cos'" v-model:config="formState.config" />
          <s3-storage-config v-if="formState.storageMode === 's3'" v-model:config="formState.config" />
          <qiniu-storage-config v-if="formState.storageMode === 'qiniu'" v-model:config="formState.config" />
          
          <!-- CDN高级配置 -->
          <cdn-config v-if="formState.enableCdn === 1 && formState.storageMode !== 'local'" v-model:config="formState.config" />
          
          <!-- 提交按钮 -->
          <el-form-item>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">保存配置</el-button>
            <el-button @click="resetForm">重置</el-button>
            <el-button 
              type="info" 
              @click="testConnection" 
              :loading="testLoading"
              :disabled="formState.storageMode === 'local'"
            >测试连接</el-button>
          </el-form-item>
        </el-form>
      </el-spin>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getUploadConfig, updateUploadConfig, testStorageConnection, type UploadConfigType } from '../../api/uploadConfig'

// 导入子组件
import LocalStorageConfig from './components/upload-config/LocalStorageConfig.vue'
import OssStorageConfig from './components/upload-config/OssStorageConfig.vue'
import CosStorageConfig from './components/upload-config/CosStorageConfig.vue'
import S3StorageConfig from './components/upload-config/S3StorageConfig.vue'
import QiniuStorageConfig from './components/upload-config/QiniuStorageConfig.vue'
import CdnConfig from './components/upload-config/CdnConfig.vue'

// 状态
const loading = ref(false)
const submitLoading = ref(false)
const testLoading = ref(false)
const formRef = ref()

// 表单数据
const formState = reactive<UploadConfigType>({
  id: null,
  storageMode: 'local',
  maxSize: 10485760,
  maxSizeMB: 10,
  allowedExtensions: 'jpg,png,gif,pdf,doc,docx,xls,xlsx,zip,rar',
  enableCdn: 0,
  cdnDomain: '',
  status: 1,
  remark: '',
  config: {
    localPath: './uploads'
  }
})

// 表单验证规则
const rules = {
  storageMode: [{ required: true, message: '请选择存储方式', trigger: 'change' }],
  maxSizeMB: [{ required: true, message: '请输入最大文件大小', trigger: 'blur' }],
  allowedExtensions: [{ required: true, message: '请输入允许的文件类型', trigger: 'blur' }],
  cdnDomain: [{ required: true, message: '请输入CDN域名', trigger: 'blur' }]
}

// 处理存储方式变更
const handleStorageModeChange = (value: string) => {
  // 根据存储方式重置配置
  switch (value) {
    case 'local':
      formState.config = { localPath: './uploads' }
      break
    case 'oss':
      formState.config = { 
        endpoint: '',
        bucket: '',
        accessKey: '',
        accessSecret: '',
        domain: ''
      }
      break
    case 'cos':
      formState.config = { 
        region: 'ap-shanghai',
        bucket: '',
        secretId: '',
        secretKey: '',
        domain: ''
      }
      break
    case 's3':
      formState.config = { 
        region: 'us-east-1',
        bucket: '',
        accessKey: '',
        secretKey: '',
        domain: ''
      }
      break
    case 'qiniu':
      formState.config = {
        accessKey: '',
        secretKey: '',
        bucket: '',
        zone: 'z0', // 默认华东
        useHTTPS: true,
        domain: ''
      }
      break
  }
}

// 获取上传配置
const fetchUploadConfig = async () => {
  try {
    loading.value = true
    const response = await getUploadConfig()
    
    // 计算MB值（后端返回byte单位）
    if (response.maxSize) {
      response.maxSizeMB = Math.floor(response.maxSize / (1024 * 1024))
    }
    
    Object.assign(formState, response)
    ElMessage.success('配置加载成功')
  } catch (error) {
    console.error('获取上传配置失败', error)
    ElMessage.error('获取上传配置失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    // 将MB转换为byte
    formState.maxSize = formState.maxSizeMB ? formState.maxSizeMB * 1024 * 1024 : 0
    
    await updateUploadConfig(formState)
    ElMessage.success('上传配置更新成功')
  } catch (error) {
    console.error('更新上传配置失败', error)
    ElMessage.error('更新上传配置失败')
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
  fetchUploadConfig()
}

// 测试连接
const testConnection = async () => {
  try {
    await formRef.value.validate()
    
    testLoading.value = true
    await testStorageConnection(formState)
    ElMessage.success('连接测试成功')
  } catch (error) {
    console.error('连接测试失败', error)
    ElMessage.error('连接测试失败')
  } finally {
    testLoading.value = false
  }
}

// 组件加载完成后获取配置
onMounted(() => {
  fetchUploadConfig()
})
</script>

<style scoped>
.upload-config-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-hint {
  margin-left: 10px;
  color: #909399;
}
</style>
