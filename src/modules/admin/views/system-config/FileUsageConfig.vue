<!--
 * @Description: 文件用途配置管理组件
 * @Author: Cascade
 * @Date: 2025-06-18
 -->
<template>
  <div class="file-usage-config-container">
    <!-- 工具栏 -->
    <el-card class="box-card" shadow="never">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="用途代码">
          <el-input v-model="queryParams.usageCode" placeholder="请输入用途代码" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="用途名称">
          <el-input v-model="queryParams.usageName" placeholder="请输入用途名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable @change="handleQuery">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleQuery">搜索</el-button>
          <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            :icon="Plus"
            @click="handleAdd"
            v-auth="['admin', 'system:fileusage:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            :icon="EditPen"
            :disabled="single"
            @click="handleUpdate"
            v-auth="['admin', 'system:fileusage:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            :icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-auth="['admin', 'system:fileusage:delete']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            :icon="RefreshRight"
            @click="handleRefreshCache"
            v-auth="['admin', 'system:fileusage:refresh']"
          >刷新缓存</el-button>
        </el-col>
         <el-col :span="1.5">
          <el-button
            type="info"
            plain
            :icon="MagicStick"
            @click="handleInitDefault"
            v-auth="['admin', 'system:fileusage:init']"
          >初始化默认配置</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 表格数据 -->
    <el-card class="box-card mt-4" shadow="never">
      <el-table v-loading="loading" :data="configList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" prop="id" width="80" align="center" />
        <el-table-column label="用途代码" prop="usage_code" :show-overflow-tooltip="true" />
        <el-table-column label="用途名称" prop="usage_name" :show-overflow-tooltip="true" />
        <el-table-column label="允许匿名" align="center" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.allow_anonymous === 1 ? 'success' : 'info'">
              {{ scope.row.allow_anonymous === 1 ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="最大文件(MB)" prop="max_file_size_mb" align="center" width="120">
          <template #default="scope">
            {{ scope.row.max_file_size === 0 ? '全局配置' : scope.row.max_file_size_mb }}
          </template>
        </el-table-column>
        <el-table-column label="允许类型" prop="allowed_types" :show-overflow-tooltip="true">
           <template #default="scope">
            {{ scope.row.allowed_types || '全局配置' }}
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort_order" align="center" width="80" />
        <el-table-column label="状态" align="center" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
              v-auth="['admin', 'system:fileusage:edit']"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="created_at" width="160" align="center">
          <template #default="scope">
            <span>{{ formatTime(scope.row.created_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              link
              type="primary"
              :icon="EditPen"
              @click="handleUpdate(scope.row)"
              v-auth="['admin', 'system:fileusage:edit']"
            >修改</el-button>
            <el-button
              link
              type="danger"
              :icon="Delete"
              @click="handleDelete(scope.row)"
              v-auth="['admin', 'system:fileusage:delete']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-show="total > 0"
        :total="total"
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="getList"
        @size-change="getList"
      />
    </el-card>

    <!-- 添加或修改文件用途配置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="700px" append-to-body @close="cancel">
      <el-form ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用途代码" prop="usage_code">
              <el-input v-model="form.usage_code" placeholder="请输入用途代码" :disabled="dialog.isEdit" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用途名称" prop="usage_name">
              <el-input v-model="form.usage_name" placeholder="请输入用途名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="用途描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入用途描述" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="允许匿名上传" prop="allow_anonymous">
              <el-radio-group v-model="form.allow_anonymous">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="最大文件大小" prop="max_file_size_mb_input">
              <el-input-number v-model="form.max_file_size_mb_input" :min="0" controls-position="right" style="width: 100%;"/>
               <div class="el-form-item__extra-info">MB (0 表示使用全局配置)</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort_order">
              <el-input-number v-model="form.sort_order" :min="0" controls-position="right" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="允许的文件类型" prop="allowed_types">
          <el-input v-model="form.allowed_types" placeholder="例如: jpg,png,gif (逗号分隔, 空表示全局配置)" />
           <div class="el-form-item__extra-info">多个类型用英文逗号分隔，留空表示使用全局配置</div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  Search, Refresh, Plus, EditPen, Delete, RefreshRight, MagicStick
} from '@element-plus/icons-vue'
import {
  getFileUsageConfigList,
  getFileUsageConfigDetail,
  saveFileUsageConfig,
  deleteFileUsageConfig,
  refreshFileUsageCache,
  initDefaultFileUsageConfig,
  type FileUsageConfigItem,
  type FileUsageConfigListParams,
  type SaveFileUsageConfigRequest
} from '../../api/fileUsageConfig' // 确保路径正确
// import { formatTime } from '@/utils/formatTime' // 假设有时间格式化工具, 暂时使用内置方法

// 简易时间格式化函数 (如果项目中没有全局的 formatTime)
const formatTime = (timeStr: string | undefined) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleString() // 或者更详细的格式化 date.getFullYear() + '-' + ...
};


// 加载状态
const loading = ref(true)
// 配置列表
const configList = ref<FileUsageConfigItem[]>([])
// 总条数
const total = ref(0)
// 选中数组
const ids = ref<(number | string)[]>([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)

// 查询参数
const queryParams = reactive<FileUsageConfigListParams>({
  page: 1,
  pageSize: 10,
  usageCode: undefined,
  usageName: undefined,
  status: undefined,
  sortField: 'sort_order',
  sortOrder: 'asc'
})

// 对话框状态
const dialog = reactive({
  visible: false,
  title: '',
  isEdit: false
})

// 表单实例
const dataFormRef = ref<FormInstance | null>(null)

// 表单数据
const form = reactive<SaveFileUsageConfigRequest & { max_file_size_mb_input?: number }> ({
  id: null,
  usage_code: '',
  usage_name: '',
  description: '',
  allow_anonymous: 0,
  max_file_size: 0, // 存储字节
  max_file_size_mb_input: 0, // 用于表单输入 MB
  allowed_types: '',
  sort_order: 10,
  status: 1,
  remark: ''
})

// 表单校验规则
const rules = reactive<FormRules>({
  usage_code: [
    { required: true, message: '用途代码不能为空', trigger: 'blur' },
    { max: 50, message: '用途代码长度不能超过50个字符', trigger: 'blur' }
  ],
  usage_name: [
    { required: true, message: '用途名称不能为空', trigger: 'blur' },
    { max: 100, message: '用途名称长度不能超过100个字符', trigger: 'blur' }
  ],
  allow_anonymous: [{ required: true, message: '请选择是否允许匿名上传', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  max_file_size_mb_input: [
    { type: 'number', message: '文件大小必须为数字值', trigger: 'blur' }
  ],
  sort_order: [
    { type: 'number', message: '排序必须为数字值', trigger: 'blur' }
  ]
})

/**
 * 获取列表
 */
async function getList() {
  loading.value = true
  try {
    const response = await getFileUsageConfigList(queryParams)
    configList.value = response.list
    total.value = response.total
  } catch (error) {
    ElMessage.error('获取列表失败')
    console.error('获取列表失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 搜索按钮操作
 */
function handleQuery() {
  queryParams.page = 1
  getList()
}

/**
 * 重置按钮操作
 */
function resetQuery() {
  queryParams.page = 1
  queryParams.usageCode = undefined
  queryParams.usageName = undefined
  queryParams.status = undefined
  queryParams.sortField = 'sort_order'
  queryParams.sortOrder = 'asc'
  handleQuery()
}

/**
 * 多选框选中数据
 */
function handleSelectionChange(selection: FileUsageConfigItem[]) {
  ids.value = selection.map(item => item.id!)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/**
 * 重置表单
 */
function resetForm() {
  form.id = null
  form.usage_code = ''
  form.usage_name = ''
  form.description = ''
  form.allow_anonymous = 0
  form.max_file_size = 0
  form.max_file_size_mb_input = 0
  form.allowed_types = ''
  form.sort_order = 10
  form.status = 1
  form.remark = ''
  // 清除校验状态
  nextTick(() => {
    dataFormRef.value?.clearValidate()
  })
}

/**
 * 新增按钮操作
 */
function handleAdd() {
  resetForm()
  dialog.isEdit = false
  dialog.title = '添加文件用途配置'
  dialog.visible = true
}

/**
 * 修改按钮操作
 */
async function handleUpdate(row?: FileUsageConfigItem) {
  resetForm()
  const currentId = row?.id || ids.value[0]
  if (!currentId) return

  try {
    loading.value = true
    const response = await getFileUsageConfigDetail(currentId)
    Object.assign(form, response)
    // MB 转换
    form.max_file_size_mb_input = response.max_file_size && response.max_file_size > 0 
                                ? Math.round(response.max_file_size / (1024 * 1024)) 
                                : 0;
    dialog.isEdit = true
    dialog.title = '修改文件用途配置'
    dialog.visible = true
  } catch (error) {
    ElMessage.error('获取详情失败')
    console.error('获取详情失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 状态修改
 */
async function handleStatusChange(row: FileUsageConfigItem) {
  const text = row.status === 1 ? '启用' : '禁用'
  try {
    await ElMessageBox.confirm(`确认要"${text}"${row.usage_name}"吗?`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await saveFileUsageConfig({ 
        id: row.id, 
        usage_code: row.usage_code, // 确保传递了必要字段
        usage_name: row.usage_name,
        allow_anonymous: row.allow_anonymous,
        status: row.status 
    })
    ElMessage.success(`${text}成功`)
    getList() // 刷新列表以显示最新状态，或者仅更新当前行状态
  } catch (error) {
    ElMessage.error(`${text}失败`)
    // 恢复状态
    row.status = row.status === 1 ? 0 : 1
    console.error('状态修改失败:', error)
  }
}

/**
 * 删除按钮操作
 */
async function handleDelete(row?: FileUsageConfigItem) {
  const deleteIds = row?.id ? [row.id] : ids.value
  if (deleteIds.length === 0) return

  try {
    await ElMessageBox.confirm('是否确认删除选中的数据项?', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await Promise.all(deleteIds.map(id => deleteFileUsageConfig(id)))
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    ElMessage.error('删除失败')
    console.error('删除失败:', error)
  }
}

/**
 * 刷新缓存按钮操作
 */
async function handleRefreshCache() {
  try {
    loading.value = true;
    await refreshFileUsageCache()
    ElMessage.success('缓存刷新成功')
  } catch (error) {
    ElMessage.error('缓存刷新失败')
    console.error('缓存刷新失败:', error)
  } finally {
    loading.value = false;
  }
}

/**
 * 初始化默认配置按钮操作
 */
async function handleInitDefault() {
  try {
    await ElMessageBox.confirm('是否确认初始化系统默认的文件用途配置？此操作可能会覆盖现有同名配置。', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    loading.value = true;
    await initDefaultFileUsageConfig()
    ElMessage.success('默认配置初始化成功')
    getList() // 刷新列表
  } catch (error) {
    if (error !== 'cancel') { // 用户取消操作时，ElMessageBox会reject 'cancel'
        ElMessage.error('初始化失败')
        console.error('初始化默认配置失败:', error)
    }
  } finally {
    loading.value = false;
  }
}


/**
 * 提交按钮
 */
async function submitForm() {
  if (!dataFormRef.value) return
  await dataFormRef.value.validate(async (valid) => {
    if (valid) {
      // MB 转换为 Bytes
      if (form.max_file_size_mb_input && form.max_file_size_mb_input > 0) {
        form.max_file_size = form.max_file_size_mb_input * 1024 * 1024;
      } else {
        form.max_file_size = 0; // 0 表示使用全局配置
      }
      // 移除临时的mb输入字段，避免发送到后端
      const saveData: SaveFileUsageConfigRequest = { ...form };
      delete (saveData as any).max_file_size_mb_input;

      try {
        loading.value = true
        await saveFileUsageConfig(saveData)
        ElMessage.success(dialog.isEdit ? '修改成功' : '新增成功')
        dialog.visible = false
        getList()
      } catch (error) {
        ElMessage.error(dialog.isEdit ? '修改失败' : '新增失败')
        console.error('保存失败:', error)
      } finally {
        loading.value = false
      }
    }
  })
}

/**
 * 取消按钮
 */
function cancel() {
  resetForm()
  dialog.visible = false
}

// 组件挂载后加载数据
onMounted(() => {
  getList()
})

</script>

<style lang="scss" scoped>
.file-usage-config-container {
  padding: 10px;
}
.mb8 {
  margin-bottom: 8px;
}
.mt-4 {
  margin-top: 1rem; /* 16px */
}

.el-form-item__extra-info {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  margin-top: 2px;
}
</style>
