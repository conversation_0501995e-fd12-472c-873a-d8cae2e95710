<!--
 * @Description: 社区地址管理 - 编辑对话框组件
 * @Author: AI Assistant
 * @Date: 2025-06-09
 * @Version: 1.0.0
 * 提供社区地址的添加和编辑功能
-->
<template>
  <el-dialog
    :title="isCreate ? '添加地址' : '编辑地址'"
    v-model="dialogVisible"
    width="800px"
    @closed="handleClosed"
  >
    <el-form
      ref="dataForm"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <!-- 自动解析文本区域 -->
      <el-form-item label="自动解析">
        <el-input
          v-model="parseText"
          type="textarea"
          :rows="3"
          placeholder="请输入地址信息进行自动解析，例如：贵州民族大学南校区：26.371671, 106.626431"
          @input="handleParseText"
        />
        <div class="parse-tip">
          <el-text size="small" type="info">
            支持格式：地址名称：纬度, 经度 或 地址名称 纬度, 经度 或 纬度, 经度（经度为三位数开头，纬度为二位数开头）
          </el-text>
        </div>
      </el-form-item>
      
      <el-form-item label="地址名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入地址名称" />
      </el-form-item>
      <el-form-item label="级别" prop="level">
        <el-select v-model="formData.level" placeholder="请选择级别" style="width: 100%" @change="handleLevelChange">
          <el-option
            v-for="item in levelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="父级地址" prop="parentId" v-if="(formData && formData.level ? formData.level : 0) > 1">
        <el-cascader
          v-model="formData.parentId"
          :options="addressOptions"
          :props="{ checkStrictly: true, value: 'value', label: 'label', emitPath: false }"
          clearable
          placeholder="请选择父级地址"
          style="width: 100%"
          :disabled="!isCreate"
        />
      </el-form-item>
      <el-form-item label="位置选择" style="width: 100%;">
        <div class="location-selector" style="width: 100%;">
          <TiandituMap
            :key="mapkey"
            :width="'100%'"
            :height="'300px'"
            :longitude="(formData.longitude && formData.latitude) ? formData.longitude : defaultMapConfig.center[0]"
            :latitude="(formData.longitude && formData.latitude) ? formData.latitude : defaultMapConfig.center[1]"
            :zoom="(formData.longitude && formData.latitude) ? 16 : defaultMapConfig.zoom"
            :editable-marker="editableMarkerData"
            :edit-mode="true"
            :add-marker-mode="!formData.longitude || !formData.latitude || formData.longitude === 0 || formData.latitude === 0"
            @map-click="handleMapClick"
            @editable-marker-update="handleMarkerUpdate"
          />
          <div class="coordinate-info">
            <span>{{ formData.longitude || '未设置' }},{{ formData.latitude || '未设置' }}</span>
            <span></span>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="排序值" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" :max="999" />
        <span class="tip">数字越小越靠前</span>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
// 注册组件
import * as TiandituMapComponents from '@/components/map';

export default {
  components: {
    TiandituMap: TiandituMapComponents.TiandituMap
  }
};
</script>

<script lang="ts" setup>
import { ref, watch, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  levelOptions, 
  type CommunityAddress,
  type CommunityAddressOption,
  type CreateCommunityAddressRequest,
  type UpdateCommunityAddressRequest
} from '@/modules/admin/api/communityAddress'

const props = defineProps<{
  visible: boolean
  addressOptions: CommunityAddressOption[]
  isCreate: boolean
  addressData?: Partial<CommunityAddress>
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'create', data: CreateCommunityAddressRequest): void
  (e: 'update', data: UpdateCommunityAddressRequest): void
}>()

// 响应式数据
const dialogVisible = ref(props.visible)
const dataForm = ref()
const parseText = ref('')

const mapkey = ref(0)

// 地图默认配置
const defaultMapConfig = {
  center: [116.397428, 39.90923], // 默认位置北京天安门
  zoom: 12
};

// 表单数据
const formData = ref<Partial<CommunityAddress & CreateCommunityAddressRequest & UpdateCommunityAddressRequest>>({
  id: undefined,
  name: '',
  parentId: null,
  level: 1,
  longitude: 0,
  latitude: 0,
  sort: 0,
  status: 1,
  description: ''
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入地址名称', trigger: 'blur' }],
  level: [{ required: true, message: '请选择级别', trigger: 'change' }],
  longitude: [{ required: true, message: '请输入经度', trigger: 'blur' }],
  latitude: [{ required: true, message: '请输入纬度', trigger: 'blur' }]
}

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    // 对话框打开时重新初始化数据
    initFormData()
  }
})

// 监听dialogVisible变化，同步到父组件
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 监听addressData变化
watch(() => props.addressData, () => {
  if (dialogVisible.value) {
    initFormData()
  }
}, { deep: true })

/**
 * 初始化表单数据
 */
const initFormData = () => {
  if (props.addressData) {
    formData.value = {
      ...props.addressData,
      longitude: Number(props.addressData.longitude) || 0,
      latitude: Number(props.addressData.latitude) || 0
    }
    mapkey.value = Math.random()
  } else {
    resetForm()
  }
}

// 可编辑标记点数据
const editableMarkerData = computed(() => {
  if (formData.value.longitude && formData.value.latitude && 
      formData.value.longitude !== 0 && formData.value.latitude !== 0) {
    return {
      lng: formData.value.longitude,
      lat: formData.value.latitude,
      title: formData.value.name || '地址位置'
    };
  }
  return null;
});



/**
 * @function handleMapClick
 * @description 处理地图点击事件
 * @param event 点击事件
 */
const handleMapClick = (event: any) => {
  // 当地址经纬度为0时，第一次点击地图创建可编辑标记点
  if (!formData.value.longitude || !formData.value.latitude || 
      formData.value.longitude === 0 || formData.value.latitude === 0) {
    if (event && event.lnglat) {
      formData.value.longitude = parseFloat(event.lnglat.lng.toFixed(6))
      formData.value.latitude = parseFloat(event.lnglat.lat.toFixed(6))
    }
  }
}

/**
 * @function handleMarkerUpdate
 * @description 处理标记点更新事件
 * @param lnglat 更新的经纬度
 */
const handleMarkerUpdate = (lnglat: { lng: number, lat: number }) => {
  // 当用户拖拽标记点时更新坐标数据
  if (lnglat && lnglat.lng && lnglat.lat) {
    // 保留6位小数
    formData.value.longitude = parseFloat(lnglat.lng.toFixed(6))
    formData.value.latitude = parseFloat(lnglat.lat.toFixed(6))
  }
}

/**
 * @function resetForm
 * @description 重置表单数据
 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: '',
    parentId: null,
    level: 1,
    longitude: 0,
    latitude: 0,
    sort: 0,
    status: 1,
    description: ''
  }
  parseText.value = ''
}

/**
 * @function handleClosed
 * @description 处理对话框关闭事件
 */
const handleClosed = () => {
  resetForm()
}

/**
 * @function handleLevelChange
 * @description 处理级别变化
 */
const handleLevelChange = () => {
  if (formData.value.level === 1) {
    formData.value.parentId = null
  }
}

/**
 * @function handleParseText
 * @description 处理自动解析文本
 */
const handleParseText = () => {
  const text = parseText.value.trim()
  if (!text) return
  
  try {
    // 正则表达式匹配坐标（经度为三位数开头，纬度为二位数开头）
    const coordinateRegex = /([1-9]\d{2}\.\d+)[\s,，、]+([1-9]\d\.\d+)|([1-9]\d\.\d+)[\s,，、]+([1-9]\d{2}\.\d+)/g
    const matches = coordinateRegex.exec(text)
    
    if (matches) {
      let longitude: number
      let latitude: number
      
      // 判断哪个是经度（三位数开头）哪个是纬度（二位数开头）
      if (matches[1] && matches[2]) {
        // 第一种情况：经度在前，纬度在后
        longitude = parseFloat(parseFloat(matches[1]).toFixed(6))
        latitude = parseFloat(parseFloat(matches[2]).toFixed(6))
      } else if (matches[3] && matches[4]) {
        // 第二种情况：纬度在前，经度在后
        latitude = parseFloat(parseFloat(matches[3]).toFixed(6))
        longitude = parseFloat(parseFloat(matches[4]).toFixed(6))
      } else {
        return
      }
      
      // 设置坐标
      formData.value.longitude = longitude
      formData.value.latitude = latitude
      
      // 提取地址名称（坐标前面的文本，去除冒号等分隔符）
      const nameMatch = text.substring(0, matches.index).replace(/[：:，,、\s]+$/, '').trim()
      if (nameMatch) {
        formData.value.name = nameMatch
      }
      
      ElMessage.success('解析成功！')
    } else {
      ElMessage.warning('未能识别到有效的坐标信息，请检查格式')
    }
  } catch (error) {
    console.error('解析文本失败:', error)
    ElMessage.error('解析失败，请检查输入格式')
  }
}

/**
 * @function handleSubmit
 * @description 处理表单提交
 */
const handleSubmit = async () => {
  try {
    await dataForm.value.validate()
    
    if (props.isCreate) {
      const createData: CreateCommunityAddressRequest = {
        name: formData.value.name!,
        parentId: formData.value.parentId,
        level: formData.value.level!,
        longitude: formData.value.longitude!,
        latitude: formData.value.latitude!,
        sort: formData.value.sort || 0,
        status: formData.value.status || 1,
        description: formData.value.description
      }
      emit('create', createData)
    } else {
      const updateData: UpdateCommunityAddressRequest = {
        id: formData.value.id!,
        name: formData.value.name!,
        parentId: formData.value.parentId,
        level: formData.value.level!,
        longitude: formData.value.longitude!,
        latitude: formData.value.latitude!,
        sort: formData.value.sort || 0,
        status: formData.value.status || 1,
        description: formData.value.description
      }
      emit('update', updateData)
    }
    
    nextTick(() => {
      dialogVisible.value = false
    })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.parse-tip {
  margin-top: 5px;
}

.parse-tip .el-text {
  line-height: 1.4;
}

.location-selector {
  .coordinate-info {
    margin-top: 10px;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-size: 14px;
    color: #606266;
    display: flex;
    justify-content: space-between;
  }
}
</style>
