<!--
 * @Description: 本地存储配置组件
 * @Author: Cascade
 * @Date: 2025-06-07
 -->
<template>
  <div class="local-storage-config">
    <el-divider>本地存储配置</el-divider>
    
    <el-form-item label="存储路径" prop="config.localPath">
      <el-input 
        v-model="config.localPath" 
        placeholder="请输入本地存储路径，例如：./uploads"
        style="width: 500px"
      />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
/**
 * 本地存储配置组件
 * 用于配置本地文件存储路径
 */
import { watch } from 'vue'

interface LocalStorageConfig {
  localPath?: string;
}

// 组件属性
const props = defineProps<{
  config: LocalStorageConfig;
}>()

// 组件事件
const emit = defineEmits<{
  (e: 'update:config', config: LocalStorageConfig): void;
}>()

// 监听配置变化并触发更新事件
watch(() => props.config, (newConfig) => {
  emit('update:config', newConfig)
}, { deep: true })
</script>

<style scoped>
.local-storage-config {
  margin-top: 20px;
}
</style>
