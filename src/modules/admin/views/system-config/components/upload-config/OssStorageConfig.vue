<!--
 * @Description: 阿里云OSS存储配置组件
 * @Author: Cascade
 * @Date: 2025-06-07
 -->
<template>
  <div class="oss-storage-config">
    <el-divider>阿里云OSS配置</el-divider>
    
    <el-form-item label="Endpoint" prop="config.endpoint">
      <el-input 
        v-model="config.endpoint" 
        placeholder="请输入OSS Endpoint，例如：oss-cn-hangzhou.aliyuncs.com"
        style="width: 500px"
      />
    </el-form-item>
    
    <el-form-item label="Bucket" prop="config.bucket">
      <el-input 
        v-model="config.bucket"
        placeholder="请输入OSS Bucket名称"
        style="width: 500px"
      />
    </el-form-item>
    
    <el-form-item label="AccessKey" prop="config.accessKey">
      <el-input 
        v-model="config.accessKey"
        placeholder="请输入AccessKey"
        style="width: 500px"
      />
    </el-form-item>
    
    <el-form-item label="AccessSecret" prop="config.accessSecret">
      <el-input 
        v-model="config.accessSecret"
        placeholder="请输入AccessSecret"
        type="password"
        style="width: 500px"
        show-password
      />
    </el-form-item>
    
    <el-form-item label="自定义域名" prop="config.domain">
      <el-input 
        v-model="config.domain"
        placeholder="请输入自定义域名，例如：https://oss.example.com"
        style="width: 500px"
      />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
/**
 * 阿里云OSS存储配置组件
 * 用于配置阿里云OSS存储相关参数
 */
// import { defineProps, defineEmits } from 'vue'

interface OssStorageConfig {
  endpoint?: string;
  bucket?: string;
  accessKey?: string;
  accessSecret?: string;
  domain?: string;
}

// 组件属性
defineProps<{
  config: OssStorageConfig;
}>()

// 组件事件
defineEmits<{
  (e: 'update:config', config: OssStorageConfig): void;
}>()
</script>

<style scoped>
.oss-storage-config {
  margin-top: 20px;
}
</style>
