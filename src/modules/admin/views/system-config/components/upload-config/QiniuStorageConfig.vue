<!--
 * @Description: 七牛云存储配置组件
 * @Author: Cascade
 * @Date: 2025-06-07
 -->
<template>
  <div class="qiniu-storage-config">
    <el-divider>七牛云存储配置</el-divider>
    
    <el-form-item label="AccessKey" prop="config.accessKey">
      <el-input 
        v-model="config.accessKey"
        placeholder="请输入AccessKey"
        style="width: 500px"
      />
    </el-form-item>
    
    <el-form-item label="SecretKey" prop="config.secretKey">
      <el-input 
        v-model="config.secretKey"
        placeholder="请输入SecretKey"
        type="password"
        show-password
        style="width: 500px"
      />
    </el-form-item>
    
    <el-form-item label="Bucket" prop="config.bucket">
      <el-input 
        v-model="config.bucket"
        placeholder="请输入Bucket名称"
        style="width: 500px"
      />
    </el-form-item>
    
    <el-form-item label="存储区域" prop="config.zone">
      <el-radio-group v-model="config.zone">
        <el-radio :value="'z0'">华东</el-radio>
        <el-radio :value="'z1'">华北</el-radio>
        <el-radio :value="'z2'">华南</el-radio>
        <el-radio :value="'na0'">北美</el-radio>
        <el-radio :value="'as0'">新加坡(东南亚)</el-radio>
        <el-radio :value="'ap-northeast-1'">华东-浙江2</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="使用HTTPS" prop="config.useHTTPS">
      <el-radio-group v-model="useHTTPS">
        <el-radio :value="true">是</el-radio>
        <el-radio :value="false">否</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="自定义域名" prop="config.domain">
      <el-input 
        v-model="config.domain"
        placeholder="请输入自定义域名，例如：https://cdn.example.com"
        style="width: 500px"
      />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
/**
 * 七牛云存储配置组件
 * 用于配置七牛云存储相关参数
 */
import { watch, computed } from 'vue'

interface QiniuStorageConfig {
  accessKey?: string;
  secretKey?: string;
  bucket?: string;
  zone?: string;
  useHTTPS?: boolean;
  domain?: string;
}

// 组件属性
const props = defineProps<{
  config: QiniuStorageConfig;
}>()

// 组件事件
const emit = defineEmits<{
  (e: 'update:config', config: QiniuStorageConfig): void;
}>()

// 确保 useHTTPS 有默认值
const useHTTPS = computed({
  get: () => props.config.useHTTPS ?? false,
  set: (value) => {
    const newConfig = { ...props.config, useHTTPS: value }
    emit('update:config', newConfig)
  }
})

// 监听配置变化并触发更新事件
watch(() => props.config, (newConfig) => {
  if (newConfig.useHTTPS === undefined) {
    // 只有当useHTTPS是 undefined 时才设置默认值为false
    emit('update:config', { ...newConfig, useHTTPS: false })
  }
}, { deep: true })
</script>

<style scoped>
.qiniu-storage-config {
  margin-top: 20px;
}
</style>
