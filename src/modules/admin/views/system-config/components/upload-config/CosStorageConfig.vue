<!--
 * @Description: 腾讯云COS存储配置组件
 * @Author: Cascade
 * @Date: 2025-06-07
 -->
<template>
  <div class="cos-storage-config">
    <el-divider>腾讯云COS配置</el-divider>
    
    <el-form-item label="区域" prop="config.region">
      <el-select
        v-model="config.region"
        placeholder="请选择COS区域"
        style="width: 500px"
      >
        <el-option value="ap-beijing" label="华北地区(北京)"></el-option>
        <el-option value="ap-shanghai" label="华东地区(上海)"></el-option>
        <el-option value="ap-guangzhou" label="华南地区(广州)"></el-option>
        <el-option value="ap-chengdu" label="西南地区(成都)"></el-option>
        <el-option value="ap-hongkong" label="中国香港"></el-option>
        <el-option value="na-toronto" label="北美地区(多伦多)"></el-option>
        <el-option value="na-siliconvalley" label="美国西部(硅谷)"></el-option>
        <el-option value="eu-frankfurt" label="欧洲地区(法兰克福)"></el-option>
      </el-select>
    </el-form-item>
    
    <el-form-item label="Bucket" prop="config.bucket">
      <el-input 
        v-model="config.bucket"
        placeholder="请输入COS Bucket名称，例如：example-1250000000"
        style="width: 500px"
      />
    </el-form-item>
    
    <el-form-item label="SecretId" prop="config.secretId">
      <el-input 
        v-model="config.secretId"
        placeholder="请输入SecretId"
        style="width: 500px"
      />
    </el-form-item>
    
    <el-form-item label="SecretKey" prop="config.secretKey">
      <el-input 
        v-model="config.secretKey"
        placeholder="请输入SecretKey"
        type="password"
        show-password
        style="width: 500px"
      />
    </el-form-item>
    
    <el-form-item label="自定义域名" prop="config.domain">
      <el-input 
        v-model="config.domain"
        placeholder="请输入自定义域名，例如：https://cos.example.com"
        style="width: 500px"
      />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
/**
 * 腾讯云COS存储配置组件
 * 用于配置腾讯云COS存储相关参数
 */
// import { defineProps, defineEmits } from 'vue'

interface CosStorageConfig {
  region?: string;
  bucket?: string;
  secretId?: string;
  secretKey?: string;
  domain?: string;
}

// 组件属性
defineProps<{
  config: CosStorageConfig;
}>()

// 组件事件
defineEmits<{
  (e: 'update:config', config: CosStorageConfig): void;
}>()
</script>

<style scoped>
.cos-storage-config {
  margin-top: 20px;
}
</style>
