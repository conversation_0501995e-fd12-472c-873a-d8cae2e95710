<!--
 * @Description: AWS S3存储配置组件
 * @Author: Cascade
 * @Date: 2025-06-07
 -->
<template>
  <div class="s3-storage-config">
    <el-divider>AWS S3配置</el-divider>
    
    <el-form-item label="区域" prop="config.region">
      <el-select
        v-model="config.region"
        placeholder="请选择S3区域"
        style="width: 500px"
      >
        <el-option value="us-east-1" label="US East (N. Virginia)"></el-option>
        <el-option value="us-east-2" label="US East (Ohio)"></el-option>
        <el-option value="us-west-1" label="US West (N. California)"></el-option>
        <el-option value="us-west-2" label="US West (Oregon)"></el-option>
        <el-option value="ap-east-1" label="Asia Pacific (Hong Kong)"></el-option>
        <el-option value="ap-south-1" label="Asia Pacific (Mumbai)"></el-option>
        <el-option value="ap-northeast-1" label="Asia Pacific (Tokyo)"></el-option>
        <el-option value="ap-northeast-2" label="Asia Pacific (Seoul)"></el-option>
        <el-option value="ap-southeast-1" label="Asia Pacific (Singapore)"></el-option>
        <el-option value="ap-southeast-2" label="Asia Pacific (Sydney)"></el-option>
        <el-option value="ca-central-1" label="Canada (Central)"></el-option>
        <el-option value="eu-central-1" label="Europe (Frankfurt)"></el-option>
        <el-option value="eu-west-1" label="Europe (Ireland)"></el-option>
        <el-option value="eu-west-2" label="Europe (London)"></el-option>
        <el-option value="eu-west-3" label="Europe (Paris)"></el-option>
        <el-option value="eu-north-1" label="Europe (Stockholm)"></el-option>
      </el-select>
    </el-form-item>
    
    <el-form-item label="Bucket" prop="config.bucket">
      <el-input 
        v-model="config.bucket"
        placeholder="请输入S3 Bucket名称"
        style="width: 500px"
      />
    </el-form-item>
    
    <el-form-item label="AccessKey" prop="config.accessKey">
      <el-input 
        v-model="config.accessKey"
        placeholder="请输入AccessKey"
        style="width: 500px"
      />
    </el-form-item>
    
    <el-form-item label="SecretKey" prop="config.secretKey">
      <el-input 
        v-model="config.secretKey"
        placeholder="请输入SecretKey"
        type="password"
        show-password
        style="width: 500px"
      />
    </el-form-item>
    
    <el-form-item label="自定义域名" prop="config.domain">
      <el-input 
        v-model="config.domain"
        placeholder="请输入自定义域名，例如：https://s3.example.com"
        style="width: 500px"
      />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
/**
 * AWS S3存储配置组件
 * 用于配置AWS S3存储相关参数
 */
import { watch } from 'vue'

interface S3StorageConfig {
  region?: string;
  bucket?: string;
  accessKey?: string;
  secretKey?: string;
  domain?: string;
}

// 组件属性
const props = defineProps<{
  config: S3StorageConfig;
}>()

// 组件事件
const emit = defineEmits<{
  (e: 'update:config', config: S3StorageConfig): void;
}>()

// 监听配置变化并触发更新事件
watch(() => props.config, (newConfig) => {
  emit('update:config', newConfig)
}, { deep: true })
</script>

<style scoped>
.s3-storage-config {
  margin-top: 20px;
}
</style>
