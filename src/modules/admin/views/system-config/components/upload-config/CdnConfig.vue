<!--
 * @Description: CDN配置组件
 * @Author: Cascade
 * @Date: 2025-06-07
 -->
<template>
  <div class="cdn-config">
    <el-divider>CDN加速配置</el-divider>
    
    <el-form-item label="CDN AccessKey" prop="config.cdnAccessKey">
      <el-input 
        v-model="config.cdnAccessKey"
        placeholder="请输入CDN AccessKey"
        style="width: 500px"
      />
    </el-form-item>
    
    <el-form-item label="CDN SecretKey" prop="config.cdnSecretKey">
      <el-input 
        v-model="config.cdnSecretKey"
        placeholder="请输入CDN SecretKey"
        type="password"
        show-password
        style="width: 500px"
      />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
/**
 * CDN配置组件
 * 用于配置CDN加速的相关参数
 * 当启用CDN加速且不是本地存储时显示
 */
import { watch } from 'vue'

interface CdnConfig {
  cdnAccessKey?: string;
  cdnSecretKey?: string;
}

// 组件属性
const props = defineProps<{
  config: CdnConfig;
}>()

// 组件事件
const emit = defineEmits<{
  (e: 'update:config', config: CdnConfig): void;
}>()

// 监听配置变化并触发更新事件
watch(() => props.config, (newConfig) => {
  emit('update:config', newConfig)
}, { deep: true })
</script>

<style scoped>
.cdn-config {
  margin-top: 20px;
}
</style>
