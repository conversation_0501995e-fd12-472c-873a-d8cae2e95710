<!--
 * @Description: 社区地址管理 - 搜索过滤组件
 * @Author: AI Assistant
 * @Date: 2025-06-09
 * @Version: 1.0.0
-->
<template>
  <div class="filter-container">
    <el-form :inline="true" :model="listQuery" class="demo-form-inline">
      <el-form-item label="地址名称">
        <el-input v-model="listQuery.name" placeholder="请输入地址名称" clearable style="width: 200px;" />
      </el-form-item>
      <el-form-item label="级别">
        <el-select v-model="listQuery.level" placeholder="请选择级别" clearable style="width: 120px;">
          <el-option
            v-for="item in levelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="listQuery.status" placeholder="请选择状态" clearable style="width: 120px;">
          <el-option label="全部" :value="-1" />
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleFilter">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'
import { 
  levelOptions, 
  statusOptions,
  type CommunityAddressQueryParams 
} from '@/modules/admin/api/communityAddress'

const props = defineProps<{
  modelValue: CommunityAddressQueryParams
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: CommunityAddressQueryParams): void
  (e: 'search'): void
  (e: 'reset'): void
}>()

// 响应式数据
const listQuery = reactive<CommunityAddressQueryParams>({
  ...props.modelValue
})

// 方法
/**
 * @function handleFilter
 * @description 处理搜索过滤
 */
const handleFilter = () => {
  emit('update:modelValue', { ...listQuery })
  emit('search')
}

/**
 * @function resetQuery
 * @description 重置查询条件
 */
const resetQuery = () => {
  listQuery.name = ''
  listQuery.level = undefined
  listQuery.status = undefined
  emit('update:modelValue', { ...listQuery })
  emit('reset')
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}
</style>
