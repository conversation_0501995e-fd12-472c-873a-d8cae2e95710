<!--
 * @Description: 系统配置工具栏组件
 * @Author: AI Assistant
 * @Date: 2025-05-28
 -->
<template>
  <div class="config-toolbar">
    <el-input
      v-model="searchValue"
      placeholder="搜索配置名称或键名"
      clearable
      @clear="handleSearch"
      @keyup.enter="handleSearch"
    >
      <template #append>
        <el-button @click="handleSearch">
          <el-icon><Search /></el-icon>
        </el-button>
      </template>
    </el-input>
    
    <div class="toolbar-actions">
      <el-button type="info" @click="handleRefreshCache">
        <el-icon><Refresh /></el-icon> 刷新缓存
      </el-button>
      
      <el-button type="primary" @click="handleAddConfig">添加配置</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'

/**
 * 定义组件属性
 */
const props = defineProps({
  searchQuery: {
    type: String,
    default: ''
  }
})

/**
 * 定义组件事件
 */
const emits = defineEmits(['search', 'refresh-cache', 'add'])

// 搜索值
const searchValue = ref(props.searchQuery)

// 监听外部searchQuery变化
watch(() => props.searchQuery, (newVal) => {
  searchValue.value = newVal
})

/**
 * 处理搜索事件
 */
const handleSearch = () => {
  emits('search', searchValue.value)
}

/**
 * 处理刷新缓存事件
 */
const handleRefreshCache = () => {
  emits('refresh-cache')
}

/**
 * 处理添加配置事件
 */
const handleAddConfig = () => {
  emits('add')
}
</script>

<style scoped>
.config-toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 10px;
}

.config-toolbar .el-input {
  width: 300px;
}

.toolbar-actions {
  display: flex;
  gap: 10px;
}
</style>
