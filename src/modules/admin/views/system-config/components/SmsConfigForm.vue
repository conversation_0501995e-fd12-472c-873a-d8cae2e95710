<!--
 * @file 短信配置表单组件
 * @description 用于管理短信服务配置的表单组件
 -->
<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import type {
  SmsConfigData,
  SaveSmsConfigRequest
} from '@/modules/admin/api/smsConfig';
import {
  smsProviders,
  statusOptions
} from '@/modules/admin/api/smsConfig';

// 定义组件的Props和Emits
const props = defineProps<{
  config: SmsConfigData;
  loading?: boolean;
}>();

const emit = defineEmits<{
  save: [config: SaveSmsConfigRequest];
  cancel: [];
}>();

// 表单数据，从传入的config属性初始化并深拷贝
const formData = ref<SaveSmsConfigRequest>({
  id: props.config.id,
  provider: props.config.provider || 'aliyun',
  accessKey: props.config.accessKey || '',
  signName: props.config.signName || '',
  templateCodeRegister: props.config.templateCodeRegister || '',
  templateCodeLogin: props.config.templateCodeLogin || '',
  templateCodeResetPwd: props.config.templateCodeResetPwd || '',
  templateCodeNotice: props.config.templateCodeNotice || '',
  dailyLimit: props.config.dailyLimit || 1000,
  status: props.config.status === undefined ? 1 : props.config.status,
  remark: props.config.remark || ''
});

// 根据不同服务商动态设置字段提示
const accessKeyLabel = computed(() => {
  return formData.value.provider === 'submail' ? 'AccessKey (app_id)' : 'AccessKey';
});

const accessKeyPlaceholder = computed(() => {
  return formData.value.provider === 'submail' ? '请输入app_id' : '请输入访问密钥ID';
});

const accessSecretLabel = computed(() => {
  return formData.value.provider === 'submail' ? 'AccessSecret (app_key)' : 'AccessSecret';
});

const accessSecretPlaceholder = computed(() => {
  return formData.value.provider === 'submail' 
    ? '如需修改请输入app_key，留空则保持原值' 
    : '如需修改请输入新密钥，留空则保持原值';
});

const templatePlaceholder = computed(() => {
  return formData.value.provider === 'submail' 
    ? '请输入短信项目ID (project)' 
    : '请输入模板ID';
});

// 监听props.config的变化，及时更新表单数据
watch(
  () => props.config,
  (newConfig) => {
    formData.value = {
      id: newConfig.id,
      provider: newConfig.provider || 'aliyun',
      accessKey: newConfig.accessKey || '',
      signName: newConfig.signName || '',
      templateCodeRegister: newConfig.templateCodeRegister || '',
      templateCodeLogin: newConfig.templateCodeLogin || '',
      templateCodeResetPwd: newConfig.templateCodeResetPwd || '',
      templateCodeNotice: newConfig.templateCodeNotice || '',
      dailyLimit: newConfig.dailyLimit || 1000,
      status: newConfig.status === undefined ? 1 : newConfig.status,
      remark: newConfig.remark || ''
    };
    // 不要在这里设置accessSecret，因为从后端返回的是脱敏的
  },
  { deep: true }
);

// 表单校验规则
const formRules = ref<FormRules>({
  provider: [
    { required: true, message: '请选择短信服务提供商', trigger: 'change' }
  ],
  accessKey: [
    { required: true, message: '请输入AccessKey', trigger: 'blur' }
  ],
  signName: [
    { required: true, message: '请输入短信签名', trigger: 'blur' }
  ],
  templateCodeRegister: [
    { required: true, message: '请输入注册验证码模板ID', trigger: 'blur' }
  ],
  templateCodeLogin: [
    { required: true, message: '请输入登录验证码模板ID', trigger: 'blur' }
  ],
  templateCodeResetPwd: [
    { required: true, message: '请输入重置密码验证码模板ID', trigger: 'blur' }
  ],
  templateCodeNotice: [
    { required: true, message: '请输入通知消息模板ID', trigger: 'blur' }
  ],
  dailyLimit: [
    { required: true, message: '请输入每日发送上限', trigger: 'blur' },
    { type: 'number', min: 1, message: '每日发送上限必须大于0', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
});

// 表单引用
const formRef = ref<FormInstance>();

/**
 * @function submitForm
 * @description 提交表单
 */
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      // 构造提交的数据对象
      const submitData: SaveSmsConfigRequest = {
        ...formData.value
      };
      
      // 如果accessSecret为空，则不提交此字段（保留原值）
      if (!formData.value.accessSecret?.trim()) {
        delete submitData.accessSecret;
      }
      
      emit('save', submitData);
    } else {
      ElMessage.error('表单验证失败，请检查输入');
    }
  });
};

/**
 * @function resetForm
 * @description 重置表单
 */
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 通知父组件取消操作
  emit('cancel');
};

// 暴露方法给父组件调用
defineExpose({
  submitForm,
  resetForm
});
</script>

<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="140px"
    size="default"
    status-icon
  >
    <el-form-item label="服务提供商" prop="provider">
      <el-select v-model="formData.provider" placeholder="请选择短信服务提供商" style="width: 100%">
        <el-option
          v-for="item in smsProviders"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      
      <div v-if="formData.provider === 'submail'" class="field-provider-info">
        <div class="title">赛邮(Submail)配置说明：</div>
        <div>• AccessKey 对应赛邮的 app_id</div>
        <div>• AccessSecret 对应赛邮的 app_key</div>
        <div>• 模板ID字段对应赛邮的短信项目ID (project)</div>
      </div>
    </el-form-item>
    
    <el-form-item :label="accessKeyLabel" prop="accessKey">
      <el-input
        v-model="formData.accessKey"
        :placeholder="accessKeyPlaceholder"
      />
      <div v-if="formData.provider === 'submail'" class="field-hint">赛邮服务请填写 app_id</div>
    </el-form-item>
    
    <el-form-item :label="accessSecretLabel" prop="accessSecret">
      <el-input
        v-model="formData.accessSecret"
        type="password"
        show-password
        :placeholder="accessSecretPlaceholder"
      />
      <div v-if="formData.provider === 'submail'" class="field-hint">赛邮服务请填写 app_key</div>
    </el-form-item>
    
    <el-form-item label="短信签名" prop="signName">
      <el-input
        v-model="formData.signName"
        placeholder="请输入短信签名"
      />
    </el-form-item>
    
    <el-form-item label="注册验证码模板" prop="templateCodeRegister">
      <el-input
        v-model="formData.templateCodeRegister"
        :placeholder="templatePlaceholder"
      />
      <div v-if="formData.provider === 'submail'" class="field-hint">赛邮服务请填写项目ID (project)</div>
    </el-form-item>
    
    <el-form-item label="登录验证码模板" prop="templateCodeLogin">
      <el-input
        v-model="formData.templateCodeLogin"
        :placeholder="templatePlaceholder"
      />
      <div v-if="formData.provider === 'submail'" class="field-hint">赛邮服务请填写项目ID (project)</div>
    </el-form-item>
    
    <el-form-item label="重置密码模板" prop="templateCodeResetPwd">
      <el-input
        v-model="formData.templateCodeResetPwd"
        :placeholder="templatePlaceholder"
      />
      <div v-if="formData.provider === 'submail'" class="field-hint">赛邮服务请填写项目ID (project)</div>
    </el-form-item>
    
    <el-form-item label="通知消息模板" prop="templateCodeNotice">
      <el-input
        v-model="formData.templateCodeNotice"
        :placeholder="templatePlaceholder"
      />
      <div v-if="formData.provider === 'submail'" class="field-hint">赛邮服务请填写项目ID (project)</div>
    </el-form-item>
    
    <el-form-item label="每日发送上限" prop="dailyLimit">
      <el-input-number
        v-model="formData.dailyLimit"
        :min="1"
        :step="100"
        placeholder="请输入每日发送上限"
        style="width: 100%"
      />
    </el-form-item>
    
    <el-form-item label="状态" prop="status">
      <el-radio-group v-model="formData.status">
        <el-radio
          v-for="item in statusOptions"
          :key="item.value"
          :label="item.value"
          >{{ item.label }}</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="备注" prop="remark">
      <el-input
        v-model="formData.remark"
        type="textarea"
        rows="3"
        placeholder="请输入备注信息"
      />
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" :loading="props.loading" @click="submitForm">保存配置</el-button>
      <el-button @click="resetForm">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<style scoped>
/* 表单相关样式 */
.field-hint {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  padding-top: 4px;
}

.field-provider-info {
  margin-top: 10px;
  padding: 10px;
  background-color: #f0f9eb;
  border-radius: 4px;
  color: #67c23a;
  font-size: 14px;
}

.field-provider-info .title {
  font-weight: bold;
  margin-bottom: 5px;
}
</style>
