<!--
 * @Description: 系统配置列表组件
 * @Author: AI Assistant
 * @Date: 2025-05-28
 -->
<template>
  <div class="config-list">
    <el-table
      v-loading="loading"
      :data="configList"
      stripe
      border
      style="width: 100%"
    >
      <el-table-column prop="configKey" label="配置键名" min-width="150" />
      <el-table-column label="配置名称" min-width="150">
        <template #default="scope">
          {{ formatConfigName(scope.row.configKey) }}
        </template>
      </el-table-column>
      <el-table-column prop="configValue" label="配置值" min-width="200" show-overflow-tooltip />
      <el-table-column prop="configType" label="类型" min-width="100">
        <template #default="scope">
          <el-tag :type="getTypeTagType(scope.row.configType)">
            {{ scope.row.configType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
      <el-table-column prop="updatedAt" label="更新时间" min-width="170">
        <template #default="scope">
          {{ formatDate(scope.row.updatedAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="180">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-popconfirm
            title="确定要删除这个配置吗？"
            @confirm="handleDelete(scope.row.id)"
          >
            <template #reference>
              <el-button size="small" type="danger">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @update:current-page="handlePageChange"
        @update:page-size="handleSizeChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
//import { computed } from 'vue'
import type { SystemConfig } from '@/modules/admin/api/systemConfig'

/**
 * 组件属性定义
 */
defineProps({
  configList: {
    type: Array as () => SystemConfig[],
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 10
  },
  total: {
    type: Number,
    default: 0
  }
})

/**
 * 组件事件定义
 */
const emits = defineEmits(['edit', 'delete', 'page-change', 'size-change'])

/**
 * 格式化配置名称
 * @param configKey 配置键名
 * @returns 格式化后的配置名称
 */
const formatConfigName = (configKey: string) => {
  if (!configKey) return ''
  return configKey.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')
}

/**
 * 根据类型返回标签类型
 * @param type 配置类型
 * @returns 对应的标签类型
 */
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    text: 'info',
    number: 'success',
    boolean: 'warning',
    json: 'info',
    html: 'primary'
  }
  return typeMap[type] || 'info'
}

/**
 * 日期格式化工具函数
 * @param date 日期字符串或日期对象
 * @returns 格式化后的日期字符串
 */
const formatDate = (date: string | Date) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 处理编辑事件
 * @param row 当前行数据
 */
const handleEdit = (row: SystemConfig) => {
  emits('edit', row)
}

/**
 * 处理删除事件
 * @param id 要删除的配置ID
 */
const handleDelete = (id: string | number) => {
  emits('delete', id)
}

/**
 * 处理页码变化
 * @param page 新的页码
 */
const handlePageChange = (page: number) => {
  emits('page-change', page)
}

/**
 * 处理每页显示数量变化
 * @param size 新的每页显示数量
 */
const handleSizeChange = (size: number) => {
  emits('size-change', size)
}
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
