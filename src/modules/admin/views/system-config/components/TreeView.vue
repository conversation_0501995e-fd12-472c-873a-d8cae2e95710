<!--
 * @Description: 社区地址管理 - 树形视图组件
 * @Author: AI Assistant
 * @Date: 2025-06-09
 * @Version: 1.0.0
-->
<template>
  <div class="tree-container" v-loading="loading">
    <el-tree
      ref="addressTree"
      :data="addressList"
      node-key="id"
      :props="{ children: 'children', label: 'name' }"
      :expand-on-click-node="false"
      highlight-current
    >
      <template #default="{ data }">
        <div class="custom-tree-node">
          <div class="node-info">
            <span class="name-label">{{ data.name }}</span>
            <el-tag size="small" class="level-tag">{{ getLevelText(data.level) }}</el-tag>
            <el-tag size="small" type="warning" class="sort-tag">排序: {{ data.sort }}</el-tag>
            <el-tag :type="data.status ? 'success' : 'danger'" size="small" class="status-tag">
              {{ data.status ? '启用' : '禁用' }}
            </el-tag>
            <span class="coordinates">经度: {{ data.longitude }}, 纬度: {{ data.latitude }}</span>
            <span class="coordinates wgs84">WGS84坐标 - 经度: {{ convertToWGS84(data.longitude, data.latitude).longitude.toFixed(6) }}, 纬度: {{ convertToWGS84(data.longitude, data.latitude).latitude.toFixed(6) }}</span>
          </div>
          <div class="node-actions">
            <el-button type="primary" size="small" @click="handleCreate(data)">
              添加子地址
            </el-button>
            <el-button type="primary" size="small" @click="handleUpdate(data)">
              编辑
            </el-button>
            <el-popconfirm
              title="确认删除该地址?"
              @confirm="handleDelete(data)"
            >
              <template #reference>
                <el-button type="danger" size="small">删除</el-button>
              </template>
            </el-popconfirm>
          </div>
        </div>
      </template>
    </el-tree>
    
    <!-- 空数据提示 -->
    <el-empty v-if="addressList.length === 0" description="暂无地址数据" />
  </div>
</template>

<script lang="ts" setup>
import { 
  levelOptions,
  type CommunityAddress
} from '@/modules/admin/api/communityAddress'
import { convertToWGS84 } from '@/modules/admin/utils/coordinateTransform'

defineProps<{
  addressList: CommunityAddress[]
  loading: boolean
}>()

const emit = defineEmits<{
  (e: 'create', parent?: CommunityAddress): void
  (e: 'update', address: CommunityAddress): void
  (e: 'delete', address: CommunityAddress): void
}>()

/**
 * @function handleCreate
 * @description 处理创建子地址
 * @param parent 父级地址数据
 */
const handleCreate = (parent: CommunityAddress) => {
  emit('create', parent)
}

/**
 * @function handleUpdate
 * @description 处理编辑地址
 * @param address 地址数据
 */
const handleUpdate = (address: CommunityAddress) => {
  emit('update', address)
}

/**
 * @function handleDelete
 * @description 处理删除地址
 * @param address 地址数据
 */
const handleDelete = (address: CommunityAddress) => {
  emit('delete', address)
}

/**
 * @function getLevelText
 * @description 获取级别文本
 * @param level 级别值
 * @returns 级别文本
 */
const getLevelText = (level: number) => {
  const option = levelOptions.find(item => item.value === level)
  return option ? option.label : '未知'
}
</script>

<style scoped>
.tree-container {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px;
  background-color: #fff;
  min-height: 400px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
  width: 100%;
}

.node-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.name-label {
  font-weight: bold;
  font-size: 14px;
  margin-right: 8px;
}

.level-tag,
.sort-tag,
.status-tag {
  margin-right: 5px;
}

.coordinates {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.coordinates.wgs84 {
  color: #67C23A;
  font-style: italic;
  display: block;
  margin-left: 0;
  margin-top: 4px;
}

.node-actions {
  white-space: nowrap;
}
</style>
