<!--
 * @Description: 社区地址管理 - 表格视图组件
 * @Author: AI Assistant
 * @Date: 2025-06-09
 * @Version: 1.0.0
 * 提供社区地址的表格视图展示
-->
<template>
  <div class="table-container" v-loading="loading">
    <el-table
      :data="addressList"
      stripe
      border
      style="width: 100%"
    >
      <el-table-column prop="name" label="地址名称" min-width="150" />
      <el-table-column label="级别" min-width="100">
        <template #default="scope">
          <el-tag :type="getLevelTagType(scope.row.level)">
            {{ getLevelText(scope.row.level) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="父级地址" min-width="150">
        <template #default="scope">
          {{ scope.row.parentName || '无' }}
        </template>
      </el-table-column>
      <el-table-column label="坐标" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          <div>经度: {{ scope.row.longitude }}, 纬度: {{ scope.row.latitude }}</div>
          <div class="wgs84-coords">
            WGS84坐标 - 经度: {{ convertToWGS84(scope.row.longitude, scope.row.latitude).longitude.toFixed(6) }},
            纬度: {{ convertToWGS84(scope.row.longitude, scope.row.latitude).latitude.toFixed(6) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="排序值" min-width="80" />
      <el-table-column label="状态" min-width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status ? 'success' : 'danger'">
            {{ scope.row.status ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
      <el-table-column prop="updatedAt" label="更新时间" min-width="170">
        <template #default="scope">
          {{ formatDate(scope.row.updatedAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="230">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleCreate(scope.row)">
            添加子地址
          </el-button>
          <el-button type="primary" size="small" @click="handleUpdate(scope.row)">
            编辑
          </el-button>
          <el-popconfirm
            title="确认删除该地址?"
            @confirm="handleDelete(scope.row)"
          >
            <template #reference>
              <el-button size="small" type="danger">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @update:current-page="handlePageChange"
        @update:page-size="handleSizeChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { 
  levelOptions,
  type CommunityAddress 
} from '@/modules/admin/api/communityAddress'
import { convertToWGS84, formatDate } from '@/modules/admin/utils/coordinateTransform'

defineProps<{
  addressList: CommunityAddress[]
  loading: boolean
  currentPage: number
  pageSize: number
  total: number
}>()

const emit = defineEmits<{
  (e: 'create', parent?: CommunityAddress): void
  (e: 'update', address: CommunityAddress): void
  (e: 'delete', address: CommunityAddress): void
  (e: 'page-change', page: number): void
  (e: 'size-change', size: number): void
}>()

/**
 * @function handleCreate
 * @description 处理创建子地址
 * @param parent 父级地址数据
 */
const handleCreate = (parent: CommunityAddress) => {
  emit('create', parent)
}

/**
 * @function handleUpdate
 * @description 处理编辑地址
 * @param address 地址数据
 */
const handleUpdate = (address: CommunityAddress) => {
  emit('update', address)
}

/**
 * @function handleDelete
 * @description 处理删除地址
 * @param address 地址数据
 */
const handleDelete = (address: CommunityAddress) => {
  emit('delete', address)
}

/**
 * @function handlePageChange
 * @description 处理页码变化
 * @param page 新页码
 */
const handlePageChange = (page: number) => {
  emit('page-change', page)
}

/**
 * @function handleSizeChange
 * @description 处理每页大小变化
 * @param size 新的每页大小
 */
const handleSizeChange = (size: number) => {
  emit('size-change', size)
}

/**
 * @function getLevelText
 * @description 获取级别文本
 * @param level 级别值
 * @returns 级别文本
 */
const getLevelText = (level: number) => {
  const option = levelOptions.find(item => item.value === level)
  return option ? option.label : '未知'
}

/**
 * @function getLevelTagType
 * @description 获取级别标签类型
 * @param level 级别值
 * @returns 标签类型
 */
const getLevelTagType = (level: number) => {
  const typeMap: Record<number, string> = {
    1: 'primary',
    2: 'success',
    3: 'warning'
  }
  return typeMap[level] || 'info'
}
</script>

<style scoped>
.table-container {
  margin-top: 20px;
}

.wgs84-coords {
  color: #67C23A;
  font-style: italic;
  font-size: 12px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
