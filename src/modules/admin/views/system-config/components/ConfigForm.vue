<!--
 * @Description: 系统配置表单组件
 * @Author: AI Assistant
 * @Date: 2025-05-28
 -->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑配置' : '新增配置'"
    width="50%"
    destroy-on-close
    @closed="handleDialogClosed"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="配置键名" prop="configKey">
        <el-input v-model="formData.configKey" :disabled="isEdit" placeholder="请输入配置键名，如：site_name" />
      </el-form-item>
      <el-form-item label="配置类型" prop="configType">
        <el-select v-model="formData.configType" placeholder="请选择配置类型">
          <el-option label="文本" value="text" />
          <el-option label="数字" value="number" />
          <el-option label="布尔值" value="boolean" />
          <el-option label="JSON" value="json" />
          <el-option label="HTML" value="html" />
          <el-option label="图片" value="image" />
        </el-select>
      </el-form-item>
      <el-form-item label="配置值" prop="configValue">
        <el-input
          v-if="formData.configType === 'text'"
          v-model="formData.configValue"
          type="text"
          placeholder="请输入配置值"
        />
        <div v-else-if="formData.configType === 'html'" class="editor-container">
          <Toolbar
            style="border-bottom: 1px solid #ccc"
            :editor="editorRef"
            :defaultConfig="toolbarConfig"
            mode="default"
          />
          <Editor
            style="height: 300px; overflow-y: hidden;"
            v-model="formData.configValue"
            :defaultConfig="editorConfig"
            mode="default"
            @onCreated="handleEditorCreated"
          />
        </div>
        <el-input-number
          v-else-if="formData.configType === 'number'"
          v-model="formData.configValue"
          :controls="false"
          placeholder="请输入数字值"
          style="width: 100%"
        />
        <el-select
          v-else-if="formData.configType === 'boolean'"
          v-model="formData.configValue"
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option :label="'是'" :value="'1'" />
          <el-option :label="'否'" :value="'0'" />
        </el-select>
        <!-- Banner配置编辑器 -->
        <banner-config-editor
          v-else-if="formData.configType === 'json' && formData.category === 'banner'"
          v-model="formData.configValue"
        />
        <!-- App菜单配置编辑器 -->
        <app-menu-config-editor
          v-else-if="formData.configType === 'json' && formData.category === 'appmenu'"
          v-model="formData.configValue"
        />
        <!-- 配送费配置编辑器 -->
        <delivery-fee-config-editor
          v-else-if="formData.configType === 'json' && formData.category === 'deliveryFee'"
          v-model="formData.configValue"
        />
        <div v-else-if="formData.configType === 'image'">
          <div v-if="formData.configValue" class="image-preview-container">
            <el-image :src="formattedImageUrl" fit="contain" class="preview-image" />
          </div>
          <FileUploader
            file-usage="site_logo"
            accept="image/*"
            :max-size="2"
            button-type="primary"
            :show-file-list="false"
            @success="handleImageUploadSuccess"
          >
            <template #tip>
              <div class="uploader-tip">
                建议格式：PNG/JPG，大小不超过2MB
              </div>
            </template>
          </FileUploader>
          <el-button v-if="formData.configValue" type="danger" plain @click="formData.configValue = ''" style="margin-top: 8px">删除图片</el-button>
        </div>
        <el-input
          v-else-if="formData.configType === 'json'"
          v-model="formData.configValue"
          type="textarea"
          :rows="5"
          placeholder="请输入JSON格式的配置值"
        />
        <el-input
          v-else
          v-model="formData.configValue"
          placeholder="请输入配置值"
        />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入配置描述"
        />
      </el-form-item>
      <div v-if="isEdit">
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="系统配置" prop="isSystem">
          <el-select v-model="formData.isSystem" placeholder="请选择是否系统配置" style="width: 100%">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, shallowRef, computed, watch, onBeforeUnmount } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import type { IDomEditor } from '@wangeditor/editor'
import { ElMessage, type FormInstance } from 'element-plus'

// 子组件
import BannerConfigEditor from '@/modules/admin/components/BannerConfigEditor.vue'
import AppMenuConfigEditor from '@/modules/admin/components/AppMenuConfigEditor.vue'
import DeliveryFeeConfigEditor from '@/modules/admin/components/DeliveryFeeConfigEditor.vue'
import FileUploader from '@/components/common/FileUploader.vue'

// 工具和类型
import EditorUploader, { type InsertImageFnType, type InsertVideoFnType } from '@/components/common/EditorUploader'
import { adjustLinkProtocol } from '@/utils/format'
import type { SystemConfig } from '@/modules/admin/api/systemConfig'

// 样式
import '@wangeditor/editor/dist/css/style.css'

/**
 * 组件属性
 */
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  configData: {
    type: Object as () => SystemConfig,
    default: () => ({})
  },
  category: {
    type: String,
    default: 'system'
  },
  loading: {
    type: Boolean,
    default: false
  }
})

/**
 * 组件事件
 */
const emits = defineEmits(['update:visible', 'submit', 'closed'])

// 表单引用
const formRef = ref<FormInstance>()
// 对话框可见性
const dialogVisible = computed({
  get() {
    return props.visible
  },
  set(val) {
    emits('update:visible', val)
  }
})

const formattedImageUrl = computed(() => {
  return adjustLinkProtocol(formData.configValue)
})

/**
 * 处理图片上传成功
 * @param response
 */
// 处理图标上传成功
const handleImageUploadSuccess = (response: any) => {
  console.log('图标上传成功:', response)
  if (response && response.file_url) {
    formData.configValue = response.file_url
    ElMessage.success('图标上传成功')
  } else {
    ElMessage.error('图标上传失败，请重试')
  }
}

// 表单数据
const formData = reactive<SystemConfig>({
  id: '',
  configKey: '',
  configValue: '',
  configType: 'text',
  description: '',
  category: props.category,
  status: 0,
  isSystem: 0
})

// 编辑器相关
const editorRef = shallowRef()

/**
 * 编辑器配置
 */
const editorConfig = {
  placeholder: '请输入内容...',
  MENU_CONF: {
    // 图片上传配置
    uploadImage: {
      // 自定义上传
      customUpload(file: File, insertFn: InsertImageFnType) {
        EditorUploader.uploadImage(file, insertFn);
      },
      // 小于该值就插入base64格式
      base64LimitSize: 5 * 1024, // 5kb
    },
    
    // 视频上传配置
    uploadVideo: {
      // 自定义上传
      customUpload(file: File, insertFn: InsertVideoFnType) {
        EditorUploader.uploadVideo(file, insertFn);
      },
      // 视频文件大小限制
      maxFileSize: 50 * 1024 * 1024, // 50MB
    }
  }
}

// 工具栏配置
const toolbarConfig = {
  excludeKeys: [],
}

// 表单验证规则
const rules = {
  configKey: [
    { required: true, message: '请输入配置键名', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_.]+$/, message: '键名只能包含字母、数字、下划线和点', trigger: 'blur' }
  ],
  configValue: [
    { required: true, message: '请输入配置值', trigger: 'blur' }
  ],
  configType: [
    { required: true, message: '请选择配置类型', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入配置描述', trigger: 'blur' }
  ]
}

/**
 * 监听配置数据变化，更新表单
 */
watch(() => props.configData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.assign(formData, newVal)
  }
}, { immediate: true, deep: true })

/**
 * 监听分类变化
 */
watch(() => props.category, (newVal) => {
  formData.category = newVal
  
  // Banner分类默认使用JSON格式
  if (newVal === 'banner' && !props.isEdit) {
    formData.configType = 'json'
    formData.configValue = '[]' // 空数组作为默认值
  }
}, { immediate: true })

/**
 * 处理表单提交
 */
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 处理特定类型的值
    let processedValue = formData.configValue
    if (formData.configType === 'json') {
      try {
        // 验证JSON格式
        JSON.parse(formData.configValue)
      } catch (e) {
        ElMessage.error('JSON格式不正确')
        return
      }
    }
    
    // 提交表单
    emits('submit', {
      ...formData,
      value: processedValue
    })
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

/**
 * 编辑器创建完成回调
 */
const handleEditorCreated = (editor: IDomEditor) => {
  editorRef.value = editor
}

/**
 * 对话框关闭处理
 */
const handleDialogClosed = () => {
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  // 销毁编辑器
  const editor = editorRef.value
  if (editor) {
    editor.destroy()
  }
  
  // 触发关闭事件
  emits('closed')
}

/**
 * 组件销毁前，销毁编辑器实例
 */
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})
</script>

<style scoped>
.image-preview-container {
  margin-bottom: 10px;
}

.preview-image {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.uploader-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.editor-container {
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}
</style>
