<!--
 * @Description: 社区地址管理 - 操作按钮组件
 * @Author: AI Assistant
 * @Date: 2025-06-09
 * @Version: 1.0.0
-->
<template>
  <div class="action-container">
    <el-button type="primary" @click="$emit('create')">
      <el-icon><Plus /></el-icon> 添加地址
    </el-button>
    <el-button type="info" @click="$emit('refresh-cache')">
      <el-icon><Refresh /></el-icon> 刷新缓存
    </el-button>
    <el-button @click="$emit('toggle-view')">
      <el-icon><List /></el-icon> {{ viewMode === 'tree' ? '列表视图' : '树形视图' }}
    </el-button>
  </div>
</template>

<script lang="ts" setup>
import { Plus, Refresh, List } from '@element-plus/icons-vue'

defineProps<{
  viewMode: 'tree' | 'list'
}>()

defineEmits<{
  (e: 'create'): void
  (e: 'refresh-cache'): void
  (e: 'toggle-view'): void
}>()
</script>

<style scoped>
.action-container {
  margin-bottom: 20px;
}
</style>
