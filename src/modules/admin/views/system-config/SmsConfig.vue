<script setup lang="ts">
/**
 * @file 短信配置管理组件
 * @description 该组件用于管理系统的短信服务配置，包括创建、查询、更新和刷新缓存功能。
 */
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox, ElButton, ElCard, ElDescriptions, ElDescriptionsItem, ElTag, ElDivider } from 'element-plus';
import { getSmsConfig, saveSmsConfig, refreshSmsConfigCache, type SmsConfigData, type SaveSmsConfigRequest } from '@/modules/admin/api/smsConfig';
import SmsConfigForm from './components/SmsConfigForm.vue';

// 配置数据
const configData = ref<SmsConfigData>({});

// 加载状态
const loading = ref(false);

// 是否显示编辑表单
const showEditForm = ref(false);

/**
 * @function fetchSmsConfig
 * @description 获取当前的短信配置信息
 */
const fetchSmsConfig = async () => {
  loading.value = true;
  try {
    const res = await getSmsConfig();
    configData.value = res || {};
    console.log('获取到SMS配置:', configData.value);
  } catch (error) {
    console.error('获取短信配置失败:', error);
    ElMessage.error('获取短信配置失败，请检查网络或联系管理员');
  } finally {
    loading.value = false;
  }
};

/**
 * @function handleEditConfig
 * @description 显示编辑表单
 */
const handleEditConfig = () => {
  showEditForm.value = true;
};

/**
 * @function handleSaveConfig
 * @description 保存短信配置
 * @param {SaveSmsConfigRequest} formData - 表单数据
 */
const handleSaveConfig = async (formData: SaveSmsConfigRequest) => {
  loading.value = true;
  try {
    await saveSmsConfig(formData);
    ElMessage.success('短信配置保存成功');
    showEditForm.value = false; // 关闭编辑表单
    await fetchSmsConfig(); // 重新获取配置
  } catch (error) {
    console.error('保存短信配置失败:', error);
    ElMessage.error('保存短信配置失败，请检查网络或联系管理员');
  } finally {
    loading.value = false;
  }
};

/**
 * @function handleCancelEdit
 * @description 取消编辑
 */
const handleCancelEdit = () => {
  showEditForm.value = false;
};

/**
 * @function handleRefreshCache
 * @description 刷新短信配置缓存
 */
const handleRefreshCache = async () => {
  try {
    await ElMessageBox.confirm('确定要刷新短信配置缓存吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    loading.value = true;
    await refreshSmsConfigCache();
    ElMessage.success('缓存刷新成功');
    await fetchSmsConfig(); // 重新获取配置
  } catch (error) {
    if (error !== 'cancel') {
      console.error('刷新缓存失败:', error);
      ElMessage.error('刷新缓存失败，请检查网络或联系管理员');
    }
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取初始配置
onMounted(() => {
  fetchSmsConfig();
});
</script>

<template>
  <el-card
    class="sms-config-card"
    v-loading="loading"
    element-loading-text="加载中..."
  >
    <template #header>
      <div class="card-header">
        <span>短信服务配置</span>
        <div>
          <el-button
            type="primary"
            :icon="showEditForm ? 'View' : 'Edit'"
            @click="showEditForm ? handleCancelEdit() : handleEditConfig()"
          >
            {{ showEditForm ? '查看配置' : '编辑配置' }}
          </el-button>
          <el-button
            v-if="!showEditForm"
            type="warning"
            icon="Refresh"
            @click="handleRefreshCache"
          >
            刷新缓存
          </el-button>
        </div>
      </div>
    </template>
    
    <!-- 显示配置详情 -->
    <div v-if="!showEditForm">
      <el-descriptions
        title="短信服务提供商信息"
        :column="1"
        border
      >
        <el-descriptions-item label="配置ID">
          {{ configData.id || '尚未创建' }}
        </el-descriptions-item>
        <el-descriptions-item label="服务提供商">
          {{ configData.providerText || '--' }} ({{ configData.provider || '--' }})
        </el-descriptions-item>
        <el-descriptions-item label="访问密钥ID">
          {{ configData.accessKey || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="configData.status === 1 ? 'success' : 'info'">
            {{ configData.statusText || (configData.status === 1 ? '启用' : '禁用') }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="left">短信模板配置</el-divider>
      
      <el-descriptions :column="1" border>
        <el-descriptions-item label="短信签名">
          {{ configData.signName || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="注册验证码模板">
          {{ configData.templateCodeRegister || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="登录验证码模板">
          {{ configData.templateCodeLogin || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="重置密码模板">
          {{ configData.templateCodeResetPwd || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="通知消息模板">
          {{ configData.templateCodeNotice || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="每日发送上限">
          {{ configData.dailyLimit || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          {{ configData.remark || '--' }}
        </el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="left">更新记录</el-divider>
      
      <el-descriptions :column="1" border>
        <el-descriptions-item label="创建时间">
          {{ configData.createdAt || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="最后更新时间">
          {{ configData.updatedAt || '--' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    
    <!-- 显示编辑表单 -->
    <div v-else>
      <SmsConfigForm 
        :config="configData"
        :loading="loading"
        @save="handleSaveConfig"
        @cancel="handleCancelEdit"
      />
    </div>
  </el-card>
</template>

<style scoped>
.sms-config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.el-divider {
  margin: 24px 0;
}
</style>
