<!--
  文件：GridManagement.vue
  职责：网格管理页面的主视图组件，负责展示和管理网格数据，处理用户相关操作。
  主要功能：展示网格列表、支持增删改查、与store及service层交互。
  作者：张二浩
  创建时间：2025-04-15
  更新时间：2025-04-17
  网格管理视图，用于生成和管理网格配置
  本文件为网格管理页面，布局组件已迁移为 grid-layout-plus
-->
<!--
  GridManagement.vue
  管理后台的网格布局管理页面，实现了网格组件的增删改查和布局持久化。
-->
<!--
  GridManagement.vue
  本文件用于管理后台网格布局，包括保存与同步布局位置信息等逻辑。
-->
<!--
  GridManagement.vue
  网格管理页面，包含对GridLayout子组件的主动方法调用
-->
<!--
  @file GridManagement.vue
  @description 配置管理主页面，包含配置列表与操作（加载、启用、禁用、删除等）
  <AUTHOR> AI
  @date 2025-04-23
-->
<template>
  <div class="api-management">
    <!-- 选择行放入 el-card -->
    <el-card class="box-card">
      <!-- 模块选择器组件 -->
      <ModuleSelector
        :modules="store.modules"
        v-model="store.selectedModule"
        v-model:selected-group="store.selectedGroup"
        :group-names="store.groupNames"
        @module-change="store.handleModuleChange"
        @group-change="store.handleGroupChange"
        @refresh-current-module="store.refreshCurrentModule"
        @refresh-all-modules="store.refreshAllModules"
        @show-dtos="store.showDtosDrawer = true"
      />
      
      <!-- 页面配置列表卡片 -->
      <div style="margin-top: 20px">
        <el-row :gutter="20">
          <el-col :span="24">
            <div v-if="!store.isConfigListCollapsed">
              <!-- 这里将 el-table 的 data 绑定到 filteredUiConfigs -->
              <el-table :data="filteredUiConfigs" style="width: 100%" border>
                <el-table-column type="expand">
                  <template #default="props">
                    <p><strong>路径:</strong> {{ props.row.frontend_path }}</p>
                    <p><strong>模块/分组:</strong> {{ props.row.module }}/{{ props.row.group }}</p>
                    <p><strong>备注:</strong> {{ props.row.remark }}</p>
                    <p><strong>更新时间:</strong> {{ props.row.updated_at }}</p>
                  </template>
                </el-table-column>
                <el-table-column label="ID" prop="id" width="80" />
                <el-table-column label="图标" width="100">
                  <template #default="scope">
                    <IconSelector v-model="scope.row.icon" @update:model-value="() => store.updateConfigIcon(scope.row)" />
                  </template>
                </el-table-column>
                <el-table-column label="version" prop="version" />
                <el-table-column label="标题" prop="title" width="180" />
                <el-table-column label="配置键" prop="config_key" />
                <el-table-column label="状态" prop="status" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                      {{ scope.row.status === 1 ? '启用' : '禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template #default="scope">
                    <el-button 
                      type="primary" 
                      link 
                      @click="store.loadConfigById(scope.row)"
                    >
                      加载配置
                    </el-button>
                    
                    <el-button 
                      v-if="scope.row.status === 0"
                      type="success" 
                      link 
                      @click="store.updateConfigStatus(scope.row, 1)"
                    >
                      启用
                    </el-button>
                    
                    <el-button 
                      v-if="scope.row.status === 1"
                      type="warning" 
                      link 
                      @click="store.updateConfigStatus(scope.row, 0)"
                    >
                      禁用
                    </el-button>
                    
                    <el-button 
                      type="danger"
                      link
                      @click="handleDelete(scope.row)"
                    >
                      删除
                    </el-button>
                    
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else class="collapsed-card-icon" @click="store.isConfigListCollapsed = false">
              <el-icon><Document /></el-icon>
              <span>配置列表 ({{ store.uiConfigs.length }})</span>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 页面配置工具栏 -->
      <el-row v-if="store.gridLayoutStore.structureData" :gutter="20" class="page-config-row">
        <el-col :span="7">
          <el-input
            v-model="store.gridLayoutStore.structureData.title"
            placeholder="页面标题"
            clearable
          />
        </el-col>
        <el-col :span="7">
          <el-input
            v-model="store.gridLayoutStore.structureData.frontend_path"
            placeholder="前端路由路径"
            clearable
          />
        </el-col>
        <el-col :span="3">
          <IconSelector v-model="store.gridLayoutStore.structureData.icon" />
        </el-col>
        <el-col :span="3">
          <el-button 
            type="primary" 
            @click="store.saveConfig"
            :disabled="!store.gridLayoutStore.structureData || !store.gridLayoutStore.structureData.title"
          >
            保存配置
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button 
            type="warning" 
            @click="store.unloadConfig"
          >
            卸载配置
          </el-button>
        </el-col>
      </el-row>
      <el-row v-else :gutter="20" class="page-config-row">
        <el-col :span="8">
          <el-input
            v-model="store.pageTitle"
            placeholder="页面标题"
            clearable
          />
        </el-col>
        <el-col :span="8">
          <el-input
            v-model="store.frontendPath"
            placeholder="前端路由路径"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <IconSelector v-model="store.selectedIcon" />
        </el-col>
        <el-col :span="4">
          <el-button 
            type="primary" 
            @click="store.createConfig"
            :disabled="!store.pageTitle"
          >
            新建配置
          </el-button>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- API列表与控制器列表 -->
    <el-row :gutter="20">
      <el-col :span="18">
        <ApiListCard
          v-model:is-collapsed="store.isApiListCollapsed"
          :apis="store.apis"
          :store="store"
          :can-set-base-url="true"
          @set-base-url="store.setBaseUrl"
        />
      </el-col>
      <el-col :span="6">
        <ControllersListCard
          v-model:is-collapsed="store.isControllersListCollapsed"
          :controllers="store.controllers"
          :store="store"
        />
      </el-col>
    </el-row>
    <!-- 页面配置编辑器组件 -->
    <GridPageConfigEditor
      v-if="true"
      :store="store"
      @refresh-page="store.refreshBasePage"
    />
    <!-- 网格管理区域 -->
    <el-card v-if="store.showPage" class="box-card" style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <h3>{{ store.gridLayoutStore.structureData?.title || 'DTO预览' }}</h3>
          <div>
            <el-button type="default" @click="handlePrevStep">
              <el-icon><ArrowLeft /></el-icon>
              上一步
            </el-button>
            <el-button type="default" @click="handleNextStep">
              <el-icon><ArrowRight /></el-icon>
              下一步
            </el-button>
            <el-button type="primary" @click="handleGridRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="success" @click="store.openGridItemDialog(-1)">
              <el-icon><Plus /></el-icon>
              添加网格项
            </el-button>
            <el-button type="info" @click="openGridManagement">
              <el-icon><Setting /></el-icon>
              网格管理
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 网格布局区域 -->
      <GridLayout
        ref="gridLayoutRef"
        v-if="store.showPage"
        :key="gridKey+ 'gridlayout'"
        :store="store.gridLayoutStore"
        :editable="true"
        @item-click="editGridItem"
        @item-delete="removeGridItem"
        @items-updated="(val) => savePositions(val)"
      />
    </el-card>
    
    <!-- DTOs抽屉组件 -->
    <DTOsDrawer
      v-model="store.showDtosDrawer"
      :dtos="store.dtos"
      :selected-group="store.selectedGroup"
      :store="store"
      @select-dto="() => {
        store.setDtoSource();
        store.generateConfigFromSource();
      }"
    />
    
    <!-- 网格项编辑对话框 -->
    <GridItemDialog
      v-model="store.showGridDialog" 
      v-model:item="store.currentGridItem"
      :id="store.currentId"
      :store="store"
      @save="store.saveGridItem"
      
    />
    
    <!-- 网格信息管理对话框 -->
    <el-dialog
      v-model="showGridManagementDialog"
      title="网格信息管理"
      width="80%"
      destroy-on-close
    >
      <GridInfoManagement :store="store" @add-grids-to-page="handleBatchAddGrids" @copy-grids-to-page="handleAddGrids" @add-grid-to-page="handleAddGrids" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showGridManagementDialog = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, provide, computed, watch, nextTick, toRaw } from 'vue';
import GridPageConfigEditor from '@/components/page/GridPageConfigEditor.vue';
import { Document, Plus, Refresh, Setting, ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import IconSelector from '@/components/page/IconSelector.vue';
import type { AdminGridInfoDTO, AdminUIConfigDTO, LayoutGridItem } from '@/modules/admin/types';
import { ElMessageBox, ElMessage } from 'element-plus';
//import cloneDeep from 'lodash/cloneDeep';
// 导入自定义组件
import ModuleSelector from '@/modules/admin/views/gridManagement/components/ModuleSelector.vue';
import ApiListCard from '@/modules/admin/views/gridManagement/components/ApiListCard.vue';
import ControllersListCard from '@/modules/admin/views/gridManagement/components/ControllersListCard.vue';
import DTOsDrawer from '@/modules/admin/views/gridManagement/components/DTOsDrawer.vue';
import GridItemDialog from '@/modules/admin/views/gridManagement/components/GridItemDialog.vue';
import GridInfoManagement from '@/modules/admin/views/GridInfoManagement.vue';
import GridLayout from '@/components/page/GridLayout.vue';

// 导入Store
import { useGridManagementStore } from './gridManagementStore';
import localforage from 'localforage';

// 使用Store（单例模式）
const store = useGridManagementStore();

// 网格管理对话框状态  @close="handleGridRefresh"
const showGridManagementDialog = ref(false);
const gridKey = ref(0);
//const currentId = ref(0);
// 提供store，供子组件使用（组件通信优化）
provide('gridManagementStore', store);

// GridLayout子组件的ref，类型安全声明
const gridLayoutRef = ref<InstanceType<typeof GridLayout> | null>(null)

/**
 * 计算属性：根据所选模块和分组过滤页面配置列表
 * 只展示 module/group 匹配所选项的内容
 */
const filteredUiConfigs = computed(() => {
  // 若未选择模块或分组，则展示全部
  if (!store.selectedModule && !store.selectedGroup) return [];
  return store.uiConfigs.filter(
    (item: any) => {
      const moduleMatch = !store.selectedModule || item.module === store.selectedModule;
      const groupMatch = !store.selectedGroup || item.group === store.selectedGroup;
      return moduleMatch && groupMatch;
    }
  );
});

/**
 * 保存网格布局位置，并同步更新 localforage 中 last_config 的 grid_items.position 字段
 * @param val 网格布局的最新位置信息数组 [{id, x, y, w, h}]
 */
const savePositions = async (val: Array<{i: string | number, x: number, y: number, w: number, h: number}>) => {
  // 1. 获取上次保存的配置
  let lastConfig: AdminUIConfigDTO | null = await localforage.getItem('grid_management_last_config');
  console.log('lastConfig', lastConfig, val);
  try {
    if (!lastConfig) {
      //console.error('未找到上次保存的配置', store.gridLayoutStore.structureData); // 新建一个上次保存配置
      if(!store.gridLayoutStore.structureData) return;
      lastConfig = store.gridLayoutStore.structureData as unknown as AdminUIConfigDTO;
      //return;
    }

    // 3. 遍历 grid_items，更新 position 字段
    if (Array.isArray(lastConfig.grid_items)) {
      //寻找grid_items中是否有id为-1的项，有则return
      const index = lastConfig.grid_items.findIndex(item => item.id === -1);
      if (index !== -1) return;
      lastConfig.grid_items.forEach(item => {

        // val 的 id 字段和 grid_items 的 id 对应
        const match = val.find(v => String(v.i) === String(item.id));
        if (match) {
          // position 字段为字符串，内容是 {x, y, w, h}
          item.position = {
            i: item.id,
            x: match.x,
            y: match.y,
            w: match.w,
            h: match.h
          };
        }
      });
    }
    //lastConfig.config_content = configContentObj;
    // 4. 如果 config_content 里也有 grid_items，做同步（可选，视结构而定）
    if(store.gridLayoutStore.structureData) { // 取消，因为自己修改的store，不需要同步
      let configContentObj = store.gridLayoutStore.structureData
      if (Array.isArray(configContentObj.grid_items)) {
        configContentObj.grid_items.forEach((item: AdminGridInfoDTO) => {
          const match = val.find(v => String(v.i) === String(item.id) || String(v.i) === String(item.id));
          if (match) {
            item.position = {
              i: item.id,
              x: match.x,
              y: match.y,
              w: match.w,
              h: match.h
            };
          }
        });
      }
    }

    const rawConfig = JSON.parse(JSON.stringify(toRaw(lastConfig)));
    // 5. 存回 localforage
    await localforage.setItem('grid_management_last_config', rawConfig);

    // 6. 调用 store 更新
    await store.updateGridPositions(val.map(item => ({
      i: item.i,
      x: item.x,
      y: item.y,
      w: item.w,
      h: item.h
    })));
  } catch (error) {
    console.error('更新网格布局位置失败:', error);
  }
};

// 编辑网格项
function editGridItem(gridItem: LayoutGridItem) {

  const id = gridItem.i;
  //const gridInfo = store.gridLayoutStore.structureData?.grid_items.find(item => item.id === id);
  //console.log('[GridManagement.vue]编辑网格项:', gridInfo);
  store.openGridItemDialog(Number(id));
}

// 删除网格项
function removeGridItem(id: number) {
  ElMessageBox.confirm(
    '确定要删除这个网格项吗？此操作不可恢复。',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    store.removeGridItem(id);
    ElMessage.success('删除成功');
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
}

// 打开网格管理对话框
function openGridManagement() {
  showGridManagementDialog.value = true;
}

// 刷新网格
function handleGridRefresh() {
  gridKey.value = Date.now();
  store.refreshGridItems();
  ElMessage.success('网格已刷新');
}

/**
 * 处理批量添加网格项到页面
 * @param {Array<AdminGridInfoDTO>} grids 需要添加的网格项数组
 */
function handleBatchAddGrids(grids: AdminGridInfoDTO[]) {
  // 批量添加网格项到页面
  if (Array.isArray(grids) && grids.length > 0) {
    // addGridItem方法
    grids.map(grid => {grid.position = { i:grid.id, x: 0, y: 0, w: 6, h: 4}});
    store.addGridItems(grids);
    //service.setBatchUIConfigGridRelation({ uiConfigId: store.selectedUiConfig?.id as number, gridInfoIds: grids.map(grid => Number(grid.id)) });
    ElMessage.success(`已批量添加${grids.length}个网格项到页面`);
  } else {
    ElMessage.info('没有可添加的网格项');
  }
}

function handleAddGrids(gridInfo: AdminGridInfoDTO) {
  console.log('handleAddGrids', gridInfo);
  store.addGridItem(gridInfo);
  ElMessage.success('已添加"' + gridInfo.name + '"到页面');
}

/**
 * 上一步，step减1
 */
function handlePrevStep() {
  store.gridLayoutStore.step--;
}

/**
 * 下一步，step加1
 */
function handleNextStep() {
  store.gridLayoutStore.step++;
}

// 调用子组件的refreshLayout方法
function callChildRefreshLayout() {
  // 主动调用GridLayout子组件暴露的方法
  gridLayoutRef.value?.refreshLayout && gridLayoutRef.value.refreshLayout();
}

watch(() => store.currentId, async (newId) => {
  if(newId === 0) {
    console.log('currentId 归零了！', newId);
    await nextTick();
    callChildRefreshLayout();
  }
}, { immediate: true });


// 启动时初始化
onMounted(async () => {
  // 初始化模块数据
  store.handleModuleChange();
  //console.log('【调试】store，onMounted', store, store.isUnloadingConfig);
  
  // 如果处于卸载配置的状态，则不尝试加载缓存配置
  if (store.isUnloadingConfig) {
    console.log('正在卸载配置，不加载缓存配置');
    return;
  }
  
  // 尝试加载缓存的配置（localforage）
  try {
    const cachedConfig = await localforage.getItem('grid_management_last_config');
    console.log('cachedConfig', cachedConfig);
    
    // 检查是否存在有效的缓存配置
    if (cachedConfig && !store.isUnloadingConfig) {
      // 类型校验，确保为对象且包含 AdminUIConfigDTO 的所有关键字段
      store.loadConfigById(cachedConfig as AdminUIConfigDTO);
    }
  } catch (e) {
    console.error('加载缓存配置失败', e);
    store.handleModuleChange();
  }
});

// 组件卸载前保存当前状态
onBeforeUnmount(async () => {
  try {
    //console.log('【调试】store，onBeforeUnmount', store.gridLayoutStore.structureData);
    store.saveLastConfig();
  } catch (e) {
    //console.error('【错误】保存配置到localforage失败:', e);
  }
});

// 删除操作弹窗确认
function handleDelete(row: AdminUIConfigDTO) {
  ElMessageBox.confirm('确定要删除该配置吗？', '提示', {
    confirmButtonText: '删除',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    store.deleteConfig(row);
  });
}
</script>

<style scoped>
.api-management {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-config-row {
  margin-top: 20px;
}

.collapsed-card-icon {
  text-align: center;
  cursor: pointer;
  padding: 10px;
  border: 1px dashed #ccc;
  border-radius: 4px;
  margin-bottom: 10px;
}

.collapsed-card-icon:hover {
  background-color: #f5f7fa;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}

:deep(.el-card__header) {
  padding: 10px 20px;
}

:deep(.el-card__body) {
  padding: 15px;
}
</style>