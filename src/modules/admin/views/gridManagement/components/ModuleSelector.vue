<!--
  ModuleSelector.vue
  模块选择器组件，提供模块和分组的选择功能
-->
<template>
  <div class="module-selector">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-select 
          v-model="selectedModule" 
          placeholder="请选择模块" 
          @change="handleModuleChange"
          clearable
        >
          <el-option
            v-for="module in modules"
            :key="module"
            :label="module"
            :value="module"
          />
        </el-select>
      </el-col>
      <el-col :span="8">
        <el-select 
          v-model="selectedGroup" 
          placeholder="请选择分组" 
          :disabled="!groupNames.length" 
          @change="handleGroupChange"
          clearable
        >
          <el-option
            v-for="group in groupNames"
            :key="group.page_name"
            :label="group.page_name"
            :value="group.page_name"
          />
        </el-select>
      </el-col>
      <el-col :span="8">
        <el-button @click="refreshCurrentModule" type="primary">刷新当前</el-button>
        <el-button @click="refreshAllModules" type="danger">刷新所有</el-button>
        <el-button @click="showDtos" type="primary">查看 DTOs</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { PropType } from 'vue';
import type { GroupName } from '../GridManagementService';

// 定义组件属性
const props = defineProps({
  // 模块列表
  modules: {
    type: Array as PropType<string[]>,
    required: true
  },
  // 当前选中的模块
  modelValue: {
    type: String,
    required: true
  },
  // 选中的分组
  selectedGroup: {
    type: String,
    default: ''
  },
  // 分组列表
  groupNames: {
    type: Array as PropType<GroupName[]>,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits([
  'update:modelValue',           // 更新选中的模块
  'update:selectedGroup',        // 更新选中的分组
  'module-change',               // 模块变更事件
  'group-change',                // 分组变更事件
  'refresh-current-module',      // 刷新当前模块事件
  'refresh-all-modules',         // 刷新所有模块事件
  'show-dtos'                    // 显示DTOs抽屉事件
]);

// 计算属性：选中的模块（双向绑定）
const selectedModule = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value);
  }
});

// 计算属性：选中的分组（双向绑定）
const selectedGroup = computed({
  get: () => props.selectedGroup,
  set: (value) => {
    emit('update:selectedGroup', value);
  }
});

/**
 * 处理模块变更
 */
function handleModuleChange() {
  emit('module-change', selectedModule.value);
}

/**
 * 处理分组变更
 * @param newValue 新选中的分组值
 */
function handleGroupChange(newValue: string) {
  console.log('handleGroupChange', newValue);
  emit('group-change', newValue);
}

/**
 * 刷新当前模块
 */
function refreshCurrentModule() {
  emit('refresh-current-module');
}

/**
 * 刷新所有模块
 */
function refreshAllModules() {
  emit('refresh-all-modules');
}

/**
 * 显示DTOs抽屉
 */
function showDtos() {
  emit('show-dtos');
}
</script>

<style scoped>
.module-selector {
  margin-bottom: 20px;
}
</style>