<!--
  ApiListCard.vue
  API列表卡片组件，显示API列表及操作
  增加了API列表过滤功能，只显示path中包含store.selectedGroup的数据
-->
<template>
  <el-card class="box-card">
    <template #header>
      <div class="card-header">
        <span>api列表</span>
        <el-button 
          link
          @click="isCollapsed = !isCollapsed"
          :icon="isCollapsed ? 'Expand' : 'Fold'"
        >
          {{ isCollapsed ? '展开' : '折叠' }}
        </el-button>
      </div>
    </template>
    <div v-if="!isCollapsed">
      <el-table :data="filteredApis" style="width: 100%" row-key="id" border>
        <el-table-column type="expand">
          <template #default="props">
            <p><strong>Path:</strong> {{ props.row.path }}</p>
            <p><strong>Method:</strong> {{ props.row.method }}</p>
            <p><strong>Description:</strong> {{ props.row.description }}</p>
            <p><strong>Controller:</strong> {{ props.row.controller_name }}</p>
          </template>
        </el-table-column>
        <el-table-column label="ID" prop="id" width="80" />
        <el-table-column label="Name" prop="name" width="200" />
        <el-table-column label="Path" prop="path" />
        <el-table-column label="Method" prop="method" width="100" />
        <el-table-column label="Description" prop="description" />
        <el-table-column label="Controller" prop="controller_name" />
        <el-table-column label="Status" prop="status" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? 'Active' : 'Inactive' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              link 
              @click="() => setBaseUrl(scope.row.path)"
              :disabled="!canSetBaseUrl"
            >
              设为baseUrl
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-else class="collapsed-card-icon" @click="isCollapsed = false">
      <el-icon><Document /></el-icon>
      <span>API列表 ({{ filteredApis.length }})</span>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import type { PropType } from 'vue';
import { Document } from '@element-plus/icons-vue';

// 定义组件属性
const props = defineProps({
  // API列表
  apis: {
    type: Array as PropType<any[]>,
    required: true
  },
  // 是否可以设置baseUrl
  canSetBaseUrl: {
    type: Boolean,
    default: false
  },
  // 网格管理store
  store: {
    type: Object as PropType<any>,
    required: true
  }
});

// 定义事件
const emit = defineEmits(['set-base-url']);

// 折叠状态
const isCollapsed = ref(true);

/**
 * 计算属性：只显示path包含selectedGroup的API
 */
const filteredApis = computed(() => {
  const group = props.store.selectedGroup;
  if (!group) return props.apis;
  return props.apis.filter(api => typeof api.path === 'string' && api.path.includes(group));
});

/**
 * 监听selectedModule变化并更新API列表
 */
watch(() => props.store.selectedModule, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    // 调用store刷新当前模块API列表
    if (typeof props.store.refreshCurrentModule === 'function') {
      props.store.refreshCurrentModule();
    }
  }
});

/**
 * 设置base URL
 */
function setBaseUrl(path: string) {
  emit('set-base-url', path);
}
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.collapsed-card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  cursor: pointer;
  color: #409eff;
  transition: all 0.3s ease;
}

.collapsed-card-icon:hover {
  background-color: #f5f7fa;
}

.collapsed-card-icon .el-icon {
  font-size: 24px;
  margin-right: 10px;
}
</style>