<!--
  @description GridItemDialog - 网格系统组件编辑对话框
  <AUTHOR>
  @date 2025-04-11
  @文件注释: GridItemDialog.vue
  用于管理网格项的弹窗表单，包括步骤、名称等字段。
  步骤字段已由单输入框修改为多选下拉框，选项为0~10，绑定数组。
-->
<template>
  <el-dialog
    :key="store.currentId||-1+'itemDialog'"
    v-model="visible"
    :title="store.currentId > 0 && store.gridLayoutStore?.structureData?.grid_items?.find((item: any) => String(item.id) === String(store.currentId)) ? '编辑组件' : '添加组件'"
    width="60%"
    destroy-on-close
  >
    <el-tabs v-if="currentGridItem">
      <el-tab-pane label="基本信息">
        <el-form :model="currentGridItem" label-width="100px">
          <el-form-item label="组件名称">
            <el-input v-model="currentGridItem.name" placeholder="请输入组件名称" />
          </el-form-item>
          
          <el-form-item label="组件类型">
            <el-select v-model="currentGridItem.content.type" placeholder="请选择组件类型" @change="handleGridItemTypeChange">
              <el-option label="页面组件" value="page" />
              <el-option label="信息卡片" value="info" />
              <el-option label="数据表格" value="table" />
              <el-option label="图表组件" value="chart" />
              <el-option label="表单组件" value="form" />
              <el-option label="自定义组件" value="custom" />
              <el-option label="搜索组件" value="search" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="图标">
            <IconSelector v-model="currentGridItem.content.icon"/>
          </el-form-item>
          
          <el-form-item label="锁定位置">
            <el-switch v-model="currentGridItem.position.static" />
          </el-form-item>
          
          <el-form-item label="禁止移动">
            <el-switch v-model="currentGridItem.position.isDraggable" />
          </el-form-item>
          
          <el-form-item label="禁止调整大小">
            <el-switch v-model="currentGridItem.position.isResizable" />
          </el-form-item>

          <el-form-item label="步骤">
            <!-- 步骤字段，支持多选，选项为0~10，绑定数组 currentGridItem.step -->
            <el-select v-model="currentGridItem.step" multiple placeholder="请选择步骤">
              <el-option v-for="n in 11" :key="n-1" :label="n-1" :value="n-1" />
            </el-select>
          </el-form-item>
          
          <!-- <el-form-item label="自动定位">
            <el-switch v-model="currentGridItem.position.isBounded" />
          </el-form-item> -->
          
          <el-form-item label="数据API">
            <el-input v-model="currentGridItem.api" placeholder="请输入数据API地址" />
          </el-form-item>
          
          <el-form-item label="备注">
            <el-input 
              v-model="currentGridItem.remark" 
              type="textarea" 
              placeholder="请输入备注说明"
            />
          </el-form-item>
          
          <el-form-item label="状态">
            <el-radio-group v-model="currentGridItem.status">
              <el-radio :value=1>启用</el-radio>
              <el-radio :value=0>禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="组件配置" v-if="currentGridItem?.content?.type">
        <!-- 使用动态组件加载不同类型的配置组件 -->
        <component 
          :is="getConfigComponent()" 
          :store="store"
          @update:content="handleContentUpdate"
        />
      </el-tab-pane>
    </el-tabs>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="() => closeDialog()">取消</el-button>
        <el-button type="primary" @click="save">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/**
 * 网格系统组件编辑对话框
 * 支持多种组件类型的配置和编辑
 */
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import IconSelector from '@/components/page/IconSelector.vue';
//import type { GridItemContent, AdminGridInfoDTO } from '../../../types';

// 导入配置组件
import InfoCardConfig from '@/modules/admin/components/config/InfoCardConfig.vue';
import TableConfig from '@/modules/admin/components/config/TableConfig.vue';
import ChartConfig from '@/modules/admin/components/config/ChartConfig.vue';
import FormConfig from '@/modules/admin/components/config/FormConfig.vue';
import CustomConfig from '@/modules/admin/components/config/CustomConfig.vue';
import SearchConfig from '@/modules/admin/components/config/SearchConfig.vue';
import PageConfig from '@/modules/admin/components/config/PageConfig.vue';

// 获取store
//const store = useGridManagementStore();

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  store: {
    type: Object,
    required: true
  }
});

const store = props.store
const emit = defineEmits([
  'update:modelValue'
]);

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});
const currentGridItem = ref<any | null>(null);

/**
 * 组件挂载时初始化数据
 */
onMounted(() => {
  console.log('[Debug] 组件挂载时 currentGridItem:', currentGridItem.value);
  initGridItemData();
  console.log('[Debug] initGridItemData后 currentGridItem:', currentGridItem.value);
});


/**
 * 初始化网格项数据
 */
/**
 * 创建默认的网格项模板
 * @returns 默认的网格项对象
 */
function createDefaultGridItem(): any {
  // 生成随机字符串作为网格项的i标识
  const randomId = `item_${Math.random().toString(36).substring(2, 9)}`;
  
  return {
    id: store.currentId, // 使用当前的负数ID作为临时ID
    name: '',
    uiConfigId: store.selectedUiConfig?.id || 0,
    step: [0], // 默认步骤为0
    status: 1, // 默认启用
    api: '',
    remark: '',
    position: {
      i: randomId,         // GridLayout 必需的标识字段
      static: false,      // 默认不锁定位置
      isDraggable: true,  // 默认可拖动
      isResizable: true,  // 默认可调整大小
      isBounded: false,   // 默认不受限制
      x: 0,               // 默认位置 x
      y: 0,               // 默认位置 y
      w: 6,               // 默认宽度
      h: 4                // 默认高度
    },
    content: {
      type: 'custom',      // 默认类型为自定义
      title: '',          // 默认标题为空
      icon: '',           // 默认无图标
      showTitle: true,    // 默认显示标题
      refreshable: true,  // 默认可刷新
      configurable: true, // 默认可配置
      editable: true,     // 默认可编辑
      closable: true,     // 默认可关闭
      config: {}          // 默认空配置
    },
    created_at: new Date().toISOString(), // 创建时间
    updated_at: new Date().toISOString()  // 更新时间
  };
}

function initGridItemData() {
  console.log('[Debug] initGridItemData开始执行，currentId:', store.currentId);
  
  // 处理负数ID（新建模式）
  if (store.currentId < 0) {
    console.log('[Debug] 检测到负数ID，创建默认网格项');
    currentGridItem.value = createDefaultGridItem();
    console.log('[Debug] 创建了默认网格项:', currentGridItem.value);
    return;
  }
  
  if(!store.gridLayoutStore.structureData || !store.gridLayoutStore.structureData.grid_items) {
    console.log('[Debug] 结构数据不存在:', {
      'structureData存在': !!store.gridLayoutStore.structureData,
      'grid_items存在': !!(store.gridLayoutStore.structureData && store.gridLayoutStore.structureData.grid_items)
    });
    return;
  }
  console.log('[Debug] 结构数据存在，grid_items长度:', store.gridLayoutStore.structureData.grid_items.length);
  //console.log('[GridItemDialog] initGridItemData', store.gridLayoutStore.structureData, store.currentId,store.gridLayoutStore.structureData.grid_items);
    // 根据id查找对应的grid item
    currentGridItem.value = store.gridLayoutStore.structureData.grid_items.find(
      (item: any) => String(item.id) === String(store.currentId)
    );
  if (!currentGridItem.value) {
    console.log('[Debug] 找不到匹配的grid item, currentId:', store.currentId);
    console.log('[Debug] 所有可用ID:', store.gridLayoutStore.structureData.grid_items.map((item: any) => item.id));
    return;
  }
  console.log('[GridItemDialog] currentGridItem:', currentGridItem.value);
  
  // 确保 currentGridItem.step 默认为 [0] 并为数组类型
  if (!Array.isArray(currentGridItem.value.step)) {
    currentGridItem.value.step = [0];
  }
}

/**
 * 获取配置组件，根据不同组件类型返回对应的配置界面
 * @returns 对应类型的配置组件
 */
function getConfigComponent() {
  if (!currentGridItem.value) {
    console.log('[Debug] getConfigComponent: currentGridItem为null，返回默认CustomConfig');
    return CustomConfig;
  }
  switch (currentGridItem.value.content.type || 'custom') {
    case 'info': return InfoCardConfig;
    case 'table': return TableConfig;
    case 'chart': return ChartConfig;
    case 'form': return FormConfig;
    case 'custom': return CustomConfig;
    case 'search': return SearchConfig;
    case 'page': return PageConfig;
    default: return CustomConfig;
  }
}

/**
 * 处理内容更新
 * @param newContent 更新后的内容
 */
function handleContentUpdate(newContent: any) {
  console.log('[GridItemDialog] 接收到内容更新:', newContent);
  
  if (!currentGridItem.value) {
    console.warn('[GridItemDialog] 无法更新内容: currentGridItem 为空');
    return;
  }
  
  if (!newContent) {
    console.warn('[GridItemDialog] 更新内容为空');
    return;
  }
  
  // 检测是否存在真正的嵌套content结构并处理
  let processedContent = { ...newContent };
  // 更精确地判断嵌套content结构：
  // 1. 存在content属性
  // 2. 不包含id、name等顶层属性（这表明是一个完整的GridItem对象）
  // 3. 内层content需要是一个对象，包含type属性
  if (processedContent.content && 
      typeof processedContent.content === 'object' &&
      processedContent.content.type && 
      !processedContent.id && 
      !processedContent.name && 
      !processedContent.position) {
    console.log('[GridItemDialog] 检测到嵌套content结构，正在处理');
    // 将内层content提取出来，代替外层
    const innerContent = processedContent.content;
    delete processedContent.content; // 删除外层content
    
    // 合并内外层属性
    Object.assign(processedContent, innerContent);
    console.log('[GridItemDialog] 处理后的content:', processedContent);
  } else if (processedContent.id && processedContent.name) {
    // 如果包含id和name，说明这是一个完整的GridItem对象，只需要取content部分
    console.log('[GridItemDialog] 检测到完整的GridItem对象，只提取content部分');
    if (processedContent.content && typeof processedContent.content === 'object') {
      processedContent = { ...processedContent.content };
      console.log('[GridItemDialog] 提取content后:', processedContent);
    }
  }
  
  // 保存当前类型以防止被覆盖
  const currentType = currentGridItem.value.content.type;
  
  // 记录更新前的重要数据状态
  console.log('[GridItemDialog] 更新前的数据状态:', JSON.stringify({
    hasConfig: !!currentGridItem.value.content.config,
    hasColumns: !!(currentGridItem.value.content.config && currentGridItem.value.content.config.columns),
    columnsLength: currentGridItem.value.content.config?.columns?.length || 0
  }));
  
  // 保存原始重要属性引用
  const originalConfig = currentGridItem.value.content.config || {};
  const originalColumns = originalConfig.columns || [];
  
  // 更新content，使用已处理的内容
  currentGridItem.value.content = {
    ...currentGridItem.value.content,     // 保留原有属性
    ...processedContent,                  // 合并处理后的属性
    config: {
      ...(currentGridItem.value.content.config || {}),  // 保留原有config
      ...(processedContent.config || {})              // 合并新config
    }
  };
  
  // 如果新内容没有columns，但原始内容有，确保保留原始的columns
  if (originalColumns.length > 0 && (!currentGridItem.value.content.config.columns || currentGridItem.value.content.config.columns.length === 0)) {
    console.log('[GridItemDialog] 检测到columns属性丢失，正在恢复');
    currentGridItem.value.content.config.columns = [...originalColumns];
  }
  
  // 如果没有类型，恢复原来的类型
  if (!currentGridItem.value.content.type) {
    currentGridItem.value.content.type = currentType;
  }
  
  // 检查这个对象是否还存在嵌套content，如果有，删除它
  // 使用类型断言绕过TypeScript的类型检查
  const contentAny = currentGridItem.value.content as any;
  if (contentAny && typeof contentAny === 'object' && contentAny.content) {
    console.log('[GridItemDialog] 警告: 仍然存在嵌套content，再次清除');
    delete contentAny.content;
  }
  
  // 检查更新后的数据状态
  console.log('[GridItemDialog] 已更新内容，更新后的数据状态:', JSON.stringify({
    hasConfig: !!currentGridItem.value.content.config,
    hasColumns: !!(currentGridItem.value.content.config && currentGridItem.value.content.config.columns),
    columnsLength: currentGridItem.value.content.config?.columns?.length || 0
  }));
  
  console.log('[GridItemDialog] 已更新内容:', currentGridItem.value.content);
}

/**
 * 处理网格项类型变更，更新默认配置
 * @param type 组件类型
 */
function handleGridItemTypeChange(type: string) {
  if (!currentGridItem.value) {
    console.log('[Debug] handleGridItemTypeChange: currentGridItem为null');
    return;
  }
  console.log('[Debug] 类型变更为:', type);

}


watch(() => store.currentId, async (newId, oldId) => {
  console.log('[Debug] currentId变化:', { oldId, newId });
  await nextTick();
  if(newId !== 0) {
    console.log('[Debug] currentId不为0，调用initGridItemData');
    initGridItemData();
  } else {
    console.log('[Debug] currentId为0，不调用initGridItemData');
  }
  console.log('[Debug] watch回调后 currentGridItem:', currentGridItem.value);
}, { deep: true, immediate: true });

/**
 * 关闭对话框
 */
async function closeDialog() {
  
    //从griditems中删除id不合法的gridItem
    store.gridLayoutStore.structureData.grid_items = store.gridLayoutStore.structureData.grid_items.filter(
      (item: any) => item.id > 0
    );
    store.gridLayoutStore.structureData.grid_items = [...store.gridLayoutStore.structureData.grid_items];
    store.saveLastConfig();
  
  visible.value = false;
  store.currentId = 0;
}

/**
 * 保存组件配置
 * 确保在保存前content已正确更新
 */
async function save() {
  if (!currentGridItem.value) {
    console.log('[Debug] save: currentGridItem为null，无法保存');
    return;
  }
  console.log('save', currentGridItem.value);
  // 确保最新的配置已更新到store
  //updateStoreContent();
  
  // 确保uiConfigId已设置
  if (store.selectedUiConfig && store.selectedUiConfig.id) {
    currentGridItem.value.uiConfigId = store.selectedUiConfig.id;
  }
  
  // 如果是新建模式，需要将当前编辑的网格项添加到store中
  if (currentGridItem.value.id <= 0) {
    // 生成一个新的id或使用store提供的方法生成
    // 这里假设store有一个方法来添加新的网格项
    await store.addGridItem(currentGridItem.value);
  } else {
    // 更新已有的网格项
    await store.saveGridItem(currentGridItem.value);
    
  }
  await store.saveLastConfig();
  //store.currentId = 0;
  await closeDialog();
}
</script>

<style scoped>
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.gap-2 {
  gap: 0.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.width-32 {
  width: 8rem;
}
</style>
