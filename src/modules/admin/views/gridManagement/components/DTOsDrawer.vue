<!--
  文件：DTOsDrawer.vue
  职责：DTO抽屉组件，负责展示、筛选和懒加载DTO数据，支持关键字过滤和选择。
  主要功能：DTO列表展示、筛选、分页、与父组件交互。
  作者：张二浩
  创建时间：2025-04-15
  更新时间：2025-04-17
  DTO抽屉组件，用于选择和预览DTO结构。
-->
<template>
  <el-drawer
    v-model="visible"
    title="DTOs 列表"
    size="50%"
    :close-on-click-modal="true"
    :append-to-body="false"
  >
    <template #header>
      <el-input
        v-model="keyword"
        placeholder="请输入关键词筛选"
        style="margin-bottom: 20px;"
        clearable
        @input="handleFilterChange"
      />
    </template>
    
    <div v-for="(dto, index) in filteredDtos" 
         :key="dto.id+':'+index" 
         class="dto-card">
      <el-card 
        :class="{ 'dto-card-selected': selectedDtoId === dto.id }"
        @click="selectDto(dto)"
        shadow="hover"
      >
        <template #header>
          <div class="clearfix">
            <span>{{ dto.name }}</span>
            <el-button style="float: right; padding: 3px 0" link @click.stop="selectDto(dto)">
              选择为DTO源
            </el-button>
          </div>
        </template>
        <el-table :data="formatDtoStructure(dto.structure)" border>
          <el-table-column label="字段名" prop="key" width="180" />
          <el-table-column label="描述" prop="description" />
          <el-table-column label="类型" prop="type" width="100" />
          <el-table-column label="验证" prop="validation" width="280" />
        </el-table>
      </el-card>
    </div>
    <!-- 懒加载占位元素 -->
    <div ref="lazyLoadTrigger" style="height: 20px;"></div>
  </el-drawer>
</template>

<script setup lang="ts">
/**
 * DTOsDrawer 组件
 * 负责DTO数据的筛选、分页、选择。
 */
import { ref, watch, onMounted, computed } from 'vue';
import type { DtoStructure } from '@/modules/admin/views/gridManagement/GridManagementService';

// 定义属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  dtos: {
    type: Array<DtoStructure>,
    default: () => []
  },
  selectedDtoId: {
    type: [Number, String],
    default: null
  },
  pageSize: {
    type: Number,
    default: 10
  },
  store: {
    type: Object,
    required: true
  }
});

const store = props.store;
// 定义事件
const emit = defineEmits([
  'update:modelValue',
  'select-dto',
]);

// 本地状态
const keyword = ref('');
const lazyLoadTrigger = ref<HTMLElement | null>(null);
const loadedCount = ref(props.pageSize);

// 计算属性：drawer是否可见
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

// 计算属性：筛选和分页后的DTO列表
const filteredDtos = computed(() => {
  let dtos = props.dtos || [];
  // 先按module过滤
  if (props.store.selectedModule) {
    // 兼容ref与普通值
    const selectedModule = props.store.selectedModule.value !== undefined ? props.store.selectedModule.value : props.store.selectedModule;
    dtos = dtos.filter(dto => dto.module === selectedModule);
  }
  let filtered = dtos;
  if (keyword.value) {
    const k = keyword.value.toLowerCase();
    filtered = dtos.filter(dto =>
      dto.name?.toLowerCase().includes(k) ||
      dto.description?.toLowerCase().includes(k)
    );
  }
  return filtered.slice(0, loadedCount.value);
});

// 格式化DTO结构为表格数据
function formatDtoStructure(structure: any) {
  return Object.entries(structure).map(([key, value]: [string, any]) => ({
    key,
    ...(value as any)
  }));
}

// 选择DTO
function selectDto(dto: any) {
  store.gridLayoutStore.structureData.dto = dto;
  emit('select-dto', dto);
}

// 处理过滤变化
function handleFilterChange() {
  loadedCount.value = props.pageSize; // 重置分页
}

// 监听筛选关键词变化
watch(() => keyword.value, (_newValue) => {
  handleFilterChange();
});

// 监听抽屉打开时，自动填充分组关键词
watch(() => visible.value, (newValue) => {
  if (newValue && props.store.selectedGroup && !keyword.value) {
    keyword.value = props.store.selectedGroup;
    handleFilterChange();
  }
});

// 懒加载
onMounted(() => {
  const observer = new IntersectionObserver((entries) => {
    if (entries[0].isIntersecting) {
      loadedCount.value += props.pageSize;
      props.store.loadMoreDtos();
    }
  });
  if (lazyLoadTrigger.value) {
    observer.observe(lazyLoadTrigger.value);
  }
});
</script>

<style scoped>
.dto-card {
  margin-bottom: 20px;
}

.dto-card-selected {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.2);
}

.clearfix {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
