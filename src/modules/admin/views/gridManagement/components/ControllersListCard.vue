<!--
  ControllersListCard.vue
  控制器列表卡片组件，显示controllers列表及操作
-->
<template>
  <el-card class="box-card">
    <template #header>
      <div class="card-header">
        <span>controllers列表</span>
        <el-button 
          link
          @click="isCollapsed = !isCollapsed"
          :icon="isCollapsed ? 'Expand' : 'Fold'"
        >
          {{ isCollapsed ? '展开' : '折叠' }}
        </el-button>
      </div>
    </template>
    <div v-if="!isCollapsed">
      <el-table :data="controllers" style="width: 100%" border>
        <el-table-column type="expand">
          <template #default="props">
            <p><strong>方法介绍:</strong> {{ props.row.method_description }}</p>
          </template>
        </el-table-column>
        <el-table-column label="ID" prop="id" width="80" />
        <el-table-column label="方法名" prop="method_name" width="200" />
        <el-table-column label="描述" prop="method_description" />
        <el-table-column label="dtos" prop="used_dto_ids" />
      </el-table>
    </div>
    <div v-else class="collapsed-card-icon" @click="isCollapsed = false">
      <el-icon><Setting /></el-icon>
      <span>控制器列表 ({{ controllers.length }})</span>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { PropType } from 'vue';
import { Setting } from '@element-plus/icons-vue';

// 定义组件属性
const props = defineProps({
  // 控制器列表
  controllers: {
    type: Array as PropType<any[]>,
    required: true
  },
  // 网格管理store
  store: {
    type: Object as PropType<any>,
    required: true
  }
});

// 定义事件
const emit = defineEmits(['controller-selected']);

// 折叠状态
const isCollapsed = ref(true);

/**
 * 监听selectedModule变化并更新控制器列表
 */
watch(() => props.store.selectedModule, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    // 调用store刷新当前模块控制器列表
    if (typeof props.store.refreshCurrentModule === 'function') {
      props.store.refreshCurrentModule();
    }
  }
});

/**
 * 选择控制器
 */
// function selectController(controller: any) {
//   emit('controller-selected', controller);
// }
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.collapsed-card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  cursor: pointer;
  color: #409eff;
  transition: all 0.3s ease;
}

.collapsed-card-icon:hover {
  background-color: #f5f7fa;
}

.collapsed-card-icon .el-icon {
  font-size: 24px;
  margin-right: 10px;
}
</style> 