/**
 * 文件：gridManagementStore.ts
 * 职责：管理网格数据的状态，提供响应式数据和业务逻辑方法。
 * 主要功能：网格数据的获取、增删改查、分页、状态管理。
 * 作者：张二浩
 * 创建时间：2025-04-15
 * 更新时间：2025-04-18
 * 网格管理状态存储，用于管理网格配置和布局的状态
 * 实现组件间数据共享，便于维护
 */

import { defineStore } from 'pinia';
import { computed, ref, toRaw, nextTick } from 'vue';
import { GridManagementService } from './GridManagementService';
import { createGridLayoutStore } from '@/components/page/gridLayoutStore';
import GridItemAdapter from '@/modules/admin/adapters/GridItemAdapter';
import CacheService from '@/modules/admin/service/CacheService';
import type { AdminGridInfoDTO, AdminUIConfigDTO, LayoutGridItem, PageData } from '@/modules/admin/types';
import type { ServiceConfig } from '@/types/shared';
import { ElMessage } from 'element-plus';
import type { DtoStructure } from './GridManagementService';
import localforage from 'localforage';
import { cloneDeep } from 'lodash';
import { createGridComponentStore } from '@/components/page/gridComponentStore';

// 缓存服务实例
const cacheService = new CacheService('gridManagement');

// ===================== 数据结构定义 =====================
/**
 * 元数据类型定义
 */
interface MetaData {
  modules: string[];
  groupNames: { page_name: string }[];
  dtos: DtoStructure[];
  apis: any[];
  controllers: any[];
}

// 默认Grid服务配置
const defaultServiceConfig: ServiceConfig = {
  baseUrl: '/api/grid',
  apiPrefix: '/api',
  addTitle: '新增',
  viewTitle: '查看',
  editTitle: '编辑',
  customActions: {},
  gridOptions: {
    column: 12,
    cellHeight: 50,
    margin: 10
  },
  messages: {
    addSuccess: '新增成功',
    deleteSuccess: '删除成功',
    deleteConfirm: '确定删除吗？',
    editSuccess: '更新成功'
  },
  resizable: true,
  draggable: true,
  headers: {},
  timeout: 30000,
  withCredentials: true,
  urls: {
    list: '/list',
    detail: '/detail',
    create: '/create',
    update: '/update',
    delete: '/delete'
  },
  idField: 'id',
  immediate: true,
  defaultParams: {
    page: 1,
    pageSize: 10
  },
  pageInfoMap: {
    total: 'total',
    current: 'current',
    page: 'page',
    pageSize: 'pageSize',
    pageSizeList: 'pageSizeList'
  },
  formatRequest: (params) => params,
  formatResponse: (response) => ({
    data: response.data || [],
    total: response.total || 0
  }),
  formatDetailResponse: (response) => response.data,
  formatFormData: (data) => data,
  sortField: 'createTime',
  sortOrder: 'desc',
  defaultFormData: {}
};

// 定义 store
export const useGridManagementStore = defineStore('gridManagement', () => {
  // 实例化服务层
  const service = new GridManagementService();
  
  // 实例化 gridLayoutStore
  const gridLayoutStore = createGridLayoutStore(defaultServiceConfig);
  
  // 私有状态：按照优化方案分类数据
  // 2. 网格项数据 - 网格布局中的组件数据
  const _gridItems = ref<AdminGridInfoDTO[]>([]);
  
  // 3. 元数据 - 模块、分组、DTO等元数据
  const _metaData = ref<MetaData>({
    modules: ["admin", "system", "merchant", "user", "order", "apidoc", "payment", "ui_config", "delivery"],
    groupNames: [],
    dtos: [],
    apis: [],
    controllers: []
  });
  
  const defaultGridItem = ref<AdminGridInfoDTO>({
    id: -1,
    name: '',
    api: '',
    position: {
      x: 1,
      y: 1,
      w: 4,
      h: 4,
      i: -1
    },
    content: {
      type: 'custom',
      title: '',
      icon: '',
      showTitle: true,
      refreshable: true,
      configurable: true,
      closable: true,
      editable: true,
      themeMode: 'light',
      customStyle: { opacity: "1" },
      config: {
        columns: [],
        pagination: {
          pageSize: 10,
          pageSizes: [10, 20, 50, 100]
        },
        showPagination: true
      }
    },
    remark: '',
    status: 1,
    created_at: '',
    updated_at: ''
  });
  
  // 过滤和分页
  const filterKeyword = ref('');
  const loadedCount = ref(10);
  const currentGridItem = ref<AdminGridInfoDTO | null>(null);
  const currentId = ref(0); //默认为0，-1为新增，打开时为对应项的id
  //const selectedDto = ref<DTOInfoDTO | null>(null);
  const dtoSource = ref<DtoStructure | null>(null);
  const dtoSourceJson = ref('');
  const uiConfigs = ref<AdminUIConfigDTO[]>([]);
  const selectedUiConfig = ref<AdminUIConfigDTO | null>(null); // ？可以去掉？
  
  // UI状态
  const showPage = ref(false);
  const showGridDialog = ref(false); // 修改为一致的名称
  const processedDtoFields = ref<any[]>([]);
  
  // 显示的状态
  const selectedModule = ref('');
  const selectedGroup = ref('');
  const pageTitle = ref('');
  const selectedIcon = ref('el-icon-s-grid');
  const frontendPath = ref('');
  const pageConfig = ref<any>(null); // ？可以去掉？
  
  // 计算属性
  // 移除未使用的计算属性
  // const visibleDtosComputed = computed(() => {
  //   ...
  // });
  
  /**
   * 同步数据到GridLayoutStore
   */
  function syncDataToLayoutStore() {
    if (!pageConfig.value) return;
    
    // 转换网格项为gridStack可识别的格式
    const nodes = GridItemAdapter.toGridStackNodes(_gridItems.value);
    
    // 更新gridLayoutStore的gridItems
    gridLayoutStore.gridItems.value = nodes;
    
    // 更新服务配置中的baseUrl（如果存在）
    if (gridLayoutStore.serviceConfig && gridLayoutStore.serviceConfig.value) {
      gridLayoutStore.serviceConfig.value.baseUrl = frontendPath.value || '/api/grid';
    }
    
    // 不调用有可能不存在的初始化方法
  }
  
  /**
   * 处理DTO结构数据
   * @param dto DTO结构数据
   */
  function processDtoStructure(dto: any) {
    if (!dto) {
      ElMessage.warning('DTO数据不能为空');
      return;
    }
    
    // 将传入的DTO设置为当前DTO源
    dtoSource.value = dto;
    
    // 将DTO字符串化并设置到dtoSourceJson
    dtoSourceJson.value = JSON.stringify(dto, null, 2);
    
    // 生成配置
    generateConfigFromSource();
  }
  
  /**
   * 切换分组
   */
  async function handleGroupChange(group?: string) {
    selectedGroup.value = group || '';
    
    try {
      // 获取分组对应的UI配置
      const uiConfigsResponse = await service.fetchUiConfigs(false, {
        modules: selectedModule.value,
        group: selectedGroup.value,
        page: 1,
        pageSize: 2000
      });
      
      if (uiConfigsResponse) {
        uiConfigs.value = uiConfigsResponse.list || [];
      }
      
      // 获取分组对应的DTO列表
      const dtosResponse = await service.fetchDtos(false, {
        module: selectedModule.value,
        group: selectedGroup.value
      });
      
      if (dtosResponse) {
        _metaData.value.dtos = dtosResponse.list || [];
      }
    } catch (error) {
      console.error('切换分组失败', error);
      ElMessage.error('切换分组失败，请稍后重试');
    }
  }
  
  /**
   * 刷新当前模块数据
   */
  async function refreshCurrentModule() {
    try {
      // 清除缓存
      await cacheService.clear();
      
      // 重新获取数据
      await handleModuleChange();
      
      ElMessage.success('刷新成功');
    } catch (error) {
      console.error('刷新当前模块失败', error);
      ElMessage.error('刷新当前模块失败，请稍后重试');
    }
  }
  
  /**
   * 刷新所有模块数据
   */
  async function refreshAllModules() {
    try {
      // 清除缓存
      await cacheService.clear();
      
      // 使用service层的方法刷新所有模块数据，强制刷新
      await service.fetchApiDoc(true);
      await service.fetchGroupNames(true);
      await service.fetchDtos(true);
      await service.fetchControllers(true);
      await service.fetchUiConfigs(true);
      
      // 重新获取数据
      await handleModuleChange();
      
      ElMessage.success('刷新所有模块成功');
    } catch (error) {
      console.error('刷新所有模块失败', error);
      ElMessage.error('刷新所有模块失败，请稍后重试');
    }
  }
  
  /**
   * 初始化或刷新模块数据
   */
  async function handleModuleChange() {
    console.log('handleModuleChange', selectedModule.value);
    selectedGroup.value = '';
    try {
      // 获取API文档
      const apiDocResponse = await service.fetchApiDoc();
      if (apiDocResponse) {
        _metaData.value.apis = service.filterApiDoc(apiDocResponse);
      }
      
      // 获取分组名称
      const groupResponse = await service.fetchGroupNames();
      if (groupResponse) {
        _metaData.value.groupNames = groupResponse.list || [];
      }
      
      // 获取DTO列表
      const dtosResponse = await service.fetchDtos();
      if (dtosResponse) {
        _metaData.value.dtos = dtosResponse.list || [];
      }
      
      // 获取控制器列表
      const controllersResponse = await service.fetchControllers();
      if (controllersResponse) {
        _metaData.value.controllers = controllersResponse.list || [];
      }
      
      // 获取UI配置列表
      const uiConfigsResponse = await service.fetchUiConfigs();
      if (uiConfigsResponse) {
        uiConfigs.value = uiConfigsResponse.list || [];
      }
    } catch (error) {
      console.error('初始化模块数据失败', error);
      ElMessage.error('初始化模块数据失败，请稍后重试');
    }
  }
  
  
  /**
   * 更新DTO源对象
   */
  // function updateDtoSourceJson(val: Record<string, any>) {
  //   dtoSource.value = val;
  //   try {
  //     dtoSourceJson.value = JSON.stringify(val, null, 2);
  //   } catch (error) {
  //     dtoSourceJson.value = '{}';
  //   }
  // }
  
  /**
   * 设置DTO源
   * 从gridLayoutStore的structureData中获取DTO数据,并解析,供组件配置选择
   * 从dto文件，提取选择用的数组，供组件选择
   */
  function setDtoSource() {
    //dtoSource.value = dto;
    
    // 解析DTO结构
    if (gridLayoutStore.structureData.value && gridLayoutStore.structureData.value.dto) {
      processedDtoFields.value = service.processDtoStructure(gridLayoutStore.structureData.value.dto.structure as unknown as Record<string, unknown>);
      
      // 如果DTO有结构，则直接以JSON格式显示
      // dtoSourceJson.value = JSON.stringify(gridLayoutStore.structureData.value.dto, null, 2);
    } else {
      // dtoSourceJson.value = '';
    }
  }
  
  /**
   * 从DTO源生成配置
   */
  // function handleGenerateFromDto() {
  //   if (!dtoSource.value) {
  //     ElMessage.warning('请先选择一个DTO');
  //     return;
  //   }
    
  //   // 使用service层的方法生成配置
  //   generateConfigFromSource();
  // }
  
  /**
   * 批量更新网格项位置
   * updateGridPositions
   */
  async function updateGridPositions(items: Array<LayoutGridItem>) {
    return service.updateGridPositions(items);
  }

  
  /**
   * 生成配置
   */
  async function generateConfigFromSource() {
    //console.log('[GridManagementStore] generateConfigFromSource', gridLayoutStore.structureData.value);
    if (!gridLayoutStore.structureData.value?.dto) {
      ElMessage.warning('请先选择一个DTO');
      return;
    }
    
    try {
      // 使用service的generateConfig方法生成配置
      const generatedConfig = await service.generateConfig(gridLayoutStore.structureData.value.dto);
      
      // 更新本地状态
      pageConfig.value = generatedConfig;
      
      // 同步数据到GridLayoutStore
      syncDataToLayoutStore();
      showPage.value = true;
      
      ElMessage.success('配置已生成');
    } catch (error) {
      console.error('生成配置失败', error);
      ElMessage.error('生成配置失败，请稍后重试');
    }
  }
  
  /**
   * 重新生成配置
   */
  async function regenerateConfig() {
    if (!gridLayoutStore.structureData.value?.dto) {
      ElMessage.warning('请先选择一个DTO');
      return;
    }
    
    try {
      // 重新生成配置
      if(!gridLayoutStore.structureData.value) {
        ElMessage.warning('请先生成配置');
        return;
      }
      const generatedConfig = await service.generateConfig(gridLayoutStore.structureData.value.dto);
      
      // 更新本地状态
      pageConfig.value = generatedConfig;
      
      // 同步数据到GridLayoutStore
      syncDataToLayoutStore();
      
      ElMessage.success('配置已重新生成');
    } catch (error) {
      console.error('重新生成配置失败', error);
      ElMessage.error('重新生成配置失败，请稍后重试');
    }
  }
  
  /**
   * 保存页面配置信息，包括新建和更新
   */
  async function saveConfig() {
    
    try {
      // 构建保存需要的配置数据
      if(!gridLayoutStore.structureData.value) {
        ElMessage.warning('请先生成配置');
        return;
      }
      console.log('saveConfig', gridLayoutStore.structureData.value);
      // 随机得到一个版本号
      const timestamp = new Date().toISOString().replace(/[-:]/g, '').split('.')[0].replace('T', '');
      const randomStr = Math.random().toString(36).substring(2, 8);
      const versionHash = `${timestamp}-0`;
      const version = randomStr;
      // 获取当前选中的配置
      const configData = {
        icon: gridLayoutStore.structureData.value.icon,
        dto: JSON.stringify(gridLayoutStore.structureData.value.dto),
        module: gridLayoutStore.structureData.value.module,
        group: gridLayoutStore.structureData.value.group,
        title: gridLayoutStore.structureData.value.title,
        frontend_path: gridLayoutStore.structureData.value.frontend_path,
        config_type: 'page',
        config_key: `${gridLayoutStore.structureData.value.module}_${gridLayoutStore.structureData.value.group}_${gridLayoutStore.structureData.value.title}`,
        config_content: JSON.stringify(gridLayoutStore.structureData.value.config_content),
        remark: '',
        status: 1,
        version: version,
        version_hash: versionHash,
        uiConfigId: gridLayoutStore.structureData.value.id as number || 0, // ？可以去掉？
        draggable: true,
        resizable: true
      };
      console.log('saveConfig', configData);
      // 使用service层保存配置
      const response = await service.saveUiConfig(configData);
      
      // 保存后 刷新配置列表 并选中刚保存的配置
      if (response) {
        const uiConfigsResponse = await service.fetchUiConfigs(true);
        if (uiConfigsResponse) {
          uiConfigs.value = uiConfigsResponse.list || [];
          // 找到刚保存的配置并选中
          const savedConfig = uiConfigs.value.find(item => 
            item.module === gridLayoutStore.structureData.value?.module && 
            item.group === gridLayoutStore.structureData.value?.group && 
            item.title === gridLayoutStore.structureData.value?.title
          );
          
          if (savedConfig) {
            selectedUiConfig.value = savedConfig;
          }
        }
        
        ElMessage.success('配置已保存');
      }
    } catch (error) {
      console.error('保存配置失败', error);
      ElMessage.error('保存配置失败，请稍后重试');
    }
  }
  
  /**
   * 从ID加载配置
   */
  async function loadConfigById(config: AdminUIConfigDTO) {
    if (!config || !config.id) {
      ElMessage.error('配置ID无效');
      return;
    }
    
    try {
      //console.log('[GridManagementStore] loadConfigById', gridLayoutStore.structureData.value);
      selectedUiConfig.value = config;
      //const dto = typeof config.dto === 'string' ? JSON.parse(config.dto || '{}') : config.dto;
      // selectedDto.value = dto;
      selectedGroup.value = config.group;
      selectedModule.value = config.module;
      //console.log('[GridManagementStore] loadConfigById', selectedGroup.value, selectedModule.value);
      
      // 创建componentStore实例，确保在setStructureDataFromBackend调用时能关联到gridLayoutStore
      console.log('[GridManagementStore] 创建componentStore实例');
      // 创建符合UiPageConfig类型的配置对象
      const uiPageConfig = {
        frontendPath: config.frontend_path,
        icon: config.icon,
        moduleName: config.module,
        serviceConfig: gridLayoutStore.getServiceConfig()
      };
      const componentStore = createGridComponentStore(gridLayoutStore.getService(), uiPageConfig);
      
      // 将创建的componentStore实例传递给setStructureDataFromBackend方法
      await gridLayoutStore.setStructureDataFromBackend(config, componentStore);
      
      if(gridLayoutStore.structureData.value&&gridLayoutStore.structureData.value.dto) {
        processedDtoFields.value = service.processDtoStructure(gridLayoutStore.structureData.value.dto?.structure as unknown as Record<string, unknown>);
      }
      //console.log('[GridManagementStore] loadConfigById', gridLayoutStore.structureData.value, showPage.value);
      //setDtoSource();
      showPage.value = true;
      // 使用service层加载配置
      //const { pageConfig, gridItems } = await service.loadUiConfigById(config);
      //console.log('[GridManagementStore] loadConfigById', { pageConfig, gridItems });
      //if (pageConfig) {
        // 更新本地状态
        // pageConfig.value = pageConfig;
        // pageTitle.value = config.title;
        // frontendPath.value = config.frontend_path;
        // selectedIcon.value = config.icon;
        // selectedModule.value = config.module;
        // selectedGroup.value = config.group || '';
        
        // 准备网格项数据
        // if (configData.gridItems && configData.gridItems.length > 0) {
        //   _gridItems.value = configData.gridItems;
        // } else if (configData.pageConfig && Array.isArray(configData.pageConfig.layout)) {
        //   // 如果没有直接的gridItems但有layout配置，则从layout生成网格项
        //   const gridItemsData = configData.pageConfig.layout.map((item: any) => {
        //     return GridItemAdapter.fromConfigItem(item);
        //   });
        //   _gridItems.value = gridItemsData;
        // } else {
        //   _gridItems.value = [];
        // }
        
        // 同步数据到GridLayoutStore
        //syncDataToLayoutStore();
        //showPage.value = true;
        
        ElMessage.success('配置已加载');
      //} else {
        //ElMessage.warning('配置内容为空');
      //}
    } catch (error) {
      console.error('加载配置失败', error);
      ElMessage.error('加载配置失败，请稍后重试');
    }
  }
  
  /**
   * 跳转到页面设置
   * @param config 配置信息
   */
  function gotoPageSetting(config: AdminUIConfigDTO) {
    loadConfigById(config);
  }

  /**
   * 处理配置变更事件
   */
  async function handleConfigChange(id: number) {
    try {
      // 查找已加载的配置
      //console.log('[GridManagementStore] handleConfigChange', id, uiConfigs.value);
      const configData = uiConfigs.value.find(c => c.id === id);
      
      if (configData) {
        await loadConfigById(configData);
        ElMessage.success('配置已更新');
      } else {
        ElMessage.warning('未找到对应配置');
      }
    } catch (error) {
      console.error('处理配置变更失败', error);
      ElMessage.error('处理配置变更失败');
    }
  }
  
  /**
   * 删除网格项与UI配置的关联关系，并在视图中移除网格项（API交互由service负责）
   * 实际删除行为为调用service.deleteUIConfigRelation，成功后刷新本地网格项列表
   * @param id 网格项唯一标识
   */
  async function removeGridItem(id: number) {
    //console.log('[GridManagementStore] removeGridItem', id);
    // if (!selectedUiConfig.value || !selectedUiConfig.value.id) {
    //   ElMessage.warning('当前UI配置无效，无法删除网格项');
    //   return;
    // }

    try {
      // 调用service方法解除关联
      if(id>0 && gridLayoutStore.structureData.value?.id) {
        await service.deleteUIConfigRelation(id, gridLayoutStore.structureData.value.id);
      }
      // 直接从gridLayoutStore.structureData.value.grid_items中移除
      if (gridLayoutStore.structureData.value?.grid_items) {
        gridLayoutStore.structureData.value.grid_items = gridLayoutStore.structureData.value.grid_items.filter((item: AdminGridInfoDTO) => item.id !== id);
      }

      // 成功后刷新网格项列表
      // 清除缓存
      await nextTick();
      await saveLastConfig();
      ElMessage.success('网格项已移除关联并刷新列表');
    } catch (error) {
      console.error('删除网格项关联失败', error);
      ElMessage.error('删除网格项失败，请稍后重试');
    }
  }
  
  /**
   * 更新网格项
   */
  async function updateGridItem(id: number, updates: Partial<AdminGridInfoDTO>) {
    try {
      // 查找要更新的网格项
      const index = _gridItems.value.findIndex(item => item.id === id);
      
      if (index !== -1) {
        // 更新本地状态
        _gridItems.value[index] = { ..._gridItems.value[index], ...updates };
        
        // 同步数据到GridLayoutStore
        syncDataToLayoutStore();
        
        ElMessage.success('网格项已更新');
      } else {
        ElMessage.warning('网格项不存在');
      }
    } catch (error) {
      console.error('更新网格项失败', error);
      ElMessage.error('更新网格项失败，请稍后重试');
    }
  }
  
  /**
   * 批量添加网格项
   */
  async function addGridItems(gridItems: AdminGridInfoDTO[]) {
    //console.log('[GridManagementStore] addGridItem', gridItems, gridLayoutStore.structureData.value);
    gridItems.forEach(item => {
      item.uiConfigId = gridLayoutStore.structureData.value?.id;
      item.ui_config_id = gridLayoutStore.structureData.value?.id;
      const position = { i:0, x: 0, y: 0, w: 6, h: 4 };
      item.position = position;
      item.step = [0];
    });
    try {
      // 生成ID
      gridLayoutStore.structureData.value?.grid_items.push(...gridItems);
      const ids = gridLayoutStore.structureData.value?.grid_items.map(item => Number(item.id)) || [];
      await service.setBatchUIConfigGridRelation({ uiConfigId: gridLayoutStore.structureData.value?.id as number, gridInfoIds: ids });

      const rawConfig = JSON.parse(JSON.stringify(toRaw(gridLayoutStore.structureData.value)));
      await localforage.setItem('grid_management_last_config', rawConfig);
      if (gridLayoutStore.structureData.value) { // 避免 undefined 赋值
        //gridLayoutStore.structureData.value = { ...gridLayoutStore.structureData.value }; // 触发响应式更新
        gridLayoutStore.structureData.value.grid_items = [...gridLayoutStore.structureData.value.grid_items];
      }
      ElMessage.success('网格项已添加');
    } catch (error) {
      console.error('添加网格项失败', error);
      ElMessage.error('添加网格项失败，请稍后重试');
    }
  }

  /**
   * 添加单个网格项
   */
  async function addGridItem(gridItem: AdminGridInfoDTO) {
    console.log('[GridManagementStore] addGridItem', gridItem, gridLayoutStore.structureData.value);

    try {
      // 生成ID
      //检查id是否合法
      if (!gridItem.id || gridItem.id <= 0) {
        // 保存原始ID，用于后续查找匹配项
        const originalId = gridItem.id;
        console.log('[GridManagementStore] 原始ID:', originalId);
        
        // 调用接口新建 获取id
        gridItem.uiConfigId = gridLayoutStore.structureData.value?.id;
        gridItem.ui_config_id = gridLayoutStore.structureData.value?.id;
        const position = { i:0, x: 0, y: 0, w: 6, h: 4 };
        const response: any = await service.addGridItem({ ...gridItem, position });
        console.log('[GridManagementStore] addGridItem 服务器响应:', response);
        
        // 服务器返回新ID后，更新gridItem的ID
        if (response && response.id) {
          // 找到gridLayoutStore.structureData.value.grid_items中与原始ID匹配的项
          const index = gridLayoutStore.structureData.value?.grid_items.findIndex(item => item.id === originalId) ?? -1;
          console.log('[GridManagementStore] 查找本地网格项, 原始ID:', originalId, '索引:', index);
          
          if (index !== -1 && gridLayoutStore.structureData.value?.grid_items) {
            // 更新网格项ID和position.i
            gridLayoutStore.structureData.value.grid_items[index].id = response.id;
            gridLayoutStore.structureData.value.grid_items[index].position.i = response.id;
            console.log('[GridManagementStore] 已更新网格项ID:', response.id);
            
            // 重新赋值griditem，以便让视图更新
            gridLayoutStore.structureData.value.grid_items = [...gridLayoutStore.structureData.value.grid_items];
            // 更新gridItem的ID，以便后续使用
            gridItem.id = response.id;
          } else {
            if (gridLayoutStore.structureData.value) {
              gridItem.id = response.id;
              gridLayoutStore.structureData.value.grid_items.push(gridItem);
              // 重新赋值griditem，以便让视图更新
              gridLayoutStore.structureData.value.grid_items = [...gridLayoutStore.structureData.value.grid_items];
            }
          }
        } 
      } else {
        gridItem.uiConfigId = gridLayoutStore.structureData.value?.id;
        gridItem.ui_config_id = gridLayoutStore.structureData.value?.id;
        const position = { i:gridItem.id, x: 0, y: 0, w: 6, h: 4 };
        gridItem.step = [0]; 
        gridItem.position = position;
        const response: any = await service.updateGridItem(gridItem.id, gridItem);
        console.log('updateGridItem response', response);
        const index = gridLayoutStore.structureData.value?.grid_items.findIndex(item => item.id === gridItem.id) ?? -1;
        if (index !== -1 && gridLayoutStore.structureData.value?.grid_items) {
          //gridLayoutStore.structureData.value.grid_items[index].id = gridItem.id;
          gridLayoutStore.structureData.value.grid_items[index].position = gridItem.position;
          // 重新赋值griditem，以便让视图更新
          gridLayoutStore.structureData.value.grid_items = [...gridLayoutStore.structureData.value.grid_items];
          // 触发响应式更新
          //currentId.value = 0;
        } else {
          if (gridLayoutStore.structureData.value) {
            gridLayoutStore.structureData.value.grid_items.push(gridItem);
            // 重新赋值griditem，以便让视图更新
            gridLayoutStore.structureData.value.grid_items = [...gridLayoutStore.structureData.value.grid_items];
          }
        }

      }
      // 更新缓存
      await saveLastConfig();
      //添加到结构数据,找到id不合法的项，将id替换过去
      //gridLayoutStore.structureData.value?.grid_items.push(gridItem);
      // const ids = gridLayoutStore.structureData.value?.grid_items.map(item => Number(item.id)) || [];
      // await service.setBatchUIConfigGridRelation({ uiConfigId: gridLayoutStore.structureData.value?.id as number, gridInfoIds: ids });
      // const rawConfig = JSON.parse(JSON.stringify(toRaw(gridLayoutStore.structureData.value)));
      // await localforage.setItem('grid_management_last_config', rawConfig);
      
      if (gridLayoutStore.structureData.value) { // 避免 undefined 赋值
        gridLayoutStore.structureData.value = { ...gridLayoutStore.structureData.value }; // 触发响应式更新
        
        // 二次检查确保没有负ID的项
        const hasNegativeId = gridLayoutStore.structureData.value.grid_items.some(
          (item: AdminGridInfoDTO) => item.id < 0
        );
        
        if (hasNegativeId) {
          console.warn('[GridManagementStore] 警告: 保存前仍有负ID的网格项!');
        }
      }
      
      ElMessage.success('网格项已添加');
      console.log('[GridManagementStore] 网格项添加完成，当前网格项:', gridLayoutStore.structureData.value?.grid_items);
      return gridItem; // 返回更新后的网格项
    } catch (error) {
      console.error('添加网格项失败', error);
      ElMessage.error('添加网格项失败，请稍后重试');
      throw error; // 抛出错误以便调用者处理
    } 
  }
  
  /**
   * 从GridStackNode更新网格项
   */
  // async function updateGridItemsFromNodes(nodes: any[]) {
  //   try {
  //     // 遍历节点更新网格项位置和大小
  //     console.log('[GridManagementStore] updateGridItemsFromNodes', nodes);
  //     nodes.forEach(node => {
  //       const id = parseInt(node.id);
  //       const index = _gridItems.value.findIndex(item => item.id === id);
        
  //       if (index !== -1) {
  //         // 更新位置和大小
  //         _gridItems.value[index].position.x = node.x;
  //         _gridItems.value[index].position.y = node.y;
  //         _gridItems.value[index].position.w = node.w;
  //         _gridItems.value[index].position.h = node.h;
  //       }
  //     });
      
  //     // 更新页面配置
  //     if (pageConfig.value && pageConfig.value.layout) {
  //       pageConfig.value.layout = _gridItems.value.map(item => {
  //         return {
  //           id: item.id,
  //           x: item.position.x,
  //           y: item.position.y,
  //           w: item.position.w,
  //           h: item.position.h,
  //           component_type: item.content.component_type,
  //           component_props: item.content.component_props,
  //           component_events: item.content.component_events,
  //           component_slots: item.content.component_slots,
  //           service_config: item.service_config
  //         };
  //       });
  //     }
  //   } catch (error) {
  //     console.error('更新网格项位置失败', error);
  //     ElMessage.error('更新网格项位置失败，请稍后重试');
  //   }
  // }
  
  /**
   * 更新配置图标
   */
  async function updateConfigIcon(config: AdminUIConfigDTO) {
    try {
      // 更新图标API调用
      const response = await service.updateConfigIcon(config);
      
      // 更新本地状态
      if (response) {
        ElMessage.success('图标已更新');
        // 刷新UI配置列表
        const uiConfigsResponse = await service.fetchUiConfigs(true);
        if (uiConfigsResponse) {
          uiConfigs.value = uiConfigsResponse.list || [];
        }
      }
    } catch (error) {
      console.error('更新配置图标失败', error);
      ElMessage.error('更新配置图标失败，请稍后重试');
    }
  }
  
  /**
   * 更新配置状态
   */
  async function updateConfigStatus(config: AdminUIConfigDTO, status: number) {
    try {
      // 更新状态API调用
      const response = await service.updateConfigStatus(config, status);
      
      // 更新本地状态
      if (response) {
        ElMessage.success('状态已更新');
        // 刷新UI配置列表
        const uiConfigsResponse = await service.fetchUiConfigs(true);
        if (uiConfigsResponse) {
          uiConfigs.value = uiConfigsResponse.list || [];
        }
      }
    } catch (error) {
      console.error('更新配置状态失败', error);
      ElMessage.error('更新配置状态失败，请稍后重试');
    }
  }
  
  /**
   * 删除UI配置
   * @param config 需要删除的UI配置对象
   */
  async function deleteConfig(config: AdminUIConfigDTO) {
    try {
      // 假设service有deleteUiConfig方法，传入id
      await service.deleteUiConfig(config.id);
      await refreshCurrentModule(); // 删除后刷新配置列表
      ElMessage.success('删除成功');
    } catch (error: any) {
      ElMessage.error('删除失败: ' + (error?.message || '未知错误'));
    }
  }
  
  /**
   * 打开网格项对话框
   */
  function openGridItemDialog(id?: number) {
    //console.log('[GridManagementStore] openGridItemDialog', id, gridLayoutStore.structureData.value);
    if (!gridLayoutStore.structureData.value) {
      return;
    }
    if(id && id > 0) {
      currentId.value = id;
    } else {
      if(id && id < 0) {
        if(gridLayoutStore.structureData.value.grid_items.length > 0) {
          //查找id为-1的网格项是否存在，如果存在不在继续增加，而是打开现有的-1的项
          const existingItem = gridLayoutStore.structureData.value.grid_items.find(item => item.id === -1);
          if (existingItem) {
            //currentId.value = -1;
            //console.log('[GridManagementStore] openGridItemDialog', id, currentId.value, gridLayoutStore.structureData.value.grid_items);
            //showGridDialog.value = true;
          } else {
            gridLayoutStore.structureData.value.grid_items.push(cloneDeep(defaultGridItem.value));
          }
        }
      }
      currentId.value = -1;
    }
    //console.log('[GridManagementStore] openGridItemDialog', id, currentId.value, gridLayoutStore.structureData.value.grid_items);
    // 打开编辑对话框
    if(currentId.value != 0) {
      showGridDialog.value = true;
    }
  }
  
async function saveLastConfig() {
  if (gridLayoutStore.structureData.value) {
    // 使用 toRaw 和 JSON 序列化，确保可存储
    const rawConfig = JSON.parse(JSON.stringify(toRaw(gridLayoutStore.structureData.value)));
    await localforage.setItem('grid_management_last_config', rawConfig);
  }
}

  /**
   * 保存网格项
   */
  async function saveGridItem(gridItem: AdminGridInfoDTO) {
    try {
      if (!gridItem.name) {
        ElMessage.warning('请输入组件名称');
        return;
      }
      await service.updateGridItem(gridItem.id, gridItem);
      // 如果是更新
      if (_gridItems.value.some(item => item.id === gridItem.id)) {
        const index = _gridItems.value.findIndex(item => item.id === gridItem.id);
        _gridItems.value[index] = { ...gridItem };
      } else {
        // 如果是新增
        _gridItems.value.push(gridItem);
      }
      
      // 同步数据到GridLayoutStore
      syncDataToLayoutStore();
      
      // 关闭对话框
      showGridDialog.value = false;
      
      ElMessage.success('网格项已保存');
    } catch (error) {
      console.error('保存网格项失败', error);
      ElMessage.error('保存网格项失败，请稍后重试');
    }
  }
  
   /**
   * 设置baseUrl
   */
  function setBaseUrl(path: string) {
    //console.log('[GridManagementStore] setBaseUrl', path);
    // 如果path以/api开头，就去掉它
    if (path.startsWith('/api')) {
      path = path.substring(5);
    }

    // 更新serviceConfig
    if (gridLayoutStore && gridLayoutStore.structureData.value && gridLayoutStore.structureData.value.config_content.serviceConfig.baseUrl) {
      //console.log('[GridManagementStore] setBaseUrl', path, gridLayoutStore.structureData.value.config_content.serviceConfig);
      gridLayoutStore.structureData.value.config_content.serviceConfig.baseUrl = path;
      //console.log('[GridManagementStore] setBaseUrl', path, gridLayoutStore.structureData.value?.config_content.serviceConfig);
    }
  }
  
  /**
   * 刷新BasePage
   */
  function refreshBasePage() {
    // 刷新页面数据，从GridLayoutStore获取最新状态
    syncDataToLayoutStore();
  }
  
  /**
   * 刷新网格项列表
   */
  async function refreshGridItems() {
    try {
      const cachedConfig = await localforage.getItem('grid_management_last_config');
        console.log('cachedConfig', cachedConfig);
    
      // 检查是否存在有效的缓存配置
      if (cachedConfig) {
        // 类型校验，确保为对象且包含 AdminUIConfigDTO 的所有关键字段
        loadConfigById(cachedConfig as AdminUIConfigDTO);
      }
      ElMessage.success('网格项列表已刷新');
    } catch (error) {
      console.error('刷新网格项列表失败', error);
      ElMessage.error('刷新网格项列表失败，请稍后重试');
    }
  }
  
  /**
   * 更新当前网格项内容
   * @param content 新内容对象
   */
  function updateCurrentGridItemContent(content: any) {
    if (currentGridItem.value) {
      currentGridItem.value.content = content;
    }
  }
  
  // UI状态变量
  const isApiListCollapsed = ref(false);
  const isControllersListCollapsed = ref(false);
  const isConfigListCollapsed = ref(false);
  const showDtosDrawer = ref(false);
  // 标记是否正在卸载配置状态，防止卸载后立即重新加载
  const isUnloadingConfig = ref(false);
  
  /**
   * 卸载配置，重置状态
   * 将清除当前加载的配置，并重置localforage中的grid_management_last_config
   */
  async function unloadConfig() {
    //console.log('[GridManagementStore] unloadConfig');
    try {
      // 设置卸载状态标记
      isUnloadingConfig.value = true;
      //console.log('[GridManagementStore] unloadConfig', isUnloadingConfig.value);
      // 清除structureData
      showPage.value = false;
      if (gridLayoutStore.structureData && gridLayoutStore.structureData.value) {
        // 不能直接设置为null，使用空对象替代
        gridLayoutStore.structureData.value = undefined;
      }
      
      // 清除localforage中的grid_management_last_config
      await localforage.removeItem('grid_management_last_config');
      
      // 重置状态
      showPage.value = false;
      pageConfig.value = null;
      pageTitle.value = '';
      frontendPath.value = '';
      selectedIcon.value = 'Menu';
      _gridItems.value = [];
      
      // 刷新网格项
      refreshGridItems();
      
      ElMessage.success('配置已卸载');
      
      // 设置一个延时，一段时间后重置标志
      setTimeout(() => {
        isUnloadingConfig.value = false;
      }, 1000);
    } catch (error) {
      isUnloadingConfig.value = false; // 确保出错时也会重置标志
      console.error('卸载配置失败', error);
      ElMessage.error('卸载配置失败，请稍后重试');
    }
  }
  
  /**
   * 创建新配置
   * 使用默认模板创建一个新的页面配置
   */
  async function createConfig() {
    if (!pageTitle.value) {
      ElMessage.warning('请输入页面标题');
      return;
    }
    
    try {
      // 生成一个随机版本号
      const timestamp = new Date().toISOString().replace(/[-:]/g, '').split('.')[0].replace('T', '');
      const randomStr = Math.random().toString(36).substring(2, 8);
      const versionHash = `${timestamp}-0`;
      const version = randomStr;
      
      // 创建基本配置
      const newConfig = {
        id: 0, // 新建时ID为0
        frontend_path: frontendPath.value || pageTitle.value.toLowerCase().replace(/\s+/g, '_'),
        version_hash: versionHash,
        config_type: "page",
        config_key: `${selectedModule.value || 'admin'}:page:${version}`,
        config_content: JSON.stringify({
          frontendPath: '',
          icon: '',
          moduleName: '',
          serviceConfig: {
            baseUrl: `v1/${selectedModule.value || 'admin'}/secured/${frontendPath.value || pageTitle.value.toLowerCase().replace(/\s+/g, '_')}`,
            addTitle: `新增${pageTitle.value}`,
            editTitle: `编辑${pageTitle.value}信息`,
            messages: {
              addSuccess: `${pageTitle.value}创建成功`,
              updateSuccess: `${pageTitle.value}更新成功`,
              deleteConfirm: `确定要删除该${pageTitle.value}吗？`,
              deleteSuccess: `${pageTitle.value}删除成功`
            },
            gridOptions: {
              column: 24,
              cellHeight: 20,
              margin: 4
            },
            customActions: {},
            draggable: true,
            resizable: true,
            apiPrefix: "/api",
            viewTitle: `查看${pageTitle.value}信息`
          }
        }),
        status: 1,
        remark: `${pageTitle.value}的页面配置`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        module: selectedModule.value || "admin",
        title: pageTitle.value,
        group: selectedGroup.value || "default",
        version: version,
        icon: selectedIcon.value || "Menu",
        draggable: true,
        resizable: true,
        uiConfigId: 0,
        grid_items: []
      };
      // 调用saveUiConfig保存配置
      const response: any = await service.saveUiConfig(newConfig);
      console.log('saveUiConfig response', response);
      if (response) {
        newConfig.id = response;
        newConfig.uiConfigId = response;
        //newConfig.config_content = JSON.parse(newConfig.config_content);
      }

      //将newConfig转换为PageData
      const pageData: PageData = {
        id: newConfig.id,
        config_key: newConfig.config_key,
        config_type: newConfig.config_type,
        created_at: newConfig.created_at,
        updated_at: newConfig.updated_at,
        draggable: newConfig.draggable,
        resizable: newConfig.resizable,
        status: newConfig.status,
        title: newConfig.title,
        version: newConfig.version,
        version_hash: newConfig.version_hash,
        remark: newConfig.remark,
        module: newConfig.module,
        group: newConfig.group,
        icon: newConfig.icon,
        frontend_path: newConfig.frontend_path,
        config_content: JSON.parse(newConfig.config_content),
        grid_items: newConfig.grid_items
      };
      // 将新配置设置到gridLayoutStore
      await gridLayoutStore.setStructureDataFromBackend(pageData);
      
      // 保存到localforage
      await localforage.setItem('grid_management_last_config', toRaw(newConfig));
      
      // 更新状态
      showPage.value = true;
      pageConfig.value = newConfig.config_content;
      
      // 同步数据到GridLayoutStore
      syncDataToLayoutStore();
      
      ElMessage.success('新配置已创建，请添加组件并保存');
    } catch (error) {
      console.error('创建配置失败', error);
      ElMessage.error('创建配置失败，请稍后重试');
    }
  }
  
  // 返回服务层的所有状态和方法以及新增的状态和方法
  return {
    // 共享gridLayoutStore实例
    gridLayoutStore,
    service,
    _metaData,
    // 数据访问
    modules: computed(() => _metaData.value.modules),
    groupNames: computed(() => _metaData.value.groupNames),
    apis: computed(() => _metaData.value.apis),
    controllers: computed(() => _metaData.value.controllers),
    dtos: computed(() => _metaData.value.dtos),
    gridItems: computed(() => _gridItems.value),
    pageConfig,
    uiConfigs,
    selectedUiConfig,
    loadedCount,
    defaultGridItem,
    
    // 显示状态
    selectedModule,
    selectedGroup,
    pageTitle,
    frontendPath,
    selectedIcon,
    showPage,
    showGridDialog,
    showDtosDrawer,
    currentId,
    
    // 过滤状态
    filterKeyword,
    
    // 折叠状态
    isApiListCollapsed,
    isControllersListCollapsed,
    isConfigListCollapsed,
    
    // 编辑状态
    currentGridItem,
    dtoSource,
    dtoSourceJson,
    processedDtoFields,
    
    // 业务方法
    handleModuleChange,
    handleGroupChange,
    refreshCurrentModule,
    refreshAllModules,
    //updateDtoSourceJson,
    setDtoSource,
    //handleGenerateFromDto,
    generateConfigFromSource,
    regenerateConfig,
    saveConfig,
    createConfig,
    unloadConfig,
    loadConfigById,
    updateGridItem,
    addGridItem,
    addGridItems,
    removeGridItem,
    //updateGridItemsFromNodes,
    updateConfigIcon,
    updateConfigStatus,
    openGridItemDialog,
    saveGridItem,
    setBaseUrl,
    refreshBasePage,
    refreshGridItems,
    saveLastConfig,
    updateGridPositions,
    deleteConfig,
    
    // 新增方法
    syncDataToLayoutStore,
    gotoPageSetting,
    handleConfigChange,
    updateCurrentGridItemContent,
    processDtoStructure,
    
    // 状态控制
    isUnloadingConfig
  };
});