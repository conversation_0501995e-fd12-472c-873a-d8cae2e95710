/**
 * 文件：GridManagementService.ts
 * 职责：封装所有网格管理相关的后端API调用。
 * 主要功能：提供网格数据的增删改查接口方法，负责与后端通信。
 * 作者：张二浩
 * 创建时间：2025-04-15
 * 更新时间：2025-04-18
 * 网格管理服务层，提供网格管理所需的API交互和数据处理功能
 */

import { ElMessage } from 'element-plus';
import { 
  getApiDoc, 
  getGroupNames, 
  getDtos, 
  getControllers, 
  getUiConfigs, 
  createConfigs, 
  updateUiConfig 
} from '@/modules/admin/api';
import { GridPageConfigGenerator } from '@/modules/admin/components/GridPageConfigGenerator';
import type { ApiStructure } from '@/modules/admin/components/GridPageConfigGenerator';
//import { useApiManagementStore } from '@/modules/admin/stores/apiManagement'; 
//import GridItemAdapter from '@/modules/admin/adapters/GridItemAdapter';
import { useGridManagementStore } from '@/modules/admin/views/gridManagement/gridManagementStore';
import CacheService from '@/modules/admin/service/CacheService';
import type { AdminUIConfigDTO, AdminGridInfoDTO, DTOInfoDTO } from '@/modules/admin/types';
//import type { BaseResponse } from '../constants/apiTypes';

// 导入网格信息相关API
import { 
  //getGridInfoList, 
  createGridInfo, 
  updateGridInfo, 
  batchUpdateGridInfoPosition,
  setGridInfoUIConfigRelation,
  deleteUIConfigRelation,
  setBatchUIConfigGridRelation
} from '@/modules/admin/api/gridInfos';

import { deleteUiConfig as apiDeleteUiConfig } from '@/modules/admin/api/uiConfig';

// ===================== TS类型声明 =====================
/**
 * grid_item数据库原始结构类型
 */
// interface RawGridItem {
//   id: string | number;
//   position?: string | RawGridItemPosition;
//   content?: Record<string, unknown>;
//   name?: string;
//   api?: string;
//   dto?: string;
//   remark?: string;
//   status?: number;
//   created_at?: string;
//   updated_at?: string;
//   [key: string]: unknown;
// }

/**
 * grid_item.position结构类型
 */
// interface RawGridItemPosition {
//   x?: number;
//   y?: number;
//   w?: number;
//   h?: number;
//   min_w?: number;
//   min_h?: number;
//   max_w?: number;
//   max_h?: number;
//   locked?: boolean;
//   no_move?: boolean;
//   no_resize?: boolean;
//   auto_position?: boolean;
//   lastUiPosition?: {
//     x: number;
//     y: number;
//     w: number;
//     h: number;
//   };
// }

/**
 * 标准化后的grid组件配置类型
 */
// interface GridItemConfig extends RawGridItemPosition {
//   i: string;
//   id: string;
//   static: boolean;
//   'is-draggable': boolean;
//   'is-resizable': boolean;
//   'is-bounded': boolean;
//   content?: Record<string, unknown>;
//   [key: string]: unknown;
// }

/**
 * 获取uiconfig配置的参数类型
 * @param params 请求参数
 */
interface GetUiConfigParams {
  modules: string;
  group: string;
  page: number;
  pageSize: number;
}

// 定义 configData 的接口
export interface ConfigData {
  icon: string;
  module: string;
  group: string;
  title: string;
  frontend_path: string;
  config_type: string;
  config_key: string;
  config_content: string;
  remark: string;
  status: number;
  version: string;
  version_hash: string;
  uiConfigId: number;
  dto?: string;
  grid_items?: AdminGridInfoDTO[]; 
  draggable: boolean;
  resizable: boolean;
}

// 分组接口
export interface GroupName {
  page_name: string;
}

// 字段信息接口
export interface FieldInfo {
  prop: string;
  type: string;
  validation: string;
  defaultValue: unknown;
  label: string;
  options?: { value: number | string; label: string }[];
  [key: string]: unknown;
}

// API响应类型
export interface ApiResponse<T> {
  list: T[];
  page: number;
  pageSize: number;
  total: number;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  list: T[];
  page: number;
  pageSize: number;
  total: number;
}

// 加载UI配置响应类型
export interface LoadUiConfigResponse {
  pageConfig: Record<string, unknown>;
  gridItems: AdminGridInfoDTO[];
}

// 缓存配置接口
interface CacheConfig {
  key: string;
  expiry: number; // 过期时间，单位毫秒
}

// API基本节点结构
export interface ApiNode {
  name: string;
  path: string;
  method: string;
  description?: string;
  parameters?: Record<string, unknown>[];
  responses?: Record<string, unknown>;
  [key: string]: unknown;
}

// DTO结构接口
export interface DtoStructure {
  name: string;
  description?: string;
  [key: string]: unknown;
}

/**
 * GridManagementService类
 * 负责与后端API交互，处理数据转换，不存储状态
 */
export class GridManagementService {
  // 缓存服务实例
  private cacheService = new CacheService('gridManagement');
  
  // API管理Store
  //private apiManagementStore = useApiManagementStore();
  private gridManagementStore = useGridManagementStore();
  
  // 默认的缓存过期时间配置
  private readonly CACHE_EXPIRY = {
    SHORT: 300000,    // 5分钟
    MEDIUM: 1800000,  // 30分钟
    LONG: 3600000     // 1小时
  };
  
  // 构造函数
  constructor() {}
  
  /**
   * 格式化原始grid_item数据为前端可用的grid组件配置
   * @param item 原始grid_item对象
   * @returns 标准化后的grid组件配置对象
   */
  // private formatGridItemConfig(item: RawGridItem): GridItemConfig {
  //   let position: RawGridItemPosition = { x: 0, y: 0, w: 4, h: 4 };
    
  //   if (item.position) {
  //     if (typeof item.position === 'string') {
  //       try {
  //         position = JSON.parse(item.position);
  //       } catch (e) {
  //         console.error('解析position失败', e);
  //       }
  //     } else {
  //       position = item.position;
  //     }
  //   }
    
  //   // 构建唯一标识符
  //   const uniqueId = `grid-item-${item.id}`;
    
  //   return {
  //     i: uniqueId,
  //     id: uniqueId,
  //     x: position.x || 0,
  //     y: position.y || 0,
  //     w: position.w || 4,
  //     h: position.h || 4,
  //     min_w: position.min_w || 1,
  //     min_h: position.min_h || 1,
  //     max_w: position.max_w || 0,
  //     max_h: position.max_h || 0,
  //     locked: position.locked || false,
  //     no_move: position.no_move || false,
  //     no_resize: position.no_resize || false,
  //     auto_position: position.auto_position || false,
  //     lastUiPosition: position.lastUiPosition,
  //     static: position.locked || false,
  //     'is-draggable': !(position.locked || position.no_move || false),
  //     'is-resizable': !(position.locked || position.no_resize || false),
  //     'is-bounded': true,
  //     content: item.content
  //   };
  // }
  
  /**
   * 计算配置的校验和
   * @param config 配置对象
   * @returns 校验和字符串
   */
  public calculateConfigChecksum(config: Record<string, unknown>): string {
    try {
      const jsonStr = JSON.stringify(config);
      let hash = 0;
      for (let i = 0; i < jsonStr.length; i++) {
        const char = jsonStr.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
      }
      return Math.abs(hash).toString(16);
    } catch (e) {
      console.error('计算校验和失败', e);
      return Date.now().toString(16);
    }
  }
  
  /**
   * 处理DTO结构并提取字段信息
   * @param structure DTO的结构对象
   * @returns 处理后的字段数组
   */
  public processDtoStructure(structure: Record<string, unknown>): FieldInfo[] {
    console.log('【GridManagementService】processDtoStructure', structure);
    const fields: FieldInfo[] = [];
    
    Object.entries(structure).forEach(([key, field]) => {
      // 对field类型进行检查，确保它是一个对象
      if (typeof field === 'object' && field !== null) {
        const fieldObj = field as Record<string, unknown>;
        const fieldType = (fieldObj.type as string) || 'string';
        const description = (fieldObj.description as string) || '';
        
        // 处理description字段，提取label和options
        let label = description;
        let options: { value: number | string; label: string }[] | undefined = undefined;
        
        // 检查description是否包含冒号和逗号分隔的选项
        if (description && description.includes('：') && description.includes(',')) {
          const colonIndex = description.indexOf('：');
          if (colonIndex !== -1) {
            // 提取冒号前的部分作为label
            label = description.substring(0, colonIndex);
            
            // 提取冒号后的部分，检查是否有选项格式
            const optionsText = description.substring(colonIndex + 1);
            const optionParts = optionsText.split(',');
            
            // 检查选项格式是否合适（例如：0-普通,1-银牌等）
            if (optionParts.length > 0 && optionParts[0].includes('-')) {
              options = optionParts.map(item => {
                const parts = item.trim().split('-');
                if (parts.length === 2) {
                  // 尝试将value转换为数字，如果失败则保留为字符串
                  const value = isNaN(Number(parts[0])) ? parts[0] : Number(parts[0]);
                  return {
                    value,
                    label: parts[1]
                  };
                }
                return { value: item, label: item };
              });
            }
          }
        }
        
        const fieldInfo: FieldInfo = {
          prop: key,
          type: fieldType,
          validation: (fieldObj.validation as string) || '',
          defaultValue: fieldType === 'string' ? '' : 
                       fieldType === 'number' ? 0 :
                       fieldType === 'boolean' ? false : null,
          label: label
        };
        
        // 如果存在选项，则添加到字段信息中
        if (options && options.length > 0) {
          fieldInfo.options = options;
        }
        
        fields.push(fieldInfo);
      } else {
        // 如果field不是对象，创建一个基本的字段信息
        fields.push({
          prop: key,
          type: 'string',
          validation: '',
          defaultValue: '',
          label: key
        });
      }
    });
    
    return fields;
  }
  
  /**
   * 生成缓存键
   * @param baseKey 基础键名
   * @param params 参数对象
   * @returns 完整缓存键
   */
  private generateCacheKey(baseKey: string, params?: Record<string, unknown>): string {
    return params ? `${baseKey}_${JSON.stringify(params)}` : baseKey;
  }
  
  /**
   * 通用缓存获取和API请求方法
   * @param cacheConfig 缓存配置
   * @param apiCall API调用函数
   * @param forceRefresh 是否强制刷新
   * @returns API响应
   */
  private async getCachedOrFresh<T>(
    cacheConfig: CacheConfig,
    apiCall: () => Promise<T>,
    forceRefresh: boolean = false
  ): Promise<T> {
    //console.log('【调试】getCachedOrFresh for all', cacheConfig.key, apiCall, forceRefresh);
    // 如果不强制刷新，尝试从缓存获取
    if (!forceRefresh) {
      const cached = await this.cacheService.get<T>(cacheConfig.key);
      if (cached)
        {
          //console.log('【调试】从缓存获取数据:', cacheConfig.key, cached);
          return cached;
        } 
      else {
        //return await apiCall();
      }
    }
    
    // 从API获取数据
    try {
      const response = await apiCall();
      
      if (response) {
        // 缓存数据
        await this.cacheService.set(cacheConfig.key, response, cacheConfig.expiry);
        //console.log('【调试】缓存数据:', cacheConfig.key, response);
        return response;
      }
      
      throw new Error('API响应数据为空');
    } catch (error) {
      console.error(`获取数据失败: ${cacheConfig.key}`, error);

      throw error;
    }
  }

  /**
   * 从API响应中提取适当的响应数据
   * 处理不同类型的API响应，确保类型安全
   * 支持常见的分页结构：{list,total,page,pageSize}
   * @param response API响应
   * @param defaultValue 默认值
   * @returns 标准化的响应数据
   */
  private extractApiResponseData<T>(response: unknown, defaultValue: T): T {
    if (response && typeof response === 'object') {
      // 优先处理常见分页结构
      if ('list' in response && Array.isArray((response as any).list)) {
        return (response as any).list as T;
      }
      if ('data' in response && (response as any).data != null) {
        return (response as any).data as T;
      }
    }
    return defaultValue;
  }
  
  /**
   * 获取API文档
   * @param forceRefresh 是否强制刷新
   * @returns API文档数据
   */
  public async fetchApiDoc(forceRefresh: boolean = false, params?: Record<string, unknown>): Promise<ApiNode[]> {
    // 确保默认获取所有数据
    if (!params) {
      params = {module: this.gridManagementStore.selectedModule, page: 1, pageSize: 2000};
    } else if (!params.page || !params.pageSize) {
      params = {...params, page: 1, pageSize: 2000};
    }
    
    const cacheKey = this.generateCacheKey('api_doc', {});
    ///console.log('【调试】apidoc cacheKey:', cacheKey);
    return this.getCachedOrFresh<ApiNode[]>(
      { key: cacheKey, expiry: this.CACHE_EXPIRY.LONG },
      async () => {
        const response = await getApiDoc(params);
        //console.log('【调试】API文档数据:', response);
        return this.extractApiResponseData(response, []);
      },
      forceRefresh
    );
  }
  
  /**
   * 过滤API文档
   * @param apis API数组
   * @returns 过滤后的API数组
   */
  public filterApiDoc(apis: ApiNode[]): ApiNode[] {
    if (!apis || !Array.isArray(apis)) return [];
    
    return apis.filter(_api => {
      // 过滤逻辑
      return true;
    });
  }
  
  /**
   * 获取分组名称
   * @param forceRefresh 是否强制刷新
   * @param params 请求参数
   * @returns 分组名称数据
   */
  public async fetchGroupNames(
    forceRefresh: boolean = false, 
    params?: Record<string, unknown>
  ): Promise<PaginatedResponse<GroupName>> {
    // 确保默认获取所有数据
    if (!params) {
      params = {module: this.gridManagementStore.selectedModule||'admin',page: 1, pageSize: 2000};
    } else if (!params.page || !params.pageSize) {
      params = {...params, page: 1, pageSize: 2000};
    }
    
    const cacheKey = this.generateCacheKey('group_names', {});
    //console.log('【调试】cacheKey', cacheKey);
    return this.getCachedOrFresh<PaginatedResponse<GroupName>>(
      { key: cacheKey, expiry: this.CACHE_EXPIRY.MEDIUM },
      async () => {
        const response = await getGroupNames(params);
        return response as unknown as PaginatedResponse<GroupName>;
      },
      forceRefresh
    );
  }
  
  /**
   * 获取DTO列表
   * @param forceRefresh 是否强制刷新
   * @param params 请求参数
   * @returns DTO列表数据
   */
  public async fetchDtos(
    forceRefresh: boolean = false, 
    params?: Record<string, unknown>
  ): Promise<PaginatedResponse<DtoStructure>> {
    // 确保默认获取所有数据
    if (!params) {
      params = {page: 1, pageSize: 2000};
    } else if (!params.page || !params.pageSize) {
      params = {page: 1, pageSize: 2000};
    }
    
    const cacheKey = this.generateCacheKey('dtos', {});
    return this.getCachedOrFresh<PaginatedResponse<DtoStructure>>(
      { key: cacheKey, expiry: this.CACHE_EXPIRY.MEDIUM },
      async () => {
        const response = await getDtos(params);
        return response as unknown as PaginatedResponse<DtoStructure>;
      },
      forceRefresh
    );
  }
  
  /**
   * 获取控制器列表
   * @param forceRefresh 是否强制刷新
   * @param params 请求参数
   * @returns 控制器列表数据
   */
  public async fetchControllers(
    forceRefresh: boolean = false, 
    params?: Record<string, unknown>
  ): Promise<PaginatedResponse<ApiNode>> {
    // 确保默认获取所有数据
    if (!params) {
      params = {page: 1, pageSize: 2000};
    } else if (!params.page || !params.pageSize) {
      params = {...params, page: 1, pageSize: 2000};
    }
    
    const cacheKey = this.generateCacheKey('controllers', {});
    return this.getCachedOrFresh<PaginatedResponse<ApiNode>>(
      { key: cacheKey, expiry: this.CACHE_EXPIRY.MEDIUM },
      async () => {
        const response = await getControllers(params);
        return response as unknown as PaginatedResponse<ApiNode>;
      },
      forceRefresh
    );
  }
  
  /**
   * 获取UI配置列表
   * @param forceRefresh 是否强制刷新
   * @param params 请求参数
   * @returns UI配置列表数据
   */
  public async fetchUiConfigs(
    forceRefresh: boolean = false, 
    params?: GetUiConfigParams
  ): Promise<PaginatedResponse<AdminUIConfigDTO>> {
    const cacheKey = this.generateCacheKey('ui_configs', {});
    return this.getCachedOrFresh<PaginatedResponse<AdminUIConfigDTO>>(
      { key: cacheKey, expiry: this.CACHE_EXPIRY.MEDIUM },
      async () => {
        const response = await getUiConfigs(params);
        //console.log('【调试】获取UI配置列表:', response);
        try {
          (response as PaginatedResponse<AdminUIConfigDTO>).list.forEach((item: AdminUIConfigDTO) => {
            item.config_content = typeof item.config_content === 'string' ? JSON.parse(item.config_content) : item.config_content;
            item.dto = typeof item.dto === 'string' && item.dto !== '' ? JSON.parse(item.dto) : item.dto;
            item.grid_items?.map((gridItem: AdminGridInfoDTO) => {
              gridItem.position = typeof gridItem.position === 'string' ? JSON.parse(gridItem.position) : gridItem.position;
              gridItem.content = typeof gridItem.content === 'string' ? JSON.parse(gridItem.content) : gridItem.content;
            });
          }); // 解析config_content字段
        return response as unknown as PaginatedResponse<AdminUIConfigDTO>;
      } catch (error) {
        console.error('解析UI配置数据失败:', error);
        throw error;
      }
      },
      forceRefresh
    );
  }
  
  /**
   * 更新UI配置图标
   * @param config UI配置对象
   * @returns 更新结果
   */
  public async updateConfigIcon(config: AdminUIConfigDTO): Promise<ApiResponse<any>> {
    try {
      if (typeof config.id !== 'number') {
        throw new Error('无效的配置ID');
      }
      
      const response = await updateUiConfig(config.id, { icon: config.icon }) as ApiResponse<any>;
      return response;
    } catch (error) {
      console.error('更新UI配置图标失败', error);
      ElMessage.error('更新UI配置图标失败');
      throw error;
    }
  }
  
  /**
   * 更新UI配置状态
   * @param config UI配置对象
   * @param status 状态值
   * @returns 更新结果
   */
  public async updateConfigStatus(config: AdminUIConfigDTO, status: number): Promise<ApiResponse<any>> {
    try {
      if (typeof config.id !== 'number') {
        throw new Error('无效的配置ID');
      }
      
      const response = await updateUiConfig(config.id, { status }) as ApiResponse<any>;
      return response;
    } catch (error) {
      console.error('更新UI配置状态失败', error);
      ElMessage.error('更新UI配置状态失败');
      throw error;
    }
  }
  
  /**
   * 添加网格项
   * @param gridItem 网格项对象
   * @returns 添加结果
   */
  public async addGridItem(gridItem: AdminGridInfoDTO): Promise<ApiResponse<any>> {
    try {
      // 确保position字段是字符串
      const position = typeof gridItem.position === 'object' ? 
                      JSON.stringify(gridItem.position) : 
                      gridItem.position;
      
      const data = {
        ...gridItem,
        position,
        content: typeof gridItem.content === 'object' ? 
                JSON.stringify(gridItem.content) : 
                gridItem.content
      };
      
      const response = await createGridInfo(data) as ApiResponse<any>;
      console.log('添加网格项 成功 返回：', response);
      return response;
    } catch (error) {
      console.error('添加网格项失败', error);
      ElMessage.error('添加网格项失败');
      throw error;
    }
  }
  
  /**
   * 更新网格项
   * @param id 网格项ID
   * @param updates 更新数据
   * @returns 更新结果
   */
  public async updateGridItem(id: number, updates: Partial<AdminGridInfoDTO>): Promise<ApiResponse<any>> {
    try {
      // 如果更新包含position字段，确保它是字符串
      let updatesData = { ...updates } as any;
      
      if (updates.position && typeof updates.position === 'object') {
        updatesData.position = JSON.stringify(updates.position);
      }
      
      // 如果更新包含content字段，确保它是字符串
      if (updates.content && typeof updates.content === 'object') {
        updatesData.content = JSON.stringify(updates.content);
      }
      
      const response = await updateGridInfo(id, updatesData) as ApiResponse<any>;
      return response;
    } catch (error) {
      console.error('更新网格项失败', error);
      ElMessage.error('更新网格项失败');
      throw error;
    }
  }
  
/**获取网格真实id值
 * @param id 网格项ID string类型
 * @returns 网格项ID number类型
 */
  public getGridId(id: string): number {
    if(id.startsWith('grid-item-')) {
      return parseInt(id.replace('grid-item-', ''));
    }
    return parseInt(id);
  }

  /**
   * 批量更新网格项位置
   * @param items 网格项数组
   * @returns 更新结果
   */
  public async updateGridPositions(items: Array<{i: string | number, x: number, y: number, w: number, h: number}>): Promise<ApiResponse<any>> {
    console.log('更新网格项位置', items);
    if(!items || items.length === 0) {
      // 返回一个空的ApiResponse对象，而不是undefined
      return Promise.resolve({
        list: [],
        page: 1,
        pageSize: 0,
        total: 0
      });
    }
    try {
      // 转换位置数据
      const positionData = items.map(item => ({
        id: typeof item.i === 'string' ? this.getGridId(item.i) : item.i,
        position: JSON.stringify({
          i: item.i,
          x: item.x,
          y: item.y,
          w: item.w,
          h: item.h
        })
      }));
      
      // API期望的格式可能与我们的参数结构不同
      // 根据API需要，调整数据格式
      // 过滤掉id为null、undefined或NaN的项，避免提交无效数据
      const requestData = {
        items: positionData
          .filter(item => item.id !== null && item.id !== undefined && !isNaN(Number(item.id))) // 过滤掉id为null、undefined或NaN的项
          .map(item => ({
            uiConfigId: (this.gridManagementStore.gridLayoutStore.structureData?.id || 0) as number, // 这个值在批量更新位置时不重要，但API需要
            id: item.id,
            position: item.position
          }))
      };
      if(requestData.items.length === 0) {
        return Promise.resolve({
          list: [],
          page: 1,
          pageSize: 0,
          total: 0
        });
      }
      const response = await batchUpdateGridInfoPosition(requestData) as ApiResponse<any>;
      return response;
    } catch (error) {
      console.error('批量更新网格项位置失败', error);
      ElMessage.error('批量更新网格项位置失败');
      throw error;
    }
  }
  
  /**
   * 删除网格项
   * @param id 网格项ID
   * @returns 删除结果
   */
  public async removeGridItem(id: number): Promise<ApiResponse<any>> {
    try {
      // 使用updateGridInfo将状态设为0表示删除
      const response = await updateGridInfo(id, { status: 0 }) as ApiResponse<any>;
      return response;
    } catch (error) {
      console.error('删除网格项失败', error);
      ElMessage.error('删除网格项失败');
      throw error;
    }
  }
  
  /**
   * 设置网格项与UI配置的关联关系
   * @param gridInfoId 网格项ID
   * @param uiConfigIds UI配置ID数组
   * @returns 设置结果
   */
  public async setGridInfoUIConfigRelation(gridInfoId: number, uiConfigIds: number[]): Promise<ApiResponse<any>> {
    try {
      const response = await setGridInfoUIConfigRelation({ gridInfoId, uiConfigIds }) as ApiResponse<any>;
      return response;
    } catch (error) {
      console.error('设置网格项与UI配置关联关系失败', error);
      ElMessage.error('设置网格项与UI配置关联关系失败');
      throw error;
    }
  }
  
  /**
   * 删除网格项与UI配置的关联关系
   * @param gridInfoId 网格项ID
   * @param uiConfigId UI配置ID
   * @returns 删除结果
   */
  public async deleteUIConfigRelation(gridInfoId: number, uiConfigId: number): Promise<ApiResponse<any>> {
    try {
      // 调用API解除关联
      const response = await deleteUIConfigRelation({ grid_info_id: gridInfoId, ui_config_id: uiConfigId }) as ApiResponse<any>;
      // 清除缓存
      await this.cacheService.clear('grid_items');
      return response;
    } catch (error) {
      console.error('删除网格项与UI配置关联失败', error);
      ElMessage.error('删除网格项与UI配置关联失败');
      throw error;
    }
  }
  
  /**
   * 批量设置UI配置与网格信息的关联关系
   * @param data 包含 uiConfigId 和 gridInfoIds 的对象
   * @returns 设置结果
   */
  public async setBatchUIConfigGridRelation(data: { uiConfigId: number; gridInfoIds: number[] }): Promise<ApiResponse<any>> {
    try {
      const response = await setBatchUIConfigGridRelation(data) as ApiResponse<any>;
      await this.cacheService.clear('grid_items');
      return response;
    } catch (error) {
      console.error('批量设置UI配置与网格信息关联关系失败', error);
      ElMessage.error('批量设置UI配置与网格信息关联关系失败');
      throw error;
    }
  }
  
  /**
   * 刷新网格项列表
   * @param params 请求参数
   * @returns 网格项列表数据
   */
  // public async refreshGridItems(params?: Record<string, unknown>): Promise<AdminGridInfoDTO[]> {
  //   try {
  //     // 尝试从缓存获取
  //     const cacheKey = this.generateCacheKey('grid_items', params);
  //     const cachedData = await this.cacheService.get<AdminGridInfoDTO[]>(cacheKey);
      
  //     if (cachedData) {
  //       return cachedData;
  //     }
      
  //     // 从API获取数据
  //     const response = await getGridInfoList(params || {});
  //     const items = GridItemAdapter.batchToFrontend(response.list || []);
  //     // 缓存数据
  //     await this.cacheService.set(cacheKey, items, this.CACHE_EXPIRY.SHORT);
  //     return items;
  //   } catch (error) {
  //     console.error('刷新网格项列表失败', error);
  //     ElMessage.error('刷新网格项列表失败');
  //     throw error;
  //   }
  // }
  
  /**
   * 从DTO生成配置
   * @param dto DTO对象
   * @returns 生成的配置对象
   */
  public generateConfig(dto: DTOInfoDTO): Record<string, unknown> {
    try {
      const generator = new GridPageConfigGenerator();
      
      // 获取API文档
      //const apiDoc = this.apiManagementStore.apis;
      
      // 转换为ApiStructure类型
      const apiStructure: ApiStructure = {
        //domain: this.apiManagementStore.domain,
        //apis: apiDoc
      };
      console.log('[GridManagementService] apiStructure', apiStructure);
      // 使用生成器生成配置
      const pageConfig = generator.generateConfig(dto, apiStructure);
      
      return pageConfig || {};
    } catch (error) {
      console.error('生成配置失败', error);
      ElMessage.error('生成配置失败');
      throw error;
    }
  }
  
  /**
   * 保存UI配置
   * @param configData 配置数据
   * @returns 保存结果
   */
  public async saveUiConfig(configData: ConfigData): Promise<ApiResponse<any>> {
    try {
      let response: ApiResponse<any>;
      
      // 判断是更新还是创建
      if (configData.uiConfigId) {
        // 更新现有配置
        response = await updateUiConfig(configData.uiConfigId, configData) as ApiResponse<any>;
      } else {
        // 创建新配置
        response = await createConfigs(configData) as ApiResponse<any>;
      }
      
      return response;
    } catch (error) {
      console.error('保存UI配置失败', error);
      ElMessage.error('保存UI配置失败');
      throw error;
    }
  }
  
  /**
   * 从ID加载UI配置
   * @param config UI配置对象
   * @returns 加载的配置对象及网格项
   */
  // public async loadUiConfigById(config: AdminUIConfigDTO): Promise<LoadUiConfigResponse> {
  //   try {
  //     // 解析配置内容
  //     const pageConfig = typeof config.config_content === 'string' ? JSON.parse(config.config_content || '{}') : config.config_content;
      

  //     // 处理网格项
  //     let gridItems: AdminGridInfoDTO[] = [];
      
  //     return { pageConfig, gridItems };
  //   } catch (error) {
  //     console.error('加载UI配置失败', error);
  //     ElMessage.error('加载UI配置失败');
  //     throw error;
  //   }
  // }
  
  /**
   * 删除UI配置
   * @param id UI配置ID
   */
  async deleteUiConfig(id: number): Promise<void> {
    await apiDeleteUiConfig(id);
  }
}

export default GridManagementService;