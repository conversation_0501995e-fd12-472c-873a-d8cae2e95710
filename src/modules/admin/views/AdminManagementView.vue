<template>
  <!-- 添加一个根元素包裹所有内容 -->
  <div class="admin-management-container">
    <div class="admin-management">
      <PlusPage
        ref="plusPageInstance"
        :request="getAdminList"
        :columns="columns"
        :params="state.query"
        :search="{ labelWidth: '100px', colProps: { span: 8 } }"
        :table="{
          isSelection: true,
          actionBar: { buttons, width: 280 },
          onClickAction: handleTableOption,
          onSelectionChange: handleSelect
        }"
        :pagination="{
          page: state.query.page,
          pageSize: state.query.pageSize,
          total: state.total
        }"
      >
        <template #table-title>
          <el-row class="button-row">
            <el-button type="primary" :icon="Plus" @click="handleCreate"> 添加管理员 </el-button>
            <el-button :icon="Delete" type="danger" @click="handleBatchDelete"> 批量删除 </el-button>
          </el-row>
        </template>
      </PlusPage>
    </div>

    <!-- 弹窗编辑 -->
    <PlusDialogForm
      v-model:visible="visible"
      v-model="form"
      :form="{ columns: formColumns, labelPosition: 'left', rules }"
      :dialog="{ title: dialogTitle + '管理员', width: '600px', top: '12vh', confirmLoading }"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    />

    <!-- 查看弹窗 -->
    <PlusDialog
      v-model="detailsVisible"
      width="700px"
      title="管理员详情"
      top="20vh"
      :has-footer="false"
    >
      <PlusDescriptions :column="2" :columns="columns" :data="currentRow" />
    </PlusDialog>
  </div>
</template>

<script setup lang="ts">
/**
 * @file AdminManagementView.vue
 * <AUTHOR>
 * @date 2025-01-25
 * @version 1.0.0
 * @description 管理员管理页面 - 使用PlusPage组件实现的简单页面
 * 包含管理员列表展示、搜索、新增、编辑、删除、查看等功能
 */
import { computed, reactive, toRefs, ref, onBeforeUnmount } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import type { FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import type { ButtonsCallBackParams, PlusPageInstance, PlusColumn } from 'plus-pro-components'
import { useTable } from 'plus-pro-components'
import { 
  getAdminList as apiGetAdminList, 
  createAdmin, 
  updateAdmin, 
  deleteAdmin, 
  //updateAdminStatus,
  resetAdminPassword
} from '@/modules/admin/api'
import type { AdminInfo, AdminListParams } from '@/modules/admin/types'
import { AdminRole, AdminStatus } from '@/modules/admin/types'

/**
 * 状态接口定义
 */
interface State {
  /** 检索数据 */
  query: AdminListParams
  /** 总数 */
  total: number
  /** 当前选择的行数据 */
  currentRow: Partial<AdminInfo>
  /** 表单弹窗是否可见 */
  visible: boolean
  /** 详情弹窗是否可见 */
  detailsVisible: boolean
  /** 当前选择多行的id集合 */
  selectedIds: number[]
  /** 提交loading */
  confirmLoading: boolean
  /** 是否是创建 */
  isCreate: boolean
  /** 是否批量 */
  isBatch: boolean
  /** 表单 */
  form: {
    username: string
    nickname: string
    password?: string
    email?: string
    mobile?: string
    role: AdminRole
    status: AdminStatus
    id?: number
  }
  /** 校验规则 */
  rules: FormRules
}

const plusPageInstance = ref<PlusPageInstance | null>(null)

/**
 * 响应式状态
 */
const state = reactive<State>({
  query: {
    page: 1,
    pageSize: 10,
    username: '',
    nickname: '',
    role: undefined,
    status: undefined
  },
  total: 0,
  currentRow: {},
  visible: false,
  detailsVisible: false,
  confirmLoading: false,
  isCreate: true,
  isBatch: false,
  selectedIds: [],
  form: {
    username: '',
    nickname: '',
    password: '',
    email: '',
    mobile: '',
    role: AdminRole.ADMIN,
    status: AdminStatus.ACTIVE
  },
  rules: {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
    ],
    nickname: [
      { required: true, message: '请输入昵称', trigger: 'blur' },
      { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
    ],
    email: [
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ],
    mobile: [
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    role: [
      { required: true, message: '请选择角色', trigger: 'change' }
    ],
    status: [
      { required: true, message: '请选择状态', trigger: 'change' }
    ]
  }
})

/**
 * 计算属性
 */
const dialogTitle = computed(() => (state.isCreate ? '新增' : '编辑'))
const { buttons } = useTable()

/**
 * 角色选项
 */
const roleOptions = [
  { label: '超级管理员', value: AdminRole.SUPER_ADMIN },
  { label: '普通管理员', value: AdminRole.ADMIN },
  { label: '运营人员', value: AdminRole.OPERATIONS },
  { label: '客服人员', value: AdminRole.CUSTOMER_SERVICE }
]

/**
 * 状态选项
 */
const statusOptions = [
  { label: '正常', value: AdminStatus.ACTIVE },
  { label: '禁用', value: AdminStatus.DISABLED }
]

/**
 * 表格列配置
 */
const columns: PlusColumn[] = [
  {
    label: '用户名',
    width: 120,
    prop: 'username'
  },
  {
    label: '昵称',
    width: 120,
    prop: 'nickname'
  },
  {
    label: '邮箱',
    width: 180,
    prop: 'email',
    hideInSearch: true
  },
  {
    label: '手机号',
    width: 130,
    prop: 'mobile',
    hideInSearch: true
  },
  {
    label: '角色',
    width: 120,
    prop: 'role',
    valueType: 'select',
    options: roleOptions,
    fieldProps: {
      clearable: true,
      placeholder: '请选择角色'
    }
  },
  {
    label: '状态',
    width: 100,
    prop: 'status',
    valueType: 'select',
    options: statusOptions,
    fieldProps: {
      clearable: true,
      placeholder: '请选择状态'
    }
  },
  {
    label: '最后登录时间',
    minWidth: 170,
    prop: 'lastLoginTime',
    valueType: 'date-picker',
    hideInSearch: true
  },
  {
    label: '创建时间',
    minWidth: 170,
    prop: 'createTime',
    valueType: 'date-picker',
    hideInSearch: true
  }
]

/**
 * 表单列配置
 */
const formColumns: PlusColumn[] = [
  {
    label: '用户名',
    prop: 'username',
    hideInTable: true
  },
  {
    label: '昵称',
    prop: 'nickname',
    hideInTable: true
  },
  {
    label: '密码',
    prop: 'password',
    component: 'el-input',
    hideInTable: true,
    hideInForm: !state.isCreate
  },
  {
    label: '邮箱',
    prop: 'email',
    hideInTable: true
  },
  {
    label: '手机号',
    prop: 'mobile',
    hideInTable: true
  },
  {
    label: '角色',
    prop: 'role',
    valueType: 'select',
    options: roleOptions,
    hideInTable: true
  },
  {
    label: '状态',
    prop: 'status',
    valueType: 'select',
    options: statusOptions,
    hideInTable: true
  }
]

/**
 * 操作按钮配置
 */
buttons.value = [
  {
    text: '编辑',
    code: 'update',
    props: { type: 'primary', size: 'small' }
  },
  {
    text: '重置密码',
    code: 'resetPassword',
    confirm: true,
    props: { type: 'warning', size: 'small' }
  },
  {
    text: '删除',
    code: 'delete',
    confirm: true,
    props: { type: 'danger', size: 'small' }
  },
  {
    text: '查看',
    code: 'view',
    props: { type: 'info', size: 'small' }
  }
]

/**
 * 获取管理员列表
 * @param params 查询参数
 */
const getAdminList = async (params: AdminListParams) => {
  try {
    const response : any = await apiGetAdminList(params)
    return {
      data: response.list || [],
      total: response.total || 0
    }
  } catch (error) {
    console.error('获取管理员列表失败:', error)
    return { data: [], total: 0 }
  }
}

/**
 * 按钮操作处理
 * @param params 按钮回调参数
 */
const handleTableOption = ({ row, buttonRow }: ButtonsCallBackParams): void => {
  state.currentRow = { ...row }
  switch (buttonRow.code) {
    case 'update':
      state.form = { ...row } as any
      state.isCreate = false
      state.visible = true
      break
    case 'delete':
      state.isBatch = false
      handleDelete()
      break
    case 'resetPassword':
      handleResetPassword(row.id)
      break
    case 'view':
      state.detailsVisible = true
      break
  }
}

/**
 * 重新请求列表接口
 */
const refresh = () => {
  plusPageInstance.value?.getList()
}

/**
 * 删除管理员
 */
const handleDelete = async (): Promise<void> => {
  try {
    const params = state.isBatch ? state.selectedIds : [state.currentRow.id as number]
    
    for (const id of params) {
      await deleteAdmin(id)
    }
    
    ElMessage.success('删除成功')
    refresh()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

/**
 * 重置密码
 * @param id 管理员ID
 */
const handleResetPassword = async (id: number): Promise<void> => {
  try {
    await resetAdminPassword(id)
    ElMessage.success('密码重置成功')
  } catch (error) {
    ElMessage.error('密码重置失败')
  }
}

/**
 * 批量删除
 */
const handleBatchDelete = async () => {
  if (!state.selectedIds.length) {
    ElMessage.warning('请选择数据！')
    return
  }
  try {
    await ElMessageBox.confirm('确定删除所选数据', '提示')
    state.isBatch = true
    handleDelete()
  } catch (error) {
    console.log(error)
  }
}

/**
 * 选择处理
 * @param data 选择的数据
 */
const handleSelect = (data: any) => {
  state.selectedIds = [...data].map(item => item.id)
}

/**
 * 创建管理员
 */
const handleCreate = (): void => {
  state.currentRow = {}
  state.form = {
    username: '',
    nickname: '',
    password: '',
    email: '',
    mobile: '',
    role: AdminRole.ADMIN,
    status: AdminStatus.ACTIVE
  }
  state.isCreate = true
  state.visible = true
}

/**
 * 取消操作
 */
const handleCancel = () => {
  state.visible = false
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  try {
    state.confirmLoading = true
    const params = { ...state.form }
    
    if (state.isCreate) {
      await createAdmin(params as any)
      ElMessage.success('创建成功')
    } else {
      const { id, ...updateData } = params
      await updateAdmin(id!, updateData)
      ElMessage.success('更新成功')
    }
    
    handleCancel()
    refresh()
  } catch (error) {
    ElMessage.error(state.isCreate ? '创建失败' : '更新失败')
  }
  state.confirmLoading = false
}

/**
 * 重置组件状态
 */
const resetComponentState = () => {
  // 重置表单状态
  state.form = {
    username: '',
    nickname: '',
    password: '',
    email: '',
    mobile: '',
    role: AdminRole.ADMIN,
    status: AdminStatus.ACTIVE
  }
  
  // 重置其他状态
  state.visible = false
  state.detailsVisible = false
  state.confirmLoading = false
  state.isCreate = true
  state.isBatch = false
  state.selectedIds = []
  state.currentRow = {}
  
  // 重置查询参数
  state.query = {
    page: 1,
    pageSize: 10,
    username: '',
    nickname: '',
    role: undefined,
    status: undefined
  }
}

/**
 * 路由离开前的处理
 */
onBeforeRouteLeave((to, from, next) => {
  console.log('🔄 AdminManagementView 路由离开:', from.path, '->', to.path)
  
  // 如果有未保存的表单，可以在这里提示用户
  if (state.visible && state.confirmLoading) {
    // 如果正在提交，等待完成
    const checkSubmit = () => {
      if (!state.confirmLoading) {
        resetComponentState()
        next()
      } else {
        setTimeout(checkSubmit, 100)
      }
    }
    checkSubmit()
  } else {
    resetComponentState()
    next()
  }
})

/**
 * 组件卸载前的清理
 */
onBeforeUnmount(() => {
  console.log('🧹 AdminManagementView 组件卸载清理')
  resetComponentState()
})

const { form, confirmLoading, rules, currentRow, visible, detailsVisible } = toRefs(state)
</script>

<style scoped>
.admin-management {
  padding: 20px;
}

.button-row {
  margin-bottom: 16px;
}

.button-row .el-button {
  margin-right: 12px;
}
</style>
