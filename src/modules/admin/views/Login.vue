<template>
  <div class="admin-login">
    <div class="login-container">
      <div class="login-header">
        <h1>{{ systemInfo?.siteName || 'O_Mall 管理后台' }}</h1>
        <p>欢迎使用{{ systemInfo?.siteName || '多商家电商平台' }}管理系统</p>
      </div>

      <el-card class="login-form-card">
        <h2>管理员登录</h2>

        <PlusForm
          ref="formRef"
          v-model="formData"
          :columns="columns"
          :rules="rules"
          @change="handleChange"
          @submit="handleLogin"
          @submit-error="handleSubmitError"
          @reset="handleReset"
          :has-label="false"
        >
          <template #footer="{ handleSubmit, handleReset }">
            <div style="margin: 0 auto">
              <el-button type="primary" @click="handleSubmit">登陆</el-button>
              <el-button type="warning" @click="handleReset">重置</el-button>
            </div>
          </template>
        </PlusForm>
        <div class="login-footer">
          <p v-if="systemInfo?.copyright">{{ systemInfo.copyright }}</p>
          <p v-else>&copy; 2024-2025 O_Mall 多商家电商平台</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import localforage from 'localforage';
//import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useAdminStore } from '../stores/adminStore';
import { useSystemStore } from '@/stores/systemStore';
import type { PlusColumn, FieldValues } from 'plus-pro-components';

//const router = useRouter();
const adminStore = useAdminStore();
const systemStore = useSystemStore();
const systemInfo = computed(() => systemStore.systemInfo);

const formRef = ref();
const loading = ref(false);

const formData = reactive({
  username: 'admin',
  password: 'password123',
  remember: [] as string[],
  //rememberMe: false
});
// const formset = reactive({
//   rememberUsername: false,
//   rememberMe: false
// })

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应为3-20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20个字符', trigger: 'blur' }
  ]
};

const columns: PlusColumn[] = [
  {
    label: '用户名',
    prop: 'username',
    width: 120,
    tooltip: '请输入管理员用户名',
    fieldProps: {
      placeholder: '请输入管理员用户名',
      prefixIcon: 'User'
    },
    hasLabel: ref(true)
  
  },
  {
    label: '密    码',
    prop: 'password',
    width: 120,
    tooltip: '请输入管理员密码',
    fieldProps: {
      placeholder: '请输入密码',
      prefixIcon: 'Lock',
      showPassword: true
    },
    hasLabel: ref(true)
  },
  {
    label: '',
    prop: 'remember',
    valueType: 'checkbox',
    options: [
      { label: '记住用户名', value: 'rememberUsername'},
      { label: '30天内免登录', value: 'rememberMe' }
    ]
  }
];

const handleChange = async (values: FieldValues, prop: PlusColumn) => {
  console.log('Form values changed:', values, prop);
  if((values.remember as string[]).includes('rememberUsername')) {
    await localforage.setItem('rememberUsername', values.username);
  } else {
    await localforage.removeItem('rememberUsername');
  }
  if((values.remember as string[]).includes('rememberMe')) {
    console.log('rememberMe checked', values.rememberMe);
    await localforage.setItem('rememberMe', true);
  } else {
    await localforage.removeItem('rememberMe');
  }
};

const handleSubmitError = (err: any) => {
  console.error('Form validation error:', err);
};

const handleReset = () => {
  console.log('Form reset');
};

const handleLogin = async (values: FieldValues) => {
  if (values.rememberUsername) {
    await localforage.setItem('rememberUsername', values.username);
  } else {
    await localforage.removeItem('rememberUsername');
  }

  loading.value = true;
  try {
    await localforage.setItem('rememberMe', true);
    sessionStorage.setItem('rememberMe', '1');
    
    const loginResult = await adminStore.loginAction(
      values.username as string,
      values.password as string,
      true
    );

    if (loginResult.success) {
      // 处理登录响应中的新字段
      handleLoginResponse(loginResult);
      
      // 强制重新初始化路由
      try {
        // 动态导入路由初始化函数
        const { initializeRoutes } = await import('@/router');
        //const { clearRouteCache } = await import('@/utils/routeGuard');
        
        // 清除路由缓存并重新初始化
        //await clearRouteCache();
        await initializeRoutes();
        
        console.log('路由初始化完成，准备跳转到控制台');
      } catch (error) {
        console.error('路由初始化失败:', error);
      }
      
      // 使用原生导航而不是Vue Router，避免触发路由守卫
      ElMessage.success('登录成功，正在跳转...');
      setTimeout(() => {
        window.location.href = window.location.origin + '/admin/dashboard';
      }, 500);
    } else {
      ElMessage.error(loginResult.message || '登录失败，请检查用户名和密码');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '登录失败，请检查用户名和密码');
  } finally {
    loading.value = false;
  }
};

/**
 * 处理登录响应中的新字段
 * @param loginData 登录响应数据
 */
function handleLoginResponse(loginData: any) {
  if (!loginData) return;
  
  // 处理设备ID
  if (loginData.device_id) {
    localStorage.setItem('admin_device_id', loginData.device_id);
  }
  
  // 处理新设备提醒
  if (loginData.is_new_device) {
    ElMessage.info('检测到您在新设备上登录，请注意账户安全');
  }
  
  // 处理风险等级
  if (loginData.risk_level) {
    switch (loginData.risk_level) {
      case 'high':
        ElMessage.warning('检测到异常登录行为，请注意账户安全');
        break;
      case 'medium':
        ElMessage.info('登录环境存在一定风险，建议开启双重验证');
        break;
      // low级别不显示提醒
    }
  }
  
  // 处理其他可能的字段
  if (loginData.security_tips) {
    // 可以在适当的时候显示安全提示
    console.log('安全提示:', loginData.security_tips);
  }
}

// 在组件挂载后尝试使用长期 token 登录
onMounted(async () => {
  const rememberUsernameValue = await localforage.getItem('rememberUsername');
  if (rememberUsernameValue) {
    console.log('rememberUsername', rememberUsernameValue);
    formData.username = String(rememberUsernameValue) || '';
    formData.remember.push('rememberUsername');
  }
  const rememberMeValue = await localforage.getItem('rememberMe');
  if (rememberMeValue) { //  如果 rememberMeValue 存在且为真值 (true, 1, "true" 等)
    formData.remember.push('rememberMe'); // 强制转换为布尔值
  }
  
  try {
    // 获取当前管理员信息，如果成功则表示已登录
    const currentAdmin = await adminStore.fetchCurrentAdmin();
    console.log('currentAdmin', currentAdmin);
    
    if (currentAdmin) {
      console.log('开始自动登录...');
      // 使用原生导航而不是Vue Router，避免触发路由守卫
      ElMessage.success('自动登录成功，正在跳转...');
      setTimeout(() => {
        window.location.href = window.location.origin + '/admin/dashboard';
      }, 500);
    }
  } catch (error) {
    console.error('获取管理员信息失败:', error);
    // 静默失败，让用户手动登录
  }
});
</script>

<style scoped lang="scss">
.admin-login {
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $background-color-base; /* #f4f4f5 */
  font-family: $font-family; /* "Inter", "PingFang SC", "Microsoft YaHei", sans-serif */
}

.login-container {
  width: 100%;
  max-width: 500px; /* 减小最大宽度 */
  padding: 20px; /* 减小内边距 */
  background-color: #fff; /* 白色背景 */
  border-radius: 8px; /* 圆角 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 阴影 */
}

.login-header {
  text-align: center;
  margin-bottom: 20px; /* 减小间距 */
}

.login-header h1 {
  font-size: 24px; /* 减小字体 */
  color: $primary-color; /* #2563eb */
  margin-bottom: 10px;
}

.login-header p {
  color: $text-secondary; /* #71717a */
  font-size: 14px; /* 减小字体 */
}

.login-form-card {
  padding: 20px; /* 减小内边距 */
}

.login-form-card h2 {
  text-align: center;
  margin-bottom: 20px; /* 减小间距 */
  font-size: 20px; /* 减小字体 */
  color: $text-primary; /* #18181b */
  font-weight: 500;
}

.form-actions {
  margin-top: 20px; /* 减小间距 */
}

.login-button {
  width: 100%;
  padding: 12px 0;
  font-size: 16px; /* 减小字体 */
  transition: all 0.3s;
  border-radius: 4px;
  margin-top: 10px;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1); /* 加强阴影 */
}

.login-footer {
  margin-top: 20px; /* 减小间距 */
  text-align: center;
  color: $text-secondary; /* #71717a */
  font-size: 12px; /* 减小字体 */
}
</style>
