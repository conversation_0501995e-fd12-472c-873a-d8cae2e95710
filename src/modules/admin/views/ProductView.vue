<template>
  <div class="product-view">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>商品管理</span>
          <div class="filter-box">
            <el-select v-model="audit_status" placeholder="审核状态" clearable @change="handleFilterChange">
              <el-option label="全部" value="" />
              <el-option label="待审核" value="PENDING" />
              <el-option label="已通过" value="APPROVED" />
              <el-option label="已拒绝" value="REJECTED" />
            </el-select>
            <el-select v-model="merchantId" placeholder="商家筛选" clearable @change="handleFilterChange">
              <el-option label="全部商家" value="" />
              <!-- 实际应用中这里需要动态加载商家列表 -->
            </el-select>
            <el-input
              v-model="searchKeyword"
              placeholder="请输入商品名称/编号"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #append>
                <el-button @click="handleSearch">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="productList"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="商品图片" width="100">
          <template #default="{ row }">
            <el-image 
              style="width: 60px; height: 60px" 
              :src="row.images[0]" 
              fit="cover"
              :preview-src-list="row.images"
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="商品名称" width="180" />
        <el-table-column label="价格" width="120">
          <template #default="{ row }">
            <div>
              <span class="price">¥{{ row.price.toFixed(2) }}</span>
              <span v-if="row.originalPrice" class="original-price">¥{{ row.originalPrice.toFixed(2) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="stock" label="库存" width="80" />
        <el-table-column prop="category" label="分类" width="100" />
        <el-table-column prop="merchantName" label="商家" width="120" />
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="STATUS_TAG_CONFIG.productStatus[row.status as keyof typeof STATUS_TAG_CONFIG['productStatus']].type">
              {{ STATUS_TAG_CONFIG.productStatus[row.status as keyof typeof STATUS_TAG_CONFIG['productStatus']].label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="审核状态" width="80">
          <template #default="{ row }">
            <el-tag :type="STATUS_TAG_CONFIG.productAuditStatus[row.audit_status as keyof typeof STATUS_TAG_CONFIG['productAuditStatus']].type">
              {{ STATUS_TAG_CONFIG.productAuditStatus[row.audit_status as keyof typeof STATUS_TAG_CONFIG['productAuditStatus']].label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="{ row }">
            <!-- 审核操作 -->
            <el-button
              v-if="row.audit_status === 'PENDING'"
              type="success"
              size="small"
              @click="handleAudit(row, 'APPROVED')"
            >
              通过
            </el-button>
            <el-button
              v-if="row.audit_status === 'PENDING'"
              type="danger"
              size="small"
              @click="handleAudit(row, 'REJECTED')"
            >
              拒绝
            </el-button>
            
            <!-- 查看详情 -->
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 审核对话框 -->
    <el-dialog
      v-model="auditDialogVisible"
      :title="auditDialogTitle"
      width="500px"
    >
      <el-form :model="auditForm" label-width="80px">
        <el-form-item v-if="auditForm.audit_status === 'REJECTED'" label="拒绝原因" required>
          <el-input
            v-model="auditForm.reason"
            type="textarea"
            rows="4"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAudit">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 商品详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="商品详情"
      width="700px"
    >
      <div v-if="currentProduct" class="product-detail">
        <div class="product-images">
          <el-carousel :interval="4000" type="card" height="200px">
            <el-carousel-item v-for="(image, index) in currentProduct.images" :key="index">
              <el-image :src="image" fit="contain" style="width: 100%; height: 100%" />
            </el-carousel-item>
          </el-carousel>
        </div>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="商品ID">{{ currentProduct.id }}</el-descriptions-item>
          <el-descriptions-item label="商品名称">{{ currentProduct.name }}</el-descriptions-item>
          <el-descriptions-item label="价格">
            <span class="price">¥{{ currentProduct.price.toFixed(2) }}</span>
            <span v-if="currentProduct.originalPrice" class="original-price">¥{{ currentProduct.originalPrice.toFixed(2) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="库存">{{ currentProduct.stock }}</el-descriptions-item>
          <el-descriptions-item label="分类">{{ currentProduct.category }}</el-descriptions-item>
          <el-descriptions-item label="商家">{{ currentProduct.merchantName }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="STATUS_TAG_CONFIG.productStatus[currentProduct.status].type">
              {{ STATUS_TAG_CONFIG.productStatus[currentProduct.status].label }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="审核状态">
            <el-tag :type="STATUS_TAG_CONFIG.productAuditStatus[currentProduct.audit_status].type">
              {{ STATUS_TAG_CONFIG.productAuditStatus[currentProduct.audit_status].label }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentProduct.createTime }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ currentProduct.updateTime || '暂无' }}</el-descriptions-item>
          <el-descriptions-item label="商品描述" :span="2">
            {{ currentProduct.description || '暂无描述' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, Picture } from '@element-plus/icons-vue';
import { getProductList, auditProduct } from '../api';
import { STATUS_TAG_CONFIG } from '../constants';
import type { Product } from '../types';

const productList = ref<Product[]>([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchKeyword = ref('');
const audit_status = ref('');
const merchantId = ref('');

// 审核对话框
const auditDialogVisible = ref(false);
const auditDialogTitle = ref('');
const auditForm = reactive({
  id: 0,
  audit_status: '',
  reason: '',
});

// 详情对话框
const detailDialogVisible = ref(false);
const currentProduct = ref<Product | null>(null);

// 获取商品列表
const fetchProductList = async () => {
  loading.value = true;
  try {
    const params: any = {
      page: currentPage.value,
      pageSize: pageSize.value,
    };
    
    if (audit_status.value) {
      params.audit_status = audit_status.value;
    }
    
    if (merchantId.value) {
      params.merchantId = Number(merchantId.value);
    }
    
    // 实际接口可能需要添加搜索关键词参数
    // if (searchKeyword.value) {
    //   params.keyword = searchKeyword.value;
    // }
    
    const response:any = await getProductList(params);
    
    if (response.data.code === 0) {
      const { list, total: totalCount } = response.data.data;
      productList.value = list;
      total.value = totalCount;
    } else {
      ElMessage.error(response.data.message || '获取商品列表失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取商品列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  fetchProductList();
};

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchProductList();
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  fetchProductList();
};

// 处理筛选变化
const handleFilterChange = () => {
  currentPage.value = 1;
  fetchProductList();
};

// 处理审核
const handleAudit = (product: Product, status: string) => {
  auditForm.id = product.id;
  auditForm.audit_status = status;
  auditForm.reason = '';
  
  auditDialogTitle.value = status === 'APPROVED' ? '审核通过' : '审核拒绝';
  auditDialogVisible.value = true;
};

// 提交审核
const submitAudit = async () => {
  if (auditForm.audit_status === 'REJECTED' && !auditForm.reason) {
    ElMessage.warning('请输入拒绝原因');
    return;
  }
  
  try {
    const response:any = await auditProduct(
      auditForm.id,
      {
        audit_status: auditForm.audit_status,
        reason: auditForm.reason,
        remark: auditForm.reason
      }
    );
    
    if (response.data.code === 0) {
      ElMessage.success('审核操作成功');
      auditDialogVisible.value = false;
      fetchProductList();
    } else {
      ElMessage.error(response.data.message || '审核操作失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '审核操作失败');
  }
};

// 查看商品详情
const handleViewDetail = (product: Product) => {
  currentProduct.value = product;
  detailDialogVisible.value = true;
};

onMounted(() => {
  fetchProductList();
});
</script>


<style scoped>
.product-view {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-box {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.price {
  font-weight: bold;
  color: #f56c6c;
}

.original-price {
  margin-left: 5px;
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.product-detail {
  padding: 20px 0;
}

.product-images {
  margin-bottom: 20px;
}
</style>
