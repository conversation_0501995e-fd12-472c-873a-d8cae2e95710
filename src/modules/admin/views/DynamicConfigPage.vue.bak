<!-- 
  动态配置页面组件
  通过路由参数加载不同配置并使用BasePage渲染对应的功能页面
  支持本地缓存、版本控制和配置热更新
-->
<template>
  <div class="dynamic-config-page">
    <!-- 加载中状态显示 -->
    <el-skeleton :loading="loading" animated>
      <template #template>
        <div style="padding: 20px;">
          <el-skeleton-item variant="p" style="width: 100%; height: 60px; margin-bottom: 20px" />
          <div style="display: flex; justify-content: space-between; margin-bottom: 20px">
            <el-skeleton-item variant="button" style="width: 250px; height: 40px;" />
            <el-skeleton-item variant="button" style="width: 150px; height: 40px;" />
          </div>
          <el-skeleton-item variant="p" style="width: 100%; height: 400px" />
        </div>
      </template>
      
      <!-- 实际页面内容 -->
      <template #default>
        <template v-if="!error">
          <BasePage
            :key="configId"
            :title="pageConfig.title || '动态页面'"
            :service-config="pageConfig.serviceConfig"
            :table-config="pageConfig.tableConfig"
            :form-config="pageConfig.formConfig"
            :search-config="pageConfig.searchConfig"
            :toolbar-config="pageConfig.toolbarConfig"
            :info-config="pageConfig.infoConfig"
            @toolbar-action="handleToolbarAction"
            @row-action="handleRowAction"
            @search="handleSearch"
            @upload-success="handleUploadSuccess"
          />
        </template>
        <template v-else>
          <el-result
            icon="error"
            title="配置加载失败"
            :sub-title="errorMessage"
          >
            <template #extra>
              <el-button type="primary" @click="reloadConfig">重试</el-button>
              <el-button @click="useDefaultConfig">使用默认配置</el-button>
            </template>
          </el-result>
        </template>
      </template>
    </el-skeleton>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed, watch, h } from 'vue';
import { useRoute, useRouter } from 'vue-router';
//import { ElMessage, ElMessageBox, ElEmpty } from 'element-plus';
import { BasePage } from '@/components/page';
import localforage from 'localforage';
import { getConfigById } from '../api/uiConfig';
// 导入BasePage所需的类型定义
import type { TableConfig, SearchConfig, FormConfig, ServiceConfig, CustomPlusColumn, CustomActionConfig } from '@/components/page/types';

// 定义组件Props
const props = defineProps({
  // 配置ID
  configId: {
    type: String,
    required: true
  },
  // 路径配置对象
  pathConfig: {
    type: Object,
    default: () => ({})
  }
});

// 定义页面状态
const loading = ref(true);
const error = ref(false);
const errorMessage = ref('');
const route = useRoute();
const router = useRouter();

// 生成配置ID和缓存键
const configId = computed(() => {
  // 优先使用props中的configId
  if (props.configId) {
    return props.configId;
  }
  
  // 其次使用路由参数
  if (route.params.configId) {
    return String(route.params.configId);
  } 
  
  // 最后使用路径作为配置ID
  return route.path.replace(/\//g, '_');
});

// 获取路径配置信息
const pathConfig = computed(() => {
  return props.pathConfig || {};
});

// 生成本地存储的缓存键
const cacheKey = computed(() => {
  // 使用configId和versionHash组合作为缓存键，确保版本更新时自动失效
  const versionPart = pathConfig.value.versionHash ? `-${pathConfig.value.versionHash}` : '';
  return `config_${configId.value}${versionPart}`;
});

// 定义配置类型
interface PageConfig {
  // 版本信息
  version: string;
  // 页面标题
  title: string;
  // 服务配置
  serviceConfig: ServiceConfig;
  // 表格配置
  tableConfig?: TableConfig;
  // 表单配置
  formConfig?: FormConfig;
  // 搜索配置
  searchConfig?: SearchConfig;
  // 工具栏配置
  toolbarConfig?: {
    buttons: Array<{
      type?: string;
      text: string;
      icon?: string;
      action: string;
      disabled?: boolean;
    }>;
  };
  // 详情配置
  infoConfig?: {
    column?: number;
    size?: string;
    direction?: string;
    columns: CustomPlusColumn[];
    border?: boolean;
    width?: string | number;
  };
  // 扩展配置
  extends?: Record<string, any>;
}

// 页面配置状态
const pageConfig = reactive<PageConfig>({
  version: '1.0.0',
  title: '',
  serviceConfig: {
    baseUrl: '/api',
    formatResponse: (response: any) => {
      // 默认格式化响应结果
      if (!response) return { list: [], total: 0 };
      if (response.list !== undefined && response.total !== undefined) {
        return response;
      }
      if (Array.isArray(response)) {
        return { list: response, total: response.length };
      }
      if (response.data && Array.isArray(response.data)) {
        return { list: response.data, total: response.data.length };
      }
      return { list: [], total: 0 };
    },
    messages: {
      addSuccess: '创建成功',
      editSuccess: '更新成功',
      deleteConfirm: '确定要删除吗？',
      deleteSuccess: '删除成功'
    }
  }
});

// 默认页面配置，在无法获取远程配置时使用
const defaultPageConfig: PageConfig = {
  version: '1.0.0',
  title: pathConfig.value.title || '动态配置页面',
  serviceConfig: {
    baseUrl: `/api/${configId.value}`,
    messages: {
      addSuccess: '创建成功',
      editSuccess: '更新成功',
      deleteConfirm: '确定要删除吗？',
      deleteSuccess: '删除成功'
    }
  },
  tableConfig: {
    columns: [
      { label: 'ID', prop: 'id', width: 80 },
      { label: '名称', prop: 'name', width: 150 },
      { label: '描述', prop: 'description', ellipsis: true }
    ],
    rowKey: 'id',
    showSelection: true,
    showIndex: true,
    actions: {
      width: 200,
      label: '操作',
      showView: true,
      showEdit: true,
      showDelete: true,
      buttons: []
    }
  },
  formConfig: {
    columns: [
      { 
        label: '名称', 
        prop: 'name',
        rules: [{ required: true, message: '请输入名称' }] 
      },
      { 
        label: '描述', 
        prop: 'description',
        valueType: 'textarea'
      }
    ],
    span: 24,
    labelWidth: 100
  },
  searchConfig: {
    columns: [
      { label: '名称', prop: 'name' }
    ]
  },
  toolbarConfig: {
    buttons: [
      { type: 'primary', text: '新建', action: 'create', icon: 'plus' }
    ]
  },
  infoConfig: {
    columns: [
      { label: 'ID', prop: 'id' },
      { label: '名称', prop: 'name' },
      { label: '描述', prop: 'description' }
    ],
    column: 2,
    border: true
  }
};

/**
 * 从本地存储加载配置
 * 使用cacheKey作为存储键，将path配置也纳入缓存策略
 */
async function loadLocalConfig() {
  try {
    console.log(`尝试从本地存储加载配置: ${cacheKey.value}`);
    const data = await localforage.getItem(cacheKey.value);
    
    if (data) {
      console.log(`从本地存储加载配置成功:`, data);
      return data as PageConfig;
    } else {
      console.log(`本地存储中不存在配置: ${cacheKey.value}`);
      return null;
    }
  } catch (error) {
    console.error(`从本地存储加载配置失败:`, error);
    return null;
  }
}

/**
 * 保存配置到本地存储
 * @param config 页面配置
 */
async function saveLocalConfig(config: PageConfig) {
  try {
    // 将pathConfig信息合并到配置中，方便后续使用
    const enrichedConfig = {
      ...config,
      _pathConfig: pathConfig.value,
      _lastUpdated: new Date().toISOString()
    };
    
    console.log(`保存配置到本地存储: ${cacheKey.value}`, enrichedConfig);
    await localforage.setItem(cacheKey.value, enrichedConfig);
    return true;
  } catch (error) {
    console.error(`保存配置到本地存储失败:`, error);
    return false;
  }
}

/**
 * 从后端API获取配置
 * 
 * 流程：
 * 1. 尝试从本地缓存加载配置
 * 2. 发送请求到后端API，查询指定configId的配置
 * 3. 解析后端返回的配置内容
 * 4. 保存配置到本地缓存
 * 5. 返回配置数据
 */
async function fetchRemoteConfig() {
  try {
    // 获取本地存储的配置版本
    const localConfig = await loadLocalConfig();
    const localVersion = localConfig?.version || '0.0.0';
    
    console.log(`开始获取页面配置，ID: ${configId.value}, 本地版本: ${localVersion}`);

    // 使用pathConfig中的数据增强API调用
    const queryParams = {
      configId: configId.value,
      configKey: pathConfig.value.configKey,
      configType: pathConfig.value.configType,
      group: pathConfig.value.group,
      versionHash: pathConfig.value.versionHash
    };
    
    // 请求后端获取配置
    const response: any = await getConfigById(configId.value, queryParams);
    
    // 判断是否有配置数据
    if (response && response.list && response.list.length > 0) {
      const configData = response.list[0];
      console.log('获取到页面配置数据:', configData);
      
      // 解析配置内容
      if (configData.config_content) {
        let parsedConfig;
        try {
          parsedConfig = typeof configData.config_content === 'string' 
            ? JSON.parse(configData.config_content)
            : configData.config_content;
          
          console.log('解析配置内容成功 get:', parsedConfig);
        } catch (e) {
          console.error('解析配置内容失败:', e);
          throw new Error('配置格式错误');
        }
        
        // 使用pathConfig中的数据增强配置
        const enhancedConfig = {
          ...parsedConfig,
          title: parsedConfig.title || pathConfig.value.title || configId.value,
          version: configData.version || '1.0.0',
          //icon: configData.icon || pathConfig.value.icon || 'icon-default',
          serviceConfig: {
            ...parsedConfig.serviceConfig,
            // 如果serviceConfig中没有baseUrl，则使用configId构建
            baseUrl: parsedConfig.serviceConfig?.baseUrl || `/api/${configId.value}`
          }
        };
        //console.log('enhancedConfig:', enhancedConfig);
        // 确保类型兼容性
        if (enhancedConfig.tableConfig) {
          
          // 确保tableConfig.actions的格式正确
          if (Array.isArray(enhancedConfig.tableConfig.actions)) {
            // 如果actions是数组，转换为对象格式
            const actionButtons = enhancedConfig.tableConfig.actions;
            enhancedConfig.tableConfig.actions = {
              width: 200,
              label: '操作',
              showView: true,
              showEdit: true,
              showDelete: true,
              buttons: actionButtons.map((action: any) => ({
                text: action.label || action.text || '',
                action: action.type || action.action || '',
                type: action.danger ? 'danger' : 'primary'
              }))
            };
          }
          
          // 确保columns格式正确
          if (enhancedConfig.tableConfig.columns) {
            enhancedConfig.tableConfig.columns = enhancedConfig.tableConfig.columns.map((col: any) => ({
              ...col,
              label: col.title || col.label,
              prop: col.dataIndex || col.field || col.prop
            }));
          }
          
          // 重命名showIndexColumn为showIndex
          if ('showIndexColumn' in enhancedConfig.tableConfig) {
            enhancedConfig.tableConfig.showIndex = enhancedConfig.tableConfig.showIndexColumn;
            delete enhancedConfig.tableConfig.showIndexColumn;
          }
        }
        
        // 处理搜索配置
        if (enhancedConfig.searchConfig) {
          // 将items转换为columns
          if (enhancedConfig.searchConfig.items && !enhancedConfig.searchConfig.columns) {
            enhancedConfig.searchConfig.columns = enhancedConfig.searchConfig.items.map((item: any) => ({
              label: item.label || '',
              prop: item.field || item.prop || ''
            }));
            delete enhancedConfig.searchConfig.items;
          }
          
          // 将cols转换为span
          if ('cols' in enhancedConfig.searchConfig && !('span' in enhancedConfig.searchConfig)) {
            enhancedConfig.searchConfig.span = enhancedConfig.searchConfig.cols;
            delete enhancedConfig.searchConfig.cols;
          }
        }
        
        // 处理表单配置
        if (enhancedConfig.formConfig) {
          // 将items转换为columns
          if (enhancedConfig.formConfig.items && !enhancedConfig.formConfig.columns) {
            enhancedConfig.formConfig.columns = enhancedConfig.formConfig.items.map((item: any) => ({
              label: item.label || '',
              prop: item.field || item.prop || '',
              valueType: item.type
            }));
            delete enhancedConfig.formConfig.items;
          }
          
          // 将cols转换为span
          if ('cols' in enhancedConfig.formConfig && !('span' in enhancedConfig.formConfig)) {
            enhancedConfig.formConfig.span = enhancedConfig.formConfig.cols;
            delete enhancedConfig.formConfig.cols;
          }
        }
        
        // 处理工具栏配置
        if (enhancedConfig.toolbarConfig) {
          // 将actions转换为buttons
          if (enhancedConfig.toolbarConfig.actions && !enhancedConfig.toolbarConfig.buttons) {
            enhancedConfig.toolbarConfig.buttons = enhancedConfig.toolbarConfig.actions.map((action: any) => ({
              text: action.label || action.text || '',
              action: action.type || action.action || '',
              type: action.type || 'primary',
              icon: action.icon
            }));
            delete enhancedConfig.toolbarConfig.actions;
          }
          
          // 处理showCreateAction
          if (enhancedConfig.toolbarConfig.showCreateAction && (!enhancedConfig.toolbarConfig.buttons || enhancedConfig.toolbarConfig.buttons.length === 0)) {
            enhancedConfig.toolbarConfig.buttons = [
              {
                type: 'primary',
                text: enhancedConfig.toolbarConfig.createButtonText || '新建',
                action: 'create',
                icon: 'plus'
              }
            ];
          }
        }
        
        // 确保infoConfig有columns属性
        if (enhancedConfig.infoConfig && (!enhancedConfig.infoConfig.columns || enhancedConfig.infoConfig.columns.length === 0)) {
          enhancedConfig.infoConfig.columns = enhancedConfig.tableConfig?.columns?.map((col: any) => ({
            ...col,
            label: col.label || '',
            prop: col.prop || ''
          })) || [];
        }
        
        // 更新本地存储
        await saveLocalConfig(enhancedConfig);
        console.log(`配置已保存到本地存储，版本: ${enhancedConfig.version}`);
        
        return enhancedConfig;
      }
    }
    
    // 如果没有获取到配置数据，但有本地配置，则使用本地配置
    if (localConfig) {
      //console.log('未获取到新配置，使用本地缓存的配置');
      return localConfig;
    }
    
    // 如果没有本地配置，则使用pathConfig构建一个基础配置
    //console.log('未获取到配置，使用pathConfig构建基础配置');
    const baseConfig = {
      ...defaultPageConfig,
      title: pathConfig.value.title || defaultPageConfig.title,
      serviceConfig: {
        ...defaultPageConfig.serviceConfig,
        baseUrl: `/api/${configId.value}`
      }
    };
    
    await saveLocalConfig(baseConfig);
    return baseConfig;
  } catch (error) {
    console.error('获取页面配置失败:', error);
    
    // 尝试使用本地配置恢复
    const localConfig = await loadLocalConfig();
    if (localConfig) {
      //console.log('使用本地缓存的配置恢复');
      return localConfig;
    }
    
    throw error;
  }
}

/**
 * 初始化页面配置
 * 
 * 流程：
 * 1. 重置页面状态
 * 2. 尝试从后端获取配置
 * 3. 更新页面配置
 * 4. 处理可能的错误
 */
async function initPageConfig() {
  loading.value = true;
  error.value = false;
  errorMessage.value = '';
  
  try {
    console.log(`开始初始化页面配置: ${configId.value}，路径配置:`, pathConfig.value);
    
    // 从后端获取配置
    const config = await fetchRemoteConfig();
    
    // 更新页面配置
    Object.assign(pageConfig, config);
    
    console.log('页面配置初始化完成:', pageConfig);
  } catch (err: any) {
    console.error('初始化页面配置失败:', err);
    error.value = true;
    errorMessage.value = err.message || '加载配置失败，请重试';
  } finally {
    // 无论是否成功，都结束加载状态
    loading.value = false;
  }
}

/**
 * 使用默认配置
 */
function useDefaultConfig() {
  // 重置错误状态
  error.value = false;
  errorMessage.value = '';
  
  // 使用默认配置
  Object.assign(pageConfig, defaultPageConfig);
  
  // 保存到本地存储
  saveLocalConfig(defaultPageConfig);
}

/**
 * 重新加载配置
 */
function reloadConfig() {
  initPageConfig();
}

/**
 * 处理工具栏操作
 */
function handleToolbarAction(action: string) {
  console.log('工具栏操作:', action);
}

/**
 * 处理行操作
 */
function handleRowAction(action: string, row: any) {
  console.log('行操作:', action, row);
}

/**
 * 处理搜索
 */
function handleSearch(params: any) {
  console.log('搜索参数:', params);
}

/**
 * 处理上传成功
 */
function handleUploadSuccess(url: string, field: string, response: any) {
  console.log('上传成功:', { url, field, response });
}

// 监听路由参数变化，重新加载配置
watch(() => route.params.configId, (newId, oldId) => {
  if (newId !== oldId) {
    console.log('配置ID变更，重新加载配置:', newId);
    initPageConfig();
  }
});

// 监听路由props变化
watch(() => props.configId, (newId, oldId) => {
  if (newId !== oldId) {
    console.log('Props配置ID变更，重新加载配置:', newId);
    initPageConfig();
  }
});

// 组件挂载时初始化配置
onMounted(() => {
  initPageConfig();
});
</script>

<style scoped>
.dynamic-config-page {
  height: 100%;
  padding: 16px;
  background-color: #f5f7fa;
}
</style>