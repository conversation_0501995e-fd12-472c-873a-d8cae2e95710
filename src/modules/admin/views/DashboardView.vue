<template>
  <div class="dashboard-view">
    <div class="stat-cards">
      <el-card class="stat-card">
        <template #header>
          <div class="card-header">
            <span>用户统计</span>
            <el-icon><User /></el-icon>
          </div>
        </template>
        <div class="stat-value">{{ statistics.userCount }}</div>
        <div class="stat-label">总用户数</div>
      </el-card>
      
      <el-card class="stat-card">
        <template #header>
          <div class="card-header">
            <span>商家统计</span>
            <el-icon><Shop /></el-icon>
          </div>
        </template>
        <div class="stat-value">{{ statistics.merchantCount }}</div>
        <div class="stat-label">总商家数</div>
      </el-card>
      
      <el-card class="stat-card">
        <template #header>
          <div class="card-header">
            <span>商品统计</span>
            <el-icon><Goods /></el-icon>
          </div>
        </template>
        <div class="stat-value">{{ statistics.productCount }}</div>
        <div class="stat-label">总商品数</div>
      </el-card>
      
      <el-card class="stat-card">
        <template #header>
          <div class="card-header">
            <span>订单统计</span>
            <el-icon><List /></el-icon>
          </div>
        </template>
        <div class="stat-value">{{ statistics.orderCount }}</div>
        <div class="stat-label">总订单数</div>
      </el-card>
    </div>
    
    <div class="today-stats">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>今日数据</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="today-stat-item">
              <div class="today-stat-label">今日订单数</div>
              <div class="today-stat-value">{{ statistics.todayOrderCount }}</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="today-stat-item">
              <div class="today-stat-label">今日销售额</div>
              <div class="today-stat-value price">¥{{ statistics.todaySalesAmount.toFixed(2) }}</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
    
    <div class="quick-actions">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>快捷操作</span>
          </div>
        </template>
        <div class="action-buttons">
          <el-button type="primary" @click="router.push('/admin/users')">
            <el-icon><User /></el-icon>
            用户管理
          </el-button>
          <el-button type="success" @click="router.push('/admin/merchants')">
            <el-icon><Shop /></el-icon>
            商家管理
          </el-button>
          <el-button type="warning" @click="router.push('/admin/products')">
            <el-icon><Goods /></el-icon>
            商品管理
          </el-button>
          <el-button type="danger" @click="router.push('/admin/orders')">
            <el-icon><List /></el-icon>
            订单管理
          </el-button>
        </div>
      </el-card>
    </div>
    
    <div class="pending-items">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>待处理事项</span>
          </div>
        </template>
        <el-tabs>
          <el-tab-pane label="待审核商家">
            <div v-if="pendingMerchants.length > 0">
              <el-table :data="pendingMerchants" style="width: 100%">
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="name" label="商家名称" />
                <el-table-column prop="contact_name" label="联系人" />
                <el-table-column prop="contact_mobile" label="联系电话" />
                <el-table-column prop="created_at" label="申请时间" >
                  <template #default="{ row }">
                    {{ formatTime(row.created_at) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                  <template #default="{ row }">
                    <el-button type="primary" size="small" @click="router.push('/admin/merchants/'+row.id)">
                      去审核
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <el-empty v-else description="暂无待审核商家" />
          </el-tab-pane>
          
          <el-tab-pane label="待审核商品">
            <div v-if="pendingProducts.length > 0">
              <el-table :data="pendingProducts" style="width: 100%">
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="name" label="商品名称" />
                <el-table-column prop="merchantName" label="所属商家" />
                <el-table-column prop="created_at" label="申请时间" >
                  <template #default="{ row }">
                    {{ formatTime(row.created_at) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                  <template #default="{ row }">
                    <el-button type="primary" size="small" @click="router.push('/admin/product/'+row.id)">
                      去审核
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <el-empty v-else description="暂无待审核商品" />
          </el-tab-pane>
          
          <el-tab-pane label="待发货订单">
            <div v-if="pendingShipOrders.length > 0">
              <el-table :data="pendingShipOrders" style="width: 100%">
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="order_no" label="订单号" />
                <el-table-column prop="username" label="用户" />
                <el-table-column prop="merchantName" label="商家" />
                <el-table-column label="金额">
                  <template #default="{ row }">
                    <span class="price">¥{{ row.total_amount.toFixed(2) }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="created_at" label="创建时间" >
                  <template #default="{ row }">
                    {{ formatTime(row.created_at) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                  <template #default="{ row }">
                    <el-button type="primary" size="small" @click="router.push('/admin/order/'+row.id)">
                      去处理
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <el-empty v-else description="暂无待发货订单" />
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { User, Shop, Goods, List } from '@element-plus/icons-vue';
import { getStatistics, getMerchantList, getProductList, getOrderList } from '../api';
import type { Merchant, Product, Order } from '../types';
import { ElMessage } from 'element-plus';
import { formatTime } from '@/utils/format';
import { useAdminStore } from '../stores/adminStore';
import { useRouter } from 'vue-router';
import { registerDynamicRoutes, initializeRoutes } from '@/router';

const router = useRouter();
const adminStore = useAdminStore();

const statistics = ref({
  userCount: 0,
  merchantCount: 0,
  productCount: 0,
  orderCount: 0,
  todayOrderCount: 0,
  todaySalesAmount: 0,
});

const pendingMerchants = ref<Merchant[]>([]);
const pendingProducts = ref<Product[]>([]);
const pendingShipOrders = ref<Order[]>([]);
const isRoutesLoading = ref(false);

// 预加载并注册路由
const preloadAndRegisterRoutes = async () => {
  isRoutesLoading.value = true;
  try {
    console.log('DashboardView: 开始预加载路由...');
    
    // 确保路由初始化
    await initializeRoutes();
    
    // 强制刷新前端路径数据并注册路由
    const paths = await adminStore.fetchFrontendPaths(true);
    
    if (paths && paths.length > 0) {
      console.log('DashboardView: 获取到前端路径数据，开始注册路由');
      await registerDynamicRoutes(paths);
      console.log('DashboardView: 路由注册完成');
    } else {
      console.warn('DashboardView: 未获取到前端路径数据');
    }
  } catch (error) {
    console.error('DashboardView: 预加载路由失败', error);
    ElMessage.warning('部分功能可能无法正常访问，请刷新页面重试');
  } finally {
    isRoutesLoading.value = false;
  }
};

// 获取统计数据
const fetchStatistics = async () => {
  try {
    // 请求拦截器已直接返回data内容
    const response = await getStatistics();
    // 使用类型断言确保类型安全
    statistics.value = response as {
      userCount: number;
      merchantCount: number;
      productCount: number;
      orderCount: number;
      todayOrderCount: number;
      todaySalesAmount: number;
    };
    console.log('获取统计数据成功:', response);
  } catch (error) {
    console.error('获取统计数据失败', error);
  }
};

// 获取待审核商家
const fetchPendingMerchants = async () => {
  try {
    const response = await getMerchantList({
      page: 1,
      page_size: 5,
      audit_status: 0,
    });
    // 请求拦截器已直接返回data内容
    // 使用类型断言确保类型安全
    if (response && typeof response === 'object' && 'list' in response) {
      pendingMerchants.value = (response as { list: Merchant[] }).list ? (response as { list: Merchant[] }).list : [];
      console.log('获取待审核商家成功:', response);
    } else {
      pendingMerchants.value = [];
      console.warn('获取待审核商家：响应数据结构不符合预期', response);
    }
  } catch (error) {
    console.error('获取待审核商家失败', error);
  }
};

// 获取待审核商品
const fetchPendingProducts = async () => {
  try {
    const response = await getProductList({
      page: 1,
      pageSize: 5,
      audit_status: 0,
    });
    // 请求拦截器已直接返回data内容
    // 使用类型断言确保类型安全
    if (response && typeof response === 'object' && 'list' in response) {
      pendingProducts.value = (response as { list: Product[] }).list;
      console.log('获取待审核商品成功:', response);
    } else {
      console.warn('获取待审核商品：响应数据结构不符合预期', response);
    }
  } catch (error) {
    console.error('获取待审核商品失败', error);
  }
};

// 获取待发货订单
const fetchPendingShipOrders = async () => {
  try {
    const response = await getOrderList({
      page: 1,
      pageSize: 5,
      status: 'PAID',
    });
    // 请求拦截器已直接返回data内容
    // 使用类型断言确保类型安全
    if (response && typeof response === 'object' && 'list' in response) {
      pendingShipOrders.value = (response as { list: Order[] }).list;
      console.log('获取待发货订单成功:', response);
    } else {
      console.warn('获取待发货订单：响应数据结构不符合预期', response);
    }
  } catch (error) {
    console.error('获取待发货订单失败', error);
  }
};

onMounted(async () => {
  // 先预加载并注册路由
  await preloadAndRegisterRoutes();
  
  // 获取统计数据
  fetchStatistics();
  
  // 获取待处理事项
  fetchPendingMerchants();
  fetchPendingProducts();
  fetchPendingShipOrders();
});
</script>

<style scoped>
.dashboard-view {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.stat-card {
  text-align: center;
}

.stat-value {
  font-size: 36px;
  font-weight: bold;
  margin: 10px 0;
}

.stat-label {
  color: #909399;
}

.today-stat-item {
  text-align: center;
  padding: 10px;
}

.today-stat-label {
  color: #909399;
  margin-bottom: 5px;
}

.today-stat-value {
  font-size: 24px;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 10px;
}

.price {
  color: #f56c6c;
}
</style>