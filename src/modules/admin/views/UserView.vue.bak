<!-- 
  用户管理页面 
  使用BasePage组件实现CRUD功能
  通过JSON配置动态生成页面内容，支持本地存储和版本检查
-->
<template>
  <div class="user-view">
    <!-- 加载中状态显示 -->
    <el-skeleton :loading="loading" animated>
      <template #template>
        <div style="padding: 20px;">
          <el-skeleton-item variant="p" style="width: 100%; height: 60px; margin-bottom: 20px" />
          <div style="display: flex; justify-content: space-between; margin-bottom: 20px">
            <el-skeleton-item variant="button" style="width: 250px; height: 40px;" />
            <el-skeleton-item variant="button" style="width: 150px; height: 40px;" />
          </div>
          <el-skeleton-item variant="p" style="width: 100%; height: 400px" />
        </div>
      </template>
      
      <!-- 实际页面内容 -->
      <template #default>
        <BasePage
          :title="pageConfig.title || '用户管理'"
          :service-config="pageConfig.serviceConfig"
          :table-config="pageConfig.tableConfig"
          :form-config="pageConfig.formConfig"
          :search-config="pageConfig.searchConfig"
          :toolbar-config="pageConfig.toolbarConfig"
          :info-config="pageConfig.infoConfig"
          @toolbar-action="handleToolbarAction"
          @row-action="handleRowAction"
          @search="handleSearch"
          @upload-success="handleUploadSuccess"
        />
      </template>
    </el-skeleton>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed, h } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElEmpty } from 'element-plus';
import { BasePage } from '@/components/page';
//import UserService from '../service/userService';
import type { User, UserStatus } from '../types';
import type { TableValueType, FormItemValueType } from 'plus-pro-components';
import localforage from 'localforage';
import axios from 'axios';

// 定义页面状态
const loading = ref(true);
const route = useRoute();

// 定义配置类型
interface PageConfig {
  // 版本信息
  version: string;
  // 页面标题
  title: string;
  // 服务配置
  serviceConfig: {
    baseUrl: string;
    formatResponse?: (response: any) => any;
    customActions?: Record<string, any>;
    messages?: Record<string, string>;
    addTitle?: string;
    editTitle?: string;
    viewTitle?: string;
    // 其他服务配置...
  };
  // 表格配置
  tableConfig: {
    columns: Array<any>;
    actions?: Record<string, any>;
    showSelection?: boolean;
    showIndex?: boolean;
    showActions?: boolean;
    rowKey?: string;
    showPagination?: boolean;
    pagination?: {
      pageSize?: number;
      pageSizes?: number[];
    };
    // 其他表格配置...
  };
  // 表单配置
  formConfig: {
    columns: Array<any>;
    labelWidth?: number | string;
    labelPosition?: 'left' | 'right' | 'top';
    // 其他表单配置...
  };
  // 工具栏配置
  toolbarConfig: {
    buttons: Array<any>;
    // 其他工具栏配置...
  };
  // 搜索配置
  searchConfig?: {
    columns?: Array<any>;
    // 其他搜索配置...
  };
  // 详情查看配置
  infoConfig?: {
    column?: number;
    columns: Array<any>;
    border?: boolean;
    size?: string;
    direction?: string;
  };
}

// 默认页面配置，在无法获取远程配置时使用
const defaultPageConfig: PageConfig = {
  version: '1.0.0',
  title: '用户管理',
  serviceConfig: {
    baseUrl: 'v1/admin/secured/users',
    formatResponse: (response: any) => {
      return {
        data: response.list || [],
        page: response.page || 1,
        pageSize: response.pageSize || 10,
        total: response.total || 0
      };
    },
    customActions: {
      activate: {
        url: '/status',
        method: 'put',
        confirmMessage: '确定要激活该用户吗？',
        successMessage: '用户已激活',
        formatParams: (params: any) => {
          return {
            id: params.id,
            status: 'ACTIVE' as UserStatus
          };
        }
      },
      lock: {
        url: '/status',
        method: 'put',
        confirmMessage: '确定要锁定该用户吗？',
        successMessage: '用户已锁定',
        formatParams: (params: any) => {
          return {
            id: params.id,
            status: 'LOCKED' as UserStatus
          };
        }
      },
      disable: {
        url: '/status',
        method: 'put',
        confirmMessage: '确定要禁用该用户吗？',
        successMessage: '用户已禁用',
        formatParams: (params: any) => {
          return {
            id: params.id,
            status: 'DISABLED' as UserStatus
          };
        }
      }
    },
    messages: {
      addSuccess: '用户创建成功',
      updateSuccess: '用户更新成功',
      deleteConfirm: '确定要删除该用户吗？',
      deleteSuccess: '用户删除成功'
    },
    addTitle: '新增用户',
    editTitle: '编辑用户信息',
    viewTitle: '用户详情'
  },
  tableConfig: {
    columns: [
      { label: 'ID', prop: 'id', width: 80 },
      { label: '用户名', prop: 'username', width: 120 },
      { label: '昵称', prop: 'nickname', width: 120 },
      { 
        label: '头像', 
        prop: 'avatar', 
        width: 100,
        valueType: 'img' as TableValueType,
        fieldProps: {
          style: {
            width: '100%'
          }
        },
        fieldSlots: {
          error: () => h(ElEmpty, { description: '图片坏了' })
        }
      },
      { label: '手机号', prop: 'mobile', width: 120 },
      { label: '邮箱', prop: 'email', minWidth: 180 },
      { 
        label: '状态', 
        prop: 'status', 
        width: 100,
        valueType: 'tag' as TableValueType,
        valueEnum: {
          ACTIVE: { text: '正常', status: 'success' },
          LOCKED: { text: '锁定', status: 'warning' },
          DISABLED: { text: '禁用', status: 'danger' }
        }
      },
      { 
        label: '注册时间', 
        prop: 'created_at', 
        width: 180,
        valueType: 'dateTime' as TableValueType,
        hideInSearch: true // 隐藏在搜索表单中
      },
      { 
        label: '最后登录时间', 
        prop: 'last_login_at', 
        width: 180,
        valueType: 'dateTime' as TableValueType,
        hideInSearch: true // 隐藏在搜索表单中
      }
    ],
    actions: {
      title: '操作',
      width: 200,
      buttons: [
        {
          text: '激活',
          type: 'success' as const,
          action: 'activate',
          condition: (row: User) => row.status !== 'ACTIVE'
        },
        {
          text: '锁定',
          type: 'warning' as const,
          action: 'lock',
          condition: (row: User) => row.status === 'ACTIVE'
        },
        {
          text: '禁用',
          type: 'danger' as const,
          action: 'disable',
          condition: (row: User) => row.status !== 'DISABLED'
        },
        {
          text: '查看',
          type: 'info' as const,
          action: 'view',
          icon: 'View'
        },
        {
          text: '编辑',
          type: 'primary' as const,
          action: 'edit'
        }
      ]
    },
    pagination: {
      pageSize: 15,
      pageSizes: [10, 20, 30, 50]
    },
    showSelection: true,
    showIndex: true,
    showActions: true,
    rowKey: 'id',
    showPagination: true
  },
  formConfig: {
    columns: [
      {
        label: '用户名',
        prop: 'username',
        required: true,
        rules: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        fieldProps: {
          placeholder: '请输入用户名'
        }
      },
      {
        label: '密码',
        prop: 'password',
        required: false,
        valueType: 'password' as FormItemValueType,
        fieldProps: {
          placeholder: '请输入密码',
          showPassword: true
        },
        rules: [
          { required: false, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
        ]
      },
      {
        label: '昵称',
        prop: 'nickname',
        fieldProps: {
          placeholder: '请输入昵称'
        }
      },
      {
        label: '手机号',
        prop: 'mobile',
        fieldProps: {
          placeholder: '请输入手机号'
        },
        rules: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ]
      },
      {
        label: '邮箱',
        prop: 'email',
        fieldProps: {
          placeholder: '请输入邮箱'
        },
        rules: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      {
        label: '头像',
        prop: 'avatar',
        valueType: 'upload' as FormItemValueType,
        uploadProps: {
          action: '/v1/admin/upload',
          accept: '.jpg,.jpeg,.png,.gif',
          fileLimit: 1,
          sizeLimit: 2 * 1024 * 1024,
          fileUsage: 'avatar',
          tip: '点击或拖拽图片到此区域上传头像'
        }
      },
      {
        label: '性别',
        prop: 'gender',
        valueType: 'radio' as FormItemValueType,
        options: [
          { label: '男', value: 1 },
          { label: '女', value: 2 },
          { label: '保密', value: 0 }
        ],
        defaultValue: 1
      },
      {
        label: '状态',
        prop: 'status',
        valueType: 'radio' as FormItemValueType,
        options: [
          { label: '正常', value: 'ACTIVE' },
          { label: '锁定', value: 'LOCKED' },
          { label: '禁用', value: 'DISABLED' }
        ],
        defaultValue: 'ACTIVE'
      }
    ],
    labelWidth: 80,
    labelPosition: 'right' as const
  },
  toolbarConfig: {
    buttons: [
      {
        text: '新增用户',
        type: 'primary' as const,
        icon: 'Plus',
        action: 'add'
      },
      {
        text: '刷新',
        icon: 'Refresh',
        action: 'refresh'
      }
    ]
  },
  searchConfig: {
    columns: [
      { label: '用户名', prop: 'username' },
      { label: '昵称', prop: 'nickname' },
      { label: '手机号', prop: 'mobile' },
      { label: '邮箱', prop: 'email' },
      { label: '状态', prop: 'status' }
    ]
  },
  infoConfig: {
    columns: [
      { label: 'ID', prop: 'id' },
      { label: '用户名', prop: 'username' },
      { label: '昵称', prop: 'nickname' },
      { 
        label: '头像', 
        prop: 'avatar',
        renderDescriptionsItem: ({ value }: { value: string }) => {
          if (!value) return '无头像';
          return h('el-image', { 
            src: value, 
            style: { width: '100px', height: '100px' },
            previewSrcList: [value]
          });
        }
      },
      { label: '手机号', prop: 'mobile' },
      { label: '邮箱', prop: 'email' },
      { 
        label: '状态', 
        prop: 'status',
        renderDescriptionsItem: ({ value }: { value: string }) => {
          const statusMap: Record<string, { text: string, type: string }> = {
            ACTIVE: { text: '正常', type: 'success' },
            LOCKED: { text: '锁定', type: 'warning' },
            DISABLED: { text: '禁用', type: 'danger' }
          };
          const status = statusMap[value] || { text: value, type: 'info' };
          return h('el-tag', { type: status.type }, { default: () => status.text });
        }
      },
      { label: '最后登录IP', prop: 'last_login_ip' },
      { label: '注册时间', prop: 'created_at' },
      { label: '最后登录时间', prop: 'last_login_at' },
      { label: '更新时间', prop: 'updated_at' }
    ],
    border: true
  }
};

// 存储最终使用的页面配置
const pageConfig = reactive<PageConfig>({...defaultPageConfig});

/**
 * 生成页面配置存储键
 * @returns 唯一的存储键
 */
const getStorageKey = computed(() => {
  return `page_config_${route.path}`;
});

/**
 * 从本地存储加载配置
 */
async function loadLocalConfig() {
  try {
    const localConfig = await localforage.getItem<{version: string, config: PageConfig}>(getStorageKey.value);
    
    if (localConfig) {
      console.log('从本地存储加载配置:', localConfig);
      return localConfig;
    }
  } catch (error) {
    console.error('从本地存储加载配置失败:', error);
  }
  return null;
}

/**
 * 保存配置到本地存储
 * @param config 页面配置
 */
async function saveLocalConfig(config: PageConfig) {
  try {
    await localforage.setItem(getStorageKey.value, {
      version: config.version,
      config: config,
      updated: new Date().toISOString()
    });
    console.log('配置已保存到本地存储');
  } catch (error) {
    console.error('保存配置到本地存储失败:', error);
  }
}

/**
 * 从后端API获取最新配置
 */
async function fetchRemoteConfig() {
  try {
    // 获取本地存储的配置版本
    const localConfig = await loadLocalConfig();
    const localVersion = localConfig?.version || '0.0.0';
    
    // 请求后端检查配置版本并获取最新配置
    const response = await axios.get('/api/page-config', {
      params: {
        path: route.path,
        version: localVersion
      }
    });
    
    // 后端返回的配置数据
    const { hasChanged, config, version } = response.data;
    
    if (hasChanged && config) {
      // 有新版本配置
      console.log('从后端获取到新配置，版本:', version);
      
      // 合并配置
      const newConfig = {
        ...config,
        version 
      };
      
      // 更新本地存储
      await saveLocalConfig(newConfig);
      
      // 返回新配置
      return newConfig;
    } else if (localConfig) {
      // 无变化，使用本地配置
      console.log('使用本地配置，版本:', localVersion);
      return localConfig.config;
    } else {
      // 无配置，返回默认配置
      console.log('使用默认配置');
      return defaultPageConfig;
    } 
  } catch (error) {
    console.error('获取远程配置失败:', error);
    // 失败时返回本地配置
    const localConfig = await loadLocalConfig();
    if (localConfig) {
      return localConfig.config;
    }
  }
  
  // 兜底使用默认配置
  return defaultPageConfig;
}

/**
 * 初始化页面配置
 */
async function initPageConfig() {
  try {
    loading.value = true;
    
    // 尝试获取最新配置
    const config = await fetchRemoteConfig();
    
    // 更新页面配置
    Object.assign(pageConfig, config);
    
    console.log('页面配置初始化完成', pageConfig);
  } catch (error) {
    console.error('初始化页面配置失败:', error);
    ElMessage.error('加载页面配置失败，使用默认配置');
  } finally {
    loading.value = false;
  }
}

// 处理工具栏操作
const handleToolbarAction = (action: string) => {
  console.log('工具栏操作:', action);
};

// 处理行操作
const handleRowAction = (action: string, row: any) => {
  console.log('行操作:', action, row);
};

// 处理搜索
const handleSearch = (params: any) => {
  console.log('搜索参数:', params);
};

// 处理上传成功
const handleUploadSuccess = (url: string, field: string, response: any) => {
  console.log('上传成功:', url, field, response);
};

// 页面加载
onMounted(async () => {
  console.log('用户管理页面加载中...');
  await initPageConfig();
  console.log('用户管理页面已加载完成');
});
</script>

<style lang="scss" scoped>
.user-view {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  
  /* 加载占位符样式 */
  .el-skeleton {
    width: 100%;
  }
}
</style>