<template>
  <div class="role-management-view">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>角色管理</span>
          <div class="action-box">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入角色名称/编码"
              clearable
              @keyup.enter="handleSearch"
              class="search-input"
            >
              <template #append>
                <el-button @click="handleSearch">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon> 新增角色
            </el-button>
            <el-button type="warning" @click="handleBatchStatusChange('DISABLED')" :disabled="selectedRoles.length === 0">
              批量禁用
            </el-button>
            <el-button type="success" @click="handleBatchStatusChange('ENABLED')" :disabled="selectedRoles.length === 0">
              批量启用
            </el-button>
            <!-- <el-button type="danger" @click="handleDeleteSelected" :disabled="selectedRoles.length === 0">
              批量删除
            </el-button> -->
            <el-button type="info" @click="exportRoles">
              导出角色列表
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="roleList"
        style="width: 100%"
        border
        stripe
        :max-height="tableMaxHeight"
        highlight-current-row
        table-layout="auto"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="70" align="center" />
        <el-table-column prop="name" label="角色名称" width="150" show-overflow-tooltip />
        <el-table-column prop="code" label="角色编码" width="150" show-overflow-tooltip />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 'ENABLED' ? 'success' : 'danger'" size="small">
              {{ row.status === 'ENABLED' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" show-overflow-tooltip>
          <template #default="{ row }">
            {{ (row.created_at) ? formatTime(row.created_at) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="180">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                v-if="row.status === 'ENABLED'"
                type="warning"
                size="small"
                @click="handleStatusChange(row.id, 'DISABLED')"
              >
                禁用
              </el-button>
              <el-button
                v-else
                type="success"
                size="small"
                @click="handleStatusChange(row.id, 'ENABLED')"
              >
                启用
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="handleAssignPermissions(row)"
              >
                分配权限
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑角色对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑角色' : '新增角色'"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="formData.name" />
        </el-form-item>
        <el-form-item label="角色编码" prop="code">
          <el-input v-model="formData.code" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input v-model="formData.description" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 分配权限对话框 -->
    <el-dialog
      v-model="permissionDialogVisible"
      title="分配权限"
      width="600px"
    >
      <div v-if="currentRole">
        <p class="role-info">当前角色: {{ currentRole.name }} ({{ currentRole.code }})</p>
        
        <el-form>
          <el-form-item label="权限选择">
            <div class="permission-tree-container">
              <el-input 
                v-model="permissionSearchKeyword" 
                placeholder="搜索权限" 
                clearable 
                class="permission-search"
              />
              <el-checkbox
                v-model="checkAll"
                :indeterminate="isIndeterminate"
                @change="handleCheckAllChange"
              >
                全选
              </el-checkbox>
              <div class="permission-list">
                <el-checkbox-group v-model="selectedPermissions" @change="handlePermissionChange">
                  <div v-for="(group, type) in groupedPermissions" :key="type" class="permission-group">
                    <div class="permission-group-title">{{ type }}</div>
                    <el-checkbox
                      v-for="permission in filterPermissions(group)"
                      :key="permission.id"
                      :label="permission.id"
                      :disabled="permission.status === 'DISABLED'"
                    >
                      <el-tooltip :content="permission.description || ''" placement="top" :disabled="!permission.description">
                        <span>{{ permission.name }} ({{ permission.code }})</span>
                      </el-tooltip>
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPermissions">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * @file RoleManagementView.vue
 * @description 角色管理页面
 */
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';
import { Search, Plus } from '@element-plus/icons-vue';
import { RoleService, PermissionService } from '../service';
import { formatTime } from '@/utils/format';
import type { Role, Permission, PermissionListParams } from '../types';

// 批量操作
const selectedRoles = ref<number[]>([]);
const tableMaxHeight = ref(500);

// 表格数据
const roleList = ref<Role[]>([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchKeyword = ref('');

// 表单数据
const dialogVisible = ref(false);
const isEdit = ref(false);
const formRef = ref<FormInstance>();
const formData = reactive<Partial<Role>>({
  id: undefined,
  name: '',
  code: '',
  description: '',
  status: 'ENABLED'
});
// 添加加载动画
//const loading = ref(false);

// 提示信息
const showSuccessMessage = (message: string) => {
  ElMessage.success(message);
};

const showErrorMessage = (message: string) => {
  ElMessage.error(message);
};

// // 示例：在获取角色列表时显示加载动画
// const fetchRoleList = async () => {
//   loading.value = true;
//   try {
//     const res: any = await RoleService.getList({
//       page: currentPage.value,
//       pageSize: pageSize.value,
//       name: searchKeyword.value || undefined,
//       code: searchKeyword.value || undefined
//     });
//     roleList.value = (res.data?.list || []) as Role[];
//     total.value = res.data?.total || 0;
//     showSuccessMessage('获取角色列表成功');
//   } catch (error) {
//     console.error("获取角色列表失败", error);
//     showErrorMessage('获取角色列表失败');
//   } finally {
//     loading.value = false;
//   }
// };

// 表单验证规则
const formRules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { pattern: /^[A-Z_]+$/, message: '角色编码只能包含大写字母和下划线', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
});

// 权限分配相关数据
const permissionDialogVisible = ref(false);
const permissionList = ref<Permission[]>([]);
const permissionLoading = ref(false);
const currentRole = ref<Role | null>(null);
const selectedPermissions = ref<number[]>([]);
const permissionSearchKeyword = ref('');
const checkAll = ref(false);
const isIndeterminate = ref(false);

/**
 * @description 获取角色列表，支持分页和搜索
 * @returns {Promise<void>}
 */
const fetchRoleList = async () => {
  loading.value = true;
  try {
    const res: any = await RoleService.getList({
      page: currentPage.value,
      pageSize: pageSize.value,
      name: searchKeyword.value || undefined,
      code: searchKeyword.value || undefined
    });
    roleList.value = (res.data?.list || []) as Role[];
    total.value = res.data?.total || 0;
  } catch (error) {
    console.error("获取角色列表失败", error);
    ElMessage.error('获取角色列表失败');
  } finally {
    loading.value = false;
  }
};

/**
 * @description 获取所有权限
 * @returns {Promise<void>}
 */
const fetchAllPermissions = async () => {
  permissionLoading.value = true;
  try {
    const params: PermissionListParams = {
      page: 1,
      pageSize: 1000, // 获取所有权限
      status: 'ENABLED'
    };
    const res: any = await PermissionService.getList(params);
    permissionList.value = (res.data?.list || []) as Permission[];
  } catch (error) {
    console.error("获取权限列表失败", error);
    ElMessage.error('获取权限列表失败');
  } finally {
    permissionLoading.value = false;
  }
};

/**
 * @description 按类型分组权限
 */
const groupedPermissions = computed(() => {
  const groups: Record<string, Permission[]> = {};
  
  permissionList.value.forEach(permission => {
    if (!groups[permission.type]) {
      groups[permission.type] = [];
    }
    groups[permission.type].push(permission);
  });
  
  return groups;
});

/**
 * @description 过滤权限 - 根据搜索关键词
 */
const filterPermissions = (permissions: Permission[]) => {
  if (!permissionSearchKeyword.value) return permissions;
  
  return permissions.filter(permission => 
    permission.name.includes(permissionSearchKeyword.value) || 
    permission.code.includes(permissionSearchKeyword.value) ||
    (permission.description && permission.description.includes(permissionSearchKeyword.value))
  );
};

/**
 * @description 处理全选变化
 */
const handleCheckAllChange = (value: boolean) => {
  const enabledPermissions = permissionList.value
    .filter(p => p.status === 'ENABLED')
    .map(p => p.id);
  
  selectedPermissions.value = value ? enabledPermissions : [];
  isIndeterminate.value = false;
};

/**
 * @description 处理权限选择变化
 */
const handlePermissionChange = (value: number[]) => {
  const enabledPermissionsCount = permissionList.value.filter(p => p.status === 'ENABLED').length;
  checkAll.value = value.length === enabledPermissionsCount && enabledPermissionsCount > 0;
  isIndeterminate.value = value.length > 0 && value.length < enabledPermissionsCount;
};

/**
 * @description 搜索角色
 */
const handleSearch = () => {
  currentPage.value = 1;
  fetchRoleList();
};

/**
 * @description 每页数量改变时触发
 * @param {number} size - 每页数量
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  fetchRoleList();
};

/**
 * @description 当前页码改变时触发
 * @param {number} page - 当前页码
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  fetchRoleList();
};

/**
 * @description 新增角色
 */
const handleAdd = () => {
  isEdit.value = false;
  dialogVisible.value = true;
  // 重置表单
  formData.id = undefined;
  formData.name = '';
  formData.code = '';
  formData.description = '';
  formData.status = 'ENABLED';
};

/**
 * @description 编辑角色
 * @param {Role} row - 角色信息
 */
const handleEdit = async (row: Role) => {
  isEdit.value = true;
  dialogVisible.value = true;
  
  try {
    // 获取角色详情
    const res = await RoleService.getDetail(row.id);
    const roleData = res.data as Role;
    // 回显表单
    formData.id = roleData.id;
    formData.name = roleData.name;
    formData.code = roleData.code;
    formData.description = roleData.description;
    formData.status = roleData.status;
  } catch (error) {
    console.error("获取角色详情失败", error);
    ElMessage.error('获取角色详情失败');
    dialogVisible.value = false;
  }
};

/**
 * @description 分配权限
 * @param {Role} row - 角色信息
 */
const handleAssignPermissions = async (row: Role) => {
  currentRole.value = row;
  permissionDialogVisible.value = true;
  
  // 获取所有权限列表
  await fetchAllPermissions();
  
  try {
    // 获取角色详情，包括已分配的权限
    const res = await RoleService.getDetail(row.id);
    const roleData = res.data as Role;
    if (roleData.permissions && roleData.permissions.length > 0) {
      selectedPermissions.value = roleData.permissions.map(p => p.id);
      
      // 更新全选状态
      const enabledPermissionsCount = permissionList.value.filter(p => p.status === 'ENABLED').length;
      checkAll.value = selectedPermissions.value.length === enabledPermissionsCount && enabledPermissionsCount > 0;
      isIndeterminate.value = selectedPermissions.value.length > 0 && selectedPermissions.value.length < enabledPermissionsCount;
    } else {
      selectedPermissions.value = [];
      checkAll.value = false;
      isIndeterminate.value = false;
    }
  } catch (error) {
    console.error("获取角色权限失败", error);
    ElMessage.error('获取角色权限失败');
  }
};

/**
 * @description 提交权限分配
 */
const submitPermissions = async () => {
  if (!currentRole.value) return;
  
  try {
    await RoleService.assignPermissions(currentRole.value.id, selectedPermissions.value );
    ElMessage.success('分配权限成功');
    permissionDialogVisible.value = false;
    fetchRoleList(); // 刷新列表
  } catch (error) {
    console.error("分配权限失败", error);
    ElMessage.error('分配权限失败');
  }
};

/**
 * @description 提交表单 (新增/编辑)
 */
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (isEdit.value) {
          // 编辑角色
          await RoleService.update(formData.id as number, formData);
          ElMessage.success('编辑角色成功');
        } else {
          // 新增角色
          await RoleService.create(formData);
          ElMessage.success('新增角色成功');
        }
        dialogVisible.value = false;
        fetchRoleList(); // 刷新列表
      } catch (error) {
        console.error(isEdit.value ? "编辑角色失败" : "新增角色失败", error);
        ElMessage.error(isEdit.value ? '编辑角色失败' : '新增角色失败');
      }
    }
  });
};

/**
 * @description 更改角色状态 (启用/禁用)
 * @param {number} id - 角色ID
 * @param {string} status - 目标状态
 */
const handleStatusChange = async (id: number, status: 'ENABLED' | 'DISABLED') => {
  try {
    await RoleService.update(id, { status });
    ElMessage.success('角色状态更新成功');
    fetchRoleList(); // 刷新列表
  } catch (error) {
    console.error("角色状态更新失败", error);
    ElMessage.error('角色状态更新失败');
  }
};

/**
 * @description 删除角色
 * @param {Role} row - 角色信息
 */
const handleDelete = async (row: Role) => {
  ElMessageBox.confirm(
    `确定要删除角色 ${row.name} 吗?`,
    '删除角色',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        await RoleService.delete(row.id);
        ElMessage.success('删除角色成功');
        fetchRoleList(); // 刷新列表
      } catch (error) {
        console.error("删除角色失败", error);
        ElMessage.error('删除角色失败');
      }
    })
    .catch(() => {
      ElMessage.info('已取消删除');
    });
};

const handleSelectionChange = (selection: Role[]) => {
  selectedRoles.value = selection.map(role => role.id);
};

const handleBatchStatusChange = async (_status: 'ENABLED' | 'DISABLED') => {
  if (selectedRoles.value.length === 0) {
    showErrorMessage('请选择要操作的角色');
    return;
  }
  
  try {
    //await RoleService.batchUpdateStatus(selectedRoles.value, status);
    showSuccessMessage('批量更新状态成功');
    fetchRoleList();
  } catch (error) {
    console.error("批量更新状态失败", error);
    showErrorMessage('批量更新状态失败');
  }
};

// 导出数据
const exportRoles = async () => {
  //try {
  //   const res = await RoleService.export();
  //   const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
  //   const url = window.URL.createObjectURL(blob);
  //   const a = document.createElement('a');
  //   a.href = url;
  //   a.download = 'roles.xlsx';
  //   document.body.appendChild(a);
  //   a.click();
  //   a.remove();
  //   window.URL.revokeObjectURL(url);
  //   showSuccessMessage('导出角色列表成功');
  // } catch (error) {
  //   console.error("导出角色列表失败", error);
  //   showErrorMessage('导出角色列表失败');
  // }
};

// 页面加载时获取角色列表
onMounted(() => {
  fetchRoleList();
});
</script>

<style scoped lang="scss">
.role-management-view {
  padding: 20px;
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
  }
  .action-box {
    display: flex;
    align-items: center;
    .search-input {
      width: 250px;
      margin-right: 10px;
    }
  }
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .role-info {
    font-size: 16px;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
  }
  
  .permission-tree-container {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 15px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .permission-search {
      margin-bottom: 15px;
    }
    
    .permission-list {
      max-height: 400px;
      overflow-y: auto;
      margin-top: 10px;
      
      .permission-group {
        margin-bottom: 15px;
        
        .permission-group-title {
          font-weight: bold;
          margin-bottom: 5px;
          padding: 5px;
          background-color: #f5f7fa;
        }
        
        .el-checkbox {
          display: block;
          margin-left: 15px;
          margin-bottom: 8px;
        }
      }
    }
  }
}
</style>
