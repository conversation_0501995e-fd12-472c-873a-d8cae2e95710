<!--
  @description GridDashboard - 基于gridstack的可拖拽网格仪表盘配置编辑器
  <AUTHOR> AI
  @date 2025-04-11
-->
<template>
  <div class="grid-dashboard-view">
    <div class="dashboard-header">
      <h2>网格仪表盘配置编辑器</h2>
      <div class="action-buttons">
        <el-tooltip content="添加模块" placement="top">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
          </el-button>
        </el-tooltip>
        <el-dropdown @command="handleExportImport" trigger="click">
          <el-button type="primary">
            <el-icon class="el-icon--left"><Operation /></el-icon>
            操作
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="export">
                <el-icon><Download /></el-icon> 导出配置
              </el-dropdown-item>
              <el-dropdown-item command="import">
                <el-icon><Upload /></el-icon> 导入配置
              </el-dropdown-item>
              <el-dropdown-item command="clear" divided>
                <el-icon><Delete /></el-icon> 清除配置
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button type="primary" :disabled="!layoutChanged" @click="saveLayout">
          <el-icon><Finished /></el-icon> 保存配置
        </el-button>
        <el-button @click="resetLayout">
          <el-icon><Refresh /></el-icon> 重置配置
        </el-button>
      </div>
    </div>

    <div class="dashboard-content">
      <GridPage 
        ref="gridPageRef"
        :items="gridItems" 
        :options="gridOptions"
        @change="handleGridChange"
        @added="handleItemAdded"
        @removed="handleItemRemoved"
      />
    </div>

    <!-- 新增模块对话框 -->
    <el-dialog 
      v-model="addItemDialogVisible" 
      title="添加新模块" 
      width="30%"
    >
      <el-form ref="addItemFormRef" :model="newItemForm" label-width="80px">
        <el-form-item label="标题" prop="title" :rules="[{ required: true, message: '请输入标题', trigger: 'blur' }]">
          <el-input v-model="newItemForm.title" placeholder="请输入模块标题" />
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="newItemForm.type" placeholder="请选择模块类型">
            <el-option 
              v-for="item in gridItemTypes" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="图标">
          <el-input v-model="newItemForm.icon" placeholder="请输入图标名称" />
        </el-form-item>
        <el-form-item label="初始宽度">
          <el-slider v-model="newItemForm.w" :min="1" :max="12" show-stops />
        </el-form-item>
        <el-form-item label="初始高度">
          <el-slider v-model="newItemForm.h" :min="1" :max="6" show-stops />
        </el-form-item>
        <el-form-item label="可刷新">
          <el-switch v-model="newItemForm.refreshable" />
        </el-form-item>
        <el-form-item label="可配置">
          <el-switch v-model="newItemForm.configurable" />
        </el-form-item>
        <el-form-item label="可编辑">
          <el-switch v-model="newItemForm.editable" />
        </el-form-item>
        <el-form-item label="可关闭">
          <el-switch v-model="newItemForm.closable" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addItemDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddItem">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入布局对话框 -->
    <el-dialog 
      v-model="importDialogVisible" 
      title="导入配置" 
      width="60%"
    >
      <el-form>
        <el-form-item>
          <el-input
            v-model="importJsonText"
            type="textarea"
            :rows="15"
            placeholder="请粘贴JSON格式的配置数据"
          />
        </el-form-item>
        <el-form-item v-if="importError">
          <el-alert :title="importError" type="error" show-icon />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmImport">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 配置编辑对话框 -->
    <el-dialog
      v-if="configEditorVisible"
      v-model="configEditorVisible"
      title="编辑模块配置"
      width="80%"
      top="5vh"
      :before-close="handleConfigEditorClose"
      append-to-body
      destroy-on-close
    >
      <el-tabs v-model="activeConfigTab">
        <el-tab-pane label="基础配置" name="basic">
          <el-form :model="editingItemContent" label-width="100px" v-if="editingItemContent">
            <el-form-item label="标题">
              <el-input v-model="editingItemContent.title" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item label="图标">
              <el-input v-model="editingItemContent.icon" placeholder="请输入图标名称" />
            </el-form-item>
            <el-form-item label="显示标题">
              <el-switch v-model="editingItemContent.showTitle" active-text="显示" inactive-text="隐藏" />
            </el-form-item>
            <el-form-item label="刷新按钮">
              <el-switch v-model="editingItemContent.refreshable" active-text="显示" inactive-text="隐藏" />
            </el-form-item>
            <el-form-item label="设置按钮">
              <el-switch v-model="editingItemContent.configurable" active-text="显示" inactive-text="隐藏" />
            </el-form-item>
            <el-form-item label="关闭按钮">
              <el-switch v-model="editingItemContent.closable" active-text="显示" inactive-text="隐藏" />
            </el-form-item>
            <el-form-item label="主题模式">
              <el-select v-model="editingItemContent.themeMode" placeholder="请选择主题模式">
                <el-option label="日间模式" value="light" />
                <el-option label="夜间模式" value="dark" />
                <el-option label="跟随系统" value="auto" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="样式设置" name="style">
          <el-form :model="editingItemContent" label-width="100px" v-if="editingItemContent">
            <el-form-item label="背景颜色">
              <el-color-picker v-model="editingItemContent.backgroundColor" show-alpha />
            </el-form-item>
            <el-form-item label="标题背景色">
              <el-color-picker v-model="editingItemContent.titleBackgroundColor" show-alpha />
            </el-form-item>
            <el-form-item label="文本颜色">
              <el-color-picker v-model="editingItemContent.textColor" show-alpha />
            </el-form-item>
            <el-form-item label="透明度">
              <el-slider 
                v-model="gridItemOpacity" 
                :min="0" 
                :max="100" 
                :step="1"
                :format-tooltip="formatOpacity"
                @input="updateGridItemOpacity"
              />
            </el-form-item>
            <el-form-item label="边框">
              <el-select v-model="gridItemBorder" placeholder="请选择边框样式" @change="updateGridItemStyle">
                <el-option label="无边框" value="none" />
                <el-option label="细边框" value="thin" />
                <el-option label="圆角边框" value="rounded" />
                <el-option label="阴影" value="shadow" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
  
        <el-tab-pane :label="getConfigTabLabel" name="content">
          <!-- 根据类型渲染不同的配置表单 -->
          <component 
            :is="getConfigEditorComponent" 
            v-if="editingItemContent && editingItemContent.config" 
            :config="editingItemContent.config" 
            @update:config="updateItemConfig"
          />
          <div v-else class="empty-config-notice">
            没有可配置的内容
          </div>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleConfigEditorClose">取消</el-button>
          <el-button type="primary" @click="saveItemConfig">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * GridDashboard - 基于gridstack的可拖拽网格仪表盘配置编辑器
 * 支持添加、删除、拖拽、调整大小等操作
 */
import { ref, onMounted, reactive, watch, nextTick, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Delete, Edit, Plus, Setting, Download, Upload, Refresh, View } from '@element-plus/icons-vue';
import localforage from 'localforage';
import type { GridContentConfig } from '@/types/grid';
import type { GridStackNode } from 'gridstack';
import GridPage from '@/components/page/GridPage.vue';
import FormConfigEditor from '@/components/base/FormConfigEditor.vue';

// 网格页面引用
const gridPageRef = ref<InstanceType<typeof GridPage> | null>(null);

// 网格配置
const gridOptions = reactive({
  column: 12,
  cellHeight: 80,
  margin: 10,
  disableOneColumnMode: false,
  float: false,
  animate: true
});

// 默认网格项数据（类型断言为GridStackNode[]解决类型问题）
const defaultGridItems = [
  { 
    x: 0, y: 0, w: 4, h: 2, id: 'item-1',
    content: {
      type: 'info',
      title: '网站访问统计',
      icon: 'DataLine',
      showTitle: true,
      refreshable: true,
      configurable: false,
      editable: true,
      closable: true,
      config: {
        items: [
          { label: '今日访问', value: '2,856', color: '#409EFF' },
          { label: '昨日访问', value: '1,958', color: '#67C23A' },
          { label: '本周访问', value: '13,285', color: '#E6A23C' },
          { label: '本月访问', value: '48,583', color: '#F56C6C' }
        ]
      }
    }
  },
  { 
    x: 4, y: 0, w: 4, h: 2, id: 'item-2',
    content: {
      type: 'info',
      showTitle: true,
      title: '系统状态',
      icon: 'Monitor',
      refreshable: true,
      configurable: false,
      editable: true,
      closable: true,
      config: {
        items: [
          { label: 'CPU使用率', value: '35', unit: '%', color: 'blue' },
          { label: '内存使用率', value: '68', unit: '%', color: 'orange' },
          { label: '存储空间', value: '42', unit: '%', color: 'green' }
        ]
      }
    }
  },
  { 
    x: 8, y: 0, w: 4, h: 2, id: 'item-3',
    content: {
      type: 'custom',
      title: '最近任务',
      icon: 'Calendar',
      showTitle: true,
      refreshable: true,
      configurable: false,
      editable: true,
      closable: true,
      config: {
        html: `
          <div class="task-list">
            <div class="task-item">
              <el-tag type="success" size="small">已完成</el-tag>
              <span class="task-title">系统更新</span>
            </div>
            <div class="task-item">
              <el-tag type="warning" size="small">进行中</el-tag>
              <span class="task-title">数据备份</span>
            </div>
            <div class="task-item">
              <el-tag type="info" size="small">待处理</el-tag>
              <span class="task-title">日志分析</span>
            </div>
          </div>
        `
      }
    }
  },
  { 
    x: 0, y: 2, w: 6, h: 3, id: 'item-4',
    content: {
      type: 'table',
      title: '数据表格',
      showTitle: true,
      icon: 'List',
      refreshable: true,
      configurable: true,
      editable: true,
      closable: true,
      config: {
        columns: [
          { label: '日期', prop: 'date', width: 180 },
          { label: '名称', prop: 'name', width: 180 },
          { label: '状态', prop: 'status' }
        ],
        dataSource: 'static',
        staticData: [
          { date: '2025-04-01', name: '项目A', status: '进行中' },
          { date: '2025-04-02', name: '项目B', status: '已完成' },
          { date: '2025-04-03', name: '项目C', status: '待处理' }
        ],
        showIndex: true,
        showPagination: true,
        pagination: {
          pageSize: 10,
          pageSizes: [5, 10, 20]
        },
        rowKey: 'date'
      }
    }
  },
  { 
    x: 6, y: 2, w: 6, h: 3, id: 'item-5',
    content: {
      type: 'chart',
      title: '销售统计',
      showTitle: true,
      icon: 'TrendCharts',
      refreshable: true,
      configurable: true,
      editable: true,
      closable: true,
      config: {
        chartType: 'bar',
        dataSource: 'static',
        data: {
          xAxis: {
            data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
          },
          series: [
            {
              name: '本周销售',
              type: 'bar',
              data: [120, 132, 101, 134, 90, 230, 210]
            },
            {
              name: '上周销售',
              type: 'bar',
              data: [220, 182, 191, 234, 290, 330, 310]
            }
          ]
        }
      }
    }
  }
] as unknown as GridStackNode[];

// 当前网格项数据
const gridItems = ref<GridStackNode[]>([...defaultGridItems]);
// 原始网格项数据（用于重置）
const originalGridItems = ref<GridStackNode[]>([...defaultGridItems]);
// 标记布局是否已更改
const layoutChanged = ref(false);

// 监听网格项变化
watch(gridItems, () => {
  layoutChanged.value = true;
}, { deep: true });

// 新建网格项表单
const addItemDialogVisible = ref(false);
const newItemForm = reactive({
  title: '',
  type: 'info',
  icon: '',
  w: 6,
  h: 4,
  refreshable: true,
  configurable: false,
  editable: true,
  closable: true
});

// 网格项类型选项
const gridItemTypes = [
  { label: '信息卡片', value: 'info' },
  { label: '表格组件', value: 'table' },
  { label: '高级表格', value: 'plustable' },
  { label: '表单组件', value: 'plusform' },
  { label: '图表组件', value: 'chart' },
  { label: '自定义组件', value: 'custom' }
];

// 确认添加网格项
const confirmAddItem = async () => {
  // 基础配置
  const baseConfig: Partial<GridContentConfig> = {
    type: newItemForm.type,
    title: newItemForm.title,
    icon: newItemForm.icon,
    refreshable: newItemForm.refreshable,
    configurable: newItemForm.configurable,
    editable: newItemForm.editable,
    closable: newItemForm.closable,
    showTitle: true,
    themeMode: 'light'
  };
  
  // 根据类型创建不同的配置
  let itemConfig: GridContentConfig | null = null;
  
  if (newItemForm.type === 'info') {
    itemConfig = {
      ...baseConfig,
      config: {
        items: [
          { label: '指标1', value: '0', color: '#409EFF' },
          { label: '指标2', value: '0', color: '#67C23A' },
          { label: '指标3', value: '0', color: '#E6A23C' }
        ]
      }
    } as unknown as GridContentConfig;
  } else if (newItemForm.type === 'table') {
    itemConfig = {
      ...baseConfig,
      config: {
        columns: [
          { label: '列1', prop: 'col1' },
          { label: '列2', prop: 'col2' },
          { label: '列3', prop: 'col3' }
        ],
        pagination: {
          pageSize: 10,
          pageSizes: [10, 20, 50, 100]
        },
        showPagination: true,
        staticData: [
          { col1: '数据1', col2: '数据2', col3: '数据3' },
          { col1: '数据4', col2: '数据5', col3: '数据6' }
        ]
      }
    } as unknown as GridContentConfig;
  } else if (newItemForm.type === 'plustable') {
    itemConfig = {
      ...baseConfig,
      config: {
        columns: [
          { label: '名称', prop: 'name' },
          { 
            label: '状态', 
            prop: 'status',
            valueType: 'select',
            options: [
              { label: '未解决', value: '0', type: 'primary' },
              { label: '已解决', value: '1', type: 'success' },
              { label: '解决中', value: '2', type: 'info' },
              { label: '失败', value: '3', type: 'danger' },
              { label: '审核中', value: '4', type: 'warning' }
            ]
          },
          { 
            label: '标签', 
            prop: 'tag',
            valueType: 'radio',
            options: [
              { label: '标签1', value: 'tag1', color: '#409EFF' },
              { label: '标签2', value: 'tag2', color: '#67C23A' },
              { label: '标签3', value: 'tag3', color: '#E6A23C' }
            ]
          }
        ],
        pagination: {
          pageSize: 10,
          pageSizes: [10, 20, 50, 100]
        },
        rowKey: 'id',
        showPagination: true,
        dataSource: 'static',
        staticData: [
          { id: 1, name: '项目1', status: '0', tag: 'tag1' },
          { id: 2, name: '项目2', status: '1', tag: 'tag2' },
          { id: 3, name: '项目3', status: '2', tag: 'tag3' },
          { id: 4, name: '项目4', status: '3', tag: 'tag1' },
          { id: 5, name: '项目5', status: '4', tag: 'tag2' }
        ]
      }
    } as unknown as GridContentConfig;
  } else if (newItemForm.type === 'plusform') {
    itemConfig = {
      ...baseConfig,
      config: {
        columns: [
          {
            label: '名称',
            width: 120,
            prop: 'name',
            valueType: 'copy',
            tooltip: '请输入名称'
          },
          {
            label: '状态',
            width: 120,
            prop: 'status',
            valueType: 'select',
            options: [
              {
                label: '未解决',
                value: '0',
                color: 'red'
              },
              {
                label: '已解决',
                value: '1',
                color: 'blue'
              },
              {
                label: '解决中',
                value: '2',
                color: 'yellow'
              },
              {
                label: '失败',
                value: '3',
                color: 'red'
              }
            ]
          },
          {
            label: '标签',
            width: 120,
            prop: 'tag'
          },
          {
            label: '评分',
            width: 200,
            prop: 'rate',
            valueType: 'rate'
          },
          {
            label: '是否显示',
            width: 100,
            prop: 'switch',
            valueType: 'switch'
          },
          {
            label: '说明',
            prop: 'desc',
            valueType: 'textarea',
            fieldProps: {
              maxlength: 50,
              showWordLimit: true,
              autosize: { minRows: 2, maxRows: 4 }
            }
          }
        ],
        initialValues: {
          status: '0',
          name: '',
          rate: 3,
          switch: true
        },
        rules: {
          name: [
            {
              required: true,
              message: '请输入名称'
            }
          ],
          tag: [
            {
              required: true,
              message: '请输入标签'
            }
          ]
        },
        layout: 'horizontal',
        labelWidth: '120px',
        showSubmitButton: true,
        showResetButton: true,
        submitText: '提交',
        resetText: '重置'
      }
    } as unknown as GridContentConfig;
  } else if (newItemForm.type === 'chart') {
    itemConfig = {
      ...baseConfig,
      config: {
        chartType: 'bar',
        dataSource: 'static',
        data: {
          xAxis: {
            data: ['类别1', '类别2', '类别3', '类别4', '类别5']
          },
          series: [{
            name: '系列1',
            type: 'bar',
            data: [10, 52, 200, 334, 390]
          }]
        }
      }
    } as unknown as GridContentConfig;
  } else if (newItemForm.type === 'custom') {
    itemConfig = {
      ...baseConfig,
      config: {
        html: '<div style="padding: 20px; text-align: center;"><h3>自定义内容</h3><p>这里可以放置任何HTML内容</p></div>'
      }
    } as unknown as GridContentConfig;
  }
  
  if (!itemConfig) return;
  
  // 创建网格项
  const gridItem = {
    x: 0,
    y: 0,
    w: newItemForm.w,
    h: newItemForm.h,
    id: `item-${Date.now()}`,
    content: itemConfig
  };
  
  // 添加到网格
  gridItems.value.push(gridItem as unknown as GridStackNode);
  
  // 更新状态
  layoutChanged.value = true;
  addItemDialogVisible.value = false;
  
  // 重置表单
  Object.assign(newItemForm, {
    title: '',
    type: 'info',
    icon: '',
    w: 6,
    h: 4,
    refreshable: true,
    configurable: false,
    closable: true,
    editable: true
  });
};

// 打开添加网格项对话框
const handleAdd = () => {
  // 重置表单
  Object.assign(newItemForm, {
    title: '',
    type: 'info',
    icon: '',
    w: 6,
    h: 4,
    refreshable: true,
    configurable: false,
    closable: true,
    editable: true
  });
  
  addItemDialogVisible.value = true;
};

// 导入布局对话框状态
const importDialogVisible = ref(false);
const importJsonText = ref('');
const importError = ref('');

// 处理网格变化事件
const handleGridChange = (_items: GridStackNode[]) => {
  // 标记布局已更改
  layoutChanged.value = true;
};

// 处理添加项目事件
const handleItemAdded = (_items: GridStackNode[]) => {
  console.log('项目已添加:', _items);
  layoutChanged.value = true;
};

// 处理移除项目事件
const handleItemRemoved = (_items: GridStackNode[]) => {
  console.log('项目已移除:', _items);
  layoutChanged.value = true;
};

// 挂载监听事件，用于处理网格项中的编辑按钮点击
onMounted(async () => {
  // 从localforage加载保存的布局
  await loadSavedLayout();
  
  // 添加全局事件监听，处理网格项中的编辑按钮点击
  document.addEventListener('grid-item-edit', ((event: CustomEvent) => {
    console.log('收到网格项编辑事件:', event.detail);
    const { node } = event.detail;
    if (node && node.content) {
      openConfigEditor(node);
    }
  }) as EventListener);
  
  // 添加全局事件监听，处理网格项中的配置按钮点击
  document.addEventListener('grid-item-config', ((event: CustomEvent) => {
    console.log('收到网格项配置事件:', event.detail);
    const { node } = event.detail;
    if (node && node.content) {
      openConfigEditor(node);
    }
  }) as EventListener);
});

// 导出布局为JSON字符串
const exportLayoutAsJson = (): string => {
  if (!gridPageRef.value) return '';
  
  try {
    const currentLayout = gridPageRef.value.getGridData();
    return JSON.stringify(currentLayout, null, 2);
  } catch (error) {
    console.error('导出布局失败:', error);
    return '';
  }
};

// 从JSON字符串导入布局
const importLayoutFromJson = (jsonStr: string): GridStackNode[] | null => {
  try {
    const parsed = JSON.parse(jsonStr);
    if (!Array.isArray(parsed)) {
      throw new Error('导入的数据不是有效的数组');
    }
    
    // 验证数据结构
    for (const item of parsed) {
      if (typeof item !== 'object' || item === null) {
        throw new Error('布局项必须是对象');
      }
      
      // 检查必要的属性
      if (!(item.hasOwnProperty('w') && item.hasOwnProperty('h'))) {
        throw new Error('布局项缺少必要的尺寸属性(w,h)');
      }
    }
    
    return parsed as GridStackNode[];
  } catch (error) {
    console.error('解析导入的JSON失败:', error);
    if (error instanceof Error) {
      importError.value = `解析失败: ${error.message}`;
    } else {
      importError.value = '解析失败: 无效的JSON数据';
    }
    return null;
  }
};

// 处理导出/导入/清除命令
const handleExportImport = (command: string) => {
  switch (command) {
    case 'export':
      handleExportLayout();
      break;
    case 'import':
      handleImportLayout();
      break;
    case 'clear':
      handleClearLayout();
      break;
  }
};

// 处理导出布局
const handleExportLayout = () => {
  const jsonData = exportLayoutAsJson();
  if (!jsonData) {
    ElMessage.error('导出布局失败，请稍后重试');
    return;
  }
  
  // 创建下载链接
  const blob = new Blob([jsonData], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  
  link.href = url;
  link.download = `grid-dashboard-layout-${new Date().toISOString().slice(0, 10)}.json`;
  document.body.appendChild(link);
  link.click();
  
  // 清理
  setTimeout(() => {
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, 100);
  
  ElMessage.success('布局已导出');
};

// 处理导入布局
const handleImportLayout = () => {
  importJsonText.value = '';
  importError.value = '';
  importDialogVisible.value = true;
};

// 确认导入布局
const confirmImport = async () => {
  try {
    // 处理JSON导入
    const layoutData = importLayoutFromJson(importJsonText.value);
    if (layoutData) {
      // 使用正确的类型转换
      gridItems.value = layoutData as unknown as GridStackNode[];
      originalGridItems.value = [...layoutData] as unknown as GridStackNode[];
      
      // 重置状态
      layoutChanged.value = false;
      importError.value = '';
      importDialogVisible.value = false;
      importJsonText.value = '';
      
      // 显示成功消息
      ElMessage.success('布局导入成功');
    }
  } catch (error) {
    console.error('导入布局出错:', error);
    importError.value = '导入失败，请检查JSON格式是否正确';
  }
};

// 处理清除保存的布局
const handleClearLayout = () => {
  ElMessageBox.confirm('确定要清除保存的布局吗？此操作不可恢复。', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const cleared = await clearSavedLayout();
    
    if (cleared) {
      // 重置为默认布局
      gridItems.value = [...defaultGridItems];
      originalGridItems.value = [...defaultGridItems];
      layoutChanged.value = false;
      
      ElMessage.success('已清除保存的布局并重置为默认布局');
    } else {
      ElMessage.error('清除布局失败');
    }
  }).catch(() => {
    // 取消操作
  });
};

// 保存布局
const saveLayout = async () => {
  if (!gridPageRef.value) return;
  
  try {
    // 获取当前布局
    const currentLayout = gridPageRef.value.getGridData();
    
    // 保存到本地状态
    originalGridItems.value = [...currentLayout] as unknown as GridStackNode[];
    
    // 保存到localforage
    const saved = await saveLayoutToStorage(currentLayout as unknown as GridStackNode[]);
    
    if (saved) {
      layoutChanged.value = false;
      ElMessage.success('布局已保存');
    } else {
      ElMessage.warning('布局保存失败，请稍后重试');
    }
  } catch (error) {
    console.error('保存布局出错:', error);
    ElMessage.error('保存布局时发生错误');
  }
};

// 重置布局
const resetLayout = () => {
  ElMessageBox.confirm('确定要重置布局吗？未保存的更改将丢失', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    if (!gridPageRef.value) return;
    
    try {
      // 重置网格项
      gridItems.value = [...originalGridItems.value];
      layoutChanged.value = false;
      
      // 通知用户
      ElMessage.success('布局已重置');
    } catch (error) {
      console.error('重置布局出错:', error);
      ElMessage.error('重置布局时发生错误');
    }
  }).catch(() => {
    // 取消操作
  });
};

// 组件挂载时的操作
onMounted(async () => {
  // 从localforage加载保存的布局
  const savedLayout = await loadLayoutFromStorage();
  
  if (savedLayout) {
    try {
      // 更新原始布局和当前布局
      originalGridItems.value = [...savedLayout];
      gridItems.value = [...savedLayout];
      console.log('已从localforage加载布局', savedLayout);
    } catch (error) {
      console.error('应用保存的布局时出错:', error);
      // 如果加载失败，使用默认布局
      originalGridItems.value = [...defaultGridItems];
      gridItems.value = [...defaultGridItems];
    }
  } else {
    console.log('没有找到保存的布局，使用默认布局');
  }
});

// 配置编辑对话框状态
const configEditorVisible = ref(false);
const editingItemContent = ref<GridContentConfig | null>(null);
const editingItemId = ref<string | null>(null);
const configEditorRef = ref<any>(null);

// 配置编辑器的当前激活标签
const activeConfigTab = ref('basic');

// 样式配置
const gridItemOpacity = ref(100);
const gridItemBorder = ref('thin');

// 获取配置标签名称
const getConfigTabLabel = computed(() => {
  if (!editingItemContent.value) return '内容配置';
  
  // 根据类型设置标签名称
  const typeMap: Record<string, string> = {
    'table': '表格配置',
    'chart': '图表配置',
    'info': '信息卡片配置',
    'plustable': '高级表格配置',
    'plusform': '表单配置',
    'custom': '自定义配置'
  };
  
  return typeMap[editingItemContent.value.type] || '内容配置';
});

// 格式化透明度显示
const formatOpacity = (val: number) => {
  return `${val}%`;
};

// 更新网格项透明度
const updateGridItemOpacity = (val: number) => {
  if (editingItemContent.value) {
    // 计算透明度值 (0-1之间)
    const opacity = val / 100;
    
    // 确保customStyle对象存在
    if (!editingItemContent.value.customStyle) {
      editingItemContent.value.customStyle = {};
    }
    
    // 设置透明度
    editingItemContent.value.customStyle.opacity = opacity.toString();
  }
};

// 更新网格项边框样式
const updateGridItemStyle = (style: string) => {
  if (!editingItemContent.value || !editingItemContent.value.customStyle) {
    return;
  }
  
  const customStyle = editingItemContent.value.customStyle;
  
  // 根据选择的样式设置相应的CSS属性
  switch (style) {
    case 'none':
      customStyle.border = 'none';
      customStyle.borderRadius = '0';
      customStyle.boxShadow = 'none';
      break;
    case 'thin':
      customStyle.border = '1px solid var(--el-border-color)';
      customStyle.borderRadius = '4px';
      customStyle.boxShadow = 'none';
      break;
    case 'rounded':
      customStyle.border = '1px solid var(--el-border-color)';
      customStyle.borderRadius = '12px';
      customStyle.boxShadow = 'none';
      break;
    case 'shadow':
      customStyle.border = 'none';
      customStyle.borderRadius = '8px';
      customStyle.boxShadow = 'var(--el-box-shadow)';
      break;
  }
};

// 获取配置编辑器组件
const getConfigEditorComponent = computed(() => {
  if (!editingItemContent.value) return null;
  
  // 所有类型都使用通用的FormConfigEditor组件
  return FormConfigEditor;
});

// 更新某一项的配置
const updateItemConfig = (config: any) => {
  if (editingItemContent.value) {
    editingItemContent.value.config = config;
  }
};

// 打开配置编辑对话框
const openConfigEditor = (item: GridStackNode) => {
  // 使用深拷贝避免引用共享导致的响应式问题
  editingItemContent.value = JSON.parse(JSON.stringify(item.content));
  editingItemId.value = item.id || null;
  
  // 初始化样式配置值
  if (editingItemContent.value) {
    // 设置透明度滑块的值
    if (editingItemContent.value.customStyle && editingItemContent.value.customStyle.opacity) {
      gridItemOpacity.value = Math.round(parseFloat(editingItemContent.value.customStyle.opacity) * 100);
    } else {
      gridItemOpacity.value = 100;
    }
    
    // 设置边框样式的值
    if (editingItemContent.value.customStyle) {
      if (editingItemContent.value.customStyle.boxShadow && editingItemContent.value.customStyle.boxShadow !== 'none') {
        gridItemBorder.value = 'shadow';
      } else if (editingItemContent.value.customStyle.borderRadius === '12px') {
        gridItemBorder.value = 'rounded';
      } else if (editingItemContent.value.customStyle.border === 'none') {
        gridItemBorder.value = 'none';
      } else {
        gridItemBorder.value = 'thin';
      }
    } else {
      gridItemBorder.value = 'thin';
    }
    
    // 确保必要的默认值存在
    if (editingItemContent.value.showTitle === undefined) {
      editingItemContent.value.showTitle = true;
    }
    
    if (editingItemContent.value.themeMode === undefined) {
      editingItemContent.value.themeMode = 'light';
    }
  }
  
  // 延迟显示对话框，确保数据先设置好
  nextTick(() => {
    configEditorVisible.value = true;
    // 默认显示基础配置标签页
    activeConfigTab.value = 'basic';
  });
};

// 关闭配置编辑对话框
const handleConfigEditorClose = () => {
  configEditorVisible.value = false;
  // 清空编辑状态，避免残留数据造成问题
  editingItemContent.value = null;
  editingItemId.value = null;
};

// 取消配置编辑
const handleConfigEditorCancel = () => {
  configEditorVisible.value = false;
  // 清空编辑状态，避免残留数据造成问题
  editingItemContent.value = null;
  editingItemId.value = null;
};

// 保存配置
const saveItemConfig = () => {
  if (!editingItemContent.value || !editingItemId.value) return;
  
  // 查找要更新的网格项
  const targetItem = gridItems.value.find(item => item.id === editingItemId.value);
  if (targetItem) {
    // 更新网格项内容
    targetItem.content = JSON.parse(JSON.stringify(editingItemContent.value));
    
    // 如果有标题，同步更新
    if (targetItem.content && (targetItem.content as unknown as GridContentConfig).title) {
      // @ts-ignore
      targetItem.title = (targetItem.content as GridContentConfig).title;
    }
    
    // 更新布局状态
    layoutChanged.value = true;
    
    // 更新本地存储
    saveGridLayout();
  }
  
  // 关闭对话框
  configEditorVisible.value = false;
  
  // 清空编辑状态
  editingItemContent.value = null;
  editingItemId.value = null;
};

// 设置localforage配置
const gridDashboardStore = localforage.createInstance({
  name: 'gridDashboard',
  description: '网格仪表盘布局数据存储'
});

// 保存布局到localforage
const saveLayoutToStorage = async (layout: GridStackNode[]): Promise<boolean> => {
  try {
    await gridDashboardStore.setItem('dashboardLayout', layout);
    console.log('布局已保存到localforage', layout);
    return true;
  } catch (error) {
    console.error('保存布局到localforage失败:', error);
    return false;
  }
};

// 从localforage加载布局
const loadLayoutFromStorage = async (): Promise<GridStackNode[] | null> => {
  try {
    const savedLayout = await gridDashboardStore.getItem<GridStackNode[]>('dashboardLayout');
    if (savedLayout) {
      console.log('从localforage加载到布局数据', savedLayout);
      return savedLayout;
    }
    return null;
  } catch (error) {
    console.error('从localforage加载布局失败:', error);
    return null;
  }
};

// 清除已保存的布局
const clearSavedLayout = async (): Promise<boolean> => {
  try {
    await gridDashboardStore.removeItem('dashboardLayout');
    console.log('已清除保存的布局');
    return true;
  } catch (error) {
    console.error('清除布局失败:', error);
    return false;
  }
};

// 加载保存的布局
const loadSavedLayout = async () => {
  try {
    // 从localforage加载保存的布局
    const savedLayout = await loadLayoutFromStorage();
    
    if (savedLayout && savedLayout.length > 0) {
      gridItems.value = savedLayout;
      originalGridItems.value = [...savedLayout];
    } else {
      // 如果没有保存的布局，使用默认布局
      gridItems.value = [...defaultGridItems];
      originalGridItems.value = [...defaultGridItems];
    }
  } catch (error) {
    console.error('加载布局出错:', error);
    ElMessage.error('加载布局失败');
    
    // 使用默认布局
    gridItems.value = [...defaultGridItems];
    originalGridItems.value = [...defaultGridItems];
  }
};

// 保存网格布局到本地存储
const saveGridLayout = () => {
  // 从gridPageRef中获取网格数据
  if (gridPageRef.value) {
    try {
      // 使用getGridData方法获取网格数据，而不是不存在的serializeLayout方法
      const gridData = gridPageRef.value.getGridData();
      
      // 保存到localforage
      localforage.setItem('gridDashboardLayout', JSON.stringify(gridData))
        .then(() => {
          console.log('网格布局已保存');
          ElMessage.success('布局已保存');
          layoutChanged.value = false;
        })
        .catch(err => {
          console.error('保存网格布局失败:', err);
          ElMessage.error('保存布局失败');
        });
    } catch (error) {
      console.error('获取网格数据失败:', error);
      ElMessage.error('保存布局失败：获取网格数据出错');
    }
  }
};
</script>

<style scoped>
.grid-dashboard-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  box-sizing: border-box;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.dashboard-header h2 {
  margin: 0;
  font-size: 20px;
  color: var(--el-text-color-primary);
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.dashboard-content {
  flex: 1;
  overflow: hidden;
  background-color: var(--el-bg-color-page);
  border-radius: 4px;
  border: 1px solid var(--el-border-color-light);
}

/* 编辑按钮样式 */
.edit-button {
  margin-right: 4px;
}

.edit-icon:before {
  content: "\e61c";
  font-family: element-icons;
}

.custom-grid-item {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.custom-grid-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.custom-grid-item-title {
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 6px;
}

.custom-grid-item-content {
  flex: 1;
  padding: 12px;
  overflow: auto;
}

.custom-grid-item-actions {
  display: flex;
  gap: 4px;
}

/* 导入对话框样式 */
.import-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.import-tip {
  margin: 0;
  color: var(--el-text-color-secondary);
}

.import-warning {
  margin-top: 10px;
}
</style>
