<!--
 * 全局商品分类管理 - 列表页面
 * 提供全局商品分类的增删改查功能
 * 支持多级分类展示和管理
-->
<template>
  <div class="global-category-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h2>全局商品分类管理</h2>
        </div>
      </template>

      <!-- 搜索和操作区域 -->
      <!-- <div class="filter-container">
        <el-form :inline="true" :model="listQuery" class="demo-form-inline">
          <el-form-item label="分类名称">
            <el-input v-model="listQuery.name" placeholder="请输入分类名称" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="listQuery.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div> -->

      <!-- 操作按钮 -->
      <div class="action-container">
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon> 添加分类
        </el-button>
        <!-- <el-button @click="handleExport">
          <el-icon><Download /></el-icon> 导出
        </el-button> -->
      </div>

      <!-- 分类树形控件 -->
      <div class="tree-container" v-loading="listLoading">
        <el-tree
          ref="categoryTree"
          :data="categoryList"
          node-key="id"
          default-expand-all
          :props="{ children: 'children', label: 'name' }"
          :expand-on-click-node="false"
          highlight-current
        >
          <template #default="{ data }">
            <div class="custom-tree-node">
              <div class="node-info">
                <span class="name-label">{{ data.name }}</span>
                <el-tag size="small" class="code-tag">{{ data.code }}</el-tag>
                <el-tag size="small" type="info" class="level-tag">层级: {{ data.level }}</el-tag>
                <el-tag size="small" type="warning" class="sort-tag">排序: {{ data.sort_order }}</el-tag>
                <el-tag :type="data.is_active ? 'success' : 'danger'" size="small" class="status-tag">
                  {{ data.is_active ? '启用' : '禁用' }}
                </el-tag>
              </div>
              <div class="node-actions">
                <el-button type="primary" size="small" @click="handleCreate(data)">
                  添加子分类
                </el-button>
                <el-button type="primary" size="small" @click="handleUpdate(data)">
                  编辑
                </el-button>
                <el-popconfirm
                  title="确认删除该分类?"
                  @confirm="handleDelete(data)"
                >
                  <template #reference>
                    <el-button type="danger" size="small">删除</el-button>
                  </template>
                </el-popconfirm>
              </div>
            </div>
          </template>
        </el-tree>
        
        <!-- 空数据提示 -->
        <el-empty v-if="categoryList.length === 0" description="暂无分类数据" />
      </div>
    </el-card>

    <!-- 添加/编辑分类对话框 -->
    <el-dialog
      :title="dialogStatus === 'create' ? '添加分类' : '编辑分类'"
      v-model="dialogFormVisible"
      width="600px"
    >
      <el-form
        ref="dataForm"
        :model="temp"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="temp.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类编码" prop="code">
          <el-input v-model="temp.code" placeholder="请输入分类编码" :disabled="dialogStatus === 'update'" />
        </el-form-item>
        <el-form-item label="父级分类" prop="parent_id">
          <el-cascader
            v-model="temp.parent_id"
            :options="categoryOptions"
            :props="{ checkStrictly: true, value: 'id', label: 'name', emitPath: false }"
            clearable
            placeholder="不选择则为一级分类"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="分类描述" prop="description">
          <el-input
            v-model="temp.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>
        <!-- <el-form-item label="分类图标" prop="icon">
          <el-upload
            class="avatar-uploader"
            :action="uploadUrl"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="temp.icon" :src="temp.icon" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item> -->
        <el-form-item label="排序值" prop="sort_order">
          <el-input-number v-model="temp.sort_order" :min="0" :max="999" />
          <span class="tip">数字越小越靠前</span>
        </el-form-item>
        <el-form-item label="状态" prop="is_active">
          <el-radio-group v-model="temp.is_active">
            <el-radio :label="true">启用</el-radio>
            <el-radio :label="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage, ElTree } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { getCategoryTree, createCategory, updateCategory, deleteCategory } from '@/api/admin/global-category';
//import type { UploadProps } from 'element-plus';

// 类型定义
interface CategoryItem {
  id?: number;
  name: string;
  code: string;
  parent_id: number | null;
  description?: string;
  icon?: string;
  sort_order: number;
  is_active: boolean;
  level?: number;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
  children?: CategoryItem[];
}

interface ListQueryParams {
  name: string;
  status: number | null;
  page: number;
  limit: number;
}

// 响应式数据
const listLoading = ref(false);
const dialogFormVisible = ref(false);
const dialogStatus = ref('');
const categoryList = ref<CategoryItem[]>([]);
const categoryOptions = ref<CategoryItem[]>([]);
const dataForm = ref();

const listQuery = reactive<ListQueryParams>({
  name: '',
  status: null,
  page: 1,
  limit: 20
});

const temp = ref<Partial<CategoryItem>>({
  id: undefined,
  name: '',
  code: '',
  parent_id: null,
  description: '',
  icon: '',
  sort_order: 0,
  is_active: true
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入分类编码', trigger: 'blur' }]
};

// 生命周期钩子
onMounted(() => {
  getList();
});

// 方法
// 方法
/**
 * 获取分类列表数据
 */
const getList = async () => {
  listLoading.value = true;
  try {
    const res = await getCategoryTree(listQuery);
    console.log('API Response:', res);
    
    // 处理嵌套的category对象
    const processResponseData = (data: any[]): CategoryItem[] => {
      return data.map(item => {
        const category = item.category || item; // 支持嵌套category或直接是category对象
        return {
          id: category.id,
          name: category.name,
          code: category.code,
          parent_id: category.parent_id,
          description: category.description || '',
          icon: category.icon || '',
          sort_order: category.sort_order || 0,
          is_active: category.is_active ?? true,
          level: category.level,
          created_at: category.created_at,
          updated_at: category.updated_at,
          created_by: category.created_by,
          updated_by: category.updated_by,
          children: category.children ? processResponseData(category.children) : []
        };
      });
    };

    const categoryData = Array.isArray(res) ? processResponseData(res) : [];
    console.log('Processed category data:', categoryData);
    categoryList.value = categoryData;

    // 处理分类选项，用于级联选择器
    categoryOptions.value = [{
      id: 0,
      name: '一级分类',
      code: 'root',
      parent_id: null,
      description: '顶级分类',
      icon: 'folder',
      sort_order: 0,
      is_active: true,
      children: []
    } as CategoryItem];

    // 递归处理分类树
    const processTree = (tree: CategoryItem[]) => {
      return tree.map(item => {
        const node = { ...item };
        if (node.children && node.children.length > 0) {
          node.children = processTree(node.children);
        }
        return node;
      });
    };

    categoryOptions.value[0].children = processTree([...categoryData]);
  } catch (error) {
    console.error('获取分类列表失败:', error);
  } finally {
    listLoading.value = false;
  }
};

const resetTemp = () => {
  temp.value = {
    id: undefined,
    name: '',
    code: '',
    parent_id: null,
    description: '',
    icon: '',
    sort_order: 0,
    is_active: true
  };
};

const handleCreate = (row?: CategoryItem) => {
  resetTemp();
  dialogStatus.value = 'create';
  if (row) {
    temp.value.parent_id = row.id || null;
  }
  dialogFormVisible.value = true;
  nextTick(() => {
    (document.querySelector('.el-dialog__body') as HTMLElement).scrollTop = 0;
  });
};

const handleUpdate = (row: CategoryItem) => {
  temp.value = { ...row };
  dialogStatus.value = 'update';
  dialogFormVisible.value = true;
  nextTick(() => {
    (document.querySelector('.el-dialog__body') as HTMLElement).scrollTop = 0;
  });
};

const createData = async () => {
  try {
    await dataForm.value.validate();
    await createCategory(temp.value);
    ElMessage.success('创建成功');
    dialogFormVisible.value = false;
    getList();
  } catch (error) {
    console.error('创建分类失败:', error);
  }
};

const updateData = async () => {
  if (!temp.value.id) return;
  try {
    await dataForm.value.validate();
    await updateCategory(temp.value.id, temp.value);
    ElMessage.success('更新成功');
    dialogFormVisible.value = false;
    getList();
  } catch (error) {
    console.error('更新分类失败:', error);
  }
};

const handleDelete = async (row: CategoryItem) => {
  if (!row.id) return;
  try {
    await deleteCategory(row.id);
    ElMessage.success('删除成功');
    getList();
  } catch (error) {
    console.error('删除分类失败:', error);
  }
};

// const handleFilter = () => {
//   listQuery.page = 1;
//   getList();
// };

// const resetQuery = () => {
//   listQuery.name = '';
//   listQuery.status = null;
//   handleFilter();
// };

// const handleExport = async () => {
//   try {
//     const res = await exportCategory(listQuery);
//     const url = window.URL.createObjectURL(new Blob([res.data]));
//     const link = document.createElement('a');
//     link.href = url;
//     link.setAttribute('download', `分类数据_${new Date().getTime()}.xlsx`);
//     document.body.appendChild(link);
//     link.click();
//     document.body.removeChild(link);
//     window.URL.revokeObjectURL(url);
//   } catch (error) {
//     console.error('导出分类失败:', error);
//   }
// };

</script>

<style scoped>
.global-category-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.action-container {
  margin-bottom: 20px;
}

.tree-container {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px;
  background-color: #fff;
  min-height: 400px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
  width: 100%;
}

.node-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.name-label {
  font-weight: bold;
  font-size: 14px;
  margin-right: 8px;
}

.code-tag,
.level-tag,
.sort-tag,
.status-tag {
  margin-right: 5px;
}

.node-actions {
  white-space: nowrap;
}

.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  background-color: #f5f7fa;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style>
