<!--
  GridInfoManagement.vue
  网格信息管理视图组件
  用于展示、选择和管理网格信息
-->
<template>
  <div class="grid-info-management">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>网格信息管理</span>
          <!-- <el-button type="primary" size="small" @click="createNewGridInfo">
            <el-icon><Plus /></el-icon> 新增网格
          </el-button> -->
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="关键词">
          <el-input v-model="searchForm.keyword" placeholder="请输入关键词" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 网格信息列表 -->
      <el-table 
        :data="gridInfoList" 
        style="width: 100%" 
        border 
        @selection-change="handleSelectionChange"
      >
        <!-- 添加多选列 -->
        <el-table-column type="selection" width="55" :selectable="(row: GridInfo) => !isGridItemExisting(row.id)" />
        <el-table-column label="ID" prop="id" width="80" />
        <el-table-column label="名称" prop="name" />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="已添加" width="80">
          <template #default="scope">
            <el-tag v-if="isGridItemExisting(scope.row.id)" type="info">已添加</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button 
              type="success" 
              link 
              :disabled="isGridItemExisting(scope.row.id)"
              @click="() => addToPage(scope.row)"
            >
              {{ isGridItemExisting(scope.row.id) ? '已添加' : '添加到页面' }}
            </el-button>
            <el-button type="primary" link @click="() => copyToPage(scope.row)">拷贝到页面</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 批量操作按钮 -->
      <div class="batch-actions" v-if="selectedGrids.length > 0">
        <el-button type="primary" @click="batchAddToPage">批量添加到页面</el-button>
      </div>
    </el-card>

    <!-- 网格信息编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="currentGridInfo.id ? '编辑网格信息' : '新增网格信息'"
      width="50%"
      destroy-on-close
    >
      <el-form :model="currentGridInfo" label-width="100px" :rules="formRules" ref="gridFormRef">
        <el-form-item label="名称" prop="name">
          <el-input v-model="currentGridInfo.name" placeholder="请输入名称" />
        </el-form-item>
        <!-- <el-form-item label="X坐标" prop="x">
          <el-input-number v-model="currentGridInfo.x" :min="0" :max="11" />
        </el-form-item>
        <el-form-item label="Y坐标" prop="y">
          <el-input-number v-model="currentGridInfo.y" :min="0" />
        </el-form-item>
        <el-form-item label="宽度" prop="w">
          <el-input-number v-model="currentGridInfo.w" :min="1" :max="12" />
        </el-form-item>
        <el-form-item label="高度" prop="h">
          <el-input-number v-model="currentGridInfo.h" :min="1" :max="12" />
        </el-form-item> -->
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="currentGridInfo.status">
            <el-radio value=1>启用</el-radio>
            <el-radio value=0>禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="currentGridInfo.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveGridInfo">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  getGridInfoList, 
  createGridInfo, 
  // getGridInfo, 
  updateGridInfo, 
  // deleteGridInfo as removeGridInfo,
  // updateGridInfoStatus
} from '../api/gridInfos';

// 定义props
const props = defineProps({
  // 已存在的网格项列表
  // existingGridItems: {
  //   type: Array,
  //   default: () => []
  // },
  store: {
    type: Object,
    required: true
  }
});

interface GridContent {
  id: number;
  type: string;
  title: string;
  icon: string;
  showTitle: boolean;
  refreshable: boolean;
  configurable: boolean;
  closable: boolean;
  config: object;
}
// 定义网格信息接口
interface GridInfo {
  id: number;
  uiConfigId: number;
  name: string;
  content: GridContent | string;
  locked: boolean;
  no_move: boolean;
  no_resize: boolean;
  auto_position: boolean;
  position?: Position;
  api: string;
  dto: string;
  remark: string;
  status: number;
  step: number[];
  created_at: string;
  updated_at: string;
}

interface Position {
  x: number;
  y: number;
  w: number;
  h: number;
}

// 定义API响应类型
// interface ApiResponse<T> {
//   data: T;
//   code: number;
//   message: string;
// }

// interface PageResult<T> {
//   records: T[];
//   total: number;
//   size: number;
//   current: number;
//   pages: number;
// }

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: ''
});

// 使用props传入的store实例
const store = props.store;

// 表单校验规则
const formRules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  x: [{ required: true, message: '请输入X坐标', trigger: 'blur' }],
  y: [{ required: true, message: '请输入Y坐标', trigger: 'blur' }],
  w: [{ required: true, message: '请输入宽度', trigger: 'blur' }],
  h: [{ required: true, message: '请输入高度', trigger: 'blur' }]
};

// 列表数据
const gridInfoList = ref<GridInfo[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const selectedGrids = ref<GridInfo[]>([]);

// 对话框状态
const dialogVisible = ref(false);
const gridFormRef = ref();

// 计算属性：判断网格项是否已存在于页面
const isGridItemExisting = (id: number): boolean => {
  // 检查id是否在existingGridItems中存在
  if (!store.gridLayoutStore.structureData) return false;
  return store.gridLayoutStore.structureData.layout.some((item: any) => item && getRealId(item.i) === id);
};

const getRealId = (id: string | number) => {
  if (typeof id === 'number') {
    return id;
  } else {
    return Number(id);
  }
}

// 当前编辑的网格信息
const defaultGridInfo = (): Partial<GridInfo> => ({
  name: '',
  content: {
    id: -1,
    type: 'info',
    title: '新组件',
    icon: 'Monitor',
    showTitle: true,
    refreshable: true,
    configurable: true,
    closable: true,
    config: {}  
  },
  locked: false,
  no_move: false,
  no_resize: false,
  auto_position: true,
  api: '',
  dto: '',
  remark: '',
  step: [0],
  status: 1
});

const currentGridInfo = ref<Partial<GridInfo>>(defaultGridInfo());

/**
 * 获取网格信息列表
 */
const fetchGridInfoList = async () => {
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchForm.keyword || undefined,
      status: searchForm.status || undefined
    };
    
    const response :any = await getGridInfoList(params);
    console.log('response', response);
    if (response) {
      gridInfoList.value = response.list || [];
      total.value = response.total || 0;
    }
  } catch (error: any) {
    console.error('获取网格信息列表失败:', error);
    ElMessage.error(`获取网格信息列表失败: ${error.message}`);
  }
};

/**
 * 创建新的网格信息
 */
// const createNewGridInfo = () => {
//   currentGridInfo.value = defaultGridInfo();
//   dialogVisible.value = true;
// };

/**
 * 编辑网格信息
 */
// const editGridInfo = async (gridInfo: GridInfo) => {
//   try {
//     const response = await getGridInfo(gridInfo.id) as ApiResponse<GridInfo>;
//     if (response && response.data) {
//       currentGridInfo.value = response.data;
//       dialogVisible.value = true;
//     }
//   } catch (error: any) {
//     console.error('获取网格信息详情失败:', error);
//     ElMessage.error(`获取网格信息详情失败: ${error.message}`);
//   }
// };

/**
 * 保存网格信息
 */
const saveGridInfo = async () => {
  // 表单验证
  gridFormRef.value?.validate(async (valid: boolean) => {
    if (!valid) return;
    
    try {
      if (currentGridInfo.value.id) {
        // 更新
        await updateGridInfo(currentGridInfo.value.id, currentGridInfo.value);
        ElMessage.success('更新网格信息成功');
      } else {
        // 创建
        await createGridInfo(currentGridInfo.value);
        ElMessage.success('创建网格信息成功');
      }
      
      dialogVisible.value = false;
      fetchGridInfoList(); // 刷新列表
    } catch (error: any) {
      console.error('保存网格信息失败:', error);
      ElMessage.error(`保存网格信息失败: ${error.message}`);
    }
  });
};

/**
 * 更新网格信息状态
 */
// const updateStatus = async (id: number, status: number) => {
//   try {
//     await updateGridInfoStatus(id, { status: status.toString() });
//     ElMessage.success(`${status === 1 ? '启用' : '禁用'}成功`);
//     fetchGridInfoList(); // 刷新列表
//   } catch (error: any) {
//     console.error('更新状态失败:', error);
//     ElMessage.error(`更新状态失败: ${error.message}`);
//   }
// };

/**
 * 删除网格信息
 */
// const deleteGridInfo = (id: number) => {
//   ElMessageBox.confirm('确定要删除该网格信息吗？', '提示', {
//     confirmButtonText: '确定',
//     cancelButtonText: '取消',
//     type: 'warning'
//   }).then(async () => {
//     try {
//       await removeGridInfo(id);
//       ElMessage.success('删除成功');
//       fetchGridInfoList(); // 刷新列表
//     } catch (error: any) {
//       console.error('删除失败:', error);
//       ElMessage.error(`删除失败: ${error.message}`);
//     }
//   }).catch(() => {
//     // 取消删除
//   });
// };

/**
 * 将网格添加到页面
 */
const addToPage = (gridInfo: GridInfo) => {
  // 检查网格项是否已存在
  console.log('gridInfo', gridInfo);
  if (isGridItemExisting(Number(gridInfo.id))) {
    ElMessage.warning(`组件"${gridInfo.name}"已存在于页面，无需重复添加`);
    return;
  }

  // 使用事件总线发送事件，通知父组件添加此网格项到页面
  emit('add-grid-to-page', gridInfo);
  ElMessage.success(`已添加"${gridInfo.name}"到页面`);
};

/**
 * 将网格复制到页面
 */
const copyToPage = (gridInfo: GridInfo) => {
  gridInfo.id = -1;
  // 使用事件总线发送事件，通知父组件添加此网格项到页面
  emit('add-grid-to-page', gridInfo);
  ElMessage.success(`已添加"${gridInfo.name}"到页面`);
};

/**
 * 批量添加到页面
 */
const emit = defineEmits(['add-grids-to-page', 'copy-grids-to-page', 'add-grid-to-page']);
const batchAddToPage = () => {
  console.log('[GridInfoManagement] batchAddToPage', store.gridLayoutStore.structureData.layout);
  // 过滤掉已经存在于页面的网格项
  const filteredGrids = selectedGrids.value.filter((grid: GridInfo) => !isGridItemExisting(Number(grid.id)));
  
  if (!filteredGrids || filteredGrids.length === 0) {
    ElMessage.warning('选中的网格项已全部添加到页面，无需重复添加');
    return;
  }
  console.log('[GridInfoManagement] batchAddToPage', store.gridLayoutStore.structureData.layout, filteredGrids);
  // 使用emit事件通知父组件批量添加网格项到页面
  emit('add-grids-to-page', filteredGrids);
  ElMessage.success(`已批量添加${filteredGrids.length}个组件到页面`);
};

/**
 * 处理表格选择变化
 */
const handleSelectionChange = (selection: GridInfo[]) => {
  selection.map(item => {
    item.position = typeof item.position === 'string' ? JSON.parse(item.position) : item.position;
    if(!item.position) {
      item.position = { x: 0, y: 0, w: 4, h: 4 };
    }
    item.content = typeof item.content === 'string' ? JSON.parse(item.content) : item.content;
  });
  console.log('[GridInfoManagement] handleSelectionChange', selection, store.gridLayoutStore.structureData);
  selectedGrids.value = selection;
};

/**
 * 搜索处理
 */
const handleSearch = () => {
  currentPage.value = 1; // 重置页码
  fetchGridInfoList();
};

/**
 * 重置搜索
 */
const resetSearch = () => {
  searchForm.keyword = '';
  searchForm.status = '';
  currentPage.value = 1;
  fetchGridInfoList();
};

/**
 * 分页大小变化处理
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  fetchGridInfoList();
};

/**
 * 当前页变化处理
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  fetchGridInfoList();
};

// 初始化
onMounted(() => {
  fetchGridInfoList();
});
</script>

<style scoped>
.grid-info-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.batch-actions {
  margin-top: 20px;
}
</style> 