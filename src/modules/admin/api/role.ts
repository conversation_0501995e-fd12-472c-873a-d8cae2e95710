/**
 * 角色管理相关API
 */

import { get, post, put, del } from '@/modules/admin/utils/request';
import type { Role, RoleListParams } from '../types';

/**
 * 获取角色列表
 * @param params 查询参数
 */
export function getRoleList(params: RoleListParams) {
  return get('/v1/admin/secured/roles', params);
}

/**
 * 获取角色详情
 * @param id 角色ID
 */
export function getRole(id: number) {
  return get(`/v1/admin/secured/roles/${id}`);
}

/**
 * 创建角色
 * @param data 角色信息
 */
export function createRole(data: Partial<Role>) {
  return post<Role>('/v1/admin/secured/roles', data);
}

/**
 * 更新角色
 * @param id 角色ID
 * @param data 角色信息
 */
export function updateRole(id: number, data: Partial<Role>) {
  return put<Role>(`/v1/admin/secured/roles/${id}`, data);
}

/**
 * 删除角色
 * @param id 角色ID
 */
export function deleteRole(id: number) {
  return del(`/v1/admin/secured/roles/${id}`);
}

/**
 * 分配角色
 * @param data 分配角色参数
 */
export function assignRole(data: { userId: number; roleId: number }) {
  return post('/v1/admin/secured/roles/assign', data);
}

/**
 * 为角色分配权限
 * @param roleId 角色ID
 * @param data 权限ID列表
 */
export function assignPermissions(roleId: number, data: { permissionIds: number[] }) {
  return put(`/v1/admin/secured/roles/${roleId}/permissions`, data);
}