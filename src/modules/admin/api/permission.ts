/**
 * 权限管理相关API
 */

import { get, post, put, del } from '@/modules/admin/utils/request';
import type { Permission, PermissionListParams } from '../types';

/**
 * 获取权限列表
 * @param params 查询参数
 */
export function getPermissionList(params: PermissionListParams) {
  return get('/v1/admin/secured/permissions', params);
}

/**
 * 获取权限详情
 * @param id 权限ID
 */
export function getPermission(id: number) {
  return get(`/v1/admin/secured/permissions/${id}`);
}

/**
 * 创建权限
 * @param data 权限信息
 */
export function createPermission(data: Partial<Permission>) {
  return post<Permission>('/v1/admin/secured/permissions', data);
}

/**
 * 更新权限
 * @param id 权限ID
 * @param data 权限信息
 */
export function updatePermission(id: number, data: Partial<Permission>) {
  return put<Permission>(`/v1/admin/secured/permissions/${id}`, data);
}

/**
 * 删除权限
 * @param id 权限ID
 */
export function deletePermission(id: number) {
  return del(`/v1/admin/secured/permissions/${id}`);
}