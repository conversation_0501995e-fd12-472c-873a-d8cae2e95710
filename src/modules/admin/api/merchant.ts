/**
 * 商户管理相关API
 */

import { get, post, put } from '@/modules/admin/utils/request';
import type { ApiResponse } from '@/types';

/**
 * 获取商户列表
 * @param params 查询参数
 */
export function getMerchantList(params: {
  page: number;
  page_size: number;
  audit_status?: number;
}) {
  return get('/v1/admin/secured/merchants', params);
}

/**
 * 创建商户
 * @param data 商户信息
 */
export function createMerchant(data: {
  name: string;
  contact_name: string;
  contact_mobile: string;
  contact_email: string;
  address: string;
  description: string;
  is_recommended?: number;
}) {
  return post('/v1/admin/secured/merchants/add', data);
}

/**
 * 更新商户信息
 * @param id 商户ID
 * @param data 商户信息
 */
export function updateMerchant(id: number, data: any) {
  return put(`/v1/admin/secured/merchants/${id}`, data);
}

/**
 * 审核商户
 * @param id 商户ID
 * @param data 审核信息
 */
export function auditMerchant(id: number, data: {
  audit_status: string;
  reason?: string;
}) {
  return put(`/v1/admin/secured/merchants/${id}/audit`, data);
}

/**
 * 修改商家密码
 * @param id 商户ID
 * @param data 新密码信息
 */
export function updateMerchantPassword(id: number, data: {
  new_password: string;
  confirm_password: string;
}) {
  return put(`/v1/admin/secured/merchants/${id}/password`, data);
}

/**
 * 更新商户状态
 * @param id 商户ID
 * @param data 状态信息
 */
export function updateMerchantStatus(id: number, data: {
  status: number;
}) {
  return put(`/v1/admin/secured/merchants/${id}/status`, data);
}

/**
 * 更新商户审核状态
 * @param id 商户ID
 * @param data 状态信息
 */
export function updateMerchantAudit(id: number, data: {
  audit_status: number;
  reason?: string;
}) {
  return put(`/v1/admin/secured/merchants/${id}/audit`, data);
}

/**
 * 更新商户经营状态
 * @param id 商户ID
 * @param data 状态信息
 */
export function updateMerchantOperationStatus(id: number, data: {
  operation_status: number;
}) {
  return put(`/v1/admin/secured/merchants/${id}/operation-status`, data);
}

/**
 * 获取商户详情
 * @param id 商户ID
 */
export function getMerchantDetail(id: number): Promise<ApiResponse<any>> {
  return get(`/v1/admin/secured/merchants/${id}`);
}

/**
 * 更新商家坐标
 * @param id 商户ID
 * @param data 坐标信息
 */
export function updateMerchantCoordinates(id: number, data: {
  longitude: number;
  latitude: number;
}): Promise<ApiResponse<any>> {
  return put(`/v1/admin/merchants/${id}/coordinates`, data);
}

/**
 * 获取商户商品列表
 * @param id 商户ID
 */
export function getMerchantFoods(id: number): Promise<ApiResponse<any>> {
  return get(`/v1/admin/takeout/merchant/${id}/foods`);
}

/**
 * 获取单个外卖商品统计信息
 * @param id 商户ID
 */
export function getAdminMerchantStatistics<T = any>(id: number): Promise<T> {
  return get<T>(`/v1/admin/takeout/merchant/${id}/statistics`);
}

/**
 * 更新商家推荐状态
 * @param id 商家ID
 * @param isRecommended 是否推荐：0-否，1-是
 */
export function updateMerchantRecommendStatus(
  id: number, 
  isRecommended: number
): Promise<ApiResponse<any>> {
  return put(`/v1/admin/secured/merchants/${id}/recommend`, {
    is_recommended: isRecommended
  });
}