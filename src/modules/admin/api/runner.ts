/**
 * 跑腿员管理相关API
 * <AUTHOR>
 * @date 2025-01-20
 * @version 1.0.0
 * @description 管理员对跑腿员进行管理的API接口，包括查看、审核、暂停、删除、黑名单等操作
 */

import { get, put, del } from '@/modules/admin/utils/request';

/**
 * 跑腿员信息接口
 */
export interface Runner {
  id: number;
  user_id: number;
  real_name: string;
  id_card_number?: string;
  id_card_front_pic?: string;
  id_card_back_pic?: string;
  face_pic?: string;
  mobile: string;
  status: number;
  status_desc: string;
  current_location?: string;
  latitude?: number;
  longitude?: number;
  is_online: boolean;
  working_status: number;
  working_status_desc: string;
  score: number;
  order_count: number;
  success_count: number;
  cancel_count: number;
  balance: number;
  wallet?: number;
  area_codes?: string;
  service_radius: number;
  remark?: string;
  frontend_remark?: string;
  admin_remark?: string;
  total_orders?: number;
  completed_orders?: number;
  join_time: string;
  last_login_time?: string;
  last_online_time?: string;
  create_time: string;
  update_time: string;
}

/**
 * 跑腿员列表查询参数
 */
export interface RunnerListParams {
  page?: number;
  pageSize?: number;
  status?: number;
  keyword?: string;
}

/**
 * 跑腿员列表响应
 */
export interface RunnerListResponse {
  list: Runner[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * 跑腿员统计信息
 */
export interface RunnerStatistics {
  total: number;
  pending: number;
  active: number;
  suspended: number;
  banned: number;
  rejected: number;
  today_new: number;
  today_approved: number;
  today_rejected: number;
  total_runners: number;
  pending_audit: number;
  approved_runners: number;
  rejected_runners: number;
  suspended_runners: number;
  blacklist_runners: number;
  online_runners: number;
  working_runners: number;
  today_new_runners: number;
  this_week_new_runners: number;
  this_month_new_runners: number;
}

/**
 * 跑腿员审核参数
 */
export interface RunnerAuditParams {
  status: number;
  reject_reason?: string;
  remark?: string;
}

/**
 * 跑腿员状态更新参数
 */
export interface RunnerStatusParams {
  status: number;
  reason?: string;
  remark?: string;
}

/**
 * 跑腿员备注更新参数
 */
export interface RunnerRemarkParams {
  remark?: string;
}

/**
 * 跑腿员订单信息
 */
export interface RunnerOrder {
  id: number;
  order_no: string;
  order_type: number;
  user_id: number;
  runner_id: number;
  status: number;
  status_desc: string;
  payment_status: number;
  payment_status_desc: string;
  amount: number;
  delivery_fee: number;
  tip_amount: number;
  pickup_address: string;
  delivery_address: string;
  goods_description: string;
  created_at: string;
  completed_at?: string;
}

/**
 * 获取跑腿员列表
 * @param params 查询参数
 */
export function getRunnerList(params: RunnerListParams): Promise<RunnerListResponse> {
  return get('/v1/admin/secured/runners', params);
}

/**
 * 获取跑腿员详细信息
 * @param id 跑腿员ID
 */
export function getRunnerDetail(id: number): Promise<Runner> {
  return get(`/v1/admin/secured/runners/${id}/detail`);
}

/**
 * 审核跑腿员
 * @param id 跑腿员ID
 * @param data 审核信息
 */
export function auditRunner(id: number, data: RunnerAuditParams): Promise<void> {
  return put(`/v1/admin/secured/runners/${id}/audit`, data);
}

/**
 * 更新跑腿员状态
 * @param id 跑腿员ID
 * @param data 状态信息
 */
export function updateRunnerStatus(id: number, data: RunnerStatusParams): Promise<void> {
  return put(`/v1/admin/secured/runners/${id}/status`, data);
}

/**
 * 删除跑腿员
 * @param id 跑腿员ID
 */
export function deleteRunner(id: number): Promise<void> {
  return del(`/v1/admin/secured/runners/${id}`);
}

/**
 * 获取跑腿员统计信息
 */
export function getRunnerStatistics(): Promise<RunnerStatistics> {
  return get('/v1/admin/secured/runners/statistics');
}

/**
 * 更新跑腿员备注
 * @param id 跑腿员ID
 * @param data 备注信息
 */
export function updateRunnerRemark(id: number, data: RunnerRemarkParams): Promise<void> {
  return put(`/v1/admin/secured/runners/${id}/remark`, data);
}

/**
 * 获取跑腿员订单列表
 * @param id 跑腿员ID
 * @param params 查询参数
 */
export function getRunnerOrders(id: number, params?: { page?: number; size?: number }): Promise<{ list: RunnerOrder[]; total: number; page: number; size: number; totalPages: number }> {
  return get(`/v1/admin/secured/runners/${id}/orders`, params);
}

/**
 * 跑腿员状态枚举
 */
export const RunnerStatus = {
  PENDING: 0,     // 待审核
  APPROVED: 1,    // 审核通过
  REJECTED: 2,    // 审核拒绝
  SUSPENDED: 3,   // 暂停服务
  BLACKLIST: 4    // 黑名单
} as const;

/**
 * 工作状态枚举
 */
export const WorkingStatus = {
  OFFLINE: 0,     // 离线
  IDLE: 1,        // 空闲中
  WORKING: 2,     // 工作中
  RESTING: 3      // 休息中
} as const;

/**
 * 订单状态枚举
 */
export const OrderStatus = {
  PENDING: 1,     // 待接单
  ACCEPTED: 2,    // 已接单
  PICKING: 3,     // 取货中
  DELIVERING: 4,  // 配送中
  CANCELLED: 5,   // 已取消
  COMPLETED: 6    // 已完成
} as const;

/**
 * 获取状态描述
 * @param status 状态值
 */
export function getRunnerStatusDesc(status: number): string {
  const statusMap: Record<number, string> = {
    [RunnerStatus.PENDING]: '待审核',
    [RunnerStatus.APPROVED]: '审核通过',
    [RunnerStatus.REJECTED]: '审核拒绝',
    [RunnerStatus.SUSPENDED]: '暂停服务',
    [RunnerStatus.BLACKLIST]: '黑名单'
  };
  return statusMap[status] || '未知状态';
}

/**
 * 获取工作状态描述
 * @param status 工作状态值
 */
export function getWorkingStatusDesc(status: number): string {
  const statusMap: Record<number, string> = {
    [WorkingStatus.OFFLINE]: '离线',
    [WorkingStatus.IDLE]: '空闲中',
    [WorkingStatus.WORKING]: '工作中',
    [WorkingStatus.RESTING]: '休息中'
  };
  return statusMap[status] || '未知状态';
}

/**
 * 获取订单状态描述
 * @param status 订单状态值
 */
export function getOrderStatusDesc(status: number): string {
  const statusMap: Record<number, string> = {
    [OrderStatus.PENDING]: '待接单',
    [OrderStatus.ACCEPTED]: '已接单',
    [OrderStatus.PICKING]: '取货中',
    [OrderStatus.DELIVERING]: '配送中',
    [OrderStatus.CANCELLED]: '已取消',
    [OrderStatus.COMPLETED]: '已完成'
  };
  return statusMap[status] || '未知状态';
}