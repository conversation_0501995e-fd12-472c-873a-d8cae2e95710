import request from '@/modules/admin/utils/request';


/**
 * 获取指定模块的API文档
 * @param module 模块名称
 * @returns 返回API文档数据
 */
export function getApiDoc(module: object) {
    return request({
      url: '/v1/apidoc/apis',
      method: 'get',
      params: { ...module }
    });
  }
  
  /**
   * 获取指定模块的分组名列表
   * @param module 模块名称
   * @returns 返回分组名列表
   */
  export async function getGroupNames(module: object): Promise<string[]> {
    //console.log('【调试】getGroupNames params:', module);
    return request({
      url: '/v1/apidoc/api-pages',
      method: 'get',
      params: { ...module }
    });
  }

  /**
   * 获取指定模块的接口列表
   * @param module 模块名称
   * @returns 返回接口列表
   */
  export async function getDtos(module: object): Promise<string[]> {
    return request({
      url: '/v1/apidoc/dtos',
      method: 'get',
      params: { ...module }
    });
  }

  /**
   * 获取指定模块的控制器列表
   * @param module 模块名称
   * @returns 返回控制器列表
   */
  export async function getControllers(module: object): Promise<string[]> {
    return request({
      url: '/v1/apidoc/controllers',
      method: 'get',
      params: { ...module }
    });
  }
  
  