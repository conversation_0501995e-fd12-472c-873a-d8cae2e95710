/**
 * 分销管理相关API
 * 作者：张二浩
 * 日期：2025-01-15
 * 版本：1.0.0
 * 描述：提供分销配置、分销关系、分销统计等相关API接口
 */

import { get, post, put, del } from '@/modules/admin/utils/request';

// ===================== 类型定义 =====================

/**
 * 分销等级配置
 */
export interface ReferralLevel {
  id?: number;
  level: number;
  name: string;
  code?: string;
  required_referrals: number;
  commission_rate: number;
  description: string;
  icon?: string;
  icon_color?: string;
  upgrade_bonus?: number;
  monthly_bonus?: number;
  is_active?: boolean;
}

/**
 * 佣金配置
 */
export interface CommissionConfig {
  level1_rate: number;
  level2_rate: number;
  level3_rate: number;
  first_level_rate?: number;
  second_level_rate?: number;
  third_level_rate?: number;
  max_levels: number;
  min_order_amount: number;
  settlement_period?: string;
  settlement_delay_days?: number;
  withdrawal_threshold?: number;
  commission_validity_days?: number;
  relationship_validity_days?: number;
  auto_approve?: boolean;
  auto_approve_threshold?: number;
  allow_self_referral?: boolean;
  allow_duplicate_referral?: boolean;
  description?: string;
}

/**
 * 分销关系
 */
export interface ReferralRelationship {
  id: number;
  referrer_id: number;
  referee_id: number;
  referrer_name: string;
  referee_name: string;
  status: string;
  level?: number;
  remark?: string;
  commission_earned?: number;
  effective_at?: string;
  expires_at?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

/**
 * 分销统计数据
 */
export interface ReferralStatistics {
  total_referrers: number;
  total_referees: number;
  total_commission: number;
  active_referrers: number;
}

/**
 * 审核日志
 */
export interface AuditLog {
  id: number;
  action: string;
  operator: string;
  created_at: string;
  remark?: string;
}

/**
 * 佣金记录
 */
export interface CommissionRecord {
  id: number;
  user_id: number;
  user_name: string;
  order_id: string;
  commission_type: string;
  type: string;
  level: number;
  amount: number;
  rate: number;
  order_amount: number;
  status: string;
  created_at: string;
  updated_at: string;
  remark?: string;
  audit_logs?: AuditLog[];
}

/**
 * 用户信息
 */
export interface User {
  id: number;
  username: string;
  nickname: string;
  avatar?: string;
  name?: string;
  phone?: string;
  referral_count?: number;
  commission_earned?: number;
}

/**
 * 查询参数
 */
export interface ReferralQueryParams {
  page?: number;
  pageSize?: number;
  referrer_id?: string;
  referee_id?: string;
  status?: string;
  keyword?: string;
}

// ===================== 分销配置管理 =====================

/**
 * 初始化系统默认的分销配置数据
 * @returns 初始化结果
 */
export function initializeReferralConfigs() {
  return post('/v1/admin/secured/referral/configs/initialize');
}

/**
 * 获取分销等级配置
 * @returns 分销等级配置列表
 */
export function getReferralLevels() {
  return get('/v1/admin/secured/referral/config/levels');
}

/**
 * 更新分销等级配置
 * @param data 分销等级配置数据
 * @returns 更新结果
 */
export function updateReferralLevels(data: { levels: ReferralLevel[] }) {
  return put('/v1/admin/secured/referral/config/levels', data);
}

/**
 * 获取佣金比例配置
 * @returns 佣金配置
 */
export function getCommissionConfig(): Promise<CommissionConfig> {
  return get('/v1/admin/secured/referral/config/commission-rates');
}

/**
 * 更新佣金比例配置
 * @param data 佣金配置数据
 * @returns 更新结果
 */
export function updateCommissionConfig(data: CommissionConfig) {
  return put('/v1/admin/secured/referral/config/commission-rates', data);
}

// ===================== 分销关系管理 =====================

/**
 * 获取分销关系列表
 * @param params 查询参数
 * @returns 分销关系列表
 */
export function getReferralRelationships(params: ReferralQueryParams) {
  return get('/v1/admin/secured/referral/relationships', params);
}

/**
 * 创建分销关系
 * @param data 分销关系数据
 * @returns 创建结果
 */
export function createReferralRelationship(data: {
  referrer_id: number;
  referee_id: number;
}) {
  return post('/v1/admin/secured/referral/relationships', data);
}

/**
 * 删除分销关系
 * @param id 分销关系ID
 * @returns 删除结果
 */
export function deleteReferralRelationship(id: number) {
  return del(`/v1/admin/secured/referral/relationships/${id}`);
}

/**
 * 更新分销关系状态
 * @param id 分销关系ID
 * @param data 状态数据
 * @returns 更新结果
 */
export function updateReferralRelationshipStatus(id: number, data: {
  status: string;
}) {
  return put(`/v1/admin/secured/referral/relationships/${id}/status`, data);
}

// ===================== 分销统计 =====================

/**
 * 获取分销统计数据
 * @param params 查询参数
 * @returns 统计数据
 */
export function getReferralStatistics(params?: {
  start_date?: string;
  end_date?: string;
  type?: string;
}) {
  return get('/v1/admin/secured/referral/statistics', params);
}

/**
 * 获取分销图表数据
 * @param params 查询参数
 * @returns 图表数据
 */
export function getReferralChartData(params?: {
  start_date?: string;
  end_date?: string;
  chart_type?: string;
}) {
  return get('/v1/admin/secured/referral/charts', params);
}

/**
 * 导出分销数据
 * @param params 导出参数
 * @returns 导出结果
 */
export function exportReferralData(params?: {
  start_date?: string;
  end_date?: string;
  type?: string;
  format?: string;
  date_range?: string;
}) {
  return get('/v1/admin/secured/referral/export', params);
}

// ===================== 分销佣金管理 =====================

/**
 * 佣金记录查询参数
 */
export interface CommissionQueryParams {
  page?: number;
  pageSize?: number;
  size?: number;
  user_id?: number | string;
  order_id?: string;
  status?: string;
  type?: string;
  start_date?: string;
  end_date?: string;
  date_range?: [Date, Date] | null;
  [key: string]: any;
}

/**
 * 佣金记录列表响应
 */
export interface CommissionRecordsResponse {
  items: CommissionRecord[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * 获取佣金记录列表
 * @param params 查询参数
 * @returns 佣金记录列表
 */
export function getCommissionRecords(params: CommissionQueryParams): Promise<CommissionRecordsResponse> {
  return get('/v1/admin/secured/referral/commission', params);
}

/**
 * 审核佣金记录
 * @param id 佣金记录ID
 * @param data 审核数据
 * @returns 审核结果
 */
export function auditCommissionRecord(id: number, data: {
  status: string;
  remark?: string;
}) {
  return put(`/v1/admin/secured/referral/commissions/${id}/audit`, data);
}

/**
 * 批量审核佣金记录
 * @param data 批量审核数据
 * @returns 审核结果
 */
export function batchAuditCommissionRecords(data: {
  ids: number[];
  status: string;
  remark?: string;
}) {
  return put('/v1/admin/secured/referral/commissions/batch-audit', data);
}

// ===================== 额外的API接口 =====================

/**
 * 获取分销趋势数据
 * @param params 查询参数
 * @returns 趋势数据
 */
export function getReferralTrendData(params?: {
  start_date?: string;
  end_date?: string;
}) {
  return get('/v1/admin/secured/referral/trend', params);
}

/**
 * 获取分销等级分布数据
 * @param params 查询参数
 * @returns 等级分布数据
 */
export function getReferralLevelDistribution(params?: {
  start_date?: string;
  end_date?: string;
}) {
  return get('/v1/admin/secured/referral/level-distribution', params);
}

/**
 * 获取佣金统计数据
 * @param params 查询参数
 * @returns 佣金统计数据
 */
export function getCommissionStatistics(params?: {
  start_date?: string;
  end_date?: string;
}) {
  return get('/v1/admin/secured/referral/commission-statistics', params);
}

/**
 * 获取TOP推荐人数据
 * @param params 查询参数
 * @returns TOP推荐人数据
 */
export function getTopReferrers(params?: {
  start_date?: string;
  end_date?: string;
  limit?: number;
}) {
  return get('/v1/admin/secured/referral/top-referrers', params);
}

/**
 * 佣金汇总数据
 */
export interface CommissionSummary {
  total_amount: number;
  pending_amount: number;
  paid_amount: number;
  frozen_amount: number;
}

/**
 * 获取佣金汇总数据
 * @param params 查询参数
 * @returns 佣金汇总数据
 */
export function getCommissionSummary(params?: {
  start_date?: string;
  end_date?: string;
}): Promise<CommissionSummary> {
  return get('/v1/admin/secured/referral/commission-summary', params);
}

/**
 * 审核佣金记录
 * @param id 佣金记录ID
 * @param data 审核数据
 * @returns 审核结果
 */
export function approveCommissionRecord(id: number, data?: {
  remark?: string;
}) {
  return put(`/v1/admin/secured/referral/commissions/${id}/approve`, data);
}

/**
 * 拒绝佣金记录
 * @param id 佣金记录ID
 * @param data 拒绝数据
 * @returns 拒绝结果
 */
export function rejectCommissionRecord(id: number, data?: {
  remark?: string;
}) {
  return put(`/v1/admin/secured/referral/commissions/${id}/reject`, data);
}

/**
 * 冻结佣金记录
 * @param id 佣金记录ID
 * @param data 冻结数据
 * @returns 冻结结果
 */
export function freezeCommissionRecord(id: number, data?: {
  remark?: string;
}) {
  return put(`/v1/admin/secured/referral/commissions/${id}/freeze`, data);
}

/**
 * 批量审核通过佣金记录
 * @param data 批量审核数据
 * @returns 审核结果
 */
export function batchApproveCommissions(data: {
  ids: number[];
  remark?: string;
}) {
  return put('/v1/admin/secured/referral/commissions/batch-approve', data);
}

/**
 * 批量拒绝佣金记录
 * @param data 批量拒绝数据
 * @returns 拒绝结果
 */
export function batchRejectCommissions(data: {
  ids: number[];
  remark?: string;
}) {
  return put('/v1/admin/secured/referral/commissions/batch-reject', data);
}

/**
 * 批量冻结佣金记录
 * @param data 批量冻结数据
 * @returns 冻结结果
 */
export function batchFreezeCommissions(data: {
  ids: number[];
  remark?: string;
}) {
  return put('/v1/admin/secured/referral/commissions/batch-freeze', data);
}

/**
 * 导出佣金记录
 * @param params 导出参数
 * @returns 导出结果
 */
export function exportCommissionRecords(params?: {
  start_date?: string;
  end_date?: string;
  status?: string;
  format?: string;
}) {
  return get('/v1/admin/secured/referral/commissions/export', params);
}

/**
 * 审核佣金
 * @param id 佣金ID
 * @param data 审核数据
 * @returns 审核结果
 */
export function approveCommission(id: number, data?: {
  remark?: string;
}) {
  return put(`/v1/admin/secured/referral/commissions/${id}/approve`, data);
}

/**
 * 拒绝佣金
 * @param id 佣金ID
 * @param data 拒绝数据
 * @returns 拒绝结果
 */
export function rejectCommission(id: number, data?: {
  remark?: string;
}) {
  return put(`/v1/admin/secured/referral/commissions/${id}/reject`, data);
}

/**
 * 发放佣金
 * @param id 佣金ID
 * @param data 发放数据
 * @returns 发放结果
 */
export function payCommission(id: number, data?: {
  remark?: string;
}) {
  return put(`/v1/admin/secured/referral/commissions/${id}/pay`, data);
}

/**
 * 冻结佣金
 * @param id 佣金ID
 * @param data 冻结数据
 * @returns 冻结结果
 */
export function freezeCommission(id: number, data?: {
  remark?: string;
}) {
  return put(`/v1/admin/secured/referral/commissions/${id}/freeze`, data);
}