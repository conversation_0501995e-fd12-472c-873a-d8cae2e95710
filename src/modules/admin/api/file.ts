/**
 * 文件上传管理相关API
 */
import { get, post, del } from '@/modules/admin/utils/request';

/**
 * 上传文件
 * @param formData 包含文件和其他参数的FormData对象
 * @param config 请求配置，可包含onUploadProgress回调函数
 */
export function uploadFile(formData: FormData, config?: any) {
  return post('/v1/admin/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    ...config
  });
}

/**
 * 获取文件信息
 * @param id 文件ID
 */
export function getFileInfo(id: number | string) {
  return get(`/v1/admin/files/${id}`);
}

/**
 * 获取文件列表
 * @param params 查询参数
 */
export function getFileList(params?: {
  file_usage?: string;
  user_type?: string;
  user_id?: number;
  username?: string;
  is_anonymous?: boolean;
  start_time?: string;
  end_time?: string;
  page?: number;
  page_size?: number;
}) {
  return get('/v1/admin/files', params);
}

/**
 * 删除文件
 * @param id 文件ID
 */
export function deleteFile(id: number | string) {
  return del(`/v1/admin/files/${id}`);
}

/**
 * 获取上传配置
 */
export function getUploadConfig() {
  return get('/v1/admin/config');
}