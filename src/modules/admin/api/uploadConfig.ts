/**
 * @Description: 系统上传配置API接口
 * @Author: Cascade
 * @Date: 2025-06-07
 */

import { get, post } from '@/modules/admin/utils/request';

/**
 * 上传配置类型定义
 */
export interface UploadConfigType {
  id?: number | null;
  storageMode: 'local' | 'oss' | 'cos' | 's3' | 'qiniu';
  maxSize: number;
  maxSizeMB?: number;
  allowedExtensions: string;
  enableCdn: number;
  cdnDomain?: string;
  status: number;
  statusText?: string;
  remark?: string;
  createdAt?: string;
  updatedAt?: string;
  config: {
    localPath?: string;
    endpoint?: string;
    bucket?: string;
    accessKey?: string;
    accessSecret?: string;
    domain?: string;
    region?: string;
    secretId?: string;
    secretKey?: string;
    cdnAccessKey?: string;
    cdnSecretKey?: string;
    zone?: string; // 七牛云存储区域
    useHTTPS?: boolean; // 七牛云是否使用HTTPS
  };
}

/**
 * 获取上传配置
 * @returns Promise<UploadConfigType> 上传配置对象
 */
export function getUploadConfig() {
  return get<UploadConfigType>('/v1/system/secured/upload/config');
}

/**
 * 更新上传配置
 * @param data 上传配置数据
 * @returns Promise<any> 更新结果
 */
export function updateUploadConfig(data: UploadConfigType) {
  return post<any>('/v1/system/secured/upload/config', data);
}

/**
 * 测试存储服务连接
 * @param data 存储服务配置数据
 * @returns Promise<any> 测试结果
 */
export function testStorageConnection(data: UploadConfigType) {
  return post<any>('/v1/system/secured/upload/test-connection', data);
}
