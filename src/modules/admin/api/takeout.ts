/**
 * 管理员外卖商品管理相关API
 * 提供商品查询、审核等功能
 */

import { get, put } from '@/modules/admin/utils/request';

/**
 * 获取外卖商品列表
 * @param params 查询参数
 */
export function getAdminFoodList(params: {
  page: number;
  pageSize: number;
  status?: number;
  merchantId?: number;
  categoryId?: number;
  keyword?: string;
}) {
  return get('/v1/admin/takeout/foods', params);
}

/** 
 * 获取单个外卖商品详情
 * @param id 商品ID
 */
export function getAdminFoodDetail<T = any>(id: number): Promise<T> {
  return get<T>(`/v1/admin/takeout/foods/${id}`);
}

/**
 * 获取单个外卖商品统计信息
 * @param id 商户ID
 */
export function getAdminMerchantStatistics<T = any>(id: number): Promise<T> {
  return get<T>(`/v1/admin/takeout/merchant/${id}/statistics`);
}

/**
 * 获取外卖商品食品列表
 * @param params 查询参数
 */
export function getAdminMerchantFoodsList<T = any>(params: {
  merchantId: number;
}): Promise<T> {
  return get<T>(`/v1/admin/takeout/merchant/${params.merchantId}/foods`);
}

/**
 * 审核外卖商品
 * @param id 商品ID
 * @param data 审核信息
 */
export function auditFood(id: number, data: {
  audit_status: number; // 审核状态：0-未审核，1-审核通过，2-审核拒绝
  reason?: string; // 拒绝原因
}) {
  return put(`/v1/admin/takeout/foods/${id}/audit`, data);
}

/**
 * 获取外卖商品分类列表
 */
export function getAdminFoodCategories() {
  return get('/v1/admin/takeout/categories');
}

/**
 * 获取商品所有规格
 * @param foodId 商品ID
 */
export function getAdminFoodSpecs(foodId: number) {
  return get(`/v1/admin/takeout/foods/${foodId}/variants`);
}
