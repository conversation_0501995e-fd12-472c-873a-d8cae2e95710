/**
 * 产品管理相关API
 */

import { get, post, put, del } from '@/modules/admin/utils/request';

/**
 * 获取产品列表
 * @param params 查询参数
 */
export function getProductList(params: {
  page: number;
  pageSize: number;
  status?: string;
  categoryId?: number;
  keyword?: string;
  audit_status?: number;
}) {
  return get('/v1/admin/takeout/foods', params);
}

/**
 * 审核产品
 * @param id 产品ID
 * @param data 审核信息
 */
export function auditProduct(id: number, data: {
  audit_status: string;
  reason?: string;
  remark?: string;
}) {
  return post(`/v1/admin/secured/products/${id}/audit`, data);
}

/**
 * 更新产品状态
 * @param id 产品ID
 * @param data 状态信息
 */
export function updateProductStatus(id: number, data: {
  status: string;
}) {
  return put(`/v1/admin/secured/products/${id}/status`, data);
}

/**
 * 创建产品
 * @param data 产品信息
 */
export function createProduct(data: any) {
  return post('/v1/admin/secured/products', data);
}

/**
 * 更新产品
 * @param id 产品ID
 * @param data 产品信息
 */
export function updateProduct(id: number, data: any) {
  return put(`/v1/admin/secured/products/${id}`, data);
}

/**
 * 删除产品
 * @param id 产品ID
 */
export function deleteProduct(id: number) {
  return del(`/v1/admin/secured/products/${id}`);
}