/**
 * 管理员认证相关API
 */

import { post, get } from '@/modules/admin/utils/request';
import type { AdminLoginParams, AdminInfo } from '../types';

/**
 * 管理员登录
 * @param data 登录参数
 */
export function login(data: AdminLoginParams) {
  return post<{ 
    token_info: object; 
    admin: AdminInfo;
    device_id?: string;
    is_new_device?: boolean;
    risk_level?: string;
  }>('/v1/admin/login', data);
}

/**
 * 刷新令牌
 * @param data 刷新令牌参数
 */
export function refreshToken(data: { refresh_token: string }) {
  return post<{ token_info: object; admin: AdminInfo }>('/v1/admin/refresh-token', data);
}

/**
 * 管理员登出
 */
export function logout(deviceId: string) {
  return post(`/v1/admin/devices/${deviceId}/logout`);
}

/**
 * 获取当前管理员信息
 */
export function getCurrentAdmin() {
  return get<AdminInfo>('/v1/admin/secured/current');
}