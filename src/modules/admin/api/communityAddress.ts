/**
 * @Description: 社区地址管理API接口
 * @Author: AI Assistant
 * @Date: 2025-01-27
 * @Version: 1.0.0
 */

import request from '@/modules/admin/utils/request'

// 社区地址数据类型定义
export interface CommunityAddress {
  id?: number
  name: string
  parentId: number | null
  level: number // 1-小区，2-楼栋，3-单元
  longitude: number
  latitude: number
  fullPath?: string
  sort: number
  status: number // 1-启用，0-禁用
  description?: string
  createdAt?: string
  updatedAt?: string
  children?: CommunityAddress[]
}

// 创建社区地址请求参数
export interface CreateCommunityAddressRequest {
  name: string
  parentId?: number | null
  level: number
  longitude: number
  latitude: number
  sort?: number
  status?: number
  description?: string
}

// 更新社区地址请求参数
export interface UpdateCommunityAddressRequest {
  id: number
  name: string
  parentId?: number | null
  level: number
  longitude: number
  latitude: number
  sort?: number
  status?: number
  description?: string
}

// 查询参数
export interface CommunityAddressQueryParams {
  page?: number
  pageSize?: number
  name?: string
  level?: number
  parentId?: number
  status?: number
}

// 地址选择器选项
export interface CommunityAddressOption {
  value: number
  label: string
  children?: CommunityAddressOption[]
  longitude: number
  latitude: number
  level: number
}

// 分页响应
export interface CommunityAddressListResponse {
  items: CommunityAddress[]
  total: number
  page: number
  pageSize: number
}

// 树形结构响应
export interface CommunityAddressTreeResponse {
  items: CommunityAddress[]
}

// 地址选择器选项响应
export interface CommunityAddressOptionsResponse {
  options: CommunityAddressOption[]
}

// 完整地址信息请求
export interface SelectedCommunityAddressRequest {
  communityId: number
  buildingId: number
  unitId: number
}

// 完整地址信息响应
export interface SelectedCommunityAddressResponse {
  fullPath: string
  longitude: number
  latitude: number
}

// 级别选项
export const levelOptions = [
  { label: '小区', value: 1 },
  { label: '楼栋', value: 2 },
  { label: '单元', value: 3 }
]

// 状态选项
export const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
]

/**
 * @function getCommunityAddressList
 * @description 获取社区地址列表（分页）
 * @param params 查询参数
 * @returns Promise<CommunityAddressListResponse>
 */
export const getCommunityAddressList = (params: CommunityAddressQueryParams) => {
  return request.get<CommunityAddressListResponse>('/v1/admin/secured/addresses', { params })
}

/**
 * @function getCommunityAddressTree
 * @description 获取社区地址树形结构
 * @param parentId 父级ID，默认为0
 * @returns Promise<CommunityAddressTreeResponse>
 */
export const getCommunityAddressTree = (parentId: number = 0) => {
  return request.get<CommunityAddressTreeResponse>('/v1/admin/secured/addresses/tree', {
    params: { parentId }
  })
}

/**
 * @function getCommunityAddressOptions
 * @description 获取地址选择器选项
 * @returns Promise<CommunityAddressOptionsResponse>
 */
export const getCommunityAddressOptions = () => {
  return request.get<CommunityAddressOptionsResponse>('/v1/admin/secured/addresses/options')
}

/**
 * @function getCommunityAddressById
 * @description 根据ID获取社区地址详情
 * @param id 地址ID
 * @returns Promise<CommunityAddress>
 */
export const getCommunityAddressById = (id: number) => {
  return request.get<CommunityAddress>(`/v1/admin/secured/addresses/${id}`)
}

/**
 * @function getChildrenByParentId
 * @description 根据父级ID获取子地址列表
 * @param parentId 父级ID
 * @param level 可选的级别筛选
 * @returns Promise<CommunityAddress[]>
 */
export const getChildrenByParentId = (parentId: number, level?: number) => {
  const params = level ? { level } : {}
  return request.get<CommunityAddress[]>(`/v1/admin/secured/addresses/parent/${parentId}`, { params })
}

/**
 * @function createCommunityAddress
 * @description 创建社区地址
 * @param data 创建请求参数
 * @returns Promise<{ id: number }>
 */
export const createCommunityAddress = (data: CreateCommunityAddressRequest) => {
  return request.post<{ id: number }>(`/v1/admin/secured/addresses`, data)
}

/**
 * @function updateCommunityAddress
 * @description 更新社区地址
 * @param data 更新请求参数
 * @returns Promise<void>
 */
export const updateCommunityAddress = (data: UpdateCommunityAddressRequest) => {
  return request.put<void>(`/v1/admin/secured/addresses`, data)
}

/**
 * @function deleteCommunityAddress
 * @description 删除社区地址
 * @param id 地址ID
 * @returns Promise<void>
 */
export const deleteCommunityAddress = (id: number) => {
  return request.delete<void>(`/admin/api/system/addresses/${id}`)
}

/**
 * @function getFullAddressInfo
 * @description 获取完整地址信息
 * @param data 选定的地址ID信息
 * @returns Promise<SelectedCommunityAddressResponse>
 */
export const getFullAddressInfo = (data: SelectedCommunityAddressRequest) => {
  return request.post<SelectedCommunityAddressResponse>('/v1/admin/secured/addresses/full-info', data)
}

/**
 * @function refreshCommunityAddressCache
 * @description 刷新社区地址缓存
 * @returns Promise<{ message: string }>
 */
export const refreshCommunityAddressCache = () => {
  return request.post<{ message: string }>(`/v1/admin/secured/addresses/cache/refresh`)
}