/**
 * @description 文件用途配置 API 服务
 * <AUTHOR>
 * @date 2025-06-18
 */
import request from '@/modules/admin/utils/request'

const BASE_URL = '/v1/system/secured/file-usage'

// ------------------------- 类型定义 -------------------------

export interface FileUsageConfigItem {
  id: number | string | null // 允许 null 以便创建时
  usage_code: string
  usage_name: string
  description?: string
  allow_anonymous: 0 | 1
  allow_anonymous_text?: string
  max_file_size?: number // bytes, 0 表示使用全局配置
  max_file_size_mb?: number
  allowed_types?: string // comma-separated, 空表示使用全局配置
  sort_order?: number
  status: 0 | 1
  status_text?: string
  remark?: string
  created_at?: string
  updated_at?: string
}

export interface FileUsageConfigListParams {
  page?: number
  pageSize?: number
  usageCode?: string
  usageName?: string
  status?: 0 | 1
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

export interface FileUsageConfigListResponse {
  total: number
  list: FileUsageConfigItem[]
}

export interface SaveFileUsageConfigRequest {
  id?: number | string | null // null 或 0 表示新增
  usage_code: string
  usage_name: string
  description?: string
  allow_anonymous: 0 | 1
  max_file_size?: number
  allowed_types?: string
  sort_order?: number
  status: 0 | 1
  remark?: string
}

export interface FileUsageConfigOptionsResponse {
  allowed_usage_types: string[]
  anonymous_usage_types: string[]
}

// ------------------------- API 函数 -------------------------

/**
 * 获取文件用途配置列表
 * @param params 查询参数
 */
export function getFileUsageConfigList(
  params: FileUsageConfigListParams
): Promise<FileUsageConfigListResponse> {
  return request({
    url: `${BASE_URL}/list`,
    method: 'get',
    params
  })
}

/**
 * 获取文件用途配置详情
 * @param id 配置ID
 */
export function getFileUsageConfigDetail(id: number | string): Promise<FileUsageConfigItem> {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'get'
  })
}

/**
 * 保存文件用途配置 (创建或更新)
 * @param data 配置数据
 */
export function saveFileUsageConfig(
  data: SaveFileUsageConfigRequest
): Promise<FileUsageConfigItem> {
  return request({
    url: `${BASE_URL}/save`,
    method: 'post',
    data
  })
}

/**
 * 删除文件用途配置
 * @param id 配置ID
 */
export function deleteFileUsageConfig(id: number | string): Promise<any> {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'delete'
  })
}

/**
 * 获取文件用途选项
 */
export function getFileUsageOptions(): Promise<FileUsageConfigOptionsResponse> {
  return request({
    url: `${BASE_URL}/options`,
    method: 'get'
  })
}

/**
 * 初始化默认文件用途配置
 */
export function initDefaultFileUsageConfig(): Promise<any> {
  return request({
    url: `${BASE_URL}/init`,
    method: 'post'
  })
}

/**
 * 检查并初始化必需的文件用途配置
 * @param codes 用途代码列表
 */
export function checkAndInitFileUsageConfig(codes: string[]): Promise<any> {
  return request({
    url: `${BASE_URL}/check-init`,
    method: 'post',
    data: codes
  })
}

/**
 * 刷新文件用途配置缓存
 */
export function refreshFileUsageCache(): Promise<any> {
  return request({
    url: `${BASE_URL}/refresh`,
    method: 'post'
  })
}
