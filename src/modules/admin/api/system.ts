/**
 * 系统管理相关API
 */

import { get, post, put, del } from '@/modules/admin/utils/request';

/**
 * 获取系统统计数据
 */
export function getSystemStats() {
  return get('/v1/admin/secured/stats');
}

/**
 * 获取系统配置
 */
export function getSystemConfig() {
  return get('/v1/admin/secured/config');
}

/**
 * 更新系统配置
 * @param data 系统配置
 */
export function updateSystemConfig(data: Record<string, any>) {
  return put('/v1/admin/secured/config', data);
}

/**
 * 获取系统日志
 * @param params 查询参数
 */
export function getSystemLogs(params: {
  page: number;
  pageSize: number;
  level?: string;
  startTime?: string;
  endTime?: string;
}) {
  return get('/v1/admin/secured/system-logs', params);
}

/**
 * 清理系统日志
 */
export function clearSystemLogs() {
  return del('/v1/admin/secured/system-logs');
}

/**
 * 获取系统资源使用情况
 */
export function getSystemResources() {
  return get('/v1/admin/secured/resources');
}

/**
 * 获取系统备份列表
 */
export function getBackupList() {
  return get('/v1/admin/secured/backups');
}

/**
 * 创建系统备份
 */
export function createBackup() {
  return post('/v1/admin/secured/backups');
}

/**
 * 恢复系统备份
 * @param backupId 备份ID
 */
export function restoreBackup(backupId: string) {
  return post(`/v1/admin/secured/backups/${backupId}/restore`);
}

/**
 * 删除系统备份
 * @param backupId 备份ID
 */
export function deleteBackup(backupId: string) {
  return del(`/v1/admin/secured/backups/${backupId}`);
}

/**
 * 获取系统通知列表
 * @param params 查询参数
 */
export function getNotificationList(params: {
  page: number;
  pageSize: number;
  type?: string;
  status?: string;
}) {
  return get('/v1/admin/secured/notifications', params);
}

/**
 * 创建系统通知
 * @param data 通知信息
 */
export function createNotification(data: {
  title: string;
  content: string;
  type: string;
  target?: string[];
}) {
  return post('/v1/admin/secured/notifications', data);
}

/**
 * 更新系统通知
 * @param id 通知ID
 * @param data 通知信息
 */
export function updateNotification(id: number, data: {
  title?: string;
  content?: string;
  type?: string;
  target?: string[];
  status?: string;
}) {
  return put(`/v1/admin/secured/notifications/${id}`, data);
}

/**
 * 删除系统通知
 * @param id 通知ID
 */
export function deleteNotification(id: number) {
  return del(`/v1/admin/secured/notifications/${id}`);
}

/**
 * 获取系统信息
 */
export function getSysInfo() {
  return get('/v1/system/info/basic');
}