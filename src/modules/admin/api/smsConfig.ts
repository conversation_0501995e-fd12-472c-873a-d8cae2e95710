/**
 * @file 短信配置相关API
 * @description 该文件包含获取、更新和刷新短信配置的API函数。
 */
import request from '@/modules/admin/utils/request';

/**
 * @interface SmsConfigData
 * @description 短信配置数据结构，符合后端API的数据格式
 */
export interface SmsConfigData {
  id?: number;
  provider?: string;
  providerText?: string;
  accessKey?: string;
  accessSecret?: string;
  signName?: string;
  templateCodeRegister?: string;
  templateCodeLogin?: string;
  templateCodeResetPwd?: string;
  templateCodeNotice?: string;
  dailyLimit?: number;
  status?: number;
  statusText?: string;
  remark?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * @interface SaveSmsConfigRequest
 * @description 保存/更新短信配置请求数据结构
 */
export interface SaveSmsConfigRequest {
  id?: number;
  provider: string;
  accessKey: string;
  accessSecret?: string; // 如果不修改密钥，可以不传递此字段
  signName: string;
  templateCodeRegister: string;
  templateCodeLogin: string;
  templateCodeResetPwd: string;
  templateCodeNotice: string;
  dailyLimit: number;
  status: number;
  remark?: string;
}

/**
 * @function getSmsConfig
 * @description 获取短信配置信息
 * @returns {Promise<SmsConfigData>} 返回包含短信配置信息的Promise对象
 */
export function getSmsConfig(): Promise<SmsConfigData> {
  return request.get('/v1/system/secured/sms/config');
}

/**
 * @function saveSmsConfig
 * @description 保存或更新短信配置信息
 * @param {SaveSmsConfigRequest} data - 短信配置数据
 * @returns {Promise<any>} 返回操作结果的Promise对象
 */
export function saveSmsConfig(data: SaveSmsConfigRequest): Promise<any> {
  return request.post('/v1/system/secured/sms/config', data);
}

/**
 * @function refreshSmsConfigCache
 * @description 刷新短信配置缓存
 * @returns {Promise<any>} 返回操作结果的Promise对象
 */
export function refreshSmsConfigCache(): Promise<any> {
  return request.post('/v1/system/secured/sms/config/refresh_cache');
}

/**
 * @const smsProviders
 * @description 短信服务提供商选项列表
 */
export const smsProviders = [
  { value: 'aliyun', label: '阿里云' },
  { value: 'tencent', label: '腾讯云' },
  { value: 'yunpian', label: '云片' },
  { value: 'submail', label: '赛邮' },
  { value: 'custom', label: '自定义' }
];

/**
 * @const statusOptions
 * @description 状态选项列表
 */
export const statusOptions = [
  { value: 1, label: '启用' },
  { value: 0, label: '禁用' }
];
