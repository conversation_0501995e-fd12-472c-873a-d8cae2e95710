/**
 * 管理员相关API
 */

import { get, post, put, del } from '@/modules/admin/utils/request';
import type { AdminInfo, AdminListParams } from '../types';

/**
 * 获取管理员信息
 */
export function getAdminInfo() {
  return get<AdminInfo>('/v1/admin/secured/profile');
}

/**
 * 更新管理员信息
 * @param data 管理员信息
 */
export function updateAdminInfo(data: Partial<AdminInfo>) {
  return put<AdminInfo>('/v1/admin/secured/profile', data);
}

/**
 * 更新管理员头像
 * @param file 头像url
 */
export function updateAdminAvatar(file: any) {
  return put<AdminInfo>('/v1/admin/secured/avatar', file);
}

/**
 * 修改密码
 * @param data 密码信息
 */
export function changePassword(data: { oldPassword: string; newPassword: string }) {
  return post('/v1/admin/secured/change-password', data);
}

/**
 * 获取管理员列表
 * @param params 查询参数
 */
export function getAdminList(params: AdminListParams) {
  return get('/v1/admin/secured/admin', params);
}

/**
 * 创建管理员
 * @param data 管理员信息
 */
export function createAdmin(data: Omit<AdminInfo, 'id' | 'createTime' | 'creatorId'>) {
  return post<AdminInfo>('/v1/admin/secured/admin', data);
}

/**
 * 更新管理员
 * @param id 管理员ID
 * @param data 管理员信息
 */
export function updateAdmin(id: number, data: Partial<AdminInfo>) {
  return put<AdminInfo>(`/v1/admin/secured/admin/${id}`, data);
}

/**
 * 删除管理员
 * @param id 管理员ID
 */
export function deleteAdmin(id: number) {
  return del(`/v1/admin/secured/admin/${id}`);
}

/**
 * 重置管理员密码
 * @param id 管理员ID
 */
export function resetAdminPassword(id: number) {
  return post(`/v1/admin/secured/admin/${id}/reset-password`);
}

/**
 * 更新管理员状态
 * @param id 管理员ID
 * @param status 状态
 */
export function updateAdminStatus(id: number, status: string) {
  return put(`/v1/admin/secured/admin/${id}/status`, { status });
}

/**
 * 获取管理员操作日志
 * @param params 查询参数
 */
export function getAdminLogs(params: {
  page: number;
  pageSize: number;
  adminId?: number;
  type?: string;
  startTime?: string;
  endTime?: string;
}) {
  return get('/v1/admin/secured/logs', params);
}

/**
 * 获取统计数据
 */
export function getStatistics() {
  return get('/v1/admin/secured/statistics');
}