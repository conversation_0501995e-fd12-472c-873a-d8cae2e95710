/**
 * 订单管理相关API
 */

import { get, put } from '@/modules/admin/utils/request';

/**
 * 获取订单列表
 * @param params 查询参数
 */
export function getOrderList(params: {
  page: number;
  pageSize: number;
  status?: string;
  startTime?: string;
  endTime?: string;
}) {
  return get('/v1/admin/takeout/orders', params);
}

/**
 * 获取订单详情
 * @param id 订单ID
 */
export function getOrderDetail(id: number) {
  return get(`/v1/admin/secured/orders/${id}`);
}

/**
 * 更新订单状态
 * @param id 订单ID
 * @param data 状态信息
 */
export function updateOrderStatus(id: number, data: {
  status: string;
  remark?: string;
}) {
  return put(`/v1/admin/secured/orders/${id}/status`, data);
}

/**
 * 更新订单支付状态
 * @param id 订单ID
 * @param data 支付状态信息
 */
export function updateOrderPaymentStatus(id: number, data: {
  paymentStatus: string;
}) {
  return put(`/v1/admin/secured/orders/${id}/payment-status`, data);
}

/**
 * 更新订单配送状态
 * @param id 订单ID
 * @param data 配送状态信息
 */
export function updateOrderDeliveryStatus(id: number, data: {
  deliveryStatus: string;
}) {
  return put(`/v1/admin/secured/orders/${id}/delivery-status`, data);
}

/**
 * 更新订单支付方式
 * @param id 订单ID
 * @param data 支付方式信息
 */
export function updateOrderPaymentMethod(id: number, data: {
  paymentMethod: string;
}) {
  return put(`/v1/admin/secured/orders/${id}/payment-method`, data);
}

/**
 * 更新订单配送公司
 * @param id 订单ID
 * @param data 配送公司信息
 */
export function updateOrderDeliveryCompany(id: number, data: {
  deliveryCompany: string;
}) {
  return put(`/v1/admin/secured/orders/${id}/delivery-company`, data);
}

/**
 * 更新订单配送跟踪号
 * @param id 订单ID
 * @param data 配送跟踪号信息
 */
export function updateOrderDeliveryTrackingNumber(id: number, data: {
  trackingNumber: string;
}) {
  return put(`/v1/admin/secured/orders/${id}/delivery-tracking-number`, data);
}