/**
 * UI配置相关API
 */

import { post, get, put, del } from '@/modules/admin/utils/request';

export function getUiConfigs(params: {modules: string, group: string, page: number, pageSize: number} | undefined) {
  return get('/v1/ui-config/ui-configs', params);
}

export function createConfigs(data: any) {
  return post('/v1/ui-config/ui-configs', data);
}

/**
 * 更新UI配置
 * @param id 配置ID
 * @param data 更新的配置数据
 * @returns 更新结果
 */
export function updateUiConfig(id: number, data: any) {
  return put(`/v1/ui-config/ui-configs/${id}`, data);
}

/**
 * 获取特定配置ID的UI配置
 * @param configId 配置ID或配置键
 * @param extraParams 额外的查询参数，包含路径配置信息
 * @returns 配置数据
 */
export function getConfigById(configId: string, extraParams: Record<string, any> = {}) {
  // 处理configId，如果包含模块前缀，提取作为module参数
  let params: Record<string, any> = {
    config_key: configId,
    status: 1,
    page: 1,
    pageSize: 1
  };
  
  // 检查是否为模块路径格式（如 module_path）
  if (configId.includes('_')) {
    const [module, path] = configId.split('_', 2);
    params.module = module;
    
    // 如果是格式化的模块路径，使用path作为config_key
    if (path) {
      params.config_key = path;
    }
  }
  
  // 合并额外参数
  if (extraParams) {
    // 处理configKey参数，优先使用它作为config_key
    if (extraParams.configKey) {
      params.config_key = extraParams.configKey;
    }
    
    // 处理configType参数
    if (extraParams.configType) {
      params.config_type = extraParams.configType;
    }
    
    // 处理group参数
    if (extraParams.group) {
      params.group = extraParams.group;
    }
    
    // 处理version参数
    if (extraParams.versionHash) {
      params.version_hash = extraParams.versionHash;
    }
  }
  
  console.log('获取配置参数:', params);
  return get('/v1/ui-config/ui-configs', params);
}

/**
 * 获取前端路径
 * @returns 前端路径数据
 */
export function getFrontendPaths() {
  return get('/v1/ui-config/ui-configs/frontend-paths');
}

/**
 * 删除UI配置
 * @param id UI配置ID
 */
export function deleteUiConfig(id: number) {
  return del(`/v1/ui-config/ui-configs/${id}`);
}

/**
 * 根据ID获取单条UI配置
 * @param id 配置ID
 * @returns 单条UI配置数据
 */
export function getUiConfigById(id: number | string) {
  return get(`/v1/ui-config/ui-configs/${id}`);
}