/**
 * 系统任务管理相关API
 */

import { get, post, put, del } from '@/modules/admin/utils/request';

/**
 * 获取系统任务列表
 * @param params 查询参数
 */
export function getTaskList(params: {
  page: number;
  pageSize: number;
  type?: string;
  status?: string;
}) {
  return get('/v1/admin/secured/tasks', params);
}

/**
 * 创建系统任务
 * @param data 任务信息
 */
export function createTask(data: {
  name: string;
  type: string;
  cron: string;
  command: string;
  params?: Record<string, any>;
}) {
  return post('/v1/admin/secured/tasks', data);
}

/**
 * 更新系统任务
 * @param id 任务ID
 * @param data 任务信息
 */
export function updateTask(id: number, data: {
  name?: string;
  type?: string;
  cron?: string;
  command?: string;
  params?: Record<string, any>;
  status?: string;
}) {
  return put(`/v1/admin/secured/tasks/${id}`, data);
}

/**
 * 删除系统任务
 * @param id 任务ID
 */
export function deleteTask(id: number) {
  return del(`/v1/admin/secured/tasks/${id}`);
}

/**
 * 执行系统任务
 * @param id 任务ID
 */
export function executeTask(id: number) {
  return post(`/v1/admin/secured/tasks/${id}/execute`);
}

/**
 * 获取系统任务日志
 * @param params 查询参数
 */
export function getTaskLogs(params: {
  page: number;
  pageSize: number;
  taskId?: number;
  status?: string;
  startTime?: string;
  endTime?: string;
}) {
  return get('/v1/admin/secured/task-logs', params);
}