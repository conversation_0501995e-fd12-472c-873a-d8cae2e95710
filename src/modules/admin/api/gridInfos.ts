/**
 * 网格信息相关API
 */

import { get, post, put, del } from '@/modules/admin/utils/request';

/**
 * 获取网格信息列表
 * @param params 查询参数
 */
export function getGridInfoList(params: {
  page?: number;
  pageSize?: number;
  keyword?: string;
  status?: string;
}) {
  return get('/v1/admin/secured/grid-infos', params);
}

/**
 * 创建网格信息
 * @param data 网格信息数据
 */
export function createGridInfo(data: any) {
  return post('/v1/admin/secured/grid-infos', data);
}

/**
 * 获取网格信息详情
 * @param id 网格信息ID
 */
export function getGridInfo(id: number) {
  return get(`/v1/admin/secured/grid-infos/${id}`);
}

/**
 * 更新网格信息
 * @param id 网格信息ID
 * @param data 网格信息数据
 */
export function updateGridInfo(id: number, data: any) {
  return put(`/v1/admin/secured/grid-infos/${id}`, data);
}

/**
 * 删除网格信息
 * @param id 网格信息ID
 */
export function deleteGridInfo(id: number) {
  return del(`/v1/admin/secured/grid-infos/${id}`);
}

/**
 * 更新网格信息状态
 * @param id 网格信息ID
 * @param data 状态信息
 */
export function updateGridInfoStatus(id: number, data: {
  status: string;
}) {
  return put(`/v1/admin/secured/grid-infos/${id}/status`, data);
}

/**
 * 批量更新网格信息位置 使用中
 * @param data 位置信息对象
 * 
 */
export function batchUpdateGridInfoPosition(data: {
  items: Array<{
    uiConfigId: number;
    id: number;
    position: string;
  }>
}) {
  return put('/v1/admin/secured/grid-infos/batch/position', data);
}

/**
 * 设置网格信息与UI配置的关联关系
 * @param data 关联关系数据
 */
export function setGridInfoUIConfigRelation(data: {
  gridInfoId: number;
  uiConfigIds: number[];
}) {
  return post('/v1/admin/secured/grid-infos/set-ui-configs', data);
} 


/**
 * 删除网格信息与UI配置的关联关系
 * @param data 包含 gridInfoId 和 uiConfigId 的对象
 * @returns Promise<any>
 */
export function deleteUIConfigRelation(data: {
  grid_info_id: number;
  ui_config_id: number;
}) {
  // 适配后端接口 /grid-infos/delete-ui-configs
  // 约定接口为 PUT 请求（如需变更为 DELETE，请告知）
  return put('/v1/admin/secured/grid-infos/delete-ui-configs', data);
}

/**
 * 批量设置UI配置与网格信息的关联关系
 * @param data 包含 uiConfigId 和 gridInfoIds 的对象
 * @returns Promise<any>
 */
export function setBatchUIConfigGridRelation(data: {
  uiConfigId: number;
  gridInfoIds: number[];
}) {
  return put('/v1/admin/secured/grid-infos/set-batch-ui', data);
}
