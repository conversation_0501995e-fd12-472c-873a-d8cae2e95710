/**
 * 系统配置相关API
 */

import { get, post, put, del } from '@/modules/admin/utils/request';

// 系统配置接口
export interface SystemConfig {
  id: string | number;
  configKey: string;
  configValue: any;
  configType: string;
  description: string;
  category?: string; // 配置分类
  status?: number; // 状态 0:启用 1:禁用
  isSystem?: number; // 是否系统配置 1:是 0:否
  create_time?: string;
  update_time?: string;
  // 以下字段允许向后兼容性
  key?: string;
  value?: any;
  type?: string;
}

// 分页查询参数
export interface QueryParams {
  page: number;
  page_size: number;
  query?: string;
  category?: string; // 分类筛选
}

// 分页结果接口
export interface PaginatedResult<T> {
  list?: T[];      // 新格式使用list
  items?: T[];     // 兼容旧格式
  total: number;
  page: number;
  pageSize?: number; // 新格式使用pageSize
  page_size?: number; // 兼容旧格式
}

/**
 * 获取系统配置列表
 * @param params 查询参数
 */
export function getConfigList(params: QueryParams) {
  return get<PaginatedResult<SystemConfig>>('/v1/system/configs/details', params);
}

/**
 * 创建系统配置
 * @param data 系统配置数据
 */
export function createConfig(data: Omit<SystemConfig, 'id' | 'create_time' | 'update_time'>) {
  return post('/v1/system/secured/configs', data);
}

/**
 * 获取单个系统配置
 * @param id 配置ID
 */
export function getConfig(id: string | number) {
  return get<SystemConfig>(`/v1/system/secured/configs/${id}`);
}

/**
 * 更新系统配置
 * @param id 配置ID
 * @param data 系统配置数据
 */
export function updateConfig(id: string | number, data: Omit<SystemConfig, 'id' | 'create_time' | 'update_time'>) {
  return put(`/v1/system/secured/configs/${id}`, data);
}

/**
 * 删除系统配置
 * @param id 配置ID
 */
export function deleteConfig(id: string | number) {
  return del(`/v1/system/secured/configs/${id}`);
}

/**
 * 根据键名获取系统配置
 * @param key 配置键名
 */
export function getConfigByKey(key: string) {
  return get<SystemConfig>(`/v1/system/secured/configs/key/${key}`);
}

/**
 * 刷新系统配置缓存
 */
export function refreshConfigCache() {
  return post('/v1/system/secured/configs/cache/refresh');
}

/**
 * 批量获取系统配置
 * @param keys 配置键名数组
 */
export function getMultiConfigs(keys: string[]) {
  return post<Record<string, any>>('/v1/system/secured/configs/batch', { keys });
}

/**
 * 更新系统配置值
 * @param key 配置键名
 * @param value 配置值
 */
export function updateConfigValue(key: string, value: any) {
  return put('/v1/system/secured/configs/value', { key, value });
}

/**
 * 获取系统配置缓存信息
 */
export function getConfigCacheInfo() {
  return get('/v1/system/secured/configs/cache');
}
