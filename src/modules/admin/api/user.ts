/**
 * 用户管理相关API
 */

import { get, put, post } from '@/modules/admin/utils/request';

/**
 * 获取用户列表
 * @param params 查询参数
 */
export function getUserList(params: {
  page: number;
  pageSize: number;
  status?: string;
  keyword?: string;
}) {
  return get('/v1/admin/secured/users', params);
}

/**
 * 获取用户详情
 * @param id 用户ID
 */
export function getUserDetail(id: number) {
  return get(`/v1/admin/secured/users/${id}`);
}

/**
 * 更新用户状态
 * @param id 用户ID
 * @param data 状态信息
 */
export function updateUserStatus(id: number, data: {
  status: number;
}) {
  return put(`/v1/admin/secured/users/${id}/status`, data);
}

/**
 * 搜索用户
 * @param keyword 搜索关键词
 */
export function searchUsers(keyword: string) {
  return get('/v1/admin/secured/users/search', { keyword });
}

/**
 * 创建新用户
 * @param data 用户信息
 */
export function createUser(data: {
  username: string;
  password: string;
  nickname: string;
  mobile: string;
  email: string;
  gender: number;
  status: number;
}) {
  return post('/v1/admin/secured/users', data);
}

/**
 * 更新用户信息
 * @param id 用户ID
 * @param data 用户信息
 */
export function updateUser(id: string | number, data: {
  nickname?: string;
  mobile?: string;
  email?: string;
  password?: string;
}) {
  return put(`/v1/admin/secured/users/${id}`, data);
}

/**
 * 重置用户密码
 * @param id 用户ID
 * @param data 新密码信息
 */
export function resetPassword(id: string | number, data: {
  password: string;
}) {
  return post(`/v1/admin/secured/users/${id}/reset-password`, data);
}

/**
 * 独立修改用户密码
 * @param id 用户ID
 * @param data 新密码信息
 */
export function updatePassword(id: string | number, data: {
  new_password: string;
}) {
  return put(`/v1/admin/secured/users/${id}/password`, data);
}

/**
 * 更新用户积分和余额
 * @param id 用户ID
 * @param data 积分和余额信息
 */
export function updateUserPointsAndBalance(id: string | number, data: {
  points?: number;
  balance?: number;
}) {
  return put(`/v1/admin/secured/users/${id}/balance-points`, data);
}
 