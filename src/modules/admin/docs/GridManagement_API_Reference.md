# GridManagement API 参考文档

## 1. 服务层 API (GridManagementService)

### 1.1 构造函数

```typescript
constructor(gridLayoutStore?: InstanceType<typeof GridLayoutStore>)
```

**参数**：
- `gridLayoutStore`：可选，GridLayoutStore 实例，用于数据同步

**说明**：
创建 GridManagementService 实例，可选择性地注入 GridLayoutStore 实例以实现数据同步。

### 1.2 数据同步方法

#### syncStructureDataToLayoutStore

```typescript
public syncStructureDataToLayoutStore(structureData: any): void
```

**参数**：
- `structureData`：结构数据对象

**说明**：
将结构数据同步到 gridLayoutStore。

#### fetchObjectDataFromLayoutStore

```typescript
public async fetchObjectDataFromLayoutStore(): Promise<void>
```

**说明**：
触发 gridLayoutStore 的对象数据获取方法。

### 1.3 模块与分组相关方法

#### handleModuleChange

```typescript
public async handleModuleChange(): Promise<void>
```

**说明**：
处理模块变更，重新获取 API 文档、分组名称、DTO 和控制器数据。

#### handleGroupChange

```typescript
public async handleGroupChange(group?: string): Promise<void>
```

**参数**：
- `group`：可选，选中的分组名称

**说明**：
处理分组变更，过滤 API 文档和控制器，获取 UI 配置。

#### refreshCurrentModule

```typescript
public async refreshCurrentModule(): Promise<void>
```

**说明**：
刷新当前模块的数据，清除缓存并重新获取。

#### refreshAllModules

```typescript
public async refreshAllModules(): Promise<void>
```

**说明**：
刷新所有模块的数据，清除所有缓存并重新获取当前模块数据。

### 1.4 API 数据获取方法

#### fetchApiDoc

```typescript
public async fetchApiDoc(): Promise<void>
```

**说明**：
获取 API 文档，优先从缓存获取，缓存不存在则从后端获取。

#### fetchGroupNames

```typescript
public async fetchGroupNames(): Promise<void>
```

**说明**：
获取分组名称，优先从缓存获取，缓存不存在则从后端获取。

#### fetchDtos

```typescript
public async fetchDtos(): Promise<void>
```

**说明**：
获取 DTO 数据，优先从缓存获取，缓存不存在则从后端获取。

#### fetchControllers

```typescript
public async fetchControllers(): Promise<void>
```

**说明**：
获取控制器数据，优先从缓存获取，缓存不存在则从后端获取。

#### filterApiDoc

```typescript
public filterApiDoc(inapis: any[]): void
```

**参数**：
- `inapis`：API 列表

**说明**：
根据当前选中的模块和分组过滤 API 文档。

#### fetchUiConfigs

```typescript
public async fetchUiConfigs(): Promise<void>
```

**说明**：
获取 UI 配置列表。

### 1.5 DTO 相关方法

#### selectDto

```typescript
public selectDto(dto: any): void
```

**参数**：
- `dto`：DTO 对象

**说明**：
选择 DTO 以生成配置页面。

#### generateConfig

```typescript
public generateConfig(dto: any): void
```

**参数**：
- `dto`：DTO 对象

**说明**：
根据 DTO 生成页面配置。

#### processDtoStructure

```typescript
public processDtoStructure(structure: Record<string, any>): any[]
```

**参数**：
- `structure`：DTO 结构对象

**返回值**：
处理后的字段信息数组

**说明**：
处理 DTO 结构并提取字段信息。

### 1.6 网格项相关方法

#### addGridItem

```typescript
public async addGridItem(item: any): Promise<void>
```

**参数**：
- `item`：网格项数据

**说明**：
添加新的网格项。

#### updateGridItem

```typescript
public async updateGridItem(item: any): Promise<void>
```

**参数**：
- `item`：网格项数据

**说明**：
更新现有网格项。

#### removeGridItem

```typescript
public async removeGridItem(id: number): Promise<void>
```

**参数**：
- `id`：网格项 ID

**说明**：
删除网格项。

#### openGridItemDialog

```typescript
public openGridItemDialog(item?: any): void
```

**参数**：
- `item`：可选，网格项数据

**说明**：
打开网格项编辑对话框，如果提供了 item 参数则为编辑模式，否则为新建模式。

#### convertToGridStackNodes

```typescript
public convertToGridStackNodes(items: AdminGridInfoDTO[]): any[]
```

**参数**：
- `items`：网格项数组

**返回值**：
GridStack 节点数组

**说明**：
将网格项数据转换为 GridStack 节点格式。

#### updateGridItemsFromNodes

```typescript
public updateGridItemsFromNodes(nodes: any[]): void
```

**参数**：
- `nodes`：GridStack 节点数组

**说明**：
根据 GridStack 节点更新网格项数据。

#### updateGridPositions

```typescript
public async updateGridPositions(): Promise<void>
```

**说明**：
将当前网格项位置保存到后端。

## 2. 状态管理 API (gridManagementStore)

### 2.1 状态

```typescript
// 模块和分组相关
modules: Ref<string[]>
selectedModule: Ref<string>
selectedGroup: Ref<string>
groupNames: Ref<GroupName[]>

// API和控制器相关
apiDoc: Ref<string>
apis: Ref<any[]>
controllers: Ref<any[]>

// DTO相关
dtos: Ref<any[]>
selectedDto: Ref<any>
dtoSource: Ref<any>
dtoSourceJson: Ref<string>
visibleDtos: Ref<any[]>
filterKeyword: Ref<string>
processedDtoFields: Ref<any[]>

// 页面配置相关
pageConfig: Ref<any>
pageTitle: Ref<string>
frontendPath: Ref<string>
selectedIcon: Ref<string>
uiConfigs: Ref<AdminUIConfigDTO[]>
selectedUiConfig: Ref<AdminUIConfigDTO | null>
showPage: Ref<boolean>

// 网格项相关
gridItems: Ref<AdminGridInfoDTO[]>
currentGridItem: Ref<AdminGridInfoDTO | null>
showGridDialog: Ref<boolean>

// UI状态相关
isApiListCollapsed: Ref<boolean>
isControllersListCollapsed: Ref<boolean>
isConfigListCollapsed: Ref<boolean>
showDtosDrawer: Ref<boolean>
```

### 2.2 计算属性

```typescript
filteredDtos: ComputedRef<any[]>
```

**说明**：
根据过滤关键字过滤后的 DTO 列表。

### 2.3 方法

```typescript
// 继承自 GridManagementService 的所有方法

// 额外方法
updateCurrentGridItemContent(content: string): void
saveGridItem(gridItem: any): void
updateConfigIcon(config: any): Promise<void>
updateConfigStatus(config: any, status: number): Promise<void>
```

## 3. 视图组件 API (GridManagement.vue)

### 3.1 组件属性

无公开属性。

### 3.2 组件方法

```typescript
handleGridRefresh(): void
openGridItemDialog(): void
openGridManagement(): void
saveGridItem(item: any): void
```

### 3.3 子组件

- **ModuleSelector**：模块选择器组件
- **ApiListCard**：API 列表卡片组件
- **ControllersListCard**：控制器列表卡片组件
- **GridPageConfigEditor**：配置编辑组件
- **GridLayout**：网格布局组件
- **DTOsDrawer**：DTO 抽屉组件
- **GridItemDialog**：网格项编辑对话框

## 4. 网格项 API (GridItemDialog.vue)

### 4.1 组件属性

```typescript
modelValue: boolean // 控制对话框显示/隐藏
gridItem: AdminGridInfoDTO | null // 当前编辑的网格项
```

### 4.2 组件事件

```typescript
'update:modelValue': (value: boolean) => void // 更新对话框显示状态
save: (item: AdminGridInfoDTO) => void // 保存网格项
```

## 5. 后端 API 接口

### 5.1 网格信息 API

#### getGridInfoList

```typescript
function getGridInfoList(params: {
  page?: number;
  pageSize?: number;
  keyword?: string;
  status?: string;
}): Promise<any>
```

**说明**：
获取网格信息列表。

#### createGridInfo

```typescript
function createGridInfo(data: any): Promise<any>
```

**说明**：
创建网格信息。

#### getGridInfo

```typescript
function getGridInfo(id: number): Promise<any>
```

**说明**：
获取网格信息详情。

#### updateGridInfo

```typescript
function updateGridInfo(id: number, data: any): Promise<any>
```

**说明**：
更新网格信息。

#### deleteGridInfo

```typescript
function deleteGridInfo(id: number): Promise<any>
```

**说明**：
删除网格信息。

#### updateGridInfoStatus

```typescript
function updateGridInfoStatus(id: number, data: {
  status: string;
}): Promise<any>
```

**说明**：
更新网格信息状态。

#### batchUpdateGridInfoPosition

```typescript
function batchUpdateGridInfoPosition(data: {
  items: Array<{
    uiConfigId: number;
    id: number;
    position: {
      x: number;
      y: number;
      w: number;
      h: number;
      min_w: number;
      min_h: number;
      max_w: number;
      max_h: number;
      locked: boolean;
      no_move: boolean;
      no_resize: boolean;
      auto_position: boolean;
      lastUiPosition: { top: number; left: number } | null;
    };
  }>
}): Promise<any>
```

**说明**：
批量更新网格信息位置。

## 6. 类型定义

### 6.1 网格项相关类型

```typescript
interface RawGridItem {
  id: string | number;
  position?: string | RawGridItemPosition;
  content?: any;
  [key: string]: any;
}

interface RawGridItemPosition {
  x?: number;
  y?: number;
  w?: number;
  h?: number;
  min_w?: number;
  min_h?: number;
  max_w?: number;
  max_h?: number;
  locked?: boolean;
  no_move?: boolean;
  no_resize?: boolean;
  auto_position?: boolean;
}

interface GridItemConfig extends RawGridItemPosition {
  i: string;
  id: string;
  static: boolean;
  'is-draggable': boolean;
  'is-resizable': boolean;
  'is-bounded': boolean;
  content?: any;
  [key: string]: any;
}

interface AdminGridInfoDTO {
  id: number;
  name: string;
  type: string;
  content: any;
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
    min_w?: number;
    min_h?: number;
    max_w?: number;
    max_h?: number;
    locked?: boolean;
    no_move?: boolean;
    no_resize?: boolean;
    auto_position?: boolean;
  };
  status: number;
  uiConfigId: number;
}
```

### 6.2 配置相关类型

```typescript
interface ConfigData {
  icon: string;
  module: string;
  group: string;
  title: string;
  frontend_path: string;
  config_type: string;
  config_key: string;
  config_content: string;
  remark: string;
  status: number;
  version: string;
  version_hash: string;
  uiConfigId: number;
  dto?: string;
  grid_items?: AdminGridInfoDTO[];
  draggable: boolean;
  resizable: boolean;
}

interface GroupName {
  page_name: string;
}

interface AdminUIConfigDTO {
  id: number;
  icon: string;
  module: string;
  group: string;
  title: string;
  frontend_path: string;
  config_type: string;
  config_key: string;
  config_content: string;
  remark: string;
  status: number;
  version: string;
  version_hash: string;
  created_at: string;
  updated_at: string;
}
```

## 7. 使用示例

### 7.1 基本使用

```typescript
// 在组件中使用 store
import { useGridManagementStore } from '../stores/gridManagementStore';

const store = useGridManagementStore();

// 加载模块数据
await store.handleModuleChange();

// 选择分组
await store.handleGroupChange('user');

// 选择 DTO 生成配置
store.selectDto(selectedDto);

// 保存配置
await store.saveConfig();
```

### 7.2 网格项操作

```typescript
// 添加网格项
const newItem = {
  name: '用户列表',
  type: 'table',
  content: JSON.stringify({
    columns: [
      { prop: 'id', label: 'ID' },
      { prop: 'username', label: '用户名' },
      { prop: 'email', label: '邮箱' }
    ],
    data: []
  }),
  position: {
    x: 0,
    y: 0,
    w: 6,
    h: 4
  },
  status: 1,
  uiConfigId: store.selectedUiConfig.value?.id
};

await store.addGridItem(newItem);

// 更新网格项
const updatedItem = {
  ...store.currentGridItem.value,
  name: '更新后的名称'
};

await store.updateGridItem(updatedItem);

// 删除网格项
await store.removeGridItem(itemId);

// 保存布局
await store.updateGridPositions();
```

### 7.3 自定义网格项内容

```typescript
// 更新当前网格项内容
const content = {
  dataSource: 'api',
  apiUrl: '/api/users',
  columns: [
    { prop: 'id', label: 'ID', width: 80 },
    { prop: 'username', label: '用户名', sortable: true },
    { prop: 'email', label: '邮箱' },
    { prop: 'status', label: '状态', formatter: (row) => row.status === 1 ? '启用' : '禁用' }
  ],
  pagination: {
    pageSize: 10,
    total: 0,
    currentPage: 1
  },
  actions: [
    { name: '编辑', type: 'primary', event: 'edit' },
    { name: '删除', type: 'danger', event: 'delete' }
  ]
};

store.updateCurrentGridItemContent(JSON.stringify(content));
store.saveGridItem(store.currentGridItem.value);
```

## 8. 错误处理

所有 API 方法都应使用 try/catch 进行错误处理：

```typescript
try {
  await store.saveConfig();
  // 成功处理
} catch (error) {
  // 错误处理
  console.error('保存配置失败:', error);
  // 显示错误消息
  ElMessage.error('保存配置失败，请重试');
}
```

## 9. 版本兼容性

- 当前 API 适用于 GridManagement 组件 v1.0.0 及以上版本
- 与 Vue 3.3.0+ 和 Pinia 2.1.0+ 兼容
- 依赖 Element Plus 2.3.0+ 和 grid-layout-plus 1.0.0+

## 10. 更新日志

### v1.0.0 (2025-04-15)

- 初始版本发布
- 支持基本的网格管理功能
- 集成 API 和 DTO 管理

### v1.1.0 (2025-04-22)

- 适配 GridLayoutStore 新类与工厂函数
- 确保类型安全
- 优化性能和用户体验

---

本文档将随组件更新而更新，如有问题请联系开发团队。