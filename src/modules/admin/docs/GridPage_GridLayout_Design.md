# GridPage 组件与网格管理结构设计文档

---

## 一、概述

GridPage 组件是基于 gridstack.js 的响应式布局网格页面组件，服务于后台管理系统的页面可视化搭建与动态内容管理。其核心作用是承载和渲染多类型、可拖拽、可配置的 grid item，实现灵活的页面布局和业务扩展。

---

## 二、核心数据结构

### 1. 路由数据（Route Data）

- 描述：系统内所有页面的路由与基础信息。
- 结构示例：
  ```js
  {
    "home": {
      path: "/home",
      name: "Home",
      meta: { title: "首页" },
      pageConfig: { ... } // 页面数据
    },
    ...
  }
  ```
- TODO：详细字段说明与扩展方式

### 2. 页面数据（Page Data）

- 描述：单个页面的整体配置，包括布局、标题、包含的 grid item 等。
- 结构示例：
  ```js
  {
    title: "首页",
    layout: "grid",
    gridItems: [ ... ] // 见下文
  }
  ```
- TODO：页面配置的详细字段说明

### 3. 可拖拽组件（Grid Item）

- 描述：页面内的功能单元，支持拖拽排序和布局调整。
- 结构示例：
  ```js
  {
    type: "table",         // 组件类型
    content: [ ... ],      // 组件内容，类型依赖 type
    position: { x: 0, y: 0, w: 4, h: 2 }, // 布局信息
    config: { ... }        // 其他自定义配置
  }
  ```
- type 字段决定渲染组件和 content 的数据类型。
- TODO：各类型 grid item 的详细结构与数据校验规则

---

## 三、Grid Item 分类与 type 字段

### 1. type 的作用

- 决定渲染组件类型（如 table、form、card、chart 等）。
- 决定 content 字段的数据结构和类型，保证类型安全。
- 支持后续组件类型扩展。

### 2. 常见 type 及其数据类型

| type   | 渲染组件 | content 数据类型 | 说明                    |
|--------|----------|------------------|-------------------------|
| table  | 表格     | array           | 表格数据，数组结构      |
| form   | 表单     | object          | 表单结构，键值对对象    |
| card   | 卡片     | object/string   | 卡片内容                |
| chart  | 图表     | object          | 图表配置与数据          |
| custom | 自定义   | 任意            | 可扩展自定义组件        |

- TODO：更多类型的定义与说明

---

## 四、GridPage 组件结构与功能

### 1. 组件职责

- 基于 gridstack.js 实现响应式网格布局。
- 支持 grid item 的拖拽、调整大小、动态渲染。
- 通过 type 字段动态挂载不同类型的子组件。
- 提供事件与方法：增删改查、保存布局、刷新数据等。

### 2. 主要属性与方法

- props.store：外部传入的 store 实例，管理页面和 grid item 数据。
- getGridData / addGridItem / removeGridItem / updateGridItem / saveLayout / refresh 等方法。
- TODO：详细 API 和事件说明

### 3. 渲染流程

- 根据 store 提供的 gridItems，遍历并根据 type 渲染对应子组件。
- 支持 run-time 挂载和卸载，保证页面响应性能。
- TODO：渲染流程图与时序说明

---

## 五、数据流与交互流程

1. 页面加载：根据路由数据加载页面数据。
2. 页面编辑：支持增删 grid item，拖拽排序，编辑单个 grid item。
3. 组件渲染：根据 type 渲染子组件，content 作为数据源。
4. 数据校验：保存或切换 type 时校验 content 类型。
5. 批量操作：支持批量添加、删除、排序 grid item。
6. 与后端联动：配置持久化，支持实时同步。
7. TODO：详细的交互流程与异常处理说明

---

## 六、扩展与最佳实践

- 结构清晰，注释完善，便于维护。
- type 与 content 类型强关联，避免运行时类型错误。
- 组件解耦，便于扩展和复用。
- TODO：测试建议与性能优化方案

---

## 七、预留补充区

- TODO：接口文档、典型数据示例、各类型 grid item 详细结构、与后端约定、权限控制、国际化支持等内容后续补充。

---

## 八、GridManagementService 变量分类与业务解读

### 1. 模块与分组相关（用于后端API与DTO检索）
- modules：模块列表（如 admin、merchant、user 等），用于筛选和定位业务模块。
- selectedModule：当前选中的模块。
- groupNames：当前模块下的分组名集合，进一步细分模块下的业务。
- selectedGroup：当前选中的分组。

### 2. API 与 DTO 数据相关（接口与数据结构说明）
- apiDoc、originalApis、apis：API 文档与接口列表，描述当前模块/分组下可用的接口。
- dtos：DTO 文件列表（数据传输对象），描述接口的请求/响应字段结构。
- selectedDto：当前选中的 DTO 文件。
- dtoSource、dtoSourceJson：DTO 文件的原始数据和 JSON 字符串，便于前端解析和展示。
- visibleDtos、loadedCount、filterKeyword：DTO 列表的展示与过滤控制。

### 3. 控制器数据相关
- controllers、originalControllers：当前模块/分组下的控制器列表。

### 4. 页面配置与UI配置相关
- pageConfig：当前页面的整体配置对象（包含布局、服务配置、标题、icon等）。
- pageTitle、frontendPath、selectedIcon：页面标题、前端路由、图标等基础信息。
- uiConfigs：后端返回的页面（UI）配置列表，每一项都是一个完整的页面配置信息。
- selectedUiConfig：当前选中的页面配置。
- showPage：页面展示控制。

### 5. 网格项（Grid Item）相关
- gridItems：当前页面下所有 grid item（可拖拽组件）的数据列表。
- showGridDialog、currentGridItem：控制网格项编辑弹窗与当前编辑项。

### 6. UI 状态与交互控制
- isApiListCollapsed、isControllersListCollapsed、isConfigListCollapsed：控制 API、控制器、配置列表的折叠状态。
- showDtosDrawer：控制 DTO 抽屉的显示。
- processedDtoFields：处理后的 DTO 字段信息，便于动态生成表单、表格等。

---

## 九、业务流程梳理与典型页面配置说明

1. 选择模块/分组 → 获取后端 API、DTO、页面配置（uiConfigs）。
2. 选择/编辑 DTO → 自动生成表单项、表格列、校验规则等。
3. 编辑页面配置（如 config_content），配置页面基础信息、布局参数、API 路径、grid item 列表等。
4. 编辑/拖拽 grid item → 通过 type+content 生成不同类型的页面组件。
5. 保存页面配置（uiConfigs）→ 提交给后端，持久化所有页面结构和内容。

### 典型页面配置示例说明

uiConfigs 中的 config_content 字段，是整个页面的核心配置，包含：
- 页面基础元数据（version、title、icon、moduleName、frontendPath 等）
- 服务配置（serviceConfig，包括 API 路径、标题、消息、布局参数等）
- gridOptions（网格布局参数）
- gridItems（页面内所有功能块）
- 关联 DTO 结构（决定页面数据结构和展示内容）

前端生成页面配置时，需组装出与后端一致的结构，便于页面的回显和二次编辑。

---

如需补充具体内容，请直接在“TODO”处补充或提出需求，文档会持续完善。
