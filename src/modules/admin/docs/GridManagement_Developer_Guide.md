# GridManagement 开发指南

## 1. 开发环境准备

### 1.1 依赖项

开发 GridManagement 组件需要以下依赖：

```json
{
  "dependencies": {
    "vue": "^3.3.0",
    "pinia": "^2.1.0",
    "element-plus": "^2.3.0",
    "grid-layout-plus": "^1.0.0",
    "localforage": "^1.10.0",
    "axios": "^1.4.0"
  },
  "devDependencies": {
    "typescript": "^5.0.0",
    "@types/node": "^18.0.0"
  }
}
```

### 1.2 目录结构

```
src/modules/admin/
├── api/
│   ├── gridInfos.ts       # 网格信息相关API
│   └── uiConfig.ts        # UI配置相关API
├── components/
│   ├── ApiListCard.vue    # API列表卡片组件
│   ├── DTOsDrawer.vue     # DTO抽屉组件
│   ├── GridItemDialog.vue # 网格项编辑对话框
│   └── GridPageConfigGenerator.ts # 配置生成器
├── docs/
│   ├── GridManagement_Technical_Doc.md # 技术文档
│   └── GridPage_GridLayout_Design.md   # 设计文档
├── service/
│   └── GridManagementService.ts # 服务层
├── stores/
│   └── gridManagementStore.ts   # 状态管理
├── types/
│   └── index.ts           # 类型定义
└── views/
    └── GridManagement.vue # 主视图组件
```

## 2. 组件扩展开发

### 2.1 添加新的网格项类型

要添加新的网格项类型，需要完成以下步骤：

1. **定义类型接口**

在 `types/index.ts` 中添加新类型的接口定义：

```typescript
export interface CustomGridItemContent {
  // 自定义属性
  title: string;
  data: any;
  settings: {
    // 自定义设置
    showHeader: boolean;
    theme: string;
  };
}
```

2. **创建组件**

创建对应的 Vue 组件：

```vue
<!-- components/grid-items/CustomGridItem.vue -->
<template>
  <div class="custom-grid-item">
    <div v-if="content.settings.showHeader" class="header">
      {{ content.title }}
    </div>
    <div class="content">
      <!-- 自定义内容渲染 -->
    </div>
  </div>
</template>

<script setup lang="ts">
// import { defineProps } from 'vue';
import type { CustomGridItemContent } from '../../types';

const props = defineProps<{
  content: CustomGridItemContent;
  editable: boolean;
}>();
</script>
```

3. **注册组件**

在 GridLayout 组件中注册新类型：

```typescript
// 在组件映射表中添加新类型
const componentMap = {
  table: TableGridItem,
  form: FormGridItem,
  chart: ChartGridItem,
  custom: CustomGridItem, // 新增类型
};
```

### 2.2 自定义网格项内容编辑器

为新的网格项类型创建自定义编辑器：

```vue
<!-- components/editors/CustomGridItemEditor.vue -->
<template>
  <div class="custom-editor">
    <el-form :model="formData" label-width="100px">
      <el-form-item label="标题">
        <el-input v-model="formData.title" />
      </el-form-item>
      
      <el-form-item label="显示头部">
        <el-switch v-model="formData.settings.showHeader" />
      </el-form-item>
      
      <el-form-item label="主题">
        <el-select v-model="formData.settings.theme">
          <el-option label="默认" value="default" />
          <el-option label="暗色" value="dark" />
          <el-option label="亮色" value="light" />
        </el-select>
      </el-form-item>
      
      <!-- 其他自定义设置 -->
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { CustomGridItemContent } from '../../types';

const props = defineProps<{
  modelValue: CustomGridItemContent;
}>();

const emit = defineEmits(['update:modelValue']);

const formData = ref<CustomGridItemContent>(JSON.parse(JSON.stringify(props.modelValue)));

watch(formData, (newVal) => {
  emit('update:modelValue', newVal);
}, { deep: true });
</script>
```

### 2.3 集成到 GridItemDialog

在 `GridItemDialog.vue` 中集成新的编辑器：

```typescript
// 编辑器组件映射
const editorMap = {
  table: TableEditor,
  form: FormEditor,
  chart: ChartEditor,
  custom: CustomGridItemEditor, // 新增编辑器
};

// 动态组件渲染
const currentEditor = computed(() => {
  return editorMap[gridItemForm.value.type] || null;
});
```

## 3. 数据流与状态管理

### 3.1 数据流向图

```
┌─────────────────┐
│    用户操作     │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  GridManagement  │ 视图层：处理用户交互
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│gridManagementStore│ 状态层：管理状态变更
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│GridManagementService│ 服务层：处理业务逻辑
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│     API 调用    │ API层：与后端交互
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│    后端服务     │
└─────────────────┘
```

### 3.2 状态同步机制

`GridManagementService` 与 `gridLayoutStore` 之间的状态同步：

```typescript
// 同步结构数据到 gridLayoutStore
public syncStructureDataToLayoutStore(structureData: any) {
  if (this.gridLayoutStore) {
    this.gridLayoutStore.structureData.value = JSON.parse(JSON.stringify(structureData));
  }
}

// 触发 gridLayoutStore 的对象数据获取
public async fetchObjectDataFromLayoutStore() {
  if (this.gridLayoutStore && typeof this.gridLayoutStore.fetchObjectData === 'function') {
    try {
      await this.gridLayoutStore.fetchObjectData();
    } catch (e) {
      console.error('fetchObjectData调用出错', e);
    }
  }
}
```

## 4. 高级功能开发

### 4.1 组件间数据联动

实现不同网格项之间的数据联动：

```typescript
// 在 gridManagementStore 中添加事件总线
const eventBus = reactive({
  listeners: {} as Record<string, Function[]>,
  
  // 发布事件
  emit(event: string, data: any) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(data));
    }
  },
  
  // 订阅事件
  on(event: string, callback: Function) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
    
    // 返回取消订阅函数
    return () => {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    };
  }
});

// 在组件中使用
const unsubscribe = store.eventBus.on('dataUpdated', (data) => {
  // 处理数据更新
});

// 发布事件
store.eventBus.emit('dataUpdated', { id: 1, value: 'new data' });

// 组件卸载时取消订阅
onUnmounted(() => {
  unsubscribe();
});
```

### 4.2 自定义布局算法

实现自定义的网格布局算法：

```typescript
/**
 * 自动布局算法 - 根据内容类型优化布局
 * @param gridItems 网格项列表
 * @returns 优化后的网格项列表
 */
function autoLayout(gridItems: AdminGridInfoDTO[]): AdminGridInfoDTO[] {
  // 复制数组，避免修改原数组
  const items = JSON.parse(JSON.stringify(gridItems));
  
  // 按类型分组
  const typeGroups = {};
  items.forEach(item => {
    if (!typeGroups[item.type]) {
      typeGroups[item.type] = [];
    }
    typeGroups[item.type].push(item);
  });
  
  // 布局逻辑
  // 例如：表格类型占据更多宽度，图表类型占据更多高度
  let currentY = 0;
  
  // 处理表格类型
  if (typeGroups['table']) {
    typeGroups['table'].forEach(item => {
      item.position.x = 0;
      item.position.y = currentY;
      item.position.w = 12; // 占满宽度
      item.position.h = 6;  // 固定高度
      currentY += item.position.h;
    });
  }
  
  // 处理图表类型 - 并排放置
  if (typeGroups['chart']) {
    let chartX = 0;
    typeGroups['chart'].forEach(item => {
      item.position.x = chartX;
      item.position.y = currentY;
      item.position.w = 6;  // 半宽
      item.position.h = 4;  // 固定高度
      
      chartX += 6;
      if (chartX >= 12) { // 换行
        chartX = 0;
        currentY += 4;
      }
    });
    
    if (chartX > 0) { // 确保Y坐标更新
      currentY += 4;
    }
  }
  
  // 处理其他类型...
  
  return items;
}
```

## 5. 性能优化

### 5.1 大数据量处理

处理大量网格项的优化策略：

```typescript
// 1. 虚拟滚动实现
import { useVirtualList } from '@vueuse/core';

// 在组件中使用
const { list, containerProps, wrapperProps } = useVirtualList(
  gridItems,
  {
    itemHeight: 50, // 每项的高度
    overscan: 10    // 预渲染的项数
  }
);

// 2. 分页加载
const pageSize = 20;
const currentPage = ref(1);

const displayedItems = computed(() => {
  const start = (currentPage.value - 1) * pageSize;
  const end = start + pageSize;
  return gridItems.value.slice(start, end);
});

// 3. 延迟渲染复杂组件
const renderComplexComponents = ref(false);

onMounted(() => {
  // 延迟200ms渲染复杂组件，优先保证页面框架渲染
  setTimeout(() => {
    renderComplexComponents.value = true;
  }, 200);
});
```

### 5.2 缓存策略

实现数据缓存以提高性能：

```typescript
// 使用localforage缓存API响应
async function fetchWithCache(key: string, fetchFn: () => Promise<any>, expireTime = 3600000) {
  try {
    // 尝试从缓存获取
    const cached = await localforage.getItem(key);
    if (cached) {
      const { data, timestamp } = cached as { data: any, timestamp: number };
      
      // 检查是否过期
      if (Date.now() - timestamp < expireTime) {
        console.log(`Using cached data for ${key}`);
        return data;
      }
    }
    
    // 缓存不存在或已过期，重新获取
    const freshData = await fetchFn();
    
    // 更新缓存
    await localforage.setItem(key, {
      data: freshData,
      timestamp: Date.now()
    });
    
    return freshData;
  } catch (error) {
    console.error('Cache operation failed:', error);
    // 缓存操作失败，直接获取新数据
    return fetchFn();
  }
}

// 使用示例
async function getApiDocWithCache() {
  const cacheKey = `api_doc_${selectedModule.value}`;
  return fetchWithCache(
    cacheKey,
    () => getApiDoc({ module: selectedModule.value, page: 1, pageSize: 1000 }),
    30 * 60 * 1000 // 30分钟过期
  );
}
```

## 6. 测试指南

### 6.1 单元测试

使用 Vitest 进行单元测试：

```typescript
// GridManagementService.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { GridManagementService } from '../service/GridManagementService';

describe('GridManagementService', () => {
  let service: GridManagementService;
  
  beforeEach(() => {
    service = new GridManagementService();
    // 模拟API调用
    vi.mock('../api', () => ({
      getApiDoc: vi.fn().mockResolvedValue({ list: [] }),
      getGroupNames: vi.fn().mockResolvedValue({ list: [] }),
      getDtos: vi.fn().mockResolvedValue({ list: [] }),
    }));
  });
  
  it('should process DTO structure correctly', () => {
    const structure = {
      id: { type: 'number', description: 'ID' },
      name: { type: 'string', description: '名称：1-名称1,2-名称2' },
      status: { type: 'number', description: '状态：0-禁用,1-启用' }
    };
    
    const result = service.processDtoStructure(structure);
    
    expect(result).toHaveLength(3);
    expect(result[0].prop).toBe('id');
    expect(result[1].label).toBe('名称');
    expect(result[2].options).toHaveLength(2);
  });
  
  // 更多测试...
});
```

### 6.2 组件测试

使用 Vue Test Utils 测试组件：

```typescript
// GridItemDialog.test.ts
import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import GridItemDialog from '../components/GridItemDialog.vue';
import { ElForm, ElInput, ElSelect } from 'element-plus';

describe('GridItemDialog', () => {
  it('renders correctly with props', async () => {
    const gridItem = {
      id: 1,
      name: 'Test Item',
      type: 'table',
      content: { data: [] },
      position: { x: 0, y: 0, w: 4, h: 2 },
      status: 1,
      uiConfigId: 1
    };
    
    const wrapper = mount(GridItemDialog, {
      props: {
        modelValue: true,
        gridItem
      },
      global: {
        stubs: {
          ElDialog: true,
          ElForm: true,
          ElFormItem: true,
          ElInput: true,
          ElSelect: true,
          ElOption: true
        }
      }
    });
    
    expect(wrapper.find('.dialog-title').text()).toContain('编辑网格项');
    expect(wrapper.findComponent(ElInput).exists()).toBe(true);
    
    // 测试表单值
    const nameInput = wrapper.findComponent('[data-test="name-input"]');
    expect(nameInput.props('modelValue')).toBe('Test Item');
    
    // 测试保存事件
    await wrapper.find('.save-button').trigger('click');
    expect(wrapper.emitted('save')).toBeTruthy();
  });
});
```

## 7. 部署与发布

### 7.1 构建优化

优化生产构建：

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
  plugins: [
    vue(),
    visualizer({ // 分析构建产物
      open: true,
      gzipSize: true,
      brotliSize: true
    })
  ],
  build: {
    target: 'es2015',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // 移除console
        drop_debugger: true // 移除debugger
      }
    },
    rollupOptions: {
      output: {
        manualChunks: {
          'element-plus': ['element-plus'],
          'grid-layout': ['grid-layout-plus'],
          'vendor': ['vue', 'pinia', 'vue-router']
        }
      }
    }
  }
});
```

### 7.2 版本管理

版本发布流程：

1. 更新版本号：
   ```bash
   npm version patch # 小版本更新
   npm version minor # 功能版本更新
   npm version major # 主版本更新
   ```

2. 生成变更日志：
   ```bash
   conventional-changelog -p angular -i CHANGELOG.md -s
   ```

3. 构建生产版本：
   ```bash
   npm run build
   ```

4. 发布：
   ```bash
   npm publish
   ```

## 8. 常见问题解答

### 8.1 网格项拖拽问题

**问题**：网格项无法拖拽或调整大小

**解决方案**：

1. 检查 `draggable` 和 `resizable` 属性：
   ```typescript
   // 确保这些属性设置正确
   const gridConfig = {
     draggable: true,
     resizable: true
   };
   ```

2. 检查网格项的锁定状态：
   ```typescript
   // 确保网格项未被锁定
   gridItem.position.locked = false;
   gridItem.position.no_move = false;
   gridItem.position.no_resize = false;
   ```

3. 检查CSS冲突：
   ```css
   /* 确保没有CSS规则阻止拖拽 */
   .grid-stack-item {
     pointer-events: auto !important;
   }
   ```

### 8.2 数据同步问题

**问题**：网格布局更改后数据未保存

**解决方案**：

1. 确保正确监听布局变化事件：
   ```typescript
   // 在GridLayout组件中
   onMounted(() => {
     gridStack.value?.on('change', (event, items) => {
       // 将变化同步到store
       store.updateGridItemsFromNodes(items);
     });
   });
   ```

2. 确保调用了保存API：
   ```typescript
   // 在保存布局时
   async function saveLayout() {
     try {
       await store.updateGridPositions();
       ElMessage.success('布局已保存');
     } catch (error) {
       ElMessage.error('保存布局失败');
       console.error(error);
     }
   }
   ```

## 9. 贡献指南

### 9.1 代码规范

- 遵循 TypeScript 类型安全原则
- 使用 ESLint 和 Prettier 保持代码风格一致
- 组件命名使用 PascalCase
- 方法命名使用 camelCase
- 常量使用 UPPER_SNAKE_CASE

### 9.2 提交规范

使用 Angular 提交规范：

```
<type>(<scope>): <subject>

<body>

<footer>
```

类型（type）：
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码风格修改
- refactor: 重构
- perf: 性能优化
- test: 测试
- chore: 构建过程或辅助工具变动

示例：
```
feat(grid-item): 添加新的网格项类型支持

- 增加自定义图表类型
- 支持数据源配置
- 添加样式自定义选项

Closes #123
```

## 10. 参考资源

- [Vue 3 文档](https://v3.vuejs.org/)
- [Pinia 文档](https://pinia.vuejs.org/)
- [Element Plus 文档](https://element-plus.org/)
- [GridStack.js 文档](https://github.com/gridstack/gridstack.js)
- [TypeScript 文档](https://www.typescriptlang.org/docs/)
- [Vue Test Utils 文档](https://test-utils.vuejs.org/)

---

本指南将持续更新，如有问题或建议，请联系开发团队。