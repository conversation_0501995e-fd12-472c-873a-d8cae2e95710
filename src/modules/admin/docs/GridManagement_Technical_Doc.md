# GridManagement 组件技术文档

## 1. 概述

GridManagement 是一个强大的网格管理系统，用于后台管理系统的页面可视化搭建与动态内容管理。它基于 gridstack.js 实现响应式网格布局，支持组件的拖拽、调整大小和动态渲染，为管理界面提供灵活的布局和业务扩展能力。

### 1.1 核心功能

- **可视化页面构建**：通过拖拽方式构建管理界面
- **动态组件管理**：支持多种类型组件的动态加载与配置
- **响应式布局**：自适应不同屏幕尺寸的网格布局
- **API 与 DTO 集成**：与后端 API 和数据结构紧密集成
- **配置持久化**：支持布局和组件配置的保存与恢复

### 1.2 技术栈

- **前端框架**：Vue 3 + TypeScript
- **状态管理**：Pinia
- **UI 组件库**：Element Plus
- **网格布局**：grid-layout-plus (基于 gridstack.js)
- **数据存储**：localforage (本地缓存)

## 2. 系统架构

GridManagement 组件采用三层架构设计：

```
┌─────────────────┐
│     视图层      │ GridManagement.vue
└────────┬────────┘
         │
┌────────▼────────┐
│     状态层      │ gridManagementStore.ts
└────────┬────────┘
         │
┌────────▼────────┐
│     服务层      │ GridManagementService.ts
└────────┬────────┘
         │
┌────────▼────────┐
│     API 层      │ gridInfos.ts, uiConfig.ts 等
└─────────────────┘
```

### 2.1 各层职责

- **视图层 (View)**：负责用户界面渲染和交互处理
- **状态层 (Store)**：管理应用状态，提供响应式数据
- **服务层 (Service)**：封装业务逻辑和 API 调用
- **API 层**：与后端通信，处理数据请求和响应

## 3. 核心组件

### 3.1 GridManagementService

`GridManagementService` 是系统的核心服务类，负责封装所有网格管理相关的后端 API 调用和数据处理逻辑。

#### 主要职责

- 与后端 API 交互，获取和保存网格数据
- 处理模块、分组、DTO 和 API 数据
- 生成和管理页面配置
- 处理网格项的增删改查操作

#### 关键方法

```typescript
// 模块和分组相关
handleModuleChange(): Promise<void>
handleGroupChange(group?: string): Promise<void>

// API 和 DTO 相关
fetchApiDoc(): Promise<void>
fetchDtos(): Promise<void>
processDtoStructure(structure: Record<string, any>): any[]

// 页面配置相关
generateConfig(dto: any): void
saveConfig(): Promise<void>

// 网格项相关
addGridItem(item: any): Promise<void>
updateGridItem(item: any): Promise<void>
removeGridItem(id: number): Promise<void>
```

### 3.2 gridManagementStore

`gridManagementStore` 是基于 Pinia 的状态管理器，负责管理网格数据的状态，提供响应式数据和业务逻辑方法。

#### 主要职责

- 提供全局状态管理
- 封装 GridManagementService 的方法
- 实现组件间数据共享

#### 关键特性

```typescript
// 状态定义
modules: Ref<string[]>
selectedModule: Ref<string>
groupNames: Ref<GroupName[]>
dtos: Ref<any[]>
pageConfig: Ref<any>
gridItems: Ref<AdminGridInfoDTO[]>

// 计算属性
filteredDtos: ComputedRef<any[]>

// 方法
updateCurrentGridItemContent(content: string): void
saveGridItem(gridItem: any): void
```

### 3.3 GridManagement.vue

`GridManagement.vue` 是网格管理的主视图组件，负责展示和管理网格数据，处理用户相关操作。

#### 主要功能

- 模块和分组选择
- API 和 DTO 浏览
- 页面配置编辑
- 网格项管理
- 布局调整

## 4. 数据结构

### 4.1 网格项 (Grid Item)

网格项是页面内的功能单元，支持拖拽排序和布局调整。

```typescript
interface AdminGridInfoDTO {
  id: number;
  name: string;
  type: string;
  content: any;
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
    min_w?: number;
    min_h?: number;
    max_w?: number;
    max_h?: number;
    locked?: boolean;
    no_move?: boolean;
    no_resize?: boolean;
    auto_position?: boolean;
  };
  status: number;
  uiConfigId: number;
}
```

### 4.2 页面配置

页面配置包含页面的整体设置和布局信息。

```typescript
interface ConfigData {
  icon: string;
  module: string;
  group: string;
  title: string;
  frontend_path: string;
  config_type: string;
  config_key: string;
  config_content: string;
  remark: string;
  status: number;
  version: string;
  version_hash: string;
  uiConfigId: number;
  dto?: string;
  grid_items?: AdminGridInfoDTO[];
  draggable: boolean;
  resizable: boolean;
}
```

## 5. 工作流程

### 5.1 页面配置生成流程

1. 选择模块和分组
2. 浏览并选择 DTO
3. 生成页面配置
4. 调整配置参数
5. 保存配置

### 5.2 网格项管理流程

1. 加载页面配置
2. 添加/编辑网格项
3. 调整网格布局
4. 保存布局和内容

## 6. API 接口

### 6.1 网格信息 API

```typescript
// 获取网格信息列表
getGridInfoList(params: { page?: number; pageSize?: number; keyword?: string; status?: string; })

// 创建网格信息
createGridInfo(data: any)

// 获取网格信息详情
getGridInfo(id: number)

// 更新网格信息
updateGridInfo(id: number, data: any)

// 删除网格信息
deleteGridInfo(id: number)

// 更新网格信息状态
updateGridInfoStatus(id: number, data: { status: string; })

// 批量更新网格信息位置
batchUpdateGridInfoPosition(data: { items: Array<...> })
```

## 7. 使用指南

### 7.1 基本使用

1. 引入并注册组件

```typescript
import { GridManagement } from '@/modules/admin/views';
```

2. 在路由中配置

```typescript
{
  path: '/admin/grid-management',
  name: 'GridManagement',
  component: GridManagement,
  meta: {
    title: '网格管理',
    requiresAuth: true,
    permissions: ['admin:grid:view']
  }
}
```

### 7.2 创建新页面配置

1. 选择模块和分组
2. 选择或输入 DTO 结构
3. 设置页面标题和路径
4. 点击「生成配置」按钮
5. 调整生成的配置
6. 点击「保存配置」按钮

### 7.3 管理网格项

1. 点击「添加组件」按钮
2. 设置组件名称、类型和内容
3. 拖拽调整组件位置和大小
4. 点击「保存布局」按钮

## 8. 最佳实践

### 8.1 性能优化

- 使用懒加载减少初始加载时间
- 合理设置网格项的最小/最大尺寸
- 避免在一个页面中放置过多网格项

### 8.2 扩展性建议

- 创建自定义组件类型时，遵循现有的类型定义
- 使用 TypeScript 接口确保类型安全
- 为新组件提供完整的配置选项

## 9. 常见问题

### 9.1 网格项无法拖拽

- 检查 `draggable` 属性是否设置为 `true`
- 确认网格项未被锁定 (`locked: false`)
- 验证用户是否有编辑权限

### 9.2 配置无法保存

- 检查网络连接
- 确认用户权限
- 验证配置数据格式是否正确

## 10. 未来规划

- 支持更多类型的组件
- 增强组件间的数据联动
- 提供更丰富的主题和样式选项
- 优化移动端体验

---

## 附录

### A. 组件类型列表

| 类型 | 描述 | 支持的配置项 |
|------|------|-------------|
| table | 数据表格 | 列配置、分页、排序、筛选 |
| form | 表单 | 字段、验证规则、布局 |
| chart | 图表 | 图表类型、数据源、样式 |
| card | 卡片 | 标题、内容、操作 |
| list | 列表 | 项目模板、分页、排序 |

### B. 相关文档

- [GridPage 组件与网格管理结构设计文档](./GridPage_GridLayout_Design.md)
- [Element Plus 文档](https://element-plus.org/)
- [grid-layout-plus 文档](https://github.com/gridstack/gridstack.js)