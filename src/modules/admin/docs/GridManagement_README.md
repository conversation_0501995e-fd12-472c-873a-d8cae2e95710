# GridManagement 组件文档

## 概述

GridManagement 是一个强大的网格管理系统，用于后台管理系统的页面可视化搭建与动态内容管理。它基于 gridstack.js 实现响应式网格布局，支持组件的拖拽、调整大小和动态渲染，为管理界面提供灵活的布局和业务扩展能力。

## 文档导航

本文档集包含以下几个部分，请根据需要查阅：

1. [技术文档](./GridManagement_Technical_Doc.md) - 系统架构、核心功能和数据结构的详细说明
2. [开发指南](./GridManagement_Developer_Guide.md) - 详细的开发教程和最佳实践
3. [API 参考](./GridManagement_API_Reference.md) - 完整的 API 接口说明和使用示例
4. [设计文档](./GridPage_GridLayout_Design.md) - GridPage 组件与网格管理的设计理念

## 核心功能

- **可视化页面构建**：通过拖拽方式构建管理界面
- **动态组件管理**：支持多种类型组件的动态加载与配置
- **响应式布局**：自适应不同屏幕尺寸的网格布局
- **API 与 DTO 集成**：与后端 API 和数据结构紧密集成
- **配置持久化**：支持布局和组件配置的保存与恢复

## 快速开始

### 1. 引入组件

```typescript
import { GridManagement } from '@/modules/admin/views';
```

### 2. 在路由中配置

```typescript
{
  path: '/admin/grid-management',
  name: 'GridManagement',
  component: GridManagement,
  meta: {
    title: '网格管理',
    requiresAuth: true,
    permissions: ['admin:grid:view']
  }
}
```

### 3. 基本使用流程

1. 选择模块和分组
2. 浏览并选择 DTO
3. 生成页面配置
4. 添加和编辑网格项
5. 调整网格布局
6. 保存配置

## 系统架构

GridManagement 组件采用三层架构设计：

```
┌─────────────────┐
│     视图层      │ GridManagement.vue
└────────┬────────┘
         │
┌────────▼────────┐
│     状态层      │ gridManagementStore.ts
└────────┬────────┘
         │
┌────────▼────────┐
│     服务层      │ GridManagementService.ts
└────────┬────────┘
         │
┌────────▼────────┐
│     API 层      │ gridInfos.ts, uiConfig.ts 等
└─────────────────┘
```

## 技术栈

- **前端框架**：Vue 3 + TypeScript
- **状态管理**：Pinia
- **UI 组件库**：Element Plus
- **网格布局**：grid-layout-plus (基于 gridstack.js)
- **数据存储**：localforage (本地缓存)

## 版本信息

- 当前版本：v1.1.0
- 发布日期：2025-04-22
- 兼容性：Vue 3.3.0+, Pinia 2.1.0+, Element Plus 2.3.0+

## 贡献指南

欢迎贡献代码或提出改进建议！请参阅[开发指南](./GridManagement_Developer_Guide.md)中的贡献章节了解详情。

## 联系方式

如有问题或建议，请联系开发团队：

- 作者：张二浩
- 邮箱：<EMAIL>

---

© 2025 O-Mall 团队。保留所有权利。