/**
 * API响应类型定义
 */

// 基础响应类型
export interface BaseResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 管理员登录响应
export interface AdminLoginResponse {
  token: string;
  longTermToken: string;
  expireTime: number;
  adminInfo: {
    id: number;
    username: string;
    nickname: string;
    avatar?: string;
    role: string;
    permissions: string[];
  };
}

// 管理员信息
export interface AdminInfo {
  id: number;
  username: string;
  nickname: string;
  avatar?: string;
  role: string;
  permissions: string[];
  status: number;
  lastLoginTime?: string;
  createTime: string;
  updateTime: string;
}

// 管理员列表项
export interface AdminListItem extends AdminInfo {
  roleName: string;
}

// 角色信息
export interface RoleInfo {
  id: number;
  name: string;
  code: string;
  description?: string;
  permissions: string[];
  status: number;
  createTime: string;
  updateTime: string;
}

// 权限信息
export interface PermissionInfo {
  id: number;
  name: string;
  code: string;
  type: string;
  parentId?: number;
  path?: string;
  component?: string;
  icon?: string;
  sort: number;
  status: number;
  createTime: string;
  updateTime: string;
}

// 系统配置
export interface SystemConfig {
  id: number;
  key: string;
  value: string;
  description?: string;
  type: string;
  createTime: string;
  updateTime: string;
}

// 系统日志
export interface SystemLog {
  id: number;
  type: string;
  content: string;
  operator?: string;
  ip?: string;
  userAgent?: string;
  createTime: string;
}

// 系统资源
export interface SystemResource {
  id: number;
  name: string;
  type: string;
  size: number;
  path: string;
  mimeType: string;
  createTime: string;
  updateTime: string;
}

// 系统备份
export interface SystemBackup {
  id: number;
  name: string;
  path: string;
  size: number;
  type: string;
  status: number;
  createTime: string;
}

// 系统通知
export interface SystemNotification {
  id: number;
  title: string;
  content: string;
  type: string;
  status: number;
  startTime: string;
  endTime: string;
  createTime: string;
  updateTime: string;
}

// 系统任务
export interface SystemTask {
  id: number;
  name: string;
  type: string;
  cron: string;
  command: string;
  status: number;
  lastRunTime?: string;
  nextRunTime?: string;
  createTime: string;
  updateTime: string;
}

// 任务日志
export interface TaskLog {
  id: number;
  taskId: number;
  status: number;
  startTime: string;
  endTime?: string;
  result?: string;
  error?: string;
  createTime: string;
}

// 统计数据
export interface SystemStats {
  userCount: number;
  orderCount: number;
  productCount: number;
  merchantCount: number;
  revenue: number;
  todayOrderCount: number;
  todayRevenue: number;
  pendingOrderCount: number;
  pendingRefundCount: number;
} 