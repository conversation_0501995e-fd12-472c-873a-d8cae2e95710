// 管理后台菜单项
export const ADMIN_MENU_ITEMS = [
  {
    key: 'dashboard',
    label: '控制台',
    icon: 'Odometer',
    path: '/admin/dashboard',
  },
  {
    key: 'users',
    label: '用户管理',
    icon: 'User',
    path: '/admin/users',
  },
  {
    key: 'merchants',
    label: '商家管理',
    icon: 'Shop',
    path: '/admin/merchants',
  },
  {
    key: 'foods',
    label: '外卖管理',
    icon: 'Goods',
    path: '/admin/takeout/food/list',
  },
  {
    key: 'runners',
    label: '跑腿员管理',
    icon: 'UserFilled',
    path: '/admin/runners',
  },
  {    key: 'referral',    label: '分销配置管理',    icon: 'Share',    path: '/admin/referral',  },
  // {
  //   key: 'orders',
  //   label: '订单管理',
  //   icon: 'List',
  //   path: '/admin/orders',
  // },
  {
    key: 'admins',
    label: '管理员管理',
    icon: 'UserFilled',
    path: '/admin/admins',
  },
  {
    key: 'system',
    label: '系统设置',
    icon: 'Setting',
    path: '/admin/system',
    children: [
      // {
      //   key: 'apis',
      //   label: 'API管理',
      //   path: '/admin/system/apis',
      // },
      {
        key: 'gridmanagement',
        label: 'Grid管理',
        path: '/admin/system/gridmanagement',
      },
      // {
      //   key: 'grid',
      //   label: '网格仪表盘',
      //   path: '/admin/system/grid',
      // },
      {
        key: 'permissions',
        label: '权限设置',
        path: '/admin/system/permissions',
      },
      {
        key: 'roles',
        label: '角色权限',
        path: '/admin/system/roles',
      },
      {
        key: 'config',
        label: '系统配置',
        path: '/admin/system/config',
      },
      {
        key: 'global-categories',
        label: '全局分类管理',
        icon: 'FolderOpened',
        path: '/admin/global-categories',
      },
    ],
  },
];

// 状态标签配置
export const STATUS_TAG_CONFIG = {
  // 管理员状态
  adminStatus: {
    ACTIVE: { type: 'success', label: '正常' },
    LOCKED: { type: 'warning', label: '锁定' },
    DISABLED: { type: 'danger', label: '禁用' },
  },
  // 用户状态
  userStatus: {
    ACTIVE: { type: 'success', label: '正常' },
    LOCKED: { type: 'warning', label: '锁定' },
    DISABLED: { type: 'danger', label: '禁用' },
  },
  // 商家状态
  merchantStatus: {
    ACTIVE: { type: 'success', label: '正常' },
    SUSPENDED: { type: 'warning', label: '暂停营业' },
    CLOSED: { type: 'danger', label: '关闭' },
  },
  // 商家审核状态
  merchantAuditStatus: {
    PENDING: { type: 'info', label: '待审核' },
    APPROVED: { type: 'success', label: '已通过' },
    REJECTED: { type: 'danger', label: '已拒绝' },
  },
  // 商品状态
  productStatus: {
    ON_SALE: { type: 'success', label: '在售' },
    OFF_SHELF: { type: 'info', label: '下架' },
    SOLD_OUT: { type: 'warning', label: '售罄' },
  } as const, // 使用 as const 断言，确保类型是字面量类型
  // 商品审核状态
  productAuditStatus: {
    PENDING: { type: 'info', label: '待审核' },
    APPROVED: { type: 'success', label: '已通过' },
    REJECTED: { type: 'danger', label: '已拒绝' },
  } as const, // 使用 as const 断言，确保类型是字面量类型
  // 订单状态
  orderStatus: {
    10: { type: 'info', label: '待支付' },
    20: { type: 'success', label: '已支付' },
    30: { type: 'primary', label: '处理中' },
    40: { type: 'primary', label: '配送中' },
    50: { type: 'success', label: '已完成' },
    60: { type: 'info', label: '已取消' },
    70: { type: 'warning', label: '退款中' },
    80: { type: 'danger', label: '已退款' },
  },
  // 支付状态
  paymentStatus: {
    UNPAID: { type: 'info', label: '未支付' },
    PAID: { type: 'success', label: '已支付' },
    REFUNDING: { type: 'warning', label: '退款中' },
    REFUNDED: { type: 'danger', label: '已退款' },
  },
  // 配送状态
  deliveryStatus: {
    10: { type: 'info', label: '未发货' },
    20: { type: 'primary', label: '已发货' },
    30: { type: 'success', label: '已送达' },
  },
};
