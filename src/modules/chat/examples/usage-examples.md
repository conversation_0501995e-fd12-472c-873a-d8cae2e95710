# 聊天模块使用示例

## 在不同Layout中使用聊天功能

### 1. 管理员后台中使用聊天

```vue
<!-- AdminLayout.vue -->
<template>
  <div class="admin-layout">
    <!-- 其他管理员界面内容 -->
    
    <!-- 聊天组件 -->
    <ChatWindow 
      v-if="showChat"
      :show-session-list="true"
      :default-minimized="false"
      @close="handleCloseChat"
    />
    
    <!-- 聊天按钮 -->
    <button @click="toggleChat" class="chat-toggle-btn">
      <ChatDotRound />
      聊天
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ChatWindow from '@/modules/chat/components/ChatWindow.vue'
import { ChatDotRound } from '@element-plus/icons-vue'

const showChat = ref(false)

const toggleChat = () => {
  showChat.value = !showChat.value
}

const handleCloseChat = () => {
  showChat.value = false
}
</script>
```

**Token使用**: 自动使用 `admin_access_token`
**路由**: `/admin/*`
**权限**: 管理员权限

### 2. 商家后台中使用聊天

```vue
<!-- MerchantLayout.vue -->
<template>
  <div class="merchant-layout">
    <!-- 其他商家界面内容 -->
    
    <!-- 聊天组件 -->
    <ChatWindow 
      v-if="showChat"
      :show-session-list="true"
      :default-minimized="false"
      @close="handleCloseChat"
    />
    
    <!-- 聊天按钮 -->
    <button @click="toggleChat" class="chat-toggle-btn">
      <Service />
      客服
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ChatWindow from '@/modules/chat/components/ChatWindow.vue'
import { Service } from '@element-plus/icons-vue'

const showChat = ref(false)

const toggleChat = () => {
  showChat.value = !showChat.value
}

const handleCloseChat = () => {
  showChat.value = false
}
</script>
```

**Token使用**: 自动使用 `merchant_access_token`
**路由**: `/merchant/*`
**权限**: 商家权限

### 3. 用户端中使用聊天

```vue
<!-- UserLayout.vue -->
<template>
  <div class="user-layout">
    <!-- 其他用户界面内容 -->
    
    <!-- 聊天组件 -->
    <ChatWindow 
      v-if="showChat"
      :show-session-list="true"
      :default-minimized="true"
      @close="handleCloseChat"
    />
    
    <!-- 聊天按钮 -->
    <button @click="toggleChat" class="chat-toggle-btn">
      <ChatDotRound />
      消息
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ChatWindow from '@/modules/chat/components/ChatWindow.vue'
import { ChatDotRound } from '@element-plus/icons-vue'

const showChat = ref(false)

const toggleChat = () => {
  showChat.value = !showChat.value
}

const handleCloseChat = () => {
  showChat.value = false
}
</script>
```

**Token使用**: 自动使用 `user_access_token`
**路由**: `/user/*` 或 `/runner/*`
**权限**: 用户权限

## API调用示例

### 发送消息

```typescript
import { MessageService } from '@/modules/chat/services/message'

const messageService = new MessageService()

// 在管理员后台发送消息（自动使用admin token）
await messageService.sendTextMessage(sessionId, '管理员回复消息')

// 在商家后台发送消息（自动使用merchant token）
await messageService.sendTextMessage(sessionId, '商家回复消息')

// 在用户端发送消息（自动使用user token）
await messageService.sendTextMessage(sessionId, '用户发送消息')
```

### 获取会话列表

```typescript
import * as chatApi from '@/modules/chat/api'

// 在不同layout中调用相同的API，但会使用不同的token
const sessions = await chatApi.getSessions({
  page: 1,
  page_size: 20
})
```

## WebSocket连接示例

```typescript
import { getChatWebSocketManager } from '@/modules/chat/services/websocket-manager'

// 在任何layout中都可以这样使用
const wsManager = getChatWebSocketManager({
  debug: true
})

// 连接时会自动使用当前layout对应的token
await wsManager.connect()

// 监听消息
wsManager.on('new_message', (messageData) => {
  console.log('收到新消息:', messageData)
})
```

## 调试和验证

### 1. 检查当前使用的Token

```typescript
// 在浏览器控制台中运行
console.log('当前路径:', window.location.pathname)

// 检查localStorage中的token
console.log('Admin token:', localStorage.getItem('admin_access_token'))
console.log('Merchant token:', localStorage.getItem('merchant_access_token'))
console.log('User token:', localStorage.getItem('user_access_token'))
```

### 2. 验证网络请求

打开浏览器开发者工具的Network标签页，查看聊天相关的请求：

- 请求头中的 `Authorization` 字段应该包含正确的token
- 控制台日志应该显示正确的命名空间信息

### 3. 测试不同场景

1. **管理员后台测试**
   - 访问 `/admin/dashboard`
   - 打开聊天功能
   - 发送消息
   - 检查使用的是admin token

2. **商家后台测试**
   - 访问 `/merchant/products`
   - 打开聊天功能
   - 发送消息
   - 检查使用的是merchant token

3. **用户端测试**
   - 访问 `/user/profile`
   - 打开聊天功能
   - 发送消息
   - 检查使用的是user token

## 常见问题

### Q: 如何添加新的模块支持？

A: 在 `getCurrentNamespace()` 函数中添加新的路由判断：

```typescript
function getCurrentNamespace(): string {
  const currentPath = window.location.pathname;
  
  if (currentPath.startsWith('/admin')) {
    return 'admin';
  } else if (currentPath.startsWith('/merchant')) {
    return 'merchant';
  } else if (currentPath.startsWith('/newmodule')) {
    return 'newmodule';  // 新增模块
  } else if (currentPath.startsWith('/user') || currentPath.startsWith('/runner')) {
    return 'user';
  }
  
  return 'user';
}
```

### Q: 如何处理token过期？

A: 系统会自动检测token过期并尝试刷新。如果刷新失败，会清除对应模块的token并提示用户重新登录。

### Q: 跨模块聊天如何处理？

A: 每个模块使用自己的token进行身份验证，后端会根据token确定用户身份和权限。跨模块聊天通过后端的权限控制来实现。

## 最佳实践

1. **统一的错误处理**: 在每个layout中统一处理聊天相关的错误
2. **状态管理**: 使用Pinia store管理聊天状态，避免重复初始化
3. **性能优化**: 合理使用组件的懒加载和缓存机制
4. **用户体验**: 提供清晰的加载状态和错误提示
