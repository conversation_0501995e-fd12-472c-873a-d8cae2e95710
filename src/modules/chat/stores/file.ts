/**
 * 文件状态管理
 */

import { defineStore } from 'pinia'
import { ref } from 'vue'
// 简化的类型定义
interface FileInfo {
  id: string
  name: string
  size: number
  type: string
  url?: string
  created_at: string
}

interface FileUploadProgress {
  id: string
  progress: number
  status: 'pending' | 'uploading' | 'completed' | 'error'
  speed?: number
  error?: string
}

interface FileUploadRequest {
  file: File
  sessionId?: string
  metadata?: Record<string, any>
}

interface FileDownloadRequest {
  fileId: string
  fileName?: string
}

interface FilePreviewResponse {
  id: string
  preview_url: string
  thumbnail_url?: string
  metadata?: Record<string, any>
}
// import { FileService } from '../services/file' // 暂时注释掉
// import { Logger } from '../utils/logger' // 未使用，注释掉

/**
 * 文件状态接口
 */
export interface FileState {
  // 文件上传
  uploadProgress: Map<string, FileUploadProgress> // uploadId -> progress
  uploadQueue: FileUploadRequest[] // 上传队列
  isUploading: boolean
  
  // 文件下载
  downloadProgress: Map<string, number> // fileId -> progress (0-100)
  downloadQueue: FileDownloadRequest[] // 下载队列
  isDownloading: boolean
  
  // 文件预览
  previewCache: Map<string, FilePreviewResponse> // fileId -> preview data
  previewLoading: Set<string> // 正在预览的文件ID
  
  // 文件列表
  files: FileInfo[]
  fileCache: Map<string, FileInfo> // fileId -> file info
  
  // 文件分类
  filesByType: Map<string, FileInfo[]> // fileType -> files
  
  // 搜索和过滤
  searchQuery: string
  searchResults: FileInfo[]
  searchLoading: boolean
  filters: {
    type?: string
    size_min?: number
    size_max?: number
    created_after?: number
    created_before?: number
    uploader?: string
  }
  
  // 分页信息
  pagination: {
    page: number
    per_page: number
    total: number
    has_more: boolean
  }
  
  // UI状态
  isLoading: boolean
  error: string | null
  
  // 文件操作
  selectedFiles: Set<string> // 选中的文件ID
  
  // 文件设置
  settings: {
    maxFileSize: number // 最大文件大小（字节）
    allowedTypes: string[] // 允许的文件类型
    autoCompress: boolean // 自动压缩图片
    compressionQuality: number // 压缩质量 (0-1)
    chunkSize: number // 分块上传大小
    maxConcurrentUploads: number // 最大并发上传数
    enablePreview: boolean // 启用文件预览
  }
  
  // 统计信息
  statistics: {
    totalFiles: number
    totalSize: number
    uploadedToday: number
    downloadedToday: number
    storageUsed: number
    storageLimit: number
  } | null
}

/**
 * 文件状态管理
 */
// 简化的文件 Store
export const useFileStore = defineStore('file', () => {
  const files = ref<any[]>([])
  const uploadProgress = ref(new Map<string, any>())
  const uploadQueue = ref<any[]>([])
  const isUploading = ref(false)
  const error = ref<string | null>(null)
  const statistics = ref<any | null>(null)

  // 简化的方法
  const uploadFile = async (file: File) => {
    console.log('上传文件:', file.name)
    return { success: true, file_url: '/uploads/' + file.name }
  }

  const deleteFile = async (fileId: string) => {
    console.log('删除文件:', fileId)
    return { success: true }
  }

  return {
    files,
    uploadProgress,
    uploadQueue,
    isUploading,
    error,
    statistics,
    uploadFile,
    deleteFile
  }
})

export default useFileStore
