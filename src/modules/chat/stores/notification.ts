/**
 * 通知状态管理
 */

import { defineStore } from 'pinia'
import { ref } from 'vue'
import type {
  Notification,
  NotificationType,
  NotificationPriority,
  NotificationStatus,
  NotificationChannel,
  NotificationSettings,
  NotificationTemplate,
  // NotificationBatchOperation, // 未使用，注释掉
  // NotificationStatistics, // 未使用，注释掉
  // SendNotificationRequest, // 未使用，注释掉
  // GetNotificationsRequest, // 未使用，注释掉
  // UpdateNotificationSettingsRequest // 未使用，注释掉
} from '../types/notification'
// import { NotificationService } from '../services/notification' // 未使用，注释掉
// import { Logger } from '../utils/logger' // 未使用，注释掉

// 简化的统计类型定义
interface NotificationStatistics {
  total_notifications: number
  unread_notifications: number
  read_notifications: number
  archived_notifications: number
}

/**
 * 通知状态接口
 */
export interface NotificationState {
  // 通知数据
  notifications: Notification[]
  notificationCache: Map<number, Notification> // notificationId -> notification
  
  // 分页信息
  pagination: {
    page: number
    per_page: number
    total: number
    has_more: boolean
  }
  
  // 未读通知
  unreadCount: number
  unreadNotifications: Notification[]
  
  // 通知设置
  settings: NotificationSettings | null
  
  // 通知模板
  templates: NotificationTemplate[]
  
  // 统计信息
  statistics: NotificationStatistics | null
  
  // 过滤和搜索
  filters: {
    type?: NotificationType
    status?: NotificationStatus
    priority?: NotificationPriority
    channel?: NotificationChannel
    created_after?: number
    created_before?: number
  }
  searchQuery: string
  searchResults: Notification[]
  searchLoading: boolean
  
  // UI状态
  isLoading: boolean
  error: string | null
  
  // 通知操作
  selectedNotifications: Set<number> // 选中的通知ID
  
  // 实时通知
  showDesktopNotifications: boolean
  playNotificationSounds: boolean
  
  // 通知队列（用于批量处理）
  notificationQueue: Notification[]
  isProcessingQueue: boolean
}

/**
 * 通知状态管理
 */
// 简化的通知 Store
export const useNotificationStore = defineStore('notification', () => {
  const notifications = ref<any[]>([])
  const unreadCount = ref(0)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const statistics = ref<NotificationStatistics | null>(null)

  // 简化的方法
  const loadNotifications = async () => {
    console.log('加载通知')
    return { success: true, notifications: [] }
  }

  const markAsRead = async (notificationId: number) => {
    console.log('标记通知已读:', notificationId)
    return { success: true }
  }

  return {
    notifications,
    unreadCount,
    isLoading,
    error,
    statistics,
    loadNotifications,
    markAsRead
  }
})

export default useNotificationStore
