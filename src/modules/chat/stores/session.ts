/**
 * 简化的会话状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getCategorySessions } from '../api'
import {
  SessionType,
  SessionStatus,
  SessionPriority
} from '../types/session'
import type {
  Session,
  SessionParticipant,
  // SessionSettings, // 未使用，注释掉
  CreateSessionRequest,
  UpdateSessionRequest,
  GetSessionsRequest
} from '../types/session'

export const useSessionStore = defineStore('session', () => {
  // State
  const sessions = ref<Session[]>([])
  const sessionCache = ref(new Map<string, Session>())
  const currentSessionId = ref<string | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // 用于跟踪正在进行的会话加载请求，防止竞态条件
  const loadingRequests = ref(new Map<string, { promise: Promise<any>, requestId: number }>())
  const participants = ref(new Map<string, SessionParticipant[]>())
  const statistics = ref<any>(null)
  const selectedSessions = ref(new Set<string>())
  const pagination = ref({
    page: 1,
    per_page: 20,
    total: 0,
    has_more: false
  })
  const searchResults = ref<Session[]>([])
  const searchQuery = ref('')
  const searchLoading = ref(false)
  const filters = ref<any>({})
  const sortBy = ref('updated_at')
  const sortOrder = ref<'asc' | 'desc'>('desc')

  // Getters
  const currentSession = computed(() => {
    if (!currentSessionId.value) return null
    return sessionCache.value.get(currentSessionId.value) || null
  })

  const filteredSessions = computed(() => {
    return sessions.value.filter(session => {
      // 简化的过滤逻辑
      if (filters.value.status && session.status !== filters.value.status) {
        return false
      }
      if (filters.value.type && session.type !== filters.value.type) {
        return false
      }
      return true
    })
  })

  const hasMoreSessions = computed(() => {
    return pagination.value.has_more
  })

  // Actions
  const createSession = async (request: CreateSessionRequest) => {
    try {
      isLoading.value = true
      error.value = null

      // 简化会话创建
      const session: any = {
        id: Date.now(),
        type: request.type || SessionType.CUSTOMER_SERVICE,
        status: SessionStatus.ACTIVE,
        priority: SessionPriority.NORMAL,
        title: request.title || '新会话',
        participants: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        // 添加必要的默认属性
        creator_id: 1,
        creator_type: 'user',
        unread_count: 0,
        last_message_at: new Date().toISOString()
      }

      sessions.value.unshift(session)
      sessionCache.value.set(session.id.toString(), session)

      return session
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建会话失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 辅助函数：映射会话类型
  const mapSessionType = (backendType: string): SessionType => {
    switch (backendType) {
      case 'user_to_user':
        return SessionType.FRIEND_CHAT
      case 'user_to_merchant':
        return SessionType.PRESALE_CONSULTATION
      case 'merchant_to_user':
        return SessionType.CUSTOMER_SERVICE
      case 'order_notification':
        return SessionType.SYSTEM_NOTIFICATION
      case 'system_notification':
        return SessionType.SYSTEM_NOTIFICATION
      case 'presale_consultation':
        return SessionType.PRESALE_CONSULTATION
      case 'after_sale_service':
        return SessionType.AFTER_SALE_SERVICE
      case 'friend_chat':
        return SessionType.FRIEND_CHAT
      case 'customer_service':
        return SessionType.CUSTOMER_SERVICE
      case 'group_chat':
        return SessionType.GROUP_CHAT
      default:
        return SessionType.FRIEND_CHAT
    }
  }

  // 辅助函数：生成会话标题
  const generateSessionTitle = (item: any): string => {
    console.log('🏷️ [generateSessionTitle] 生成会话标题:', {
      sessionId: item.id,
      target_name: item.target_name,
      sender_name: item.last_message?.sender_name,
      hasTargetName: !!item.target_name
    })

    // 优先使用target_name字段（后端返回的对话对象名称）
    if (item.target_name) {
      const title = `与 ${item.target_name} 的对话`
      console.log('✅ [generateSessionTitle] 使用target_name:', title)
      return title
    }

    // 回退到使用最后消息的发送者名称
    if (item.last_message?.sender_name) {
      const title = `与 ${item.last_message.sender_name} 的对话`
      console.log('⚠️ [generateSessionTitle] 回退到sender_name:', title)
      return title
    }

    const title = `会话 ${item.id}`
    console.log('❌ [generateSessionTitle] 使用默认标题:', title)
    return title
  }

  const loadSessions = async (request: GetSessionsRequest = {}) => {
    const requestKey = `${request.category || 'chat'}_${request.page || 1}_${request.page_size || 20}`
    
    // 检查是否已有相同的请求正在进行
    const existingRequest = loadingRequests.value.get(requestKey)
    if (existingRequest) {
      console.log(`⏳ 会话加载请求已存在，等待完成: ${requestKey}`)
      return await existingRequest.promise
    }

    const requestId = Date.now()
    console.log(`🔄 开始加载会话列表 [${requestId}]:`, request)
    
    const requestPromise = (async () => {
      try {
        isLoading.value = true
        error.value = null

        // 调用真实的API
        const response = await getCategorySessions({
          category: request.category || 'chat',  // 默认使用chat类别
          page: request.page || 1,
          page_size: request.page_size || 20
        })

        // 检查请求是否仍然有效（防止竞态条件）
        const currentRequest = loadingRequests.value.get(requestKey)
        if (!currentRequest || currentRequest.requestId !== requestId) {
          console.log(`⚠️ 会话加载请求已被新请求替代，丢弃结果 [${requestId}]`)
          return null
        }

        console.log(`✅ 会话列表API响应 [${requestId}]:`, response)

        // 根据实际API响应结构解析数据
        // API响应格式: {category: 'service', list: Array, page: 1, page_count: 1, page_size: 20, total: 2}
        const sessionData = response.list || response.data?.list || response.data?.sessions || []
        
        if (response && sessionData && Array.isArray(sessionData)) {
          // 再次检查请求有效性
          const finalRequest = loadingRequests.value.get(requestKey)
          if (!finalRequest || finalRequest.requestId !== requestId) {
            console.log(`⚠️ 会话加载请求在处理过程中被替代，丢弃结果 [${requestId}]`)
            return null
          }

          // 转换后端数据格式为前端Session格式
          const sessionList = sessionData.map((item: any) => ({
            id: item.id,
            type: mapSessionType(item.type),
            status: item.status === 0 ? SessionStatus.ACTIVE : SessionStatus.INACTIVE,
            priority: SessionPriority.NORMAL,
            title: generateSessionTitle(item),
            participants: [],
            created_at: item.created_at,
            updated_at: item.updated_at,
            creator_id: item.creator_id,
            creator_type: item.creator_type,
            receiver_id: item.receiver_id,
            receiver_type: item.receiver_type,
            unread_count: item.unread_count || 0,
            last_message_id: item.last_message_id,
            last_message: item.last_message,
            last_message_at: item.last_message?.created_at || item.updated_at,
            // 添加target_name和target_avatar字段
            target_name: item.target_name || item.last_message?.sender_name || `用户${item.id}`,
            target_avatar: item.target_avatar || '',
            // 保留原始后端类型信息，用于分类过滤
            original_type: item.type
          }))

          sessions.value = sessionList as unknown as Session[]
          sessionList.forEach(session => {
            sessionCache.value.set(session.id.toString(), session as unknown as Session)
          })

          // 更新分页信息
          // API响应格式: {page: 1, page_count: 1, page_size: 20, total: 2}
          pagination.value = {
            page: response.page || response.data?.page || 1,
            per_page: response.page_size || response.data?.page_size || 20,
            total: response.total || response.data?.total || 0,
            has_more: (response.page || response.data?.page || 1) * (response.page_size || response.data?.page_size || 20) < (response.total || response.data?.total || 0)
          }

          console.log(`✅ 会话列表加载成功 [${requestId}]:`, {
            count: sessionList.length,
            page: pagination.value.page,
            total: pagination.value.total
          })

          return sessionList
        } else {
          console.warn(`⚠️ API响应格式异常 [${requestId}]:`, response)
          sessions.value = []
          return []
        }
      } catch (err) {
        console.error(`❌ 会话加载失败 [${requestId}]:`, err)
        error.value = err instanceof Error ? err.message : '加载会话失败'
        throw err
      } finally {
        isLoading.value = false
      }
    })()

    // 存储请求Promise和ID
    loadingRequests.value.set(requestKey, { promise: requestPromise, requestId })

    try {
      const result = await requestPromise
      return result
    } finally {
      // 清理已完成的请求
      const currentRequest = loadingRequests.value.get(requestKey)
      if (currentRequest && currentRequest.requestId === requestId) {
        loadingRequests.value.delete(requestKey)
      }
    }
  }

  const updateSession = async (sessionId: string, request: UpdateSessionRequest) => {
    try {
      const session = sessionCache.value.get(sessionId)
      if (session) {
        Object.assign(session, request)
        session.updated_at = new Date().toISOString()
        sessionCache.value.set(sessionId, session)
      }
      return session
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新会话失败'
      throw err
    }
  }

  const deleteSession = async (sessionId: string) => {
    try {
      sessions.value = sessions.value.filter(s => s.id.toString() !== sessionId)
      sessionCache.value.delete(sessionId)
      if (currentSessionId.value === sessionId) {
        currentSessionId.value = null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除会话失败'
      throw err
    }
  }

  const searchSessions = async (query: string) => {
    try {
      searchLoading.value = true
      searchQuery.value = query
      
      if (!query.trim()) {
        searchResults.value = []
        return
      }

      // 简化的搜索逻辑
      searchResults.value = sessions.value.filter(session =>
        session.title?.toLowerCase().includes(query.toLowerCase())
      )
    } catch (err) {
      error.value = err instanceof Error ? err.message : '搜索失败'
      throw err
    } finally {
      searchLoading.value = false
    }
  }

  const clearSearchResults = () => {
    searchResults.value = []
    searchQuery.value = ''
  }

  const setFilters = (newFilters: any) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const loadMoreSessions = async () => {
    if (!hasMoreSessions.value) return
    // 简化实现
    console.log('加载更多会话')
  }

  const archiveSession = async (sessionId: string) => {
    return updateSession(sessionId, {
      session_id: Number(sessionId),
      status: SessionStatus.ARCHIVED
    })
  }

  const joinSession = async (sessionId: string) => {
    console.log('加入会话:', sessionId)
  }

  const leaveSession = async (sessionId: string) => {
    console.log('离开会话:', sessionId)
  }

  const loadSessionParticipants = async (sessionId: string) => {
    participants.value.set(sessionId, [])
  }

  const loadSessionStats = async (sessionId: string) => {
    console.log('加载会话统计:', sessionId)
  }

  // 取消指定类型的会话加载请求
  const cancelLoadSessions = (requestKey: string) => {
    const request = loadingRequests.value.get(requestKey)
    if (request) {
      loadingRequests.value.delete(requestKey)
      console.log(`🚫 已取消会话加载请求: ${requestKey}`)
      return true
    }
    return false
  }

  // 取消所有正在进行的会话加载请求
  const cancelAllLoadSessions = () => {
    const canceledCount = loadingRequests.value.size
    if (canceledCount > 0) {
      loadingRequests.value.clear()
      console.log(`🚫 已取消所有会话加载请求，共 ${canceledCount} 个`)
    }
    return canceledCount
  }

  return {
    // State
    sessions,
    sessionCache,
    currentSessionId,
    isLoading,
    error,
    participants,
    statistics,
    selectedSessions,
    pagination,
    searchResults,
    searchQuery,
    searchLoading,
    filters,
    sortBy,
    sortOrder,
    
    // Getters
    currentSession,
    filteredSessions,
    hasMoreSessions,
    
    // Actions
    createSession,
    loadSessions,
    updateSession,
    deleteSession,
    searchSessions,
    clearSearchResults,
    setFilters,
    loadMoreSessions,
    archiveSession,
    joinSession,
    leaveSession,
    loadSessionParticipants,
    loadSessionStats,
    cancelLoadSessions,
    cancelAllLoadSessions
  }
})
