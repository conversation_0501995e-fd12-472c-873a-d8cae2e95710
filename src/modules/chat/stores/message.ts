/**
 * 消息状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  Message,
  // MessageType, // 未使用，注释掉
  // MessageStatus, // 未使用，注释掉
  // MessageSearchRequest, // 不存在，注释掉
  // MessageSearchResponse, // 不存在，注释掉
  // MessageStatistics, // 不存在，注释掉
  MessageDraft
} from '../types/message'
import { MessageService } from '../services/message'
import { post } from '../utils/request'
// import { Logger } from '../utils/logger' // 未使用，注释掉

// 简化的统计类型定义
interface MessageStatistics {
  total_messages: number
  unread_messages: number
  sent_messages: number
  received_messages: number
}

/**
 * 消息状态接口
 */
export interface MessageState {
  // 消息数据 - 多维结构：分类 -> 会话 -> 消息列表
  messagesByCategory: Map<string, Map<string, Message[]>> // category -> sessionId -> messages
  messageCache: Map<string, Message> // messageId -> message
  drafts: Map<string, MessageDraft> // sessionId -> draft
  
  // 分页信息 - 多维结构：分类 -> 会话 -> 分页信息
  paginationByCategory: Map<string, Map<string, {
    page: number
    per_page: number
    total: number
    has_more: boolean
  }>> // category -> sessionId -> pagination
  
  // 搜索相关
  searchResults: Message[]
  searchQuery: string
  searchLoading: boolean
  
  // 统计信息
  statistics: MessageStatistics | null
  
  // 输入状态
  typingUsers: Map<string, Set<string>> // sessionId -> Set<userId>
  typingTimeouts: Map<string, Map<string, number>> // sessionId -> userId -> timeoutId
  
  // 消息状态
  sendingMessages: Set<string> // 正在发送的消息ID
  failedMessages: Set<string> // 发送失败的消息ID
  
  // 未读消息
  unreadCounts: Map<string, number> // sessionId -> count
  lastReadTimes: Map<string, number> // sessionId -> timestamp
  
  // UI状态
  isLoading: boolean
  error: string | null
  
  // 消息操作
  selectedMessages: Set<string> // 选中的消息ID
  replyingTo: Message | null // 正在回复的消息
  editingMessage: Message | null // 正在编辑的消息
}

/**
 * 消息状态管理
 */
// 简化的消息 Store，使用 Composition API 风格
export const useMessageStore = defineStore('message', () => {
  // 多维消息存储：分类 -> 会话 -> 消息列表
  const messagesByCategory = ref(new Map<string, Map<string, any[]>>())
  const messageCache = ref(new Map<string, any>())
  const drafts = ref(new Map<string, any>())
  const searchResults = ref<any[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastReadTimes = ref(new Map<string, string>())
  const statistics = ref<MessageStatistics | null>(null)
  const editingMessage = ref<any | null>(null)
  const typingUsers = ref(new Map<string, any[]>()) // sessionId -> typing users
  const unreadCounts = ref(new Map<string, number>()) // sessionId -> unread count
  
  // 当前活跃的消息分类
  const currentCategory = ref<string>('CHAT')
  
  // 兼容性：获取当前分类的消息列表
  const messages = computed(() => {
    const categoryMessages = messagesByCategory.value.get(currentCategory.value)
    return categoryMessages || new Map<string, any[]>()
  })

  // 简化的方法
  const sendMessage = async (sessionId: string, content: string, type: any = 'TEXT') => {
    const messageService = new MessageService()
    const tempId = Date.now().toString()

    const tempMessage: any = {
      id: tempId,
      session_id: Number(sessionId),
      sender_id: 1,
      sender_type: 'USER' as any,
      receiver_id: 1,
      receiver_type: 'USER' as any,
      type,
      content,
      status: 'SENDING' as any,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_deleted: false
    }

    addMessage(tempMessage, currentCategory.value)

    try {
      const sentMessage = await messageService.sendMessage({
        session_id: Number(sessionId),
        type: type,
        content,
        category: 'CHAT' as any
      } as any)

      removeMessage(tempId, currentCategory.value)
      addMessage(sentMessage as any, currentCategory.value)
      clearDraft(sessionId)
    } catch (error) {
      tempMessage.status = 'FAILED' as any
      updateMessage(tempMessage, currentCategory.value)
      throw error
    }
  }

  // 新的发送消息方法 - 参考文档中的简化方式
  const sendMessageAdvanced = async (messageData: any) => {
    console.log('📤 开始发送消息:', messageData)

    if (!messageData || !messageData.session_id) {
      console.error('❌ 消息数据无效:', messageData)
      throw new Error('消息数据无效')
    }

    const sessionId = messageData.session_id.toString()
    const content = messageData.content?.trim()

    if (!content) {
      console.error('❌ 消息内容为空')
      throw new Error('消息内容不能为空')
    }

    // 注意：token认证由request.ts自动处理，无需手动获取

    const tempId = Date.now().toString()

    // 构建临时消息对象
    const tempMessage: any = {
      id: tempId,
      session_id: Number(sessionId),
      sender_id: 1, // 当前用户ID，应该从用户状态获取
      sender_type: 'USER' as any,
      receiver_id: 1,
      receiver_type: 'USER' as any,
      type: messageData.message_type || 'text',
      content: content,
      status: 'SENDING' as any,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_deleted: false
    }

    // 处理回复消息
    if (messageData.reply_to_id) {
      tempMessage.reply_to_id = messageData.reply_to_id
    }

    // 处理文件消息
    if (messageData.files && messageData.files.length > 0) {
      tempMessage.type = 'FILE'
      tempMessage.files = messageData.files
    }

    console.log('📝 创建临时消息:', tempMessage)

    // 添加临时消息到界面（使用当前分类）
    addMessage(tempMessage, currentCategory.value)

    try {
      // 使用统一的request工具发送消息
      console.log('🚀 发送消息到服务器...')

      // 使用项目统一的post方法，它会自动处理token和响应格式
      const result = await post(`/sessions/${sessionId}/messages/text`, { content })

      console.log('📨 服务器响应:', result)

      // 根据响应格式提取消息数据
      let sentMessage = result

      // 如果响应包含message字段，提取实际消息数据
      if (result && (result as any).message && typeof (result as any).message === 'object') {
        sentMessage = (result as any).message
      } else if (result && (result as any).data) {
        sentMessage = (result as any).data
      }

      console.log('✅ 服务器返回消息:', sentMessage)

      // 验证返回的消息数据
      if (!sentMessage || !(sentMessage as any).id) {
        console.error('❌ 服务器返回的消息格式无效:', sentMessage)
        throw new Error('服务器返回的消息格式无效')
      }

      console.log('📝 提取的实际消息数据:', sentMessage)

      // 移除临时消息，添加服务器返回的消息
      removeMessage(tempId, currentCategory.value)

      // 立即在UI中展示已发送的消息（参考文档建议）
      addMessage(sentMessage, currentCategory.value)

      console.log('✅ 消息发送成功并已添加到UI:', (sentMessage as any).id)

      // 清空草稿
      clearDraft(sessionId)

      return sentMessage
    } catch (error) {
      // 标记消息发送失败
      console.error('❌ 消息发送失败:', error)
      tempMessage.status = 'FAILED' as any
      updateMessage(tempMessage, currentCategory.value)
      throw error
    }
  }

  const addMessage = (message: any, category?: string) => {
    // 验证消息数据
    if (!message) {
      console.error('❌ addMessage: 消息数据为空')
      return
    }

    if (!message.id) {
      console.error('❌ addMessage: 消息ID为空', message)
      return
    }

    if (!message.session_id) {
      console.error('❌ addMessage: 会话ID为空', message)
      return
    }

    try {
      const messageId = message.id.toString()
      const sessionId = message.session_id.toString()
      const targetCategory = category || currentCategory.value
      const currentCat = currentCategory.value

      console.log('📝 添加消息到store:', {
        messageId,
        sessionId,
        category: targetCategory,
        currentCategory: currentCat,
        content: message.content?.substring(0, 50) + '...',
        sender_type: message.sender_type
      })

      // 🔧 验证分类有效性：只有当前分类的消息才会被添加
      if (targetCategory !== currentCat) {
        console.log(`🚫 拒绝添加非当前分类的消息 (目标分类: ${targetCategory}, 当前分类: ${currentCat})`)
        return
      }

      // 添加到消息缓存
      messageCache.value.set(messageId, message)

      // 确保分类存在
      if (!messagesByCategory.value.has(targetCategory)) {
        messagesByCategory.value.set(targetCategory, new Map<string, any[]>())
      }

      const categoryMessages = messagesByCategory.value.get(targetCategory)!
      const sessionMessages = categoryMessages.get(sessionId) || []

      // 🔧 检查消息是否已存在，避免重复添加
      const existingIndex = sessionMessages.findIndex(m => m.id.toString() === messageId)
      if (existingIndex !== -1) {
        console.log(`ℹ️ 消息 ${messageId} 已存在，跳过添加`)
        return
      }

      sessionMessages.push(message)

      // 按时间排序
      if (sessionMessages.length > 1) {
        sessionMessages.sort((a, b) => {
          const timeA = new Date(a.created_at || 0).getTime()
          const timeB = new Date(b.created_at || 0).getTime()
          return timeA - timeB
        })
      }

      categoryMessages.set(sessionId, sessionMessages)

      console.log('✅ 消息添加成功，当前分类会话消息数:', sessionMessages.length)
    } catch (error) {
      console.error('❌ addMessage 失败:', error, message)
    }
  }

  const removeMessage = (messageId: string, category?: string) => {
    messageCache.value.delete(messageId)

    if (category) {
      // 从指定分类中移除消息
      const categoryMessages = messagesByCategory.value.get(category)
      if (categoryMessages) {
        for (const [, sessionMessages] of categoryMessages) {
          const index = sessionMessages.findIndex((m: any) => m.id.toString() === messageId)
          if (index !== -1) {
            sessionMessages.splice(index, 1)
            break
          }
        }
      }
    } else {
      // 从所有分类和会话中移除该消息
      for (const [, categoryMessages] of messagesByCategory.value) {
        for (const [, sessionMessages] of categoryMessages) {
          const index = sessionMessages.findIndex((m: any) => m.id.toString() === messageId)
          if (index !== -1) {
            sessionMessages.splice(index, 1)
            return
          }
        }
      }
    }
  }

  const updateMessage = (message: any, category?: string) => {
    messageCache.value.set(message.id.toString(), message)

    const targetCategory = category || currentCategory.value
    const categoryMessages = messagesByCategory.value.get(targetCategory)
    if (categoryMessages) {
      const sessionMessages = categoryMessages.get(message.session_id.toString())
      if (sessionMessages) {
        const index = sessionMessages.findIndex((m: any) => m.id.toString() === message.id.toString())
        if (index !== -1) {
          sessionMessages[index] = message
        }
      }
    }
  }

  const clearDraft = (sessionId: string) => {
    drafts.value.delete(sessionId)
  }

  // 用于跟踪正在进行的请求 - 多维结构：分类-会话 -> 请求信息
  const loadingRequests = ref(new Map<string, { promise: Promise<any>, requestId: number }>())
  
  const loadMessages = async (sessionId: string, category?: string) => {
    const targetCategory = category || currentCategory.value
    const requestKey = `${targetCategory}-${sessionId}`
    const requestStartTime = Date.now()

    try {
      // 检查是否已有相同分类-会话的请求正在进行
      const existingRequest = loadingRequests.value.get(requestKey)
      if (existingRequest) {
        console.log(`⏳ 分类 ${targetCategory} 会话 ${sessionId} 的消息加载请求已在进行中，等待完成...`)
        return await existingRequest.promise
      }

      isLoading.value = true
      const messageService = new MessageService()
      const requestId = Date.now()

      // 创建请求Promise
      const requestPromise = (async () => {
        console.log(`🔄 开始加载分类 ${targetCategory} 会话 ${sessionId} 的消息... (请求ID: ${requestId})`)

        try {
          // 🔧 在API调用前再次验证分类是否仍然有效
          if (targetCategory !== currentCategory.value) {
            console.log(`🚫 API调用前检测到分类已切换 (${targetCategory} -> ${currentCategory.value})，取消请求 (请求ID: ${requestId})`)
            return null
          }

          // 使用正确的API调用方式
          const response = await messageService.getMessages(Number(sessionId), {
            page: 1,
            page_size: 10,
            order: 'desc'
          })

          // 🔧 增强的竞态条件检查
          const currentRequest = loadingRequests.value.get(requestKey)
          const currentCat = currentCategory.value
          const requestDuration = Date.now() - requestStartTime

          if (!currentRequest || currentRequest.requestId !== requestId) {
            console.log(`⚠️ 分类 ${targetCategory} 会话 ${sessionId} 的请求已被新请求替代，丢弃结果 (请求ID: ${requestId}, 耗时: ${requestDuration}ms)`)
            return null
          }

          if (targetCategory !== currentCat) {
            console.log(`🚫 API响应时检测到分类已切换 (${targetCategory} -> ${currentCat})，丢弃结果 (请求ID: ${requestId}, 耗时: ${requestDuration}ms)`)
            return null
          }

          // 修复：使用正确的响应结构 - API返回的是list字段，不是messages字段
          console.log(`🔍 API原始响应数据 (请求ID: ${requestId}):`, response)

          // 使用类型断言处理响应数据
          const responseAny = response as any
          const messageList = responseAny.list || responseAny.messages || response.data?.messages || []
          console.log(`📋 API返回消息数据 (请求ID: ${requestId}):`, {
            category: targetCategory,
            sessionId,
            total: responseAny.total || response.data?.total,
            page: responseAny.page || response.data?.page,
            messageCount: messageList.length,
            firstMessage: messageList[0],
            responseKeys: Object.keys(responseAny)
          })

          // 再次检查请求是否仍然有效
          const finalRequest = loadingRequests.value.get(requestKey)
          if (!finalRequest || finalRequest.requestId !== requestId) {
            console.log(`⚠️ 分类 ${targetCategory} 会话 ${sessionId} 的请求在处理过程中被替代，丢弃结果 (请求ID: ${requestId})`)
            return null
          }

          messageList.forEach((message: any) => {
            messageCache.value.set(message.id.toString(), message)
          })

          // 🔧 最终验证：存储前再次检查分类是否仍然有效
          const finalCategory = currentCategory.value
          if (targetCategory !== finalCategory) {
            console.log(`🚫 存储前检测到分类已切换 (${targetCategory} -> ${finalCategory})，丢弃结果 (请求ID: ${requestId})`)
            return null
          }

          // 确保分类存在
          if (!messagesByCategory.value.has(targetCategory)) {
            messagesByCategory.value.set(targetCategory, new Map<string, any[]>())
          }

          const categoryMessages = messagesByCategory.value.get(targetCategory)!

          // 对消息按时间排序（升序，最新消息在底部）
          const sortedMessages = messageList.sort((a: any, b: any) => {
            const timeA = new Date(a.created_at || 0).getTime()
            const timeB = new Date(b.created_at || 0).getTime()
            return timeA - timeB
          })

          categoryMessages.set(sessionId, sortedMessages)
          console.log(`✅ 已存储分类 ${targetCategory} 会话 ${sessionId} 的 ${messageList.length} 条消息到store (请求ID: ${requestId}, 耗时: ${Date.now() - requestStartTime}ms)`)
          console.log(`📊 消息时间排序: 最早 ${sortedMessages[0]?.created_at} -> 最新 ${sortedMessages[sortedMessages.length - 1]?.created_at}`)

          // 🔧 初始化分页信息
          const paginationKey = `${targetCategory}-${sessionId}`
          const total = responseAny.total || response.data?.total || 0
          const page = responseAny.page || response.data?.page || 1
          const pageCount = (responseAny as any).page_count || (response.data as any)?.page_count || 1
          const pageSize = responseAny.page_size || response.data?.page_size || 10

          // 计算是否还有更多消息
          const hasMore = page < pageCount || (total > messageList.length)

          console.log(`📄 [loadMessages] 分页信息:`, {
            total,
            page,
            pageCount,
            pageSize,
            currentMessages: messageList.length,
            hasMore,
            paginationKey
          })

          // 初始化或更新分页信息
          paginationInfo.value.set(paginationKey, {
            currentPage: page,
            hasMore: hasMore,
            isLoadingMore: false
          })

          return {
            messages: messageList,
            total,
            has_more: hasMore,
            page,
            page_count: pageCount
          }
        } catch (err) {
          console.error(`❌ 加载分类 ${targetCategory} 会话 ${sessionId} 消息失败 (请求ID: ${requestId}):`, err)
          error.value = err instanceof Error ? err.message : String(err)
          throw err
        }
      })()
      
      // 存储请求Promise和ID
      loadingRequests.value.set(requestKey, { promise: requestPromise, requestId })
      
      try {
        const result = await requestPromise
        return result
      } finally {
        // 清理完成的请求（只清理当前请求）
        const currentRequest = loadingRequests.value.get(requestKey)
        if (currentRequest && currentRequest.requestId === requestId) {
          loadingRequests.value.delete(requestKey)
        }
      }
    } catch (err) {
      console.error('加载消息失败:', err)
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 取消指定分类-会话的加载请求
  const cancelLoadMessages = (sessionId: string, category?: string) => {
    const targetCategory = category || currentCategory.value
    const requestKey = `${targetCategory}-${sessionId}`
    const request = loadingRequests.value.get(requestKey)
    if (request) {
      console.log(`🚫 取消分类 ${targetCategory} 会话 ${sessionId} 的消息加载请求 (请求ID: ${request.requestId})`)
      loadingRequests.value.delete(requestKey)
      return true
    }
    return false
  }

  // 分页信息跟踪
  const paginationInfo = ref(new Map<string, { currentPage: number, hasMore: boolean, isLoadingMore: boolean }>())

  // 加载更多历史消息
  const loadMoreMessages = async (sessionId: string, category?: string) => {
    const targetCategory = category || currentCategory.value
    const paginationKey = `${targetCategory}-${sessionId}`

    // 获取或初始化分页信息
    let pageInfo = paginationInfo.value.get(paginationKey)
    if (!pageInfo) {
      pageInfo = { currentPage: 1, hasMore: true, isLoadingMore: false }
      paginationInfo.value.set(paginationKey, pageInfo)
    }

    // 如果没有更多消息或正在加载，直接返回
    if (!pageInfo.hasMore || pageInfo.isLoadingMore) {
      console.log(`📄 会话 ${sessionId} 没有更多历史消息或正在加载中`)
      return { messages: [], hasMore: false }
    }

    try {
      pageInfo.isLoadingMore = true
      const messageService = new MessageService()
      const nextPage = pageInfo.currentPage + 1

      console.log(`📄 加载会话 ${sessionId} 第 ${nextPage} 页历史消息...`)

      const response = await messageService.getMessages(Number(sessionId), {
        page: nextPage,
        page_size: 10,
        order: 'desc'
      })

      // 🔧 修复：使用正确的响应结构 - API返回的是list字段，不是messages字段
      console.log(`🔍 [loadMoreMessages] API原始响应:`, response)

      const responseAny = response as any
      const messageList = responseAny.list || responseAny.messages || (response.data as any)?.messages || (response.data as any)?.list || []
      const total = responseAny.total || response.data?.total || 0
      const page = responseAny.page || response.data?.page || nextPage
      const pageCount = (responseAny as any).page_count || (response.data as any)?.page_count || 1

      console.log(`📋 [loadMoreMessages] 解析后的数据:`, {
        messageCount: messageList.length,
        total,
        page,
        pageCount,
        hasMore: page < pageCount
      })

      if (messageList && messageList.length > 0) {
        // 更新分页信息
        pageInfo.currentPage = page
        pageInfo.hasMore = page < pageCount // 根据页数判断是否还有更多

        // 将新消息添加到现有消息列表的开头（因为是历史消息）
        const categoryMessages = messagesByCategory.value.get(targetCategory)
        if (categoryMessages) {
          const sessionMessages = categoryMessages.get(sessionId) || []

          // 过滤掉已存在的消息，避免重复
          const existingIds = new Set(sessionMessages.map(m => m.id?.toString()))
          const newMessages = messageList.filter((m: any) => !existingIds.has(m.id?.toString()))

          if (newMessages.length > 0) {
            // 将新的历史消息插入到列表开头
            sessionMessages.unshift(...newMessages)

            // 重新排序确保时间顺序正确
            sessionMessages.sort((a, b) => {
              const timeA = new Date(a.created_at || 0).getTime()
              const timeB = new Date(b.created_at || 0).getTime()
              return timeA - timeB
            })

            categoryMessages.set(sessionId, sessionMessages)

            console.log(`✅ 加载了 ${newMessages.length} 条历史消息，总消息数: ${sessionMessages.length}`)
          }
        }

        return { messages: messageList, hasMore: pageInfo.hasMore }
      } else {
        // 没有更多消息
        pageInfo.hasMore = false
        console.log(`📄 会话 ${sessionId} 没有更多历史消息`)
        return { messages: [], hasMore: false }
      }
    } catch (error) {
      console.error(`❌ 加载更多历史消息失败:`, error)
      return { messages: [], hasMore: pageInfo.hasMore }
    } finally {
      pageInfo.isLoadingMore = false
    }
  }

  // 取消指定分类的所有加载请求
  const cancelCategoryLoadMessages = (category: string) => {
    let canceledCount = 0
    const keysToDelete: string[] = []
    
    for (const [requestKey, request] of loadingRequests.value) {
      if (requestKey.startsWith(`${category}-`)) {
        console.log(`🚫 取消分类 ${category} 的消息加载请求: ${requestKey} (请求ID: ${request.requestId})`)
        keysToDelete.push(requestKey)
        canceledCount++
      }
    }
    
    keysToDelete.forEach(key => loadingRequests.value.delete(key))
    return canceledCount
  }

  // 取消所有正在进行的加载请求
  const cancelAllLoadMessages = () => {
    const canceledCount = loadingRequests.value.size
    if (canceledCount > 0) {
      console.log(`🚫 取消所有正在进行的消息加载请求，共 ${canceledCount} 个`)
      loadingRequests.value.clear()
    }
    return canceledCount
  }

  // 设置当前活跃的消息分类
  const setCurrentCategory = (category: string) => {
    console.log(`🔄 切换消息分类: ${currentCategory.value} -> ${category}`)
    currentCategory.value = category
  }

  // 获取指定分类的消息列表
  const getCategoryMessages = (category: string) => {
    return messagesByCategory.value.get(category) || new Map<string, any[]>()
  }

  // 清空指定分类的所有消息
  const clearCategoryMessages = (category: string) => {
    const categoryMessages = messagesByCategory.value.get(category)
    if (categoryMessages) {
      categoryMessages.clear()
      console.log(`🗑️ 已清空分类 ${category} 的所有消息`)
    }
  }

  return {
    // 状态
    messages, // 兼容性：当前分类的消息列表
    messagesByCategory, // 多维消息存储
    messageCache,
    drafts,
    searchResults,
    isLoading,
    error,
    lastReadTimes,
    statistics,
    editingMessage,
    typingUsers,
    unreadCounts,
    currentCategory,
    
    // 消息操作方法
    sendMessage: sendMessageAdvanced, // 使用新的发送方法
    sendMessageSimple: sendMessage, // 保留原有的简单发送方法
    addMessage,
    removeMessage,
    updateMessage,
    clearDraft,
    
    // 消息加载方法
    loadMessages,
    loadMoreMessages,

    // 分页信息
    paginationInfo: computed(() => paginationInfo.value),

    // 请求取消方法
    cancelLoadMessages,
    cancelCategoryLoadMessages,
    cancelAllLoadMessages,
    
    // 分类管理方法
    setCurrentCategory,
    getCategoryMessages,
    clearCategoryMessages
  }
})

export default useMessageStore
