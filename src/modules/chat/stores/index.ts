/**
 * 聊天模块状态管理统一导出
 */

// 导出所有状态管理
export { useChatStore } from './chat'
export { useMessageStore } from './message'
export { useSessionStore } from './session'
export { useNotificationStore } from './notification'
export { useFileStore } from './file'

// 导出类型定义（简化版本，因为使用了 Composition API）
// export type { ChatState } from './chat'
// export type { MessageState } from './message'
// export type { SessionState } from './session'
// export type { NotificationState } from './notification'
// export type { FileState } from './file'

// 导入所有 store
import { useChatStore } from './chat'
import { useMessageStore } from './message'
import { useSessionStore } from './session'
import { useNotificationStore } from './notification'
import { useFileStore } from './file'

// 默认导出
export default {
  useChatStore,
  useMessageStore,
  useSessionStore,
  useNotificationStore,
  useFileStore
}