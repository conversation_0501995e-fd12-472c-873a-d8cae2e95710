/**
 * 聊天状态管理
 */

import { defineStore } from 'pinia'
import { EVENT_NAMES } from '../constants'
import type {
  ChatClientStatus,
  ChatUser,
  ChatRoom,
  ChatSettings,
  QuickReply,
  MessageTemplate,
  ChatStatistics
} from '../types/chat'
import { UserOnlineStatus } from '../types/chat'
import type {
  Message,
  MessageType,
  MessageStatus
} from '../types/message'
import type {
  Session,
  SessionType
  // SessionStatus // 未使用，注释掉
} from '../types/session'
import type {
  Notification,
  NotificationSettings
} from '../types/notification'
import { ChatClient } from '../services/chat-client'
import { getCurrentDeviceId } from '@/utils/deviceInfo'
import type { MessageRouter } from '../handlers/base/MessageRouter'
// import { Logger } from '../utils/logger' // 暂时注释掉未使用的导入

/**
 * 聊天状态接口
 */
export interface ChatState {
  // 客户端状态
  clientStatus: ChatClientStatus
  isConnected: boolean
  isInitializing: boolean // 连接初始化状态锁
  isDisconnecting: boolean // 断开连接状态锁
  reconnectAttempts: number
  lastConnectedAt: number | null

  // 聊天UI状态
  isChatUIVisible: boolean
  currentVisibleSessionId: string | null

  // 当前用户
  currentUser: ChatUser | null
  
  // 消息相关
  messages: Map<string, Message[]> // sessionId -> messages
  messageCache: Map<string, Message> // messageId -> message
  unreadCounts: Map<string, number> // sessionId -> unreadCount
  typingUsers: Map<string, Set<string>> // sessionId -> Set<userId>
  
  // 会话相关
  sessions: Session[]
  currentSessionId: string | null
  sessionCache: Map<string, Session> // sessionId -> session
  
  // 通知相关
  notifications: Notification[]
  notificationSettings: NotificationSettings | null
  unreadNotificationCount: number
  
  // 聊天室相关
  chatRooms: ChatRoom[]
  currentRoomId: string | null
  
  // 用户相关
  onlineUsers: Map<string, UserOnlineStatus> // userId -> status
  userProfiles: Map<string, ChatUser> // userId -> profile
  
  // 设置相关
  chatSettings: ChatSettings | null
  quickReplies: QuickReply[]
  messageTemplates: MessageTemplate[]
  
  // 统计相关
  statistics: ChatStatistics | null
  
  // UI状态
  isLoading: boolean
  error: string | null
  
  // 文件上传
  uploadProgress: Map<string, number> // uploadId -> progress

  // 消息路由器
  messageRouter: MessageRouter | null

  // ChatClient实例
  chatClient: ChatClient | null
}

/**
 * 聊天状态管理
 */
export const useChatStore = defineStore('chat', {
  state: (): ChatState => ({
    // 客户端状态
    clientStatus: 'DISCONNECTED' as any,
    isConnected: false,
    isInitializing: false,
    isDisconnecting: false,
    reconnectAttempts: 0,
    lastConnectedAt: null,

    // 聊天UI状态
    isChatUIVisible: false,
    currentVisibleSessionId: null,

    // 当前用户
    currentUser: null,
    
    // 消息相关
    messages: new Map(),
    messageCache: new Map(),
    unreadCounts: new Map(),
    typingUsers: new Map(),
    
    // 会话相关
    sessions: [],
    currentSessionId: null,
    sessionCache: new Map(),
    
    // 通知相关
    notifications: [],
    notificationSettings: null,
    unreadNotificationCount: 0,
    
    // 聊天室相关
    chatRooms: [],
    currentRoomId: null,
    
    // 用户相关
    onlineUsers: new Map(),
    userProfiles: new Map(),
    
    // 设置相关
    chatSettings: null,
    quickReplies: [],
    messageTemplates: [],
    
    // 统计相关
    statistics: null,
    
    // UI状态
    isLoading: false,
    error: null,
    
    // 文件上传
    uploadProgress: new Map(),

    // 消息路由器
    messageRouter: null,

    // ChatClient实例
    chatClient: null
  }),

  getters: {
    /**
     * 获取当前会话
     */
    currentSession: (state): Session | null => {
      if (!state.currentSessionId) return null
      return state.sessionCache.get(state.currentSessionId) || null
    },

    /**
     * 获取当前会话的消息
     */
    currentMessages: (state): Message[] => {
      if (!state.currentSessionId) return []
      return state.messages.get(state.currentSessionId) || []
    },

    /**
     * 获取当前会话的未读数量
     */
    currentUnreadCount: (state): number => {
      if (!state.currentSessionId) return 0
      return state.unreadCounts.get(state.currentSessionId) || 0
    },

    /**
     * 获取总未读数量
     */
    totalUnreadCount: (state): number => {
      let total = 0
      for (const count of state.unreadCounts.values()) {
        total += count
      }
      return total
    },

    /**
     * 获取当前会话的正在输入用户
     */
    currentTypingUsers: (state): string[] => {
      if (!state.currentSessionId) return []
      const typingSet = state.typingUsers.get(state.currentSessionId)
      return typingSet ? Array.from(typingSet) : []
    },

    /**
     * 获取在线用户数量
     */
    onlineUserCount: (state): number => {
      let count = 0
      for (const status of state.onlineUsers.values()) {
        if (status === 'online') count++
      }
      return count
    },

    /**
     * 检查用户是否在线
     */
    isUserOnline: (state) => (userId: string): boolean => {
      return state.onlineUsers.get(userId) === 'online'
    },

    /**
     * 获取会话按类型分组
     */
    sessionsByType: (state) => {
      const grouped: Record<SessionType, Session[]> = {
        presale_consultation: [],
        after_sale_service: [],
        friend_chat: [],
        customer_service: [],
        system_notification: [],
        group_chat: []
      }
      
      state.sessions.forEach(session => {
        grouped[session.type].push(session)
      })
      
      return grouped
    },

    /**
     * 获取活跃会话（有未读消息或最近活动）
     */
    activeSessions: (state): Session[] => {
      const now = Date.now()
      const activeThreshold = 24 * 60 * 60 * 1000 // 24小时
      
      return state.sessions.filter(session => {
        const hasUnread = (state.unreadCounts.get(session.id.toString()) || 0) > 0
        const isRecent = (now - new Date(session.updated_at).getTime()) < activeThreshold
        return hasUnread || isRecent
      })
    }
  },

  actions: {
    // 辅助方法：获取 ChatClient 实例
    async getChatClient(userType: string = 'merchant') {
      // 根据用户类型获取对应的token
      let token: string = ''
      let userId: number = 0
      
      if (userType === 'user') {
        const { useUserStore } = await import('@/modules/user/stores/userStore')
        const userStore = useUserStore()
        token = userStore.token || ''
        userId = Number(userStore.userInfo?.id) || 0
      } else if (userType === 'admin') {
        // 直接从localforage获取管理员token
        const adminToken = await import('localforage').then(lf => lf.default.getItem('admin_access_token'))
        token = adminToken ? String(adminToken) : ''

        // 获取管理员信息
        const { useAdminStore } = await import('@/modules/admin/stores/adminStore')
        const adminStore = useAdminStore()
        userId = Number(adminStore.currentAdmin?.id) || 0
      } else {
        const { useMerchantStore } = await import('@/modules/merchant/stores/merchantStore')
        const merchantStore = useMerchantStore()
        token = merchantStore.token || ''
        userId = Number(merchantStore.merchantInfo?.id) || 0
      }
      
      // 构建WebSocket URL，根据API文档添加token和device_id参数
      const wsBaseUrl = (import.meta.env?.VITE_WS_URL || 'ws://localhost:8181') + '/api/v1/chat/ws'
      const deviceId = getCurrentDeviceId()
      const wsUrlWithToken = token ? `${wsBaseUrl}?token=${encodeURIComponent(token)}&device_id=${encodeURIComponent(deviceId)}` : wsBaseUrl
      
      return new ChatClient({
        apiBaseUrl: '/api/v1/chat',
        wsBaseUrl: wsUrlWithToken,
        userToken: token,
        userId: userId,
        userType: userType as any,
        autoReconnect: true,
        maxReconnectAttempts: 5,
        reconnectInterval: 1000,
        heartbeatInterval: 30000,
        messageQueueSize: 100,
        enableNotifications: true,
        enableSound: true,
        debug: false
      })
    },
    /**
     * 初始化聊天客户端
     */
    async initializeChat(config: any) {
      console.log('🔧 [ChatStore] initializeChat 方法开始执行');

      // 连接状态锁检查 - 防止重复初始化
      if (this.isInitializing) {
        console.warn('⚠️ [ChatStore] 聊天服务正在初始化中，跳过重复调用');
        return;
      }

      if (this.isConnected && this.chatClient) {
        console.warn('⚠️ [ChatStore] 聊天服务已连接，跳过重复初始化');
        return;
      }

      try {
        // 设置初始化状态锁
        this.isInitializing = true;
        this.isLoading = true
        this.error = null
        console.log('🔧 [ChatStore] 设置初始化锁和loading状态，清除错误');
        
        // 根据用户类型获取对应的token
        let token: string = ''
        let userId: number = 0
        let userType: string = config.userType || 'merchant'
        
        if (userType === 'user') {
           console.log('🔧 [ChatStore] 开始获取用户token');
           const { useUserStore } = await import('@/modules/user/stores/userStore')
           const userStore = useUserStore()
           token = userStore.token || ''
           userId = Number(userStore.userInfo?.id) || 0
           console.log('🔧 [ChatStore] 用户token获取结果:', { hasToken: !!token, tokenLength: token?.length, userId });

           if (!token) {
             throw new Error('用户未登录，无法初始化聊天服务')
           }
         } else if (userType === 'admin') {
           console.log('🔧 [ChatStore] 开始获取管理员token');
           // 直接从localforage获取管理员token
           const adminToken = await import('localforage').then(lf => lf.default.getItem('admin_access_token'))
           token = adminToken ? String(adminToken) : ''

           // 获取管理员信息
           const { useAdminStore } = await import('@/modules/admin/stores/adminStore')
           const adminStore = useAdminStore()
           userId = Number(adminStore.currentAdmin?.id) || 0
           console.log('🔧 [ChatStore] 管理员token获取结果:', { hasToken: !!token, tokenLength: token?.length, userId });

           if (!token) {
             throw new Error('管理员未登录，无法初始化聊天服务')
           }
         } else {
           console.log('🔧 [ChatStore] 开始获取商家token');
           const merchantStore = (await import('@/modules/merchant/stores/merchantStore')).useMerchantStore()
           token = merchantStore.token || ''
           userId = Number(merchantStore.merchantInfo?.id) || 0
           console.log('🔧 [ChatStore] 商家token获取结果:', { hasToken: !!token, tokenLength: token?.length, userId });

           if (!token) {
             throw new Error('商家未登录，无法初始化聊天服务')
           }
         }
        
        // 构建WebSocket URL，根据API文档添加token和device_id参数
        console.log('🔧 [ChatStore] 开始构建WebSocket URL');
        const wsBaseUrl = (import.meta.env?.VITE_WS_URL || 'ws://localhost:8181') + '/api/v1/chat/ws'
        const deviceId = getCurrentDeviceId()
        const wsUrlWithToken = `${wsBaseUrl}?token=${encodeURIComponent(token)}&device_id=${encodeURIComponent(deviceId)}`
        console.log('🔧 [ChatStore] WebSocket URL构建完成:', wsUrlWithToken.substring(0, 100) + '...');
        console.log('🔧 [ChatStore] 设备ID:', deviceId);
        
        console.log('🔧 [ChatStore] 开始创建ChatClient实例');
        const chatClient = new ChatClient({
          ...config,
          wsBaseUrl: wsUrlWithToken,
          userToken: token,
          userId: userId,
          userType: userType as any
        })
        console.log('🔧 [ChatStore] ChatClient实例创建完成');
        
        console.log('🔧 [ChatStore] 开始注册事件监听器');
        // 监听连接状态变化
        chatClient.on('statusChange', (status: ChatClientStatus) => {
          console.log('🔧 [ChatStore] 连接状态变化:', status);
          this.clientStatus = status
          this.isConnected = status === 'connected'
          
          if (status === 'connected') {
            this.lastConnectedAt = Date.now()
            this.reconnectAttempts = 0
            console.log('🔧 [ChatStore] 连接成功，重置重连计数');
          } else if (status === 'reconnecting') {
            this.reconnectAttempts++
            console.log('🔧 [ChatStore] 重连中，尝试次数:', this.reconnectAttempts);
          }
        })
        
        // 监听消息事件 - 使用正确的事件名称
        chatClient.on(EVENT_NAMES.MESSAGE_RECEIVED, (message: Message) => {
          console.log('🔧 [ChatStore] 收到消息:', {
            id: message.id,
            content: message.content?.substring(0, 50) + '...',
            senderType: message.sender_type,
            sessionId: message.session_id,
            currentSessionId: this.currentSessionId
          });

          // 🔧 修复重复添加问题：消息已通过消息路由器处理，这里不再重复添加
          // 只记录日志，实际添加由CommonMessageHandler处理
          console.log('ℹ️ [ChatStore] 消息将通过消息路由器处理，避免重复添加');
        })

        chatClient.on('messageStatusUpdated', (messageId: string, status: MessageStatus) => {
          console.log('🔧 [ChatStore] 消息状态更新:', messageId, status);
          this.updateMessageStatus(messageId, status)
        })
        
        // 监听会话事件
        chatClient.on('sessionCreated', (session: Session) => {
          console.log('🔧 [ChatStore] 会话创建:', session.id);
          this.addSession(session)
        })
        
        chatClient.on('sessionUpdated', (session: Session) => {
          console.log('🔧 [ChatStore] 会话更新:', session.id);
          this.updateSession(session)
        })
        
        // 监听用户状态事件
        chatClient.on('userStatusChanged', (userId: string, status: UserOnlineStatus) => {
          console.log('🔧 [ChatStore] 用户状态变化:', userId, status);
          this.updateUserStatus(userId, status)
        })
        
        // 监听输入状态事件
        chatClient.on('typingStatusChanged', (sessionId: string, userId: string, isTyping: boolean) => {
          console.log('🔧 [ChatStore] 输入状态变化:', sessionId, userId, isTyping);
          this.updateTypingStatus(sessionId, userId, isTyping)
        })
        
        // 监听通知事件
        chatClient.on('notificationReceived', (notification: Notification) => {
          console.log('🔧 [ChatStore] 收到通知:', notification.id);
          this.addNotification(notification)
        })

        // 监听会话成员在线状态事件
        chatClient.on('sessionMembersOnline', (data: any) => {
          console.log('🔧 [ChatStore] 收到会话成员在线状态:', data);
          this.updateSessionMembersOnlineStatus(data)
        })

        // 监听用户上线/下线事件（用于显示ElNotification）
        chatClient.on('user:online', (data: any) => {
          console.log('🔧 [ChatStore] 收到用户上线事件:', data);
          this.handleUserOnlineNotification(data)
        })

        chatClient.on('user:offline', (data: any) => {
          console.log('🔧 [ChatStore] 收到用户下线事件:', data);
          this.handleUserOfflineNotification(data)
        })

        // 监听未读数量更新事件
        chatClient.on('unreadCountUpdated', (unreadCount: any) => {
          console.log('🔧 [ChatStore] 收到未读数量更新:', unreadCount);
          if (unreadCount && typeof unreadCount.total === 'number') {
            this.setTotalUnreadCount(unreadCount.total)
          }
        })
        
        console.log('🔧 [ChatStore] 事件监听器注册完成，开始连接聊天服务');
        // 连接聊天服务
        await chatClient.connect()
        console.log('🔧 [ChatStore] chatClient.connect() 调用完成');

        // 保存ChatClient实例到store
        this.chatClient = chatClient
        console.log('🔧 [ChatStore] ChatClient实例已保存到store');

        // 如果已经设置了消息路由器，立即集成
        if (this.messageRouter) {
          this.integrateMessageRouter()
        }

        console.log('🔧 [ChatStore] initializeChat 方法即将正常结束');
        console.log('🔧 [ChatStore] initializeChat 方法成功完成，准备返回');
      } catch (error) {
        console.error('🔧 [ChatStore] initializeChat 方法捕获到错误:', error);
        this.error = error instanceof Error ? error.message : '初始化聊天失败'
        throw error
      } finally {
        console.log('🔧 [ChatStore] initializeChat 方法进入finally块，释放初始化锁');
        this.isInitializing = false;
        this.isLoading = false
        console.log('🔧 [ChatStore] initializeChat 方法完全结束');
      }
    },

    /**
     * 断开聊天连接
     */
    async disconnectChat() {
      // 断开连接状态锁检查 - 防止重复断开
      if (this.isDisconnecting) {
        console.warn('⚠️ [ChatStore] 聊天服务正在断开中，跳过重复调用');
        return;
      }

      try {
        // 设置断开连接状态锁
        this.isDisconnecting = true;
        console.log('🔧 [ChatStore] 开始断开聊天连接');

        // 如果有ChatClient实例，先断开连接
        if (this.chatClient) {
          try {
            // 停止ChatClient的所有定时器和重连机制
            console.log('🔧 [ChatStore] 开始断开ChatClient连接');
            await this.chatClient.disconnect()
            console.log('🔧 [ChatStore] ChatClient已断开连接');

            // 清理事件监听器
            this.chatClient.removeAllListeners();
            console.log('🔧 [ChatStore] ChatClient事件监听器已清理');

            // 销毁ChatClient实例（确保彻底清理）
            if (typeof this.chatClient.destroy === 'function') {
              this.chatClient.destroy();
              console.log('🔧 [ChatStore] ChatClient实例已销毁');
            }
          } catch (clientError) {
            console.warn('🔧 [ChatStore] ChatClient断开连接时出现警告:', clientError);
          }

          // 确保ChatClient实例完全销毁
          this.chatClient = null
        }

        // 重置所有连接相关状态
        this.clientStatus = 'DISCONNECTED' as any
        this.isConnected = false
        this.isInitializing = false  // 重置初始化锁
        this.reconnectAttempts = 0
        this.lastConnectedAt = null

        console.log('✅ [ChatStore] 聊天连接已断开，所有状态已重置');

      } catch (error) {
        console.error('❌ [ChatStore] 断开聊天连接失败:', error);
        this.error = error instanceof Error ? error.message : '断开连接失败'
        throw error
      } finally {
        // 释放断开连接状态锁
        this.isDisconnecting = false;
        console.log('🔧 [ChatStore] 断开连接状态锁已释放');
      }
    },

    /**
     * Token更新后重新连接WebSocket
     * @param userType 用户类型
     * @param userId 用户ID
     */
    async reconnectAfterTokenRefresh(userType: string, userId?: number) {
      // 防止重复重连
      if (this.isInitializing || this.isDisconnecting) {
        console.warn('⚠️ [ChatStore] 正在初始化或断开连接中，跳过Token重连');
        return;
      }

      try {
        console.log('🔄 [ChatStore] Token更新后重新连接WebSocket', {
          userType,
          userId,
          currentStatus: {
            isConnected: this.isConnected,
            isInitializing: this.isInitializing,
            isDisconnecting: this.isDisconnecting,
            clientStatus: this.clientStatus
          }
        });

        // 如果当前有连接，先断开（这会停止自动重连机制）
        if (this.isConnected || this.chatClient) {
          console.log('🔧 [ChatStore] 断开旧的WebSocket连接，停止自动重连');
          await this.disconnectChat();

          // 等待一小段时间确保连接完全断开和清理完成
          await new Promise(resolve => setTimeout(resolve, 800));
        }

        // 使用新token重新初始化连接
        console.log('🔧 [ChatStore] 使用新token重新初始化连接');
        await this.initializeChat({
          userType: userType,
          userId: userId
        });

        console.log('✅ [ChatStore] Token更新后WebSocket重连成功');
      } catch (error) {
        console.error('❌ [ChatStore] Token更新后WebSocket重连失败:', error);
        this.error = error instanceof Error ? error.message : 'Token更新后重连失败'
        throw error
      }
    },

    /**
     * 发送消息
     */
    async sendMessage(sessionId: string, content: string, type: MessageType = 'TEXT' as any) {
      try {
        const chatClient = await this.getChatClient()
        const message = await chatClient.sendMessage({
          session_id: Number(sessionId),
          type,
          content,
          category: 'CHAT' as any
          // metadata: {} // 属性不存在，注释掉
        })
        
        this.addMessage(message as any)
        return message
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '发送消息失败'
        throw error
      }
    },

    /**
     * 创建会话
     */
    async createSession(type: SessionType, _participants: string[], title?: string) {
      try {
        const chatClient = await this.getChatClient()
        const session = await chatClient.createSession({
          type,
          title: title || '',
          receiver_id: 1, // 添加必需的属性
          receiver_type: 'USER' as any // 添加必需的属性
          // participants, // 属性不存在，注释掉
          // settings: {} // 属性不存在，注释掉
        })
        
        this.addSession(session as any)
        return session
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '创建会话失败'
        throw error
      }
    },

    /**
     * 加载会话列表
     */
    async loadSessions() {
      try {
        this.isLoading = true
        const chatClient = await this.getChatClient()
        
        const response = await chatClient.getSessions({
          page: 1,
          page_size: 50
        })
        
        this.sessions = response.data?.sessions || []
        
        // 更新会话缓存
        this.sessions.forEach(session => {
          this.sessionCache.set(session.id.toString(), session)
        })
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '加载会话列表失败'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 加载消息历史
     */
    async loadMessages(sessionId: string, page = 1, perPage = 50) {
      try {
        const chatClient = await this.getChatClient()
        
        const response = await chatClient.getMessages({
          session_id: Number(sessionId),
          page,
          page_size: perPage
        })
        
        const messages = response.data?.messages || []
        
        // 更新消息缓存
        messages.forEach((message: any) => {
          this.messageCache.set(message.id, message)
        })
        
        // 更新会话消息列表
        if (page === 1) {
          this.messages.set(sessionId, messages)
        } else {
          const existingMessages = this.messages.get(sessionId) || []
          this.messages.set(sessionId, [...messages, ...existingMessages])
        }
        
        return messages
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '加载消息失败'
        throw error
      }
    },

    /**
     * 切换当前会话
     */
    async switchSession(sessionId: string) {
      try {
        this.currentSessionId = sessionId
        
        // 加载消息历史（如果还没有加载）
        if (!this.messages.has(sessionId)) {
          await this.loadMessages(sessionId)
        }
        
        // 标记消息已读
        await this.markSessionRead(sessionId)
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '切换会话失败'
        throw error
      }
    },

    /**
     * 标记会话已读
     */
    async markSessionRead(sessionId: string) {
      try {
        const chatClient = await this.getChatClient()
        
        await chatClient.markMessageRead({
          session_id: Number(sessionId),
          message_ids: [] // 添加必需的属性，空数组表示标记会话中所有消息为已读
        })
        
        // 更新未读数量
        this.unreadCounts.set(sessionId, 0)
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '标记已读失败'
        throw error
      }
    },

    /**
     * 上传文件
     */
    async uploadFile(file: File, sessionId: string) {
      try {
        const chatClient = await this.getChatClient()
        
        const result = await chatClient.uploadFile(file, Number(sessionId))
        // 简化上传处理，移除进度回调
        
        // 发送文件消息
        if (result && typeof result === 'string') {
          await this.sendMessage(sessionId, result, 'FILE' as any)
        }
        
        // 清理上传进度
        this.uploadProgress.delete(file.name)
        
        return result
        
      } catch (error) {
        this.uploadProgress.delete(file.name)
        this.error = error instanceof Error ? error.message : '文件上传失败'
        throw error
      }
    },

    /**
     * 添加消息
     */
    addMessage(message: Message) {
      console.log('📝 [ChatStore] 开始添加消息:', {
        messageId: message.id,
        sessionId: message.session_id,
        currentSessionId: this.currentSessionId,
        content: message.content?.substring(0, 30) + '...'
      })

      // 🔧 检查消息是否已存在，避免重复添加
      const messageId = message.id.toString()
      if (this.messageCache.has(messageId)) {
        console.log('ℹ️ [ChatStore] 消息已存在，跳过添加:', messageId)
        return
      }

      // 更新消息缓存
      this.messageCache.set(messageId, message)

      // 更新会话消息列表
      const sessionMessages = this.messages.get(message.session_id.toString()) || []

      // 🔧 双重检查：确保消息不在会话列表中
      const existingIndex = sessionMessages.findIndex(m => m.id.toString() === messageId)
      if (existingIndex !== -1) {
        console.log('ℹ️ [ChatStore] 消息已在会话列表中，跳过添加:', messageId)
        return
      }

      const messageCountBefore = sessionMessages.length
      sessionMessages.push(message)
      this.messages.set(message.session_id.toString(), sessionMessages)

      console.log('📊 [ChatStore] 会话消息更新:', {
        sessionId: message.session_id,
        messageCountBefore,
        messageCountAfter: sessionMessages.length
      })

      // 更新未读数量（如果不是当前会话）
      if (message.session_id.toString() !== this.currentSessionId) {
        const currentCount = this.unreadCounts.get(message.session_id.toString()) || 0
        this.unreadCounts.set(message.session_id.toString(), currentCount + 1)
        console.log('📬 [ChatStore] 更新未读数量:', {
          sessionId: message.session_id,
          unreadCount: currentCount + 1
        })
      }

      // 更新会话的最后消息时间
      const session = this.sessionCache.get(message.session_id.toString())
      if (session) {
        session.updated_at = message.created_at
        this.updateSession(session)
        console.log('🔄 [ChatStore] 更新会话时间:', session.id)
      }

      console.log('✅ [ChatStore] 消息添加完成')
    },

    /**
     * 设置聊天UI可见状态
     */
    setChatUIVisible(visible: boolean, sessionId?: string) {
      console.log('🔧 [ChatStore] 设置聊天UI状态:', { visible, sessionId })
      this.isChatUIVisible = visible
      if (visible && sessionId) {
        this.currentVisibleSessionId = sessionId
      } else if (!visible) {
        this.currentVisibleSessionId = null
      }
    },

    /**
     * 检查聊天UI是否对指定会话可见
     */
    isChatUIVisibleForSession(sessionId: string): boolean {
      return this.isChatUIVisible && this.currentVisibleSessionId === sessionId
    },

    /**
     * 更新消息状态
     */
    updateMessageStatus(messageId: string, status: MessageStatus) {
      const message = this.messageCache.get(messageId)
      if (message) {
        message.status = status
        this.messageCache.set(messageId, message)
        
        // 更新会话消息列表中的消息
        const sessionMessages = this.messages.get(message.session_id.toString())
        if (sessionMessages) {
          const index = sessionMessages.findIndex(m => m.id.toString() === messageId)
          if (index !== -1) {
            sessionMessages[index] = message
          }
        }
      }
    },

    /**
     * 添加会话
     */
    addSession(session: Session) {
      // 更新会话缓存
      this.sessionCache.set(session.id.toString(), session)
      
      // 添加到会话列表（如果不存在）
      const existingIndex = this.sessions.findIndex(s => s.id === session.id)
      if (existingIndex === -1) {
        this.sessions.unshift(session)
      } else {
        this.sessions[existingIndex] = session
      }
      
      // 按更新时间排序
      this.sessions.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
    },

    /**
     * 更新会话
     */
    updateSession(session: Session) {
      // 更新会话缓存
      this.sessionCache.set(session.id.toString(), session)
      
      // 更新会话列表
      const index = this.sessions.findIndex(s => s.id === session.id)
      if (index !== -1) {
        this.sessions[index] = session
        
        // 重新排序
        this.sessions.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
      }
    },

    /**
     * 更新用户在线状态
     */
    updateUserStatus(userId: string, status: UserOnlineStatus) {
      this.onlineUsers.set(userId, status)
    },

    /**
     * 更新会话成员在线状态
     */
    updateSessionMembersOnlineStatus(data: any) {
      const { sessionId, onlineMembers } = data

      if (!sessionId || !onlineMembers) {
        console.warn('⚠️ [ChatStore] 会话成员在线状态数据不完整:', data)
        return
      }

      // 更新对应会话的在线状态
      const session = this.sessions.find(s => s.id.toString() === sessionId.toString())
      if (session) {
        // 检查是否有任何成员在线
        const hasOnlineMembers = onlineMembers.some((member: any) => member.online_status === 'active')
        // 使用类型断言来添加is_online属性
        ;(session as any).is_online = hasOnlineMembers

        console.log('✅ [ChatStore] 已更新会话在线状态:', {
          sessionId,
          hasOnlineMembers,
          sessionTitle: session.title,
          memberCount: onlineMembers.length
        })
      }

      // 更新在线用户状态
      onlineMembers.forEach((member: any) => {
        const userId = member.user_id || member.merchant_id
        if (userId) {
          const status = member.online_status === 'active' ? 'online' : 'offline'
          this.onlineUsers.set(userId.toString(), status as UserOnlineStatus)
        }
      })
    },

    /**
     * 更新输入状态
     */
    updateTypingStatus(sessionId: string, userId: string, isTyping: boolean) {
      let typingSet = this.typingUsers.get(sessionId)
      if (!typingSet) {
        typingSet = new Set()
        this.typingUsers.set(sessionId, typingSet)
      }

      if (isTyping) {
        typingSet.add(userId)
      } else {
        typingSet.delete(userId)
      }
    },

    /**
     * 处理用户上线通知
     */
    async handleUserOnlineNotification(data: any) {
      console.log('🔍 [ChatStore] 处理用户上线通知:', data)

      try {
        // 检查是否应该显示ElNotification
        if (!this.isChatUIVisible) {
          const { getNotificationFilter } = await import('../config/notificationFilter')
          const notificationFilter = getNotificationFilter('user') // 这里应该根据当前用户类型动态获取

          if (notificationFilter.shouldShowNotification('user_online')) {
            const { ElNotification } = await import('element-plus')

            ElNotification({
              title: '用户上线',
              message: `用户 ${data.user_id} 已上线`,
              type: 'info',
              duration: 3000,
              showClose: true
            })
          }
        }

        // 更新用户在线状态
        this.updateUserStatus(data.user_id, UserOnlineStatus.ONLINE)
      } catch (error) {
        console.error('🔍 [ChatStore] 处理用户上线通知失败:', error)
      }
    },

    /**
     * 处理用户下线通知
     */
    async handleUserOfflineNotification(data: any) {
      console.log('🔍 [ChatStore] 处理用户下线通知:', data)

      try {
        // 检查是否应该显示ElNotification
        if (!this.isChatUIVisible) {
          const { getNotificationFilter } = await import('../config/notificationFilter')
          const notificationFilter = getNotificationFilter('user') // 这里应该根据当前用户类型动态获取

          if (notificationFilter.shouldShowNotification('user_offline')) {
            const { ElNotification } = await import('element-plus')

            ElNotification({
              title: '用户下线',
              message: `用户 ${data.user_id} 已下线`,
              type: 'info',
              duration: 3000,
              showClose: true
            })
          }
        }

        // 更新用户在线状态
        this.updateUserStatus(data.user_id, UserOnlineStatus.OFFLINE)
      } catch (error) {
        console.error('🔍 [ChatStore] 处理用户下线通知失败:', error)
      }
    },

    /**
     * 添加通知
     */
    addNotification(notification: Notification) {
      this.notifications.unshift(notification)
      
      // 限制通知数量
      if (this.notifications.length > 100) {
        this.notifications = this.notifications.slice(0, 100)
      }
      
      // 更新未读数量
      if (notification.status === 'PENDING' as any) {
        this.unreadNotificationCount++
      }
    },

    /**
     * 标记通知已读
     */
    async markNotificationRead(notificationId: number) {
      try {
        // const chatClient = this.getChatClient() // 未使用，注释掉
        
        // await chatClient.markNotificationRead({ // 方法不存在，暂时注释掉
        //   notification_id: notificationId
        // }) // 方法不存在，暂时注释掉
        
        // 更新本地状态
        const notification = this.notifications.find(n => n.id === notificationId)
        if (notification && notification.status === 'PENDING' as any) {
          notification.status = 'READ' as any
          this.unreadNotificationCount = Math.max(0, this.unreadNotificationCount - 1)
        }
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '标记通知已读失败'
        throw error
      }
    },

    /**
     * 清除错误
     */
    clearError() {
      this.error = null
    },

    /**
     * 重置状态
     */
    resetState() {
      this.clientStatus = 'DISCONNECTED' as any
      this.isConnected = false
      this.reconnectAttempts = 0
      this.lastConnectedAt = null
      this.currentUser = null
      this.messages.clear()
      this.messageCache.clear()
      this.unreadCounts.clear()
      this.typingUsers.clear()
      this.sessions = []
      this.currentSessionId = null
      this.sessionCache.clear()
      this.notifications = []
      this.notificationSettings = null
      this.unreadNotificationCount = 0
      this.chatRooms = []
      this.currentRoomId = null
      this.onlineUsers.clear()
      this.userProfiles.clear()
      this.chatSettings = null
      this.quickReplies = []
      this.messageTemplates = []
      this.statistics = null
      this.isLoading = false
      this.error = null
      this.uploadProgress.clear()
    },

    /**
     * 设置总的未读消息数量
     * 这个方法用于从API获取的总未读数量更新到store中
     */
    setTotalUnreadCount(total: number) {
      // 清空现有的未读计数
      this.unreadCounts.clear()
      // 如果有总数，设置一个特殊的key来存储总数
      if (total > 0) {
        this.unreadCounts.set('__total__', total)
      }
    },

    /**
     * 设置消息路由器
     * 用于集成新的消息处理器架构
     */
    setMessageRouter(router: MessageRouter) {
      console.log('🔧 [ChatStore] 设置消息路由器:', router)
      this.messageRouter = router

      // 如果已经有ChatClient实例，将路由器集成到消息处理中
      if (this.chatClient) {
        this.integrateMessageRouter()
      }
    },

    /**
     * 集成消息路由器到ChatClient
     * 将新的消息处理器与现有的WebSocket消息处理集成
     */
    integrateMessageRouter() {
      if (!this.messageRouter || !this.chatClient) {
        console.warn('⚠️ [ChatStore] 消息路由器或ChatClient未初始化，跳过集成')
        return
      }

      console.log('🔧 [ChatStore] 开始集成消息路由器到ChatClient')

      // 监听WebSocket原始消息，通过路由器处理
      this.chatClient.on('rawMessage', async (message: any) => {
        try {
          console.log('🔧 [ChatStore] 收到原始WebSocket消息，通过路由器处理:', message.type)

          // 将WebSocket消息转换为标准格式
          const standardMessage = {
            id: message.id || `msg_${Date.now()}`,
            type: message.type,
            data: message.data || message,
            timestamp: message.timestamp || Date.now(),
            from: message.from || 'server',
            to: message.to || 'client'
          }

          // 通过消息路由器处理
          const results = await this.messageRouter!.routeMessage(standardMessage)

          console.log('✅ [ChatStore] 消息路由器处理完成:', {
            messageType: message.type,
            handlerCount: results.length,
            successCount: results.filter(r => r.success).length
          })

        } catch (error) {
          console.error('❌ [ChatStore] 消息路由器处理失败:', error)
        }
      })

      console.log('✅ [ChatStore] 消息路由器集成完成')
    }
  },

  // persist: {
  //   key: 'chat-store',
  //   storage: localStorage,
  //   paths: [
  //     'currentSessionId',
  //     'chatSettings',
  //     'notificationSettings',
  //     'quickReplies',
  //     'messageTemplates'
  //   ]
  // } // 暂时注释掉 persist 配置
})

export default useChatStore