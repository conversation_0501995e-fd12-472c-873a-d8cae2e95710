<template>
  <teleport to="body">
    <div class="notification-toast-container">
      <transition-group
        name="notification"
        tag="div"
        class="notification-toast-list"
      >
        <div
          v-for="notification in visibleNotifications"
          :key="notification.id"
          class="notification-toast"
          :class="getNotificationClass(notification)"
          @click="handleNotificationClick(notification)"
        >
          <!-- 图标 -->
          <div class="notification-toast__icon">
            <svg v-if="getNotificationCategory(notification.type) === 'success'" class="notification-toast__icon-svg" viewBox="0 0 24 24">
              <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z"/>
            </svg>
            
            <svg v-else-if="getNotificationCategory(notification.type) === 'error'" class="notification-toast__icon-svg" viewBox="0 0 24 24">
              <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,7A1,1 0 0,0 11,8V12A1,1 0 0,0 12,13A1,1 0 0,0 13,12V8A1,1 0 0,0 12,7M12,17.5A1.5,1.5 0 0,0 13.5,16A1.5,1.5 0 0,0 12,14.5A1.5,1.5 0 0,0 10.5,16A1.5,1.5 0 0,0 12,17.5Z"/>
            </svg>
            
            <svg v-else-if="getNotificationCategory(notification.type) === 'warning'" class="notification-toast__icon-svg" viewBox="0 0 24 24">
              <path d="M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z"/>
            </svg>
            
            <svg v-else-if="getNotificationCategory(notification.type) === 'info'" class="notification-toast__icon-svg" viewBox="0 0 24 24">
              <path d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
            </svg>
            
            <svg v-else-if="getNotificationCategory(notification.type) === 'message'" class="notification-toast__icon-svg" viewBox="0 0 24 24">
              <path d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M20,16H6L4,18V4H20V16Z"/>
            </svg>
            
            <svg v-else class="notification-toast__icon-svg" viewBox="0 0 24 24">
              <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z"/>
            </svg>
          </div>
          
          <!-- 内容 -->
          <div class="notification-toast__content">
            <!-- 头像（消息通知） -->
            <div v-if="isMessageNotification(notification) && getMessageAvatar(notification)" class="notification-toast__avatar">
              <img
                :src="getMessageAvatar(notification)"
                :alt="getMessageSenderName(notification) || '用户'"
                class="notification-toast__avatar-img"
              />
            </div>
            
            <!-- 标题和内容 -->
            <div class="notification-toast__text">
              <h4 v-if="notification.title" class="notification-toast__title">
                {{ notification.title }}
              </h4>
              
              <p class="notification-toast__message">
                {{ notification.content }}
              </p>
              
              <!-- 时间戳 -->
              <span class="notification-toast__time">
                {{ formatTime(notification.created_at) }}
              </span>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="notification-toast__actions">
            <!-- 标记已读按钮 -->
            <button
              v-if="isMessageNotification(notification) && !isNotificationRead(notification)"
              class="notification-toast__action-btn notification-toast__action-btn--read"
              @click.stop="markAsRead(notification)"
              title="标记已读"
            >
              <svg class="notification-toast__action-icon" viewBox="0 0 24 24">
                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z"/>
              </svg>
            </button>
            
            <!-- 回复按钮 -->
            <button
              v-if="isMessageNotification(notification)"
              class="notification-toast__action-btn notification-toast__action-btn--reply"
              @click.stop="replyToMessage(notification)"
              title="回复"
            >
              <svg class="notification-toast__action-icon" viewBox="0 0 24 24">
                <path d="M10,9V5L3,12L10,19V14.9C15,14.9 18.5,16.5 21,20C20,15 17,10 10,9Z"/>
              </svg>
            </button>
            
            <!-- 关闭按钮 -->
            <button
              class="notification-toast__action-btn notification-toast__action-btn--close"
              @click.stop="dismissNotification(notification)"
              title="关闭"
            >
              <svg class="notification-toast__action-icon" viewBox="0 0 24 24">
                <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
              </svg>
            </button>
          </div>
          
          <!-- 进度条 -->
          <div
            v-if="true"
            class="notification-toast__progress"
            :style="{ animationDuration: `${props.defaultDuration}ms` }"
          ></div>
        </div>
      </transition-group>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useNotificationStore, useSessionStore } from '../stores'
import type { Notification } from '../types'

// Props
interface Props {
  maxVisible?: number
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  defaultDuration?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxVisible: 5,
  position: 'top-right',
  defaultDuration: 5000
})

// Emits
interface Emits {
  notificationClick: [notification: Notification]
  notificationDismiss: [notification: Notification]
  replyToMessage: [notification: Notification]
}

const emit = defineEmits<Emits>()

// Stores
const notificationStore = useNotificationStore()
const sessionStore = useSessionStore()

// Store refs
const { notifications } = storeToRefs(notificationStore)

// State
const visibleNotifications = ref<Notification[]>([])
const timers = ref<Map<number, number>>(new Map())

// Computed
const notificationQueue = computed(() => {
  return notifications.value
    .filter((_n: any) => true) // 暂时显示所有通知
    .sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, props.maxVisible)
})

// Methods
const getNotificationClass = (notification: Notification) => {
  const baseClass = 'notification-toast'
  const typeClass = `${baseClass}--${notification.type}`
  const priorityClass = notification.priority ? `${baseClass}--${notification.priority}` : ''
  
  return [baseClass, typeClass, priorityClass].filter(Boolean).join(' ')
}

const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }
  
  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }
  
  // 格式化为时间
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Helper functions
const getNotificationCategory = (type: any) => {
  // 根据通知类型返回分类
  if (type.includes('MESSAGE')) return 'message'
  if (type.includes('ERROR') || type.includes('FAILED')) return 'error'
  if (type.includes('WARNING') || type.includes('TIMEOUT')) return 'warning'
  if (type.includes('SUCCESS') || type.includes('DELIVERED')) return 'success'
  return 'info'
}

const isMessageNotification = (notification: Notification) => {
  return notification.type.toString().includes('MESSAGE')
}

const getMessageAvatar = (notification: Notification) => {
  if ('data' in notification && notification.data && typeof notification.data === 'object') {
    return (notification.data as any).sender_avatar || ''
  }
  return ''
}

const getMessageSenderName = (notification: Notification) => {
  if ('data' in notification && notification.data && typeof notification.data === 'object') {
    return (notification.data as any).sender_name || ''
  }
  return ''
}

const isNotificationRead = (notification: Notification) => {
  return notification.status === 'read'
}

const getMessageSessionId = (notification: Notification) => {
  if ('data' in notification && notification.data && typeof notification.data === 'object') {
    return (notification.data as any).session_id
  }
  return null
}

const handleNotificationClick = (notification: Notification) => {
  emit('notificationClick', notification)
  
  // 如果是消息通知，切换到对应会话
  if (isMessageNotification(notification)) {
    const sessionId = getMessageSessionId(notification)
    if (sessionId) {
      sessionStore.currentSessionId = sessionId.toString()
    }
  }

  // 标记为已读
  if (!isNotificationRead(notification)) {
    markAsRead(notification)
  }
  
  // 自动关闭
  dismissNotification(notification)
}

const markAsRead = async (notification: Notification) => {
  try {
    // 标记已读的逻辑暂时简化
    console.log('标记通知已读:', notification.id)
  } catch (error) {
    console.error('标记通知已读失败:', error)
  }
}

const replyToMessage = (notification: Notification) => {
  emit('replyToMessage', notification)
  
  // 切换到对应会话
  const sessionId = getMessageSessionId(notification)
  if (sessionId) {
    sessionStore.currentSessionId = sessionId.toString()
  }
  
  // 关闭通知
  dismissNotification(notification)
}

const dismissNotification = (notification: Notification) => {
  // 清除定时器
  const timer = timers.value.get(notification.id)
  if (timer) {
    clearTimeout(timer)
    timers.value.delete(notification.id)
  }
  
  // 从可见列表中移除
  const index = visibleNotifications.value.findIndex(n => n.id === notification.id)
  if (index > -1) {
    visibleNotifications.value.splice(index, 1)
  }
  
  emit('notificationDismiss', notification)
}

const addNotification = (notification: Notification) => {
  // 检查是否已存在
  const exists = visibleNotifications.value.some(n => n.id === notification.id)
  if (exists) return
  
  // 添加到可见列表
  visibleNotifications.value.unshift(notification)
  
  // 限制最大显示数量
  if (visibleNotifications.value.length > props.maxVisible) {
    const removed = visibleNotifications.value.splice(props.maxVisible)
    removed.forEach(n => {
      const timer = timers.value.get(n.id)
      if (timer) {
        clearTimeout(timer)
        timers.value.delete(n.id)
      }
    })
  }
  
  // 设置自动关闭定时器
  // 自动关闭通知
  const duration = props.defaultDuration
  const timer = window.setTimeout(() => {
    dismissNotification(notification)
  }, duration)

  timers.value.set(notification.id, timer)
}

const updateVisibleNotifications = () => {
  const currentIds = new Set(visibleNotifications.value.map(n => n.id))
  const queueIds = new Set(notificationQueue.value.map((n: any) => n.id))
  
  // 移除不在队列中的通知
  visibleNotifications.value = visibleNotifications.value.filter(n => queueIds.has(n.id))
  
  // 添加新通知
  notificationQueue.value.forEach((notification: any) => {
    if (!currentIds.has(notification.id)) {
      addNotification(notification)
    }
  })
}

const clearAllTimers = () => {
  timers.value.forEach(timer => clearTimeout(timer))
  timers.value.clear()
}

// Lifecycle
onMounted(() => {
  updateVisibleNotifications()
  
  // 监听通知变化
  const unwatch = notificationStore.$subscribe(() => {
    updateVisibleNotifications()
  })
  
  onUnmounted(() => {
    unwatch()
    clearAllTimers()
  })
})

onUnmounted(() => {
  clearAllTimers()
})
</script>

<style scoped>
.notification-toast-container {
  @apply fixed z-50 pointer-events-none;
  top: 1rem;
  right: 1rem;
}

.notification-toast-list {
  @apply space-y-2;
}

.notification-toast {
  @apply bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm pointer-events-auto cursor-pointer transition-all duration-300 relative overflow-hidden;
}

.notification-toast:hover {
  @apply shadow-xl transform scale-105;
}

.notification-toast--success {
  @apply border-l-4 border-l-green-500;
}

.notification-toast--error {
  @apply border-l-4 border-l-red-500;
}

.notification-toast--warning {
  @apply border-l-4 border-l-yellow-500;
}

.notification-toast--info {
  @apply border-l-4 border-l-blue-500;
}

.notification-toast--message {
  @apply border-l-4 border-l-purple-500;
}

.notification-toast--high {
  @apply ring-2 ring-red-500 ring-opacity-50;
}

.notification-toast--medium {
  @apply ring-2 ring-yellow-500 ring-opacity-50;
}

.notification-toast__icon {
  @apply flex-shrink-0 mr-3;
}

.notification-toast__icon-svg {
  @apply w-6 h-6 fill-current;
}

.notification-toast--success .notification-toast__icon-svg {
  @apply text-green-500;
}

.notification-toast--error .notification-toast__icon-svg {
  @apply text-red-500;
}

.notification-toast--warning .notification-toast__icon-svg {
  @apply text-yellow-500;
}

.notification-toast--info .notification-toast__icon-svg {
  @apply text-blue-500;
}

.notification-toast--message .notification-toast__icon-svg {
  @apply text-purple-500;
}

.notification-toast__content {
  @apply flex items-start flex-1 min-w-0;
}

.notification-toast__avatar {
  @apply flex-shrink-0 mr-3;
}

.notification-toast__avatar-img {
  @apply w-8 h-8 rounded-full object-cover;
}

.notification-toast__text {
  @apply flex-1 min-w-0;
}

.notification-toast__title {
  @apply text-sm font-semibold text-gray-900 mb-1 m-0;
}

.notification-toast__message {
  @apply text-sm text-gray-700 mb-1 m-0 break-words;
}

.notification-toast__time {
  @apply text-xs text-gray-500;
}

.notification-toast__actions {
  @apply flex items-center space-x-1 ml-2;
}

.notification-toast__action-btn {
  @apply p-1 rounded hover:bg-gray-100 transition-colors;
}

.notification-toast__action-btn--read {
  @apply hover:bg-green-50 hover:text-green-600;
}

.notification-toast__action-btn--reply {
  @apply hover:bg-blue-50 hover:text-blue-600;
}

.notification-toast__action-btn--close {
  @apply hover:bg-red-50 hover:text-red-600;
}

.notification-toast__action-icon {
  @apply w-4 h-4 fill-current;
}

.notification-toast__progress {
  @apply absolute bottom-0 left-0 h-1 bg-blue-500 rounded-b-lg;
  animation: notification-progress linear forwards;
}

@keyframes notification-progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* 动画效果 */
.notification-enter-active {
  @apply transition-all duration-300;
}

.notification-leave-active {
  @apply transition-all duration-300;
}

.notification-enter-from {
  @apply opacity-0 transform translate-x-full;
}

.notification-leave-to {
  @apply opacity-0 transform translate-x-full;
}

.notification-move {
  @apply transition-transform duration-300;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .notification-toast-container {
    @apply left-4 right-4;
    top: 1rem;
  }
  
  .notification-toast {
    @apply max-w-none;
  }
}

/* 位置变体 */
.notification-toast-container--top-left {
  top: 1rem;
  left: 1rem;
  right: auto;
}

.notification-toast-container--bottom-right {
  top: auto;
  bottom: 1rem;
  right: 1rem;
}

.notification-toast-container--bottom-left {
  top: auto;
  bottom: 1rem;
  left: 1rem;
  right: auto;
}

.notification-toast-container--bottom-right .notification-enter-from,
.notification-toast-container--bottom-right .notification-leave-to,
.notification-toast-container--bottom-left .notification-enter-from,
.notification-toast-container--bottom-left .notification-leave-to {
  @apply transform translate-y-full;
}
</style>