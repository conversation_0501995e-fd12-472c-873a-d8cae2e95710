<template>
  <div class="chat-file-message" :class="messageClass">
    <!-- 图片消息 -->
    <div v-if="isImage" class="image-message">
      <div class="image-container" @click="previewImage">
        <img
          :src="fileData.thumbnail_url || fileData.url"
          :alt="fileData.filename"
          class="message-image"
          @load="onImageLoad"
          @error="onImageError"
        />
        <div v-if="imageLoading" class="image-loading">
          <el-icon class="is-loading"><Loading /></el-icon>
        </div>
        <div v-if="imageError" class="image-error">
          <el-icon><Picture /></el-icon>
          <span>图片加载失败</span>
        </div>
        <!-- 图片信息覆盖层 -->
        <div class="image-overlay">
          <div class="image-info">
            <span class="image-name">{{ fileData.filename }}</span>
            <span class="image-size">{{ formatFileSize(fileData.size) }}</span>
          </div>
          <div class="image-actions">
            <el-button
              type="primary"
              size="small"
              circle
              @click.stop="downloadFile"
            >
              <el-icon><Download /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件消息 -->
    <div v-else class="file-message">
      <div class="file-container" @click="downloadFile">
        <div class="file-icon">
          <span class="file-type-icon">{{ getFileTypeIcon(fileData.type) }}</span>
        </div>
        <div class="file-info">
          <div class="file-name" :title="fileData.filename">
            {{ fileData.filename }}
          </div>
          <div class="file-meta">
            <span class="file-size">{{ formatFileSize(fileData.size) }}</span>
            <span v-if="fileData.duration" class="file-duration">
              {{ formatDuration(fileData.duration) }}
            </span>
          </div>
        </div>
        <div class="file-actions">
          <el-button
            type="primary"
            size="small"
            :loading="downloading"
            @click.stop="downloadFile"
          >
            <el-icon><Download /></el-icon>
            <span>下载</span>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      :title="fileData.filename"
      width="90%"
      top="5vh"
      center
      append-to-body
      class="image-preview-dialog"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
    >
      <div class="preview-container">
        <div class="preview-image-wrapper">
          <img
            :src="fileData.url"
            :alt="fileData.filename"
            class="preview-image"
            @load="onPreviewImageLoad"
            @error="onPreviewImageError"
          />
          <div v-if="previewImageLoading" class="preview-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载中...</span>
          </div>
          <div v-if="previewImageError" class="preview-error">
            <el-icon><Picture /></el-icon>
            <span>图片加载失败</span>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="preview-actions">
          <el-button @click="previewVisible = false">关闭</el-button>
          <el-button type="primary" @click="downloadFile">
            <el-icon><Download /></el-icon>
            下载原图
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Picture, Download } from '@element-plus/icons-vue'
import { chatFileService } from '../services/chatFileService'

// Props
interface Props {
  fileData: {
    id: string
    url: string
    filename: string
    size: number
    type: string
    thumbnail_url?: string
    width?: number
    height?: number
    duration?: number
  }
  isSender?: boolean
}

const props = defineProps<Props>()

// 响应式数据
const imageLoading = ref(true)
const imageError = ref(false)
const previewVisible = ref(false)
const previewImageLoading = ref(false)
const previewImageError = ref(false)
const downloading = ref(false)

// 计算属性
const isImage = computed(() => {
  return chatFileService.isImageFile({ type: props.fileData.type } as File)
})

const messageClass = computed(() => {
  return {
    'is-sender': props.isSender,
    'is-image': isImage.value,
    'is-file': !isImage.value
  }
})

// 方法
const onImageLoad = () => {
  imageLoading.value = false
  imageError.value = false
}

const onImageError = () => {
  imageLoading.value = false
  imageError.value = true
}

const previewImage = () => {
  if (!imageError.value) {
    previewVisible.value = true
    previewImageLoading.value = true
    previewImageError.value = false
  }
}

const onPreviewImageLoad = () => {
  previewImageLoading.value = false
  previewImageError.value = false
}

const onPreviewImageError = () => {
  previewImageLoading.value = false
  previewImageError.value = true
}

const downloadFile = async () => {
  if (downloading.value) return

  downloading.value = true
  try {
    // 创建下载链接
    const link = document.createElement('a')
    link.href = props.fileData.url
    link.download = props.fileData.filename
    link.target = '_blank'
    
    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('开始下载文件')
  } catch (error: any) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载失败，请稍后重试')
  } finally {
    downloading.value = false
  }
}

const getFileTypeIcon = (fileType: string): string => {
  return chatFileService.getFileTypeIcon(fileType)
}

const formatFileSize = (bytes: number): string => {
  return chatFileService.formatFileSize(bytes)
}

const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}
</script>

<style scoped lang="scss">
.chat-file-message {
  max-width: 300px;
  
  &.is-sender {
    margin-left: auto;
  }

  // 图片消息样式
  .image-message {
    .image-container {
      position: relative;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      background: #f5f5f5;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.02);
        
        .image-overlay {
          opacity: 1;
        }
      }

      .message-image {
        width: 100%;
        height: auto;
        max-height: 200px;
        object-fit: cover;
        display: block;
      }

      .image-loading,
      .image-error {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.1);
        color: #666;
        font-size: 14px;
        gap: 8px;
      }

      .image-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
        color: white;
        padding: 12px;
        opacity: 0;
        transition: opacity 0.2s ease;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        .image-info {
          flex: 1;
          min-width: 0;

          .image-name {
            display: block;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 2px;
          }

          .image-size {
            font-size: 11px;
            opacity: 0.8;
          }
        }

        .image-actions {
          flex-shrink: 0;
          margin-left: 8px;

          .el-button {
            --el-button-size: 24px;
          }
        }
      }
    }
  }

  // 文件消息样式
  .file-message {
    .file-container {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: white;
      border: 1px solid #e1e5e9;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #f8f9fa;
        border-color: #409eff;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .file-icon {
        flex-shrink: 0;
        width: 40px;
        height: 40px;
        background: #f0f2f5;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;

        .file-type-icon {
          font-size: 20px;
        }
      }

      .file-info {
        flex: 1;
        min-width: 0;

        .file-name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 4px;
        }

        .file-meta {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: #666;

          .file-duration {
            &::before {
              content: '•';
              margin-right: 4px;
            }
          }
        }
      }

      .file-actions {
        flex-shrink: 0;

        .el-button {
          font-size: 12px;
          padding: 6px 12px;
        }
      }
    }
  }

  // 发送者样式调整
  &.is-sender {
    .file-message .file-container {
      background: #409eff;
      border-color: #409eff;
      color: white;

      .file-icon {
        background: rgba(255, 255, 255, 0.2);
      }

      .file-info {
        .file-name {
          color: white;
        }

        .file-meta {
          color: rgba(255, 255, 255, 0.8);
        }
      }

      .file-actions .el-button {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }
      }

      &:hover {
        background: #337ecc;
        border-color: #337ecc;
      }
    }
  }
}

// 图片预览对话框样式
:deep(.image-preview-dialog) {
  .el-dialog {
    margin: 0 !important;
    max-height: 95vh;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    flex-shrink: 0;
  }

  .el-dialog__body {
    padding: 0;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__footer {
    padding: 16px 20px;
    border-top: 1px solid #ebeef5;
    flex-shrink: 0;
  }

  .preview-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f5f5f5;
    overflow: hidden;
    min-height: 300px;
    max-height: calc(95vh - 120px); /* 减去头部和底部的高度 */
  }

  .preview-image-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .preview-image {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: white;
  }

  .preview-loading,
  .preview-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: #909399;
    font-size: 14px;
  }

  .preview-loading .el-icon {
    font-size: 24px;
  }

  .preview-error .el-icon {
    font-size: 48px;
    color: #f56c6c;
  }

  .preview-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.image-preview-dialog) {
    .el-dialog {
      width: 95% !important;
      margin: 0 !important;
    }

    .preview-container {
      max-height: calc(95vh - 100px);
    }
  }
}
</style>
