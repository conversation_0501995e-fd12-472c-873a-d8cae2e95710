<template>
  <div class="message-category-tabs">
    <div class="category-tabs">
      <div
        v-for="category in categories"
        :key="category.type"
        class="category-tab"
        :class="{ 'category-tab--active': currentCategory === category.type }"
        @click="handleCategoryClick(category.type)"
      >
        <div class="category-tab__icon" :style="{ color: category.color }">
          <component :is="getCategoryIcon(category.icon)" />
        </div>
        <div class="category-tab__content">
          <div class="category-tab__title">{{ category.title }}</div>
          <div v-if="category.unreadCount > 0" class="category-tab__badge">
            {{ category.unreadCount > 99 ? '99+' : category.unreadCount }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 总未读数显示 -->
    <div v-if="totalUnread > 0" class="total-unread-badge">
      {{ totalUnread > 99 ? '99+' : totalUnread }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  ChatDotRound, 
  Bell, 
  ShoppingBag, 
  Service 
} from '@element-plus/icons-vue'
import type { 
  MessageCategoryType, 
  MessageCategoryDTO 
} from '../types/message-category'

// Props
interface Props {
  categories: MessageCategoryDTO[]
  currentCategory: MessageCategoryType
  totalUnread?: number
}

const props = withDefaults(defineProps<Props>(), {
  totalUnread: 0
})

// Emits
interface Emits {
  (e: 'category-change', category: MessageCategoryType): void
}

const emit = defineEmits<Emits>()

// Methods
const handleCategoryClick = (category: MessageCategoryType) => {
  if (category !== props.currentCategory) {
    emit('category-change', category)
  }
}

const getCategoryIcon = (iconName: string) => {
  const iconMap: Record<string, any> = {
    chat: ChatDotRound,
    notification: Bell,
    goods: ShoppingBag,
    service: Service
  }
  return iconMap[iconName] || ChatDotRound
}
</script>

<style scoped>
.message-category-tabs {
  position: relative;
  background: #fff;
  border-bottom: 1px solid #e5e7eb;
}

.category-tabs {
  display: flex;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.category-tabs::-webkit-scrollbar {
  display: none;
}

.category-tab {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  min-width: 120px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
  position: relative;
}

.category-tab:hover {
  background-color: #f9fafb;
}

.category-tab--active {
  border-bottom-color: #3b82f6;
  background-color: #eff6ff;
}

.category-tab__icon {
  font-size: 20px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-tab__content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.category-tab__title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.category-tab--active .category-tab__title {
  color: #3b82f6;
}

.category-tab__badge {
  background: #ef4444;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.total-unread-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #ef4444;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-tab {
    min-width: 100px;
    padding: 10px 12px;
  }
  
  .category-tab__title {
    font-size: 13px;
  }
  
  .category-tab__icon {
    font-size: 18px;
    margin-right: 6px;
  }
}
</style>
