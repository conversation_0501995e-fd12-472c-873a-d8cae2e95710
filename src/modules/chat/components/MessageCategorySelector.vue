<template>
  <div class="message-category-selector">
    <div class="category-tabs">
      <div
        v-for="category in categories"
        :key="category.type"
        class="category-tab"
        :class="{
          'category-tab--active': selectedCategory === category.type,
          [`category-tab--${category.type}`]: true
        }"
        @click="selectCategory(category.type)"
      >
        <div class="category-icon">
          <component :is="getCategoryIcon(category.icon)" />
        </div>
        <div class="category-info">
          <span class="category-title">{{ category.title }}</span>
          <span v-if="category.unread_count > 0" class="category-badge">
            {{ formatUnreadCount(category.unread_count) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { MessageCategory, MessageCategoryUtils } from '../types/message'
import type { MessageCategoryInfo } from '../types/message'
import {
  ChatDotRound,
  Bell,
  ShoppingBag,
  Service
} from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue?: MessageCategory
  categories?: MessageCategoryInfo[]
  showUnreadCount?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: MessageCategory.CHAT,
  categories: () => [],
  showUnreadCount: true
})

// Emits
interface Emits {
  'update:modelValue': [category: MessageCategory]
  'category-change': [category: MessageCategory]
}

const emit = defineEmits<Emits>()

// 响应式数据
const selectedCategory = ref<MessageCategory>(props.modelValue)

// 计算属性
const categories = computed(() => {
  if (props.categories.length > 0) {
    return props.categories
  }
  // 使用默认分类
  return MessageCategoryUtils.getAllCategories()
})

// 方法
const selectCategory = (category: MessageCategory) => {
  selectedCategory.value = category
  emit('update:modelValue', category)
  emit('category-change', category)
}

const getCategoryIcon = (iconName: string) => {
  const iconMap: Record<string, any> = {
    chat: ChatDotRound,
    notification: Bell,
    order: ShoppingBag,
    service: Service
  }
  return iconMap[iconName] || ChatDotRound
}

const formatUnreadCount = (count: number): string => {
  if (count > 99) {
    return '99+'
  }
  return count.toString()
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  selectedCategory.value = newValue
})

// 生命周期
onMounted(() => {
  selectedCategory.value = props.modelValue
})
</script>

<style scoped>
.message-category-selector {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.category-tabs {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
  background-color: #fff;
  min-width: 120px;
}

.category-tab:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
}

.category-tab--active {
  background-color: #eff6ff;
  border-color: #3b82f6;
  color: #3b82f6;
}

.category-tab--chat.category-tab--active {
  background-color: #eff6ff;
  border-color: #3b82f6;
  color: #3b82f6;
}

.category-tab--system.category-tab--active {
  background-color: #fef3c7;
  border-color: #f59e0b;
  color: #f59e0b;
}

.category-tab--order.category-tab--active {
  background-color: #d1fae5;
  border-color: #10b981;
  color: #10b981;
}

.category-tab--service.category-tab--active {
  background-color: #ede9fe;
  border-color: #8b5cf6;
  color: #8b5cf6;
}

.category-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.category-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.category-title {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
}

.category-badge {
  background-color: #ef4444;
  color: #fff;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
  line-height: 1.2;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-tabs {
    flex-direction: column;
  }
  
  .category-tab {
    min-width: auto;
    width: 100%;
  }
}
</style>
