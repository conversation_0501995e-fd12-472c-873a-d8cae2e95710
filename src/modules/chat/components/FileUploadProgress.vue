<template>
  <teleport to="body">
    <div v-if="hasActiveUploads" class="file-upload-progress">
      <div class="file-upload-progress__container">
        <!-- 头部 -->
        <div class="file-upload-progress__header">
          <div class="file-upload-progress__title">
            <svg class="file-upload-progress__title-icon" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
            </svg>
            <span>文件上传 ({{ activeUploads.length }})</span>
          </div>
          
          <div class="file-upload-progress__actions">
            <!-- 暂停/恢复所有 -->
            <button
              v-if="canPauseAll"
              class="file-upload-progress__action-btn"
              @click="pauseAll"
              title="暂停所有"
            >
              <svg class="file-upload-progress__action-icon" viewBox="0 0 24 24">
                <path d="M14,19H18V5H14M6,19H10V5H6V19Z"/>
              </svg>
            </button>
            
            <button
              v-if="canResumeAll"
              class="file-upload-progress__action-btn"
              @click="resumeAll"
              title="恢复所有"
            >
              <svg class="file-upload-progress__action-icon" viewBox="0 0 24 24">
                <path d="M8,5.14V19.14L19,12.14L8,5.14Z"/>
              </svg>
            </button>
            
            <!-- 取消所有 -->
            <button
              class="file-upload-progress__action-btn file-upload-progress__action-btn--danger"
              @click="cancelAll"
              title="取消所有"
            >
              <svg class="file-upload-progress__action-icon" viewBox="0 0 24 24">
                <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
              </svg>
            </button>
            
            <!-- 最小化/展开 -->
            <button
              class="file-upload-progress__action-btn"
              @click="toggleMinimized"
              :title="isMinimized ? '展开' : '最小化'"
            >
              <svg v-if="isMinimized" class="file-upload-progress__action-icon" viewBox="0 0 24 24">
                <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"/>
              </svg>
              <svg v-else class="file-upload-progress__action-icon" viewBox="0 0 24 24">
                <path d="M7.41,15.41L12,10.83L16.59,15.41L18,14L12,8L6,14L7.41,15.41Z"/>
              </svg>
            </button>
          </div>
        </div>
        
        <!-- 总体进度 -->
        <div v-if="!isMinimized" class="file-upload-progress__overall">
          <div class="file-upload-progress__overall-info">
            <span class="file-upload-progress__overall-text">
              总进度: {{ Math.round(overallProgress) }}%
            </span>
            <span class="file-upload-progress__overall-speed">
              {{ formatSpeed(overallSpeed) }}
            </span>
          </div>
          
          <div class="file-upload-progress__overall-bar">
            <div
              class="file-upload-progress__overall-fill"
              :style="{ width: `${overallProgress}%` }"
            ></div>
          </div>
        </div>
        
        <!-- 文件列表 -->
        <div v-if="!isMinimized" class="file-upload-progress__list">
          <div
            v-for="upload in activeUploads"
            :key="upload.id"
            class="file-upload-progress__item"
            :class="getUploadItemClass(upload)"
          >
            <!-- 文件信息 -->
            <div class="file-upload-progress__item-info">
              <!-- 文件图标/预览 -->
              <div class="file-upload-progress__item-icon">
                <img
                  v-if="isImageFile(upload.file) && upload.preview"
                  :src="upload.preview"
                  :alt="upload.file.name"
                  class="file-upload-progress__item-preview"
                />
                <svg v-else class="file-upload-progress__item-file-icon" viewBox="0 0 24 24">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                </svg>
              </div>
              
              <!-- 文件详情 -->
              <div class="file-upload-progress__item-details">
                <p class="file-upload-progress__item-name">{{ upload.file.name }}</p>
                <p class="file-upload-progress__item-size">
                  {{ formatFileSize(upload.uploaded) }} / {{ formatFileSize(upload.file.size) }}
                </p>
                <p class="file-upload-progress__item-status">
                  {{ getStatusText(upload.status) }}
                  <span v-if="upload.speed > 0" class="file-upload-progress__item-speed">
                    ({{ formatSpeed(upload.speed) }})
                  </span>
                </p>
              </div>
            </div>
            
            <!-- 进度条 -->
            <div class="file-upload-progress__item-progress">
              <div class="file-upload-progress__item-progress-bar">
                <div
                  class="file-upload-progress__item-progress-fill"
                  :class="getProgressFillClass(upload.status)"
                  :style="{ width: `${upload.progress}%` }"
                ></div>
              </div>
              
              <span class="file-upload-progress__item-progress-text">
                {{ Math.round(upload.progress) }}%
              </span>
            </div>
            
            <!-- 操作按钮 -->
            <div class="file-upload-progress__item-actions">
              <!-- 暂停/恢复 -->
              <button
                v-if="upload.status === 'uploading'"
                class="file-upload-progress__item-action"
                @click="pauseUpload(upload.id)"
                title="暂停"
              >
                <svg class="file-upload-progress__item-action-icon" viewBox="0 0 24 24">
                  <path d="M14,19H18V5H14M6,19H10V5H6V19Z"/>
                </svg>
              </button>
              
              <button
                v-else-if="upload.status === 'paused'"
                class="file-upload-progress__item-action"
                @click="resumeUpload(upload.id)"
                title="恢复"
              >
                <svg class="file-upload-progress__item-action-icon" viewBox="0 0 24 24">
                  <path d="M8,5.14V19.14L19,12.14L8,5.14Z"/>
                </svg>
              </button>
              
              <!-- 重试 -->
              <button
                v-if="upload.status === 'error'"
                class="file-upload-progress__item-action"
                @click="retryUpload(upload.id)"
                title="重试"
              >
                <svg class="file-upload-progress__item-action-icon" viewBox="0 0 24 24">
                  <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
                </svg>
              </button>
              
              <!-- 取消/删除 -->
              <button
                class="file-upload-progress__item-action file-upload-progress__item-action--danger"
                @click="cancelUpload(upload.id)"
                :title="upload.status === 'completed' ? '删除' : '取消'"
              >
                <svg v-if="upload.status === 'completed'" class="file-upload-progress__item-action-icon" viewBox="0 0 24 24">
                  <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                </svg>
                <svg v-else class="file-upload-progress__item-action-icon" viewBox="0 0 24 24">
                  <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                </svg>
              </button>
            </div>
            
            <!-- 错误信息 -->
            <div v-if="upload.status === 'error' && upload.error" class="file-upload-progress__item-error">
              <svg class="file-upload-progress__item-error-icon" viewBox="0 0 24 24">
                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,7A1,1 0 0,0 11,8V12A1,1 0 0,0 12,13A1,1 0 0,0 13,12V8A1,1 0 0,0 12,7M12,17.5A1.5,1.5 0 0,0 13.5,16A1.5,1.5 0 0,0 12,14.5A1.5,1.5 0 0,0 10.5,16A1.5,1.5 0 0,0 12,17.5Z"/>
              </svg>
              <span class="file-upload-progress__item-error-text">{{ upload.error }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useFileStore } from '../stores'
// FileUpload 类型暂时移除，使用简化的类型

// Props
interface Props {
  autoHide?: boolean
  hideDelay?: number
}

const props = withDefaults(defineProps<Props>(), {
  autoHide: true,
  hideDelay: 3000
})

// Emits
interface Emits {
  uploadComplete: [upload: any]
  uploadError: [upload: any, error: string]
}

const emit = defineEmits<Emits>()

// Store
const fileStore = useFileStore()

// Store refs
const { uploadQueue } = storeToRefs(fileStore)

// State
const isMinimized = ref(false)
const hideTimer = ref<number | null>(null)

// Computed
const activeUploads = computed(() => {
  return uploadQueue.value.filter((upload: any) =>
    upload.status !== 'completed' ||
    (upload.status === 'completed' && !props.autoHide)
  )
})

const hasActiveUploads = computed(() => {
  return activeUploads.value.length > 0
})

const overallProgress = computed(() => {
  if (activeUploads.value.length === 0) return 0
  
  const totalProgress = activeUploads.value.reduce((sum: number, upload: any) => sum + upload.progress, 0)
  return totalProgress / activeUploads.value.length
})

const overallSpeed = computed(() => {
  return activeUploads.value.reduce((sum: number, upload: any) => sum + (upload.speed || 0), 0)
})

const canPauseAll = computed(() => {
  return activeUploads.value.some((upload: any) => upload.status === 'uploading')
})

const canResumeAll = computed(() => {
  return activeUploads.value.some((upload: any) => upload.status === 'paused')
})

// Methods
const getUploadItemClass = (upload: any) => {
  return {
    'file-upload-progress__item--uploading': upload.status === 'uploading',
    'file-upload-progress__item--paused': upload.status === 'paused',
    'file-upload-progress__item--completed': upload.status === 'completed',
    'file-upload-progress__item--error': upload.status === 'error'
  }
}

const getProgressFillClass = (status: string) => {
  return {
    'bg-blue-500': status === 'uploading',
    'bg-yellow-500': status === 'paused',
    'bg-green-500': status === 'completed',
    'bg-red-500': status === 'error'
  }
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '等待中',
    'uploading': '上传中',
    'paused': '已暂停',
    'completed': '已完成',
    'error': '上传失败',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

const isImageFile = (file: File) => {
  return file.type.startsWith('image/')
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const formatSpeed = (bytesPerSecond: number) => {
  if (bytesPerSecond === 0) return '0 B/s'
  
  const k = 1024
  const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s']
  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k))
  
  return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const toggleMinimized = () => {
  isMinimized.value = !isMinimized.value
}

const pauseUpload = async (uploadId: string) => {
  try {
    // 暂停上传的逻辑暂时简化
    console.log('暂停上传:', uploadId)
  } catch (error) {
    console.error('暂停上传失败:', error)
  }
}

const resumeUpload = async (uploadId: string) => {
  try {
    // 恢复上传的逻辑暂时简化
    console.log('恢复上传:', uploadId)
  } catch (error) {
    console.error('恢复上传失败:', error)
  }
}

const retryUpload = async (uploadId: string) => {
  try {
    // 重试上传的逻辑暂时简化
    console.log('重试上传:', uploadId)
  } catch (error) {
    console.error('重试上传失败:', error)
  }
}

const cancelUpload = async (uploadId: string) => {
  try {
    // 取消上传的逻辑暂时简化
    console.log('取消上传:', uploadId)
  } catch (error) {
    console.error('取消上传失败:', error)
  }
}

const pauseAll = async () => {
  const uploadingIds = activeUploads.value
    .filter((upload: any) => upload.status === 'uploading')
    .map((upload: any) => upload.id)
  
  for (const id of uploadingIds) {
    await pauseUpload(id)
  }
}

const resumeAll = async () => {
  const pausedIds = activeUploads.value
    .filter((upload: any) => upload.status === 'paused')
    .map((upload: any) => upload.id)
  
  for (const id of pausedIds) {
    await resumeUpload(id)
  }
}

const cancelAll = async () => {
  const activeIds = activeUploads.value
    .filter((upload: any) => upload.status !== 'completed')
    .map((upload: any) => upload.id)
  
  for (const id of activeIds) {
    await cancelUpload(id)
  }
}

const scheduleAutoHide = () => {
  if (!props.autoHide) return
  
  // 清除之前的定时器
  if (hideTimer.value) {
    clearTimeout(hideTimer.value)
  }
  
  // 检查是否所有上传都已完成
  const allCompleted = activeUploads.value.every((upload: any) => upload.status === 'completed')
  
  if (allCompleted && activeUploads.value.length > 0) {
    hideTimer.value = window.setTimeout(() => {
      // 移除已完成的上传
      const completedIds = activeUploads.value
        .filter((upload: any) => upload.status === 'completed')
        .map((upload: any) => upload.id)

      completedIds.forEach((id: any) => {
        // 移除上传的逻辑暂时简化
        console.log('移除上传:', id)
      })
    }, props.hideDelay)
  }
}

// Lifecycle
onMounted(() => {
  // 监听上传状态变化
  watch(
    () => activeUploads.value.map((u: any) => ({ id: u.id, status: u.status })),
    (newUploads: any[], oldUploads: any[]) => {
      // 检查是否有上传完成
      if (oldUploads) {
        newUploads.forEach((newUpload: any, index: number) => {
          const oldUpload = oldUploads[index]
          if (oldUpload && oldUpload.status !== 'completed' && newUpload.status === 'completed') {
            const upload = activeUploads.value.find((u: any) => u.id === newUpload.id)
            if (upload) {
              emit('uploadComplete', upload)
            }
          }
          
          if (oldUpload && oldUpload.status !== 'error' && newUpload.status === 'error') {
            const upload = activeUploads.value.find((u: any) => u.id === newUpload.id)
            if (upload) {
              emit('uploadError', upload, upload.error || '上传失败')
            }
          }
        })
      }
      
      // 安排自动隐藏
      scheduleAutoHide()
    },
    { deep: true }
  )
})

onUnmounted(() => {
  if (hideTimer.value) {
    clearTimeout(hideTimer.value)
  }
})
</script>

<style scoped>
.file-upload-progress {
  @apply fixed bottom-4 right-4 z-40 max-w-md w-full;
}

.file-upload-progress__container {
  @apply bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden;
}

.file-upload-progress__header {
  @apply flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200;
}

.file-upload-progress__title {
  @apply flex items-center space-x-2 text-sm font-semibold text-gray-900;
}

.file-upload-progress__title-icon {
  @apply w-4 h-4 fill-current text-gray-600;
}

.file-upload-progress__actions {
  @apply flex items-center space-x-1;
}

.file-upload-progress__action-btn {
  @apply p-1 rounded hover:bg-gray-200 transition-colors;
}

.file-upload-progress__action-btn--danger {
  @apply hover:bg-red-50 hover:text-red-600;
}

.file-upload-progress__action-icon {
  @apply w-4 h-4 fill-current;
}

.file-upload-progress__overall {
  @apply p-3 border-b border-gray-200;
}

.file-upload-progress__overall-info {
  @apply flex items-center justify-between text-xs text-gray-600 mb-2;
}

.file-upload-progress__overall-bar {
  @apply w-full h-2 bg-gray-200 rounded-full overflow-hidden;
}

.file-upload-progress__overall-fill {
  @apply h-full bg-blue-500 transition-all duration-300;
}

.file-upload-progress__list {
  @apply max-h-64 overflow-y-auto;
}

.file-upload-progress__item {
  @apply p-3 border-b border-gray-100 last:border-b-0;
}

.file-upload-progress__item--uploading {
  @apply bg-blue-50;
}

.file-upload-progress__item--paused {
  @apply bg-yellow-50;
}

.file-upload-progress__item--completed {
  @apply bg-green-50;
}

.file-upload-progress__item--error {
  @apply bg-red-50;
}

.file-upload-progress__item-info {
  @apply flex items-start space-x-3 mb-2;
}

.file-upload-progress__item-icon {
  @apply flex-shrink-0;
}

.file-upload-progress__item-preview {
  @apply w-10 h-10 rounded object-cover;
}

.file-upload-progress__item-file-icon {
  @apply w-10 h-10 fill-current text-gray-600;
}

.file-upload-progress__item-details {
  @apply flex-1 min-w-0;
}

.file-upload-progress__item-name {
  @apply text-sm font-medium text-gray-900 truncate m-0;
}

.file-upload-progress__item-size {
  @apply text-xs text-gray-600 m-0;
}

.file-upload-progress__item-status {
  @apply text-xs text-gray-600 m-0;
}

.file-upload-progress__item-speed {
  @apply text-blue-600;
}

.file-upload-progress__item-progress {
  @apply flex items-center space-x-2 mb-2;
}

.file-upload-progress__item-progress-bar {
  @apply flex-1 h-1.5 bg-gray-200 rounded-full overflow-hidden;
}

.file-upload-progress__item-progress-fill {
  @apply h-full transition-all duration-300;
}

.file-upload-progress__item-progress-text {
  @apply text-xs text-gray-600 font-medium;
}

.file-upload-progress__item-actions {
  @apply flex items-center justify-end space-x-1;
}

.file-upload-progress__item-action {
  @apply p-1 rounded hover:bg-gray-200 transition-colors;
}

.file-upload-progress__item-action--danger {
  @apply hover:bg-red-100 hover:text-red-600;
}

.file-upload-progress__item-action-icon {
  @apply w-3 h-3 fill-current;
}

.file-upload-progress__item-error {
  @apply flex items-start space-x-2 mt-2 p-2 bg-red-100 rounded text-xs text-red-700;
}

.file-upload-progress__item-error-icon {
  @apply w-4 h-4 fill-current text-red-500 flex-shrink-0;
}

.file-upload-progress__item-error-text {
  @apply flex-1;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .file-upload-progress {
    @apply left-4 right-4 max-w-none;
  }
}
</style>