<template>
  <div class="message-category-detail">
    <!-- 详情头部 -->
    <div class="detail-header">
      <div class="header-left">
        <button class="back-btn" @click="$emit('back')">
          <i class="icon-arrow-left"></i>
        </button>
        <div class="category-info">
          <div class="category-icon" :style="{ color: categoryColor }">
            <i :class="`icon-${categoryIcon}`"></i>
          </div>
          <div class="category-details">
            <h3 class="category-title">{{ categoryTitle }}</h3>
            <span class="message-count">{{ totalCount }} 条消息</span>
          </div>
        </div>
      </div>
      
      <div class="header-actions">
        <button 
          class="action-btn"
          @click="toggleSelectMode"
          :class="{ 'active': selectMode }"
        >
          <i class="icon-select"></i>
          {{ selectMode ? '取消' : '选择' }}
        </button>
        <button class="action-btn" @click="showFilterPanel = !showFilterPanel">
          <i class="icon-filter"></i>
          筛选
        </button>
        <button class="action-btn" @click="refreshMessages">
          <i class="icon-refresh" :class="{ 'spinning': loading }"></i>
        </button>
      </div>
    </div>

    <!-- 筛选面板 -->
    <div class="filter-panel" v-if="showFilterPanel">
      <div class="filter-row">
        <div class="filter-item">
          <label>状态</label>
          <select v-model="filters.isRead">
            <option value="">全部</option>
            <option value="false">未读</option>
            <option value="true">已读</option>
          </select>
        </div>
        
        <div class="filter-item" v-if="categoryType === 'system'">
          <label>通知类型</label>
          <select v-model="filters.subType">
            <option value="">全部类型</option>
            <option value="system_maintenance">系统维护</option>
            <option value="version_update">版本更新</option>
            <option value="security_notice">安全公告</option>
            <option value="activity_notice">活动通知</option>
          </select>
        </div>
        
        <div class="filter-item" v-if="categoryType === 'order'">
          <label>订单类型</label>
          <select v-model="filters.subType">
            <option value="">全部类型</option>
            <option value="payment_success">支付成功</option>
            <option value="order_shipped">订单发货</option>
            <option value="order_delivered">订单送达</option>
            <option value="refund_success">退款成功</option>
          </select>
        </div>
        
        <div class="filter-item">
          <label>时间范围</label>
          <select v-model="filters.timeRange">
            <option value="">全部时间</option>
            <option value="today">今天</option>
            <option value="week">最近一周</option>
            <option value="month">最近一月</option>
          </select>
        </div>
        
        <div class="filter-actions">
          <button class="btn btn-primary" @click="applyFilters">应用</button>
          <button class="btn btn-secondary" @click="resetFilters">重置</button>
        </div>
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div class="batch-actions" v-if="selectMode && selectedMessages.length > 0">
      <div class="selected-info">
        已选择 {{ selectedMessages.length }} 条消息
      </div>
      <div class="batch-buttons">
        <button class="batch-btn" @click="batchMarkRead">
          <i class="icon-check"></i>
          标记已读
        </button>
        <button class="batch-btn" @click="batchToggleTop">
          <i class="icon-pin"></i>
          {{ hasTopSelected ? '取消置顶' : '置顶' }}
        </button>
        <button class="batch-btn danger" @click="batchDelete">
          <i class="icon-delete"></i>
          删除
        </button>
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="message-list" ref="messageListRef">
      <div 
        v-for="message in messages" 
        :key="message.id"
        class="message-item"
        :class="{
          'unread': !message.isRead,
          'top': message.isTop,
          'selected': selectedMessages.includes(message.id)
        }"
        @click="handleMessageClick(message)"
      >
        <!-- 选择框 -->
        <div class="message-select" v-if="selectMode">
          <input 
            type="checkbox" 
            :checked="selectedMessages.includes(message.id)"
            @change="toggleMessageSelect(message.id)"
            @click.stop
          >
        </div>

        <!-- 消息内容 -->
        <div class="message-content">
          <!-- 系统通知 -->
          <template v-if="categoryType === 'system' && isSystemMessage(message)">
            <div class="system-message">
              <div class="message-header">
                <div class="message-type">
                  <i :class="getSystemTypeIcon(message.type)"></i>
                  <span>{{ getSystemTypeTitle(message.type) }}</span>
                </div>
                <div class="message-time">{{ formatTime(message.createdAt) }}</div>
              </div>
              <div class="message-title">{{ message.title }}</div>
              <div class="message-summary">{{ message.content }}</div>
              <div class="message-meta" v-if="message.priority">
                <span class="priority" :class="message.priority">{{ getPriorityText(message.priority) }}</span>
              </div>
            </div>
          </template>

          <!-- 订单消息 -->
          <template v-else-if="categoryType === 'order' && isOrderMessage(message)">
            <div class="order-message">
              <div class="message-header">
                <div class="message-type">
                  <i :class="getOrderTypeIcon(message.type)"></i>
                  <span>{{ getOrderTypeTitle(message.type) }}</span>
                </div>
                <div class="message-time">{{ formatTime(message.createdAt) }}</div>
              </div>
              <div class="message-title">{{ message.title }}</div>
              <div class="order-info">
                <div class="order-number">订单号：{{ message.orderNo }}</div>
                <div class="order-amount" v-if="message.amount">金额：¥{{ message.amount }}</div>
              </div>
              <div class="message-summary">{{ message.content }}</div>
            </div>
          </template>

          <!-- 客服消息 -->
          <template v-else-if="categoryType === 'service' && isServiceMessage(message)">
            <div class="service-message">
              <div class="message-header">
                <div class="customer-info">
                  <img 
                    :src="message.customerInfo.customerAvatar || '/default-avatar.png'" 
                    :alt="message.customerInfo.customerName"
                    class="customer-avatar"
                  >
                  <div class="customer-details">
                    <div class="customer-name">{{ message.customerInfo.customerName }}</div>
                    <div class="service-type">{{ getServiceTypeTitle(message.type) }}</div>
                  </div>
                </div>
                <div class="message-time">{{ formatTime(message.createdAt) }}</div>
              </div>
              <div class="message-title">{{ message.title }}</div>
              <div class="message-summary">{{ message.content }}</div>
              <div class="service-status">
                <span class="status" :class="message.status">{{ getStatusText(message.status) }}</span>
                <span class="priority" :class="message.priority">{{ getPriorityText(message.priority) }}</span>
              </div>
            </div>
          </template>

          <!-- 聊天消息 -->
          <template v-else-if="categoryType === 'chat' && isChatMessage(message)">
            <div class="chat-message">
              <div class="message-header">
                <div class="participant-info">
                  <img 
                    :src="message.participantInfo.participantAvatar || '/default-avatar.png'" 
                    :alt="message.participantInfo.participantName"
                    class="participant-avatar"
                  >
                  <div class="participant-details">
                    <div class="participant-name">{{ message.participantInfo.participantName }}</div>
                    <div class="online-status" :class="{ 'online': message.participantInfo.isOnline }">
                      {{ message.participantInfo.isOnline ? '在线' : '离线' }}
                    </div>
                  </div>
                </div>
                <div class="message-time">{{ formatTime(message.createdAt) }}</div>
              </div>
              <div class="last-message" v-if="message.lastMessage">
                <span class="sender">{{ getSenderName(message.lastMessage) }}:</span>
                <span class="content">{{ message.lastMessage.content }}</span>
              </div>
            </div>
          </template>
        </div>

        <!-- 消息状态 -->
        <div class="message-status">
          <div class="status-indicators">
            <i v-if="message.isTop" class="icon-pin top-indicator"></i>
            <i v-if="!message.isRead" class="icon-dot unread-indicator"></i>
          </div>
          <div class="message-actions">
            <button class="action-btn" @click.stop="toggleMessageTop(message)">
              <i :class="message.isTop ? 'icon-unpin' : 'icon-pin'"></i>
            </button>
            <button class="action-btn" @click.stop="markMessageRead(message)" v-if="!message.isRead">
              <i class="icon-check"></i>
            </button>
            <button class="action-btn danger" @click.stop="deleteMessage(message)">
              <i class="icon-delete"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="load-more" v-if="hasMore && !loading">
        <button class="load-more-btn" @click="loadMore">加载更多</button>
      </div>

      <!-- 加载中 -->
      <div class="loading" v-if="loading">
        <i class="icon-loading spinning"></i>
        <span>加载中...</span>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="!loading && messages.length === 0">
        <div class="empty-icon">
          <i class="icon-empty"></i>
        </div>
        <div class="empty-text">暂无消息</div>
        <div class="empty-desc">{{ getEmptyDescription() }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { 
  MessageCategoryType, 
  MessageCategoryItem,
  SystemNotificationMessage,
  OrderNotificationMessage,
  ServiceMessageInfo,
  ChatMessageInfo
} from '../types/message-category'
import { 
  SYSTEM_NOTIFICATION_TYPES,
  ORDER_NOTIFICATION_TYPES,
  SERVICE_MESSAGE_TYPES,
  MESSAGE_PRIORITY_CONFIG,
  MESSAGE_CATEGORY_COLORS,
  MESSAGE_CATEGORY_ICONS
} from '../constants/message-category'

// Props
interface Props {
  categoryType: MessageCategoryType
  categoryTitle: string
  initialMessages?: MessageCategoryItem[]
}

const props = defineProps<Props>()

// Emits
interface Emits {
  back: []
  messageClick: [message: MessageCategoryItem]
  messagesUpdated: [messages: MessageCategoryItem[]]
  batchOperation: [operation: string, messageIds: number[]]
}

const emit = defineEmits<Emits>()

// 响应式数据
const messages = ref<MessageCategoryItem[]>(props.initialMessages || [])
const loading = ref(false)
const selectMode = ref(false)
const selectedMessages = ref<number[]>([])
const showFilterPanel = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const messageListRef = ref<HTMLElement>()

// 筛选条件
const filters = ref({
  isRead: '',
  subType: '',
  timeRange: '',
  keyword: ''
})

// 计算属性
const categoryColor = computed(() => MESSAGE_CATEGORY_COLORS[props.categoryType])
const categoryIcon = computed(() => MESSAGE_CATEGORY_ICONS[props.categoryType])
const totalCount = computed(() => messages.value.length)

const hasTopSelected = computed(() => {
  return selectedMessages.value.some(id => {
    const message = messages.value.find(m => m.id === id)
    return message?.isTop
  })
})

// 方法
const toggleSelectMode = () => {
  selectMode.value = !selectMode.value
  if (!selectMode.value) {
    selectedMessages.value = []
  }
}

const toggleMessageSelect = (messageId: number) => {
  const index = selectedMessages.value.indexOf(messageId)
  if (index > -1) {
    selectedMessages.value.splice(index, 1)
  } else {
    selectedMessages.value.push(messageId)
  }
}

const handleMessageClick = (message: MessageCategoryItem) => {
  if (selectMode.value) {
    toggleMessageSelect(message.id)
  } else {
    emit('messageClick', message)
    // 标记为已读
    if (!message.isRead) {
      markMessageRead(message)
    }
  }
}

const refreshMessages = async () => {
  loading.value = true
  try {
    // 这里应该调用API刷新消息列表
    await new Promise(resolve => setTimeout(resolve, 1000))
    currentPage.value = 1
    // messages.value = await fetchMessages()
  } catch (error) {
    console.error('刷新消息失败:', error)
  } finally {
    loading.value = false
  }
}

const loadMore = async () => {
  if (loading.value || !hasMore.value) return
  
  loading.value = true
  try {
    currentPage.value += 1
    // const newMessages = await fetchMessages(currentPage.value)
    // messages.value.push(...newMessages)
    // hasMore.value = newMessages.length > 0
  } catch (error) {
    console.error('加载更多失败:', error)
  } finally {
    loading.value = false
  }
}

const applyFilters = async () => {
  loading.value = true
  try {
    currentPage.value = 1
    // messages.value = await fetchMessages(1, filters.value)
    showFilterPanel.value = false
  } catch (error) {
    console.error('应用筛选失败:', error)
  } finally {
    loading.value = false
  }
}

const resetFilters = () => {
  filters.value = {
    isRead: '',
    subType: '',
    timeRange: '',
    keyword: ''
  }
  applyFilters()
}

const markMessageRead = async (message: MessageCategoryItem) => {
  try {
    message.isRead = true
    // 调用API标记已读
    console.log('标记消息已读:', message.id)
  } catch (error) {
    console.error('标记已读失败:', error)
  }
}

const toggleMessageTop = async (message: MessageCategoryItem) => {
  try {
    message.isTop = !message.isTop
    // 调用API切换置顶状态
    console.log('切换置顶状态:', message.id, message.isTop)
  } catch (error) {
    console.error('切换置顶失败:', error)
  }
}

const deleteMessage = async (message: MessageCategoryItem) => {
  if (!confirm('确定要删除这条消息吗？')) return
  
  try {
    const index = messages.value.findIndex(m => m.id === message.id)
    if (index > -1) {
      messages.value.splice(index, 1)
    }
    // 调用API删除消息
    console.log('删除消息:', message.id)
  } catch (error) {
    console.error('删除消息失败:', error)
  }
}

const batchMarkRead = async () => {
  try {
    selectedMessages.value.forEach(id => {
      const message = messages.value.find(m => m.id === id)
      if (message) {
        message.isRead = true
      }
    })
    emit('batchOperation', 'markRead', selectedMessages.value)
    selectedMessages.value = []
  } catch (error) {
    console.error('批量标记已读失败:', error)
  }
}

const batchToggleTop = async () => {
  try {
    const isTop = !hasTopSelected.value
    selectedMessages.value.forEach(id => {
      const message = messages.value.find(m => m.id === id)
      if (message) {
        message.isTop = isTop
      }
    })
    emit('batchOperation', 'toggleTop', selectedMessages.value)
    selectedMessages.value = []
  } catch (error) {
    console.error('批量置顶失败:', error)
  }
}

const batchDelete = async () => {
  if (!confirm(`确定要删除选中的 ${selectedMessages.value.length} 条消息吗？`)) return
  
  try {
    const idsToDelete = [...selectedMessages.value]
    messages.value = messages.value.filter(m => !idsToDelete.includes(m.id))
    emit('batchOperation', 'delete', idsToDelete)
    selectedMessages.value = []
  } catch (error) {
    console.error('批量删除失败:', error)
  }
}

// 类型守卫函数
const isSystemMessage = (message: MessageCategoryItem): message is SystemNotificationMessage => {
  return 'priority' in message && 'publishTime' in message
}

const isOrderMessage = (message: MessageCategoryItem): message is OrderNotificationMessage => {
  return 'orderId' in message && 'orderNo' in message
}

const isServiceMessage = (message: MessageCategoryItem): message is ServiceMessageInfo => {
  return 'serviceInfo' in message && 'customerInfo' in message && 'status' in message
}

const isChatMessage = (message: MessageCategoryItem): message is ChatMessageInfo => {
  return 'participantInfo' in message && 'sessionId' in message && !('serviceInfo' in message)
}

// 工具方法
const formatTime = (time: string) => {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

const getSystemTypeIcon = (type: string) => {
  return SYSTEM_NOTIFICATION_TYPES[type as keyof typeof SYSTEM_NOTIFICATION_TYPES]?.icon || 'notification'
}

const getSystemTypeTitle = (type: string) => {
  return SYSTEM_NOTIFICATION_TYPES[type as keyof typeof SYSTEM_NOTIFICATION_TYPES]?.title || '系统通知'
}

const getOrderTypeIcon = (type: string) => {
  return ORDER_NOTIFICATION_TYPES[type as keyof typeof ORDER_NOTIFICATION_TYPES]?.icon || 'shopping-cart'
}

const getOrderTypeTitle = (type: string) => {
  return ORDER_NOTIFICATION_TYPES[type as keyof typeof ORDER_NOTIFICATION_TYPES]?.title || '订单消息'
}

const getServiceTypeTitle = (type: string) => {
  return SERVICE_MESSAGE_TYPES[type as keyof typeof SERVICE_MESSAGE_TYPES]?.title || '客服消息'
}

const getPriorityText = (priority: string) => {
  return MESSAGE_PRIORITY_CONFIG[priority as keyof typeof MESSAGE_PRIORITY_CONFIG]?.title || priority
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    waiting: '等待中',
    active: '进行中',
    closed: '已关闭',
    transferred: '已转接'
  }
  return statusMap[status] || status
}

const getSenderName = (lastMessage: any) => {
  return lastMessage.senderType === 'user' ? '我' : '对方'
}

const getEmptyDescription = () => {
  const descriptions: Record<MessageCategoryType, string> = {
    chat: '暂无聊天消息，开始与好友或商家聊天吧',
    system: '暂无系统通知，系统有重要消息时会在这里显示',
    order: '暂无订单消息，下单后相关消息会在这里显示',
    service: '暂无客服消息，联系客服后相关消息会在这里显示'
  }
  return descriptions[props.categoryType] || '暂无相关消息'
}

// 生命周期
onMounted(() => {
  // 初始化数据
  if (!props.initialMessages?.length) {
    refreshMessages()
  }
})

onUnmounted(() => {
  // 清理工作
})

// 暴露给父组件的方法
defineExpose({
  refreshMessages,
  loadMore,
  getSelectedMessages: () => selectedMessages.value,
  clearSelection: () => {
    selectedMessages.value = []
    selectMode.value = false
  }
})
</script>

<style scoped>
.message-category-detail {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #f5f5f5;
  color: #666;
  cursor: pointer;
  margin-right: 12px;
  transition: all 0.2s;
}

.back-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.category-info {
  display: flex;
  align-items: center;
}

.category-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(24, 144, 255, 0.1);
  margin-right: 12px;
  font-size: 18px;
}

.category-title {
  margin: 0 0 2px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.message-count {
  font-size: 12px;
  color: #8c8c8c;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  border-color: #40a9ff;
  color: #1890ff;
}

.action-btn.active {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 筛选面板 */
.filter-panel {
  padding: 16px 20px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.filter-item select {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  background: #fff;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.btn {
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

.btn-secondary {
  background: #fff;
  color: #666;
}

/* 批量操作栏 */
.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #e6f7ff;
  border-bottom: 1px solid #91d5ff;
}

.selected-info {
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

.batch-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #1890ff;
  border-radius: 4px;
  background: #fff;
  color: #1890ff;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.batch-btn:hover {
  background: #1890ff;
  color: #fff;
}

.batch-btn.danger {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.batch-btn.danger:hover {
  background: #ff4d4f;
  color: #fff;
}

/* 消息列表 */
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.message-item {
  display: flex;
  align-items: flex-start;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.message-item:hover {
  background: #f5f5f5;
}

.message-item.unread {
  background: #f6ffed;
  border-left: 3px solid #52c41a;
}

.message-item.top {
  background: #fff7e6;
  border-left: 3px solid #fa8c16;
}

.message-item.selected {
  background: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.message-select {
  margin-right: 12px;
  padding-top: 2px;
}

.message-content {
  flex: 1;
  min-width: 0;
}

/* 系统消息样式 */
.system-message .message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.message-time {
  font-size: 12px;
  color: #8c8c8c;
}

.message-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
  line-height: 1.4;
}

.message-summary {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.priority {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
}

.priority.urgent {
  background: #fff2f0;
  color: #f5222d;
}

.priority.high {
  background: #fff7e6;
  color: #fa541c;
}

.priority.normal {
  background: #e6f7ff;
  color: #1890ff;
}

.priority.low {
  background: #f6f6f6;
  color: #8c8c8c;
}

/* 订单消息样式 */
.order-info {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.order-number {
  color: #1890ff;
}

.order-amount {
  color: #fa8c16;
  font-weight: 500;
}

/* 客服消息样式 */
.customer-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.customer-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.customer-name {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
}

.service-type {
  font-size: 11px;
  color: #8c8c8c;
}

.service-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
}

.status.waiting {
  background: #fff7e6;
  color: #fa8c16;
}

.status.active {
  background: #f6ffed;
  color: #52c41a;
}

.status.closed {
  background: #f6f6f6;
  color: #8c8c8c;
}

.status.transferred {
  background: #e6f7ff;
  color: #1890ff;
}

/* 聊天消息样式 */
.participant-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.participant-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.participant-name {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
}

.online-status {
  font-size: 11px;
  color: #8c8c8c;
}

.online-status.online {
  color: #52c41a;
}

.last-message {
  margin-top: 8px;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.last-message .sender {
  color: #1890ff;
  margin-right: 4px;
}

/* 消息状态 */
.message-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  margin-left: 12px;
}

.status-indicators {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.top-indicator {
  color: #fa8c16;
  font-size: 12px;
}

.unread-indicator {
  color: #52c41a;
  font-size: 8px;
}

.message-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.message-actions .action-btn {
  width: 24px;
  height: 24px;
  padding: 0;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.message-actions .action-btn:hover {
  border-color: #40a9ff;
  color: #1890ff;
}

.message-actions .action-btn.danger:hover {
  border-color: #ff7875;
  color: #ff4d4f;
}

/* 加载更多 */
.load-more {
  padding: 20px;
  text-align: center;
}

.load-more-btn {
  padding: 8px 24px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
}

.load-more-btn:hover {
  border-color: #40a9ff;
  color: #1890ff;
}

/* 加载中 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #8c8c8c;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  text-align: center;
  line-height: 1.4;
}
</style>