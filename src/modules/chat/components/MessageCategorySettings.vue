<template>
  <div class="message-category-settings">
    <!-- 设置头部 -->
    <div class="settings-header">
      <h3>消息分类设置</h3>
      <button @click="$emit('close')" class="close-btn">
        <i class="icon-close"></i>
      </button>
    </div>

    <!-- 设置内容 -->
    <div class="settings-content">
      <!-- 分类选择 -->
      <div class="category-selector">
        <div class="selector-label">选择分类</div>
        <div class="category-tabs">
          <button 
            v-for="category in categories" 
            :key="category.type"
            @click="selectedCategory = category.type"
            :class="['category-tab', { active: selectedCategory === category.type }]"
          >
            <i :class="category.icon"></i>
            <span>{{ category.title }}</span>
          </button>
        </div>
      </div>

      <!-- 当前分类设置 -->
      <div v-if="currentSettings" class="category-settings">
        <div class="settings-section">
          <h4>基础设置</h4>
          
          <!-- 启用状态 -->
          <div class="setting-item">
            <div class="setting-label">
              <span>启用此分类</span>
              <small>关闭后将不再接收此类消息</small>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input 
                  type="checkbox" 
                  v-model="currentSettings.enabled"
                  @change="updateSettings"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <!-- 通知设置 -->
          <div class="setting-item">
            <div class="setting-label">
              <span>桌面通知</span>
              <small>新消息时显示桌面通知</small>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input 
                  type="checkbox" 
                  v-model="currentSettings.notificationEnabled"
                  @change="updateSettings"
                  :disabled="!currentSettings.enabled"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <!-- 声音提醒 -->
          <div class="setting-item">
            <div class="setting-label">
              <span>声音提醒</span>
              <small>新消息时播放提示音</small>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input 
                  type="checkbox" 
                  v-model="currentSettings.soundEnabled"
                  @change="updateSettings"
                  :disabled="!currentSettings.enabled"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <!-- 震动提醒 -->
          <div class="setting-item">
            <div class="setting-label">
              <span>震动提醒</span>
              <small>新消息时设备震动（移动端）</small>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input 
                  type="checkbox" 
                  v-model="currentSettings.vibrationEnabled"
                  @change="updateSettings"
                  :disabled="!currentSettings.enabled"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <!-- 消息预览 -->
          <div class="setting-item">
            <div class="setting-label">
              <span>消息预览</span>
              <small>通知中显示消息内容预览</small>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input 
                  type="checkbox" 
                  v-model="currentSettings.showPreview"
                  @change="updateSettings"
                  :disabled="!currentSettings.enabled"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>
        </div>

        <div class="settings-section">
          <h4>自动操作</h4>
          
          <!-- 自动标记已读 -->
          <div class="setting-item">
            <div class="setting-label">
              <span>自动标记已读</span>
              <small>查看消息后自动标记为已读</small>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input 
                  type="checkbox" 
                  v-model="currentSettings.autoMarkRead"
                  @change="updateSettings"
                  :disabled="!currentSettings.enabled"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <!-- 自动标记已读延迟 -->
          <div v-if="currentSettings.autoMarkRead" class="setting-item">
            <div class="setting-label">
              <span>标记延迟</span>
              <small>查看后延迟多少秒标记已读</small>
            </div>
            <div class="setting-control">
              <div class="number-input">
                <input 
                  type="number" 
                  v-model.number="currentSettings.autoMarkReadDelay"
                  @change="updateSettings"
                  min="0"
                  max="60"
                  :disabled="!currentSettings.enabled"
                >
                <span class="unit">秒</span>
              </div>
            </div>
          </div>
        </div>

        <div class="settings-section">
          <h4>存储设置</h4>
          
          <!-- 消息保存天数 -->
          <div class="setting-item">
            <div class="setting-label">
              <span>消息保存天数</span>
              <small>超过此天数的消息将被自动清理</small>
            </div>
            <div class="setting-control">
              <div class="number-input">
                <input 
                  type="number" 
                  v-model.number="currentSettings.maxStorageDays"
                  @change="updateSettings"
                  min="1"
                  max="365"
                  :disabled="!currentSettings.enabled"
                >
                <span class="unit">天</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 特殊设置（根据分类类型显示） -->
        <div v-if="selectedCategory === 'system'" class="settings-section">
          <h4>系统通知设置</h4>
          
          <!-- 优先级过滤 -->
          <div class="setting-item">
            <div class="setting-label">
              <span>最低优先级</span>
              <small>只显示此优先级及以上的通知</small>
            </div>
            <div class="setting-control">
              <select 
                v-model="systemSettings.minPriority"
                @change="updateCustomSettings"
                :disabled="!currentSettings.enabled"
              >
                <option value="low">低优先级</option>
                <option value="normal">普通</option>
                <option value="high">高优先级</option>
                <option value="urgent">紧急</option>
              </select>
            </div>
          </div>
        </div>

        <div v-if="selectedCategory === 'order'" class="settings-section">
          <h4>订单消息设置</h4>
          
          <!-- 订单状态过滤 -->
          <div class="setting-item">
            <div class="setting-label">
              <span>关注的订单状态</span>
              <small>只接收选中状态的订单消息</small>
            </div>
            <div class="setting-control">
              <div class="checkbox-group">
                <label v-for="status in orderStatuses" :key="status.value" class="checkbox-item">
                  <input 
                    type="checkbox" 
                    :value="status.value"
                    v-model="orderSettings.watchedStatuses"
                    @change="updateCustomSettings"
                    :disabled="!currentSettings.enabled"
                  >
                  <span>{{ status.label }}</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <div v-if="selectedCategory === 'service'" class="settings-section">
          <h4>客服消息设置</h4>
          
          <!-- 自动回复过滤 -->
          <div class="setting-item">
            <div class="setting-label">
              <span>显示自动回复</span>
              <small>是否显示系统自动回复消息</small>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input 
                  type="checkbox" 
                  v-model="serviceSettings.showAutoReply"
                  @change="updateCustomSettings"
                  :disabled="!currentSettings.enabled"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <!-- 满意度调查 -->
          <div class="setting-item">
            <div class="setting-label">
              <span>满意度调查提醒</span>
              <small>服务结束后提醒进行满意度评价</small>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input 
                  type="checkbox" 
                  v-model="serviceSettings.satisfactionReminder"
                  @change="updateCustomSettings"
                  :disabled="!currentSettings.enabled"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>
        </div>

        <div v-if="selectedCategory === 'chat'" class="settings-section">
          <h4>聊天消息设置</h4>
          
          <!-- 在线状态显示 -->
          <div class="setting-item">
            <div class="setting-label">
              <span>显示在线状态</span>
              <small>显示聊天对象的在线状态</small>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input 
                  type="checkbox" 
                  v-model="chatSettings.showOnlineStatus"
                  @change="updateCustomSettings"
                  :disabled="!currentSettings.enabled"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <!-- 已读回执 -->
          <div class="setting-item">
            <div class="setting-label">
              <span>已读回执</span>
              <small>发送和接收消息已读状态</small>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input 
                  type="checkbox" 
                  v-model="chatSettings.readReceipt"
                  @change="updateCustomSettings"
                  :disabled="!currentSettings.enabled"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置底部 -->
    <div class="settings-footer">
      <button @click="resetToDefault" class="reset-btn">
        恢复默认设置
      </button>
      <div class="footer-actions">
        <button @click="$emit('close')" class="cancel-btn">
          取消
        </button>
        <button @click="saveSettings" class="save-btn" :disabled="saving">
          {{ saving ? '保存中...' : '保存设置' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 消息分类设置组件
 * <AUTHOR>
 * @date 2024-01-20
 * @version 1.0.0
 * @description 用于配置各种消息分类的设置选项，包括通知、自动操作、存储等设置
 */

import { ref, computed, watch, onMounted } from 'vue'
import type { 
  MessageCategorySettings,
  MessageCategory
} from '../types/message-category'
import {
  MessageCategoryType
} from '../types/message-category'

// Props
interface Props {
  categories: MessageCategory[]
  visible: boolean
}

const props = defineProps<Props>()

// Emits
interface Emits {
  close: []
  save: [settings: MessageCategorySettings[]]
}

const emit = defineEmits<Emits>()

// 响应式数据
const selectedCategory = ref<MessageCategoryType>(MessageCategoryType.SYSTEM)
const saving = ref(false)
const settingsMap = ref<Map<MessageCategoryType, MessageCategorySettings>>(new Map())

// 特殊设置
const systemSettings = ref({
  minPriority: 'low' as 'low' | 'normal' | 'high' | 'urgent'
})

const orderSettings = ref({
  watchedStatuses: ['payment_success', 'order_shipped', 'order_delivered'] as string[]
})

const serviceSettings = ref({
  showAutoReply: true,
  satisfactionReminder: true
})

const chatSettings = ref({
  showOnlineStatus: true,
  readReceipt: true
})

// 订单状态选项
const orderStatuses = [
  { value: 'payment_success', label: '支付成功' },
  { value: 'payment_failed', label: '支付失败' },
  { value: 'order_confirmed', label: '订单确认' },
  { value: 'order_shipped', label: '订单发货' },
  { value: 'order_delivered', label: '订单送达' },
  { value: 'order_completed', label: '订单完成' },
  { value: 'order_cancelled', label: '订单取消' },
  { value: 'refund_success', label: '退款成功' }
]

// 计算属性
const currentSettings = computed(() => {
  return settingsMap.value.get(selectedCategory.value)
})

// 监听选中分类变化
watch(selectedCategory, (newCategory) => {
  loadCustomSettings(newCategory)
})

// 组件挂载时初始化
onMounted(() => {
  initializeSettings()
})

/**
 * 初始化设置数据
 */
const initializeSettings = () => {
  props.categories.forEach(category => {
    const defaultSettings: MessageCategorySettings = {
      userId: 0, // 实际使用时从用户信息获取
      categoryType: category.type,
      enabled: category.enabled,
      notificationEnabled: true,
      soundEnabled: true,
      vibrationEnabled: false,
      showPreview: true,
      autoMarkRead: true,
      autoMarkReadDelay: 3,
      maxStorageDays: 30,
      customSettings: {},
      updatedAt: new Date().toISOString()
    }
    settingsMap.value.set(category.type, defaultSettings)
  })
  
  // 加载第一个分类的自定义设置
  if (props.categories.length > 0) {
    selectedCategory.value = props.categories[0].type
    loadCustomSettings(selectedCategory.value)
  }
}

/**
 * 加载自定义设置
 */
const loadCustomSettings = (categoryType: MessageCategoryType) => {
  const settings = settingsMap.value.get(categoryType)
  if (!settings?.customSettings) return

  switch (categoryType) {
    case MessageCategoryType.SYSTEM:
      systemSettings.value = {
        minPriority: settings.customSettings.minPriority || 'low'
      }
      break
    case MessageCategoryType.ORDER:
      orderSettings.value = {
        watchedStatuses: settings.customSettings.watchedStatuses || ['payment_success', 'order_shipped', 'order_delivered']
      }
      break
    case MessageCategoryType.SERVICE:
      serviceSettings.value = {
        showAutoReply: settings.customSettings.showAutoReply ?? true,
        satisfactionReminder: settings.customSettings.satisfactionReminder ?? true
      }
      break
    case MessageCategoryType.CHAT:
      chatSettings.value = {
        showOnlineStatus: settings.customSettings.showOnlineStatus ?? true,
        readReceipt: settings.customSettings.readReceipt ?? true
      }
      break
  }
}

/**
 * 更新基础设置
 */
const updateSettings = () => {
  const settings = settingsMap.value.get(selectedCategory.value)
  if (settings) {
    settings.updatedAt = new Date().toISOString()
  }
}

/**
 * 更新自定义设置
 */
const updateCustomSettings = () => {
  const settings = settingsMap.value.get(selectedCategory.value)
  if (!settings) return

  switch (selectedCategory.value) {
    case MessageCategoryType.SYSTEM:
      settings.customSettings = {
        ...settings.customSettings,
        ...systemSettings.value
      }
      break
    case MessageCategoryType.ORDER:
      settings.customSettings = {
        ...settings.customSettings,
        ...orderSettings.value
      }
      break
    case MessageCategoryType.SERVICE:
      settings.customSettings = {
        ...settings.customSettings,
        ...serviceSettings.value
      }
      break
    case MessageCategoryType.CHAT:
      settings.customSettings = {
        ...settings.customSettings,
        ...chatSettings.value
      }
      break
  }
  
  settings.updatedAt = new Date().toISOString()
}

/**
 * 恢复默认设置
 */
const resetToDefault = () => {
  if (confirm('确定要恢复默认设置吗？这将清除所有自定义配置。')) {
    initializeSettings()
  }
}

/**
 * 保存设置
 */
const saveSettings = async () => {
  saving.value = true
  
  try {
    const allSettings = Array.from(settingsMap.value.values())
    emit('save', allSettings)
    
    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 显示成功提示
    // 这里可以集成全局提示组件
    console.log('设置保存成功')
    
  } catch (error) {
    console.error('保存设置失败:', error)
    // 显示错误提示
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.message-category-settings {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
  background: #fafafa;
}

.settings-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f0f0f0;
  color: #333;
}

.settings-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.category-selector {
  margin-bottom: 24px;
}

.selector-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.category-tabs {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s;
}

.category-tab:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.category-tab.active {
  border-color: #1890ff;
  background: #e6f7ff;
  color: #1890ff;
}

.category-tab i {
  font-size: 16px;
}

.category-settings {
  max-width: 600px;
}

.settings-section {
  margin-bottom: 32px;
}

.settings-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.setting-label {
  flex: 1;
  margin-right: 20px;
}

.setting-label span {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.setting-label small {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.setting-control {
  flex-shrink: 0;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.2s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.2s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #1890ff;
}

input:disabled + .slider {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

/* 数字输入框样式 */
.number-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.number-input input {
  width: 80px;
  padding: 6px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
}

.number-input input:focus {
  outline: none;
  border-color: #1890ff;
}

.number-input input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.number-input .unit {
  font-size: 12px;
  color: #666;
}

/* 选择框样式 */
select {
  padding: 6px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

select:focus {
  outline: none;
  border-color: #1890ff;
}

select:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

/* 复选框组样式 */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.checkbox-item input {
  margin: 0;
}

.checkbox-item input:disabled {
  cursor: not-allowed;
}

.checkbox-item input:disabled + span {
  color: #ccc;
  cursor: not-allowed;
}

.settings-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-top: 1px solid #eee;
  background: #fafafa;
}

.reset-btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.reset-btn:hover {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

.cancel-btn {
  padding: 8px 20px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.cancel-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.save-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  background: #1890ff;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.save-btn:hover:not(:disabled) {
  background: #40a9ff;
}

.save-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>