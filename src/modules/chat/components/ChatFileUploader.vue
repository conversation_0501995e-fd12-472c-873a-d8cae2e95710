<template>
  <div class="chat-file-uploader">
    <!-- 文件上传按钮组 -->
    <div class="upload-buttons">
      <!-- 图片上传按钮 -->
      <el-button
        type="primary"
        size="small"
        :loading="isUploading"
        @click="triggerImageUpload"
        class="upload-btn image-btn"
      >
        <el-icon><Picture /></el-icon>
        <span>图片</span>
      </el-button>

      <!-- 文件上传按钮 -->
      <el-button
        type="primary"
        size="small"
        :loading="isUploading"
        @click="triggerFileUpload"
        class="upload-btn file-btn"
      >
        <el-icon><Document /></el-icon>
        <span>文件</span>
      </el-button>
    </div>

    <!-- 隐藏的文件输入框 -->
    <input
      ref="imageInput"
      type="file"
      accept="image/*"
      multiple
      style="display: none"
      @change="handleImageSelect"
    />
    
    <input
      ref="fileInput"
      type="file"
      :accept="fileAccept"
      multiple
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- 上传预览区域 -->
    <div v-if="uploadQueue.length > 0" class="upload-preview">
      <div class="upload-preview-header">
        <span class="upload-count">待上传文件 ({{ uploadQueue.length }})</span>
        <el-button
          type="text"
          size="small"
          @click="clearQueue"
          class="clear-btn"
        >
          清空
        </el-button>
      </div>
      
      <div class="upload-list">
        <div
          v-for="(item, index) in uploadQueue"
          :key="item.id"
          class="upload-item"
          :class="{ 'is-uploading': item.status === 'uploading' }"
        >
          <!-- 文件预览 -->
          <div class="file-preview">
            <img
              v-if="item.type === 'image' && item.preview"
              :src="item.preview"
              :alt="item.file.name"
              class="image-preview"
            />
            <div v-else class="file-icon">
              <el-icon><Document /></el-icon>
            </div>
          </div>

          <!-- 文件信息 -->
          <div class="file-info">
            <div class="file-name" :title="item.file.name">
              {{ item.file.name }}
            </div>
            <div class="file-size">
              {{ formatFileSize(item.file.size) }}
            </div>
            <div v-if="item.status === 'uploading'" class="upload-progress">
              <el-progress
                :percentage="item.progress"
                :stroke-width="4"
                :show-text="false"
              />
              <span class="progress-text">{{ item.progress }}%</span>
            </div>
            <div v-else-if="item.status === 'error'" class="error-message">
              {{ item.error }}
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="file-actions">
            <el-button
              v-if="item.status === 'pending'"
              type="text"
              size="small"
              @click="removeFromQueue(index)"
            >
              <el-icon><Close /></el-icon>
            </el-button>
            <el-button
              v-else-if="item.status === 'error'"
              type="text"
              size="small"
              @click="retryUpload(index)"
            >
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 上传操作按钮 -->
      <div class="upload-actions">
        <el-button
          type="primary"
          size="small"
          :loading="isUploading"
          :disabled="uploadQueue.length === 0"
          @click="startUpload"
        >
          开始上传
        </el-button>
        <el-button
          size="small"
          @click="clearQueue"
        >
          取消
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Picture, Document, Close, Refresh } from '@element-plus/icons-vue'
import { post } from '@/utils/request'
import { getAutoConfiguredUploadUrl } from '../services/uploadConfig'
import { sendMediaUrlMessageWithData } from '../services/mediaMessageService'

// Props
interface Props {
  sessionId?: string | number
  maxFileSize?: number // KB
  maxImageSize?: number // KB
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  maxFileSize: 10 * 1024, // 10MB
  maxImageSize: 5 * 1024,  // 5MB
  disabled: false
})

// Emits
interface Emits {
  fileUploaded: [data: { type: 'image' | 'file', file: File, result: any, sessionId?: string | number }]
  messageSent: [data: { type: 'image' | 'file', file: File, uploadResult: any, messageResult: any, sessionId: string | number }]
  messageSendError: [data: { type: 'image' | 'file', file: File, uploadResult: any, error: string, sessionId: string | number }]
  uploadError: [error: string]
  uploadStart: []
  uploadComplete: []
}

const emit = defineEmits<Emits>()

// 上传队列项接口
interface UploadItem {
  id: string
  file: File
  type: 'image' | 'file'
  preview?: string
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress: number
  error?: string
  result?: any
}

// 响应式数据
const imageInput = ref<HTMLInputElement>()
const fileInput = ref<HTMLInputElement>()
const uploadQueue = ref<UploadItem[]>([])
const isUploading = ref(false)

// 计算属性
const fileAccept = computed(() => {
  return [
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
    '.txt', '.zip', '.rar', '.7z',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed'
  ].join(',')
})

// 方法
const triggerImageUpload = () => {
  if (props.disabled) return
  imageInput.value?.click()
}

const triggerFileUpload = () => {
  if (props.disabled) return
  fileInput.value?.click()
}

const handleImageSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (!files) return

  Array.from(files).forEach(file => {
    addToQueue(file, 'image')
  })

  // 重置input
  target.value = ''
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (!files) return

  Array.from(files).forEach(file => {
    addToQueue(file, 'file')
  })

  // 重置input
  target.value = ''
}

const addToQueue = async (file: File, type: 'image' | 'file') => {
  // 验证文件大小
  const maxSize = type === 'image' ? props.maxImageSize : props.maxFileSize
  if (file.size > maxSize * 1024) {
    ElMessage.error(`文件大小不能超过 ${formatFileSize(maxSize * 1024)}`)
    return
  }

  // 验证文件类型
  if (type === 'image' && !file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  const item: UploadItem = {
    id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    file,
    type,
    status: 'pending',
    progress: 0
  }

  // 为图片生成预览
  if (type === 'image') {
    try {
      item.preview = await generateImagePreview(file)
    } catch (error) {
      console.warn('生成图片预览失败:', error)
    }
  }

  uploadQueue.value.push(item)
}

const generateImagePreview = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      resolve(e.target?.result as string)
    }
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

const removeFromQueue = (index: number) => {
  uploadQueue.value.splice(index, 1)
}

const clearQueue = () => {
  uploadQueue.value = []
}

const startUpload = async () => {
  if (isUploading.value || uploadQueue.value.length === 0) return

  isUploading.value = true
  emit('uploadStart')

  try {
    for (const item of uploadQueue.value) {
      if (item.status === 'pending' || item.status === 'error') {
        await uploadSingleFile(item)
      }
    }
  } finally {
    isUploading.value = false
    emit('uploadComplete')
  }
}

const uploadSingleFile = async (item: UploadItem) => {
  item.status = 'uploading'
  item.progress = 0
  item.error = undefined

  try {
    // 动态获取上传URL（根据当前layout自动选择）
    const uploadUrl = getAutoConfiguredUploadUrl()

    const formData = new FormData()
    formData.append('file', item.file)
    formData.append('file_usage', item.type === 'image' ? 'chat_image' : 'chat_file')

    if (props.sessionId) {
      formData.append('session_id', props.sessionId.toString())
    }

    console.log(`📤 开始上传文件: ${item.file.name} -> ${uploadUrl}`)

    const response = await post(uploadUrl, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent: any) => {
        if (progressEvent.total) {
          item.progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        }
      }
    })

    item.status = 'success'
    item.progress = 100
    item.result = response

    // 如果有sessionId，自动发送媒体消息
    if (props.sessionId) {
      try {
        console.log(`📨 自动发送媒体消息: ${item.file.name}`)
        console.log('📁 文件上传响应:', response)

        // 从上传响应中提取文件信息
        const uploadData = (response as any).data || response
        if (!uploadData || !uploadData.id) {
          throw new Error('上传响应中缺少文件ID信息')
        }

        // 构造媒体URL消息数据
        const mediaUrlData = {
          resource_id: uploadData.id,                    // 上传后返回的文件ID
          content: uploadData.file_url,                  // 媒体文件URL
          message_type: item.type,                       // 媒体类型 (image/file等)
          file_name: uploadData.file_name,               // 文件名称
          file_size: uploadData.file_size,               // 文件大小
          file_type: uploadData.file_type,               // 文件MIME类型
          file_ext: uploadData.file_ext                  // 文件扩展名
        }

        console.log('📨 发送媒体URL消息数据:', mediaUrlData)

        // 调用媒体URL消息接口
        const messageResponse = await sendMediaUrlMessageWithData(
          props.sessionId,
          mediaUrlData
        )

        console.log('✅ 媒体消息发送成功:', messageResponse)

        // 发送消息成功事件
        emit('messageSent', {
          type: item.type,
          file: item.file,
          uploadResult: response,
          messageResult: messageResponse,
          sessionId: props.sessionId
        })

        ElMessage.success(`${item.type === 'image' ? '图片' : '文件'}发送成功`)

        // 发送成功后，从上传队列中移除该文件
        const itemIndex = uploadQueue.value.findIndex(queueItem => queueItem.id === item.id)
        if (itemIndex !== -1) {
          uploadQueue.value.splice(itemIndex, 1)
          console.log(`📤 文件 ${item.file.name} 发送成功，已从上传队列移除`)

          // 检查是否所有文件都已处理完成
          checkAndHideUploadArea()
        }

      } catch (error: any) {
        console.error('❌ 发送媒体消息失败:', error)
        ElMessage.error(`${item.type === 'image' ? '图片' : '文件'}上传成功，但发送失败: ${error.message}`)

        // 发送失败事件
        emit('messageSendError', {
          type: item.type,
          file: item.file,
          uploadResult: response,
          error: error.message,
          sessionId: props.sessionId
        })
      }
    } else {
      // 没有sessionId时，只触发上传成功事件
      emit('fileUploaded', {
        type: item.type,
        file: item.file,
        result: response,
        sessionId: props.sessionId
      })

      ElMessage.success(`${item.type === 'image' ? '图片' : '文件'}上传成功`)

      // 上传成功后，从上传队列中移除该文件
      const itemIndex = uploadQueue.value.findIndex(queueItem => queueItem.id === item.id)
      if (itemIndex !== -1) {
        uploadQueue.value.splice(itemIndex, 1)
        console.log(`📁 文件 ${item.file.name} 上传成功，已从上传队列移除`)

        // 检查是否所有文件都已处理完成
        checkAndHideUploadArea()
      }
    }

  } catch (error: any) {
    item.status = 'error'
    item.error = error.message || '上传失败'
    
    emit('uploadError', error.message || '上传失败')
    ElMessage.error(`${item.file.name} 上传失败: ${item.error}`)
  }
}

const retryUpload = async (index: number) => {
  const item = uploadQueue.value[index]
  if (item) {
    await uploadSingleFile(item)
  }
}

// 检查并隐藏上传区域
const checkAndHideUploadArea = () => {
  // 如果队列为空，说明所有文件都已处理完成
  if (uploadQueue.value.length === 0) {
    console.log('✅ 所有文件处理完成，上传区域将自动隐藏')
    // 可以在这里添加一个短暂的延迟，让用户看到成功消息
    setTimeout(() => {
      // 上传区域会因为 uploadQueue.length === 0 而自动隐藏
      console.log('🔄 上传区域已隐藏')
    }, 1000)
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 暴露方法
defineExpose({
  clearQueue,
  startUpload
})
</script>

<style scoped lang="scss">
.chat-file-uploader {
  .upload-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;

    .upload-btn {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 12px;

      &.image-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;

        &:hover {
          background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
      }

      &.file-btn {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;

        &:hover {
          background: linear-gradient(135deg, #e081e9 0%, #e3455a 100%);
        }
      }
    }
  }

  .upload-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-top: 12px;

    .upload-preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .upload-count {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .clear-btn {
        color: #666;
        padding: 0;
      }
    }

    .upload-list {
      max-height: 200px;
      overflow-y: auto;
    }

    .upload-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px;
      background: white;
      border-radius: 6px;
      margin-bottom: 8px;
      border: 1px solid #e1e5e9;

      &.is-uploading {
        background: #f0f8ff;
        border-color: #409eff;
      }

      .file-preview {
        flex-shrink: 0;
        width: 40px;
        height: 40px;
        border-radius: 4px;
        overflow: hidden;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;

        .image-preview {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .file-icon {
          color: #666;
          font-size: 20px;
        }
      }

      .file-info {
        flex: 1;
        min-width: 0;

        .file-name {
          font-size: 13px;
          font-weight: 500;
          color: #333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 2px;
        }

        .file-size {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
        }

        .upload-progress {
          display: flex;
          align-items: center;
          gap: 8px;

          .el-progress {
            flex: 1;
          }

          .progress-text {
            font-size: 12px;
            color: #409eff;
            min-width: 35px;
          }
        }

        .error-message {
          font-size: 12px;
          color: #f56c6c;
        }
      }

      .file-actions {
        flex-shrink: 0;

        .el-button {
          padding: 4px;
          min-height: auto;
        }
      }
    }

    .upload-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid #e1e5e9;
    }
  }
}
</style>
