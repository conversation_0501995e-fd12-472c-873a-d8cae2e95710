<template>
  <div class="message-list">
    <!-- 消息列表头部 -->
    <div class="message-list__header">
      <div class="message-list__session-info">
        <div class="message-list__session-avatar">
          <img
            v-if="false"
            src=""
            alt=""
            class="message-list__session-avatar-img"
          />
          <div v-else class="message-list__session-avatar-placeholder">
            {{ getAvatarText('会话') }}
          </div>
        </div>
        
        <div class="message-list__session-details">
          <h3 class="message-list__session-name">
            {{ getSessionName(currentSession) }}
          </h3>
          <p class="message-list__session-status">
            <span
              class="message-list__status-dot"
              :class="`message-list__status-dot--${getSessionStatus(currentSession)}`"
            ></span>
            {{ getStatusText(getSessionStatus(currentSession)) }}
          </p>
        </div>
      </div>
      
      <div class="message-list__actions">
        <!-- 搜索消息 -->
        <button
          class="message-list__action-btn"
          @click="showSearch = !showSearch"
          title="搜索消息"
        >
          <svg class="message-list__icon" viewBox="0 0 24 24">
            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
          </svg>
        </button>
        
        <!-- 消息操作 -->
        <button
          class="message-list__action-btn"
          @click="showMessageActions = !showMessageActions"
          title="消息操作"
        >
          <svg class="message-list__icon" viewBox="0 0 24 24">
            <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- 搜索框 -->
    <div v-if="showSearch" class="message-list__search">
      <input
        v-model="searchQuery"
        type="text"
        placeholder="搜索消息..."
        class="message-list__search-input"
        @input="handleSearch"
      />
    </div>
    
    <!-- 消息操作栏 -->
    <div v-if="showMessageActions" class="message-list__message-actions">
      <button
        class="message-list__message-action"
        @click="handleSelectAll"
        :disabled="currentMessages.length === 0"
      >
        {{ selectedMessages.size === currentMessages.length ? '取消全选' : '全选' }}
      </button>
      
      <button
        class="message-list__message-action"
        @click="handleMarkAsRead"
        :disabled="selectedMessages.size === 0"
      >
        标记已读
      </button>
      
      <button
        class="message-list__message-action message-list__message-action--danger"
        @click="handleDeleteSelected"
        :disabled="selectedMessages.size === 0"
      >
        删除选中
      </button>
    </div>
    
    <!-- 消息内容区域 -->
    <div class="message-list__content" ref="messageContainer">
      <!-- 加载历史消息 -->
      <div v-if="hasMoreMessages" class="message-list__load-more">
        <button
          class="message-list__load-more-btn"
          @click="loadMoreMessages"
          :disabled="isLoadingMore"
        >
          <div v-if="isLoadingMore" class="message-list__spinner"></div>
          {{ isLoadingMore ? '加载中...' : '加载更多消息' }}
        </button>
      </div>
      
      <!-- 消息列表 -->
      <div v-if="isLoading && currentMessages.length === 0" class="message-list__loading">
        <div class="message-list__spinner"></div>
        <p>加载消息中...</p>
      </div>

      <div v-else-if="error" class="message-list__error">
        <p>{{ error }}</p>
        <button class="message-list__retry-btn" @click="loadMessages">
          重试
        </button>
      </div>

      <div v-else-if="currentMessages.length === 0" class="message-list__empty">
        <div class="message-list__empty-icon">
          <svg viewBox="0 0 24 24">
            <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
          </svg>
        </div>
        <p>暂无消息，开始聊天吧！</p>
      </div>
      
      <div v-else class="message-list__messages">
        <!-- 日期分组 -->
        <div
          v-for="(group, date) in groupedMessages"
          :key="date"
          class="message-list__date-group"
        >
          <div class="message-list__date-divider">
            <span class="message-list__date-text">{{ formatDate(date) }}</span>
          </div>
          
          <!-- 消息项 -->
          <div
            v-for="message in group"
            :key="message.id"
            class="message-list__message"
            :class="{
              'message-list__message--own': message.sender_id === currentUserId,
              'message-list__message--selected': selectedMessages.has(message.id.toString()),
              'message-list__message--unread': message.status !== 'read'
            }"
            @click="handleMessageClick(message)"
            @contextmenu.prevent="handleMessageContextMenu(message, $event)"
          >
            <!-- 消息选择框 -->
            <div v-if="showMessageActions" class="message-list__message-checkbox">
              <input
                type="checkbox"
                :checked="selectedMessages.has(message.id.toString())"
                @change="handleMessageSelect(message.id.toString())"
                @click.stop
              />
            </div>
            
            <!-- 消息头像 -->
            <div
              v-if="message.sender_id !== currentUserId"
              class="message-list__message-avatar"
            >
              <img
                v-if="getSenderAvatar(message)"
                :src="getSenderAvatar(message)"
                :alt="getSenderName(message)"
                class="message-list__message-avatar-img"
              />
              <div v-else class="message-list__message-avatar-placeholder">
                {{ getAvatarText(getSenderName(message)) }}
              </div>
            </div>
            
            <!-- 消息内容 -->
            <div class="message-list__message-content">
              <!-- 发送者信息 -->
              <div
                v-if="message.sender_id !== currentUserId"
                class="message-list__message-sender"
              >
                <span class="message-list__sender-name">{{ getSenderName(message) }}</span>
                <span class="message-list__message-time">{{ formatTime(new Date(message.created_at).getTime()) }}</span>
              </div>
              
              <!-- 消息气泡 -->
              <div
                class="message-list__message-bubble"
                :class="{
                  'message-list__message-bubble--own': message.sender_id === currentUserId,
                  'message-list__message-bubble--system': message.type === 'system'
                }"
              >
                <!-- 文本消息 -->
                <div v-if="message.type === 'text'" class="message-list__text-content">
                  <p v-html="formatTextContent(message.content)"></p>
                </div>
                
                <!-- 图片消息 -->
                <div v-else-if="message.type === 'image'" class="message-list__image-content">
                  <img
                    :src="message.content"
                    :alt="message.content || '图片'"
                    class="message-list__image"
                    @click="handleImagePreview(message.content)"
                  />
                </div>
                
                <!-- 文件消息 -->
                <div v-else-if="message.type === 'file'" class="message-list__file-content">
                  <div class="message-list__file-info">
                    <div class="message-list__file-icon">
                      <svg viewBox="0 0 24 24">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                      </svg>
                    </div>
                    <div class="message-list__file-details">
                      <p class="message-list__file-name">{{ (message as any).file_name }}</p>
                      <p class="message-list__file-size">{{ formatFileSize((message as any).file_size) }}</p>
                    </div>
                  </div>
                  <button
                    class="message-list__file-download"
                    @click="handleFileDownload(message)"
                  >
                    下载
                  </button>
                </div>
                
                <!-- 系统消息 -->
                <div v-else-if="message.type === 'system'" class="message-list__system-content">
                  <p>{{ message.content }}</p>
                </div>
                
                <!-- 消息状态 -->
                <div
                  v-if="message.sender_id === currentUserId"
                  class="message-list__message-status"
                >
                  <span class="message-list__message-time">{{ formatTime(new Date(message.created_at).getTime()) }}</span>
                  <div class="message-list__status-indicators">
                    <!-- 发送状态 -->
                    <svg
                      v-if="message.status === 'sending'"
                      class="message-list__status-icon message-list__status-icon--sending"
                      viewBox="0 0 24 24"
                    >
                      <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                        <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416;0 31.416" repeatCount="indefinite"/>
                        <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416;-31.416" repeatCount="indefinite"/>
                      </circle>
                    </svg>
                    
                    <svg
                      v-else-if="message.status === 'sent'"
                      class="message-list__status-icon message-list__status-icon--sent"
                      viewBox="0 0 24 24"
                    >
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                    
                    <svg
                      v-else-if="message.status === 'delivered'"
                      class="message-list__status-icon message-list__status-icon--delivered"
                      viewBox="0 0 24 24"
                    >
                      <path d="M18 7l-1.41-1.41-6.34 6.34 1.41 1.41L18 7zm4.24-1.41L11.66 16.17 7.48 12l-1.41 1.41L11.66 19l12-12-1.42-1.41zM.41 13.41L6 19l1.41-1.41L1.83 12 .41 13.41z"/>
                    </svg>
                    
                    <svg
                      v-else-if="message.status === 'read'"
                      class="message-list__status-icon message-list__status-icon--read"
                      viewBox="0 0 24 24"
                    >
                      <path d="M18 7l-1.41-1.41-6.34 6.34 1.41 1.41L18 7zm4.24-1.41L11.66 16.17 7.48 12l-1.41 1.41L11.66 19l12-12-1.42-1.41zM.41 13.41L6 19l1.41-1.41L1.83 12 .41 13.41z"/>
                    </svg>
                    
                    <svg
                      v-else-if="message.status === 'failed'"
                      class="message-list__status-icon message-list__status-icon--failed"
                      viewBox="0 0 24 24"
                    >
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 自己消息的头像 -->
            <div
              v-if="message.sender_id === currentUserId"
              class="message-list__message-avatar message-list__message-avatar--own"
            >
              <img
                v-if="currentUserAvatar"
                :src="currentUserAvatar"
                :alt="currentUserName"
                class="message-list__message-avatar-img"
              />
              <div v-else class="message-list__message-avatar-placeholder">
                {{ getAvatarText(currentUserName) }}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 正在输入指示器 -->
      <div v-if="typingUsers.size > 0" class="message-list__typing">
        <div class="message-list__typing-avatar">
          <div class="message-list__typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
        <div class="message-list__typing-text">
          {{ getTypingText() }}
        </div>
      </div>
    </div>
    
    <!-- 消息上下文菜单 -->
    <div
      v-if="showContextMenu"
      class="message-list__context-menu"
      :style="{ top: contextMenuY + 'px', left: contextMenuX + 'px' }"
      @click="showContextMenu = false"
    >
      <div class="message-list__context-menu-content" @click.stop>
        <button
          class="message-list__context-menu-item"
          @click="handleReplyMessage"
        >
          <svg class="message-list__context-menu-icon" viewBox="0 0 24 24">
            <path d="M10 9V5l-7 7 7 7v-4.1c5 0 8.5 1.6 11 5.1-1-5-4-10-11-11z"/>
          </svg>
          回复
        </button>
        
        <button
          class="message-list__context-menu-item"
          @click="handleForwardMessage"
        >
          <svg class="message-list__context-menu-icon" viewBox="0 0 24 24">
            <path d="M12 8V4l8 8-8 8v-4H4V8z"/>
          </svg>
          转发
        </button>
        
        <button
          class="message-list__context-menu-item"
          @click="handleCopyMessage"
        >
          <svg class="message-list__context-menu-icon" viewBox="0 0 24 24">
            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
          </svg>
          复制
        </button>
        
        <button
          v-if="selectedMessage?.sender_id === currentUserId"
          class="message-list__context-menu-item"
          @click="handleEditMessage"
        >
          <svg class="message-list__context-menu-icon" viewBox="0 0 24 24">
            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
          </svg>
          编辑
        </button>
        
        <button
          v-if="selectedMessage?.sender_id === currentUserId"
          class="message-list__context-menu-item message-list__context-menu-item--danger"
          @click="handleDeleteMessage"
        >
          <svg class="message-list__context-menu-icon" viewBox="0 0 24 24">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
          </svg>
          删除
        </button>
      </div>
    </div>
    
    <!-- 图片预览 -->
    <div v-if="showImagePreview" class="message-list__image-preview" @click="showImagePreview = false">
      <div class="message-list__image-preview-content" @click.stop>
        <img :src="previewImageUrl" alt="预览图片" class="message-list__preview-image" />
        <button class="message-list__image-preview-close" @click="showImagePreview = false">
          <svg viewBox="0 0 24 24">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useMessageStore, useSessionStore, useChatStore } from '../stores'
import type { Message } from '../types'
import { debounce } from 'lodash-es'

// Props
interface Props {
  sessionId: string | number
  messageCategory?: string
}

const props = defineProps<Props>()

// Emits
interface Emits {
  messageReply: [message: Message]
  messageForward: [message: Message]
  messageEdit: [message: Message]
}

const emit = defineEmits<Emits>()

// Stores
const messageStore = useMessageStore()
const sessionStore = useSessionStore()
const chatStore = useChatStore()

// Store refs
const {
  messages,
  isLoading,
  error,
  typingUsers
} = storeToRefs(messageStore)

const { currentSessionId } = storeToRefs(sessionStore)
const { currentUser } = storeToRefs(chatStore)

// State
const messageContainer = ref<HTMLElement>()
const showSearch = ref(false)
const showMessageActions = ref(false)
const showContextMenu = ref(false)
const showImagePreview = ref(false)
const searchQuery = ref('')
const selectedMessages = ref<Set<string>>(new Set())
const selectedMessage = ref<Message | null>(null)
const contextMenuX = ref(0)
const contextMenuY = ref(0)
const previewImageUrl = ref('')

// Computed
const currentUserId = computed(() => currentUser.value?.id || '')
const currentUserName = computed(() => currentUser.value?.name || '')
const currentUserAvatar = computed(() => currentUser.value?.avatar || '')

// 获取当前会话的消息
const currentMessages = computed(() => {
  const sessionId = props.sessionId?.toString() || currentSessionId.value
  if (!sessionId) return []
  return messages.value.get(sessionId) || []
})

// 获取当前会话
const currentSession = computed(() => {
  const sessionId = props.sessionId?.toString() || currentSessionId.value
  if (!sessionId) return null
  return sessionStore.sessionCache.get(sessionId) || null
})

// 添加缺失的响应式变量
const hasMoreMessages = ref(false)
const isLoadingMore = ref(false)

const groupedMessages = computed(() => {
  const groups: Record<string, Message[]> = {}

  currentMessages.value.forEach(message => {
    const date = new Date(message.created_at).toDateString()
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(message)
  })

  return groups
})

// Methods
const getAvatarText = (name: string) => {
  return name ? name.charAt(0).toUpperCase() : '?'
}

const getSessionName = (session: any) => {
  if (!session) return '选择会话'
  return session.title || `会话 ${session.id}`
}

const getSessionStatus = (session: any) => {
  if (!session) return 'offline'
  return session.status || 'offline'
}

const getSenderName = (message: Message) => {
  return `用户${message.sender_id}`
}

const getSenderAvatar = (_message: Message) => {
  return '' // 暂时返回空字符串，实际应该根据 sender_id 获取头像
}

const getStatusText = (status?: string) => {
  switch (status) {
    case 'active':
      return '在线'
    case 'waiting':
      return '等待中'
    case 'closed':
      return '已关闭'
    case 'archived':
      return '已归档'
    default:
      return '离线'
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  if (date.toDateString() === today.toDateString()) {
    return '今天'
  } else if (date.toDateString() === yesterday.toDateString()) {
    return '昨天'
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'long',
      day: 'numeric'
    })
  }
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatTextContent = (content: string) => {
  // 处理链接
  const urlRegex = /(https?:\/\/[^\s]+)/g
  content = content.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>')
  
  // 处理换行
  content = content.replace(/\n/g, '<br>')
  
  return content
}

const formatFileSize = (size: number) => {
  if (size < 1024) {
    return `${size} B`
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(1)} KB`
  } else if (size < 1024 * 1024 * 1024) {
    return `${(size / (1024 * 1024)).toFixed(1)} MB`
  } else {
    return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`
  }
}

const getTypingText = () => {
  const users = Array.from(typingUsers.value.values()).flat()
  if (users.length === 1) {
    return `用户${users[0]} 正在输入...`
  } else if (users.length === 2) {
    return `用户${users[0]} 和 用户${users[1]} 正在输入...`
  } else if (users.length > 2) {
    return `用户${users[0]} 等 ${users.length} 人正在输入...`
  }
  return ''
}

const handleSearch = debounce((event: Event) => {
  const query = (event.target as HTMLInputElement).value
  if (query.trim()) {
    // 搜索消息的逻辑暂时简化
    console.log('搜索消息:', query)
  } else {
    // 清除搜索结果的逻辑暂时简化
    console.log('清除搜索结果')
  }
}, 300)

const handleSelectAll = () => {
  if (selectedMessages.value.size === currentMessages.value.length) {
    selectedMessages.value.clear()
  } else {
    selectedMessages.value.clear()
    currentMessages.value.forEach(m => selectedMessages.value.add(m.id.toString()))
  }
}

const handleMarkAsRead = async () => {
  try {
    // 批量标记已读的逻辑暂时简化
    console.log('批量标记已读:', Array.from(selectedMessages.value))
    selectedMessages.value.clear()
  } catch (error) {
    console.error('批量标记已读失败:', error)
  }
}

const handleDeleteSelected = async () => {
  if (confirm(`确定要删除选中的 ${selectedMessages.value.size} 条消息吗？`)) {
    try {
      // 批量删除消息的逻辑暂时简化
      console.log('批量删除消息:', Array.from(selectedMessages.value))
      selectedMessages.value.clear()
    } catch (error) {
      console.error('批量删除消息失败:', error)
    }
  }
}

const handleMessageClick = (message: Message) => {
  if (showMessageActions.value) {
    handleMessageSelect(message.id.toString())
  }
}

const handleMessageSelect = (messageId: string) => {
  if (selectedMessages.value.has(messageId)) {
    selectedMessages.value.delete(messageId)
  } else {
    selectedMessages.value.add(messageId)
  }
}

const handleMessageContextMenu = (message: Message, event: MouseEvent) => {
  selectedMessage.value = message
  contextMenuX.value = event.clientX
  contextMenuY.value = event.clientY
  showContextMenu.value = true
}

const handleReplyMessage = () => {
  if (selectedMessage.value) {
    emit('messageReply', selectedMessage.value)
  }
  showContextMenu.value = false
}

const handleForwardMessage = () => {
  if (selectedMessage.value) {
    emit('messageForward', selectedMessage.value)
  }
  showContextMenu.value = false
}

const handleCopyMessage = async () => {
  if (selectedMessage.value && selectedMessage.value.type === 'text') {
    try {
      await navigator.clipboard.writeText(selectedMessage.value.content)
      // TODO: 显示复制成功提示
    } catch (error) {
      console.error('复制消息失败:', error)
    }
  }
  showContextMenu.value = false
}

const handleEditMessage = () => {
  if (selectedMessage.value) {
    emit('messageEdit', selectedMessage.value)
  }
  showContextMenu.value = false
}

const handleDeleteMessage = async () => {
  if (selectedMessage.value && confirm('确定要删除这条消息吗？')) {
    try {
      // 删除消息的逻辑暂时简化
      console.log('删除消息:', selectedMessage.value.id)
    } catch (error) {
      console.error('删除消息失败:', error)
    }
  }
  showContextMenu.value = false
}

const handleImagePreview = (imageUrl: string) => {
  previewImageUrl.value = imageUrl
  showImagePreview.value = true
}

const handleFileDownload = async (message: Message) => {
  try {
    // TODO: 实现文件下载
    console.log('下载文件:', message)
  } catch (error) {
    console.error('下载文件失败:', error)
  }
}

const loadMessages = async () => {
  const sessionId = props.sessionId?.toString() || currentSessionId.value
  if (!sessionId) return

  try {
    console.log('加载消息:', sessionId, '分类:', props.messageCategory)
    // 🔧 修复竞态问题：传递消息分类参数，确保消息加载到正确的多维数组位置
    await messageStore.loadMessages(sessionId, props.messageCategory)
    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('加载消息失败:', error)
  }
}

const loadMoreMessages = async () => {
  const sessionId = props.sessionId?.toString() || currentSessionId.value
  if (!sessionId) return

  try {
    const oldScrollHeight = messageContainer.value?.scrollHeight || 0
    console.log('加载更多消息:', sessionId)
    // 这里可以实现分页加载更多消息的逻辑
    await nextTick()

    // 保持滚动位置
    if (messageContainer.value) {
      const newScrollHeight = messageContainer.value.scrollHeight
      messageContainer.value.scrollTop = newScrollHeight - oldScrollHeight
    }
  } catch (error) {
    console.error('加载更多消息失败:', error)
  }
}

const scrollToBottom = (smooth = false) => {
  if (messageContainer.value) {
    messageContainer.value.scrollTo({
      top: messageContainer.value.scrollHeight,
      behavior: smooth ? 'smooth' : 'auto'
    })
  }
}

const handleScroll = () => {
  if (!messageContainer.value) return
  
  const { scrollTop } = messageContainer.value
  
  // 滚动到顶部时加载更多消息
  if (scrollTop === 0 && hasMoreMessages.value && !isLoadingMore.value) {
    loadMoreMessages()
  }
}

const handleClickOutside = (_event: Event) => {
  if (showContextMenu.value) {
    showContextMenu.value = false
  }
}

// Lifecycle
onMounted(() => {
  if (currentSession.value) {
    loadMessages()
  }
  
  messageContainer.value?.addEventListener('scroll', handleScroll)
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  messageContainer.value?.removeEventListener('scroll', handleScroll)
  document.removeEventListener('click', handleClickOutside)
})

// Watch
watch(
  () => props.sessionId,
  (newSessionId, oldSessionId) => {
    if (newSessionId && newSessionId !== oldSessionId) {
      selectedMessages.value.clear()
      showMessageActions.value = false
      loadMessages()
    }
  },
  { immediate: true }
)

// 也监听store中的currentSessionId变化
watch(
  () => currentSessionId.value,
  (newSessionId, oldSessionId) => {
    if (newSessionId && newSessionId !== oldSessionId && !props.sessionId) {
      selectedMessages.value.clear()
      showMessageActions.value = false
      loadMessages()
    }
  }
)

watch(
  () => currentMessages.value.length,
  (newLength, oldLength) => {
    if (newLength > oldLength) {
      nextTick(() => {
        scrollToBottom(true)
      })
    }
  }
)

// 暴露方法给父组件
defineExpose({
  scrollToBottom,
  loadMessages,
  loadMoreMessages
})
</script>

<style scoped>
.message-list {
  @apply h-full flex flex-col bg-white;
}

.message-list__header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50;
}

.message-list__session-info {
  @apply flex items-center;
}

.message-list__session-avatar {
  @apply w-10 h-10 rounded-full overflow-hidden mr-3;
}

.message-list__session-avatar-img {
  @apply w-full h-full object-cover;
}

.message-list__session-avatar-placeholder {
  @apply w-full h-full bg-blue-500 text-white flex items-center justify-center font-semibold;
}

.message-list__session-details {
  @apply flex flex-col;
}

.message-list__session-name {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.message-list__session-status {
  @apply flex items-center text-sm text-gray-600 m-0;
}

.message-list__status-dot {
  @apply w-2 h-2 rounded-full mr-2;
}

.message-list__status-dot--active {
  @apply bg-green-500;
}

.message-list__status-dot--waiting {
  @apply bg-yellow-500;
}

.message-list__status-dot--closed {
  @apply bg-gray-400;
}

.message-list__status-dot--archived {
  @apply bg-gray-300;
}

.message-list__status-dot--offline {
  @apply bg-gray-400;
}

.message-list__actions {
  @apply flex items-center space-x-2;
}

.message-list__action-btn {
  @apply p-2 rounded-md hover:bg-gray-200 transition-colors;
}

.message-list__icon {
  @apply w-5 h-5 fill-current text-gray-600;
}

.message-list__search {
  @apply p-4 border-b border-gray-200;
}

.message-list__search-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.message-list__message-actions {
  @apply flex items-center space-x-2 p-4 border-b border-gray-200 bg-blue-50;
}

.message-list__message-action {
  @apply px-3 py-1 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.message-list__message-action--danger {
  @apply text-red-600 border-red-300 hover:bg-red-50;
}

.message-list__content {
  @apply flex-1 overflow-y-auto p-4;
}

.message-list__loading {
  @apply flex flex-col items-center justify-center p-8;
}

.message-list__spinner {
  @apply w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mb-4;
}

.message-list__error {
  @apply flex flex-col items-center justify-center p-8 text-center;
}

.message-list__retry-btn {
  @apply mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors;
}

.message-list__empty {
  @apply flex flex-col items-center justify-center p-8 text-center;
}

.message-list__empty-icon {
  @apply w-16 h-16 text-gray-400 mb-4;
}

.message-list__empty-icon svg {
  @apply w-full h-full fill-current;
}

.message-list__load-more {
  @apply text-center mb-4;
}

.message-list__load-more-btn {
  @apply px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2;
}

.message-list__messages {
  @apply space-y-6;
}

.message-list__date-group {
  @apply space-y-4;
}

.message-list__date-divider {
  @apply flex items-center justify-center;
}

.message-list__date-text {
  @apply px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-full;
}

.message-list__message {
  @apply flex items-start space-x-3 group;
}

.message-list__message--own {
  @apply flex-row-reverse space-x-reverse;
}

.message-list__message--selected {
  @apply bg-blue-50 rounded-lg p-2 -m-2;
}

.message-list__message--unread {
  @apply relative;
}

.message-list__message--unread::before {
  @apply absolute -left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-500 rounded-full;
  content: '';
}

.message-list__message-checkbox {
  @apply flex items-center;
}

.message-list__message-avatar {
  @apply w-8 h-8 rounded-full overflow-hidden flex-shrink-0;
}

.message-list__message-avatar--own {
  @apply order-last;
}

.message-list__message-avatar-img {
  @apply w-full h-full object-cover;
}

.message-list__message-avatar-placeholder {
  @apply w-full h-full bg-gray-400 text-white flex items-center justify-center text-sm font-semibold;
}

.message-list__message-content {
  @apply flex-1 min-w-0;
}

.message-list__message-sender {
  @apply flex items-center space-x-2 mb-1;
}

.message-list__sender-name {
  @apply text-sm font-semibold text-gray-900;
}

.message-list__message-time {
  @apply text-xs text-gray-500;
}

.message-list__message-bubble {
  @apply max-w-xs lg:max-w-md px-4 py-2 rounded-lg;
}

.message-list__message-bubble--own {
  @apply bg-blue-500 text-white ml-auto;
}

.message-list__message-bubble:not(.message-list__message-bubble--own):not(.message-list__message-bubble--system) {
  @apply bg-gray-100 text-gray-900;
}

.message-list__message-bubble--system {
  @apply bg-yellow-100 text-yellow-800 text-center;
}

.message-list__text-content p {
  @apply m-0 break-words;
}

.message-list__text-content a {
  @apply underline;
}

.message-list__image-content {
  @apply p-0;
}

.message-list__image {
  @apply max-w-full h-auto rounded-lg cursor-pointer hover:opacity-90 transition-opacity;
}

.message-list__file-content {
  @apply flex items-center justify-between space-x-3;
}

.message-list__file-info {
  @apply flex items-center space-x-3;
}

.message-list__file-icon {
  @apply w-8 h-8 text-gray-600;
}

.message-list__file-icon svg {
  @apply w-full h-full fill-current;
}

.message-list__file-details {
  @apply flex flex-col;
}

.message-list__file-name {
  @apply text-sm font-semibold m-0;
}

.message-list__file-size {
  @apply text-xs text-gray-500 m-0;
}

.message-list__file-download {
  @apply px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors;
}

.message-list__system-content p {
  @apply m-0 text-sm;
}

.message-list__message-status {
  @apply flex items-center justify-end space-x-2 mt-1;
}

.message-list__status-indicators {
  @apply flex items-center space-x-1;
}

.message-list__status-icon {
  @apply w-4 h-4;
}

.message-list__status-icon--sending {
  @apply text-gray-400;
}

.message-list__status-icon--sent {
  @apply text-gray-500;
}

.message-list__status-icon--delivered {
  @apply text-blue-500;
}

.message-list__status-icon--read {
  @apply text-blue-600;
}

.message-list__status-icon--failed {
  @apply text-red-500;
}

.message-list__typing {
  @apply flex items-center space-x-3 mt-4;
}

.message-list__typing-avatar {
  @apply w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center;
}

.message-list__typing-dots {
  @apply flex space-x-1;
}

.message-list__typing-dots span {
  @apply w-2 h-2 bg-gray-400 rounded-full animate-pulse;
}

.message-list__typing-dots span:nth-child(1) {
  animation-delay: 0s;
}

.message-list__typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.message-list__typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

.message-list__typing-text {
  @apply text-sm text-gray-600;
}

.message-list__context-menu {
  @apply fixed inset-0 z-50;
}

.message-list__context-menu-content {
  @apply absolute bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-[150px];
}

.message-list__context-menu-item {
  @apply w-full flex items-center px-4 py-2 text-left hover:bg-gray-100 transition-colors;
}

.message-list__context-menu-item--danger {
  @apply text-red-600 hover:bg-red-50;
}

.message-list__context-menu-icon {
  @apply w-4 h-4 fill-current mr-3;
}

.message-list__image-preview {
  @apply fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50;
}

.message-list__image-preview-content {
  @apply relative max-w-4xl max-h-4xl;
}

.message-list__preview-image {
  @apply max-w-full max-h-full object-contain;
}

.message-list__image-preview-close {
  @apply absolute top-4 right-4 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-colors;
}

.message-list__image-preview-close svg {
  @apply w-6 h-6 fill-current;
}
</style>