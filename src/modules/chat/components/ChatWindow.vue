<template>
  <div class="chat-window" :class="{ 'chat-window--minimized': isMinimized }">
    <!-- 聊天窗口头部 -->
    <div class="chat-window__header">
      <div class="chat-window__header-left">
        <div class="chat-window__avatar">
          <img
            v-if="getSessionAvatar(currentSession)"
            :src="getSessionAvatar(currentSession)"
            :alt="getSessionName(currentSession)"
            class="chat-window__avatar-img"
          />
          <div v-else class="chat-window__avatar-placeholder">
            {{ getAvatarText(getSessionName(currentSession)) }}
          </div>
        </div>
        <div class="chat-window__info">
          <h3 class="chat-window__title">
            {{ getSessionName(currentSession) || '选择会话' }}
          </h3>
          <p class="chat-window__status">
            <span
              class="chat-window__status-dot"
              :class="`chat-window__status-dot--${getSessionStatus()}`"
            ></span>
            {{ getSessionStatusText() }}
          </p>
          <p class="chat-window__connection-status">
            <span
              class="chat-window__connection-dot"
              :class="`chat-window__connection-dot--${connectionStatusBuffer}`"
            ></span>
            {{ getConnectionStatusText() }}
          </p>
        </div>
      </div>
      
      <div class="chat-window__header-right">
        <!-- 切换会话列表按钮 -->
        <button
          class="chat-window__action-btn"
          @click="toggleSessionList"
          :title="showSessionList ? '隐藏会话列表' : '显示会话列表'"
        >
          <svg class="chat-window__icon" viewBox="0 0 24 24">
            <path v-if="showSessionList" d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
            <path v-else d="M3 18h13v-2H3v2zm0-5h10v-2H3v2zm0-7v2h13V6H3zm18 9.59L17.42 12 21 8.42 19.58 7l-5 5 5 5L21 15.59z"/>
          </svg>
        </button>

        <!-- 会话操作按钮 -->
        <button
          v-if="currentSession"
          class="chat-window__action-btn"
          @click="showSessionInfo = !showSessionInfo"
          title="会话信息"
        >
          <svg class="chat-window__icon" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
          </svg>
        </button>
        
        <!-- 最小化/最大化按钮 -->
        <button
          class="chat-window__action-btn"
          @click="toggleMinimize"
          :title="isMinimized ? '展开' : '最小化'"
        >
          <svg class="chat-window__icon" viewBox="0 0 24 24">
            <path v-if="isMinimized" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            <path v-else d="M19 13H5v-2h14v2z"/>
          </svg>
        </button>
        
        <!-- 关闭按钮 -->
        <button
          class="chat-window__action-btn chat-window__action-btn--close"
          @click="$emit('close')"
          title="关闭"
        >
          <svg class="chat-window__icon" viewBox="0 0 24 24">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- 聊天窗口主体 -->
    <div v-show="!isMinimized" class="chat-window__body">
      <!-- 消息分类按钮区域 - 顶部水平排列 -->
      <div class="chat-window__category-tabs">
        <button
          v-for="category in messageCategories"
          :key="category.type"
          class="category-tab"
          :class="{
            'category-tab--active': currentMessageCategory === category.type,
            [`category-tab--${category.type}`]: true
          }"
          @click="handleCategoryChange(category.type)"
        >
          <div class="category-icon">
            <component :is="getCategoryIcon(category.icon)" />
          </div>
          <span class="category-title">{{ category.title }}</span>
          <span v-if="category.unreadCount > 0" class="category-badge">
            {{ category.unreadCount }}
          </span>
        </button>

      </div>

      <!-- 主体内容区域 -->
      <div class="chat-window__main">
        <!-- 会话列表 - 系统通知和订单分类不显示会话列表 -->
        <div v-if="showSessionList && currentMessageCategory !== MessageCategoryType.SYSTEM && currentMessageCategory !== MessageCategoryType.ORDER" class="chat-window__sidebar">
          <div class="category-session-list">
            <div class="category-header">
              <h4>{{ getCurrentCategoryTitle() }}</h4>
              <span class="session-count">{{ filteredSessions.length }} 个会话</span>
            </div>
            <div class="session-items">
              <div
                v-for="session in filteredSessions"
                :key="session.id"
                class="session-item"
                :class="{
                  'session-item--active': currentSessionId === session.id.toString(),
                  'session-item--unread': session.unread_count > 0
                }"
                @click="handleSessionSelect(session)"
              >
                <div class="session-avatar">
                  <img v-if="getSessionAvatar(session)" :src="getSessionAvatar(session)" :alt="session.title" />
                  <div v-else class="avatar-placeholder">
                    {{ getAvatarText(session.title || session.id.toString()) }}
                  </div>
                  <!-- 在线状态指示器 -->
                  <div class="session-status-dot"
                       :class="`session-status-dot--${getSessionOnlineStatus(session)}`">
                  </div>
                </div>
                <div class="session-info">
                  <div class="session-title">
                    {{ session.title || `会话 ${session.id}` }}
                    <!-- <span v-if="session.is_typing" class="typing-indicator">正在输入...</span> -->
                  </div>
                  <div class="session-preview">{{ getLastMessagePreview(session) }}</div>
                  <div class="session-time">{{ formatTime(session.updated_at || session.last_message_time) }}</div>
                </div>
                <div class="session-status">
                  <div v-if="session.unread_count > 0" class="unread-badge">
                    {{ session.unread_count > 99 ? '99+' : session.unread_count }}
                  </div>
                </div>
              </div>
            </div>
            <div v-if="filteredSessions.length === 0" class="empty-sessions">
              <div class="empty-icon">
                <component :is="getCategoryIcon(getCurrentCategoryIcon())" />
              </div>
              <p>暂无{{ getCurrentCategoryTitle() }}</p>
            </div>
          </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-window__content">


          <!-- 简化的聊天界面：直接根据currentSessionId显示 -->
          <div v-if="!currentSessionId" class="chat-window__empty">
            <div class="chat-window__empty-icon">
              <svg viewBox="0 0 24 24">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
              </svg>
            </div>
            <h3 v-if="currentMessageCategory === MessageCategoryType.SYSTEM">系统通知</h3>
            <h3 v-else>开始聊天</h3>
            <p v-if="currentMessageCategory === MessageCategoryType.SYSTEM">暂无系统通知消息</p>
            <p v-else>选择一个会话或创建新的会话开始聊天</p>
            <button v-if="currentMessageCategory !== MessageCategoryType.SYSTEM" class="chat-window__start-btn" @click="handleSessionCreate">
              创建新会话
            </button>
          </div>

          <!-- 聊天界面：当有currentSessionId时显示 -->
          <div v-else class="chat-window__chat">
            <!-- 消息列表区域 -->
            <div ref="messageContainer" class="message-list-container">
              <div v-if="isLoadingMessages" class="loading-messages">
                <p>加载消息中...</p>
              </div>
              <div v-else-if="currentSessionMessages.length === 0" class="empty-messages">
                <p>暂无消息，开始聊天吧！</p>
              </div>
              <div v-else class="messages-list">
                <!-- 加载更多历史消息指示器 -->
                <div v-if="isLoadingMore" class="loading-more-messages">
                  <div class="loading-spinner"></div>
                  <span>加载更多消息中...</span>
                </div>
                <div v-else-if="!hasMoreMessages && currentSessionMessages.length > 0" class="no-more-messages">
                  <span>没有更多消息了</span>
                </div>

                <div
                  v-for="message in currentSessionMessages"
                  :key="message.id"
                  class="message-item"
                  :class="{
                    'message-own': isOwnMessage(message),
                    'message-other': !isOwnMessage(message)
                  }"
                >
                  <!-- 头像区域 - 左侧显示对方头像 -->
                  <div v-if="!isOwnMessage(message)" class="message-avatar message-avatar--left">
                    <img
                      v-if="message.sender_avatar"
                      :src="message.sender_avatar"
                      :alt="message.sender_name"
                      class="avatar-image"
                      @error="handleAvatarError"
                    />
                    <div
                      v-else
                      class="avatar-placeholder"
                      :class="{
                        'avatar-merchant': message.sender_type === 'merchant',
                        'avatar-user': message.sender_type === 'user'
                      }"
                    >
                      {{ getAvatarText(message.sender_name) }}
                    </div>
                  </div>

                  <!-- 消息内容区域 -->
                  <div class="message-bubble" :class="{
                    'message-bubble--own': isOwnMessage(message),
                    'message-bubble--other': !isOwnMessage(message)
                  }">
                    <!-- 消息头部 - 显示发送者名字和时间 -->
                    <div class="message-header" :class="{
                      'message-header--own': isOwnMessage(message),
                      'message-header--other': !isOwnMessage(message)
                    }">
                      <span v-if="!isOwnMessage(message)" class="message-sender">{{ message.sender_name }}</span>
                      <span class="message-time">{{ formatMessageTime(message.created_at) }}</span>
                      <span v-if="isOwnMessage(message)" class="message-sender message-sender--own">{{ getCurrentUserDisplayName(message) }}</span>
                    </div>

                    <!-- 文件消息 -->
                    <div v-if="message.type === 'image' || message.type === 'file'" class="message-file">
                      <ChatFileMessage
                        :file-data="adaptMessageToFileData(message)"
                        :is-sender="isOwnMessage(message)"
                      />
                    </div>

                    <!-- 文本消息 -->
                    <div v-else class="message-text">
                      {{ message.content }}
                      <!-- 订单/退款消息跳转图标 -->
                      <button
                        v-if="shouldShowJumpIcon(message)"
                        class="order-jump-icon"
                        @click="handleOrderJump(message)"
                        :title="isOrderMessage(message) ? '查看订单详情' : '查看退款详情'"
                      >
                        <el-icon class="jump-icon">
                          <Connection />
                        </el-icon>
                      </button>
                    </div>
                    <div
                      v-if="message.status === 0"
                      class="message-status sending"
                      title="发送中"
                    >
                      <svg class="status-icon" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                        <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2"/>
                      </svg>
                    </div>
                    <div
                      v-else-if="message.status === 1"
                      class="message-status sent"
                      title="已发送"
                    >
                      <svg class="status-icon" viewBox="0 0 24 24">
                        <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" fill="none"/>
                      </svg>
                    </div>
                  </div>

                  <!-- 头像区域 - 右侧显示自己的头像 -->
                  <div v-if="isOwnMessage(message)" class="message-avatar message-avatar--right">
                    <img
                      v-if="getCurrentUserAvatar(message) && getCurrentUserAvatar(message) !== '/images/default_avatar.png'"
                      :src="getCurrentUserAvatar(message)"
                      :alt="getCurrentUserDisplayName(message)"
                      class="avatar-image"
                      @error="handleAvatarError"
                    />
                    <div
                      v-else
                      class="avatar-placeholder"
                      :class="{
                        'avatar-merchant': currentUserType === 'merchant',
                        'avatar-user': currentUserType === 'user'
                      }"
                    >
                      {{ getAvatarText(getCurrentUserDisplayName(message)) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 文件上传区域 -->
            <div v-if="!shouldHideInputArea" class="file-upload-container">
              <ChatFileUploader
                :session-id="currentSessionId || ''"
                :disabled="!currentSessionId"
                @file-uploaded="handleFileUploaded"
                @message-sent="handleMediaMessageSent"
                @message-send-error="handleMediaMessageError"
                @upload-error="handleUploadError"
                @upload-start="handleUploadStart"
                @upload-complete="handleUploadComplete"
              />
            </div>

            <!-- 输入区域 -->
            <div v-if="!shouldHideInputArea" class="message-input-container">
              <div class="input-wrapper">
                <textarea
                  v-model="messageInputText"
                  placeholder="输入消息..."
                  class="message-textarea"
                  @keydown.enter.prevent="handleSendMessage"
                  rows="1"
                ></textarea>
                <button
                  @click="handleSendMessage"
                  :disabled="!messageInputText.trim()"
                  class="send-button"
                >
                  发送
                </button>
              </div>
            </div>
            
            <!-- 系统通知和订单消息的提示信息 -->
            <div v-if="shouldHideInputArea" class="input-disabled-notice">
              <div class="notice-content">
                <span class="notice-icon">ℹ️</span>
                <span class="notice-text">
                  {{ currentSession?.type === SessionType.SYSTEM_NOTIFICATION || (currentSession?.type as any) === 'system_notification' ? '系统通知为只读消息' : '订单消息为只读消息' }}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 会话信息面板 -->
        <div v-if="showSessionInfo && currentSession" class="chat-window__info-panel">
          <SessionInfo
            :session="currentSession"
            @session-close="handleSessionClose"
          />
        </div>
      </div>
    </div>
    
    <!-- 通知区域 -->
    <NotificationToast />
    
    <!-- 文件上传进度 -->
    <FileUploadProgress v-if="showUploadProgress" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick, inject } from 'vue'
import { storeToRefs } from 'pinia'
import {
  useChatStore,
  useSessionStore,
  useMessageStore,
  useFileStore
} from '../stores'
import type { Session, Message } from '../types'
import { SessionType, SessionStatus /* , SessionPriority */ } from '../types'
import {
  MessageCategoryType
} from '../types/message-category'
import type {
  MessageCategoryDTO,
  UnreadCountDTO
} from '../types/message-category'
import MessageCategoryService from '../services/message-category.service'
import {
  ChatDotRound,
  Bell,
  ShoppingBag,
  Service,
  Connection
} from '@element-plus/icons-vue'
import MessageList from './MessageList.vue'
import SessionInfo from './SessionInfo.vue'
import NotificationToast from './NotificationToast.vue'
import FileUploadProgress from './FileUploadProgress.vue'
import ChatFileUploader from './ChatFileUploader.vue'
import ChatFileMessage from './ChatFileMessage.vue'
import { chatFileService } from '../services/chatFileService'


// Props
interface Props {
  showSessionList?: boolean
  defaultMinimized?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showSessionList: true,
  defaultMinimized: false
})

// Emits
interface Emits {
  close: []
  sessionSelect: [session: Session]
  messageSend: [message: Message]
}

const emit = defineEmits<Emits>()

// Stores
const chatStore = useChatStore()
const sessionStore = useSessionStore()
const fileStore = useFileStore()

// 注入chatStore（统一聊天服务）
const injectedChatStore = inject('chatStore', null)

// 获取实际的聊天服务实例
const getWebSocketService = () => {
  // 优先使用注入的chatStore
  if (injectedChatStore) {
    return injectedChatStore
  }
  
  // 回退到本地chatStore
  return chatStore
}

// 调试用的强制状态变量
const forceShowChat = ref(false)
const forceHideEmpty = ref(false)

// Store refs
const { sessions, currentSessionId } = storeToRefs(sessionStore)
const { uploadQueue } = storeToRefs(fileStore)

// 连接状态缓冲，避免短暂断开时的UI闪烁
const connectionStatusBuffer = ref<'connected' | 'connecting' | 'disconnected' | 'reconnecting'>('disconnected')
const connectionStatusTimer = ref<number | null>(null)
const isInitializing = ref(true) // 标记组件是否正在初始化

// 使用WebSocket服务的连接状态，如果不可用则回退到chatStore
const isConnected = computed(() => {
  const wsService = getWebSocketService()
  if (wsService) {
    return wsService.isConnected || false
  }
  return chatStore.isConnected
})

// 获取实际的WebSocket状态
const getActualWebSocketStatus = (): string => {
  // 优先使用WebSocket服务的状态
  const wsService = getWebSocketService()
  if (wsService) {
    const serviceStatus = wsService.clientStatus
    const serviceConnected = wsService.isConnected
    const wsReadyState = null // chatStore不提供readyState

    console.log('ChatWindow: WebSocket服务状态检查:', {
      hasService: !!wsService,
      serviceStatus,
      serviceConnected,
      wsReadyState,
      wsReadyStateText: wsReadyState === WebSocket.OPEN ? 'OPEN' :
                       wsReadyState === WebSocket.CONNECTING ? 'CONNECTING' :
                       wsReadyState === WebSocket.CLOSING ? 'CLOSING' :
                       wsReadyState === WebSocket.CLOSED ? 'CLOSED' : 'UNKNOWN'
    })

    // 如果WebSocket实际状态是OPEN，但服务状态不是connected，以实际状态为准
    if (wsReadyState === WebSocket.OPEN && serviceStatus !== 'connected') {
      console.log('ChatWindow: WebSocket实际已连接，但服务状态不正确，返回connected')
      return 'connected'
    }

    // 如果服务状态存在，直接使用
    if (serviceStatus) {
      return serviceStatus
    }

    // 如果没有状态但有连接标志，根据连接标志判断
    if (typeof serviceConnected === 'boolean') {
      return serviceConnected ? 'connected' : 'disconnected'
    }
  }

  // 回退到chatStore的状态
  console.log('ChatWindow: 回退到chatStore状态:', isConnected.value)
  return isConnected.value ? 'connected' : 'disconnected'
}

// 更新缓冲状态的函数
const updateConnectionStatusBuffer = (newStatus: string) => {
  console.log('ChatWindow: 更新连接状态缓冲:', newStatus, '当前缓冲状态:', connectionStatusBuffer.value, '初始化中:', isInitializing.value)

  // 清除之前的定时器
  if (connectionStatusTimer.value) {
    clearTimeout(connectionStatusTimer.value)
    connectionStatusTimer.value = null
  }

  // 如果是连接状态，立即更新
  if (newStatus === 'connected') {
    connectionStatusBuffer.value = 'connected'
    console.log('ChatWindow: 立即更新为已连接状态')
    return
  }

  // 如果是连接中或重连中，立即更新
  if (newStatus === 'connecting' || newStatus === 'reconnecting') {
    connectionStatusBuffer.value = newStatus as any
    console.log('ChatWindow: 立即更新为连接中状态:', newStatus)
    return
  }

  // 如果是断开状态，需要特殊处理
  if (newStatus === 'disconnected') {
    // 如果组件正在初始化，延迟更新以等待WebSocket服务状态稳定
    const delayTime = isInitializing.value ? 1000 : 500

    connectionStatusTimer.value = setTimeout(() => {
      // 再次检查状态，如果仍然是断开状态才更新
      const currentStatus = getActualWebSocketStatus()
      console.log('ChatWindow: 延迟检查状态结果:', currentStatus, '延迟时间:', delayTime)

      if (currentStatus === 'disconnected') {
        connectionStatusBuffer.value = 'disconnected'
        console.log('ChatWindow: 确认更新为断开状态')
      } else {
        console.log('ChatWindow: 状态已恢复，不更新为断开状态')
      }
    }, delayTime)
  }
}

// Computed
const currentSession = computed(() => {
  const sessionId = currentSessionId.value
  const allSessions = sessions.value

  console.log('🔍 currentSession计算开始:', {
    currentSessionId: sessionId,
    sessionsCount: allSessions.length,
    sessionIds: allSessions.map(s => s.id.toString())
  })

  if (!sessionId) {
    console.log('🔍 currentSession结果: 无currentSessionId')
    return null
  }

  const session = allSessions.find(s => {
    const match = s.id.toString() === sessionId
    console.log(`🔍 检查会话 ${s.id} (${s.title}): ${match ? '匹配' : '不匹配'}`)
    return match
  })

  console.log('🔍 currentSession计算结果:', {
    currentSessionId: sessionId,
    foundSession: !!session,
    sessionTitle: session?.title,
    sessionId: session?.id
  })

  return session || null
})

// 判断是否应该隐藏输入区域（订单消息和系统通知不允许发送消息）
const shouldHideInputArea = computed(() => {
  if (!currentSession.value) {
    return false
  }
  
  const sessionType = currentSession.value.type
  const isSystemNotification = sessionType === SessionType.SYSTEM_NOTIFICATION || (sessionType as any) === 'system_notification'
  const isOrderNotification = sessionType === SessionType.AFTER_SALE_SERVICE || (sessionType as any) === 'order_notification'
  
  console.log('🔍 shouldHideInputArea计算:', {
    sessionType,
    isSystemNotification,
    isOrderNotification,
    shouldHide: isSystemNotification || isOrderNotification
  })
  
  return isSystemNotification || isOrderNotification
})

// 强制的显示状态控制
// const shouldShowChatInterface = computed(() => {
//   const hasSession = !!currentSession.value
//   const hasSessionId = !!currentSessionId.value
//   const hasSessionsData = sessions.value.length > 0

//   // 只有当真正有会话数据且选中了会话时才显示聊天界面
//   const result = hasSession && hasSessionId && hasSessionsData

//   console.log('🔍 shouldShowChatInterface计算:', {
//     hasSession,
//     hasSessionId,
//     hasSessionsData,
//     result
//   })

//   return result
// })

// const shouldShowEmptyState = computed(() => {
//   const result = !shouldShowChatInterface.value
//   console.log('🔍 shouldShowEmptyState计算:', result)
//   return result
// })

// 简化的消息管理
const currentSessionMessages = ref<any[]>([])
const isLoadingMessages = ref(false)

// 获取消息store实例
const messageStore = useMessageStore()

// 消息分类相关状态 - 移到前面避免初始化顺序问题
const currentMessageCategory = ref<MessageCategoryType>(MessageCategoryType.SERVICE)
const messageCategories = ref<MessageCategoryDTO[]>([])
const unreadCount = ref<UnreadCountDTO>({
  total: 0,
  categories: { chat: 0, system: 0, order: 0, service: 0 },
  conversations: {}
})

// 消息分类服务实例
const messageCategoryService = new MessageCategoryService()

// 动态确定当前用户类型
const getCurrentUserType = () => {
  // 检查当前路由或layout来确定用户类型
  const currentRoute = window.location.pathname
  if (currentRoute.includes('/merchant') || currentRoute.includes('/admin')) {
    return 'merchant'
  } else {
    return 'user'
  }
}

const currentUserType = getCurrentUserType()
console.log('🔍 当前用户类型:', currentUserType)

// 获取当前用户信息和头像
const getCurrentUserInfo = async () => {
  if (currentUserType === 'merchant') {
    // 商家端：使用商家store中的merchantInfo.logo字段
    try {
      const { useMerchantStore } = await import('@/modules/merchant/stores/merchantStore')
      const merchantStore = useMerchantStore()
      console.log('🏪 商家store信息:', {
        hasMerchantInfo: !!merchantStore.merchantInfo,
        merchantId: merchantStore.merchantInfo?.id,
        merchantName: merchantStore.merchantInfo?.name,
        merchantLogo: merchantStore.merchantInfo?.logo
      })
      return {
        id: merchantStore.merchantInfo?.id || 1,
        name: merchantStore.merchantInfo?.name || '商家',
        avatar: merchantStore.merchantInfo?.logo || '/images/default-shop.png' // 使用merchantInfo.logo
      }
    } catch (error) {
      console.warn('无法获取商家信息，使用默认值:', error)
      return {
        id: 1,
        name: '商家',
        avatar: '/images/default-shop.png'
      }
    }
  } else {
    // 用户端：尝试从多个可能的用户store获取信息
    let userInfo = null

    // 方法1: 尝试从modules/user/stores/userStore获取
    try {
      const { useUserStore } = await import('@/modules/user/stores/userStore')
      const userStore = useUserStore()
      console.log('👤 用户store信息 (modules):', {
        hasUserInfo: !!userStore.userInfo,
        userId: userStore.userInfo?.id,
        username: userStore.userInfo?.username,
        nickname: userStore.userInfo?.nickname,
        avatar: userStore.userInfo?.avatar
      })
      if (userStore.userInfo?.id) {
        userInfo = {
          id: userStore.userInfo.id,
          name: userStore.userInfo.nickname || userStore.userInfo.username || '用户',
          avatar: userStore.userInfo.avatar || '/images/default_avatar.png'
        }
      }
    } catch (error) {
      console.warn('无法从modules/user/stores/userStore获取用户信息:', (error as Error).message)
    }

    // 方法2: 如果第一种方法失败，尝试从stores/user获取
    if (!userInfo) {
      try {
        const { useUserStore } = await import('@/stores/user')
        const userStore = useUserStore()
        console.log('👤 用户store信息 (stores):', {
          hasUserInfo: !!userStore.userInfo,
          userId: userStore.userInfo?.id,
          username: userStore.userInfo?.username,
          nickname: userStore.userInfo?.nickname,
          avatar: userStore.userInfo?.avatar
        })
        if (userStore.userInfo?.id) {
          userInfo = {
            id: userStore.userInfo.id,
            name: userStore.userInfo.nickname || userStore.userInfo.username || '用户',
            avatar: userStore.userInfo.avatar || '/images/default_avatar.png'
          }
        }
      } catch (error) {
        console.warn('无法从stores/user获取用户信息:', (error as Error).message)
      }
    }

    // 方法3: 尝试从localStorage获取
    if (!userInfo) {
      try {
        const storedUserInfo = localStorage.getItem('user_info') || localStorage.getItem('user_user_info')
        if (storedUserInfo) {
          const parsedUserInfo = JSON.parse(storedUserInfo)
          console.log('👤 localStorage用户信息:', parsedUserInfo)
          if (parsedUserInfo?.id) {
            userInfo = {
              id: parsedUserInfo.id,
              name: parsedUserInfo.nickname || parsedUserInfo.username || '用户',
              avatar: parsedUserInfo.avatar || '/images/default_avatar.png'
            }
          }
        }
      } catch (error) {
        console.warn('无法从localStorage获取用户信息:', (error as Error).message)
      }
    }

    // 如果所有方法都失败，使用默认值
    if (!userInfo) {
      console.warn('所有用户信息获取方法都失败，使用默认值')
      userInfo = {
        id: 1,
        name: '用户',
        avatar: '/images/default_avatar.png'
      }
    }

    return userInfo
  }
}

// 使用响应式变量管理用户信息初始化状态
let currentUserInfo: any = null
const isUserInfoInitialized = ref(false)

// 上划加载更多消息相关（需要在监听器之前定义）
const isLoadingMore = ref(false)
const hasScrolledToBottomOnce = ref(false) // 标记是否已经滚动到底部过（用于首次加载）
const scrollListenerSetup = ref(false) // 标记滚动监听器是否已设置
const isScrollingToBottom = ref(false) // 标记是否正在执行滚动到底部操作

// 异步初始化用户信息
const initializeUserInfo = async () => {
  try {
    currentUserInfo = await getCurrentUserInfo()
    isUserInfoInitialized.value = true
    console.log('🔍 当前用户信息初始化完成:', currentUserInfo)
  } catch (error) {
    console.error('❌ 用户信息初始化失败:', error)
    // 设置默认值
    currentUserInfo = {
      id: 1,
      name: '用户',
      avatar: '/images/default_avatar.png'
    }
    isUserInfoInitialized.value = true
  }
}

// 安全地处理消息列表，为每条消息补充头像信息
const safeEnrichMessages = async (messages: any[]): Promise<any[]> => {
  // 等待用户信息初始化完成（最多等待3秒）
  let waitCount = 0
  while (!isUserInfoInitialized.value && waitCount < 30) {
    await new Promise(resolve => setTimeout(resolve, 100))
    waitCount++
  }

  if (!isUserInfoInitialized.value) {
    console.warn('⚠️ 用户信息初始化超时，使用默认头像处理消息')
  }

  return messages.map(enrichMessageWithAvatar)
}

// 为消息补充默认头像信息
const enrichMessageWithAvatar = (message: any) => {
  // 如果消息已经有头像，直接返回
  if (message.sender_avatar) {
    return message
  }

  // 安全检查：确保 currentUserInfo 已初始化
  if (!currentUserInfo) {
    console.warn('⚠️ currentUserInfo 未初始化，使用默认头像')
    // 根据消息发送者类型设置默认头像
    let defaultAvatar = '/images/default_avatar.png'
    if (message.sender_type === 'merchant') {
      defaultAvatar = '/images/default-shop.png'
    } else if (message.sender_type === 'user') {
      defaultAvatar = '/images/default_avatar.png'
    } else if (message.sender_type === 'admin') {
      defaultAvatar = '/images/default_avatar.png'
    }

    return {
      ...message,
      sender_avatar: defaultAvatar
    }
  }

  // 判断是否为当前用户的消息
  let isCurrentUser = false
  try {
    isCurrentUser = isOwnMessage(message)
  } catch (error) {
    console.warn('⚠️ 判断消息归属时出错，使用默认逻辑:', error)
    // 如果判断失败，根据消息类型设置默认头像
    let defaultAvatar = '/images/default_avatar.png'
    if (message.sender_type === 'merchant') {
      defaultAvatar = '/images/default-shop.png'
    } else if (message.sender_type === 'user') {
      defaultAvatar = '/images/default_avatar.png'
    } else if (message.sender_type === 'admin') {
      defaultAvatar = '/images/default_avatar.png'
    }

    return {
      ...message,
      sender_avatar: defaultAvatar
    }
  }

  let defaultAvatar = ''
  if (isCurrentUser) {
    // 如果是当前用户的消息，使用当前用户的头像
    defaultAvatar = currentUserInfo.avatar || '/images/default_avatar.png'
  } else {
    // 如果是对方的消息，根据发送者类型设置默认头像
    if (message.sender_type === 'merchant') {
      defaultAvatar = '/images/default-shop.png'
    } else if (message.sender_type === 'user') {
      defaultAvatar = '/images/default_avatar.png'
    } else if (message.sender_type === 'admin') {
      defaultAvatar = '/images/default_avatar.png'
    } else {
      defaultAvatar = '/images/default_avatar.png'
    }
  }

  return {
    ...message,
    sender_avatar: defaultAvatar
  }
}

// 立即开始初始化
;(async () => {
  await initializeUserInfo()
  console.log('🔍 当前用户信息:', currentUserInfo)

  // 验证头像获取是否正确
  const validateAvatarSetup = () => {
    console.log('🔍 头像设置验证:')
    console.log('- 用户类型:', currentUserType)
    console.log('- 用户ID:', currentUserInfo.id)
    console.log('- 用户名称:', currentUserInfo.name)
    console.log('- 用户头像:', currentUserInfo.avatar)

    if (currentUserType === 'merchant') {
      console.log('✅ 商家端应使用 merchantInfo.logo 字段')
    } else {
      console.log('✅ 用户端应使用 userInfo.avatar 字段')
    }
  }

  validateAvatarSetup()

  // 调试当前用户信息和消息数据
  const debugUserAndMessages = async () => {
    console.log('🔍 详细调试信息:')
    console.log('1. 当前路径:', window.location.pathname)
    console.log('2. 用户类型判断:', currentUserType)
    console.log('3. 当前用户信息:', currentUserInfo)

    // 检查页面上显示的用户名
    const userNameElement = document.querySelector('.chat-window__title')
    if (userNameElement) {
      console.log('4. 页面显示的用户名:', userNameElement.textContent)
    }

    // 检查是否有用户store
    try {
      if (currentUserType === 'user') {
        const { useUserStore } = await import('@/modules/user/stores/userStore')
        const userStore = useUserStore()
        console.log('5. 用户Store详细信息:', {
          userInfo: userStore.userInfo,
          isLoggedIn: userStore.isLoggedIn,
          token: userStore.token ? '存在' : '不存在'
        })
      }
    } catch (error) {
      console.log('5. 无法获取用户Store:', (error as Error).message)
    }
  }

  await debugUserAndMessages()
})()

// 提供默认值以防异步加载未完成
if (!currentUserInfo) {
  currentUserInfo = {
    id: 1,
    name: '用户',
    avatar: '/images/default_avatar.png'
  }
}

// 判断消息是否为当前用户发送的
const isOwnMessage = (message: any): boolean => {
  // 安全检查：确保 currentUserInfo 已初始化
  if (!currentUserInfo) {
    console.warn('⚠️ isOwnMessage: currentUserInfo 未初始化，仅使用类型判断')
    return message.sender_type === currentUserType
  }

  // 方法1: 根据sender_type判断
  const isSameType = message.sender_type === currentUserType

  // 方法2: 根据sender_id判断（如果有的话）
  const isSameId = message.sender_id === currentUserInfo.id

  // 方法3: 根据sender_name判断（备用方案）
  const isSameName = message.sender_name === currentUserInfo.name

  // 方法4: 特殊情况 - 如果当前用户是"测试用户1"，检查消息发送者是否也是"测试用户1"
  const isTestUser1 = currentUserInfo.name === '测试用户1' && message.sender_name === '测试用户1'

  // 方法5: 检查页面标题中的用户名
  let pageUserName = ''
  try {
    const titleElement = document.querySelector('title')
    if (titleElement && titleElement.textContent) {
      const titleMatch = titleElement.textContent.match(/测试用户\d+/)
      if (titleMatch) {
        pageUserName = titleMatch[0]
      }
    }
  } catch (error) {
    // 忽略错误
  }
  const isPageUser = pageUserName && message.sender_name === pageUserName

  // 优先使用ID判断，然后是特殊用户名判断，最后是类型判断
  let isOwn = false
  if (message.sender_id && currentUserInfo.id && currentUserInfo.id !== 1) {
    // 如果有有效的ID（不是默认值1），优先使用ID判断
    isOwn = isSameId
  } else if (isTestUser1 || isPageUser) {
    // 特殊情况：测试用户的名称匹配
    isOwn = true
  } else if (message.sender_name && currentUserInfo.name && currentUserInfo.name !== '用户') {
    // 如果有有效的名称（不是默认值"用户"），使用名称判断
    isOwn = isSameName
  } else if (message.sender_type && currentUserType) {
    // 最后使用类型判断
    isOwn = isSameType
  }

  // console.log(`🔍 消息归属判断 [消息${message.id}]:`, {
  //   '消息发送者类型': message.sender_type,
  //   '消息发送者ID': message.sender_id,
  //   '消息发送者名称': message.sender_name,
  //   '当前用户类型': currentUserType,
  //   '当前用户ID': currentUserInfo.id,
  //   '当前用户名称': currentUserInfo.name,
  //   '页面用户名': pageUserName,
  //   '类型匹配': isSameType,
  //   'ID匹配': isSameId,
  //   '名称匹配': isSameName,
  //   '测试用户1匹配': isTestUser1,
  //   '页面用户匹配': isPageUser,
  //   '最终判断': isOwn,
  //   '消息内容': message.content?.substring(0, 20) + '...'
  // })

  return isOwn
}

// 获取当前用户的显示名称
const getCurrentUserDisplayName = (message: any): string => {
  // 如果消息中有发送者名称，优先使用
  if (message.sender_name) {
    return message.sender_name
  }

  // 否则使用当前用户信息中的名称
  return currentUserInfo.name || '我'
}

// 获取当前用户的头像
const getCurrentUserAvatar = (message: any): string => {
  // 如果消息中有发送者头像，优先使用
  if (message.sender_avatar) {
    // console.log(`🖼️ 使用消息中的头像: ${message.sender_avatar}`)
    return message.sender_avatar
  }

  // 否则使用当前用户信息中的头像
  const avatar = currentUserInfo.avatar || '/images/default_avatar.png'
  // console.log(`🖼️ 使用当前用户头像: ${avatar}`)
  return avatar
}

// 加载当前会话的消息
const loadCurrentSessionMessages = async (sessionId: string) => {
  if (!sessionId) return

  // 🔧 记录加载开始时的状态，用于验证加载完成时状态是否仍然有效
  const loadStartCategory = currentMessageCategory.value
  const loadStartSessionId = currentSessionId.value
  const loadStartTime = Date.now()

  try {
    isLoadingMessages.value = true
    console.log(`🔄 加载会话 ${sessionId} 的消息... (分类: ${loadStartCategory}, 时间: ${loadStartTime})`)

    // 🔧 验证加载前状态
    if (sessionId !== loadStartSessionId) {
      console.log(`⚠️ 会话已切换 (${sessionId} -> ${loadStartSessionId})，取消加载`)
      return
    }

    let result: any = null

    // 🔧 特殊处理系统消息（session_id = 0）
    if (sessionId === '0' && loadStartCategory === MessageCategoryType.SYSTEM) {
      console.log('🔄 加载系统消息（直接从messageStore获取）')
      const messageStore = useMessageStore()
      const categoryMessages = messageStore.getCategoryMessages('system')
      const systemMessages = categoryMessages.get('0') || []

      result = {
        messages: systemMessages,
        total: systemMessages.length,
        has_more: false
      }
    } else {
      // 普通会话消息加载
      const messageStore = useMessageStore()
      // 🔧 修复竞态问题：传递当前消息分类参数
      result = await messageStore.loadMessages(sessionId, loadStartCategory)
    }

    // 🔧 关键验证：检查加载完成时状态是否仍然有效
    const currentCategory = currentMessageCategory.value
    const currentSession = currentSessionId.value

    if (currentCategory !== loadStartCategory || currentSession !== sessionId) {
      console.log(`🚫 消息加载完成时状态已变化，丢弃结果:`, {
        加载时分类: loadStartCategory,
        当前分类: currentCategory,
        加载的会话: sessionId,
        当前会话: currentSession,
        加载耗时: Date.now() - loadStartTime + 'ms'
      })
      return
    }

    // 检查加载结果
    if (!result) {
      console.warn('⚠️ [ChatWindow] 消息加载结果为空')
      currentSessionMessages.value = []
      return
    }

    // 更新本地消息列表，并为消息补充头像信息
    const messages = result.messages || []
    const enrichedMessages = await safeEnrichMessages(messages)
    currentSessionMessages.value = enrichedMessages
    console.log(`✅ 已加载 ${currentSessionMessages.value.length} 条消息 (分类: ${currentCategory}, 会话: ${sessionId})`)
    console.log(`📋 消息详情:`, currentSessionMessages.value.map(m => ({
      id: m.id,
      sender_type: m.sender_type,
      sender_name: m.sender_name,
      content: m.content.substring(0, 20) + '...',
      created_at: m.created_at
    })))

    // 首次加载消息时滚动到底部
    console.log('📜 [ChatWindow] 首次加载消息，滚动到底部')
    scrollToBottom(false) // 加载时不使用平滑滚动
    hasScrolledToBottomOnce.value = true

    // 在消息加载完成后确保滚动监听器已设置
    nextTick(() => {
      ensureScrollListener()
    })

  } catch (error) {
    console.error('加载消息失败:', error)
    // 🔧 只有在状态仍然匹配时才清空消息列表
    if (currentMessageCategory.value === loadStartCategory && currentSessionId.value === sessionId) {
      currentSessionMessages.value = []
    }
  } finally {
    isLoadingMessages.value = false
  }
}

// 监听currentSessionId变化，自动加载消息
watch(currentSessionId, async (newSessionId, oldSessionId) => {
  if (newSessionId && newSessionId !== oldSessionId) {
    console.log(`🔄 会话切换: ${oldSessionId} → ${newSessionId}`)

    // 🔧 更新聊天UI的当前会话状态
    chatStore.setChatUIVisible(true, newSessionId)
    console.log('🔧 [ChatWindow] 更新聊天UI当前会话:', newSessionId)

    await loadCurrentSessionMessages(newSessionId)
  }
}, { immediate: true })

// 监听chatStore中的消息变化，同步到本地状态
watch(() => {
  if (currentSessionId.value) {
    return chatStore.messages.get(currentSessionId.value.toString())
  }
  return []
}, (newMessages) => {
  if (newMessages && newMessages.length > 0) {
    console.log('🔄 [ChatWindow] 同步chatStore消息到本地:', {
      sessionId: currentSessionId.value,
      messageCount: newMessages.length,
      latestMessage: newMessages[newMessages.length - 1]?.content?.substring(0, 30) + '...'
    })

    // 检查是否有新消息需要添加
    const currentIds = new Set(currentSessionMessages.value.map(m => m.id?.toString()))
    const newMessagesToAdd = newMessages.filter(m => !currentIds.has(m.id?.toString()))

    if (newMessagesToAdd.length > 0) {
      console.log('✅ [ChatWindow] 发现新消息，添加到界面:', newMessagesToAdd.length, '条')
      console.log('📊 [ChatWindow] 滚动状态:', {
        isLoadingMore: isLoadingMore.value,
        hasScrolledToBottomOnce: hasScrolledToBottomOnce.value,
        shouldScroll: !isLoadingMore.value && hasScrolledToBottomOnce.value
      })

      currentSessionMessages.value.push(...newMessagesToAdd)

      // 只在非加载更多历史消息时滚动到底部（实时新消息）
      if (!isLoadingMore.value && hasScrolledToBottomOnce.value) {
        console.log('📜 [ChatWindow] 实时新消息，滚动到底部')
        nextTick(() => {
          scrollToBottom()
        })
      } else if (isLoadingMore.value) {
        console.log('📜 [ChatWindow] 加载更多历史消息，保持当前滚动位置')
      } else {
        console.log('📜 [ChatWindow] 首次同步消息，不滚动（已在loadMessages中处理）')
      }
    }
  }
}, { deep: true, immediate: true })

// 监听消息数量变化，自动滚动到底部
watch(() => currentSessionMessages.value.length, (newLength, oldLength) => {
  if (newLength > oldLength) {
    console.log('📊 [ChatWindow] 消息数量变化:', {
      from: oldLength,
      to: newLength,
      isLoadingMore: isLoadingMore.value,
      hasScrolledToBottomOnce: hasScrolledToBottomOnce.value,
      shouldScroll: !isLoadingMore.value && hasScrolledToBottomOnce.value
    })

    // 只有在非加载更多历史消息且已经首次滚动过的情况下才滚动到底部
    if (!isLoadingMore.value && hasScrolledToBottomOnce.value) {
      console.log('📜 [ChatWindow] 实时消息数量增加，滚动到底部')
      scrollToBottom()
    } else {
      console.log('📜 [ChatWindow] 加载历史消息或首次加载，不滚动')
    }
  }
})

// 监听store中的消息变化，同步到本地状态
// 🔧 修复竞态问题：增强分类和时序验证，防止异步加载导致的消息覆盖
watch(() => {
  if (currentSessionId.value && currentMessageCategory.value) {
    // 直接从messagesByCategory获取特定分类下的消息，避免依赖currentCategory
    const categoryMessages = messageStore.messagesByCategory.get(currentMessageCategory.value)
    if (categoryMessages) {
      const messages = categoryMessages.get(currentSessionId.value.toString()) || []
      return {
        messages,
        category: currentMessageCategory.value,
        sessionId: currentSessionId.value,
        timestamp: Date.now() // 添加时间戳用于时序验证
      }
    }
  }
  return {
    messages: [],
    category: currentMessageCategory.value,
    sessionId: currentSessionId.value,
    timestamp: Date.now()
  }
}, async (newData, oldData) => {
  if (!newData.sessionId) {
    console.log('⚠️ 没有当前会话ID，跳过消息同步')
    return
  }

  // 🔧 关键修复：验证数据是否仍然有效（防止过期的异步数据覆盖当前数据）
  const currentCategory = currentMessageCategory.value
  const currentSession = currentSessionId.value

  if (newData.category !== currentCategory || newData.sessionId !== currentSession) {
    console.log('🚫 检测到过期的消息数据，拒绝同步:', {
      数据分类: newData.category,
      当前分类: currentCategory,
      数据会话: newData.sessionId,
      当前会话: currentSession,
      数据时间戳: newData.timestamp
    })
    return
  }

  // 检测分类切换：如果分类发生变化，清空当前消息列表
  if (oldData && newData.category !== oldData.category) {
    console.log('🔄 检测到分类切换，清空当前消息列表:', {
      旧分类: oldData.category,
      新分类: newData.category,
      会话ID: newData.sessionId
    })
    currentSessionMessages.value = []
    return
  }

  // 检测会话切换：如果会话发生变化，重新加载消息
  if (oldData && newData.sessionId !== oldData.sessionId) {
    console.log('🔄 检测到会话切换，重新加载消息:', {
      旧会话: oldData.sessionId,
      新会话: newData.sessionId,
      分类: newData.category
    })
    // 🔧 再次验证数据有效性
    if (newData.category === currentCategory && newData.sessionId === currentSession) {
      const enrichedMessages = await safeEnrichMessages(newData.messages)
      currentSessionMessages.value = [...enrichedMessages]
    } else {
      console.log('🚫 会话切换时数据已过期，跳过同步')
    }
    return
  }

  if (newData.messages && newData.sessionId) {
    console.log('🔄 检测到store消息变化，同步到本地状态')
    console.log('📊 当前分类:', newData.category)
    console.log('📊 Store中的消息数量:', newData.messages.length)
    console.log('📊 本地消息数量:', currentSessionMessages.value.length)

    // 🔧 最终验证：确保数据仍然匹配当前状态
    if (newData.category !== currentCategory || newData.sessionId !== currentSession) {
      console.log('🚫 消息同步时检测到状态不匹配，跳过同步:', {
        数据分类: newData.category,
        当前分类: currentCategory,
        数据会话: newData.sessionId,
        当前会话: currentSession
      })
      return
    }

    // 智能同步：检查是否有新消息需要添加
    const currentIds = new Set(currentSessionMessages.value.map((m: Message) => m.id?.toString()))
    const newMessagesToAdd = newData.messages.filter((m: Message) => !currentIds.has(m.id?.toString()))

    if (newMessagesToAdd.length > 0) {
      console.log('✅ 发现新消息，添加到本地列表:', newMessagesToAdd.length, '条')
      console.log('📋 新消息详情:', newMessagesToAdd.map((m: Message) => ({
        id: m.id,
        content: m.content?.substring(0, 20) + '...',
        sender_type: m.sender_type
      })))

      // 合并新消息到本地状态，并补充头像信息
      const enrichedMessages = await safeEnrichMessages(newData.messages)
      currentSessionMessages.value = [...enrichedMessages]

      // 滚动到底部显示新消息
      nextTick(() => {
        scrollToBottom()
      })
    } else if (newData.messages.length !== currentSessionMessages.value.length) {
      // 如果数量不同但没有新消息，可能是消息状态更新
      console.log('🔄 消息状态可能有更新，同步整个列表')
      const enrichedMessages = await safeEnrichMessages(newData.messages)
      currentSessionMessages.value = [...enrichedMessages]
    }
  }
}, { deep: true, immediate: false })

// 简化的消息发送处理
const handleSimplifiedMessageSend = async (content: string) => {
  if (!currentSessionId.value || !content.trim()) return

  try {
    console.log(`📤 发送消息到会话 ${currentSessionId.value}:`, content)

    // 创建临时消息对象，使用负数ID避免与服务器ID冲突
    const tempId = -Date.now() // 负数临时ID
    const tempMessage = {
      id: tempId,
      session_id: parseInt(currentSessionId.value),
      sender_id: currentUserInfo.id, // 使用当前用户ID
      sender_type: currentUserType, // 动态确定用户类型
      sender_name: currentUserInfo.name, // 使用当前用户名称
      sender_avatar: currentUserInfo.avatar, // 使用当前用户头像
      content: content.trim(),
      type: 'text',
      resource_id: '',
      status: 0, // 0表示发送中
      created_at: new Date().toISOString(),
      isTemporary: true // 标记为临时消息
    }

    // 立即添加到消息列表
    currentSessionMessages.value.push(tempMessage)
    console.log('📝 添加临时消息到本地列表，ID:', tempId)

    // 滚动到底部显示新消息
    scrollToBottom()

    // 发送到服务器
    const messageStore = useMessageStore()
    await messageStore.sendMessage({
      session_id: currentSessionId.value,
      content: content.trim(),
      message_type: 'text'
    })

    // 发送成功后，移除临时消息（真实消息会通过store同步添加）
    const tempIndex = currentSessionMessages.value.findIndex(m => m.id === tempId)
    if (tempIndex !== -1) {
      currentSessionMessages.value.splice(tempIndex, 1)
      console.log('🗑️ 移除临时消息，等待store同步真实消息')
    }

    console.log('✅ 消息发送成功')

  } catch (error) {
    console.error('发送消息失败:', error)

    // 发送失败时，更新临时消息状态
    const tempMessage = currentSessionMessages.value.find(m => m.isTemporary && m.content === content.trim())
    if (tempMessage) {
      tempMessage.status = -1 // -1表示发送失败
      console.log('❌ 标记临时消息为发送失败')
    }
  }
}

// 加载更多消息
// 注释掉未使用的函数
// const handleLoadMoreMessages = async () => {
//   // 这里可以实现分页加载逻辑
//   console.log('加载更多消息...')
// }

// 消息输入相关
const messageInputText = ref('')
const messageContainer = ref<HTMLElement>()



// 从messageStore获取分页信息
const hasMoreMessages = computed(() => {
  if (!currentSessionId.value) return false

  const paginationKey = `${currentMessageCategory.value}-${currentSessionId.value}`
  const pageInfo = messageStore.paginationInfo.get(paginationKey)

  console.log('🔍 [ChatWindow] 检查hasMoreMessages:', {
    sessionId: currentSessionId.value,
    category: currentMessageCategory.value,
    paginationKey,
    pageInfo,
    hasMore: pageInfo?.hasMore ?? true
  })

  return pageInfo?.hasMore ?? true // 默认为true，直到加载后确定
})

// 加载更多历史消息
const loadMoreMessages = async () => {
  if (!currentSessionId.value || isLoadingMore.value || !hasMoreMessages.value) {
    return
  }

  try {
    isLoadingMore.value = true
    console.log('🔄 [ChatWindow] 开始加载更多历史消息...')

    const result = await messageStore.loadMoreMessages(currentSessionId.value.toString())

    if (result && result.messages && result.messages.length > 0) {
      console.log(`✅ [ChatWindow] 加载了 ${result.messages.length} 条历史消息`)
      console.log(`📊 [ChatWindow] 分页状态更新: hasMore=${result.hasMore}`)

      // 消息会通过watch自动同步到界面，这里不需要手动处理
      // hasMoreMessages状态由messageStore的分页信息管理
    } else {
      console.log('📄 [ChatWindow] 没有更多历史消息')
      // hasMoreMessages状态由messageStore的分页信息管理
    }
  } catch (error) {
    console.error('❌ [ChatWindow] 加载更多消息失败:', error)
  } finally {
    isLoadingMore.value = false
  }
}

// 确保滚动监听器已设置
const ensureScrollListener = () => {
  console.log('🔍 [ChatWindow] 检查滚动监听器状态:', {
    scrollListenerSetup: scrollListenerSetup.value,
    hasMessageContainer: !!messageContainer.value
  })

  if (!messageContainer.value) {
    console.warn('⚠️ [ChatWindow] messageContainer.value 不存在，无法设置滚动监听器')
    return false
  }

  try {
    // 如果已经设置过，先移除旧的监听器
    if (scrollListenerSetup.value) {
      messageContainer.value.removeEventListener('scroll', handleScroll)
      console.log('🔄 [ChatWindow] 移除旧的滚动监听器')
    }

    // 添加新的监听器
    messageContainer.value.addEventListener('scroll', handleScroll, { passive: true })
    scrollListenerSetup.value = true

    console.log('✅ [ChatWindow] 滚动监听器设置成功')

    // 测试滚动容器状态
    const scrollInfo = {
      scrollTop: messageContainer.value.scrollTop,
      scrollHeight: messageContainer.value.scrollHeight,
      clientHeight: messageContainer.value.clientHeight,
      hasScrollbar: messageContainer.value.scrollHeight > messageContainer.value.clientHeight
    }
    console.log('📏 [ChatWindow] 滚动容器状态:', scrollInfo)

    return true
  } catch (error) {
    console.error('❌ [ChatWindow] 设置滚动监听器失败:', error)
    return false
  }
}

// 滚动事件处理 - 检测上划到顶部
const handleScroll = (event: Event) => {
  const container = event.target as HTMLElement
  if (!container) {
    console.log('⚠️ [ChatWindow] 滚动事件：容器不存在')
    return
  }

  // 如果正在执行滚动到底部操作，忽略所有滚动事件
  if (isScrollingToBottom.value) {
    console.log('🚫 [ChatWindow] 正在执行滚动到底部操作，忽略滚动事件')
    return
  }

  // 添加详细的滚动调试信息
  const scrollInfo = {
    scrollTop: container.scrollTop,
    scrollHeight: container.scrollHeight,
    clientHeight: container.clientHeight,
    isAtTop: container.scrollTop <= 10,
    isAtBottom: container.scrollTop + container.clientHeight >= container.scrollHeight - 10,
    hasMoreMessages: hasMoreMessages.value,
    isLoadingMore: isLoadingMore.value,
    hasScrolledToBottomOnce: hasScrolledToBottomOnce.value,
    isScrollingToBottom: isScrollingToBottom.value
  }

  // 只在接近顶部时打印详细信息
  if (container.scrollTop <= 50) {
    console.log('🎯 [ChatWindow] 滚动事件被触发！')
    console.log('📜 [ChatWindow] 滚动事件详情:', scrollInfo)
  }

  // 检测是否滚动到顶部（允许一些误差）
  const isAtTop = container.scrollTop <= 10

  // 增加安全检查：只有在用户真正滚动到顶部且已经完成首次滚动后才触发加载更多
  if (isAtTop && hasMoreMessages.value && !isLoadingMore.value && hasScrolledToBottomOnce.value) {
    // 额外检查：确保不是刚刚滚动到底部后立即触发的
    const isAtBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 10

    if (isAtBottom) {
      console.log('🔝 [ChatWindow] 检测到在底部位置，忽略顶部触发（可能是滚动位置设置问题）')
      return
    }

    console.log('🔝 [ChatWindow] 检测到滚动到顶部，触发加载更多消息')

    // 记录当前滚动位置，用于加载完成后恢复位置
    const currentScrollHeight = container.scrollHeight

    loadMoreMessages().then(() => {
      // 加载完成后，保持用户的相对滚动位置
      nextTick(() => {
        const newScrollHeight = container.scrollHeight
        const heightDiff = newScrollHeight - currentScrollHeight
        if (heightDiff > 0) {
          container.scrollTop = heightDiff + 10 // 稍微向下偏移一点
          console.log('📍 [ChatWindow] 恢复滚动位置，偏移:', heightDiff)
        }
      })
    })
  } else if (isAtTop) {
    console.log('🔝 [ChatWindow] 滚动到顶部，但不满足加载条件:', {
      hasMoreMessages: hasMoreMessages.value,
      isLoadingMore: isLoadingMore.value,
      hasScrolledToBottomOnce: hasScrolledToBottomOnce.value,
      isScrollingToBottom: isScrollingToBottom.value,
      reason: isScrollingToBottom.value ? '正在执行滚动到底部操作' :
              !hasScrolledToBottomOnce.value ? '尚未完成首次滚动' :
              !hasMoreMessages.value ? '没有更多消息' :
              isLoadingMore.value ? '正在加载中' : '未知原因'
    })
  }
}

// 组件挂载时尝试添加滚动事件监听（简化版本）
onMounted(() => {
  console.log('🔧 [ChatWindow] 组件挂载，尝试设置滚动监听器')

  // 延迟尝试设置滚动监听器，给DOM更多时间渲染
  setTimeout(() => {
    ensureScrollListener()
  }, 100)
})

// 组件卸载时移除滚动事件监听
onUnmounted(() => {
  if (messageContainer.value) {
    messageContainer.value.removeEventListener('scroll', handleScroll)
    scrollListenerSetup.value = false
    console.log('📱 [ChatWindow] 已移除滚动事件监听器')
  } else {
    console.log('⚠️ [ChatWindow] 组件卸载时 messageContainer.value 不存在')
  }
})

// 重置分页状态
const resetPaginationState = () => {
  // hasMoreMessages现在由messageStore管理，这里只重置loading状态
  isLoadingMore.value = false
  hasScrolledToBottomOnce.value = false // 重置滚动状态，允许新会话首次滚动
  scrollListenerSetup.value = false // 重置滚动监听器状态，允许重新设置
  isScrollingToBottom.value = false // 重置滚动到底部状态

  // 清除messageStore中的分页信息，让它重新初始化
  if (currentSessionId.value) {
    const paginationKey = `${currentMessageCategory.value}-${currentSessionId.value}`
    messageStore.paginationInfo.delete(paginationKey)
    console.log('🔄 [ChatWindow] 已清除分页状态，将重新初始化:', paginationKey)
  }

  console.log('🔄 [ChatWindow] 重置所有滚动状态:', {
    hasScrolledToBottomOnce: hasScrolledToBottomOnce.value,
    scrollListenerSetup: scrollListenerSetup.value,
    isScrollingToBottom: isScrollingToBottom.value
  })
}

// 监听会话切换，重置分页状态
watch(currentSessionId, (newSessionId, oldSessionId) => {
  if (newSessionId !== oldSessionId) {
    console.log('🔄 [ChatWindow] 会话切换检测:', {
      from: oldSessionId,
      to: newSessionId,
      hasScrolledToBottomOnce: hasScrolledToBottomOnce.value
    })
    resetPaginationState()
    console.log('🔄 [ChatWindow] 会话切换，重置分页状态完成')
  }
})

// 手动测试滚动功能（开发调试用）
const testScrollFunction = () => {
  console.log('🧪 [ChatWindow] 手动测试滚动功能...')

  if (!messageContainer.value) {
    console.error('❌ [ChatWindow] messageContainer.value 不存在，无法测试')
    return
  }

  console.log('📊 [ChatWindow] 当前滚动状态:', {
    scrollTop: messageContainer.value.scrollTop,
    scrollHeight: messageContainer.value.scrollHeight,
    clientHeight: messageContainer.value.clientHeight
  })

  // 模拟滚动到顶部
  console.log('🔝 [ChatWindow] 模拟滚动到顶部...')
  messageContainer.value.scrollTop = 0

  // 手动触发滚动事件
  const scrollEvent = new Event('scroll', { bubbles: true })
  messageContainer.value.dispatchEvent(scrollEvent)
}

// 将测试函数暴露到全局，方便在控制台调用
if (typeof window !== 'undefined') {
  (window as any).testScrollFunction = testScrollFunction
  console.log('🔧 [ChatWindow] 测试函数已暴露到全局: window.testScrollFunction()')
}

// 发送消息处理
const handleSendMessage = async () => {
  const content = messageInputText.value.trim()
  if (!content) return

  await handleSimplifiedMessageSend(content)
  messageInputText.value = ''
}

// 文件上传相关方法
const handleFileUploaded = async (uploadData: any) => {
  console.log('📁 文件上传成功:', uploadData)

  if (!currentSessionId.value) {
    console.warn('⚠️ 当前没有选中的会话')
    return
  }

  try {
    // 创建文件消息数据
    const fileMessageData = chatFileService.createFileMessageData(
      { success: true, data: uploadData.result },
      uploadData.file
    )

    if (!fileMessageData) {
      console.error('❌ 创建文件消息数据失败')
      return
    }

    // 发送文件消息
    await sendFileMessage(fileMessageData, uploadData.type)

  } catch (error) {
    console.error('❌ 处理文件上传失败:', error)
  }
}

const sendFileMessage = async (fileData: any, messageType: 'image' | 'file') => {
  if (!currentSessionId.value) return

  try {
    // 创建文件消息对象
    const fileMessage = {
      id: Date.now(),
      session_id: parseInt(currentSessionId.value),
      sender_id: currentUserInfo.id,
      sender_type: currentUserType,
      sender_name: currentUserInfo.name,
      sender_avatar: currentUserInfo.avatar,
      content: messageType === 'image' ? '[图片]' : `[文件] ${fileData.filename}`,
      type: messageType,
      file_data: fileData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // 添加到当前消息列表
    currentSessionMessages.value.push(fileMessage)

    // 滚动到底部
    nextTick(() => {
      scrollToBottom()
    })

    console.log('✅ 文件消息发送成功:', fileMessage)

  } catch (error) {
    console.error('❌ 发送文件消息失败:', error)
  }
}

// 新的媒体消息事件处理方法
const handleMediaMessageSent = async (data: any) => {
  console.log('✅ 媒体消息发送成功:', data)

  try {
    // 从API响应中提取消息数据
    const messageResponse = data.messageResult || data
    let messageData = null

    // 处理不同的响应格式
    if (messageResponse?.data?.data) {
      // 格式: { data: { data: { id, content, ... } } }
      messageData = messageResponse.data.data
    } else if (messageResponse?.data) {
      // 格式: { data: { id, content, ... } }
      messageData = messageResponse.data
    } else if (messageResponse?.id) {
      // 直接的消息对象
      messageData = messageResponse
    }

    if (messageData && messageData.id) {
      console.log('📨 处理媒体消息API响应:', messageData)

      // 补充头像信息
      const enrichedMessages = await safeEnrichMessages([messageData])
      const enrichedMessage = enrichedMessages[0]

      // 检查消息是否已存在（避免重复添加）
      const existingMessage = currentSessionMessages.value.find(m => m.id === messageData.id)
      if (!existingMessage) {
        console.log('✅ 媒体消息发送成功，准备添加到store:', {
          id: messageData.id,
          type: messageData.type,
          content: messageData.content?.substring(0, 50) + '...',
          sender: messageData.sender_name
        })

        // 🔧 修复重复添加问题：只添加到messageStore，让监听器自动同步到本地
        // 这样避免了直接添加到currentSessionMessages导致的重复
        try {
          messageStore.addMessage(enrichedMessage)
          console.log('📝 媒体消息已添加到store，等待监听器同步到界面')
        } catch (storeError) {
          console.warn('⚠️ 添加消息到store失败:', storeError)
          // 如果store添加失败，作为备选方案直接添加到本地
          currentSessionMessages.value.push(enrichedMessage)
          console.log('📝 备选方案：直接添加到本地消息列表')
        }
      } else {
        console.log('ℹ️ 媒体消息已存在，跳过添加:', messageData.id)
      }
    } else {
      console.warn('⚠️ 无法从API响应中提取有效的消息数据:', data)
    }
  } catch (error) {
    console.error('❌ 处理媒体消息响应失败:', error, data)
  }

  // 滚动到底部以显示新消息
  nextTick(() => {
    scrollToBottom()
  })
}

const handleMediaMessageError = (data: any) => {
  console.error('❌ 媒体消息发送失败:', data)

  // 可以在这里显示重试选项或错误提示
  // 由于文件已经上传成功，可以提供重新发送消息的选项
}

const handleUploadError = (error: string) => {
  console.error('❌ 文件上传失败:', error)
}

const handleUploadStart = () => {
  console.log('📤 开始上传文件')
}

const handleUploadComplete = () => {
  console.log('✅ 文件上传完成')
}

// 格式化消息时间
const formatMessageTime = (timestamp: string) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 滚动到底部
const scrollToBottom = (smooth = true) => {
  nextTick(() => {
    if (messageContainer.value) {
      // 设置滚动状态标记
      isScrollingToBottom.value = true

      const targetScrollTop = messageContainer.value.scrollHeight - messageContainer.value.clientHeight

      console.log('📜 [ChatWindow] 准备滚动到底部:', {
        scrollHeight: messageContainer.value.scrollHeight,
        clientHeight: messageContainer.value.clientHeight,
        currentScrollTop: messageContainer.value.scrollTop,
        targetScrollTop,
        smooth,
        isScrollingToBottom: isScrollingToBottom.value
      })

      // 临时禁用滚动事件监听，避免触发加载更多
      const tempDisableScroll = () => {
        if (messageContainer.value) {
          messageContainer.value.removeEventListener('scroll', handleScroll)
          console.log('🚫 [ChatWindow] 临时禁用滚动事件监听')
        }
      }

      // 重新启用滚动事件监听
      const reEnableScroll = () => {
        if (messageContainer.value && scrollListenerSetup.value) {
          messageContainer.value.addEventListener('scroll', handleScroll, { passive: true })
          console.log('✅ [ChatWindow] 重新启用滚动事件监听')
        }
        // 重置滚动状态标记
        isScrollingToBottom.value = false
        console.log('🔄 [ChatWindow] 重置滚动状态标记')
      }

      // 禁用滚动监听
      tempDisableScroll()

      // 执行滚动
      const scrollOptions: ScrollToOptions = {
        top: targetScrollTop,
        behavior: smooth ? 'smooth' : 'auto'
      }
      messageContainer.value.scrollTo(scrollOptions)

      // 等待滚动完成后重新启用监听
      setTimeout(() => {
        console.log('✅ [ChatWindow] 滚动操作完成，重新启用监听器')
        reEnableScroll()
      }, smooth ? 500 : 200) // 给足够的时间让滚动完成

    } else {
      console.warn('⚠️ [ChatWindow] scrollToBottom: messageContainer.value 不存在')
    }
  })
}

// 处理头像加载错误
const handleAvatarError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 隐藏错误的图片，让占位符显示
  img.style.display = 'none'
}

// 将消息数据适配为ChatFileMessage组件期望的fileData格式
const adaptMessageToFileData = (message: any) => {
  if (!message) {
    console.warn('⚠️ adaptMessageToFileData: message is undefined')
    return null
  }

  // 如果消息已经有file_data属性，直接使用
  if (message.file_data) {
    return message.file_data
  }

  // 从消息内容中提取文件信息
  const fileUrl = message.content || ''
  const fileName = extractFileNameFromUrl(fileUrl) || `${message.type}_${message.id}`

  // 构造fileData对象
  const fileData = {
    id: message.id?.toString() || '',
    url: fileUrl,
    filename: fileName,
    size: (message as any).file_size || 0,
    type: getFileTypeFromMessage(message),
    thumbnail_url: message.thumbnail_url || (message.type === 'image' ? fileUrl : undefined),
    width: message.width,
    height: message.height,
    duration: message.duration
  }

  // console.log('🔄 适配消息数据为fileData:', { message, fileData })
  return fileData
}

// 从URL中提取文件名
const extractFileNameFromUrl = (url: string): string => {
  if (!url) return ''

  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    const fileName = pathname.split('/').pop() || ''

    // 如果文件名包含扩展名，直接返回
    if (fileName.includes('.')) {
      return fileName
    }

    // 否则根据URL路径推断文件名和扩展名
    const pathSegments = pathname.split('/')
    const fileId = pathSegments[pathSegments.length - 1]

    // 根据路径中的类型信息推断扩展名
    if (pathname.includes('/image/')) {
      return `${fileId}.png`
    } else if (pathname.includes('/video/')) {
      return `${fileId}.mp4`
    } else if (pathname.includes('/audio/')) {
      return `${fileId}.mp3`
    } else {
      return `${fileId}.file`
    }
  } catch (error) {
    console.warn('⚠️ 无法从URL提取文件名:', url, error)
    return url.split('/').pop() || 'unknown_file'
  }
}

// 根据消息类型获取文件MIME类型
const getFileTypeFromMessage = (message: any): string => {
  const messageType = message.type

  switch (messageType) {
    case 'image':
      return 'image/png'
    case 'video':
      return 'video/mp4'
    case 'voice':
      return 'audio/mp3'
    case 'file':
    default:
      // 尝试从URL中推断类型
      const url = message.content || ''
      if (url.includes('.jpg') || url.includes('.jpeg')) return 'image/jpeg'
      if (url.includes('.png')) return 'image/png'
      if (url.includes('.gif')) return 'image/gif'
      if (url.includes('.mp4')) return 'video/mp4'
      if (url.includes('.mp3')) return 'audio/mp3'
      if (url.includes('.pdf')) return 'application/pdf'
      return 'application/octet-stream'
  }
}

// 根据当前分类过滤会话
const filteredSessions = computed(() => {
  const allSessions = sessions.value
  const currentCategory = currentMessageCategory.value

  console.log(`🔍 过滤会话 - 当前分类: ${currentCategory}`)
  console.log(`📊 总会话数: ${allSessions.length}`)
  console.log(`📋 所有会话:`, allSessions.map(s => ({
    id: s.id,
    type: s.type,
    title: s.title,
    target_name: s.title
  })))

  const filtered = allSessions.filter(session => {
    let shouldInclude = false

    // 根据消息分类过滤会话的逻辑
    switch (currentCategory) {
      case MessageCategoryType.CHAT:
        shouldInclude = session.type === SessionType.FRIEND_CHAT ||
                       session.type === SessionType.GROUP_CHAT ||
                       session.type === SessionType.PRESALE_CONSULTATION
        break
      case MessageCategoryType.SYSTEM:
        shouldInclude = session.type === SessionType.SYSTEM_NOTIFICATION || (session.type as any) === 'system_notification'
        break
      case MessageCategoryType.ORDER:
        shouldInclude = session.type === SessionType.AFTER_SALE_SERVICE || 
                       (session as any).original_type === 'order_notification' ||
                       (session.type === SessionType.SYSTEM_NOTIFICATION && (session as any).notification_category === 'order')
        break
      case MessageCategoryType.SERVICE:
        // 客服消息包含：客服服务、售前咨询、售后服务
        shouldInclude = session.type === SessionType.CUSTOMER_SERVICE ||
                       session.type === SessionType.PRESALE_CONSULTATION ||
                       session.type === SessionType.AFTER_SALE_SERVICE
        break
      default:
        shouldInclude = true
    }

    console.log(`🔍 会话${session.id} (${session.type}) 是否包含在${currentCategory}分类: ${shouldInclude}`)
    return shouldInclude
  })

  console.log(`✅ 过滤结果: ${filtered.length} 个会话`)
  console.log(`📋 过滤后的会话:`, filtered.map(s => ({
    id: s.id,
    type: s.type,
    title: s.title
  })))

  return filtered
})

// Refs
const messageListRef = ref<InstanceType<typeof MessageList>>()
// const messageInputRef = ref<InstanceType<typeof MessageInput>>()

// State
const isMinimized = ref(props.defaultMinimized)
const showSessionInfo = ref(false)
const showSessionList = ref(props.showSessionList)
// const replyMessage = ref<Message | null>(null)
// const isLoading = ref(false) // 未使用，注释掉

// 消息分类相关状态已移动到前面避免初始化顺序问题
// Computed
const showUploadProgress = computed(() => uploadQueue.value.length > 0)

// Methods
const toggleMinimize = () => {
  isMinimized.value = !isMinimized.value
}

const toggleSessionList = () => {
  showSessionList.value = !showSessionList.value
  console.log('🔄 切换会话列表显示状态:', showSessionList.value)
}

// 消息分类相关方法
const handleCategoryChange = async (category: MessageCategoryType) => {
  console.log('🔄 切换到消息分类:', category)

  // 🔧 立即清空当前消息列表，防止显示错误分类的消息
  currentSessionMessages.value = []
  console.log('🗑️ 已清空当前消息列表，准备切换分类')

  // 取消当前分类的所有消息加载请求，防止竞态条件
  const messageStore = useMessageStore()
  const canceledMessageCount = messageStore.cancelCategoryLoadMessages(category)
  if (canceledMessageCount > 0) {
    console.log(`🚫 已取消分类 ${category} 的 ${canceledMessageCount} 个正在进行的消息加载请求`)
  }

  // 取消所有正在进行的会话加载请求，防止竞态条件
  const canceledSessionCount = sessionStore.cancelAllLoadSessions()
  if (canceledSessionCount > 0) {
    console.log(`🚫 已取消 ${canceledSessionCount} 个正在进行的会话加载请求`)
  }

  // 先设置messageStore的当前分类
  messageStore.setCurrentCategory(category)

  // 然后更新本地分类状态
  currentMessageCategory.value = category

  // 暂时保存当前选中的会话ID
  const previousSessionId = sessionStore.currentSessionId

  // 🔧 重置会话选择状态，确保分类切换时的干净状态
  sessionStore.currentSessionId = null

  // 🔧 系统通知分类使用消息列表模式，其他分类使用会话模式
  if (category === MessageCategoryType.SYSTEM) {
    console.log(`📥 开始加载 ${category} 分类的消息列表（直接消息模式）`)
    await loadSystemMessages()
  } else {
    console.log(`📥 开始加载 ${category} 分类的会话列表`)
    await loadCategorySessions(category)

    // 加载完成后，尝试恢复或自动选择会话
    await autoSelectSession(previousSessionId)
  }

  // 更新分类的未读数量
  await updateUnreadCount()
}

// 自动选择会话逻辑
const autoSelectSession = async (previousSessionId: string | null) => {
  try {
    console.log('🔄 自动选择会话逻辑开始...', {
      previousSessionId,
      currentSessionId: sessionStore.currentSessionId
    })

    // 等待一下让filteredSessions更新
    await nextTick()

    const availableSessions = filteredSessions.value
    console.log(`📊 当前分类可用会话数: ${availableSessions.length}`)

    if (availableSessions.length === 0) {
      console.log('ℹ️ 当前分类无可用会话，清空currentSessionId')
      sessionStore.currentSessionId = null
      return
    }

    // 1. 尝试恢复之前选中的会话（如果在当前分类中存在）
    if (previousSessionId) {
      const previousSession = availableSessions.find(s => s.id.toString() === previousSessionId)
      if (previousSession) {
        console.log('✅ 恢复之前选中的会话:', previousSession.title)
        sessionStore.currentSessionId = previousSessionId
        await loadSessionMessages(previousSessionId)
        return
      }
    }

    // 2. 自动选择第一个会话
    const firstSession = availableSessions[0]
    console.log('🎯 自动选择第一个会话:', firstSession.title)
    await handleSessionSelect(firstSession)

  } catch (error) {
    console.error('❌ 自动选择会话失败:', error)
    sessionStore.currentSessionId = null
  }
}

// 加载消息分类列表
const loadMessageCategories = async () => {
  try {
    const categories = await messageCategoryService.getMessageCategories()
    messageCategories.value = categories
  } catch (error) {
    console.error('加载消息分类失败:', error)
  }
}

// 加载系统消息列表（直接消息模式）
const loadSystemMessages = async () => {
  try {
    console.log('🔄 开始加载系统消息列表...')

    // 使用消息分类服务加载系统消息
    const response = await messageCategoryService.getCategoryMessages({
      type: MessageCategoryType.SYSTEM,
      page: 1,
      page_size: 20
    })

    console.log('📡 系统消息API响应:', response)

    // 处理响应数据
    const messageList = response.list || []
    console.log(`✅ 已加载 ${messageList.length} 条系统消息`)

    if (messageList.length > 0) {
      // 将系统消息存储到消息store中，使用特殊的session_id = 0
      const messageStore = useMessageStore()

      // 清空之前的系统消息
      messageStore.clearCategoryMessages('system')

      // 添加新的系统消息到store
      messageList.forEach((message: any) => {
        // 确保系统消息有正确的session_id
        message.session_id = 0
        messageStore.addMessage(message, 'system')
      })

      // 设置当前会话为系统消息会话（session_id = 0）
      sessionStore.currentSessionId = '0'

      console.log('✅ 系统消息加载完成')
    } else {
      console.log('ℹ️ 暂无系统消息')
      // 清空当前会话选择
      sessionStore.currentSessionId = null
    }

  } catch (error) {
    console.error('❌ 加载系统消息失败:', error)
    console.error('错误详情:', {
      message: (error as Error).message,
      stack: (error as Error).stack
    })
  }
}

// 加载分类会话列表
const loadCategorySessions = async (category: MessageCategoryType) => {
  try {
    console.log(`🔄 开始加载${category}分类会话...`)

    // 使用sessionStore的loadSessions方法，它已经包含了竞态条件保护
    const sessionList = await sessionStore.loadSessions({
      category: category,
      page: 1,
      page_size: 20
    })

    if (sessionList === null) {
      console.log(`⚠️ ${category}分类会话加载被取消或替代`)
      return
    }

    const sessionCount = sessionStore.sessions ? sessionStore.sessions.length : 0
    console.log(`✅ 已加载 ${sessionCount} 个${category}会话`)

    if (sessionCount === 0) {
      console.log(`ℹ️ ${category}分类暂无会话`)
      return
    }

    console.log(`📋 ${category}会话列表:`, sessionStore.sessions)
    console.log(`✅ 已成功加载 ${sessionCount} 个${category}会话`)

  } catch (error) {
    console.error(`❌ 加载${category}分类会话失败:`, error)
    console.error('错误详情:', {
      message: (error as Error).message,
      stack: (error as Error).stack
    })
  }
}

const getCategoryIcon = (iconName: string) => {
  const iconMap: Record<string, any> = {
    chat: ChatDotRound,
    notification: Bell,
    order: ShoppingBag,
    service: Service
  }
  return iconMap[iconName] || ChatDotRound
}

// 加载分类消息列表（用于系统通知和订单消息）
// const loadCategoryMessages = async (category: MessageCategoryType) => {
//   try {
//     isLoading.value = true
//     console.log(`🔄 开始加载${category}分类消息...`)

//     const response = await messageCategoryService.getCategoryMessages({
//       type: category,
//       page: 1,
//       page_size: 20
//     })

//     console.log(`📥 API返回的${category}消息数据:`, response)

//     // 安全地处理可能为null的list
//     const messageList = response.list || []
//     const messageCount = messageList.length

//     console.log(`✅ 已加载 ${messageCount} 条${category}消息`)

//     if (messageCount === 0) {
//       console.log(`ℹ️ ${category}分类暂无消息`)
//     } else {
//       console.log(`📋 ${category}消息列表:`, messageList)
//     }

//     // TODO: 这里可以将消息列表显示在界面上
//     // 具体实现取决于UI设计，可能需要更新某个状态来显示消息列表

//   } catch (error) {
//     console.error(`❌ 加载${category}分类消息失败:`, error)
//   } finally {
//     isLoading.value = false
//   }
// }

// 更新未读统计
const updateUnreadCount = async () => {
  try {
    const count = await messageCategoryService.getUnreadCount()
    unreadCount.value = count
  } catch (error) {
    console.error('更新未读统计失败:', error)
  }
}

// 更新分类未读数量
const updateCategoryUnreadCount = async (category: MessageCategoryType) => {
  try {
    // 计算当前分类的未读消息数量
    const sessions = sessionStore.sessions || []
    const unreadCount = sessions.reduce((total, session) => {
      return total + (session.unread_count || 0)
    }, 0)

    // 更新分类信息中的未读数量
    const categoryInfo = messageCategories.value.find(c => c.type === category)
    if (categoryInfo) {
      categoryInfo.unreadCount = unreadCount
    }
  } catch (error) {
    console.error('更新分类未读数量失败:', error)
  }
}

// 获取当前分类标题
const getCurrentCategoryTitle = () => {
  const category = messageCategories.value.find(c => c.type === currentMessageCategory.value)
  return category?.title || '聊天消息'
}

// 获取当前分类图标
const getCurrentCategoryIcon = () => {
  const category = messageCategories.value.find(c => c.type === currentMessageCategory.value)
  return category?.icon || 'chat'
}

// 获取头像文本
const getAvatarText = (name?: string) => {
  if (!name) return '?'
  return name.charAt(0).toUpperCase()
}

// 判断是否为订单消息
const isOrderMessage = (message: any) => {
  // 检查消息是否包含订单相关的元数据
  return message.notification_type === 'order_user_update' ||
         (message.metadata && message.metadata.is_order_notification) ||
         (message.type === 'notification' && message.content && message.content.includes('订单'))
}

// 判断是否为退款消息
const isRefundMessage = (message: any) => {
  // 检查消息是否包含退款相关的元数据
  return message.notification_type === 'user_refund_result' ||
         (message.metadata && message.metadata.is_refund_notification) ||
         (message.type === 'notification' && message.content && message.content.includes('退款'))
}

// 判断是否需要显示跳转图标（订单消息或退款消息）
const shouldShowJumpIcon = (message: any) => {
  return isOrderMessage(message) || isRefundMessage(message)
}

// 处理跳转（订单或退款）
const handleOrderJump = (message: any) => {
  try {
    // 判断是订单消息还是退款消息
    if (isOrderMessage(message)) {
      handleOrderDetailJump(message)
    } else if (isRefundMessage(message)) {
      handleRefundDetailJump(message)
    }
  } catch (error) {
    console.error('❌ 跳转失败:', error)
  }
}

// 处理订单详情跳转
const handleOrderDetailJump = (message: any) => {
  let actionUrl = null

  // 优先从notification_data中获取action_url
  if (message.notification_data && message.notification_data.action_url) {
    actionUrl = message.notification_data.action_url
  } else if (message.metadata && message.metadata.action_url) {
    actionUrl = message.metadata.action_url
  } else if (message.data && message.data.action_url) {
    actionUrl = message.data.action_url
  }

  // 如果有action_url，直接使用
  if (actionUrl) {
    console.log('🔗 使用action_url跳转到订单详情:', actionUrl)

    if (typeof window !== 'undefined') {
      window.location.href = actionUrl
    }
    return
  }

  // 如果没有action_url，回退到原有逻辑（基于订单ID构建URL）
  let orderId = null

  // 从不同的数据结构中提取订单ID
  if (message.metadata && message.metadata.order_id) {
    orderId = message.metadata.order_id
  } else if (message.notification_data && message.notification_data.order_id) {
    orderId = message.notification_data.order_id
  } else if (message.data && message.data.order_id) {
    orderId = message.data.order_id
  }

  if (orderId) {
    // 根据用户类型确定跳转地址
    const userType = getCurrentUserType()
    let orderUrl = ''

    if (userType === 'merchant') {
      orderUrl = `/merchant/order/detail/${orderId}`
    } else {
      orderUrl = `/user/takeout/order/${orderId}`
    }

    console.log('🔗 使用订单ID跳转到订单详情:', orderUrl)

    if (typeof window !== 'undefined') {
      window.location.href = orderUrl
    }
  } else {
    console.warn('⚠️ 无法从消息中提取action_url或订单ID:', message)
  }
}

// 处理退款详情跳转
const handleRefundDetailJump = (message: any) => {
  let actionUrl = null

  // 优先从notification_data中获取action_url
  if (message.notification_data && message.notification_data.action_url) {
    actionUrl = message.notification_data.action_url
  } else if (message.metadata && message.metadata.action_url) {
    actionUrl = message.metadata.action_url
  } else if (message.data && message.data.action_url) {
    actionUrl = message.data.action_url
  }

  // 如果有action_url，直接使用
  if (actionUrl) {
    console.log('🔗 使用action_url跳转到退款详情:', actionUrl)

    if (typeof window !== 'undefined') {
      window.location.href = actionUrl
    }
    return
  }

  // 如果没有action_url，回退到原有逻辑（基于退款ID或订单ID构建URL）
  let refundId = null
  let orderId = null

  // 从不同的数据结构中提取退款ID或订单ID
  if (message.metadata) {
    refundId = message.metadata.refund_id
    orderId = message.metadata.order_id
  } else if (message.notification_data) {
    refundId = message.notification_data.refund_id
    orderId = message.notification_data.order_id
  } else if (message.data) {
    refundId = message.data.refund_id
    orderId = message.data.order_id
  }

  // 优先使用退款ID，如果没有则使用订单ID
  const targetId = refundId || orderId

  if (targetId) {
    // 根据用户类型确定跳转地址
    const userType = getCurrentUserType()
    let refundUrl = ''

    if (userType === 'merchant') {
      // 商家端退款管理页面
      refundUrl = `/merchant/refund/detail/${targetId}`
    } else {
      // 用户端退款详情页面
      refundUrl = `/user/refund/detail/${targetId}`
    }

    console.log('🔗 使用ID跳转到退款详情:', refundUrl)

    if (typeof window !== 'undefined') {
      window.location.href = refundUrl
    }
  } else {
    console.warn('⚠️ 无法从消息中提取action_url、退款ID或订单ID:', message)
  }
}



// 会话成员在线状态管理
const sessionMembersOnlineStatus = ref(new Map<string, Map<string, any>>()) // sessionId -> Map<userId, memberInfo>

// 获取会话在线状态
const getSessionOnlineStatus = (session: any) => {
  if (!session) return 'offline'

  // 首先检查session自身的is_online属性
  if (session.is_online !== undefined) {
    return session.is_online ? 'online' : 'offline'
  }

  // 检查会话成员的在线状态
  const sessionId = session.id.toString()
  const membersStatus = sessionMembersOnlineStatus.value.get(sessionId)

  if (membersStatus && membersStatus.size > 0) {
    // 如果有任何成员在线，则显示为在线
    for (const memberInfo of membersStatus.values()) {
      if (memberInfo.online_status === 'active') {
        return 'online'
      }
    }
  }

  return 'offline'
}

// 注意：会话成员在线状态更新现在通过ChatStore的updateSessionMembersOnlineStatus方法处理
// 这样可以确保状态在整个应用中保持一致

// 获取最后消息预览
const getLastMessagePreview = (session: any) => {
  if (!session) return '暂无消息'

  console.log('🔍 ChatWindow getLastMessagePreview 调试:', {
    sessionId: session.id,
    hasLastMessage: !!session.last_message,
    lastMessage: session.last_message,
    senderName: session.last_message?.sender_name
  })

  // 检查是否有最后消息
  if (session.last_message && session.last_message.content) {
    const content = session.last_message.content

    // 优先使用 sender_name，如果没有则尝试其他字段
    let senderName = session.last_message.sender_name

    if (!senderName) {
      console.warn('⚠️ ChatWindow sender_name为空，尝试其他字段:', {
        sessionId: session.id,
        lastMessage: session.last_message
      })
      senderName = '未知用户'
    }

    console.log('📝 ChatWindow 消息预览:', {
      sessionId: session.id,
      senderName,
      content: content.substring(0, 20) + '...'
    })

    // 根据消息类型显示不同的预览
    let preview = ''
    switch (session.last_message.type) {
      case 'text':
        preview = content
        break
      case 'image':
        preview = '[图片]'
        break
      case 'file':
        preview = '[文件]'
        break
      case 'voice':
        preview = '[语音]'
        break
      default:
        preview = content
    }

    // 添加发送者信息（如果不是当前用户）
    const fullPreview = `${senderName}: ${preview}`

    // 限制预览文本长度
    const result = fullPreview.length > 50
      ? fullPreview.substring(0, 50) + '...'
      : fullPreview

    console.log('✅ ChatWindow 最终预览结果:', result)
    return result
  }

  console.log('ℹ️ ChatWindow 无最后消息:', session.id)
  return '暂无消息'
}

// 格式化时间
const formatTime = (timeStr: string | undefined) => {
  if (!timeStr) return ''

  try {
    const date = new Date(timeStr)
    const now = new Date()
    const diff = now.getTime() - date.getTime()

    // 小于1分钟显示"刚刚"
    if (diff < 60 * 1000) {
      return '刚刚'
    }

    // 小于1小时显示"X分钟前"
    if (diff < 60 * 60 * 1000) {
      const minutes = Math.floor(diff / (60 * 1000))
      return `${minutes}分钟前`
    }

    // 小于24小时显示"X小时前"
    if (diff < 24 * 60 * 60 * 1000) {
      const hours = Math.floor(diff / (60 * 60 * 1000))
      return `${hours}小时前`
    }

    // 小于7天显示"X天前"
    if (diff < 7 * 24 * 60 * 60 * 1000) {
      const days = Math.floor(diff / (24 * 60 * 60 * 1000))
      return `${days}天前`
    }

    // 超过7天显示具体日期
    const month = date.getMonth() + 1
    const day = date.getDate()
    return `${month}月${day}日`
  } catch (error) {
    console.error('格式化时间失败:', error)
    return ''
  }
}

const getSessionName = (session: Session | null) => {
  if (!session) return ''

  console.log('🏷️ [ChatWindow] getSessionName 调试:', {
    sessionId: session.id,
    sessionType: session.type,
    title: (session as any).title,
    target_name: (session as any).target_name,
    hasTitle: !!(session as any).title,
    hasTargetName: !!(session as any).target_name
  })

  // 优先使用title字段（已经由generateSessionTitle生成）
  const sessionWithTarget = session as any
  if (sessionWithTarget.title) {
    console.log('✅ [ChatWindow] 使用title字段:', sessionWithTarget.title)
    return sessionWithTarget.title
  }

  // 其次使用target_name字段（后端返回的对话对象名称）
  if (sessionWithTarget.target_name) {
    const name = `与 ${sessionWithTarget.target_name} 的对话`
    console.log('✅ [ChatWindow] 使用target_name字段:', name)
    return name
  }

  // 如果没有target_name，回退到原有的逻辑
  switch (session.type) {
    case SessionType.PRESALE_CONSULTATION:
      return session.product_name || '售前咨询'
    case SessionType.AFTER_SALE_SERVICE:
      return session.order_id ? `订单 ${session.order_id}` : '售后服务'
    case SessionType.FRIEND_CHAT:
      return session.friend_info?.name || '好友聊天'
    case SessionType.CUSTOMER_SERVICE:
      return '客服咨询'
    default:
      return '会话'
  }
}

const getSessionAvatar = (session: Session | null) => {
  if (!session) return ''

  // 优先使用target_avatar字段（使用类型断言）
  const sessionWithAvatar = session as any
  if (sessionWithAvatar.target_avatar) {
    return sessionWithAvatar.target_avatar
  }

  // 回退到其他头像字段
  switch (session.type) {
    case SessionType.PRESALE_CONSULTATION:
      return session.customer_info?.avatar || ''
    case SessionType.AFTER_SALE_SERVICE:
      return session.customer_info?.avatar || ''
    case SessionType.FRIEND_CHAT:
      return session.friend_info?.avatar || ''
    case SessionType.CUSTOMER_SERVICE:
      return '' // CustomerServiceSession 没有 service_info 属性
    default:
      return ''
  }
}

const getSessionStatus = () => {
  if (!isConnected.value) return 'offline'
  if (!currentSession.value) return 'idle'

  switch (currentSession.value.status) {
    case SessionStatus.ACTIVE:
      return 'online'
    case SessionStatus.INACTIVE:
      return 'away'
    case SessionStatus.CLOSED:
      return 'offline'
    default:
      return 'idle'
  }
}

const getSessionStatusText = () => {
  if (!isConnected.value) return '连接已断开'
  if (!currentSession.value) return '未选择会话'

  // 🔍 添加状态调试信息
  const sessionStatus = currentSession.value.status
  console.log('🔍 获取会话状态文本:', {
    sessionId: currentSession.value.id,
    sessionTitle: currentSession.value.title,
    sessionStatus,
    sessionStatusType: typeof sessionStatus,
    isActive: sessionStatus === SessionStatus.ACTIVE,
    isInactive: sessionStatus === SessionStatus.INACTIVE,
    SessionStatusEnum: {
      ACTIVE: SessionStatus.ACTIVE,
      INACTIVE: SessionStatus.INACTIVE,
      CLOSED: SessionStatus.CLOSED
    }
  })

  // 🔥 临时修复：强制显示"在线"状态，避免显示"等待中"
  // TODO: 等状态转换逻辑完全修复后可以移除这个临时方案
  if (currentSession.value && isConnected.value) {
    console.log('🔧 临时修复：强制返回"在线"状态')
    return '在线'
  }

  switch (currentSession.value.status) {
    case SessionStatus.ACTIVE:
      return '在线'
    case SessionStatus.INACTIVE:
      return '等待中'
    case SessionStatus.CLOSED:
      return '已关闭'
    default:
      return '空闲'
  }
}

// 获取WebSocket连接状态文本（使用缓冲状态）
const getConnectionStatusText = () => {
  switch (connectionStatusBuffer.value) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中...'
    case 'reconnecting':
      return '重连中...'
    default:
      return '连接已断开'
  }
}

const handleSessionSelect = async (session: Session) => {
  try {
    console.log('🎯 开始选择会话:', {
      sessionId: session.id,
      sessionTitle: session.title,
      sessionType: session.type
    })

    // 设置当前会话ID
    const sessionIdStr = session.id.toString()
    console.log('📝 设置前状态:', {
      oldCurrentSessionId: sessionStore.currentSessionId,
      newSessionIdStr: sessionIdStr,
      sessionsCount: sessions.value.length
    })

    sessionStore.currentSessionId = sessionIdStr
    showSessionInfo.value = false

    console.log('📝 已设置currentSessionId:', sessionIdStr)

    // 等待响应式更新
    await nextTick()

    console.log('🔍 设置后验证:', {
      currentSessionId: sessionStore.currentSessionId,
      currentSession: currentSession.value,
      hasCurrentSession: !!currentSession.value
    })

    // 加载会话的历史消息
    await loadSessionMessages(sessionIdStr)

    // 标记会话消息为已读
    await markSessionAsRead(sessionIdStr)

    // 发送会话选择事件
    emit('sessionSelect', session)

    console.log('✅ 会话选择完成:', session.title || session.id)
    console.log('🔍 最终currentSession状态:', {
      hasCurrentSession: !!currentSession.value,
      currentSessionId: currentSession.value?.id,
      currentSessionTitle: currentSession.value?.title
    })
  } catch (error) {
    console.error('❌ 选择会话失败:', error)
  }
}

// 加载会话消息
const loadSessionMessages = async (sessionId: string) => {
  try {
    console.log(`🔄 开始加载会话 ${sessionId} 的历史消息...`)
    console.log(`🔍 加载前currentSession状态:`, {
      currentSessionId: sessionStore.currentSessionId,
      hasCurrentSession: !!currentSession.value,
      currentCategory: currentMessageCategory.value
    })

    const messageStore = useMessageStore()
    // 🔧 修复竞态问题：传递当前消息分类参数，确保消息加载到正确的多维数组位置
    await messageStore.loadMessages(sessionId, currentMessageCategory.value)

    const messages = messageStore.messages
    console.log(`✅ 已加载会话 ${sessionId} 在分类 ${currentMessageCategory.value} 下的 ${Array.from(messages.values()).flat().length} 条消息`)

    console.log(`🔍 加载后currentSession状态:`, {
      currentSessionId: sessionStore.currentSessionId,
      hasCurrentSession: !!currentSession.value
    })

    if (Array.from(messages.values()).flat().length > 0) {
      console.log(`📋 最新消息:`, Array.from(messages.values()).flat().slice(-1)[0])
    } else {
      console.log(`ℹ️ 会话 ${sessionId} 暂无历史消息`)
    }

    // 滚动到底部显示最新消息
    nextTick(() => {
      if (messageListRef.value) {
        messageListRef.value.scrollToBottom()
      }
    })

  } catch (error) {
    console.error(`❌ 加载会话 ${sessionId} 消息失败:`, error)
  }
}

// 标记会话为已读
const markSessionAsRead = async (sessionId: string) => {
  try {
    console.log(`🔄 标记会话 ${sessionId} 为已读...`)

    // 查找会话
    const session = sessions.value.find(s => s.id.toString() === sessionId)
    if (!session) {
      console.log(`⚠️ 未找到会话 ${sessionId}`)
      return
    }

    if (session.unread_count > 0) {
      const originalUnreadCount = session.unread_count

      // 调用API标记会话为已读
      try {
        await messageCategoryService.markSessionAsRead(parseInt(sessionId))
        console.log(`✅ API调用成功，会话 ${sessionId} 已标记为已读`)
      } catch (apiError) {
        console.error(`❌ API调用失败:`, apiError)
        // 即使API调用失败，也继续更新本地状态
      }

      // 更新本地会话的未读数量
      session.unread_count = 0
      console.log(`📊 会话 ${sessionId} 未读数量: ${originalUnreadCount} → 0`)

      // 更新分类的未读数量
      await updateCategoryUnreadCount(currentMessageCategory.value)

      // 更新总的未读统计
      await updateUnreadCount()
    } else {
      console.log(`ℹ️ 会话 ${sessionId} 已经是已读状态`)
    }
  } catch (error) {
    console.error(`❌ 标记会话 ${sessionId} 已读失败:`, error)
  }
}

const handleSessionCreate = async () => {
  try {
    // 创建会话的逻辑暂时简化
    console.log('创建新会话')
  } catch (error) {
    console.error('创建会话失败:', error)
  }
}

// const handleSessionUpdate = (session: Session) => {
//   // 更新会话的逻辑暂时简化
//   console.log('更新会话:', session)
// }

const handleSessionClose = async () => {
  const currentSessionId = sessionStore.currentSessionId
  if (!currentSessionId) return

  try {
    // 关闭会话的逻辑暂时简化
    sessionStore.currentSessionId = null
    showSessionInfo.value = false
  } catch (error) {
    console.error('关闭会话失败:', error)
  }
}

// 调试当前状态
const debugCurrentState = () => {
  console.log('🔍 当前聊天组件状态调试:')
  console.log('  - currentMessageCategory:', currentMessageCategory.value)
  console.log('  - currentSessionId:', currentSessionId.value)
  console.log('  - sessions.length:', sessions.value.length)
  console.log('  - filteredSessions.length:', filteredSessions.value.length)
  console.log('  - currentSession:', currentSession.value)
  console.log('  - hasCurrentSession:', !!currentSession.value)

  if (sessions.value.length > 0) {
    console.log('  - 所有会话:', sessions.value.map(s => ({
      id: s.id,
      title: s.title,
      type: s.type
    })))
  }

  if (filteredSessions.value.length > 0) {
    console.log('  - 过滤后会话:', filteredSessions.value.map(s => ({
      id: s.id,
      title: s.title,
      type: s.type
    })))
  }
}

// Lifecycle
onMounted(async () => {
  try {
    console.log('🚀 初始化聊天客户端')

    // 🔧 设置聊天UI为可见状态
    chatStore.setChatUIVisible(true, currentSessionId.value || undefined)
    console.log('🔧 [ChatWindow] 设置聊天UI为可见状态')

    // 初始化WebSocket连接
    await initializeWebSocket()

    // 加载消息分类列表
    await loadMessageCategories()

    // 更新未读统计
    await updateUnreadCount()

    // 初始化默认分类（客服消息）
    console.log('📂 初始化默认分类: SERVICE')
    console.log('📊 初始化前状态:', {
      currentMessageCategory: currentMessageCategory.value,
      sessions: sessions.value.length
    })

    await handleCategoryChange(MessageCategoryType.SERVICE)

    // 初始化完成后，更新连接状态
    console.log('🔄 初始化完成，更新连接状态')
    isInitializing.value = false

    // 立即检查并更新连接状态
    const actualStatus = getActualWebSocketStatus()
    console.log('🔍 初始化完成后的实际状态:', actualStatus)
    updateConnectionStatusBuffer(actualStatus)

    console.log('✅ 聊天客户端初始化完成')
    debugCurrentState()

    // 模拟设置一些会话的在线状态用于演示
    setTimeout(() => {
      if (sessions.value.length > 0) {
        // 设置第一个会话为在线状态
        ;(sessions.value[0] as any).is_online = true
        console.log('🟢 设置第一个会话为在线状态')

        // 其他会话设为离线状态
        for (let i = 1; i < sessions.value.length; i++) {
          ;(sessions.value[i] as any).is_online = false
        }
      }
    }, 1000)



    // 暴露调试函数到全局作用域
    if (typeof window !== 'undefined') {
      ;(window as any).debugChatState = debugCurrentState
      ;(window as any).testSessionSelect = async (sessionId: any) => {
        console.log('🧪 测试会话选择:', sessionId)
        const session = sessions.value.find(s => s.id.toString() === sessionId.toString())
        if (session) {
          await handleSessionSelect(session)
        } else {
          console.error('未找到会话:', sessionId)
        }
      }
      ;(window as any).forceSetCurrentSession = (sessionId: any) => {
        console.log('🔧 强制设置currentSessionId:', sessionId)
        sessionStore.currentSessionId = sessionId.toString()
        console.log('🔍 设置后状态:', {
          currentSessionId: sessionStore.currentSessionId,
          currentSession: currentSession.value
        })
      }
      ;(window as any).checkDOMElements = () => {
        console.log('🔍 DOM元素检查:')
        const emptyDiv = document.querySelector('.chat-window__empty')
        const chatDiv = document.querySelector('.chat-window__chat')
        const messageList = document.querySelector('.message-list')
        const messageInput = document.querySelector('.message-input')

        console.log('  - .chat-window__empty:', emptyDiv ? '存在' : '不存在')
        console.log('  - .chat-window__chat:', chatDiv ? '存在' : '不存在')
        console.log('  - .message-list:', messageList ? '存在' : '不存在')
        console.log('  - .message-input:', messageInput ? '存在' : '不存在')

        if (emptyDiv) {
          console.log('  - empty div display:', getComputedStyle(emptyDiv).display)
          console.log('  - empty div visibility:', getComputedStyle(emptyDiv).visibility)
        }
        if (chatDiv) {
          console.log('  - chat div display:', getComputedStyle(chatDiv).display)
          console.log('  - chat div visibility:', getComputedStyle(chatDiv).visibility)
        }

        return { emptyDiv, chatDiv, messageList, messageInput }
      }
      ;(window as any).forceRefreshInterface = () => {
        console.log('🔄 强制刷新界面...')
        // 临时清空再恢复，强制触发响应式更新
        const tempSessionId = sessionStore.currentSessionId
        sessionStore.currentSessionId = null
        setTimeout(() => {
          sessionStore.currentSessionId = tempSessionId
          console.log('🔄 界面刷新完成')
        }, 100)
      }
      ;(window as any).forceShowChatInterface = () => {
        console.log('🔧 强制显示聊天界面...')
        const emptyDiv = document.querySelector('.chat-window__empty')
        const chatDiv = document.querySelector('.chat-window__chat')

        if (emptyDiv) {
          ;(emptyDiv as HTMLElement).style.display = 'none'
          console.log('✅ 隐藏空状态界面')
        }

        if (chatDiv) {
          ;(chatDiv as HTMLElement).style.display = 'flex'
          ;(chatDiv as HTMLElement).style.visibility = 'visible'
          console.log('✅ 显示聊天界面')
        } else {
          console.error('❌ 未找到聊天界面DOM元素')
        }

        // 检查子组件
        const messageList = document.querySelector('.message-list')
        const messageInput = document.querySelector('.message-input')
        console.log('📋 子组件状态:', {
          messageList: messageList ? '存在' : '不存在',
          messageInput: messageInput ? '存在' : '不存在'
        })
      }
      ;(window as any).forceToggleChatInterface = () => {
        console.log('🔧 强制切换聊天界面状态...')
        forceShowChat.value = !forceShowChat.value
        forceHideEmpty.value = !forceHideEmpty.value
        console.log('🔧 强制状态已更新:', {
          forceShowChat: forceShowChat.value,
          forceHideEmpty: forceHideEmpty.value
        })
      }
      console.log('💡 调试函数已暴露:')
      console.log('  - window.debugChatState()')
      console.log('  - window.testSessionSelect(sessionId)')
      console.log('  - window.forceSetCurrentSession(sessionId)')
      console.log('  - window.checkDOMElements()')
      console.log('  - window.forceRefreshInterface()')
      console.log('  - window.forceShowChatInterface()')
      console.log('  - window.forceToggleChatInterface()')
    }
  } catch (error) {
    console.error('❌ 初始化聊天失败:', error)
  }
})

// 初始化WebSocket连接
const initializeWebSocket = async () => {
  try {
    // 优先使用WebSocket服务
    const wsService = getWebSocketService()
    if (wsService) {
      // 检查是否已经连接，如果没有连接则尝试连接
      if (!wsService.isConnected) {
        await wsService.initializeChat({})
        console.log('WebSocket服务已连接')
      } else {
        console.log('WebSocket服务已经连接，无需重复连接')
      }

      // 设置WebSocket消息监听
      setupWebSocketMessageListener()
      console.log('使用chatStore，通过watch监听状态变化')
    } else {
      // 使用默认的聊天WebSocket连接
      await chatStore.initializeChat({})
      console.log('默认聊天WebSocket已连接')
    }
  } catch (error) {
    console.error('WebSocket连接失败:', error)
  }
}

// 设置WebSocket消息监听
const setupWebSocketMessageListener = () => {
  const wsService = getWebSocketService()
  if (!wsService) {
    console.warn('⚠️ 无法获取WebSocket服务，跳过消息监听设置')
    return
  }

  // WebSocket消息监听已通过ChatStore和ChatClient的事件系统处理
  // 会话成员在线状态更新会通过ChatStore的updateSessionMembersOnlineStatus方法处理
  console.log('✅ WebSocket消息监听器已设置（通过ChatStore事件系统）')
}

// 处理WebSocket消息
// const handleWebSocketMessage = (message: any) => {
//   console.log('📨 ChatWindow收到WebSocket消息:', message)

//   // 兼容不同的消息格式
//   const messageType = message.type || message.event
//   const messageData = message.message || message.data
//   const sessionId = message.session_id

//   console.log('🔍 消息格式分析:', {
//     originalType: message.type,
//     originalEvent: message.event,
//     resolvedType: messageType,
//     hasData: !!messageData,
//     sessionId: sessionId
//   })

//   // 根据消息类型处理
//   switch (messageType) {
//     case 'new_message':
//       console.log('🆕 处理新消息:', messageData)
//       handleNewMessage(messageData, sessionId?.toString())
//       break
//     case 'message_status_update':
//       handleMessageStatusUpdate(messageData)
//       break
//     case 'session_update':
//       console.log('🔄 处理会话更新:', message)
//       handleSessionUpdate(message)
//       break
//     default:
//       console.log('❓ 未知消息类型:', messageType, message)
//   }
// }

// 处理新消息
// const handleNewMessage = (messageData: any, sessionId?: string) => {
//   console.log('🆕 处理新消息数据:', messageData, '会话ID:', sessionId)

//   if (!messageData) {
//     console.warn('⚠️ 消息数据为空')
//     return
//   }

//   const targetSessionId = sessionId || messageData.session_id?.toString()

//   if (!targetSessionId) {
//     console.warn('⚠️ 无法确定目标会话ID', {
//       sessionId,
//       messageSessionId: messageData.session_id,
//       messageData
//     })
//     return
//   }

//   // 检查是否是自己发送的消息（额外保护）
//   const isOwnMessage = messageData.sender_type === currentUserType

//   if (isOwnMessage) {
//     console.log('🚫 ChatWindow跳过自己发送的消息:', {
//       messageId: messageData.id,
//       senderType: messageData.sender_type,
//       currentUserType: currentUserType,
//       content: messageData.content?.substring(0, 30) + '...'
//     })
//     return
//   }

//   console.log('🎯 目标会话ID:', targetSessionId, '类型:', typeof targetSessionId)
//   console.log('🎯 当前会话ID:', currentSessionId.value, '类型:', typeof currentSessionId.value)
//   console.log('📊 当前会话总数:', sessions.value.length)
//   console.log('✅ 处理来自商家的消息:', {
//     messageId: messageData.id,
//     senderType: messageData.sender_type,
//     content: messageData.content?.substring(0, 30) + '...'
//   })

//   // 如果是当前会话的消息，直接添加到消息列表
//   // 确保会话ID比较时类型匹配
//   const isCurrentSession = currentSessionId.value?.toString() === targetSessionId.toString()

//   if (isCurrentSession) {
//     console.log('✅ 消息属于当前会话:', targetSessionId, '当前会话ID:', currentSessionId.value)
//     console.log('📝 当前本地消息列表长度:', currentSessionMessages.value.length)

//     // 注意：不再直接添加到本地消息列表
//     // 因为我们现在通过store同步机制来更新本地状态
//     // 消息会通过 messageStore.addMessage() 添加到store
//     // 然后通过watch监听器自动同步到本地状态
//     console.log('💡 消息将通过store同步机制更新到界面')
//   }

//   // 更新会话的最后消息和未读数量
//   // 尝试多种方式查找会话：数字ID、字符串ID
//   const session = sessions.value.find(s =>
//     s.id.toString() === targetSessionId ||
//     s.id === parseInt(targetSessionId) ||
//     s.id === targetSessionId
//   )

//   if (session) {
//     console.log('🔄 更新会话信息:', session.title, '会话ID匹配:', {
//       sessionId: session.id,
//       targetSessionId,
//       sessionIdType: typeof session.id,
//       targetSessionIdType: typeof targetSessionId
//     })

//     // 更新最后消息信息
//     session.last_message = {
//       type: messageData.type || 'text',
//       content: messageData.content,
//       preview_text: messageData.content,
//       created_at: messageData.created_at
//     }
//     session.last_message_time = messageData.created_at

//     // 如果不是当前会话，增加未读数量
//     if (!isCurrentSession) {
//       session.unread_count = (session.unread_count || 0) + 1
//       console.log('📊 会话未读数量增加:', session.unread_count)
//     }
//   } else {
//     console.warn('⚠️ 未找到对应的会话:', targetSessionId)
//     console.warn('📋 当前所有会话:', sessions.value.map(s => ({
//       id: s.id,
//       idType: typeof s.id,
//       title: s.title
//     })))
//   }

//   // 更新消息存储（用于其他组件）
//   try {
//     const messageStore = useMessageStore()
//     // 确保消息数据包含正确的session_id
//     const messageForStore = {
//       ...messageData,
//       session_id: parseInt(targetSessionId) // 确保session_id是数字类型
//     }
//     console.log('📝 添加消息到store:', {
//       messageId: messageForStore.id,
//       sessionId: messageForStore.session_id,
//       content: messageForStore.content?.substring(0, 30) + '...'
//     })
//     messageStore.addMessage(messageForStore)
//   } catch (error) {
//     console.error('❌ 更新消息存储失败:', error, {
//       targetSessionId,
//       messageData
//     })
//   }
// }

// 处理消息状态更新
// const handleMessageStatusUpdate = (statusData: any) => {
//   const messageStore = useMessageStore()
//   const existingMessage = messageStore.messageCache.get(statusData.message_id.toString())
//   if (existingMessage) {
//     const updatedMessage = { ...existingMessage, status: statusData.status }
//     messageStore.updateMessage(updatedMessage)
//   }
// }

// 处理WebSocket连接成功
// const handleWebSocketConnected = () => {
//   console.log('WebSocket连接成功')
// }

// 处理WebSocket断开连接
// const handleWebSocketDisconnected = () => {
//   console.log('WebSocket连接断开')
// }

onUnmounted(() => {
  // 🔧 设置聊天UI为不可见状态
  chatStore.setChatUIVisible(false)
  console.log('🔧 [ChatWindow] 设置聊天UI为不可见状态')

  // 只清理事件监听器，不断开WebSocket连接
  // WebSocket连接应该作为后台服务持续运行，用于接收订单消息、系统通知等
  try {
    const wsService = getWebSocketService()
    if (wsService) {
      console.log('🧹 清理ChatWindow，保持WebSocket连接')
      // chatStore不支持事件监听器，无需清理
      // 注意：不调用 disconnect()，让WebSocket作为后台服务继续运行
    } else {
      console.log('🧹 清理默认聊天Store，保持WebSocket连接')
      // 对于默认的chatStore，也不应该断开连接
      // chatStore.disconnect() // 注释掉这行
    }
    console.log('✅ ChatWindow事件监听器已清理，WebSocket连接保持运行')
  } catch (error) {
    console.error('❌ 清理ChatWindow事件监听器失败:', error)
  }
})

// Watch
watch(
  () => sessionStore.currentSessionId,
  (sessionId) => {
    console.log('🔍 currentSessionId变化:', sessionId)
    if (sessionId) {
      console.log('切换到会话:', sessionId)
    }
  },
  { immediate: true }
)

watch(
  () => currentSession.value,
  (newSession, oldSession) => {
    console.log('🔍 currentSession变化:', {
      old: oldSession ? `${oldSession.id}:${oldSession.title}` : 'null',
      new: newSession ? `${newSession.id}:${newSession.title}` : 'null'
    })
  },
  { immediate: true }
)

watch(
  () => sessions.value.length,
  (newLength, oldLength) => {
    console.log('🔍 sessions数量变化:', `${oldLength || 0} → ${newLength}`)
    if (newLength > 0) {
      console.log('🔍 当前所有会话:', sessions.value.map(s => ({
        id: s.id,
        title: s.title,
        type: s.type
      })))
    }
  },
  { immediate: true }
)

watch(
  () => isMinimized.value,
  (minimized) => {
    if (!minimized && sessionStore.currentSessionId) {
      // 展开时滚动到底部
      nextTick(() => {
        // 滚动到底部的逻辑暂时简化
        console.log('滚动到底部')
      })
    }
  }
)

// 统一的WebSocket状态监听器
watch(
  [
    () => {
      const wsService = getWebSocketService()
      return wsService?.clientStatus
    },
    () => {
      const wsService = getWebSocketService()
      return wsService?.isConnected
    },
    () => isConnected.value
  ],
  ([serviceStatus, serviceConnected, storeConnected]) => {
    console.log('ChatWindow: WebSocket状态监听触发:', {
      serviceStatus,
      serviceConnected,
      storeConnected,
      isInitializing: isInitializing.value,
      hasWebSocketService: !!getWebSocketService()
    })

    // 获取最新的实际状态
    const actualStatus = getActualWebSocketStatus()
    console.log('ChatWindow: 计算出的实际状态:', actualStatus)

    // 如果组件正在初始化且状态是connected，立即更新
    if (isInitializing.value && actualStatus === 'connected') {
      console.log('ChatWindow: 初始化中检测到连接状态，立即更新')
      connectionStatusBuffer.value = 'connected'
      return
    }

    // 如果组件正在初始化且状态不是connected，跳过更新
    if (isInitializing.value) {
      console.log('ChatWindow: 组件初始化中，跳过断开状态更新')
      return
    }

    // 更新缓冲状态
    updateConnectionStatusBuffer(actualStatus)
  },
  { immediate: true, deep: true } // 恢复立即触发，但在回调中处理初始化逻辑
)

// 监听会话在线状态变化
watch(
  () => sessions.value.map(s => ({ id: s.id, is_online: (s as any).is_online })),
  (newStatuses, oldStatuses) => {
    if (oldStatuses && newStatuses.length === oldStatuses.length) {
      // 检查是否有在线状态变化
      const changes = newStatuses.filter((newStatus, index) => {
        const oldStatus = oldStatuses[index]
        return oldStatus && newStatus.is_online !== oldStatus.is_online
      })

      if (changes.length > 0) {
        console.log('🔄 检测到会话在线状态变化:', changes)
        // 触发界面重新渲染（Vue的响应式系统会自动处理）
      }
    }
  },
  { deep: true }
)



// 添加一个刷新连接状态的方法
const refreshConnectionStatus = () => {
  console.log('🔄 刷新连接状态')

  // 重新检查WebSocket服务状态
  const wsService = getWebSocketService()
  if (wsService) {
    console.log('🔍 检查WebSocket服务状态:', {
      status: wsService.clientStatus,
      isConnected: wsService.isConnected,
      wsReadyState: 'N/A (chatStore不提供此信息)'
    })
  } else {
    console.log('⚠️ 未找到WebSocket服务')
  }

  const actualStatus = getActualWebSocketStatus()
  console.log('🔍 刷新后的实际状态:', actualStatus)

  // 直接更新缓冲状态，不使用延迟
  connectionStatusBuffer.value = actualStatus as any

  // 重新启动状态监听器
  isInitializing.value = false
}

// 暴露刷新方法给父组件使用
defineExpose({
  refreshConnectionStatus
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (connectionStatusTimer.value) {
    clearTimeout(connectionStatusTimer.value)
    connectionStatusTimer.value = null
  }
})
</script>

<style scoped>
.chat-window {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  width: 1000px;
  height: 650px;
  max-width: 95vw;
  max-height: 90vh;
}

.chat-window--minimized {
  height: 60px;
}

.chat-window__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
  z-index: 10;
}

.chat-window__header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-window__avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}

.chat-window__avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chat-window__avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.chat-window__info {
  display: flex;
  flex-direction: column;
}

.chat-window__title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.chat-window__status {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

.chat-window__status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
}

.chat-window__status-dot--online {
  background-color: #10b981;
}

.chat-window__status-dot--away {
  background-color: #f59e0b;
}

.chat-window__status-dot--offline {
  background-color: #9ca3af;
}

.chat-window__status-dot--idle {
  background-color: #3b82f6;
}

.chat-window__connection-status {
  display: flex;
  align-items: center;
  font-size: 11px;
  color: #9ca3af;
  margin: 2px 0 0 0;
}

.chat-window__connection-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 4px;
}

.chat-window__connection-dot--connected {
  background-color: #10b981;
}

.chat-window__connection-dot--connecting {
  background-color: #f59e0b;
  animation: pulse 1.5s ease-in-out infinite;
}

.chat-window__connection-dot--reconnecting {
  background-color: #f59e0b;
  animation: pulse 1.5s ease-in-out infinite;
}

.chat-window__connection-dot--disconnected {
  background-color: #ef4444;
}

/* 连接中的脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 消息分类按钮样式 - 顶部水平排列 */
.chat-window__category-tabs {
  display: flex;
  flex-direction: row;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding: 12px 16px;
  gap: 8px;
  overflow-x: auto;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #fff;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: fit-content;
  flex-shrink: 0;
}

.category-tab:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.category-tab--active {
  background-color: #eff6ff;
  border-color: #3b82f6;
  color: #3b82f6;
}

.category-tab--chat.category-tab--active {
  background-color: #eff6ff;
  border-color: #3b82f6;
  color: #3b82f6;
}

.category-tab--system.category-tab--active {
  background-color: #fef3c7;
  border-color: #f59e0b;
  color: #f59e0b;
}

.category-tab--order.category-tab--active {
  background-color: #d1fae5;
  border-color: #10b981;
  color: #10b981;
}

.category-tab--service.category-tab--active {
  background-color: #ede9fe;
  border-color: #8b5cf6;
  color: #8b5cf6;
}

.category-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.category-title {
  font-size: 13px;
  font-weight: 500;
}

.category-badge {
  background-color: #ef4444;
  color: #fff;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 5px;
  border-radius: 8px;
  min-width: 16px;
  text-align: center;
  line-height: 1;
}

/* 分类会话列表样式 */
.category-session-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.category-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.session-count {
  font-size: 12px;
  color: #6b7280;
}

.session-items {
  flex: 1;
  overflow-y: auto;
}

.session-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s;
}

.session-item:hover {
  background-color: #f9fafb;
}

.session-item--active {
  background-color: #eff6ff;
  border-left: 3px solid #3b82f6;
}

.session-item--unread {
  background-color: #fefefe;
}

.session-item--unread .session-title {
  font-weight: 600;
}

.session-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.session-status-dot {
  position: absolute;
  bottom: 0px;
  right: 0px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  z-index: 10;
}

.session-status-dot--online {
  background-color: #10b981; /* 绿色 */
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
}

.session-status-dot--offline {
  background-color: #6b7280; /* 灰色 */
}

.session-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #3b82f6;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.session-info {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
}

.typing-indicator {
  font-size: 12px;
  color: #10b981;
  font-weight: 400;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.session-preview {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.session-time {
  font-size: 11px;
  color: #9ca3af;
}

.session-status {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.unread-badge {
  background-color: #ef4444;
  color: #fff;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.empty-sessions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 16px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  color: #d1d5db;
  margin-bottom: 12px;
}

.empty-sessions p {
  margin: 0;
  font-size: 14px;
}

.chat-window__header-right {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 20;
}

.chat-window__action-btn {
  padding: 8px;
  border-radius: 6px;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
  z-index: 30;
  pointer-events: auto;
}

.chat-window__action-btn:hover {
  background-color: #e5e7eb;
}

.chat-window__action-btn--close:hover {
  background-color: #fef2f2;
  color: #dc2626;
}

.chat-window__icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

.chat-window__body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-window__main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.chat-window__sidebar {
  width: 240px;
  border-right: 1px solid #e5e7eb;
  background-color: #f9fafb;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-window__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}



.chat-window__empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 32px;
}

.chat-window__empty-icon {
  width: 64px;
  height: 64px;
  color: #9ca3af;
  margin-bottom: 16px;
}

.chat-window__empty-icon svg {
  width: 100%;
  height: 100%;
  fill: currentColor;
}

.chat-window__empty h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
}

.chat-window__empty p {
  color: #6b7280;
  margin-bottom: 24px;
}

.chat-window__start-btn {
  padding: 8px 24px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chat-window__start-btn:hover {
  background-color: #2563eb;
}

.chat-window__chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%; /* 确保有明确的高度 */
  min-height: 0; /* 允许flex子项收缩 */
}

/* 优化的消息列表样式 */
.message-list-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 16px;
  background: linear-gradient(to bottom, #f8fafc, #f1f5f9);
  min-height: 0; /* 关键：允许flex子项收缩 */
  max-height: 100%; /* 确保不会超出父容器 */
  scroll-behavior: smooth; /* 平滑滚动 */
}

/* 自定义滚动条样式 */
.message-list-container::-webkit-scrollbar {
  width: 6px;
}

.message-list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.message-list-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.message-list-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.loading-messages,
.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
  text-align: center;
}

.loading-messages p,
.empty-messages p {
  font-size: 16px;
  margin: 0;
  font-weight: 500;
}

.loading-messages::before {
  content: '';
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.empty-messages::before {
  content: '💬';
  font-size: 48px;
  margin-bottom: 16px;
}

/* 加载更多消息样式 */
.loading-more-messages,
.no-more-messages {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
  color: #6b7280;
  font-size: 14px;
  text-align: center;
  border-bottom: 1px solid #f3f4f6;
  background-color: #fafafa;
}

.loading-more-messages {
  gap: 8px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.no-more-messages {
  color: #9ca3af;
  font-style: italic;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 8px 0;
}

.message-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  max-width: 80%;
}

.message-item.message-own {
  align-self: flex-end;
  flex-direction: row;
}

.message-item.message-other {
  align-self: flex-start;
  flex-direction: row;
}

/* 头像样式 */
.message-avatar {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
}

/* 左侧消息的头像（对方消息） */
.message-avatar--left {
  order: 1;
}

/* 右侧消息的头像（自己的消息） */
.message-avatar--right {
  order: 3;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e5e7eb;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  color: white;
  border: 2px solid #e5e7eb;
}

.avatar-placeholder.avatar-merchant {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.avatar-placeholder.avatar-user {
  background: linear-gradient(135deg, #10b981, #059669);
}

/* 消息气泡样式 */
.message-bubble {
  position: relative;
  background: white;
  border-radius: 16px;
  padding: 12px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  min-width: 120px;
  max-width: 100%;
  order: 2;
  flex: 1;
}

/* 自己发送的消息气泡 */
.message-bubble--own {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  color: white !important;
  border-color: #2563eb !important;
}

/* 对方发送的消息气泡 */
.message-bubble--other {
  background: white !important;
  color: #1f2937 !important;
  border-color: #e5e7eb !important;
}

/* 兼容旧样式 */
.message-item.message-own .message-bubble {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border-color: #2563eb;
}

.message-item.message-other .message-bubble {
  background: white;
  color: #1f2937;
}

/* 消息气泡箭头 */
.message-bubble::before {
  content: '';
  position: absolute;
  top: 12px;
  width: 0;
  height: 0;
  border: 6px solid transparent;
}

.message-item.message-own .message-bubble::before {
  right: -11px;
  border-left-color: #2563eb;
}

.message-item.message-other .message-bubble::before {
  left: -11px;
  border-right-color: white;
}

/* 消息头部 */
.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  gap: 12px;
}

/* 自己消息的头部 - 显示时间和发送者名字，右对齐 */
.message-header--own {
  justify-content: flex-end;
  flex-direction: row-reverse;
}

/* 对方消息的头部 - 显示发送者名字和时间，左对齐 */
.message-header--other {
  justify-content: flex-start;
  flex-direction: row;
}

.message-sender {
  font-size: 12px;
  font-weight: 600;
  opacity: 0.8;
}

.message-sender--own {
  color: rgba(255, 255, 255, 0.9);
  margin-left: 8px;
}

.message-item.message-own .message-sender {
  color: rgba(255, 255, 255, 0.9);
}

.message-item.message-other .message-sender {
  color: #6b7280;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  white-space: nowrap;
}

.message-item.message-own .message-time {
  color: rgba(255, 255, 255, 0.8);
}

.message-item.message-other .message-time {
  color: #9ca3af;
}

/* 消息文本 */
.message-text {
  word-wrap: break-word;
  line-height: 1.5;
  font-size: 14px;
  margin: 0;
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 订单跳转图标 */
.order-jump-icon {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  opacity: 0.7;
  margin-left: 8px;
  position: relative;
  z-index: 2; /* 确保在消息状态图标之上 */
}

.order-jump-icon:hover {
  background-color: rgba(59, 130, 246, 0.1);
  opacity: 1;
  transform: scale(1.1);
}

.order-jump-icon .jump-icon {
  width: 16px;
  height: 16px;
  color: #3b82f6;
  transition: color 0.2s ease;
}

.order-jump-icon:hover .jump-icon {
  color: #2563eb;
}

/* 消息状态 */
.message-status {
  position: absolute;
  bottom: 8px;
  right: 40px; /* 向左移动，为跳转图标让出空间 */
  width: 16px;
  height: 16px;
  z-index: 1;
}

.status-icon {
  width: 100%;
  height: 100%;
}

.message-status.sending {
  color: rgba(255, 255, 255, 0.6);
  animation: pulse 2s infinite;
}

.message-status.sent {
  color: rgba(255, 255, 255, 0.8);
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-item {
    max-width: 90%;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
  }

  .avatar-placeholder {
    font-size: 14px;
  }

  .message-bubble {
    padding: 10px 12px;
    border-radius: 12px;
  }

  .message-text {
    font-size: 13px;
  }

  .message-sender {
    font-size: 11px;
  }

  .message-time {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .message-list-container {
    padding: 16px 12px;
  }

  .messages-list {
    gap: 12px;
  }

  .message-item {
    gap: 8px;
    max-width: 95%;
  }

  .message-avatar {
    width: 28px;
    height: 28px;
  }

  .avatar-placeholder {
    font-size: 12px;
  }
}

/* 简化的输入区域样式 */
.message-input-container {
  flex-shrink: 0; /* 防止被压缩 */
  border-top: 1px solid #e5e7eb;
  padding: 16px;
  background: white;
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.message-textarea {
  flex: 1;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 12px;
  resize: none;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4;
  max-height: 120px;
}

.message-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.send-button {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-button:hover:not(:disabled) {
  background: #2563eb;
}

.send-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.chat-window__info-panel {
  width: 320px;
  border-left: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .chat-window {
    width: 90vw;
    height: 80vh;
    max-width: 90vw;
    max-height: 80vh;
  }
}

@media (max-width: 768px) {
  .chat-window {
    width: 100vw;
    height: 100vh;
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 0;
  }

  .chat-window__sidebar,
  .chat-window__info-panel {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 10;
  }
}

/* 文件上传相关样式 */
.file-upload-container {
  padding: 12px 16px 0;
  background: white;
  border-top: 1px solid #e1e5e9;
}

/* 输入禁用提示样式 */
.input-disabled-notice {
  flex-shrink: 0;
  border-top: 1px solid #e5e7eb;
  padding: 16px;
  background: #f9fafb;
}

.notice-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #6b7280;
  font-size: 14px;
}

.notice-icon {
  font-size: 16px;
}

.notice-text {
  font-weight: 500;
}

.message-file {
  margin-top: 8px;
}

/* 文件消息样式调整 */
.message-bubble .message-file {
  margin-top: 8px;

  :deep(.chat-file-message) {
    max-width: none;

    .file-message .file-container {
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 0.2);

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }

    .image-message .image-container {
      border-radius: 8px;
      overflow: hidden;
    }
  }
}

/* 发送者消息的文件样式 */
.message-own .message-file {
  :deep(.chat-file-message) {
    &.is-sender {
      .file-message .file-container {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: white;

        .file-info {
          .file-name {
            color: white;
          }

          .file-meta {
            color: rgba(255, 255, 255, 0.8);
          }
        }

        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }
      }
    }
  }
}
</style>