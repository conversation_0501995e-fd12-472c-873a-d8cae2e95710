<template>
  <div class="session-info" :class="{ 'session-info--collapsed': collapsed }">
    <!-- 头部 -->
    <div class="session-info__header">
      <h3 class="session-info__title">会话信息</h3>
      <button
        class="session-info__toggle"
        @click="$emit('toggle')"
        :title="collapsed ? '展开' : '收起'"
      >
        <svg class="session-info__toggle-icon" :class="{ 'rotate-180': !collapsed }" viewBox="0 0 24 24">
          <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"/>
        </svg>
      </button>
    </div>
    
    <!-- 内容区域 -->
    <div v-if="!collapsed" class="session-info__content">
      <!-- 会话基本信息 -->
      <div v-if="currentSession" class="session-info__section">
        <div class="session-info__session-header">
          <div class="session-info__session-avatar">
            <img
              v-if="false"
              src=""
              alt=""
              class="session-info__session-avatar-img"
            />
            <div v-else class="session-info__session-avatar-placeholder">
              {{ getAvatarText(getSessionName(currentSession)) }}
            </div>
          </div>
          
          <div class="session-info__session-details">
            <h4 class="session-info__session-name">{{ getSessionName(currentSession) }}</h4>
            <p class="session-info__session-type">{{ getSessionTypeText(currentSession.type) }}</p>
            <p class="session-info__session-status">
              <span class="session-info__status-dot" :class="getStatusClass(currentSession.status)"></span>
              {{ getStatusText(currentSession.status) }}
            </p>
          </div>
        </div>
        
        <!-- 会话操作 -->
        <div class="session-info__actions">
          <button
            class="session-info__action-btn"
            @click="toggleMute"
            :title="'静音'"
          >
            <svg class="session-info__action-icon" viewBox="0 0 24 24">
              <path v-if="false" d="M12,4L9.91,6.09L12,8.18M4.27,3L3,4.27L7.73,9H3V15H7L12,20V13.27L16.25,17.53C15.58,18.04 14.83,18.46 14,18.7V20.77C15.38,20.45 16.63,19.82 17.68,18.96L19.73,21L21,19.73L12,10.73M19,12C19,12.94 18.8,13.82 18.46,14.64L19.97,16.15C20.62,14.91 21,13.5 21,12C21,7.72 18,4.14 14,3.23V5.29C16.89,6.15 19,8.83 19,12M16.5,12C16.5,10.23 15.5,8.71 14,7.97V10.18L16.45,12.63C16.5,12.43 16.5,12.21 16.5,12Z"/>
              <path v-else d="M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.85 14,18.71V20.77C18,19.86 21,16.28 21,12C21,7.72 18,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16.03C15.5,15.29 16.5,13.77 16.5,12M3,9V15H7L12,20V4L7,9H3Z"/>
            </svg>
          </button>
          
          <button
            class="session-info__action-btn"
            @click="togglePin"
            :title="'置顶'"
          >
            <svg class="session-info__action-icon" :class="{ 'text-yellow-500': false }" viewBox="0 0 24 24">
              <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z"/>
            </svg>
          </button>
          
          <button
            class="session-info__action-btn"
            @click="archiveSession"
            title="归档会话"
          >
            <svg class="session-info__action-icon" viewBox="0 0 24 24">
              <path d="M3,3H21V7H3V3M4,8H20V21H4V8M9.5,11A0.5,0.5 0 0,0 9,11.5V13H15V11.5A0.5,0.5 0 0,0 14.5,11H9.5Z"/>
            </svg>
          </button>
          
          <button
            class="session-info__action-btn session-info__action-btn--danger"
            @click="showDeleteConfirm = true"
            title="删除会话"
          >
            <svg class="session-info__action-icon" viewBox="0 0 24 24">
              <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
            </svg>
          </button>
        </div>
      </div>
      
      <!-- 参与者列表 -->
      <div v-if="currentParticipants.length > 0" class="session-info__section">
        <h5 class="session-info__section-title">
          参与者 ({{ currentParticipants.length }})
        </h5>

        <div class="session-info__participants">
          <div
            v-for="participant in currentParticipants"
            :key="participant.user_id"
            class="session-info__participant"
          >
            <div class="session-info__participant-avatar">
              <img
                v-if="participant.avatar"
                :src="participant.avatar"
                :alt="participant.name"
                class="session-info__participant-avatar-img"
              />
              <div v-else class="session-info__participant-avatar-placeholder">
                {{ getAvatarText(participant.name) }}
              </div>
              
              <!-- 在线状态指示器 -->
              <div
                class="session-info__participant-status"
                :class="getOnlineStatusClass(participant.user_id.toString())"
                :title="getOnlineStatusText(participant.user_id.toString())"
              ></div>
            </div>
            
            <div class="session-info__participant-info">
              <p class="session-info__participant-name">{{ participant.name }}</p>
              <p class="session-info__participant-role">{{ getRoleText(participant.role || '') }}</p>
            </div>
            
            <!-- 参与者操作 -->
            <div v-if="canManageParticipants" class="session-info__participant-actions">
              <button
                v-if="participant.role !== 'admin'"
                class="session-info__participant-action"
                @click="removeParticipant(participant.user_id.toString())"
                title="移除参与者"
              >
                <svg class="session-info__participant-action-icon" viewBox="0 0 24 24">
                  <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
        
        <!-- 添加参与者 -->
        <div v-if="canManageParticipants" class="session-info__add-participant">
          <button
            class="session-info__add-participant-btn"
            @click="showAddParticipant = true"
          >
            <svg class="session-info__add-participant-icon" viewBox="0 0 24 24">
              <path d="M15,14C12.33,14 7,15.33 7,18V20H23V18C23,15.33 17.67,14 15,14M6,10V7H4V10H1V12H4V15H6V12H9V10M15,12A4,4 0 0,0 19,8A4,4 0 0,0 15,4A4,4 0 0,0 11,8A4,4 0 0,0 15,12Z"/>
            </svg>
            添加参与者
          </button>
        </div>
      </div>
      
      <!-- 会话设置 -->
      <div class="session-info__section">
        <h5 class="session-info__section-title">设置</h5>
        
        <div class="session-info__settings">
          <div class="session-info__setting-item">
            <label class="session-info__setting-label">
              <input
                type="checkbox"
                v-model="localSettings.mute_notifications"
                @change="updateSettings"
                class="session-info__setting-checkbox"
              />
              <span>消息通知</span>
            </label>
          </div>
          
          <div class="session-info__setting-item">
            <label class="session-info__setting-label">
              <input
                type="checkbox"
                v-model="localSettings.mute_notifications"
                @change="updateSettings"
                class="session-info__setting-checkbox"
              />
              <span>声音提醒</span>
            </label>
          </div>
          
          <div class="session-info__setting-item">
            <label class="session-info__setting-label">
              <input
                type="checkbox"
                v-model="localSettings.auto_reply_enabled"
                @change="updateSettings"
                class="session-info__setting-checkbox"
              />
              <span>自动标记已读</span>
            </label>
          </div>
        </div>
      </div>
      
      <!-- 会话统计 -->
      <div v-if="false" class="session-info__section">
        <h5 class="session-info__section-title">统计信息</h5>
        
        <div class="session-info__stats">
          <div class="session-info__stat-item">
            <span class="session-info__stat-label">总消息数</span>
            <span class="session-info__stat-value">0</span>
          </div>
          
          <div class="session-info__stat-item">
            <span class="session-info__stat-label">今日消息</span>
            <span class="session-info__stat-value">0</span>
          </div>
          
          <div class="session-info__stat-item">
            <span class="session-info__stat-label">创建时间</span>
            <span class="session-info__stat-value">{{ formatDate(currentSession?.created_at) }}</span>
          </div>
          
          <div class="session-info__stat-item">
            <span class="session-info__stat-label">最后活动</span>
            <span class="session-info__stat-value">{{ formatDate(currentSession?.updated_at) }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 删除确认对话框 -->
    <div v-if="showDeleteConfirm" class="session-info__modal-overlay" @click="showDeleteConfirm = false">
      <div class="session-info__modal" @click.stop>
        <h4 class="session-info__modal-title">确认删除</h4>
        <p class="session-info__modal-content">
          确定要删除会话 "{{ getSessionName(currentSession) }}" 吗？此操作不可撤销。
        </p>
        <div class="session-info__modal-actions">
          <button
            class="session-info__modal-btn session-info__modal-btn--secondary"
            @click="showDeleteConfirm = false"
          >
            取消
          </button>
          <button
            class="session-info__modal-btn session-info__modal-btn--danger"
            @click="confirmDelete"
          >
            删除
          </button>
        </div>
      </div>
    </div>
    
    <!-- 添加参与者对话框 -->
    <div v-if="showAddParticipant" class="session-info__modal-overlay" @click="showAddParticipant = false">
      <div class="session-info__modal" @click.stop>
        <h4 class="session-info__modal-title">添加参与者</h4>
        <div class="session-info__modal-content">
          <input
            v-model="searchUserQuery"
            type="text"
            placeholder="搜索用户..."
            class="session-info__search-input"
            @input="searchUsers"
          />
          
          <div v-if="searchResults.length > 0" class="session-info__search-results">
            <div
              v-for="user in searchResults"
              :key="user.id"
              class="session-info__search-result"
              @click="addParticipant(user)"
            >
              <div class="session-info__search-result-avatar">
                <img
                  v-if="user.avatar"
                  :src="user.avatar"
                  :alt="user.name"
                  class="session-info__search-result-avatar-img"
                />
                <div v-else class="session-info__search-result-avatar-placeholder">
                  {{ getAvatarText(user.name) }}
                </div>
              </div>
              <div class="session-info__search-result-info">
                <p class="session-info__search-result-name">{{ user.name }}</p>
                <p class="session-info__search-result-email">{{ user.email }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="session-info__modal-actions">
          <button
            class="session-info__modal-btn session-info__modal-btn--secondary"
            @click="showAddParticipant = false"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useSessionStore, useChatStore } from '../stores'
import type { SessionSettings, User } from '../types'
import { debounce } from 'lodash-es'

// Props
interface Props {
  collapsed?: boolean
}

withDefaults(defineProps<Props>(), {
  collapsed: false
})

// Emits
interface Emits {
  toggle: []
}

defineEmits<Emits>()

// Stores
const sessionStore = useSessionStore()
const chatStore = useChatStore()

// Store refs
const { currentSession, participants } = storeToRefs(sessionStore)
const { currentUser } = storeToRefs(chatStore)

// State
const showDeleteConfirm = ref(false)
const showAddParticipant = ref(false)
const searchUserQuery = ref('')
const searchResults = ref<User[]>([])
const localSettings = ref<SessionSettings>({
  mute_notifications: false,
  auto_reply_enabled: false,
  auto_reply_message: '',
  tags: []
})

// Computed
const canManageParticipants = computed(() => {
  if (!currentSession.value || !currentUser.value) return false
  
  const currentParticipants = getCurrentParticipants()
  const userParticipant = currentParticipants.find(
    p => p.user_id === currentUser.value?.id
  )
  
  return userParticipant?.role === 'admin' || userParticipant?.role === 'moderator'
})

// Computed
const currentParticipants = computed(() => {
  if (!currentSession.value) return []
  return participants.value.get(currentSession.value.id.toString()) || []
})

// Helper functions
const getSessionName = (session: any) => {
  return session?.title || '未知会话'
}

const getCurrentParticipants = () => {
  if (!currentSession.value) return []
  return participants.value.get(currentSession.value.id.toString()) || []
}

// Methods
const getAvatarText = (name: string) => {
  return name.charAt(0).toUpperCase()
}

const getSessionTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'private': '私聊',
    'group': '群聊',
    'channel': '频道',
    'support': '客服'
  }
  return typeMap[type] || type
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'active': '活跃',
    'inactive': '不活跃',
    'archived': '已归档',
    'closed': '已关闭'
  }
  return statusMap[status] || status
}

const getStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    'active': 'bg-green-500',
    'inactive': 'bg-yellow-500',
    'archived': 'bg-gray-500',
    'closed': 'bg-red-500'
  }
  return classMap[status] || 'bg-gray-500'
}

const getRoleText = (role: string) => {
  const roleMap: Record<string, string> = {
    'admin': '管理员',
    'moderator': '协管员',
    'member': '成员',
    'guest': '访客'
  }
  return roleMap[role] || role
}

const getOnlineStatusClass = (_userId: string) => {
  // 简化实现
  return 'bg-gray-400'
}

const getOnlineStatusText = (_userId: string) => {
  // 简化实现
  return '离线'
}

const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }
  
  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }
  
  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  }
  
  // 格式化为日期
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const toggleMute = async () => {
  if (!currentSession.value) return
  
  try {
    await sessionStore.updateSession(currentSession.value.id.toString(), {
      session_id: currentSession.value.id,
      title: currentSession.value.title
    })
  } catch (error) {
    console.error('切换静音状态失败:', error)
  }
}

const togglePin = async () => {
  if (!currentSession.value) return
  
  try {
    await sessionStore.updateSession(currentSession.value.id.toString(), {
      session_id: currentSession.value.id,
      title: currentSession.value.title
    })
  } catch (error) {
    console.error('切换置顶状态失败:', error)
  }
}

const archiveSession = async () => {
  if (!currentSession.value) return
  
  try {
    await sessionStore.archiveSession(currentSession.value.id.toString())
  } catch (error) {
    console.error('归档会话失败:', error)
  }
}

const confirmDelete = async () => {
  if (!currentSession.value) return
  
  try {
    await sessionStore.deleteSession(currentSession.value.id.toString())
    showDeleteConfirm.value = false
  } catch (error) {
    console.error('删除会话失败:', error)
  }
}

const removeParticipant = async (userId: string) => {
  if (!currentSession.value) return
  
  try {
    // 简化实现
    console.log('移除参与者:', userId)
  } catch (error) {
    console.error('移除参与者失败:', error)
  }
}

const searchUsers = debounce(async () => {
  if (!searchUserQuery.value.trim()) {
    searchResults.value = []
    return
  }
  
  try {
    // TODO: 实现用户搜索API
    // const results = await userApi.searchUsers(searchUserQuery.value)
    // searchResults.value = results.filter(user => 
    //   !participants.value.some(p => p.user_id === user.id)
    // )
    searchResults.value = []
  } catch (error) {
    console.error('搜索用户失败:', error)
    searchResults.value = []
  }
}, 300)

const addParticipant = async (user: User) => {
  if (!currentSession.value) return
  
  try {
    // 简化实现
    console.log('添加参与者:', user.id)
    showAddParticipant.value = false
    searchUserQuery.value = ''
    searchResults.value = []
  } catch (error) {
    console.error('添加参与者失败:', error)
  }
}

const updateSettings = async () => {
  if (!currentSession.value) return
  
  try {
    // TODO: 实现会话设置更新API
    console.log('更新会话设置:', localSettings.value)
  } catch (error) {
    console.error('更新设置失败:', error)
  }
}

const loadSessionData = async () => {
  if (!currentSession.value) return
  
  try {
    // 加载参与者
    await sessionStore.loadSessionParticipants(currentSession.value.id.toString())

    // 加载统计信息
    await sessionStore.loadSessionStats(currentSession.value.id.toString())
    
    // 加载会话设置
    // TODO: 实现会话设置加载
  } catch (error) {
    console.error('加载会话数据失败:', error)
  }
}

// Lifecycle
onMounted(() => {
  if (currentSession.value) {
    loadSessionData()
  }
})

// Watch
watch(
  () => currentSession.value?.id,
  (newSessionId) => {
    if (newSessionId) {
      loadSessionData()
    }
  }
)
</script>

<style scoped>
.session-info {
  @apply w-80 bg-white border-l border-gray-200 flex flex-col transition-all duration-300;
}

.session-info--collapsed {
  @apply w-0 overflow-hidden;
}

.session-info__header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.session-info__title {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.session-info__toggle {
  @apply p-1 rounded hover:bg-gray-100 transition-colors;
}

.session-info__toggle-icon {
  @apply w-5 h-5 fill-current text-gray-600 transition-transform duration-200;
}

.session-info__content {
  @apply flex-1 overflow-y-auto;
}

.session-info__section {
  @apply p-4 border-b border-gray-100 last:border-b-0;
}

.session-info__section-title {
  @apply text-sm font-semibold text-gray-700 mb-3 m-0;
}

.session-info__session-header {
  @apply flex items-start space-x-3 mb-4;
}

.session-info__session-avatar {
  @apply relative;
}

.session-info__session-avatar-img {
  @apply w-12 h-12 rounded-full object-cover;
}

.session-info__session-avatar-placeholder {
  @apply w-12 h-12 rounded-full bg-blue-500 text-white flex items-center justify-center font-semibold;
}

.session-info__session-details {
  @apply flex-1 min-w-0;
}

.session-info__session-name {
  @apply text-base font-semibold text-gray-900 mb-1 m-0;
}

.session-info__session-type {
  @apply text-sm text-gray-600 mb-1 m-0;
}

.session-info__session-status {
  @apply flex items-center text-sm text-gray-600 m-0;
}

.session-info__status-dot {
  @apply w-2 h-2 rounded-full mr-2;
}

.session-info__actions {
  @apply flex items-center space-x-2;
}

.session-info__action-btn {
  @apply p-2 rounded hover:bg-gray-100 transition-colors;
}

.session-info__action-btn--danger {
  @apply hover:bg-red-50 hover:text-red-600;
}

.session-info__action-icon {
  @apply w-5 h-5 fill-current text-gray-600;
}

.session-info__action-btn--danger .session-info__action-icon {
  @apply text-red-600;
}

.session-info__participants {
  @apply space-y-3;
}

.session-info__participant {
  @apply flex items-center space-x-3;
}

.session-info__participant-avatar {
  @apply relative;
}

.session-info__participant-avatar-img {
  @apply w-8 h-8 rounded-full object-cover;
}

.session-info__participant-avatar-placeholder {
  @apply w-8 h-8 rounded-full bg-gray-500 text-white flex items-center justify-center text-sm font-semibold;
}

.session-info__participant-status {
  @apply absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white;
}

.session-info__participant-info {
  @apply flex-1 min-w-0;
}

.session-info__participant-name {
  @apply text-sm font-medium text-gray-900 truncate m-0;
}

.session-info__participant-role {
  @apply text-xs text-gray-500 m-0;
}

.session-info__participant-actions {
  @apply flex items-center space-x-1;
}

.session-info__participant-action {
  @apply p-1 rounded hover:bg-red-50 hover:text-red-600 transition-colors;
}

.session-info__participant-action-icon {
  @apply w-4 h-4 fill-current;
}

.session-info__add-participant {
  @apply mt-3;
}

.session-info__add-participant-btn {
  @apply w-full flex items-center justify-center space-x-2 p-2 border border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:text-blue-600 transition-colors;
}

.session-info__add-participant-icon {
  @apply w-4 h-4 fill-current;
}

.session-info__settings {
  @apply space-y-3;
}

.session-info__setting-item {
  @apply flex items-center;
}

.session-info__setting-label {
  @apply flex items-center space-x-2 text-sm text-gray-700 cursor-pointer;
}

.session-info__setting-checkbox {
  @apply rounded border-gray-300 text-blue-600 focus:ring-blue-500;
}

.session-info__stats {
  @apply space-y-2;
}

.session-info__stat-item {
  @apply flex items-center justify-between text-sm;
}

.session-info__stat-label {
  @apply text-gray-600;
}

.session-info__stat-value {
  @apply font-medium text-gray-900;
}

.session-info__modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.session-info__modal {
  @apply bg-white rounded-lg shadow-xl max-w-md w-full mx-4;
}

.session-info__modal-title {
  @apply text-lg font-semibold text-gray-900 p-4 border-b border-gray-200 m-0;
}

.session-info__modal-content {
  @apply p-4 text-gray-700;
}

.session-info__modal-actions {
  @apply flex items-center justify-end space-x-3 p-4 border-t border-gray-200;
}

.session-info__modal-btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
}

.session-info__modal-btn--secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.session-info__modal-btn--danger {
  @apply bg-red-600 text-white hover:bg-red-700;
}

.session-info__search-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-4;
}

.session-info__search-results {
  @apply max-h-48 overflow-y-auto space-y-2;
}

.session-info__search-result {
  @apply flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 cursor-pointer;
}

.session-info__search-result-avatar {
  @apply flex-shrink-0;
}

.session-info__search-result-avatar-img {
  @apply w-8 h-8 rounded-full object-cover;
}

.session-info__search-result-avatar-placeholder {
  @apply w-8 h-8 rounded-full bg-gray-500 text-white flex items-center justify-center text-sm font-semibold;
}

.session-info__search-result-info {
  @apply flex-1 min-w-0;
}

.session-info__search-result-name {
  @apply text-sm font-medium text-gray-900 truncate m-0;
}

.session-info__search-result-email {
  @apply text-xs text-gray-500 truncate m-0;
}
</style>