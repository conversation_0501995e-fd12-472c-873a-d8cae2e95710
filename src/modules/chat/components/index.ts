/**
 * 聊天模块组件导出
 * 统一导出聊天模块的所有Vue组件
 */

// 主要组件
export { default as ChatWindow } from './ChatWindow.vue'
export { default as SessionList } from './SessionList.vue'
export { default as MessageList } from './MessageList.vue'
export { default as MessageInput } from './MessageInput.vue'
export { default as SessionInfo } from './SessionInfo.vue'

// 功能组件
export { default as NotificationToast } from './NotificationToast.vue'
export { default as FileUploadProgress } from './FileUploadProgress.vue'

// 消息分类组件
export { default as MessageCategorySelector } from './MessageCategorySelector.vue'
export { default as MessageCategoryManager } from './MessageCategoryManager.vue'
// 暂时注释掉以避免导入错误
// export { default as MessageCategoryList } from './MessageCategoryList.vue'
export { default as MessageCategoryDetail } from './MessageCategoryDetail.vue'

// 组件类型定义
export interface ChatComponentProps {
  // ChatWindow 组件属性
  ChatWindow?: {
    width?: number
    height?: number
    resizable?: boolean
    draggable?: boolean
    minimizable?: boolean
    maximizable?: boolean
    closable?: boolean
    title?: string
    theme?: 'light' | 'dark'
  }
  
  // SessionList 组件属性
  SessionList?: {
    showSearch?: boolean
    showFilter?: boolean
    showStats?: boolean
    itemHeight?: number
    virtualScroll?: boolean
    groupByDate?: boolean
  }
  
  // MessageList 组件属性
  MessageList?: {
    showSearch?: boolean
    showTimestamp?: boolean
    showAvatar?: boolean
    showSender?: boolean
    groupByDate?: boolean
    virtualScroll?: boolean
    itemHeight?: number
    autoScroll?: boolean
    imagePreview?: boolean
  }
  
  // MessageInput 组件属性
  MessageInput?: {
    placeholder?: string
    maxLength?: number
    showEmoji?: boolean
    showFile?: boolean
    showVoice?: boolean
    showQuickReply?: boolean
    autoResize?: boolean
    maxHeight?: number
    sendOnEnter?: boolean
  }
  
  // SessionInfo 组件属性
  SessionInfo?: {
    showParticipants?: boolean
    showSettings?: boolean
    showStats?: boolean
    collapsible?: boolean
  }
  
  // NotificationToast 组件属性
  NotificationToast?: {
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
    duration?: number
    maxCount?: number
    showClose?: boolean
    showIcon?: boolean
    pauseOnHover?: boolean
  }
  
  // FileUploadProgress 组件属性
  FileUploadProgress?: {
    autoHide?: boolean
    hideDelay?: number
  }
}

// 组件事件定义
export interface ChatComponentEvents {
  // ChatWindow 组件事件
  ChatWindow?: {
    minimize: []
    maximize: []
    restore: []
    close: []
    resize: [width: number, height: number]
    move: [x: number, y: number]
  }
  
  // SessionList 组件事件
  SessionList?: {
    sessionSelect: [sessionId: string]
    sessionCreate: []
    sessionUpdate: [sessionId: string, data: any]
    sessionDelete: [sessionId: string]
    sessionArchive: [sessionId: string]
    sessionPin: [sessionId: string, pinned: boolean]
    sessionMute: [sessionId: string, muted: boolean]
    search: [query: string]
    filter: [filters: any]
  }
  
  // MessageList 组件事件
  MessageList?: {
    messageSelect: [messageId: string]
    messageReply: [messageId: string]
    messageForward: [messageId: string]
    messageEdit: [messageId: string]
    messageDelete: [messageId: string]
    messageCopy: [messageId: string]
    imagePreview: [imageUrl: string]
    fileDownload: [fileId: string]
    loadMore: []
    search: [query: string]
  }
  
  // MessageInput 组件事件
  MessageInput?: {
    send: [content: string, files?: File[]]
    typing: [isTyping: boolean]
    focus: []
    blur: []
    fileSelect: [files: File[]]
    emojiSelect: [emoji: string]
    quickReplySelect: [reply: string]
    voiceStart: []
    voiceEnd: [audioBlob: Blob]
  }
  
  // SessionInfo 组件事件
  SessionInfo?: {
    settingChange: [key: string, value: any]
    participantAdd: [userId: string]
    participantRemove: [userId: string]
    participantRoleChange: [userId: string, role: string]
  }
  
  // NotificationToast 组件事件
  NotificationToast?: {
    click: [notificationId: string]
    close: [notificationId: string]
    action: [notificationId: string, action: string]
  }
  
  // FileUploadProgress 组件事件
  FileUploadProgress?: {
    uploadComplete: [upload: any]
    uploadError: [upload: any, error: string]
  }
}

// 组件插槽定义
export interface ChatComponentSlots {
  // ChatWindow 组件插槽
  ChatWindow?: {
    header?: any
    sidebar?: any
    main?: any
    footer?: any
  }
  
  // SessionList 组件插槽
  SessionList?: {
    header?: any
    search?: any
    filter?: any
    item?: { session: any, index: number }
    empty?: any
    loading?: any
  }
  
  // MessageList 组件插槽
  MessageList?: {
    header?: any
    search?: any
    message?: { message: any, index: number }
    typing?: any
    empty?: any
    loading?: any
  }
  
  // MessageInput 组件插槽
  MessageInput?: {
    prepend?: any
    append?: any
    toolbar?: any
    preview?: any
  }
  
  // SessionInfo 组件插槽
  SessionInfo?: {
    header?: any
    info?: any
    participants?: any
    settings?: any
    stats?: any
  }
}