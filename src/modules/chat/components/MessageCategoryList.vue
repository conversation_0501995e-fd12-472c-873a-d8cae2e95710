<template>
  <div class="message-category-list">
    <!-- 搜索框 -->
    <div class="search-bar">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索消息..."
        prefix-icon="Search"
        clearable
        @input="handleSearch"
      />
    </div>

    <!-- 消息列表 -->
    <div class="message-list" v-loading="loading">
      <div
        v-for="message in messages"
        :key="message.id"
        class="message-item"
        :class="{ 'message-item--unread': message.status === 0 }"
        @click="handleMessageClick(message)"
      >
        <div class="message-item__header">
          <div class="message-item__title">{{ message.title }}</div>
          <div class="message-item__time">{{ formatTime(message.created_at) }}</div>
        </div>
        
        <div class="message-item__content">
          {{ formatMessagePreview(message.content) }}
        </div>
        
        <div v-if="message.notification_type" class="message-item__type">
          {{ getNotificationTypeText(message.notification_type) }}
        </div>
        
        <!-- 未读标识 -->
        <div v-if="message.status === 0" class="message-item__unread-dot"></div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="!loading && messages.length === 0" class="empty-state">
        <div class="empty-state__icon">
          <component :is="getEmptyIcon()" />
        </div>
        <div class="empty-state__text">
          {{ searchKeyword ? '没有找到相关消息' : '暂无消息' }}
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="total > 0" class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { ElInput, ElPagination } from 'element-plus'
import { ChatDotRound, Bell, ShoppingBag } from '@element-plus/icons-vue'
import type { 
  MessageCategoryType, 
  MessageListItem 
} from '../types/message-category'

// 临时创建服务类，避免导入错误
class TempMessageCategoryService {
  async getCategoryMessages(_params: any) {
    // 模拟API调用
    return {
      list: [],
      total: 0,
      page: 1,
      page_size: 20,
      page_count: 0
    }
  }
  
  async markMessageAsRead(messageId: number) {
    console.log('标记消息已读:', messageId)
  }
  
  formatTime(timeStr: string): string {
    if (!timeStr) return ''
    
    try {
      const date = new Date(timeStr)
      const now = new Date()
      const diff = now.getTime() - date.getTime()
      
      if (diff < 60 * 1000) return '刚刚'
      if (diff < 60 * 60 * 1000) return `${Math.floor(diff / (60 * 1000))}分钟前`
      if (diff < 24 * 60 * 60 * 1000) return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
      if (diff < 7 * 24 * 60 * 60 * 1000) return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`
      
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${month}月${day}日`
    } catch (error) {
      return ''
    }
  }
  
  getNotificationTypeText(type?: string): string {
    if (!type) return ''
    
    const typeTexts: Record<string, string> = {
      'order_paid': '订单支付成功',
      'order_shipped': '订单已发货',
      'order_delivered': '订单已送达',
      'system_maintenance': '系统维护通知',
      'system_update': '系统更新通知',
      'service_reply': '客服回复'
    }
    
    return typeTexts[type] || '消息通知'
  }
}

// Props
interface Props {
  category: MessageCategoryType
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'message-click', message: MessageListItem): void
  (e: 'unread-change'): void
}

const emit = defineEmits<Emits>()

// Data
const messages = ref<MessageListItem[]>([])
const loading = ref(false)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// Service
const messageCategoryService = new TempMessageCategoryService()

// Methods
const loadMessages = async () => {
  try {
    loading.value = true
    
    const response = await messageCategoryService.getCategoryMessages({
      type: props.category,
      page: currentPage.value,
      page_size: pageSize.value,
      keyword: searchKeyword.value || undefined
    })
    
    messages.value = response.list
    total.value = response.total
    
  } catch (error) {
    console.error('加载消息失败:', error)
  } finally {
    loading.value = false
  }
}

const handleMessageClick = async (message: MessageListItem) => {
  // 如果消息未读，标记为已读
  if (message.status === 0) {
    try {
      await messageCategoryService.markMessageAsRead(message.id)
      message.status = 1
      emit('unread-change')
    } catch (error) {
      console.error('标记消息已读失败:', error)
    }
  }
  
  emit('message-click', message)
}

const handleSearch = () => {
  currentPage.value = 1
  loadMessages()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadMessages()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadMessages()
}

const formatTime = (timeStr: string): string => {
  return messageCategoryService.formatTime(timeStr)
}

const formatMessagePreview = (content: string): string => {
  if (!content) return '暂无内容'
  const maxLength = 100
  return content.length > maxLength ? content.substring(0, maxLength) + '...' : content
}

const getNotificationTypeText = (type: string): string => {
  return messageCategoryService.getNotificationTypeText(type)
}

const getEmptyIcon = () => {
  const iconMap = {
    system: Bell,
    order: ShoppingBag,
    service: ChatDotRound,
    chat: ChatDotRound
  }
  return iconMap[props.category] || ChatDotRound
}

// Watchers
watch(() => props.category, () => {
  currentPage.value = 1
  searchKeyword.value = ''
  loadMessages()
})

// Lifecycle
onMounted(() => {
  loadMessages()
})
</script>

<style scoped>
.message-category-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-bar {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.message-item {
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.message-item:hover {
  background-color: #f9fafb;
}

.message-item--unread {
  background-color: #eff6ff;
}

.message-item__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.message-item__title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  flex: 1;
  margin-right: 12px;
}

.message-item__time {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
}

.message-item__content {
  font-size: 13px;
  color: #4b5563;
  line-height: 1.5;
  margin-bottom: 8px;
}

.message-item__type {
  font-size: 12px;
  color: #3b82f6;
  background-color: #dbeafe;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
}

.message-item__unread-dot {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 8px;
  height: 8px;
  background-color: #ef4444;
  border-radius: 50%;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-state__icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state__text {
  font-size: 14px;
}

.pagination {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: center;
}
</style>
