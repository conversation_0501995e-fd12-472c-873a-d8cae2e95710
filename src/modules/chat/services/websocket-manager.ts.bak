/**
 * 统一的WebSocket管理器
 * 集中处理所有WebSocket消息的接收、发送和状态管理
 */

import { EventEmitter } from '../utils/event-emitter'
import { getWebSocketToken } from '../api'
import { getApiBaseUrl } from '@/utils/apiConfig'

// WebSocket消息类型
export enum WebSocketMessageType {
  // 认证相关
  AUTH = 'auth',
  AUTH_SUCCESS = 'auth_success',
  AUTH_FAILED = 'auth_failed',
  
  // 心跳相关
  PING = 'ping',
  PONG = 'pong',
  
  // 消息相关
  NEW_MESSAGE = 'new_message',
  MESSAGE_READ = 'message_read',
  MESSAGE_DELIVERED = 'message_delivered',
  MESSAGE_RECALLED = 'message_recalled',
  
  // 会话相关
  SESSION_CREATED = 'session_created',
  SESSION_UPDATED = 'session_updated',
  SESSION_DELETED = 'session_deleted',
  
  // 用户状态相关
  USER_ONLINE = 'user_online',
  USER_OFFLINE = 'user_offline',
  USER_TYPING = 'user_typing',
  USER_STOP_TYPING = 'user_stop_typing',
  
  // 通知相关
  NOTIFICATION = 'notification',
  
  // 错误相关
  ERROR = 'error'
}

// WebSocket连接状态
export enum WebSocketStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

// WebSocket消息接口
export interface WebSocketMessage {
  type: WebSocketMessageType
  id?: string
  timestamp: number
  data?: any
}

// WebSocket配置接口
export interface WebSocketConfig {
  url?: string
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
  heartbeatTimeout?: number
  debug?: boolean
}

/**
 * 统一的WebSocket管理器类
 */
export class ChatWebSocketManager extends EventEmitter {
  private ws: WebSocket | null = null
  private status: WebSocketStatus = WebSocketStatus.DISCONNECTED
  private config: Required<WebSocketConfig>
  private reconnectAttempts = 0
  private reconnectTimer: number | null = null
  private heartbeatTimer: number | null = null
  private heartbeatTimeoutTimer: number | null = null
  private isAuthenticated = false
  private messageQueue: WebSocketMessage[] = []

  constructor(config: WebSocketConfig = {}) {
    super()
    
    // 默认配置
    this.config = {
      url: config.url || `${getApiBaseUrl().replace('http', 'ws')}/api/v1/chat/ws`,
      reconnectInterval: config.reconnectInterval || 3000,
      maxReconnectAttempts: config.maxReconnectAttempts || 10,
      heartbeatInterval: config.heartbeatInterval || 30000,
      heartbeatTimeout: config.heartbeatTimeout || 10000,
      debug: config.debug || false
    }
    
    this.log('WebSocket管理器初始化完成', this.config)
  }

  /**
   * 连接WebSocket
   * @deprecated 此服务已废弃，请使用统一的chatStore代替
   */
  async connect(): Promise<void> {
    console.warn('⚠️ ChatWebSocketManager.connect() 已废弃，请使用统一的chatStore代替')
    console.warn('⚠️ 为避免重复连接和冲突，此方法将不执行任何操作')
    return

    // 以下代码已禁用，避免与新的WebSocket服务冲突
    /*
    if (this.status === WebSocketStatus.CONNECTED || this.status === WebSocketStatus.CONNECTING) {
      this.log('WebSocket已连接或正在连接中')
      return
    }

    try {
      this.setStatus(WebSocketStatus.CONNECTING)

      // 获取WebSocket连接token
      const tokenData = await getWebSocketToken()
      const wsUrl = `${this.config.url}?token=${tokenData.token}`

      this.log('开始连接WebSocket:', wsUrl)

      this.ws = new WebSocket(wsUrl)
      this.setupEventListeners()

      // 等待连接建立
      await this.waitForConnection()

    } catch (error) {
      this.log('WebSocket连接失败:', error)
      this.setStatus(WebSocketStatus.ERROR)
      this.scheduleReconnect()
      throw error
    }
    */
  }

  /**
   * 断开WebSocket连接
   */
  disconnect(): void {
    this.log('断开WebSocket连接')
    
    this.clearTimers()
    this.isAuthenticated = false
    this.messageQueue = []
    
    if (this.ws) {
      this.ws.close(1000, 'Normal closure')
      this.ws = null
    }
    
    this.setStatus(WebSocketStatus.DISCONNECTED)
  }

  /**
   * 发送消息
   */
  async sendMessage(message: WebSocketMessage): Promise<void> {
    if (this.status !== WebSocketStatus.CONNECTED || !this.isAuthenticated) {
      this.log('WebSocket未连接或未认证，消息加入队列:', message)
      this.messageQueue.push(message)
      return
    }

    try {
      const messageStr = JSON.stringify(message)
      this.ws?.send(messageStr)
      this.log('发送WebSocket消息:', message)
    } catch (error) {
      this.log('发送WebSocket消息失败:', error)
      throw error
    }
  }

  /**
   * 获取连接状态
   */
  getStatus(): WebSocketStatus {
    return this.status
  }

  /**
   * 是否已连接
   */
  isConnected(): boolean {
    return this.status === WebSocketStatus.CONNECTED && this.isAuthenticated
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.ws) return

    this.ws.onopen = (event) => {
      this.log('WebSocket连接已建立')
      this.setStatus(WebSocketStatus.CONNECTED)
      this.reconnectAttempts = 0
      this.isAuthenticated = true // 简化认证流程，实际项目中可能需要额外的认证步骤
      
      // 开始心跳
      this.startHeartbeat()
      
      // 发送队列中的消息
      this.flushMessageQueue()
      
      this.emit('connected', event)
    }

    this.ws.onclose = (event) => {
      this.log('WebSocket连接已关闭:', event.code, event.reason)
      this.setStatus(WebSocketStatus.DISCONNECTED)
      this.isAuthenticated = false
      this.clearTimers()
      
      this.emit('disconnected', event)
      
      // 如果不是正常关闭，尝试重连
      if (event.code !== 1000) {
        this.scheduleReconnect()
      }
    }

    this.ws.onerror = (event) => {
      this.log('WebSocket连接错误:', event)
      this.setStatus(WebSocketStatus.ERROR)
      this.emit('error', event)
    }

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data)
        this.handleMessage(message)
      } catch (error) {
        this.log('解析WebSocket消息失败:', error, event.data)
      }
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: WebSocketMessage): void {
    this.log('收到WebSocket消息:', message)

    // 处理心跳响应
    if (message.type === WebSocketMessageType.PONG) {
      this.handlePong()
      return
    }

    // 处理认证响应
    if (message.type === WebSocketMessageType.AUTH_SUCCESS) {
      this.isAuthenticated = true
      this.flushMessageQueue()
      this.emit('authenticated', message)
      return
    }

    if (message.type === WebSocketMessageType.AUTH_FAILED) {
      this.isAuthenticated = false
      this.emit('auth_failed', message)
      return
    }

    // 根据消息类型分发事件
    switch (message.type) {
      case WebSocketMessageType.NEW_MESSAGE:
        this.emit('new_message', message.data)
        break
      case WebSocketMessageType.MESSAGE_READ:
        this.emit('message_read', message.data)
        break
      case WebSocketMessageType.MESSAGE_DELIVERED:
        this.emit('message_delivered', message.data)
        break
      case WebSocketMessageType.MESSAGE_RECALLED:
        this.emit('message_recalled', message.data)
        break
      case WebSocketMessageType.SESSION_CREATED:
        this.emit('session_created', message.data)
        break
      case WebSocketMessageType.SESSION_UPDATED:
        this.emit('session_updated', message.data)
        break
      case WebSocketMessageType.SESSION_DELETED:
        this.emit('session_deleted', message.data)
        break
      case WebSocketMessageType.USER_ONLINE:
        this.emit('user_online', message.data)
        break
      case WebSocketMessageType.USER_OFFLINE:
        this.emit('user_offline', message.data)
        break
      case WebSocketMessageType.USER_TYPING:
        this.emit('user_typing', message.data)
        break
      case WebSocketMessageType.USER_STOP_TYPING:
        this.emit('user_stop_typing', message.data)
        break
      case WebSocketMessageType.NOTIFICATION:
        this.emit('notification', message.data)
        break
      case WebSocketMessageType.ERROR:
        this.emit('websocket_error', message.data)
        break
      default:
        this.log('未知的WebSocket消息类型:', message.type)
    }

    // 发送通用消息事件
    this.emit('message', message)
  }

  /**
   * 等待连接建立
   */
  private waitForConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve()
        return
      }

      const timeout = setTimeout(() => {
        reject(new Error('WebSocket连接超时'))
      }, 10000)

      const handleOpen = () => {
        clearTimeout(timeout)
        resolve()
      }

      const handleError = (error: Event) => {
        clearTimeout(timeout)
        reject(error)
      }

      this.ws?.addEventListener('open', handleOpen, { once: true })
      this.ws?.addEventListener('error', handleError, { once: true })
    })
  }

  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = window.setInterval(() => {
      this.sendPing()
    }, this.config.heartbeatInterval)
  }

  /**
   * 发送心跳
   */
  private sendPing(): void {
    const pingMessage: WebSocketMessage = {
      type: WebSocketMessageType.PING,
      id: this.generateMessageId(),
      timestamp: Date.now()
    }

    this.sendMessage(pingMessage).catch(error => {
      this.log('发送心跳失败:', error)
    })

    // 设置心跳超时
    this.heartbeatTimeoutTimer = window.setTimeout(() => {
      this.log('心跳超时，重新连接')
      this.disconnect()
      this.scheduleReconnect()
    }, this.config.heartbeatTimeout)
  }

  /**
   * 处理心跳响应
   */
  private handlePong(): void {
    if (this.heartbeatTimeoutTimer) {
      clearTimeout(this.heartbeatTimeoutTimer)
      this.heartbeatTimeoutTimer = null
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      this.log('达到最大重连次数，停止重连')
      this.emit('max_reconnect_attempts_reached')
      return
    }

    this.reconnectAttempts++
    this.setStatus(WebSocketStatus.RECONNECTING)
    
    const delay = this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1)
    this.log(`第${this.reconnectAttempts}次重连，${delay}ms后开始`)

    this.reconnectTimer = window.setTimeout(() => {
      this.connect().catch(error => {
        this.log('重连失败:', error)
      })
    }, delay)

    this.emit('reconnecting', { attempt: this.reconnectAttempts, delay })
  }

  /**
   * 发送队列中的消息
   */
  private flushMessageQueue(): void {
    if (this.messageQueue.length === 0) return

    this.log(`发送队列中的${this.messageQueue.length}条消息`)
    
    const messages = [...this.messageQueue]
    this.messageQueue = []
    
    messages.forEach(message => {
      this.sendMessage(message).catch(error => {
        this.log('发送队列消息失败:', error)
      })
    })
  }

  /**
   * 清除定时器
   */
  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
    
    if (this.heartbeatTimeoutTimer) {
      clearTimeout(this.heartbeatTimeoutTimer)
      this.heartbeatTimeoutTimer = null
    }
  }

  /**
   * 设置连接状态
   */
  private setStatus(status: WebSocketStatus): void {
    if (this.status !== status) {
      const oldStatus = this.status
      this.status = status
      this.log('WebSocket状态变更:', oldStatus, '->', status)
      this.emit('status_changed', { oldStatus, newStatus: status })
    }
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 日志输出
   */
  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[ChatWebSocketManager]', ...args)
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.disconnect()
    this.removeAllListeners()
    this.log('WebSocket管理器已销毁')
  }
}

// 创建单例实例
let instance: ChatWebSocketManager | null = null

/**
 * 获取WebSocket管理器实例
 * @deprecated 此服务已废弃，请使用统一的chatStore代替
 */
export function getChatWebSocketManager(config?: WebSocketConfig): ChatWebSocketManager {
  console.warn('⚠️ getChatWebSocketManager() 已废弃，请使用统一的chatStore代替')
  console.warn('⚠️ 为避免重复连接，此服务将不会自动连接WebSocket')

  if (!instance) {
    instance = new ChatWebSocketManager(config)
  }
  return instance
}

/**
 * 销毁WebSocket管理器实例
 */
export function destroyChatWebSocketManager(): void {
  if (instance) {
    instance.destroy()
    instance = null
  }
}

export default ChatWebSocketManager
