/**
 * 通知服务
 */

import type {
  Notification,
  NotificationSettings,
  SendNotificationRequest,
  SendNotificationResponse,
  GetNotificationsRequest,
  GetNotificationsResponse,
  MarkNotificationReadRequest,
  MarkNotificationReadResponse,
  // UpdateNotificationSettingsRequest, // 未使用，注释掉
  // UpdateNotificationSettingsResponse, // 未使用，注释掉
  NotificationStatistics,
  NotificationBatchOperation,
  NotificationQueueItem,
  NotificationType,
  NotificationPriority,
  // NotificationStatus // 暂时注释掉未使用的导入
} from '../types/notification'
import { Logger } from '../utils/logger'
// import { API_ENDPOINTS, API_TIMEOUT, NOTIFICATION } from '../constants' // 暂时注释掉

/**
 * 通知服务类
 */
export class NotificationService {
  private baseUrl: string
  private logger: Logger
  private abortController: AbortController | null = null
  private notificationQueue: NotificationQueueItem[] = []
  private isProcessingQueue = false
  private settings: NotificationSettings

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
    this.logger = new Logger('NotificationService')
    this.settings = this.getDefaultSettings()
    this.startQueueProcessor()
  }

  /**
   * 获取默认通知设置
   */
  private getDefaultSettings(): NotificationSettings {
    return {
      user_id: 1,
      user_type: 'USER' as any,
      enabled: true,
      channels: {
        'IN_APP': {
          enabled: true,
          types: ['MESSAGE' as any, 'SESSION' as any]
        },
        'PUSH': {
          enabled: true,
          types: ['MESSAGE' as any]
        },
        'EMAIL': {
          enabled: false,
          types: []
        },
        'SMS': {
          enabled: false,
          types: []
        }
      } as any,
      priority_filter: ['NORMAL' as any, 'HIGH' as any],
      do_not_disturb: {
        enabled: false
      },
      grouping: {
        enabled: true,
        max_group_size: 5,
        group_timeout: 300
      },
      auto_read: {
        enabled: false,
        delay: 5000
      }
    }
  }

  /**
   * 发送通知
   */
  async sendNotification(request: SendNotificationRequest): Promise<SendNotificationResponse> {
    try {
      this.logger.debug('Sending notification:', request)
      
      const response = await this.fetch(`${this.baseUrl}/notifications`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })
      
      const result = await response.json() as SendNotificationResponse
      
      // 简化响应处理
      this.logger.debug('Notification sent successfully:', result.notification_ids)
      return result
      
    } catch (error) {
      this.logger.error('Failed to send notification:', error)
      throw error
    }
  }

  /**
   * 获取通知列表
   */
  async getNotifications(request: GetNotificationsRequest): Promise<GetNotificationsResponse> {
    try {
      this.logger.debug('Getting notifications:', request)
      
      const params = new URLSearchParams()
      if (request.types) params.append('types', request.types.join(','))
      if (request.statuses) params.append('statuses', request.statuses.join(','))
      if (request.channels) params.append('channels', request.channels.join(','))
      if (request.page) params.append('page', request.page.toString())
      // if (request.page_size) params.append('page_size', request.page_size.toString()) // 属性不存在，注释掉
      
      const url = `${this.baseUrl}/notifications?${params.toString()}`
      const response = await this.fetch(url)
      
      const result = await response.json() as GetNotificationsResponse
      
      // 简化响应处理
      this.logger.debug(`Retrieved ${result.notifications?.length || 0} notifications`)
      return result
      
    } catch (error) {
      this.logger.error('Failed to get notifications:', error)
      throw error
    }
  }

  /**
   * 标记通知已读
   */
  async markNotificationRead(request: MarkNotificationReadRequest): Promise<MarkNotificationReadResponse> {
    try {
      this.logger.debug('Marking notification as read:', request)
      
      const response = await this.fetch(`${this.baseUrl}/notifications/read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })
      
      const result = await response.json() as MarkNotificationReadResponse
      
      // 简化响应处理
      
      this.logger.debug('Notification marked as read successfully')
      return result
      
    } catch (error) {
      this.logger.error('Failed to mark notification as read:', error)
      throw error
    }
  }

  /**
   * 更新通知偏好设置
   */
  async updateNotificationPreferences(request: any): Promise<any> {
    try {
      this.logger.debug('Updating notification preferences:', request)
      
      const response = await this.fetch(`${this.baseUrl}/notifications/preferences`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })
      
      const result = await response.json() as any
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to update notification preferences')
      }
      
      // 更新本地设置
      if (result.settings) {
        this.settings = result.settings
      }
      
      this.logger.debug('Notification preferences updated successfully')
      return result
      
    } catch (error) {
      this.logger.error('Failed to update notification preferences:', error)
      throw error
    }
  }

  /**
   * 获取通知设置
   */
  async getNotificationSettings(): Promise<NotificationSettings> {
    try {
      this.logger.debug('Getting notification settings')
      
      const response = await this.fetch(`${this.baseUrl}/notifications/settings`)
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get notification settings')
      }
      
      this.settings = result.settings || this.getDefaultSettings()
      this.logger.debug('Notification settings retrieved successfully')
      return this.settings
      
    } catch (error) {
      this.logger.error('Failed to get notification settings:', error)
      throw error
    }
  }

  /**
   * 删除通知
   */
  async deleteNotification(notificationId: number): Promise<boolean> {
    try {
      this.logger.debug('Deleting notification:', notificationId)
      
      const response = await this.fetch(`${this.baseUrl}/notifications/${notificationId}`, {
        method: 'DELETE'
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete notification')
      }
      
      this.logger.debug('Notification deleted successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to delete notification:', error)
      throw error
    }
  }

  /**
   * 批量操作通知
   */
  async batchOperateNotifications(operation: NotificationBatchOperation): Promise<boolean> {
    try {
      this.logger.debug('Batch operating notifications:', operation)
      
      const response = await this.fetch(`${this.baseUrl}/notifications/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(operation)
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to batch operate notifications')
      }
      
      this.logger.debug('Batch operation completed successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to batch operate notifications:', error)
      throw error
    }
  }

  /**
   * 获取通知统计
   */
  async getNotificationStatistics(startTime?: number, endTime?: number): Promise<NotificationStatistics> {
    try {
      this.logger.debug('Getting notification statistics:', { startTime, endTime })
      
      const params = new URLSearchParams()
      if (startTime) params.append('start_time', startTime.toString())
      if (endTime) params.append('end_time', endTime.toString())
      
      const url = `${this.baseUrl}/notifications/statistics?${params.toString()}`
      const response = await this.fetch(url)
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get notification statistics')
      }
      
      this.logger.debug('Notification statistics retrieved successfully')
      return result.statistics
      
    } catch (error) {
      this.logger.error('Failed to get notification statistics:', error)
      throw error
    }
  }

  /**
   * 获取未读通知数量
   */
  async getUnreadCount(type?: NotificationType): Promise<number> {
    try {
      this.logger.debug('Getting unread notification count:', type)
      
      const params = new URLSearchParams()
      if (type) params.append('type', type)
      
      const url = `${this.baseUrl}/notifications/unread-count?${params.toString()}`
      const response = await this.fetch(url)
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get unread count')
      }
      
      return result.count || 0
      
    } catch (error) {
      this.logger.error('Failed to get unread count:', error)
      throw error
    }
  }

  /**
   * 清空所有通知
   */
  async clearAllNotifications(): Promise<boolean> {
    try {
      this.logger.debug('Clearing all notifications')
      
      const response = await this.fetch(`${this.baseUrl}/notifications/clear`, {
        method: 'POST'
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to clear all notifications')
      }
      
      this.logger.debug('All notifications cleared successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to clear all notifications:', error)
      throw error
    }
  }

  /**
   * 标记所有通知已读
   */
  async markAllNotificationsRead(): Promise<boolean> {
    try {
      this.logger.debug('Marking all notifications as read')
      
      const response = await this.fetch(`${this.baseUrl}/notifications/read-all`, {
        method: 'POST'
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to mark all notifications as read')
      }
      
      this.logger.debug('All notifications marked as read successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to mark all notifications as read:', error)
      throw error
    }
  }

  /**
   * 添加通知到队列
   */
  addToQueue(notification: Notification, priority: NotificationPriority = 'NORMAL' as any): void {
    const queueItem: any = {
      id: Date.now().toString(),
      notification,
      // priority, // 属性不存在，注释掉
      created_at: new Date().toISOString(),
      scheduled_at: new Date().toISOString(),
      attempts: 0,
      max_attempts: 3
    }
    
    // 根据优先级插入队列
    if (priority === 'urgent') {
      this.notificationQueue.unshift(queueItem)
    } else {
      this.notificationQueue.push(queueItem)
    }
    
    this.logger.debug('Notification added to queue:', { id: notification.id, priority })
  }

  /**
   * 处理通知队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.notificationQueue.length === 0) {
      return
    }
    
    this.isProcessingQueue = true
    
    try {
      while (this.notificationQueue.length > 0) {
        const item = this.notificationQueue.shift()
        if (!item) continue
        
        try {
          await this.processNotification(item.notification)
          this.logger.debug('Notification processed successfully:', item.notification.id)
        } catch (error) {
          this.logger.error('Failed to process notification:', error)
          
          // 重试逻辑
          // 简化重试逻辑
          if (true) { // 暂时总是重试
            this.notificationQueue.push(item)
            this.logger.debug('Notification added back to queue for retry:', item.notification.id)
          }
        }
        
        // 避免过快处理
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    } finally {
      this.isProcessingQueue = false
    }
  }

  /**
   * 处理单个通知
   */
  private async processNotification(notification: Notification): Promise<void> {
    // 检查通知设置
    if (!this.shouldShowNotification(notification)) {
      return
    }
    
    // 简化通知处理
    this.logger.debug('Processing notification:', notification.id)
  }

  /**
   * 检查是否应该显示通知
   */
  private shouldShowNotification(_notification: Notification): boolean {
    // 简化通知过滤逻辑
    return true
  }

  /**
   * 显示桌面通知
   */
  private showDesktopNotification(notification: Notification): void {
    if (!('Notification' in window)) {
      return
    }
    
    if (Notification.permission === 'granted') {
      const desktopNotification = new Notification(notification.title, {
        body: notification.content,
        icon: '/icons/notification.png',
        tag: `notification_${notification.id}`,
        requireInteraction: notification.priority === 'urgent'
      })
      
      desktopNotification.onclick = () => {
        window.focus()
        // 触发通知点击事件
        this.onNotificationClick(notification)
        desktopNotification.close()
      }
      
      // 自动关闭
      setTimeout(() => {
        desktopNotification.close()
      }, 5000) // 5秒显示时间
      
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          this.showDesktopNotification(notification)
        }
      })
    }
  }

  // 播放通知声音方法已移除，因为未使用

  /**
   * 发送移动推送 - 已禁用
   */
  // private async sendMobilePush(_notification: Notification): Promise<void> {
  //   try {
  //     // 这里可以集成第三方推送服务，如 Firebase Cloud Messaging
  //     this.logger.debug('Sending mobile push notification:', _notification.id)
  //     // TODO: 实现移动推送逻辑
  //   } catch (error) {
  //     this.logger.error('Failed to send mobile push:', error)
  //   }
  // }

  /**
   * 发送邮件通知 - 已禁用
   */
  // private async sendEmailNotification(_notification: Notification): Promise<void> {
  //   try {
  //     this.logger.debug('Sending email notification:', _notification.id)
  //     // TODO: 实现邮件通知逻辑
  //   } catch (error) {
  //     this.logger.error('Failed to send email notification:', error)
  //   }
  // }

  /**
   * 发送短信通知 - 已禁用
   */
  // private async sendSMSNotification(_notification: Notification): Promise<void> {
  //   try {
  //     this.logger.debug('Sending SMS notification:', _notification.id)
  //     // TODO: 实现短信通知逻辑
  //   } catch (error) {
  //     this.logger.error('Failed to send SMS notification:', error)
  //   }
  // }

  /**
   * 通知点击处理
   */
  private onNotificationClick(notification: Notification): void {
    this.logger.debug('Notification clicked:', notification.id)
    // TODO: 实现通知点击处理逻辑
  }

  /**
   * 启动队列处理器
   */
  private startQueueProcessor(): void {
    setInterval(() => {
      this.processQueue().catch(error => {
        this.logger.error('Queue processing error:', error)
      })
    }, 1000) // 每秒检查一次队列
  }

  /**
   * 请求通知权限
   */
  async requestNotificationPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      throw new Error('This browser does not support notifications')
    }
    
    if (Notification.permission === 'default') {
      const permission = await Notification.requestPermission()
      this.logger.info('Notification permission:', permission)
      return permission
    }
    
    return Notification.permission
  }

  /**
   * 获取当前设置
   */
  getSettings(): NotificationSettings {
    return { ...this.settings }
  }

  /**
   * 更新本地设置
   */
  updateSettings(settings: Partial<NotificationSettings>): void {
    this.settings = { ...this.settings, ...settings }
    this.logger.debug('Notification settings updated locally')
  }

  /**
   * 取消请求
   */
  cancelRequests(): void {
    if (this.abortController) {
      this.abortController.abort()
      this.abortController = null
    }
  }

  /**
   * 通用fetch方法
   */
  private async fetch(url: string, options: RequestInit = {}): Promise<Response> {
    // 创建新的AbortController
    this.abortController = new AbortController()
    
    const defaultOptions: RequestInit = {
      signal: this.abortController.signal,
      headers: {
        'Accept': 'application/json',
        ...options.headers
      }
    }
    
    const mergedOptions = { ...defaultOptions, ...options }
    
    // 设置超时
    const timeoutId = setTimeout(() => {
      if (this.abortController) {
        this.abortController.abort()
      }
    }, 30000) // 30秒超时
    
    try {
      const response = await fetch(url, mergedOptions)
      clearTimeout(timeoutId)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      return response
      
    } catch (error) {
      clearTimeout(timeoutId)
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout or cancelled')
      }
      
      throw error
    }
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.cancelRequests()
    this.notificationQueue = []
    this.isProcessingQueue = false
    this.logger.info('NotificationService destroyed')
  }
}

export default NotificationService