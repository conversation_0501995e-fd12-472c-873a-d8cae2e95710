/**
 * 消息分类服务
 * 处理四类消息分类的业务逻辑
 */

import * as chatApi from '../api'
import {
  MessageCategoryType  // 作为值导入，因为在运行时需要使用
} from '../types/message-category'
import type {
  MessageCategoryDTO,
  UnreadCountDTO,
  SessionDTO,
  MessageListItem,
  GetSessionsRequest,
  GetMessagesRequest,
  PaginatedResponse
} from '../types/message-category'

/**
 * 消息分类服务类
 */
export class MessageCategoryService {
  /**
   * 获取消息分类列表
   */
  async getMessageCategories(): Promise<MessageCategoryDTO[]> {
    try {
      const response = await chatApi.getMessageCategories()
      // 响应拦截器已经返回了data，所以直接使用response
      return response || []
    } catch (error) {
      console.error('获取消息分类失败:', error)
      throw error
    }
  }

  /**
   * 获取未读消息统计
   */
  async getUnreadCount(): Promise<UnreadCountDTO> {
    try {
      const response = await chatApi.getUnreadCount()
      // 响应拦截器已经返回了data，所以直接使用response
      return response || {
        total: 0,
        categories: { chat: 0, system: 0, order: 0, service: 0 },
        conversations: {}
      }
    } catch (error) {
      console.error('获取未读统计失败:', error)
      throw error
    }
  }

  /**
   * 获取分类会话列表
   * 支持系统消息和订单消息的统一处理
   */
  async getCategorySessions(params: GetSessionsRequest): Promise<PaginatedResponse<SessionDTO> & { category?: MessageCategoryType }> {
    try {
      const response = await chatApi.getCategorySessions(params)
      console.log('📡 getCategorySessions 响应数据:', response)
      console.log('🎯 请求分类:', params.category)

      // 响应拦截器已经返回了data字段，所以response就是实际的数据
      // 检查是否是GetSessionsResponse格式（包含success、data字段）
      if (response && typeof response === 'object') {
        let sessionData: any
        let sessions: any[] = []
        let responseCategory: string | undefined

        // 使用类型断言来处理不同的响应格式
        const responseAny = response as any

        // 处理多种可能的响应格式
        console.log('🔍 开始分析响应格式:', {
          hasData: !!responseAny.data,
          hasList: !!responseAny.list,
          hasSessions: !!responseAny.sessions,
          hasCategory: !!responseAny.category,
          isArray: Array.isArray(responseAny),
          keys: Object.keys(responseAny)
        })

        // 检查是否包含category信息（系统消息和订单消息的标准格式）
        if (responseAny.category) {
          responseCategory = responseAny.category
          console.log('📋 检测到带category的响应格式，分类:', responseCategory)
        }

        if (responseAny.data && responseAny.data.sessions) {
          // 格式1: GetSessionsResponse格式 {success: true, data: {sessions: [...], total: ...}}
          console.log('📋 检测到GetSessionsResponse格式')
          sessionData = responseAny.data
          sessions = sessionData.sessions || []
        } else if (responseAny.data && responseAny.data.list) {
          // 格式2: GetSessionsResponse格式 {success: true, data: {list: [...], total: ...}}
          console.log('📋 检测到GetSessionsResponse(list)格式')
          sessionData = responseAny.data
          sessions = sessionData.list || []
        } else if (responseAny.list && Array.isArray(responseAny.list)) {
          // 格式3: 直接的会话数据格式 {list: [...], total: ...} - 实际API返回格式
          console.log('📋 检测到直接会话数据格式(list) ✅')
          sessionData = responseAny
          sessions = responseAny.list || []
        } else if (responseAny.sessions && Array.isArray(responseAny.sessions)) {
          // 格式4: 直接的会话数据格式 {sessions: [...], total: ...}
          console.log('📋 检测到直接会话数据格式(sessions)')
          sessionData = responseAny
          sessions = responseAny.sessions || []
        } else if (Array.isArray(responseAny)) {
          // 格式5: 直接的会话数组
          console.log('📋 检测到会话数组格式')
          sessions = responseAny
          sessionData = {
            list: responseAny,
            total: responseAny.length,
            page: 1,
            page_size: responseAny.length
          }
        } else {
          console.error('❌ 未识别的响应格式:', responseAny)
          console.error('响应格式详情:', {
            type: typeof responseAny,
            constructor: responseAny?.constructor?.name,
            keys: responseAny ? Object.keys(responseAny) : 'null'
          })
        }

        // 确保sessionData不为undefined
        if (!sessionData) {
          console.warn('⚠️ sessionData为undefined，使用默认值')
          sessionData = {
            total: sessions.length,
            page: 1,
            page_size: 20
          }
        }

        console.log('📊 会话数据分析:', {
          sessionsCount: sessions.length,
          sessionData,
          responseCategory,
          requestCategory: params.category,
          firstSession: sessions[0]
        })

        // 转换Session对象到SessionDTO格式
        const sessionDTOs: SessionDTO[] = sessions.map(session => {
          // 特殊处理系统消息和订单消息的会话类型
          let sessionType = session.type
          if (responseCategory === 'system' || params.category === 'system') {
            sessionType = 'system_notification'
            console.log('🔄 系统消息会话类型设置为: system_notification')
          } else if (responseCategory === 'order' || params.category === 'order') {
            sessionType = 'order_notification'
            console.log('🔄 订单消息会话类型设置为: order_notification')
          }

          const dto = {
            id: session.id,
            type: sessionType,
            status: (session as any).status, // 🔥 保留会话状态信息
            target_name: this.getSessionTargetName(session),
            target_avatar: this.getSessionTargetAvatar(session),
            last_message: session.last_message ? {
              id: session.last_message.id || 0,
              content: session.last_message.content,
              type: session.last_message.type,
              created_at: session.last_message.created_at,
              // 🔥 保留发送者信息 - 这是关键修复！
              sender_id: session.last_message.sender_id,
              sender_name: session.last_message.sender_name,
              sender_avatar: session.last_message.sender_avatar,
              sender_type: session.last_message.sender_type,
              session_id: session.last_message.session_id,
              resource_id: session.last_message.resource_id,
              status: session.last_message.status
            } : null,
            unread_count: session.unread_count || 0,
            created_at: session.created_at,
            updated_at: session.updated_at
          }

          console.log('🔄 转换会话:', session.id, '→', dto)
          console.log('📋 会话分类信息:', {
            原始类型: session.type,
            响应分类: responseCategory,
            请求分类: params.category,
            最终类型: sessionType
          })

          // 🔍 验证发送者信息是否正确保留
          if (dto.last_message) {
            console.log('📝 last_message发送者信息:', {
              sessionId: session.id,
              sender_name: dto.last_message.sender_name,
              sender_avatar: dto.last_message.sender_avatar,
              content: dto.last_message.content?.substring(0, 20) + '...'
            })
          }

          return dto
        })

        // 确保category字段类型正确
        const finalCategory = responseCategory || params.category
        const typedCategory = finalCategory as MessageCategoryType

        const result = {
          list: sessionDTOs,
          total: sessionData?.total || sessions.length,
          page: sessionData?.page || 1,
          page_size: sessionData?.page_size || 20,
          page_count: Math.ceil((sessionData?.total || sessions.length) / (sessionData?.page_size || 20)),
          category: typedCategory
        }

        console.log('✅ getCategorySessions 返回结果:', result)
        return result
      }

      // 如果响应格式不正确，返回空结果
      console.warn('⚠️ 响应格式不正确，返回空结果')
      return {
        list: [],
        total: 0,
        page: 1,
        page_size: 20,
        page_count: 0,
        category: params.category
      }
    } catch (error) {
      console.error('❌ 获取分类会话失败:', error)
      throw error
    }
  }

  /**
   * 获取分类消息列表
   */
  async getCategoryMessages(params: GetMessagesRequest): Promise<PaginatedResponse<MessageListItem>> {
    try {
      const response = await chatApi.getCategoryMessages(params)
      // 响应拦截器已经返回了data，所以直接使用response
      return response || {
        list: [],
        total: 0,
        page: 1,
        page_size: 20,
        page_count: 0
      }
    } catch (error) {
      console.error('获取分类消息失败:', error)
      throw error
    }
  }

  /**
   * 标记消息为已读
   */
  async markMessageAsRead(messageId: number): Promise<void> {
    try {
      await chatApi.markMessageAsRead(messageId)
    } catch (error) {
      console.error('标记消息已读失败:', error)
      throw error
    }
  }

  /**
   * 标记分类消息为已读
   */
  async markCategoryAsRead(category: MessageCategoryType): Promise<void> {
    try {
      await chatApi.markCategoryAsRead(category)
    } catch (error) {
      console.error('标记分类已读失败:', error)
      throw error
    }
  }

  /**
   * 标记会话消息为已读
   */
  async markSessionAsRead(sessionId: number): Promise<void> {
    try {
      await chatApi.markSessionAsRead(sessionId)
    } catch (error) {
      console.error('标记会话已读失败:', error)
      throw error
    }
  }

  /**
   * 判断分类是否显示会话列表
   * 修改：让所有分类都使用会话模式，包括订单消息和系统通知
   */
  isSessionCategory(_category: MessageCategoryType): boolean {
    // 所有分类都使用会话模式，确保订单消息和系统通知也能获取会话列表
    return true
  }

  /**
   * 判断分类是否显示消息列表
   * 修改：由于所有分类都使用会话模式，此方法现在返回false
   */
  isMessageCategory(_category: MessageCategoryType): boolean {
    // 所有分类都使用会话模式，不再有纯消息列表模式
    return false
  }

  /**
   * 获取分类的默认配置
   */
  getCategoryConfig(category: MessageCategoryType) {
    const configs = {
      [MessageCategoryType.CHAT]: {
        title: '聊天消息',
        icon: 'chat',
        color: '#4D8EFF',
        showSessions: true
      },
      [MessageCategoryType.SYSTEM]: {
        title: '系统通知',
        icon: 'notification',
        color: '#FF9500',
        showSessions: true  // 修改：系统通知也使用会话模式
      },
      [MessageCategoryType.ORDER]: {
        title: '订单消息',
        icon: 'goods',
        color: '#34C759',
        showSessions: true  // 修改：订单消息也使用会话模式
      },
      [MessageCategoryType.SERVICE]: {
        title: '客服消息',
        icon: 'service',
        color: '#FF3B30',
        showSessions: true
      }
    }
    
    return configs[category] || configs[MessageCategoryType.CHAT]
  }

  /**
   * 格式化时间显示
   */
  formatTime(timeStr: string): string {
    if (!timeStr) return ''
    
    try {
      const date = new Date(timeStr)
      const now = new Date()
      const diff = now.getTime() - date.getTime()
      
      // 小于1分钟显示"刚刚"
      if (diff < 60 * 1000) {
        return '刚刚'
      }
      
      // 小于1小时显示"X分钟前"
      if (diff < 60 * 60 * 1000) {
        const minutes = Math.floor(diff / (60 * 1000))
        return `${minutes}分钟前`
      }
      
      // 小于24小时显示"X小时前"
      if (diff < 24 * 60 * 60 * 1000) {
        const hours = Math.floor(diff / (60 * 60 * 1000))
        return `${hours}小时前`
      }
      
      // 小于7天显示"X天前"
      if (diff < 7 * 24 * 60 * 60 * 1000) {
        const days = Math.floor(diff / (24 * 60 * 60 * 1000))
        return `${days}天前`
      }
      
      // 超过7天显示具体日期
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${month}月${day}日`
    } catch (error) {
      console.error('格式化时间失败:', error)
      return ''
    }
  }

  /**
   * 获取通知类型的显示文本
   */
  getNotificationTypeText(notificationType?: string): string {
    if (!notificationType) return ''
    
    const typeTexts: Record<string, string> = {
      // 订单相关
      'order_paid': '订单支付成功',
      'order_shipped': '订单已发货',
      'order_delivered': '订单已送达',
      'order_cancelled': '订单已取消',
      'order_refunded': '订单已退款',
      
      // 系统相关
      'system_maintenance': '系统维护通知',
      'system_update': '系统更新通知',
      'system_announcement': '系统公告',
      
      // 客服相关
      'service_reply': '客服回复',
      'service_assigned': '客服已分配',
      'service_closed': '客服会话已关闭'
    }
    
    return typeTexts[notificationType] || '消息通知'
  }

  /**
   * 获取会话目标名称
   * 支持系统消息和订单消息的特殊处理
   */
  private getSessionTargetName(session: any): string {
    // 优先使用API返回的target_name字段
    if (session.target_name) {
      console.log('🎯 使用API返回的target_name:', session.target_name)
      return session.target_name
    }

    // 根据会话类型获取目标名称（备用逻辑）
    switch (session.type) {
      case 'group':
        return session.group_info?.name || '群聊'
      case 'user_to_user':
        return session.receiver_info?.name || session.customer_info?.name || '用户'
      case 'presale_consultation':
        return session.customer_info?.name || '客户'
      case 'after_sale_service':
        return session.customer_info?.name || '客户'
      case 'friend_chat':
        return session.friend_info?.name || '好友'
      case 'customer_service':
        return session.assigned_agent?.name || '客服'
      case 'system_notification':
        // 系统通知：优先使用发送者名称，否则使用默认名称
        if (session.last_message?.sender_name) {
          return session.last_message.sender_name
        }
        return '系统通知'
      case 'order_notification':
        // 订单消息：优先使用发送者名称，否则使用默认名称
        if (session.last_message?.sender_name) {
          return session.last_message.sender_name
        }
        return '订单消息'
      default:
        console.warn('⚠️ 未知会话类型:', session.type, '使用默认名称')
        // 对于未知类型，尝试使用发送者名称或默认名称
        if (session.last_message?.sender_name) {
          return session.last_message.sender_name
        }
        return session.title || '未知会话'
    }
  }

  /**
   * 获取会话目标头像
   */
  private getSessionTargetAvatar(session: any): string {
    // 优先使用API返回的target_avatar字段
    if (session.target_avatar) {
      console.log('🎯 使用API返回的target_avatar:', session.target_avatar)
      return session.target_avatar
    }

    // 根据会话类型获取目标头像（备用逻辑）
    switch (session.type) {
      case 'group':
        return session.group_info?.avatar || ''
      case 'user_to_user':
        return session.receiver_info?.avatar || session.customer_info?.avatar || ''
      case 'presale_consultation':
        return session.customer_info?.avatar || ''
      case 'after_sale_service':
        return session.customer_info?.avatar || ''
      case 'friend_chat':
        return session.friend_info?.avatar || ''
      case 'customer_service':
        return session.assigned_agent?.avatar || ''
      case 'system_notification':
        return '' // 系统通知通常没有头像
      default:
        return ''
    }
  }
}

// 导出单例实例
export const messageCategoryService = new MessageCategoryService()

export default MessageCategoryService
