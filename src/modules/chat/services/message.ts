/**
 * 消息服务
 * 使用新的API接口
 */

import * as chatApi from '../api'
import type {
  SendMessageResponse,
  GetMessagesResponse,
  MarkMessageReadRequest,
  MarkMessageReadResponse,
  SearchMessagesRequest,
  SearchMessagesResponse,
} from '../types/message'

/**
 * 消息服务类
 */
export class MessageService {
  /**
   * 获取消息列表
   */
  async getMessages(sessionId: number, params?: {
    page?: number
    page_size?: number
    order?: 'asc' | 'desc'
  }): Promise<GetMessagesResponse> {
    return chatApi.getMessages(sessionId, params)
  }

  /**
   * 发送文本消息
   */
  async sendTextMessage(sessionId: number, content: string): Promise<SendMessageResponse> {
    return chatApi.sendTextMessage({
      session_id: sessionId,
      type: 'TEXT' as any,
      category: 'CHAT' as any,
      content
    })
  }

  /**
   * 发送媒体消息
   */
  async sendMediaMessage(
    sessionId: number,
    file: File,
    type: 'image' | 'file' | 'voice' | 'video'
  ): Promise<SendMessageResponse> {
    return chatApi.sendMediaMessage(sessionId, file, type)
  }

  /**
   * 发送媒体URL消息
   */
  async sendMediaUrlMessage(sessionId: number, mediaData: {
    resource_id: string | number
    message_type: 'image' | 'file' | 'voice' | 'video'
    content: string
    file_name: string
    file_size: number
    file_type: string
    file_ext: string
  }): Promise<SendMessageResponse> {
    return chatApi.sendMediaUrlMessage(sessionId, mediaData)
  }

  /**
   * 通用发送消息方法（兼容旧接口）
   */
  async sendMessage(messageData: {
    session_id: number
    content: string
    message_type?: 'text' | 'image' | 'file' | 'voice' | 'video'
    files?: File[]
    reply_to_id?: number
  }): Promise<SendMessageResponse> {
    const { session_id, content, message_type = 'text', files } = messageData

    // 如果有文件，发送媒体消息
    if (files && files.length > 0) {
      const file = files[0] // 暂时只支持单文件
      const type = message_type === 'text' ? 'file' : message_type as 'image' | 'file' | 'voice' | 'video'
      return this.sendMediaMessage(session_id, file, type)
    }

    // 否则发送文本消息
    return this.sendTextMessage(session_id, content)
  }

  /**
   * 标记消息已读
   */
  async markMessageRead(request: MarkMessageReadRequest): Promise<MarkMessageReadResponse> {
    return chatApi.markMessageRead(request)
  }

  /**
   * 搜索消息
   */
  async searchMessages(request: SearchMessagesRequest): Promise<SearchMessagesResponse> {
    return chatApi.searchMessages(request)
  }

  /**
   * 删除消息
   */
  async deleteMessage(messageId: number): Promise<void> {
    return chatApi.deleteMessage(messageId)
  }

  /**
   * 撤回消息
   */
  async recallMessage(messageId: number): Promise<void> {
    return chatApi.recallMessage(messageId)
  }

  /**
   * 获取消息统计
   */
  async getMessageStats(sessionId?: number): Promise<any> {
    return chatApi.getMessageStats(sessionId)
  }

  /**
   * 上传文件
   */
  async uploadFile(file: File, sessionId?: number): Promise<any> {
    return chatApi.uploadFile(file, sessionId)
  }

  /**
   * 获取文件下载链接
   */
  async getFileDownloadUrl(fileId: string): Promise<{ url: string }> {
    return chatApi.getFileDownloadUrl(fileId)
  }
}

export default MessageService