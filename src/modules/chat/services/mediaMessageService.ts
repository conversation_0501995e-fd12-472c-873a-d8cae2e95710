/**
 * 媒体消息服务
 * 处理文件上传和媒体消息发送的完整流程
 */

import { sendMediaMessage, sendMediaUrlMessage } from '../api'
import { getAutoConfiguredUploadUrl } from './uploadConfig'
import { post } from '@/utils/request'

export interface MediaUploadResult {
  success: boolean
  data?: {
    url: string
    file_name: string
    file_size: number
    file_type: string
    file_ext: string
    file_id?: string
  }
  message?: string
  error?: string
}

export interface MediaMessageData {
  sessionId: number
  file: File
  type: 'image' | 'file' | 'voice' | 'video'
  onProgress?: (progress: number) => void
}

// 内部使用的媒体URL消息数据接口
interface InternalMediaUrlMessageData {
  sessionId: number
  messageType: 'image' | 'file' | 'voice' | 'video'
  content: string
  fileName: string
  fileSize: number
  fileType: string
  fileExt: string
  resourceId?: string | number
}

/**
 * 媒体消息服务类
 */
export class MediaMessageService {
  /**
   * 上传文件并发送媒体消息（方式1：直接上传文件）
   */
  async uploadAndSendMediaMessage(data: MediaMessageData): Promise<any> {
    const { sessionId, file, type } = data
    
    try {
      console.log(`📤 开始上传并发送媒体消息: ${file.name} (${type})`)
      
      // 直接使用后端的媒体消息接口上传文件
      const response = await sendMediaMessage(sessionId, file, type)
      
      console.log('✅ 媒体消息发送成功:', response)
      return response
      
    } catch (error: any) {
      console.error('❌ 媒体消息发送失败:', error)
      throw new Error(`发送${this.getTypeDisplayName(type)}失败: ${error.message}`)
    }
  }

  /**
   * 先上传文件，再发送媒体URL消息（方式2：分步处理）
   */
  async uploadFileThenSendMessage(data: MediaMessageData): Promise<any> {
    const { sessionId, file, type, onProgress } = data
    
    try {
      console.log(`📤 开始分步上传文件: ${file.name} (${type})`)
      
      // 第一步：上传文件到文件服务
      const uploadResult = await this.uploadFileToStorage(file, onProgress)
      
      if (!uploadResult.success || !uploadResult.data) {
        throw new Error(uploadResult.error || '文件上传失败')
      }
      
      console.log('📁 文件上传成功:', uploadResult.data)
      
      // 第二步：发送媒体URL消息
      const messageData: InternalMediaUrlMessageData = {
        sessionId,
        messageType: type,
        content: uploadResult.data.url,
        fileName: uploadResult.data.file_name,
        fileSize: uploadResult.data.file_size,
        fileType: uploadResult.data.file_type,
        fileExt: uploadResult.data.file_ext,
        resourceId: uploadResult.data.file_id
      }
      
      const messageResponse = await this.sendMediaUrlMessage(messageData)
      
      console.log('✅ 媒体URL消息发送成功:', messageResponse)
      return messageResponse
      
    } catch (error: any) {
      console.error('❌ 分步发送媒体消息失败:', error)
      throw new Error(`发送${this.getTypeDisplayName(type)}失败: ${error.message}`)
    }
  }

  /**
   * 上传文件到存储服务
   */
  private async uploadFileToStorage(file: File, onProgress?: (progress: number) => void): Promise<MediaUploadResult> {
    try {
      const uploadUrl = getAutoConfiguredUploadUrl()
      
      const formData = new FormData()
      formData.append('file', file)
      formData.append('file_usage', this.getFileUsage(file))
      
      console.log(`📁 上传文件到存储服务: ${file.name} -> ${uploadUrl}`)
      
      const response = await post(uploadUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent: any) => {
          if (progressEvent.total && onProgress) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            onProgress(progress)
          }
        }
      })
      
      // 处理上传响应，提取文件信息
      const fileData = this.extractFileDataFromResponse(response, file)
      
      return {
        success: true,
        data: fileData,
        message: '文件上传成功'
      }
      
    } catch (error: any) {
      console.error('文件上传失败:', error)
      return {
        success: false,
        error: error.message || '文件上传失败'
      }
    }
  }

  /**
   * 发送媒体URL消息
   */
  private async sendMediaUrlMessage(data: InternalMediaUrlMessageData): Promise<any> {
    const { sessionId, messageType, content, fileName, fileSize, fileType, fileExt, resourceId } = data
    
    const messageData = {
      resource_id: resourceId || '',
      message_type: messageType,
      content,
      file_name: fileName,
      file_size: fileSize,
      file_type: fileType,
      file_ext: fileExt
    }
    
    return sendMediaUrlMessage(sessionId, messageData)
  }

  /**
   * 从上传响应中提取文件数据
   */
  private extractFileDataFromResponse(response: any, originalFile: File) {
    // 根据实际的上传接口响应格式调整
    const data = response.data || response
    
    return {
      url: data.url || data.file_url || data.path,
      file_name: data.file_name || data.filename || originalFile.name,
      file_size: data.file_size || data.size || originalFile.size,
      file_type: data.file_type || data.mime_type || originalFile.type,
      file_ext: data.file_ext || this.getFileExtension(originalFile.name),
      file_id: data.file_id || data.id
    }
  }

  /**
   * 获取文件用途标识
   */
  private getFileUsage(file: File): string {
    if (file.type.startsWith('image/')) {
      return 'chat_image'
    } else if (file.type.startsWith('video/')) {
      return 'chat_video'
    } else if (file.type.startsWith('audio/')) {
      return 'chat_voice'
    } else {
      return 'chat_file'
    }
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(fileName: string): string {
    const lastDotIndex = fileName.lastIndexOf('.')
    return lastDotIndex !== -1 ? fileName.substring(lastDotIndex) : ''
  }

  /**
   * 获取类型显示名称
   */
  private getTypeDisplayName(type: string): string {
    const typeNames: Record<string, string> = {
      image: '图片',
      file: '文件',
      voice: '语音',
      video: '视频'
    }
    return typeNames[type] || '文件'
  }

  /**
   * 根据文件类型自动判断消息类型
   */
  static getMessageTypeFromFile(file: File): 'image' | 'file' | 'voice' | 'video' {
    if (file.type.startsWith('image/')) {
      return 'image'
    } else if (file.type.startsWith('video/')) {
      return 'video'
    } else if (file.type.startsWith('audio/')) {
      return 'voice'
    } else {
      return 'file'
    }
  }

  /**
   * 验证文件类型和大小
   */
  static validateFile(file: File, maxSize: number = 10 * 1024 * 1024): { valid: boolean; error?: string } {
    // 检查文件大小
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`
      }
    }

    // 检查文件类型（可以根据需要扩展）
    const allowedTypes = [
      'image/', 'video/', 'audio/',
      'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument',
      'text/', 'application/json'
    ]

    const isAllowed = allowedTypes.some(type => file.type.startsWith(type))
    if (!isAllowed) {
      return {
        valid: false,
        error: '不支持的文件类型'
      }
    }

    return { valid: true }
  }
}

/**
 * 导出单例实例
 */
export const mediaMessageService = new MediaMessageService()

/**
 * 便捷方法：发送媒体消息（推荐使用）
 */
export async function sendMediaMessageWithFile(
  sessionId: number,
  file: File,
  onProgress?: (progress: number) => void
): Promise<any> {
  const type = MediaMessageService.getMessageTypeFromFile(file)
  
  // 验证文件
  const validation = MediaMessageService.validateFile(file)
  if (!validation.valid) {
    throw new Error(validation.error)
  }
  
  // 使用直接上传方式（推荐）
  return mediaMessageService.uploadAndSendMediaMessage({
    sessionId,
    file,
    type,
    onProgress
  })
}

/**
 * 便捷方法：分步发送媒体消息（备用方案）
 */
export async function sendMediaMessageInSteps(
  sessionId: number,
  file: File,
  onProgress?: (progress: number) => void
): Promise<any> {
  const type = MediaMessageService.getMessageTypeFromFile(file)

  // 验证文件
  const validation = MediaMessageService.validateFile(file)
  if (!validation.valid) {
    throw new Error(validation.error)
  }

  // 使用分步上传方式
  return mediaMessageService.uploadFileThenSendMessage({
    sessionId,
    file,
    type,
    onProgress
  })
}

/**
 * 新的便捷方法：使用上传后的文件数据发送媒体URL消息
 */
export interface MediaUrlMessageData {
  resource_id: number | string    // 上传后返回的文件ID
  content: string                 // 媒体文件URL
  message_type: string           // 媒体类型 (image/file等)
  file_name: string              // 文件名称
  file_size: number              // 文件大小
  file_type: string              // 文件MIME类型
  file_ext: string               // 文件扩展名
}

export async function sendMediaUrlMessageWithData(
  sessionId: number | string,
  mediaData: MediaUrlMessageData
): Promise<any> {
  try {
    console.log(`📨 发送媒体URL消息到会话 ${sessionId}:`, mediaData)

    // 调用媒体URL消息API
    const response = await sendMediaUrlMessage(sessionId, mediaData)

    console.log('✅ 媒体URL消息发送成功:', response)
    return response

  } catch (error: any) {
    console.error('❌ 媒体URL消息发送失败:', error)
    throw new Error(`发送媒体消息失败: ${error.message}`)
  }
}
