/**
 * 聊天文件上传服务
 */

import { post } from '@/utils/request'
import { getAutoConfiguredUploadUrl } from './uploadConfig'

export interface ChatFileUploadOptions {
  sessionId?: string | number
  onProgress?: (progress: number) => void
  compress?: boolean
}

export interface ChatFileUploadResult {
  success: boolean
  data?: {
    id: string
    url: string
    filename: string
    size: number
    type: string
    thumbnail_url?: string
    width?: number
    height?: number
    duration?: number
  }
  message?: string
  error?: string
}

export interface ChatMessageFileData {
  id: string
  url: string
  filename: string
  size: number
  type: string
  thumbnail_url?: string
  width?: number
  height?: number
  duration?: number
}

/**
 * 聊天文件上传服务类
 */
export class ChatFileService {
  private static instance: ChatFileService
  
  // 支持的图片类型
  private readonly imageTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp'
  ]

  // 支持的文件类型
  private readonly fileTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/csv',
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed',
    'video/mp4',
    'video/webm',
    'video/ogg',
    'audio/mp3',
    'audio/wav',
    'audio/ogg',
    'audio/mpeg'
  ]

  // 文件大小限制 (字节)
  private readonly maxImageSize = 5 * 1024 * 1024  // 5MB
  private readonly maxFileSize = 10 * 1024 * 1024  // 10MB

  private constructor() {}

  public static getInstance(): ChatFileService {
    if (!ChatFileService.instance) {
      ChatFileService.instance = new ChatFileService()
    }
    return ChatFileService.instance
  }

  /**
   * 验证文件
   */
  public validateFile(file: File): { valid: boolean; error?: string } {
    // 检查文件大小
    const isImage = this.isImageFile(file)
    const maxSize = isImage ? this.maxImageSize : this.maxFileSize
    
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `文件大小不能超过 ${this.formatFileSize(maxSize)}`
      }
    }

    // 检查文件类型
    const allowedTypes = [...this.imageTypes, ...this.fileTypes]
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: `不支持的文件类型: ${file.type}`
      }
    }

    // 检查文件名长度
    if (file.name.length > 255) {
      return {
        valid: false,
        error: '文件名过长，请重命名后上传'
      }
    }

    return { valid: true }
  }

  /**
   * 上传聊天图片
   */
  public async uploadChatImage(
    file: File, 
    options: ChatFileUploadOptions = {}
  ): Promise<ChatFileUploadResult> {
    // 验证文件
    const validation = this.validateFile(file)
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error
      }
    }

    // 确保是图片文件
    if (!this.isImageFile(file)) {
      return {
        success: false,
        error: '请选择图片文件'
      }
    }

    try {
      // 可选的图片压缩
      let processedFile = file
      if (options.compress !== false && file.size > 1024 * 1024) { // 大于1MB时压缩
        processedFile = await this.compressImage(file)
      }

      return await this.uploadFile(processedFile, 'chat_image', options)
    } catch (error: any) {
      console.error('上传聊天图片失败:', error)
      return {
        success: false,
        error: error.message || '图片上传失败'
      }
    }
  }

  /**
   * 上传聊天文件
   */
  public async uploadChatFile(
    file: File, 
    options: ChatFileUploadOptions = {}
  ): Promise<ChatFileUploadResult> {
    // 验证文件
    const validation = this.validateFile(file)
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error
      }
    }

    try {
      return await this.uploadFile(file, 'chat_file', options)
    } catch (error: any) {
      console.error('上传聊天文件失败:', error)
      return {
        success: false,
        error: error.message || '文件上传失败'
      }
    }
  }

  /**
   * 通用文件上传方法
   */
  private async uploadFile(
    file: File,
    usage: 'chat_image' | 'chat_file',
    options: ChatFileUploadOptions
  ): Promise<ChatFileUploadResult> {
    // 动态获取上传URL（根据当前layout自动选择）
    const uploadUrl = getAutoConfiguredUploadUrl()

    const formData = new FormData()
    formData.append('file', file)
    formData.append('file_usage', usage)

    if (options.sessionId) {
      formData.append('session_id', options.sessionId.toString())
    }

    console.log(`📤 ChatFileService上传文件: ${file.name} -> ${uploadUrl}`)

    const response = await post(uploadUrl, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent: any) => {
        if (options.onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          options.onProgress(progress)
        }
      }
    })

    return {
      success: true,
      data: (response as any).data || response,
      message: (response as any).message || '上传成功'
    }
  }

  /**
   * 压缩图片
   */
  private compressImage(file: File, quality: number = 0.8): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        try {
          // 计算压缩后的尺寸
          const maxWidth = 1920
          const maxHeight = 1080
          let { width, height } = img

          if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height)
            width *= ratio
            height *= ratio
          }

          canvas.width = width
          canvas.height = height

          // 绘制压缩后的图片
          ctx?.drawImage(img, 0, 0, width, height)

          // 转换为Blob
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const compressedFile = new File([blob], file.name, {
                  type: file.type,
                  lastModified: file.lastModified
                })
                resolve(compressedFile)
              } else {
                reject(new Error('图片压缩失败'))
              }
            },
            file.type,
            quality
          )
        } catch (error) {
          reject(error)
        }
      }

      img.onerror = () => {
        reject(new Error('图片加载失败'))
      }

      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * 判断是否为图片文件
   */
  public isImageFile(file: File): boolean {
    return this.imageTypes.includes(file.type)
  }

  /**
   * 判断是否为支持的文件类型
   */
  public isSupportedFile(file: File): boolean {
    return [...this.imageTypes, ...this.fileTypes].includes(file.type)
  }

  /**
   * 获取文件类型图标
   */
  public getFileTypeIcon(fileType: string): string {
    if (fileType.startsWith('image/')) {
      return '🖼️'
    } else if (fileType.startsWith('video/')) {
      return '🎥'
    } else if (fileType.startsWith('audio/')) {
      return '🎵'
    } else if (fileType === 'application/pdf') {
      return '📄'
    } else if (fileType.includes('word') || fileType.includes('document')) {
      return '📝'
    } else if (fileType.includes('excel') || fileType.includes('spreadsheet')) {
      return '📊'
    } else if (fileType.includes('powerpoint') || fileType.includes('presentation')) {
      return '📽️'
    } else if (fileType.includes('zip') || fileType.includes('rar') || fileType.includes('7z')) {
      return '📦'
    } else {
      return '📎'
    }
  }

  /**
   * 格式化文件大小
   */
  public formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  /**
   * 创建文件消息数据
   */
  public createFileMessageData(
    uploadResult: ChatFileUploadResult,
    file: File
  ): ChatMessageFileData | null {
    if (!uploadResult.success || !uploadResult.data) {
      return null
    }

    return {
      id: uploadResult.data.id,
      url: uploadResult.data.url,
      filename: uploadResult.data.filename || file.name,
      size: uploadResult.data.size || file.size,
      type: uploadResult.data.type || file.type,
      thumbnail_url: uploadResult.data.thumbnail_url,
      width: uploadResult.data.width,
      height: uploadResult.data.height,
      duration: uploadResult.data.duration
    }
  }
}

// 导出单例实例
export const chatFileService = ChatFileService.getInstance()
export default chatFileService
