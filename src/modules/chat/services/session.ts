/**
 * 会话服务
 */

import type {
  Session,
  CreateSessionRequest,
  CreateSessionResponse,
  GetSessionsRequest,
  GetSessionsResponse,
  UpdateSessionRequest,
  UpdateSessionResponse,
  SearchSessionsRequest,
  SearchSessionsResponse,
  // SessionStats, // 未使用，注释掉
  // SessionOperationLog, // 不存在，注释掉
  // SessionInviteRequest, // 不存在，注释掉
  // SessionTransfer, // 未使用，注释掉
  SessionType,
  SessionStatus
} from '../types/session'
import { Logger } from '../utils/logger'
// import { API_ENDPOINTS, API_TIMEOUT } from '../constants' // 暂时注释掉

/**
 * 会话服务类
 */
export class SessionService {
  private baseUrl: string
  private logger: Logger
  private abortController: AbortController | null = null

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
    this.logger = new Logger('SessionService')
  }

  /**
   * 创建会话
   */
  async createSession(request: CreateSessionRequest): Promise<CreateSessionResponse> {
    try {
      this.logger.debug('Creating session:', request)
      
      const response = await this.fetch(`${this.baseUrl}/sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })
      
      const result = await response.json() as CreateSessionResponse
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to create session')
      }
      
      this.logger.debug('Session created successfully:', result.session)
      return result
      
    } catch (error) {
      this.logger.error('Failed to create session:', error)
      throw error
    }
  }

  /**
   * 获取会话列表
   */
  async getSessions(request: GetSessionsRequest): Promise<GetSessionsResponse> {
    try {
      this.logger.debug('🔧 [SessionService] getSessions方法开始执行:', request)
      
      const params = new URLSearchParams()
      if (request.type) params.append('type', request.type)
      if (request.status) params.append('status', request.status)
      if (request.participant_id) params.append('participant_id', request.participant_id.toString())
      if (request.participant_type) params.append('participant_type', request.participant_type)
      if (request.page) params.append('page', request.page.toString())
      if (request.page_size) params.append('page_size', request.page_size.toString())
      if (request.sort_by) params.append('sort_by', request.sort_by)
      if (request.sort_order) params.append('sort_order', request.sort_order)
      
      const url = `${this.baseUrl}/sessions?${params.toString()}`
      this.logger.debug('🔧 [SessionService] 准备调用API:', url)
      
      const response = await this.fetch(url)
      this.logger.debug('🔧 [SessionService] API响应状态:', response.status)
      
      const result = await response.json()
      this.logger.debug('🔧 [SessionService] API响应数据:', result)
      
      // 处理标准的API响应格式 {code, message, data}
      if (result.code !== 200) {
        throw new Error(result.message || 'Failed to get sessions')
      }
      
      // 转换为GetSessionsResponse格式
       const sessionsResponse: GetSessionsResponse = {
         success: true,
         data: result.data,
         error: undefined
       }
      
      this.logger.debug(`🔧 [SessionService] 成功获取 ${result.data?.sessions?.length || result.data?.list?.length || 0} 个会话`)
      return sessionsResponse
      
    } catch (error) {
      this.logger.error('🔧 [SessionService] getSessions失败:', error)
      throw error
    }
  }

  /**
   * 获取会话详情
   */
  async getSessionById(sessionId: number): Promise<Session | null> {
    try {
      this.logger.debug('Getting session by ID:', sessionId)
      
      const response = await this.fetch(`${this.baseUrl}/sessions/${sessionId}`)
      
      if (response.status === 404) {
        return null
      }
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get session')
      }
      
      this.logger.debug('Session retrieved successfully:', result.session)
      return result.session
      
    } catch (error) {
      this.logger.error('Failed to get session by ID:', error)
      throw error
    }
  }

  /**
   * 更新会话
   */
  async updateSession(sessionId: number, request: UpdateSessionRequest): Promise<UpdateSessionResponse> {
    try {
      this.logger.debug('Updating session:', { sessionId, request })
      
      const response = await this.fetch(`${this.baseUrl}/sessions/${sessionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })
      
      const result = await response.json() as UpdateSessionResponse
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to update session')
      }
      
      this.logger.debug('Session updated successfully:', result.session)
      return result
      
    } catch (error) {
      this.logger.error('Failed to update session:', error)
      throw error
    }
  }

  /**
   * 关闭会话
   */
  async closeSession(sessionId: number, reason?: string): Promise<boolean> {
    try {
      this.logger.debug('Closing session:', { sessionId, reason })
      
      const response = await this.fetch(`${this.baseUrl}/sessions/${sessionId}/close`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reason })
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to close session')
      }
      
      this.logger.debug('Session closed successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to close session:', error)
      throw error
    }
  }

  /**
   * 归档会话
   */
  async archiveSession(sessionId: number): Promise<boolean> {
    try {
      this.logger.debug('Archiving session:', sessionId)
      
      const response = await this.fetch(`${this.baseUrl}/sessions/${sessionId}/archive`, {
        method: 'POST'
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to archive session')
      }
      
      this.logger.debug('Session archived successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to archive session:', error)
      throw error
    }
  }

  /**
   * 恢复会话
   */
  async restoreSession(sessionId: number): Promise<boolean> {
    try {
      this.logger.debug('Restoring session:', sessionId)
      
      const response = await this.fetch(`${this.baseUrl}/sessions/${sessionId}/restore`, {
        method: 'POST'
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to restore session')
      }
      
      this.logger.debug('Session restored successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to restore session:', error)
      throw error
    }
  }

  /**
   * 删除会话
   */
  async deleteSession(sessionId: number, hard: boolean = false): Promise<boolean> {
    try {
      this.logger.debug('Deleting session:', { sessionId, hard })
      
      const params = new URLSearchParams()
      if (hard) params.append('hard', 'true')
      
      const url = `${this.baseUrl}/sessions/${sessionId}?${params.toString()}`
      const response = await this.fetch(url, {
        method: 'DELETE'
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete session')
      }
      
      this.logger.debug('Session deleted successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to delete session:', error)
      throw error
    }
  }

  /**
   * 搜索会话
   */
  async searchSessions(request: SearchSessionsRequest): Promise<SearchSessionsResponse> {
    try {
      this.logger.debug('Searching sessions:', request)
      
      const response = await this.fetch(`${this.baseUrl}/sessions/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })
      
      const result = await response.json() as SearchSessionsResponse
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to search sessions')
      }
      
      this.logger.debug(`Found ${result.data?.sessions?.length || 0} sessions`)
      return result
      
    } catch (error) {
      this.logger.error('Failed to search sessions:', error)
      throw error
    }
  }

  /**
   * 加入会话
   */
  async joinSession(sessionId: number, userId: number, userType: string): Promise<boolean> {
    try {
      this.logger.debug('Joining session:', { sessionId, userId, userType })
      
      const response = await this.fetch(`${this.baseUrl}/sessions/${sessionId}/join`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          user_id: userId,
          user_type: userType
        })
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to join session')
      }
      
      this.logger.debug('Joined session successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to join session:', error)
      throw error
    }
  }

  /**
   * 离开会话
   */
  async leaveSession(sessionId: number, userId: number, userType: string): Promise<boolean> {
    try {
      this.logger.debug('Leaving session:', { sessionId, userId, userType })
      
      const response = await this.fetch(`${this.baseUrl}/sessions/${sessionId}/leave`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          user_id: userId,
          user_type: userType
        })
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to leave session')
      }
      
      this.logger.debug('Left session successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to leave session:', error)
      throw error
    }
  }

  /**
   * 邀请用户加入会话
   */
  async inviteToSession(sessionId: number, request: any): Promise<boolean> {
    try {
      this.logger.debug('Inviting to session:', { sessionId, request })
      
      const response = await this.fetch(`${this.baseUrl}/sessions/${sessionId}/invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to invite to session')
      }
      
      this.logger.debug('Invitation sent successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to invite to session:', error)
      throw error
    }
  }

  /**
   * 转接会话
   */
  async transferSession(sessionId: number, request: any): Promise<boolean> {
    try {
      this.logger.debug('Transferring session:', { sessionId, request })
      
      const response = await this.fetch(`${this.baseUrl}/sessions/${sessionId}/transfer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to transfer session')
      }
      
      this.logger.debug('Session transferred successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to transfer session:', error)
      throw error
    }
  }

  /**
   * 获取会话统计
   */
  async getSessionStatistics(type?: SessionType, status?: SessionStatus, startTime?: number, endTime?: number): Promise<any> {
    try {
      this.logger.debug('Getting session statistics:', { type, status, startTime, endTime })
      
      const params = new URLSearchParams()
      if (type) params.append('type', type)
      if (status) params.append('status', status)
      if (startTime) params.append('start_time', startTime.toString())
      if (endTime) params.append('end_time', endTime.toString())
      
      const url = `${this.baseUrl}/sessions/statistics?${params.toString()}`
      const response = await this.fetch(url)
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get session statistics')
      }
      
      this.logger.debug('Session statistics retrieved successfully')
      return result.statistics
      
    } catch (error) {
      this.logger.error('Failed to get session statistics:', error)
      throw error
    }
  }

  /**
   * 获取会话操作日志
   */
  async getSessionLogs(sessionId: number, page: number = 1, perPage: number = 20): Promise<any[]> {
    try {
      this.logger.debug('Getting session logs:', { sessionId, page, perPage })
      
      const params = new URLSearchParams()
      params.append('page', page.toString())
      params.append('per_page', perPage.toString())
      
      const url = `${this.baseUrl}/sessions/${sessionId}/logs?${params.toString()}`
      const response = await this.fetch(url)
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get session logs')
      }
      
      this.logger.debug(`Retrieved ${result.logs?.length || 0} session logs`)
      return result.logs || []
      
    } catch (error) {
      this.logger.error('Failed to get session logs:', error)
      throw error
    }
  }

  /**
   * 设置会话优先级
   */
  async setSessionPriority(sessionId: number, priority: 'low' | 'normal' | 'high' | 'urgent'): Promise<boolean> {
    try {
      this.logger.debug('Setting session priority:', { sessionId, priority })
      
      const response = await this.fetch(`${this.baseUrl}/sessions/${sessionId}/priority`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ priority })
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to set session priority')
      }
      
      this.logger.debug('Session priority set successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to set session priority:', error)
      throw error
    }
  }

  /**
   * 添加会话标签
   */
  async addSessionTags(sessionId: number, tags: string[]): Promise<boolean> {
    try {
      this.logger.debug('Adding session tags:', { sessionId, tags })
      
      const response = await this.fetch(`${this.baseUrl}/sessions/${sessionId}/tags`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ tags })
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to add session tags')
      }
      
      this.logger.debug('Session tags added successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to add session tags:', error)
      throw error
    }
  }

  /**
   * 移除会话标签
   */
  async removeSessionTags(sessionId: number, tags: string[]): Promise<boolean> {
    try {
      this.logger.debug('Removing session tags:', { sessionId, tags })
      
      const response = await this.fetch(`${this.baseUrl}/sessions/${sessionId}/tags`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ tags })
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to remove session tags')
      }
      
      this.logger.debug('Session tags removed successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to remove session tags:', error)
      throw error
    }
  }

  /**
   * 获取会话参与者
   */
  async getSessionParticipants(sessionId: number): Promise<any[]> {
    try {
      this.logger.debug('Getting session participants:', sessionId)
      
      const response = await this.fetch(`${this.baseUrl}/sessions/${sessionId}/participants`)
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get session participants')
      }
      
      this.logger.debug(`Retrieved ${result.participants?.length || 0} participants`)
      return result.participants || []
      
    } catch (error) {
      this.logger.error('Failed to get session participants:', error)
      throw error
    }
  }

  /**
   * 批量操作会话
   */
  async batchUpdateSessions(sessionIds: number[], updates: Partial<UpdateSessionRequest>): Promise<boolean> {
    try {
      this.logger.debug('Batch updating sessions:', { sessionIds, updates })
      
      const response = await this.fetch(`${this.baseUrl}/sessions/batch`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          session_ids: sessionIds,
          updates
        })
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to batch update sessions')
      }
      
      this.logger.debug('Sessions updated successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to batch update sessions:', error)
      throw error
    }
  }

  /**
   * 取消请求
   */
  cancelRequests(): void {
    if (this.abortController) {
      this.abortController.abort()
      this.abortController = null
    }
  }

  /**
   * 通用fetch方法
   */
  private async fetch(url: string, options: RequestInit = {}): Promise<Response> {
    // 创建新的AbortController
    this.abortController = new AbortController()
    
    const defaultOptions: RequestInit = {
      signal: this.abortController.signal,
      headers: {
        'Accept': 'application/json',
        ...options.headers
      }
    }
    
    const mergedOptions = { ...defaultOptions, ...options }
    
    // 设置超时
    const timeoutId = setTimeout(() => {
      if (this.abortController) {
        this.abortController.abort()
      }
    }, 30000) // 30秒超时
    
    try {
      const response = await fetch(url, mergedOptions)
      clearTimeout(timeoutId)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      return response
      
    } catch (error) {
      clearTimeout(timeoutId)
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout or cancelled')
      }
      
      throw error
    }
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.cancelRequests()
    this.logger.info('SessionService destroyed')
  }
}

export default SessionService