/**
 * 文件服务
 */

import type {
  FileUploadConfig
} from '../types/chat'
import { Logger } from '../utils/logger'
// import { API_ENDPOINTS, API_TIMEOUT, MESSAGE } from '../constants' // 暂时注释掉

/**
 * 文件上传进度回调
 */
export type FileUploadProgressCallback = (progress: number) => void

/**
 * 文件上传结果
 */
export interface FileUploadResult {
  success: boolean
  file_id?: string
  file_url?: string
  file_name?: string
  file_size?: number
  file_type?: string
  thumbnail_url?: string
  error?: string
}

/**
 * 文件下载选项
 */
export interface FileDownloadOptions {
  filename?: string
  inline?: boolean // 是否在浏览器中打开而不是下载
}

/**
 * 文件预览信息
 */
export interface FilePreviewInfo {
  can_preview: boolean
  preview_url?: string
  thumbnail_url?: string
  file_type: string
  file_size: number
  dimensions?: {
    width: number
    height: number
  }
  duration?: number // 音视频文件的时长（秒）
}

/**
 * 文件验证结果
 */
export interface FileValidationResult {
  valid: boolean
  error?: string
  warnings?: string[]
}

/**
 * 文件服务类
 */
export class FileService {
  private baseUrl: string
  private logger: Logger
  private config: FileUploadConfig
  private abortController: AbortController | null = null
  private uploadQueue: Map<string, AbortController> = new Map()

  constructor(baseUrl: string, config?: Partial<FileUploadConfig>) {
    this.baseUrl = baseUrl
    this.logger = new Logger('FileService')
    this.config = this.getDefaultConfig(config)
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(config?: Partial<FileUploadConfig>): FileUploadConfig {
    const defaultConfig: FileUploadConfig = {
      upload_url: '/api/v1/files/upload',
      max_file_size: 10 * 1024 * 1024, // 10MB
      allowed_types: [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'video/mp4',
        'video/webm',
        'audio/mp3',
        'audio/wav',
        'audio/ogg',
        'application/pdf',
        'text/plain',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ],
      chunk_size: 1024 * 1024, // 1MB
      concurrent_uploads: 3,
      auto_compress_images: true,
      generate_thumbnails: true,
      // scan_for_viruses: true // 属性不存在，暂时注释掉
    }
    
    return { ...defaultConfig, ...config }
  }

  /**
   * 验证文件
   */
  validateFile(file: File): FileValidationResult {
    const result: FileValidationResult = {
      valid: true,
      warnings: []
    }

    // 检查文件大小
    if (file.size > this.config.max_file_size) {
      result.valid = false
      result.error = `文件大小超过限制 (${this.formatFileSize(this.config.max_file_size)})`
      return result
    }

    // 检查文件类型
    if (!this.config.allowed_types.includes(file.type)) {
      result.valid = false
      result.error = `不支持的文件类型: ${file.type}`
      return result
    }

    // 检查文件名
    if (file.name.length > 255) {
      result.valid = false
      result.error = '文件名过长'
      return result
    }

    // 警告检查
    if (file.size > this.config.max_file_size * 0.8) {
      result.warnings?.push('文件较大，上传可能需要较长时间')
    }

    if (file.type.startsWith('image/') && file.size > 5 * 1024 * 1024) {
      result.warnings?.push('图片文件较大，建议压缩后上传')
    }

    return result
  }

  /**
   * 上传文件
   */
  async uploadFile(
    file: File,
    options?: {
      onProgress?: FileUploadProgressCallback
      compress?: boolean
      generateThumbnail?: boolean
    }
  ): Promise<FileUploadResult> {
    try {
      this.logger.debug('Starting file upload:', {
        name: file.name,
        size: file.size,
        type: file.type
      })

      // 验证文件
      const validation = this.validateFile(file)
      if (!validation.valid) {
        throw new Error(validation.error)
      }

      // 显示警告
      if (validation.warnings && validation.warnings.length > 0) {
        this.logger.warn('File upload warnings:', validation.warnings)
      }

      // 生成上传ID
      const uploadId = this.generateUploadId()
      
      // 创建AbortController用于取消上传
      const abortController = new AbortController()
      this.uploadQueue.set(uploadId, abortController)

      try {
        let processedFile = file

        // 图片压缩
        if (options?.compress !== false && 
            this.config.auto_compress_images && 
            file.type.startsWith('image/')) {
          processedFile = await this.compressImage(file)
          this.logger.debug('Image compressed:', {
            originalSize: file.size,
            compressedSize: processedFile.size
          })
        }

        // 分块上传或直接上传
        let result: FileUploadResult
        if (processedFile.size > (this.config.chunk_size || 1024 * 1024)) {
          result = await this.uploadFileInChunks(
            processedFile,
            uploadId,
            abortController.signal,
            options?.onProgress
          )
        } else {
          result = await this.uploadFileDirect(
            processedFile,
            uploadId,
            abortController.signal,
            options?.onProgress
          )
        }

        this.logger.debug('File upload completed:', result)
        return result

      } finally {
        // 清理上传队列
        this.uploadQueue.delete(uploadId)
      }

    } catch (error) {
      this.logger.error('File upload failed:', error)
      
      if (error instanceof Error && error.name === 'AbortError') {
        return {
          success: false,
          error: '上传已取消'
        }
      }
      
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败'
      }
    }
  }

  /**
   * 直接上传文件
   */
  private async uploadFileDirect(
    file: File,
    uploadId: string,
    signal: AbortSignal,
    onProgress?: FileUploadProgressCallback
  ): Promise<FileUploadResult> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('upload_id', uploadId)

    const xhr = new XMLHttpRequest()
    
    return new Promise((resolve, reject) => {
      // 监听上传进度
      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100
            onProgress(progress)
          }
        })
      }

      // 监听完成
      xhr.addEventListener('load', () => {
        try {
          const response = JSON.parse(xhr.responseText)
          if (xhr.status === 200 && response.success) {
            resolve(response)
          } else {
            reject(new Error(response.error || '上传失败'))
          }
        } catch (error) {
          reject(new Error('响应解析失败'))
        }
      })

      // 监听错误
      xhr.addEventListener('error', () => {
        reject(new Error('网络错误'))
      })

      // 监听取消
      signal.addEventListener('abort', () => {
        xhr.abort()
        reject(new Error('上传已取消'))
      })

      // 发送请求
      xhr.open('POST', `${this.baseUrl}/files/upload`)
      xhr.send(formData)
    })
  }

  /**
   * 分块上传文件
   */
  private async uploadFileInChunks(
    file: File,
    uploadId: string,
    signal: AbortSignal,
    onProgress?: FileUploadProgressCallback
  ): Promise<FileUploadResult> {
    const chunkSize = this.config.chunk_size || 1024 * 1024
    const totalChunks = Math.ceil(file.size / chunkSize)
    let uploadedBytes = 0

    this.logger.debug('Starting chunked upload:', {
      fileSize: file.size,
      chunkSize,
      totalChunks
    })

    // 初始化分块上传
    const initResponse = await this.fetch(`${this.baseUrl}/files/upload/init`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        upload_id: uploadId,
        file_name: file.name,
        file_size: file.size,
        file_type: file.type,
        total_chunks: totalChunks
      }),
      signal
    })

    const initResult = await initResponse.json()
    if (!initResult.success) {
      throw new Error(initResult.error || '初始化分块上传失败')
    }

    // 上传每个分块
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      if (signal.aborted) {
        throw new Error('上传已取消')
      }

      const start = chunkIndex * chunkSize
      const end = Math.min(start + chunkSize, file.size)
      const chunk = file.slice(start, end)

      const formData = new FormData()
      formData.append('chunk', chunk)
      formData.append('upload_id', uploadId)
      formData.append('chunk_index', chunkIndex.toString())
      formData.append('total_chunks', totalChunks.toString())

      const chunkResponse = await this.fetch(`${this.baseUrl}/files/upload/chunk`, {
        method: 'POST',
        body: formData,
        signal
      })

      const chunkResult = await chunkResponse.json()
      if (!chunkResult.success) {
        throw new Error(chunkResult.error || `分块 ${chunkIndex + 1} 上传失败`)
      }

      uploadedBytes += chunk.size
      if (onProgress) {
        const progress = (uploadedBytes / file.size) * 100
        onProgress(progress)
      }

      this.logger.debug(`Chunk ${chunkIndex + 1}/${totalChunks} uploaded`)
    }

    // 完成分块上传
    const completeResponse = await this.fetch(`${this.baseUrl}/files/upload/complete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        upload_id: uploadId
      }),
      signal
    })

    const completeResult = await completeResponse.json()
    if (!completeResult.success) {
      throw new Error(completeResult.error || '完成分块上传失败')
    }

    return completeResult
  }

  /**
   * 压缩图片
   */
  private async compressImage(file: File): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        try {
          // 计算压缩后的尺寸
          const maxWidth = 1920
          const maxHeight = 1080
          let { width, height } = img

          if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height)
            width *= ratio
            height *= ratio
          }

          canvas.width = width
          canvas.height = height

          // 绘制压缩后的图片
          ctx?.drawImage(img, 0, 0, width, height)

          // 转换为Blob
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const compressedFile = new File([blob], file.name, {
                  type: file.type,
                  lastModified: file.lastModified
                })
                resolve(compressedFile)
              } else {
                reject(new Error('图片压缩失败'))
              }
            },
            file.type,
            0.8 // 压缩质量
          )
        } catch (error) {
          reject(error)
        }
      }

      img.onerror = () => {
        reject(new Error('图片加载失败'))
      }

      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * 下载文件
   */
  async downloadFile(fileId: string, options?: FileDownloadOptions): Promise<void> {
    try {
      this.logger.debug('Downloading file:', fileId)

      const params = new URLSearchParams()
      if (options?.filename) {
        params.append('filename', options.filename)
      }
      if (options?.inline) {
        params.append('inline', 'true')
      }

      const url = `${this.baseUrl}/files/download/${fileId}?${params.toString()}`
      
      if (options?.inline) {
        // 在新窗口中打开
        window.open(url, '_blank')
      } else {
        // 下载文件
        const link = document.createElement('a')
        link.href = url
        link.download = options?.filename || ''
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }

      this.logger.debug('File download initiated')
    } catch (error) {
      this.logger.error('File download failed:', error)
      throw error
    }
  }

  /**
   * 获取文件预览信息
   */
  async getFilePreview(fileId: string): Promise<FilePreviewInfo> {
    try {
      this.logger.debug('Getting file preview:', fileId)

      const response = await this.fetch(`${this.baseUrl}/files/preview/${fileId}`)
      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || '获取文件预览失败')
      }

      return result.preview_info
    } catch (error) {
      this.logger.error('Failed to get file preview:', error)
      throw error
    }
  }

  /**
   * 删除文件
   */
  async deleteFile(fileId: string): Promise<boolean> {
    try {
      this.logger.debug('Deleting file:', fileId)

      const response = await this.fetch(`${this.baseUrl}/files/delete/${fileId}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || '删除文件失败')
      }

      this.logger.debug('File deleted successfully')
      return true
    } catch (error) {
      this.logger.error('Failed to delete file:', error)
      throw error
    }
  }

  /**
   * 取消文件上传
   */
  cancelUpload(uploadId: string): boolean {
    const abortController = this.uploadQueue.get(uploadId)
    if (abortController) {
      abortController.abort()
      this.uploadQueue.delete(uploadId)
      this.logger.debug('Upload cancelled:', uploadId)
      return true
    }
    return false
  }

  /**
   * 取消所有上传
   */
  cancelAllUploads(): void {
    for (const [_uploadId, abortController] of this.uploadQueue) {
      abortController.abort()
    }
    this.uploadQueue.clear()
    this.logger.debug('All uploads cancelled')
  }

  /**
   * 获取上传进度
   */
  getUploadProgress(): Map<string, number> {
    // 这里应该返回实际的上传进度
    // 由于示例中没有实现进度跟踪，返回空Map
    return new Map()
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 获取文件类型图标
   */
  getFileTypeIcon(fileType: string): string {
    if (fileType.startsWith('image/')) {
      return '🖼️'
    } else if (fileType.startsWith('video/')) {
      return '🎥'
    } else if (fileType.startsWith('audio/')) {
      return '🎵'
    } else if (fileType === 'application/pdf') {
      return '📄'
    } else if (fileType.includes('word') || fileType.includes('document')) {
      return '📝'
    } else if (fileType.includes('excel') || fileType.includes('spreadsheet')) {
      return '📊'
    } else if (fileType.includes('powerpoint') || fileType.includes('presentation')) {
      return '📽️'
    } else {
      return '📎'
    }
  }

  /**
   * 检查文件类型是否可预览
   */
  canPreviewFile(fileType: string): boolean {
    const previewableTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'video/mp4',
      'video/webm',
      'audio/mp3',
      'audio/wav',
      'audio/ogg',
      'application/pdf',
      'text/plain'
    ]
    
    return previewableTypes.includes(fileType)
  }

  /**
   * 生成上传ID
   */
  private generateUploadId(): string {
    return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取配置
   */
  getConfig(): FileUploadConfig {
    return { ...this.config }
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<FileUploadConfig>): void {
    this.config = { ...this.config, ...config }
    this.logger.debug('File service config updated')
  }

  /**
   * 通用fetch方法
   */
  private async fetch(url: string, options: RequestInit = {}): Promise<Response> {
    const defaultOptions: RequestInit = {
      headers: {
        'Accept': 'application/json',
        ...options.headers
      }
    }

    const mergedOptions = { ...defaultOptions, ...options }

    // 设置超时
    const timeoutId = setTimeout(() => {
      if (this.abortController) {
        this.abortController.abort()
      }
    }, 30000) // 30秒超时

    try {
      const response = await fetch(url, mergedOptions)
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return response
    } catch (error) {
      clearTimeout(timeoutId)

      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout or cancelled')
      }

      throw error
    }
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.cancelAllUploads()
    if (this.abortController) {
      this.abortController.abort()
      this.abortController = null
    }
    this.logger.info('FileService destroyed')
  }
}

export default FileService