/**
 * 聊天文件上传配置服务
 * 根据当前用户类型（用户/商家）动态选择上传接口
 */

export interface UploadEndpoint {
  url: string
  method: 'POST'
}

export type UserType = 'user' | 'merchant'

/**
 * 上传配置类
 */
export class UploadConfigService {
  private static instance: UploadConfigService
  private currentUserType: UserType = 'user'

  private constructor() {}

  public static getInstance(): UploadConfigService {
    if (!UploadConfigService.instance) {
      UploadConfigService.instance = new UploadConfigService()
    }
    return UploadConfigService.instance
  }

  /**
   * 设置当前用户类型
   */
  public setUserType(userType: UserType): void {
    this.currentUserType = userType
    console.log(`🔧 聊天上传配置: 用户类型设置为 ${userType}`)
  }

  /**
   * 获取当前用户类型
   */
  public getUserType(): UserType {
    return this.currentUserType
  }

  /**
   * 根据当前用户类型获取上传接口配置
   */
  public getUploadEndpoint(): UploadEndpoint {
    const endpoints: Record<UserType, UploadEndpoint> = {
      user: {
        url: '/v1/user/secured/upload',
        method: 'POST'
      },
      merchant: {
        url: '/v1/merchant/upload',
        method: 'POST'
      }
    }

    const endpoint = endpoints[this.currentUserType]
    console.log(`📤 聊天上传接口: ${this.currentUserType} -> ${endpoint.url}`)
    
    return endpoint
  }

  /**
   * 自动检测用户类型（基于当前路由）
   */
  public autoDetectUserType(): UserType {
    const currentPath = window.location.pathname
    
    if (currentPath.includes('/merchant')) {
      this.currentUserType = 'merchant'
    } else if (currentPath.includes('/user')) {
      this.currentUserType = 'user'
    } else {
      // 默认为用户类型
      this.currentUserType = 'user'
    }

    console.log(`🔍 自动检测用户类型: ${currentPath} -> ${this.currentUserType}`)
    return this.currentUserType
  }

  /**
   * 获取上传URL
   */
  public getUploadUrl(): string {
    return this.getUploadEndpoint().url
  }

  /**
   * 验证当前配置
   */
  public validateConfig(): boolean {
    const endpoint = this.getUploadEndpoint()
    const isValid = !!(endpoint && endpoint.url && endpoint.method)
    
    if (!isValid) {
      console.error('❌ 聊天上传配置无效:', { userType: this.currentUserType, endpoint })
    } else {
      console.log('✅ 聊天上传配置有效:', { userType: this.currentUserType, endpoint })
    }
    
    return isValid
  }
}

/**
 * 导出单例实例
 */
export const uploadConfigService = UploadConfigService.getInstance()

/**
 * 便捷方法：获取当前上传URL
 */
export function getCurrentUploadUrl(): string {
  // 自动检测用户类型
  uploadConfigService.autoDetectUserType()
  return uploadConfigService.getUploadUrl()
}

/**
 * 便捷方法：设置用户类型并获取上传URL
 */
export function getUploadUrlForUserType(userType: UserType): string {
  uploadConfigService.setUserType(userType)
  return uploadConfigService.getUploadUrl()
}

/**
 * 便捷方法：根据路由自动配置并获取上传URL
 */
export function getAutoConfiguredUploadUrl(): string {
  const userType = uploadConfigService.autoDetectUserType()
  const url = uploadConfigService.getUploadUrl()
  
  console.log(`🚀 聊天上传自动配置完成: ${userType} -> ${url}`)
  return url
}
