/**
 * 聊天模块插件
 * 用于在Vue应用中集成聊天功能
 */

import type { App, Plugin } from 'vue'
// import { createPinia } from 'pinia' // 暂时注释掉未使用的导入
import type { Router } from 'vue-router'

// 导入聊天模块相关内容
import { chatRoutes, chatRouteGuards } from '../router'
import { chatConfig, getCurrentConfig, validateConfig } from '../config'
import { ChatClient } from '../services'
// import { WebSocketManager, EventEmitter, Logger } from '../services' // 暂时注释掉未使用的导入
import { useChatStore, useMessageStore, useSessionStore, useNotificationStore, useFileStore } from '../stores'
import '../styles/index.css'

// 插件选项接口
export interface ChatPluginOptions {
  // 路由配置
  router?: Router
  addRoutes?: boolean
  routePrefix?: string
  
  // 配置覆盖
  config?: Partial<typeof chatConfig>
  
  // 功能开关
  features?: {
    websocket?: boolean
    fileUpload?: boolean
    notification?: boolean
    desktop?: boolean
    sound?: boolean
  }
  
  // 主题配置
  theme?: {
    mode?: 'light' | 'dark' | 'auto'
    primaryColor?: string
    customCSS?: string
  }
  
  // 权限配置
  permissions?: {
    chat?: boolean
    fileUpload?: boolean
    notification?: boolean
    admin?: boolean
  }
  
  // 回调函数
  callbacks?: {
    onReady?: () => void
    onError?: (error: Error) => void
    onConnect?: () => void
    onDisconnect?: () => void
    onMessage?: (message: any) => void
  }
}

// 默认选项
const defaultOptions: ChatPluginOptions = {
  addRoutes: true,
  routePrefix: '/chat',
  features: {
    websocket: true,
    fileUpload: true,
    notification: true,
    desktop: true,
    sound: true
  },
  theme: {
    mode: 'light'
  },
  permissions: {
    chat: true,
    fileUpload: true,
    notification: true,
    admin: false
  }
}

// 聊天插件类
class ChatPlugin {
  private app: App | null = null
  private options: ChatPluginOptions
  private config: typeof chatConfig
  private chatClient: ChatClient | null = null
  // private logger: Logger
  // private eventEmitter: EventEmitter

  constructor(options: ChatPluginOptions = {}) {
    this.options = { ...defaultOptions, ...options }
    this.config = getCurrentConfig()
    // this.logger = new Logger('ChatPlugin')
    // this.eventEmitter = new EventEmitter()
    
    // 合并用户配置
    if (options.config) {
      Object.assign(this.config, options.config)
    }
    
    // 验证配置
    const validation = validateConfig(this.config)
    if (!validation.isValid) {
      console.error('Invalid chat configuration:', validation.errors)
      throw new Error(`Chat configuration validation failed: ${validation.errors.join(', ')}`)
    }
  }
  
  // 安装插件
  install(app: App) {
    this.app = app
    
    try {
      // 注册全局属性
      this.registerGlobalProperties(app)
      
      // 设置主题
      this.setupTheme()
      
      // 添加路由
      if (this.options.addRoutes && this.options.router) {
        this.addRoutes(this.options.router)
      }
      
      // 初始化聊天客户端
      if (this.options.features?.websocket) {
        this.initializeChatClient()
      }
      
      // 设置权限
      this.setupPermissions()
      
      // 注册事件监听器
      this.registerEventListeners()
      
      console.log('Chat plugin installed successfully')
      
      // 调用就绪回调
      if (this.options.callbacks?.onReady) {
        this.options.callbacks.onReady()
      }
    } catch (error) {
      console.error('Failed to install chat plugin:', error)
      
      if (this.options.callbacks?.onError) {
        this.options.callbacks.onError(error as Error)
      }
      
      throw error
    }
  }
  
  // 注册全局属性
  private registerGlobalProperties(app: App) {
    // 注册聊天配置
    app.config.globalProperties.$chatConfig = this.config
    
    // 注册聊天客户端（延迟初始化）
    app.config.globalProperties.$chatClient = null
    
    // 注册事件发射器
    // app.config.globalProperties.$chatEvents = this.eventEmitter
    
    // 注册日志器
    // app.config.globalProperties.$chatLogger = this.logger
    
    // 注册聊天工具函数
    app.config.globalProperties.$chat = {
      // 获取聊天客户端
      getClient: () => this.chatClient,
      
      // 获取配置
      getConfig: () => this.config,
      
      // 发送消息
      sendMessage: async (content: string, sessionId: string, options?: any) => {
        return this.chatClient?.sendMessage({
          content,
          session_id: parseInt(sessionId),
          type: 'text',
          ...options
        })
      },
      
      // 加入会话
      joinSession: async (sessionId: string) => {
        return this.chatClient?.joinSession(parseInt(sessionId))
      },
      
      // 离开会话
      leaveSession: async (sessionId: string) => {
        return this.chatClient?.leaveSession(parseInt(sessionId))
      },
      
      // 检查权限
      hasPermission: (permission: string) => {
        return this.checkPermission(permission)
      }
    }
  }
  
  // 设置主题
  private setupTheme() {
    const { theme } = this.options
    
    if (theme?.mode) {
      document.documentElement.setAttribute('data-theme', theme.mode)
    }
    
    if (theme?.primaryColor) {
      document.documentElement.style.setProperty('--chat-primary-color', theme.primaryColor)
    }
    
    if (theme?.customCSS) {
      const style = document.createElement('style')
      style.textContent = theme.customCSS
      document.head.appendChild(style)
    }
  }
  
  // 添加路由
  private addRoutes(router: Router) {
    const { routePrefix } = this.options
    
    // 添加路由守卫
    router.beforeEach(chatRouteGuards.beforeEnter)
    router.beforeEach(chatRouteGuards.beforeLeave)
    
    // 添加聊天路由
    chatRoutes.forEach(route => {
      if (routePrefix && routePrefix !== '/chat') {
        route.path = route.path.replace('/chat', routePrefix)
      }
      router.addRoute(route)
    })
    
    console.log('Chat routes added successfully')
  }
  
  // 初始化聊天客户端
  private async initializeChatClient() {
    try {
      this.chatClient = new ChatClient({
        apiBaseUrl: '/api/v1/chat',
        wsBaseUrl: import.meta.env?.VITE_WS_URL || '/api/v1/chat/ws',
        userToken: '',
        userId: 0,
        userType: 'user' as any,
        autoReconnect: true,
        maxReconnectAttempts: 5,
        reconnectInterval: 1000,
        heartbeatInterval: 30000,
        messageQueueSize: 100,
        enableNotifications: true,
        enableSound: true,
        debug: false
      })
      
      // 设置全局属性
      if (this.app) {
        this.app.config.globalProperties.$chatClient = this.chatClient
      }
      
      // 注册事件监听器
      this.chatClient.on('connect', () => {
        console.log('Chat client connected')
        if (this.options.callbacks?.onConnect) {
          this.options.callbacks.onConnect()
        }
      })
      
      this.chatClient.on('disconnect', () => {
        console.log('Chat client disconnected')
        if (this.options.callbacks?.onDisconnect) {
          this.options.callbacks.onDisconnect()
        }
      })
      
      this.chatClient.on('message', (message) => {
        if (this.options.callbacks?.onMessage) {
          this.options.callbacks.onMessage(message)
        }
      })
      
      this.chatClient.on('error', (error) => {
        console.error('Chat client error:', error)
        if (this.options.callbacks?.onError) {
          this.options.callbacks.onError(error)
        }
      })
      
      // 连接到服务器
      await this.chatClient.connect()
      
      console.log('Chat client initialized successfully')
    } catch (error) {
      console.error('Failed to initialize chat client:', error)
      throw error
    }
  }
  
  // 设置权限
  private setupPermissions() {
    const { permissions } = this.options
    
    // 存储权限到全局状态
    if (typeof window !== 'undefined') {
      (window as any).__CHAT_PERMISSIONS__ = permissions
    }
  }
  
  // 检查权限
  private checkPermission(permission: string): boolean {
    const permissions = this.options.permissions || {}
    
    switch (permission) {
      case 'chat':
        return permissions.chat ?? true
      case 'fileUpload':
        return permissions.fileUpload ?? true
      case 'notification':
        return permissions.notification ?? true
      case 'admin':
        return permissions.admin ?? false
      default:
        return false
    }
  }
  
  // 注册事件监听器
  private registerEventListeners() {
    // 监听主题变化
    if (this.options.theme?.mode === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const updateTheme = (e: MediaQueryListEvent) => {
        document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light')
      }
      
      mediaQuery.addEventListener('change', updateTheme)
      updateTheme({ matches: mediaQuery.matches } as MediaQueryListEvent)
    }
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // this.chatClient?.pause() // 方法不存在，暂时注释掉
      } else {
        // this.chatClient?.resume() // 方法不存在，暂时注释掉
      }
    })
    
    // 监听网络状态变化
    window.addEventListener('online', () => {
      this.chatClient?.connect() // 使用 connect 替代 reconnect
    })
    
    window.addEventListener('offline', () => {
      this.chatClient?.disconnect()
    })
  }
  
  // 销毁插件
  destroy() {
    if (this.chatClient) {
      this.chatClient.disconnect()
      this.chatClient = null
    }
    
    // this.eventEmitter.removeAllListeners()
    this.app = null
    
    console.log('Chat plugin destroyed')
  }
}

// 创建插件实例
export const createChatPlugin = (options?: ChatPluginOptions): Plugin => {
  const plugin = new ChatPlugin(options)
  
  return {
    install: (app: App) => plugin.install(app)
  }
}

// 聊天组合式函数
export const useChat = () => {
  const chatStore = useChatStore()
  const messageStore = useMessageStore()
  const sessionStore = useSessionStore()
  const notificationStore = useNotificationStore()
  const fileStore = useFileStore()
  
  return {
    // Stores
    chatStore,
    messageStore,
    sessionStore,
    notificationStore,
    fileStore,
    
    // 便捷方法 - 暂时注释掉复杂的方法调用
    // sendMessage: messageStore.sendMessage,
    // joinSession: sessionStore.joinSession,
    // leaveSession: sessionStore.leaveSession,
    // uploadFile: fileStore.uploadFile,
    // showNotification: notificationStore.showNotification
  }
}

// 聊天指令
export const chatDirectives = {
  // 自动聚焦指令
  focus: {
    mounted(el: HTMLElement) {
      el.focus()
    }
  },
  
  // 点击外部关闭指令
  clickOutside: {
    mounted(el: HTMLElement & { _clickOutside?: any }, binding: any) {
      el._clickOutside = (event: Event) => {
        if (!(el === event.target || el.contains(event.target as Node))) {
          binding.value(event)
        }
      }
      document.addEventListener('click', el._clickOutside)
    },
    unmounted(el: HTMLElement & { _clickOutside?: any }) {
      if (el._clickOutside) {
        document.removeEventListener('click', el._clickOutside)
        delete el._clickOutside
      }
    }
  },
  
  // 长按指令
  longPress: {
    mounted(el: HTMLElement & { _longPress?: any }, binding: any) {
      let timer: number | null = null
      
      const start = () => {
        timer = window.setTimeout(() => {
          binding.value()
        }, binding.arg || 500)
      }
      
      const cancel = () => {
        if (timer) {
          clearTimeout(timer)
          timer = null
        }
      }
      
      el.addEventListener('mousedown', start)
      el.addEventListener('mouseup', cancel)
      el.addEventListener('mouseleave', cancel)
      el.addEventListener('touchstart', start)
      el.addEventListener('touchend', cancel)
      el.addEventListener('touchcancel', cancel)
      
      el._longPress = { start, cancel }
    },
    unmounted(el: HTMLElement & { _longPress?: any }) {
      if (el._longPress) {
        const { start, cancel } = el._longPress
      
        el.removeEventListener('mousedown', start)
        el.removeEventListener('mouseup', cancel)
        el.removeEventListener('mouseleave', cancel)
        el.removeEventListener('touchstart', start)
        el.removeEventListener('touchend', cancel)
        el.removeEventListener('touchcancel', cancel)

        delete el._longPress
      }
    }
  }
}

// 类型声明扩展
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $chatConfig: typeof chatConfig
    $chatClient: ChatClient | null
    // $chatEvents: EventEmitter
    // $chatLogger: Logger
    $chat: {
      getClient(): ChatClient | null
      getConfig(): typeof chatConfig
      sendMessage(content: string, sessionId: string, options?: any): Promise<any>
      joinSession(sessionId: string): Promise<void>
      leaveSession(sessionId: string): Promise<void>
      hasPermission(permission: string): boolean
    }
  }
}

// 默认导出
export default createChatPlugin