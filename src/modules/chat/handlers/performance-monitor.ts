/**
 * 消息处理器性能监控
 * @description 监控消息处理的性能指标，提供优化建议
 */

import type { MessageHandleResult } from './base/MessageHandler'
import type { WebSocketMessage } from '../types/websocket'

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  // 消息处理统计
  totalMessages: number
  successfulMessages: number
  failedMessages: number
  
  // 时间统计
  totalProcessingTime: number
  averageProcessingTime: number
  minProcessingTime: number
  maxProcessingTime: number
  
  // 处理器统计
  handlerStats: Map<string, HandlerStats>
  
  // 消息类型统计
  messageTypeStats: Map<string, MessageTypeStats>
  
  // 错误统计
  errorStats: Map<string, number>
  
  // 时间窗口统计
  recentPerformance: RecentPerformance
}

/**
 * 处理器统计
 */
export interface HandlerStats {
  name: string
  type: string
  totalMessages: number
  successfulMessages: number
  failedMessages: number
  totalProcessingTime: number
  averageProcessingTime: number
  lastProcessedAt: number
}

/**
 * 消息类型统计
 */
export interface MessageTypeStats {
  messageType: string
  count: number
  successCount: number
  failureCount: number
  totalProcessingTime: number
  averageProcessingTime: number
  lastProcessedAt: number
}

/**
 * 最近性能统计
 */
export interface RecentPerformance {
  last1Minute: TimeWindowStats
  last5Minutes: TimeWindowStats
  last15Minutes: TimeWindowStats
}

/**
 * 时间窗口统计
 */
export interface TimeWindowStats {
  messageCount: number
  averageProcessingTime: number
  errorRate: number
  throughput: number // 每秒消息数
}

/**
 * 性能事件
 */
export interface PerformanceEvent {
  timestamp: number
  messageId: string
  messageType: string
  handlerName: string
  processingTime: number
  success: boolean
  error?: string
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  private metrics: PerformanceMetrics
  private events: PerformanceEvent[] = []
  private maxEvents = 10000 // 最大保存事件数
  private startTime = Date.now()
  
  constructor() {
    this.metrics = this.initializeMetrics()
  }
  
  /**
   * 初始化性能指标
   */
  private initializeMetrics(): PerformanceMetrics {
    return {
      totalMessages: 0,
      successfulMessages: 0,
      failedMessages: 0,
      totalProcessingTime: 0,
      averageProcessingTime: 0,
      minProcessingTime: Infinity,
      maxProcessingTime: 0,
      handlerStats: new Map(),
      messageTypeStats: new Map(),
      errorStats: new Map(),
      recentPerformance: {
        last1Minute: { messageCount: 0, averageProcessingTime: 0, errorRate: 0, throughput: 0 },
        last5Minutes: { messageCount: 0, averageProcessingTime: 0, errorRate: 0, throughput: 0 },
        last15Minutes: { messageCount: 0, averageProcessingTime: 0, errorRate: 0, throughput: 0 }
      }
    }
  }
  
  /**
   * 记录消息处理事件
   */
  recordMessageProcessing(
    message: WebSocketMessage,
    results: MessageHandleResult[],
    processingTime: number
  ): void {
    const timestamp = Date.now()
    
    // 更新总体统计
    this.metrics.totalMessages++
    this.metrics.totalProcessingTime += processingTime
    this.metrics.averageProcessingTime = this.metrics.totalProcessingTime / this.metrics.totalMessages
    this.metrics.minProcessingTime = Math.min(this.metrics.minProcessingTime, processingTime)
    this.metrics.maxProcessingTime = Math.max(this.metrics.maxProcessingTime, processingTime)
    
    const hasSuccess = results.some(r => r.success)
    if (hasSuccess) {
      this.metrics.successfulMessages++
    } else {
      this.metrics.failedMessages++
    }
    
    // 更新消息类型统计
    this.updateMessageTypeStats(message.type, processingTime, hasSuccess)
    
    // 更新处理器统计
    for (const result of results) {
      this.updateHandlerStats(result, processingTime)
      
      // 记录错误
      if (!result.success && result.error) {
        const errorKey = `${result.handlerName}:${result.error}`
        this.metrics.errorStats.set(errorKey, (this.metrics.errorStats.get(errorKey) || 0) + 1)
      }
      
      // 记录事件
      this.events.push({
        timestamp,
        messageId: message.id,
        messageType: message.type,
        handlerName: result.handlerName,
        processingTime,
        success: result.success,
        error: result.error
      })
    }
    
    // 限制事件数量
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents)
    }
    
    // 更新最近性能统计
    this.updateRecentPerformance()
  }
  
  /**
   * 更新消息类型统计
   */
  private updateMessageTypeStats(messageType: string, processingTime: number, success: boolean): void {
    let stats = this.metrics.messageTypeStats.get(messageType)
    if (!stats) {
      stats = {
        messageType,
        count: 0,
        successCount: 0,
        failureCount: 0,
        totalProcessingTime: 0,
        averageProcessingTime: 0,
        lastProcessedAt: 0
      }
      this.metrics.messageTypeStats.set(messageType, stats)
    }
    
    stats.count++
    stats.totalProcessingTime += processingTime
    stats.averageProcessingTime = stats.totalProcessingTime / stats.count
    stats.lastProcessedAt = Date.now()
    
    if (success) {
      stats.successCount++
    } else {
      stats.failureCount++
    }
  }
  
  /**
   * 更新处理器统计
   */
  private updateHandlerStats(result: MessageHandleResult, processingTime: number): void {
    let stats = this.metrics.handlerStats.get(result.handlerName)
    if (!stats) {
      stats = {
        name: result.handlerName,
        type: result.handlerType || 'unknown',
        totalMessages: 0,
        successfulMessages: 0,
        failedMessages: 0,
        totalProcessingTime: 0,
        averageProcessingTime: 0,
        lastProcessedAt: 0
      }
      this.metrics.handlerStats.set(result.handlerName, stats)
    }
    
    stats.totalMessages++
    stats.totalProcessingTime += processingTime
    stats.averageProcessingTime = stats.totalProcessingTime / stats.totalMessages
    stats.lastProcessedAt = Date.now()
    
    if (result.success) {
      stats.successfulMessages++
    } else {
      stats.failedMessages++
    }
  }
  
  /**
   * 更新最近性能统计
   */
  private updateRecentPerformance(): void {
    const now = Date.now()
    const windows = [
      { key: 'last1Minute' as const, duration: 60 * 1000 },
      { key: 'last5Minutes' as const, duration: 5 * 60 * 1000 },
      { key: 'last15Minutes' as const, duration: 15 * 60 * 1000 }
    ]
    
    for (const window of windows) {
      const cutoff = now - window.duration
      const recentEvents = this.events.filter(e => e.timestamp >= cutoff)
      
      const stats = this.metrics.recentPerformance[window.key]
      stats.messageCount = recentEvents.length
      
      if (recentEvents.length > 0) {
        stats.averageProcessingTime = recentEvents.reduce((sum, e) => sum + e.processingTime, 0) / recentEvents.length
        stats.errorRate = recentEvents.filter(e => !e.success).length / recentEvents.length
        stats.throughput = recentEvents.length / (window.duration / 1000)
      } else {
        stats.averageProcessingTime = 0
        stats.errorRate = 0
        stats.throughput = 0
      }
    }
  }
  
  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    this.updateRecentPerformance()
    return { ...this.metrics }
  }
  
  /**
   * 获取性能报告
   */
  getPerformanceReport(): string {
    const metrics = this.getMetrics()
    const uptime = Date.now() - this.startTime
    
    let report = '📊 消息处理器性能报告\n'
    report += '='.repeat(50) + '\n\n'
    
    // 总体统计
    report += '📈 总体统计:\n'
    report += `  运行时间: ${Math.round(uptime / 1000)}秒\n`
    report += `  总消息数: ${metrics.totalMessages}\n`
    report += `  成功消息: ${metrics.successfulMessages} (${((metrics.successfulMessages / metrics.totalMessages) * 100).toFixed(1)}%)\n`
    report += `  失败消息: ${metrics.failedMessages} (${((metrics.failedMessages / metrics.totalMessages) * 100).toFixed(1)}%)\n`
    report += `  平均处理时间: ${metrics.averageProcessingTime.toFixed(2)}ms\n`
    report += `  最小处理时间: ${metrics.minProcessingTime === Infinity ? 0 : metrics.minProcessingTime.toFixed(2)}ms\n`
    report += `  最大处理时间: ${metrics.maxProcessingTime.toFixed(2)}ms\n\n`
    
    // 最近性能
    report += '⏱️ 最近性能:\n'
    report += `  最近1分钟: ${metrics.recentPerformance.last1Minute.messageCount}条消息, `
    report += `平均${metrics.recentPerformance.last1Minute.averageProcessingTime.toFixed(2)}ms, `
    report += `错误率${(metrics.recentPerformance.last1Minute.errorRate * 100).toFixed(1)}%\n`
    report += `  最近5分钟: ${metrics.recentPerformance.last5Minutes.messageCount}条消息, `
    report += `平均${metrics.recentPerformance.last5Minutes.averageProcessingTime.toFixed(2)}ms, `
    report += `错误率${(metrics.recentPerformance.last5Minutes.errorRate * 100).toFixed(1)}%\n`
    report += `  最近15分钟: ${metrics.recentPerformance.last15Minutes.messageCount}条消息, `
    report += `平均${metrics.recentPerformance.last15Minutes.averageProcessingTime.toFixed(2)}ms, `
    report += `错误率${(metrics.recentPerformance.last15Minutes.errorRate * 100).toFixed(1)}%\n\n`
    
    // 处理器统计
    report += '🔧 处理器统计:\n'
    const sortedHandlers = Array.from(metrics.handlerStats.values())
      .sort((a, b) => b.totalMessages - a.totalMessages)
    
    for (const handler of sortedHandlers.slice(0, 10)) {
      report += `  ${handler.name} (${handler.type}): ${handler.totalMessages}条消息, `
      report += `平均${handler.averageProcessingTime.toFixed(2)}ms, `
      report += `成功率${((handler.successfulMessages / handler.totalMessages) * 100).toFixed(1)}%\n`
    }
    
    // 消息类型统计
    report += '\n📝 消息类型统计:\n'
    const sortedMessageTypes = Array.from(metrics.messageTypeStats.values())
      .sort((a, b) => b.count - a.count)
    
    for (const msgType of sortedMessageTypes.slice(0, 10)) {
      report += `  ${msgType.messageType}: ${msgType.count}条消息, `
      report += `平均${msgType.averageProcessingTime.toFixed(2)}ms, `
      report += `成功率${((msgType.successCount / msgType.count) * 100).toFixed(1)}%\n`
    }
    
    // 错误统计
    if (metrics.errorStats.size > 0) {
      report += '\n❌ 错误统计:\n'
      const sortedErrors = Array.from(metrics.errorStats.entries())
        .sort((a, b) => b[1] - a[1])
      
      for (const [error, count] of sortedErrors.slice(0, 5)) {
        report += `  ${error}: ${count}次\n`
      }
    }
    
    return report
  }
  
  /**
   * 重置统计数据
   */
  reset(): void {
    this.metrics = this.initializeMetrics()
    this.events = []
    this.startTime = Date.now()
  }
  
  /**
   * 获取性能建议
   */
  getPerformanceRecommendations(): string[] {
    const metrics = this.getMetrics()
    const recommendations: string[] = []
    
    // 检查平均处理时间
    if (metrics.averageProcessingTime > 100) {
      recommendations.push('⚠️ 平均处理时间较长(>100ms)，建议优化消息处理逻辑')
    }
    
    // 检查错误率
    const errorRate = metrics.failedMessages / metrics.totalMessages
    if (errorRate > 0.05) {
      recommendations.push('⚠️ 错误率较高(>5%)，建议检查错误处理逻辑')
    }
    
    // 检查处理器性能
    for (const [name, stats] of metrics.handlerStats) {
      if (stats.averageProcessingTime > 50) {
        recommendations.push(`⚠️ 处理器 ${name} 平均处理时间较长(${stats.averageProcessingTime.toFixed(2)}ms)`)
      }
      
      const handlerErrorRate = stats.failedMessages / stats.totalMessages
      if (handlerErrorRate > 0.1) {
        recommendations.push(`⚠️ 处理器 ${name} 错误率较高(${(handlerErrorRate * 100).toFixed(1)}%)`)
      }
    }
    
    // 检查最近性能趋势
    const recent1Min = metrics.recentPerformance.last1Minute
    const recent5Min = metrics.recentPerformance.last5Minutes
    
    if (recent1Min.errorRate > recent5Min.errorRate * 2) {
      recommendations.push('⚠️ 最近1分钟错误率显著上升，建议检查系统状态')
    }
    
    if (recent1Min.averageProcessingTime > recent5Min.averageProcessingTime * 1.5) {
      recommendations.push('⚠️ 最近1分钟处理时间显著增加，可能存在性能问题')
    }
    
    if (recommendations.length === 0) {
      recommendations.push('✅ 系统性能良好，无需特别优化')
    }
    
    return recommendations
  }
}

// 全局性能监控器实例
export const globalPerformanceMonitor = new PerformanceMonitor()

// 在开发环境下暴露性能监控器
if (import.meta.env.DEV && typeof window !== 'undefined') {
  (window as any).performanceMonitor = globalPerformanceMonitor

  console.log('💡 性能监控器已暴露到window.performanceMonitor')
  console.log('  - window.performanceMonitor.getMetrics()')
  console.log('  - window.performanceMonitor.getPerformanceReport()')
  console.log('  - window.performanceMonitor.getPerformanceRecommendations()')
}
