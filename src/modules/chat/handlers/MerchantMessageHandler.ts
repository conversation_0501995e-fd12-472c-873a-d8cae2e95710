/**
 * 商家消息处理器
 * @description 处理商家特有的WebSocket消息类型
 */

import { BaseMessageHandler } from './base/BaseMessageHandler'
import type { MessageHandleResult, MessageHandlerConfig } from './base/MessageHandler'
import type { WebSocketMessage } from '../types/websocket'
import { useChatStore } from '../stores/chat'
import { getNotificationFilter } from '../config/notificationFilter'

/**
 * 商家特有的消息类型 - 根据后端广播文档更新
 */
export enum MerchantMessageType {
  // 后端广播文档中的商家消息类型
  // 订单相关通知
  MERCHANT_NEW_ORDER = 'merchant_new_order',
  MERCHANT_ORDER_CANCEL = 'merchant_order_cancel',
  MERCHANT_ORDER_STATUS_UPDATE = 'merchant_order_status_update',

  // 退款相关通知
  MERCHANT_REFUND_REQUEST = 'merchant_refund_request',
  MERCHANT_REFUND_STATUS_UPDATE = 'merchant_refund_status_update',

  // 营业状态通知
  MERCHANT_BUSINESS_STATUS = 'merchant_business_status',
  MERCHANT_STORE_CLOSING_REMINDER = 'merchant_store_closing_reminder',
  MERCHANT_STORE_OPENING_REMINDER = 'merchant_store_opening_reminder',

  // 商品管理通知
  MERCHANT_PRODUCT_AUDIT = 'merchant_product_audit',
  MERCHANT_PRODUCT_STOCK_ALERT = 'merchant_product_stock_alert',
  MERCHANT_PRODUCT_OFFLINE = 'merchant_product_offline',

  // 评价和投诉通知
  MERCHANT_NEW_REVIEW = 'merchant_new_review',
  MERCHANT_COMPLAINT = 'merchant_complaint',

  // 促销活动通知
  MERCHANT_PROMOTION_START = 'merchant_promotion_start',
  MERCHANT_PROMOTION_END = 'merchant_promotion_end',
  MERCHANT_COUPON_USAGE = 'merchant_coupon_usage',

  // 财务相关通知
  MERCHANT_SETTLEMENT = 'merchant_settlement',
  MERCHANT_WITHDRAWAL_STATUS = 'merchant_withdrawal_status',

  // 系统通知
  MERCHANT_SYSTEM_NOTIFICATION = 'merchant_system_notification',
  MERCHANT_POLICY_UPDATE = 'merchant_policy_update',

  // 保留原有的兼容性类型
  NEW_ORDER = 'new_order',
  ORDER_STATUS_CHANGE = 'order_status_change',
  ORDER_PAYMENT_STATUS = 'order_payment_status',
  ORDER_CANCELLATION_REQUEST = 'order_cancellation_request',
  ORDER_RETURN_REQUEST = 'order_return_request',

  // 客户服务
  CUSTOMER_INQUIRY = 'customer_inquiry',
  CUSTOMER_SERVICE_REQUEST = 'customer_service_request',
  CUSTOMER_COMPLAINT = 'customer_complaint',
  CUSTOMER_REVIEW_RECEIVED = 'customer_review_received',

  // 商品管理
  PRODUCT_AUDIT_RESULT = 'product_audit_result',
  PRODUCT_STATUS_CHANGE = 'product_status_change',
  PRODUCT_INVENTORY_ALERT = 'product_inventory_alert',
  PRODUCT_PRICE_ALERT = 'product_price_alert',

  // 店铺管理
  SHOP_STATUS_CHANGE = 'shop_status_change',
  SHOP_AUDIT_RESULT = 'shop_audit_result',
  SHOP_RATING_UPDATE = 'shop_rating_update',
  SHOP_VIOLATION_WARNING = 'shop_violation_warning',

  // 营销活动
  PROMOTION_STATUS = 'promotion_status',
  CAMPAIGN_NOTIFICATION = 'campaign_notification',
  COUPON_USAGE_ALERT = 'coupon_usage_alert',
  MARKETING_PERFORMANCE = 'marketing_performance',

  // 财务相关
  SETTLEMENT_NOTIFICATION = 'settlement_notification',
  PAYMENT_RECEIVED = 'payment_received',
  COMMISSION_DEDUCTION = 'commission_deduction',
  FINANCIAL_STATEMENT = 'financial_statement',
  WITHDRAWAL_STATUS = 'withdrawal_status',

  // 库存管理
  INVENTORY_LOW_STOCK = 'inventory_low_stock',
  INVENTORY_OUT_OF_STOCK = 'inventory_out_of_stock',
  INVENTORY_RESTOCK_REMINDER = 'inventory_restock_reminder',

  // 物流相关
  SHIPPING_STATUS_UPDATE = 'shipping_status_update',
  DELIVERY_EXCEPTION = 'delivery_exception',
  LOGISTICS_PARTNER_NOTIFICATION = 'logistics_partner_notification',

  // 数据分析
  SALES_REPORT = 'sales_report',
  PERFORMANCE_METRICS = 'performance_metrics',
  COMPETITOR_ANALYSIS = 'competitor_analysis'
}

/**
 * 商家消息处理器实现
 */
export class MerchantMessageHandler extends BaseMessageHandler {
  private merchantStore: any
  private chatStore: any

  constructor(config: MessageHandlerConfig = {}) {
    super('Merchant', 'merchant', config)
  }

  /**
   * 初始化处理器
   */
  protected async onInitialize(): Promise<void> {
    // 初始化chatStore
    this.chatStore = useChatStore()

    // 动态导入商家store
    try {
      const { useMerchantStore } = await import('@/modules/merchant/stores/merchantStore')
      this.merchantStore = useMerchantStore()
      this.debug('MerchantMessageHandler initialized with merchant store')
    } catch (error) {
      this.logger.warn('Failed to load merchant store, some features may not work:', error)
    }
  }

  /**
   * 获取支持的消息类型列表
   */
  getSupportedMessageTypes(): string[] {
    return [
      // WebSocket基础消息类型
      'notification',
      'message',
      'system_message',
      // 商家特有的消息类型
      ...Object.values(MerchantMessageType)
    ]
  }

  /**
   * 处理具体的消息逻辑
   */
  protected async processMessage(message: WebSocketMessage): Promise<MessageHandleResult> {
    this.debug(`Processing merchant message type: ${message.type}`, message)

    try {
      // 首先检查是否是notification类型的消息
      if (message.type === 'notification') {
        return await this.handleMerchantNotificationMessage(message as any)
      }

      switch (message.type as unknown as MerchantMessageType) {
        // 订单相关
        case MerchantMessageType.NEW_ORDER:
          return await this.handleNewOrder(message)
        case MerchantMessageType.ORDER_STATUS_CHANGE:
          return await this.handleOrderStatusChange(message)
        case MerchantMessageType.ORDER_PAYMENT_STATUS:
          return await this.handleOrderPaymentStatus(message)
        case MerchantMessageType.ORDER_CANCELLATION_REQUEST:
          return await this.handleOrderCancellationRequest(message)
        case MerchantMessageType.ORDER_RETURN_REQUEST:
          return await this.handleOrderReturnRequest(message)

        // 客户服务
        case MerchantMessageType.CUSTOMER_INQUIRY:
          return await this.handleCustomerInquiry(message)
        case MerchantMessageType.CUSTOMER_SERVICE_REQUEST:
          return await this.handleCustomerServiceRequest(message)
        case MerchantMessageType.CUSTOMER_COMPLAINT:
          return await this.handleCustomerComplaint(message)
        case MerchantMessageType.CUSTOMER_REVIEW_RECEIVED:
          return await this.handleCustomerReviewReceived(message)

        // 商品管理
        case MerchantMessageType.PRODUCT_AUDIT_RESULT:
          return await this.handleProductAuditResult(message)
        case MerchantMessageType.PRODUCT_STATUS_CHANGE:
          return await this.handleProductStatusChange(message)
        case MerchantMessageType.PRODUCT_INVENTORY_ALERT:
          return await this.handleProductInventoryAlert(message)
        case MerchantMessageType.PRODUCT_PRICE_ALERT:
          return await this.handleProductPriceAlert(message)

        // 店铺管理
        case MerchantMessageType.SHOP_STATUS_CHANGE:
          return await this.handleShopStatusChange(message)
        case MerchantMessageType.SHOP_AUDIT_RESULT:
          return await this.handleShopAuditResult(message)
        case MerchantMessageType.SHOP_RATING_UPDATE:
          return await this.handleShopRatingUpdate(message)
        case MerchantMessageType.SHOP_VIOLATION_WARNING:
          return await this.handleShopViolationWarning(message)

        // 营销活动
        case MerchantMessageType.PROMOTION_STATUS:
          return await this.handlePromotionStatus(message)
        case MerchantMessageType.CAMPAIGN_NOTIFICATION:
          return await this.handleCampaignNotification(message)
        case MerchantMessageType.COUPON_USAGE_ALERT:
          return await this.handleCouponUsageAlert(message)
        case MerchantMessageType.MARKETING_PERFORMANCE:
          return await this.handleMarketingPerformance(message)

        // 财务相关
        case MerchantMessageType.SETTLEMENT_NOTIFICATION:
          return await this.handleSettlementNotification(message)
        case MerchantMessageType.PAYMENT_RECEIVED:
          return await this.handlePaymentReceived(message)
        case MerchantMessageType.COMMISSION_DEDUCTION:
          return await this.handleCommissionDeduction(message)
        case MerchantMessageType.FINANCIAL_STATEMENT:
          return await this.handleFinancialStatement(message)
        case MerchantMessageType.WITHDRAWAL_STATUS:
          return await this.handleWithdrawalStatus(message)

        // 库存管理
        case MerchantMessageType.INVENTORY_LOW_STOCK:
          return await this.handleInventoryLowStock(message)
        case MerchantMessageType.INVENTORY_OUT_OF_STOCK:
          return await this.handleInventoryOutOfStock(message)
        case MerchantMessageType.INVENTORY_RESTOCK_REMINDER:
          return await this.handleInventoryRestockReminder(message)

        // 物流相关
        case MerchantMessageType.SHIPPING_STATUS_UPDATE:
          return await this.handleShippingStatusUpdate(message)
        case MerchantMessageType.DELIVERY_EXCEPTION:
          return await this.handleDeliveryException(message)
        case MerchantMessageType.LOGISTICS_PARTNER_NOTIFICATION:
          return await this.handleLogisticsPartnerNotification(message)

        // 数据分析
        case MerchantMessageType.SALES_REPORT:
          return await this.handleSalesReport(message)
        case MerchantMessageType.PERFORMANCE_METRICS:
          return await this.handlePerformanceMetrics(message)
        case MerchantMessageType.COMPETITOR_ANALYSIS:
          return await this.handleCompetitorAnalysis(message)

        default:
          return this.createErrorResult(`Unsupported merchant message type: ${message.type}`)
      }
    } catch (error) {
      this.logger.error(`Error processing merchant message type ${message.type}:`, error)
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Unknown error',
        { messageType: message.type }
      )
    }
  }

  /**
   * 处理新订单通知
   */
  private async handleNewOrder(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { order_id, customer_info, order_amount, products } = message.data || {}
    
    this.logger.info('New order received:', { order_id, order_amount })
    
    // 创建订单通知
    await this.createMerchantNotification({
      type: 'new_order',
      level: 'info',
      title: 'New Order Received',
      content: `New order #${order_id} for ${order_amount}`,
      action_required: true,
      metadata: {
        order_id,
        customer_info,
        order_amount,
        products
      },
      timestamp: Date.now()
    })
    
    // 更新订单统计
    if (this.merchantStore && this.merchantStore.updateOrderStats) {
      this.merchantStore.updateOrderStats('new_order', { order_id, order_amount })
    }
    
    return this.createSuccessResult({
      type: 'new_order',
      order_id,
      order_amount,
      action_required: true
    })
  }

  /**
   * 处理客户咨询
   */
  private async handleCustomerInquiry(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { inquiry_id, customer_id, product_id, question, priority } = message.data || {}
    
    this.debug('Customer inquiry received:', { inquiry_id, customer_id, product_id })
    
    await this.createMerchantNotification({
      type: 'customer_inquiry',
      level: priority === 'high' ? 'warning' : 'info',
      title: 'Customer Inquiry',
      content: `Customer inquiry about product ${product_id}`,
      action_required: true,
      metadata: {
        inquiry_id,
        customer_id,
        product_id,
        question,
        priority
      },
      timestamp: Date.now()
    })
    
    return this.createSuccessResult({
      type: 'customer_inquiry',
      inquiry_id,
      priority,
      action_required: true
    })
  }

  /**
   * 处理商品审核结果
   */
  private async handleProductAuditResult(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { product_id, status, reason, reviewer_id } = message.data || {}
    
    this.debug('Product audit result:', { product_id, status, reason })
    
    await this.createMerchantNotification({
      type: 'product_audit',
      level: status === 'approved' ? 'success' : 'warning',
      title: 'Product Audit Result',
      content: `Product ${product_id} audit ${status}`,
      metadata: {
        product_id,
        status,
        reason,
        reviewer_id
      },
      timestamp: Date.now()
    })
    
    // 更新商品状态
    if (this.merchantStore && this.merchantStore.updateProductStatus) {
      this.merchantStore.updateProductStatus(product_id, status)
    }
    
    return this.createSuccessResult({
      type: 'product_audit',
      product_id,
      status
    })
  }

  /**
   * 处理库存不足警报
   */
  private async handleInventoryLowStock(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { product_id, current_stock, threshold, product_name } = message.data || {}
    
    this.logger.warn('Low stock alert:', { product_id, current_stock, threshold })
    
    await this.createMerchantNotification({
      type: 'inventory_alert',
      level: 'warning',
      title: 'Low Stock Alert',
      content: `${product_name} stock is low (${current_stock} remaining)`,
      action_required: true,
      metadata: {
        product_id,
        current_stock,
        threshold,
        product_name
      },
      timestamp: Date.now()
    })
    
    return this.createSuccessResult({
      type: 'inventory_alert',
      product_id,
      current_stock,
      action_required: true
    })
  }

  /**
   * 处理结算通知
   */
  private async handleSettlementNotification(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { settlement_id, amount, period, status } = message.data || {}
    
    this.logger.info('Settlement notification:', { settlement_id, amount, period, status })
    
    await this.createMerchantNotification({
      type: 'settlement',
      level: 'info',
      title: 'Settlement Notification',
      content: `Settlement ${settlement_id} for ${amount} is ${status}`,
      metadata: {
        settlement_id,
        amount,
        period,
        status
      },
      timestamp: Date.now()
    })
    
    // 更新财务数据
    if (this.merchantStore && this.merchantStore.updateFinancialData) {
      this.merchantStore.updateFinancialData('settlement', { settlement_id, amount, status })
    }
    
    return this.createSuccessResult({
      type: 'settlement',
      settlement_id,
      amount,
      status
    })
  }

  /**
   * 创建商家通知的通用方法
   */
  private async createMerchantNotification(notification: {
    type: string
    level: 'info' | 'success' | 'warning' | 'error'
    title: string
    content: string
    action_required?: boolean
    metadata?: any
    timestamp: number
  }): Promise<void> {
    this.debug('Creating merchant notification:', notification)
    
    if (this.merchantStore && this.merchantStore.addNotification) {
      this.merchantStore.addNotification({
        id: Date.now(),
        ...notification,
        status: 'unread',
        created_at: new Date().toISOString()
      })
    }
  }

  // 其他处理方法的简化实现
  private async handleOrderStatusChange(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'order_status_change', processed: true })
  }

  private async handleOrderPaymentStatus(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'order_payment_status', processed: true })
  }

  private async handleOrderCancellationRequest(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'order_cancellation_request', processed: true })
  }

  private async handleOrderReturnRequest(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'order_return_request', processed: true })
  }

  private async handleCustomerServiceRequest(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'customer_service_request', processed: true })
  }

  private async handleCustomerComplaint(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'customer_complaint', processed: true })
  }

  private async handleCustomerReviewReceived(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'customer_review_received', processed: true })
  }

  private async handleProductStatusChange(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'product_status_change', processed: true })
  }

  private async handleProductInventoryAlert(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'product_inventory_alert', processed: true })
  }

  private async handleProductPriceAlert(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'product_price_alert', processed: true })
  }

  private async handleShopStatusChange(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'shop_status_change', processed: true })
  }

  private async handleShopAuditResult(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'shop_audit_result', processed: true })
  }

  private async handleShopRatingUpdate(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'shop_rating_update', processed: true })
  }

  private async handleShopViolationWarning(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'shop_violation_warning', processed: true })
  }

  private async handlePromotionStatus(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'promotion_status', processed: true })
  }

  private async handleCampaignNotification(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'campaign_notification', processed: true })
  }

  private async handleCouponUsageAlert(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'coupon_usage_alert', processed: true })
  }

  private async handleMarketingPerformance(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'marketing_performance', processed: true })
  }

  private async handlePaymentReceived(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'payment_received', processed: true })
  }

  private async handleCommissionDeduction(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'commission_deduction', processed: true })
  }

  private async handleFinancialStatement(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'financial_statement', processed: true })
  }

  private async handleWithdrawalStatus(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'withdrawal_status', processed: true })
  }

  private async handleInventoryOutOfStock(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'inventory_out_of_stock', processed: true })
  }

  private async handleInventoryRestockReminder(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'inventory_restock_reminder', processed: true })
  }

  private async handleShippingStatusUpdate(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'shipping_status_update', processed: true })
  }

  private async handleDeliveryException(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'delivery_exception', processed: true })
  }

  private async handleLogisticsPartnerNotification(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'logistics_partner_notification', processed: true })
  }

  private async handleSalesReport(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'sales_report', processed: true })
  }

  private async handlePerformanceMetrics(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'performance_metrics', processed: true })
  }

  private async handleCompetitorAnalysis(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'competitor_analysis', processed: true })
  }

  /**
   * 处理商家通知消息
   */
  private async handleMerchantNotificationMessage(message: any): Promise<MessageHandleResult> {
    this.debug('Processing merchant notification message', message.data)

    // 检查是否是特殊事件类型的通知
    const eventType = message.event
    if (eventType) {
      return await this.handleMerchantEventNotification(message, eventType)
    }

    // 检查notification_type来确定具体的通知类型
    const notificationType = message.notification_type || message.data?.notification_type

    switch (notificationType) {
      case 'refund_request':
        return await this.handleMerchantRefundRequestNotification(message)
      default:
        return await this.handleGenericMerchantNotification(message)
    }
  }

  /**
   * 处理带事件类型的商家通知消息
   */
  private async handleMerchantEventNotification(message: any, eventType: string): Promise<MessageHandleResult> {
    this.debug(`Processing merchant event notification: ${eventType}`, message.data)

    try {
      switch (eventType) {
        // 退款相关通知
        case 'merchant_refund_request':
          return await this.handleMerchantRefundRequestEvent(message)
        case 'merchant_refund_status_update':
          return await this.handleMerchantRefundStatusUpdateEvent(message)

        // 订单相关通知
        case 'merchant_new_order':
          return await this.handleMerchantNewOrderEvent(message)
        case 'merchant_order_cancel':
          return await this.handleMerchantOrderCancelEvent(message)
        case 'merchant_order_status_update':
          return await this.handleMerchantOrderStatusUpdateEvent(message)

        // 营业状态通知
        case 'merchant_business_status':
          return await this.handleMerchantBusinessStatusEvent(message)
        case 'merchant_store_closing_reminder':
          return await this.handleMerchantStoreClosingReminderEvent(message)

        // 商品管理通知
        case 'merchant_product_audit_result':
          return await this.handleMerchantProductAuditResultEvent(message)
        case 'merchant_product_inventory_alert':
          return await this.handleMerchantProductInventoryAlertEvent(message)

        // 财务相关通知
        case 'merchant_settlement_notification':
          return await this.handleMerchantSettlementNotificationEvent(message)
        case 'merchant_payment_received':
          return await this.handleMerchantPaymentReceivedEvent(message)

        default:
          this.debug(`Unknown merchant event type: ${eventType}`)
          return await this.handleGenericMerchantNotification(message)
      }
    } catch (error) {
      this.logger.error(`Error processing merchant event notification ${eventType}:`, error)
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Unknown error',
        { eventType, messageData: message.data }
      )
    }
  }

  /**
   * 处理商家退款申请事件
   */
  private async handleMerchantRefundRequestEvent(message: any): Promise<MessageHandleResult> {
    const data = message.data || {}
    const {
      refund_id,
      order_id,
      order_no,
      refund_amount,
      refund_reason,
      message: refundMessage,
      priority = 3,
      action_url
    } = data

    this.debug('Processing merchant refund request event:', {
      refund_id,
      order_id,
      order_no,
      refund_amount
    })

    try {
      // 检查聊天UI是否打开，如果没有打开则显示ElNotification
      const shouldShowNotification = await this.shouldShowElNotification('notification', 'merchant_refund_request')

      if (shouldShowNotification) {
        const { ElNotification } = await import('element-plus')

        const notificationConfig = this.getNotificationConfig(priority, {
          title: '收到退款申请',
          message: refundMessage || `订单${order_no}收到退款申请，退款金额¥${refund_amount}`,
          onClick: action_url ? () => this.handleActionClick(action_url) : undefined
        })

        ElNotification(notificationConfig)
      }

      // 添加到聊天存储
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: '收到退款申请',
          content: refundMessage || `订单${order_no}收到退款申请，退款金额¥${refund_amount}`,
          category: 'refund' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            refund_id,
            order_id,
            order_no,
            refund_amount,
            refund_reason,
            action_url,
            notification_type: 'refund_request',
            show_in_session_list: false
          }
        }
        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'merchant_refund_request',
        refund_id,
        order_id,
        notification_displayed: shouldShowNotification
      })

    } catch (error) {
      this.logger.error('Error handling merchant refund request event:', error)
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Failed to handle refund request',
        { refund_id, order_id }
      )
    }
  }

  /**
   * 处理通用商家通知
   */
  private async handleGenericMerchantNotification(message: any): Promise<MessageHandleResult> {
    this.debug('Processing generic merchant notification', message)

    try {
      const shouldShowNotification = await this.shouldShowElNotification('notification')

      if (shouldShowNotification) {
        const { ElNotification } = await import('element-plus')

        const title = message.data?.title || message.title || '系统通知'
        const content = message.data?.message || message.message || message.content || '您有新的通知'
        const priority = message.data?.priority || message.priority || 2
        const action_url = message.data?.action_url || message.action_url

        const notificationConfig = this.getNotificationConfig(priority, {
          title,
          message: content,
          onClick: action_url ? () => this.handleActionClick(action_url) : undefined
        })

        ElNotification(notificationConfig)
      }

      return this.createSuccessResult({
        type: 'generic_merchant_notification',
        notification_displayed: shouldShowNotification
      })

    } catch (error) {
      this.logger.error('Error handling generic merchant notification:', error)
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Failed to handle notification'
      )
    }
  }

  /**
   * 处理商家退款申请通知（通过notification_type）
   */
  private async handleMerchantRefundRequestNotification(message: any): Promise<MessageHandleResult> {
    // 重用退款申请事件的处理逻辑
    return await this.handleMerchantRefundRequestEvent(message)
  }

  /**
   * 检查是否应该显示ElNotification
   * 只有在聊天UI未打开时才显示通知，并且通过通知过滤器检查
   */
  private async shouldShowElNotification(messageType?: string, eventType?: string): Promise<boolean> {
    try {
      // 首先检查聊天UI是否打开
      if (this.chatStore && typeof this.chatStore.isChatUIVisible !== 'undefined') {
        const chatUIVisible = this.chatStore.isChatUIVisible
        if (chatUIVisible) {
          return false // 聊天UI打开时不显示通知
        }
      }

      // 然后检查通知过滤器设置
      if (messageType) {
        const notificationFilter = getNotificationFilter('merchant')
        const shouldShow = notificationFilter.shouldShowNotification(messageType, eventType)
        if (!shouldShow) {
          this.debug(`Notification filtered out: ${messageType}${eventType ? `:${eventType}` : ''}`)
          return false
        }
      }

      // 如果通过了所有检查，显示通知
      return true
    } catch (error) {
      this.logger.error('Error checking notification display conditions:', error)
      return true // 出错时默认显示通知
    }
  }

  /**
   * 根据优先级获取通知配置
   */
  private getNotificationConfig(priority: number, options: {
    title: string
    message: string
    onClick?: () => void
  }) {
    const { title, message, onClick } = options

    // 根据优先级确定通知类型和持续时间
    let type: 'success' | 'warning' | 'info' | 'error' = 'info'
    let duration = 5000

    switch (priority) {
      case 1: // 低优先级
        type = 'info'
        duration = 4000
        break
      case 2: // 中优先级
        type = 'warning'
        duration = 6000
        break
      case 3: // 高优先级
        type = 'error'
        duration = 8000
        break
      default:
        type = 'info'
        duration = 5000
    }

    return {
      title,
      message,
      type,
      duration,
      position: 'top-right' as const,
      showClose: true,
      onClick
    }
  }

  /**
   * 处理action_url点击事件
   */
  private handleActionClick(actionUrl: string): void {
    try {
      if (typeof window !== 'undefined') {
        window.location.href = actionUrl
      }
    } catch (error) {
      this.logger.error('Error handling action click:', error)
    }
  }

  // 其他事件处理方法的基础实现
  private async handleMerchantRefundStatusUpdateEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericMerchantNotification(message)
  }

  private async handleMerchantNewOrderEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericMerchantNotification(message)
  }

  private async handleMerchantOrderCancelEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericMerchantNotification(message)
  }

  private async handleMerchantOrderStatusUpdateEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericMerchantNotification(message)
  }

  private async handleMerchantBusinessStatusEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericMerchantNotification(message)
  }

  private async handleMerchantStoreClosingReminderEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericMerchantNotification(message)
  }

  private async handleMerchantProductAuditResultEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericMerchantNotification(message)
  }

  private async handleMerchantProductInventoryAlertEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericMerchantNotification(message)
  }

  private async handleMerchantSettlementNotificationEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericMerchantNotification(message)
  }

  private async handleMerchantPaymentReceivedEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericMerchantNotification(message)
  }
}
