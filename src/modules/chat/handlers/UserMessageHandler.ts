/**
 * 用户消息处理器
 * @description 处理用户特有的WebSocket消息类型
 */

import { BaseMessageHandler } from './base/BaseMessageHandler'
import type { MessageHandleResult, MessageHandlerConfig } from './base/MessageHandler'
import type { WebSocketMessage, NotificationMessage } from '../types/websocket'
import { WebSocketMessageType } from '../types/websocket'
import { useChatStore } from '../stores/chat'
import { getNotificationFilter } from '../config/notificationFilter'

/**
 * 用户特有的消息类型 - 根据后端广播文档更新
 */
export enum UserMessageType {
  // 订单相关通知
  USER_ORDER_STATUS_UPDATE = 'user_order_status_update',
  USER_ORDER_PAYMENT_SUCCESS = 'user_order_payment_success',
  USER_ORDER_DELIVERY_UPDATE = 'user_order_delivery_update',
  USER_ORDER_COMPLETED = 'user_order_completed',

  // 退款相关通知
  USER_REFUND_RESULT = 'user_refund_result',
  USER_REFUND_PROGRESS = 'user_refund_progress',

  // 优惠券和促销通知
  USER_COUPON_RECEIVED = 'user_coupon_received',
  USER_COUPON_EXPIRE_REMINDER = 'user_coupon_expire_reminder',
  USER_PROMOTION = 'user_promotion',

  // 跑腿员任务相关通知
  RUNNER_TASK_ASSIGNED = 'runner_task_assigned',
  RUNNER_TASK_STATUS_UPDATE = 'runner_task_status_update',
  RUNNER_TASK_CANCELLED = 'runner_task_cancelled',

  // 跑腿员收益相关通知
  RUNNER_EARNINGS = 'runner_earnings',
  RUNNER_WITHDRAWAL = 'runner_withdrawal',
  RUNNER_DAILY_EARNINGS = 'runner_daily_earnings',

  // 跑腿员状态相关通知
  RUNNER_STATUS_CHANGE = 'runner_status_change',
  RUNNER_LOCATION_UPDATE = 'runner_location_update',

  // 评价和投诉通知
  USER_REVIEW_REMINDER = 'user_review_reminder',
  USER_REVIEW_REPLY = 'user_review_reply',
  USER_COMPLAINT_STATUS = 'user_complaint_status',

  // 系统通知
  USER_SYSTEM_NOTIFICATION = 'user_system_notification',
  USER_ACCOUNT_SECURITY = 'user_account_security',
  USER_BALANCE_CHANGE = 'user_balance_change',

  // 保留一些原有的兼容性类型
  CART_REMINDER = 'cart_reminder',
  WISHLIST_UPDATE = 'wishlist_update',
  PRICE_ALERT = 'price_alert',
  PRODUCT_RECOMMENDATION = 'product_recommendation',
  FLASH_SALE_NOTIFICATION = 'flash_sale_notification',

  // 客服消息
  CUSTOMER_SERVICE_REPLY = 'customer_service_reply',
  SUPPORT_TICKET_UPDATE = 'support_ticket_update',
  LIVE_CHAT_INVITATION = 'live_chat_invitation',
  FAQ_SUGGESTION = 'faq_suggestion',

  // 账户相关
  PROFILE_UPDATE_RESULT = 'profile_update_result',
  PASSWORD_CHANGE_NOTICE = 'password_change_notice',
  LOGIN_NOTIFICATION = 'login_notification',
  ACCOUNT_VERIFICATION = 'account_verification',

  // 社交功能
  FRIEND_REQUEST = 'friend_request',
  FOLLOW_NOTIFICATION = 'follow_notification',
  SHARE_REWARD = 'share_reward',

  // 支付相关
  PAYMENT_SUCCESS = 'payment_success',
  PAYMENT_FAILED = 'payment_failed',
  REFUND_PROCESSED = 'refund_processed',
  WALLET_BALANCE_UPDATE = 'wallet_balance_update',

  // 物流相关
  SHIPPING_DELAY = 'shipping_delay',
  PACKAGE_ARRIVED = 'package_arrived',
  PICKUP_REMINDER = 'pickup_reminder',
  RETURN_STATUS_UPDATE = 'return_status_update',

  // 个性化推荐
  PERSONALIZED_OFFER = 'personalized_offer',
  BROWSING_HISTORY_REMINDER = 'browsing_history_reminder',
  SIMILAR_PRODUCTS = 'similar_products',
  SEASONAL_RECOMMENDATION = 'seasonal_recommendation'
}

/**
 * 用户消息处理器实现
 */
export class UserMessageHandler extends BaseMessageHandler {
  private userStore: any
  private chatStore: any

  constructor(config: MessageHandlerConfig = {}) {
    super('User', 'user', config)
  }

  /**
   * 初始化处理器
   */
  protected async onInitialize(): Promise<void> {
    // 初始化chatStore
    this.chatStore = useChatStore()

    // 动态导入用户store
    try {
      const { useUserStore } = await import('@/modules/user/stores/userStore')
      this.userStore = useUserStore()
      this.debug('UserMessageHandler initialized with user store')
    } catch (error) {
      this.logger.warn('Failed to load user store, some features may not work:', error)
    }
  }

  /**
   * 获取支持的消息类型列表
   */
  getSupportedMessageTypes(): string[] {
    return [
      // WebSocket基础消息类型
      'notification',
      'message',
      'system_message',
      // 用户特有的消息类型
      ...Object.values(UserMessageType)
    ]
  }

  /**
   * 处理具体的消息逻辑
   */
  protected async processMessage(message: WebSocketMessage): Promise<MessageHandleResult> {
    this.debug(`Processing user message type: ${message.type}`, message)

    try {
      // 首先检查是否是notification类型的消息
      if (message.type === WebSocketMessageType.NOTIFICATION) {
        return await this.handleUserNotificationMessage(message as NotificationMessage)
      }

      switch (message.type as unknown as UserMessageType) {
        // 新增的后端广播消息类型 - 订单相关
        case UserMessageType.USER_ORDER_STATUS_UPDATE:
          return await this.handleUserOrderStatusUpdate(message)
        case UserMessageType.USER_ORDER_PAYMENT_SUCCESS:
          return await this.handleUserOrderPaymentSuccess(message)
        case UserMessageType.USER_ORDER_DELIVERY_UPDATE:
          return await this.handleUserOrderDeliveryUpdate(message)
        case UserMessageType.USER_ORDER_COMPLETED:
          return await this.handleUserOrderCompleted(message)

        // 退款相关
        case UserMessageType.USER_REFUND_RESULT:
          return await this.handleUserRefundResult(message)
        case UserMessageType.USER_REFUND_PROGRESS:
          return await this.handleUserRefundProgress(message)

        // 优惠券和促销
        case UserMessageType.USER_COUPON_RECEIVED:
          return await this.handleUserCouponReceived(message)
        case UserMessageType.USER_COUPON_EXPIRE_REMINDER:
          return await this.handleUserCouponExpireReminder(message)
        case UserMessageType.USER_PROMOTION:
          return await this.handleUserPromotion(message)

        // 跑腿员任务相关
        case UserMessageType.RUNNER_TASK_ASSIGNED:
          return await this.handleRunnerTaskAssigned(message)
        case UserMessageType.RUNNER_TASK_STATUS_UPDATE:
          return await this.handleRunnerTaskStatusUpdate(message)
        case UserMessageType.RUNNER_TASK_CANCELLED:
          return await this.handleRunnerTaskCancelled(message)

        // 跑腿员收益相关
        case UserMessageType.RUNNER_EARNINGS:
          return await this.handleRunnerEarnings(message)
        case UserMessageType.RUNNER_WITHDRAWAL:
          return await this.handleRunnerWithdrawal(message)
        case UserMessageType.RUNNER_DAILY_EARNINGS:
          return await this.handleRunnerDailyEarnings(message)

        // 跑腿员状态相关
        case UserMessageType.RUNNER_STATUS_CHANGE:
          return await this.handleRunnerStatusChange(message)
        case UserMessageType.RUNNER_LOCATION_UPDATE:
          return await this.handleRunnerLocationUpdate(message)

        // 评价和投诉
        case UserMessageType.USER_REVIEW_REMINDER:
          return await this.handleUserReviewReminder(message)
        case UserMessageType.USER_REVIEW_REPLY:
          return await this.handleUserReviewReply(message)
        case UserMessageType.USER_COMPLAINT_STATUS:
          return await this.handleUserComplaintStatus(message)

        // 系统通知
        case UserMessageType.USER_SYSTEM_NOTIFICATION:
          return await this.handleUserSystemNotification(message)
        case UserMessageType.USER_ACCOUNT_SECURITY:
          return await this.handleUserAccountSecurity(message)
        case UserMessageType.USER_BALANCE_CHANGE:
          return await this.handleUserBalanceChange(message)

        // 保留的兼容性类型 - 购物相关
        case UserMessageType.CART_REMINDER:
          return await this.handleCartReminder(message)
        case UserMessageType.WISHLIST_UPDATE:
          return await this.handleWishlistUpdate(message)
        case UserMessageType.PRICE_ALERT:
          return await this.handlePriceAlert(message)
        case UserMessageType.PRODUCT_RECOMMENDATION:
          return await this.handleProductRecommendation(message)
        case UserMessageType.FLASH_SALE_NOTIFICATION:
          return await this.handleFlashSaleNotification(message)

        // 客服消息
        case UserMessageType.CUSTOMER_SERVICE_REPLY:
          return await this.handleCustomerServiceReply(message)
        case UserMessageType.SUPPORT_TICKET_UPDATE:
          return await this.handleSupportTicketUpdate(message)
        case UserMessageType.LIVE_CHAT_INVITATION:
          return await this.handleLiveChatInvitation(message)
        case UserMessageType.FAQ_SUGGESTION:
          return await this.handleFaqSuggestion(message)

        // 账户相关
        case UserMessageType.PROFILE_UPDATE_RESULT:
          return await this.handleProfileUpdateResult(message)
        case UserMessageType.PASSWORD_CHANGE_NOTICE:
          return await this.handlePasswordChangeNotice(message)
        case UserMessageType.LOGIN_NOTIFICATION:
          return await this.handleLoginNotification(message)
        case UserMessageType.ACCOUNT_VERIFICATION:
          return await this.handleAccountVerification(message)

        // 社交功能
        case UserMessageType.FRIEND_REQUEST:
          return await this.handleFriendRequest(message)
        case UserMessageType.FOLLOW_NOTIFICATION:
          return await this.handleFollowNotification(message)
        case UserMessageType.SHARE_REWARD:
          return await this.handleShareReward(message)

        // 支付相关
        case UserMessageType.PAYMENT_SUCCESS:
          return await this.handlePaymentSuccess(message)
        case UserMessageType.PAYMENT_FAILED:
          return await this.handlePaymentFailed(message)
        case UserMessageType.REFUND_PROCESSED:
          return await this.handleRefundProcessed(message)
        case UserMessageType.WALLET_BALANCE_UPDATE:
          return await this.handleWalletBalanceUpdate(message)

        // 物流相关
        case UserMessageType.SHIPPING_DELAY:
          return await this.handleShippingDelay(message)
        case UserMessageType.PACKAGE_ARRIVED:
          return await this.handlePackageArrived(message)
        case UserMessageType.PICKUP_REMINDER:
          return await this.handlePickupReminder(message)
        case UserMessageType.RETURN_STATUS_UPDATE:
          return await this.handleReturnStatusUpdate(message)

        // 个性化推荐
        case UserMessageType.PERSONALIZED_OFFER:
          return await this.handlePersonalizedOffer(message)
        case UserMessageType.BROWSING_HISTORY_REMINDER:
          return await this.handleBrowsingHistoryReminder(message)
        case UserMessageType.SIMILAR_PRODUCTS:
          return await this.handleSimilarProducts(message)
        case UserMessageType.SEASONAL_RECOMMENDATION:
          return await this.handleSeasonalRecommendation(message)

        default:
          return this.createErrorResult(`Unsupported user message type: ${message.type}`)
      }
    } catch (error) {
      this.logger.error(`Error processing user message type ${message.type}:`, error)
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Unknown error',
        { messageType: message.type }
      )
    }
  }

  /**
   * 处理购物车提醒
   */
  private async handleCartReminder(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { cart_items, total_amount, expiry_time } = message.data || {}
    
    this.debug('Cart reminder received:', { cart_items: cart_items?.length, total_amount })
    
    await this.createUserNotification({
      type: 'cart_reminder',
      level: 'info',
      title: 'Cart Reminder',
      content: `You have ${cart_items?.length || 0} items in your cart`,
      metadata: {
        cart_items,
        total_amount,
        expiry_time
      },
      timestamp: Date.now()
    })
    
    return this.createSuccessResult({
      type: 'cart_reminder',
      items_count: cart_items?.length || 0,
      total_amount
    })
  }

  /**
   * 处理价格提醒
   */
  private async handlePriceAlert(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { product_id, product_name, old_price, new_price, discount_percentage } = message.data || {}
    
    this.debug('Price alert received:', { product_id, old_price, new_price })
    
    await this.createUserNotification({
      type: 'price_alert',
      level: 'success',
      title: 'Price Drop Alert',
      content: `${product_name} price dropped by ${discount_percentage}%`,
      action_required: true,
      metadata: {
        product_id,
        product_name,
        old_price,
        new_price,
        discount_percentage
      },
      timestamp: Date.now()
    })
    
    return this.createSuccessResult({
      type: 'price_alert',
      product_id,
      discount_percentage,
      action_required: true
    })
  }

  /**
   * 处理订单确认
   */
  private async handleOrderConfirmed(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { order_id, order_number, total_amount, estimated_delivery } = message.data || {}
    
    this.logger.info('Order confirmed:', { order_id, order_number, total_amount })
    
    await this.createUserNotification({
      type: 'order_confirmed',
      level: 'success',
      title: 'Order Confirmed',
      content: `Your order #${order_number} has been confirmed`,
      metadata: {
        order_id,
        order_number,
        total_amount,
        estimated_delivery
      },
      timestamp: Date.now()
    })
    
    // 更新用户订单状态
    if (this.userStore && this.userStore.updateOrderStatus) {
      this.userStore.updateOrderStatus(order_id, 'confirmed')
    }
    
    return this.createSuccessResult({
      type: 'order_confirmed',
      order_id,
      order_number
    })
  }

  /**
   * 处理优惠券接收
   */
  private async handleCouponReceived(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { coupon_id, coupon_code, discount_amount, expiry_date, usage_conditions } = message.data || {}
    
    this.debug('Coupon received:', { coupon_id, coupon_code, discount_amount })
    
    await this.createUserNotification({
      type: 'coupon_received',
      level: 'success',
      title: 'Coupon Received',
      content: `You received a ${discount_amount} coupon`,
      metadata: {
        coupon_id,
        coupon_code,
        discount_amount,
        expiry_date,
        usage_conditions
      },
      timestamp: Date.now()
    })
    
    // 更新用户优惠券
    if (this.userStore && this.userStore.addCoupon) {
      this.userStore.addCoupon({
        id: coupon_id,
        code: coupon_code,
        discount_amount,
        expiry_date,
        status: 'available'
      })
    }
    
    return this.createSuccessResult({
      type: 'coupon_received',
      coupon_id,
      discount_amount
    })
  }

  /**
   * 处理客服回复
   */
  private async handleCustomerServiceReply(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { ticket_id, agent_name, reply_content, attachments } = message.data || {}
    
    this.debug('Customer service reply received:', { ticket_id, agent_name })
    
    await this.createUserNotification({
      type: 'customer_service_reply',
      level: 'info',
      title: 'Customer Service Reply',
      content: `${agent_name} replied to your support ticket`,
      action_required: true,
      metadata: {
        ticket_id,
        agent_name,
        reply_content,
        attachments
      },
      timestamp: Date.now()
    })
    
    return this.createSuccessResult({
      type: 'customer_service_reply',
      ticket_id,
      agent_name,
      action_required: true
    })
  }

  /**
   * 处理安全警报
   */
  private async handleSecurityAlert(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { alert_type, description, ip_address, location, timestamp: alertTime } = message.data || {}
    
    this.logger.warn('Security alert for user:', { alert_type, ip_address, location })
    
    await this.createUserNotification({
      type: 'security_alert',
      level: 'error',
      title: 'Security Alert',
      content: `Security alert: ${alert_type}`,
      action_required: true,
      metadata: {
        alert_type,
        description,
        ip_address,
        location,
        alert_time: alertTime
      },
      timestamp: Date.now()
    })
    
    return this.createSuccessResult({
      type: 'security_alert',
      alert_type,
      action_required: true
    })
  }

  /**
   * 创建用户通知的通用方法
   */
  private async createUserNotification(notification: {
    type: string
    level: 'info' | 'success' | 'warning' | 'error'
    title: string
    content: string
    action_required?: boolean
    metadata?: any
    timestamp: number
  }): Promise<void> {
    this.debug('Creating user notification:', notification)
    
    if (this.userStore && this.userStore.addNotification) {
      this.userStore.addNotification({
        id: Date.now(),
        ...notification,
        status: 'unread',
        created_at: new Date().toISOString()
      })
    }
  }

  // 其他处理方法的简化实现
  private async handleWishlistUpdate(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'wishlist_update', processed: true })
  }

  private async handleProductRecommendation(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'product_recommendation', processed: true })
  }

  private async handleFlashSaleNotification(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'flash_sale_notification', processed: true })
  }

  private async handleOrderShipped(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'order_shipped', processed: true })
  }

  private async handleOrderDelivered(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'order_delivered', processed: true })
  }

  private async handleOrderCancelled(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'order_cancelled', processed: true })
  }

  private async handleOrderRefunded(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'order_refunded', processed: true })
  }

  private async handleDeliveryUpdate(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'delivery_update', processed: true })
  }

  private async handleSupportTicketUpdate(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'support_ticket_update', processed: true })
  }

  private async handleLiveChatInvitation(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'live_chat_invitation', processed: true })
  }

  private async handleFaqSuggestion(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'faq_suggestion', processed: true })
  }

  private async handleProfileUpdateResult(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'profile_update_result', processed: true })
  }

  private async handlePasswordChangeNotice(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'password_change_notice', processed: true })
  }

  private async handleLoginNotification(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'login_notification', processed: true })
  }

  private async handleAccountVerification(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'account_verification', processed: true })
  }

  private async handleCouponExpiryReminder(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'coupon_expiry_reminder', processed: true })
  }

  private async handlePromotionAvailable(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'promotion_available', processed: true })
  }

  private async handleLoyaltyPointsUpdate(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'loyalty_points_update', processed: true })
  }

  private async handleMembershipUpgrade(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'membership_upgrade', processed: true })
  }

  private async handleFriendRequest(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'friend_request', processed: true })
  }

  private async handleReviewReply(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'review_reply', processed: true })
  }

  private async handleFollowNotification(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'follow_notification', processed: true })
  }

  private async handleShareReward(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'share_reward', processed: true })
  }

  private async handlePaymentSuccess(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'payment_success', processed: true })
  }

  private async handlePaymentFailed(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'payment_failed', processed: true })
  }

  private async handleRefundProcessed(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'refund_processed', processed: true })
  }

  private async handleWalletBalanceUpdate(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'wallet_balance_update', processed: true })
  }

  private async handleShippingDelay(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'shipping_delay', processed: true })
  }

  private async handlePackageArrived(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'package_arrived', processed: true })
  }

  private async handlePickupReminder(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'pickup_reminder', processed: true })
  }

  private async handleReturnStatusUpdate(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'return_status_update', processed: true })
  }

  private async handlePersonalizedOffer(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'personalized_offer', processed: true })
  }

  private async handleBrowsingHistoryReminder(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'browsing_history_reminder', processed: true })
  }

  private async handleSimilarProducts(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'similar_products', processed: true })
  }

  private async handleSeasonalRecommendation(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'seasonal_recommendation', processed: true })
  }

  /**
   * 处理用户通知消息
   */
  private async handleUserNotificationMessage(message: NotificationMessage): Promise<MessageHandleResult> {
    this.debug('Processing user notification message', message.data)

    console.log(`🔍 [UserMessageHandler] 处理用户通知消息:`, {
      messageType: message.type,
      event: (message as any).event,
      notification_type: (message as any).notification_type || (message as any).data?.notification_type,
      data: message.data,
      fullMessage: message
    })

    // 检查是否是特殊事件类型的通知
    const eventType = (message as any).event
    if (eventType) {
      console.log(`🔍 [UserMessageHandler] 发现事件类型: ${eventType}，调用handleEventNotification`)
      return await this.handleEventNotification(message as any, eventType)
    }

    // 检查notification_type来确定具体的通知类型
    const notificationType = (message as any).notification_type || (message as any).data?.notification_type
    console.log(`🔍 [UserMessageHandler] 检查notification_type: ${notificationType}`)

    switch (notificationType) {
      case 'order_user_update':
        console.log(`🔍 [UserMessageHandler] 处理订单更新通知`)
        return await this.handleOrderNotification(message)
      default:
        console.log(`🔍 [UserMessageHandler] 使用通用用户通知处理`)
        return await this.handleGenericUserNotification(message)
    }
  }

  /**
   * 处理带事件类型的通知消息
   */
  private async handleEventNotification(message: any, eventType: string): Promise<MessageHandleResult> {
    this.debug(`Processing event notification: ${eventType}`, message.data)

    console.log(`🔍 [UserMessageHandler] 处理事件通知:`, {
      eventType,
      messageType: message.type,
      data: message.data,
      fullMessage: message
    })

    try {
      switch (eventType) {
        // 订单相关通知
        case 'user_order_status_update':
          return await this.handleUserOrderStatusUpdateEvent(message)
        case 'user_order_payment_success':
          return await this.handleUserOrderPaymentSuccessEvent(message)
        case 'user_order_delivery_update':
          return await this.handleUserOrderDeliveryUpdateEvent(message)
        case 'user_order_completed':
          return await this.handleUserOrderCompletedEvent(message)

        // 退款相关通知
        case 'user_refund_result':
          return await this.handleRefundResultNotification(message)
        case 'user_refund_progress':
          return await this.handleUserRefundProgressEvent(message)

        // 优惠券和促销通知
        case 'user_coupon_received':
          return await this.handleUserCouponReceivedEvent(message)
        case 'user_coupon_expire_reminder':
          return await this.handleUserCouponExpireReminderEvent(message)
        case 'user_promotion':
          return await this.handleUserPromotionEvent(message)

        // 跑腿员任务相关通知
        case 'runner_task_assigned':
          return await this.handleRunnerTaskAssignedEvent(message)
        case 'runner_task_status_update':
          return await this.handleRunnerTaskStatusUpdateEvent(message)
        case 'runner_task_cancelled':
          return await this.handleRunnerTaskCancelledEvent(message)

        // 跑腿员收益相关通知
        case 'runner_earnings':
          return await this.handleRunnerEarningsEvent(message)
        case 'runner_withdrawal':
          return await this.handleRunnerWithdrawalEvent(message)
        case 'runner_daily_earnings':
          return await this.handleRunnerDailyEarningsEvent(message)

        // 跑腿员状态相关通知
        case 'runner_status_change':
          return await this.handleRunnerStatusChangeEvent(message)
        case 'runner_location_update':
          return await this.handleRunnerLocationUpdateEvent(message)

        // 评价和投诉通知
        case 'user_review_reminder':
          return await this.handleUserReviewReminderEvent(message)
        case 'user_review_reply':
          return await this.handleUserReviewReplyEvent(message)
        case 'user_complaint_status':
          return await this.handleUserComplaintStatusEvent(message)

        // 系统通知
        case 'user_system_notification':
          return await this.handleUserSystemNotificationEvent(message)
        case 'user_account_security':
          return await this.handleUserAccountSecurityEvent(message)
        case 'user_balance_change':
          return await this.handleUserBalanceChangeEvent(message)

        default:
          // 对于未知事件类型，使用默认处理
          return await this.handleGenericEventNotification(message, eventType)
      }
    } catch (error) {
      this.logger.error(`Error handling event notification ${eventType}:`, error)
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Unknown error',
        { eventType, messageData: message.data }
      )
    }
  }

  /**
   * 处理退款结果通知
   */
  private async handleRefundResultNotification(message: any): Promise<MessageHandleResult> {
    const data = message.data || {}
    const {
      message: refundMessage,
      status,
      refund_amount,
      refund_no,
      order_no,
      action_url,
      notification_type
    } = data

    this.debug('Processing refund result notification:', {
      status,
      refund_amount,
      refund_no,
      order_no,
      isChatUIVisible: this.chatStore?.isChatUIVisible
    })

    // 检查是否应该显示ElNotification（只有在聊天UI未打开时才显示）
    const shouldShowNotification = await this.shouldShowElNotification('notification', 'user_refund_result')

    // 根据退款状态确定通知类型和图标
    const notificationConfig = this.getRefundNotificationConfig(status, refundMessage, refund_amount)

    // 动态导入ElNotification以避免SSR问题
    try {
      if (shouldShowNotification) {
        const { ElNotification } = await import('element-plus')

        // 显示ElNotification
        ElNotification({
          title: notificationConfig.title,
          message: notificationConfig.message,
          type: notificationConfig.type,
          duration: notificationConfig.duration,
          showClose: true,
          onClick: action_url ? () => {
            // 如果有action_url，点击时跳转
            if (typeof window !== 'undefined') {
              window.location.href = action_url
            }
          } : undefined
        })
      }

      // 同时添加到chatStore中
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: notificationConfig.title,
          content: notificationConfig.message,
          category: notificationConfig.type as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            refund_no,
            order_no,
            refund_amount,
            status,
            action_url,
            notification_type
          }
        }

        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'refund_result_notification',
        event: 'user_refund_result',
        status,
        refund_amount,
        refund_no,
        order_no,
        notification_displayed: shouldShowNotification
      })

    } catch (error) {
      this.logger.error('Failed to show refund notification:', error)

      // 如果ElNotification加载失败，至少记录到chatStore
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: '退款通知',
          content: refundMessage || '您的退款申请有新进展',
          category: status === 'approved' ? 'success' : 'info' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: { refund_no, order_no, refund_amount, status }
        }

        this.chatStore.addNotification(notification)
      }

      return this.createErrorResult('Failed to display notification', {
        refund_no,
        order_no,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * 获取退款通知配置
   */
  private getRefundNotificationConfig(status: string, message: string, refundAmount: number) {
    switch (status) {
      case 'approved':
        return {
          title: '退款申请已通过',
          message: message || `您的退款申请已通过，退款金额 ${refundAmount} 元将在3-5个工作日内退回`,
          type: 'success' as const,
          duration: 8000 // 成功消息显示更长时间
        }
      case 'rejected':
        return {
          title: '退款申请被拒绝',
          message: message || '很抱歉，您的退款申请被拒绝',
          type: 'error' as const,
          duration: 6000
        }
      case 'processing':
        return {
          title: '退款处理中',
          message: message || '您的退款申请正在处理中，请耐心等待',
          type: 'info' as const,
          duration: 5000
        }
      default:
        return {
          title: '退款通知',
          message: message || '您的退款申请有新进展',
          type: 'info' as const,
          duration: 5000
        }
    }
  }

  /**
   * 处理订单通知消息
   */
  private async handleOrderNotification(message: NotificationMessage): Promise<MessageHandleResult> {
    const data = (message as any).notification_data || message.data || {}
    const {
      message: orderMessage,
      order_id,
      order_no,
      status,
      pay_amount,
      action_url
    } = data

    this.debug('Processing order notification:', {
      order_id,
      order_no,
      status,
      pay_amount
    })

    // 检查是否应该显示ElNotification（只有在聊天UI未打开时才显示）
    const shouldShowNotification = await this.shouldShowElNotification('notification', 'order_user_update')

    // 根据订单状态确定通知类型
    const notificationConfig = this.getOrderNotificationConfig(status, orderMessage, order_no)

    // 动态导入ElNotification以避免SSR问题
    try {
      if (shouldShowNotification) {
        const { ElNotification } = await import('element-plus')

        // 显示ElNotification
        ElNotification({
          title: notificationConfig.title,
          message: notificationConfig.message,
          type: notificationConfig.type,
          duration: notificationConfig.duration,
          showClose: true,
          onClick: action_url ? () => {
            // 构建用户订单详情路径
            const userOrderUrl = `/user/takeout/order/${order_id}`
            if (typeof window !== 'undefined') {
              window.location.href = userOrderUrl
            }
          } : undefined
        })
      }

      // 同时添加到chatStore中，但不显示在会话列表中（类似系统通知）
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: notificationConfig.title,
          content: notificationConfig.message,
          category: 'system' as any, // 使用system分类，类似系统通知，不显示在会话列表中
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            order_id,
            order_no,
            status,
            pay_amount,
            action_url: `/user/takeout/order/${order_id}`,
            notification_type: 'order_user_update',
            show_in_session_list: false // 标记不在会话列表中显示
          }
        }

        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'order_notification',
        order_id,
        order_no,
        status,
        notification_displayed: true
      })

    } catch (error) {
      this.logger.error('Failed to show order notification:', error)

      // 如果ElNotification加载失败，至少记录到chatStore
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: '订单通知',
          content: orderMessage || '您的订单状态有更新',
          category: 'order' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            order_id,
            order_no,
            status,
            show_in_session_list: false
          }
        }

        this.chatStore.addNotification(notification)
      }

      return this.createErrorResult('Failed to display notification', {
        order_id,
        order_no,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * 获取订单通知配置
   */
  private getOrderNotificationConfig(status: string, message: string, orderNo: string) {
    switch (status) {
      case '支付成功':
        return {
          title: '订单支付成功',
          message: message || `您的订单 ${orderNo} 支付成功，商家正在准备餐品`,
          type: 'success' as const,
          duration: 6000
        }
      case '已接单':
        return {
          title: '商家已接单',
          message: message || `您的订单 ${orderNo} 已被商家接单，正在准备中`,
          type: 'info' as const,
          duration: 5000
        }
      case '配送中':
        return {
          title: '订单配送中',
          message: message || `您的订单 ${orderNo} 正在配送中，请耐心等待`,
          type: 'info' as const,
          duration: 5000
        }
      case '已完成':
        return {
          title: '订单已完成',
          message: message || `您的订单 ${orderNo} 已完成，感谢您的使用`,
          type: 'success' as const,
          duration: 6000
        }
      case '已取消':
        return {
          title: '订单已取消',
          message: message || `您的订单 ${orderNo} 已取消`,
          type: 'warning' as const,
          duration: 5000
        }
      default:
        return {
          title: '订单状态更新',
          message: message || `您的订单 ${orderNo} 状态已更新为 ${status}`,
          type: 'info' as const,
          duration: 5000
        }
    }
  }

  /**
   * 处理通用用户通知
   */
  private async handleGenericUserNotification(message: NotificationMessage): Promise<MessageHandleResult> {
    this.debug('Processing generic user notification', message.data)

    console.log(`🔍 [UserMessageHandler] 处理通用用户通知:`, {
      messageType: message.type,
      data: message.data,
      fullMessage: message
    })

    try {
      // 检查是否应该显示ElNotification
      const shouldShowNotification = await this.shouldShowElNotification('notification')
      console.log(`🔍 [UserMessageHandler] 通用用户通知是否显示ElNotification:`, shouldShowNotification)

      if (shouldShowNotification) {
        const { ElNotification } = await import('element-plus')

        const title = message.data?.title || (message as any).title || '系统通知'
        const content = message.data?.content || (message as any).message || (message as any).content || '您有新的通知'
        const priority = (message as any).data?.priority || (message as any).priority || 2
        const action_url = message.data?.action_url || (message as any).action_url

        const notificationConfig = this.getNotificationConfig(priority, {
          title,
          message: content,
          onClick: action_url ? () => this.handleActionClick(action_url) : undefined
        })

        console.log(`🔍 [UserMessageHandler] 显示ElNotification:`, notificationConfig)
        ElNotification(notificationConfig)
      }

      // 同时添加到chatStore中
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: message.data?.title || (message as any).title || '系统通知',
          content: message.data?.content || (message as any).content || (message as any).message || '您有新的通知',
          category: message.data?.category || 'info' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }

        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'generic_user_notification',
        title: message.data?.title || (message as any).title,
        category: message.data?.category,
        notification_displayed: shouldShowNotification
      })

    } catch (error) {
      console.error(`🔍 [UserMessageHandler] 处理通用用户通知失败:`, error)
      this.logger.error('Error handling generic user notification:', error)
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Failed to handle notification'
      )
    }
  }

  /**
   * 处理通用事件通知
   */
  private async handleGenericEventNotification(message: any, eventType: string): Promise<MessageHandleResult> {
    const data = message.data || {}

    this.debug(`Processing generic event notification: ${eventType}`, data)
    console.log(`🔍 [UserMessageHandler] 处理通用事件通知:`, {
      eventType,
      messageType: message.type,
      event: message.event,
      data: data,
      fullMessage: message
    })

    // 检查是否应该显示ElNotification（只有在聊天UI未打开时才显示）
    const shouldShowNotification = await this.shouldShowElNotification('notification', eventType)

    // 尝试显示通用通知
    try {
      if (shouldShowNotification) {
        const { ElNotification } = await import('element-plus')

        ElNotification({
          title: data.message || '系统通知',
          message: data.message || `收到新的${eventType}事件`,
          type: 'info',
          duration: 5000,
          showClose: true
        })
      }

      // 同时添加到chatStore中
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: data.message || '系统通知',
          content: data.message || `收到新的${eventType}事件`,
          category: 'info' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            event_type: eventType,
            original_data: data
          }
        }
        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'generic_event_notification',
        event: eventType,
        notification_displayed: shouldShowNotification
      })

    } catch (error) {
      this.logger.error(`Failed to show generic event notification for ${eventType}:`, error)
      return this.createErrorResult('Failed to display notification', { eventType })
    }
  }

  // ==================== 新增的事件处理方法 ====================

  /**
   * 处理订单状态更新事件
   */
  private async handleUserOrderStatusUpdateEvent(message: any): Promise<MessageHandleResult> {
    const data = message.data || {}
    const {
      order_id,
      order_no,
      old_status,
      new_status,
      status_message,
      message: orderMessage,
      priority = 2,
      action_url
    } = data

    this.debug('Processing order status update event:', {
      order_id,
      order_no,
      old_status,
      new_status
    })

    try {
      const { ElNotification } = await import('element-plus')

      const notificationConfig = this.getNotificationConfig(priority, {
        title: '订单状态更新',
        message: orderMessage || status_message || `订单状态已更新：${new_status}`,
        onClick: action_url ? () => this.handleActionClick(action_url) : undefined
      })

      ElNotification(notificationConfig)

      // 添加到聊天存储
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: '订单状态更新',
          content: orderMessage || status_message || `订单状态已更新：${new_status}`,
          category: 'order' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            order_id,
            order_no,
            old_status,
            new_status,
            action_url,
            notification_type: 'order_status_update',
            show_in_session_list: false
          }
        }
        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'order_status_update',
        order_id,
        order_no,
        old_status,
        new_status,
        notification_displayed: true
      })

    } catch (error) {
      this.logger.error('Failed to show order status update notification:', error)
      return this.createErrorResult('Failed to display notification', {
        order_id,
        order_no,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * 处理订单支付成功事件
   */
  private async handleUserOrderPaymentSuccessEvent(message: any): Promise<MessageHandleResult> {
    const data = message.data || {}
    const {
      order_id,
      order_no,
      pay_amount,
      message: orderMessage,
      priority = 3,
      action_url
    } = data

    this.debug('Processing order payment success event:', {
      order_id,
      order_no,
      pay_amount
    })

    try {
      const { ElNotification } = await import('element-plus')

      const notificationConfig = this.getNotificationConfig(priority, {
        title: '支付成功',
        message: orderMessage || `订单支付成功，金额：¥${pay_amount}`,
        onClick: action_url ? () => this.handleActionClick(action_url) : undefined
      })

      ElNotification(notificationConfig)

      // 添加到聊天存储
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: '支付成功',
          content: orderMessage || `订单支付成功，金额：¥${pay_amount}`,
          category: 'payment' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            order_id,
            order_no,
            pay_amount,
            action_url,
            notification_type: 'order_payment_success',
            show_in_session_list: false
          }
        }
        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'order_payment_success',
        order_id,
        order_no,
        pay_amount,
        notification_displayed: true
      })

    } catch (error) {
      this.logger.error('Failed to show payment success notification:', error)
      return this.createErrorResult('Failed to display notification', {
        order_id,
        order_no,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * 处理订单配送更新事件
   */
  private async handleUserOrderDeliveryUpdateEvent(message: any): Promise<MessageHandleResult> {
    const data = message.data || {}
    const {
      order_id,
      order_no,
      delivery_status,
      estimated_time,
      message: orderMessage,
      priority = 2,
      action_url
    } = data

    this.debug('Processing order delivery update event:', {
      order_id,
      order_no,
      delivery_status,
      estimated_time
    })

    try {
      const { ElNotification } = await import('element-plus')

      const notificationConfig = this.getNotificationConfig(priority, {
        title: '配送更新',
        message: orderMessage || `您的订单正在配送中，预计${estimated_time}送达`,
        onClick: action_url ? () => this.handleActionClick(action_url) : undefined
      })

      ElNotification(notificationConfig)

      // 添加到聊天存储
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: '配送更新',
          content: orderMessage || `您的订单正在配送中，预计${estimated_time}送达`,
          category: 'delivery' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            order_id,
            order_no,
            delivery_status,
            estimated_time,
            action_url,
            notification_type: 'order_delivery_update',
            show_in_session_list: false
          }
        }
        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'order_delivery_update',
        order_id,
        order_no,
        delivery_status,
        estimated_time,
        notification_displayed: true
      })

    } catch (error) {
      this.logger.error('Failed to show delivery update notification:', error)
      return this.createErrorResult('Failed to display notification', {
        order_id,
        order_no,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  // ==================== 辅助方法 ====================

  /**
   * 根据优先级获取通知配置
   */
  private getNotificationConfig(priority: number, options: {
    title: string
    message: string
    onClick?: () => void
  }) {
    const baseConfig = {
      title: options.title,
      message: options.message,
      showClose: true,
      onClick: options.onClick
    }

    switch (priority) {
      case 3: // 高优先级
        return {
          ...baseConfig,
          type: 'success' as const,
          duration: 8000,
          position: 'top-right' as const
        }
      case 2: // 中优先级
        return {
          ...baseConfig,
          type: 'info' as const,
          duration: 6000,
          position: 'top-right' as const
        }
      case 1: // 低优先级
      default:
        return {
          ...baseConfig,
          type: 'info' as const,
          duration: 4000,
          position: 'bottom-right' as const
        }
    }
  }

  /**
   * 处理动作点击
   */
  private handleActionClick(actionUrl: string) {
    try {
      // 动态导入router以避免SSR问题
      import('@/router').then(({ default: router }) => {
        if (actionUrl.startsWith('http')) {
          window.open(actionUrl, '_blank')
        } else {
          router.push(actionUrl)
        }
      }).catch(error => {
        this.logger.error('Failed to navigate:', error)
        // 如果路由导入失败，尝试直接跳转
        if (actionUrl.startsWith('http')) {
          window.open(actionUrl, '_blank')
        } else {
          window.location.href = actionUrl
        }
      })
    } catch (error) {
      this.logger.error('Failed to handle action click:', error)
    }
  }

  // ==================== 兼容性方法 ====================

  /**
   * 兼容性方法：处理订单状态更新
   */
  private async handleUserOrderStatusUpdate(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleUserOrderStatusUpdateEvent(message)
  }

  /**
   * 兼容性方法：处理订单支付成功
   */
  private async handleUserOrderPaymentSuccess(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleUserOrderPaymentSuccessEvent(message)
  }

  /**
   * 兼容性方法：处理订单配送更新
   */
  private async handleUserOrderDeliveryUpdate(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleUserOrderDeliveryUpdateEvent(message)
  }

  /**
   * 兼容性方法：处理订单完成
   */
  private async handleUserOrderCompleted(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleUserOrderCompletedEvent(message)
  }

  /**
   * 处理订单完成事件
   */
  private async handleUserOrderCompletedEvent(message: any): Promise<MessageHandleResult> {
    const data = message.data || {}
    const {
      order_id,
      order_no,
      total_amount,
      message: orderMessage,
      priority = 2,
      action_url
    } = data

    this.debug('Processing order completed event:', {
      order_id,
      order_no,
      total_amount
    })

    try {
      const { ElNotification } = await import('element-plus')

      const notificationConfig = this.getNotificationConfig(priority, {
        title: '订单完成',
        message: orderMessage || '订单已完成，快来评价一下吧！',
        onClick: action_url ? () => this.handleActionClick(action_url) : undefined
      })

      ElNotification(notificationConfig)

      // 添加到聊天存储
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: '订单完成',
          content: orderMessage || '订单已完成，快来评价一下吧！',
          category: 'order' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            order_id,
            order_no,
            total_amount,
            action_url,
            notification_type: 'order_completed',
            show_in_session_list: false
          }
        }
        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'order_completed',
        order_id,
        order_no,
        total_amount,
        notification_displayed: true
      })

    } catch (error) {
      this.logger.error('Failed to show order completed notification:', error)
      return this.createErrorResult('Failed to display notification', {
        order_id,
        order_no,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  // ==================== 退款相关事件处理 ====================

  /**
   * 兼容性方法：处理退款结果
   */
  private async handleUserRefundResult(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleRefundResultNotification(message)
  }

  /**
   * 兼容性方法：处理退款进度
   */
  private async handleUserRefundProgress(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleUserRefundProgressEvent(message)
  }

  /**
   * 处理退款进度事件
   */
  private async handleUserRefundProgressEvent(message: any): Promise<MessageHandleResult> {
    const data = message.data || {}
    const {
      refund_id,
      refund_no,
      progress,
      estimated_time,
      message: refundMessage,
      priority = 2,
      action_url
    } = data

    this.debug('Processing refund progress event:', {
      refund_id,
      refund_no,
      progress,
      estimated_time
    })

    try {
      const { ElNotification } = await import('element-plus')

      const notificationConfig = this.getNotificationConfig(priority, {
        title: '退款进度',
        message: refundMessage || `退款正在处理中，预计${estimated_time}完成`,
        onClick: action_url ? () => this.handleActionClick(action_url) : undefined
      })

      ElNotification(notificationConfig)

      // 添加到聊天存储
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: '退款进度',
          content: refundMessage || `退款正在处理中，预计${estimated_time}完成`,
          category: 'refund' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            refund_id,
            refund_no,
            progress,
            estimated_time,
            action_url,
            notification_type: 'refund_progress',
            show_in_session_list: false
          }
        }
        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'refund_progress',
        refund_id,
        refund_no,
        progress,
        estimated_time,
        notification_displayed: true
      })

    } catch (error) {
      this.logger.error('Failed to show refund progress notification:', error)
      return this.createErrorResult('Failed to display notification', {
        refund_id,
        refund_no,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  // ==================== 优惠券和促销相关事件处理 ====================

  /**
   * 兼容性方法：处理优惠券领取
   */
  private async handleUserCouponReceived(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleUserCouponReceivedEvent(message)
  }

  /**
   * 处理优惠券领取事件
   */
  private async handleUserCouponReceivedEvent(message: any): Promise<MessageHandleResult> {
    const data = message.data || {}
    const {
      coupon_id,
      coupon_name,
      discount_amount,
      expire_time,
      message: couponMessage,
      priority = 1,
      action_url
    } = data

    this.debug('Processing coupon received event:', {
      coupon_id,
      coupon_name,
      discount_amount
    })

    try {
      const { ElNotification } = await import('element-plus')

      const notificationConfig = this.getNotificationConfig(priority, {
        title: '优惠券到账',
        message: couponMessage || `恭喜您获得${coupon_name}，立减¥${discount_amount}`,
        onClick: action_url ? () => this.handleActionClick(action_url) : undefined
      })

      ElNotification(notificationConfig)

      // 添加到聊天存储
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: '优惠券到账',
          content: couponMessage || `恭喜您获得${coupon_name}，立减¥${discount_amount}`,
          category: 'coupon' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            coupon_id,
            coupon_name,
            discount_amount,
            expire_time,
            action_url,
            notification_type: 'coupon_received',
            show_in_session_list: false
          }
        }
        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'coupon_received',
        coupon_id,
        coupon_name,
        discount_amount,
        notification_displayed: true
      })

    } catch (error) {
      this.logger.error('Failed to show coupon received notification:', error)
      return this.createErrorResult('Failed to display notification', {
        coupon_id,
        coupon_name,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * 兼容性方法：处理优惠券过期提醒
   */
  private async handleUserCouponExpireReminder(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleUserCouponExpireReminderEvent(message)
  }

  /**
   * 处理优惠券过期提醒事件
   */
  private async handleUserCouponExpireReminderEvent(message: any): Promise<MessageHandleResult> {
    const data = message.data || {}
    const {
      coupon_id,
      coupon_name,
      expire_time,
      message: couponMessage,
      priority = 2,
      action_url
    } = data

    this.debug('Processing coupon expire reminder event:', {
      coupon_id,
      coupon_name,
      expire_time
    })

    try {
      const { ElNotification } = await import('element-plus')

      const notificationConfig = this.getNotificationConfig(priority, {
        title: '优惠券即将过期',
        message: couponMessage || `您的${coupon_name}即将过期，请尽快使用`,
        onClick: action_url ? () => this.handleActionClick(action_url) : undefined
      })

      ElNotification(notificationConfig)

      // 添加到聊天存储
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: '优惠券即将过期',
          content: couponMessage || `您的${coupon_name}即将过期，请尽快使用`,
          category: 'coupon' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            coupon_id,
            coupon_name,
            expire_time,
            action_url,
            notification_type: 'coupon_expire_reminder',
            show_in_session_list: false
          }
        }
        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'coupon_expire_reminder',
        coupon_id,
        coupon_name,
        expire_time,
        notification_displayed: true
      })

    } catch (error) {
      this.logger.error('Failed to show coupon expire reminder notification:', error)
      return this.createErrorResult('Failed to display notification', {
        coupon_id,
        coupon_name,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * 兼容性方法：处理促销活动
   */
  private async handleUserPromotion(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleUserPromotionEvent(message)
  }

  /**
   * 处理促销活动事件
   */
  private async handleUserPromotionEvent(message: any): Promise<MessageHandleResult> {
    const data = message.data || {}
    const {
      promotion_id,
      promotion_title,
      promotion_content,
      message: promotionMessage,
      priority = 1,
      action_url
    } = data

    this.debug('Processing promotion event:', {
      promotion_id,
      promotion_title,
      promotion_content
    })

    try {
      const { ElNotification } = await import('element-plus')

      const notificationConfig = this.getNotificationConfig(priority, {
        title: promotion_title || '促销活动',
        message: promotionMessage || promotion_content || '新的促销活动开始啦！',
        onClick: action_url ? () => this.handleActionClick(action_url) : undefined
      })

      ElNotification(notificationConfig)

      // 添加到聊天存储
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: promotion_title || '促销活动',
          content: promotionMessage || promotion_content || '新的促销活动开始啦！',
          category: 'promotion' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            promotion_id,
            promotion_title,
            promotion_content,
            action_url,
            notification_type: 'promotion',
            show_in_session_list: false
          }
        }
        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'promotion',
        promotion_id,
        promotion_title,
        promotion_content,
        notification_displayed: true
      })

    } catch (error) {
      this.logger.error('Failed to show promotion notification:', error)
      return this.createErrorResult('Failed to display notification', {
        promotion_id,
        promotion_title,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  // ==================== 跑腿员任务相关事件处理 ====================

  /**
   * 兼容性方法：处理跑腿员任务分配
   */
  private async handleRunnerTaskAssigned(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleRunnerTaskAssignedEvent(message)
  }

  /**
   * 处理跑腿员任务分配事件
   */
  private async handleRunnerTaskAssignedEvent(message: any): Promise<MessageHandleResult> {
    const data = message.data || {}
    const {
      task_id,
      task_no,
      task_type,
      pickup_address,
      delivery_address,
      reward,
      message: taskMessage,
      priority = 3,
      action_url
    } = data

    this.debug('Processing runner task assigned event:', {
      task_id,
      task_no,
      task_type,
      reward
    })

    try {
      const { ElNotification } = await import('element-plus')

      const notificationConfig = this.getNotificationConfig(priority, {
        title: '新任务分配',
        message: taskMessage || `新任务分配：${task_type}任务，奖励¥${reward}`,
        onClick: action_url ? () => this.handleActionClick(action_url) : undefined
      })

      ElNotification(notificationConfig)

      // 添加到聊天存储
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: '新任务分配',
          content: taskMessage || `新任务分配：${task_type}任务，奖励¥${reward}`,
          category: 'task' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            task_id,
            task_no,
            task_type,
            pickup_address,
            delivery_address,
            reward,
            action_url,
            notification_type: 'runner_task_assigned',
            show_in_session_list: false
          }
        }
        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'runner_task_assigned',
        task_id,
        task_no,
        task_type,
        reward,
        notification_displayed: true
      })

    } catch (error) {
      this.logger.error('Failed to show runner task assigned notification:', error)
      return this.createErrorResult('Failed to display notification', {
        task_id,
        task_no,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * 兼容性方法：处理跑腿员任务状态更新
   */
  private async handleRunnerTaskStatusUpdate(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleRunnerTaskStatusUpdateEvent(message)
  }

  /**
   * 处理跑腿员任务状态更新事件
   */
  private async handleRunnerTaskStatusUpdateEvent(message: any): Promise<MessageHandleResult> {
    const data = message.data || {}
    const {
      task_id,
      task_no,
      old_status,
      new_status,
      message: taskMessage,
      priority = 2,
      action_url
    } = data

    this.debug('Processing runner task status update event:', {
      task_id,
      task_no,
      old_status,
      new_status
    })

    try {
      const { ElNotification } = await import('element-plus')

      const notificationConfig = this.getNotificationConfig(priority, {
        title: '任务状态更新',
        message: taskMessage || `任务状态已更新：${new_status}`,
        onClick: action_url ? () => this.handleActionClick(action_url) : undefined
      })

      ElNotification(notificationConfig)

      // 添加到聊天存储
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: '任务状态更新',
          content: taskMessage || `任务状态已更新：${new_status}`,
          category: 'task' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            task_id,
            task_no,
            old_status,
            new_status,
            action_url,
            notification_type: 'runner_task_status_update',
            show_in_session_list: false
          }
        }
        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'runner_task_status_update',
        task_id,
        task_no,
        old_status,
        new_status,
        notification_displayed: true
      })

    } catch (error) {
      this.logger.error('Failed to show runner task status update notification:', error)
      return this.createErrorResult('Failed to display notification', {
        task_id,
        task_no,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * 兼容性方法：处理跑腿员任务取消
   */
  private async handleRunnerTaskCancelled(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleRunnerTaskCancelledEvent(message)
  }

  /**
   * 检查是否应该显示ElNotification
   * 只有在聊天UI未打开时才显示通知，并且通过通知过滤器检查
   */
  private async shouldShowElNotification(messageType?: string, eventType?: string): Promise<boolean> {
    try {
      console.log(`🔍 [UserMessageHandler] shouldShowElNotification 检查开始:`, {
        messageType,
        eventType,
        chatUIVisible: this.chatStore?.isChatUIVisible
      })

      // 首先检查聊天UI是否打开
      if (this.chatStore && typeof this.chatStore.isChatUIVisible !== 'undefined') {
        const chatUIVisible = this.chatStore.isChatUIVisible
        if (chatUIVisible) {
          console.log(`🔍 [UserMessageHandler] 聊天UI已打开，不显示通知`)
          return false // 聊天UI打开时不显示通知
        }
      }

      // 然后检查通知过滤器设置
      if (messageType) {
        const notificationFilter = getNotificationFilter('user')
        const shouldShow = notificationFilter.shouldShowNotification(messageType, eventType)
        if (!shouldShow) {
          console.log(`🔍 [UserMessageHandler] 通知被过滤器过滤: ${messageType}${eventType ? `:${eventType}` : ''}`)
          this.debug(`Notification filtered out: ${messageType}${eventType ? `:${eventType}` : ''}`)
          return false
        }
      }

      // 如果通过了所有检查，显示通知
      console.log(`🔍 [UserMessageHandler] 通过所有检查，应该显示通知`)
      return true
    } catch (error) {
      console.error(`🔍 [UserMessageHandler] shouldShowElNotification 检查出错:`, error)
      this.logger.error('Error checking notification display conditions:', error)
      return true // 出错时默认显示通知
    }
  }

  /**
   * 处理跑腿员任务取消事件
   */
  private async handleRunnerTaskCancelledEvent(message: any): Promise<MessageHandleResult> {
    const data = message.data || {}
    const {
      task_id,
      task_no,
      cancel_reason,
      message: taskMessage,
      priority = 2,
      action_url
    } = data

    this.debug('Processing runner task cancelled event:', {
      task_id,
      task_no,
      cancel_reason
    })

    try {
      const { ElNotification } = await import('element-plus')

      const notificationConfig = this.getNotificationConfig(priority, {
        title: '任务已取消',
        message: taskMessage || `任务已取消：${cancel_reason}`,
        onClick: action_url ? () => this.handleActionClick(action_url) : undefined
      })

      ElNotification(notificationConfig)

      // 添加到聊天存储
      if (this.chatStore) {
        const notification = {
          id: Date.now(),
          title: '任务已取消',
          content: taskMessage || `任务已取消：${cancel_reason}`,
          category: 'task' as any,
          status: 'PENDING' as any,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            task_id,
            task_no,
            cancel_reason,
            action_url,
            notification_type: 'runner_task_cancelled',
            show_in_session_list: false
          }
        }
        this.chatStore.addNotification(notification)
      }

      return this.createSuccessResult({
        type: 'runner_task_cancelled',
        task_id,
        task_no,
        cancel_reason,
        notification_displayed: true
      })

    } catch (error) {
      this.logger.error('Failed to show runner task cancelled notification:', error)
      return this.createErrorResult('Failed to display notification', {
        task_id,
        task_no,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  // ==================== 其他事件处理方法的占位符实现 ====================

  // 跑腿员收益相关
  private async handleRunnerEarnings(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleRunnerEarningsEvent(message)
  }

  private async handleRunnerEarningsEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericEventNotification(message, 'runner_earnings')
  }

  private async handleRunnerWithdrawal(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleRunnerWithdrawalEvent(message)
  }

  private async handleRunnerWithdrawalEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericEventNotification(message, 'runner_withdrawal')
  }

  private async handleRunnerDailyEarnings(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleRunnerDailyEarningsEvent(message)
  }

  private async handleRunnerDailyEarningsEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericEventNotification(message, 'runner_daily_earnings')
  }

  // 跑腿员状态相关
  private async handleRunnerStatusChange(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleRunnerStatusChangeEvent(message)
  }

  private async handleRunnerStatusChangeEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericEventNotification(message, 'runner_status_change')
  }

  private async handleRunnerLocationUpdate(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleRunnerLocationUpdateEvent(message)
  }

  private async handleRunnerLocationUpdateEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericEventNotification(message, 'runner_location_update')
  }

  // 评价和投诉相关
  private async handleUserReviewReminder(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleUserReviewReminderEvent(message)
  }

  private async handleUserReviewReminderEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericEventNotification(message, 'user_review_reminder')
  }

  private async handleUserReviewReply(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleUserReviewReplyEvent(message)
  }

  private async handleUserReviewReplyEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericEventNotification(message, 'user_review_reply')
  }

  private async handleUserComplaintStatus(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleUserComplaintStatusEvent(message)
  }

  private async handleUserComplaintStatusEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericEventNotification(message, 'user_complaint_status')
  }

  // 系统通知相关
  private async handleUserSystemNotification(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleUserSystemNotificationEvent(message)
  }

  private async handleUserSystemNotificationEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericEventNotification(message, 'user_system_notification')
  }

  private async handleUserAccountSecurity(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleUserAccountSecurityEvent(message)
  }

  private async handleUserAccountSecurityEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericEventNotification(message, 'user_account_security')
  }

  private async handleUserBalanceChange(message: WebSocketMessage): Promise<MessageHandleResult> {
    return await this.handleUserBalanceChangeEvent(message)
  }

  private async handleUserBalanceChangeEvent(message: any): Promise<MessageHandleResult> {
    return await this.handleGenericEventNotification(message, 'user_balance_change')
  }
}
