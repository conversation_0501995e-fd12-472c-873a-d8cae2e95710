/**
 * 消息处理器性能优化工具
 * @description 提供自动性能优化和调优功能
 */

import { globalPerformanceMonitor } from './performance-monitor'
import type { MessageRouter } from './base/MessageRouter'
import type { MessageHandler } from './base/MessageHandler'

/**
 * 优化配置
 */
export interface OptimizationConfig {
  // 性能阈值
  maxAverageProcessingTime: number // 最大平均处理时间(ms)
  maxErrorRate: number // 最大错误率
  maxMemoryUsage: number // 最大内存使用量(MB)
  
  // 优化策略
  enableHandlerCaching: boolean // 启用处理器缓存
  enableMessageBatching: boolean // 启用消息批处理
  enablePriorityQueue: boolean // 启用优先级队列
  
  // 监控间隔
  monitoringInterval: number // 监控间隔(ms)
  optimizationInterval: number // 优化间隔(ms)
}

/**
 * 优化建议
 */
export interface OptimizationSuggestion {
  type: 'performance' | 'memory' | 'error' | 'throughput'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  action: string
  impact: string
  implementation?: () => Promise<void>
}

/**
 * 性能优化器
 */
export class PerformanceOptimizer {
  private config: OptimizationConfig
  private messageRouter?: MessageRouter
  private monitoringTimer?: NodeJS.Timeout
  private optimizationTimer?: NodeJS.Timeout
  private handlerCache = new Map<string, MessageHandler>()
  private messageBatch: any[] = []
  private batchTimer?: NodeJS.Timeout
  
  constructor(config: Partial<OptimizationConfig> = {}) {
    this.config = {
      maxAverageProcessingTime: 100,
      maxErrorRate: 0.05,
      maxMemoryUsage: 100,
      enableHandlerCaching: true,
      enableMessageBatching: false,
      enablePriorityQueue: false,
      monitoringInterval: 30000, // 30秒
      optimizationInterval: 300000, // 5分钟
      ...config
    }
  }
  
  /**
   * 启动性能优化器
   */
  start(messageRouter: MessageRouter): void {
    this.messageRouter = messageRouter
    
    console.log('🚀 启动性能优化器')
    console.log('📋 优化配置:', this.config)
    
    // 启动监控
    this.startMonitoring()
    
    // 启动定期优化
    this.startOptimization()
  }
  
  /**
   * 停止性能优化器
   */
  stop(): void {
    console.log('⏹️ 停止性能优化器')
    
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer)
      this.monitoringTimer = undefined
    }
    
    if (this.optimizationTimer) {
      clearInterval(this.optimizationTimer)
      this.optimizationTimer = undefined
    }
    
    if (this.batchTimer) {
      clearTimeout(this.batchTimer)
      this.batchTimer = undefined
    }
    
    this.handlerCache.clear()
    this.messageBatch = []
  }
  
  /**
   * 启动监控
   */
  private startMonitoring(): void {
    this.monitoringTimer = setInterval(() => {
      this.performHealthCheck()
    }, this.config.monitoringInterval)
  }
  
  /**
   * 启动定期优化
   */
  private startOptimization(): void {
    this.optimizationTimer = setInterval(() => {
      this.performOptimization()
    }, this.config.optimizationInterval)
  }
  
  /**
   * 执行健康检查
   */
  private performHealthCheck(): void {
    const metrics = globalPerformanceMonitor.getMetrics()
    
    console.log('🔍 执行性能健康检查')
    
    // 检查平均处理时间
    if (metrics.averageProcessingTime > this.config.maxAverageProcessingTime) {
      console.warn(`⚠️ 平均处理时间过长: ${metrics.averageProcessingTime.toFixed(2)}ms > ${this.config.maxAverageProcessingTime}ms`)
    }
    
    // 检查错误率
    const errorRate = metrics.failedMessages / metrics.totalMessages
    if (errorRate > this.config.maxErrorRate) {
      console.warn(`⚠️ 错误率过高: ${(errorRate * 100).toFixed(2)}% > ${(this.config.maxErrorRate * 100).toFixed(2)}%`)
    }
    
    // 检查内存使用
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memoryUsage = (performance as any).memory.usedJSHeapSize / 1024 / 1024
      if (memoryUsage > this.config.maxMemoryUsage) {
        console.warn(`⚠️ 内存使用过高: ${memoryUsage.toFixed(2)}MB > ${this.config.maxMemoryUsage}MB`)
      }
    }
  }
  
  /**
   * 执行性能优化
   */
  private async performOptimization(): Promise<void> {
    console.log('⚡ 执行性能优化')
    
    const suggestions = this.generateOptimizationSuggestions()
    
    if (suggestions.length === 0) {
      console.log('✅ 系统性能良好，无需优化')
      return
    }
    
    console.log(`📋 发现 ${suggestions.length} 个优化建议`)
    
    // 按严重程度排序
    suggestions.sort((a, b) => {
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
      return severityOrder[b.severity] - severityOrder[a.severity]
    })
    
    // 执行自动优化
    for (const suggestion of suggestions) {
      if (suggestion.implementation && suggestion.severity !== 'low') {
        try {
          console.log(`🔧 执行优化: ${suggestion.title}`)
          await suggestion.implementation()
          console.log(`✅ 优化完成: ${suggestion.title}`)
        } catch (error) {
          console.error(`❌ 优化失败: ${suggestion.title}`, error)
        }
      }
    }
  }
  
  /**
   * 生成优化建议
   */
  generateOptimizationSuggestions(): OptimizationSuggestion[] {
    const metrics = globalPerformanceMonitor.getMetrics()
    const suggestions: OptimizationSuggestion[] = []
    
    // 处理时间优化
    if (metrics.averageProcessingTime > this.config.maxAverageProcessingTime) {
      suggestions.push({
        type: 'performance',
        severity: metrics.averageProcessingTime > this.config.maxAverageProcessingTime * 2 ? 'critical' : 'high',
        title: '优化消息处理时间',
        description: `平均处理时间 ${metrics.averageProcessingTime.toFixed(2)}ms 超过阈值 ${this.config.maxAverageProcessingTime}ms`,
        action: '启用处理器缓存和消息批处理',
        impact: '可减少 20-40% 的处理时间',
        implementation: async () => {
          this.config.enableHandlerCaching = true
          this.config.enableMessageBatching = true
        }
      })
    }
    
    // 错误率优化
    const errorRate = metrics.failedMessages / metrics.totalMessages
    if (errorRate > this.config.maxErrorRate) {
      suggestions.push({
        type: 'error',
        severity: errorRate > this.config.maxErrorRate * 2 ? 'critical' : 'high',
        title: '降低错误率',
        description: `错误率 ${(errorRate * 100).toFixed(2)}% 超过阈值 ${(this.config.maxErrorRate * 100).toFixed(2)}%`,
        action: '增强错误处理和重试机制',
        impact: '可减少 50-70% 的错误',
        implementation: async () => {
          // 这里可以实现错误处理优化逻辑
          console.log('🔧 增强错误处理机制')
        }
      })
    }
    
    // 处理器性能优化
    for (const [name, stats] of metrics.handlerStats) {
      if (stats.averageProcessingTime > 50) {
        suggestions.push({
          type: 'performance',
          severity: stats.averageProcessingTime > 100 ? 'high' : 'medium',
          title: `优化处理器 ${name}`,
          description: `处理器 ${name} 平均处理时间 ${stats.averageProcessingTime.toFixed(2)}ms 较长`,
          action: '优化处理器算法或增加缓存',
          impact: '可提升该处理器 30-50% 的性能'
        })
      }
      
      const handlerErrorRate = stats.failedMessages / stats.totalMessages
      if (handlerErrorRate > 0.1) {
        suggestions.push({
          type: 'error',
          severity: handlerErrorRate > 0.2 ? 'high' : 'medium',
          title: `修复处理器 ${name} 错误`,
          description: `处理器 ${name} 错误率 ${(handlerErrorRate * 100).toFixed(1)}% 过高`,
          action: '检查处理器逻辑和错误处理',
          impact: '可提升系统稳定性'
        })
      }
    }
    
    // 内存优化
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memoryUsage = (performance as any).memory.usedJSHeapSize / 1024 / 1024
      if (memoryUsage > this.config.maxMemoryUsage) {
        suggestions.push({
          type: 'memory',
          severity: memoryUsage > this.config.maxMemoryUsage * 1.5 ? 'high' : 'medium',
          title: '优化内存使用',
          description: `内存使用 ${memoryUsage.toFixed(2)}MB 超过阈值 ${this.config.maxMemoryUsage}MB`,
          action: '清理缓存和优化数据结构',
          impact: '可减少 20-30% 的内存使用',
          implementation: async () => {
            // 清理缓存
            this.handlerCache.clear()
            this.messageBatch = []
            
            // 触发垃圾回收（如果可用）
            if (typeof gc !== 'undefined') {
              gc()
            }
          }
        })
      }
    }
    
    // 吞吐量优化
    const recentThroughput = metrics.recentPerformance.last1Minute.throughput
    if (recentThroughput < 10 && metrics.totalMessages > 100) {
      suggestions.push({
        type: 'throughput',
        severity: 'medium',
        title: '提升消息吞吐量',
        description: `最近1分钟吞吐量 ${recentThroughput.toFixed(2)} 消息/秒较低`,
        action: '启用消息批处理和优先级队列',
        impact: '可提升 2-3倍 的吞吐量',
        implementation: async () => {
          this.config.enableMessageBatching = true
          this.config.enablePriorityQueue = true
        }
      })
    }
    
    return suggestions
  }
  
  /**
   * 获取优化报告
   */
  getOptimizationReport(): string {
    const suggestions = this.generateOptimizationSuggestions()
    const metrics = globalPerformanceMonitor.getMetrics()
    
    let report = '⚡ 性能优化报告\n'
    report += '='.repeat(50) + '\n\n'
    
    // 当前状态
    report += '📊 当前性能状态:\n'
    report += `  平均处理时间: ${metrics.averageProcessingTime.toFixed(2)}ms (阈值: ${this.config.maxAverageProcessingTime}ms)\n`
    report += `  错误率: ${((metrics.failedMessages / metrics.totalMessages) * 100).toFixed(2)}% (阈值: ${(this.config.maxErrorRate * 100).toFixed(2)}%)\n`
    report += `  最近1分钟吞吐量: ${metrics.recentPerformance.last1Minute.throughput.toFixed(2)} 消息/秒\n`
    
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memoryUsage = (performance as any).memory.usedJSHeapSize / 1024 / 1024
      report += `  内存使用: ${memoryUsage.toFixed(2)}MB (阈值: ${this.config.maxMemoryUsage}MB)\n`
    }
    
    report += '\n'
    
    // 优化建议
    if (suggestions.length === 0) {
      report += '✅ 系统性能良好，无需优化\n'
    } else {
      report += `🔧 优化建议 (${suggestions.length}个):\n\n`
      
      const groupedSuggestions = suggestions.reduce((groups, suggestion) => {
        const key = suggestion.severity
        if (!groups[key]) groups[key] = []
        groups[key].push(suggestion)
        return groups
      }, {} as Record<string, OptimizationSuggestion[]>)
      
      const severityOrder = ['critical', 'high', 'medium', 'low'] as const
      
      for (const severity of severityOrder) {
        const group = groupedSuggestions[severity]
        if (group && group.length > 0) {
          const icon = severity === 'critical' ? '🚨' : severity === 'high' ? '⚠️' : severity === 'medium' ? '💡' : '📝'
          report += `${icon} ${severity.toUpperCase()} 优先级:\n`
          
          for (const suggestion of group) {
            report += `  • ${suggestion.title}\n`
            report += `    ${suggestion.description}\n`
            report += `    建议: ${suggestion.action}\n`
            report += `    预期效果: ${suggestion.impact}\n\n`
          }
        }
      }
    }
    
    return report
  }
  
  /**
   * 获取配置
   */
  getConfig(): OptimizationConfig {
    return { ...this.config }
  }
  
  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig }
    console.log('🔧 性能优化器配置已更新:', newConfig)
  }
}

// 全局性能优化器实例
export const globalPerformanceOptimizer = new PerformanceOptimizer()

// 在开发环境下暴露性能优化器
if (import.meta.env.DEV) {
  (window as any).performanceOptimizer = globalPerformanceOptimizer
  
  console.log('💡 性能优化器已暴露到window.performanceOptimizer')
  console.log('  - window.performanceOptimizer.generateOptimizationSuggestions()')
  console.log('  - window.performanceOptimizer.getOptimizationReport()')
  console.log('  - window.performanceOptimizer.getConfig()')
  console.log('  - window.performanceOptimizer.updateConfig(config)')
}
