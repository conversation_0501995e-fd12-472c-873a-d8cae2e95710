/**
 * WebSocket消息处理器统一导出
 * @description 提供所有消息处理器的统一入口
 */

// 基础结构
export * from './base'

// 具体处理器
export { CommonMessageHandler } from './CommonMessageHandler'
export { AdminMessageHandler, AdminMessageType } from './AdminMessageHandler'
export { MerchantMessageHandler, MerchantMessageType } from './MerchantMessageHandler'
export { UserMessageHandler, UserMessageType } from './UserMessageHandler'

// 工具函数和工厂
import { MessageRouter } from './base/MessageRouter'
import { CommonMessageHandler } from './CommonMessageHandler'
import { AdminMessageHandler } from './AdminMessageHandler'
import { MerchantMessageHandler } from './MerchantMessageHandler'
import { UserMessageHandler } from './UserMessageHandler'
import type { MessageHandlerConfig } from './base/MessageHandler'

/**
 * 创建消息处理器工厂函数
 */
export const createMessageHandlers = (config: {
  userType: 'admin' | 'merchant' | 'user'
  handlerConfig?: MessageHandlerConfig
}) => {
  const { userType, handlerConfig = {} } = config
  const router = new MessageRouter()
  
  // 公共消息处理器（所有用户类型都需要）
  const commonHandler = new CommonMessageHandler(handlerConfig)
  router.registerHandler(commonHandler)
  
  // 根据用户类型添加特定处理器
  let specificHandler
  switch (userType) {
    case 'admin':
      specificHandler = new AdminMessageHandler(handlerConfig)
      break
    case 'merchant':
      specificHandler = new MerchantMessageHandler(handlerConfig)
      break
    case 'user':
      specificHandler = new UserMessageHandler(handlerConfig)
      break
    default:
      throw new Error(`Unsupported user type: ${userType}`)
  }
  
  router.registerHandler(specificHandler)
  
  return {
    router,
    commonHandler,
    specificHandler,
    handlers: [commonHandler, specificHandler]
  }
}

/**
 * 初始化消息处理器
 */
export const initializeMessageHandlers = async (config: {
  userType: 'admin' | 'merchant' | 'user'
  handlerConfig?: MessageHandlerConfig
}) => {
  const { router, handlers } = createMessageHandlers(config)
  
  // 初始化所有处理器
  await Promise.all(handlers.map(handler => handler.initialize()))
  
  return router
}

/**
 * 消息处理器管理器
 */
export class MessageHandlerManager {
  private router: MessageRouter | null = null
  private handlers: any[] = []
  private initialized = false

  /**
   * 初始化管理器
   */
  async initialize(userType: 'admin' | 'merchant' | 'user', config?: MessageHandlerConfig): Promise<void> {
    if (this.initialized) {
      throw new Error('MessageHandlerManager already initialized')
    }

    const result = createMessageHandlers({ userType, handlerConfig: config })
    this.router = result.router
    this.handlers = result.handlers

    // 初始化所有处理器
    await Promise.all(this.handlers.map(handler => handler.initialize()))
    
    this.initialized = true
  }

  /**
   * 获取路由器
   */
  getRouter(): MessageRouter {
    if (!this.router) {
      throw new Error('MessageHandlerManager not initialized')
    }
    return this.router
  }

  /**
   * 获取所有处理器
   */
  getHandlers() {
    return this.handlers
  }

  /**
   * 获取管理器状态
   */
  getStatus() {
    return {
      initialized: this.initialized,
      handlerCount: this.handlers.length,
      routerStats: this.router?.getStats()
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    if (this.router) {
      this.router.destroy()
      this.router = null
    }
    
    this.handlers = []
    this.initialized = false
  }
}

/**
 * 全局消息处理器管理器实例
 */
export const messageHandlerManager = new MessageHandlerManager()

/**
 * 便捷的初始化函数
 */
export const setupMessageHandlers = async (
  userType: 'admin' | 'merchant' | 'user',
  config?: MessageHandlerConfig
): Promise<MessageRouter> => {
  await messageHandlerManager.initialize(userType, config)
  return messageHandlerManager.getRouter()
}

/**
 * 获取当前路由器
 */
export const getMessageRouter = (): MessageRouter => {
  return messageHandlerManager.getRouter()
}

/**
 * 清理消息处理器
 */
export const cleanupMessageHandlers = (): void => {
  messageHandlerManager.destroy()
}
