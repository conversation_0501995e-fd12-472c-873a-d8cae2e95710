/**
 * 公共消息处理器
 * @description 处理管理员、商家、用户三个角色共同的WebSocket消息类型
 */

import { BaseMessageHandler } from './base/BaseMessageHandler'
import type { MessageHandleResult, MessageHandlerConfig } from './base/MessageHandler'
import type {
  WebSocketMessage,
  HeartbeatMessage,
  UserStatusMessage,
  NotificationMessage
} from '../types/websocket'
import { WebSocketMessageType } from '../types/websocket'
import { useChatStore } from '../stores/chat'

/**
 * 公共消息处理器实现
 */
export class CommonMessageHandler extends BaseMessageHandler {
  private chatStore: any

  constructor(config: MessageHandlerConfig = {}) {
    super('Common', 'common', config)
  }

  /**
   * 初始化处理器
   */
  protected async onInitialize(): Promise<void> {
    this.chatStore = useChatStore()
    this.debug('CommonMessageHandler initialized')
  }

  /**
   * 获取支持的消息类型列表
   */
  getSupportedMessageTypes(): string[] {
    return [
      // 心跳相关
      WebSocketMessageType.PING,
      WebSocketMessageType.PONG,
      WebSocketMessageType.HEARTBEAT,
      
      // 认证相关
      WebSocketMessageType.AUTH_SUCCESS,
      WebSocketMessageType.AUTH_FAILED,
      
      // 基础用户状态
      WebSocketMessageType.USER_ONLINE,
      WebSocketMessageType.USER_OFFLINE,
      WebSocketMessageType.USER_STATUS_CHANGE,
      
      // 系统通知
      WebSocketMessageType.SYSTEM_MESSAGE,
      WebSocketMessageType.NOTIFICATION,
      
      // 会话基础操作
      WebSocketMessageType.SESSION_CREATED,
      WebSocketMessageType.SESSION_UPDATED,
      WebSocketMessageType.SESSION_CLOSED,
      
      // 会话成员在线状态
      'session_members_online',
      
      // 聊天消息
      WebSocketMessageType.MESSAGE,
      WebSocketMessageType.MESSAGE_ACK,
      WebSocketMessageType.MESSAGE_DELIVERED,
      WebSocketMessageType.MESSAGE_READ,
      
      // 输入状态
      WebSocketMessageType.TYPING_START,
      WebSocketMessageType.TYPING_STOP,
      
      // 错误和限流
      WebSocketMessageType.ERROR,
      WebSocketMessageType.RATE_LIMIT
    ]
  }

  /**
   * 处理具体的消息逻辑
   */
  protected async processMessage(message: WebSocketMessage): Promise<MessageHandleResult> {
    this.debug(`Processing common message type: ${message.type}`, message)

    try {
      switch (message.type) {
        // 心跳消息
        case WebSocketMessageType.PING:
        case WebSocketMessageType.PONG:
        case WebSocketMessageType.HEARTBEAT:
          return await this.handleHeartbeatMessage(message as HeartbeatMessage)

        // 认证消息
        case WebSocketMessageType.AUTH_SUCCESS:
          return await this.handleAuthSuccess(message)
        case WebSocketMessageType.AUTH_FAILED:
          return await this.handleAuthFailed(message)

        // 用户状态消息
        case WebSocketMessageType.USER_ONLINE:
        case WebSocketMessageType.USER_OFFLINE:
        case WebSocketMessageType.USER_STATUS_CHANGE:
          return await this.handleUserStatusMessage(message as UserStatusMessage)

        // 系统通知
        case WebSocketMessageType.SYSTEM_MESSAGE:
        case WebSocketMessageType.NOTIFICATION:
          return await this.handleNotificationMessage(message as NotificationMessage)

        // 会话消息
        case WebSocketMessageType.SESSION_CREATED:
          return await this.handleSessionCreated(message)
        case WebSocketMessageType.SESSION_UPDATED:
          return await this.handleSessionUpdated(message)
        case WebSocketMessageType.SESSION_CLOSED:
          return await this.handleSessionClosed(message)

        // 聊天消息
        case WebSocketMessageType.MESSAGE:
          return await this.handleChatMessage(message)
        case WebSocketMessageType.MESSAGE_ACK:
        case WebSocketMessageType.MESSAGE_DELIVERED:
        case WebSocketMessageType.MESSAGE_READ:
          return await this.handleMessageStatus(message)

        // 输入状态
        case WebSocketMessageType.TYPING_START:
        case WebSocketMessageType.TYPING_STOP:
          return await this.handleTypingStatus(message)

        // 错误处理
        case WebSocketMessageType.ERROR:
          return await this.handleError(message)
        case WebSocketMessageType.RATE_LIMIT:
          return await this.handleRateLimit(message)

        default:
          return this.createErrorResult(`Unsupported message type: ${message.type}`)
      }
    } catch (error) {
      this.logger.error(`Error processing message type ${message.type}:`, error)
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Unknown error',
        { messageType: message.type }
      )
    }
  }

  /**
   * 处理心跳消息
   */
  private async handleHeartbeatMessage(message: HeartbeatMessage): Promise<MessageHandleResult> {
    this.debug('Processing heartbeat message', { type: message.type })
    
    // 心跳消息通常由WebSocketManager处理，这里只做记录
    return this.createSuccessResult({
      type: 'heartbeat',
      processed: true
    })
  }

  /**
   * 处理认证成功
   */
  private async handleAuthSuccess(message: WebSocketMessage): Promise<MessageHandleResult> {
    this.debug('Authentication successful', message.data)
    
    // 更新连接状态
    if (this.chatStore) {
      this.chatStore.clientStatus = 'connected'
      this.chatStore.isConnected = true
    }
    
    return this.createSuccessResult({
      type: 'auth_success',
      authenticated: true
    })
  }

  /**
   * 处理认证失败
   */
  private async handleAuthFailed(message: WebSocketMessage): Promise<MessageHandleResult> {
    this.logger.warn('Authentication failed', message.data)
    
    // 更新连接状态
    if (this.chatStore) {
      this.chatStore.clientStatus = 'disconnected'
      this.chatStore.isConnected = false
      this.chatStore.error = message.data?.error_message || 'Authentication failed'
    }
    
    return this.createSuccessResult({
      type: 'auth_failed',
      error: message.data?.error_message
    })
  }

  /**
   * 处理用户状态消息
   */
  private async handleUserStatusMessage(message: UserStatusMessage): Promise<MessageHandleResult> {
    const { user_id, user_type, status } = message.data
    
    this.debug('User status changed', { user_id, user_type, status })
    
    if (this.chatStore) {
      this.chatStore.updateUserStatus(user_id.toString(), status as any)
    }
    
    return this.createSuccessResult({
      type: 'user_status',
      user_id,
      user_type,
      status
    })
  }

  /**
   * 处理通知消息
   */
  private async handleNotificationMessage(message: NotificationMessage): Promise<MessageHandleResult> {
    this.debug('Processing notification', message.data)

    if (this.chatStore) {
      // 创建通知对象
      const notification = {
        id: Date.now(),
        title: message.data.title,
        content: message.data.content,
        category: message.data.category,
        status: 'PENDING' as any,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      this.chatStore.addNotification(notification)
    }

    return this.createSuccessResult({
      type: 'notification',
      title: message.data.title,
      category: message.data.category
    })
  }





  /**
   * 处理会话创建
   */
  private async handleSessionCreated(message: WebSocketMessage): Promise<MessageHandleResult> {
    this.debug('Session created', message.data)
    
    if (this.chatStore && message.data?.session) {
      this.chatStore.addSession(message.data.session)
    }
    
    return this.createSuccessResult({
      type: 'session_created',
      session_id: message.data?.session?.id
    })
  }

  /**
   * 处理会话更新
   */
  private async handleSessionUpdated(message: WebSocketMessage): Promise<MessageHandleResult> {
    this.debug('Session updated', message.data)
    
    if (this.chatStore && message.data?.session) {
      this.chatStore.updateSession(message.data.session)
    }
    
    return this.createSuccessResult({
      type: 'session_updated',
      session_id: message.data?.session?.id
    })
  }

  /**
   * 处理会话关闭
   */
  private async handleSessionClosed(message: WebSocketMessage): Promise<MessageHandleResult> {
    this.debug('Session closed', message.data)
    
    // 这里可以添加会话关闭的处理逻辑
    
    return this.createSuccessResult({
      type: 'session_closed',
      session_id: message.data?.session?.id
    })
  }



  /**
   * 处理聊天消息
   */
  private async handleChatMessage(message: WebSocketMessage): Promise<MessageHandleResult> {
    this.debug('Processing chat message', {
      hasData: !!message.data,
      messageId: message.data?.id || message.data?.message?.id
    })

    if (this.chatStore) {
      // 兼容不同的消息格式
      let chatMessage = null
      if (message.data?.message) {
        chatMessage = message.data.message
      } else if (message.data?.id) {
        chatMessage = message.data
      }

      if (chatMessage) {
        this.chatStore.addMessage(chatMessage)

        // 🔧 检查是否需要显示通知
        await this.checkAndShowMessageNotification(chatMessage)
      }
    }

    return this.createSuccessResult({
      type: 'chat_message',
      message_id: message.data?.id || message.data?.message?.id
    })
  }

  /**
   * 检查并显示消息通知
   */
  private async checkAndShowMessageNotification(message: any): Promise<void> {
    if (!this.chatStore || !message) {
      return
    }

    const sessionId = message.session_id?.toString()
    if (!sessionId) {
      return
    }

    // 检查聊天UI是否对该会话可见
    const isChatVisible = this.chatStore.isChatUIVisibleForSession(sessionId)

    this.debug('Checking message notification', {
      sessionId,
      isChatVisible,
      messageId: message.id,
      senderType: message.sender_type
    })

    // 如果聊天UI未打开或不是当前会话，显示通知
    if (!isChatVisible) {
      await this.showMessageNotification(message)
    }
  }

  /**
   * 显示消息通知
   */
  private async showMessageNotification(message: any): Promise<void> {
    try {
      const { ElNotification } = await import('element-plus')

      // 构建通知内容
      let notificationTitle = '新消息'
      let notificationMessage = ''

      // 根据发送者类型设置标题
      if (message.sender_type === 'merchant') {
        notificationTitle = '商家消息'
      } else if (message.sender_type === 'admin') {
        notificationTitle = '客服消息'
      } else if (message.sender_type === 'user') {
        notificationTitle = '用户消息'
      }

      // 处理消息内容
      if (typeof message.content === 'string') {
        if (message.type === 'image' || message.content.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
          notificationMessage = '发送了一张图片'
        } else if (message.type === 'file') {
          notificationMessage = '发送了一个文件'
        } else {
          notificationMessage = message.content.length > 50
            ? message.content.substring(0, 50) + '...'
            : message.content
        }
      } else {
        notificationMessage = '发送了一条消息'
      }

      // 显示通知
      ElNotification({
        title: notificationTitle,
        message: notificationMessage,
        type: 'info',
        duration: 5000,
        showClose: true,
        onClick: () => {
          // 点击通知时打开聊天UI并跳转到对应会话
          this.handleNotificationClick(message)
        }
      })

      this.debug('Message notification shown', {
        messageId: message.id,
        sessionId: message.session_id,
        title: notificationTitle,
        content: notificationMessage
      })

    } catch (error) {
      this.logger.error('Failed to show message notification:', error)
    }
  }

  /**
   * 处理通知点击事件
   */
  private handleNotificationClick(message: any): void {
    try {
      const sessionId = message.session_id?.toString()
      if (!sessionId) {
        return
      }

      // 设置当前会话ID
      if (this.chatStore) {
        this.chatStore.currentSessionId = sessionId
        this.chatStore.setChatUIVisible(true, sessionId)
      }

      // 触发全局事件，让Layout组件监听并打开聊天UI
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('openChatUI', {
          detail: { sessionId, message }
        }))
      }

      this.debug('Notification clicked, opening chat UI', {
        sessionId,
        messageId: message.id
      })

    } catch (error) {
      this.logger.error('Failed to handle notification click:', error)
    }
  }

  /**
   * 处理消息状态更新
   */
  private async handleMessageStatus(message: WebSocketMessage): Promise<MessageHandleResult> {
    this.debug('Message status update', message.data)
    
    if (this.chatStore && message.data?.message_id) {
      const status = message.type === WebSocketMessageType.MESSAGE_ACK ? 'sent' :
                    message.type === WebSocketMessageType.MESSAGE_DELIVERED ? 'delivered' : 'read'
      
      this.chatStore.updateMessageStatus(message.data.message_id.toString(), status as any)
    }
    
    return this.createSuccessResult({
      type: 'message_status',
      message_id: message.data?.message_id,
      status: message.type
    })
  }

  /**
   * 处理输入状态
   */
  private async handleTypingStatus(message: WebSocketMessage): Promise<MessageHandleResult> {
    this.debug('Typing status', message.data)
    
    if (this.chatStore && message.data) {
      const isTyping = message.type === WebSocketMessageType.TYPING_START
      this.chatStore.updateTypingStatus(
        message.data.session_id?.toString(),
        message.data.user_id?.toString(),
        isTyping
      )
    }
    
    return this.createSuccessResult({
      type: 'typing_status',
      session_id: message.data?.session_id,
      user_id: message.data?.user_id,
      is_typing: message.type === WebSocketMessageType.TYPING_START
    })
  }

  /**
   * 处理错误消息
   */
  private async handleError(message: WebSocketMessage): Promise<MessageHandleResult> {
    this.logger.error('WebSocket error message received', message.data)
    
    if (this.chatStore) {
      this.chatStore.error = message.data?.error_message || 'WebSocket error'
    }
    
    return this.createSuccessResult({
      type: 'error',
      error_code: message.data?.error_code,
      error_message: message.data?.error_message
    })
  }

  /**
   * 处理限流消息
   */
  private async handleRateLimit(message: WebSocketMessage): Promise<MessageHandleResult> {
    this.logger.warn('Rate limit exceeded', message.data)
    
    return this.createSuccessResult({
      type: 'rate_limit',
      limit_type: message.data?.limit_type,
      retry_after: message.data?.retry_after
    })
  }
}
