/**
 * WebSocket消息处理器基础实现类
 * @description 提供消息处理器的通用功能实现
 */

import { EventEmitter } from '../../utils/event-emitter'
import { Logger } from '../../utils/logger'
import type { 
  MessageHandler, 
  MessageHandleResult, 
  MessageHandlerConfig,
  MessageHandlerEvents
} from './MessageHandler'
import type { WebSocketMessage } from '../../types/websocket'

/**
 * 消息处理器基础实现类
 */
export abstract class BaseMessageHandler extends EventEmitter<MessageHandlerEvents> implements MessageHandler {
  protected logger: Logger
  protected config: MessageHandlerConfig
  protected initialized = false
  protected messageCount = 0
  protected errorCount = 0
  protected lastProcessedAt?: number

  constructor(
    public readonly name: string,
    public readonly type: 'admin' | 'merchant' | 'user' | 'common',
    config: MessageHandlerConfig = {}
  ) {
    super()
    this.config = {
      debug: false,
      timeout: 5000,
      maxRetries: 3,
      errorStrategy: 'retry',
      ...config
    }
    this.logger = new Logger(`${name}MessageHandler`)
  }

  /**
   * 初始化处理器
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      this.logger.warn('Handler already initialized')
      return
    }

    try {
      await this.onInitialize()
      this.initialized = true
      this.logger.info('Handler initialized successfully')
      this.emit('handler:initialized', this)
    } catch (error) {
      this.logger.error('Failed to initialize handler:', error)
      throw error
    }
  }

  /**
   * 销毁处理器
   */
  destroy(): void {
    if (!this.initialized) {
      return
    }

    try {
      this.onDestroy()
      this.initialized = false
      this.removeAllListeners()
      this.logger.info('Handler destroyed')
      this.emit('handler:destroyed', this)
    } catch (error) {
      this.logger.error('Error during handler destruction:', error)
    }
  }

  /**
   * 处理WebSocket消息
   */
  async handleMessage(message: WebSocketMessage): Promise<MessageHandleResult> {
    if (!this.initialized) {
      throw new Error('Handler not initialized')
    }

    if (!this.canHandle(message.type)) {
      return {
        success: false,
        error: `Handler ${this.name} cannot handle message type: ${message.type}`
      }
    }

    this.emit('message:start', message)
    this.messageCount++

    const startTime = Date.now()
    let retries = 0

    while (retries <= this.config.maxRetries!) {
      try {
        const result = await this.processMessage(message)
        this.lastProcessedAt = Date.now()
        
        if (this.config.debug) {
          this.logger.debug(`Message processed in ${Date.now() - startTime}ms:`, {
            type: message.type,
            success: result.success,
            retries
          })
        }

        this.emit('message:success', message, result)
        return result
      } catch (error) {
        retries++
        this.errorCount++
        
        this.logger.error(`Message processing failed (attempt ${retries}):`, error)
        
        if (retries > this.config.maxRetries!) {
          const errorResult: MessageHandleResult = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
          
          this.emit('message:error', message, error as Error)
          
          if (this.config.errorStrategy === 'throw') {
            throw error
          }
          
          return errorResult
        }

        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * retries))
      }
    }

    // 这里不应该到达，但为了类型安全
    return {
      success: false,
      error: 'Unexpected error in message processing'
    }
  }

  /**
   * 获取处理器状态
   */
  getStatus() {
    return {
      initialized: this.initialized,
      messageCount: this.messageCount,
      errorCount: this.errorCount,
      lastProcessedAt: this.lastProcessedAt
    }
  }

  /**
   * 检查是否可以处理指定类型的消息
   */
  canHandle(messageType: string): boolean {
    return this.getSupportedMessageTypes().includes(messageType)
  }

  // 抽象方法，子类必须实现

  /**
   * 获取支持的消息类型列表
   */
  abstract getSupportedMessageTypes(): string[]

  /**
   * 处理具体的消息逻辑
   */
  protected abstract processMessage(message: WebSocketMessage): Promise<MessageHandleResult>

  /**
   * 初始化时调用的钩子方法
   */
  protected async onInitialize(): Promise<void> {
    // 子类可以重写此方法进行自定义初始化
  }

  /**
   * 销毁时调用的钩子方法
   */
  protected onDestroy(): void {
    // 子类可以重写此方法进行自定义清理
  }

  /**
   * 记录调试信息
   */
  protected debug(message: string, data?: any): void {
    if (this.config.debug) {
      this.logger.debug(message, data)
    }
  }

  /**
   * 创建成功结果
   */
  protected createSuccessResult(data?: any): MessageHandleResult {
    return {
      success: true,
      data
    }
  }

  /**
   * 创建错误结果
   */
  protected createErrorResult(error: string, data?: any): MessageHandleResult {
    return {
      success: false,
      error,
      data
    }
  }
}
