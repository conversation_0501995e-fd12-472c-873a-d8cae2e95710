/**
 * WebSocket消息处理器基础接口
 * @description 定义所有消息处理器必须实现的接口
 */

import type { WebSocketMessage } from '../../types/websocket'

/**
 * 消息处理结果
 */
export interface MessageHandleResult {
  success: boolean
  error?: string
  data?: any
}

/**
 * 消息处理器接口
 */
export interface MessageHandler {
  /**
   * 处理器名称
   */
  readonly name: string

  /**
   * 处理器类型（admin, merchant, user, common）
   */
  readonly type: 'admin' | 'merchant' | 'user' | 'common'

  /**
   * 初始化处理器
   */
  initialize(): Promise<void>

  /**
   * 销毁处理器，清理资源
   */
  destroy(): void

  /**
   * 检查是否可以处理指定类型的消息
   * @param messageType 消息类型
   */
  canHandle(messageType: string): boolean

  /**
   * 处理WebSocket消息
   * @param message WebSocket消息
   */
  handleMessage(message: WebSocketMessage): Promise<MessageHandleResult>

  /**
   * 获取处理器支持的消息类型列表
   */
  getSupportedMessageTypes(): string[]

  /**
   * 获取处理器状态
   */
  getStatus(): {
    initialized: boolean
    messageCount: number
    errorCount: number
    lastProcessedAt?: number
  }
}

/**
 * 消息路由器接口
 */
export interface MessageRouter {
  /**
   * 注册消息处理器
   * @param handler 消息处理器
   */
  registerHandler(handler: MessageHandler): void

  /**
   * 取消注册消息处理器
   * @param handlerName 处理器名称
   */
  unregisterHandler(handlerName: string): void

  /**
   * 路由消息到对应的处理器
   * @param message WebSocket消息
   */
  routeMessage(message: WebSocketMessage): Promise<MessageHandleResult[]>

  /**
   * 获取所有注册的处理器
   */
  getHandlers(): MessageHandler[]

  /**
   * 获取路由统计信息
   */
  getStats(): {
    totalMessages: number
    successCount: number
    errorCount: number
    handlerCount: number
  }
}

/**
 * 消息处理器配置
 */
export interface MessageHandlerConfig {
  /**
   * 是否启用调试模式
   */
  debug?: boolean

  /**
   * 消息处理超时时间（毫秒）
   */
  timeout?: number

  /**
   * 最大重试次数
   */
  maxRetries?: number

  /**
   * 错误处理策略
   */
  errorStrategy?: 'ignore' | 'retry' | 'throw'

  /**
   * 自定义配置
   */
  custom?: Record<string, any>
}

/**
 * 消息处理器事件
 */
export interface MessageHandlerEvents {
  /**
   * 消息处理开始
   */
  'message:start': (message: WebSocketMessage) => void

  /**
   * 消息处理成功
   */
  'message:success': (message: WebSocketMessage, result: MessageHandleResult) => void

  /**
   * 消息处理失败
   */
  'message:error': (message: WebSocketMessage, error: Error) => void

  /**
   * 处理器初始化完成
   */
  'handler:initialized': (handler: MessageHandler) => void

  /**
   * 处理器销毁
   */
  'handler:destroyed': (handler: MessageHandler) => void
}
