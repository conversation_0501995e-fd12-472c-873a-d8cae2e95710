/**
 * WebSocket消息路由器
 * @description 负责将WebSocket消息路由到对应的处理器
 */

import { EventEmitter } from '../../utils/event-emitter'
import { Logger } from '../../utils/logger'
import type {
  MessageRouter as IMessageRouter,
  MessageHandler,
  MessageHandleResult
} from './MessageHandler'
import type { WebSocketMessage } from '../../types/websocket'
import { globalPerformanceMonitor } from '../performance-monitor'

/**
 * 消息路由器实现
 */
export class MessageRouter extends EventEmitter implements IMessageRouter {
  private handlers = new Map<string, MessageHandler>()
  private logger: Logger
  private stats = {
    totalMessages: 0,
    successCount: 0,
    errorCount: 0
  }

  constructor() {
    super()
    this.logger = new Logger('MessageRouter')
  }

  /**
   * 注册消息处理器
   */
  registerHandler(handler: MessageHandler): void {
    if (this.handlers.has(handler.name)) {
      this.logger.warn(`Handler ${handler.name} already registered, replacing...`)
    }

    this.handlers.set(handler.name, handler)
    this.logger.info(`Registered handler: ${handler.name} (type: ${handler.type})`)
    
    // 记录处理器支持的消息类型
    const supportedTypes = handler.getSupportedMessageTypes()
    this.logger.debug(`Handler ${handler.name} supports message types:`, supportedTypes)
  }

  /**
   * 取消注册消息处理器
   */
  unregisterHandler(handlerName: string): void {
    const handler = this.handlers.get(handlerName)
    if (handler) {
      handler.destroy()
      this.handlers.delete(handlerName)
      this.logger.info(`Unregistered handler: ${handlerName}`)
    } else {
      this.logger.warn(`Handler ${handlerName} not found for unregistration`)
    }
  }

  /**
   * 路由消息到对应的处理器
   */
  async routeMessage(message: WebSocketMessage): Promise<MessageHandleResult[]> {
    const startTime = Date.now()
    this.stats.totalMessages++

    const messageType = message.type
    this.logger.debug(`Routing message type: ${messageType}`)

    // 查找能处理此消息的处理器
    const capableHandlers = Array.from(this.handlers.values())
      .filter(handler => handler.canHandle(messageType))

    if (capableHandlers.length === 0) {
      this.logger.warn(`No handler found for message type: ${messageType}`)
      this.stats.errorCount++
      const result = [{
        success: false,
        error: `No handler available for message type: ${messageType}`
      }]

      // 记录性能监控数据
      const processingTime = Date.now() - startTime
      globalPerformanceMonitor.recordMessageProcessing(message, result, processingTime)

      return result
    }

    this.logger.debug(`Found ${capableHandlers.length} handler(s) for message type: ${messageType}`)

    // 并行处理消息
    const results = await Promise.allSettled(
      capableHandlers.map(handler => this.processWithHandler(handler, message))
    )

    // 处理结果
    const handleResults: MessageHandleResult[] = []
    let hasSuccess = false

    for (let i = 0; i < results.length; i++) {
      const result = results[i]
      const handler = capableHandlers[i]

      if (result.status === 'fulfilled') {
        handleResults.push(result.value)
        if (result.value.success) {
          hasSuccess = true
        }
      } else {
        this.logger.error(`Handler ${handler.name} threw an error:`, result.reason)
        handleResults.push({
          success: false,
          error: `Handler ${handler.name} error: ${result.reason.message || 'Unknown error'}`
        })
      }
    }

    // 更新统计
    if (hasSuccess) {
      this.stats.successCount++
    } else {
      this.stats.errorCount++
    }

    // 记录性能监控数据
    const processingTime = Date.now() - startTime
    globalPerformanceMonitor.recordMessageProcessing(message, handleResults, processingTime)

    return handleResults
  }

  /**
   * 使用指定处理器处理消息
   */
  private async processWithHandler(
    handler: MessageHandler, 
    message: WebSocketMessage
  ): Promise<MessageHandleResult> {
    const startTime = Date.now()
    
    try {
      const result = await handler.handleMessage(message)
      const duration = Date.now() - startTime
      
      this.logger.debug(`Handler ${handler.name} processed message in ${duration}ms:`, {
        type: message.type,
        success: result.success
      })
      
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      this.logger.error(`Handler ${handler.name} failed after ${duration}ms:`, error)
      throw error
    }
  }

  /**
   * 获取所有注册的处理器
   */
  getHandlers(): MessageHandler[] {
    return Array.from(this.handlers.values())
  }

  /**
   * 获取路由统计信息
   */
  getStats() {
    return {
      ...this.stats,
      handlerCount: this.handlers.size
    }
  }

  /**
   * 获取处理器信息
   */
  getHandlerInfo(): Array<{
    name: string
    type: string
    supportedTypes: string[]
    status: any
  }> {
    return Array.from(this.handlers.values()).map(handler => ({
      name: handler.name,
      type: handler.type,
      supportedTypes: handler.getSupportedMessageTypes(),
      status: handler.getStatus()
    }))
  }

  /**
   * 清理所有处理器
   */
  destroy(): void {
    this.logger.info('Destroying message router...')
    
    for (const handler of this.handlers.values()) {
      try {
        handler.destroy()
      } catch (error) {
        this.logger.error(`Error destroying handler ${handler.name}:`, error)
      }
    }
    
    this.handlers.clear()
    this.removeAllListeners()
    this.logger.info('Message router destroyed')
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalMessages: 0,
      successCount: 0,
      errorCount: 0
    }
    this.logger.info('Router statistics reset')
  }
}
