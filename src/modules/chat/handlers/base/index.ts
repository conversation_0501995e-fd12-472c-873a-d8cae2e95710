/**
 * 消息处理器基础结构统一导出
 */

// 接口和类型
export type {
  MessageHandler,
  MessageRouter as IMessageRouter,
  MessageHandleResult,
  MessageHandlerConfig,
  MessageHandlerEvents
} from './MessageHandler'

// 基础实现类
export { BaseMessageHandler } from './BaseMessageHandler'
export { MessageRouter } from './MessageRouter'

// 工具函数
export const createMessageRouter = () => new MessageRouter()

/**
 * 消息处理器工厂函数
 */
export interface MessageHandlerFactory<T extends MessageHandler = MessageHandler> {
  create(config?: MessageHandlerConfig): T
  getType(): string
  getName(): string
}

/**
 * 消息处理器注册表
 */
export class MessageHandlerRegistry {
  private static instance: MessageHandlerRegistry
  private factories = new Map<string, MessageHandlerFactory>()

  static getInstance(): MessageHandlerRegistry {
    if (!MessageHandlerRegistry.instance) {
      MessageHandlerRegistry.instance = new MessageHandlerRegistry()
    }
    return MessageHandlerRegistry.instance
  }

  /**
   * 注册处理器工厂
   */
  registerFactory(name: string, factory: MessageHandlerFactory): void {
    this.factories.set(name, factory)
  }

  /**
   * 创建处理器实例
   */
  createHandler(name: string, config?: MessageHandlerConfig): MessageHandler {
    const factory = this.factories.get(name)
    if (!factory) {
      throw new Error(`Handler factory not found: ${name}`)
    }
    return factory.create(config)
  }

  /**
   * 获取所有注册的工厂名称
   */
  getRegisteredNames(): string[] {
    return Array.from(this.factories.keys())
  }

  /**
   * 检查工厂是否已注册
   */
  hasFactory(name: string): boolean {
    return this.factories.has(name)
  }
}
