/**
 * 管理员消息处理器
 * @description 处理管理员特有的WebSocket消息类型
 */

import { BaseMessageHandler } from './base/BaseMessageHandler'
import type { MessageHandleResult, MessageHandlerConfig } from './base/MessageHandler'
import type { WebSocketMessage } from '../types/websocket'
import { useChatStore } from '../stores/chat'
import { getNotificationFilter } from '../config/notificationFilter'

/**
 * 管理员特有的消息类型
 */
export enum AdminMessageType {
  // 系统管理通知
  ADMIN_SYSTEM_ALERT = 'admin_system_alert',
  SYSTEM_MAINTENANCE_NOTICE = 'system_maintenance_notice',
  PLATFORM_STATISTICS_UPDATE = 'platform_statistics_update',
  
  // 用户管理
  USER_VERIFICATION_STATUS = 'user_verification_status',
  USER_ACCOUNT_STATUS_CHANGE = 'user_account_status_change',
  USER_VIOLATION_REPORT = 'user_violation_report',
  
  // 商家管理
  MERCHANT_VERIFICATION_STATUS = 'merchant_verification_status',
  MERCHANT_ACCOUNT_STATUS_CHANGE = 'merchant_account_status_change',
  MERCHANT_VIOLATION_REPORT = 'merchant_violation_report',
  
  // 内容管理
  CONTENT_VIOLATION_REPORT = 'content_violation_report',
  CONTENT_AUDIT_RESULT = 'content_audit_result',
  CONTENT_MODERATION_ALERT = 'content_moderation_alert',
  
  // 订单管理
  ORDER_DISPUTE_ALERT = 'order_dispute_alert',
  ORDER_REFUND_REQUEST = 'order_refund_request',
  ORDER_ABNORMAL_ACTIVITY = 'order_abnormal_activity',
  
  // 财务管理
  FINANCIAL_ALERT = 'financial_alert',
  SETTLEMENT_EXCEPTION = 'settlement_exception',
  PAYMENT_ANOMALY = 'payment_anomaly',
  
  // 安全管理
  SECURITY_ALERT = 'security_alert',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  LOGIN_ANOMALY = 'login_anomaly',
  
  // 系统监控
  SYSTEM_PERFORMANCE_ALERT = 'system_performance_alert',
  DATABASE_ALERT = 'database_alert',
  API_RATE_LIMIT_ALERT = 'api_rate_limit_alert'
}

/**
 * 管理员消息处理器实现
 */
export class AdminMessageHandler extends BaseMessageHandler {
  private adminStore: any
  private chatStore: any

  constructor(config: MessageHandlerConfig = {}) {
    super('Admin', 'admin', config)
  }

  /**
   * 初始化处理器
   */
  protected async onInitialize(): Promise<void> {
    // 初始化chatStore
    this.chatStore = useChatStore()

    // 动态导入管理员store
    try {
      const { useAdminStore } = await import('@/modules/admin/stores/adminStore')
      this.adminStore = useAdminStore()
      this.debug('AdminMessageHandler initialized with admin store')
    } catch (error) {
      this.logger.warn('Failed to load admin store, some features may not work:', error)
    }
  }

  /**
   * 获取支持的消息类型列表
   */
  getSupportedMessageTypes(): string[] {
    return [
      // WebSocket基础消息类型
      'notification',
      'message',
      'system_message',
      // 管理员特有的消息类型
      ...Object.values(AdminMessageType)
    ]
  }

  /**
   * 处理具体的消息逻辑
   */
  protected async processMessage(message: WebSocketMessage): Promise<MessageHandleResult> {
    this.debug(`Processing admin message type: ${message.type}`, message)

    try {
      // 首先检查是否是notification类型的消息
      if (message.type === 'notification') {
        return await this.handleAdminNotificationMessage(message as any)
      }

      switch (message.type as unknown as AdminMessageType) {
        // 系统管理通知
        case AdminMessageType.ADMIN_SYSTEM_ALERT:
          return await this.handleSystemAlert(message)
        case AdminMessageType.SYSTEM_MAINTENANCE_NOTICE:
          return await this.handleMaintenanceNotice(message)
        case AdminMessageType.PLATFORM_STATISTICS_UPDATE:
          return await this.handleStatisticsUpdate(message)

        // 用户管理
        case AdminMessageType.USER_VERIFICATION_STATUS:
          return await this.handleUserVerificationStatus(message)
        case AdminMessageType.USER_ACCOUNT_STATUS_CHANGE:
          return await this.handleUserAccountStatusChange(message)
        case AdminMessageType.USER_VIOLATION_REPORT:
          return await this.handleUserViolationReport(message)

        // 商家管理
        case AdminMessageType.MERCHANT_VERIFICATION_STATUS:
          return await this.handleMerchantVerificationStatus(message)
        case AdminMessageType.MERCHANT_ACCOUNT_STATUS_CHANGE:
          return await this.handleMerchantAccountStatusChange(message)
        case AdminMessageType.MERCHANT_VIOLATION_REPORT:
          return await this.handleMerchantViolationReport(message)

        // 内容管理
        case AdminMessageType.CONTENT_VIOLATION_REPORT:
          return await this.handleContentViolationReport(message)
        case AdminMessageType.CONTENT_AUDIT_RESULT:
          return await this.handleContentAuditResult(message)
        case AdminMessageType.CONTENT_MODERATION_ALERT:
          return await this.handleContentModerationAlert(message)

        // 订单管理
        case AdminMessageType.ORDER_DISPUTE_ALERT:
          return await this.handleOrderDisputeAlert(message)
        case AdminMessageType.ORDER_REFUND_REQUEST:
          return await this.handleOrderRefundRequest(message)
        case AdminMessageType.ORDER_ABNORMAL_ACTIVITY:
          return await this.handleOrderAbnormalActivity(message)

        // 财务管理
        case AdminMessageType.FINANCIAL_ALERT:
          return await this.handleFinancialAlert(message)
        case AdminMessageType.SETTLEMENT_EXCEPTION:
          return await this.handleSettlementException(message)
        case AdminMessageType.PAYMENT_ANOMALY:
          return await this.handlePaymentAnomaly(message)

        // 安全管理
        case AdminMessageType.SECURITY_ALERT:
          return await this.handleSecurityAlert(message)
        case AdminMessageType.SUSPICIOUS_ACTIVITY:
          return await this.handleSuspiciousActivity(message)
        case AdminMessageType.LOGIN_ANOMALY:
          return await this.handleLoginAnomaly(message)

        // 系统监控
        case AdminMessageType.SYSTEM_PERFORMANCE_ALERT:
          return await this.handleSystemPerformanceAlert(message)
        case AdminMessageType.DATABASE_ALERT:
          return await this.handleDatabaseAlert(message)
        case AdminMessageType.API_RATE_LIMIT_ALERT:
          return await this.handleApiRateLimitAlert(message)

        default:
          return this.createErrorResult(`Unsupported admin message type: ${message.type}`)
      }
    } catch (error) {
      this.logger.error(`Error processing admin message type ${message.type}:`, error)
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Unknown error',
        { messageType: message.type }
      )
    }
  }

  /**
   * 处理系统警报
   */
  private async handleSystemAlert(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { level, title, content, action_required } = message.data || {}
    
    this.logger.warn('System alert received:', { level, title, content })
    
    // 创建系统通知
    await this.createAdminNotification({
      type: 'system_alert',
      level: level || 'warning',
      title: title || 'System Alert',
      content: content || 'System alert received',
      action_required: action_required || false,
      timestamp: Date.now()
    })
    
    return this.createSuccessResult({
      type: 'system_alert',
      level,
      action_required
    })
  }

  /**
   * 处理维护通知
   */
  private async handleMaintenanceNotice(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { start_time, end_time, description, affected_services } = message.data || {}
    
    this.logger.info('Maintenance notice received:', { start_time, end_time, description })
    
    await this.createAdminNotification({
      type: 'maintenance_notice',
      level: 'info',
      title: 'System Maintenance Notice',
      content: description || 'System maintenance scheduled',
      metadata: {
        start_time,
        end_time,
        affected_services
      },
      timestamp: Date.now()
    })
    
    return this.createSuccessResult({
      type: 'maintenance_notice',
      start_time,
      end_time
    })
  }

  /**
   * 处理平台统计更新
   */
  private async handleStatisticsUpdate(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { statistics_type, data: statsData, period } = message.data || {}
    
    this.debug('Platform statistics update:', { statistics_type, period })
    
    // 更新管理员store中的统计数据
    if (this.adminStore && this.adminStore.updateStatistics) {
      this.adminStore.updateStatistics(statistics_type, statsData)
    }
    
    return this.createSuccessResult({
      type: 'statistics_update',
      statistics_type,
      period
    })
  }

  /**
   * 处理用户验证状态
   */
  private async handleUserVerificationStatus(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { user_id, status, reason, reviewer_id } = message.data || {}
    
    this.debug('User verification status update:', { user_id, status, reason })
    
    await this.createAdminNotification({
      type: 'user_verification',
      level: status === 'approved' ? 'success' : 'warning',
      title: 'User Verification Update',
      content: `User ${user_id} verification ${status}`,
      metadata: {
        user_id,
        status,
        reason,
        reviewer_id
      },
      timestamp: Date.now()
    })
    
    return this.createSuccessResult({
      type: 'user_verification',
      user_id,
      status
    })
  }

  /**
   * 处理商家验证状态
   */
  private async handleMerchantVerificationStatus(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { merchant_id, status, reason, reviewer_id } = message.data || {}
    
    this.debug('Merchant verification status update:', { merchant_id, status, reason })
    
    await this.createAdminNotification({
      type: 'merchant_verification',
      level: status === 'approved' ? 'success' : 'warning',
      title: 'Merchant Verification Update',
      content: `Merchant ${merchant_id} verification ${status}`,
      metadata: {
        merchant_id,
        status,
        reason,
        reviewer_id
      },
      timestamp: Date.now()
    })
    
    return this.createSuccessResult({
      type: 'merchant_verification',
      merchant_id,
      status
    })
  }

  /**
   * 处理内容违规举报
   */
  private async handleContentViolationReport(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { content_id, content_type, violation_type, reporter_id, severity } = message.data || {}
    
    this.logger.warn('Content violation report:', { content_id, content_type, violation_type, severity })
    
    await this.createAdminNotification({
      type: 'content_violation',
      level: severity === 'high' ? 'error' : 'warning',
      title: 'Content Violation Report',
      content: `${content_type} content reported for ${violation_type}`,
      action_required: true,
      metadata: {
        content_id,
        content_type,
        violation_type,
        reporter_id,
        severity
      },
      timestamp: Date.now()
    })
    
    return this.createSuccessResult({
      type: 'content_violation',
      content_id,
      severity,
      action_required: true
    })
  }

  /**
   * 处理安全警报
   */
  private async handleSecurityAlert(message: WebSocketMessage): Promise<MessageHandleResult> {
    const { alert_type, severity, description, affected_user, ip_address } = message.data || {}
    
    this.logger.error('Security alert:', { alert_type, severity, description })
    
    await this.createAdminNotification({
      type: 'security_alert',
      level: 'error',
      title: 'Security Alert',
      content: description || `Security alert: ${alert_type}`,
      action_required: severity === 'critical',
      metadata: {
        alert_type,
        severity,
        affected_user,
        ip_address
      },
      timestamp: Date.now()
    })
    
    return this.createSuccessResult({
      type: 'security_alert',
      alert_type,
      severity,
      action_required: severity === 'critical'
    })
  }

  /**
   * 创建管理员通知的通用方法
   */
  private async createAdminNotification(notification: {
    type: string
    level: 'info' | 'success' | 'warning' | 'error'
    title: string
    content: string
    action_required?: boolean
    metadata?: any
    timestamp: number
  }): Promise<void> {
    this.debug('Creating admin notification:', notification)
    
    // 这里可以集成到管理员的通知系统
    // 例如：存储到数据库、发送邮件、推送到管理员界面等
    
    if (this.adminStore && this.adminStore.addNotification) {
      this.adminStore.addNotification({
        id: Date.now(),
        ...notification,
        status: 'unread',
        created_at: new Date().toISOString()
      })
    }
  }

  // 其他处理方法的简化实现
  private async handleUserAccountStatusChange(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'user_account_status_change', processed: true })
  }

  private async handleUserViolationReport(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'user_violation_report', processed: true })
  }

  private async handleMerchantAccountStatusChange(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'merchant_account_status_change', processed: true })
  }

  private async handleMerchantViolationReport(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'merchant_violation_report', processed: true })
  }

  private async handleContentAuditResult(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'content_audit_result', processed: true })
  }

  private async handleContentModerationAlert(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'content_moderation_alert', processed: true })
  }

  private async handleOrderDisputeAlert(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'order_dispute_alert', processed: true })
  }

  private async handleOrderRefundRequest(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'order_refund_request', processed: true })
  }

  private async handleOrderAbnormalActivity(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'order_abnormal_activity', processed: true })
  }

  private async handleFinancialAlert(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'financial_alert', processed: true })
  }

  private async handleSettlementException(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'settlement_exception', processed: true })
  }

  private async handlePaymentAnomaly(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'payment_anomaly', processed: true })
  }

  private async handleSuspiciousActivity(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'suspicious_activity', processed: true })
  }

  private async handleLoginAnomaly(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'login_anomaly', processed: true })
  }

  private async handleSystemPerformanceAlert(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'system_performance_alert', processed: true })
  }

  private async handleDatabaseAlert(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'database_alert', processed: true })
  }

  private async handleApiRateLimitAlert(message: WebSocketMessage): Promise<MessageHandleResult> {
    return this.createSuccessResult({ type: 'api_rate_limit_alert', processed: true })
  }

  /**
   * 处理管理员通知消息
   */
  private async handleAdminNotificationMessage(message: any): Promise<MessageHandleResult> {
    this.debug('Processing admin notification message', message.data)

    try {
      const shouldShowNotification = await this.shouldShowElNotification('notification')

      if (shouldShowNotification) {
        const { ElNotification } = await import('element-plus')

        const title = message.data?.title || message.title || '管理员通知'
        const content = message.data?.message || message.message || message.content || '您有新的管理员通知'
        const priority = message.data?.priority || message.priority || 2
        const action_url = message.data?.action_url || message.action_url

        const notificationConfig = this.getNotificationConfig(priority, {
          title,
          message: content,
          onClick: action_url ? () => this.handleActionClick(action_url) : undefined
        })

        ElNotification(notificationConfig)
      }

      return this.createSuccessResult({
        type: 'admin_notification',
        notification_displayed: shouldShowNotification
      })

    } catch (error) {
      this.logger.error('Error handling admin notification:', error)
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Failed to handle notification'
      )
    }
  }

  /**
   * 检查是否应该显示ElNotification
   * 只有在聊天UI未打开时才显示通知，并且通过通知过滤器检查
   */
  private async shouldShowElNotification(messageType?: string, eventType?: string): Promise<boolean> {
    try {
      // 首先检查聊天UI是否打开
      if (this.chatStore && typeof this.chatStore.isChatUIVisible !== 'undefined') {
        const chatUIVisible = this.chatStore.isChatUIVisible
        if (chatUIVisible) {
          return false // 聊天UI打开时不显示通知
        }
      }

      // 然后检查通知过滤器设置
      if (messageType) {
        const notificationFilter = getNotificationFilter('admin')
        const shouldShow = notificationFilter.shouldShowNotification(messageType, eventType)
        if (!shouldShow) {
          this.debug(`Notification filtered out: ${messageType}${eventType ? `:${eventType}` : ''}`)
          return false
        }
      }

      // 如果通过了所有检查，显示通知
      return true
    } catch (error) {
      this.logger.error('Error checking notification display conditions:', error)
      return true // 出错时默认显示通知
    }
  }

  /**
   * 根据优先级获取通知配置
   */
  private getNotificationConfig(priority: number, options: {
    title: string
    message: string
    onClick?: () => void
  }) {
    const { title, message, onClick } = options

    let type: 'success' | 'warning' | 'info' | 'error' = 'info'
    let duration = 5000

    switch (priority) {
      case 1:
        type = 'info'
        duration = 4000
        break
      case 2:
        type = 'warning'
        duration = 6000
        break
      case 3:
        type = 'error'
        duration = 8000
        break
      default:
        type = 'info'
        duration = 5000
    }

    return {
      title,
      message,
      type,
      duration,
      position: 'top-right' as const,
      showClose: true,
      onClick
    }
  }

  /**
   * 处理action_url点击事件
   */
  private handleActionClick(actionUrl: string): void {
    try {
      if (typeof window !== 'undefined') {
        window.location.href = actionUrl
      }
    } catch (error) {
      this.logger.error('Error handling action click:', error)
    }
  }
}
