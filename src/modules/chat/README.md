# 聊天室模块 (Chat Module)

## 概述

聊天室模块是一个基于WebSocket的实时通信系统，支持售前咨询、售后服务和好友聊天等功能。该模块采用模块化设计，可以在商家端、用户端和管理端之间实现无缝通信。

## 功能特性

### 核心功能
- **实时消息传输**: 基于WebSocket的双向通信
- **多种消息类型**: 文本、图片、文件、语音、视频消息
- **会话管理**: 创建、管理和维护聊天会话
- **消息状态**: 已发送、已送达、已读状态跟踪
- **离线消息**: 支持离线消息存储和推送

### 业务场景
1. **售前咨询**: 用户向商家咨询商品信息
2. **售后服务**: 订单相关问题处理和客服支持
3. **好友聊天**: 用户之间的私人聊天功能
4. **系统通知**: 重要系统消息推送

### 用户角色
- **用户 (User)**: 普通消费者
- **商家 (Merchant)**: 商户客服人员
- **管理员 (Admin)**: 平台管理人员
- **系统 (System)**: 自动化消息发送

## 技术架构

### 前端架构
```
聊天模块 (Chat Module)
├── API层 (api/)
│   ├── chatApi.ts          # 聊天API接口
│   ├── messageApi.ts       # 消息API接口
│   ├── sessionApi.ts       # 会话API接口
│   └── websocketApi.ts     # WebSocket API
├── 组件层 (components/)
│   ├── ChatWindow.vue      # 聊天窗口主组件
│   ├── MessageList.vue     # 消息列表组件
│   ├── MessageInput.vue    # 消息输入组件
│   ├── SessionList.vue     # 会话列表组件
│   ├── FileUpload.vue      # 文件上传组件
│   └── EmojiPicker.vue     # 表情选择器
├── 服务层 (service/)
│   ├── ChatService.ts      # 聊天业务逻辑
│   ├── WebSocketService.ts # WebSocket连接管理
│   ├── MessageService.ts   # 消息处理服务
│   └── NotificationService.ts # 通知服务
├── 状态管理 (stores/)
│   ├── chatStore.ts        # 聊天状态管理
│   ├── messageStore.ts     # 消息状态管理
│   └── sessionStore.ts     # 会话状态管理
├── 类型定义 (types/)
│   ├── chat.ts            # 聊天相关类型
│   ├── message.ts         # 消息类型定义
│   └── session.ts         # 会话类型定义
├── 工具函数 (utils/)
│   ├── messageUtils.ts    # 消息处理工具
│   ├── timeUtils.ts       # 时间格式化工具
│   └── fileUtils.ts       # 文件处理工具
└── 视图页面 (views/)
    ├── ChatRoom.vue        # 聊天室主页面
    ├── SessionManage.vue   # 会话管理页面
    └── ChatSettings.vue   # 聊天设置页面
```

### WebSocket连接架构
```
客户端 WebSocket 连接
├── 连接管理器 (ConnectionManager)
│   ├── 自动重连机制
│   ├── 心跳检测
│   └── 连接状态监控
├── 消息处理器 (MessageHandler)
│   ├── 消息分发
│   ├── 消息确认
│   └── 错误处理
└── 事件监听器 (EventListener)
    ├── 连接事件
    ├── 消息事件
    └── 错误事件
```

## 数据流设计

### 消息发送流程
1. 用户在输入框输入消息
2. 前端验证消息内容
3. 通过WebSocket发送消息到服务器
4. 服务器处理并转发消息
5. 接收方收到消息并显示
6. 发送已读回执

### 会话创建流程
1. 用户发起聊天请求
2. 系统检查是否存在会话
3. 创建新会话或使用现有会话
4. 建立WebSocket连接
5. 加载历史消息
6. 开始实时通信

## 使用指南

### 基本使用
```typescript
// 1. 导入聊天服务
import { ChatService } from '@/modules/chat/service/ChatService'

// 2. 初始化聊天服务
const chatService = new ChatService({
  apiBaseUrl: 'https://api.example.com',
  wsBaseUrl: 'wss://api.example.com',
  userToken: 'your-jwt-token',
  userId: 123
})

// 3. 建立连接
await chatService.connect()

// 4. 发送消息
await chatService.sendMessage(sessionId, {
  type: 'text',
  content: 'Hello, World!'
})
```

### 组件使用
```vue
<template>
  <div class="chat-container">
    <!-- 聊天窗口组件 -->
    <ChatWindow
      :session-id="currentSessionId"
      :user-type="userType"
      @message-sent="handleMessageSent"
      @session-closed="handleSessionClosed"
    />
  </div>
</template>

<script setup lang="ts">
import ChatWindow from '@/modules/chat/components/ChatWindow.vue'

const currentSessionId = ref<number | null>(null)
const userType = ref<'user' | 'merchant' | 'admin'>('user')

const handleMessageSent = (message: Message) => {
  console.log('消息已发送:', message)
}

const handleSessionClosed = () => {
  currentSessionId.value = null
}
</script>
```

## 配置说明

### 环境变量
```env
# WebSocket服务地址
VITE_WS_BASE_URL=wss://api.example.com

# 聊天API地址
VITE_CHAT_API_URL=https://api.example.com/chat

# 文件上传地址
VITE_UPLOAD_URL=https://api.example.com/upload

# 最大文件大小 (MB)
VITE_MAX_FILE_SIZE=10

# 支持的文件类型
VITE_ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx
```

### 聊天配置
```typescript
export const chatConfig = {
  // WebSocket配置
  websocket: {
    reconnectAttempts: 5,
    reconnectInterval: 3000,
    heartbeatInterval: 30000
  },
  
  // 消息配置
  message: {
    maxLength: 1000,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']
  },
  
  // 通知配置
  notification: {
    enabled: true,
    sound: true,
    desktop: true
  }
}
```

## API接口

### RESTful API
- `GET /api/v1/chat/sessions` - 获取会话列表
- `POST /api/v1/chat/sessions` - 创建新会话
- `GET /api/v1/chat/sessions/{id}/messages` - 获取会话消息
- `POST /api/v1/chat/messages` - 发送消息
- `PUT /api/v1/chat/messages/{id}/read` - 标记消息已读

### WebSocket事件
- `message` - 新消息事件
- `typing` - 正在输入事件
- `read` - 消息已读事件
- `online` - 用户上线事件
- `offline` - 用户下线事件

## 开发指南

### 添加新消息类型
1. 在 `types/message.ts` 中定义新的消息类型
2. 在 `MessageService.ts` 中添加处理逻辑
3. 在 `MessageList.vue` 中添加渲染组件
4. 在 `MessageInput.vue` 中添加发送逻辑

### 自定义主题
1. 修改 `styles/chat-theme.scss` 文件
2. 定义新的CSS变量
3. 在组件中使用主题变量

### 扩展功能
1. 创建新的服务类
2. 添加对应的API接口
3. 更新状态管理
4. 创建或修改UI组件

## 注意事项

1. **安全性**: 所有消息都需要进行内容过滤和XSS防护
2. **性能**: 大量消息时需要实现虚拟滚动
3. **兼容性**: 确保WebSocket在各种网络环境下的稳定性
4. **用户体验**: 提供良好的加载状态和错误提示
5. **数据同步**: 确保多端消息状态的一致性

## 版本历史

- v1.0.0 - 初始版本，支持基本聊天功能
- v1.1.0 - 添加文件上传和表情功能
- v1.2.0 - 支持语音和视频消息
- v1.3.0 - 添加消息搜索和会话管理