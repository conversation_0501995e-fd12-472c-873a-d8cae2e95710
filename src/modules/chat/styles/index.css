/**
 * 聊天模块样式文件
 * 包含聊天界面的全局样式、主题变量和组件样式
 */

/* CSS 变量定义 */
:root {
  /* 聊天主题色彩 */
  --chat-primary-color: #1890ff;
  --chat-primary-hover: #40a9ff;
  --chat-primary-active: #096dd9;
  --chat-primary-light: #e6f7ff;
  
  /* 消息气泡颜色 */
  --chat-message-sent: #1890ff;
  --chat-message-received: #f5f5f5;
  --chat-message-system: #fff2e8;
  --chat-message-error: #fff1f0;
  
  /* 状态颜色 */
  --chat-success: #52c41a;
  --chat-warning: #faad14;
  --chat-error: #ff4d4f;
  --chat-info: #1890ff;
  
  /* 文字颜色 */
  --chat-text-primary: #262626;
  --chat-text-secondary: #8c8c8c;
  --chat-text-disabled: #bfbfbf;
  --chat-text-inverse: #ffffff;
  
  /* 背景颜色 */
  --chat-bg-primary: #ffffff;
  --chat-bg-secondary: #fafafa;
  --chat-bg-tertiary: #f5f5f5;
  --chat-bg-hover: #f0f0f0;
  --chat-bg-active: #e6f7ff;
  
  /* 边框颜色 */
  --chat-border-color: #d9d9d9;
  --chat-border-light: #f0f0f0;
  --chat-border-dark: #bfbfbf;
  
  /* 阴影 */
  --chat-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --chat-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --chat-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  
  /* 圆角 */
  --chat-radius-sm: 4px;
  --chat-radius-md: 6px;
  --chat-radius-lg: 8px;
  --chat-radius-xl: 12px;
  
  /* 间距 */
  --chat-spacing-xs: 4px;
  --chat-spacing-sm: 8px;
  --chat-spacing-md: 12px;
  --chat-spacing-lg: 16px;
  --chat-spacing-xl: 24px;
  
  /* 字体大小 */
  --chat-font-xs: 12px;
  --chat-font-sm: 14px;
  --chat-font-md: 16px;
  --chat-font-lg: 18px;
  --chat-font-xl: 20px;
  
  /* 动画时长 */
  --chat-transition-fast: 0.15s;
  --chat-transition-normal: 0.3s;
  --chat-transition-slow: 0.5s;
  
  /* Z-index 层级 */
  --chat-z-dropdown: 1000;
  --chat-z-modal: 1050;
  --chat-z-popover: 1060;
  --chat-z-tooltip: 1070;
  --chat-z-notification: 1080;
}

/* 暗色主题 */
[data-theme="dark"] {
  --chat-primary-color: #177ddc;
  --chat-primary-hover: #3c9ae8;
  --chat-primary-active: #0958d9;
  --chat-primary-light: #111b26;
  
  --chat-message-sent: #177ddc;
  --chat-message-received: #262626;
  --chat-message-system: #2b1d16;
  --chat-message-error: #2a1215;
  
  --chat-text-primary: #ffffff;
  --chat-text-secondary: #a6a6a6;
  --chat-text-disabled: #595959;
  --chat-text-inverse: #000000;
  
  --chat-bg-primary: #141414;
  --chat-bg-secondary: #1f1f1f;
  --chat-bg-tertiary: #262626;
  --chat-bg-hover: #2c2c2c;
  --chat-bg-active: #111b26;
  
  --chat-border-color: #434343;
  --chat-border-light: #303030;
  --chat-border-dark: #595959;
}

/* 聊天窗口基础样式 */
.chat-window {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: var(--chat-font-sm);
  line-height: 1.5;
  color: var(--chat-text-primary);
  background-color: var(--chat-bg-primary);
}

/* 聊天布局 */
.chat-layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.chat-sidebar {
  width: 280px;
  min-width: 240px;
  max-width: 400px;
  background-color: var(--chat-bg-secondary);
  border-right: 1px solid var(--chat-border-color);
  display: flex;
  flex-direction: column;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.chat-info-panel {
  width: 320px;
  min-width: 280px;
  max-width: 400px;
  background-color: var(--chat-bg-secondary);
  border-left: 1px solid var(--chat-border-color);
  display: flex;
  flex-direction: column;
}

/* 消息气泡样式 */
.message-bubble {
  max-width: 70%;
  padding: var(--chat-spacing-sm) var(--chat-spacing-md);
  border-radius: var(--chat-radius-lg);
  word-wrap: break-word;
  word-break: break-word;
  position: relative;
}

.message-bubble--sent {
  background-color: var(--chat-message-sent);
  color: var(--chat-text-inverse);
  margin-left: auto;
  border-bottom-right-radius: var(--chat-radius-sm);
}

.message-bubble--received {
  background-color: var(--chat-message-received);
  color: var(--chat-text-primary);
  margin-right: auto;
  border-bottom-left-radius: var(--chat-radius-sm);
}

.message-bubble--system {
  background-color: var(--chat-message-system);
  color: var(--chat-text-secondary);
  margin: 0 auto;
  text-align: center;
  font-size: var(--chat-font-xs);
  border-radius: var(--chat-radius-xl);
}

.message-bubble--error {
  background-color: var(--chat-message-error);
  color: var(--chat-error);
  border: 1px solid var(--chat-error);
}

/* 消息状态指示器 */
.message-status {
  display: inline-flex;
  align-items: center;
  margin-left: var(--chat-spacing-xs);
  font-size: var(--chat-font-xs);
  color: var(--chat-text-secondary);
}

.message-status--sending {
  color: var(--chat-warning);
}

.message-status--sent {
  color: var(--chat-success);
}

.message-status--failed {
  color: var(--chat-error);
}

/* 输入框样式 */
.chat-input {
  border: 1px solid var(--chat-border-color);
  border-radius: var(--chat-radius-md);
  padding: var(--chat-spacing-sm) var(--chat-spacing-md);
  font-size: var(--chat-font-sm);
  line-height: 1.5;
  background-color: var(--chat-bg-primary);
  color: var(--chat-text-primary);
  resize: none;
  outline: none;
  transition: border-color var(--chat-transition-fast);
}

.chat-input:focus {
  border-color: var(--chat-primary-color);
  box-shadow: 0 0 0 2px var(--chat-primary-light);
}

.chat-input::placeholder {
  color: var(--chat-text-disabled);
}

/* 按钮样式 */
.chat-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--chat-spacing-xs) var(--chat-spacing-md);
  border: 1px solid var(--chat-border-color);
  border-radius: var(--chat-radius-md);
  background-color: var(--chat-bg-primary);
  color: var(--chat-text-primary);
  font-size: var(--chat-font-sm);
  cursor: pointer;
  transition: all var(--chat-transition-fast);
  text-decoration: none;
  user-select: none;
}

.chat-btn:hover {
  background-color: var(--chat-bg-hover);
  border-color: var(--chat-border-dark);
}

.chat-btn:active {
  background-color: var(--chat-bg-active);
}

.chat-btn--primary {
  background-color: var(--chat-primary-color);
  border-color: var(--chat-primary-color);
  color: var(--chat-text-inverse);
}

.chat-btn--primary:hover {
  background-color: var(--chat-primary-hover);
  border-color: var(--chat-primary-hover);
}

.chat-btn--primary:active {
  background-color: var(--chat-primary-active);
  border-color: var(--chat-primary-active);
}

.chat-btn--danger {
  background-color: var(--chat-error);
  border-color: var(--chat-error);
  color: var(--chat-text-inverse);
}

.chat-btn--text {
  border: none;
  background: transparent;
  color: var(--chat-primary-color);
}

.chat-btn--text:hover {
  background-color: var(--chat-primary-light);
}

.chat-btn--icon {
  padding: var(--chat-spacing-xs);
  border: none;
  background: transparent;
  border-radius: var(--chat-radius-sm);
}

.chat-btn--icon:hover {
  background-color: var(--chat-bg-hover);
}

/* 头像样式 */
.chat-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  background-color: var(--chat-bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--chat-font-sm);
  font-weight: 500;
  color: var(--chat-text-secondary);
}

.chat-avatar--sm {
  width: 24px;
  height: 24px;
  font-size: var(--chat-font-xs);
}

.chat-avatar--lg {
  width: 48px;
  height: 48px;
  font-size: var(--chat-font-lg);
}

/* 在线状态指示器 */
.online-indicator {
  position: relative;
}

.online-indicator::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid var(--chat-bg-primary);
  background-color: var(--chat-success);
}

.online-indicator--offline::after {
  background-color: var(--chat-text-disabled);
}

.online-indicator--away::after {
  background-color: var(--chat-warning);
}

.online-indicator--busy::after {
  background-color: var(--chat-error);
}

/* 徽章样式 */
.chat-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
  height: 16px;
  padding: 0 var(--chat-spacing-xs);
  font-size: var(--chat-font-xs);
  font-weight: 500;
  line-height: 1;
  color: var(--chat-text-inverse);
  background-color: var(--chat-error);
  border-radius: var(--chat-radius-xl);
}

.chat-badge--primary {
  background-color: var(--chat-primary-color);
}

.chat-badge--success {
  background-color: var(--chat-success);
}

.chat-badge--warning {
  background-color: var(--chat-warning);
}

.chat-badge--dot {
  width: 8px;
  height: 8px;
  min-width: 8px;
  padding: 0;
  border-radius: 50%;
}

/* 分割线 */
.chat-divider {
  height: 1px;
  background-color: var(--chat-border-light);
  margin: var(--chat-spacing-md) 0;
}

.chat-divider--vertical {
  width: 1px;
  height: auto;
  margin: 0 var(--chat-spacing-md);
}

/* 加载动画 */
.chat-loading {
  display: inline-flex;
  align-items: center;
  gap: var(--chat-spacing-xs);
}

.chat-loading-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: var(--chat-text-secondary);
  animation: chat-loading-bounce 1.4s ease-in-out infinite both;
}

.chat-loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.chat-loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes chat-loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 滚动条样式 */
.chat-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--chat-border-color) transparent;
}

.chat-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background-color: var(--chat-border-color);
  border-radius: var(--chat-radius-sm);
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: var(--chat-border-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: var(--chat-z-modal);
    transform: translateX(-100%);
    transition: transform var(--chat-transition-normal);
  }
  
  .chat-sidebar--open {
    transform: translateX(0);
  }
  
  .chat-info-panel {
    position: fixed;
    right: 0;
    top: 0;
    height: 100vh;
    z-index: var(--chat-z-modal);
    transform: translateX(100%);
    transition: transform var(--chat-transition-normal);
  }
  
  .chat-info-panel--open {
    transform: translateX(0);
  }
  
  .message-bubble {
    max-width: 85%;
  }
}

@media (max-width: 480px) {
  .chat-sidebar,
  .chat-info-panel {
    width: 100vw;
  }
  
  .message-bubble {
    max-width: 90%;
    padding: var(--chat-spacing-xs) var(--chat-spacing-sm);
  }
}

/* 打印样式 */
@media print {
  .chat-window {
    background: white;
    color: black;
  }
  
  .chat-sidebar,
  .chat-info-panel {
    display: none;
  }
  
  .message-bubble {
    border: 1px solid #ccc;
    background: white !important;
    color: black !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --chat-border-color: #000000;
    --chat-text-secondary: #000000;
  }
  
  .message-bubble {
    border: 2px solid var(--chat-border-color);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}