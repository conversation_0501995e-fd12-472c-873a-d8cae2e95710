/**
 * 聊天模块路由配置
 */

import type { RouteRecordRaw } from 'vue-router'

// 路由组件懒加载
const ChatWindow = () => import('../components/ChatWindow.vue')
const SessionList = () => import('../components/SessionList.vue')
const MessageList = () => import('../components/MessageList.vue')

// 聊天模块路由配置
export const chatRoutes: RouteRecordRaw[] = [
  {
    path: '/chat',
    name: 'Chat',
    component: ChatWindow,
    meta: {
      title: '聊天',
      requiresAuth: true,
      keepAlive: true,
      icon: 'chat'
    },
    children: [
      {
        path: '',
        name: 'ChatIndex',
        redirect: '/chat/sessions'
      },
      {
        path: 'sessions',
        name: 'ChatSessions',
        component: SessionList,
        meta: {
          title: '会话列表',
          requiresAuth: true
        }
      },
      {
        path: 'session/:sessionId',
        name: 'ChatSession',
        component: MessageList,
        meta: {
          title: '聊天会话',
          requiresAuth: true,
          keepAlive: true
        },
        props: true
      },
      {
        path: 'session/:sessionId/info',
        name: 'ChatSessionInfo',
        component: () => import('../components/SessionInfo.vue'),
        meta: {
          title: '会话信息',
          requiresAuth: true
        },
        props: true
      }
    ]
  }
]

// 路由守卫
export const chatRouteGuards = {
  // 进入聊天模块前的守卫
  beforeEnter: (_to: any, _from: any, next: any) => {
    // 检查用户是否已登录
    const token = localStorage.getItem('token')
    if (!token) {
      next('/login')
      return
    }
    
    // 检查聊天权限
    const userPermissions = JSON.parse(localStorage.getItem('userPermissions') || '[]')
    if (!userPermissions.includes('chat:access')) {
      next('/403')
      return
    }
    
    next()
  },
  
  // 离开聊天模块前的守卫
  beforeLeave: (_to: any, _from: any, next: any) => {
    // 检查是否有未发送的消息
    const hasUnsavedChanges = sessionStorage.getItem('chat:unsavedChanges')
    if (hasUnsavedChanges) {
      const confirmed = confirm('您有未发送的消息，确定要离开吗？')
      if (!confirmed) {
        next(false)
        return
      }
    }
    
    next()
  }
}

// 路由元信息类型定义
export interface ChatRouteMeta {
  title: string
  requiresAuth?: boolean
  keepAlive?: boolean
  icon?: string
  permissions?: string[]
  breadcrumb?: string[]
}

// 路由参数类型定义
export interface ChatRouteParams {
  sessionId?: string
  messageId?: string
  userId?: string
}

// 路由查询参数类型定义
export interface ChatRouteQuery {
  search?: string
  filter?: string
  page?: string
  size?: string
  sort?: string
  order?: 'asc' | 'desc'
}

// 路由工具函数
export const chatRouteUtils = {
  // 生成会话路由
  getSessionRoute: (sessionId: string) => {
    return {
      name: 'ChatSession',
      params: { sessionId }
    }
  },
  
  // 生成会话信息路由
  getSessionInfoRoute: (sessionId: string) => {
    return {
      name: 'ChatSessionInfo',
      params: { sessionId }
    }
  },
  
  // 生成带查询参数的会话列表路由
  getSessionsRoute: (query?: ChatRouteQuery) => {
    return {
      name: 'ChatSessions',
      query
    }
  },
  
  // 解析路由参数
  parseRouteParams: (route: any): ChatRouteParams => {
    return {
      sessionId: route.params.sessionId,
      messageId: route.params.messageId,
      userId: route.params.userId
    }
  },
  
  // 解析查询参数
  parseRouteQuery: (route: any): ChatRouteQuery => {
    return {
      search: route.query.search,
      filter: route.query.filter,
      page: route.query.page,
      size: route.query.size,
      sort: route.query.sort,
      order: route.query.order
    }
  },
  
  // 检查当前路由是否为聊天相关路由
  isChatRoute: (route: any): boolean => {
    return route.path.startsWith('/chat')
  },
  
  // 检查当前路由是否为会话路由
  isSessionRoute: (route: any): boolean => {
    return route.name === 'ChatSession'
  },
  
  // 获取当前会话ID
  getCurrentSessionId: (route: any): string | null => {
    return route.params.sessionId || null
  }
}

// 路由导航方法
export const chatNavigation = {
  // 导航到会话列表
  toSessions: (router: any, query?: ChatRouteQuery) => {
    return router.push(chatRouteUtils.getSessionsRoute(query))
  },
  
  // 导航到指定会话
  toSession: (router: any, sessionId: string) => {
    return router.push(chatRouteUtils.getSessionRoute(sessionId))
  },
  
  // 导航到会话信息
  toSessionInfo: (router: any, sessionId: string) => {
    return router.push(chatRouteUtils.getSessionInfoRoute(sessionId))
  },
  
  // 返回上一页
  goBack: (router: any) => {
    return router.go(-1)
  },
  
  // 替换当前路由
  replace: (router: any, route: any) => {
    return router.replace(route)
  }
}

// 默认导出
export default {
  routes: chatRoutes,
  guards: chatRouteGuards,
  utils: chatRouteUtils,
  navigation: chatNavigation
}