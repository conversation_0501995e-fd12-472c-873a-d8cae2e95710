/**
 * 聊天相关类型定义
 */

import type { SenderType } from './message'
import type { SessionType } from './session'

// 聊天客户端配置
export interface ChatClientConfig {
  apiBaseUrl: string
  wsBaseUrl: string
  userToken: string
  userId: number
  userType: SenderType
  autoReconnect?: boolean
  maxReconnectAttempts?: number
  reconnectInterval?: number
  heartbeatInterval?: number
  messageQueueSize?: number
  enableNotifications?: boolean
  enableSound?: boolean
  debug?: boolean
}

// 聊天客户端状态
export enum ChatClientStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

// 用户在线状态
export enum UserOnlineStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  AWAY = 'away',
  BUSY = 'busy',
  INVISIBLE = 'invisible'
}

// 聊天用户信息
export interface ChatUser {
  id: number
  type: SenderType
  name: string
  avatar?: string
  nickname?: string
  email?: string
  phone?: string
  status: UserOnlineStatus
  last_seen?: string
  is_typing?: boolean
  // 商家特有字段
  merchant_id?: number
  merchant_name?: string
  department?: string
  role?: string
  // 用户特有字段
  level?: string
  registration_date?: string
  is_vip?: boolean
  // 管理员特有字段
  permissions?: string[]
  admin_level?: number
}

// 聊天房间信息
export interface ChatRoom {
  id: number
  name: string
  description?: string
  type: SessionType
  participants: ChatUser[]
  max_participants?: number
  is_private: boolean
  created_by: number
  created_at: string
  settings: {
    allow_file_upload: boolean
    allow_voice_message: boolean
    allow_video_message: boolean
    message_retention_days: number
    auto_delete_messages: boolean
  }
}

// 聊天事件类型
export enum ChatEventType {
  // 连接事件
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  RECONNECTED = 'reconnected',
  CONNECTION_ERROR = 'connection_error',
  
  // 消息事件
  MESSAGE_RECEIVED = 'message_received',
  MESSAGE_SENT = 'message_sent',
  MESSAGE_DELIVERED = 'message_delivered',
  MESSAGE_READ = 'message_read',
  MESSAGE_FAILED = 'message_failed',
  
  // 会话事件
  SESSION_CREATED = 'session_created',
  SESSION_UPDATED = 'session_updated',
  SESSION_CLOSED = 'session_closed',
  SESSION_ARCHIVED = 'session_archived',
  
  // 用户事件
  USER_ONLINE = 'user_online',
  USER_OFFLINE = 'user_offline',
  USER_TYPING = 'user_typing',
  USER_STOP_TYPING = 'user_stop_typing',
  USER_JOINED = 'user_joined',
  USER_LEFT = 'user_left',
  
  // 系统事件
  SYSTEM_NOTIFICATION = 'system_notification',
  SYSTEM_MAINTENANCE = 'system_maintenance',
  SYSTEM_ERROR = 'system_error'
}

// 聊天事件数据
export interface ChatEvent {
  type: ChatEventType
  timestamp: number
  data: any
  session_id?: number
  user_id?: number
  user_type?: SenderType
}

// 聊天统计信息
export interface ChatStatistics {
  total_sessions: number
  active_sessions: number
  total_messages: number
  unread_messages: number
  online_users: number
  response_time: {
    average: number
    median: number
    percentile_95: number
  }
  message_types: Record<string, number>
  peak_hours: Array<{
    hour: number
    message_count: number
  }>
  user_satisfaction: {
    average_rating: number
    total_ratings: number
    rating_distribution: Record<number, number>
  }
}

// 聊天设置
export interface ChatSettings {
  // 通知设置
  notifications: {
    enabled: boolean
    sound: boolean
    desktop: boolean
    email: boolean
    push: boolean
    quiet_hours: {
      enabled: boolean
      start_time: string
      end_time: string
    }
  }
  
  // 消息设置
  messages: {
    auto_read: boolean
    show_typing_indicator: boolean
    show_read_receipts: boolean
    message_preview_length: number
    auto_delete_after_days: number
  }
  
  // 隐私设置
  privacy: {
    show_online_status: boolean
    allow_stranger_messages: boolean
    block_list: number[]
    auto_accept_friend_requests: boolean
  }
  
  // 界面设置
  ui: {
    theme: 'light' | 'dark' | 'auto'
    font_size: 'small' | 'medium' | 'large'
    compact_mode: boolean
    show_avatars: boolean
    show_timestamps: boolean
    emoji_style: 'native' | 'twitter' | 'apple' | 'google'
  }
  
  // 文件设置
  files: {
    auto_download_images: boolean
    auto_download_files: boolean
    max_file_size: number
    allowed_file_types: string[]
    save_location: string
  }
}

// 快捷回复
export interface QuickReply {
  id: number
  title: string
  content: string
  category: string
  user_id: number
  user_type: SenderType
  usage_count: number
  is_active: boolean
  created_at: string
  updated_at: string
}

// 消息模板
export interface MessageTemplate {
  id: number
  name: string
  content: string
  variables: Array<{
    name: string
    type: 'text' | 'number' | 'date' | 'select'
    required: boolean
    default_value?: string
    options?: string[] // for select type
  }>
  category: string
  user_id: number
  user_type: SenderType
  is_public: boolean
  usage_count: number
  created_at: string
  updated_at: string
}

// 聊天机器人配置
export interface ChatBotConfig {
  enabled: boolean
  name: string
  avatar?: string
  welcome_message: string
  fallback_message: string
  working_hours: {
    enabled: boolean
    timezone: string
    schedule: Array<{
      day: number // 0-6, 0为周日
      start_time: string
      end_time: string
    }>
  }
  auto_responses: Array<{
    keywords: string[]
    response: string
    confidence_threshold: number
  }>
  escalation_rules: Array<{
    condition: string
    action: 'transfer_to_human' | 'create_ticket' | 'send_notification'
    target?: number
  }>
}

// 文件上传配置
export interface FileUploadConfig {
  max_file_size: number // bytes
  allowed_types: string[]
  upload_url: string
  chunk_size?: number
  concurrent_uploads?: number
  auto_compress_images?: boolean
  image_quality?: number // 0-100
  generate_thumbnails?: boolean
}

// 聊天权限
export interface ChatPermissions {
  can_send_messages: boolean
  can_send_files: boolean
  can_send_images: boolean
  can_send_voice: boolean
  can_send_video: boolean
  can_create_sessions: boolean
  can_invite_users: boolean
  can_remove_users: boolean
  can_modify_session: boolean
  can_delete_messages: boolean
  can_view_history: boolean
  can_export_chat: boolean
  max_file_size: number
  max_message_length: number
  rate_limit: {
    messages_per_minute: number
    files_per_hour: number
  }
}

// 聊天错误类型
export enum ChatErrorType {
  NETWORK_ERROR = 'network_error',
  AUTHENTICATION_ERROR = 'authentication_error',
  PERMISSION_ERROR = 'permission_error',
  VALIDATION_ERROR = 'validation_error',
  SERVER_ERROR = 'server_error',
  FILE_UPLOAD_ERROR = 'file_upload_error',
  MESSAGE_SEND_ERROR = 'message_send_error',
  SESSION_ERROR = 'session_error',
  UNKNOWN_ERROR = 'unknown_error'
}

// 聊天错误信息
export interface ChatError {
  type: ChatErrorType
  code: string
  message: string
  details?: any
  timestamp: number
  session_id?: number
  message_id?: number
  retry_count?: number
  is_recoverable: boolean
}

// 聊天日志
export interface ChatLog {
  id: string
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
  data?: any
  timestamp: number
  user_id?: number
  session_id?: number
  source: string
}

// 聊天性能指标
export interface ChatPerformanceMetrics {
  connection_time: number
  message_send_time: number
  message_receive_time: number
  file_upload_time: number
  memory_usage: number
  cpu_usage: number
  network_usage: {
    bytes_sent: number
    bytes_received: number
  }
  error_rate: number
  uptime: number
}