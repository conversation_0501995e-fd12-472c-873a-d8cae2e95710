/**
 * 消息相关类型定义
 */

// 消息分类枚举
export enum MessageCategory {
  CHAT = 'chat',           // 聊天消息
  SYSTEM = 'system',       // 系统通知
  ORDER = 'order',         // 订单消息
  SERVICE = 'service'      // 客服消息
}

// 消息类型枚举
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  VOICE = 'voice',
  VIDEO = 'video',
  SYSTEM = 'system',
  PRODUCT_CARD = 'product_card',
  ORDER_CARD = 'order_card'
}

// 消息状态枚举
export enum MessageStatus {
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed'
}

// 发送者类型枚举
export enum SenderType {
  USER = 'user',
  MERCHANT = 'merchant',
  ADMIN = 'admin',
  SYSTEM = 'system'
}

// 基础消息接口
export interface BaseMessage {
  id: number
  session_id: number
  sender_id: number
  sender_type: SenderType
  receiver_id: number
  receiver_type: SenderType
  type: MessageType
  category: MessageCategory  // 新增：消息分类
  content: string
  status: MessageStatus
  created_at: string
  updated_at: string
  is_deleted: boolean
}

// 文本消息
export interface TextMessage extends BaseMessage {
  type: MessageType.TEXT
  content: string
}

// 图片消息
export interface ImageMessage extends BaseMessage {
  type: MessageType.IMAGE
  resource_id: string
  image_url: string
  thumbnail_url?: string
  width?: number
  height?: number
  file_size?: number
}

// 文件消息
export interface FileMessage extends BaseMessage {
  type: MessageType.FILE
  resource_id: string
  file_name: string
  file_url: string
  file_size: number
  file_ext: string
  mime_type: string
}

// 语音消息
export interface VoiceMessage extends BaseMessage {
  type: MessageType.VOICE
  resource_id: string
  voice_url: string
  duration: number // 秒
  file_size: number
}

// 视频消息
export interface VideoMessage extends BaseMessage {
  type: MessageType.VIDEO
  resource_id: string
  video_url: string
  thumbnail_url?: string
  duration: number // 秒
  width?: number
  height?: number
  file_size: number
}

// 系统消息
export interface SystemMessage extends BaseMessage {
  type: MessageType.SYSTEM
  category: MessageCategory.SYSTEM
  system_type: 'notification' | 'warning' | 'info' | 'session_created' | 'session_closed'
  content: string
}

// 系统通知消息
export interface SystemNotificationMessage extends BaseMessage {
  category: MessageCategory.SYSTEM
  notification_type: 'system_maintenance' | 'version_update' | 'security_notice' | 'activity_notice' | 'policy_update' | 'feature_announcement'
  title: string
  summary?: string
  image_url?: string
  action_type?: string
  action_url?: string
  priority: number
  expire_time?: string
}

// 订单消息
export interface OrderMessage extends BaseMessage {
  category: MessageCategory.ORDER
  order_id: number
  order_no: string
  order_type: 'payment_success' | 'payment_failed' | 'order_confirmed' | 'order_shipped' | 'order_delivered' | 'order_completed' | 'order_cancelled' | 'refund_applied' | 'refund_approved' | 'refund_rejected' | 'refund_success' | 'refund_failed'
  amount?: number
  refund_amount?: number
  goods_info?: Array<{
    goods_id: string
    goods_name: string
    goods_image: string
    quantity: number
  }>
  action_type?: string
  action_url?: string
}

// 客服消息
export interface ServiceMessage extends BaseMessage {
  category: MessageCategory.SERVICE
  service_info: {
    service_id: string
    service_name: string
    service_avatar: string
    is_online: boolean
  }
  session_info?: {
    session_id: string
    last_message?: {
      content: string
      time: string
      type: string
    }
    unread_count: number
  }
}

// 聊天消息（普通用户间聊天）
export interface ChatMessage extends BaseMessage {
  category: MessageCategory.CHAT
  // 聊天消息使用基础消息结构即可
}

// 商品卡片消息
export interface ProductCardMessage extends BaseMessage {
  type: MessageType.PRODUCT_CARD
  product_id: number
  product_name: string
  product_image: string
  product_price: number
  product_url: string
  merchant_id: number
  merchant_name: string
}

// 订单卡片消息
export interface OrderCardMessage extends BaseMessage {
  type: MessageType.ORDER_CARD
  order_id: number
  order_number: string
  order_status: string
  order_amount: number
  order_time: string
  product_count: number
  order_url: string
}

// 联合消息类型
export type Message =
  | TextMessage
  | ImageMessage
  | FileMessage
  | VoiceMessage
  | VideoMessage
  | SystemMessage
  | SystemNotificationMessage
  | OrderMessage
  | ServiceMessage
  | ChatMessage
  | ProductCardMessage
  | OrderCardMessage

// 消息分类信息
export interface MessageCategoryInfo {
  type: MessageCategory
  title: string
  icon: string
  color: string
  unread_count: number
  path: string
}

// 消息发送请求
export interface SendMessageRequest {
  session_id: number
  type: MessageType
  category: MessageCategory
  content: string
  resource_id?: string
  extra_data?: Record<string, any>
}

// 消息发送响应
export interface SendMessageResponse {
  success: boolean
  message: Message
  error?: string
}

// 消息列表请求
export interface GetMessagesRequest {
  session_id?: number
  category?: MessageCategory  // 新增：按分类筛选
  page?: number
  page_size?: number
  before_message_id?: number
  after_message_id?: number
}

// 获取消息分类请求
export interface GetMessageCategoriesRequest {
  include_unread_count?: boolean
}

// 获取消息分类响应
export interface GetMessageCategoriesResponse {
  success: boolean
  data: MessageCategoryInfo[]
  error?: string
}

// 消息列表响应
export interface GetMessagesResponse {
  success: boolean
  data: {
    messages: Message[]
    total: number
    page: number
    page_size: number
    has_more: boolean
  }
  error?: string
}

// 标记消息已读请求
export interface MarkMessageReadRequest {
  message_ids: number[]
  session_id?: number
}

// 标记消息已读响应
export interface MarkMessageReadResponse {
  success: boolean
  updated_count: number
  error?: string
}

// 消息搜索请求
export interface SearchMessagesRequest {
  keyword: string
  session_id?: number
  category?: MessageCategory  // 新增：按分类搜索
  message_type?: MessageType
  sender_type?: SenderType
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// 消息搜索响应
export interface SearchMessagesResponse {
  success: boolean
  data: {
    messages: Message[]
    total: number
    page: number
    page_size: number
    has_more: boolean
  }
  error?: string
}

// 消息统计信息
export interface MessageStats {
  total_messages: number
  unread_messages: number
  today_messages: number
  message_types: Record<MessageType, number>
  message_categories: Record<MessageCategory, number>  // 新增：按分类统计
  unread_by_category: Record<MessageCategory, number>  // 新增：按分类未读统计
}

// 消息预览信息
export interface MessagePreview {
  type: MessageType
  content: string
  preview_text: string
  created_at: string
}

// 正在输入状态
export interface TypingStatus {
  session_id: number
  user_id: number
  user_type: SenderType
  is_typing: boolean
  timestamp: number
}

// 消息回执
export interface MessageReceipt {
  message_id: number
  session_id: number
  user_id: number
  user_type: SenderType
  status: MessageStatus
  timestamp: number
}

// 文件上传进度
export interface FileUploadProgress {
  file_id: string
  file_name: string
  total_size: number
  uploaded_size: number
  progress: number // 0-100
  status: 'uploading' | 'completed' | 'failed' | 'cancelled'
  error?: string
}

// 消息草稿
export interface MessageDraft {
  session_id: number
  content: string
  type: MessageType
  category: MessageCategory
  created_at: string
  updated_at: string
}

// 消息分类工具函数
export const MessageCategoryUtils = {
  /**
   * 获取消息分类信息
   */
  getCategoryInfo(category: MessageCategory): MessageCategoryInfo {
    const categoryMap: Record<MessageCategory, Omit<MessageCategoryInfo, 'unread_count'>> = {
      [MessageCategory.CHAT]: {
        type: MessageCategory.CHAT,
        title: '聊天消息',
        icon: 'chat',
        color: '#3b82f6',
        path: '/chat'
      },
      [MessageCategory.SYSTEM]: {
        type: MessageCategory.SYSTEM,
        title: '系统通知',
        icon: 'notification',
        color: '#f59e0b',
        path: '/system'
      },
      [MessageCategory.ORDER]: {
        type: MessageCategory.ORDER,
        title: '订单消息',
        icon: 'order',
        color: '#10b981',
        path: '/order'
      },
      [MessageCategory.SERVICE]: {
        type: MessageCategory.SERVICE,
        title: '客服消息',
        icon: 'service',
        color: '#8b5cf6',
        path: '/service'
      }
    }

    return {
      ...categoryMap[category],
      unread_count: 0
    }
  },

  /**
   * 获取所有消息分类
   */
  getAllCategories(): MessageCategoryInfo[] {
    return Object.values(MessageCategory).map(category =>
      this.getCategoryInfo(category)
    )
  },

  /**
   * 根据消息类型判断消息分类
   */
  getCategoryByMessageType(messageType: MessageType): MessageCategory {
    switch (messageType) {
      case MessageType.SYSTEM:
        return MessageCategory.SYSTEM
      case MessageType.ORDER_CARD:
        return MessageCategory.ORDER
      default:
        return MessageCategory.CHAT
    }
  },

  /**
   * 格式化消息预览文本
   */
  formatPreviewText(message: Message): string {
    switch (message.type) {
      case MessageType.TEXT:
        return (message as TextMessage).content
      case MessageType.IMAGE:
        return '[图片]'
      case MessageType.FILE:
        return '[文件]'
      case MessageType.VOICE:
        return '[语音]'
      case MessageType.VIDEO:
        return '[视频]'
      case MessageType.SYSTEM:
        return (message as SystemMessage).content
      case MessageType.PRODUCT_CARD:
        return '[商品卡片]'
      case MessageType.ORDER_CARD:
        return '[订单卡片]'
      default:
        return (message as BaseMessage).content || ''
    }
  }
}