/**
 * 消息分类相关类型定义
 * 根据新的API文档更新，支持四类消息分类
 */

import type { MessageType, SenderType } from './message'

// 消息分类类型枚举
export enum MessageCategoryType {
  CHAT = 'chat',                    // 聊天消息
  SYSTEM = 'system',                // 系统通知
  ORDER = 'order',                  // 订单消息
  SERVICE = 'service'               // 客服消息
}

// 系统通知子类型
export enum SystemNotificationType {
  SYSTEM_MAINTENANCE = 'system_maintenance',     // 系统维护
  VERSION_UPDATE = 'version_update',             // 版本更新
  SECURITY_NOTICE = 'security_notice',           // 安全公告
  ACTIVITY_NOTICE = 'activity_notice',           // 活动通知
  POLICY_UPDATE = 'policy_update',               // 政策更新
  FEATURE_ANNOUNCEMENT = 'feature_announcement'  // 功能公告
}

// 订单通知子类型
export enum OrderNotificationType {
  PAYMENT_SUCCESS = 'payment_success',           // 支付成功
  PAYMENT_FAILED = 'payment_failed',             // 支付失败
  ORDER_CONFIRMED = 'order_confirmed',           // 订单确认
  ORDER_SHIPPED = 'order_shipped',               // 订单发货
  ORDER_DELIVERED = 'order_delivered',           // 订单送达
  ORDER_COMPLETED = 'order_completed',           // 订单完成
  ORDER_CANCELLED = 'order_cancelled',           // 订单取消
  REFUND_APPLIED = 'refund_applied',             // 退款申请
  REFUND_APPROVED = 'refund_approved',           // 退款通过
  REFUND_REJECTED = 'refund_rejected',           // 退款拒绝
  REFUND_SUCCESS = 'refund_success',             // 退款成功
  REFUND_FAILED = 'refund_failed'                // 退款失败
}

// 客服消息子类型
export enum ServiceMessageType {
  CUSTOMER_INQUIRY = 'customer_inquiry',         // 客户咨询
  AGENT_RESPONSE = 'agent_response',             // 客服回复
  SESSION_ASSIGNED = 'session_assigned',         // 会话分配
  SESSION_TRANSFERRED = 'session_transferred',   // 会话转接
  SESSION_CLOSED = 'session_closed',             // 会话关闭
  SATISFACTION_SURVEY = 'satisfaction_survey',   // 满意度调查
  AUTO_REPLY = 'auto_reply'                      // 自动回复
}

// 消息分类信息接口
export interface MessageCategory {
  type: MessageCategoryType
  title: string
  icon: string
  color: string
  description?: string
  unreadCount: number
  totalCount: number
  lastMessageTime?: string
  enabled: boolean
  sortOrder: number
}

// 消息分类统计信息
export interface MessageCategoryStats {
  categoryType: MessageCategoryType
  totalMessages: number
  unreadMessages: number
  todayMessages: number
  thisWeekMessages: number
  thisMonthMessages: number
  averageResponseTime?: number // 仅客服消息有效
  satisfactionScore?: number   // 仅客服消息有效
  lastUpdated: string
}

// 系统通知消息
export interface SystemNotificationMessage {
  id: number
  type: SystemNotificationType
  title: string
  content: string
  summary?: string
  imageUrl?: string
  actionType?: 'link' | 'modal' | 'page'
  actionUrl?: string
  actionText?: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  isRead: boolean
  isTop: boolean
  publishTime: string
  expireTime?: string
  targetUsers?: number[] // 目标用户ID列表，空表示全员
  tags: string[]
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
}

// 订单通知消息
export interface OrderNotificationMessage {
  id: number
  type: OrderNotificationType
  title: string
  content: string
  orderId: number
  orderNo: string
  amount?: number
  refundAmount?: number
  merchantId: number
  merchantName: string
  goodsInfo?: Array<{
    goodsId: string
    goodsName: string
    goodsImage: string
    quantity: number
    price: number
  }>
  actionType?: 'order_detail' | 'payment' | 'refund' | 'evaluation'
  actionUrl?: string
  actionText?: string
  isRead: boolean
  isTop: boolean
  userId: number
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
}

// 客服消息信息
export interface ServiceMessageInfo {
  id: number
  sessionId: number
  type: ServiceMessageType
  title: string
  content: string
  serviceInfo: {
    serviceId: number
    serviceName: string
    serviceAvatar?: string
    department: string
    isOnline: boolean
    responseTime?: number // 平均响应时间（秒）
  }
  customerInfo: {
    customerId: number
    customerName: string
    customerAvatar?: string
    customerLevel?: string
  }
  lastMessage?: {
    content: string
    time: string
    type: MessageType
    senderId: number
    senderType: SenderType
  }
  unreadCount: number
  isRead: boolean
  isTop: boolean
  priority: 'low' | 'normal' | 'high' | 'urgent'
  status: 'waiting' | 'active' | 'closed' | 'transferred'
  satisfactionScore?: number // 1-5分
  tags: string[]
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
}

// 聊天消息信息（普通聊天）
export interface ChatMessageInfo {
  id: number
  sessionId: number
  title: string
  content: string
  participantInfo: {
    participantId: number
    participantName: string
    participantAvatar?: string
    participantType: SenderType
    isOnline: boolean
    lastSeen?: string
  }
  lastMessage?: {
    content: string
    time: string
    type: MessageType
    senderId: number
    senderType: SenderType
  }
  unreadCount: number
  isRead: boolean
  isTop: boolean
  isMuted: boolean
  isPinned: boolean
  tags: string[]
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
}

// 消息分类项联合类型
export type MessageCategoryItem = 
  | SystemNotificationMessage
  | OrderNotificationMessage
  | ServiceMessageInfo
  | ChatMessageInfo

// 获取消息分类列表请求
export interface GetMessageCategoriesRequest {
  userId?: number
  includeStats?: boolean
  includeDisabled?: boolean
}

// 获取消息分类列表响应
export interface GetMessageCategoriesResponse {
  success: boolean
  data: {
    categories: MessageCategory[]
    stats?: MessageCategoryStats[]
    totalUnread: number
  }
  error?: string
}

// 获取分类消息列表请求
export interface GetCategoryMessagesRequest {
  categoryType: MessageCategoryType
  subType?: string // 子类型，如系统通知的具体类型
  page?: number
  pageSize?: number
  isRead?: boolean
  isTop?: boolean
  startDate?: string
  endDate?: string
  keyword?: string
  tags?: string[]
  sortBy?: 'created_at' | 'updated_at' | 'priority'
  sortOrder?: 'asc' | 'desc'
}

// 获取分类消息列表响应
export interface GetCategoryMessagesResponse {
  success: boolean
  data: {
    messages: MessageCategoryItem[]
    total: number
    page: number
    pageSize: number
    hasMore: boolean
    unreadCount: number
  }
  error?: string
}

// 标记分类消息已读请求
export interface MarkCategoryMessagesReadRequest {
  categoryType: MessageCategoryType
  messageIds?: number[]
  markAll?: boolean
  beforeDate?: string
}

// 标记分类消息已读响应
export interface MarkCategoryMessagesReadResponse {
  success: boolean
  data: {
    markedCount: number
    updatedUnreadCount: number
  }
  error?: string
}

// 删除分类消息请求
export interface DeleteCategoryMessagesRequest {
  categoryType: MessageCategoryType
  messageIds: number[]
}

// 删除分类消息响应
export interface DeleteCategoryMessagesResponse {
  success: boolean
  data: {
    deletedCount: number
  }
  error?: string
}

// 置顶/取消置顶分类消息请求
export interface ToggleCategoryMessageTopRequest {
  categoryType: MessageCategoryType
  messageId: number
  isTop: boolean
}

// 置顶/取消置顶分类消息响应
export interface ToggleCategoryMessageTopResponse {
  success: boolean
  data: {
    messageId: number
    isTop: boolean
  }
  error?: string
}

// 搜索分类消息请求
export interface SearchCategoryMessagesRequest {
  categoryType?: MessageCategoryType
  keyword: string
  subType?: string
  isRead?: boolean
  startDate?: string
  endDate?: string
  page?: number
  pageSize?: number
}

// 搜索分类消息响应
export interface SearchCategoryMessagesResponse {
  success: boolean
  data: {
    messages: MessageCategoryItem[]
    total: number
    page: number
    pageSize: number
    hasMore: boolean
    searchTime: number // 搜索耗时（毫秒）
  }
  error?: string
}

// 消息分类设置
export interface MessageCategorySettings {
  userId: number
  categoryType: MessageCategoryType
  enabled: boolean
  notificationEnabled: boolean
  soundEnabled: boolean
  vibrationEnabled: boolean
  showPreview: boolean
  autoMarkRead: boolean
  autoMarkReadDelay: number // 秒
  maxStorageDays: number // 消息保存天数
  customSettings?: Record<string, any>
  updatedAt: string
}

// 更新消息分类设置请求
export interface UpdateMessageCategorySettingsRequest {
  categoryType: MessageCategoryType
  settings: Partial<Omit<MessageCategorySettings, 'userId' | 'categoryType' | 'updatedAt'>>
}

// 更新消息分类设置响应
export interface UpdateMessageCategorySettingsResponse {
  success: boolean
  data: MessageCategorySettings
  error?: string
}

// ==================== 新API相关类型定义 ====================

// 消息分类DTO（根据新API文档）
export interface MessageCategoryDTO {
  type: MessageCategoryType
  title: string
  icon: string
  color: string
  unreadCount: number  // 匹配后端返回的字段名
  path: string
}

// 未读消息统计DTO
export interface UnreadCountDTO {
  total: number
  categories: {
    chat: number
    system: number
    order: number
    service: number
  }
  conversations: Record<string, number>
}

// 会话DTO
export interface SessionDTO {
  id: number
  type: string
  status?: number  // 🔥 添加状态字段，0=活跃，1=非活跃
  target_name: string
  target_avatar: string
  last_message: {
    id: number
    content: string
    type: string
    created_at: string
  } | null
  unread_count: number
  created_at: string
  updated_at: string
}

// 消息列表项
export interface MessageListItem {
  id: number
  title: string
  content: string
  type: string
  notification_type?: string
  status: number  // 0:未读 1:已读
  created_at: string
  extra_data?: Record<string, any>
}

// 分页响应基础结构
export interface PaginatedResponse<T> {
  list: T[]
  total: number
  page: number
  page_size: number
  page_count: number
}

// 获取消息分类响应（新API）
export interface GetMessageCategoriesNewResponse {
  data: MessageCategoryDTO[]
}

// 获取未读统计响应
export interface GetUnreadCountResponse {
  data: UnreadCountDTO
}

// 获取会话列表响应
export interface GetSessionsResponse {
  data: PaginatedResponse<SessionDTO> & {
    category?: MessageCategoryType
  }
}

// 获取消息列表响应
export interface GetMessagesResponse {
  data: PaginatedResponse<MessageListItem>
}

// 获取会话列表请求参数
export interface GetSessionsRequest {
  category?: MessageCategoryType
  page?: number
  page_size?: number
}

// 获取消息列表请求参数
export interface GetMessagesRequest {
  type: MessageCategoryType
  page?: number
  page_size?: number
  keyword?: string
}

// 消息分类配置
export interface MessageCategoryConfig {
  type: MessageCategoryType
  title: string
  icon: string
  color: string
  path: string
  showSessions: boolean  // 是否显示会话列表（聊天和客服消息）
}

// 默认消息分类配置
export const DEFAULT_MESSAGE_CATEGORIES: MessageCategoryConfig[] = [
  {
    type: MessageCategoryType.CHAT,
    title: '聊天消息',
    icon: 'chat',
    color: '#4D8EFF',
    path: '/pages/chat/sessions/index',
    showSessions: true
  },
  {
    type: MessageCategoryType.SYSTEM,
    title: '系统通知',
    icon: 'notification',
    color: '#FF9500',
    path: '/pages/message/system',
    showSessions: false
  },
  {
    type: MessageCategoryType.ORDER,
    title: '订单消息',
    icon: 'goods',
    color: '#34C759',
    path: '/pages/message/order',
    showSessions: false
  },
  {
    type: MessageCategoryType.SERVICE,
    title: '客服消息',
    icon: 'service',
    color: '#FF3B30',
    path: '/pages/message/service',
    showSessions: true
  }
]