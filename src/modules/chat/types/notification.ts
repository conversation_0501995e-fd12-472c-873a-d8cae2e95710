/**
 * 通知相关类型定义
 */

import type { SenderType } from './message'

// 通知类型
export enum NotificationType {
  // 消息通知
  NEW_MESSAGE = 'new_message',
  MESSAGE_DELIVERED = 'message_delivered',
  MESSAGE_READ = 'message_read',
  
  // 会话通知
  SESSION_CREATED = 'session_created',
  SESSION_ASSIGNED = 'session_assigned',
  SESSION_TRANSFERRED = 'session_transferred',
  SESSION_CLOSED = 'session_closed',
  SESSION_TIMEOUT = 'session_timeout',
  
  // 用户状态通知
  USER_ONLINE = 'user_online',
  USER_OFFLINE = 'user_offline',
  USER_TYPING = 'user_typing',
  
  // 系统通知
  SYSTEM_MAINTENANCE = 'system_maintenance',
  SYSTEM_UPDATE = 'system_update',
  SYSTEM_ANNOUNCEMENT = 'system_announcement',
  
  // 业务通知
  ORDER_UPDATE = 'order_update',
  PAYMENT_SUCCESS = 'payment_success',
  REFUND_PROCESSED = 'refund_processed',
  PRODUCT_INQUIRY = 'product_inquiry',
  
  // 客服通知
  CUSTOMER_WAITING = 'customer_waiting',
  QUEUE_POSITION_CHANGED = 'queue_position_changed',
  AGENT_ASSIGNED = 'agent_assigned',
  AGENT_UNAVAILABLE = 'agent_unavailable',
  
  // 错误通知
  CONNECTION_ERROR = 'connection_error',
  MESSAGE_FAILED = 'message_failed',
  FILE_UPLOAD_FAILED = 'file_upload_failed'
}

// 通知优先级
export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

// 通知状态
export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
  EXPIRED = 'expired'
}

// 通知渠道
export enum NotificationChannel {
  IN_APP = 'in_app',
  PUSH = 'push',
  EMAIL = 'email',
  SMS = 'sms',
  DESKTOP = 'desktop',
  SOUND = 'sound'
}

// 基础通知接口
export interface BaseNotification {
  id: number
  type: NotificationType
  title: string
  content: string
  priority: NotificationPriority
  status: NotificationStatus
  channels: NotificationChannel[]
  recipient_id: number
  recipient_type: SenderType
  sender_id?: number
  sender_type?: SenderType
  data?: Record<string, any>
  action_url?: string
  action_text?: string
  icon?: string
  image?: string
  sound?: string
  vibration?: boolean
  created_at: string
  updated_at: string
  expires_at?: string
  read_at?: string
  delivered_at?: string
}

// 消息通知
export interface MessageNotification extends BaseNotification {
  type: NotificationType.NEW_MESSAGE | NotificationType.MESSAGE_DELIVERED | NotificationType.MESSAGE_READ
  data: {
    message_id: number
    session_id: number
    sender_name: string
    sender_avatar?: string
    message_preview: string
    message_type: string
  }
}

// 会话通知
export interface SessionNotification extends BaseNotification {
  type: NotificationType.SESSION_CREATED | NotificationType.SESSION_ASSIGNED | NotificationType.SESSION_TRANSFERRED | NotificationType.SESSION_CLOSED | NotificationType.SESSION_TIMEOUT
  data: {
    session_id: number
    session_type: string
    customer_name?: string
    agent_name?: string
    transfer_reason?: string
    timeout_duration?: number
  }
}

// 用户状态通知
export interface UserStatusNotification extends BaseNotification {
  type: NotificationType.USER_ONLINE | NotificationType.USER_OFFLINE | NotificationType.USER_TYPING
  data: {
    user_id: number
    user_name: string
    user_avatar?: string
    session_id?: number
    last_seen?: string
  }
}

// 系统通知
export interface SystemNotification extends BaseNotification {
  type: NotificationType.SYSTEM_MAINTENANCE | NotificationType.SYSTEM_UPDATE | NotificationType.SYSTEM_ANNOUNCEMENT
  data: {
    maintenance_start?: string
    maintenance_end?: string
    update_version?: string
    update_features?: string[]
    announcement_category?: string
    affected_services?: string[]
  }
}

// 业务通知
export interface BusinessNotification extends BaseNotification {
  type: NotificationType.ORDER_UPDATE | NotificationType.PAYMENT_SUCCESS | NotificationType.REFUND_PROCESSED | NotificationType.PRODUCT_INQUIRY
  data: {
    order_id?: string
    order_status?: string
    payment_amount?: number
    payment_method?: string
    refund_amount?: number
    refund_reason?: string
    product_id?: number
    product_name?: string
    inquiry_content?: string
  }
}

// 客服通知
export interface CustomerServiceNotification extends BaseNotification {
  type: NotificationType.CUSTOMER_WAITING | NotificationType.QUEUE_POSITION_CHANGED | NotificationType.AGENT_ASSIGNED | NotificationType.AGENT_UNAVAILABLE
  data: {
    queue_position?: number
    estimated_wait_time?: number
    agent_id?: number
    agent_name?: string
    agent_avatar?: string
    unavailable_reason?: string
    alternative_agents?: number[]
  }
}

// 错误通知
export interface ErrorNotification extends BaseNotification {
  type: NotificationType.CONNECTION_ERROR | NotificationType.MESSAGE_FAILED | NotificationType.FILE_UPLOAD_FAILED
  data: {
    error_code: string
    error_message: string
    error_details?: any
    retry_available: boolean
    retry_count?: number
    max_retries?: number
  }
}

// 通知联合类型
export type Notification = 
  | MessageNotification
  | SessionNotification
  | UserStatusNotification
  | SystemNotification
  | BusinessNotification
  | CustomerServiceNotification
  | ErrorNotification

// 通知设置
export interface NotificationSettings {
  user_id: number
  user_type: SenderType
  enabled: boolean
  channels: {
    [key in NotificationChannel]: {
      enabled: boolean
      types: NotificationType[]
      quiet_hours?: {
        enabled: boolean
        start_time: string
        end_time: string
        timezone: string
      }
      sound_settings?: {
        enabled: boolean
        volume: number
        custom_sound?: string
      }
    }
  }
  priority_filter: NotificationPriority[]
  do_not_disturb: {
    enabled: boolean
    start_time?: string
    end_time?: string
    days?: number[]
  }
  grouping: {
    enabled: boolean
    max_group_size: number
    group_timeout: number
  }
  auto_read: {
    enabled: boolean
    delay: number
  }
}

// 通知模板
export interface NotificationTemplate {
  id: number
  type: NotificationType
  name: string
  title_template: string
  content_template: string
  variables: string[]
  default_channels: NotificationChannel[]
  default_priority: NotificationPriority
  icon?: string
  sound?: string
  action_template?: {
    url: string
    text: string
  }
  conditions?: {
    user_types?: SenderType[]
    time_range?: {
      start: string
      end: string
    }
    custom_rules?: Record<string, any>
  }
  created_at: string
  updated_at: string
}

// 通知批量操作
export interface NotificationBatchOperation {
  operation: 'mark_read' | 'mark_unread' | 'delete' | 'archive'
  notification_ids: number[]
  filters?: {
    types?: NotificationType[]
    priorities?: NotificationPriority[]
    date_range?: {
      start: string
      end: string
    }
    sender_ids?: number[]
    read_status?: boolean
  }
}

// 通知统计
export interface NotificationStatistics {
  total_count: number
  unread_count: number
  by_type: Record<NotificationType, number>
  by_priority: Record<NotificationPriority, number>
  by_channel: Record<NotificationChannel, number>
  by_status: Record<NotificationStatus, number>
  today_count: number
  this_week_count: number
  this_month_count: number
  delivery_rate: number
  read_rate: number
  average_read_time: number
}

// 通知队列项
export interface NotificationQueueItem {
  id: string
  notification: Notification
  scheduled_at: string
  attempts: number
  max_attempts: number
  next_retry_at?: string
  error_message?: string
  created_at: string
}

// 通知发送请求
export interface SendNotificationRequest {
  type: NotificationType
  title: string
  content: string
  recipients: {
    user_id: number
    user_type: SenderType
  }[]
  priority?: NotificationPriority
  channels?: NotificationChannel[]
  data?: Record<string, any>
  action_url?: string
  action_text?: string
  icon?: string
  image?: string
  sound?: string
  vibration?: boolean
  expires_at?: string
  scheduled_at?: string
  template_id?: number
  template_variables?: Record<string, any>
}

// 通知发送响应
export interface SendNotificationResponse {
  success: boolean
  notification_ids: number[]
  failed_recipients: {
    user_id: number
    user_type: SenderType
    error: string
  }[]
  scheduled_count: number
  immediate_count: number
}

// 获取通知列表请求
export interface GetNotificationsRequest {
  page?: number
  per_page?: number
  types?: NotificationType[]
  priorities?: NotificationPriority[]
  statuses?: NotificationStatus[]
  channels?: NotificationChannel[]
  read_status?: boolean
  date_range?: {
    start: string
    end: string
  }
  sender_ids?: number[]
  search?: string
  sort_by?: 'created_at' | 'priority' | 'read_at'
  sort_order?: 'asc' | 'desc'
}

// 获取通知列表响应
export interface GetNotificationsResponse {
  notifications: Notification[]
  pagination: {
    current_page: number
    per_page: number
    total: number
    total_pages: number
  }
  statistics: NotificationStatistics
}

// 标记通知已读请求
export interface MarkNotificationReadRequest {
  notification_ids: number[]
  read_all?: boolean
  filters?: {
    types?: NotificationType[]
    before_date?: string
  }
}

// 标记通知已读响应
export interface MarkNotificationReadResponse {
  success: boolean
  marked_count: number
  failed_ids: number[]
}

// 通知偏好设置请求
export interface UpdateNotificationSettingsRequest {
  settings: Partial<NotificationSettings>
}

// 通知偏好设置响应
export interface UpdateNotificationSettingsResponse {
  success: boolean
  settings: NotificationSettings
}