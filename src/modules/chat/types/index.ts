/**
 * 聊天模块类型定义统一导出
 */

// 消息相关类型
export type {
  Message,
  MessageType,
  MessageStatus,
  SendMessageRequest,
  SendMessageResponse,
  GetMessagesRequest,
  GetMessagesResponse,
  MarkMessageReadRequest,
  MarkMessageReadResponse,
  SearchMessagesRequest,
  SearchMessagesResponse,
  MessageStats,
  MessagePreview,
  TypingStatus,
  MessageReceipt,
  FileUploadProgress,
  MessageDraft
} from './message'

export { MessageCategoryUtils } from './message'

// 消息分类相关类型
export type {
  MessageCategory as MessageCategoryType,
  MessageCategoryStats,
  MessageCategoryDTO,
  GetMessageCategoriesRequest,
  GetMessageCategoriesResponse,
  GetMessageCategoriesNewResponse
} from './message-category'

// 会话相关类型
export { SessionType, SessionStatus, SessionPriority } from './session'
export type {
  SessionParticipant,
  SessionSettings,
  FriendChatSession,
  GroupChatSession,
  CustomerServiceSession,
  PresaleConsultationSession,
  AfterSaleServiceSession,
  SystemNotificationSession,
  Session,
  CreateSessionRequest as SessionCreateRequest,
  CreateSessionResponse,
  GetSessionsRequest as SessionsRequest,
  GetSessionsResponse as SessionsResponse,
  UpdateSessionRequest as SessionUpdateRequest,
  UpdateSessionResponse
} from './session'

// 聊天相关类型
export * from './chat'

// WebSocket相关类型
export type {
  WebSocketMessage,
  WebSocketMessageType,
  ChatMessage as WSChatMessage
} from './websocket'

// 通知相关类型
export * from './notification'

// 添加简化的通用类型
export interface User {
  id: number
  name: string
  email?: string
  avatar?: string
  role?: string
  status?: 'online' | 'offline' | 'away' | 'busy'
  created_at: string
  updated_at: string
}

export interface FileUpload {
  id: string
  name: string
  size: number
  type: string
  status: 'pending' | 'uploading' | 'completed' | 'failed' | 'cancelled'
  progress: number
  url?: string
  error?: string
  created_at: string
}

export interface SessionStats {
  total_messages: number
  total_participants: number
  average_response_time: number
  satisfaction_score?: number
  created_at: string
  updated_at: string
}