/**
 * 聊天API接口定义
 * 使用统一的请求工具处理所有HTTP请求
 */

import { get, post, put, del } from '../utils/request'
import type {
  SendMessageRequest,
  SendMessageResponse,
  GetMessagesRequest,
  GetMessagesResponse,
  MarkMessageReadRequest,
  MarkMessageReadResponse,
  SearchMessagesRequest,
  SearchMessagesResponse
} from '../types/message'

import type {
  CreateSessionRequest,
  CreateSessionResponse,
  GetSessionsRequest,
  GetSessionsResponse,
  UpdateSessionRequest,
  UpdateSessionResponse,
  SearchSessionsRequest,
  SearchSessionsResponse
} from '../types/session'

import type {
  SendNotificationRequest,
  SendNotificationResponse,
  GetNotificationsRequest,
  GetNotificationsResponse,
  MarkNotificationReadRequest,
  MarkNotificationReadResponse,
  UpdateNotificationSettingsRequest,
  UpdateNotificationSettingsResponse
} from '../types/notification'

// ==================== 消息相关API ====================

/**
 * 获取消息列表
 */
export async function getMessages(sessionId: number, params?: Partial<GetMessagesRequest>): Promise<GetMessagesResponse> {
  return get<GetMessagesResponse>(`/sessions/${sessionId}/messages`, params)
}

/**
 * 发送文本消息
 */
export async function sendTextMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
  return post<SendMessageResponse>(`/sessions/${request.session_id}/messages`, request)
}

/**
 * 发送媒体消息
 */
export async function sendMediaMessage(
  sessionId: number,
  file: File,
  type: 'image' | 'file' | 'voice' | 'video'
): Promise<SendMessageResponse> {
  const formData = new FormData()
  formData.append('file', file)

  return post<SendMessageResponse>(`/sessions/${sessionId}/messages/media?type=${type}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 发送媒体URL消息
 */
export async function sendMediaUrlMessage(sessionId: number | string, mediaData: {
  resource_id: number | string    // 上传后返回的文件ID
  content: string                 // 媒体文件URL
  message_type: string           // 媒体类型 (image/file等)
  file_name: string              // 文件名称
  file_size: number              // 文件大小
  file_type: string              // 文件MIME类型
  file_ext: string               // 文件扩展名
}): Promise<SendMessageResponse> {
  console.log(`📨 调用媒体URL消息API: /sessions/${sessionId}/messages/media-url`, mediaData)
  return post<SendMessageResponse>(`/sessions/${sessionId}/messages/media-url`, mediaData)
}

/**
 * 标记消息已读
 */
export async function markMessageRead(request: MarkMessageReadRequest): Promise<MarkMessageReadResponse> {
  return post<MarkMessageReadResponse>('/messages/read', request)
}

/**
 * 搜索消息
 */
export async function searchMessages(request: SearchMessagesRequest): Promise<SearchMessagesResponse> {
  return get<SearchMessagesResponse>('/messages/search', request)
}

/**
 * 删除消息
 */
export async function deleteMessage(messageId: number): Promise<void> {
  return del<void>(`/messages/${messageId}`)
}

/**
 * 撤回消息
 */
export async function recallMessage(messageId: number): Promise<void> {
  return post<void>(`/messages/${messageId}/recall`)
}

// ==================== 会话相关API ====================

/**
 * 创建会话
 */
export async function createSession(request: CreateSessionRequest): Promise<CreateSessionResponse> {
  return post<CreateSessionResponse>('/sessions', request)
}

/**
 * 获取会话列表
 */
export async function getSessions(params?: Partial<GetSessionsRequest>): Promise<GetSessionsResponse> {
  console.log('📡 调用会话列表API:', '/sessions', params)
  return get<GetSessionsResponse>('/sessions', params)
}

/**
 * 获取会话详情
 */
export async function getSessionDetail(sessionId: number): Promise<any> {
  return get<any>(`/sessions/${sessionId}`)
}

/**
 * 加入会话
 */
export async function joinSession(sessionId: number, joinData?: {
  receiver_id?: number
  receiver_type?: 'user' | 'merchant'
}): Promise<any> {
  return post<any>(`/sessions/${sessionId}/join`, joinData)
}

/**
 * 标记会话消息为已读
 */
export async function markSessionRead(sessionId: number): Promise<void> {
  return put<void>(`/sessions/${sessionId}/read`)
}

/**
 * 更新会话
 */
export async function updateSession(request: UpdateSessionRequest): Promise<UpdateSessionResponse> {
  return put<UpdateSessionResponse>(`/sessions/${request.session_id}`, request)
}

/**
 * 删除会话
 */
export async function deleteSession(sessionId: number): Promise<void> {
  return del<void>(`/sessions/${sessionId}`)
}

/**
 * 搜索会话
 */
export async function searchSessions(request: SearchSessionsRequest): Promise<SearchSessionsResponse> {
  return get<SearchSessionsResponse>('/sessions/search', request)
}

// ==================== 通知相关API ====================

/**
 * 发送通知
 */
export async function sendNotification(request: SendNotificationRequest): Promise<SendNotificationResponse> {
  return post<SendNotificationResponse>('/notifications', request)
}

/**
 * 获取通知列表
 */
export async function getNotifications(request: GetNotificationsRequest): Promise<GetNotificationsResponse> {
  return get<GetNotificationsResponse>('/notifications', request)
}

/**
 * 标记通知已读
 */
export async function markNotificationRead(request: MarkNotificationReadRequest): Promise<MarkNotificationReadResponse> {
  return post<MarkNotificationReadResponse>('/notifications/read', request)
}

/**
 * 更新通知设置
 */
export async function updateNotificationSettings(request: UpdateNotificationSettingsRequest): Promise<UpdateNotificationSettingsResponse> {
  return put<UpdateNotificationSettingsResponse>('/notification-settings', request)
}

// ==================== 文件上传相关API ====================

/**
 * 上传文件
 */
export async function uploadFile(file: File, sessionId?: number): Promise<any> {
  const formData = new FormData()
  formData.append('file', file)
  if (sessionId) {
    formData.append('session_id', sessionId.toString())
  }

  return post<any>('/files/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取文件下载链接
 */
export async function getFileDownloadUrl(fileId: string): Promise<{ url: string }> {
  return get<{ url: string }>(`/files/${fileId}/download`)
}

// ==================== 用户相关API ====================

/**
 * 获取在线用户列表
 */
export async function getOnlineUsers(): Promise<any[]> {
  return get<any[]>('/users/online')
}

/**
 * 获取用户信息
 */
export async function getUserInfo(userId: number): Promise<any> {
  return get<any>(`/users/${userId}`)
}

// ==================== 统计相关API ====================

/**
 * 获取聊天统计信息
 */
export async function getChatStats(): Promise<any> {
  return get<any>('/stats')
}

/**
 * 获取消息统计信息
 */
export async function getMessageStats(sessionId?: number): Promise<any> {
  const params = sessionId ? { session_id: sessionId } : {}
  return get<any>('/stats/messages', params)
}

// ==================== 消息分类相关API ====================

/**
 * 获取消息分类列表
 */
export async function getMessageCategories(): Promise<any> {
  return get<any>('/message-categories')
}

/**
 * 获取未读消息数量
 * @returns 返回未读消息总数
 */
export async function getUnreadCount(): Promise<any> {
  return get<any>('/unread-count')
}

/**
 * 获取分类会话列表
 * 支持所有消息分类：chat、system、order、service
 */
export async function getCategorySessions(params?: Partial<GetSessionsRequest>): Promise<GetSessionsResponse> {
  // 确保传递必要的参数，支持所有分类
  const requestParams = {
    page: 1,
    page_size: 20,
    category: 'chat',  // 默认为chat，但会被params中的category覆盖
    ...params  // 用户传递的参数会覆盖默认值
  }

  console.log('📡 调用分类会话列表API:', '/sessions', requestParams)
  console.log('🎯 请求分类:', requestParams.category)
  return get<GetSessionsResponse>('/sessions', requestParams)
}

/**
 * 获取分类消息列表
 */
export async function getCategoryMessages(params: {
  type: 'chat' | 'system' | 'order' | 'service'
  page?: number
  page_size?: number
  keyword?: string
}): Promise<any> {
  return get<any>('/messages', params)
}

/**
 * 标记消息为已读
 */
export async function markMessageAsRead(messageId: number): Promise<void> {
  return post<void>(`/messages/${messageId}/read`)
}

/**
 * 标记分类消息为已读
 */
export async function markCategoryAsRead(category: 'chat' | 'system' | 'order' | 'service'): Promise<void> {
  return post<void>(`/categories/${category}/read`)
}

/**
 * 标记会话消息为已读
 */
export async function markSessionAsRead(sessionId: number): Promise<void> {
  return post<void>(`/sessions/${sessionId}/read`)
}

// ==================== WebSocket相关API ====================

/**
 * 获取WebSocket连接token
 */
export async function getWebSocketToken(): Promise<{ token: string }> {
  return get<{ token: string }>('/ws/token')
}

/**
 * 验证WebSocket连接
 */
export async function validateWebSocketConnection(): Promise<{ valid: boolean }> {
  return get<{ valid: boolean }>('/ws/validate')
}

// 创建ChatApi对象，包含所有API函数
export const ChatApi = {
  // 消息相关
  getMessages,
  sendTextMessage,
  sendMediaMessage,
  sendMediaUrlMessage,
  markMessageRead,
  searchMessages,
  deleteMessage,
  recallMessage,
  
  // 会话相关
  createSession,
  getSessions,
  getSessionDetail,
  joinSession,
  markSessionRead,
  updateSession,
  deleteSession,
  searchSessions,
  
  // 通知相关
  sendNotification,
  getNotifications,
  markNotificationRead,
  updateNotificationSettings,
  
  // 文件相关
  uploadFile,
  getFileDownloadUrl,
  
  // 用户相关
  getOnlineUsers,
  getUserInfo,
  
  // 统计相关
  getChatStats,
  getMessageStats,
  
  // 消息分类相关
  getMessageCategories,
  getUnreadCount,
  getCategorySessions,
  getCategoryMessages,
  markMessageAsRead,
  markCategoryAsRead,
  markSessionAsRead,
  
  // WebSocket相关
  getWebSocketToken,
  validateWebSocketConnection
}

// 导出所有API函数
export * from '../types/message'
export * from '../types/session'
export * from '../types/notification'