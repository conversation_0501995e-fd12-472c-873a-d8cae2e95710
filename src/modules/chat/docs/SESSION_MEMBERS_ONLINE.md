# 会话成员在线状态功能实现

## 概述

本文档描述了如何实现WebSocket推送的会话成员在线状态功能，当用户在WebSocket成功连接后，会收到`session_members_online`事件的推送消息，前端需要根据这些状态更新消息会话列表中对应成员的在线状态。

## WebSocket消息格式

用户收到的WebSocket推送消息格式如下：

```json
{
  "type": "notification",
  "event": "session_members_online",
  "session_id": 12,
  "data": {
    "member_count": 1,
    "online_members": [{
      "last_activity": "2025-07-29T17:18:02+08:00",
      "merchant_avatar": "http://omallimg.qwyx.shop/merchant_logo/2025/06/10/1749519350919156000_DZtRIVc0.png",
      "merchant_id": 1,
      "merchant_name": "songda",
      "online_status": "active",
      "user_avatar": "http://omallimg.qwyx.shop/merchant_logo/2025/06/10/1749519350919156000_DZtRIVc0.png",
      "user_id": 1,
      "user_name": "songda",
      "user_type": "merchant"
    }],
    "session_id": 12,
    "timestamp": "2025-07-29T17:18:02+08:00"
  },
  "timestamp": 1753780682
}
```

## 实现架构

### 1. ChatClient层 (chat-client.ts)

在`ChatClient`中添加了对`session_members_online`事件的处理：

```typescript
// 处理通知消息
private handleNotificationMessage(message: NotificationMessage): void {
  // 检查是否是会话成员在线状态通知
  if ((message as any).event === 'session_members_online') {
    this.handleSessionMembersOnlineNotification(message as any)
    return
  }
  
  this.emit(EVENT_NAMES.NOTIFICATION_RECEIVED, notification)
}

// 处理会话成员在线状态通知
private handleSessionMembersOnlineNotification(message: any): void {
  const { data } = message
  
  // 发射会话成员在线状态事件
  this.emit('sessionMembersOnline', {
    sessionId: data.session_id,
    onlineMembers: data.online_members || [],
    memberCount: data.member_count || 0,
    timestamp: data.timestamp || message.timestamp
  })
}
```

### 2. ChatStore层 (chat.ts)

在`ChatStore`中监听`sessionMembersOnline`事件并更新状态：

```typescript
// 监听会话成员在线状态事件
chatClient.on('sessionMembersOnline', (data: any) => {
  console.log('🔧 [ChatStore] 收到会话成员在线状态:', data);
  this.updateSessionMembersOnlineStatus(data)
})

// 更新会话成员在线状态
updateSessionMembersOnlineStatus(data: any) {
  const { sessionId, onlineMembers } = data
  
  // 更新对应会话的在线状态
  const session = this.sessions.find(s => s.id.toString() === sessionId.toString())
  if (session) {
    const hasOnlineMembers = onlineMembers.some((member: any) => member.online_status === 'active')
    ;(session as any).is_online = hasOnlineMembers
  }
  
  // 更新在线用户状态
  onlineMembers.forEach((member: any) => {
    const userId = member.user_id || member.merchant_id
    if (userId) {
      const status = member.online_status === 'active' ? 'online' : 'offline'
      this.onlineUsers.set(userId.toString(), status as UserOnlineStatus)
    }
  })
}
```

### 3. ChatWindow组件层 (ChatWindow.vue)

在`ChatWindow`组件中：

1. **获取会话在线状态**：
```typescript
const getSessionOnlineStatus = (session: any) => {
  if (!session) return 'offline'
  
  // 首先检查session自身的is_online属性
  if (session.is_online !== undefined) {
    return session.is_online ? 'online' : 'offline'
  }
  
  return 'offline'
}
```

2. **监听状态变化**：
```typescript
// 监听会话在线状态变化
watch(
  () => sessions.value.map(s => ({ id: s.id, is_online: (s as any).is_online })),
  (newStatuses, oldStatuses) => {
    if (oldStatuses && newStatuses.length === oldStatuses.length) {
      const changes = newStatuses.filter((newStatus, index) => {
        const oldStatus = oldStatuses[index]
        return oldStatus && newStatus.is_online !== oldStatus.is_online
      })
      
      if (changes.length > 0) {
        console.log('🔄 检测到会话在线状态变化:', changes)
      }
    }
  },
  { deep: true }
)
```

3. **UI显示**：
```vue
<!-- 在线状态指示器 -->
<div class="session-status-dot"
     :class="`session-status-dot--${getSessionOnlineStatus(session)}`">
</div>
```

## CSS样式

在线状态指示器的样式：

```css
.session-status-dot {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.session-status-dot--online {
  background-color: #10b981; /* 绿色表示在线 */
}

.session-status-dot--offline {
  background-color: #6b7280; /* 灰色表示离线 */
}
```

## 数据流

1. **WebSocket接收消息** → `ChatClient.handleWebSocketMessage()`
2. **解析通知消息** → `ChatClient.handleNotificationMessage()`
3. **处理在线状态** → `ChatClient.handleSessionMembersOnlineNotification()`
4. **发射事件** → `chatClient.emit('sessionMembersOnline', data)`
5. **Store监听** → `ChatStore.updateSessionMembersOnlineStatus()`
6. **更新状态** → 更新`session.is_online`和`onlineUsers`
7. **UI响应** → Vue响应式系统自动更新界面

## 测试

创建了测试文件`session-members-online.test.ts`来验证功能：

- ChatClient事件处理测试
- ChatStore状态更新测试
- 在线/离线状态切换测试

## 使用说明

1. 确保WebSocket连接正常
2. 当收到`session_members_online`事件时，会话列表中的在线状态指示器会自动更新
3. 绿色圆点表示有成员在线，灰色圆点表示所有成员离线
4. 状态更新是实时的，无需手动刷新

## 注意事项

1. 在线状态基于`online_status === 'active'`判断
2. 支持多种用户类型（merchant、user等）
3. 状态更新会同时影响会话列表和在线用户列表
4. 使用Vue的响应式系统确保UI自动更新
