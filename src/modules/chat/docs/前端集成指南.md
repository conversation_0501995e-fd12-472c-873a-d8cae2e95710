# 消息分类系统前端集成指南

## 概述

本指南详细说明如何在前端项目中集成消息分类系统，实现四类消息的完整功能：聊天消息、系统通知、订单消息、客服消息。

## 技术架构

### 1. 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端页面层     │    │   API接口层      │    │   后端服务层     │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ 消息中心页面     │◄──►│ 消息分类API      │◄──►│ MessageCategory │
│ 聊天会话页面     │    │ 会话管理API      │    │ Service         │
│ 通知列表页面     │    │ 消息管理API      │    │ ChatService     │
│ 订单消息页面     │    │ WebSocket API   │    │ CacheService    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 数据流向

```
用户操作 → 前端组件 → API调用 → 后端处理 → 数据库操作
    ↑                                              ↓
WebSocket推送 ← 实时通知 ← 缓存更新 ← 业务逻辑处理
```

## 核心组件设计

### 1. 消息中心主组件

```vue
<template>
  <div class="message-center">
    <!-- 分类标签页 -->
    <div class="category-tabs">
      <div 
        v-for="category in categories" 
        :key="category.type"
        :class="['tab-item', { active: activeCategory === category.type }]"
        @click="switchCategory(category.type)"
      >
        <icon :name="category.icon" :color="category.color" />
        <span class="title">{{ category.title }}</span>
        <badge v-if="category.unread_count > 0" :count="category.unread_count" />
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 聊天消息和客服消息显示会话列表 -->
      <session-list 
        v-if="isSessionCategory(activeCategory)"
        :category="activeCategory"
        :sessions="sessions"
        @session-click="handleSessionClick"
      />
      
      <!-- 系统通知和订单消息显示消息列表 -->
      <message-list 
        v-else
        :category="activeCategory"
        :messages="messages"
        @message-click="handleMessageClick"
      />
    </div>
  </div>
</template>

<script>
import { MessageCenterAPI } from '@/api/message-center'
import { WebSocketManager } from '@/utils/websocket'

export default {
  name: 'MessageCenter',
  data() {
    return {
      categories: [],
      activeCategory: 'chat',
      sessions: [],
      messages: [],
      loading: false,
      wsManager: null
    }
  },
  
  async created() {
    await this.initializeData()
    this.setupWebSocket()
  },
  
  methods: {
    // 初始化数据
    async initializeData() {
      try {
        // 加载消息分类
        const categoriesResult = await MessageCenterAPI.getCategories()
        this.categories = categoriesResult.data
        
        // 加载默认分类数据
        await this.loadCategoryData(this.activeCategory)
      } catch (error) {
        this.$message.error('初始化失败：' + error.message)
      }
    },
    
    // 设置WebSocket连接
    setupWebSocket() {
      this.wsManager = new WebSocketManager()
      this.wsManager.connect()
      
      // 监听消息事件
      this.wsManager.on('new_message', this.handleNewMessage)
      this.wsManager.on('message_read', this.handleMessageRead)
      this.wsManager.on('unread_count_updated', this.updateUnreadCount)
    },
    
    // 切换分类
    async switchCategory(category) {
      if (this.activeCategory === category) return
      
      this.activeCategory = category
      await this.loadCategoryData(category)
    },
    
    // 加载分类数据
    async loadCategoryData(category) {
      this.loading = true
      try {
        if (this.isSessionCategory(category)) {
          // 加载会话列表
          const result = await MessageCenterAPI.getSessions({ category })
          this.sessions = result.data.list
        } else {
          // 加载消息列表
          const result = await MessageCenterAPI.getMessages({ type: category })
          this.messages = result.data.list
        }
      } catch (error) {
        this.$message.error('加载数据失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    // 判断是否为会话类型分类
    isSessionCategory(category) {
      return ['chat', 'service'].includes(category)
    },
    
    // 处理新消息
    handleNewMessage(data) {
      // 更新未读统计
      this.updateCategoryUnreadCount(data.category, 1)
      
      // 如果是当前分类，更新列表
      if (data.category === this.activeCategory) {
        this.refreshCurrentData()
      }
      
      // 显示通知
      this.showNotification(data)
    },
    
    // 处理消息已读
    handleMessageRead(data) {
      this.updateCategoryUnreadCount(data.category, -1)
    },
    
    // 更新分类未读数量
    updateCategoryUnreadCount(categoryType, delta) {
      const category = this.categories.find(c => c.type === categoryType)
      if (category) {
        category.unread_count = Math.max(0, category.unread_count + delta)
      }
    },
    
    // 刷新当前数据
    async refreshCurrentData() {
      await this.loadCategoryData(this.activeCategory)
    },
    
    // 显示通知
    showNotification(data) {
      if (Notification.permission === 'granted') {
        new Notification(data.title || '新消息', {
          body: data.content,
          icon: '/static/icons/message.png'
        })
      }
    }
  }
}
</script>
```

### 2. 会话列表组件

```vue
<template>
  <div class="session-list">
    <div 
      v-for="session in sessions" 
      :key="session.id"
      :class="['session-item', { unread: session.unread_count > 0 }]"
      @click="$emit('session-click', session)"
    >
      <avatar :src="session.target_avatar" :name="session.target_name" />
      
      <div class="session-info">
        <div class="header">
          <span class="name">{{ session.target_name }}</span>
          <span class="time">{{ formatTime(session.updated_at) }}</span>
        </div>
        
        <div class="content">
          <span class="last-message">{{ getLastMessageText(session.last_message) }}</span>
          <badge v-if="session.unread_count > 0" :count="session.unread_count" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SessionList',
  props: {
    category: String,
    sessions: Array
  },
  
  methods: {
    formatTime(time) {
      // 时间格式化逻辑
      return new Date(time).toLocaleString()
    },
    
    getLastMessageText(message) {
      if (!message) return '暂无消息'
      
      switch (message.type) {
        case 'text':
          return message.content
        case 'image':
          return '[图片]'
        case 'file':
          return '[文件]'
        default:
          return '[消息]'
      }
    }
  }
}
</script>
```

### 3. 消息列表组件

```vue
<template>
  <div class="message-list">
    <div 
      v-for="message in messages" 
      :key="message.id"
      :class="['message-item', { unread: message.status === 0 }]"
      @click="handleMessageClick(message)"
    >
      <div class="message-header">
        <span class="title">{{ message.title }}</span>
        <span class="time">{{ formatTime(message.created_at) }}</span>
      </div>
      
      <div class="message-content">
        {{ message.content }}
      </div>
      
      <div v-if="message.extra_data" class="message-extra">
        <!-- 根据消息类型显示不同的扩展信息 -->
        <order-info v-if="isOrderMessage(message)" :data="message.extra_data" />
        <system-info v-else-if="isSystemMessage(message)" :data="message.extra_data" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MessageList',
  props: {
    category: String,
    messages: Array
  },
  
  methods: {
    handleMessageClick(message) {
      // 标记为已读
      if (message.status === 0) {
        this.markAsRead(message.id)
      }
      
      // 触发点击事件
      this.$emit('message-click', message)
    },
    
    async markAsRead(messageId) {
      try {
        await MessageCenterAPI.markMessageAsRead(messageId)
        // 更新本地状态
        const message = this.messages.find(m => m.id === messageId)
        if (message) {
          message.status = 1
        }
      } catch (error) {
        console.error('标记已读失败:', error)
      }
    },
    
    isOrderMessage(message) {
      return message.notification_type && message.notification_type.startsWith('order_')
    },
    
    isSystemMessage(message) {
      return message.notification_type && message.notification_type.startsWith('system_')
    },
    
    formatTime(time) {
      return new Date(time).toLocaleString()
    }
  }
}
</script>
```

## API封装

### 1. API服务类

```javascript
// api/message-center.js
import request from '@/utils/request'

export class MessageCenterAPI {
  // 获取消息分类列表
  static async getCategories() {
    return await request.get('/api/v1/chat/message-categories')
  }
  
  // 获取未读消息统计
  static async getUnreadCount() {
    return await request.get('/api/v1/chat/unread-count')
  }
  
  // 获取会话列表
  static async getSessions(params = {}) {
    return await request.get('/api/v1/chat/sessions', { params })
  }
  
  // 获取消息列表
  static async getMessages(params = {}) {
    return await request.get('/api/v1/chat/messages', { params })
  }
  
  // 标记消息为已读
  static async markMessageAsRead(messageId) {
    return await request.post(`/api/v1/chat/messages/${messageId}/read`)
  }
  
  // 标记分类为已读
  static async markCategoryAsRead(category) {
    return await request.post(`/api/v1/chat/categories/${category}/read`)
  }
  
  // 标记会话为已读
  static async markSessionAsRead(sessionId) {
    return await request.post(`/api/v1/chat/sessions/${sessionId}/read`)
  }
}
```

### 2. WebSocket管理器

```javascript
// utils/websocket.js
export class WebSocketManager {
  constructor() {
    this.ws = null
    this.listeners = new Map()
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
  }
  
  connect() {
    try {
      this.ws = new WebSocket(process.env.VUE_APP_WS_URL)
      
      this.ws.onopen = () => {
        console.log('WebSocket连接成功')
        this.reconnectAttempts = 0
      }
      
      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data)
        this.handleMessage(data)
      }
      
      this.ws.onclose = () => {
        console.log('WebSocket连接关闭')
        this.reconnect()
      }
      
      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error)
      }
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.reconnect()
    }
  }
  
  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect()
      }, this.reconnectInterval)
    }
  }
  
  handleMessage(data) {
    const listeners = this.listeners.get(data.type) || []
    listeners.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error('WebSocket消息处理错误:', error)
      }
    })
  }
  
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }
  
  off(event, callback) {
    const listeners = this.listeners.get(event) || []
    const index = listeners.indexOf(callback)
    if (index > -1) {
      listeners.splice(index, 1)
    }
  }
  
  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data))
    }
  }
  
  close() {
    if (this.ws) {
      this.ws.close()
    }
  }
}
```

## 状态管理

### 1. Vuex Store模块

```javascript
// store/modules/message-center.js
import { MessageCenterAPI } from '@/api/message-center'

const state = {
  categories: [],
  unreadCount: {
    total: 0,
    categories: {},
    conversations: {}
  },
  currentCategory: 'chat',
  sessions: [],
  messages: []
}

const mutations = {
  SET_CATEGORIES(state, categories) {
    state.categories = categories
  },
  
  SET_UNREAD_COUNT(state, unreadCount) {
    state.unreadCount = unreadCount
  },
  
  SET_CURRENT_CATEGORY(state, category) {
    state.currentCategory = category
  },
  
  SET_SESSIONS(state, sessions) {
    state.sessions = sessions
  },
  
  SET_MESSAGES(state, messages) {
    state.messages = messages
  },
  
  UPDATE_CATEGORY_UNREAD(state, { category, delta }) {
    const categoryItem = state.categories.find(c => c.type === category)
    if (categoryItem) {
      categoryItem.unread_count = Math.max(0, categoryItem.unread_count + delta)
    }
    
    state.unreadCount.categories[category] = Math.max(0, 
      (state.unreadCount.categories[category] || 0) + delta)
    
    state.unreadCount.total = Math.max(0, state.unreadCount.total + delta)
  }
}

const actions = {
  async loadCategories({ commit }) {
    try {
      const result = await MessageCenterAPI.getCategories()
      commit('SET_CATEGORIES', result.data)
      return result.data
    } catch (error) {
      console.error('加载消息分类失败:', error)
      throw error
    }
  },
  
  async loadUnreadCount({ commit }) {
    try {
      const result = await MessageCenterAPI.getUnreadCount()
      commit('SET_UNREAD_COUNT', result.data)
      return result.data
    } catch (error) {
      console.error('加载未读统计失败:', error)
      throw error
    }
  },
  
  async switchCategory({ commit, dispatch }, category) {
    commit('SET_CURRENT_CATEGORY', category)
    
    if (['chat', 'service'].includes(category)) {
      await dispatch('loadSessions', { category })
    } else {
      await dispatch('loadMessages', { type: category })
    }
  },
  
  async loadSessions({ commit }, params) {
    try {
      const result = await MessageCenterAPI.getSessions(params)
      commit('SET_SESSIONS', result.data.list)
      return result.data
    } catch (error) {
      console.error('加载会话列表失败:', error)
      throw error
    }
  },
  
  async loadMessages({ commit }, params) {
    try {
      const result = await MessageCenterAPI.getMessages(params)
      commit('SET_MESSAGES', result.data.list)
      return result.data
    } catch (error) {
      console.error('加载消息列表失败:', error)
      throw error
    }
  }
}

const getters = {
  totalUnreadCount: state => state.unreadCount.total,
  getCategoryUnreadCount: state => category => state.unreadCount.categories[category] || 0,
  isSessionCategory: () => category => ['chat', 'service'].includes(category)
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
```

## 部署和配置

### 1. 环境配置

```javascript
// .env.development
VUE_APP_API_BASE_URL=http://localhost:8080/api/v1
VUE_APP_WS_URL=ws://localhost:8080/ws

// .env.production
VUE_APP_API_BASE_URL=https://api.example.com/api/v1
VUE_APP_WS_URL=wss://api.example.com/ws
```

### 2. 路由配置

```javascript
// router/index.js
const routes = [
  {
    path: '/message-center',
    name: 'MessageCenter',
    component: () => import('@/views/MessageCenter.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/chat/:sessionId',
    name: 'ChatSession',
    component: () => import('@/views/ChatSession.vue'),
    meta: { requiresAuth: true }
  }
]
```

## 性能优化建议

1. **虚拟滚动**: 对于大量消息列表，使用虚拟滚动组件
2. **懒加载**: 图片和文件采用懒加载策略
3. **缓存策略**: 合理使用本地缓存减少API调用
4. **防抖节流**: 对搜索和滚动事件进行防抖处理
5. **组件复用**: 提取公共组件避免重复渲染

## 测试建议

1. **单元测试**: 对核心组件和API服务进行单元测试
2. **集成测试**: 测试WebSocket连接和消息推送功能
3. **端到端测试**: 测试完整的用户操作流程
4. **性能测试**: 测试大量数据下的页面性能

这个集成指南提供了完整的前端实现方案，包括组件设计、API封装、状态管理等各个方面，可以帮助前端开发者快速集成消息分类功能。
