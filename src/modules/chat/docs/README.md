# 聊天模块文档

## 模块概述

聊天模块提供了用户间及用户与商家间的即时通信服务，支持文本、图片、文件和语音消息。该模块采用WebSocket实现实时通讯，并集成JWT认证保证通信安全。

## 目录结构

```
chat/
├── controllers/          # 控制器，处理API请求
│   ├── blacklist_controller.go  # 黑名单控制器
│   ├── friend_controller.go     # 好友关系控制器
│   ├── group_controller.go      # 群组控制器
│   ├── group_member_controller.go # 群组成员控制器
│   ├── message_controller.go    # 消息控制器
│   ├── session_controller.go    # 会话控制器
│   └── websocket_controller.go  # WebSocket连接控制器
├── dto/                 # 数据传输对象
│   ├── blacklist_dto.go # 黑名单相关DTO
│   ├── chat_dto.go      # 聊天相关DTO定义
│   ├── friend_dto.go    # 好友关系相关DTO
│   └── group_dto.go     # 群组相关DTO
├── middlewares/         # 中间件
│   └── jwt_middleware.go  # JWT认证中间件
├── models/              # 数据模型
│   ├── blacklist.go     # 黑名单模型
│   ├── chat_message.go  # 消息模型
│   ├── chat_session.go  # 会话模型
│   ├── friend.go        # 好友关系模型
│   ├── friend_request.go # 好友请求模型
│   ├── group.go         # 群组模型
│   └── group_member.go  # 群组成员模型
├── repositories/        # 数据访问层
│   ├── chat_repository.go  # 聊天数据仓库接口
│   └── impl/            # 接口实现
│       └── chat_repository_impl.go  # 聊天数据仓库实现
├── services/            # 业务逻辑层
│   ├── blacklist_service.go  # 黑名单服务接口
│   ├── chat_service.go  # 聊天服务接口
│   ├── friend_service.go # 好友服务接口
│   ├── group_service.go  # 群组服务接口
│   ├── websocket_manager.go  # WebSocket连接管理器
│   └── impl/            # 接口实现
│       ├── blacklist_service_impl.go  # 黑名单服务实现
│       ├── chat_service_impl.go  # 聊天服务实现
│       ├── friend_service_impl.go  # 好友服务实现
│       └── group_service_impl.go  # 群组服务实现
├── routers/             # 路由配置
│   └── router.go        # 聊天模块路由
├── docs/                # 文档
└── init.go              # 模块初始化
```

## 核心功能

1.  **会话管理**: 创建、查询、更新和删除聊天会话。
2.  **消息收发**: 发送和接收文本、媒体消息，以及获取历史消息。
3.  **好友关系管理**: 发送和处理好友请求，管理好友列表。
4.  **群组管理**: 创建、管理群组及群成员。
5.  **黑名单管理**: 支持将用户加入或移出黑名单。
6.  **WebSocket通信**: 实时消息推送、状态通知和事件广播。

## 功能集成指南

为了方便前端开发者集成，我们为主要功能提供了详细的集成指南，包含完整的API说明和代码示例：

- **[好友关系管理指南](./friend-management-guide.md)**
- **[群组与群成员管理指南](./group-management-guide.md)**
- **[黑名单管理指南](./blacklist-management-guide.md)**

## API 接口概览

以下是模块中核心功能的API接口概览。详细使用方法请参考上方的功能集成指南。

### WebSocket连接

| 方法 | 路径 | 描述 |
| --- | --- | --- |
| GET | `/api/v1/chat/ws` | 建立WebSocket连接 |

### 会话与消息接口

| 方法 | 路径 | 描述 |
| --- | --- | --- |
| POST | `/api/v1/chat/sessions` | 创建聊天会话 |
| GET | `/api/v1/chat/sessions` | 获取会话列表 |
| GET | `/api/v1/chat/sessions/:id` | 获取单个会话详情 |
| PUT | `/api/v1/chat/sessions/:id/read` | 标记会话消息为已读 |
| GET | `/api/v1/chat/sessions/:session_id/messages` | 获取会话消息列表 |
| POST | `/api/v1/chat/sessions/:session_id/messages/text` | 发送文本消息 |
| POST | `/api/v1/chat/sessions/:session_id/messages/media` | 发送媒体消息 |

## 数据模型

### 会话模型 (ChatSession)

```go
// ChatSession 聊天会话模型
type ChatSession struct {
    ID            int64     // 会话ID
    Type          string    // 会话类型
    CreatorID     int64     // 创建者ID
    CreatorType   string    // 创建者类型
    ReceiverID    int64     // 接收者ID
    ReceiverType  string    // 接收者类型
    LastMessageID int64     // 最后一条消息ID
    UnreadCount   int       // 未读消息计数
    Status        int       // 状态（0:正常, 1:已关闭）
    CreatedAt     time.Time // 创建时间
    UpdatedAt     time.Time // 更新时间
}
```

### 消息模型 (ChatMessage)

```go
// ChatMessage 聊天消息模型
type ChatMessage struct {
    ID         int64     // 消息ID
    SessionID  int64     // 会话ID
    SenderID   int64     // 发送者ID
    SenderType string    // 发送者类型
    Content    string    // 消息内容
    Type       string    // 消息类型（text/image/file/voice）
    ResourceID string    // 资源ID
    Status     int       // 状态（0:未读, 1:已读）
    CreatedAt  time.Time // 创建时间
    UpdatedAt  time.Time // 更新时间
}
```

### 群组模型 (Group)

```go
// Group 群组模型
type Group struct {
    ID          int64     // 群组ID
    Name        string    // 群组名称
    Avatar      string    // 群组头像
    Description string    // 群组描述
    OwnerID     int64     // 群主ID
    OwnerType   string    // 群主类型
    MemberCount int       // 成员数量
    Status      int       // 状态（0:正常，1:已解散）
    CreatedAt   time.Time // 创建时间
    UpdatedAt   time.Time // 更新时间
}
```

### 群组成员模型 (GroupMember)

```go
// GroupMember 群组成员模型
type GroupMember struct {
    ID        int64     // 记录ID
    GroupID   int64     // 群组ID
    UserID    int64     // 用户ID
    UserType  string    // 用户类型
    Nickname  string    // 群内昵称
    Role      int       // 角色（0:普通成员，1:管理员，2:群主）
    Status    int       // 状态（0:正常，1:禁言）
    JoinTime  time.Time // 加入时间
    CreatedAt time.Time // 创建时间
    UpdatedAt time.Time // 更新时间
}
```

### 好友关系模型 (Friend)

```go
// Friend 好友关系模型
type Friend struct {
    ID          int64     // 好友ID
    UserID      int64     // 用户ID
    UserType    string    // 用户类型
    FriendID    int64     // 好友ID
    FriendType  string    // 好友类型
    Remark      string    // 备注名
    Status      int       // 状态（0:正常，1:特别关注，2:星标）
    CreatedAt   time.Time // 创建时间
    UpdatedAt   time.Time // 更新时间
}
```

### 好友请求模型 (FriendRequest)

```go
// FriendRequest 好友请求模型
type FriendRequest struct {
    ID           int64     // 请求ID
    SenderID     int64     // 发送者ID
    SenderType   string    // 发送者类型
    ReceiverID   int64     // 接收者ID
    ReceiverType string    // 接收者类型
    Message      string    // 附加消息
    Status       int       // 状态（0:未处理，1:已同意，2:已拒绝）
    CreatedAt    time.Time // 创建时间
    UpdatedAt    time.Time // 更新时间
}
```

### 黑名单模型 (Blacklist)

```go
// Blacklist 黑名单模型
type Blacklist struct {
    ID           int64     // 黑名单ID
    UserID       int64     // 用户ID
    UserType     string    // 用户类型
    BlockedID    int64     // 被拉黑用户ID
    BlockedType  string    // 被拉黑用户类型
    Reason       string    // 拉黑原因
    CreatedAt    time.Time // 创建时间
    UpdatedAt    time.Time // 更新时间
}
```

## WebSocket消息协议

### 客户端连接

客户端通过以下URL建立WebSocket连接：

```
ws://{服务器地址}/api/v1/chat/ws?token={JWT令牌}
```

或者使用Authorization头部：

```
ws://{服务器地址}/api/v1/chat/ws
Authorization: Bearer {JWT令牌}
```

### 消息格式

服务器发送的WebSocket消息遵循统一的JSON结构，具体格式请参考 `websocket_controller.go` 和相关服务实现。

```json
{
  "type": "notification",
  "event": "group_member_left",    // 成员离开
  "group_id": 456,
  "data": {
    "member_id": 789,
    "member_type": "user",
    "is_kicked": false          // 是否被踢出
  },
  "timestamp": 1623479123
}
```

```json
{
  "type": "notification",
  "event": "group_owner_transferred", // 群主转让
  "group_id": 456,
  "data": {
    "old_owner_id": 123,
    "old_owner_type": "user",
    "new_owner_id": 789,
    "new_owner_type": "user"
  },
  "timestamp": 1623479123
}
```

##### 好友相关 (type="notification")

```json
{
  "type": "notification",
  "event": "friend_request_received", // 收到好友请求
  "data": {
    "request_id": 123,
    "sender_id": 456,
    "sender_type": "user",
    "message": "我是你的老同学",
    "created_at": "2023-05-20T15:30:45Z"
  },
  "timestamp": 1623479123
}
```

```json
{
  "type": "notification",
  "event": "friend_request_accepted", // 好友请求被接受
  "data": {
    "request_id": 123,
    "friend_id": 456,
    "friend_type": "user",
    "friend_name": "张三"
  },
  "timestamp": 1623479123
}
```

##### 黑名单相关 (type="notification")

```json
{
  "type": "notification",
  "event": "user_blocked",     // 用户被拉黑
  "data": {
    "blocked_id": 456,
    "blocked_type": "user"
  },
  "timestamp": 1623479123
}
```

##### 心跳消息 (type="heartbeat")

```json
{
  "type": "heartbeat",
  "event": "ping",           // 服务器发送ping
  "timestamp": 1623479123
}
```

```json
{
  "type": "heartbeat",
  "event": "pong",           // 客户端响应pong
  "timestamp": 1623479123
}
```

## 前端应用示例

### 初始化WebSocket连接

```javascript
// 连接WebSocket
function connectWebSocket(token) {
  const wsUrl = `ws://api.example.com/api/v1/chat/ws?token=${token}`;
  const socket = new WebSocket(wsUrl);
  
  socket.onopen = () => {
    console.log('WebSocket连接已建立');
  };
  
  socket.onmessage = (event) => {
    const message = JSON.parse(event.data);
    handleWebSocketMessage(message);
  };
  
  socket.onclose = () => {
    console.log('WebSocket连接已关闭');
    // 可以实现重连逻辑
    setTimeout(() => connectWebSocket(token), 3000);
  };
  
  socket.onerror = (error) => {
    console.error('WebSocket错误:', error);
  };
  
  return socket;
}
```

### 处理接收到的消息

```javascript
function handleWebSocketMessage(message) {
  switch(message.type) {
    case 'message':
      if (message.event === 'new_message') {
        // 添加新消息到UI
        addMessageToChat(message.session_id, message.data);
        // 更新未读消息数量
        updateUnreadCount(message.session_id);
      } else if (message.event === 'message_read') {
        // 更新消息已读状态
        updateMessagesReadStatus(message.session_id);
      }
      break;
    
    case 'notification':
      // 处理通知
      showNotification(message.data);
      break;
      
    case 'heartbeat':
      // 响应心跳
      sendHeartbeatResponse();
      break;
  }
}
```

### 获取会话列表

```javascript
async function fetchSessions() {
  try {
    const response = await fetch('/api/v1/chat/sessions', {
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`
      }
    });
    
    if (!response.ok) {
      throw new Error('获取会话列表失败');
    }
    
    const result = await response.json();
    renderSessionList(result.data.sessions);
  } catch (error) {
    console.error('获取会话失败:', error);
  }
}
```

### 发送文本消息

```javascript
async function sendTextMessage(sessionId, content) {
  try {
    const response = await fetch(`/api/v1/chat/sessions/${sessionId}/messages/text`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getAuthToken()}`
      },
      body: JSON.stringify({ content })
    });
    
    if (!response.ok) {
      throw new Error('发送消息失败');
    }
    
    const result = await response.json();
    // 立即在UI中展示已发送的消息
    addMessageToChat(sessionId, result.data.message, true);
  } catch (error) {
    console.error('发送消息失败:', error);
  }
}
```

### 上传和发送媒体消息

```javascript
async function sendMediaMessage(sessionId, file, type) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', type); // image, file, voice
  
  try {
    const response = await fetch(`/api/v1/chat/sessions/${sessionId}/messages/media`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`
      },
      body: formData
    });
    
    if (!response.ok) {
      throw new Error('发送媒体消息失败');
    }
    
    const result = await response.json();
    // 立即在UI中展示已发送的媒体消息
    addMediaMessageToChat(sessionId, result.data.message, true);
  } catch (error) {
    console.error('发送媒体消息失败:', error);
  }
}
```

### 完整聊天UI组件示例 (Vue.js)

```vue
<template>
  <div class="chat-container">
    <!-- 会话列表 -->
    <div class="sessions-list">
      <div v-for="session in sessions" :key="session.id" 
           @click="selectSession(session)"
           :class="{'active': currentSession && currentSession.id === session.id}">
        <div class="session-avatar">
          <img :src="session.targetAvatar || '/default-avatar.png'" alt="头像">
        </div>
        <div class="session-info">
          <div class="session-name">{{ session.targetName }}</div>
          <div class="session-last-message">{{ getLastMessagePreview(session) }}</div>
        </div>
        <div class="session-meta">
          <div class="session-time">{{ formatTime(session.updatedAt) }}</div>
          <div v-if="session.unreadCount > 0" class="unread-badge">
            {{ session.unreadCount }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 聊天窗口 -->
    <div class="chat-window" v-if="currentSession">
      <div class="chat-header">
        <h3>{{ currentSession.targetName }}</h3>
      </div>
      
      <div class="messages-container" ref="messagesContainer">
        <div v-for="message in messages" :key="message.id" 
             :class="['message', {'own-message': isOwnMessage(message)}]">
          <div class="message-avatar">
            <img :src="getMessage Avatar(message)" alt="头像">
          </div>
          <div class="message-content">
            <!-- 文本消息 -->
            <div v-if="message.type === 'text'" class="text-message">
              {{ message.content }}
            </div>
            
            <!-- 图片消息 -->
            <div v-else-if="message.type === 'image'" class="image-message">
              <img :src="getMediaUrl(message.resourceId)" @click="previewImage(message)">
            </div>
            
            <!-- 文件消息 -->
            <div v-else-if="message.type === 'file'" class="file-message">
              <a :href="getMediaUrl(message.resourceId)" download>
                <i class="file-icon"></i>
                <span>文件下载</span>
              </a>
            </div>
            
            <!-- 语音消息 -->
            <div v-else-if="message.type === 'voice'" class="voice-message" @click="playVoice(message)">
              <i class="voice-icon"></i>
              <span>语音消息</span>
            </div>
          </div>
          <div class="message-time">
            {{ formatMessageTime(message.createdAt) }}
          </div>
        </div>
      </div>
      
      <div class="chat-input">
        <div class="input-tools">
          <button @click="showEmojiPicker = !showEmojiPicker">😊</button>
          <button @click="triggerFileUpload('image')">🖼️</button>
          <button @click="triggerFileUpload('file')">📎</button>
          <button @click="startVoiceRecord" 
                  :class="{'recording': isRecording}">🎤</button>
        </div>
        
        <textarea v-model="inputMessage" 
                  @keydown.enter.prevent="sendMessage"
                  placeholder="输入消息..."></textarea>
        
        <button class="send-button" @click="sendMessage">发送</button>
        
        <input type="file" ref="imageInput" 
               style="display: none" 
               @change="handleFileSelected('image')" 
               accept="image/*">
               
        <input type="file" ref="fileInput" 
               style="display: none" 
               @change="handleFileSelected('file')">
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      sessions: [],
      currentSession: null,
      messages: [],
      inputMessage: '',
      socket: null,
      showEmojiPicker: false,
      isRecording: false,
      page: 1,
      pageSize: 20,
      hasMoreMessages: true
    };
  },
  
  mounted() {
    this.initWebSocket();
    this.fetchSessions();
  },
  
  methods: {
    // WebSocket连接管理
    initWebSocket() {
      const token = this.$store.getters.token;
      const wsUrl = `ws://${window.location.host}/api/v1/chat/ws?token=${token}`;
      this.socket = new WebSocket(wsUrl);
      
      this.socket.onopen = this.handleWebSocketOpen;
      this.socket.onmessage = this.handleWebSocketMessage;
      this.socket.onclose = this.handleWebSocketClose;
      this.socket.onerror = this.handleWebSocketError;
    },
    
    // 获取会话列表
    async fetchSessions() {
      try {
        const response = await this.$http.get('/api/v1/chat/sessions');
        this.sessions = response.data.data.sessions;
        
        if (this.sessions.length > 0 && !this.currentSession) {
          this.selectSession(this.sessions[0]);
        }
      } catch (error) {
        console.error('获取会话失败:', error);
      }
    },
    
    // 选择会话
    async selectSession(session) {
      this.currentSession = session;
      this.messages = [];
      this.page = 1;
      this.hasMoreMessages = true;
      
      await this.loadMessages();
      
      // 标记为已读
      if (session.unreadCount > 0) {
        this.markSessionAsRead(session.id);
      }
      
      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    
    // 加载消息
    async loadMessages() {
      if (!this.currentSession || !this.hasMoreMessages) return;
      
      try {
        const response = await this.$http.get(
          `/api/v1/chat/sessions/${this.currentSession.id}/messages`,
          { params: { page: this.page, page_size: this.pageSize } }
        );
        
        const { messages, total } = response.data.data;
        
        // 添加消息到列表
        this.messages = [...messages.reverse(), ...this.messages];
        
        // 检查是否有更多消息
        this.hasMoreMessages = this.messages.length < total;
        this.page++;
      } catch (error) {
        console.error('加载消息失败:', error);
      }
    },
    
    // 发送消息
    async sendMessage() {
      if (!this.inputMessage.trim() || !this.currentSession) return;
      
      try {
        await this.$http.post(
          `/api/v1/chat/sessions/${this.currentSession.id}/messages/text`,
          { content: this.inputMessage }
        );
        
        this.inputMessage = '';
      } catch (error) {
        console.error('发送消息失败:', error);
        this.$message.error('发送失败，请重试');
      }
    },
    
    // 工具方法
    formatTime(timeString) {
      // 实现时间格式化
    },
    
    isOwnMessage(message) {
      return message.senderID === this.$store.getters.userId;
    },
    
    getMessageAvatar(message) {
      return this.isOwnMessage(message) 
        ? this.$store.getters.userAvatar 
        : this.currentSession.targetAvatar || '/default-avatar.png';
    },
    
    scrollToBottom() {
      const container = this.$refs.messagesContainer;
      container.scrollTop = container.scrollHeight;
    }
  }
};
</script>
```

## 安全性考虑

1. **认证与授权**
   - 使用JWT进行用户认证
   - 验证用户对会话的访问权限

2. **输入验证**
   - 对所有用户输入进行验证和净化
   - 限制媒体文件大小和类型

3. **WebSocket安全**
   - 验证WebSocket连接的源和Origin头
   - 实现心跳机制检测连接状态
   - 限制单用户连接数

4. **速率限制**
   - 限制消息发送频率
   - 防止DoS攻击

## 扩展建议

1. **消息加密**
   - 实现端到端加密
   - 增加消息签名验证

2. **离线消息**
   - 优化离线消息存储和推送机制

3. **消息搜索**
   - 添加消息全文搜索功能

4. **富文本消息**
   - 支持更多消息格式，如链接预览、位置分享等

5. **群聊功能**
   - 扩展支持多人群聊
