# 消息模块详细文档

## 目录
- [模块概述](#模块概述)
- [架构设计](#架构设计)
- [文件结构](#文件结构)
- [功能模块](#功能模块)
- [API接口](#api接口)
- [数据类型](#数据类型)
- [页面组件](#页面组件)
- [路由配置](#路由配置)
- [状态管理](#状态管理)
- [技术特点](#技术特点)
- [开发规范](#开发规范)
- [扩展建议](#扩展建议)

## 模块概述

消息模块是O-Mall用户端H5应用的核心功能模块之一，负责处理用户的各类消息通知，包括系统通知、订单消息、客服消息等。该模块采用Vue 3 + TypeScript + Uni-app技术栈开发，提供完整的消息管理功能。

### 主要功能
- 📱 消息分类管理（系统通知、订单消息、客服消息、聊天消息）
- 🔍 消息搜索与筛选
- 📋 消息列表展示与分页加载
- 👁️ 消息已读/未读状态管理
- 📌 消息置顶功能
- 🗑️ 消息删除功能
- 💬 实时消息推送支持
- 📊 未读消息统计

## 架构设计

### 整体架构
```
消息模块
├── 页面层 (Pages)
│   ├── 消息首页 (index.vue)
│   ├── 系统通知 (system.vue)
│   ├── 订单消息 (order.vue)
│   └── 客服消息 (service.vue)
├── API层 (API)
│   ├── 接口定义 (message.ts)
│   └── 类型定义 (message.typings.ts)
├── 组件层 (Components)
│   ├── 消息列表组件
│   ├── 消息项组件
│   └── 搜索组件
└── 工具层 (Utils)
    ├── 时间格式化
    ├── 消息预览处理
    └── 状态管理
```

### 数据流向
```
API数据 → 页面组件 → 状态管理 → UI渲染 → 用户交互 → API调用
```

## 文件结构

```
src/pages/message/
├── index.vue          # 消息首页 - 消息分类和列表
├── system.vue         # 系统通知页面
├── order.vue          # 订单消息页面
└── service.vue        # 客服消息页面

src/api/
├── message.ts         # 消息相关API接口
└── message.typings.ts # 消息类型定义
```

## 功能模块

### 1. 消息首页 (index.vue)

**功能特性：**
- 消息分类展示（系统通知、订单消息、客服消息、聊天消息）
- 未读消息数量统计
- 消息搜索功能
- 消息列表展示
- 分页加载
- 消息跳转导航

**核心方法：**
```typescript
// 加载消息分类
const loadMessageCategories = async () => {
  // 获取消息分类和未读统计
}

// 加载消息列表
const loadMessages = async (refresh = false) => {
  // 分页加载消息列表
}

// 搜索消息
const handleSearch = async () => {
  // 根据关键词搜索消息
}

// 跳转到分类页面
const goToCategory = (category: MessageCategory) => {
  // 导航到具体分类页面
}
```

### 2. 系统通知页面 (system.vue)

**功能特性：**
- 系统通知列表展示
- 通知详情查看
- 已读状态管理
- 分页加载
- 时间格式化显示

**通知类型：**
- 系统维护通知
- 版本更新通知
- 安全公告
- 活动通知
- 政策更新
- 功能公告

### 3. 订单消息页面 (order.vue)

**功能特性：**
- 订单状态通知
- 支付结果通知
- 物流信息通知
- 退款处理通知
- 消息图标状态显示
- 订单详情跳转

**消息类型：**
- 支付成功/失败
- 订单确认/发货/送达/完成/取消
- 退款申请/通过/拒绝/成功/失败

### 4. 客服消息页面 (service.vue)

**功能特性：**
- 客服会话列表
- 在线状态显示
- 最后消息预览
- 未读消息统计
- 新建会话功能
- 会话跳转

## API接口

### 基础接口

```typescript
// 获取消息分类
getMessageCategories(): Promise<IMessageCategoryResponse>

// 获取消息列表
getMessageList(params: IMessageListParams): Promise<IMessageListResponse>

// 获取未读消息统计
getUnreadCount(): Promise<IUnreadCountResponse>

// 搜索消息
searchMessages(params: ISearchMessageParams): Promise<ISearchMessageResponse>
```

### 系统通知接口

```typescript
// 获取系统通知列表
getSystemNotifications(params: ISystemNotificationParams): Promise<ISystemNotificationResponse>

// 获取系统通知详情
getSystemNotificationDetail(id: string): Promise<any>

// 标记系统通知为已读
markSystemNotificationAsRead(id: string): Promise<any>

// 批量标记系统通知为已读
batchMarkSystemNotificationsAsRead(ids: string[]): Promise<any>

// 删除系统通知
deleteSystemNotifications(ids: string[]): Promise<any>

// 清空已读系统通知
clearReadSystemNotifications(): Promise<any>
```

### 订单消息接口

```typescript
// 获取订单通知列表
getOrderNotifications(params: IOrderNotificationParams): Promise<IOrderNotificationResponse>

// 获取订单通知详情
getOrderNotificationDetail(id: string): Promise<any>

// 标记订单通知为已读
markOrderNotificationAsRead(id: string): Promise<any>

// 批量标记订单通知为已读
batchMarkOrderNotificationsAsRead(ids: string[]): Promise<any>

// 删除订单通知
deleteOrderNotifications(ids: string[]): Promise<any>
```

### 客服消息接口

```typescript
// 获取客服消息列表
getServiceMessages(params: IServiceMessageParams): Promise<IServiceMessageResponse>
```

### 通用操作接口

```typescript
// 标记消息为已读
markMessagesAsRead(params: IMarkReadParams): Promise<IMarkReadResponse>

// 删除消息
deleteMessages(params: IDeleteMessageParams): Promise<IDeleteMessageResponse>

// 置顶/取消置顶消息
toggleMessageTop(params: IToggleTopParams): Promise<IToggleTopResponse>
```

## 数据类型

### 核心类型定义

```typescript
// 消息分类类型
type MessageCategoryType = 'chat' | 'system' | 'order' | 'service'

// 消息分类信息
interface IMessageCategory {
  type: MessageCategoryType
  title: string
  icon: string
  color: string
  unreadCount: number
  path: string
}

// 消息项基础信息
interface IMessageItem {
  id: string
  type: MessageCategoryType
  title: string
  content: string
  avatar?: string
  time: string
  isRead: boolean
  isTop: boolean
  unreadCount?: number
  extra?: Record<string, any>
}
```

### 系统通知类型

```typescript
// 系统通知类型枚举
enum SystemNotificationType {
  SYSTEM_MAINTENANCE = 'system_maintenance',
  VERSION_UPDATE = 'version_update',
  SECURITY_NOTICE = 'security_notice',
  ACTIVITY_NOTICE = 'activity_notice',
  POLICY_UPDATE = 'policy_update',
  FEATURE_ANNOUNCEMENT = 'feature_announcement'
}

// 系统通知信息
interface ISystemNotification {
  id: string
  type: SystemNotificationType
  title: string
  content: string
  summary?: string
  imageUrl?: string
  actionType?: string
  actionUrl?: string
  priority: number
  isRead: boolean
  isTop: boolean
  publishTime: string
  expireTime?: string
  createdAt: string
  updatedAt: string
}
```

### 订单通知类型

```typescript
// 订单通知类型枚举
enum OrderNotificationType {
  PAYMENT_SUCCESS = 'payment_success',
  PAYMENT_FAILED = 'payment_failed',
  ORDER_CONFIRMED = 'order_confirmed',
  ORDER_SHIPPED = 'order_shipped',
  ORDER_DELIVERED = 'order_delivered',
  ORDER_COMPLETED = 'order_completed',
  ORDER_CANCELLED = 'order_cancelled',
  REFUND_APPLIED = 'refund_applied',
  REFUND_APPROVED = 'refund_approved',
  REFUND_REJECTED = 'refund_rejected',
  REFUND_SUCCESS = 'refund_success',
  REFUND_FAILED = 'refund_failed'
}

// 订单通知信息
interface IOrderNotification {
  id: string
  type: OrderNotificationType
  title: string
  content: string
  orderId: number
  orderNo: string
  amount?: number
  refundAmount?: number
  goodsInfo?: Array<{
    goodsId: string
    goodsName: string
    goodsImage: string
    quantity: number
  }>
  actionType?: string
  actionUrl?: string
  isRead: boolean
  isTop: boolean
  createdAt: string
  updatedAt: string
  extra?: Record<string, any>
}
```

### 客服消息类型

```typescript
// 客服消息信息
interface IServiceMessage {
  id: string
  sessionId: string
  title: string
  content: string
  serviceInfo: {
    serviceId: string
    serviceName: string
    serviceAvatar: string
    isOnline: boolean
  }
  lastMessage?: {
    content: string
    time: string
    type: string
  }
  unreadCount: number
  isRead: boolean
  isTop: boolean
  createdAt: string
  updatedAt: string
}
```

## 页面组件

### 消息首页组件结构

```vue
<template>
  <view class="message-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <wd-search v-model="searchKeyword" />
    </view>
    
    <!-- 消息分类 -->
    <view class="message-categories">
      <view v-for="category in messageCategories" 
            :key="category.type" 
            class="category-item"
            @click="goToCategory(category)">
        <!-- 分类图标和未读数量 -->
      </view>
    </view>
    
    <!-- 消息列表 -->
    <view class="message-list">
      <!-- 消息项 -->
    </view>
    
    <!-- 加载更多 -->
    <wd-loadmore :state="loadMoreState" @loadmore="loadMore" />
  </view>
</template>
```

### 组件特性

1. **响应式设计**
   - 适配不同屏幕尺寸
   - 支持横竖屏切换
   - 优化触摸交互

2. **性能优化**
   - 虚拟滚动（大数据量时）
   - 图片懒加载
   - 分页加载
   - 防抖搜索

3. **用户体验**
   - 加载状态提示
   - 空状态展示
   - 错误处理
   - 下拉刷新

## 路由配置

### pages.json配置

```json
{
  "pages": [
    {
      "path": "pages/message/index",
      "style": {
        "navigationBarTitleText": "消息"
      },
      "layout": "tabbar"
    },
    {
      "path": "pages/message/system",
      "style": {
        "navigationBarTitleText": "系统通知"
      }
    },
    {
      "path": "pages/message/order",
      "style": {
        "navigationBarTitleText": "订单消息"
      }
    },
    {
      "path": "pages/message/service",
      "style": {
        "navigationBarTitleText": "客服消息"
      }
    }
  ],
  "tabBar": {
    "list": [
      {
        "pagePath": "pages/message/index",
        "text": "消息"
      }
    ]
  }
}
```

### 路由跳转逻辑

```typescript
// 跳转到分类页面
const goToCategory = (category: MessageCategory) => {
  uni.navigateTo({
    url: category.path
  })
}

// 跳转到聊天页面
const goToChat = (message: MessageItem) => {
  switch (message.type) {
    case 'chat':
      uni.navigateTo({
        url: `/pages/chat/room/index?id=${message.id}&type=private`
      })
      break
    case 'system':
      uni.navigateTo({
        url: `/pages/message/system?id=${message.id}`
      })
      break
    case 'order':
      uni.navigateTo({
        url: `/pages/message/order?id=${message.id}`
      })
      break
    case 'service':
      uni.navigateTo({
        url: `/pages/message/service?id=${message.id}`
      })
      break
  }
}
```

## 状态管理

### 本地状态管理

每个页面组件都维护自己的本地状态：

```typescript
// 响应式数据
const loading = ref(false)
const messageList = ref<MessageItem[]>([])
const pageNo = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)
const searchKeyword = ref('')

// 计算属性
const filteredMessages = computed(() => {
  // 过滤逻辑
})

const loadMoreState = computed(() => {
  if (loading.value) return 'loading'
  if (!hasMore.value) return 'finished'
  return 'more'
})
```

### 全局状态（如需要）

可以通过Pinia或Vuex管理全局消息状态：

```typescript
// store/message.ts
export const useMessageStore = defineStore('message', {
  state: () => ({
    unreadCount: {
      chat: 0,
      system: 0,
      order: 0,
      service: 0,
      total: 0
    },
    messageCategories: [],
    recentMessages: []
  }),
  
  actions: {
    async updateUnreadCount() {
      const response = await getUnreadCount()
      this.unreadCount = response.data
    },
    
    markAsRead(messageId: string, type: MessageCategoryType) {
      // 标记消息为已读
    }
  }
})
```

## 技术特点

### 1. TypeScript支持
- 完整的类型定义
- 编译时类型检查
- 智能代码提示
- 重构安全性

### 2. Vue 3 Composition API
- 更好的逻辑复用
- 更清晰的代码组织
- 更好的TypeScript支持
- 更小的包体积

### 3. Uni-app跨平台
- 一套代码多端运行
- 原生性能体验
- 丰富的组件库
- 完善的生态系统

### 4. 响应式设计
- 移动端优先
- 适配多种屏幕
- 触摸友好交互
- 性能优化

## 开发规范

### 1. 代码规范

```typescript
/**
 * @file 文件描述
 * @description 详细描述
 * <AUTHOR>
 * @date 创建日期
 */

/**
 * 方法描述
 * @param params 参数描述
 * @returns 返回值描述
 */
const methodName = (params: ParamType): ReturnType => {
  // 实现逻辑
}
```

### 2. 命名规范

- **文件命名**：kebab-case（如：message-list.vue）
- **组件命名**：PascalCase（如：MessageList）
- **变量命名**：camelCase（如：messageList）
- **常量命名**：UPPER_SNAKE_CASE（如：MAX_PAGE_SIZE）
- **类型命名**：PascalCase + I前缀（如：IMessageItem）

### 3. 目录结构规范

```
pages/message/
├── index.vue          # 主页面
├── components/        # 页面专用组件
│   ├── MessageItem.vue
│   ├── MessageList.vue
│   └── SearchBar.vue
├── hooks/            # 页面专用hooks
│   ├── useMessage.ts
│   └── useSearch.ts
└── utils/            # 页面专用工具
    ├── format.ts
    └── validate.ts
```

### 4. API调用规范

```typescript
// 统一错误处理
const loadMessages = async () => {
  try {
    loading.value = true
    const response = await getMessageList(params)
    messageList.value = response.data.list
  } catch (error) {
    console.error('加载消息失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}
```

## 扩展建议

### 1. 功能扩展

**消息推送**
- WebSocket实时推送
- 离线消息缓存
- 推送通知权限管理
- 消息免打扰设置

**高级搜索**
- 多条件筛选
- 时间范围搜索
- 消息类型筛选
- 搜索历史记录

**消息管理**
- 批量操作（删除、标记已读）
- 消息导出功能
- 消息备份恢复
- 消息统计分析

### 2. 性能优化

**虚拟滚动**
```typescript
// 大数据量时使用虚拟滚动
import { VirtualList } from '@tanstack/vue-virtual'

<VirtualList
  :data="messageList"
  :item-size="80"
  :overscan="5"
>
  <template #default="{ item, index }">
    <MessageItem :message="item" :index="index" />
  </template>
</VirtualList>
```

**图片懒加载**
```typescript
// 使用Intersection Observer API
const useImageLazyLoad = () => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement
        img.src = img.dataset.src!
        observer.unobserve(img)
      }
    })
  })
  
  return { observer }
}
```

**缓存策略**
```typescript
// 消息列表缓存
const useMessageCache = () => {
  const cache = new Map<string, MessageItem[]>()
  
  const getCachedMessages = (key: string) => {
    return cache.get(key)
  }
  
  const setCachedMessages = (key: string, messages: MessageItem[]) => {
    cache.set(key, messages)
  }
  
  return { getCachedMessages, setCachedMessages }
}
```

### 3. 用户体验优化

**骨架屏**
```vue
<template>
  <view v-if="loading" class="skeleton">
    <view v-for="i in 5" :key="i" class="skeleton-item">
      <view class="skeleton-avatar"></view>
      <view class="skeleton-content">
        <view class="skeleton-title"></view>
        <view class="skeleton-text"></view>
      </view>
    </view>
  </view>
</template>
```

**下拉刷新**
```typescript
const onPullDownRefresh = async () => {
  await loadMessages(true)
  uni.stopPullDownRefresh()
}
```

**无限滚动**
```typescript
const onReachBottom = () => {
  if (hasMore.value && !loading.value) {
    loadMore()
  }
}
```

### 4. 测试建议

**单元测试**
```typescript
// tests/message.spec.ts
import { describe, it, expect } from 'vitest'
import { formatTime } from '@/pages/message/utils/format'

describe('消息工具函数', () => {
  it('应该正确格式化时间', () => {
    const time = '2024-01-01T12:00:00Z'
    const result = formatTime(time)
    expect(result).toBe('1月1日')
  })
})
```

**集成测试**
```typescript
// tests/message-page.spec.ts
import { mount } from '@vue/test-utils'
import MessageIndex from '@/pages/message/index.vue'

describe('消息首页', () => {
  it('应该正确渲染消息列表', async () => {
    const wrapper = mount(MessageIndex)
    await wrapper.vm.$nextTick()
    expect(wrapper.find('.message-list').exists()).toBe(true)
  })
})
```

## 总结

消息模块是一个功能完整、架构清晰的模块，具有以下特点：

1. **模块化设计**：清晰的文件结构和职责分离
2. **类型安全**：完整的TypeScript类型定义
3. **用户体验**：流畅的交互和友好的界面
4. **可扩展性**：良好的架构设计支持功能扩展
5. **性能优化**：分页加载、懒加载等优化策略
6. **代码规范**：统一的编码规范和注释规范

该模块为O-Mall应用提供了完整的消息管理功能，支持多种消息类型，具有良好的用户体验和开发体验。