ChatWindow.vue:660 🔍 当前用户类型: user
ChatWindow.vue:1544 🔧 [ChatWindow] 测试函数已暴露到全局: window.testScrollFunction()
ChatWindow.vue:3294 🔍 currentSessionId变化: null
ChatWindow.vue:557 🔍 currentSession计算开始: {currentSessionId: null, sessionsCount: 0, sessionIds: Array(0)}
ChatWindow.vue:564 🔍 currentSession结果: 无currentSessionId
ChatWindow.vue:3305 🔍 currentSession变化: {old: 'null', new: 'null'}
ChatWindow.vue:3316 🔍 sessions数量变化: 0 → 0
ChatWindow.vue:3355 ChatWindow: WebSocket状态监听触发: {serviceStatus: 'connected', serviceConnected: true, storeConnected: true, isInitializing: true, hasWebSocketService: true}
ChatWindow.vue:475 ChatWindow: WebSocket服务状态检查: {hasService: true, serviceStatus: 'connected', serviceConnected: true, wsReadyState: null, wsReadyStateText: 'UNKNOWN'}
ChatWindow.vue:3365 ChatWindow: 计算出的实际状态: connected
ChatWindow.vue:3369 ChatWindow: 初始化中检测到连接状态，立即更新
ChatWindow.vue:1898 🔍 过滤会话 - 当前分类: service
ChatWindow.vue:1899 📊 总会话数: 0
ChatWindow.vue:1900 📋 所有会话: []
ChatWindow.vue:1939 ✅ 过滤结果: 0 个会话
ChatWindow.vue:1940 📋 过滤后的会话: []
ChatWindow.vue:1461 🔧 [ChatWindow] 组件挂载，尝试设置滚动监听器
ChatWindow.vue:2937 🚀 初始化聊天客户端
ChatWindow.vue:3082 WebSocket服务已经连接，无需重复连接
ChatWindow.vue:3086 使用chatStore，通过watch监听状态变化
prepare.js:1 🍍 "session" store installed 🆕
prepare.js:1 🍍 "file" store installed 🆕
prepare.js:1 🍍 "message" store installed 🆕
prepare.js:1 🍍 "notification" store installed 🆕
request.ts:156 Chat request interceptor [user] config url is: /message-categories
request.ts:160 获取的user token：存在, URL: /message-categories
request.ts:164 是否在白名单中：false
request.ts:170 设置Authorization头部：Bearer eyJhbGciOi...
ChatWindow.vue:696 👤 用户store信息 (modules): {hasUserInfo: true, userId: 2, username: 'testuser', nickname: '测试用户1', avatar: 'http://omimg.qwyx.shop/avatar/1753198029947689000_YeDnD0ZG.jpg'}
ChatWindow.vue:776 🔍 当前用户信息: {id: 2, name: '测试用户1', avatar: 'http://omimg.qwyx.shop/avatar/1753198029947689000_YeDnD0ZG.jpg'}
ChatWindow.vue:780 🔍 头像设置验证:
ChatWindow.vue:781 - 用户类型: user
ChatWindow.vue:782 - 用户ID: 2
ChatWindow.vue:783 - 用户名称: 测试用户1
ChatWindow.vue:784 - 用户头像: http://omimg.qwyx.shop/avatar/1753198029947689000_YeDnD0ZG.jpg
ChatWindow.vue:789 ✅ 用户端应使用 userInfo.avatar 字段
ChatWindow.vue:797 🔍 详细调试信息:
ChatWindow.vue:798 1. 当前路径: /user/home
ChatWindow.vue:799 2. 用户类型判断: user
ChatWindow.vue:800 3. 当前用户信息: {id: 2, name: '测试用户1', avatar: 'http://omimg.qwyx.shop/avatar/1753198029947689000_YeDnD0ZG.jpg'}
ChatWindow.vue:805 4. 页面显示的用户名: 选择会话
ChatWindow.vue:813 5. 用户Store详细信息: {userInfo: Proxy(Object), isLoggedIn: true, token: '存在'}
request.ts:186 === Chat响应拦截器开始 ===
request.ts:187 响应URL: /message-categories
request.ts:188 响应状态码: 200
request.ts:189 响应数据: {code: 200, message: 'success', data: Array(4)}
request.ts:192 === Chat响应拦截器处理状态码 ===
request.ts:193 状态码: 200 消息: success
request.ts:197 === Chat响应拦截器处理成功 ===
request.ts:198 📊 数据分析: {hasData: true, dataType: 'object', dataKeys: Array(4), dataValue: Array(4)}
request.ts:206 ✅ 返回实际数据: (4) [{…}, {…}, {…}, {…}]
request.ts:260 chat get请求响应 (4) [{…}, {…}, {…}, {…}]
request.ts:156 Chat request interceptor [user] config url is: /unread-count
request.ts:160 获取的user token：存在, URL: /unread-count
request.ts:164 是否在白名单中：false
request.ts:170 设置Authorization头部：Bearer eyJhbGciOi...
ChatWindow.vue:1328 🔍 [ChatWindow] 检查滚动监听器状态: {scrollListenerSetup: false, hasMessageContainer: false}
ChatWindow.vue:1334 ⚠️ [ChatWindow] messageContainer.value 不存在，无法设置滚动监听器
ensureScrollListener @ ChatWindow.vue:1334
（匿名） @ ChatWindow.vue:1465
setTimeout
（匿名） @ ChatWindow.vue:1464
（匿名） @ chunk-GHIGOARL.js?v=dc5b30bf:9989
callWithErrorHandling @ chunk-GHIGOARL.js?v=dc5b30bf:2277
callWithAsyncErrorHandling @ chunk-GHIGOARL.js?v=dc5b30bf:2284
hook.__weh.hook.__weh @ chunk-GHIGOARL.js?v=dc5b30bf:4185
flushPostFlushCbs @ chunk-GHIGOARL.js?v=dc5b30bf:2454
flushJobs @ chunk-GHIGOARL.js?v=dc5b30bf:2495
Promise.then
queueFlush @ chunk-GHIGOARL.js?v=dc5b30bf:2391
queueJob @ chunk-GHIGOARL.js?v=dc5b30bf:2386
effect2.scheduler @ chunk-GHIGOARL.js?v=dc5b30bf:6478
trigger @ chunk-GHIGOARL.js?v=dc5b30bf:1597
endBatch @ chunk-GHIGOARL.js?v=dc5b30bf:439
notify @ chunk-GHIGOARL.js?v=dc5b30bf:1712
trigger @ chunk-GHIGOARL.js?v=dc5b30bf:1686
set value @ chunk-GHIGOARL.js?v=dc5b30bf:2019
openChat @ UserLayout.vue:373
toggleChat @ UserLayout.vue:368
callWithErrorHandling @ chunk-GHIGOARL.js?v=dc5b30bf:2277
callWithAsyncErrorHandling @ chunk-GHIGOARL.js?v=dc5b30bf:2284
emit @ chunk-GHIGOARL.js?v=dc5b30bf:7397
（匿名） @ chunk-GHIGOARL.js?v=dc5b30bf:8925
handleClick @ chunk-MYK3GQPA.js?v=dc5b30bf:6186
callWithErrorHandling @ chunk-GHIGOARL.js?v=dc5b30bf:2277
callWithAsyncErrorHandling @ chunk-GHIGOARL.js?v=dc5b30bf:2284
invoker @ chunk-GHIGOARL.js?v=dc5b30bf:11275
request.ts:186 === Chat响应拦截器开始 ===
request.ts:187 响应URL: /unread-count
request.ts:188 响应状态码: 200
request.ts:189 响应数据: {code: 200, message: 'success', data: {…}}
request.ts:192 === Chat响应拦截器处理状态码 ===
request.ts:193 状态码: 200 消息: success
request.ts:197 === Chat响应拦截器处理成功 ===
request.ts:198 📊 数据分析: {hasData: true, dataType: 'object', dataKeys: Array(3), dataValue: {…}}
request.ts:206 ✅ 返回实际数据: {total: 3, categories: {…}, conversations: {…}}
request.ts:260 chat get请求响应 {total: 3, categories: {…}, conversations: {…}}
ChatWindow.vue:2949 📂 初始化默认分类: SERVICE
ChatWindow.vue:2950 📊 初始化前状态: {currentMessageCategory: 'service', sessions: 0}
ChatWindow.vue:1976 🔄 切换到消息分类: service
ChatWindow.vue:1980 🗑️ 已清空当前消息列表，准备切换分类
message.ts:676 🔄 切换消息分类: CHAT -> service
ChatWindow.vue:2003 📋 分类 service 是否为会话分类: true
ChatWindow.vue:2012 📥 开始加载 service 分类的会话列表
ChatWindow.vue:2230 🔄 开始加载service分类会话...
session.ts:173 🔄 开始加载会话列表 [1753775675329]: {category: 'service', page: 1, page_size: 20}
index.ts:298 📡 调用分类会话列表API: /sessions {page: 1, page_size: 20, category: 'service'}
index.ts:299 🎯 请求分类: service
request.ts:156 Chat request interceptor [user] config url is: /sessions
request.ts:160 获取的user token：存在, URL: /sessions
request.ts:164 是否在白名单中：false
request.ts:170 设置Authorization头部：Bearer eyJhbGciOi...
request.ts:186 === Chat响应拦截器开始 ===
request.ts:187 响应URL: /sessions
request.ts:188 响应状态码: 200
request.ts:189 响应数据: {code: 200, message: 'success', data: {…}}
request.ts:192 === Chat响应拦截器处理状态码 ===
request.ts:193 状态码: 200 消息: success
request.ts:197 === Chat响应拦截器处理成功 ===
request.ts:198 📊 数据分析: {hasData: true, dataType: 'object', dataKeys: Array(6), dataValue: {…}}
request.ts:206 ✅ 返回实际数据: {category: 'service', list: Array(1), page: 1, page_count: 1, page_size: 20, …}
request.ts:260 chat get请求响应 {category: 'service', list: Array(1), page: 1, page_count: 1, page_size: 20, …}
session.ts:194 ✅ 会话列表API响应 [1753775675329]: {category: 'service', list: Array(1), page: 1, page_count: 1, page_size: 20, …}
session.ts:136 🏷️ [generateSessionTitle] 生成会话标题: {sessionId: 12, target_name: 'songda', sender_name: '测试用户1', hasTargetName: true}
session.ts:146 ✅ [generateSessionTitle] 使用target_name: 与 songda 的对话
session.ts:247 ✅ 会话列表加载成功 [1753775675329]: {count: 1, page: 1, total: 1}
ChatWindow.vue:557 🔍 currentSession计算开始: {currentSessionId: null, sessionsCount: 1, sessionIds: Array(1)}
ChatWindow.vue:564 🔍 currentSession结果: 无currentSessionId
ChatWindow.vue:3316 🔍 sessions数量变化: 0 → 1
ChatWindow.vue:3318 🔍 当前所有会话: [{…}]
ChatWindow.vue:1898 🔍 过滤会话 - 当前分类: service
ChatWindow.vue:1899 📊 总会话数: 1
ChatWindow.vue:1900 📋 所有会话: [{…}]
ChatWindow.vue:1935 🔍 会话12 (presale_consultation) 是否包含在service分类: true
ChatWindow.vue:1939 ✅ 过滤结果: 1 个会话
ChatWindow.vue:1940 📋 过滤后的会话: [{…}]
ChatWindow.vue:2405 🔍 ChatWindow getLastMessagePreview 调试: {sessionId: 12, hasLastMessage: true, lastMessage: Proxy(Object), senderName: '测试用户1'}
ChatWindow.vue:2427 📝 ChatWindow 消息预览: {sessionId: 12, senderName: '测试用户1', content: '不知道啊，可能还没有发生吧...'}
ChatWindow.vue:2460 ✅ ChatWindow 最终预览结果: 测试用户1: 不知道啊，可能还没有发生吧
ChatWindow.vue:2245 ✅ 已加载 1 个service会话
ChatWindow.vue:2252 📋 service会话列表: Proxy(Array) {0: {…}}
ChatWindow.vue:2253 ✅ 已成功加载 1 个service会话
ChatWindow.vue:2025 🔄 自动选择会话逻辑开始... {previousSessionId: null, currentSessionId: null}
ChatWindow.vue:2034 📊 当前分类可用会话数: 1
ChatWindow.vue:2055 🎯 自动选择第一个会话: 与 songda 的对话
ChatWindow.vue:2667 🎯 开始选择会话: {sessionId: 12, sessionTitle: '与 songda 的对话', sessionType: 'presale_consultation'}
ChatWindow.vue:2675 📝 设置前状态: {oldCurrentSessionId: null, newSessionIdStr: '12', sessionsCount: 1}
ChatWindow.vue:2684 📝 已设置currentSessionId: 12
ChatWindow.vue:1015 🔄 会话切换: null → 12
ChatWindow.vue:943 🔄 加载会话 12 的消息... (分类: service, 时间: 1753775675589)
message.ts:391 🔄 开始加载分类 service 会话 12 的消息... (请求ID: 1753775675590)
ChatWindow.vue:1140 🔄 检测到会话切换，重新加载消息: {旧会话: null, 新会话: '12', 分类: 'service'}
ChatWindow.vue:1507 🔄 [ChatWindow] 会话切换检测: {from: null, to: '12', hasScrolledToBottomOnce: false}
ChatWindow.vue:1493 🔄 [ChatWindow] 已清除分页状态，将重新初始化: service-12
ChatWindow.vue:1496 🔄 [ChatWindow] 重置所有滚动状态: {hasScrolledToBottomOnce: false, scrollListenerSetup: false, isScrollingToBottom: false, lastScrollTime: 0}
ChatWindow.vue:1513 🔄 [ChatWindow] 会话切换，重置分页状态完成
ChatWindow.vue:3294 🔍 currentSessionId变化: 12
ChatWindow.vue:3296 切换到会话: 12
ChatWindow.vue:557 🔍 currentSession计算开始: {currentSessionId: '12', sessionsCount: 1, sessionIds: Array(1)}
ChatWindow.vue:570 🔍 检查会话 12 (与 songda 的对话): 匹配
ChatWindow.vue:574 🔍 currentSession计算结果: {currentSessionId: '12', foundSession: true, sessionTitle: '与 songda 的对话', sessionId: 12}
ChatWindow.vue:3305 🔍 currentSession变化: {old: 'null', new: '12:与 songda 的对话'}
ChatWindow.vue:2534 🏷️ [ChatWindow] getSessionName 调试: {sessionId: 12, sessionType: 'presale_consultation', title: '与 songda 的对话', target_name: 'songda', hasTitle: true, …}
ChatWindow.vue:2546 ✅ [ChatWindow] 使用title字段: 与 songda 的对话
ChatWindow.vue:2534 🏷️ [ChatWindow] getSessionName 调试: {sessionId: 12, sessionType: 'presale_consultation', title: '与 songda 的对话', target_name: 'songda', hasTitle: true, …}
ChatWindow.vue:2546 ✅ [ChatWindow] 使用title字段: 与 songda 的对话
ChatWindow.vue:2618 🔍 获取会话状态文本: {sessionId: 12, sessionTitle: '与 songda 的对话', sessionStatus: 'active', sessionStatusType: 'string', isActive: true, …}
ChatWindow.vue:2635 🔧 临时修复：强制返回"在线"状态
ChatWindow.vue:2405 🔍 ChatWindow getLastMessagePreview 调试: {sessionId: 12, hasLastMessage: true, lastMessage: Proxy(Object), senderName: '测试用户1'}
ChatWindow.vue:2427 📝 ChatWindow 消息预览: {sessionId: 12, senderName: '测试用户1', content: '不知道啊，可能还没有发生吧...'}
ChatWindow.vue:2460 ✅ ChatWindow 最终预览结果: 测试用户1: 不知道啊，可能还没有发生吧
ChatWindow.vue:594 🔍 shouldHideInputArea计算: {sessionType: 'presale_consultation', isSystemNotification: false, isOrderNotification: false, shouldHide: false}
request.ts:156 Chat request interceptor [user] config url is: /sessions/12/messages
ChatWindow.vue:2689 🔍 设置后验证: {currentSessionId: '12', currentSession: Proxy(Object), hasCurrentSession: true}
ChatWindow.vue:2718 🔄 开始加载会话 12 的历史消息...
ChatWindow.vue:2719 🔍 加载前currentSession状态: {currentSessionId: '12', hasCurrentSession: true, currentCategory: 'service'}
message.ts:381 ⏳ 分类 service 会话 12 的消息加载请求已在进行中，等待完成...
request.ts:160 获取的user token：存在, URL: /sessions/12/messages
request.ts:164 是否在白名单中：false
request.ts:170 设置Authorization头部：Bearer eyJhbGciOi...
request.ts:186 === Chat响应拦截器开始 ===
request.ts:187 响应URL: /sessions/12/messages
request.ts:188 响应状态码: 200
request.ts:189 响应数据: {code: 200, message: 'success', data: {…}}
request.ts:192 === Chat响应拦截器处理状态码 ===
request.ts:193 状态码: 200 消息: success
request.ts:197 === Chat响应拦截器处理成功 ===
request.ts:198 📊 数据分析: {hasData: true, dataType: 'object', dataKeys: Array(5), dataValue: {…}}
request.ts:206 ✅ 返回实际数据: {list: Array(10), page: 1, page_count: 6, page_size: 10, total: 55}
request.ts:260 chat get请求响应 {list: Array(10), page: 1, page_count: 6, page_size: 10, total: 55}
message.ts:423 🔍 API原始响应数据 (请求ID: 1753775675590): {list: Array(10), page: 1, page_count: 6, page_size: 10, total: 55}
message.ts:428 📋 API返回消息数据 (请求ID: 1753775675590): {category: 'service', sessionId: '12', total: 55, page: 1, messageCount: 10, …}
message.ts:471 ✅ 已存储分类 service 会话 12 的 10 条消息到store (请求ID: 1753775675590, 耗时: 691ms)
message.ts:472 📊 消息时间排序: 最早 2025-07-27T22:14:48+08:00 -> 最新 2025-07-29T14:53:41+08:00
message.ts:484 📄 [loadMessages] 分页信息: {total: 55, page: 1, pageCount: 6, pageSize: 10, currentMessages: 10, …}
ChatWindow.vue:1156 🔄 检测到store消息变化，同步到本地状态
ChatWindow.vue:1157 📊 当前分类: service
ChatWindow.vue:1158 📊 Store中的消息数量: 10
ChatWindow.vue:1159 📊 本地消息数量: 0
ChatWindow.vue:1177 ✅ 发现新消息，添加到本地列表: 10 条
ChatWindow.vue:1178 📋 新消息详情: (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
ChatWindow.vue:1066 📊 [ChatWindow] 消息数量变化: {from: 0, to: 10, isLoadingMore: false, hasScrolledToBottomOnce: false, shouldScroll: false}
ChatWindow.vue:1079 📜 [ChatWindow] 加载历史消息或首次加载，不滚动
ChatWindow.vue:2534 🏷️ [ChatWindow] getSessionName 调试: {sessionId: 12, sessionType: 'presale_consultation', title: '与 songda 的对话', target_name: 'songda', hasTitle: true, …}
ChatWindow.vue:2546 ✅ [ChatWindow] 使用title字段: 与 songda 的对话
ChatWindow.vue:2534 🏷️ [ChatWindow] getSessionName 调试: {sessionId: 12, sessionType: 'presale_consultation', title: '与 songda 的对话', target_name: 'songda', hasTitle: true, …}
ChatWindow.vue:2546 ✅ [ChatWindow] 使用title字段: 与 songda 的对话
ChatWindow.vue:2618 🔍 获取会话状态文本: {sessionId: 12, sessionTitle: '与 songda 的对话', sessionStatus: 'active', sessionStatusType: 'string', isActive: true, …}
ChatWindow.vue:2635 🔧 临时修复：强制返回"在线"状态
ChatWindow.vue:2405 🔍 ChatWindow getLastMessagePreview 调试: {sessionId: 12, hasLastMessage: true, lastMessage: Proxy(Object), senderName: '测试用户1'}
ChatWindow.vue:2427 📝 ChatWindow 消息预览: {sessionId: 12, senderName: '测试用户1', content: '不知道啊，可能还没有发生吧...'}
ChatWindow.vue:2460 ✅ ChatWindow 最终预览结果: 测试用户1: 不知道啊，可能还没有发生吧
ChatWindow.vue:1718 📜 [ChatWindow] 准备滚动到底部: {scrollHeight: 329, clientHeight: 329, currentScrollTop: 0, targetScrollTop: 0, smooth: true, …}
ChatWindow.vue:1731 🚫 [ChatWindow] 临时禁用滚动事件监听
ChatWindow.vue:982 ✅ 已加载 10 条消息 (分类: service, 会话: 12)
ChatWindow.vue:983 📋 消息详情: (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
ChatWindow.vue:992 📜 [ChatWindow] 首次加载消息，滚动到底部
ChatWindow.vue:2730 ✅ 已加载会话 12 在分类 service 下的 10 条消息
ChatWindow.vue:2732 🔍 加载后currentSession状态: {currentSessionId: '12', hasCurrentSession: true}
ChatWindow.vue:2738 📋 最新消息: Proxy(Object) {id: 125, session_id: 12, sender_id: 2, sender_type: 'user', content: '不知道啊，可能还没有发生吧', …}
ChatWindow.vue:2534 🏷️ [ChatWindow] getSessionName 调试: {sessionId: 12, sessionType: 'presale_consultation', title: '与 songda 的对话', target_name: 'songda', hasTitle: true, …}
ChatWindow.vue:2546 ✅ [ChatWindow] 使用title字段: 与 songda 的对话
ChatWindow.vue:2534 🏷️ [ChatWindow] getSessionName 调试: {sessionId: 12, sessionType: 'presale_consultation', title: '与 songda 的对话', target_name: 'songda', hasTitle: true, …}
ChatWindow.vue:2546 ✅ [ChatWindow] 使用title字段: 与 songda 的对话
ChatWindow.vue:2618 🔍 获取会话状态文本: {sessionId: 12, sessionTitle: '与 songda 的对话', sessionStatus: 'active', sessionStatusType: 'string', isActive: true, …}
ChatWindow.vue:2635 🔧 临时修复：强制返回"在线"状态
ChatWindow.vue:2405 🔍 ChatWindow getLastMessagePreview 调试: {sessionId: 12, hasLastMessage: true, lastMessage: Proxy(Object), senderName: '测试用户1'}
ChatWindow.vue:2427 📝 ChatWindow 消息预览: {sessionId: 12, senderName: '测试用户1', content: '不知道啊，可能还没有发生吧...'}
ChatWindow.vue:2460 ✅ ChatWindow 最终预览结果: 测试用户1: 不知道啊，可能还没有发生吧
ChatWindow.vue:1286 🔍 [ChatWindow] 检查hasMoreMessages: {sessionId: '12', category: 'service', paginationKey: 'service-12', pageInfo: Proxy(Object), hasMore: true}
ChatWindow.vue:2758 🔄 标记会话 12 为已读...
ChatWindow.vue:2789 ℹ️ 会话 12 已经是已读状态
ChatWindow.vue:1718 📜 [ChatWindow] 准备滚动到底部: {scrollHeight: 815, clientHeight: 329, currentScrollTop: 0, targetScrollTop: 486, smooth: false, …}
ChatWindow.vue:1731 🚫 [ChatWindow] 临时禁用滚动事件监听
ChatWindow.vue:1328 🔍 [ChatWindow] 检查滚动监听器状态: {scrollListenerSetup: false, hasMessageContainer: true}
ChatWindow.vue:1349 ✅ [ChatWindow] 滚动监听器设置成功
ChatWindow.vue:1358 📏 [ChatWindow] 滚动容器状态: {scrollTop: 0, scrollHeight: 815, clientHeight: 329, hasScrollbar: true}
ChatWindow.vue:2704 ✅ 会话选择完成: 与 songda 的对话
ChatWindow.vue:2705 🔍 最终currentSession状态: {hasCurrentSession: true, currentSessionId: 12, currentSessionTitle: '与 songda 的对话'}
request.ts:156 Chat request interceptor [user] config url is: /unread-count
request.ts:160 获取的user token：存在, URL: /unread-count
request.ts:164 是否在白名单中：false
request.ts:170 设置Authorization头部：Bearer eyJhbGciOi...
request.ts:186 === Chat响应拦截器开始 ===
request.ts:187 响应URL: /unread-count
request.ts:188 响应状态码: 200
request.ts:189 响应数据: {code: 200, message: 'success', data: {…}}
request.ts:192 === Chat响应拦截器处理状态码 ===
request.ts:193 状态码: 200 消息: success
request.ts:197 === Chat响应拦截器处理成功 ===
request.ts:198 📊 数据分析: {hasData: true, dataType: 'object', dataKeys: Array(3), dataValue: {…}}
request.ts:206 ✅ 返回实际数据: {total: 3, categories: {…}, conversations: {…}}
request.ts:260 chat get请求响应 {total: 3, categories: {…}, conversations: {…}}
ChatWindow.vue:2958 🔄 初始化完成，更新连接状态
ChatWindow.vue:475 ChatWindow: WebSocket服务状态检查: {hasService: true, serviceStatus: 'connected', serviceConnected: true, wsReadyState: null, wsReadyStateText: 'UNKNOWN'}
ChatWindow.vue:2963 🔍 初始化完成后的实际状态: connected
ChatWindow.vue:510 ChatWindow: 更新连接状态缓冲: connected 当前缓冲状态: connected 初始化中: false
ChatWindow.vue:521 ChatWindow: 立即更新为已连接状态
ChatWindow.vue:2966 ✅ 聊天客户端初始化完成
ChatWindow.vue:2909 🔍 当前聊天组件状态调试:
ChatWindow.vue:2910   - currentMessageCategory: service
ChatWindow.vue:2911   - currentSessionId: 12
ChatWindow.vue:2912   - sessions.length: 1
ChatWindow.vue:2913   - filteredSessions.length: 1
ChatWindow.vue:2914   - currentSession: Proxy(Object) {id: 12, type: 'presale_consultation', status: 'active', priority: 'normal', title: '与 songda 的对话', …}
ChatWindow.vue:2915   - hasCurrentSession: true
ChatWindow.vue:2918   - 所有会话: [{…}]
ChatWindow.vue:2926   - 过滤后会话: [{…}]
ChatWindow.vue:3057 💡 调试函数已暴露:
ChatWindow.vue:3058   - window.debugChatState()
ChatWindow.vue:3059   - window.testSessionSelect(sessionId)
ChatWindow.vue:3060   - window.forceSetCurrentSession(sessionId)
ChatWindow.vue:3061   - window.checkDOMElements()
ChatWindow.vue:3062   - window.forceRefreshInterface()
ChatWindow.vue:3063   - window.forceShowChatInterface()
ChatWindow.vue:3064   - window.forceToggleChatInterface()
5ChatWindow.vue:1379 🚫 [ChatWindow] 正在执行滚动到底部操作，忽略滚动事件
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 52, targetScrollTop: 486, isScrollComplete: false, diff: 434}
3ChatWindow.vue:1379 🚫 [ChatWindow] 正在执行滚动到底部操作，忽略滚动事件
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 134, targetScrollTop: 486, isScrollComplete: false, diff: 352}
3ChatWindow.vue:1379 🚫 [ChatWindow] 正在执行滚动到底部操作，忽略滚动事件
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 242, targetScrollTop: 486, isScrollComplete: false, diff: 244}
3ChatWindow.vue:1379 🚫 [ChatWindow] 正在执行滚动到底部操作，忽略滚动事件
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 352, targetScrollTop: 486, isScrollComplete: false, diff: 134}
2ChatWindow.vue:1379 🚫 [ChatWindow] 正在执行滚动到底部操作，忽略滚动事件
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 410, targetScrollTop: 0, isScrollComplete: false, diff: 410}
ChatWindow.vue:1379 🚫 [ChatWindow] 正在执行滚动到底部操作，忽略滚动事件
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 434, targetScrollTop: 486, isScrollComplete: false, diff: 52}
2ChatWindow.vue:1379 🚫 [ChatWindow] 正在执行滚动到底部操作，忽略滚动事件
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 468, targetScrollTop: 0, isScrollComplete: false, diff: 468}
ChatWindow.vue:1379 🚫 [ChatWindow] 正在执行滚动到底部操作，忽略滚动事件
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 478, targetScrollTop: 486, isScrollComplete: false, diff: 8}
2ChatWindow.vue:1379 🚫 [ChatWindow] 正在执行滚动到底部操作，忽略滚动事件
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 486, isScrollComplete: true, diff: 0}
ChatWindow.vue:1774 ✅ [ChatWindow] 滚动已完成，重新启用监听器
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1739 ✅ [ChatWindow] 重新启用滚动事件监听
ChatWindow.vue:1743 🔄 [ChatWindow] 重置滚动状态标记
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 486, targetScrollTop: 0, isScrollComplete: false, diff: 486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 493, targetScrollTop: 0, isScrollComplete: false, diff: 493}
2ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 650, targetScrollTop: 0, isScrollComplete: false, diff: 650}
2ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 785, targetScrollTop: 0, isScrollComplete: false, diff: 785}
2ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 976, targetScrollTop: 0, isScrollComplete: false, diff: 976}
ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1008, targetScrollTop: 0, isScrollComplete: false, diff: 1008}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1008, targetScrollTop: 0, isScrollComplete: false, diff: 1008}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1008, targetScrollTop: 0, isScrollComplete: false, diff: 1008}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1008, targetScrollTop: 0, isScrollComplete: false, diff: 1008}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1008, targetScrollTop: 0, isScrollComplete: false, diff: 1008}
ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1013, targetScrollTop: 0, isScrollComplete: false, diff: 1013}
2ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1319, targetScrollTop: 0, isScrollComplete: false, diff: 1319}
ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1486, targetScrollTop: 0, isScrollComplete: false, diff: 1486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1486, targetScrollTop: 0, isScrollComplete: false, diff: 1486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1486, targetScrollTop: 0, isScrollComplete: false, diff: 1486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1486, targetScrollTop: 0, isScrollComplete: false, diff: 1486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1486, targetScrollTop: 0, isScrollComplete: false, diff: 1486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1486, targetScrollTop: 0, isScrollComplete: false, diff: 1486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1486, targetScrollTop: 0, isScrollComplete: false, diff: 1486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1486, targetScrollTop: 0, isScrollComplete: false, diff: 1486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1486, targetScrollTop: 0, isScrollComplete: false, diff: 1486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1486, targetScrollTop: 0, isScrollComplete: false, diff: 1486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1486, targetScrollTop: 0, isScrollComplete: false, diff: 1486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1486, targetScrollTop: 0, isScrollComplete: false, diff: 1486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1486, targetScrollTop: 0, isScrollComplete: false, diff: 1486}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1486, targetScrollTop: 0, isScrollComplete: false, diff: 1486}
ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1417, targetScrollTop: 0, isScrollComplete: false, diff: 1417}
2ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 1004, targetScrollTop: 0, isScrollComplete: false, diff: 1004}
2ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 687, targetScrollTop: 0, isScrollComplete: false, diff: 687}
ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 623, targetScrollTop: 0, isScrollComplete: false, diff: 623}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 623, targetScrollTop: 0, isScrollComplete: false, diff: 623}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 623, targetScrollTop: 0, isScrollComplete: false, diff: 623}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 623, targetScrollTop: 0, isScrollComplete: false, diff: 623}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 623, targetScrollTop: 0, isScrollComplete: false, diff: 623}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 623, targetScrollTop: 0, isScrollComplete: false, diff: 623}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 623, targetScrollTop: 0, isScrollComplete: false, diff: 623}
ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 485, targetScrollTop: 0, isScrollComplete: false, diff: 485}
2ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 247, targetScrollTop: 0, isScrollComplete: false, diff: 247}
ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 215, targetScrollTop: 0, isScrollComplete: false, diff: 215}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 215, targetScrollTop: 0, isScrollComplete: false, diff: 215}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 215, targetScrollTop: 0, isScrollComplete: false, diff: 215}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 215, targetScrollTop: 0, isScrollComplete: false, diff: 215}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 215, targetScrollTop: 0, isScrollComplete: false, diff: 215}
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 215, targetScrollTop: 0, isScrollComplete: false, diff: 215}
2ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 100, targetScrollTop: 0, isScrollComplete: false, diff: 100}
ChatWindow.vue:1385 🚫 [ChatWindow] 滚动事件过于频繁，忽略
ChatWindow.vue:1405 🎯 [ChatWindow] 滚动事件被触发！
ChatWindow.vue:1406 📜 [ChatWindow] 滚动事件详情: {scrollTop: 0, scrollHeight: 1815, clientHeight: 329, isAtTop: true, isAtBottom: false, …}
ChatWindow.vue:1425 🔝 [ChatWindow] 滚动事件时间间隔过短，可能是程序触发，忽略
ChatWindow.vue:1766 📜 [ChatWindow] 检查滚动状态: {currentScrollTop: 0, targetScrollTop: 0, isScrollComplete: true, diff: 0}
ChatWindow.vue:1774 ✅ [ChatWindow] 滚动已完成，重新启用监听器
ChatWindow.vue:1739 ✅ [ChatWindow] 重新启用滚动事件监听
ChatWindow.vue:1743 🔄 [ChatWindow] 重置滚动状态标记