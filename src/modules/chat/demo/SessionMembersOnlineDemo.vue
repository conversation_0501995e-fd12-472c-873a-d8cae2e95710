<template>
  <div class="session-members-online-demo">
    <h2>会话成员在线状态演示</h2>
    
    <div class="demo-section">
      <h3>模拟WebSocket消息</h3>
      <button @click="simulateOnlineMessage" class="demo-btn demo-btn--success">
        模拟成员上线
      </button>
      <button @click="simulateOfflineMessage" class="demo-btn demo-btn--warning">
        模拟成员离线
      </button>
    </div>

    <div class="demo-section">
      <h3>会话列表</h3>
      <div class="session-list">
        <div
          v-for="session in demoSessions"
          :key="session.id"
          class="session-item"
          :class="{ 'session-item--online': getSessionOnlineStatus(session) === 'online' }"
        >
          <div class="session-avatar">
            <img v-if="session.avatar" :src="session.avatar" :alt="session.title" />
            <div v-else class="avatar-placeholder">
              {{ session.title.charAt(0) }}
            </div>
            <!-- 在线状态指示器 -->
            <div class="session-status-dot"
                 :class="`session-status-dot--${getSessionOnlineStatus(session)}`">
            </div>
          </div>
          <div class="session-info">
            <div class="session-title">{{ session.title }}</div>
            <div class="session-status-text">
              {{ getSessionOnlineStatus(session) === 'online' ? '在线' : '离线' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h3>在线状态日志</h3>
      <div class="status-log">
        <div
          v-for="(log, index) in statusLogs"
          :key="index"
          class="log-item"
          :class="`log-item--${log.type}`"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// 演示会话数据
const demoSessions = reactive([
  {
    id: 12,
    title: '商家 songda',
    avatar: 'http://omallimg.qwyx.shop/merchant_logo/2025/06/10/1749519350919156000_DZtRIVc0.png',
    is_online: false
  },
  {
    id: 13,
    title: '用户 张三',
    avatar: '',
    is_online: false
  },
  {
    id: 14,
    title: '客服小王',
    avatar: '',
    is_online: true
  }
])

// 状态日志
const statusLogs = ref<Array<{ time: string, message: string, type: 'online' | 'offline' }>>([])

// 获取会话在线状态
const getSessionOnlineStatus = (session: any) => {
  return session.is_online ? 'online' : 'offline'
}

// 添加日志
const addLog = (message: string, type: 'online' | 'offline') => {
  const now = new Date()
  statusLogs.value.unshift({
    time: now.toLocaleTimeString(),
    message,
    type
  })
  
  // 限制日志数量
  if (statusLogs.value.length > 10) {
    statusLogs.value = statusLogs.value.slice(0, 10)
  }
}

// 模拟成员上线消息
const simulateOnlineMessage = () => {
  const session = demoSessions[0] // 选择第一个会话
  session.is_online = true
  
  addLog(`会话 "${session.title}" 中有成员上线`, 'online')
  
  console.log('模拟WebSocket消息 - 成员上线:', {
    type: 'notification',
    event: 'session_members_online',
    data: {
      session_id: session.id,
      member_count: 1,
      online_members: [{
        user_id: 1,
        user_name: 'songda',
        user_type: 'merchant',
        online_status: 'active',
        last_activity: new Date().toISOString()
      }]
    }
  })
}

// 模拟成员离线消息
const simulateOfflineMessage = () => {
  const session = demoSessions[0] // 选择第一个会话
  session.is_online = false
  
  addLog(`会话 "${session.title}" 中所有成员离线`, 'offline')
  
  console.log('模拟WebSocket消息 - 成员离线:', {
    type: 'notification',
    event: 'session_members_online',
    data: {
      session_id: session.id,
      member_count: 1,
      online_members: [{
        user_id: 1,
        user_name: 'songda',
        user_type: 'merchant',
        online_status: 'offline',
        last_activity: new Date().toISOString()
      }]
    }
  })
}
</script>

<style scoped>
.session-members-online-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
}

.demo-section h3 {
  margin: 0 0 15px 0;
  color: #374151;
}

.demo-btn {
  padding: 8px 16px;
  margin-right: 10px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.demo-btn--success {
  background: #10b981;
  color: white;
}

.demo-btn--success:hover {
  background: #059669;
}

.demo-btn--warning {
  background: #f59e0b;
  color: white;
}

.demo-btn--warning:hover {
  background: #d97706;
}

.session-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.session-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s;
}

.session-item--online {
  border-color: #10b981;
  background: #f0fdf4;
}

.session-avatar {
  position: relative;
  width: 40px;
  height: 40px;
  margin-right: 12px;
}

.session-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #6b7280;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.session-status-dot {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.session-status-dot--online {
  background-color: #10b981;
}

.session-status-dot--offline {
  background-color: #6b7280;
}

.session-info {
  flex: 1;
}

.session-title {
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.session-status-text {
  font-size: 12px;
  color: #6b7280;
}

.status-log {
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 10px;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid #f3f4f6;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  font-size: 12px;
  color: #6b7280;
  margin-right: 10px;
  min-width: 80px;
}

.log-message {
  font-size: 14px;
}

.log-item--online .log-message {
  color: #10b981;
}

.log-item--offline .log-message {
  color: #f59e0b;
}
</style>
