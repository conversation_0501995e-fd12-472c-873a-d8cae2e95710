/**
 * 事件发射器工具类
 */

export type EventListener = (...args: any[]) => void

export interface EventMap {
  [event: string]: EventListener[]
}

/**
 * 简单的事件发射器实现
 */
export class EventEmitter {
  private events: EventMap = {}
  private maxListeners = 10

  /**
   * 添加事件监听器
   */
  on(event: string, listener: EventListener): this {
    if (!this.events[event]) {
      this.events[event] = []
    }

    this.events[event].push(listener)

    // 检查监听器数量
    if (this.events[event].length > this.maxListeners) {
      console.warn(`Warning: Possible EventEmitter memory leak detected. ${this.events[event].length} listeners added for event '${event}'. Use setMaxListeners() to increase limit.`)
    }

    return this
  }

  /**
   * 添加一次性事件监听器
   */
  once(event: string, listener: EventListener): this {
    const onceWrapper = (...args: any[]) => {
      this.off(event, onceWrapper)
      listener.apply(this, args)
    }

    this.on(event, onceWrapper)
    return this
  }

  /**
   * 移除事件监听器
   */
  off(event: string, listener?: EventListener): this {
    if (!this.events[event]) {
      return this
    }

    if (!listener) {
      // 移除所有监听器
      delete this.events[event]
      return this
    }

    const index = this.events[event].indexOf(listener)
    if (index > -1) {
      this.events[event].splice(index, 1)
    }

    // 如果没有监听器了，删除事件
    if (this.events[event].length === 0) {
      delete this.events[event]
    }

    return this
  }

  /**
   * 触发事件
   */
  emit(event: string, ...args: any[]): boolean {
    if (!this.events[event]) {
      return false
    }

    const listeners = [...this.events[event]]
    
    for (const listener of listeners) {
      try {
        listener.apply(this, args)
      } catch (error) {
        console.error(`Error in event listener for '${event}':`, error)
      }
    }

    return true
  }

  /**
   * 移除所有事件监听器
   */
  removeAllListeners(event?: string): this {
    if (event) {
      delete this.events[event]
    } else {
      this.events = {}
    }
    return this
  }

  /**
   * 获取事件监听器列表
   */
  listeners(event: string): EventListener[] {
    return this.events[event] ? [...this.events[event]] : []
  }

  /**
   * 获取事件监听器数量
   */
  listenerCount(event: string): number {
    return this.events[event] ? this.events[event].length : 0
  }

  /**
   * 获取所有事件名称
   */
  eventNames(): string[] {
    return Object.keys(this.events)
  }

  /**
   * 设置最大监听器数量
   */
  setMaxListeners(n: number): this {
    this.maxListeners = n
    return this
  }

  /**
   * 获取最大监听器数量
   */
  getMaxListeners(): number {
    return this.maxListeners
  }

  /**
   * 在指定事件前添加监听器
   */
  prependListener(event: string, listener: EventListener): this {
    if (!this.events[event]) {
      this.events[event] = []
    }

    this.events[event].unshift(listener)
    return this
  }

  /**
   * 在指定事件前添加一次性监听器
   */
  prependOnceListener(event: string, listener: EventListener): this {
    const onceWrapper = (...args: any[]) => {
      this.off(event, onceWrapper)
      listener.apply(this, args)
    }

    this.prependListener(event, onceWrapper)
    return this
  }
}

/**
 * 创建事件发射器实例
 */
export function createEventEmitter(): EventEmitter {
  return new EventEmitter()
}

/**
 * 事件发射器装饰器
 */
export function eventEmitter<T extends { new (...args: any[]): {} }>(constructor: T) {
  return class extends constructor {
    private _eventEmitter = new EventEmitter()

    on(event: string, listener: EventListener) {
      return this._eventEmitter.on(event, listener)
    }

    once(event: string, listener: EventListener) {
      return this._eventEmitter.once(event, listener)
    }

    off(event: string, listener?: EventListener) {
      return this._eventEmitter.off(event, listener)
    }

    emit(event: string, ...args: any[]) {
      return this._eventEmitter.emit(event, ...args)
    }

    removeAllListeners(event?: string) {
      return this._eventEmitter.removeAllListeners(event)
    }

    listeners(event: string) {
      return this._eventEmitter.listeners(event)
    }

    listenerCount(event: string) {
      return this._eventEmitter.listenerCount(event)
    }

    eventNames() {
      return this._eventEmitter.eventNames()
    }

    setMaxListeners(n: number) {
      return this._eventEmitter.setMaxListeners(n)
    }

    getMaxListeners() {
      return this._eventEmitter.getMaxListeners()
    }
  }
}