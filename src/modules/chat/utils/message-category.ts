/**
 * 消息分类相关工具函数
 * <AUTHOR>
 * @date 2024-01-20
 * @version 1.0.0
 */

import type { MessageCategoryItem } from '../types/message-category'
import { MessageCategoryType } from '../types/message-category'

/**
 * 格式化消息时间
 * @param time 时间字符串或时间戳
 * @returns 格式化后的时间字符串
 */
export function formatMessageTime(time: string | number): string {
  if (!time) return ''
  
  const date = typeof time === 'string' ? new Date(time) : new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }
  
  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }
  
  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  }
  
  // 超过7天显示具体日期
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  
  // 同年只显示月日
  if (year === now.getFullYear()) {
    return `${month}-${day}`
  }
  
  // 不同年显示年月日
  return `${year}-${month}-${day}`
}

/**
 * 获取消息分类图标
 * @param categoryType 分类类型
 * @returns 图标名称
 */
export function getMessageCategoryIcon(categoryType: MessageCategoryType): string {
  const iconMap = {
    [MessageCategoryType.SYSTEM]: 'icon-system',
    [MessageCategoryType.ORDER]: 'icon-order',
    [MessageCategoryType.SERVICE]: 'icon-service',
    [MessageCategoryType.CHAT]: 'icon-chat'
  }
  return iconMap[categoryType] || 'icon-message'
}

/**
 * 获取消息分类颜色
 * @param categoryType 分类类型
 * @returns 颜色值
 */
export function getMessageCategoryColor(categoryType: MessageCategoryType): string {
  const colorMap = {
    [MessageCategoryType.SYSTEM]: '#ff4d4f',
    [MessageCategoryType.ORDER]: '#1890ff',
    [MessageCategoryType.SERVICE]: '#52c41a',
    [MessageCategoryType.CHAT]: '#722ed1'
  }
  return colorMap[categoryType] || '#666666'
}

/**
 * 获取消息分类标题
 * @param categoryType 分类类型
 * @returns 标题
 */
export function getMessageCategoryTitle(categoryType: MessageCategoryType): string {
  const titleMap = {
    [MessageCategoryType.SYSTEM]: '系统通知',
    [MessageCategoryType.ORDER]: '订单消息',
    [MessageCategoryType.SERVICE]: '客服消息',
    [MessageCategoryType.CHAT]: '聊天消息'
  }
  return titleMap[categoryType] || '未知分类'
}

/**
 * 判断消息是否未读
 * @param message 消息项
 * @returns 是否未读
 */
export function isMessageUnread(message: MessageCategoryItem): boolean {
  return !message.isRead
}

/**
 * 获取消息预览文本
 * @param message 消息项
 * @param maxLength 最大长度
 * @returns 预览文本
 */
export function getMessagePreview(message: MessageCategoryItem, maxLength: number = 100): string {
  if (!message.content) return ''
  
  let preview = message.content
  
  if (preview.length > maxLength) {
    return preview.substring(0, maxLength) + '...'
  }
  
  return preview
}

/**
 * 按时间排序消息
 * @param messages 消息列表
 * @param order 排序方式 'asc' | 'desc'
 * @returns 排序后的消息列表
 */
export function sortMessagesByTime(messages: MessageCategoryItem[], order: 'asc' | 'desc' = 'desc'): MessageCategoryItem[] {
  return [...messages].sort((a, b) => {
    const timeA = new Date(a.createdAt).getTime()
    const timeB = new Date(b.createdAt).getTime()
    
    return order === 'desc' ? timeB - timeA : timeA - timeB
  })
}

/**
 * 根据关键词过滤消息
 * @param messages 消息列表
 * @param keyword 关键词
 * @returns 过滤后的消息列表
 */
export function filterMessagesByKeyword(messages: MessageCategoryItem[], keyword: string): MessageCategoryItem[] {
  if (!keyword.trim()) return messages
  
  const lowerKeyword = keyword.toLowerCase().trim()
  
  return messages.filter(message => {
    // 搜索消息内容
    const content = getMessagePreview(message)
    if (content.toLowerCase().includes(lowerKeyword)) {
      return true
    }
    
    // 搜索发送者信息
    if ('participantInfo' in message && message.participantInfo?.participantName?.toLowerCase().includes(lowerKeyword)) {
      return true
    }
    
    // 搜索订单号（如果是订单消息）
    if ('orderNo' in message && message.orderNo?.toLowerCase().includes(lowerKeyword)) {
      return true
    }
    
    // 搜索商家名称（如果是订单消息）
    if ('merchantName' in message && message.merchantName?.toLowerCase().includes(lowerKeyword)) {
      return true
    }
    
    return false
  })
}

/**
 * 按日期分组消息
 * @param messages 消息列表
 * @returns 按日期分组的消息
 */
export function groupMessagesByDate(messages: MessageCategoryItem[]): Record<string, MessageCategoryItem[]> {
  const groups: Record<string, MessageCategoryItem[]> = {}
  
  messages.forEach(message => {
    const date = new Date(message.createdAt)
    const dateKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    
    if (!groups[dateKey]) {
      groups[dateKey] = []
    }
    
    groups[dateKey].push(message)
  })
  
  return groups
}

/**
 * 获取日期分组标题
 * @param dateKey 日期键
 * @returns 分组标题
 */
export function getDateGroupTitle(dateKey: string): string {
  const date = new Date(dateKey)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  
  if (messageDate.getTime() === today.getTime()) {
    return '今天'
  }
  
  if (messageDate.getTime() === yesterday.getTime()) {
    return '昨天'
  }
  
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  
  // 同年只显示月日
  if (year === now.getFullYear()) {
    return `${month}月${day}日`
  }
  
  // 不同年显示年月日
  return `${year}年${month}月${day}日`
}

/**
 * 计算未读消息总数
 * @param messages 消息列表
 * @returns 未读消息数量
 */
export function getUnreadCount(messages: MessageCategoryItem[]): number {
  return messages.filter(message => !message.isRead).length
}

/**
 * 获取最新消息时间
 * @param messages 消息列表
 * @returns 最新消息时间
 */
export function getLatestMessageTime(messages: MessageCategoryItem[]): string {
  if (messages.length === 0) return ''
  
  const sortedMessages = sortMessagesByTime(messages, 'desc')
  return sortedMessages[0].createdAt
}

/**
 * 生成消息唯一ID
 * @param prefix 前缀
 * @returns 唯一ID
 */
export function generateMessageId(prefix: string = 'msg'): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  return `${prefix}_${timestamp}_${random}`
}

/**
 * 验证消息数据
 * @param message 消息数据
 * @returns 验证结果
 */
export function validateMessage(message: any): { valid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (!message.id) {
    errors.push('消息ID不能为空')
  }
  
  if (!message.time) {
    errors.push('消息时间不能为空')
  }
  
  if (!message.content) {
    errors.push('消息内容不能为空')
  }
  
  if (!message.participantId) {
    errors.push('参与者ID不能为空')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 深度克隆消息对象
 * @param message 消息对象
 * @returns 克隆后的消息对象
 */
export function cloneMessage<T extends MessageCategoryItem>(message: T): T {
  return JSON.parse(JSON.stringify(message))
}

/**
 * 比较两个消息是否相同
 * @param messageA 消息A
 * @param messageB 消息B
 * @returns 是否相同
 */
export function isMessageEqual(messageA: MessageCategoryItem, messageB: MessageCategoryItem): boolean {
  return messageA.id === messageB.id && messageA.createdAt === messageB.createdAt
}

/**
 * 合并消息列表（去重）
 * @param listA 消息列表A
 * @param listB 消息列表B
 * @returns 合并后的消息列表
 */
export function mergeMessageLists(listA: MessageCategoryItem[], listB: MessageCategoryItem[]): MessageCategoryItem[] {
  const merged = [...listA]
  
  listB.forEach(messageB => {
    const exists = merged.some(messageA => isMessageEqual(messageA, messageB))
    if (!exists) {
      merged.push(messageB)
    }
  })
  
  return sortMessagesByTime(merged, 'desc')
}