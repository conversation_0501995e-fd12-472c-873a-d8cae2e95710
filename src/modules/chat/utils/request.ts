/**
 * 聊天模块请求工具
 * 专门为聊天模块处理HTTP请求，包含请求拦截器、响应拦截器和错误处理
 */
import axios from 'axios';
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios';
import { ElMessage } from 'element-plus';
import { getApiBaseUrl } from '@/utils/apiConfig';
import localforage from 'localforage';

// 聊天API响应基础接口
export interface ChatBaseResponse<T = any> {
  code: number;
  message: string;
  data?: T;
  detail?: string;
}

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: `${getApiBaseUrl()}/v1/chat`,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// token过期前的提前刷新时间（这里设置为5分钟）
const TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5分钟，单位毫秒

// 标志位，用于控制是否正在刷新token
let isRefreshingToken = false;
// 上次刷新token的时间
let lastTokenRefreshTime = 0;
// 刷新token的最小间隔时间（10秒）
const TOKEN_REFRESH_INTERVAL = 10 * 1000;

// URL 白名单，不需要权限认证的请求路径
const URL_WHITE_LIST = [
  '/health',
  '/status'
];

/**
 * 根据当前路由动态获取模块命名空间
 * @returns 模块命名空间
 */
function getCurrentNamespace(): string {
  // 获取当前路由路径
  const currentPath = window.location.pathname;

  // 根据路径判断当前所在的模块
  if (currentPath.startsWith('/admin')) {
    return 'admin';
  } else if (currentPath.startsWith('/merchant')) {
    return 'merchant';
  } else if (currentPath.startsWith('/user') || currentPath.startsWith('/runner')) {
    return 'user';
  }

  // 默认返回user（兼容旧版本）
  return 'user';
}

/**
 * 检查URL是否在白名单中
 * @param url 请求URL
 * @returns 是否在白名单中
 */
function isUrlInWhiteList(url: string | undefined): boolean {
  if (!url) return false;
  return URL_WHITE_LIST.some(whiteUrl => url.includes(whiteUrl));
}

// 检查token是否需要刷新
async function checkAndRefreshToken(config: InternalAxiosRequestConfig): Promise<string | null> {
  const namespace = getCurrentNamespace();
  const tokenKey = `${namespace}_access_token`;
  const tokenExpiryKey = `${namespace}_token_expiry`;

  // 如果是白名单内的请求，直接返回当前token（如果有）
  if (config.url && isUrlInWhiteList(config.url)) {
    const token = await localforage.getItem<string>(tokenKey);
    return token || null;
  }

  const token = await localforage.getItem<string>(tokenKey);
  const tokenExpireTime = await localforage.getItem<string>(tokenExpiryKey);

  if (!token) {
    return null;
  }

  // 如果token存在但过期时间不存在，设置一个默认过期时间（24小时）
  const now = Date.now();
  let expireTime = 0;
  if (!tokenExpireTime) {
    console.warn(`Token expiry time not found for ${namespace} module, using default expiry time`);
    // 设置一个默认的过期时间（当前时间+24小时）
    expireTime = now + 24 * 60 * 60 * 1000;
    // 保存默认过期时间以便后续使用
    await localforage.setItem(tokenExpiryKey, expireTime.toString());
  } else {
    expireTime = parseInt(tokenExpireTime);
  }

  // 如果token已经过期，强制刷新
  if (now >= expireTime) {
    console.warn(`Token has expired for ${namespace} module, must refresh before proceeding`);
    return await refreshTokenProcess(token, namespace);
  }

  // 如果token即将过期（阈值内）
  if (now >= expireTime - TOKEN_REFRESH_THRESHOLD) {
    console.log(`Token will expire soon for ${namespace} module, refreshing...`);
    // 立即刷新token但不阻塞当前请求
    refreshTokenProcess(token, namespace).catch(err => {
      console.error(`Background token refresh failed for ${namespace} module`, err);
    });
  }

  return token;
}

// 提取token刷新逻辑到独立函数
async function refreshTokenProcess(currentToken: string, namespace: string): Promise<string> {
  // 检查是否正在刷新token或者刚刚刷新过
  const now = Date.now();
  if (isRefreshingToken || (now - lastTokenRefreshTime < TOKEN_REFRESH_INTERVAL)) {
    console.log(`Token refresh in progress or recently refreshed for ${namespace} module. Using current token.`);
    return currentToken;
  }

  isRefreshingToken = true;
  try {
    // 这里应该调用对应模块的认证相关的store来刷新token
    // 暂时返回当前token，实际项目中需要实现token刷新逻辑
    console.log(`${namespace} module token refresh logic not implemented yet`);

    // 记录刷新时间
    lastTokenRefreshTime = Date.now();
    isRefreshingToken = false;

    return currentToken;
  } catch (error) {
    console.error(`Token refresh failed for ${namespace} module:`, error);
    isRefreshingToken = false;
    return currentToken;
  }
}

// 请求拦截器
service.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    const namespace = getCurrentNamespace();
    console.log(`Chat request interceptor [${namespace}] config url is:`, config.url);

    // 检查并刷新token
    const token = await checkAndRefreshToken(config);
    console.log(`获取的${namespace} token：${token ? '存在' : '不存在'}, URL: ${config.url}`);

    // 如果不是白名单中的请求，则添加token
    const isInWhiteList = isUrlInWhiteList(config.url);
    console.log(`是否在白名单中：${isInWhiteList}`);

    if (token && !isInWhiteList) {
      // 使用类型断言确保能够正确设置headers
      config.headers = config.headers || {};
      config.headers['Authorization'] = `Bearer ${token}`;
      console.log(`设置Authorization头部：Bearer ${token?.substring(0, 10)}...`);
    } else {
      console.log(`不设置Authorization头部，token状态: ${token ? '存在' : '不存在'}, 是否在白名单: ${isInWhiteList}`);
    }

    return config;
  },
  (error) => {
    console.error('Chat request error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  async (response: AxiosResponse<ChatBaseResponse<any>>) => {
    console.log('=== Chat响应拦截器开始 ===');
    console.log('响应URL:', response.config.url);
    console.log('响应状态码:', response.status);
    console.log('响应数据:', response.data);
    
    const { code, message, data } = response.data || {};
    console.log('=== Chat响应拦截器处理状态码 ===');
    console.log('状态码:', code, '消息:', message);
    
    // 请求成功
    if (code === 200) {
      console.log('=== Chat响应拦截器处理成功 ===');
      console.log('📊 数据分析:', {
        hasData: !!data,
        dataType: typeof data,
        dataKeys: data ? Object.keys(data) : 'null',
        dataValue: data
      });

      if(data) {
        console.log('✅ 返回实际数据:', data);
        return data;
      } else {
        console.warn('⚠️ 服务器返回的data字段为空，返回默认成功消息');
        console.warn('这可能导致消息发送逻辑出现问题');
        return {message: 'success'};
      }
    }

    // 处理特定错误码
    switch (code) {
      case 401:
        // 防止重复执行401处理逻辑
        if (isRefreshingToken) {
          console.log('Already handling 401 error for chat module, skipping duplicate handling');
          break;
        }

        // 获取当前命名空间并清除对应的token
        const namespace = getCurrentNamespace();
        await localforage.removeItem(`${namespace}_access_token`);

        // 清除sessionStorage和localStorage中的token
        sessionStorage.removeItem(`${namespace}_access_token`);

        console.log(`Chat拦截器：${namespace}模块需要重新登录`);
        ElMessage.error('登录已过期，请重新登录');
        break;
      case 403:
        ElMessage.error('没有权限访问该聊天资源');
        break;
      case 404:
        ElMessage.error('请求的聊天资源不存在');
        break;
      case 500:
        ElMessage.error('聊天服务器错误，请稍后重试');
        break;
      default:
        ElMessage.error(message || '聊天请求失败');
    }

    return Promise.reject(new Error(message || '聊天请求失败'));
  },
  (error) => {
    console.error('Chat response error:', error);
    const message = error.response?.data?.message || '聊天网络错误，请稍后重试';
    ElMessage.error(message);
    return Promise.reject(error);
  }
);

// 封装GET请求
export async function get<T>(url: string, params?: any): Promise<T> {
  const response = await service.get<ChatBaseResponse<T>>(url, { params });
  console.log('chat get请求响应', response);
  // 响应拦截器已经处理了数据结构，直接返回response
  return response as any;
}

// 封装POST请求
export async function post<T>(url: string, data?: any, config?: any): Promise<T> {
  const response = await service.post<ChatBaseResponse<T>>(url, data, config);
  console.log('chat post请求响应', response);
  return response as any;
}

// 封装PUT请求
export async function put<T>(url: string, data?: any): Promise<T> {
  const response = await service.put<ChatBaseResponse<T>>(url, data);
  return response as any;
}

// 封装DELETE请求
export async function del<T>(url: string, data?: any): Promise<T> {
  const response = await service.delete<ChatBaseResponse<T>>(url, { data });
  return response as any;
}

// 封装PATCH请求
export async function patch<T>(url: string, data?: any): Promise<T> {
  const response = await service.patch<ChatBaseResponse<T>>(url, data);
  return response as any;
}

export default service;
