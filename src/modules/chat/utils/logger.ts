/**
 * 日志工具类
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

export interface LogEntry {
  level: LogLevel
  message: string
  data?: any
  timestamp: number
  module: string
}

export interface LoggerConfig {
  level: LogLevel
  enableConsole: boolean
  enableStorage: boolean
  maxStorageEntries: number
  prefix: string
  colors: {
    debug: string
    info: string
    warn: string
    error: string
  }
}

/**
 * 日志记录器类
 */
export class Logger {
  private config: LoggerConfig
  private module: string
  private storage: LogEntry[] = []

  constructor(module: string, config?: Partial<LoggerConfig>) {
    this.module = module
    this.config = {
      level: LogLevel.INFO,
      enableConsole: true,
      enableStorage: false,
      maxStorageEntries: 1000,
      prefix: '[Chat]',
      colors: {
        debug: '#6c757d',
        info: '#17a2b8',
        warn: '#ffc107',
        error: '#dc3545'
      },
      ...config
    }
  }

  /**
   * 调试日志
   */
  debug(message: string, data?: any): void {
    this.log(LogLevel.DEBUG, message, data)
  }

  /**
   * 信息日志
   */
  info(message: string, data?: any): void {
    this.log(LogLevel.INFO, message, data)
  }

  /**
   * 警告日志
   */
  warn(message: string, data?: any): void {
    this.log(LogLevel.WARN, message, data)
  }

  /**
   * 错误日志
   */
  error(message: string, data?: any): void {
    this.log(LogLevel.ERROR, message, data)
  }

  /**
   * 记录日志
   */
  log(level: LogLevel, message: string, data?: any): void {
    if (level < this.config.level) {
      return
    }

    const entry: LogEntry = {
      level,
      message,
      data,
      timestamp: Date.now(),
      module: this.module
    }

    // 控制台输出
    if (this.config.enableConsole) {
      this.logToConsole(entry)
    }

    // 存储日志
    if (this.config.enableStorage) {
      this.logToStorage(entry)
    }
  }

  /**
   * 输出到控制台
   */
  private logToConsole(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toISOString()
    const levelName = LogLevel[entry.level]
    const color = this.config.colors[levelName.toLowerCase() as keyof typeof this.config.colors]
    
    const prefix = `${this.config.prefix}[${this.module}]`
    const logMessage = `${timestamp} ${prefix} ${entry.message}`

    const style = `color: ${color}; font-weight: bold;`

    switch (entry.level) {
      case LogLevel.DEBUG:
        if (entry.data !== undefined) {
          console.debug(`%c${logMessage}`, style, entry.data)
        } else {
          console.debug(`%c${logMessage}`, style)
        }
        break
      case LogLevel.INFO:
        if (entry.data !== undefined) {
          console.info(`%c${logMessage}`, style, entry.data)
        } else {
          console.info(`%c${logMessage}`, style)
        }
        break
      case LogLevel.WARN:
        if (entry.data !== undefined) {
          console.warn(`%c${logMessage}`, style, entry.data)
        } else {
          console.warn(`%c${logMessage}`, style)
        }
        break
      case LogLevel.ERROR:
        if (entry.data !== undefined) {
          console.error(`%c${logMessage}`, style, entry.data)
        } else {
          console.error(`%c${logMessage}`, style)
        }
        break
    }
  }

  /**
   * 存储日志
   */
  private logToStorage(entry: LogEntry): void {
    this.storage.push(entry)

    // 限制存储数量
    if (this.storage.length > this.config.maxStorageEntries) {
      this.storage = this.storage.slice(-this.config.maxStorageEntries)
    }
  }

  /**
   * 获取存储的日志
   */
  getLogs(filter?: {
    level?: LogLevel
    module?: string
    startTime?: number
    endTime?: number
    limit?: number
  }): LogEntry[] {
    let logs = [...this.storage]

    if (filter) {
      if (filter.level !== undefined) {
        logs = logs.filter(log => log.level >= filter.level!)
      }
      if (filter.module) {
        logs = logs.filter(log => log.module === filter.module)
      }
      if (filter.startTime) {
        logs = logs.filter(log => log.timestamp >= filter.startTime!)
      }
      if (filter.endTime) {
        logs = logs.filter(log => log.timestamp <= filter.endTime!)
      }
      if (filter.limit) {
        logs = logs.slice(-filter.limit)
      }
    }

    return logs
  }

  /**
   * 清空日志
   */
  clearLogs(): void {
    this.storage = []
  }

  /**
   * 导出日志
   */
  exportLogs(format: 'json' | 'text' = 'json'): string {
    if (format === 'json') {
      return JSON.stringify(this.storage, null, 2)
    }

    return this.storage.map(entry => {
      const timestamp = new Date(entry.timestamp).toISOString()
      const levelName = LogLevel[entry.level]
      const dataStr = entry.data ? ` ${JSON.stringify(entry.data)}` : ''
      return `${timestamp} [${levelName}] [${entry.module}] ${entry.message}${dataStr}`
    }).join('\n')
  }

  /**
   * 设置日志级别
   */
  setLevel(level: LogLevel): void {
    this.config.level = level
  }

  /**
   * 获取日志级别
   */
  getLevel(): LogLevel {
    return this.config.level
  }

  /**
   * 启用/禁用控制台输出
   */
  setConsoleEnabled(enabled: boolean): void {
    this.config.enableConsole = enabled
  }

  /**
   * 启用/禁用存储
   */
  setStorageEnabled(enabled: boolean): void {
    this.config.enableStorage = enabled
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**
   * 创建子日志记录器
   */
  createChild(module: string): Logger {
    return new Logger(`${this.module}:${module}`, this.config)
  }
}

/**
 * 全局日志记录器管理
 */
export class LoggerManager {
  private loggers: Map<string, Logger> = new Map()
  private globalConfig: Partial<LoggerConfig> = {}

  /**
   * 获取或创建日志记录器
   */
  getLogger(module: string): Logger {
    if (!this.loggers.has(module)) {
      const logger = new Logger(module, this.globalConfig)
      this.loggers.set(module, logger)
    }
    return this.loggers.get(module)!
  }

  /**
   * 设置全局配置
   */
  setGlobalConfig(config: Partial<LoggerConfig>): void {
    this.globalConfig = { ...this.globalConfig, ...config }
    
    // 更新所有现有日志记录器的配置
    this.loggers.forEach(logger => {
      logger.updateConfig(config)
    })
  }

  /**
   * 获取所有日志记录器
   */
  getAllLoggers(): Logger[] {
    return Array.from(this.loggers.values())
  }

  /**
   * 清空所有日志
   */
  clearAllLogs(): void {
    this.loggers.forEach(logger => logger.clearLogs())
  }

  /**
   * 导出所有日志
   */
  exportAllLogs(format: 'json' | 'text' = 'json'): string {
    const allLogs: LogEntry[] = []
    
    this.loggers.forEach(logger => {
      allLogs.push(...logger.getLogs())
    })

    // 按时间戳排序
    allLogs.sort((a, b) => a.timestamp - b.timestamp)

    if (format === 'json') {
      return JSON.stringify(allLogs, null, 2)
    }

    return allLogs.map(entry => {
      const timestamp = new Date(entry.timestamp).toISOString()
      const levelName = LogLevel[entry.level]
      const dataStr = entry.data ? ` ${JSON.stringify(entry.data)}` : ''
      return `${timestamp} [${levelName}] [${entry.module}] ${entry.message}${dataStr}`
    }).join('\n')
  }
}

// 全局日志管理器实例
const loggerManager = new LoggerManager()

/**
 * 获取日志记录器
 */
export function getLogger(module: string): Logger {
  return loggerManager.getLogger(module)
}

/**
 * 设置全局日志配置
 */
export function setGlobalLogConfig(config: Partial<LoggerConfig>): void {
  loggerManager.setGlobalConfig(config)
}

/**
 * 获取所有日志记录器
 */
export function getAllLoggers(): Logger[] {
  return loggerManager.getAllLoggers()
}

/**
 * 清空所有日志
 */
export function clearAllLogs(): void {
  loggerManager.clearAllLogs()
}

/**
 * 导出所有日志
 */
export function exportAllLogs(format: 'json' | 'text' = 'json'): string {
  return loggerManager.exportAllLogs(format)
}

/**
 * 日志装饰器
 */
export function log(level: LogLevel = LogLevel.INFO) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const logger = getLogger(target.constructor.name)

    descriptor.value = function (...args: any[]) {
      logger.log(level, `Calling method ${propertyKey}`, { args })
      
      try {
        const result = originalMethod.apply(this, args)
        
        if (result instanceof Promise) {
          return result.then(
            (value) => {
              logger.log(level, `Method ${propertyKey} completed successfully`, { result: value })
              return value
            },
            (error) => {
              logger.error(`Method ${propertyKey} failed`, error)
              throw error
            }
          )
        } else {
          logger.log(level, `Method ${propertyKey} completed successfully`, { result })
          return result
        }
      } catch (error) {
        logger.error(`Method ${propertyKey} failed`, error)
        throw error
      }
    }

    return descriptor
  }
}