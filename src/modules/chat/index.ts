/**
 * 聊天模块主入口文件
 */

// 导出类型定义
export * from './types'

// 导出常量
// export * from './constants' // 暂时注释掉避免重复导出

// 导出工具函数
export * from './utils'

// 导出服务层
export * from './services'

// 导出API层 - 只导出函数，避免类型重复导出
export {
  getMessages,
  sendTextMessage,
  sendMediaMessage,
  sendMediaUrlMessage,
  markMessageRead,
  searchMessages,
  deleteMessage,
  recallMessage,
  createSession,
  getSessions,
  getSessionDetail,
  joinSession,
  markSessionRead,
  updateSession,
  deleteSession,
  searchSessions,
  sendNotification,
  getNotifications,
  uploadFile,
  getFileDownloadUrl,
  getOnlineUsers,
  getUserInfo,
  getChatStats,
  getMessageStats,
  getMessageCategories,
  getUnreadCount,
  getCategorySessions,
  getCategoryMessages,
  markMessageAsRead,
  markCategoryAsRead,
  markSessionAsRead,
  getWebSocketToken,
  validateWebSocketConnection
} from './api'

// 导出状态管理
export * from './stores'

// 导出消息分类组件
// 暂时注释掉以避免导入错误
// export { default as MessageCategoryList } from './components/MessageCategoryList.vue'
// export { default as MessageCategoryDetail } from './components/MessageCategoryDetail.vue'
// export { default as MessageCategoryManager } from './components/MessageCategoryManager.vue'
// export { default as MessageCategorySettings } from './components/MessageCategorySettings.vue'

// 导出消息分类类型
export type {
  MessageCategory,
  MessageCategoryItem,
  MessageCategoryStats,
  MessageCategorySettings as MessageCategorySettingsType
} from './types/message-category'

// 导出消息分类枚举
export {
  MessageCategoryType,
  SystemNotificationType,
  OrderNotificationType,
  ServiceMessageType
} from './types/message-category'

// 导出消息分类服务
export {
  MessageCategoryService,
  messageCategoryService
} from './services/message-category.service'

// 导出主要服务实例
export { ChatClient } from './services/chat-client'
export { WebSocketManager } from './services/websocket'
export { MessageService } from './services/message'
export { SessionService } from './services/session'
export { NotificationService } from './services/notification'
export { FileService } from './services/file'
export { ChatApi } from './api'

// 导出主要状态管理
export {
  useChatStore,
  useMessageStore,
  useSessionStore,
  useNotificationStore,
  useFileStore
} from './stores'

// 导出工具类
export { EventEmitter, createEventEmitter } from './utils/event-emitter'
export { Logger, LoggerManager } from './utils/logger'

// 默认导出聊天客户端
export { ChatClient as default } from './services/chat-client'