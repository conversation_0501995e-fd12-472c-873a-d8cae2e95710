/**
 * 消息分类相关常量定义
 * <AUTHOR>
 * @date 2024-01-20
 * @version 1.0.0
 * @description 定义消息分类相关的常量、配置和默认值
 */

import type { MessageCategory, MessageCategoryType } from '../types/message-category'

// 消息分类配置
export const MESSAGE_CATEGORY_CONFIG = {
  // 分页配置
  PAGE_SIZE: {
    DEFAULT: 20,
    MIN: 10,
    MAX: 100
  },
  
  // 刷新间隔
  REFRESH_INTERVAL: {
    CHAT: 5000,      // 聊天消息 5秒
    SYSTEM: 30000,   // 系统通知 30秒
    ORDER: 10000,    // 订单消息 10秒
    SERVICE: 3000    // 客服消息 3秒
  },
  
  // 自动标记已读延迟（毫秒）
  AUTO_READ_DELAY: {
    CHAT: 2000,
    SYSTEM: 5000,
    ORDER: 3000,
    SERVICE: 1000
  },
  
  // 消息保存天数
  STORAGE_DAYS: {
    CHAT: 30,
    SYSTEM: 90,
    ORDER: 180,
    SERVICE: 60
  },
  
  // 搜索防抖延迟
  SEARCH_DEBOUNCE_DELAY: 300,
  
  // 最大未读数显示
  MAX_UNREAD_DISPLAY: 99
} as const

// 默认消息分类配置
export const DEFAULT_MESSAGE_CATEGORIES: MessageCategory[] = [
  {
    type: 'chat' as MessageCategoryType,
    title: '聊天消息',
    icon: 'chat',
    color: '#1890ff',
    description: '与好友、商家的聊天消息',
    unreadCount: 0,
    totalCount: 0,
    enabled: true,
    sortOrder: 1
  },
  {
    type: 'system' as MessageCategoryType,
    title: '系统通知',
    icon: 'notification',
    color: '#52c41a',
    description: '系统维护、版本更新等通知',
    unreadCount: 0,
    totalCount: 0,
    enabled: true,
    sortOrder: 2
  },
  {
    type: 'order' as MessageCategoryType,
    title: '订单消息',
    icon: 'shopping-cart',
    color: '#fa8c16',
    description: '订单状态、支付、退款等消息',
    unreadCount: 0,
    totalCount: 0,
    enabled: true,
    sortOrder: 3
  },
  {
    type: 'service' as MessageCategoryType,
    title: '客服消息',
    icon: 'customer-service',
    color: '#722ed1',
    description: '客服咨询、售后服务等消息',
    unreadCount: 0,
    totalCount: 0,
    enabled: true,
    sortOrder: 4
  }
]

// 系统通知类型配置
export const SYSTEM_NOTIFICATION_TYPES = {
  system_maintenance: {
    title: '系统维护',
    icon: 'tool',
    color: '#fa541c',
    priority: 'high'
  },
  version_update: {
    title: '版本更新',
    icon: 'rocket',
    color: '#1890ff',
    priority: 'normal'
  },
  security_notice: {
    title: '安全公告',
    icon: 'safety',
    color: '#f5222d',
    priority: 'urgent'
  },
  activity_notice: {
    title: '活动通知',
    icon: 'gift',
    color: '#eb2f96',
    priority: 'normal'
  },
  policy_update: {
    title: '政策更新',
    icon: 'file-text',
    color: '#13c2c2',
    priority: 'normal'
  },
  feature_announcement: {
    title: '功能公告',
    icon: 'star',
    color: '#52c41a',
    priority: 'low'
  }
} as const

// 订单通知类型配置
export const ORDER_NOTIFICATION_TYPES = {
  payment_success: {
    title: '支付成功',
    icon: 'check-circle',
    color: '#52c41a',
    priority: 'normal'
  },
  payment_failed: {
    title: '支付失败',
    icon: 'close-circle',
    color: '#f5222d',
    priority: 'high'
  },
  order_confirmed: {
    title: '订单确认',
    icon: 'file-done',
    color: '#1890ff',
    priority: 'normal'
  },
  order_shipped: {
    title: '订单发货',
    icon: 'car',
    color: '#fa8c16',
    priority: 'normal'
  },
  order_delivered: {
    title: '订单送达',
    icon: 'home',
    color: '#52c41a',
    priority: 'normal'
  },
  order_completed: {
    title: '订单完成',
    icon: 'trophy',
    color: '#52c41a',
    priority: 'low'
  },
  order_cancelled: {
    title: '订单取消',
    icon: 'stop',
    color: '#8c8c8c',
    priority: 'normal'
  },
  refund_applied: {
    title: '退款申请',
    icon: 'undo',
    color: '#fa8c16',
    priority: 'normal'
  },
  refund_approved: {
    title: '退款通过',
    icon: 'check',
    color: '#52c41a',
    priority: 'normal'
  },
  refund_rejected: {
    title: '退款拒绝',
    icon: 'close',
    color: '#f5222d',
    priority: 'high'
  },
  refund_success: {
    title: '退款成功',
    icon: 'dollar',
    color: '#52c41a',
    priority: 'normal'
  },
  refund_failed: {
    title: '退款失败',
    icon: 'exclamation-circle',
    color: '#f5222d',
    priority: 'high'
  }
} as const

// 客服消息类型配置
export const SERVICE_MESSAGE_TYPES = {
  customer_inquiry: {
    title: '客户咨询',
    icon: 'question-circle',
    color: '#1890ff',
    priority: 'normal'
  },
  agent_response: {
    title: '客服回复',
    icon: 'message',
    color: '#52c41a',
    priority: 'normal'
  },
  session_assigned: {
    title: '会话分配',
    icon: 'user-add',
    color: '#722ed1',
    priority: 'normal'
  },
  session_transferred: {
    title: '会话转接',
    icon: 'swap',
    color: '#fa8c16',
    priority: 'normal'
  },
  session_closed: {
    title: '会话关闭',
    icon: 'close-square',
    color: '#8c8c8c',
    priority: 'low'
  },
  satisfaction_survey: {
    title: '满意度调查',
    icon: 'like',
    color: '#eb2f96',
    priority: 'low'
  },
  auto_reply: {
    title: '自动回复',
    icon: 'robot',
    color: '#13c2c2',
    priority: 'low'
  }
} as const

// 消息优先级配置
export const MESSAGE_PRIORITY_CONFIG = {
  urgent: {
    title: '紧急',
    color: '#f5222d',
    weight: 4,
    icon: 'exclamation-circle'
  },
  high: {
    title: '高',
    color: '#fa541c',
    weight: 3,
    icon: 'arrow-up'
  },
  normal: {
    title: '普通',
    color: '#1890ff',
    weight: 2,
    icon: 'minus'
  },
  low: {
    title: '低',
    color: '#8c8c8c',
    weight: 1,
    icon: 'arrow-down'
  }
} as const

// 消息状态配置
export const MESSAGE_STATUS_CONFIG = {
  unread: {
    title: '未读',
    color: '#f5222d',
    icon: 'mail'
  },
  read: {
    title: '已读',
    color: '#8c8c8c',
    icon: 'mail-open'
  },
  top: {
    title: '置顶',
    color: '#fa8c16',
    icon: 'pushpin'
  },
  muted: {
    title: '免打扰',
    color: '#8c8c8c',
    icon: 'bell-slash'
  }
} as const

// 消息分类路由配置
export const MESSAGE_CATEGORY_ROUTES = {
  chat: '/chat/messages',
  system: '/messages/system',
  order: '/messages/order',
  service: '/messages/service'
} as const

// 消息分类API端点
export const MESSAGE_CATEGORY_ENDPOINTS = {
  // 获取分类列表
  GET_CATEGORIES: '/api/v1/messages/categories',
  
  // 获取分类统计
  GET_CATEGORY_STATS: '/api/v1/messages/categories/stats',
  
  // 获取分类消息列表
  GET_CATEGORY_MESSAGES: '/api/v1/messages/categories/:type',
  
  // 标记分类消息已读
  MARK_CATEGORY_READ: '/api/v1/messages/categories/:type/read',
  
  // 删除分类消息
  DELETE_CATEGORY_MESSAGES: '/api/v1/messages/categories/:type/delete',
  
  // 置顶分类消息
  TOGGLE_CATEGORY_TOP: '/api/v1/messages/categories/:type/top',
  
  // 搜索分类消息
  SEARCH_CATEGORY_MESSAGES: '/api/v1/messages/categories/search',
  
  // 更新分类设置
  UPDATE_CATEGORY_SETTINGS: '/api/v1/messages/categories/:type/settings',
  
  // 系统通知相关
  GET_SYSTEM_NOTIFICATIONS: '/api/v1/messages/system/notifications',
  GET_SYSTEM_NOTIFICATION_DETAIL: '/api/v1/messages/system/notifications/:id',
  MARK_SYSTEM_NOTIFICATIONS_READ: '/api/v1/messages/system/notifications/read',
  DELETE_SYSTEM_NOTIFICATIONS: '/api/v1/messages/system/notifications/delete',
  
  // 订单消息相关
  GET_ORDER_NOTIFICATIONS: '/api/v1/messages/order/notifications',
  GET_ORDER_NOTIFICATION_DETAIL: '/api/v1/messages/order/notifications/:id',
  MARK_ORDER_NOTIFICATIONS_READ: '/api/v1/messages/order/notifications/read',
  DELETE_ORDER_NOTIFICATIONS: '/api/v1/messages/order/notifications/delete',
  
  // 客服消息相关
  GET_SERVICE_MESSAGES: '/api/v1/messages/service/messages',
  GET_SERVICE_MESSAGE_DETAIL: '/api/v1/messages/service/messages/:id',
  MARK_SERVICE_MESSAGES_READ: '/api/v1/messages/service/messages/read',
  DELETE_SERVICE_MESSAGES: '/api/v1/messages/service/messages/delete'
} as const

// 消息分类默认设置
export const DEFAULT_CATEGORY_SETTINGS = {
  enabled: true,
  notificationEnabled: true,
  soundEnabled: true,
  vibrationEnabled: true,
  showPreview: true,
  autoMarkRead: false,
  autoMarkReadDelay: 3,
  maxStorageDays: 30
} as const

// 消息分类图标映射
export const MESSAGE_CATEGORY_ICONS = {
  chat: 'chat',
  system: 'notification',
  order: 'shopping-cart',
  service: 'customer-service'
} as const

// 消息分类颜色映射
export const MESSAGE_CATEGORY_COLORS = {
  chat: '#1890ff',
  system: '#52c41a',
  order: '#fa8c16',
  service: '#722ed1'
} as const