/**
 * 消息相关常量
 */

// 消息状态
export enum MessageStatus {
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed'
}

// 消息优先级
export enum MessagePriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

// 文件上传状态
export enum FileUploadStatus {
  PENDING = 'pending',
  UPLOADING = 'uploading',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// 消息配置常量
export const MESSAGE_CONFIG = {
  MAX_LENGTH: 2000,
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_FILE_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ],
  TYPING_TIMEOUT: 3000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000
} as const

// 消息类型图标映射
export const MESSAGE_TYPE_ICONS = {
  text: 'message',
  image: 'image',
  file: 'file',
  audio: 'microphone',
  video: 'video',
  location: 'location',
  system: 'info'
} as const
