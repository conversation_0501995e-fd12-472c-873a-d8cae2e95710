/**
 * 聊天模块常量定义
 * <AUTHOR>
 * @date 2024-01-20
 * @version 1.0.0
 * @description 定义聊天模块相关的常量、配置和默认值
 */

// 导出消息分类相关常量
export * from './message-category'

// WebSocket连接配置
export const WEBSOCKET_CONFIG = {
  // 重连配置
  RECONNECT: {
    MAX_ATTEMPTS: 5,
    INITIAL_DELAY: 1000,
    MAX_DELAY: 30000,
    BACKOFF_FACTOR: 2,
    JITTER: true
  },
  
  // 心跳配置
  HEARTBEAT: {
    INTERVAL: 30000, // 30秒
    TIMEOUT: 10000,  // 10秒
    MAX_MISSED: 3
  },
  
  // 认证配置
  AUTH: {
    TIMEOUT: 10000, // 10秒
    RETRY_ATTEMPTS: 3
  },
  
  // 消息配置
  MESSAGE: {
    QUEUE_SIZE: 100,
    TIMEOUT: 30000,
    RETRY_ATTEMPTS: 3
  }
} as const

// 消息相关常量
export const MESSAGE_CONFIG = {
  // 消息长度限制
  MAX_TEXT_LENGTH: 5000,
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  MAX_IMAGE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_VOICE_DURATION: 300, // 5分钟
  MAX_VIDEO_DURATION: 600, // 10分钟
  
  // 支持的文件类型
  SUPPORTED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  SUPPORTED_FILE_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv'
  ],
  SUPPORTED_AUDIO_TYPES: ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a'],
  SUPPORTED_VIDEO_TYPES: ['video/mp4', 'video/webm', 'video/ogg'],
  
  // 消息状态更新间隔
  STATUS_UPDATE_INTERVAL: 1000,
  
  // 消息重试配置
  RETRY: {
    MAX_ATTEMPTS: 3,
    INITIAL_DELAY: 1000,
    BACKOFF_FACTOR: 2
  }
} as const

// 会话相关常量
export const SESSION_CONFIG = {
  // 会话超时时间（毫秒）
  TIMEOUT: {
    PRESALE_CONSULTATION: 30 * 60 * 1000, // 30分钟
    AFTER_SALE_SERVICE: 60 * 60 * 1000,   // 1小时
    FRIEND_CHAT: 0, // 永不超时
    CUSTOMER_SERVICE: 45 * 60 * 1000      // 45分钟
  },
  
  // 会话列表刷新间隔
  REFRESH_INTERVAL: 30000, // 30秒
  
  // 会话历史消息加载数量
  HISTORY_PAGE_SIZE: 20,
  
  // 会话搜索防抖延迟
  SEARCH_DEBOUNCE_DELAY: 300,
  
  // 会话自动关闭警告时间
  AUTO_CLOSE_WARNING_TIME: 5 * 60 * 1000 // 5分钟
} as const

// 通知相关常量
export const NOTIFICATION_CONFIG = {
  // 通知显示时间
  DISPLAY_DURATION: {
    SUCCESS: 3000,
    INFO: 5000,
    WARNING: 8000,
    ERROR: 10000
  },
  
  // 通知音效
  SOUNDS: {
    NEW_MESSAGE: '/sounds/new-message.mp3',
    SESSION_CREATED: '/sounds/session-created.mp3',
    ERROR: '/sounds/error.mp3',
    SUCCESS: '/sounds/success.mp3'
  },
  
  // 桌面通知配置
  DESKTOP: {
    ICON: '/icons/chat-notification.png',
    BADGE: '/icons/chat-badge.png',
    TIMEOUT: 5000
  },
  
  // 通知批量操作限制
  BATCH_OPERATION_LIMIT: 100
} as const

// UI相关常量
export const UI_CONFIG = {
  // 聊天窗口配置
  CHAT_WINDOW: {
    MIN_WIDTH: 320,
    MIN_HEIGHT: 480,
    DEFAULT_WIDTH: 400,
    DEFAULT_HEIGHT: 600,
    MAX_WIDTH: 800,
    MAX_HEIGHT: 900
  },
  
  // 消息列表配置
  MESSAGE_LIST: {
    SCROLL_THRESHOLD: 100, // 距离底部多少像素时自动滚动
    LOAD_MORE_THRESHOLD: 200, // 距离顶部多少像素时加载更多
    VIRTUAL_SCROLL_ITEM_HEIGHT: 80,
    MAX_VISIBLE_ITEMS: 50
  },
  
  // 输入框配置
  INPUT: {
    MIN_HEIGHT: 40,
    MAX_HEIGHT: 120,
    PLACEHOLDER_TYPING_SPEED: 50,
    AUTO_RESIZE_DEBOUNCE: 100
  },
  
  // 动画配置
  ANIMATION: {
    MESSAGE_FADE_IN: 200,
    MESSAGE_SLIDE_IN: 300,
    TYPING_INDICATOR: 1500,
    LOADING_SPINNER: 1000
  },
  
  // 主题配置
  THEME: {
    COLORS: {
      PRIMARY: '#1890ff',
      SUCCESS: '#52c41a',
      WARNING: '#faad14',
      ERROR: '#f5222d',
      INFO: '#1890ff'
    },
    DARK_MODE: {
      BACKGROUND: '#1f1f1f',
      SURFACE: '#2d2d2d',
      TEXT: '#ffffff',
      TEXT_SECONDARY: '#b3b3b3'
    }
  }
} as const

// API相关常量
export const API_CONFIG = {
  // 基础路径
  BASE_PATH: '/api/v1/chat',
  
  // 端点
  ENDPOINTS: {
    // 消息相关
    MESSAGES: '/messages',
    SEND_MESSAGE: '/messages/send',
    MESSAGE_HISTORY: '/messages/history',
    MARK_READ: '/messages/mark-read',
    SEARCH_MESSAGES: '/messages/search',
    
    // 会话相关
    SESSIONS: '/sessions',
    CREATE_SESSION: '/sessions/create',
    SESSION_DETAIL: '/sessions/:id',
    UPDATE_SESSION: '/sessions/:id',
    CLOSE_SESSION: '/sessions/:id/close',
    TRANSFER_SESSION: '/sessions/:id/transfer',
    
    // 文件上传
    UPLOAD_FILE: '/files/upload',
    UPLOAD_IMAGE: '/files/upload/image',
    UPLOAD_VOICE: '/files/upload/voice',
    UPLOAD_VIDEO: '/files/upload/video',
    
    // 用户相关
    USER_STATUS: '/users/status',
    ONLINE_USERS: '/users/online',
    USER_PROFILE: '/users/profile',
    
    // 通知相关
    NOTIFICATIONS: '/notifications',
    NOTIFICATION_SETTINGS: '/notifications/settings',
    MARK_NOTIFICATION_READ: '/notifications/mark-read',
    
    // 统计相关
    CHAT_STATISTICS: '/statistics',
    MESSAGE_STATISTICS: '/statistics/messages',
    SESSION_STATISTICS: '/statistics/sessions',
    
    // 配置相关
    CHAT_CONFIG: '/config',
    QUICK_REPLIES: '/config/quick-replies',
    MESSAGE_TEMPLATES: '/config/templates'
  },
  
  // 请求超时时间
  TIMEOUT: {
    DEFAULT: 10000,
    UPLOAD: 60000,
    DOWNLOAD: 30000
  },
  
  // 重试配置
  RETRY: {
    MAX_ATTEMPTS: 3,
    DELAY: 1000,
    BACKOFF_FACTOR: 2
  }
} as const

// 错误代码
export const ERROR_CODES = {
  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  CONNECTION_TIMEOUT: 'CONNECTION_TIMEOUT',
  REQUEST_TIMEOUT: 'REQUEST_TIMEOUT',
  
  // 认证错误
  AUTH_FAILED: 'AUTH_FAILED',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  
  // 消息错误
  MESSAGE_TOO_LONG: 'MESSAGE_TOO_LONG',
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  UNSUPPORTED_FILE_TYPE: 'UNSUPPORTED_FILE_TYPE',
  MESSAGE_SEND_FAILED: 'MESSAGE_SEND_FAILED',
  
  // 会话错误
  SESSION_NOT_FOUND: 'SESSION_NOT_FOUND',
  SESSION_CLOSED: 'SESSION_CLOSED',
  SESSION_TIMEOUT: 'SESSION_TIMEOUT',
  SESSION_LIMIT_EXCEEDED: 'SESSION_LIMIT_EXCEEDED',
  
  // 用户错误
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_OFFLINE: 'USER_OFFLINE',
  USER_BLOCKED: 'USER_BLOCKED',
  
  // 系统错误
  SERVER_ERROR: 'SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // WebSocket错误
  WS_CONNECTION_FAILED: 'WS_CONNECTION_FAILED',
  WS_AUTH_FAILED: 'WS_AUTH_FAILED',
  WS_RECONNECT_FAILED: 'WS_RECONNECT_FAILED',
  
  // 文件上传错误
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  UPLOAD_TIMEOUT: 'UPLOAD_TIMEOUT',
  UPLOAD_CANCELLED: 'UPLOAD_CANCELLED'
} as const

// 事件名称
export const EVENT_NAMES = {
  // WebSocket事件
  WS_CONNECTED: 'ws:connected',
  WS_DISCONNECTED: 'ws:disconnected',
  WS_ERROR: 'ws:error',
  WS_RECONNECTING: 'ws:reconnecting',
  WS_MESSAGE_RECEIVED: 'ws:message:received',
  
  // 消息事件
  MESSAGE_SENT: 'message:sent',
  MESSAGE_RECEIVED: 'message:received',
  MESSAGE_DELIVERED: 'message:delivered',
  MESSAGE_READ: 'message:read',
  MESSAGE_FAILED: 'message:failed',
  
  // 会话事件
  SESSION_CREATED: 'session:created',
  SESSION_UPDATED: 'session:updated',
  SESSION_CLOSED: 'session:closed',
  SESSION_JOINED: 'session:joined',
  SESSION_LEFT: 'session:left',
  
  // 用户事件
  USER_ONLINE: 'user:online',
  USER_OFFLINE: 'user:offline',
  USER_TYPING: 'user:typing',
  USER_STOP_TYPING: 'user:stop_typing',
  
  // 通知事件
  NOTIFICATION_RECEIVED: 'notification:received',
  NOTIFICATION_READ: 'notification:read',
  NOTIFICATION_CLICKED: 'notification:clicked',
  
  // 聊天客户端事件
  CHAT_CLIENT_CONNECTED: 'chat:client:connected',
  CHAT_CLIENT_DISCONNECTED: 'chat:client:disconnected',
  CHAT_CLIENT_ERROR: 'chat:client:error',
  
  // UI事件
  CHAT_WINDOW_OPENED: 'ui:chat_window:opened',
  CHAT_WINDOW_CLOSED: 'ui:chat_window:closed',
  CHAT_WINDOW_MINIMIZED: 'ui:chat_window:minimized',
  CHAT_WINDOW_MAXIMIZED: 'ui:chat_window:maximized'
} as const

// 本地存储键名
export const STORAGE_KEYS = {
  // 用户设置
  USER_SETTINGS: 'chat:user_settings',
  NOTIFICATION_SETTINGS: 'chat:notification_settings',
  THEME_SETTINGS: 'chat:theme_settings',
  
  // 会话数据
  SESSION_CACHE: 'chat:session_cache',
  MESSAGE_DRAFTS: 'chat:message_drafts',
  QUICK_REPLIES: 'chat:quick_replies',
  
  // 临时数据
  TEMP_FILES: 'chat:temp_files',
  UPLOAD_PROGRESS: 'chat:upload_progress',
  
  // 统计数据
  USAGE_STATISTICS: 'chat:usage_statistics',
  PERFORMANCE_METRICS: 'chat:performance_metrics'
} as const

// 正则表达式
export const REGEX_PATTERNS = {
  // URL匹配
  URL: /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/g,
  
  // 邮箱匹配
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  
  // 电话号码匹配
  PHONE: /^1[3-9]\d{9}$/,
  
  // 表情符号匹配
  EMOJI: /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu,
  
  // 文件名匹配
  FILENAME: /^[^<>:"/\\|?*]+$/,
  
  // 特殊字符过滤
  SPECIAL_CHARS: /[<>"'&]/g
} as const

// 默认配置
export const DEFAULT_CONFIG = {
  // 聊天客户端配置
  CHAT_CLIENT: {
    autoConnect: true,
    enableHeartbeat: true,
    enableReconnect: true,
    enableNotifications: true,
    enableSound: true,
    enableDesktopNotification: true,
    maxConcurrentSessions: 10,
    messageHistoryLimit: 1000
  },
  
  // 用户设置
  USER_SETTINGS: {
    theme: 'light',
    language: 'zh-CN',
    fontSize: 14,
    enableAnimations: true,
    enableVibration: true,
    autoSaveMessages: true,
    showTimestamp: true,
    showReadStatus: true,
    enableTypingIndicator: true
  },
  
  // 通知设置
  NOTIFICATION_SETTINGS: {
    enabled: true,
    sound: true,
    desktop: true,
    vibration: true,
    showPreview: true,
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '08:00'
    }
  }
} as const

// 导出所有常量
export * from './websocket'
export * from './message'
export * from './session'
export * from './notification'
export * from './ui'