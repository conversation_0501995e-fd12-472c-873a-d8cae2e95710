/**
 * 会话相关常量
 */

// 会话配置常量
export const SESSION_CONFIG = {
  MAX_PARTICIPANTS: 50,
  DEFAULT_PAGE_SIZE: 20,
  MAX_SEARCH_RESULTS: 100,
  SESSION_TIMEOUT: 30 * 60 * 1000, // 30分钟
  TYPING_INDICATOR_TIMEOUT: 3000,
  AUTO_ARCHIVE_DAYS: 30
} as const

// 会话排序选项
export enum SessionSortBy {
  CREATED_AT = 'created_at',
  UPDATED_AT = 'updated_at',
  PRIORITY = 'priority',
  STATUS = 'status',
  PARTICIPANT_COUNT = 'participant_count'
}

// 会话过滤选项
export enum SessionFilterType {
  ALL = 'all',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived',
  MY_SESSIONS = 'my_sessions',
  UNREAD = 'unread'
}

// 会话操作类型
export enum SessionOperationType {
  CREATE = 'create',
  UPDATE = 'update',
  CLOSE = 'close',
  ARCHIVE = 'archive',
  DELETE = 'delete',
  JOIN = 'join',
  LEAVE = 'leave',
  TRANSFER = 'transfer',
  INVITE = 'invite'
}

// 会话权限
export enum SessionPermission {
  READ = 'read',
  WRITE = 'write',
  ADMIN = 'admin',
  OWNER = 'owner'
}

// 默认会话设置
export const DEFAULT_SESSION_SETTINGS = {
  mute_notifications: false,
  auto_reply_enabled: false,
  auto_reply_message: '',
  max_response_time: 24 * 60 * 60, // 24小时（秒）
  tags: []
} as const
