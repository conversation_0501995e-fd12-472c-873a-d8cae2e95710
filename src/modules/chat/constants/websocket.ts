/**
 * WebSocket 相关常量
 */

// WebSocket 连接状态
export enum WebSocketState {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

// WebSocket 事件类型
export enum WebSocketEventType {
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  MESSAGE = 'message',
  ERROR = 'error',
  RECONNECT = 'reconnect',
  HEARTBEAT = 'heartbeat'
}

// WebSocket 消息类型
export enum WebSocketMessageType {
  CHAT_MESSAGE = 'chat_message',
  TYPING = 'typing',
  USER_STATUS = 'user_status',
  SESSION_UPDATE = 'session_update',
  NOTIFICATION = 'notification',
  HEARTBEAT = 'heartbeat',
  ACK = 'ack'
}

// WebSocket 配置常量
export const WEBSOCKET_CONFIG = {
  RECONNECT_INTERVAL: 3000,
  MAX_RECONNECT_ATTEMPTS: 5,
  HEARTBEAT_INTERVAL: 30000,
  CONNECTION_TIMEOUT: 10000,
  MESSAGE_TIMEOUT: 5000
} as const

// WebSocket 错误代码
export enum WebSocketErrorCode {
  CONNECTION_FAILED = 'CONNECTION_FAILED',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  MESSAGE_SEND_FAILED = 'MESSAGE_SEND_FAILED',
  HEARTBEAT_TIMEOUT = 'HEARTBEAT_TIMEOUT',
  PROTOCOL_ERROR = 'PROTOCOL_ERROR'
}
