/**
 * 通知相关常量
 */

// 通知配置常量
export const NOTIFICATION_CONFIG = {
  MAX_VISIBLE: 5,
  DEFAULT_DURATION: 5000,
  AUTO_CLOSE_DELAY: 3000,
  BATCH_SIZE: 10,
  QUEUE_PROCESS_INTERVAL: 1000,
  MAX_RETRY_ATTEMPTS: 3
} as const

// 通知位置
export enum NotificationPosition {
  TOP_RIGHT = 'top-right',
  TOP_LEFT = 'top-left',
  BOTTOM_RIGHT = 'bottom-right',
  BOTTOM_LEFT = 'bottom-left',
  TOP_CENTER = 'top-center',
  BOTTOM_CENTER = 'bottom-center'
}

// 通知动画类型
export enum NotificationAnimation {
  SLIDE = 'slide',
  FADE = 'fade',
  BOUNCE = 'bounce',
  ZOOM = 'zoom'
}

// 通知声音类型
export enum NotificationSound {
  DEFAULT = 'default',
  MESSAGE = 'message',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  NONE = 'none'
}

// 通知图标映射
export const NOTIFICATION_ICONS = {
  success: 'check-circle',
  error: 'x-circle',
  warning: 'alert-triangle',
  info: 'info',
  message: 'message-circle'
} as const

// 通知颜色映射
export const NOTIFICATION_COLORS = {
  success: '#10b981',
  error: '#ef4444',
  warning: '#f59e0b',
  info: '#3b82f6',
  message: '#6366f1'
} as const
