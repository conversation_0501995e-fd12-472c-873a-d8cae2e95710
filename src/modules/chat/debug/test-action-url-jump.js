/**
 * 测试订单消息跳转功能
 * 验证是否正确使用notification_data中的action_url
 */

// 测试数据：模拟不同格式的订单消息
const testMessages = {
  // 1. 包含notification_data.action_url的订单消息
  orderWithActionUrl: {
    id: 'msg_001',
    type: 'notification',
    event: 'user_order_status_update',
    content: '您的订单状态已更新',
    notification_data: {
      order_id: 123,
      action_url: '/user/takeout/order/123?from=notification',
      status: 'confirmed'
    },
    metadata: {
      order_id: 123,
      notification_type: 'order_status_update'
    }
  },

  // 2. 包含notification_data.action_url的退款消息
  refundWithActionUrl: {
    id: 'msg_002',
    type: 'notification',
    event: 'user_refund_result',
    content: '您的退款申请已通过',
    notification_data: {
      refund_id: 456,
      order_id: 123,
      action_url: '/user/refund/detail/456?from=notification',
      status: 'approved'
    },
    metadata: {
      refund_id: 456,
      order_id: 123,
      notification_type: 'refund_result'
    }
  },

  // 3. 没有action_url的订单消息（回退到原有逻辑）
  orderWithoutActionUrl: {
    id: 'msg_003',
    type: 'notification',
    event: 'user_order_payment_success',
    content: '订单支付成功',
    notification_data: {
      order_id: 789
    },
    metadata: {
      order_id: 789,
      notification_type: 'order_payment_success'
    }
  },

  // 4. 没有action_url的退款消息（回退到原有逻辑）
  refundWithoutActionUrl: {
    id: 'msg_004',
    type: 'notification',
    event: 'user_refund_progress',
    content: '退款正在处理中',
    notification_data: {
      refund_id: 999,
      order_id: 888
    },
    metadata: {
      refund_id: 999,
      order_id: 888,
      notification_type: 'refund_progress'
    }
  },

  // 5. metadata中包含action_url的消息
  orderWithMetadataActionUrl: {
    id: 'msg_005',
    type: 'notification',
    event: 'user_order_completed',
    content: '订单已完成',
    notification_data: {
      order_id: 555
    },
    metadata: {
      order_id: 555,
      action_url: '/user/takeout/order/555?tab=review',
      notification_type: 'order_completed'
    }
  }
}

// 模拟跳转函数（用于测试，不会真正跳转）
let mockJumpResults = []

const mockWindowLocationHref = (url) => {
  mockJumpResults.push({
    timestamp: new Date().toISOString(),
    url: url,
    type: 'jump'
  })
  console.log(`🔗 模拟跳转到: ${url}`)
}

// 模拟getCurrentUserType函数
const mockGetCurrentUserType = () => 'user' // 可以改为 'merchant' 测试商家端

// 模拟订单详情跳转函数
const mockHandleOrderDetailJump = (message) => {
  let actionUrl = null

  // 优先从notification_data中获取action_url
  if (message.notification_data && message.notification_data.action_url) {
    actionUrl = message.notification_data.action_url
  } else if (message.metadata && message.metadata.action_url) {
    actionUrl = message.metadata.action_url
  } else if (message.data && message.data.action_url) {
    actionUrl = message.data.action_url
  }

  // 如果有action_url，直接使用
  if (actionUrl) {
    console.log('🔗 使用action_url跳转到订单详情:', actionUrl)
    mockWindowLocationHref(actionUrl)
    return
  }

  // 如果没有action_url，回退到原有逻辑（基于订单ID构建URL）
  let orderId = null

  // 从不同的数据结构中提取订单ID
  if (message.metadata && message.metadata.order_id) {
    orderId = message.metadata.order_id
  } else if (message.notification_data && message.notification_data.order_id) {
    orderId = message.notification_data.order_id
  } else if (message.data && message.data.order_id) {
    orderId = message.data.order_id
  }

  if (orderId) {
    // 根据用户类型确定跳转地址
    const userType = mockGetCurrentUserType()
    let orderUrl = ''

    if (userType === 'merchant') {
      orderUrl = `/merchant/order/detail/${orderId}`
    } else {
      orderUrl = `/user/takeout/order/${orderId}`
    }

    console.log('🔗 使用订单ID跳转到订单详情:', orderUrl)
    mockWindowLocationHref(orderUrl)
  } else {
    console.warn('⚠️ 无法从消息中提取action_url或订单ID:', message)
  }
}

// 模拟退款详情跳转函数
const mockHandleRefundDetailJump = (message) => {
  let actionUrl = null

  // 优先从notification_data中获取action_url
  if (message.notification_data && message.notification_data.action_url) {
    actionUrl = message.notification_data.action_url
  } else if (message.metadata && message.metadata.action_url) {
    actionUrl = message.metadata.action_url
  } else if (message.data && message.data.action_url) {
    actionUrl = message.data.action_url
  }

  // 如果有action_url，直接使用
  if (actionUrl) {
    console.log('🔗 使用action_url跳转到退款详情:', actionUrl)
    mockWindowLocationHref(actionUrl)
    return
  }

  // 如果没有action_url，回退到原有逻辑（基于退款ID或订单ID构建URL）
  let refundId = null
  let orderId = null

  // 从不同的数据结构中提取退款ID或订单ID
  if (message.metadata) {
    refundId = message.metadata.refund_id
    orderId = message.metadata.order_id
  } else if (message.notification_data) {
    refundId = message.notification_data.refund_id
    orderId = message.notification_data.order_id
  } else if (message.data) {
    refundId = message.data.refund_id
    orderId = message.data.order_id
  }

  // 优先使用退款ID，如果没有则使用订单ID
  const targetId = refundId || orderId

  if (targetId) {
    // 根据用户类型确定跳转地址
    const userType = mockGetCurrentUserType()
    let refundUrl = ''

    if (userType === 'merchant') {
      // 商家端退款管理页面
      refundUrl = `/merchant/refund/detail/${targetId}`
    } else {
      // 用户端退款详情页面
      refundUrl = `/user/refund/detail/${targetId}`
    }

    console.log('🔗 使用ID跳转到退款详情:', refundUrl)
    mockWindowLocationHref(refundUrl)
  } else {
    console.warn('⚠️ 无法从消息中提取action_url、退款ID或订单ID:', message)
  }
}

// 判断是否为订单消息
const mockIsOrderMessage = (message) => {
  return message.notification_type === 'order_status_update' ||
         message.notification_type === 'order_payment_success' ||
         message.notification_type === 'order_completed' ||
         (message.metadata && (
           message.metadata.notification_type === 'order_status_update' ||
           message.metadata.notification_type === 'order_payment_success' ||
           message.metadata.notification_type === 'order_completed'
         )) ||
         (message.type === 'notification' && message.content && message.content.includes('订单'))
}

// 判断是否为退款消息
const mockIsRefundMessage = (message) => {
  return message.notification_type === 'user_refund_result' ||
         (message.metadata && message.metadata.is_refund_notification) ||
         (message.type === 'notification' && message.content && message.content.includes('退款'))
}

// 模拟跳转处理函数
const mockHandleOrderJump = (message) => {
  try {
    // 判断是订单消息还是退款消息
    if (mockIsOrderMessage(message)) {
      mockHandleOrderDetailJump(message)
    } else if (mockIsRefundMessage(message)) {
      mockHandleRefundDetailJump(message)
    }
  } catch (error) {
    console.error('❌ 跳转失败:', error)
  }
}

// 测试函数
const runTests = () => {
  console.log('🚀 开始测试订单消息跳转功能')
  console.log('=' .repeat(50))
  
  mockJumpResults = [] // 清空结果
  
  // 测试所有消息
  Object.entries(testMessages).forEach(([key, message]) => {
    console.log(`\n📋 测试消息: ${key}`)
    console.log(`消息内容: ${message.content}`)
    console.log(`消息数据:`, {
      notification_data: message.notification_data,
      metadata: message.metadata
    })
    
    mockHandleOrderJump(message)
    console.log('-'.repeat(30))
  })
  
  // 显示测试结果汇总
  console.log('\n📊 测试结果汇总:')
  console.log('=' .repeat(50))
  mockJumpResults.forEach((result, index) => {
    console.log(`${index + 1}. ${result.url}`)
  })
  
  // 验证结果
  console.log('\n✅ 验证结果:')
  const expectedResults = [
    '/user/takeout/order/123?from=notification', // orderWithActionUrl
    '/user/refund/detail/456?from=notification',  // refundWithActionUrl
    '/user/takeout/order/789',                    // orderWithoutActionUrl (回退逻辑)
    '/user/refund/detail/999',                    // refundWithoutActionUrl (回退逻辑)
    '/user/takeout/order/555?tab=review'          // orderWithMetadataActionUrl
  ]
  
  let allPassed = true
  expectedResults.forEach((expected, index) => {
    const actual = mockJumpResults[index]?.url
    const passed = actual === expected
    console.log(`${passed ? '✅' : '❌'} 测试 ${index + 1}: ${passed ? '通过' : '失败'}`)
    if (!passed) {
      console.log(`   期望: ${expected}`)
      console.log(`   实际: ${actual}`)
      allPassed = false
    }
  })
  
  console.log(`\n🎯 总体结果: ${allPassed ? '所有测试通过' : '部分测试失败'}`)
}

// 导出测试函数供浏览器控制台使用
if (typeof window !== 'undefined') {
  window.testActionUrlJump = runTests
  window.testMessages = testMessages
  window.mockHandleOrderJump = mockHandleOrderJump
  
  console.log('🔧 订单跳转测试脚本已加载')
  console.log('使用 testActionUrlJump() 运行测试')
  console.log('使用 mockHandleOrderJump(testMessages.orderWithActionUrl) 测试单个消息')
}

// Node.js环境下直接运行测试
if (typeof module !== 'undefined' && module.exports) {
  runTests()
}
