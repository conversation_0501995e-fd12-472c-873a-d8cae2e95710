/**
 * WebSocket消息处理调试脚本
 * 用于调试WebSocket消息处理流程
 */

// 调试WebSocket消息处理流程
window.debugWebSocketMessage = function() {
  console.log('🔧 [WebSocket Debug] 开始调试WebSocket消息处理流程')
  
  // 1. 检查消息路由器状态
  const chatStore = window.$pinia?.state?.value?.chat
  if (!chatStore) {
    console.error('❌ [WebSocket Debug] ChatStore未找到')
    return
  }
  
  console.log('📊 [WebSocket Debug] ChatStore状态:', {
    hasMessageRouter: !!chatStore.messageRouter,
    hasChatClient: !!chatStore.chatClient,
    isConnected: chatStore.chatClient?.isConnected?.()
  })
  
  // 2. 检查消息路由器
  if (chatStore.messageRouter) {
    const routerStats = chatStore.messageRouter.getStats()
    const handlers = chatStore.messageRouter.getHandlers()
    
    console.log('📊 [WebSocket Debug] 消息路由器状态:', routerStats)
    console.log('📊 [WebSocket Debug] 注册的处理器:', handlers.map(h => ({
      name: h.name,
      type: h.type,
      initialized: h.getStatus().initialized,
      supportedTypes: h.getSupportedMessageTypes().slice(0, 5) // 只显示前5个类型
    })))
    
    // 检查UserMessageHandler是否支持user_refund_result
    const userHandler = handlers.find(h => h.name === 'User')
    if (userHandler) {
      const supportedTypes = userHandler.getSupportedMessageTypes()
      const supportsRefundResult = supportedTypes.includes('user_refund_result')
      console.log('📊 [WebSocket Debug] UserMessageHandler支持user_refund_result:', supportsRefundResult)
      
      if (!supportsRefundResult) {
        console.error('❌ [WebSocket Debug] UserMessageHandler不支持user_refund_result消息类型')
        console.log('📊 [WebSocket Debug] 支持的消息类型:', supportedTypes)
      }
    } else {
      console.error('❌ [WebSocket Debug] UserMessageHandler未找到')
    }
  } else {
    console.error('❌ [WebSocket Debug] 消息路由器未初始化')
  }
  
  // 3. 模拟发送user_refund_result消息
  console.log('🔧 [WebSocket Debug] 模拟发送user_refund_result消息')
  
  const testMessage = {
    type: "notification",
    event: "user_refund_result",
    session_id: 0,
    timestamp: Date.now(),
    data: {
      action_type: "refund_detail",
      action_url: "/refund/detail/14",
      message: "您的退款申请已通过，退款金额 11.00 元将在3-5个工作日内退回",
      notification_type: "refund_result",
      order_id: 88,
      order_no: "202507281713526957",
      priority: 3,
      refund_amount: 11,
      refund_id: 14,
      refund_no: "RF175379187988",
      remark: "",
      status: "approved",
      timestamp: Date.now()
    }
  }
  
  if (chatStore.messageRouter) {
    console.log('🔧 [WebSocket Debug] 通过消息路由器处理测试消息')
    chatStore.messageRouter.routeMessage(testMessage)
      .then(results => {
        console.log('✅ [WebSocket Debug] 消息处理结果:', results)
        results.forEach((result, index) => {
          if (result.success) {
            console.log(`✅ [WebSocket Debug] 处理器 ${index + 1} 处理成功:`, result.data)
          } else {
            console.error(`❌ [WebSocket Debug] 处理器 ${index + 1} 处理失败:`, result.error)
          }
        })
      })
      .catch(error => {
        console.error('❌ [WebSocket Debug] 消息路由器处理失败:', error)
      })
  } else {
    console.error('❌ [WebSocket Debug] 无法发送测试消息，消息路由器未初始化')
  }
}

// 调试消息处理器初始化状态
window.debugMessageHandlers = function() {
  console.log('🔧 [Handler Debug] 开始调试消息处理器状态')
  
  const chatStore = window.$pinia?.state?.value?.chat
  if (!chatStore?.messageRouter) {
    console.error('❌ [Handler Debug] 消息路由器未找到')
    return
  }
  
  const handlers = chatStore.messageRouter.getHandlers()
  console.log('📊 [Handler Debug] 找到处理器数量:', handlers.length)
  
  handlers.forEach(handler => {
    const status = handler.getStatus()
    const supportedTypes = handler.getSupportedMessageTypes()
    
    console.log(`📊 [Handler Debug] 处理器 ${handler.name}:`, {
      type: handler.type,
      initialized: status.initialized,
      messageCount: status.messageCount,
      errorCount: status.errorCount,
      supportedTypesCount: supportedTypes.length,
      supportsNotification: supportedTypes.includes('notification'),
      supportsUserRefundResult: supportedTypes.includes('user_refund_result'),
      canHandleNotification: handler.canHandle('notification'),
      canHandleUserRefundResult: handler.canHandle('user_refund_result')
    })
    
    // 如果是UserMessageHandler，显示更多详细信息
    if (handler.name === 'User') {
      console.log('📊 [Handler Debug] UserMessageHandler详细信息:')
      console.log('  - 支持的退款相关消息类型:', supportedTypes.filter(type => type.includes('refund')))
      console.log('  - 支持的通知相关消息类型:', supportedTypes.filter(type => type.includes('notification')))
    }
  })
}

// 检查Element Plus是否可用
window.debugElementPlus = function() {
  console.log('🔧 [ElementPlus Debug] 检查Element Plus可用性')
  
  // 检查是否可以动态导入Element Plus
  import('element-plus').then(ElementPlus => {
    console.log('✅ [ElementPlus Debug] Element Plus动态导入成功:', !!ElementPlus.ElNotification)
    
    // 测试显示通知
    if (ElementPlus.ElNotification) {
      console.log('🔧 [ElementPlus Debug] 测试显示通知')
      ElementPlus.ElNotification({
        title: '测试通知',
        message: '这是一个测试通知，用于验证Element Plus是否正常工作',
        type: 'success',
        duration: 3000
      })
    }
  }).catch(error => {
    console.error('❌ [ElementPlus Debug] Element Plus动态导入失败:', error)
  })
}

// 监听WebSocket原始消息
window.debugWebSocketRawMessages = function() {
  console.log('🔧 [WebSocket Debug] 开始监听WebSocket原始消息')
  
  const chatStore = window.$pinia?.state?.value?.chat
  if (!chatStore?.chatClient) {
    console.error('❌ [WebSocket Debug] ChatClient未找到')
    return
  }
  
  // 监听rawMessage事件
  chatStore.chatClient.on('rawMessage', (message) => {
    console.log('📨 [WebSocket Debug] 收到原始WebSocket消息:', {
      type: message.type,
      event: message.event,
      timestamp: message.timestamp,
      hasData: !!message.data,
      dataKeys: message.data ? Object.keys(message.data) : []
    })
    
    // 如果是user_refund_result消息，显示详细信息
    if (message.event === 'user_refund_result') {
      console.log('🎯 [WebSocket Debug] 收到退款结果消息:', message)
    }
  })
  
  console.log('✅ [WebSocket Debug] 已开始监听WebSocket原始消息')
}

// 一键调试所有功能
window.debugAll = function() {
  console.log('🚀 [Debug] 开始全面调试')
  window.debugMessageHandlers()
  window.debugWebSocketMessage()
  window.debugElementPlus()
  window.debugWebSocketRawMessages()
}

console.log('🔧 [WebSocket Debug] 调试脚本已加载，可用函数:')
console.log('  - debugWebSocketMessage(): 调试WebSocket消息处理')
console.log('  - debugMessageHandlers(): 调试消息处理器状态')
console.log('  - debugElementPlus(): 检查Element Plus可用性')
console.log('  - debugWebSocketRawMessages(): 监听WebSocket原始消息')
console.log('  - debugAll(): 一键调试所有功能')
