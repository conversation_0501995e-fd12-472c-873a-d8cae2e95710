// 简单测试订单跳转逻辑
console.log('🚀 开始测试订单消息跳转功能')

// 测试消息
const testMessage = {
  id: 'msg_001',
  type: 'notification',
  event: 'user_order_status_update',
  content: '您的订单状态已更新',
  notification_data: {
    order_id: 123,
    action_url: '/user/takeout/order/123?from=notification',
    status: 'confirmed'
  },
  metadata: {
    order_id: 123,
    notification_type: 'order_status_update'
  }
}

// 测试函数
function testOrderJump(message) {
  let actionUrl = null

  // 优先从notification_data中获取action_url
  if (message.notification_data && message.notification_data.action_url) {
    actionUrl = message.notification_data.action_url
    console.log('✅ 找到notification_data.action_url:', actionUrl)
  } else if (message.metadata && message.metadata.action_url) {
    actionUrl = message.metadata.action_url
    console.log('✅ 找到metadata.action_url:', actionUrl)
  } else if (message.data && message.data.action_url) {
    actionUrl = message.data.action_url
    console.log('✅ 找到data.action_url:', actionUrl)
  }

  // 如果有action_url，直接使用
  if (actionUrl) {
    console.log('🔗 使用action_url跳转到订单详情:', actionUrl)
    return actionUrl
  }

  // 回退逻辑
  let orderId = null
  if (message.metadata && message.metadata.order_id) {
    orderId = message.metadata.order_id
  } else if (message.notification_data && message.notification_data.order_id) {
    orderId = message.notification_data.order_id
  }

  if (orderId) {
    const orderUrl = `/user/takeout/order/${orderId}`
    console.log('🔗 使用订单ID跳转到订单详情:', orderUrl)
    return orderUrl
  }

  console.warn('⚠️ 无法从消息中提取action_url或订单ID')
  return null
}

// 运行测试
const result = testOrderJump(testMessage)
console.log('📊 测试结果:', result)

// 验证结果
const expected = '/user/takeout/order/123?from=notification'
const passed = result === expected
console.log(`${passed ? '✅' : '❌'} 测试${passed ? '通过' : '失败'}`)
if (!passed) {
  console.log(`期望: ${expected}`)
  console.log(`实际: ${result}`)
}

console.log('🎯 测试完成')
