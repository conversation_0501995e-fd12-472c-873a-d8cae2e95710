<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>退款通知测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #337ecc;
        }
        .log-area {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .status.error {
            background: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
        .status.info {
            background: #f4f4f5;
            border: 1px solid #909399;
            color: #909399;
        }
    </style>
</head>
<body>
    <h1>退款通知消息处理测试</h1>
    
    <div class="test-section">
        <h3>测试说明</h3>
        <p>这个页面用于测试WebSocket退款通知消息的处理流程。请确保：</p>
        <ul>
            <li>已经在浏览器中打开了主应用</li>
            <li>WebSocket连接已建立</li>
            <li>消息处理器已正确初始化</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>快速测试</h3>
        <button class="test-button" onclick="testMessageHandlerSupport()">检查消息处理器支持</button>
        <button class="test-button" onclick="testRefundApproved()">测试退款通过通知</button>
        <button class="test-button" onclick="testRefundRejected()">测试退款拒绝通知</button>
        <button class="test-button" onclick="testRefundProcessing()">测试退款处理中通知</button>
        <button class="test-button" onclick="clearLog()">清空日志</button>
    </div>

    <div class="test-section">
        <h3>测试状态</h3>
        <div id="status" class="status info">等待测试...</div>
    </div>

    <div class="test-section">
        <h3>测试日志</h3>
        <div id="log" class="log-area">点击上方按钮开始测试...\n</div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            
            if (type === 'error') {
                console.error(message);
            } else {
                console.log(message);
            }
        }

        function setStatus(message, type = 'info') {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function clearLog() {
            logElement.textContent = '';
            setStatus('日志已清空', 'info');
        }

        function testMessageHandlerSupport() {
            log('🔧 开始检查消息处理器支持...');
            setStatus('检查中...', 'info');

            try {
                // 检查是否有全局的chatStore
                if (typeof window.parent !== 'undefined' && window.parent.$pinia) {
                    const chatStore = window.parent.$pinia.state.value.chat;
                    if (chatStore && chatStore.messageRouter) {
                        const handlers = chatStore.messageRouter.getHandlers();
                        log(`✅ 找到 ${handlers.length} 个消息处理器`);
                        
                        const userHandler = handlers.find(h => h.name === 'User');
                        if (userHandler) {
                            const supportedTypes = userHandler.getSupportedMessageTypes();
                            const supportsNotification = supportedTypes.includes('notification');
                            const supportsRefundResult = supportedTypes.includes('user_refund_result');
                            
                            log(`📊 UserMessageHandler状态:`);
                            log(`  - 支持notification类型: ${supportsNotification ? '✅' : '❌'}`);
                            log(`  - 支持user_refund_result类型: ${supportsRefundResult ? '✅' : '❌'}`);
                            log(`  - 总支持类型数: ${supportedTypes.length}`);
                            
                            if (supportsNotification && supportsRefundResult) {
                                setStatus('消息处理器支持检查通过', 'success');
                            } else {
                                setStatus('消息处理器支持检查失败', 'error');
                            }
                        } else {
                            log('❌ 未找到UserMessageHandler');
                            setStatus('未找到UserMessageHandler', 'error');
                        }
                    } else {
                        log('❌ 未找到messageRouter');
                        setStatus('未找到messageRouter', 'error');
                    }
                } else {
                    log('❌ 无法访问主应用的状态');
                    setStatus('无法访问主应用状态', 'error');
                }
            } catch (error) {
                log(`❌ 检查失败: ${error.message}`);
                setStatus('检查失败', 'error');
            }
        }

        function sendTestMessage(messageData, testName) {
            log(`🔧 发送${testName}测试消息...`);
            setStatus(`测试${testName}中...`, 'info');

            try {
                if (typeof window.parent !== 'undefined' && window.parent.$pinia) {
                    const chatStore = window.parent.$pinia.state.value.chat;
                    if (chatStore && chatStore.messageRouter) {
                        log(`📨 消息内容: ${JSON.stringify(messageData, null, 2)}`);
                        
                        chatStore.messageRouter.routeMessage(messageData)
                            .then(results => {
                                log(`✅ 消息处理完成，结果数量: ${results.length}`);
                                results.forEach((result, index) => {
                                    if (result.success) {
                                        log(`  ✅ 处理器 ${index + 1}: 成功`);
                                        log(`     数据: ${JSON.stringify(result.data, null, 2)}`);
                                    } else {
                                        log(`  ❌ 处理器 ${index + 1}: 失败 - ${result.error}`);
                                    }
                                });
                                setStatus(`${testName}测试完成`, 'success');
                            })
                            .catch(error => {
                                log(`❌ 消息处理失败: ${error.message}`);
                                setStatus(`${testName}测试失败`, 'error');
                            });
                    } else {
                        log('❌ 未找到messageRouter');
                        setStatus('未找到messageRouter', 'error');
                    }
                } else {
                    log('❌ 无法访问主应用的状态');
                    setStatus('无法访问主应用状态', 'error');
                }
            } catch (error) {
                log(`❌ 发送消息失败: ${error.message}`);
                setStatus('发送消息失败', 'error');
            }
        }

        function testRefundApproved() {
            const testMessage = {
                type: "notification",
                event: "user_refund_result",
                session_id: 0,
                timestamp: Date.now(),
                data: {
                    action_type: "refund_detail",
                    action_url: "/refund/detail/14",
                    message: "您的退款申请已通过，退款金额 11.00 元将在3-5个工作日内退回",
                    notification_type: "refund_result",
                    order_id: 88,
                    order_no: "202507281713526957",
                    priority: 3,
                    refund_amount: 11,
                    refund_id: 14,
                    refund_no: "RF175379187988",
                    remark: "",
                    status: "approved",
                    timestamp: Date.now()
                }
            };
            sendTestMessage(testMessage, '退款通过');
        }

        function testRefundRejected() {
            const testMessage = {
                type: "notification",
                event: "user_refund_result",
                session_id: 0,
                timestamp: Date.now(),
                data: {
                    message: "很抱歉，您的退款申请被拒绝",
                    notification_type: "refund_result",
                    status: "rejected",
                    refund_amount: 50,
                    refund_id: 15,
                    refund_no: "RF175379187989",
                    order_id: 89,
                    order_no: "202507281713526958",
                    priority: 2
                }
            };
            sendTestMessage(testMessage, '退款拒绝');
        }

        function testRefundProcessing() {
            const testMessage = {
                type: "notification",
                event: "user_refund_result",
                session_id: 0,
                timestamp: Date.now(),
                data: {
                    message: "您的退款申请正在处理中，请耐心等待",
                    notification_type: "refund_result",
                    status: "processing",
                    refund_amount: 75,
                    refund_id: 16,
                    refund_no: "RF175379187990",
                    order_id: 90,
                    order_no: "202507281713526959",
                    priority: 1
                }
            };
            sendTestMessage(testMessage, '退款处理中');
        }

        // 页面加载完成后自动检查
        window.addEventListener('load', () => {
            log('🚀 测试页面已加载');
            setTimeout(() => {
                testMessageHandlerSupport();
            }, 1000);
        });
    </script>
</body>
</html>
