// 全面测试订单和退款消息跳转逻辑
console.log('🚀 开始全面测试消息跳转功能')
console.log('=' .repeat(50))

// 测试数据
const testCases = [
  {
    name: '订单消息 - 有notification_data.action_url',
    message: {
      notification_data: {
        order_id: 123,
        action_url: '/user/takeout/order/123?from=notification'
      },
      metadata: { order_id: 123 }
    },
    expected: '/user/takeout/order/123?from=notification',
    type: 'order'
  },
  {
    name: '订单消息 - 有metadata.action_url',
    message: {
      notification_data: { order_id: 123 },
      metadata: {
        order_id: 123,
        action_url: '/user/takeout/order/123?tab=review'
      }
    },
    expected: '/user/takeout/order/123?tab=review',
    type: 'order'
  },
  {
    name: '订单消息 - 无action_url，回退到订单ID',
    message: {
      notification_data: { order_id: 456 },
      metadata: { order_id: 456 }
    },
    expected: '/user/takeout/order/456',
    type: 'order'
  },
  {
    name: '退款消息 - 有notification_data.action_url',
    message: {
      notification_data: {
        refund_id: 789,
        order_id: 123,
        action_url: '/user/refund/detail/789?from=notification'
      },
      metadata: { refund_id: 789, order_id: 123 }
    },
    expected: '/user/refund/detail/789?from=notification',
    type: 'refund'
  },
  {
    name: '退款消息 - 无action_url，回退到退款ID',
    message: {
      notification_data: { refund_id: 999, order_id: 888 },
      metadata: { refund_id: 999, order_id: 888 }
    },
    expected: '/user/refund/detail/999',
    type: 'refund'
  }
]

// 订单跳转逻辑
function testOrderJump(message) {
  let actionUrl = null

  // 优先从notification_data中获取action_url
  if (message.notification_data && message.notification_data.action_url) {
    actionUrl = message.notification_data.action_url
  } else if (message.metadata && message.metadata.action_url) {
    actionUrl = message.metadata.action_url
  } else if (message.data && message.data.action_url) {
    actionUrl = message.data.action_url
  }

  // 如果有action_url，直接使用
  if (actionUrl) {
    return actionUrl
  }

  // 回退逻辑：基于订单ID构建URL
  let orderId = null
  if (message.metadata && message.metadata.order_id) {
    orderId = message.metadata.order_id
  } else if (message.notification_data && message.notification_data.order_id) {
    orderId = message.notification_data.order_id
  } else if (message.data && message.data.order_id) {
    orderId = message.data.order_id
  }

  if (orderId) {
    return `/user/takeout/order/${orderId}`
  }

  return null
}

// 退款跳转逻辑
function testRefundJump(message) {
  let actionUrl = null

  // 优先从notification_data中获取action_url
  if (message.notification_data && message.notification_data.action_url) {
    actionUrl = message.notification_data.action_url
  } else if (message.metadata && message.metadata.action_url) {
    actionUrl = message.metadata.action_url
  } else if (message.data && message.data.action_url) {
    actionUrl = message.data.action_url
  }

  // 如果有action_url，直接使用
  if (actionUrl) {
    return actionUrl
  }

  // 回退逻辑：基于退款ID或订单ID构建URL
  let refundId = null
  let orderId = null

  if (message.metadata) {
    refundId = message.metadata.refund_id
    orderId = message.metadata.order_id
  } else if (message.notification_data) {
    refundId = message.notification_data.refund_id
    orderId = message.notification_data.order_id
  } else if (message.data) {
    refundId = message.data.refund_id
    orderId = message.data.order_id
  }

  const targetId = refundId || orderId
  if (targetId) {
    return `/user/refund/detail/${targetId}`
  }

  return null
}

// 运行测试
let passedCount = 0
let totalCount = testCases.length

testCases.forEach((testCase, index) => {
  console.log(`\n📋 测试 ${index + 1}: ${testCase.name}`)
  
  let result
  if (testCase.type === 'order') {
    result = testOrderJump(testCase.message)
  } else {
    result = testRefundJump(testCase.message)
  }
  
  const passed = result === testCase.expected
  console.log(`${passed ? '✅' : '❌'} 结果: ${passed ? '通过' : '失败'}`)
  console.log(`   期望: ${testCase.expected}`)
  console.log(`   实际: ${result}`)
  
  if (passed) {
    passedCount++
  }
  
  console.log('-'.repeat(30))
})

// 测试结果汇总
console.log(`\n📊 测试结果汇总:`)
console.log(`总测试数: ${totalCount}`)
console.log(`通过数: ${passedCount}`)
console.log(`失败数: ${totalCount - passedCount}`)
console.log(`通过率: ${((passedCount / totalCount) * 100).toFixed(1)}%`)

const allPassed = passedCount === totalCount
console.log(`\n🎯 总体结果: ${allPassed ? '所有测试通过' : '部分测试失败'}`)

if (allPassed) {
  console.log('✅ 订单消息跳转功能修改成功！')
  console.log('✅ 现在所有订单消息都会优先使用notification_data中的action_url进行跳转')
} else {
  console.log('❌ 部分测试失败，请检查代码逻辑')
}
