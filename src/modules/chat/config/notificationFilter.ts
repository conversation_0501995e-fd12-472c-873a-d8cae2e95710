/**
 * 通知过滤配置系统
 * 用于控制哪些消息类型应该弹出ElNotification通知
 */

// 通知过滤规则类型
export interface NotificationFilterRule {
  messageType: string
  eventType?: string
  enabled: boolean
  description: string
  category: 'system' | 'business' | 'social' | 'security'
}

// 默认通知过滤配置
export const DEFAULT_NOTIFICATION_FILTERS: NotificationFilterRule[] = [
  // 系统类通知
  {
    messageType: 'notification',
    eventType: 'user_online',
    enabled: false, // 默认不弹出上线通知
    description: '用户上线通知',
    category: 'system'
  },
  {
    messageType: 'notification',
    eventType: 'user_offline',
    enabled: false, // 默认不弹出下线通知
    description: '用户下线通知',
    category: 'system'
  },
  {
    messageType: 'notification',
    eventType: 'user_status_change',
    enabled: false, // 默认不弹出状态变化通知
    description: '用户状态变化通知',
    category: 'system'
  },
  {
    messageType: 'notification',
    eventType: 'system_maintenance',
    enabled: true, // 系统维护通知应该弹出
    description: '系统维护通知',
    category: 'system'
  },
  {
    messageType: 'notification',
    eventType: 'system_announcement',
    enabled: true, // 系统公告应该弹出
    description: '系统公告',
    category: 'system'
  },

  // 业务类通知
  {
    messageType: 'notification',
    eventType: 'user_refund_result',
    enabled: true, // 退款结果通知应该弹出
    description: '退款结果通知',
    category: 'business'
  },
  {
    messageType: 'notification',
    eventType: 'user_order_status_update',
    enabled: true, // 订单状态更新应该弹出
    description: '订单状态更新通知',
    category: 'business'
  },
  {
    messageType: 'notification',
    eventType: 'merchant_refund_request',
    enabled: true, // 商家退款申请应该弹出
    description: '退款申请通知',
    category: 'business'
  },
  {
    messageType: 'notification',
    eventType: 'merchant_new_order',
    enabled: true, // 新订单通知应该弹出
    description: '新订单通知',
    category: 'business'
  },

  // 社交类通知
  {
    messageType: 'message',
    enabled: true, // 聊天消息应该弹出
    description: '聊天消息',
    category: 'social'
  },
  {
    messageType: 'notification',
    eventType: 'user_typing',
    enabled: false, // 正在输入通知不弹出
    description: '正在输入通知',
    category: 'social'
  },

  // 安全类通知
  {
    messageType: 'notification',
    eventType: 'user_account_security',
    enabled: true, // 账户安全通知应该弹出
    description: '账户安全通知',
    category: 'security'
  },
  {
    messageType: 'notification',
    eventType: 'security_alert',
    enabled: true, // 安全警报应该弹出
    description: '安全警报',
    category: 'security'
  }
]

/**
 * 通知过滤器类
 */
export class NotificationFilter {
  private filters: Map<string, NotificationFilterRule> = new Map()
  private userType: 'user' | 'merchant' | 'admin'

  constructor(userType: 'user' | 'merchant' | 'admin') {
    this.userType = userType
    this.loadDefaultFilters()
    this.loadUserPreferences()
  }

  /**
   * 加载默认过滤规则
   */
  private loadDefaultFilters(): void {
    DEFAULT_NOTIFICATION_FILTERS.forEach(rule => {
      const key = this.generateFilterKey(rule.messageType, rule.eventType)
      this.filters.set(key, { ...rule })
    })
  }

  /**
   * 从本地存储加载用户偏好设置
   */
  private loadUserPreferences(): void {
    try {
      const storageKey = `notification_filters_${this.userType}`
      const savedFilters = localStorage.getItem(storageKey)
      if (savedFilters) {
        const userFilters: NotificationFilterRule[] = JSON.parse(savedFilters)
        userFilters.forEach(rule => {
          const key = this.generateFilterKey(rule.messageType, rule.eventType)
          this.filters.set(key, rule)
        })
      }
    } catch (error) {
      console.warn('Failed to load user notification preferences:', error)
    }
  }

  /**
   * 保存用户偏好设置到本地存储
   */
  private saveUserPreferences(): void {
    try {
      const storageKey = `notification_filters_${this.userType}`
      const filtersArray = Array.from(this.filters.values())
      localStorage.setItem(storageKey, JSON.stringify(filtersArray))
    } catch (error) {
      console.warn('Failed to save user notification preferences:', error)
    }
  }

  /**
   * 生成过滤器键名
   */
  private generateFilterKey(messageType: string, eventType?: string): string {
    return eventType ? `${messageType}:${eventType}` : messageType
  }

  /**
   * 检查是否应该显示通知
   */
  shouldShowNotification(messageType: string, eventType?: string): boolean {
    const key = this.generateFilterKey(messageType, eventType)
    const rule = this.filters.get(key)

    console.log(`🔍 [NotificationFilter] 检查通知过滤:`, {
      userType: this.userType,
      messageType,
      eventType,
      key,
      rule: rule ? { enabled: rule.enabled, description: rule.description } : null
    })

    if (rule) {
      console.log(`🔍 [NotificationFilter] 找到具体规则:`, rule.enabled ? '允许显示' : '过滤掉')
      return rule.enabled
    }

    // 如果没有找到具体规则，检查通用规则
    if (eventType) {
      const genericKey = this.generateFilterKey(messageType)
      const genericRule = this.filters.get(genericKey)
      if (genericRule) {
        console.log(`🔍 [NotificationFilter] 找到通用规则:`, genericRule.enabled ? '允许显示' : '过滤掉')
        return genericRule.enabled
      }
    }

    // 默认显示通知（保守策略）
    console.log(`🔍 [NotificationFilter] 未找到规则，默认显示通知`)
    return true
  }

  /**
   * 更新过滤规则
   */
  updateFilter(messageType: string, eventType: string | undefined, enabled: boolean): void {
    const key = this.generateFilterKey(messageType, eventType)
    const existingRule = this.filters.get(key)
    
    if (existingRule) {
      existingRule.enabled = enabled
    } else {
      // 创建新规则
      this.filters.set(key, {
        messageType,
        eventType,
        enabled,
        description: `${messageType}${eventType ? `:${eventType}` : ''}`,
        category: 'system'
      })
    }
    
    this.saveUserPreferences()
  }

  /**
   * 获取所有过滤规则
   */
  getAllFilters(): NotificationFilterRule[] {
    return Array.from(this.filters.values())
  }

  /**
   * 按类别获取过滤规则
   */
  getFiltersByCategory(category: string): NotificationFilterRule[] {
    return Array.from(this.filters.values()).filter(rule => rule.category === category)
  }

  /**
   * 重置为默认设置
   */
  resetToDefaults(): void {
    this.filters.clear()
    this.loadDefaultFilters()
    this.saveUserPreferences()
  }
}

// 全局通知过滤器实例
let userNotificationFilter: NotificationFilter | null = null
let merchantNotificationFilter: NotificationFilter | null = null
let adminNotificationFilter: NotificationFilter | null = null

/**
 * 获取通知过滤器实例
 */
export function getNotificationFilter(userType: 'user' | 'merchant' | 'admin'): NotificationFilter {
  switch (userType) {
    case 'user':
      if (!userNotificationFilter) {
        userNotificationFilter = new NotificationFilter('user')
      }
      return userNotificationFilter
    case 'merchant':
      if (!merchantNotificationFilter) {
        merchantNotificationFilter = new NotificationFilter('merchant')
      }
      return merchantNotificationFilter
    case 'admin':
      if (!adminNotificationFilter) {
        adminNotificationFilter = new NotificationFilter('admin')
      }
      return adminNotificationFilter
    default:
      throw new Error(`Unsupported user type: ${userType}`)
  }
}
