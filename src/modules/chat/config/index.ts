/**
 * 聊天模块配置文件
 * 包含聊天功能的各种配置选项
 */

// 聊天客户端配置
export const chatConfig = {
  // WebSocket 连接配置
  websocket: {
    url: (import.meta.env?.VITE_WS_URL as string) || '/api/v1/chat/ws',
    reconnectInterval: 3000, // 重连间隔（毫秒）
    maxReconnectAttempts: 10, // 最大重连次数
    heartbeatInterval: 30000, // 心跳间隔（毫秒）
    connectionTimeout: 10000, // 连接超时（毫秒）
    protocols: ['chat-protocol'], // WebSocket 协议
    binaryType: 'arraybuffer' as BinaryType
  },
  
  // API 配置
  api: {
    baseURL: (import.meta.env?.VUE_APP_API_URL as string) || 'http://localhost:8080/api',
    timeout: 30000, // 请求超时（毫秒）
    retryAttempts: 3, // 重试次数
    retryDelay: 1000, // 重试延迟（毫秒）
    endpoints: {
      // 会话相关
      sessions: '/chat/sessions',
      sessionDetail: '/chat/sessions/{id}',
      sessionParticipants: '/chat/sessions/{id}/participants',
      sessionSettings: '/chat/sessions/{id}/settings',
      sessionLogs: '/chat/sessions/{id}/logs',
      sessionStats: '/chat/sessions/{id}/stats',
      
      // 消息相关
      messages: '/chat/messages',
      messageDetail: '/chat/messages/{id}',
      messageHistory: '/chat/sessions/{sessionId}/messages',
      messageSearch: '/chat/messages/search',
      
      // 文件相关
      fileUpload: '/chat/files/upload',
      fileDownload: '/chat/files/{id}/download',
      filePreview: '/chat/files/{id}/preview',
      fileDelete: '/chat/files/{id}',
      
      // 通知相关
      notifications: '/chat/notifications',
      notificationSettings: '/chat/notifications/settings',
      notificationTemplates: '/chat/notifications/templates',
      
      // 用户相关
      userProfile: '/users/profile',
      userSettings: '/users/settings',
      userStatus: '/users/status'
    }
  },
  
  // 消息配置
  message: {
    maxLength: 5000, // 消息最大长度
    maxHistorySize: 1000, // 本地消息历史最大数量
    batchSize: 50, // 批量加载消息数量
    autoMarkReadDelay: 2000, // 自动标记已读延迟（毫秒）
    typingTimeout: 3000, // 正在输入状态超时（毫秒）
    retryInterval: 5000, // 消息发送重试间隔（毫秒）
    maxRetryAttempts: 3, // 消息发送最大重试次数
    supportedTypes: [
      'text',
      'image',
      'file',
      'audio',
      'video',
      'location',
      'system'
    ] as const
  },
  
  // 文件上传配置
  file: {
    maxSize: 100 * 1024 * 1024, // 最大文件大小（100MB）
    maxCount: 10, // 最大文件数量
    chunkSize: 1024 * 1024, // 分片大小（1MB）
    concurrentUploads: 3, // 并发上传数量
    allowedTypes: [
      // 图片
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      
      // 文档
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      
      // 文本
      'text/plain',
      'text/csv',
      'application/json',
      'application/xml',
      
      // 压缩文件
      'application/zip',
      'application/x-rar-compressed',
      'application/x-7z-compressed',
      
      // 音频
      'audio/mpeg',
      'audio/wav',
      'audio/ogg',
      'audio/mp4',
      
      // 视频
      'video/mp4',
      'video/webm',
      'video/ogg',
      'video/quicktime'
    ],
    imageCompression: {
      enabled: true,
      quality: 0.8, // 压缩质量
      maxWidth: 1920, // 最大宽度
      maxHeight: 1080 // 最大高度
    },
    previewTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'text/plain',
      'application/pdf'
    ]
  },
  
  // 会话配置
  session: {
    maxParticipants: 500, // 最大参与者数量
    defaultPageSize: 20, // 默认分页大小
    maxPageSize: 100, // 最大分页大小
    autoCloseTimeout: 24 * 60 * 60 * 1000, // 自动关闭超时（24小时）
    types: [
      'private', // 私聊
      'group', // 群聊
      'channel', // 频道
      'system' // 系统
    ] as const,
    statuses: [
      'active', // 活跃
      'inactive', // 不活跃
      'archived', // 已归档
      'closed' // 已关闭
    ] as const,
    priorities: [
      'low', // 低
      'normal', // 普通
      'high', // 高
      'urgent' // 紧急
    ] as const
  },
  
  // 通知配置
  notification: {
    maxCount: 100, // 最大通知数量
    defaultDuration: 5000, // 默认显示时长（毫秒）
    positions: [
      'top-right',
      'top-left',
      'bottom-right',
      'bottom-left',
      'top-center',
      'bottom-center'
    ] as const,
    types: [
      'message', // 消息通知
      'system', // 系统通知
      'success', // 成功通知
      'warning', // 警告通知
      'error', // 错误通知
      'info' // 信息通知
    ] as const,
    desktop: {
      enabled: true, // 是否启用桌面通知
      icon: '/favicon.ico', // 通知图标
      requireInteraction: false, // 是否需要用户交互
      silent: false // 是否静音
    },
    sound: {
      enabled: true, // 是否启用声音
      volume: 0.5, // 音量（0-1）
      files: {
        message: '/sounds/message.mp3',
        notification: '/sounds/notification.mp3',
        error: '/sounds/error.mp3'
      }
    }
  },
  
  // UI 配置
  ui: {
    theme: 'light' as 'light' | 'dark' | 'auto',
    language: 'zh-CN',
    dateFormat: 'YYYY-MM-DD HH:mm:ss',
    timeFormat: 'HH:mm',
    avatarSize: {
      small: 24,
      medium: 32,
      large: 48
    },
    messageList: {
      virtualScroll: true, // 是否启用虚拟滚动
      itemHeight: 60, // 消息项高度
      bufferSize: 10, // 缓冲区大小
      autoScroll: true, // 是否自动滚动到底部
      groupByDate: true, // 是否按日期分组
      showTimestamp: true, // 是否显示时间戳
      showAvatar: true, // 是否显示头像
      showSender: true // 是否显示发送者
    },
    sessionList: {
      virtualScroll: false,
      itemHeight: 72,
      showPreview: true, // 是否显示消息预览
      showTime: true, // 是否显示时间
      showUnreadCount: true, // 是否显示未读数量
      maxPreviewLength: 50 // 消息预览最大长度
    },
    window: {
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      resizable: true,
      draggable: true,
      maximizable: true,
      minimizable: true,
      closable: true
    }
  },
  
  // 缓存配置
  cache: {
    enabled: true,
    prefix: 'chat_',
    ttl: 24 * 60 * 60 * 1000, // 缓存过期时间（24小时）
    maxSize: 50 * 1024 * 1024, // 最大缓存大小（50MB）
    storage: 'localStorage' as 'localStorage' | 'sessionStorage' | 'indexedDB',
    keys: {
      sessions: 'sessions',
      messages: 'messages',
      users: 'users',
      settings: 'settings',
      notifications: 'notifications'
    }
  },
  
  // 日志配置
  logging: {
    enabled: (import.meta.env?.NODE_ENV as string) === 'development',
    level: 'info' as 'debug' | 'info' | 'warn' | 'error',
    maxSize: 1000, // 最大日志条数
    console: true, // 是否输出到控制台
    remote: false, // 是否发送到远程服务器
    remoteUrl: '/api/logs'
  },
  
  // 性能配置
  performance: {
    debounceDelay: 300, // 防抖延迟（毫秒）
    throttleDelay: 100, // 节流延迟（毫秒）
    lazyLoadOffset: 100, // 懒加载偏移量（像素）
    imageLoadTimeout: 10000, // 图片加载超时（毫秒）
    enableServiceWorker: true, // 是否启用 Service Worker
    enableWebWorker: true // 是否启用 Web Worker
  },
  
  // 安全配置
  security: {
    enableCSP: true, // 是否启用内容安全策略
    enableXSS: true, // 是否启用 XSS 防护
    maxLoginAttempts: 5, // 最大登录尝试次数
    lockoutDuration: 15 * 60 * 1000, // 锁定时长（15分钟）
    tokenRefreshThreshold: 5 * 60 * 1000, // Token 刷新阈值（5分钟）
    encryptLocalStorage: false, // 是否加密本地存储
    allowedOrigins: [
      'http://localhost:3000',
      'http://localhost:8080',
      'https://yourdomain.com'
    ]
  }
}

// 环境特定配置
export const environmentConfig = {
  development: {
    websocket: {
      url: '/api/v1/chat/ws'
    },
    api: {
      baseURL: 'http://localhost:8080/api'
    },
    logging: {
      enabled: true,
      level: 'debug' as const
    }
  },
  
  production: {
    websocket: {
      url: 'wss://api.yourdomain.com/ws'
    },
    api: {
      baseURL: 'https://api.yourdomain.com/api'
    },
    logging: {
      enabled: false,
      level: 'error' as const
    },
    performance: {
      enableServiceWorker: true,
      enableWebWorker: true
    }
  },
  
  test: {
    websocket: {
      url: 'ws://test.yourdomain.com/ws'
    },
    api: {
      baseURL: 'http://test.yourdomain.com/api'
    },
    logging: {
      enabled: true,
      level: 'warn' as const
    }
  }
}

// 获取当前环境配置
export const getCurrentConfig = () => {
  const env = (import.meta.env?.NODE_ENV as string) || 'development'
  const envConfig = environmentConfig[env as keyof typeof environmentConfig] || environmentConfig.development
  
  // 深度合并配置
  return mergeDeep(chatConfig, envConfig)
}

// 深度合并对象
function mergeDeep(target: any, source: any): any {
  const result = { ...target }
  
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = mergeDeep(result[key] || {}, source[key])
    } else {
      result[key] = source[key]
    }
  }
  
  return result
}

// 配置验证
export const validateConfig = (config: typeof chatConfig) => {
  const errors: string[] = []
  
  // 验证 WebSocket URL
  if (!config.websocket.url) {
    errors.push('WebSocket URL is required')
  }
  
  // 验证 API URL
  if (!config.api.baseURL) {
    errors.push('API base URL is required')
  }
  
  // 验证文件大小限制
  if (config.file.maxSize <= 0) {
    errors.push('File max size must be greater than 0')
  }
  
  // 验证消息长度限制
  if (config.message.maxLength <= 0) {
    errors.push('Message max length must be greater than 0')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// 配置类型定义
export type ChatConfig = typeof chatConfig
export type EnvironmentConfig = typeof environmentConfig
export type ConfigValidationResult = ReturnType<typeof validateConfig>

// 默认导出
export default {
  config: chatConfig,
  environmentConfig,
  getCurrentConfig,
  validateConfig
}