/**
 * structureDataCacheService.ts
 * 结构数据缓存管理服务
 * @description 使用localforage实现结构数据的本地持久化，支持版本控制和差异化更新
 * <AUTHOR>
 * @date 2025-04-24
 */
import localforage from 'localforage';
import type { PageData } from '@/modules/admin/types';

/**
 * 缓存的结构数据类型
 */
interface CachedStructureData {
  data: PageData; // 实际的结构数据
  systemConfigVersion: string; // 对应的全局configVersion
  pageVersion: string; // 页面配置版本(data.version)
  pageVersionHash: string; // 版本识别号(data.version_hash)
  path: string; // 对应的前端路径
}

// 配置localforage
localforage.config({
  name: 'oMallStructureData',
  storeName: 'structure_data',
  description: 'O-Mall前端结构数据缓存'
});

/**
 * 获取缓存键名
 * @param frontendPath 前端路径
 * @returns 缓存键名
 */
const getCacheKey = (frontendPath: string): string => 
  `structure_data_${frontendPath.replace(/\//g, '_')}`;

/**
 * 获取系统配置版本
 * 如果未引入systemStore，则返回null
 */
function getSystemConfigVersion(): string | null {
  try {
    // 动态导入可能导致循环依赖，这里采用全局状态检查
    // @ts-ignore
    const systemInfo = window.$systemInfo || {};
    return systemInfo.configVersion || null;
  } catch (error) {
    console.error('获取系统配置版本失败:', error);
    return null;
  }
}

/**
 * 保存结构数据到缓存
 * @param frontendPath 前端路径
 * @param data 页面数据
 */
export async function saveStructureData(frontendPath: string, data: PageData): Promise<void> {
  const systemConfigVersion = getSystemConfigVersion();
  
  const cacheItem: CachedStructureData = {
    data,
    systemConfigVersion: systemConfigVersion || '',
    pageVersion: data.version || '',
    pageVersionHash: data.version_hash || '',
    path: frontendPath
  };
  
  await localforage.setItem(getCacheKey(frontendPath), cacheItem);
  console.log(`已缓存路径 ${frontendPath} 的结构数据，版本: ${data.version}, 识别号: ${data.version_hash}`);
}

/**
 * 从缓存获取结构数据
 * @param frontendPath 前端路径
 * @returns 页面数据或null
 */
export async function getStructureData(frontendPath: string): Promise<PageData | null> {
  const systemConfigVersion = getSystemConfigVersion();
  
  if (!systemConfigVersion) {
    console.log('系统配置未初始化，跳过缓存');
    return null; // 系统信息未初始化时不使用缓存
  }
  
  try {
    const cachedItem = await localforage.getItem<CachedStructureData>(getCacheKey(frontendPath));
    
    if (!cachedItem) {
      console.log(`路径 ${frontendPath} 没有缓存数据`);
      return null; // 缓存不存在
    }
    
    // 全局配置版本不匹配，需要更新
    if (cachedItem.systemConfigVersion !== systemConfigVersion) {
      console.log('系统配置版本已更新，缓存失效');
      return null;
    }
    
    console.log(`从缓存获取路径 ${frontendPath} 的结构数据，版本: ${cachedItem.pageVersion}`);
    return cachedItem.data;
  } catch (error) {
    console.error('读取结构数据缓存失败:', error);
    return null;
  }
}

/**
 * 检查单个页面配置是否需要更新
 * @param frontendPath 前端路径
 * @param newPageData 新页面数据
 * @returns 是否需要更新
 */
export async function checkPageVersionUpdate(frontendPath: string, newPageData: PageData): Promise<boolean> {
  try {
    const cachedItem = await localforage.getItem<CachedStructureData>(getCacheKey(frontendPath));
    
    if (!cachedItem) return true; // 缓存不存在，需要更新
    
    // 检查页面版本号和版本识别号
    return cachedItem.pageVersion !== newPageData.version || 
           cachedItem.pageVersionHash !== newPageData.version_hash;
  } catch (error) {
    console.error(`检查页面 ${frontendPath} 版本更新失败:`, error);
    return true; // 出错时默认需要更新
  }
}

/**
 * 清除特定路径的缓存
 * @param frontendPath 前端路径
 */
export async function clearStructureDataCache(frontendPath: string): Promise<void> {
  await localforage.removeItem(getCacheKey(frontendPath));
  console.log(`已清除路径 ${frontendPath} 的结构数据缓存`);
}

/**
 * 清除所有结构数据缓存
 */
export async function clearAllStructureDataCache(): Promise<void> {
  await localforage.clear();
  console.log('已清除所有结构数据缓存');
}

/**
 * 获取所有缓存的状态信息
 * @returns 缓存状态数组
 */
export async function getCacheStatus(): Promise<{path: string, version: string, hash: string}[]> {
  const keys = await localforage.keys();
  const result = [];
  
  for (const key of keys) {
    try {
      const item = await localforage.getItem<CachedStructureData>(key);
      if (item) {
        result.push({
          path: item.path,
          version: item.pageVersion,
          hash: item.pageVersionHash
        });
      }
    } catch (error) {
      console.error(`获取缓存项 ${key} 失败:`, error);
    }
  }
  
  return result;
}

/**
 * 注册调试命令到全局对象
 */
export function registerDebugCommands(): void {
  // 在控制台添加调试命令
  // @ts-ignore
  window._oMallCache = {
    getStatus: getCacheStatus,
    clearAll: clearAllStructureDataCache,
    clearPath: clearStructureDataCache
  };
  console.log('缓存调试命令已注册到 window._oMallCache');
}

/**
 * 检查并更新需要刷新的页面配置
 * @param pageList 页面列表数据
 */
export async function checkAndUpdatePageConfigs(pageList: PageData[]): Promise<void> {
  for (const page of pageList) {
    // 确保页面有前端路径
    if (page.frontend_path) {
      try {
        const cachedItem = await localforage.getItem<CachedStructureData>(getCacheKey(page.frontend_path));
        
        // 缓存存在且版本信息不匹配，删除缓存以便下次访问时更新
        if (cachedItem && (cachedItem.pageVersion !== page.version || 
                           cachedItem.pageVersionHash !== page.version_hash)) {
          await clearStructureDataCache(page.frontend_path);
          console.log(`页面 ${page.title}(${page.frontend_path}) 配置已更新，已清除旧缓存`);
        }
      } catch (error) {
        console.error(`检查页面 ${page.frontend_path} 缓存状态失败:`, error);
      }
    }
  }
}
