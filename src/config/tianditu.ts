/**
 * 天地图配置文件
 * 作者: AI Assistant
 * 日期: 2025-06-08
 * 版本: 2.0.0
 * 描述: 天地图相关配置，包括API密钥和基础设置
 */

// 从环境变量获取天地图API密钥
const tianditu_token = import.meta.env.VITE_TIANDITU_API_KEY || ''

// 多个tokens用于防止demo资源被耗光，正常业务使用上方即可
const tianditu_tokens = [
  import.meta.env.VITE_TIANDITU_API_KEY || '',
  import.meta.env.VITE_TIANDITU_API_KEY_2 || '',
  import.meta.env.VITE_TIANDITU_API_KEY_3 || '',
].filter(Boolean) as string[]

// 版本号
const version = '2.0.0'

// 是否输出错误提示
const isShowErrorMessage = true

// 默认地图配置
const defaultMapConfig = {
  center: [
    Number(import.meta.env.VITE_TIANDITU_DEFAULT_LNG || 116.40769),
    Number(import.meta.env.VITE_TIANDITU_DEFAULT_LAT || 39.89945)
  ],
  zoom: Number(import.meta.env.VITE_TIANDITU_DEFAULT_ZOOM || 12),
  minZoom: Number(import.meta.env.VITE_TIANDITU_MIN_ZOOM || 1),
  maxZoom: Number(import.meta.env.VITE_TIANDITU_MAX_ZOOM || 18)
}

// 地图类型映射
const mapTypeMapping = {
  normal: 'TMAP_NORMAL_MAP',
  satellite: 'TMAP_SATELLITE_MAP', 
  terrain: 'TMAP_TERRAIN_MAP',
  hybrid: 'TMAP_HYBRID_MAP',
  terrainHybrid: 'TMAP_TERRAIN_HYBRID_MAP'
}

export {
  tianditu_token,
  tianditu_tokens,
  version,
  isShowErrorMessage,
  defaultMapConfig,
  mapTypeMapping
}