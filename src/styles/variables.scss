// src/styles/variables.scss

// 主题颜色
$primary-color: #2563eb;          // 更现代的蓝色，提升品牌感
$success-color: #10b981;          // 柔和绿色，提升视觉舒适度
$warning-color: #f59e0b;          // 更鲜明的橙色，符合警告标准
$danger-color: #ef4444;           // 鲜艳红色，增强警示效果
$info-color: #6b7280;             // 中性灰色，适合信息提示

// 文字颜色
$text-primary: #18181b;           // 更深的黑色，提升可读性
$text-regular: #4b5563;           // 深灰色，适合正文
$text-secondary: #71717a;         // 中灰色，适合次要信息
$text-placeholder: #a1a1aa;       // 浅灰色，适合占位符

// 边框颜色
$border-color-base: #e4e4e7;      // 浅灰色，减少视觉干扰
$border-color-light: #f4f4f5;     // 超浅灰色，适合分隔线
$border-color-lighter: #fafafa;   // 极浅灰色，适合背景分隔
$border-color-extra-light: #f2f6fc; // 保留原始值，保持一致性

// 背景颜色
$background-color-base: #f4f4f5;  // 浅灰色，适合页面背景
$background-color-light: #f9fafb; // 更浅的背景色，适合卡片

$font-family: "Inter", "PingFang SC", "Microsoft YaHei", sans-serif; // 字体栈
// 字体大小
$font-size-extra-large: 20px;     // 超大字体
$font-size-large: 18px;           // 大号字体
$font-size-medium: 16px;          // 中号字体
$font-size-base: 14px;            // 基础字体大小
$font-size-small: 12px;           // 小号字体（调整为 12px，与现代设计一致）
$font-size-extra-small: 10px;     // 超小字体（新增）

// 边框圆角
$border-radius-small: 2px;        // 小圆角
$border-radius-base: 4px;         // 基础圆角
$border-radius-large: 8px;        // 大圆角（新增）
$border-radius-extra-large: 12px; // 超大圆角（新增）
$border-radius-circle: 50%;       // 圆形（调整为更常用值）

// 阴影
$box-shadow-light: 0 1px 2px 0 rgba(0, 0, 0, 0.05);                  // 浅阴影
$box-shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); // 基础阴影
$box-shadow-dark: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); // 深阴影（新增）

// 间距
$spacing-base: 8px;               // 基础间距
$spacing-small: 4px;              // 小间距（新增）
$spacing-large: 16px;             // 大间距
$spacing-larger: 24px;            // 更大间距
$spacing-extra-large: 32px;       // 超大间距
$spacing-extra-extra-large: 48px; // 极大间距