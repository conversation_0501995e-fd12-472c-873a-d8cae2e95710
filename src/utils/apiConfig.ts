/**
 * 获取API基础URL
 * 基于当前网站的URL动态生成API地址
 * 如果是开发环境，则使用开发环境的配置
 * 如果是生产环境，则基于当前网站URL生成
 */
export const getApiBaseUrl = (): string => {
    // 开发环境使用配置文件中的地址
    if (import.meta.env.DEV) {
      return import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8181'
    }
    
    // 生产环境动态获取
    const currentUrl = window.location.origin
    return `${currentUrl}/api`
  }
  
  /**
   * 获取WebSocket基础URL
   * 基于当前网站的URL动态生成WebSocket地址
   * 如果是开发环境，则使用开发环境的配置
   * 如果是生产环境，则基于当前网站URL生成
   * @param path - 可选的额外路径（例如：'/admin'）
   * @returns WebSocket URL
   */
  export const getWsBaseUrl = (path: string = ''): string => {
    // 开发环境使用配置文件中的地址
    if (import.meta.env.DEV) {
      const baseUrl = import.meta.env.VITE_WS_BASE_URL || 'ws://127.0.0.1:8181/ws'
      return `${baseUrl}${path}`
    }
    
    // 生产环境动态获取
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.host
    return `${protocol}//${host}/ws${path}`
  }