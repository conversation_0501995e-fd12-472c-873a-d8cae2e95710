/**
 * 路由守卫与鉴权工具
 * 统一处理路由权限控制和路由加载状态
 */
import type { Router } from 'vue-router';
import { useAdminStore } from '@/modules/admin/stores/adminStore';
import { registerDynamicRoutes } from '@/router';
import { ref } from 'vue';
import { useSystemStore } from '@/stores/systemStore';

// 添加一个缓存，记录已处理的路径
const processedPaths = new Set<string>();

/**
 * 清理路由处理缓存
 * 在路由切换时清理缓存，避免状态不一致
 * @param path 要清理的路径，如果不提供则清理所有缓存
 */
export function clearRouteProcessCache(path?: string) {
  if (path) {
    processedPaths.delete(path);
    console.log(`🧹 清理路由处理缓存: ${path}`);
  } else {
    processedPaths.clear();
    console.log('🧹 清理所有路由处理缓存');
  }
}

/**
 * 获取当前缓存的路径数量（用于调试）
 */
export function getProcessedPathsCount(): number {
  return processedPaths.size;
}

/**
 * 判断路径是否为动态路由
 * 通过匹配预设的动态路由模式判断路径是否可能是动态路由
 * @param path 需要判断的路径
 * @returns 是否匹配动态路由模式
 */
function isDynamicRoute(path: string): boolean {
  // 定义可能的动态路由模式 - 更宽松的匹配条件
  const dynamicPatterns = [
    // 管理员模块动态路由
    '/admin/users',
    '/admin/roles',
    '/admin/permissions',
    '/admin/system',
    '/admin/content',
    '/admin/',
    
    // 商家模块动态路由
    '/merchant/products',
    '/merchant/orders',
    // 修正：移除dashboards，避免错误匹配
    // '/merchant/dashboards',
    '/merchant/financial',
    '/merchant/statistics',
    // 商家通用模式
    '/merchant/',
    
    // 用户模块动态路由
    '/user/home',
    '/user/profile',
    '/user/orders',
    '/user/addresses',
    '/user/settings',
    '/user/page',
    '/user/'
  ];
  
  // 检查路径是否匹配任一模式
  return dynamicPatterns.some(pattern => path.startsWith(pattern));
}

/**
 * 处理路由导航逻辑
 * @param router 路由实例
 * @param to 目标路由
 * @param next 路由跳转函数
 * @param segment 路径的最后部分
 * @param possibleName 可能的路由名称
 */
function handleRouteNavigation(router: Router, to: any, next: any, segment: string, possibleName: string) {
  console.log(`🔄 尝试通过名称导航: ${possibleName}`);
  
  // 打印所有可用路由，帮助调试
  console.log('📊 当前所有注册路由:', router.getRoutes().map(r => ({
    path: r.path,
    name: r.name ? String(r.name) : undefined
  })));
  
  // 检查是否有匹配的路由名称
  console.log(`尝试匹配路由名称: ${possibleName}, 针对路径段: ${segment}`);
  console.log(`当前路径: ${to.path}, 是否包含dashboards: ${to.path.includes('dashboards')}`);
  if (router.hasRoute(possibleName)) {
    console.log(`✅ 找到匹配的路由名称: ${possibleName}`);
    next({ name: possibleName, query: to.query, params: to.params, replace: true });
  } else {
    // 查找相似路由
    console.log('🔍 搜索相似路由...');
    console.log(`当前路径段: ${segment}, 查找包含该段的路由`);
    
    // 检查是否是dashboards路径
    if (segment === 'dashboards') {
      console.log('特别处理dashboards路径');
      // 打印所有可能的商家路由
      const merchantRoutes = router.getRoutes().filter(r => r.path.startsWith('/merchant') || r.path.startsWith('merchant'));
      console.log('当前所有商家路由:', merchantRoutes.map(r => ({path: r.path, name: r.name})));
    }
    
    const similarRoutes = router.getRoutes().filter(r => {
      // 首先检查路径是否包含目标段
      const pathMatch = r.path.includes(segment);
      // 然后检查名称是否包含目标段
      const nameMatch = r.name ? String(r.name).toLowerCase().includes(segment.toLowerCase()) : false;
      
      // 特别处理dashboards
      if (segment === 'dashboards' && (pathMatch || nameMatch)) {
        console.log(`找到dashboards相关路由: ${r.path}, 名称: ${r.name ? String(r.name) : '无名称'}`);
      }
      
      return pathMatch || nameMatch;
    });
    
    console.log(`找到 ${similarRoutes.length} 个相似路由`);
    
    if (similarRoutes.length > 0) {
      // 使用第一个相似路由
      console.log(`➡️ 重定向到相似路由: ${similarRoutes[0].path}`);
      next({ path: similarRoutes[0].path, replace: true });
    } else {
      // 最后尝试使用原始路径
      console.log(`⚠️ 未找到相似路由，尝试使用原始路径: ${to.fullPath}`);
      next({ path: to.fullPath, replace: true });
    }
  }
}

/**
 * 路由状态管理对象
 * 管理路由的加载、认证和初始化状态
 */
export const routeStatus = {
  // 路由加载状态
  isLoading: ref(false),
  
  // 路由权限验证中
  isAuthenticating: ref(false),
  
  // 路由初始化状态
  isInitialized: ref(false),
  
  // 设置加载状态
  setLoading(value: boolean) {
    this.isLoading.value = value;
  },
  
  // 设置认证状态
  setAuthenticating(value: boolean) {
    this.isAuthenticating.value = value;
  },
  
  // 设置初始化状态  
  setInitialized(value: boolean) {
    this.isInitialized.value = value;
  }
};

/**
 * 设置路由守卫
 * 处理路由的权限控制、动态路由注册和页面加载状态
 * @param router Vue Router实例
 */
export function setupRouteGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    // 设置加载状态
    routeStatus.setLoading(true);
    
    console.log(`=== 路由导航开始 ===`);
    console.log(`从: ${from.path} 到: ${to.path}`);
    console.log(`匹配路由数: ${to.matched.length}`);
    console.log(`路由名称: ${to.name ? String(to.name) : 'undefined'}`);
    
    // 路由切换时清理相关缓存
    if (from.path !== to.path) {
      // 如果是从动态路由页面离开，清理该路径的缓存
      if (isDynamicRoute(from.path)) {
        clearRouteProcessCache(from.path);
      }
      
      // 如果是跨模块路由切换，清理所有缓存
      const fromModule = from.path.split('/')[1];
      const toModule = to.path.split('/')[1];
      if (fromModule !== toModule && (fromModule === 'admin' || fromModule === 'merchant' || fromModule === 'user')) {
        clearRouteProcessCache();
        console.log(`🔄 跨模块路由切换: ${fromModule} -> ${toModule}，清理所有缓存`);
      }
    }
    
    // 初始化系统信息
    const systemStore = useSystemStore();
    if (!systemStore.initialized) {
      console.log('初始化系统信息...');
      await systemStore.initSystemInfo();
    }
    
    // 详细打印匹配情况，帮助调试
    if (to.matched.length > 0) {
      console.log('匹配的路由:', to.matched.map(m => ({
        path: m.path,
        name: m.name ? String(m.name) : undefined
      })));
    } else {
      console.log('没有匹配到任何路由');
    }
    
    // 匹配条件增强：任何可能是动态路由的路径，都进行特殊处理
    const needsDynamicRouteHandling = (
      // 无匹配路由
      to.matched.length === 0 || 
      // 或匹配到404（关键条件）
      (to.matched.length === 1 && to.matched[0].name && String(to.matched[0].name) === 'NotFound') ||
      // 或已知是动态路由路径但没有正确匹配
      (isDynamicRoute(to.path) && !to.matched.some(m => m.path.includes(to.path.split('/').pop() || '')))
    );
    
    console.log(`需要动态路由处理: ${needsDynamicRouteHandling}`);
    console.log(`路径 ${to.path} 是动态路由: ${isDynamicRoute(to.path)}`);
    
    if (needsDynamicRouteHandling && isDynamicRoute(to.path)) {
      console.log(`🔄 开始处理动态路由: ${to.path}`);
      
      // 检查是否为404路由，如果是则总是会进行处理而不使用缓存
      const isNotFoundRoute = to.matched.length === 1 && to.matched[0].name === 'NotFound';
      
      // 只有非404路由且已处理过的路径才跳过处理
      if (!isNotFoundRoute && processedPaths.has(to.path)) {
        console.log(`⚠️ 路径 ${to.path} 已处理过，如果仍未解决，请清除缓存`);
        next();
        return;
      }
      
      // 标记此路径已处理，但如果是404路由则暂不标记
      // 这样如果它仍然解析为404，下次还会再次尝试处理
      if (!isNotFoundRoute) {
        processedPaths.add(to.path);
      } 
      console.log(`路径${to.path}处理状态: ${isNotFoundRoute ? '404路由，允许重复处理' : '已标记为已处理'}`);

      
      try {
        // 判断是哪个模块的路由
        const isAdminRoute = to.path.startsWith('/admin/');
        const isMerchantRoute = to.path.startsWith('/merchant/');
        const isUserRoute = to.path.startsWith('/user/');
        
        console.log(`路由类型: ${isAdminRoute ? '管理员' : isMerchantRoute ? '商家' : isUserRoute ? '用户' : '其他'}`);
        
        if (isAdminRoute) {
          // 管理员模块路由处理
          const adminStore = useAdminStore();
          
          // 确保admin token有效
          const hasToken = await adminStore.retoken();
          if (!hasToken && to.path !== '/admin/login') {
            console.log('🔒 Admin token无效，重定向到登录页');
            next('/admin/login');
            return;
          }
          
          console.log('📱 强制刷新管理员前端路径数据...');
          const paths = await adminStore.fetchFrontendPaths(true);
          
          if (paths && paths.length > 0) {
            console.log('🔧 重新注册管理员动态路由...');
            await registerDynamicRoutes(paths);
            
            // 等待路由注册完成
            await new Promise(resolve => setTimeout(resolve, 200));
            
            // 解析路径获取路由信息
            console.log('🔍 分析路径结构...');
            const pathSegments = to.path.split('/').filter(Boolean);
            
            // 路径太短，直接放行
            if (pathSegments.length < 2) {
              console.log('⚠️ 路径结构异常，直接放行');
              next();
              return;
            }
            
            // 获取路径的最后部分用于构建路由名称
            const segment = pathSegments[pathSegments.length - 1]; 
            const possibleName = `Admin${segment.charAt(0).toUpperCase() + segment.slice(1)}`;
            
            handleRouteNavigation(router, to, next, segment, possibleName);
            return;
          }
        }
        else if (isMerchantRoute) {
          // 商家模块路由处理
          console.log('检测到商家模块路径，应用特殊处理逆辑');
          // 先引入商家Store
          const { useMerchantStore } = await import('@/modules/merchant/stores/merchantStore');
          const merchantStore = useMerchantStore();
          
          // 确保商家 token 有效
          const hasToken = await merchantStore.retoken();
          if (!hasToken && to.path !== '/merchant/login') {
            console.log('🔒 Merchant token无效，重定向到登录页');
            next('/merchant/login');
            return;
          }
          
          console.log('📱 强制刷新商家前端路径数据...');
          // 先尝试从缓存获取数据，避免每次刷新都请求API
          let paths = await merchantStore.fetchFrontendPaths(false);
          
          // 如果缓存中没有数据，则强制刷新获取
          if (!paths || paths.length === 0) {
            console.log('缓存中没有路径数据，强制刷新获取');
            paths = await merchantStore.fetchFrontendPaths(true);
          }
          
          if (paths && paths.length > 0) {
            console.log('🔧 重新注册商家动态路由...');
            await registerDynamicRoutes(paths);
            
            // 等待路由注册完成 - 增加等待时间确保路由完全加载
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 解析路径获取路由信息
            console.log('🔍 分析路径结构...');
            const pathSegments = to.path.split('/').filter(Boolean);
            
            // 路径太短，直接放行
            if (pathSegments.length < 2) {
              console.log('⚠️ 路径结构异常，直接放行');
              next();
              return;
            }
            
            // 获取路径的最后部分用于构建路由名称
            const segment = pathSegments[pathSegments.length - 1]; 
            
            // 尝试两种可能的命名模式 - 单层路径和双层路径
            const possibleName1 = `Merchant${segment.charAt(0).toUpperCase() + segment.slice(1)}`;
            
            // 如果路径包含两级或以上，尝试构建更多可能的名称
            let possibleName2 = '';
            if (pathSegments.length >= 3) {
              const parentSegment = pathSegments[pathSegments.length - 2];
              possibleName2 = `Merchant${parentSegment.charAt(0).toUpperCase() + parentSegment.slice(1)}${segment.charAt(0).toUpperCase() + segment.slice(1)}`;
            }
            
            console.log(`尝试匹配路由名称: ${possibleName1} 或 ${possibleName2}`);
            
            // 首先尝试最精确的匹配
            if (possibleName2 && router.hasRoute(possibleName2)) {
              console.log(`✅ 找到匹配的路由名称: ${possibleName2}`);
              next({ name: possibleName2, query: to.query, params: to.params, replace: true });
              return;
            }
            
            // 其次尝试单层路径名称
            if (router.hasRoute(possibleName1)) {
              console.log(`✅ 找到匹配的路由名称: ${possibleName1}`);
              next({ name: possibleName1, query: to.query, params: to.params, replace: true });
              return;
            }
            
            // 如果名称匹配失败，使用路径匹配
            handleRouteNavigation(router, to, next, segment, possibleName1);
            return;
          }
        }
        else if (isUserRoute) {
          // 用户模块路由处理
          console.log('检测到用户模块路径，应用特殊处理逆辑');
          // 引入用户Store
          const { useUserStore } = await import('@/modules/user/stores/userStore');
          const userStore = useUserStore();
          
          // 确保用户 token 有效
          const hasToken = await userStore.retoken();
          if (!hasToken && to.path !== '/user/login' && to.path !== '/user/register') {
            console.log('🔒 User token无效，重定向到登录页');
            next('/user/login');
            return;
          }
          
          // 获取用户模块的路由配置
          const { generateDynamicRoutes } = await import('@/modules/user/router/index');
          
          // 模拟脚本传入一个frontendPaths结构，因为用户模块可能并没有前端路径API
          const defaultUserPaths = [{
            module: 'user',
            paths: []
          }];
          
          console.log('重新注册用户动态路由...');
          // 生成并注册用户动态路由
          generateDynamicRoutes(defaultUserPaths);
          await registerDynamicRoutes(defaultUserPaths);
          
          // 等待路由注册完成
          await new Promise(resolve => setTimeout(resolve, 500));
          
          // 解析路径获取路由信息
          console.log('🔍 分析路径结构...');
          const pathSegments = to.path.split('/').filter(Boolean);
          
          // 路径太短，直接放行
          if (pathSegments.length < 2) {
            console.log('⚠️ 路径结构异常，直接放行');
            next();
            return;
          }
          
          // 获取路径的最后部分用于构建路由名称
          const segment = pathSegments[pathSegments.length - 1]; 
          
          // 构建可能的路由名称
          const possibleName = `User${segment.charAt(0).toUpperCase() + segment.slice(1)}`;
          
          // 尝试匹配路由名称
          console.log(`尝试匹配路由名称: ${possibleName}`);
          
          if (router.hasRoute(possibleName)) {
            console.log(`✅ 找到匹配的路由名称: ${possibleName}`);
            next({ name: possibleName, query: to.query, params: to.params, replace: true });
            return;
          }
          
          // 如果名称匹配失败，使用路径匹配
          handleRouteNavigation(router, to, next, segment, possibleName);
          return;
        }
        
        console.warn('❌ 未获取到前端路径数据或不支持的模块');
        next();
      } catch (error) {
        console.error('❌ 处理动态路由失败:', error);
        // 清除标记，允许下次重试
        processedPaths.delete(to.path);
        next();
      }
      return;
    }
    
    // 登录页和首页直接放行
    if (to.path === '/admin/login' || to.path === '/user/login' || 
        to.path === '/merchant/login' || to.path === '/admin/dashboard') {
      console.log('✅ 直接放行认证页面或首页');
      next();
      return;
    }
    
    // 按模块处理权限
    try {
      if (to.path.startsWith('/admin')) {
        // 由于开发中，暂时简化为直接放行管理员路由
        console.log('✅ 放行管理员路由:', to.path);
        next();
      } else if (to.path.startsWith('/user')) {
        // 用户路由放行
        console.log('✅ 放行用户路由:', to.path);
        next();
      } else if (to.path.startsWith('/merchant')) {
        // 商家路由放行
        console.log('✅ 放行商家路由:', to.path);
        next();
      } else {
        // 其他路由
        console.log('✅ 放行其他路由:', to.path);
        next();
      }
    } catch (error) {
      console.error('❌ 路由鉴权错误:', error);
      next('/');
    }
  });

  // 全局后置钩子，用于完成加载状态
  router.afterEach((to) => {
    // 增加导航完成日志
    console.log(`✅ 导航完成: ${to.path}`);
    
    // 延迟关闭加载状态，避免闪烁
    setTimeout(() => {
      routeStatus.setLoading(false);
    }, 300);
  });

  // 全局错误处理
  router.onError((error) => {
    console.error('❌ 路由错误:', error);
    routeStatus.setLoading(false);
    routeStatus.setAuthenticating(false);
  });
}