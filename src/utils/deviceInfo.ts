/**
 * 设备信息生成工具
 * 作者: 助理
 * 日期: 2025-01-27
 * 版本: 1.0
 * 描述: 提供统一的设备信息生成逻辑，确保同一设备在所有模块中生成相同的设备ID
 */

/**
 * 设备信息接口定义
 * 兼容所有模块的设备信息字段要求
 */
export interface DeviceInfo {
  device_type: string;    // 设备类型: desktop/mobile/tablet
  device_id: string;      // 设备唯一标识
  device_name: string;    // 设备名称
  os: string;            // 操作系统
  platform: string;      // 平台信息(用户模块需要)
  browser: string;       // 浏览器
  app_version: string;   // 应用版本(用户模块需要)
  os_version: string;    // 操作系统版本(用户模块需要)
  user_agent: string;    // 用户代理
  ip?: string;           // IP地址(可选)
}

/**
 * 全局设备ID存储键名
 * 所有模块共享同一个设备ID
 */
const DEVICE_ID_KEY = 'global_device_id';

/**
 * 生成稳定的设备ID
 * 基于浏览器指纹生成，确保同一设备始终生成相同的ID
 * @returns 设备唯一标识符
 */
function generateStableDeviceId(): string {
  // 先检查是否已存在设备ID
  const existingId = localStorage.getItem(DEVICE_ID_KEY);
  if (existingId) {
    return existingId;
  }

  // 生成基于浏览器指纹的稳定设备ID
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  let canvasFingerprint = '';
  
  if (ctx) {
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Device fingerprint for O_Mall', 2, 2);
    canvasFingerprint = canvas.toDataURL();
  }
  
  // 收集浏览器指纹信息
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    navigator.languages?.join(',') || '',
    screen.width + 'x' + screen.height,
    screen.colorDepth,
    new Date().getTimezoneOffset(),
    navigator.platform,
    navigator.cookieEnabled,
    canvasFingerprint.slice(-50) // 取canvas指纹的后50个字符
  ].join('|');
  
  // 生成哈希值
  let hash = 0;
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  
  // 转换为36进制字符串并添加前缀
  const deviceId = 'omall_' + Math.abs(hash).toString(36);
  
  // 存储到localStorage
  localStorage.setItem(DEVICE_ID_KEY, deviceId);
  
  return deviceId;
}

/**
 * 获取设备类型
 * @returns 设备类型: desktop/mobile/tablet
 */
function getDeviceType(): string {
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (/ipad|tablet/.test(userAgent)) {
    return 'tablet';
  }
  
  if (/mobile|android|iphone|phone/.test(userAgent)) {
    return 'mobile';
  }
  
  return 'desktop';
}

/**
 * 获取操作系统信息
 * @returns 操作系统名称
 */
function getOperatingSystem(): string {
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (/iphone|ipad/.test(userAgent)) return 'ios';
  if (/android/.test(userAgent)) return 'android';
  if (/windows/.test(userAgent)) return 'windows';
  if (/mac/.test(userAgent)) return 'macos';
  if (/linux/.test(userAgent)) return 'linux';
  
  return 'unknown';
}

/**
 * 获取浏览器信息
 * @returns 浏览器名称
 */
function getBrowserInfo(): string {
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (/edg/.test(userAgent)) return 'Edge';
  if (/chrome/.test(userAgent) && !/edg/.test(userAgent)) return 'Chrome';
  if (/firefox/.test(userAgent)) return 'Firefox';
  if (/safari/.test(userAgent) && !/chrome/.test(userAgent)) return 'Safari';
  if (/opera/.test(userAgent)) return 'Opera';
  
  return 'Unknown';
}

/**
 * 获取设备名称
 * @returns 设备名称（操作系统 + 浏览器）
 */
function getDeviceName(): string {
  const os = getOperatingSystem();
  const browser = getBrowserInfo();
  return `${os.charAt(0).toUpperCase() + os.slice(1)} ${browser}`;
}

/**
 * 生成完整的设备信息
 * 统一的设备信息生成方法，供所有模块使用
 * @returns 设备信息对象
 */
export function generateDeviceInfo(): DeviceInfo {
  const os = getOperatingSystem();
  return {
    device_type: getDeviceType(),
    device_id: generateStableDeviceId(),
    device_name: getDeviceName(),
    os: os,
    platform: os, // platform与os保持一致
    browser: getBrowserInfo(),
    app_version: '1.0.0', // 应用版本
    os_version: navigator.userAgent, // 操作系统版本信息
    user_agent: navigator.userAgent
  };
}

/**
 * 获取当前设备ID
 * @returns 当前设备的唯一标识符
 */
export function getCurrentDeviceId(): string {
  return generateStableDeviceId();
}

/**
 * 清除设备ID（用于测试或重置）
 * 注意：这会导致下次生成新的设备ID
 */
export function clearDeviceId(): void {
  localStorage.removeItem(DEVICE_ID_KEY);
}

/**
 * 验证设备信息的完整性
 * @param deviceInfo 设备信息对象
 * @returns 是否有效
 */
export function validateDeviceInfo(deviceInfo: any): deviceInfo is DeviceInfo {
  return (
    deviceInfo &&
    typeof deviceInfo.device_type === 'string' &&
    typeof deviceInfo.device_id === 'string' &&
    typeof deviceInfo.device_name === 'string' &&
    typeof deviceInfo.os === 'string' &&
    typeof deviceInfo.browser === 'string' &&
    deviceInfo.device_id.length > 0
  );
}

/**
 * 获取设备信息摘要（用于日志记录）
 * @returns 设备信息摘要字符串
 */
export function getDeviceInfoSummary(): string {
  const info = generateDeviceInfo();
  return `${info.device_type}|${info.os}|${info.browser}|${info.device_id.slice(-8)}`;
}