/**
 * 时间格式化工具函数
 */

/**
 * 格式化日期时间
 * @param time 日期时间对象或日期时间字符串或时间戳
 * @param format 格式化模板，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期时间字符串
 */
export function formatTime(time: Date | string | number, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!time) return '';
  
  const date = typeof time === 'object' ? time : new Date(time);
  
  if (isNaN(date.getTime())) {
    return '';
  }
  
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();
  
  const formatMap: Record<string, number> = {
    'YYYY': year,
    'MM': month,
    'DD': day,
    'HH': hours,
    'mm': minutes,
    'ss': seconds
  };
  
  return format.replace(/(YYYY|MM|DD|HH|mm|ss)/g, (match) => {
    const value = formatMap[match];
    return value < 10 ? `0${value}` : `${value}`;
  });
}

/**
 * 格式化相对时间（几分钟前、几小时前、昨天等）
 * @param time 日期时间对象或日期时间字符串或时间戳
 * @returns 相对时间字符串
 */
export function formatRelativeTime(time: Date | string | number): string {
  if (!time) return '';
  
  const date = typeof time === 'object' ? time : new Date(time);
  
  if (isNaN(date.getTime())) {
    return '';
  }
  
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  
  // 转换为秒
  const diffSeconds = Math.floor(diff / 1000);
  
  if (diffSeconds < 60) {
    return '刚刚';
  }
  
  // 转换为分钟
  const diffMinutes = Math.floor(diffSeconds / 60);
  
  if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  }
  
  // 转换为小时
  const diffHours = Math.floor(diffMinutes / 60);
  
  if (diffHours < 24) {
    return `${diffHours}小时前`;
  }
  
  // 转换为天
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffDays === 1) {
    return '昨天';
  }
  
  if (diffDays < 30) {
    return `${diffDays}天前`;
  }
  
  // 超过30天，返回日期
  return formatTime(date, 'YYYY-MM-DD');
}

/**
 * 格式化金额，添加千位分隔符，保留两位小数
 * @param amount 金额
 * @param decimals 小数位数，默认2位
 * @param separator 千位分隔符，默认为,
 * @returns 格式化后的金额字符串
 */
export function formatAmount(amount: number | string, decimals: number = 2, separator: string = ','): string {
  if (amount === null || amount === undefined) {
    return '0.00';
  }
  
  // 转换为数字
  const num = parseFloat(String(amount));
  
  if (isNaN(num)) {
    return '0.00';
  }
  
  // 格式化为固定小数位
  const fixed = num.toFixed(decimals);
  
  // 分割整数部分和小数部分
  const parts = fixed.split('.');
  
  // 添加千位分隔符
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator);
  
  // 重新组合
  return parts.join('.');
}

/**
 * 将日期加上指定的天数
 * @param date 日期对象或日期时间字符串或时间戳
 * @param days 要添加的天数
 * @returns 加上天数后的新日期对象
 */
export function addDays(date: Date | string | number, days: number): Date {
  if (!date) return new Date();
  
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

/**
 * 生成唯一ID
 * @param prefix 前缀，默认为空
 * @returns 唯一ID字符串
 */
export function generateUniqueId(prefix: string = ''): string {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substring(2, 8);
  return prefix ? `${prefix}-${timestamp}-${randomStr}` : `${timestamp}-${randomStr}`;
}

/**
 * 根据当前页面协议，调整输入链接的协议
 * @param link 输入的链接
 * @returns 调整协议后的链接
 */
export function adjustLinkProtocol(link: string): string {
  if (!link) {
    return '';
  }

  // 只处理以 http:// 或 https:// 开头的链接
  if (!link.toLowerCase().startsWith('http://') && !link.toLowerCase().startsWith('https://')) {
    return link;
  }

  // 检查是否在浏览器环境中
  if (typeof window === 'undefined' || !window.location) {
    // 如果不在浏览器环境，可以返回原始链接或根据需要处理
    return link;
  }

  const currentProtocol = window.location.protocol;
  // 移除链接中已有的 http:// 或 https:// (忽略大小写)
  const newLink = link.replace(/^(https?:\/\/)/i, '');

  if (currentProtocol === 'https:') {
    return `https://${newLink}`;
  } else {
    return `http://${newLink}`;
  }
}