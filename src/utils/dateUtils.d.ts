/**
 * u65e5u671fu5de5u5177u51fdu6570u7c7bu578bu58f0u660eu6587u4ef6
 */

/**
 * u683cu5f0fu5316u65e5u671fu65f6u95f4
 * @param date u65e5u671fu5bf9u8c61u6216u53efu8f6cu4e3au65e5u671fu7684u5b57u7b26u4e32
 * @param format u683cu5f0fu5316u683cu5f0fuff0cu9ed8u8ba4u4e3a YYYY-MM-DD HH:mm:ss
 * @returns u683cu5f0fu5316u540eu7684u65e5u671fu5b57u7b26u4e32
 */
export function formatDateTime(date: Date | string, format?: string): string;

/**
 * u83b7u53d6u76f8u5bf9u65f6u95f4u63cfu8ff0
 * @param date u65e5u671fu5bf9u8c61u6216u53efu8f6cu4e3au65e5u671fu7684u5b57u7b26u4e32
 * @returns u76f8u5bf9u5f53u524du65f6u95f4u7684u63cfu8ff0uff0cu5982u201c1u5c0fu65f6u524du201du7b49
 */
export function getRelativeTime(date: Date | string): string;
