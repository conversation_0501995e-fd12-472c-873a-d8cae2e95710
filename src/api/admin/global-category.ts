import request from '@/utils/request';
import type { AxiosPromise } from 'axios';
import type { CategoryItem } from '@/modules/merchant/types';

// 获取分类树
export function getCategoryTree(params?: any): AxiosPromise<{ data: CategoryItem[] }> {
  return request({
    url: '/v1/admin/takeout/global-categories/tree',
    method: 'get',
    params
  });
}

// 创建分类
export function createCategory(data: Partial<CategoryItem>): AxiosPromise<{ data: CategoryItem }> {
  return request({
    url: '/v1/admin/takeout/global-categories',
    method: 'post',
    data
  });
}

// 更新分类
export function updateCategory(id: number, data: Partial<CategoryItem>): AxiosPromise<{ data: CategoryItem }> {
  return request({
    url: `/v1/admin/takeout/global-categories/${id}`,
    method: 'put',
    data
  });
}

// 删除分类
export function deleteCategory(id: number): AxiosPromise<void> {
  return request({
    url: `/v1/admin/takeout/global-categories/${id}`,
    method: 'delete'
  });
}

// 导出分类
export function exportCategory(params?: any): AxiosPromise<Blob> {
  return request({
    url: '/v1/admin/takeout/global-categories/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

// 导入分类
export function importCategory(file: File): AxiosPromise<void> {
  const formData = new FormData();
  formData.append('file', file);
  
  return request({
    url: '/v1/admin/takeout/global-categories/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}
