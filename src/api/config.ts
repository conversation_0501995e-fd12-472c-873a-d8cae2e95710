/**
 * API请求配置
 */
import axios from 'axios';
import { useUserStore } from '@/stores/user';
import { ElMessage } from 'element-plus';

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
request.interceptors.request.use(
  config => {
    const userStore = useUserStore();
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data;
    if (res.code !== 200) {
      ElMessage.error(res.message || '操作失败');
      
      // 401: 未登录或Token过期
      if (res.code === 401) {
        const userStore = useUserStore();
        userStore.logout();
      }
      
      return Promise.reject(new Error(res.message || '操作失败'));
    }
    return res.data;
  },
  error => {
    const message = error.response?.data?.message || '网络错误，请稍后再试';
    ElMessage.error(message);
    return Promise.reject(error);
  }
);

export default request; 