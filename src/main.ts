import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { createPinia } from 'pinia'
import './style.css'
import App from './App.vue'
import router, { initializeRoutes } from './router'
// 导入plus-pro-components 及其样式
import PlusProComponents from 'plus-pro-components'
import plusZhCn from 'plus-pro-components/es/locale/lang/zh-cn'
import 'plus-pro-components/index.css'
import { setupRouteGuards } from './utils/routeGuard'
import { useSystemStore } from './stores/systemStore'
// 导入天地图全局组件注册
import TiandituMapComponents from './components/map/globalRegister'

const app = createApp(App)
const pinia = createPinia()

// 注册所有Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用Element Plus、Pinia和路由
app.use(ElementPlus, {
  locale: { ...zhCn, ...plusZhCn }
})
app.use(pinia)
app.use(router)
// 使用 plus-pro-components
app.use(PlusProComponents)
// 注册天地图组件
app.use(TiandituMapComponents)

// 添加全局的应用启动状态
const appStartupState = {
  routesReady: false,
  guardsMounted: false
};

// 先初始化路由，然后设置路由守卫，最后挂载应用
(async function startApp() {
  try {
    console.log('开始应用初始化过程...');
    
    // 初始化系统信息
    try {
      console.log('初始化系统信息...');
      const systemStore = useSystemStore();
      await systemStore.initSystemInfo();
      console.log('系统信息初始化完成');
    } catch (sysError) {
      console.warn('系统信息初始化失败，将在路由导航时再尝试:', sysError);
    }
    
    // 尝试初始化路由
    try {
      console.log('尝试预初始化路由...');
      // 预先导入动态配置页面组件，确保它在路由初始化前已开始加载
      const adminModule = await import('@/modules/admin/router/index');
      // 强制提前加载动态配置组件
      await adminModule.getDynamicConfigComponent();
      
      // 初始化路由
      await initializeRoutes();
      
      // 额外检查：确保已注册所有必要的路由
      const registeredRoutes = router.getRoutes().map(r => r.path);
      console.log('已预初始化的路由路径:', registeredRoutes);
      
      appStartupState.routesReady = true;
      console.log('路由预初始化完成');
    } catch (routeError) {
      console.warn('路由预初始化失败，将在导航时再尝试:', routeError);
    }
    
    // 设置路由守卫
    console.log('设置路由守卫...');
    setupRouteGuards(router);
    appStartupState.guardsMounted = true;
    
    // 挂载应用前增加一个小延迟，确保路由和组件都已加载
    await new Promise(resolve => setTimeout(resolve, 50));
    
    // 挂载应用
    console.log('挂载应用...');
    app.mount('#app');
    console.log('应用已挂载，路由初始化状态:', appStartupState.routesReady ? '完成' : '未完成');
  } catch (error) {
    console.error('应用启动过程出错:', error);
    // 确保应用始终能挂载，即使有错误
    if (!app._container) {
      app.mount('#app');
    }
  }
})();
