import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
//import { fileURLToPath } from 'url';
import path from 'path';

// 计算 __filename 和 __dirname
////const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'), // 配置 @ 指向 src 目录
      'vue': 'vue/dist/vue.esm-bundler.js', // 使用包含运行时编译器的Vue版本
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "@/styles/variables.scss" as *;`, // 自动注入变量文件
      },
    },
  },
});